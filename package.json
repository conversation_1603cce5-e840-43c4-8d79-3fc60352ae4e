{"name": "sanyth-x<PERSON><PERSON>-v5", "version": "1.1.9", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules", "postinstall": "patch-package"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.0.2", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/plugin-highlight": "^1.21.0", "@element-plus/icons-vue": "^2.3.1", "@icon-park/vue-next": "^1.4.2", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vueuse/core": "^13.4.0", "axios": "^1.8.4", "bytemd": "^1.21.0", "chart.js": "^4.4.8", "compressorjs": "^1.2.1", "countup.js": "^2.8.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.0", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "ele-admin-plus": "^1.4.0", "element-plus": "^2.9.9", "github-markdown-css": "^5.5.1", "highlight.js": "^11.9.0", "jsbarcode": "^3.11.6", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "quill": "^2.0.3", "smooth-signature": "^1.0.15", "sortablejs": "^1.15.2", "tinymce": "^5.10.9", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0", "tsparticles-slim": "^2.12.0", "vditor": "^3.10.9", "viewerjs": "^1.10.1", "vite": "^6.2.1", "vue": "^3.4.21", "vue-draggable-next": "^2.2.1", "vue-echarts": "^6.5.0", "vue-i18n": "^9.12.0", "vue-router": "^4.3.0", "vue3-quill": "^0.3.1", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "vuex-persistedstate": "^4.1.0", "xgplayer": "^3.0.16", "xgplayer-hls": "^3.0.16", "xgplayer-music": "^3.0.16", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/babel-plugin-jsx": "^1.2.2", "@vue/compiler-sfc": "^3.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.1", "less": "^2.7.2", "less-loader": "^5.0.0", "patch-package": "^8.0.0", "postcss": "^8.4.38", "postinstall-postinstall": "^2.1.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "sass": "^1.87.0", "svg-sprite-loader": "^6.0.11", "unplugin-vue-components": "^0.26.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-lang-jsx": "^1.5.3", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^9.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}