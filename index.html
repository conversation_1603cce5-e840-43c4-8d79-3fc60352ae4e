<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
	<script type="text/javascript">
	    window._AMapSecurityConfig = {
	        securityJsCode: '8b419c91198bdf079b2ebb28ca6dbc8c',
	    }
	</script>
	<script src='https://webapi.amap.com/maps?v=1.4.15&key=b1af91a4ef715141f6e89e3e55afa6f0'
	        type="text/javascript"></script>
	<title></title>
    <!-- <script type="text/javascript" src="http://*************:7081/security.js"></script> -->
  </body>
</html>
