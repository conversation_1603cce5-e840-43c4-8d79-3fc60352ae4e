<template>
  <component :is="iconComponent" :theme="theme" :size="size" :fill="fill" :strokeWidth="strokeWidth" />
</template>

<script>
var util = require("@/utils/util.js");

export default {
  name: 'IconPark',
  props: {
    iconName: {
      type: String,
      required: true,
	  default: 'fail-picture'
    },
    theme: {
      type: String,
      default: 'outline'
    },
    size: {
      type: [String, Number],
      default: 24
    },
	strokeWidth: {
		type: Number,
		default: 3
	},
    fill: {
      type: [String, Array],
      default: () => {
		  return util.getCurrentThemeColor()
	  }
    }
  },
  methods: {
	/**将字符串每个大写字母前添加-并转为小写，如果没有大写字母不用转换*/
	transformString(str) {
		if(/[A-Z]/.test(str)) {
			str = str.replace(/([A-Z])/g, (match) => `-${match.toLowerCase()}`).slice(1);
		}
		return str;
	}
  },
  computed: {
	/**
	   * 将vue3.0中设置的大写iconName转换为对应的小写组件名称
	   * 如：将 AddressBook 转为 icon-address-book
		*/
    iconComponent() {
      const componentName = 'icon-' + this.transformString(this.iconName);
      return componentName;
    }
  }
};
</script>