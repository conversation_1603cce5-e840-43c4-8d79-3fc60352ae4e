<template>
	<uni-card class="detailbox" :isFull="isFull" :padding="'10px 2px 0'" :status="status">
		<image v-if="banner" style="width: 100%; height: 140px;margin-top: 12px;" :src="banner"></image>
		<!-- <text class="uni-body uni-mt-5">卡片组件通用来显示完整独立的一段信息，同时让用户理解他的作用。例如一篇文章的预览图、作者信息、时间等，卡片通常是更复杂和更详细信息的入口点。</text> -->
		<slot name="content">
			
		</slot>
		<slot name="actions">
		</slot>
	</uni-card>
</template>

<script>
	export default {
		props: {
			/**
			 * 是否通屏
			 */
			isFull: {
				type: Boolean,
				default: false,
			},
			/**
			 * 右上角状态(每种状态显示不同颜色)
			 */
			status: {
				type: String,
				default: '',
			},
			/**
			 * 图片url
			 */
			banner: {
				type: String,
				default: '',
			},
		}
	}
</script>

<style lang="scss">
</style>