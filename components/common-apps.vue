<!-- 应用组件：显示分组和分组下的所有子应用 -->
<!-- displayType: '菜单组件' -->
<template>
	<block>
	<view v-if="marginTop != 20" class="index-sticky ui-BG-card" style="margin-top: 84rpx; padding: 12px 0 0;">
		<ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding: 0 12px 8px !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in dataList" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail"
					:data-path="item.path" :data-index="index">
					<view class="image-box">
						<Icon-Park :theme="theme" :iconName="item.icon" :fill="item.iconColor" />
					</view>
					<!-- <view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view> -->
					<!-- <text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}</text> -->
					<text class="ui-TC-5 text-df">{{item.name}}</text>
				</view>
			</block>
		</view>
		<view class="charts-box" v-if="chartType">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view>
	</view>
	<view v-else class="index-sticky ui-BG-card" style="padding: 0px 0  0;">
		<ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding: 0 12px 8px !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in dataList" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" 
				:data-path="item.path" :data-index="index">
					<view class="image-box">
						<Icon-Park :iconName="item.icon" :fill="item.iconColor" :theme="theme" :size="item.size" />
					</view>
					<!-- <view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view> -->
					<!-- <text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}</text> -->
					<text class="ui-TC-5 text-df">{{item.name}}</text>
				</view>
			</block>
		</view>
		<view class="charts-box" v-if="chartType">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view>
	</view>
	</block>
</template>

<script>
	export default {
		props: {
			/** 当前选中的主题对应的系统设置的16进制颜色代码 */
			fillColor: {
				type: String,
				default: ''
			},
			theme: {
				type: String,
				default: 'outline'
			},
			/** 分组名称 */
			title: {
				type: String,
				default: ''
			},
			/** 分组名称前的图标 */
			titleIcon: {
				type: String,
				default: '',
				// cicon-titles 竖实线
			},
			/** 分组名称后的文字描述 */
			desc: {
				type: String,
				default: ''
			},
			/** 更多 */
			more: {
				type: String,
				default: ''
			},
			marginTop: {
				type: Number,
				default: 20
			},
			chartType: {
				type: String,
				default: null
			},
			opts: {
				type: Object,
				default() {
					return {}
				}
			},
			/** 应用数组 */
			dataList: {
				type: Array,
				default() {
					return []
				}
			}
		},
		data(){
			return {
				chartsDataColumn: {},
			}
		},
		methods: {
			// 跳转至对应页面
			redictAppDetail: function(e) {
				console.log('redictAppDetail', e)
				let {
					index,
					// type,
					// appid,
					// url,
					path
				} = e.currentTarget.dataset;
				console.log('path', path)
				uni.navigateTo({
					url: path
				});
				// 上方直接跳转即可，为了演示demo页添加了下面这段代码，跳转到演示页
				// if (index === 0) {
				// 	console.log(index)
				// 	uni.navigateTo({
				// 		url: '/pages/demo/list-page'
				// 	});
				// } 
			// 	else if (index === 1) {
			// 		uni.navigateTo({
			// 			url: '/pages/topic/topic'
			// 		});
			// 	} else if (index === 2) {
			// 		uni.navigateTo({
			// 			url: '/pages/hot/hot'
			// 		});
			// 	} else if (index === 3) {
			// 		uni.navigateTo({
			// 			url: '/pages/my/my'
			// 		});
			// 	}
			
			// 	if (type === 'apppage') {
			// 		// 小程序页面         
			// 		uni.navigateTo({
			// 			url: path
			// 		});
			// 	}
			
			// 	if (type === 'webpage') {
			// 		// web-view页面
			// 		url = '../webpage/webpage?url=' + url;
			// 		uni.navigateTo({
			// 			url: url
			// 		});
			// 	}
			
			// 	if (type === 'miniapp') {
			// 		// 其他小程序
			// 		url = '../webpage/webpage?url=' + url;
			// 		uni.navigateTo({
			// 			url: url
			// 		});
			// 	}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	/* 图标导航 */
	.ui-BG-card {
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
		// box-shadow: none;
		// margin: 0;
		padding: 16px;
		box-shadow: none;
		margin: 0;
		border-radius: 0;
	}
	
	.ui-item {
		display: block;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
	}
	.ui-padding-12 {
		padding-bottom: 12px;
	}
	.ui-item .i-icon {
		width: 24px;
		height: 24px;
		margin: auto;
		margin-bottom: 2px;
		display: block;
		padding: 12px;
		background-color: #ffffff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
		border-radius: 12px;
	}
	.ui-item image, .ui-item svg path {
	  // fill: red; /* 改变SVG路径的颜色 */
	}
	
	.ui-item text {
		// color: #333;
		// font-weight: 300;
		color: var(--ui-TC-5) !important;
		font-family: "Inter";
		font-size: 14px !important;
		font-style: normal;
		font-weight: 400;
		line-height: 30px; /* 1.429 */
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
	// .ui-item .i-icon {
	//   width: 24px;
	//   height: 24px;
	//   margin: auto;
	//     margin-bottom: auto;
	//   margin-bottom: 2px;
	//   display: block;
	//   padding: 12px;
	//   background-color: #ffffff;
	//   box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0), 0px 0px 0px 0px rgba(0, 0, 0, 0);
	//   border-radius: 12px;
	// }
</style>