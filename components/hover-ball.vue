<template>
	<view>
		<movable-area class="movable-area">
			<movable-view class="movable-view" :x="x" :y="y" direction="all">
				<!-- <image src="../static/images/logo.png"></image> -->
				<uni-fab ref="fab" :pattern="pattern" :content="menuList" :horizontal="horizontal" :vertical="vertical"
							:direction="direction" @trigger="trigger" @fabClick="fabClick" />
			</movable-view>
		</movable-area>
	</view>
</template>

<script>
	import { mapState } from 'vuex';
	export default {
		data() {
			return {
				x: 320, //x坐标
				y: 520, //y坐标
				directionStr: '垂直',
				horizontal: 'right',
				vertical: 'bottom',
				direction: 'vertical', // 'horizontal'
				pattern: {
					color: '#9e9e9e',
					backgroundColor: 'rgba(239, 243, 255, 0.4)',
					// selectedColor: '#007AFF',
					// buttonColor: '#007AFF',
					selectedColor: 'var(--ui-BG-Main)',
					buttonColor: 'var(--ui-BG-Main)',
					iconColor: '#fff'
				},
				is_color_type: false,
			}
		},
		created(){
			console.log('menuList ',this.menuList)
		},
		computed: {
			menuList() {
				return this.$store.getters.getMenuList;
			}
		},
		methods: {
			trigger(e) {
				console.log('e', e)
				let index = e.index;
				this.$store.commit('changeMenuActive', index);
				// this.content.map(item => item.active = false)
				// this.content[e.index].active = !e.item.active;
				// store.commit('changeMenuActive', e.index); 
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/service1/service1'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/service/service'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
				// uni.showModal({
				// 	title: '提示',
				// 	content: `您${this.content[e.index].active ? '选中了' : '取消了'}${e.item.text}`,
				// 	success: function(res) {
				// 		if (res.confirm) {
				// 			console.log('用户点击确定')
				// 		} else if (res.cancel) {
				// 			console.log('用户点击取消')
				// 		}
				// 	}
				// })
			},
			// 跳转页面
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
			
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
			fabClick() {
				console.log('点击了悬浮按钮')
				// uni.showToast({
				// 	title: '点击了悬浮按钮',
				// 	icon: 'none'
				// })
			},
		}
	}
</script>

<style lang="scss">
	$all_width: 96rpx;
	$all_height: 96rpx;

	.movable-area {
		height: 100vh;
		width: 750rpx;
		top: 0;
		position: fixed;
		pointer-events: none; //此处要加，鼠标事件可以渗透

		.movable-view {
			width: $all_width;
			height: $all_height;
			pointer-events: auto; //恢复鼠标事件

			image {
				width: $all_width;
				height: $all_height;
			}
		}
	}
</style>