# 考勤状态判断逻辑实现指南

## 概述

本文档详细说明了考勤状态判断逻辑的实现，包括自动状态判断、服务类集成和API接口使用。

## 实现的功能

### 1. 自动状态判断
- ✅ **正常打卡** - 在规定时间范围内打卡
- ✅ **迟到** - 超过上班时间 + 容忍时间（默认15分钟）
- ✅ **早退** - 早于下班时间 - 容忍时间（默认30分钟）
- ✅ **缺勤/未打卡** - 没有打卡记录
- ✅ **请假** - 已申请请假（预留接口）

### 2. 服务类集成
- ✅ `QgzxAttendanceStatusService` - 专门的状态判断服务
- ✅ `QgzxAttendanceRecordService` - 集成状态判断逻辑
- ✅ 打卡时自动判断和设置状态
- ✅ 批量状态更新功能

### 3. API接口扩展
- ✅ 根据状态查询考勤记录
- ✅ 统计各种状态的数量
- ✅ 获取异常考勤记录
- ✅ 手动更新状态
- ✅ 批量更新状态

## 核心类说明

### 1. AttendanceStatus 枚举类
```java
public enum AttendanceStatus implements TextBaseEnum {
    NORMAL("正常打卡"),
    LATE("迟到"),
    EARLY_LEAVE("早退"),
    ABSENT("缺勤/未打卡"),
    LEAVE("请假");
}
```

### 2. QgzxAttendanceStatusService 状态判断服务
```java
// 自动判断打卡状态
AttendanceStatus determineAttendanceStatus(QgzxAttendanceRecord record, QgzxJobApplication jobApplication);

// 根据时间判断状态
AttendanceStatus determineStatusByTime(LocalDateTime clockTime, LocalDateTime scheduledTime, 
                                     AttendanceType attendanceType, int toleranceMinutes);

// 更新状态
void updateAttendanceStatus(String recordId, AttendanceStatus status);
```

### 3. AttendanceStatusUtil 工具类
```java
// 根据时间规则判断状态
AttendanceStatus determineStatus(LocalDateTime clockTime, AttendanceType attendanceType,
                               LocalTime workStartTime, LocalTime workEndTime);

// 计算迟到时间
long calculateLateMinutes(LocalDateTime clockTime, LocalTime workStartTime);

// 判断是否异常状态
boolean isAbnormalStatus(AttendanceStatus status);
```

## 使用示例

### 1. 打卡时自动判断状态
```java
@Transactional
public void clockIn(QgzxAttendanceRecord record) {
    // ... 其他验证逻辑
    
    // 自动判断并设置打卡状态
    AttendanceStatus status = attendanceStatusService.determineAttendanceStatus(record, jobApplication);
    record.setAttendanceStatus(status);
    
    save(record);
}
```

### 2. 查询特定状态的考勤记录
```java
// 查询迟到记录
QgzxAttendanceRecordParam param = new QgzxAttendanceRecordParam();
param.setAttendanceStatus(AttendanceStatus.LATE);
param.setAttendanceDateStart(LocalDate.of(2025, 7, 1));
param.setAttendanceDateEnd(LocalDate.of(2025, 7, 31));

List<QgzxAttendanceRecordVO> lateRecords = attendanceRecordService.listByStatus(param);
```

### 3. 统计状态分布
```java
// 统计某学生7月份的考勤状态分布
Map<AttendanceStatus, Long> statusCount = attendanceRecordService.countByStatus(
    "student123", 
    LocalDate.of(2025, 7, 1), 
    LocalDate.of(2025, 7, 31)
);

// 结果示例：
// {NORMAL=20, LATE=3, EARLY_LEAVE=1, ABSENT=2}
```

### 4. 获取异常考勤记录
```java
// 查询异常考勤记录（迟到、早退、缺勤）
QgzxAttendanceRecordParam param = new QgzxAttendanceRecordParam();
param.setXgh("202101001");
param.setPage(1);
param.setLimit(10);

PageResult<QgzxAttendanceRecordVO> abnormalRecords = attendanceRecordService.getAbnormalRecords(param);
```

## API接口使用

### 1. 根据状态查询考勤记录
```http
GET /api/workstudy/qgzx-attendance-record/list-by-status?attendanceStatus=LATE&attendanceDateStart=2025-07-01&attendanceDateEnd=2025-07-31
```

### 2. 统计状态数量
```http
GET /api/workstudy/qgzx-attendance-record/count-by-status?studentApplyId=123&startDate=2025-07-01&endDate=2025-07-31
```

### 3. 获取异常记录
```http
GET /api/workstudy/qgzx-attendance-record/abnormal-records?xgh=202101001&page=1&limit=10
```

### 4. 更新状态
```http
PUT /api/workstudy/qgzx-attendance-record/update-status/record123?status=NORMAL
```

### 5. 批量更新状态
```http
PUT /api/workstudy/qgzx-attendance-record/batch-update-status?studentApplyId=123&startDate=2025-07-01&endDate=2025-07-31
```

## 状态判断规则

### 1. 时间规则
- **迟到判断**：实际打卡时间 > 规定上班时间 + 容忍时间（15分钟）
- **早退判断**：实际打卡时间 < 规定下班时间 - 容忍时间（30分钟）
- **正常判断**：在规定时间范围内打卡

### 2. 优先级规则
1. 缺勤（最高优先级）
2. 迟到
3. 早退
4. 请假
5. 正常（最低优先级）

### 3. 特殊情况处理
- 没有配置工作时间：默认为正常状态
- 打卡时间为空：判断为缺勤
- 请假状态：通过请假系统判断（预留接口）

## 配置说明

### 1. 默认容忍时间
```java
private static final int DEFAULT_LATE_TOLERANCE = 15;      // 迟到容忍时间（分钟）
private static final int DEFAULT_EARLY_TOLERANCE = 30;     // 早退容忍时间（分钟）
```

### 2. 默认工作时间
```java
private static final LocalTime DEFAULT_START_TIME = LocalTime.of(9, 0);   // 09:00
private static final LocalTime DEFAULT_END_TIME = LocalTime.of(17, 0);    // 17:00
```

## 扩展建议

### 1. 请假系统集成
- 实现 `isOnLeave()` 方法
- 查询请假申请表
- 自动设置请假状态

### 2. 灵活的容忍时间配置
- 支持按岗位配置不同的容忍时间
- 支持按用户角色配置容忍时间
- 支持系统参数配置

### 3. 状态变更历史
- 记录状态变更历史
- 支持状态变更审批流程
- 提供状态变更统计报表

### 4. 通知机制
- 异常状态自动通知
- 状态统计定期报告
- 批量处理结果通知

## 注意事项

1. **数据一致性**：确保 `isNormal` 字段与 `attendanceStatus` 字段的一致性
2. **性能考虑**：批量更新时注意数据量，建议分批处理
3. **权限控制**：状态更新操作需要适当的权限验证
4. **日志记录**：重要的状态变更操作需要记录日志
5. **异常处理**：时间解析失败时的降级处理策略
