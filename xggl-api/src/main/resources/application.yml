# 端口
server:
  port: 8082
  tomcat:
    max-part-count: 1000

# 多环境配置
spring:
  cache:
    type: redis
  profiles:
    active: dev

  # 连接池配置
  datasource:
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      #pool-prepared-statements: false
      #max-pool-prepared-statement-per-connection-size: 20
      filters: stat, wall
      validation-query: SELECT 1 FROM DUAL
      aop-patterns: com.sanythadmin.*.*.service.*
      stat-view-servlet:
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
  # jpa配置
  jpa:
    hibernate:
      #      naming:
      #        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
#      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.OracleDialect
#    database-platform: org.hibernate.dialect.DmDialect
    open-in-view: false

  # json时间格式设置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss

  # 设置上传文件大小
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB

  # 邮件服务器配置
  mail:
    host: smtp.qq.com
    username:
    password:
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

# Mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:com/sanythadmin/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
  global-config:
    :banner: false
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
#  type-enums-package: com.eleadmin.*.**

# 框架配置
config:
  open-office-home: C:/OpenOffice4/
  temp-file-dir: C:/Develop/WorkSpace_syt/ele-admin-api/temp/
  # token过期时间（单位：小时）
  token-expire-time: 1
  # token刷新时间（单位：小时）
  token-refresh-time: 0.5

# 消息平台配置
message:
  platform:
    client-id: ${MSG_CLIENT_ID:70np33956d3r687O}
    client-secret: ${MSG_CLIENT_SECRET:T7b93Fc76Co382ZP}
    auth-token-url: ${MSG_TOKEN_URL:http://211.103.188.27:5173/api/cus-auth/token}
    auth-refresh-url: ${MSG_TOKEN_URL:http://211.103.188.27:5173/api/cus-auth/refresh}
    send-url: ${MSG_SEND_URL:http://211.103.188.27:5173/api/msg/send}
    max-retries: 3
    retry-interval: 10000

app:
  pagination:
    # 全局分页配置
    default-page-size: 20
    max-page-size: 100

    # 特定路径的分页配置
    path-configs:
      "/api/personInfo/**":
        path-pattern: "/api/personInfo/.*"
        default-page-size: 20
        max-page-size: 100

security:
  cors:
    enabled: true
    allowed-origins:
      - https://*.edu.cn
    allowed-origin-patterns:
      - https://*.edu.cn
    allowed-methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
      - PATCH
    allowed-headers:
      - Authorization
      - Content-Type
      - X-Requested-With
      - Accept
      - Ele-Admin-Token
      - Origin
      - Access-Control-Request-Method
      - Access-Control-Request-Headers
    exposed-headers:
      - Ele-Admin-Token
    allow-credentials: true
    max-age: 3600
  rate-limit:
    enabled: true
    #全局限流设置同一个 IP 在 1 分钟内最多可以请求 60 次
    global-window-minutes: 1
    global-max-requests: 60
    exclude-paths:
      - /druid/
      - /nonlogin/
      - /lib/
    path-rules:
      # 登录相关API - 更严格的限制,同一个 IP 在1分钟内最多可以请求 10 次,checkUserLoginAttempts中会调整限制次数和限制时间
      - path-pattern: /api/login
        window-minutes: 1
        max-requests: 20
  # 无需登录认证的请求
  ignore-auth-paths:
    - /api/sec_js
    - /api/login
    - /api/captcha
    - /api/system-info
    - /api/cas/**
    - /api/file/inline/**
    - /api/file/attachment/**
    - /nonlogin/**
    - /lib/**

