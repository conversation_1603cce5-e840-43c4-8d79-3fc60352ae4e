<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>develop</title>
</head>
<body>
<div>123</div>
<p id="result"></p>
<p id="user-agent"></p>
<button onclick="onRedirect()">redirect</button>
<button onclick="testLogin()">testLogin</button>
<button onclick="getUserInfo()">getUserInfo</button>
<button onclick="ddConfig()">ddConfig</button>
<button onclick="fetchConfig()">fetchConfig</button>
<button onclick="getLocation()">getLocation</button>
<p id="user-info"></p>
<script src="/lib/platform/dingtalk/web_auth/jquery-3.7.1.min.js"></script>
<script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
<script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
<script>
    var redirectUrl = null;

    function testLogin() {
        $.get("/nonlogin/yiban/checkLogin.htm", function (data, status) {
            alert(data);
        });
    }

    function onRedirect() {
        window.location.replace(redirectUrl)
    }

    function getUserInfo() {
        $.get("/yiban/api/getUserInfo.htm").done(function (res) {
            $("#user-info").text(JSON.stringify(res));
        })
    }

    function getQueryVariableObj() {
        let o = {};
        let searchParams1 = new URLSearchParams(window.location.search);
        for (const [key, value] of searchParams1) {
            if (value) {
                o[key] = value;
            }
        }
        return o;
    }

    function getLocation() {
        dd.getLocation({
            type: 1,
            useCache: true,
            coordinate: '1',
            cacheTimeout: 20,
            withReGeocode: true,
            targetAccuracy: '200',
            success: (res) => {
                console.log(res);
                let $result = $("#result");
                $result.empty();
                $result.text(JSON.stringify(res));
            },
            fail: () => {},
            complete: () => {},
        });
    }

    function fetchConfig(callback) {
        let token = getQueryVariableObj()['t'];
        const url = window.location.href;
        $.ajax({
            url: "/api/platform/dingtalk/view/getJsConfig",
            type: "POST",
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', 'Bearer ' + token);
            },
            data: {url: url},
            success: function (res) {
                console.log(res);
                let $result = $("#result");
                $result.empty();
                $result.text(JSON.stringify(res));

                if (callback) {
                    callback(res);
                }
            }
        });
    }

    function ddConfig() {
        fetchConfig(function (res) {
            const data = res.data;

            dd.config({
                agentId: data.agentId, // 必填，微应用ID
                corpId: data.corpId,//必填，企业ID
                timeStamp: data.timeStamp, // 必填，生成签名的时间戳
                nonceStr: data.nonceStr, // 必填，自定义固定字符串。
                signature: data.signature, // 必填，签名
                type: 0,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
                jsApiList: [
                    'runtime.info',
                    'biz.contact.choose',
                    'device.geolocation'
                ] // 必填，需要使用的jsapi列表，注意：不要带dd。
            });

            dd.error(function (err) {
                alert('dd error: ' + JSON.stringify(err));
            })//该方法必须带上，用来捕获鉴权出现的异常信息，否则不方便排查出现的问题
        })
    }
</script>
</body>
</html>
