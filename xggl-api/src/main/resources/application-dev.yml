# 开发环境配置
# 数据源配置
spring:
  datasource:
    url: ********************************************
#    url: jdbc:dm://*************:5236
#    url: ************************************
    username: syt_xg_2023
    password: 123456
    driver-class-name: oracle.jdbc.OracleDriver
#    driver-class-name: dm.jdbc.driver.DmDriver
    type: com.alibaba.druid.pool.DruidDataSource
  data:
    mongodb:
      host: *************
#      host: 127.0.0.1
      port: 37017
      authentication-database: admin
      database: syt_xgxt
      username: admin
      password: "123456"
    redis:
      host: *************
#      host: 127.0.0.1
      port: 6380
      password: 123456
      database: 0
      connect-timeout: 10000

# 日志配置
logging:
  level:
    com.sanythadmin: DEBUG
    com.baomidou.mybatisplus: DEBUG

security:
  rate-limit:
    enabled: false
  cors:
    enabled: false
    allowed-origins:
      - http://localhost:8082
      - http://localhost:5173
      - http://127.0.0.1:8082
      - http://127.0.0.1:5173
    allowed-origin-patterns:
      - http://192.168.*.*:*
    # 其他配置继承自application.yml

#认证中心配置
integration:
  auth-center:
    token: ${AUTH_CENTER_TOKEN:}
    ip: ${AUTH_CENTER_IP:}
    port: ${AUTH_CENTER_PORT:}
    self-url-root: ${AUTH_CENTER_SELF_URL_ROOT:}

#移动端配置
mobile:
  url-root: ${MOBILE_URL_ROOT:}
# CAS认证登录配置
cas:
  server-login-url: https://onecas.cau.edu.cn/tpass/login
  server-validate-url: https://onecas.cau.edu.cn/tpass/serviceValidate
  service-url: https://stu.cau.edu.cn/oauth/callback
  server-logout-url: https://onecas.cau.edu.cn/tpass/logout?service=https://stu.cau.edu.cn
  username-attribute: cas:uid

sanyth:
  license:
    enabled: false