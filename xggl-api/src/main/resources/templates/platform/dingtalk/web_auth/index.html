<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv=X-UA-Compatible content="IE=edge">
    <meta name=viewport
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name=format-detection content="telephone=no, email=no">

    <title>认证</title>

    <style>
        .d-flex {
            margin-top: 5%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .d2-flex {
            display: flex;
            flex-direction: column;
            font-size: 1.8rem;
            color: #00a5ec;
        }

        @media (max-width: 1024px) {
            .d-flex {
                font-size: 18px;
            }
        }

        .loader {
            margin-top: 5%;
            border: 5px solid #f3f3f3;
            border-radius: 50%;
            border-top: 5px solid #3498db;
            width: 30px;
            height: 30px;
            /*margin-top: 50%;*/
            -webkit-animation: spin 2s linear infinite; /* Safari */
            animation: spin 2s linear infinite;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }
            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .content {
            margin-top: 0.1rem;
            margin-bottom: 0.1rem;
        }

        .content-spec {
            margin-left: 10px;
            font-size: 1rem;
            color: #6b7280;
        }
    </style>

</head>
<body>
<div class="d-flex">
    <div id="main" class="d2-flex">
        <!--        <div class="content">加载钉钉资源...</div>-->
    </div>
    <div class="loader"></div>
</div>
<script src="/lib/platform/dingtalk/web_auth/jquery-3.7.1.min.js"></script>
<script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
<script th:if="${debug}" src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
<script th:inline="javascript">
    /*<![CDATA[*/

    const corpId = /*[[${corpId}]]*/ "defaultUser";
    console.log("Hello, " + corpId);
    const clientId = /*[[${clientId}]]*/ "defaultUser";
    console.log("clientId, " + clientId);
    const pcRedirectUri = /*[[${pcUrl}]]*/ "defaultUser";
    console.log("pcRedirectUri, " + pcRedirectUri);
    const mobileRedirectUri = /*[[${mobileUrl}]]*/ "defaultUser";
    console.log("mobileRedirectUri, " + mobileRedirectUri);
    const debug = /*[[${debug}]]*/ "defaultUser";
    console.log("debug, " + debug);
    const appId = /*[[${appId}]]*/ "defaultUser";
    console.log("appId, " + appId);

    /*]]>*/
</script>
<script>
    function throughAuthCode() {
        addStatus("获取授权码...");
        // inProgress = true;
        dd.runtime.permission.requestAuthCode({
            corpId: corpId,
            clientId: clientId,
            onSuccess: function (result) {
                addStatus("获取授权码成功");
                /*{
                    code: 'hYLK98jkf0m' //string authCode
                }*/
                console.log(result);
                auth(result.code);
            },
            onFail: function (err) {
                addStatus("获取授权码失败" + err);
            },
        });
    }

    function decodeBase64UrlSafe(base64url) {
        // 替换字符
        let base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
        // 补齐 padding（`=`）
        while (base64.length % 4 !== 0) {
            base64 += '=';
        }
        return atob(base64);
    }

    function auth(authCode) {
        let queryObj = getQueryVariableObj();
        let b64Url = queryObj['b64Url'];
        let b64RoleId = queryObj['b64RoleId'];
        const isPc = getQueryVariableObj()['pc'];

        addStatus("账号认证...")
        $.post("/nonlogin/platform/dingtalk/webAuth/authenticate", {
            appId: appId,
            authCode: authCode,
            b64RoleId: b64RoleId
        })
            .done(function (res) {
                console.log("res", res);
                if (res.code !== 0) {
                    addStatusError("账号认证失败", res.message);
                    return;
                }
                // stopLoading();
                addStatus("账号认证成功");
                const data = res.data;
                const token = data.token;

                if (debug) {
                    $.post("/nonlogin/platform/dingtalk/webAuth/debug-info", {info: "b64Url: " + b64Url});
                }
                if (b64Url) {
                    try {
                        let url = decodeBase64UrlSafe(b64Url);
                        redirectWithToken(url, data);
                    } catch (e) {
                        addStatusError("解析重定向地址失败", e);
                        if (debug) {
                            $.post("/nonlogin/platform/dingtalk/webAuth/debug-info", {info: "b64Url decode error: " + e});
                        }
                    }
                    return;
                }

                // sessionStorage.setItem("appId", appId);
                // if (isPc) {
                //     if (!pcRedirectUri) {
                //         addStatusError("基础配置异常", "未配置PC端首页")
                //         return;
                //     }
                //     redirectWithToken(pcRedirectUri, token);
                // } else {
                //     if (!mobileRedirectUri) {
                //         addStatusError("基础配置异常", "未配置移动端首页")
                //         return;
                //     }
                //     redirectWithToken(mobileRedirectUri, token);
                // }

                if (!mobileRedirectUri) {
                    addStatusError("基础配置异常", "未配置移动端重定向地址")
                    return;
                }
                redirectWithToken(mobileRedirectUri, data);
            })
            .fail(function (e) {
                addStatusError('账号认证失败', e.responseText)
            })
    }

    function redirectWithToken(url, data) {
        console.log('redirectWithToken', url, data);
        const token = data.token;
        const roleId = data.roleId;
        if (debug) {
            $.post("/nonlogin/platform/dingtalk/webAuth/debug-info", {info: "url: " + url + "; data: " + JSON.stringify(data)});
        }
        // let newUrl = addQueryParamToRelativeUrl(url, "token", token);
        let newUrl = url;
        if (url.includes("?")) {
            newUrl = url + "&t=" + token;
        } else {
            newUrl = url + "?t=" + token;
        }
        if (roleId) {
            newUrl += "&rid=" + roleId;
        }
        addStatus("加载系统资源中...");
        console.log("newUrl", newUrl);
        if (debug) {
            $.post("/nonlogin/platform/dingtalk/webAuth/debug-info", {info: "newUrl: " + newUrl}).done(function () {
                window.location.replace(newUrl);
            });
        } else {
            window.location.replace(newUrl);
        }
    }

    function getQueryVariableObj() {
        let o = {};
        let searchParams1 = new URLSearchParams(window.location.search);
        for (const [key, value] of searchParams1) {
            if (value) {
                o[key] = value;
            }
        }
        return o;
    }

    var funcArr = [];
    var inProgress = false;

    function runFuncArr() {
        var intervalID = setInterval(function () {
            if (inProgress) {
                return
            }
            var func = funcArr.shift();
            if (func == null) {
                return;
            }
            inProgress = true;
            func();
        }, 900);
    }

    function addStatus(text) {
        if (!debug) {
            return
        }
        $("#main").append("<div class='content'>" + text + "</div>");
    }

    function addStatusError(text, error) {
        stopLoading();
        $("#main").append("<div class='content'>" + text + "<div class='content-spec'>" + error + "</div></div>");
    }

    function addFunc(func) {
        funcArr.push(func)
    }

    function stopLoading() {
        $(".loader").hide();
    }

    runFuncArr()
    addStatus("加载钉钉资源成功")
    addFunc(throughAuthCode)
</script>
</body>
</html>
