# 生产环境配置
# 数据源配置
spring:
  datasource:
    url: ****************************************
    username: syt_xg_2023
    password: 123456
    driver-class-name: oracle.jdbc.OracleDriver
    type: com.alibaba.druid.pool.DruidDataSource

# 日志配置
logging:
  file:
    name: ele-admin-api.log
  level:
    root: WARN
    com.sanythadmin: ERROR
    com.baomidou.mybatisplus: ERROR

# 生产环境CORS配置
security:
  cors:
    enabled: true
    allowed-origins:
      - https://main.example.edu.cn
      - https://admin.example.edu.cn
    allowed-origin-patterns:
      - https://*.example.edu.cn
    # 其他配置继承自application.yml