#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`

tpid=$(awk 'NR==1 {print; exit}' $DEPLOY_DIR/bin/tpid)
if ps -p $tpid > /dev/null
then
    if [ ${tpid} ]; then
        echo 'Stop Process...'
        kill -15 $tpid
    fi
    sleep 5
    if ps -p $tpid > /dev/null
    then
        echo 'Kill Process complete.'
        kill -9 $tpid
    else
        echo 'Stop Success.‘
    fi
else
    echo "kill: ($tpid) - No such process"
fi