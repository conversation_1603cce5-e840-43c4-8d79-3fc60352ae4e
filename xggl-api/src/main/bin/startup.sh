#!/bin/bash
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
LIB_DIR=$DEPLOY_DIR/lib
LOG_PATH=$DEPLOY_DIR/logs

BOOT_JAR=$DEPLOY_DIR/xggl-1.0-SNAPSHOT-api.jar
SPRING_CONFIG_LOCATION=$DEPLOY_DIR/config/application.yml
LOGGING_CONFIG=$DEPLOY_DIR/config/logback-spring.xml

rm -f $DEPLOY_DIR/bin/tpid
nohup java -Dloader.path=$LIB_DIR -Dlog.path=$LOG_PATH -Dspring.config.location=$SPRING_CONFIG_LOCATION -jar $BOOT_JAR --logging.config=$LOGGING_CONFIG >/dev/null 2>&1 &
echo $! > $DEPLOY_DIR/bin/tpid
echo "Start Success."
