package com.sanythadmin.common.messsage.service;

import java.util.Collection;

public interface CommonMessageService {

    void pushText(Collection<String> usernames, String content);

    /**
     * 卡片消息
     *
     * @param usernames  用户名
     * @param title      标题
     * @param content    正文，不超过512个字符
     * @param mobilePath 移动端内部路由
     * @param pcPath     PC端内部路由
     * @param roleId     角色Id
     */
    void pushCard(Collection<String> usernames, String title, String content, String mobilePath, String pcPath, String roleId);

    /**
     * 卡片消息（markdown）
     *
     * @param usernames  用户名
     * @param title      标题
     * @param content    正文，不超过512个字符（平台不支持markdown时的备用）
     * @param markdown   正文，不超过1000个字符
     * @param mobilePath 移动端内部路由
     * @param pcPath     PC端内部路由
     * @param roleId     角色Id
     */
    void pushMarkdownCard(Collection<String> usernames, String title, String content, String markdown, String mobilePath, String pcPath, String roleId);

}
