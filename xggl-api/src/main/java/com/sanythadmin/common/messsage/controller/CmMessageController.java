package com.sanythadmin.common.messsage.controller;

import com.sanythadmin.common.messsage.service.CommonMessageService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

/**
 * @since 2025/6/25 14:54
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/common/message")
public class CmMessageController {

    private final CommonMessageService commonMessageService;

    @PreAuthorize("hasAuthority('common:message:send')")
    @RequestMapping("text")
    public void text(String username, String content) {
        commonMessageService.pushText(Collections.singletonList(username), content);
    }

    @PreAuthorize("hasAuthority('common:message:send')")
    @RequestMapping("card")
    public void card(String username, String title, String content, String markdown, String path, String roleId) {
        commonMessageService.pushMarkdownCard(Collections.singletonList(username), title, content, markdown, path, null, roleId);
    }
}
