package com.sanythadmin.common.messsage.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.integration.authcenter.dto.AcMessageDto;
import com.sanythadmin.common.integration.authcenter.message.AcMessageComponent;
import com.sanythadmin.common.messsage.service.CommonMessageService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

/**
 * @since 2025/6/24 15:16
 */
@AllArgsConstructor
@Service
public class CommonMessageServiceImpl implements CommonMessageService {

    private final AcMessageComponent messageComponent;

    @Override
    public void pushText(Collection<String> usernames, String content) {
        pushMarkdownCard(usernames, null, content, null, null, null, null);
    }

    @Override
    public void pushCard(Collection<String> usernames, String title, String content, String mobilePath, String pcPath, String roleId) {
        pushMarkdownCard(usernames, title, content, null, mobilePath, pcPath, roleId);
    }

    @Override
    public void pushMarkdownCard(Collection<String> usernames, String title, String content, String markdown, String mobilePath, String pcPath, String roleId) {
        if (CollectionUtils.isEmpty(usernames)) {
            throw new BusinessException("usernames is required");
        }
        if (!StringUtils.hasText(content)) {
            throw new BusinessException("content is required");
        }

        AcMessageDto messageDto = new AcMessageDto();
        messageDto.setUsernames(usernames);
        messageDto.setContent(content);
        messageDto.setTitle(title);
        messageDto.setMarkdown(markdown);
        messageDto.setPath(mobilePath);
        messageDto.setRoleId(roleId);
        messageComponent.pushData(messageDto);
    }

}