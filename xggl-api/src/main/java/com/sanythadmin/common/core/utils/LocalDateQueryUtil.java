package com.sanythadmin.common.core.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * LocalDate查询工具类
 * 解决Oracle数据库中LocalDate字段包含时间部分导致的查询匹配问题
 * @since 2025-07-20
 */
@Component
public class LocalDateQueryUtil {

    /**
     * LocalDate精确匹配查询（解决Oracle时间部分问题）
     * 将LocalDate转换为日期范围查询：00:00:00 到 23:59:59
     * 
     * @param wrapper 查询包装器
     * @param column 字段
     * @param value LocalDate值
     * @return 查询包装器
     */
    public static <T> LambdaQueryWrapper<T> eqLocalDate(
            LambdaQueryWrapper<T> wrapper, 
            SFunction<T, LocalDate> column, 
            LocalDate value) {
        if (value == null) {
            return wrapper;
        }
        LocalDateTime startOfDay = value.atStartOfDay(); // 00:00:00
        LocalDateTime endOfDay = value.atTime(23, 59, 59); // 23:59:59
        return wrapper.ge(column, startOfDay).le(column, endOfDay);
    }

    /**
     * LocalDate范围查询
     * 处理开始日期和结束日期的范围查询
     * 
     * @param wrapper 查询包装器
     * @param column 字段
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @return 查询包装器
     */
    public static <T> LambdaQueryWrapper<T> betweenLocalDate(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> column,
            LocalDate startDate, LocalDate endDate) {
        if (startDate != null) {
            wrapper.ge(column, startDate.atStartOfDay());
        }
        if (endDate != null) {
            wrapper.le(column, endDate.atTime(23, 59, 59));
        }
        return wrapper;
    }

    /**
     * 检查当前日期是否在指定的日期范围内
     * 常用于检查岗位是否在有效期内
     * 
     * @param wrapper 查询包装器
     * @param startColumn 开始日期字段
     * @param endColumn 结束日期字段
     * @param checkDate 要检查的日期，为null时使用当前日期
     * @return 查询包装器
     */
    public static <T> LambdaQueryWrapper<T> isDateInRange(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> startColumn,
            SFunction<T, LocalDate> endColumn,
            LocalDate checkDate) {
        LocalDate targetDate = checkDate != null ? checkDate : LocalDate.now();
        LocalDateTime checkDateTime = targetDate.atStartOfDay();
        
        // 开始日期 <= 检查日期 <= 结束日期
        wrapper.le(startColumn, checkDateTime.toLocalDate().atTime(23, 59, 59))
               .ge(endColumn, checkDateTime);
        return wrapper;
    }

    /**
     * LocalDate大于等于查询
     */
    public static <T> LambdaQueryWrapper<T> geLocalDate(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> column,
            LocalDate value) {
        if (value == null) {
            return wrapper;
        }
        return wrapper.ge(column, value.atStartOfDay());
    }

    /**
     * LocalDate小于等于查询
     */
    public static <T> LambdaQueryWrapper<T> leLocalDate(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> column,
            LocalDate value) {
        if (value == null) {
            return wrapper;
        }
        return wrapper.le(column, value.atTime(23, 59, 59));
    }

    /**
     * LocalDate大于查询
     */
    public static <T> LambdaQueryWrapper<T> gtLocalDate(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> column,
            LocalDate value) {
        if (value == null) {
            return wrapper;
        }
        return wrapper.gt(column, value.atTime(23, 59, 59));
    }

    /**
     * LocalDate小于查询
     */
    public static <T> LambdaQueryWrapper<T> ltLocalDate(
            LambdaQueryWrapper<T> wrapper,
            SFunction<T, LocalDate> column,
            LocalDate value) {
        if (value == null) {
            return wrapper;
        }
        return wrapper.lt(column, value.atStartOfDay());
    }
} 