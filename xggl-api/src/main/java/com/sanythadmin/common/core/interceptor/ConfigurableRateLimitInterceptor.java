package com.sanythadmin.common.core.interceptor;

import com.sanythadmin.common.core.config.RateLimitConfig;
import com.sanythadmin.common.core.exception.RateLimitException;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.service.LockRecordService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ConfigurableRateLimitInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private RateLimitConfig rateLimitConfig;
    
    @Autowired
    private LockRecordService lockRecordService;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!rateLimitConfig.isEnabled()) {
            return true;
        }
        
        String path = request.getRequestURI();
        String clientIp = CommonUtil.getIpAddr(request);
        
        // 检查是否在排除路径中
        for (String excludePath : rateLimitConfig.getExcludePaths()) {
            if (pathMatcher.match(excludePath, path)) {
                return true;
            }
        }
        
        // 查找匹配的路径规则
        RateLimitConfig.PathRule matchedRule = null;
        for (RateLimitConfig.PathRule rule : rateLimitConfig.getPathRules()) {
            if (pathMatcher.match(rule.getPathPattern(), path)) {
                matchedRule = rule;
                break;
            }
        }
        // 在响应头中添加限流信息
        if (matchedRule != null) {
            response.setHeader("X-RateLimit-Limit", String.valueOf(matchedRule.getMaxRequests()));
            response.setHeader("X-RateLimit-Window", matchedRule.getWindowMinutes() + "m");
        } else {
            response.setHeader("X-RateLimit-Limit", String.valueOf(rateLimitConfig.getGlobalMaxRequests()));
            response.setHeader("X-RateLimit-Window", rateLimitConfig.getGlobalWindowMinutes() + "m");
        }

        // 应用限流规则
        if (matchedRule != null) {
            // 使用特定路径的规则
            return checkRateLimit(clientIp, path, matchedRule.getMaxRequests(), matchedRule.getWindowMinutes(), request);
        } else {
            // 使用全局规则
            return checkRateLimit(clientIp, "global", rateLimitConfig.getGlobalMaxRequests(), rateLimitConfig.getGlobalWindowMinutes(), request);
        }
    }
    
    private boolean checkRateLimit(String clientIp, String pathKey, int maxRequests, int windowMinutes, HttpServletRequest request) {
        String key = "rate_limit:" + pathKey + ":" + clientIp;
        String lockKey = "ip_locked:" + clientIp;
        
        // 检查IP是否被锁定
        if (redisTemplate.hasKey(lockKey)) {
            log.warn("IP {} 已被锁定", clientIp);
            throw new RateLimitException("您的IP已被临时锁定，请稍后再试");
        }
        
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            redisTemplate.expire(key, windowMinutes, TimeUnit.MINUTES);
        }
        
        if (count > maxRequests) {
            log.warn("IP {} 对路径 {} 请求过于频繁，已被限制", clientIp, pathKey);
            
            // 如果在短时间内多次触发限流，锁定IP
            String triggerKey = "rate_limit_trigger:" + clientIp;
            Long triggerCount = redisTemplate.opsForValue().increment(triggerKey);
            if (triggerCount == 1) {
                redisTemplate.expire(triggerKey, 30, TimeUnit.MINUTES);
            }
            
            if (triggerCount >= 3) {
                // 锁定IP 30分钟
                redisTemplate.opsForValue().set(lockKey, "locked", 30, TimeUnit.MINUTES);
                log.warn("IP {} 多次触发限流，已被锁定30分钟", clientIp);
                
                // 记录IP限流锁定
                lockRecordService.recordIpRateLimitLock(clientIp, pathKey, triggerCount.intValue(), 
                                                      maxRequests, windowMinutes, request);
            }
            
            throw new RateLimitException("请求过于频繁，请稍后再试");
        }
        
        return true;
    }
}