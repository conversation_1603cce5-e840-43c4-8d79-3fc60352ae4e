package com.sanythadmin.common.core.utils;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import com.sanythadmin.common.enums.UserType;

import java.lang.reflect.Field;
import java.util.Map;

public class SqlUtil {

    public static String getRealSql(Object wrapper) {
        String sqlSegment;
        Map<String, Object> paramNameValuePairs;

        if (wrapper instanceof QueryWrapper) {
            QueryWrapper<?> queryWrapper = (QueryWrapper<?>) wrapper;
            sqlSegment = queryWrapper.getSqlSegment();
            paramNameValuePairs = queryWrapper.getParamNameValuePairs();
        } else if (wrapper instanceof MPJLambdaWrapper) {
            MPJLambdaWrapper<?> mpjLambdaWrapper = (MPJLambdaWrapper<?>) wrapper;
            sqlSegment = mpjLambdaWrapper.getSqlSegment();
            paramNameValuePairs = mpjLambdaWrapper.getParamNameValuePairs();
        } else {
            throw new IllegalArgumentException("Unsupported wrapper type");
        }
        for (Map.Entry<String, Object> entry : paramNameValuePairs.entrySet()) {
            String placeholder = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
            Object value = entry.getValue();
            String realValue = "";
            if (value instanceof UserType) {
                realValue = String.valueOf(((UserType) value).getValue());
            } else if (value instanceof String) {
                realValue = "'" + value + "'";
            } else if (value instanceof ValueTextBaseEnum) {
                Field[] fields = value.getClass().getDeclaredFields();
                for (Field field : fields) {
                    EnumValue annotation = field.getAnnotation(EnumValue.class);
                    if (annotation != null) {
                        Object val = CommonUtil.getValue(field.getName(), value);
                        realValue = val instanceof String ? "'" + val + "'" : String.valueOf(val);
                        break;
                    }
                }
            } else {
                realValue = value.toString();
            }
            sqlSegment = sqlSegment.replace(placeholder, realValue);
        }
        return sqlSegment;
    }

}
