package com.sanythadmin.common.core.security.rsa;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class SecJS {
    private static StringBuilder ss = null;
    private static int length = 32;
    private static String prefix = "dk:";

    public static String getJS(String salt) {
        StringBuilder js = new StringBuilder();
        js.append("function _DK(){return \"").append(byte2hex(salt.getBytes())).append("\";}\n");
        if (ss == null) {
            synchronized (SecJS.class) {
                PublicKeyMap pm = RSAUtils.getPublicKeyMap();
                if (ss == null) {
                    ss = new StringBuilder();
                    ss.append("function _M(){return \"").append(byte2hex(pm.getModulus().getBytes())).append("\";}\n");
                    ss.append("function _E(){return \"").append(byte2hex(pm.getExponent().getBytes())).append("\";}\n");
                    ss.append("function encrypt(s){var pk = RSAUtils.getKeyPair(_E(), '', _M());var ss=encodeURIComponent(_DK()+s);var text = RSAUtils.encryptedString(pk,ss); return text;}\n");
                }
            }
        }
        js.append(ss.toString());
        return js.toString();
    }

    private static String byte2hex(byte[] bytes) {
        StringBuilder hs = new StringBuilder();
        String stmp = "";
        for (int n = 0; n < bytes.length; ++n) {
            stmp = Integer.toHexString(bytes[n] & 0xFF);
            hs.append("\\x");
            if (stmp.length() == 1) {
                hs.append("0");
                hs.append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString();
    }

    public static String newDynamicKeyScript() {
        String key = CommonUtil.randomCode(length).toLowerCase();
        RedisTemplate<String, Object> redisTemplate = SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);
        redisTemplate.opsForValue().set(prefix + key, key, 2, TimeUnit.MINUTES);
        return getJS(key);
    }

    public static void clearDynamicKey(String key) {
        if (!StringUtils.hasLength(key)) return;
        RedisTemplate<String, Object> redisTemplate = SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);
        redisTemplate.delete(prefix + key);
    }

    public static DecryptMap checkDynamicKeyAndGet(@NotEmpty String str) throws RSADecryptException {
        DecryptMap decryptMap = new DecryptMap(str);
        try {
            str = str.trim();
            str = RSAUtils.decryptStringByJs(str);
            str = URLDecoder.decode(str, StandardCharsets.UTF_8);
            Assert.hasLength(str, "请求参数解码错误");
            String key = str.substring(0, length);
            decryptMap.setKey(key);
            decryptMap.setValue(str.substring(length));
            RedisTemplate<String, Object> redisTemplate = SpringContextUtil.getBean("redisTemplate", RedisTemplate.class);
            Object cacheKey = redisTemplate.opsForValue().get(prefix + key);
            Assert.isTrue(!Objects.isNull(cacheKey) && Objects.equals(key, String.valueOf(cacheKey)), "请求参数解码key错误");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RSADecryptException("请求参数解码错误,请重试");
        }
        return decryptMap;
    }
}
