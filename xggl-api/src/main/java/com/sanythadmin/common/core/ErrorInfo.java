package com.sanythadmin.common.core;

public class ErrorInfo {
    public static final String CAPTCHA_ERROR_OR_EXPIRED = "验证码错误";
    public static final String INCORRECT_USERNAME_OR_PASSWORD = "用户名或密码错误";
    public static final String USERNAME_EXISTS = "用户名已存在";
    public static final String ID_CODE_EXISTS = "证件号已存在";
    public static final String PHONE_NUMBER_ALREADY_EXISTS = "手机号已存在";
    public static final String USERNAME_IS_EMPTY = "用户名不能为空";
    public static final String ROLE_INFO_IS_EMPTY = "角色不能为空";
    public static final String PARAMETER_NAME_EXISTS = "参数名称已存在";
    public static final String MISSING_REQUIRED_FIELD_PARAMETERS = "缺失必填项参数";

    public static final String INVALID_REQUEST_METHOD = "请求方式不正确";
    public static final String INVALID_PARAMETER_FORMAT = "参数格式不正确";
    public static final String ILLEGAL_REQUEST_PARAMETER = "非法参数异常";
    public static final String IP_TEMPORARILY_BLOCKED = "IP地址暂时被阻止访问";
    public static final String REQUEST_TOO_FREQUENT = "请求过于频繁，请稍后再试";
    public static final String ACCOUNT_TEMPORARILY_LOCKED = "账户暂时被锁定，请稍后再试";
    public static final String TOO_MANY_LOGIN_ATTEMPTS = "登录尝试次数过多，账户已被锁定";
    public static final String PARAMETER_DECRYPTION_FAILED = "参数解码错误，刷新页面后尝试";
    public static final String ACCOUNT_DISABLED = "账户已被禁用";
    public static final String ACCOUNT_LOCKED = "账户已被锁定";
    public static final String ACCOUNT_EXPIRED = "账户已过期";
    public static final String CREDENTIALS_EXPIRED = "密码已过期";

    public static final String SERVER_INTERNAL_ERROR = "服务器内部错误，请联系管理员";


}
