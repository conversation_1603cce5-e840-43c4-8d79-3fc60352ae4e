package com.sanythadmin.common.core.utils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class ArraysUtil {

    public static boolean areAllSelectedCorrect(String[] correctOptions, String[] currentSelected) {
        Set<String> correctSet = new HashSet<>(Arrays.asList(correctOptions));
        // 遍历 currentSelected，检查每个选项是否都在 correctSet 中
        for (String selected : currentSelected) {
            if (!correctSet.contains(selected)) {
                return false; // 如果有一个选项不在 correctSet 中，返回 false
            }
        }
        return true; // 如果所有选项都在 correctSet 中，返回 true
    }

}
