package com.sanythadmin.common.core.handler;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonStreamContext;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.sanythadmin.common.core.annotation.FieldMask;
import com.sanythadmin.common.core.utils.MaskUtils;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.enums.Mask;

import java.io.IOException;
import java.util.Objects;

public class MaskSerialize extends JsonSerializer<String> implements ContextualSerializer {
    /**
     * 脱敏类型
     */
    private Mask type;

    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        boolean flag = checkMaskFlag(jsonGenerator);
        if (flag) {
            switch (this.type) {
                case ID_CARD: {
                    s = MaskUtils.idCardNum(s);
                    break;
                }
                case MOBILE_PHONE: {
                    s = MaskUtils.mobilePhone(s);
                    break;
                }
                case EMAIL: {
                    s = MaskUtils.email(s);
                    break;
                }
            }
        }
        jsonGenerator.writeString(s);
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        // 为空直接跳过
        if (beanProperty == null) {
            return serializerProvider.findNullValueSerializer(beanProperty);
        }
        // 非String类直接跳过
        if (Objects.equals(beanProperty.getType().getRawClass(), String.class)) {
            FieldMask fieldMask = beanProperty.getAnnotation(FieldMask.class);
            if (fieldMask == null) {
                fieldMask = beanProperty.getContextAnnotation(FieldMask.class);
            }
            if (fieldMask != null) {
                // 如果能得到注解，就将注解的 value 传入 MaskSerialize
                return new MaskSerialize(fieldMask.value());
            }
        }
        return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
    }

    public MaskSerialize() {

    }

    public MaskSerialize(final Mask type) {
        this.type = type;
    }

    private boolean checkMaskFlag(JsonGenerator jsonGenerator) {
        boolean enableMask = false;
        JsonStreamContext outputContext = jsonGenerator.getOutputContext();
        while (true) {
            JsonStreamContext parent = outputContext.getParent();
            if (parent == null) break;
            if (parent.getCurrentValue() instanceof ApiResult) {
                ApiResult object = (ApiResult) parent.getCurrentValue();
                enableMask = object.getMask();
                break;
            }
            outputContext = parent;
        }
        return enableMask;
    }
}
