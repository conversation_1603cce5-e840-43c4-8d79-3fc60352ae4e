package com.sanythadmin.common.core.utils;

import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.UserInfoService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityUtil {
    public static SysAccount getAccount() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Object object = authentication.getPrincipal();
            if (object instanceof SysAccount)
                return (SysAccount) object;
        }
        return null;
    }

    public static String getUsername() {
        SysAccount account = getAccount();
        return account == null ? null : account.getUsername();
    }

    public static String getRealName() {
        SysAccount account = getAccount();
        return account == null ? null : account.getRealName();
    }

    public static UserInfo getUserInfo() {
        SysAccount account = getAccount();
        if (account != null) {
            UserInfoService userInfoService = SpringContextUtil.getBean(UserInfoService.class);
            return userInfoService.get(account.getUsername());
        }
        return null;
    }

    public static SysRole getRole() {
        SysAccount account = getAccount();
        return account.getRole();
    }

    public static String getRoleId() {
        SysAccount account = getAccount();
        return account.getRole().getId();
    }

    public static String getRoleScope() {
        SysAccount account = getAccount();
        return account.getRole().getRoleScope();
    }

    public static String getRoleName() {
        SysAccount account = getAccount();
        return account.getRole().getName();
    }

    public static boolean hasAuthority(String s) {
        SysAccount account = getAccount();
        if (account != null) {
            return account.getAuthorities().stream().anyMatch(m -> s.equals(m.getAuthority()));
        }
        return false;
    }
}
