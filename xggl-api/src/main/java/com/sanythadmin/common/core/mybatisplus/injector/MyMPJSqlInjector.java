package com.sanythadmin.common.core.mybatisplus.injector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.sanythadmin.common.core.mybatisplus.method.*;

import java.util.List;

/**
 * Created by JIANGPING on 2024/8/30.
 */
public class MyMPJSqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        methodList.add(new SelectGroupCount());
        methodList.add(new SelectGroupCountWithPermission());
        methodList.add(new SelectCountWithPermission());
        methodList.add(new SelectListWithPermission());
        methodList.add(new SelectPageWithPermission());
        // join查询
        methodList.add(new SelectJoinCountWithPermission());
        methodList.add(new SelectJoinListWithPermission());
        methodList.add(new SelectJoinMapsPageWithPermission());
        methodList.add(new SelectJoinMapsWithPermission());
        methodList.add(new SelectJoinMapWithPermission());
        methodList.add(new SelectJoinOneWithPermission());
        methodList.add(new SelectJoinPageWithPermission());
        return methodList;
    }
}
