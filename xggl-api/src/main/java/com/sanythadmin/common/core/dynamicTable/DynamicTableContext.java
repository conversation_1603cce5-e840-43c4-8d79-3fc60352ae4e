package com.sanythadmin.common.core.dynamicTable;

import com.baomidou.mybatisplus.annotation.TableName;

@Deprecated
public class DynamicTableContext {
    private static final ThreadLocal<String> tableNameHolder = new ThreadLocal<>();
    private static final ThreadLocal<Class<?>> entityClassHolder = new ThreadLocal<>();

    public static void setTableContext(Class<?> entityClass, String type) {
//        tableNameHolder.set(entityClass.getSimpleName().toLowerCase() + "_" + type);
        tableNameHolder.set(entityClass.getAnnotation(TableName.class).value() + "_" + type.toUpperCase());
        entityClassHolder.set(entityClass);
    }

    public static String getTableName() {
        return tableNameHolder.get();
    }

    public static Class<?> getEntityClass() {
        return entityClassHolder.get();
    }

    public static void clear() {
        tableNameHolder.remove();
        entityClassHolder.remove();
    }
}

