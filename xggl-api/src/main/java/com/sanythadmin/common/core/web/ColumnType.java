package com.sanythadmin.common.core.web;

/**
 * <AUTHOR>
 * @since 2021-10-22
 */
public class ColumnType {
    public static final String CHAR_32 = "CHAR(32)";    // 注：不足32位ORACLE会自动补全空格
    public static final String VARCHAR2_32 = "VARCHAR2(32 CHAR)";
    public static final String VARCHAR2_50 = "VARCHAR2(50 CHAR)";
    public static final String VARCHAR2_100 = "VARCHAR2(100 CHAR)";
    public static final String VARCHAR2_256 = "VARCHAR2(256 CHAR)";
    public static final String VARCHAR2_512 = "VARCHAR2(512 CHAR)";
    public static final String VARCHAR2_1024 = "VARCHAR2(1024 CHAR)";
    public static final String VARCHAR2_4000 = "VARCHAR2(4000 CHAR)";

    public static final String NUMBER_1 = "NUMBER(1)";
    public static final String NUMBER_10 = "NUMBER(10)";
    public static final String NUMBER_5_2 = "NUMBER(5,2)";
    public static final String NUMBER_7_2 = "NUMBER(7,2)";
    public static final String NUMBER_1_DEFAULT_1 = "NUMBER(1) DEFAULT 1";
    public static final String NUMBER_1_DEFAULT_0 = "NUMBER(1) DEFAULT 0";
    public static final String NUMBER_5_DEFAULT_0 = "NUMBER(5) DEFAULT 0";
    public static final String CLOB = "CLOB";
    public static final String DATE = "DATE";
    public static final String TIMESTAMP = "TIMESTAMP";
}
