package com.sanythadmin.common.core.handler;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.exception.RateLimitException;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.ApiResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @since 2018-02-22 11:29:30
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ApiResult<?> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                            HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.INVALID_REQUEST_METHOD).setError(ErrorInfo.INVALID_REQUEST_METHOD);
    }

    @ResponseBody
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ApiResult<?> accessDeniedExceptionHandler(AccessDeniedException e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.UNAUTHORIZED_CODE, Constants.UNAUTHORIZED_MSG).setError(Constants.UNAUTHORIZED_MSG);
    }

    @ResponseBody
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<?> methodArgumentNotValidExceptionHandler(BindException e, HttpServletRequest request, HttpServletResponse response) {
        BindingResult bindingResult = e.getBindingResult();
        log.error("请求[ {} ] {} 的参数校验发生错误", request.getMethod(), request.getRequestURL());
//        for (ObjectError objectError : bindingResult.getAllErrors()) {
//            FieldError fieldError = (FieldError) objectError;
//            log.error("参数 {} = {} 校验错误：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage());
//            result.put(fieldError.getField(), fieldError.getDefaultMessage());
//        }
        String defaultMessage = bindingResult.getAllErrors().get(0).getDefaultMessage();
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, defaultMessage).setError(defaultMessage);
    }

    @ResponseBody
    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<?> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数: {}", e.getMessage());
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.ILLEGAL_REQUEST_PARAMETER).setError(ErrorInfo.ILLEGAL_REQUEST_PARAMETER);
    }

    @ResponseBody
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<?> handleRuntimeException(RuntimeException e, HttpServletResponse response) {
        if (e instanceof BusinessException) {
            return businessExceptionResult((BusinessException) e, response);
        }
        BusinessException businessException = findBusinessException(e);
        if (businessException != null) {
            return businessExceptionResult(businessException, response);
        }

        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.SERVER_INTERNAL_ERROR).setError(ErrorInfo.SERVER_INTERNAL_ERROR);
    }

    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<?> businessExceptionHandler(BusinessException e, HttpServletResponse response) {
        return businessExceptionResult(e, response);
    }

    private static ApiResult<Object> businessExceptionResult(BusinessException e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        int errorCode = Constants.RESULT_ERROR_CODE;
        if (e.getCode() != null)
            errorCode = e.getCode();
        return new ApiResult<>(errorCode, e.getMessage()).setData(e.getData()).setError(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(RateLimitException.class)
    @ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)  // 429状态码
    public ApiResult<?> rateLimitExceptionHandler(RateLimitException e, HttpServletResponse response) {
        log.warn("请求被限流: {}", e.getMessage());
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.TOO_MANY_REQUESTS_CODE, ErrorInfo.REQUEST_TOO_FREQUENT).setError(e.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(CompletionException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<?> completionExceptionHandler(Throwable e, HttpServletResponse response) {
        if (e.getCause() instanceof BusinessException e1)
            return businessExceptionResult(e1, response);
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.SERVER_INTERNAL_ERROR).setError(ErrorInfo.SERVER_INTERNAL_ERROR);
    }

    @ResponseBody
    @ExceptionHandler(ExecutionException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResult<?> executionExceptionHandler(ExecutionException e, HttpServletResponse response) {
        if (e.getCause() instanceof BusinessException e1)
            return businessExceptionResult(e1, response);
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.SERVER_INTERNAL_ERROR).setError(ErrorInfo.SERVER_INTERNAL_ERROR);
    }

    @ResponseBody
    @ExceptionHandler(DataAccessException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<?> handleDataAccessException(DataAccessException e) {
        log.error("数据库访问异常: {}", e.getMessage(), e);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.SERVER_INTERNAL_ERROR).setError("数据库操作失败");
    }

    @ResponseBody
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResult<?> exceptionHandler(Throwable e, HttpServletResponse response) {
        if (e instanceof BusinessException) {
            return businessExceptionResult((BusinessException) e, response);
        }
        BusinessException businessException = findBusinessException(e);
        if (businessException != null) {
            return businessExceptionResult(businessException, response);
        }

        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, ErrorInfo.SERVER_INTERNAL_ERROR).setError(ErrorInfo.SERVER_INTERNAL_ERROR);
    }

    /**
     * 获取异常的根本原因
     */
    private Throwable getRootCause(Throwable throwable) {
        Throwable cause = throwable.getCause();
        if (cause == null || cause == throwable) {
            return throwable;
        }
        return getRootCause(cause);
    }

    /**
     * 在异常链中查找BusinessException
     *
     * @param throwable 异常
     * @return 如果找到则返回BusinessException，否则返回null
     * @since 2024-07-17
     */
    private BusinessException findBusinessException(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        if (throwable instanceof BusinessException) {
            return (BusinessException) throwable;
        }
        Throwable cause = throwable.getCause();
        if (cause != null && cause != throwable) {
            return findBusinessException(cause);
        }
        return null;
    }
}
