package com.sanythadmin.common.core.config.enumConverterFactory;

import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class StringToTextBaseEnumConverterFactory implements ConverterFactory<String, TextBaseEnum> {
    
    private static final Map<Class<?>, Converter<String, ?>> CONVERTER_CACHE = new ConcurrentHashMap<>();
    
    @Override
    public <T extends TextBaseEnum> Converter<String, T> getConverter(Class<T> targetType) {
        @SuppressWarnings("unchecked")
        Converter<String, T> converter = (Converter<String, T>) CONVERTER_CACHE.get(targetType);
        if (converter == null) {
            converter = new StringToTextBaseEnumConverter<>(targetType);
            CONVERTER_CACHE.put(targetType, converter);
        }
        return converter;
    }
}
