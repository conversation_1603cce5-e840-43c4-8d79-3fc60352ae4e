package com.sanythadmin.common.core.utils;

import com.alibaba.excel.EasyExcel;
import com.sanythadmin.common.core.web.ExcelImportError;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
public class ExcelUtils {
    private static final String FULL_DATA_FORMAT = "yyyy/MM/dd  HH:mm:ss";
    private static final String SHORT_DATA_FORMAT = "yyyy/MM/dd";
    private static final String xls = ".xls";
    private static final String xlsx = ".xlsx";

    /**
     * Excel表头对应Entity属性 解析封装javabean
     *
     * @param aClass   类
     * @param in       excel流
     * @param fileName 文件名
     * @param heads    excel表头与entity属性对应关系
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readExcelToEntity(Class<T> aClass, InputStream in, String fileName, List<Head> heads) throws Exception {
        checkFile(fileName);                            //  是否EXCEL文件
        Workbook workbook = getWorkBook(in, fileName);  //  兼容新老版本
        return readExcel(aClass, workbook, heads);
    }

    /**
     * 解析Excel转换为Entity
     *
     * @param aClass   类
     * @param in       excel流
     * @param fileName 文件名
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<T> readExcelToEntity(Class<T> aClass, InputStream in, String fileName) throws Exception {
        return readExcelToEntity(aClass, in, fileName, null);
    }

    /**
     * 校验是否是Excel文件
     *
     * @param fileName
     * @throws Exception
     */
    public static void checkFile(String fileName) throws Exception {
        if (StringUtils.hasText(fileName) && !(fileName.endsWith(xlsx) || fileName.endsWith(xls)))
            AssertUtil.throwMessage("请上传Excel文件");
    }

    /**
     * 兼容新老版Excel
     *
     * @param in
     * @param fileName
     * @return
     * @throws IOException
     */
    private static Workbook getWorkBook(InputStream in, String fileName) throws IOException {
        if (fileName.endsWith(xlsx)) {
            return new XSSFWorkbook(in);
        } else {
            return new HSSFWorkbook(in);
        }
    }

    /**
     * 解析Excel
     *
     * @param aClass   类
     * @param workbook 工作簿对象
     * @param heads    excel与entity对应关系实体
     * @param <T>
     * @return
     * @throws Exception
     */
    private static <T> List<T> readExcel(Class<T> aClass, Workbook workbook, List<Head> heads) throws Exception {
        List<ExcelImportError> errors = new ArrayList<>();
//        HashMap<String, String> headMap = new HashMap<>();
        HashMap<String, String> regexMap = new HashMap<>();
        HashMap<String, Boolean> requiredMap = new HashMap<>();
        HashMap<String, Integer> maxLengthMap = new HashMap<>();
        for (Head head : heads) {
//            headMap.put(head.getHeadName(), head.getEntityName());
            requiredMap.put(head.getEntityName(), head.getRequired());
            regexMap.put(head.getEntityName(), head.getRegex());
            maxLengthMap.put(head.getEntityName(), head.getMaxLength());
        }

        List<T> beans = new ArrayList<>();
        int sheetNum = workbook.getNumberOfSheets();
        DataFormatter dataFormatter = new DataFormatter();
        for (int sheetIndex = 0; sheetIndex < sheetNum; sheetIndex++) {
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            String sheetName = sheet.getSheetName();
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            Row head = sheet.getRow(firstRowNum);
            if (head == null)
                continue;
            short firstCellNum = head.getFirstCellNum();
            short lastCellNum = head.getLastCellNum();
            Field[] fields = aClass.getDeclaredFields();
            for (int rowIndex = firstRowNum + 1; rowIndex <= lastRowNum; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null)
                    continue;
                Constructor<T> constructor = aClass.getConstructor();
                T instance = constructor.newInstance();
                for (int cellIndex = firstCellNum; cellIndex < lastCellNum; cellIndex++) {
                    Cell headCell = head.getCell(cellIndex);
                    if (headCell == null)
                        continue;
                    String headName = dataFormatter.formatCellValue(headCell).trim();
                    Cell cell = dataRow.getCell(cellIndex);
                    if (!StringUtils.hasText(headName))
                        continue;
                    Head excelHead = heads.get(cellIndex);
                    if (headName.startsWith(excelHead.headName)) {
                        Field field = ReflectionUtils.findField(aClass, excelHead.entityName);
                        if (field == null) AssertUtil.throwMessage("excel文件表头名称错误");
                        String methodName = MethodUtils.setMethodName(field.getName());
                        Method method = aClass.getMethod(methodName, field.getType());
                        if (isDateField(field)) {
                            Date date = null;
                            if (cell != null) {
                                date = cell.getDateCellValue();
                                if (date == null) {
                                    validateValueRequired(headName, requiredMap.get(excelHead.entityName), sheetName, rowIndex, errors);
                                    continue;
                                }
                                method.invoke(instance, cell.getDateCellValue());
                            } else {
                                validateValueRequired(headName, requiredMap.get(excelHead.entityName), sheetName, rowIndex, errors);
                            }
                        } else {
                            String value = null;
                            if (cell != null) {
                                value = dataFormatter.formatCellValue(cell);
                                if (!StringUtils.hasText(value)) {
                                    validateValueRequired(headName, requiredMap.get(excelHead.entityName), sheetName, rowIndex, errors);
                                    continue;
                                }
                                Integer len = maxLengthMap.get(excelHead.entityName);
                                if (len != null && value.length() > len) {
                                    errors.add(ExcelImportError.data(sheetName, rowIndex, value, "“" + headName + "”\"字符最大长度为" + len));
                                    continue;
                                }
                                String regex = regexMap.get(excelHead.entityName);
                                if (StringUtils.hasText(regex) && !value.matches(regex)) {
                                    errors.add(ExcelImportError.data(sheetName, rowIndex, value, "“" + headName + "”\"格式错误"));
                                    continue;
                                }
                                method.invoke(instance, convertType(field.getType(), value.trim()));
                            } else {
                                validateValueRequired(headName, requiredMap.get(excelHead.entityName), sheetName, rowIndex, errors);
                            }
                        }
                    } else {
                        AssertUtil.throwMessage("excel文件表头名称错误");
                    }
                }
                beans.add(instance);
            }
        }

        checkAndWriteImportDataErrorInfo(errors);
        if (CollectionUtils.isEmpty(beans)) AssertUtil.throwMessage("导入数据为空");
        return beans;
    }

    /**
     * 是否日期字段
     *
     * @param field
     * @return
     */
    private static boolean isDateField(Field field) {
        return (Date.class == field.getType());
    }

    /**
     * 空值校验
     *
     * @param required
     * @throws Exception
     */
    private static void validateValueRequired(String headName, boolean required, String sheetName
            , int rowIndex, List<ExcelImportError> errors) {
        if (required)
            errors.add(ExcelImportError.data(sheetName, rowIndex, null, "“" + headName + "”\"不能为空"));
    }

    /**
     * 类型转换
     *
     * @param aClass
     * @param value
     * @return
     */
    private static Object convertType(Class<?> aClass, String value) {
        if (Integer.class == aClass || int.class == aClass) {
            return Integer.valueOf(value);
        }
        if (Short.class == aClass || short.class == aClass) {
            return Short.valueOf(value);
        }
        if (Byte.class == aClass || byte.class == aClass) {
            return Byte.valueOf(value);
        }
        if (Character.class == aClass || char.class == aClass) {
            return value.charAt(0);
        }
        if (Long.class == aClass || long.class == aClass) {
            return Long.valueOf(value);
        }
        if (Float.class == aClass || float.class == aClass) {
            return Float.valueOf(value);
        }
        if (Double.class == aClass || double.class == aClass) {
            return Double.valueOf(value);
        }
        if (Boolean.class == aClass || boolean.class == aClass) {
            return Boolean.valueOf(value.toLowerCase());
        }
        if (BigDecimal.class == aClass) {
            return new BigDecimal(value);
        }
        Class<?> superclass = aClass.getSuperclass();
        if (Enum.class == superclass) {
            Object[] enumConstants = aClass.getEnumConstants();
            for (Object enumConstant : enumConstants) {
                try {
                    Field field = enumConstant.getClass().getDeclaredField("text");
                    field.setAccessible(true);
                    String text = (String) field.get(enumConstant);
                    if (text.equalsIgnoreCase(value)) {
                        return enumConstant;
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return value;
    }

    /**
     * 获取properties的set和get方法
     */
    static class MethodUtils {
        private static final String SET_PREFIX = "set";
        private static final String GET_PREFIX = "get";

        private static String capitalize(String name) {
            if (name == null || name.isEmpty()) {
                return name;
            }
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }

        public static String setMethodName(String propertyName) {
            return SET_PREFIX + capitalize(propertyName);
        }

        public static String getMethodName(String propertyName) {
            return GET_PREFIX + capitalize(propertyName);
        }
    }

    @Data
    public static class Head {
        private String headName;                //  Excel名
        private String entityName;              //  实体类属性名
        private String regex;                   //  正则表达式
        private Boolean required = true;        //  值必填
        private Integer maxLength;              //  最大长度

        public Head(String headName, String entityName, boolean required, String regex) {
            this.headName = headName;
            this.entityName = entityName;
            this.required = required;
            this.regex = regex;
        }

        public Head(String headName, String entityName, boolean required) {
            this.headName = headName;
            this.entityName = entityName;
            this.required = required;
        }

        public Head(String headName, String entityName, boolean required, Integer maxLength) {
            this.headName = headName;
            this.entityName = entityName;
            this.required = required;
            this.maxLength = maxLength;
        }

        public Head(String headName, String entityName, String regex) {
            this.headName = headName;
            this.entityName = entityName;
            this.regex = regex;
        }

        public Head(String headName, String entityName) {
            this.headName = headName;
            this.entityName = entityName;
        }

        public Head(String headName, String entityName, Integer maxLength) {
            this.headName = headName;
            this.entityName = entityName;
            this.maxLength = maxLength;
        }
    }

    public static void dataImportTemplate(List<ExcelUtils.Head> heads, HttpServletResponse response) {
        try {
            for (ExcelUtils.Head head : heads) {
                if (head.getRequired())
                    head.setHeadName(head.getHeadName().concat("(必填)"));
            }
            List<String> titles = heads.stream().map(ExcelUtils.Head::getHeadName).toList();
            EasyExcelHelper.exportExcel(null, titles.toArray(new String[]{}), null, response, null);
        } catch (Exception e) {
            log.error("下载模板出现错误", e);
            AssertUtil.throwMessage("下载模板出现错误");
        }
    }

    public static void checkAndWriteImportDataErrorInfo(List<ExcelImportError> errors) {
        if (!CollectionUtils.isEmpty(errors)) {
            String id = CommonUtil.randomUUID16() + "_error";
            File tempDirectory = FileUtils.getTempDirectory();
            File filePath = new File(tempDirectory, id + ".xlsx");
            //生成本地文件
            EasyExcel.write(filePath.getAbsolutePath(), ExcelImportError.class).sheet("sheet1").doWrite(errors);
            AssertUtil.throwImportError(id);
        }
    }
}
