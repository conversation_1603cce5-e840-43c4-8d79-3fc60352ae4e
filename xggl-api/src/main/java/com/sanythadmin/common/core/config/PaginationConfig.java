package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "app.pagination")
public class PaginationConfig {
    
    /**
     * 默认分页大小
     */
    private int defaultPageSize = 20;
    
    /**
     * 最大分页大小
     */
    private int maxPageSize = 100;

    
    /**
     * 不同接口的特殊分页配置
     */
    private Map<String, PathPaginationConfig> pathConfigs = new HashMap<>();
    
    @Data
    public static class PathPaginationConfig {
        private String pathPattern;
        private int maxPageSize = 100;
        private int defaultPageSize = 20;
    }
    
    /**
     * 根据路径获取特定的分页配置
     */
    public PathPaginationConfig getPathConfig(String path) {
        return pathConfigs.entrySet().stream()
                .filter(entry -> path.matches(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取路径特定的最大分页大小
     */
    public int getMaxPageSizeForPath(String path) {
        PathPaginationConfig pathConfig = getPathConfig(path);
        return pathConfig != null ? pathConfig.getMaxPageSize() : maxPageSize;
    }
}