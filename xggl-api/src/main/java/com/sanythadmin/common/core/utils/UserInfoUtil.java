package com.sanythadmin.common.core.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.yulichang.toolkit.TableList;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.core.web.CodeCacheData;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserState;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.*;
import com.sanythadmin.common.system.param.MenuParam;
import com.sanythadmin.common.system.param.SysRoleMenuParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.common.system.service.RoleMenuService;
import com.sanythadmin.common.system.service.SysAccountRoleService;
import com.sanythadmin.common.system.service.SysMenuService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.project.code.entity.*;
import com.sanythadmin.project.code.param.*;
import com.sanythadmin.project.code.service.*;
import com.sanythadmin.project.userInfo.dto.DataScopeCache;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import com.sanythadmin.project.userInfo.vo.InstructorsVO;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UserInfoUtil {

    /**
     * 获取转换字段，如果使用此方法，请注意表别名一致
     *
     * @param userType 用户类型
     * @return {@link HashMap }<{@link String }, {@link String }>
     */
    public static HashMap<String, String> getConvertField(UserType userType) {
        HashMap<String, String> map = new HashMap<>();
        map.put("xymc", "a.name");
        map.put("zymc", "b.name");
        map.put("bjmc", "c.name");
        map.put("ryzt", "d.ztmc");
        if (UserType.STUDENT.equals(userType)) {
            map.put("pyccmc", "e.name");
            map.put("njmc", "f.name");
        }
        return map;
    }

    public static UserInfoParam convertUserInfoParam(UserInfo userInfo) {
        UserInfoParam param = new UserInfoParam();
        BeanUtils.copyProperties(userInfo, param);
        return param;
    }

    public static UserInfo convertUserInfo(UserInfoParam param) {
        UserInfo userInfo = new UserInfo();
        BeanUtils.copyProperties(param, userInfo);
        return userInfo;
    }

    public static CodeCacheData loadCodeCacheData() {
        CodeDwbService codeDwbService = SpringContextUtil.getBean(CodeDwbService.class);
        CodeZybService codeZybService = SpringContextUtil.getBean(CodeZybService.class);
        CodeBjbService codeBjbService = SpringContextUtil.getBean(CodeBjbService.class);
        CodeCommonService codeCommonService = SpringContextUtil.getBean(CodeCommonService.class);
        CodeXsztService codeXsztService = SpringContextUtil.getBean(CodeXsztService.class);
        Map<String, String> deptMap = toMap(codeDwbService.list(new CodeDwbParam()), CodeDwb::getId, CodeDwb::getName);
        Map<String, String> majorMap = toMap(codeZybService.list(new CodeZybParam()), CodeZyb::getId, CodeZyb::getName);
        Map<String, String> classMap = toMap(codeBjbService.list(new CodeBjbParam()), CodeBjb::getId, CodeBjb::getName);
        Map<String, String> codeCommonMap = toMap(codeCommonService.list(new CodeCommonParam("nj,pycc")), CodeCommon::getId, CodeCommon::getName);
        Map<String, String> stateMap = toMap(codeXsztService.list(new CodeXsztParam()), CodeXszt::getId, CodeXszt::getZtmc);
        return new CodeCacheData(deptMap, majorMap, classMap, codeCommonMap, stateMap);
    }

    public static final HashMap<String, String> codeFieldMap = new HashMap<>() {{
        put("pyccmc", "pyccid");
        put("njmc", "njid");
        put("bjmc", "bjid");
        put("zymc", "zyid");
        put("xymc", "xyid");
        put("ryzt", "ryztid");
    }};

    public static final HashMap<String, String> statFieldConvertMap = new HashMap<>() {{
        put("xyid", "xymc");
        put("zyid", "zymc");
        put("njid", "njmc");
        put("bjid", "bjmc");
        put("pyccid", "pyccmc");
        put("ryztid", "ryzt");
    }};

    public static Map<String, Map<String, String>> loadCodeDataMap(CodeCacheData codeCacheData) {
        if (codeCacheData == null) codeCacheData = loadCodeCacheData();
        CodeCacheData finalCodeCacheData = codeCacheData;
        return new HashMap<>() {{
            put("xymc", finalCodeCacheData.getDeptMap());
            put("zymc", finalCodeCacheData.getMajorMap());
            put("bjmc", finalCodeCacheData.getClassMap());
            put("njmc", finalCodeCacheData.getCodeCommonMap());
            put("pyccmc", finalCodeCacheData.getCodeCommonMap());
            put("ryzt", finalCodeCacheData.getStateMap());
        }};
    }

    /**
     * UserInfo 或其他业务对象包含 UserInfo
     *
     * @param object
     * @param <T>
     */
    public static <T> void codeTextSet(T object) {
        codeTextSet(Collections.singletonList(object), false);
    }

    public static <T> void codeTextSet(Collection<T> collection) {
        codeTextSet(collection, false);
    }

    /**
     * UserInfo 或其他业务对象包含 UserInfo
     *
     * @param collection
     * @param <T>
     */
    public static <T> void codeTextSet(Collection<T> collection, boolean setRole) {
        if (CollectionUtils.isEmpty(collection)) return;
        CodeCacheData cacheData = loadCodeCacheData();
        Map<String, Map<String, String>> codeDataMap = loadCodeDataMap(cacheData);
        List<String> list = codeFieldMap.keySet().stream().toList();
        String childObj = null;
        boolean flag = false;
        List<String> strings = new ArrayList<>();
        for (T obj : collection) {
            if (obj == null) continue;
            if (obj instanceof BaseUserInfo) {
                codeTextSet(obj, list, codeDataMap, setRole);
            } else {
                if (!StringUtils.hasText(childObj) && !flag) {
                    flag = true;
                    Field[] fields = CommonUtil.getAllFields(obj.getClass());
                    for (Field field : fields) {
                        if (codeFieldMap.containsKey(field.getName())) strings.add(field.getName());
                        Type genericType = field.getGenericType();
                        if (genericType.toString().contains("UserInfo")) {
                            childObj = field.getName();
                            break;
                        }
                    }
                }

                if (StringUtils.hasText(childObj)) {
                    Object object = ReflectUtils.invokeGetter(obj, childObj);
                    if (object != null) codeTextSet(object, list, codeDataMap, setRole);
                }

                codeTextSet(obj, strings, codeDataMap, setRole);
            }
        }
    }

    private static void codeTextSet(Object obj, List<String> strings, Map<String, Map<String, String>> codeDataMap, boolean setRole) {
        if (CollectionUtils.isEmpty(strings)) return;
        for (String string : strings) {
            Map<String, String> stringMap = codeDataMap.get(string);
            Object value = ReflectUtils.invokeGetter(obj, codeFieldMap.get(string));
            if (value != null) {
                String valueString = value.toString();
                String[] arrays = CommonUtil.split(valueString);
                String text = null;
                if (arrays.length > 1) {
                    List<String> list = new ArrayList<>();
                    for (String id : arrays) {
                        String temp_text = stringMap.get(id);
                        if (StringUtils.hasText(temp_text)) list.add(temp_text);
                    }
                    text = CommonUtil.strJoin(list);
                } else {
                    text = stringMap.get(valueString);
                }
                CommonUtil.setValue(obj, string, text);
            }
        }

        if (setRole) {
            Field[] fields = CommonUtil.getAllFields(obj.getClass());
            List<String> list = Arrays.stream(fields).map(Field::getName).toList();
            String xgh = "xgh";
            String roleId = "roleId";
            if (list.contains(roleId) && list.contains(xgh)) {
                Object username = CommonUtil.getValue(xgh, obj);
                if (username != null) {
                    SysAccountRoleService bean = SpringContextUtil.getBean(SysAccountRoleService.class);
                    List<SysAccountRole> roles = bean.list(new LambdaQueryWrapper<SysAccountRole>().eq(SysAccountRole::getUsername, String.valueOf(username)));
                    if (!CollectionUtils.isEmpty(roles)) {
                        List<String> stringList = roles.stream().map(SysAccountRole::getRoleId).toList();
                        CommonUtil.setValue(obj, roleId, CommonUtil.strJoin(stringList));
                    }
                }
            }
        }
    }

    public static <T, K, V> Map<K, V> toMap(List<T> list, Function<? super T, ? extends K> keyMapper, Function<? super T, ? extends V> valueMapper) {
        if (CollectionUtils.isEmpty(list)) return Collections.emptyMap();
        return list.stream().collect(Collectors.toMap(keyMapper, valueMapper));
    }

    /**
     * 构建业务表与用户表通用查询 wrapper
     *
     * @param userState     查询的用户群体
     * @param objectParam   业务表参数对象
     * @param joinField     业务表学工号字段SFunction
     * @param userInfoParam 用户表参数对象
     * @param userType      查询的用户类型
     * @param <T>           业务表对应实体对象类型
     * @param <U>           业务表对应参数对象类型
     */
    public static <T, U extends BaseParam> MyMPJLambdaWrapper<T, U> buildInnerJoinUserInfoWrapper(UserState userState, U objectParam, @NotNull SFunction<T, String> joinField, UserInfoParam userInfoParam, UserType... userType) {
        Map<Class<?>, BaseParam> paramMap = new HashMap<>();
        Class<UserInfo> userInfoClass = UserInfo.class;
        Class<UserOrgMap> orgMapClass = UserOrgMap.class;

        MyMPJLambdaWrapper<T, U> wrapper = new MyMPJLambdaWrapper<>(objectParam);
        wrapper.innerJoin(userInfoClass, UserInfo::getXgh, joinField)
                .leftJoin(orgMapClass, UserOrgMap::getXgh, UserInfo::getXgh);
        UserInfoParam newUserInfoParam = new UserInfoParam();
        if (!Objects.isNull(userInfoParam)) {
            BeanUtils.copyProperties(userInfoParam, newUserInfoParam);
            UserOrgMapParam orgMapParam = new UserOrgMapParam();
            BeanUtils.copyProperties(userInfoParam, orgMapParam);
            paramMap.put(orgMapClass, orgMapParam);
        }
        if (!Objects.isNull(userType) && userType.length > 0) {
            List<UserType> list = Arrays.asList(userType);
            newUserInfoParam.setUserType(null);
            if (!list.contains(UserType.STUDENT)) {
                wrapper.in(UserInfo::getUserType, list);
            } else {
                wrapper.innerJoin(CodeXszt.class, CodeXszt::getId, UserInfo::getRyztid);
                List<UserType> otherUserType = list.stream().filter(s -> !s.equals(UserType.STUDENT)).toList();
                if (userState != null && !CollectionUtils.isEmpty(otherUserType)) {
                    wrapper.and(w -> w.in(UserInfo::getUserType, otherUserType).or(w1 -> appendChild(w1, userState)));
                } else {
                    appendChild(wrapper, Objects.requireNonNullElse(userState, UserState.zaiXiaoSheng));
                }
            }
        }

        paramMap.put(userInfoClass, newUserInfoParam);
        List<TableList.Node> nodeList = wrapper.getTableList().getAll();
        nodeList.forEach(node -> {
            String alias = wrapper.getAlias() + node.getIndex();
            BaseParam baseParam = paramMap.get(node.getClazz());
            if (!Objects.isNull(baseParam)) wrapper.buildQueryCondition(alias, baseParam);
            wrapper.setTableAlias(node.getClazz(), alias);
        });
        return wrapper;
    }

    public static <T> void appendChild(MPJLambdaWrapper<T> child, UserState userState) {
        if (Objects.equals(userState, UserState.zaiXiaoSheng)) {
            child.eq(UserInfo::getUserType, UserType.STUDENT).eq(CodeXszt::getSfby, JudgeMark.NO).eq(CodeXszt::getSfzx, JudgeMark.YES);
        } else if (Objects.equals(userState, UserState.biYeSheng)) {
            child.eq(UserInfo::getUserType, UserType.STUDENT).eq(CodeXszt::getSfby, JudgeMark.YES);
        }
    }

    public static List<Menu> listMenuByRole(String roleId) {
        SysMenuService menuService = SpringContextUtil.getBean(SysMenuService.class);
        RoleMenuService roleMenuService = SpringContextUtil.getBean(RoleMenuService.class);
        List<Menu> menus = menuService.list(new MenuParam());
        List<RoleMenu> roleMenus = roleMenuService.list(new SysRoleMenuParam(roleId));

        if (CollectionUtils.isEmpty(roleMenus) || CollectionUtils.isEmpty(menus)) return null;
        List<String> menuIds = roleMenus.stream().filter((d) -> d.getRoleId().equals(roleId)).map(RoleMenu::getMenuId).toList();
        if (CollectionUtils.isEmpty(menuIds)) return null;
        List<Menu> menuList = menus.stream().filter((d) -> menuIds.contains(d.getMenuId()) && d.getDeleted() == 0).sorted(Comparator.comparing(Menu::getSortNumber)).toList();
        return new ArrayList<>(menuList);
    }

    public static String buildInitPassword(@NotNull SysAccount account) {
        SysParamService sysParamService = SpringContextUtil.getBean(SysParamService.class);
        String paramValue = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
        return buildInitPassword(account, paramValue);
    }

    public static String buildInitPassword(@NotNull SysAccount account, String passwordPattern) {
        String value = account.getUsername();
        String idCode = account.getIdCode();
        String telMobile = account.getTelMobile();
        int len = 6;
        if (StringUtils.hasText(idCode) && idCode.length() >= len) {
            value = idCode.substring(idCode.length() - len);
        } else if (StringUtils.hasText(telMobile) && idCode.length() >= len) {
            value = telMobile.substring(telMobile.length() - len);
        }
        return StringUtils.hasText(passwordPattern) ? MessageFormat.format(passwordPattern, value) : value;
    }

    public static String encodePassword(String password, BCryptPasswordEncoder encoder) {
        return encoder.encode(password);
    }

    public static String encodePassword(String password) {
        BCryptPasswordEncoder encoder = SpringContextUtil.getBean(BCryptPasswordEncoder.class);
        return encodePassword(password, encoder);
    }

    public static boolean comparePassword(String inputPassword, String dbPassword) {
        BCryptPasswordEncoder encoder = SpringContextUtil.getBean(BCryptPasswordEncoder.class);
        return encoder.matches(inputPassword, dbPassword);
    }

    public static List<InstructorsVO> instructorsMatchAndSet(List<InstructorsVO> instructorsVOS, BaseUserInfo user, String roleScope) {
        List<InstructorsVO> list = new ArrayList<>();
        for (InstructorsVO vo : instructorsVOS) {
            if (Objects.equals(vo.getXgh(), user.getXgh())) {
                list.add(vo);
                continue;
            }

            if (vo.getXgh() != null) continue;

            boolean bjEmpty = !StringUtils.hasText(vo.getBjid());
            boolean zyEmpty = !StringUtils.hasText(vo.getZyid());
            boolean xyEmpty = !StringUtils.hasText(vo.getXyid());
            boolean njEmpty = !StringUtils.hasText(vo.getNjid());

            if (!bjEmpty && Objects.equals(vo.getBjid(), user.getBjid())) {
                list.add(vo);
                continue;
            }
            if (bjEmpty && !zyEmpty && Objects.equals(vo.getZyid(), user.getZyid())) {
                list.add(vo);
                continue;
            }
            if (bjEmpty && zyEmpty && !xyEmpty && Objects.equals(vo.getXyid(), user.getXyid())) {
                list.add(vo);
                continue;
            }
            if (bjEmpty && zyEmpty && xyEmpty && !njEmpty && Objects.equals(vo.getNjid(), user.getNjid())) {
                list.add(vo);
                continue;
            }
            if (bjEmpty && zyEmpty && xyEmpty && njEmpty &&
                    Objects.equals(vo.getPyccid(), user.getPyccid()))
                list.add(vo);
        }
        return list;
    }

    public static <T> void setQueryScope(T param) {
        setQueryScope(param, "xyid", "bjid");
    }

    public static <T> void setQueryScope(T param, String xyid, String bjid) {
        if (param != null) {
            SysRole role = SecurityUtil.getRole();
            String username = SecurityUtil.getUsername();
            if (!Constants.ROLE_SCOPE_QX.equals(role.getRoleScope())) {
                if (Constants.ROLE_SCOPE_BY.equals(role.getRoleScope()) || Constants.ROLE_SCOPE_BJ.equals(role.getRoleScope())) {
                    UserDataScopeService userDataScopeService = SpringContextUtil.getBean(UserDataScopeService.class);
                    DataScopeCache scopeCache = userDataScopeService.get(username, role.getId());
                    UserDataScope userDataScope = scopeCache.getDataScope();
                    Field field = CommonUtil.getField(param.getClass(), xyid);
                    String str = userDataScope != null ? userDataScope.getXyid() : scopeCache.getDeptId();
                    if (field != null) {
                        boolean set = true;
                        String value = (String) CommonUtil.getValue(xyid, param);
                        if (StringUtils.hasText(value))
                            set = !Arrays.asList(CommonUtil.split(str)).contains(value);
                        if (set)
                            CommonUtil.setValue(param, field, str);
                    }
                }
            }
        }
    }
}
