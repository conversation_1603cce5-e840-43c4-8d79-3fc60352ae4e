package com.sanythadmin.common.core.web;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.config.PaginationConfig;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询参数基本字段
 *
 * <AUTHOR>
 * @since 2021-08-26 22:14:43
 */
@Data
public class BaseParam implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final long DEFAULT_LIMIT = 20L;
    public static final long MAX_LIMIT = 100L;

    /**
     * 分页查询页码
     */
    @TableField(exist = false)
    private Long page;

    /**
     * 分页查询每页数量
     */
    @TableField(exist = false)
    private Long limit;

    /**
     * 是否为内部调用（用于区分前端传参和代码内部调用）
     * 内部调用时不受分页大小限制
     */
    @TableField(exist = false)
    private Boolean internalCall = false;
    /**
     * 排序字段, 排序字段或sql, 如果是sql则order字段无用, 如: `id asc, name desc`
     */
    @TableField(exist = false)
    private String sort;

    /**
     * 排序方式, sort是字段名称时对应的排序方式, asc升序, desc降序
     */
    @TableField(exist = false)
    private String order;

    /**
     * 创建时间起始值
     */
    @QueryField(value = "create_time", type = QueryType.GE)
    @TableField(exist = false)
    private String createTimeStart;
    /**
     * 创建时间结束值
     */
    @QueryField(value = "create_time", type = QueryType.LE)
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * 获取集合中的第一条数据
     *
     * @param records 集合
     * @return 第一条数据
     */
    public <T> T getOne(List<T> records) {
        return CommonUtil.listGetOne(records);
    }

    public Long getPage() {
        if (page == null || page < 1) {
            return 1L;
        }
        return page;
    }

    public Long getLimit() {
        long requestedLimit = (limit == null || limit <= 0) ? DEFAULT_LIMIT : limit;

        // 如果是内部调用，直接返回请求的限制，不进行大小检查
        if (Boolean.TRUE.equals(internalCall)) {
            return requestedLimit;
        }

        // 前端调用时进行分页大小限制检查
        PaginationConfig paginationConfig = SpringContextUtil.getBean(PaginationConfig.class);
        long maxLimit = paginationConfig == null ? MAX_LIMIT : paginationConfig.getMaxPageSize();
        if (requestedLimit > maxLimit) {
            throw new IllegalArgumentException("分页大小不能超过 " + maxLimit);
        }
        return requestedLimit;
    }

    /**
     * 设置为内部调用（用于代码内部批量操作、导出等场景）
     * @return this
     */
    public BaseParam setInternalCall() {
        this.internalCall = true;
        return this;
    }


}
