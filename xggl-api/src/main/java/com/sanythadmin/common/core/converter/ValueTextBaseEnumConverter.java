package com.sanythadmin.common.core.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public class ValueTextBaseEnumConverter implements Converter<ValueTextBaseEnum> {

    @Override
    public Class<ValueTextBaseEnum> supportJavaTypeKey() {
        return ValueTextBaseEnum.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(ValueTextBaseEnum value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(value != null ? value.getText() : "");
    }
}