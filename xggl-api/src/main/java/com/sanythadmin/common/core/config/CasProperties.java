package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @since 2025/6/19.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cas")
public class CasProperties {
    private String serverLoginUrl;
    private String serverValidateUrl;
    private String serviceUrl;
    private String serverLogoutUrl;
    private String usernameAttribute;
}
