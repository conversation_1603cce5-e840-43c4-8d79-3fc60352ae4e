package com.sanythadmin.common.core.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.security.JwtSubject;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.service.SysJwtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * JWT令牌生成API - 仅用于开发环境
 * 只在 dev、test、loc 环境下生效，生产环境不会加载此控制器
 */
@RestController
@RequestMapping("/nonlogin/dev-tools")
@Profile({"dev"})
public class TokenGeneratorController {

    @Autowired
    private SysJwtService sysJwtService;
    
    @Autowired
    private SysRoleMapper sysRoleMapper;
    
    /**
     * 生成JWT令牌
     * @param role 角色名称
     * @param username 用户名
     * @param days 有效期(天)
     * @return JWT令牌
     */
    @GetMapping("/generate-token")
    public Map<String, String> generateToken(
            @RequestParam(defaultValue = "学生处") String role,
            @RequestParam(defaultValue = "admin") String username,
            @RequestParam(defaultValue = "365") Integer days) {
        
        // 根据角色名称查询角色ID
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getName, role));
        
        if (sysRole == null) {
            throw new IllegalArgumentException("未知角色: " + role);
        }
        
        String roleId = sysRole.getId();
        
        // 从服务中获取JWT配置
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        if (config == null) {
            throw new RuntimeException("未找到JWT配置");
        }
        
        Long expireTime = 60 * 60 * 24L * days;
        String accessToken = JwtUtil.buildToken(
                JwtSubject.genCacheKey(username, roleId), 
                expireTime, 
                config.getTokenKey()
        );
        
        Map<String, String> result = new HashMap<>();
        result.put("token", "Bearer " + accessToken);
        return result;
    }
    
    /**
     * 获取所有可用角色
     * @return 角色列表
     */
    @GetMapping("/available-roles")
    public Map<String, String> getAvailableRoles() {
        // 从数据库获取所有角色
        List<SysRole> roles = sysRoleMapper.selectList(new LambdaQueryWrapper<>());
        
        // 转换为Map<角色名称, 角色ID>
        return roles.stream()
                .collect(Collectors.toMap(
                        SysRole::getName,
                        SysRole::getId,
                        (existing, replacement) -> existing // 如果有重复的键，保留第一个
                ));
    }

}