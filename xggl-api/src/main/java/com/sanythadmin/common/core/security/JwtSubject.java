package com.sanythadmin.common.core.security;

import com.sanythadmin.common.core.utils.DateUtil;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Jwt载体
 *
 * <AUTHOR>
 * @since 2021-09-03 00:11:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JwtSubject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 缓存key
     */
    private String cacheKey;
    /**
     * 角色ID
     */
    private String roleId;

    private static final String LOGIN_PREFIX = "login:";
    private static final String LOGOUT_PREFIX = "logout:";

    public String getUsername() {
        if (!StringUtils.hasText(cacheKey))
            return null;
        Pattern pattern = Pattern.compile(LOGIN_PREFIX + "(.*):[^:]*$");
        Matcher matcher = pattern.matcher(cacheKey);
        return matcher.find() ? matcher.group(1) : null;
    }

    public static JwtSubject genCacheKey(String username, String roleId) {
        String key = LOGIN_PREFIX + username + ":" + DateUtil.getDateTime(DateUtil.DEFAULT_DATEMS_FORMAT);
        return new JwtSubject(key, roleId);
    }

    public static Claims parseClaims(String tokenKey, HttpServletRequest request) {
        String accessToken = JwtUtil.getAccessToken(request);
        return JwtUtil.parseToken(accessToken, tokenKey);
    }

    public String logoutCacheKey() {
        return cacheKey.replaceFirst(LOGIN_PREFIX, LOGOUT_PREFIX);
    }
}
