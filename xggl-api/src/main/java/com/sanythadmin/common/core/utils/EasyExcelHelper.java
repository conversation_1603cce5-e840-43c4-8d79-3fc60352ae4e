package com.sanythadmin.common.core.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.RowData;
import com.sanythadmin.common.core.web.SheetData;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class EasyExcelHelper<T> {
    private Class<T> tClass;
    private Field[] fields;

    public EasyExcelHelper(Class<T> tClass) {
        this.tClass = tClass;
    }

    public EasyExcelHelper() {
        Type type = getClass().getGenericSuperclass();
        Type trueType = ((ParameterizedType) type).getActualTypeArguments()[0];
        this.tClass = (Class<T>) trueType;
    }

    /**
     * 输出excel文件
     *
     * @param fileName
     * @param data
     * @param response
     */
    public void out(String fileName, Collection<T> data, HttpServletResponse response) {
        try {
            if (!StringUtils.hasLength(fileName))
                fileName = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), tClass).excelType(ExcelTypeEnum.XLSX).sheet("sheet1").doWrite(data);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public void outError(Collection<T> data, HttpServletResponse response) {
        out("错误描述", data, response);
    }

    /**
     * 读取excel
     *
     * @param inputStream
     * @return
     */
    public List<T> read(InputStream inputStream) {
        List<T> list = new ArrayList<>();
        EasyExcelFactory.read(inputStream, tClass, new ReadListener<T>() {
            @Override
            public void invoke(T data, AnalysisContext analysisContext) {
                if (fields == null) fields = CommonUtil.getAllFields(data.getClass());
                for (Field field : fields) {
                    if (field.getType() == String.class) {
                        try {
                            field.setAccessible(true);
                            Object val = field.get(data);
                            if (val != null) {
                                val = String.valueOf(val).trim();
                                field.set(data, val);
                            }
                        } catch (IllegalAccessException e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
                list.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                log.info("done");
            }
        }).sheet().doRead();
        return list;
    }

    public static SheetData read(MultipartFile file) throws IOException {
        SheetData data = new SheetData();
        EasyExcelFactory.read(file.getInputStream(), new ReadListener<Map<Integer, String>>() {
            @Override
            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                for (Integer key : headMap.keySet()) {
                    ReadCellData<?> cellData = headMap.get(key);
                    String value = cellData.getStringValue();
                    data.getHeader().put(key, value.trim());
                }
            }

            @Override
            public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
                Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
                HashMap<Integer, String> newDataMap = new HashMap<>();
                for (Integer key : dataMap.keySet()) {
                    String value = dataMap.get(key);
                    newDataMap.put(key, StringUtils.hasLength(value) ? value.trim() : value);
                }
                data.getRowData().add(new RowData(rowIndex, newDataMap));
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                System.out.println("done");
            }
        }).sheet().doRead();
        return data;
    }


    /**
     * 下载错误信息excel
     *
     * @param id
     * @param response
     */
    public void outErrorInfoExcel(String id, HttpServletResponse response) {
        try {
            File tempDirectory = FileUtils.getTempDirectory();
            File file = new File(tempDirectory, id + ".xlsx");
            response.reset();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + id + ".xlsx");
            IOUtils.copy(new FileInputStream(file), response.getOutputStream());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public void importFileEmptyCheck(List<T> data) {
        if (CollectionUtils.isEmpty(data))
            AssertUtil.throwMessage("导入失败, 文件不能为空");
    }

    /**
     * 导入错误信息生成本地临时文件
     *
     * @param errors
     * @return
     */
    public void writeImportDataErrorInfo(List<ExcelImportError> errors) {
        ExcelUtils.checkAndWriteImportDataErrorInfo(errors);
    }

    /**
     * 数据导入文件为空或者未获取到数据
     *
     * @param
     * @return
     */
    public ApiResult<String> importFileEmptyError() {
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, "导入失败, 文件不能为空", null);
    }

    /**
     * 导入数据错误
     *
     * @param
     * @return
     */
    public ApiResult<String> importDataError(List<ExcelImportError> errors) {
        String string = CommonUtil.writeErrorInfoExcel(errors);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE_PROCESS_DATA, "导入失败, 请下载错误信息", string);
    }


    /**
     * 多sheet数据导出
     *
     * @param dataMap
     * @param titles
     * @param property
     * @param response
     * @param dateFormatPattern
     * @param fileName
     * @throws Exception
     */
    public static <T> void exportExcel(Map<String, List<T>> dataMap, List<String[]> titles, List<String[]> property,
                                       HttpServletResponse response, String dateFormatPattern, String fileName)
            throws Exception {
        Workbook wb = new XSSFWorkbook();
        int index = 0;
        for (String sheetName : dataMap.keySet()) {
            createSheetAndWriteData(sheetName, wb, dataMap.get(sheetName), titles.get(index), property.get(index), dateFormatPattern);
            index++;
        }
        output(response, wb, "xlsx", fileName);
    }

    public static <T> void exportExcel(List<T> records, String[] titles, String[] property, HttpServletResponse response, String dateFormatPattern, String fileName)
            throws Exception {
        Workbook wb = new XSSFWorkbook();
        createSheetAndWriteData("sheet1", wb, records, titles, property, dateFormatPattern);
        output(response, wb, "xlsx", fileName);
    }

    public static <T> void exportExcel(List<T> records, String[] titles, String[] property, HttpServletResponse response, String dateFormatPattern)
            throws Exception {
        export(records, titles, property, response, dateFormatPattern);
    }

    public static void exportExcel(OutputStream outputStream, String sheetName, List<List<String>> headers, List<List<Object>> data) {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        EasyExcel.write(outputStream)
                .head(headers)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .sheet(sheetName)
                .doWrite(data);
    }

    private static <T> void export(List<T> records, String[] titles, String[] property, HttpServletResponse response, String dateFormatPattern) throws Exception {
        Workbook wb = new XSSFWorkbook();
        createSheetAndWriteData("sheet1", wb, records, titles, property, dateFormatPattern);
        output(response, wb, "xlsx", null);
    }

    private static void output(HttpServletResponse response, Workbook wb, String suffix, String fileName) throws IOException {
        if (!StringUtils.hasLength(suffix))
            suffix = "xls";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        Date d = new Date(System.currentTimeMillis());
        if (!StringUtils.hasLength(fileName)) {
            fileName = sdf.format(d);
        } else {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        }

        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + "." + suffix);
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        OutputStream out = response.getOutputStream();
        wb.write(out);
        out.flush();
        out.close();
    }

    private static <T> void createSheetAndWriteData(String sheetName, Workbook wb, List<T> records, String[] titles,
                                                    String[] property, String dateFormatPattern) throws Exception {
        Sheet sheet = wb.createSheet(sheetName);//创建sheet
        // 创建单元格样式
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND); // 设置前景填充样式

        // 创建字体样式
        Font font = wb.createFont();
        font.setBold(true); // 加粗
        font.setFontHeightInPoints((short) 14);
        font.setFontName("宋体");
        style.setFont(font);

        Row row = sheet.createRow(0);//创建标题
        for (int i = 0; i < titles.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellStyle(style);
            cell.setCellValue(titles[i]);
            sheet.autoSizeColumn(i);
        }

        int i = 1;
        if (!CollectionUtils.isEmpty(records)) {
            Field[] fields = null;
            Object temp = records.get(0);
            if (!(temp instanceof Map))
                fields = CommonUtil.getAllFields(temp.getClass());
            for (Object object : records) {
                Row dataRow = sheet.createRow(i);
                int j = 0;
                for (String p : property) {
                    Cell cell = dataRow.createCell(j);
                    String val = null;
                    if (object instanceof Map) {
                        Map<String, Object> newObj = (Map<String, Object>) object;
                        if (newObj.get(p) instanceof Date) {
                            val = DateUtil.format(((Date) newObj.get(p)), dateFormatPattern);
                        } else if (newObj.get(p) instanceof Timestamp) {
                            val = DateUtil.format(new Date(((Timestamp) newObj.get(p)).getTime()), dateFormatPattern);
                        } else {
                            val = getStringVal(object, p);
                        }
                    } else {
                        if (p.indexOf(".") != -1) {
                            String[] strings = p.split("\\.");
                            Object tempObj = object;
                            for (int k = 0; k < strings.length - 1; k++) {
                                Field field = tempObj.getClass().getDeclaredField(strings[k]);
                                field.setAccessible(true);
                                tempObj = field.get(tempObj);
                                if (tempObj == null) break;
                            }
                            if (tempObj != null)
                                val = getStringVal(tempObj, strings[strings.length - 1]);
                        } else {
                            Field field = CommonUtil.getField(fields, p);
                            field.setAccessible(true);
                            Object o = field.get(object);
                            if (field.getGenericType().toString().indexOf("java.util.Date") != -1) {
                                val = DateUtil.format((Date) o, dateFormatPattern);
                            } else if (o instanceof ValueTextBaseEnum) {
                                ValueTextBaseEnum valueTextBaseEnum = (ValueTextBaseEnum) o;
                                val = valueTextBaseEnum.getText();
                            } else {
                                val = getStringVal(object, p);
                            }
                        }
                    }
                    cell.setCellValue(val);
                    j++;
                }
                i++;
            }
        }
    }

    private static String getStringVal(Object bean, String fieldName) {
        Object fieldValue = BeanUtil.getFieldValue(bean, fieldName);
        return fieldValue == null ? "" : String.valueOf(fieldValue);
    }

    public static <T> void exportExcel(String fileName, List<T> records, String[] titles, String[] property, HttpServletResponse response)
            throws Exception {
        Workbook wb = new XSSFWorkbook();
        createSheetAndWriteData("sheet1", wb, records, titles, property, "yyyyMMdd HH:mm:ss");
        output(response, wb, "xlsx", fileName);
    }

    public static <T> void writeApproveErrorInfo(List<T> records, String[] titles, String[] property)
            throws Exception {
        Workbook workbook = new XSSFWorkbook();
        createSheetAndWriteData("sheet1", workbook, records, titles, property, "yyyyMMdd HH:mm:ss");
        String id = CommonUtil.randomUUID16() + "_error";
        File tempDirectory = FileUtils.getTempDirectory();
        File filePath = new File(tempDirectory, id + ".xlsx");
        FileOutputStream fos = new FileOutputStream(filePath);
        workbook.write(fos);
        AssertUtil.throwApproveError(id);
    }
}
