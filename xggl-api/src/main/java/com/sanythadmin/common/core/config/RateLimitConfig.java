package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "security.rate-limit")
public class RateLimitConfig {
    
    // 是否启用全局限流
    private boolean enabled = true;
    
    // 全局限流窗口（分钟）
    private int globalWindowMinutes = 1;
    
    // 全局窗口内最大请求数
    private int globalMaxRequests = 60;
    
    // 排除的路径
    private List<String> excludePaths = new ArrayList<>();
    
    // 特定路径的限流规则
    private List<PathRule> pathRules = new ArrayList<>();
    
    @Data
    public static class PathRule {
        // 路径模式
        private String pathPattern;
        
        // 窗口时间（分钟）
        private int windowMinutes = 1;
        
        // 窗口内最大请求数
        private int maxRequests = 60;
    }
}