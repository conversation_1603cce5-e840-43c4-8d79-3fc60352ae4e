package com.sanythadmin.common.core.config.enumConverterFactory;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;


public class StringToTextBaseEnumConverter<T extends TextBaseEnum> implements Converter<String, T> {
    
    private final Class<? extends T> enumType;
    
    public StringToTextBaseEnumConverter(Class<? extends T> enumType) {
        this.enumType = enumType;
    }
    
    @Override
    public T convert(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        
        T[] enumConstants = enumType.getEnumConstants();
        for (T constant : enumConstants) {
            if (constant.toString().equals(source) || 
                constant.getText().equals(source)) {
                return constant;
            }
        }
        throw new IllegalArgumentException("No enum constant " + enumType.getCanonicalName() + "." + source);
    }
}
