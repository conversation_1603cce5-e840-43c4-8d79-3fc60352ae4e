package com.sanythadmin.common.core.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> href="<EMAIL>">lth</a>
 * @version 2.0
 * @date Jul 25, 2013
 */
public class DateUtil {

    /**
     * 缺省的日期显示格式： yyyy-MM-dd
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_DATE_FORMAT_HOURS_MINUTES = "yyyy-MM-dd HH:mm";

    /**
     * 缺省的日期显示格式： yyyyMMddHHmmss
     */
    public static final String DEFAULT_DATEMS_FORMAT = "yyyyMMddHHmmss";

    /**
     * 缺省的日期时间显示格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";
    public static final String DEFAULT_HOURS_MINUTES_FORMAT = "HH:mm";

    private DateUtil() {
    }

    /**
     * 取得系统当前时间
     *
     * @return
     */
    public static Date getNowDate() {
        return Calendar.getInstance().getTime();
    }

    /**
     * 得到用缺省方式格式化的当前日期
     *
     * @return 当前日期
     */
    public static String getDate() {
        return getDateTime(DEFAULT_DATE_FORMAT);
    }

    public static Date getDate(String dateStr, String pattern) {
        Date cuurDate = null;
        try {
            if (!StringUtils.hasText(pattern))
                pattern = DEFAULT_DATE_FORMAT;
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            cuurDate = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return cuurDate;
    }

    /**
     * 得到用缺省方式格式化的当前日期及时间
     *
     * @return 当前日期及时间
     */
    public static String getDateTime() {
        return getDateTime(DEFAULT_DATETIME_FORMAT);
    }

    /**
     * 得到系统当前日期及时间，并用指定的方式格式化
     *
     * @param pattern 显示格式
     * @return 当前日期及时间
     */
    public static String getDateTime(String pattern) {
        Date datetime = Calendar.getInstance().getTime();
        return getDateTime(datetime, pattern);
    }

    /**
     * 得到用指定方式格式化的日期
     *
     * @param date    需要进行格式化的日期
     * @param pattern 显示格式
     * @return 日期时间字符串
     */
    public static String getDateTime(Date date, String pattern) {
        if (null == pattern || "".equals(pattern)) {
            pattern = DEFAULT_DATETIME_FORMAT;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    public static String getMSDateTime(Date date, String pattern) {
        if (null == pattern || "".equals(pattern)) {
            pattern = DEFAULT_DATEMS_FORMAT;
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

    /**
     * 得到当前年份
     *
     * @return 当前年份
     */
    public static int getCurrentYear() {
        return Calendar.getInstance().get(Calendar.YEAR);
    }

    /**
     * 得到当前月份
     *
     * @return 当前月份
     */
    public static int getCurrentMonth() {
        //用get得到的月份数比实际的小1，需要加上
        return Calendar.getInstance().get(Calendar.MONTH) + 1;
    }

    /**
     * 得到当前日
     *
     * @return 当前日
     */
    public static int getCurrentDay() {
        return Calendar.getInstance().get(Calendar.DATE);
    }

    /**
     * 取得当前日期以后若干天的日期。如果要得到以前的日期，参数用负数。 例如要得到上星期同一天的日期，参数则为-7
     *
     * @param days 增加的日期数
     * @return 增加以后的日期
     */
    public static Date addDays(int days) {
        return add(getNowDate(), days, Calendar.DATE);
    }

    /**
     * 取得指定日期以后若干天的日期。如果要得到以前的日期，参数用负数。
     *
     * @param date 基准日期
     * @param days 增加的日期数
     * @return 增加以后的日期
     */
    public static Date addDays(Date date, int days) {
        return add(date, days, Calendar.DATE);
    }

    /**
     * 取得当前日期以后某月的日期。如果要得到以前月份的日期，参数用负数。
     *
     * @param months 增加的月份数
     * @return 增加以后的日期
     */
    public static Date addMonths(int months) {
        return add(getNowDate(), months, Calendar.MONTH);
    }

    /**
     * 取得指定日期以后某月的日期。如果要得到以前月份的日期，参数用负数。 注意，可能不是同一日子，例如2003-1-31加上一个月是2003-2-28
     *
     * @param date   基准日期
     * @param months 增加的月份数
     * @return 增加以后的日期
     */
    public static Date addMonths(Date date, int months) {
        return add(date, months, Calendar.MONTH);
    }

    /**
     * 内部方法。为指定日期增加相应的天数或月数
     *
     * @param date   基准日期
     * @param amount 增加的数量
     * @param field  增加的单位，年，月或者日
     * @return 增加以后的日期
     */
    private static Date add(Date date, int amount, int field) {
        Calendar calendar = Calendar.getInstance();

        calendar.setTime(date);
        calendar.add(field, amount);

        return calendar.getTime();
    }

    /**
     * 计算两个日期相差天数。 用第一个日期减去第二个。如果前一个日期小于后一个日期，则返回负数
     *
     * @param one 第一个日期数，作为基准
     * @param two 第二个日期数，作为比较
     * @return 两个日期相差天数
     */
    public static long diffDays(Date one, Date two) {
        return (one.getTime() - two.getTime()) / (24 * 60 * 60 * 1000);
    }

    /**
     * 计算两个日期相差月份数 如果前一个日期小于后一个日期，则返回负数
     *
     * @param one 第一个日期数，作为基准
     * @param two 第二个日期数，作为比较
     * @return 两个日期相差月份数
     */
    public static int diffMonths(Date one, Date two) {

        Calendar calendar = Calendar.getInstance();

        //得到第一个日期的年分和月份数
        calendar.setTime(one);
        int yearOne = calendar.get(Calendar.YEAR);
        int monthOne = calendar.get(Calendar.MONDAY);

        //得到第二个日期的年份和月份
        calendar.setTime(two);
        int yearTwo = calendar.get(Calendar.YEAR);
        int monthTwo = calendar.get(Calendar.MONDAY);

        return (yearOne - yearTwo) * 12 + (monthOne - monthTwo);
    }

    public static LocalDate parseLocalDate(String datestr) {
        return parseLocalDate(datestr, DEFAULT_DATE_FORMAT);
    }

    public static LocalDate parseLocalDate(String datestr, String pattern) {
        return LocalDate.parse(datestr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 将一个字符串用给定的格式转换为日期类型。 <br>
     * 注意：如果返回null，则表示解析失败
     *
     * @param datestr 需要解析的日期字符串
     * @param pattern 日期字符串的格式，默认为“yyyy-MM-dd”的形式
     * @return 解析后的日期
     */
    public static Date parse(String datestr, String pattern) {
        Date date = null;

        if (null == pattern || "".equals(pattern)) {
            pattern = DEFAULT_DATE_FORMAT;
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
            date = dateFormat.parse(datestr);
        } catch (ParseException e) {
            //
        }

        return date;
    }

    /**
     * 返回给定日期中的月份中的最后一天
     *
     * @param date 基准日期
     * @return 该月最后一天的日期
     */
    public static Date getMonthLastDay(Date date) {

        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        //将日期设置为下一月第一天
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1, 1);

        //减去1天，得到的即本月的最后一天
        calendar.add(Calendar.DATE, -1);

        return calendar.getTime();
    }

    /**
     * 获得某年某月第一天
     *
     * @author: pgf
     * @creation: Apr 30, 2014 12:39:33 PM
     * @return: Date
     */
    public static Date getMonthFirstDay(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        Date firstDate = calendar.getTime();
        return firstDate;
    }

    /**
     * 获得某年某月最后一天
     *
     * @param year  年
     * @param month 月
     * @author: pgf
     * @creation: Apr 30, 2014 12:39:33 PM
     * @return: Date 日期
     */
    public static Date getMonthLastDay(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date lastDate = calendar.getTime();
        return lastDate;
    }

    /**
     * 获取当前日期是星期几
     * add by yfx 20180201
     *
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    /**
     * 获取当前日期是星期几
     * add by yfx 20180201
     *
     * @param currTime
     * @return 当前日期是星期几
     */
    public static int dayForWeek(String currTime) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.setTime(format.parse(currTime));
        int dayForWeek = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == 1) {
            dayForWeek = 7;
        } else {
            dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
        }
        return dayForWeek;
    }

    /*
     *只做时间比较,比较dateTime在time之前
     */
    public static boolean isBeforeTime(Date dateTime, String time) {
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        Calendar curTime = Calendar.getInstance();
        Calendar scaTime = Calendar.getInstance();
        try {
            Date timeScale = df.parse(time);
            curTime.setTime(df.parse(df.format(dateTime)));
            scaTime.setTime(timeScale);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (curTime.before(scaTime)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param beginDate the begin date
     * @param endDate   the end date
     * @return the every day
     * @throws ParseException the parse exception
     * @description: 获取两个日期之间的所有日期
     */
    public static List<String> getEveryDay(String beginDate, String endDate) throws ParseException {
        List<String> list = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(beginDate));
        for (long d = cal.getTimeInMillis(); d <= sdf.parse(endDate).getTime(); d = get_D_Plaus_1(cal)) {
            list.add(sdf.format(d));
        }
        return list;
    }

    private static long get_D_Plaus_1(Calendar c) {
        c.set(Calendar.DAY_OF_MONTH, c.get(Calendar.DAY_OF_MONTH) + 1);
        return c.getTimeInMillis();
    }

    /**
     * 返回给定日期中的月份中的第一天
     *
     * @param date 基准日期
     * @return 该月第一天的日期
     */
    public static Date getMonthFirstDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        if (date != null) {
            calendar.setTime(date);
        }
        int days = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.add(Calendar.DAY_OF_MONTH, 1 - days);
        return calendar.getTime();
    }

    /**
     * @param dateBegin
     * @param dateEnd
     * @return
     * @Description: 获取某个时间范围内的所有日期
     * @author: wdl
     * @date: 2019年8月2日
     */
    public static List<String> findDates(Date dateBegin, Date dateEnd) {
        return findDates(dateBegin, dateEnd, null);
    }

    /**
     * 查找日期
     *
     * @param dateBegin   日期开始
     * @param dateEnd     日期结束
     * @param includeDays 包含星期
     * @return {@link List }<{@link String }>
     */
    public static List<String> findDates(Date dateBegin, Date dateEnd, Set<Integer> includeDays) {
        List<String> lDate = new ArrayList<String>();
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        lDate.add(sd.format(dateBegin));
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(dateBegin);
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(dateEnd);
        while (dateEnd.after(calBegin.getTime())) {
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            int dayOfWeek = calBegin.get(Calendar.DAY_OF_WEEK);
            // 检查当前日期是否在包含的日期集合中
            if (CollectionUtils.isNotEmpty(includeDays) && !includeDays.contains(dayOfWeek)) {
                continue;
            }
            lDate.add(sd.format(calBegin.getTime()));

        }
        return lDate;
    }

    /**
     * 查找最小-最大日期
     *
     * @param dateStr 日期str
     * @return {@link String[] }
     * @throws Exception 例外
     */
    public static String[] findMinMaxDates(String dateStr, String pattern) throws Exception {
        pattern = StrUtil.isNotBlank(pattern) ? pattern : "yyyy-MM-dd";
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        // 将日期字符串拆分成数组
        String[] dateArray = dateStr.split(",");
        // 将字符串数组转换为Date数组
        Date[] dates = new Date[dateArray.length];
        for (int i = 0; i < dateArray.length; i++) {
            dates[i] = formatter.parse(dateArray[i]);
        }
        // 找到最小日期
        Date minDate = Arrays.stream(dates)
                .min(Comparator.naturalOrder())
                .orElseThrow(Exception::new);
        // 找到最大日期
        Date maxDate = Arrays.stream(dates)
                .max(Comparator.naturalOrder())
                .orElseThrow(Exception::new);
        // 将最小日期和最大日期格式化为字符串数组
        return new String[]{formatter.format(minDate), formatter.format(maxDate)};
    }

    /**
     * 查找最小-最大时间
     *
     * @param timeRanges 时间范围
     * @param pattern    图案
     * @return {@link String[] }
     */
    public static String[] findMinMaxTime(String timeRanges, String pattern) {
        pattern = StrUtil.isNotBlank(pattern) ? pattern : DEFAULT_TIME_FORMAT;
        SimpleDateFormat formatter = new SimpleDateFormat(pattern);
        String[] ranges = timeRanges.split(",");
        Date minTime = null;
        Date maxTime = null;
        for (String range : ranges) {
            String[] times = range.split("-");
            try {
                Date startTime = formatter.parse(times[0]);
                Date endTime = formatter.parse(times[1]);
                if (minTime == null || startTime.before(minTime)) {
                    minTime = startTime;
                }
                if (maxTime == null || endTime.after(maxTime)) {
                    maxTime = endTime;
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        String[] result = new String[2];
        result[0] = formatter.format(minTime);
        result[1] = formatter.format(maxTime);
        return result;
    }

    /**
     * 获取指定日期的开始时间和结束时间
     *
     * @param date 指定的日期
     * @return 一个包含开始时间和结束时间的 LocalDateTime 数组
     */
    public static LocalDateTime[] getStartAndEndOfDay(LocalDate date) {
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);
        return new LocalDateTime[]{startOfDay, endOfDay};
    }

    /**
     * 将 LocalDateTime 转换为指定格式的字符串，默认格式为 "yyyy-MM-dd HH:mm:ss"。
     *
     * @param localDateTime 要转换的 LocalDateTime 对象
     * @return 指定格式的日期时间字符串
     */
    public static String formatLocalDateTime(LocalDateTime localDateTime, String pattern) {
        pattern = StrUtil.isNotBlank(pattern) ? pattern : DEFAULT_DATETIME_FORMAT;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatter);
    }

    /**
     * 验证当前日期是否在指定的日期范围内
     *
     * @param startDate 开始日期，可以为null
     * @param endDate   结束日期，可以为null
     * @return true 如果当前日期在有效范围内，false 否则
     *
     * 验证规则：
     * - 如果开始日期和结束日期都为null，返回true（无限制）
     * - 如果只有开始日期不为null，验证当前日期是否在开始日期当天或之后（包含开始日期）
     * - 如果只有结束日期不为null，验证当前日期是否在结束日期当天或之前（包含结束日期）
     * - 如果开始日期和结束日期都不为null，验证当前日期是否在两者之间（包含边界日期）
     */
    public static boolean localDateIsCorrect(LocalDate startDate, LocalDate endDate) {
        LocalDate localDate = LocalDate.now();
        if (Objects.isNull(startDate) && Objects.isNull(endDate)) {
            return true;
        } else if (!Objects.isNull(startDate) && Objects.isNull(endDate)) {
            // 当前日期 >= 开始日期（包含开始日期当天）
            return !localDate.isBefore(startDate);
        } else if (Objects.isNull(startDate)) {
            // 当前日期 <= 结束日期（包含结束日期当天）
            return !localDate.isAfter(endDate);
        }
        // 开始日期 <= 当前日期 <= 结束日期（包含边界日期）
        return !localDate.isBefore(startDate) && !localDate.isAfter(endDate);
    }
    /**
     * 验证当前日期时间是否在指定的日期时间范围内
     *
     * @param startDateTime 开始日期时间，可以为null
     * @param endDateTime   结束日期时间，可以为null
     * @return true 如果当前日期时间在有效范围内，false 否则
     *
     * 验证规则：
     * - 如果开始日期时间和结束日期时间都为null，返回true（无限制）
     * - 如果只有开始日期时间不为null，验证当前日期时间是否在开始日期时间之后
     * - 如果只有结束日期时间不为null，验证当前日期时间是否在结束日期时间之前
     * - 如果开始日期时间和结束日期时间都不为null，验证当前日期时间是否在两者之间
     */
    public static boolean localDateTimeIsCorrect(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        LocalDateTime localDateTime = LocalDateTime.now();
        if (Objects.isNull(startDateTime) && Objects.isNull(endDateTime)) {
            return true;
        } else if (!Objects.isNull(startDateTime) && Objects.isNull(endDateTime)) {
            return localDateTime.isAfter(startDateTime);
        } else if (Objects.isNull(startDateTime)) {
            return localDateTime.isBefore(endDateTime);
        }
        return localDateTime.isAfter(startDateTime) && localDateTime.isBefore(endDateTime);
    }

    /**
     * 格式时间
     *
     * @param seconds 秒数
     * @return {@link String }
     */
    public static String formatDuration(long seconds) {
        if (seconds < 0) {
            throw new IllegalArgumentException("Seconds cannot be negative");
        }

        long SECONDS_IN_MINUTE = 60;
        long SECONDS_IN_HOUR = 3600;
        long SECONDS_IN_DAY = 86400;
        long SECONDS_IN_MONTH = 2592000;  // This is a rough estimate (30 days)
        long SECONDS_IN_YEAR = 31536000;  // This is a rough estimate (365 days)

        long years = seconds / SECONDS_IN_YEAR;
        seconds %= SECONDS_IN_YEAR;

        long months = seconds / SECONDS_IN_MONTH;
        seconds %= SECONDS_IN_MONTH;

        long days = seconds / SECONDS_IN_DAY;
        seconds %= SECONDS_IN_DAY;

        long hours = seconds / SECONDS_IN_HOUR;
        seconds %= SECONDS_IN_HOUR;

        long minutes = seconds / SECONDS_IN_MINUTE;
        seconds %= SECONDS_IN_MINUTE;

        StringBuilder sb = new StringBuilder();

        if (years > 0) {
            sb.append(years).append("年");
        }
        if (months > 0) {
            sb.append(months).append("月");
        }
        if (days > 0) {
            sb.append(days).append("日");
        }
        if (hours > 0) {
            sb.append(hours).append("小时");
        }
        if (minutes > 0) {
            sb.append(minutes).append("分");
        }
        if (seconds > 0 || sb.length() == 0) {
            sb.append(seconds).append("秒");
        }

        return sb.toString();
    }

    /**
     * 判断两个时间段是否冲突
     */
    public static boolean isTimeConflict(String start1, String end1, String start2, String end2) {
        LocalTime jobStart = LocalTime.parse(start1);
        LocalTime jobEnd = LocalTime.parse(end1);
        LocalTime busyStart = LocalTime.parse(start2);
        LocalTime busyEnd = LocalTime.parse(end2);
        // 判断时间段是否重叠
        return !(jobEnd.isBefore(busyStart) || jobStart.isAfter(busyEnd));
    }

    /**
     * 判断时间段1是否被时间段2完全覆盖
     */
    public static boolean isTimeFullyCovered(String jobStart, String jobEnd, String availableStart, String availableEnd) {
        LocalTime start1 = LocalTime.parse(jobStart);
        LocalTime end1 = LocalTime.parse(jobEnd);
        LocalTime start2 = LocalTime.parse(availableStart);
        LocalTime end2 = LocalTime.parse(availableEnd);
        // 判断时间段1是否被时间段2完全覆盖
        return !start1.isBefore(start2) && !end1.isAfter(end2);
    }
}

