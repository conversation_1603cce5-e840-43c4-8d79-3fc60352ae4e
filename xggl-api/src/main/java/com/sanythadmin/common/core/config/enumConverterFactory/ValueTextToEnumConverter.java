package com.sanythadmin.common.core.config.enumConverterFactory;

import com.sanythadmin.common.core.exception.BusinessException;
import org.springframework.core.convert.converter.Converter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class ValueTextToEnumConverter<T extends ValueTextBaseEnum> implements Converter<String, T> {
    private final Map<String, T> strEnumMap = new HashMap<>();

    public ValueTextToEnumConverter(Class<T> enumType) {
        Arrays.stream(enumType.getEnumConstants())
                .forEach(x -> {
                    if (x.getValue() != null)
                        strEnumMap.put(x.getValue().toString(), x);
                    strEnumMap.put(x.getText(), x);
                    strEnumMap.put(x.toString(), x);
                });
    }

    @Override
    public T convert(String source) {
        return Optional.of(source).map(strEnumMap::get).orElseThrow(() -> new BusinessException("参数类型错误"));
    }
}
