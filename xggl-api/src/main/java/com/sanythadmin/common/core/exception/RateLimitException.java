package com.sanythadmin.common.core.exception;

import com.sanythadmin.common.core.Constants;

/**
 * 限流异常
 */
public class RateLimitException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private Integer code;
    private String data;

    public RateLimitException() {
        this(Constants.RESULT_ERROR_MSG);
    }

    public RateLimitException(String message) {
        this(Constants.RESULT_ERROR_CODE, message);
    }

    public RateLimitException(String message, String data) {
        this(Constants.RESULT_ERROR_CODE, message, data);
    }

    public RateLimitException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public RateLimitException(Integer code, String message, String data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public RateLimitException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public RateLimitException(Integer code, String message, Throwable cause,
                             boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }
}
