package com.sanythadmin.common.core.security;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.config.IgnoredUrlsProperties;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.system.entity.LoginRecord;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.service.LoginRecordService;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysJwtService;
import com.sanythadmin.common.system.service.SysSessionVarService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 处理携带token的请求过滤器
 *
 * <AUTHOR>
 * @since 2020-03-30 20:48:05
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Resource
    private LoginRecordService loginRecordService;
    @Resource
    private SysAccountService sysAccountService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private SysSessionVarService sysSessionVarService;
    @Resource
    private SysJwtService sysJwtService;
    @Resource
    private IgnoredUrlsProperties ignoredUrlsProperties;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        if (ignoredUrlsProperties.ignore(requestURI)) {
            chain.doFilter(request, response);
            return;
        }

        String access_token = JwtUtil.getAccessToken(request);
        if (StringUtils.hasLength(access_token)) {
            try {
                SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
                // 解析token
                Claims claims = JwtUtil.parseToken(access_token, config.getTokenKey());
                JwtSubject subject = JwtUtil.getJwtSubject(claims);
                Object object = redisTemplate.opsForValue().get(subject.logoutCacheKey());
                if (object != null)
                    throw new RuntimeException(Constants.TOKEN_EXPIRED_MSG);
                SysAccount account = getAccount(subject, config);
                List<Menu> menus = UserInfoUtil.listMenuByRole(account.getRole().getId());
                account.setAuthorities(menus);
                List<Menu> authorities = menus != null ? menus.stream().filter(m -> StringUtils.hasText(m.getAuthority()))
                        .collect(Collectors.toList()) : null;
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        account, null, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                // token将要过期签发新token, 防止突然退出登录
                long expiration = (claims.getExpiration().getTime() - new Date().getTime()) / 1000 / 60;
                if (expiration < config.getTokenRefreshTime()) {
                    String token = JwtUtil.buildToken(subject, config.getTokenExpireTime(),
                            config.getTokenKey());
                    response.addHeader(Constants.TOKEN_HEADER_NAME, token);
                    loginRecordService.saveAsync(account.getUsername(), LoginRecord.TYPE_REFRESH, null,
                            request);

                    // 转移session变量关联至新token
                    sysSessionVarService.transfer(access_token, token);
                }
            } catch (ExpiredJwtException e) {
                log.error(e.getMessage());
                CommonUtil.responseError(response, Constants.TOKEN_EXPIRED_CODE, Constants.TOKEN_EXPIRED_MSG,
                        null);
                return;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                CommonUtil.responseError(response, Constants.BAD_CREDENTIALS_CODE, Constants.BAD_CREDENTIALS_MSG,
                        null);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    private SysAccount getAccount(JwtSubject subject, SysJwt config) {
        if (subject == null || !StringUtils.hasText(subject.getCacheKey()))
            throw new RuntimeException(Constants.TOKEN_EXPIRED_MSG);
        String cacheKey = subject.getCacheKey();
        SysAccount account = (SysAccount) redisTemplate.opsForValue().get(cacheKey);
        if (account == null) {
            account = sysAccountService.getByUsernameAndRoleId(subject.getUsername(), subject.getRoleId());
            if (account == null)
                throw new UsernameNotFoundException("Username not found");
            redisTemplate.opsForValue().set(cacheKey, account, config.getTokenExpireTime(), TimeUnit.SECONDS);
        }
        return account;
    }
}
