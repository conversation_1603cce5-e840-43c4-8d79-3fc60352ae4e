package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 系统配置属性
 *
 * <AUTHOR>
 * @since 2021-08-30 17:58:16
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "config")
public class ConfigProperties {
    /**
     * 文件上传磁盘位置
     */
    private Integer uploadLocation = 0;

    /**
     * 文件上传是否使用uuid命名
     */
    private Boolean uploadUuidName = true;

    /**
     * 文件上传生成缩略图的大小(kb)
     */
    private Integer thumbnailSize = 60;

    /**
     * OpenOffice的安装目录
     */
    private String openOfficeHome;

    /**
     * 临时文件目录
     */
    private String tempFileDir;

    /**
     * token过期时间
     */
    private Double tokenExpireTime;

    /**
     * token刷新时间
     */
    private Double tokenRefreshTime;
}
