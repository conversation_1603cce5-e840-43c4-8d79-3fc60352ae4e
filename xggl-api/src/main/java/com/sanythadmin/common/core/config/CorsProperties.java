package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * CORS配置属性
 * 从配置文件中读取CORS相关配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "security.cors")
public class CorsProperties {
    /**
     * 允许的来源列表
     */
    private List<String> allowedOrigins = new ArrayList<>();
    
    /**
     * 允许的来源模式列表
     */
    private List<String> allowedOriginPatterns = new ArrayList<>();
    
    /**
     * 允许的HTTP方法
     */
    private List<String> allowedMethods = new ArrayList<>();
    
    /**
     * 允许的HTTP头
     */
    private List<String> allowedHeaders = new ArrayList<>();
    
    /**
     * 暴露的响应头
     */
    private List<String> exposedHeaders = new ArrayList<>();
    
    /**
     * 是否允许凭证
     */
    private Boolean allowCredentials = true;
    
    /**
     * 预检请求的缓存时间（秒）
     */
    private Long maxAge = 3600L;
    
    /**
     * 是否启用CORS
     */
    private Boolean enabled = true;
}