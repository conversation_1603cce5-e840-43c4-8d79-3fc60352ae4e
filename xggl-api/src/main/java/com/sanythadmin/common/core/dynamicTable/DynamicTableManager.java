package com.sanythadmin.common.core.dynamicTable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.annotation.ColumnDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;
@Deprecated
@Component
public class DynamicTableManager {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 缓存表创建状态
    private final Map<String, Boolean> tableExistenceCache = new HashMap<>();

    public void ensureTableExists(Class<?> entityClass, String type) {
//        String tableName = entityClass.getSimpleName().toLowerCase() + "_" + type;
        String tableName = entityClass.getAnnotation(TableName.class).value() + "_" + type.toUpperCase();
        if (!tableExists(tableName)) {
            createTable(entityClass, tableName);
        }
    }

    private boolean tableExists(String tableName) {
        if (tableExistenceCache.containsKey(tableName)) {
            return tableExistenceCache.get(tableName);
        }
        String sql = "SELECT COUNT(*) FROM user_tables WHERE table_name = ?";
        Integer count = jdbcTemplate.queryForObject(sql, new Object[]{tableName.toUpperCase()}, Integer.class);
        boolean exists = count != null && count > 0;
        tableExistenceCache.put(tableName, exists);
        return exists;
    }

    private void createTable(Class<?> entityClass, String tableName) {
        StringBuilder createTableSql = new StringBuilder("CREATE TABLE " + tableName + " (");
        createTableSql.append("id VARCHAR2(50) NOT NULL PRIMARY KEY,");
        List<String> foreignKeys = new ArrayList<>();

        for (Field field : entityClass.getDeclaredFields()) {
            //忽略serialVersionUID,id等多个字段
            if ("serialVersionUID".equals(field.getName()) || "id".equals(field.getName())) {
                continue;
            }
//            String columnName = StringUtils.camelToUnderline(field.getName()).toUpperCase();
            String columnName = field.getAnnotation(TableField.class).value();
            String columnType = getColumnType(field);

            if (field.getAnnotation(ColumnDefinition.class) != null && field.getAnnotation(ColumnDefinition.class).foreignKey()) {
                String referencedTable = field.getAnnotation(ColumnDefinition.class).references();
//                String referencedTable = columnName.substring(0, columnName.length() - 3);
                createTableSql.append(columnName).append(" VARCHAR2(50),");
                foreignKeys.add("FOREIGN KEY (" + columnName + ") REFERENCES " + referencedTable + "(ID)");
            } else {
                createTableSql.append(columnName).append(" ").append(columnType).append(",");
            }
        }
        if (createTableSql.charAt(createTableSql.length() - 1) == ',') {
            createTableSql.deleteCharAt(createTableSql.length() - 1);
        }
        if (!foreignKeys.isEmpty()) {
            createTableSql.append(",");
            createTableSql.append(String.join(",", foreignKeys));
        }
        createTableSql.append(")");
        jdbcTemplate.execute(createTableSql.toString());

        tableExistenceCache.put(tableName, true);
    }

    private String getColumnType(Field field) {
        Class<?> type = field.getType();
        if (type == Long.class || type == long.class) {
            return "NUMBER(19)";
        } else if (type == String.class) {
            return "VARCHAR2(255)";
        } else if (type == Integer.class || type == int.class) {
            return "NUMBER(10)";
        } else if (type == Double.class || type == double.class) {
            return "NUMBER";
        } else if (type == Date.class) {
            return "TIMESTAMP";
        } else if (type == Enum.class) {
            return "NUMBER(1)";
        }
        return "VARCHAR2(255)";
    }
}
