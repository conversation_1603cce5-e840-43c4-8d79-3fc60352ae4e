package com.sanythadmin.common.core.security;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.config.CorsProperties;
import com.sanythadmin.common.core.config.IgnoredUrlsProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Created by JIANGPING on 2024/8/16.
 */
@Configuration
//@EnableMethodSecurity
@EnableWebSecurity
public class SecurityConfig {
    @Resource
    private JwtAccessDeniedHandler accessDeniedHandler;
    @Resource
    private JwtAuthenticationEntryPoint authenticationEntryPoint;
    @Resource
    private JwtAuthenticationFilter authenticationFilter;
    @Resource
    private CorsProperties corsProperties;
    @Resource
    private IgnoredUrlsProperties ignoredProperties;

    @Configuration
    @Profile("dev")
    public static class DevMethodSecurityConfig {

    }

    @EnableMethodSecurity
    @Configuration
    @Profile("!dev")
    public static class PrdMethodSecurityConfig {

    }
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 基础配置
        http.csrf(AbstractHttpConfigurer::disable);

        // 根据 corsProperties.enabled 决定是否启用 CORS
        if (corsProperties.getEnabled()) {
            http.cors(cors -> cors.configurationSource(corsConfigurationSource()));
        } else {
            http.cors(AbstractHttpConfigurer::disable);
        }

        // 其他安全配置
        return http.authorizeHttpRequests(auth -> {
                    auth.requestMatchers(ignoredProperties.getIgnoreAuthPaths().toArray(new String[]{})).permitAll();
                    auth.anyRequest().authenticated();
                })
                .sessionManagement(s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .exceptionHandling(e -> e.authenticationEntryPoint(authenticationEntryPoint)
                        .accessDeniedHandler(accessDeniedHandler))
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        // 如果 CORS 未启用，返回一个空的配置
        if (!corsProperties.getEnabled()) {
            UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
            source.registerCorsConfiguration("/**", new CorsConfiguration());
            return source;
        }

        CorsConfiguration configuration = new CorsConfiguration();

        // 设置允许凭证
        configuration.setAllowCredentials(corsProperties.getAllowCredentials());

        // 设置允许的来源
        if (!CollectionUtils.isEmpty(corsProperties.getAllowedOrigins())) {
            configuration.setAllowedOrigins(corsProperties.getAllowedOrigins());
        }

        // 设置允许的来源模式
        if (!CollectionUtils.isEmpty(corsProperties.getAllowedOriginPatterns())) {
            configuration.setAllowedOriginPatterns(corsProperties.getAllowedOriginPatterns());
        }

        // 设置允许的HTTP方法
        if (!CollectionUtils.isEmpty(corsProperties.getAllowedMethods())) {
            configuration.setAllowedMethods(corsProperties.getAllowedMethods());
        } else {
            configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        }

        // 设置允许的HTTP头
        if (!CollectionUtils.isEmpty(corsProperties.getAllowedHeaders())) {
            configuration.setAllowedHeaders(corsProperties.getAllowedHeaders());
        } else {
            configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "Accept",
                                                         Constants.TOKEN_HEADER_NAME, "Origin",
                                                         "Access-Control-Request-Method", "Access-Control-Request-Headers"));
        }

        // 设置暴露的响应头
        if (!CollectionUtils.isEmpty(corsProperties.getExposedHeaders())) {
            configuration.setExposedHeaders(corsProperties.getExposedHeaders());
        } else {
            configuration.setExposedHeaders(Arrays.asList(Constants.TOKEN_HEADER_NAME));
        }

        // 设置预检请求的缓存时间
        configuration.setMaxAge(corsProperties.getMaxAge());

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
