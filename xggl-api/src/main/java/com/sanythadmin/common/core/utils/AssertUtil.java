package com.sanythadmin.common.core.utils;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.exception.BusinessException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;

public class AssertUtil {

    public static void throwMessage(@NotNull BusinessException e) {
        throw e;
    }

    public static void throwMessage(@NotEmpty String message) {
        throw new BusinessException(message);
    }

    public static void throwMessage(@NotEmpty String message, String data) {
        throw new BusinessException(message, data);
    }

    public static void throwImportError(@NotEmpty String errorFileId) {
        throw new BusinessException(Constants.RESULT_ERROR_CODE_PROCESS_DATA, "导入失败, 请下载错误信息", errorFileId);
    }

    public static void throwApproveError(@NotEmpty String errorFileId) {
        throw new BusinessException(Constants.RESULT_ERROR_CODE_PROCESS_DATA, "审核失败, 请下载错误信息", errorFileId);
    }

    public static void isTrue(boolean expression, @NotEmpty String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    public static void hasLength(@NotEmpty String text, @NotEmpty String message) {
        if (!StringUtils.hasText(text)) {
            throw new BusinessException(message);
        }
    }

    public static void notNull(@NotNull Collection<?> collection, @NotEmpty String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(message);
        }
    }
}
