package com.sanythadmin.common.core.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.EnableMask;
import com.sanythadmin.common.core.annotation.IgnoreResponseBodyAdvice;
import com.sanythadmin.common.core.web.ApiResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@Slf4j
@RestControllerAdvice
public class ResponseBodyAdviceHandler implements ResponseBodyAdvice<Object> {
    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        IgnoreResponseBodyAdvice ignoreResponseBodyAdvice = returnType.getMethodAnnotation(IgnoreResponseBodyAdvice.class);
        return ignoreResponseBodyAdvice == null;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        EnableMask enableMask = returnType.getMethodAnnotation(EnableMask.class);

        // 如果本身已经是 ApiResult，直接返回
        if (body instanceof ApiResult<?> result) {
            result.setMask(enableMask != null);
            return result;
        }

        // 如果响应状态码不是 200，不包装
        if (response instanceof ServletServerHttpResponse) {
            int status = ((ServletServerHttpResponse) response).getServletResponse().getStatus();
            if (status != HttpStatus.OK.value()) {
                return body;
            }
        }

        ApiResult<Object> result = new ApiResult<>(Constants.RESULT_OK_CODE, Constants.RESULT_OK_MSG, body);
        result.setMask(enableMask != null);

        // 如果返回值是 String 类型，必须手动转为 JSON 字符串，否则报类型转换错误
        if (returnType.getParameterType().equals(String.class)) {
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            return JSON.toJSONString(result, JSONWriter.Feature.WriteMapNullValue);
        }

        return result;
    }
}
