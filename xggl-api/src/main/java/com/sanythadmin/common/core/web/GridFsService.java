package com.sanythadmin.common.core.web;

import com.alibaba.fastjson2.JSON;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.enums.ContentTypeDisposition;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.bson.BsonObjectId;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * GridFsTemplate 文件操作工具类
 */
@Slf4j
@Service
public class GridFsService {
    @Resource
    private GridFsTemplate gridFsTemplate;
    @Resource
    private GridFSBucket gridFSBucket;

    /**
     * 保存文件
     *
     * @param username       文件拥有者（用户名）
     * @param multipartFiles
     * @return
     */
    public String save(String username, MultipartFile... multipartFiles) {
        if (!StringUtils.hasText(username))
            username = SecurityUtil.getUsername();
        List<FileInfo> list = new ArrayList<>();
        try {
            for (MultipartFile file : multipartFiles) {
                List<FileInfo> fileInfos = save(username, file.getOriginalFilename(), file.getOriginalFilename(), file.getContentType(), file.getInputStream());
                list.addAll(fileInfos);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            AssertUtil.throwMessage("操作失败, 文件上传异常");
        }
        if (!CollectionUtils.isEmpty(list))
            return JSON.toJSONString(list);
        return null;
    }

    /**
     * 保存文件
     *
     * @param multipartFiles
     * @return
     */
    public String save(MultipartFile... multipartFiles) {
        return save(null, multipartFiles);
    }


    /**
     * 保存文件
     *
     * @param username
     * @param originalFilename
     * @param contentType
     * @param inputStream
     * @return
     */
    public List<FileInfo> save(String username, String filename, String originalFilename, String contentType, InputStream inputStream) {
        username = StringUtils.hasLength(username) ? username : SecurityUtil.getUsername();
        List<FileInfo> list = new ArrayList<>();
        Document metaData = new Document();
        if (StringUtils.hasText(username))
            metaData.append("_username", username);
        if (StringUtils.hasText(originalFilename))
            metaData.append("_originalFilename", originalFilename);
        ObjectId objectId = gridFsTemplate.store(inputStream, filename, contentType, metaData);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(objectId.toString());
        fileInfo.setContentType(contentType);
        fileInfo.setOriginalFilename(originalFilename);
        list.add(fileInfo);
        return list;
    }

    /**
     * 获取文件
     *
     * @param id
     * @return
     */
    public GridFSFile getGridFSFile(String id) {
        return gridFsTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)));
    }

    public GridFSFile getGridFSFile(ObjectId id) {
        return gridFsTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)));
    }

    public GridFsResource getGridFsResource(GridFSFile file) {
        if (file != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(file.getObjectId());
            return new GridFsResource(file, gridFS);
        }
        return null;
    }

    /**
     * 根据ID获取文件
     *
     * @param id
     * @return
     */
    public GridFsResource get(String id) {
        GridFSFile data = getGridFSFile(id);
        if (data != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(data.getObjectId());
            return new GridFsResource(data, gridFS);
        }
        return getByFilename(id);
    }

    /**
     * 根据文件ID获取文件
     *
     * @param fileInfo
     * @return
     */
    public GridFsResource get(FileInfo fileInfo) {
        GridFSFile data = getGridFSFile(fileInfo.getId());
        if (data != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(data.getObjectId());
            return new GridFsResource(data, gridFS);
        }
        return null;
    }

    /**
     * 根据文件名获取文件
     *
     * @param filename
     * @return
     */
    public GridFsResource getByFilename(String filename) {
        GridFSFile data = gridFsTemplate.findOne(new Query().addCriteria(Criteria.where("filename").is(filename)));
        if (data != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(data.getObjectId());
            return new GridFsResource(data, gridFS);
        }
        return null;
    }

    /**
     * 删除文件
     *
     * @param id
     */
    public void remove(String... id) {
        Query query = new Query();
        Criteria criteria = Criteria.where("_id").in(id);
        query.addCriteria(criteria);
        gridFsTemplate.delete(query);
    }

    /**
     * 删除文件
     *
     * @param id
     */
    public void remove(BsonObjectId... id) {
        Query query = new Query();
        List<ObjectId> objectIds = new ArrayList<>();
        for (BsonObjectId bsonObjectId : id) {
            if (bsonObjectId == null) continue;
            ObjectId objectId = bsonObjectId.getValue();
            objectIds.add(objectId);
        }
        Criteria criteria = Criteria.where("_id").in(objectIds);
        query.addCriteria(criteria);
        gridFsTemplate.delete(query);
    }

    /**
     * 删除文件
     *
     * @param fileInfos
     */
    public void remove(FileInfo... fileInfos) {
        for (FileInfo fileInfo : fileInfos) {
            remove(fileInfo.getId());
        }
    }

    /**
     * 删除文件
     *
     * @param fileInfos
     */
    public void remove(List<FileInfo> fileInfos) {
        if (CollectionUtils.isEmpty(fileInfos)) return;
        remove(fileInfos.toArray(new FileInfo[]{}));
    }

    /**
     * 删除文件
     *
     * @param fileInfoStr
     */
    public void removeByFileInfoStr(String fileInfoStr) {
        if (!StringUtils.hasLength(fileInfoStr)) return;
        List<FileInfo> list = new ArrayList<>();
        if (JSON.isValidArray(fileInfoStr)) {
            list = JSON.parseArray(fileInfoStr, FileInfo.class);
        } else if (JSON.isValidObject(fileInfoStr)) {
            list.add(JSON.parseObject(fileInfoStr, FileInfo.class));
        }
        remove(list);
    }

    /**
     * 文件预览
     *
     * @param id
     * @param response
     */
    public void inline(String id, HttpServletResponse response) {
        inlineOrDownload(id, ContentTypeDisposition.inline, response);
    }

    /**
     * 文件下载
     *
     * @param id
     * @param response
     */
    public void download(String id, HttpServletResponse response) {
        inlineOrDownload(id, ContentTypeDisposition.attachment, response);
    }

    /**
     * 文件预览或下载
     *
     * @param id          文件ID或文件名称
     * @param disposition
     * @param response
     */
    public void inlineOrDownload(String id, ContentTypeDisposition disposition, HttpServletResponse response) {
        GridFSFile file = getGridFSFile(id);
        GridFsResource gridFsResource = null;
        String originalFilename = null;
        if (file == null) {
            gridFsResource = getByFilename(id);
            if (gridFsResource != null) {
                BsonObjectId bsonObjectId = (BsonObjectId) gridFsResource.getId();
                file = getGridFSFile(bsonObjectId.getValue());
                originalFilename = file.getMetadata().getString("_originalFilename");
            }
        } else {
            gridFsResource = getGridFsResource(file);
        }
        if (gridFsResource != null) {
            try {
                String contentType = String.valueOf(file.getMetadata().get("_contentType"));
                if (!StringUtils.hasLength(contentType)) contentType = "application/octet-stream";
                originalFilename = URLEncoder.encode(StringUtils.hasLength(originalFilename) ? originalFilename : id, StandardCharsets.UTF_8);
                response.setCharacterEncoding("UTF-8");
                response.setContentType(contentType);
                response.setHeader("Content-disposition", disposition + ";filename=" + originalFilename);
                InputStream input = gridFsResource.getInputStream();
                ServletOutputStream output = response.getOutputStream();
                IOUtils.copy(input, output);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
