package com.sanythadmin.common.core.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sanythadmin.common.core.config.enumConverterFactory.StringToTextBaseEnumConverterFactory;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextToEnumConverterFactory;
import com.sanythadmin.common.core.interceptor.ConfigurableRateLimitInterceptor;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * WebMvc配置, 拦截器、资源映射等都在此配置
 *
 * <AUTHOR>
 * @since 2019-06-12 10:11:16
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ConfigurableRateLimitInterceptor configurableRateLimitInterceptor;

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverterFactory(new ValueTextToEnumConverterFactory());
        registry.addConverterFactory(new StringToTextBaseEnumConverterFactory());
    }

    @PostConstruct
    public void EnumObjectMapper() {
        // 解决enum不匹配问题 默认值为false
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加限流拦截器
        registry.addInterceptor(configurableRateLimitInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/error",
                        "/lib/",
                        "/favicon.ico"
                );
    }
}
