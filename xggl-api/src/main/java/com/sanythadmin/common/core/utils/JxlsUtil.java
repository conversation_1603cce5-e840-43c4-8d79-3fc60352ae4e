package com.sanythadmin.common.core.utils;

import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.system.entity.TemplateFile;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlEngine;
import org.jxls.common.Context;
import org.jxls.expression.JexlExpressionEvaluator;
import org.jxls.transform.Transformer;
import org.jxls.util.JxlsHelper;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by JIANGPING on 2024/9/14.
 */
@Slf4j
public class JxlsUtil {

    /**
     * excel 模板填充导出
     *
     * @param data         填充数据
     * @param inputStream  输入流（填充模板）
     * @param outputStream 输出流
     * @throws IOException
     */
    public static void export(Map<String, Object> data, InputStream inputStream, OutputStream outputStream) throws IOException {
        JxlsHelper jxlsHelper = JxlsHelper.getInstance();
        Transformer transformer = jxlsHelper.createTransformer(inputStream, outputStream);
        JexlExpressionEvaluator evaluator = (JexlExpressionEvaluator) transformer.getTransformationConfig().getExpressionEvaluator();
        Map<String, Object> functions = new HashMap<>();
        functions.put(MyCustomFunction.PREFIX, new MyCustomFunction());
        JexlEngine customJexlEngine = new JexlBuilder().namespaces(functions).create();
        evaluator.setJexlEngine(customJexlEngine);

        Context context = new Context();
        context.toMap().putAll(data);
        jxlsHelper.processTemplate(context, transformer);
    }

    /**
     * 按模板导出excel
     *
     * @param templateFile
     * @param collection
     * @param response
     * @param <T>
     */
    public static <T> void exportExcel(TemplateFile templateFile, Collection<T> collection, HttpServletResponse response) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {
            String template = templateFile.getTemplateName();
            GridFsService gridFsService = SpringContextUtil.getBean(GridFsService.class);
            GridFsResource fsResource = gridFsService.getByFilename(template);
            InputStream inputStream = fsResource.getInputStream();
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("list", collection);
            String filename = URLEncoder.encode(StringUtils.hasText(templateFile.getFileName()) ?
                            templateFile.getFileName() : DateUtil.getDateTime("yyyyMMddHHmmss"),
                    StandardCharsets.UTF_8);
            export(objectMap, inputStream, outputStream);
            response.setCharacterEncoding("utf8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xlsx");
            response.getOutputStream().write(outputStream.toByteArray());
            response.flushBuffer();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 自定义函数
     */
    public static class MyCustomFunction {
        public static final String PREFIX = "cf";

        /**
         * 参数值匹配
         *
         * @param val  参数1
         * @param val1 参数2
         * @return
         */
        public boolean equals(Object val, Object val1) {
            if (val == null && val1 != null)
                return false;
            return Objects.equals(val, val1);
        }

        /**
         * 日期时间格式化函数
         *
         * @param val     日期时间
         * @param pattern 格式
         * @return
         */
        public String formatDate(Object val, String pattern) {
            if (val == null) return null;
            if (val instanceof Date)
                return DateUtil.getDateTime((Date) val, pattern);
            if (val instanceof LocalDateTime)
                return DateUtil.formatLocalDateTime((LocalDateTime) val, pattern);
            if (val instanceof LocalDate)
                return ((LocalDate) val).format(DateTimeFormatter.ofPattern(pattern));
            return val.toString();
        }
    }
}
