package com.sanythadmin.common.core.dynamicTable;

import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
@Deprecated
@Service
public class DynamicTableService {

    @Resource
    private DynamicTableManager dynamicTableManager;

    public void setDynamicTableContext(Class<?> entityClass, String type) {
        DynamicTableContext.setTableContext(entityClass, type);
        dynamicTableManager.ensureTableExists(entityClass, type);
    }

}
