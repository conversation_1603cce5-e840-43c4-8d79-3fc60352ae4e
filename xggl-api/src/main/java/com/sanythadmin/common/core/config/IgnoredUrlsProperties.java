package com.sanythadmin.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;

import java.util.List;

/**
 * @since 2025/6/30.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "security")
public class IgnoredUrlsProperties {
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();
    private List<String> ignoreAuthPaths;

    public boolean ignore(String requestUrl) {
        boolean matchFlag = false;
        for (String s : ignoreAuthPaths) {
            if (antPathMatcher.match(s, requestUrl)) {
                matchFlag = true;
                break;
            }
        }
        return matchFlag;
    }
}
