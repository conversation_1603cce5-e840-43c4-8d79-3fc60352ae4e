package com.sanythadmin.common.core.utils;

import com.aspose.cells.License;
import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import jakarta.servlet.ServletOutputStream;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
public class PdfUtil {
    /**
     * EXCEL 转 PDF
     *
     * @param out
     * @param in
     */
    public static void excel2Pdf(ServletOutputStream out, InputStream in) {
        if (getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        try {
            Workbook wb = new Workbook(in);// 原始excel
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            wb.save(out, pdfSaveOptions);
            out.flush();
            out.close();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public static void excelToPdf(OutputStream out, InputStream in) throws Exception {
        if (getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        Workbook wb = new Workbook(in);// 原始excel
        PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
        pdfSaveOptions.setOnePagePerSheet(true);
        wb.save(out, pdfSaveOptions);
    }

    public static void excel2Pdf(ByteArrayOutputStream out, InputStream in, boolean onePagePerSheet) {
        if (getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        try {
            Workbook wb = new Workbook(in);// 原始excel
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(onePagePerSheet);
            wb.save(out, pdfSaveOptions);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = PdfUtil.class.getClassLoader().getResourceAsStream("com.aspose.cells.lic_2999.xml");
            License license = new License();
            license.setLicense(is);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return !result;
    }
}
