package com.sanythadmin.common.core.aspect;

import com.sanythadmin.common.core.annotation.RateLimited;
import com.sanythadmin.common.core.exception.RateLimitException;
import com.sanythadmin.common.core.utils.CommonUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.concurrent.TimeUnit;


/**
 * 限流切面
 * 优先使用全局限流,更细粒度的限流由此切面实现
 *
 * <AUTHOR>
 * @date 2025/06/25 09:51
 */
@Aspect
@Component
@Slf4j
public class RateLimitAspect {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Around("@annotation(rateLimited)")
    public Object around(ProceedingJoinPoint point, RateLimited rateLimited) throws Throwable {
        HttpServletRequest request = getCurrentRequest();
        String clientIp = CommonUtil.getIpAddr(request);
        String methodName = point.getSignature().getName();
        
        String key = "rate_limit:" + methodName + ":" + clientIp;
        
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            redisTemplate.expire(key, rateLimited.windowMinutes(), TimeUnit.MINUTES);
        }
        
        if (count > rateLimited.maxRequests()) {
            log.warn("IP {} 对方法 {} 请求过于频繁，已被限制", clientIp, methodName);
            throw new RateLimitException(rateLimited.message());
        }
        
        return point.proceed();
    }
    
    private HttpServletRequest getCurrentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
    }

}