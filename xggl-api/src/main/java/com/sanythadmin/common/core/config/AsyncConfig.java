package com.sanythadmin.common.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {
    public static final String ASYNC_EXECUTOR = "threadPoolTaskExecutor";

    @Primary
    @Bean(name = ASYNC_EXECUTOR)
    public Executor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);       // 核心线程数
        executor.setMaxPoolSize(20);        // 最大线程数
        executor.setQueueCapacity(500);     // 队列容量
        executor.setKeepAliveSeconds(60);   // 线程空闲时间
        executor.setThreadNamePrefix("MyThreadPoolTaskExecutor-");  // 线程名前缀
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        executor.initialize();              // 初始化
//        return executor;
        //使用 DelegatingSecurityContextAsyncTaskExecutor，以确保异步线程可以继承主线程的 SecurityContext
        return new DelegatingSecurityContextAsyncTaskExecutor(executor);
    }
}
