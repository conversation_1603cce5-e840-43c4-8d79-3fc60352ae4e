package com.sanythadmin.common.core;

/**
 * 系统常量
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019-10-29 15:55
 */
public class Constants {
    /**
     * 默认成功码
     */
    public static final int RESULT_OK_CODE = 0;

    /**
     * 默认失败码
     */
    public static final int RESULT_ERROR_CODE = 1;
    /**
     * 导入数据失败错误码
     */
    public static final int RESULT_ERROR_CODE_PROCESS_DATA = 2;

    /**
     * 默认成功信息
     */
    public static final String RESULT_OK_MSG = "操作成功";

    /**
     * 默认失败信息
     */
    public static final String RESULT_ERROR_MSG = "操作失败";

    /**
     * 无权限错误码
     */
    public static final int UNAUTHORIZED_CODE = 403;

    /**
     * 无权限提示信息
     */
    public static final String UNAUTHORIZED_MSG = "没有访问权限";

    /**
     * 未认证错误码
     */
    public static final int UNAUTHENTICATED_CODE = 401;

    /**
     * 未认证提示信息
     */
    public static final String UNAUTHENTICATED_MSG = "请先登录";

    /**
     * 登录过期错误码
     */
    public static final int TOKEN_EXPIRED_CODE = 401;

    /**
     * 登录过期提示信息
     */
    public static final String TOKEN_EXPIRED_MSG = "登录已过期";

    /**
     * 非法token错误码
     */
    public static final int BAD_CREDENTIALS_CODE = 401;

    /**
     * 请求过于频繁错误码
     */
    public static final int TOO_MANY_REQUESTS_CODE = 429;

    /**
     * 非法token提示信息
     */
    public static final String BAD_CREDENTIALS_MSG = "请退出重新登录";

    /**
     * 表示升序的值
     */
    public static final String ORDER_ASC_VALUE = "asc";

    /**
     * 表示降序的值
     */
    public static final String ORDER_DESC_VALUE = "desc";

    /**
     * token通过header传递的名称
     */
    public static final String TOKEN_HEADER_NAME = "Authorization";

    /**
     * token通过参数传递的名称
     */
    public static final String TOKEN_PARAM_NAME = "access_token";

    /**
     * token认证类型
     */
    public static final String TOKEN_TYPE = "Bearer";
    public static final String WHETHER_ENABLE_CAPTCHA = "是否启用登录验证码";
    public static final String OTHER = "other";

    /**
     * 角色标识
     */
    public static final String ROLE_SCOPE_QX = "全校";
    public static final String ROLE_SCOPE_BY = "本院";
    public static final String ROLE_SCOPE_BJ = "班级";
    public static final String ROLE_SCOPE_BB = "本班";
    public static final String ROLE_SCOPE_GR = "个人";
    public static final String ROLE_SCOPE_BDW = "本单位";
    public static final String ROLE_SCOPE_ZXS = "咨询师";

    /**
     * 初始密码
     */
    public static final String INITIAL_PASSWORD_PATTERN = "初始密码";
    public static final String DELETE_FILE_IDS = "deleteFileIds";

    public static final String USER_DATA_SCOPE_KEY = "user_data_scope:%s";
    public static final String USER_DATA_SCOPE_COUNT_KEY = "user_data_scope:count:%s";

    /**
     * 审核状态
     */
    public static final String STATE_PENDING = "待审批";
    public static final String STATE_PASS = "通过";
    public static final String STATE_TERMINATE = "不通过";
    public static final String STATE_REJECT = "驳回";
    public static final String STATE_RETURN = "退回";

    /**
     * 计分方式
     */
    public static final String CALC_METHOD_PERCENT = "比例";
    public static final String CALC_METHOD_VALUE = "分值";

    /**
     * 排班类型
     */
    public static final String SCHEDULE_TYPE_WEEK = "固定周期";
    public static final String SCHEDULE_TYPE_DAY = "固定日期";

    /**
     * 心理咨询预约记录状态
     */
    public static final String RICHANGCEPING = "日常测评";
    public static final String PUCHALIANGBIAO = "普查量表";

    public static final String YUYUEZHONG = "预约中";
    public static final String YIQUEREN = "已确认";
    public static final String YIQUXIAO = "已取消";
    public static final String YISHUANGYUE = "已爽约";
    public static final String YIGAIQI = "已改期";
    public static final String YIWANCHENG = "已完成";

    /**
     * 系统信息相关参数（学校名称,logo,地址等）
     */
    public static final String PARAM_MODEL_SYSTEM_INFO = "system-info";
    public static final String SCHOOL_CALENDAR_DATE = "校历日期";
}
