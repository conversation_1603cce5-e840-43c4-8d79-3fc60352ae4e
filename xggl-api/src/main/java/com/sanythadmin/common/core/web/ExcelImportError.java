package com.sanythadmin.common.core.web;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
public class ExcelImportError implements Serializable {

    /**
     * sheet
     */
    @ExcelProperty(value = "sheet", index = 0)
    private String sheetName;
    /**
     * 行号
     */
    @ExcelProperty(value = "行号", index = 1)
    private Integer line;
    /**
     * 导入原始值
     */
    @ColumnWidth(value = 10)
    @ExcelProperty(value = "原始值", index = 2)
    private String value;
    /**
     * 错误描述
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "错误描述", index = 3)
    private String remark;

    public ExcelImportError(Integer line, String value, String remark) {
        this.line = line;
        this.value = value;
        this.remark = remark;
    }

    public static ExcelImportError data(Integer line, Object value, String remark) {
        return new ExcelImportError(null, line, value != null ? String.valueOf(value) : "", remark);
    }

    public static ExcelImportError data(String sheetName, Integer line, Object value, String remark) {
        return new ExcelImportError(sheetName, line, value != null ? String.valueOf(value) : "", remark);
    }
}
