package com.sanythadmin.common.core.utils;

import org.springframework.util.StringUtils;

public class MaskUtils {
    public static String idCardNum(String idCardNum) {
        if (StringUtils.hasLength(idCardNum))
            return idCardNum.replaceAll("(\\d{4})\\d{10}(\\w{4})", "$1****$2");
        return idCardNum;
    }

    public static String mobilePhone(String mobilePhone) {
        if (StringUtils.hasLength(mobilePhone))
            return mobilePhone.replaceAll("(?<=\\d{3})\\d{4}(?=\\d{4})", "****");
        return mobilePhone;
    }

    public static String email(String email) {
        int visibleChars = 3;
        if (StringUtils.hasLength(email)) {
            int atIndex = email.indexOf('@');
            if (atIndex <= visibleChars * 2) {
                return email.substring(0, atIndex).replaceAll(".", "*") + email.substring(atIndex);
            } else {
                return email.substring(0, visibleChars)
                        .replaceAll(".", "*")
                        + email.substring(atIndex - visibleChars, atIndex)
                        + email.substring(atIndex);
            }
        }
        return email;
    }
}
