package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.param.MenuParam;
import com.sanythadmin.common.system.service.SysMenuService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:23
 */
@RestController
@RequestMapping("/api/system/menu")
public class SysMenuController extends BaseController {
    @Resource
    private SysMenuService sysMenuService;

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping("/page")
    public PageResult<Menu> page(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        page = sysMenuService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping()
    public List<Menu> list(MenuParam param) {
        return sysMenuService.list(param);
    }

    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping("/{id}")
    public Menu get(@PathVariable("id") String id) {
        return sysMenuService.getById(id);
    }

    @PreAuthorize("hasAuthority('sys:menu:save')")
    @OperationLog(module = "菜单资源", comments = "保存")
    @PostMapping()
    public void add(@RequestBody Menu menu) {
        sysMenuService.saveMenu(menu);
    }

    @PreAuthorize("hasAuthority('sys:menu:update')")
    @OperationLog(module = "菜单资源", comments = "修改")
    @PutMapping()
    public void update(@RequestBody Menu menu) {
        sysMenuService.updateMenu(menu);
    }

    @PreAuthorize("hasAuthority('sys:menu:remove')")
    @OperationLog(module = "菜单资源", comments = "删除")
    @DeleteMapping("/{id}")
    public void remove(@PathVariable("id") String id) {
        sysMenuService.deleteMenu(id);
    }

    @GetMapping("/childList/{menuType}/{menuId}")
    public List<Menu> childList(@PathVariable("menuType") Integer menuType, @PathVariable("menuId") String menuId) {
        return sysMenuService.childList(menuId, menuType);
    }
}
