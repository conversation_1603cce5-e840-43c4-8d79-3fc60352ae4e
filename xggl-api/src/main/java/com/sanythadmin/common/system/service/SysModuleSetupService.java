package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.SysModuleSetup;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2025-01-16 16:25:33
 */
public interface SysModuleSetupService extends IService<SysModuleSetup> {

    /**
     * 获取模块的年份（如果是子模块，则获取父模块的年份）
     * @param codeOrName 模块编码或名称
     * @return 年份
     */
    String getModuleYear(String codeOrName);

    /**
     * 根据父模块编码获取父模块信息
     * @param parentCode 父模块编码
     * @return 父模块信息
     */
    SysModuleSetup getParentModule(String parentCode);
}
