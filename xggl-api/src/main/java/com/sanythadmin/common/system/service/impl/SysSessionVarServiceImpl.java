package com.sanythadmin.common.system.service.impl;

import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.service.SysJwtService;
import com.sanythadmin.common.system.service.SysSessionVarService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 会话变量
 *
 * @since 2025/4/8 16:41
 */
@AllArgsConstructor
@Service
public class SysSessionVarServiceImpl implements SysSessionVarService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final SysJwtService sysJwtService;

    private String encodeKey(String str) {
        return "sys-session-var:" + DigestUtils.sha1Hex(str);
    }

    @Override
    public void setValue(String token, String key, Object value) {
        if (token == null) {
            return;
        }
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        String codeKey = encodeKey(token);
        redisTemplate.opsForHash().put(codeKey, key, value);
        redisTemplate.expire(codeKey, config.getTokenExpireTime(), TimeUnit.SECONDS);
    }

    @Override
    public void setValue(HttpServletRequest request, String key, Object value) {
        String accessToken = JwtUtil.getAccessToken(request);
        setValue(accessToken, key, value);
    }

    @Override
    public Object getValue(String token, String key) {
        if (token == null) {
            return null;
        }
        String codeKey = encodeKey(token);
        return redisTemplate.opsForHash().get(codeKey, key);
    }

    @Override
    public Object getValue(HttpServletRequest request, String key) {
        String accessToken = JwtUtil.getAccessToken(request);
        return getValue(accessToken, key);
    }

    @Override
    public <T> T getValue(HttpServletRequest request, String key, Class<T> clazz) {
        return clazz.cast(getValue(request, key));
    }

    @Override
    public void transfer(String fromToken, String toToken) {
        if (fromToken == null || toToken == null) {
            return;
        }
        String fromCodeKey = encodeKey(fromToken);
        String toCodeKey = encodeKey(toToken);
//        redisTemplate.opsForHash().entries(fromCodeKey).forEach((k, v) -> redisTemplate.opsForHash().put(toCodeKey, k, v));
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        redisTemplate.opsForHash().putAll(toCodeKey, redisTemplate.opsForHash().entries(fromCodeKey));
        redisTemplate.expire(toCodeKey, config.getTokenExpireTime(), TimeUnit.SECONDS);
    }

}
