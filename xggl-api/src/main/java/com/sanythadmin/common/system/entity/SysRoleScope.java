package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
/**
 * 角色标识
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SYS_ROLE_SCOPE")
@Table(name = "SYT_SYS_ROLE_SCOPE")
public class SysRoleScope implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色标识名称
     */
    @Id
    @Column(name = "NAME", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "NAME", type = IdType.ASSIGN_UUID)
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 角色标识名称对应类型（暂时保留）
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

}
