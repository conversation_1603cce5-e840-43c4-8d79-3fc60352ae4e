package com.sanythadmin.common.system.service;

import jakarta.servlet.http.HttpServletRequest;

public interface SysSessionVarService {
    void setValue(String token, String key, Object value);

    void setValue(HttpServletRequest request, String key, Object value);

    Object getValue(String token, String key);

    Object getValue(HttpServletRequest request, String key);

    <T> T getValue(HttpServletRequest request, String key, Class<T> clazz);

    void transfer(String fromToken, String toToken);
}
