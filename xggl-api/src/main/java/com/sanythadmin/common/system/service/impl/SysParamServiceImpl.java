package com.sanythadmin.common.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.mapper.SysParamMapper;
import com.sanythadmin.common.system.param.SysParamParam;
import com.sanythadmin.common.system.service.SysParamService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统参数Service实现
 *
 * <AUTHOR>
 * @since 2024-04-09 11:09:28
 */
@Service
public class SysParamServiceImpl extends ServiceImpl<SysParamMapper, SysParam> implements SysParamService {
    @Resource
    private GridFsService gridFsService;

    @Override
    public List<SysParam> list(SysParamParam param) {
        PageParam<SysParam, SysParamParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }

    @Override
    public SysParam getByParamName(String paramName) {
        List<SysParam> list = list(new LambdaQueryWrapper<SysParam>().eq(SysParam::getParamName, paramName));
        return CommonUtil.listGetOne(list);
    }

    @Override
    public String getParamValue(String paramName) {
        SysParam sysParam = getByParamName(paramName);
        return sysParam != null ? sysParam.getParamValue() : null;
    }

    @Transactional
    @Override
    public void edit(@Validated SysParam sysParam, Map<String, List<MultipartFile>> fileMap) {
        SysParam byParamName = getByParamName(sysParam.getParamName());
        List<FileInfo> fileInfos = new ArrayList<>();
        if (StringUtils.hasLength(sysParam.getId())) {
            if (byParamName != null && !sysParam.getId().equals(byParamName.getId()))
                AssertUtil.throwMessage(ErrorInfo.PARAMETER_NAME_EXISTS);
            SysParam param = getById(sysParam.getId());
            sysParam.setCreateTime(param.getCreateTime());
            if (StringUtils.hasLength(param.getAttachment())) {
                fileInfos = JSON.parseArray(param.getAttachment(), FileInfo.class);
                if (StringUtils.hasLength(sysParam.getDeleteFileIds())) {
                    String[] ids = sysParam.getDeleteFileIds().split(",");
                    fileInfos.removeIf(fileInfo -> Arrays.asList(ids).contains(fileInfo.getId()));
                    gridFsService.remove(ids);
                }
            }
        } else {
            if (byParamName != null)
                AssertUtil.throwMessage(ErrorInfo.PARAMETER_NAME_EXISTS);
            sysParam.setCreateTime(LocalDateTime.now());
        }

        if (!CollectionUtils.isEmpty(fileMap)) {
            List<MultipartFile> attachment = fileMap.get("attachment");
            String fileInfoStr = gridFsService.save(attachment.toArray(new MultipartFile[]{}));
            fileInfos.addAll(JSON.parseArray(fileInfoStr, FileInfo.class));
        }
        if (!CollectionUtils.isEmpty(fileInfos))
            sysParam.setAttachment(JSON.toJSONString(fileInfos));
        saveOrUpdate(sysParam);
    }

    @Override
    public void removeByIds(String... ids) {
        for (String id : ids) {
            SysParam param = getById(id);
            gridFsService.removeByFileInfoStr(param.getAttachment());
            removeById(id);
        }
    }

    @Override
    public Map<String, String> getMap(String... paramName) {
        List<SysParam> list = list(new LambdaQueryWrapper<SysParam>().in(SysParam::getParamName, (Object[]) paramName));
        if (!CollectionUtils.isEmpty(list))
            return list.stream().collect(Collectors.toMap(SysParam::getParamName, SysParam::getParamValue));
        return null;
    }
}
