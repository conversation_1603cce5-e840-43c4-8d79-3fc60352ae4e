package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 邮件发送记录
 *
 * <AUTHOR>
 * @since 2021-08-29 12:36:35
 */
@Entity
@Data
@TableName("SYT_SYS_EMAIL_RECORD")
@Table(name = "SYT_SYS_EMAIL_RECORD")
public class EmailRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", columnDefinition = ColumnType.CHAR_32)
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 收件邮箱
     */
    private String receiver;

    /**
     * 发件邮箱
     */
    private String sender;

    /**
     * 创建人
     */
    @Column(name = "create_user_id", columnDefinition = ColumnType.CHAR_32)
    private String createUserId;

    /**
     * 备注
     */
    private String comments;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
