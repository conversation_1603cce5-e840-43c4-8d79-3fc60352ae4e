package com.sanythadmin.common.system.result;

import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginUserResult implements Serializable {
    /**
     * 用户信息
     */
    private UserInfo userInfo;
    /**
     * 当前角色
     */
    private SysRole role;
    /**
     * 当前角色拥有的权限信息
     */
    private List<Menu> authorities;
}
