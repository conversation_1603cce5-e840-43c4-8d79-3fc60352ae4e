package com.sanythadmin.common.system.service;

import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.config.CasProperties;
import com.sanythadmin.common.core.config.RateLimitConfig;
import com.sanythadmin.common.core.security.JwtSubject;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.security.rsa.DecryptMap;
import com.sanythadmin.common.core.security.rsa.RSADecryptException;
import com.sanythadmin.common.core.security.rsa.SecJS;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.system.entity.LoginRecord;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.param.LoginParam;
import com.sanythadmin.common.system.result.LoginResult;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class LoginValidationService {

    @Resource
    private SysJwtService sysJwtService;
    @Resource
    private LoginRecordService loginRecordService;
    @Resource
    private LockRecordService lockRecordService;
    @Resource
    private RateLimitConfig rateLimitConfig;
    @Resource
    private SysAccountService accountService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private CasProperties casProperties;


    /**
     * 严格的参数验证，防止批量分配攻击
     */
    public void validateLoginParams(LoginParam param) {
        // 检查必需字段
        AssertUtil.hasLength(param.getCid(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getCode(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getUsername(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getPassword(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);

        // 检查参数长度限制
        if (param.getUsername().length() > 500 || param.getPassword().length() > 500 ||
                param.getCode().length() > 500 || param.getCid().length() > 500) {
            log.warn("登录参数长度异常，可能存在攻击行为");
            AssertUtil.throwMessage(ErrorInfo.INVALID_PARAMETER_FORMAT);
        }

        // 检查是否包含非法字符或模式
        validateParameterPattern(param.getUsername(), "username");
        validateParameterPattern(param.getPassword(), "password");
        validateParameterPattern(param.getCode(), "code");
        validateParameterPattern(param.getCid(), "cid");

        // 检查remember字段，防止批量分配
        if (param.getRemember() == null) {
            param.setRemember(false); // 设置默认值
        }
    }

    /**
     * 参数格式验证
     */
    public void validateParameterPattern(String value, String fieldName) {
        // 检查是否为预期的加密格式（十六进制字符串）
        if (!value.matches("^[a-fA-F0-9]+$")) {
            log.warn("字段 {} 格式不正确，可能存在攻击行为", fieldName);
            AssertUtil.throwMessage(ErrorInfo.INVALID_PARAMETER_FORMAT);
        }
    }

    /**
     * 用户级别登录尝试限制 - 专注于账户安全而非简单的请求限流
     */
    public void checkUserLoginAttempts(String username, String clientIp, HttpServletRequest request) {
        String userAttemptKey = "login_attempts:user:" + username;
        String ipAttemptKey = "login_attempts:ip:" + clientIp;
        String userLockKey = "user_locked:" + username;

        // 检查用户是否被锁定
        if (redisTemplate.hasKey(userLockKey)) {
            log.warn("用户 {} 因多次登录失败被锁定", username);
            AssertUtil.throwMessage(ErrorInfo.ACCOUNT_TEMPORARILY_LOCKED);
        }

        // 检查用户登录尝试次数
        Integer userAttempts = (Integer) redisTemplate.opsForValue().get(userAttemptKey);
        Integer ipAttempts = (Integer) redisTemplate.opsForValue().get(ipAttemptKey);

        // 从RateLimitConfig获取登录相关配置
        int maxAttempts = 5; // 默认值，与全局限流区分，专注于失败尝试
        int lockoutDuration = 15; // 默认锁定15分钟，比全局限流更严格

        // 尝试从路径规则中获取登录路径的配置
        for (RateLimitConfig.PathRule rule : rateLimitConfig.getPathRules()) {
            if (rule.getPathPattern().contains("/login")) {
                // 账户安全限制应比请求限流更严格，设为请求限流的一半
                maxAttempts = Math.max(3, rule.getMaxRequests() / 2);
                // 锁定时间应比请求限流更长
                lockoutDuration = Math.max(10, rule.getWindowMinutes() * 3);
                break;
            }
        }

        if ((userAttempts != null && userAttempts >= maxAttempts) ||
                (ipAttempts != null && ipAttempts >= maxAttempts)) {

            // 锁定用户
            redisTemplate.opsForValue().set(userLockKey, "locked",
                    lockoutDuration, TimeUnit.MINUTES);

            // 记录登录失败锁定
            int triggerCount = Math.max(userAttempts != null ? userAttempts : 0, 
                                      ipAttempts != null ? ipAttempts : 0);
            lockRecordService.recordLoginFailureLock(username, clientIp, triggerCount,
                                                   maxAttempts, lockoutDuration, request);

            log.warn("用户 {} 或IP {} 登录尝试次数过多，账户被锁定{}分钟", username, clientIp, lockoutDuration);
            AssertUtil.throwMessage(ErrorInfo.TOO_MANY_LOGIN_ATTEMPTS);
        }
    }

    /**
     * 解密登录参数
     */
    public DecryptResult decryptLoginParams(LoginParam param) {
        DecryptMap usernameDecryptMap = null;
        DecryptMap passwordDecryptMap = null;
        DecryptMap cidDecryptMap = null;
        DecryptMap codeDecryptMap = null;

        try {
            usernameDecryptMap = SecJS.checkDynamicKeyAndGet(param.getUsername());
            passwordDecryptMap = SecJS.checkDynamicKeyAndGet(param.getPassword());
            cidDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCid());
            codeDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCode());

            return new DecryptResult(
                    usernameDecryptMap.getValue(),
                    passwordDecryptMap.getValue(),
                    cidDecryptMap.getValue(),
                    codeDecryptMap.getValue()
            );

        } catch (RSADecryptException e) {
            log.error("RSA解密失败: {}", e.getMessage());
            AssertUtil.throwMessage(ErrorInfo.PARAMETER_DECRYPTION_FAILED);
            return null;
        } finally {
            // 清理动态密钥
            if (usernameDecryptMap != null) SecJS.clearDynamicKey(usernameDecryptMap.getKey());
            if (passwordDecryptMap != null) SecJS.clearDynamicKey(passwordDecryptMap.getKey());
            if (cidDecryptMap != null) SecJS.clearDynamicKey(cidDecryptMap.getKey());
            if (codeDecryptMap != null) SecJS.clearDynamicKey(codeDecryptMap.getKey());
        }
    }

    /**
     * 验证码检查
     */
    public void validateCaptcha(String cid, String code) {
        Object cachedCode = redisTemplate.opsForValue().get(cid);
        boolean isValid = (cachedCode != null && StringUtils.hasLength(code) &&
                String.valueOf(cachedCode).equalsIgnoreCase(code));

        // 无论验证码是否正确都删除，防止重复使用
        if (cachedCode != null) {
            redisTemplate.delete(cid);
        }

        if (!isValid) {
            log.warn("验证码验证失败，cid: {}", cid);
            AssertUtil.throwMessage(ErrorInfo.CAPTCHA_ERROR_OR_EXPIRED);
        }
    }

    /**
     * 账户验证
     */
    public SysAccount validateAccount(String username, String password,
                                      HttpServletRequest request, String clientIp, String userAgent) {

        SysAccount account = accountService.getByUsername(username);

        if (account == null) {
            recordFailedLogin(username, clientIp, "用户不存在", request, userAgent);
            incrementFailedAttempts(username, clientIp);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }

        validateAccount(username, request, clientIp, userAgent, account);

        if (!UserInfoUtil.comparePassword(password, account.getPassword())) {
            recordFailedLogin(username, clientIp, "密码错误", request, userAgent);
            incrementFailedAttempts(username, clientIp);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }

        return account;
    }

    private void validateAccount(String username, HttpServletRequest request, String clientIp, String userAgent, SysAccount account) {
        if (!account.isEnabled()) {
            recordFailedLogin(username, clientIp, ErrorInfo.ACCOUNT_DISABLED, request, userAgent);
            AssertUtil.throwMessage(ErrorInfo.ACCOUNT_DISABLED);
        }

        if (!account.isAccountNonLocked()) {
            recordFailedLogin(username, clientIp, ErrorInfo.ACCOUNT_LOCKED, request, userAgent);
            AssertUtil.throwMessage(ErrorInfo.ACCOUNT_LOCKED);
        }

        if (!account.isAccountNonExpired()) {
            recordFailedLogin(username, clientIp, ErrorInfo.ACCOUNT_EXPIRED, request, userAgent);
            AssertUtil.throwMessage(ErrorInfo.ACCOUNT_EXPIRED);
        }

        if (!account.isCredentialsNonExpired()) {
            recordFailedLogin(username, clientIp, ErrorInfo.CREDENTIALS_EXPIRED, request, userAgent);
            AssertUtil.throwMessage(ErrorInfo.CREDENTIALS_EXPIRED);
        }
    }

    /**
     * 记录失败的登录尝试
     */
    public void recordFailedLogin(String username, String clientIp, String reason,
                                  HttpServletRequest request, String userAgent) {
        loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR,
                ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD, request);

        log.warn("登录失败 - 用户: {}, IP: {}, 原因: {}, UserAgent: {}",
                username, clientIp, reason, userAgent);
    }

    /**
     * 增加失败尝试计数
     */
    public void incrementFailedAttempts(String username, String clientIp) {
        String userAttemptKey = "login_attempts:user:" + username;
        String ipAttemptKey = "login_attempts:ip:" + clientIp;
        int lockoutDuration = 1;
        for (RateLimitConfig.PathRule rule : rateLimitConfig.getPathRules()) {
            if (rule.getPathPattern().contains("/login")) {
                lockoutDuration = rule.getWindowMinutes();
                break;
            }
        }

        // 增加用户尝试次数
        Long userAttempts = redisTemplate.opsForValue().increment(userAttemptKey);
        if (userAttempts == 1) {
            redisTemplate.expire(userAttemptKey, lockoutDuration, TimeUnit.MINUTES);
        }

        // 增加IP尝试次数
        Long ipAttempts = redisTemplate.opsForValue().increment(ipAttemptKey);
        if (ipAttempts == 1) {
            redisTemplate.expire(ipAttemptKey, lockoutDuration, TimeUnit.MINUTES);
        }
    }

    /**
     * 清除失败尝试记录
     */
    public void clearFailedAttempts(String username, String clientIp) {
        redisTemplate.delete("login_attempts:user:" + username);
        redisTemplate.delete("login_attempts:ip:" + clientIp);
    }

    /**
     * 生成登录结果
     */
    public LoginResult generateLoginResult(SysAccount account) {
        if (CollectionUtils.isEmpty(account.getRoleList()))
            AssertUtil.throwMessage("当前账号未设置角色，无法登录，请联系管理员");
        loginRecordService.saveAsync(account.getUsername(), LoginRecord.TYPE_LOGIN, null, null);

        // 签发token
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        JwtSubject subject = JwtSubject.genCacheKey(account.getUsername(), account.getRole().getId());
        Long expireTime = config.getTokenExpireTime();
        String accessToken = JwtUtil.buildToken(subject, expireTime, config.getTokenKey());

        redisTemplate.opsForValue().set(subject.getCacheKey(), account,
                expireTime, TimeUnit.SECONDS);

        return LoginResult.build(accessToken, account.getRealName(), account.getRole());
    }

    /**
     * 解密结果内部类
     */
    public static class DecryptResult {
        private final String username;
        private final String password;
        private final String cid;
        private final String code;

        public DecryptResult(String username, String password, String cid, String code) {
            this.username = username;
            this.password = password;
            this.cid = cid;
            this.code = code;
        }

        // getters
        public String getUsername() {
            return username;
        }

        public String getPassword() {
            return password;
        }

        public String getCid() {
            return cid;
        }

        public String getCode() {
            return code;
        }
    }

    public String casLoginUrl() {
        String serverLoginUrl = casProperties.getServerLoginUrl();
        String serviceUrl = casProperties.getServiceUrl();
        if (!StringUtils.hasText(serverLoginUrl) || !StringUtils.hasText(serviceUrl))
            AssertUtil.throwMessage("CAS认证配置错误");
        return serverLoginUrl + "?service=" + URLEncoder.encode(serviceUrl, StandardCharsets.UTF_8);
    }

    public String casLogoutUrl() {
        return casProperties.getServerLogoutUrl();
    }

    public LoginResult casLoginCallback(String ticket, HttpServletRequest request) {
        if (!StringUtils.hasText(ticket) || ticket.length() > 500) {
            log.warn("登录参数长度异常，可能存在攻击行为");
            AssertUtil.throwMessage(ErrorInfo.INVALID_PARAMETER_FORMAT);
        }

        DecryptMap decryptMap = null;
        try {
            decryptMap = SecJS.checkDynamicKeyAndGet(ticket);
            ticket = decryptMap.getValue();
        } catch (RSADecryptException e) {
            log.error("RSA解密失败: {}", e.getMessage());
            AssertUtil.throwMessage(ErrorInfo.PARAMETER_DECRYPTION_FAILED);
            return null;
        } finally {
            // 清理动态密钥
            if (decryptMap != null) SecJS.clearDynamicKey(decryptMap.getKey());
        }

        log.debug("ticket: {}", ticket);
        String validateUrl = casProperties.getServerValidateUrl();
        validateUrl = validateUrl + "?ticket=" + ticket + "&service=" + casProperties.getServiceUrl();
        String xmlResponse = new RestTemplate().getForObject(validateUrl, String.class);
        String username = parseUsernameFromXml(xmlResponse);
        SysAccount account = null;
        log.debug("xmlResponse: {}", xmlResponse);
        if (StringUtils.hasText(username))
            account = accountService.getByUsername(username);
        if (account == null) AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        validateAccount(account.getUsername(), request, CommonUtil.getIpAddr(request), request.getHeader("User-Agent"), account);
        return generateLoginResult(account);
    }

    private String parseUsernameFromXml(String xml) {
        String regex = "<" + casProperties.getUsernameAttribute() + ">(.*?)</" + casProperties.getUsernameAttribute() + ">";
        log.debug("parseUsernameFromXml: {}", regex);
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(xml);
        return matcher.find() ? matcher.group(1) : null;
    }
}
