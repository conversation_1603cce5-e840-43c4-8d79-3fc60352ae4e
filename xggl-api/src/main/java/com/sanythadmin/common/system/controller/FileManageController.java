package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.enums.ContentTypeDisposition;
import com.sanythadmin.common.system.entity.TemplateFile;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonObjectId;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件管理
 */
@Slf4j
@Controller
@RequestMapping("/api/file")
public class FileManageController extends BaseController {
    @Resource
    private GridFsService gridFsService;

    /**
     * 文件上传（权限标识：fileManage:upload）
     *
     * @param file
     * @return
     */
    @PreAuthorize("hasAuthority('fileManage:upload')")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public @ResponseBody String upload(@RequestParam(name = "file") MultipartFile... file) {
        String fileString = gridFsService.save(file);
        return fileString;
    }

    /**
     * 删除文件（权限标识：fileManage:remove）
     *
     * @param id 文件ID
     * @return
     */
    @PreAuthorize("hasAuthority('fileManage:remove')")
    @RequestMapping(value = "/remove", method = RequestMethod.POST)
    public @ResponseBody ApiResult<?> remove(String id) {
        if (!StringUtils.hasLength(id))
            return fail();
        gridFsService.remove(id);
        return success();
    }

    /**
     * 文件下载或预览
     *
     * @param disposition (attachment/inline)
     * @param id
     * @param response
     */
    @RequestMapping(value = "/{disposition}/{id}", method = RequestMethod.GET)
    public void preview(@PathVariable("disposition") ContentTypeDisposition disposition, @PathVariable("id") String id,
                        HttpServletResponse response) {
        gridFsService.inlineOrDownload(id, disposition, response);
    }

    /**
     * 导入错误信息下载
     *
     * @param id
     * @param response
     */
    @RequestMapping(value = "/importErrorExcel", method = RequestMethod.GET)
    public void importErrorExcel(@RequestParam(name = "id") String id, HttpServletResponse response) {
        new EasyExcelHelper<ExcelImportError>() {
        }.outErrorInfoExcel(id, response);
    }

    /**
     * 模板文件上传（权限标识：fileManage:template）
     *
     * @param templateFile
     * @param file
     */
    @PreAuthorize("hasAuthority('fileManage:template')")
    @OperationLog(module = "文件管理", comments = "模板文件上传")
    @RequestMapping(value = "/template", method = RequestMethod.POST)
    public @ResponseBody void template(@Validated TemplateFile templateFile, @RequestParam(name = "file") MultipartFile file) {
        try {
            String fileName = templateFile.getTemplateName();
            GridFsResource fsResource = gridFsService.getByFilename(fileName);
            if (fsResource != null) {
                BsonObjectId bsonObjectId = (BsonObjectId) fsResource.getId();
                gridFsService.remove(bsonObjectId);
            }
            gridFsService.save(null, fileName, file.getOriginalFilename(), file.getContentType(), file.getInputStream());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 模板文件下载
     *
     * @param templateFile
     * @param response
     */
    @OperationLog(module = "文件管理", comments = "模板文件下载")
    @RequestMapping(value = "/template", method = RequestMethod.GET)
    public void template(@Validated TemplateFile templateFile, HttpServletResponse response) {
        gridFsService.download(templateFile.getTemplateName(), response);
    }

    /**
     * 模板检查
     *
     * @param templateFile
     * @return
     */
    @RequestMapping(value = "/template-check", method = RequestMethod.POST)
    public @ResponseBody Boolean templateCheck(@Validated @RequestBody TemplateFile templateFile) {
        String filename = templateFile.getTemplateName();
        GridFsResource fsResource = gridFsService.getByFilename(filename);
        if (fsResource == null)
            AssertUtil.throwMessage("下载失败，模板文件不存在");
        return true;
    }
}
