package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 菜单查询参数
 *
 * <AUTHOR>
 * @since 2021-08-29 19:36:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MenuParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * 菜单id
     **/
    @QueryField(type = QueryType.EQ)
    private String menuId;
    /**
     * 上级id, 0是顶级
     **/
    @QueryField(type = QueryType.EQ)
    private String parentId;
    /**
     * 菜单名称
     **/
    private String title;
    /**
     * 菜单路由关键字
     **/
    private String path;
    /**
     * 菜单组件地址
     **/
    private String component;
    /**
     * 菜单类型, 0菜单, 1按钮
     **/
    @QueryField(type = QueryType.EQ)
    private Integer menuType;
    /**
     * 权限标识
     **/
    private String authority;
    /**
     * 菜单图标
     **/
    private String icon;
    /**
     * 是否隐藏, 0否, 1是(仅注册路由不显示左侧菜单)
     **/
    @QueryField(type = QueryType.EQ)
    private Integer hide;
    /**
     * 角色ID
     */
    @QueryField(ignore = true)
    private String roleId;

    @Override
    public String getSort() {
        return "sort_number asc";
    }
}
