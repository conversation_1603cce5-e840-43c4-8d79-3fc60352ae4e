package com.sanythadmin.common.system.service;

import com.sanythadmin.common.system.entity.LockRecord;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 锁定状态检查服务
 * 提供统一的锁定状态查询接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class LockStatusService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private LockRecordService lockRecordService;

    /**
     * 检查用户是否被锁定，如果被锁定返回锁定信息
     */
    public LockStatus checkUserLockStatus(String username, String clientIp) {
        LockStatus status = new LockStatus();
        
        // 检查Redis中的实时锁定状态
        boolean isLockedInRedis = false;
        String lockReason = "";
        
        // 检查用户锁定
        if (StringUtils.hasLength(username)) {
            String userLockKey = "user_locked:" + username;
            if (redisTemplate.hasKey(userLockKey)) {
                isLockedInRedis = true;
                lockReason = "用户账户被锁定";
            }
        }
        
        // 检查IP锁定
        if (StringUtils.hasLength(clientIp)) {
            String ipLockKey = "ip_locked:" + clientIp;
            if (redisTemplate.hasKey(ipLockKey)) {
                isLockedInRedis = true;
                if (!lockReason.isEmpty()) {
                    lockReason += " 且 IP被锁定";
                } else {
                    lockReason = "IP被锁定";
                }
            }
        }
        
        status.setLocked(isLockedInRedis);
        status.setReason(lockReason);
        
        // 如果被锁定，获取详细的锁定记录
        if (isLockedInRedis) {
            LockRecord lockRecord = lockRecordService.getCurrentUserLock(username, clientIp);
            if (lockRecord != null) {
                status.setLockRecord(lockRecord);
                status.setPolicyName(lockRecord.getPolicyName());
                status.setPolicyDetails(lockRecord.getPolicyDetails());
                status.setLockStartTime(lockRecord.getLockStartTime());
                status.setLockEndTime(lockRecord.getLockEndTime());
                status.setLockType(lockRecord.getLockType());
            }
        }
        
        return status;
    }

    /**
     * 检查IP是否被锁定
     */
    public boolean isIpLocked(String clientIp) {
        if (!StringUtils.hasLength(clientIp)) {
            return false;
        }
        
        String lockKey = "ip_locked:" + clientIp;
        return redisTemplate.hasKey(lockKey);
    }

    /**
     * 检查用户是否被锁定
     */
    public boolean isUserLocked(String username) {
        if (!StringUtils.hasLength(username)) {
            return false;
        }
        
        String lockKey = "user_locked:" + username;
        return redisTemplate.hasKey(lockKey);
    }

    /**
     * 获取锁定剩余时间（秒）
     */
    public long getLockRemainingTime(String username, String clientIp) {
        long maxTime = 0;
        
        if (StringUtils.hasLength(username)) {
            String userLockKey = "user_locked:" + username;
            Long userTime = redisTemplate.getExpire(userLockKey);
            if (userTime != null && userTime > maxTime) {
                maxTime = userTime;
            }
        }
        
        if (StringUtils.hasLength(clientIp)) {
            String ipLockKey = "ip_locked:" + clientIp;
            Long ipTime = redisTemplate.getExpire(ipLockKey);
            if (ipTime != null && ipTime > maxTime) {
                maxTime = ipTime;
            }
        }
        
        return maxTime;
    }

    /**
     * 锁定状态信息
     */
    @Data
    public static class LockStatus {
        /**
         * 是否被锁定
         */
        private boolean locked;
        
        /**
         * 锁定原因
         */
        private String reason;
        
        /**
         * 锁定策略名称
         */
        private String policyName;
        
        /**
         * 锁定策略详情
         */
        private String policyDetails;
        
        /**
         * 锁定类型
         */
        private Integer lockType;
        
        /**
         * 锁定开始时间
         */
        private Date lockStartTime;
        
        /**
         * 锁定结束时间
         */
        private Date lockEndTime;
        
        /**
         * 详细锁定记录
         */
        private LockRecord lockRecord;
    }
} 