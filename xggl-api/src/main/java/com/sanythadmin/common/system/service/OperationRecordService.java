package com.sanythadmin.common.system.service;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.OperationRecord;
import com.sanythadmin.common.system.param.OperationRecordParam;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class OperationRecordService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Async
    public void saveAsync(OperationRecord record) {
        record.setId(CommonUtil.getUUID());
        mongoTemplate.save(record);
    }

    public PageResult<OperationRecord> queryPage(OperationRecordParam param) {
        Query query = getQuery(param);
        long count = mongoTemplate.count(query, OperationRecord.class);
        Pageable pageable = PageRequest.of(param.getPage().intValue() - 1, param.getLimit().intValue(),
                Sort.by(Sort.Order.desc("createTime")));
        query.with(pageable);
        List<OperationRecord> list = mongoTemplate.find(query, OperationRecord.class);
        return new PageResult(list, count);
    }

    public void remove(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, OperationRecord.class);
        }
    }

    private Query getQuery(OperationRecordParam param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(param.getId())) {
            criteria.and("id").equals(param.getId());
        }
        if (StringUtils.hasLength(param.getModule())) {
            Pattern pattern = Pattern.compile("^.*" + param.getModule() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("module").regex(pattern);
        }
        if (StringUtils.hasLength(param.getDescription())) {
            Pattern pattern = Pattern.compile("^.*" + param.getDescription() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("description").regex(pattern);
        }
        if (StringUtils.hasLength(param.getRequestMethod())) {
            Pattern pattern = Pattern.compile("^.*" + param.getRequestMethod() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("requestMethod").regex(pattern);
        }
        if (StringUtils.hasLength(param.getUsername())) {
            Pattern pattern = Pattern.compile("^.*" + param.getUsername() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("username").regex(pattern);
        }
        if (StringUtils.hasLength(param.getUrl())) {
            Pattern pattern = Pattern.compile("^.*" + param.getUrl() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("url").regex(pattern);
        }
        if (StringUtils.hasLength(param.getIp())) {
            Pattern pattern = Pattern.compile("^.*" + param.getIp() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("ip").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
