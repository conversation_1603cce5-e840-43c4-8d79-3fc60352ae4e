package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-16 16:25:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SYS_MODULE_SETUP")
@Entity
@Table(name = "SYT_SYS_MODULE_SETUP")
public class SysModuleSetup implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 模块编码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 模块名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 父模块编码
     */
    @Column(name = "PARENT_CODE")
    @TableField("PARENT_CODE")
    private String parentCode;

    /**
     * 年份批次
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 是否关联流程
     */
    @Column(name = "SFGLLC",columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFGLLC")
    private JudgeMark sfgllc;

    /**
     * 判断是否为一级模块（父模块）
     * @return true-一级模块，false-二级模块
     */
    @Transient
    public boolean isParentModule() {
        return this.parentCode == null || this.parentCode.trim().isEmpty();
    }

}
