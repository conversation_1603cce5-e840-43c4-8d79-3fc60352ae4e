package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.service.SysRoleService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:02
 */
@RestController
@RequestMapping("/api/system/role")
public class SysRoleController extends BaseController {
    @Resource
    private SysRoleService roleService;

    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/page")
    public PageResult<SysRole> page(RoleParam param) {
        return roleService.page(param);
    }

    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping()
    public List<SysRole> list(RoleParam param) {
        return roleService.list(param);
    }

    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/{id}")
    public SysRole get(@PathVariable("id") String id) {
        return roleService.getById(id);
    }

    @PreAuthorize("hasAuthority('sys:role:save')")
    @OperationLog(module = "角色管理", comments = "保存角色")
    @PostMapping()
    public void save(@RequestBody SysRole sysRole) {
        roleService.saveRole(sysRole);
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色管理", comments = "修改角色")
    @PutMapping()
    public void update(@RequestBody SysRole sysRole) {
        roleService.updateRole(sysRole);
    }

    @PreAuthorize("hasAuthority('sys:role:remove')")
    @OperationLog(module = "角色管理", comments = "批量删除")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        roleService.batchRemove(ids);
    }
}
