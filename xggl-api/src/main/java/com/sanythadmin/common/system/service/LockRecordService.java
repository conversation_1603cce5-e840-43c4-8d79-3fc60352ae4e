package com.sanythadmin.common.system.service;

import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.LockRecord;
import com.sanythadmin.common.system.param.LockRecordParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 锁定记录Service
 *
 */
@Slf4j
@Service
public class LockRecordService {
    
    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 异步记录登录失败锁定
     */
    @Async
    public void recordLoginFailureLock(String username, String clientIp, int triggerCount, 
                                     int lockThreshold, int lockWindowMinutes, 
                                     HttpServletRequest request) {
        LockRecord record = createBaseLockRecord(username, clientIp, request);
        record.setLockType(LockRecord.TYPE_LOGIN_ATTEMPTS);
        record.setPolicyName("用户登录失败限制策略");
        record.setPolicyDetails(String.format("用户或IP在%d分钟内登录失败超过%d次", lockWindowMinutes, lockThreshold));
        record.setTriggerCount(triggerCount);
        record.setLockThreshold(lockThreshold);
        record.setLockWindowMinutes(lockWindowMinutes);
        record.setComments("用户因多次登录失败被锁定");
        
        saveRecord(record, lockWindowMinutes);
    }

    /**
     * 异步记录IP限流锁定
     */
    @Async
    public void recordIpRateLimitLock(String clientIp, String pathKey, int triggerCount, 
                                    int maxRequests, int windowMinutes, 
                                    HttpServletRequest request) {
        LockRecord record = createBaseLockRecord(null, clientIp, request);
        record.setLockType(LockRecord.TYPE_RATE_LIMIT_IP);
        record.setPolicyName("IP限流锁定策略");
        record.setPolicyDetails(String.format("IP在%d分钟内对路径%s请求超过%d次，触发限流后多次重复触发", 
                               windowMinutes, pathKey, maxRequests));
        record.setTriggerPath(pathKey);
        record.setTriggerCount(triggerCount);
        record.setLockThreshold(maxRequests);
        record.setLockWindowMinutes(windowMinutes);
        record.setComments("IP因多次触发限流被锁定30分钟");
        
        saveRecord(record, 30); // IP锁定固定30分钟
    }

    /**
     * 异步记录特定路径限流锁定
     */
    @Async
    public void recordPathRateLimitLock(String clientIp, String path, int triggerCount, 
                                      int maxRequests, int windowMinutes,
                                      HttpServletRequest request) {
        LockRecord record = createBaseLockRecord(null, clientIp, request);
        record.setLockType(LockRecord.TYPE_RATE_LIMIT_PATH);
        record.setPolicyName("特定路径限流策略");
        record.setPolicyDetails(String.format("IP对特定路径%s在%d分钟内请求超过%d次", 
                               path, windowMinutes, maxRequests));
        record.setTriggerPath(path);
        record.setTriggerCount(triggerCount);
        record.setLockThreshold(maxRequests);
        record.setLockWindowMinutes(windowMinutes);
        record.setComments("IP对特定路径访问过于频繁被限制");
        
        saveRecord(record, windowMinutes);
    }

    /**
     * 创建基础锁定记录
     */
    private LockRecord createBaseLockRecord(String username, String clientIp, HttpServletRequest request) {
        LockRecord record = new LockRecord();
        record.setId(CommonUtil.getUUID());
        record.setUsername(username);
        record.setClientIp(clientIp);
        record.setLockStatus(LockRecord.STATUS_LOCKED);
        record.setLockStartTime(new Date());
        
        // 获取请求信息
        if (request != null) {
            String userAgentHeader = JakartaServletUtil.getHeaderIgnoreCase(request, "User-Agent");
            if (userAgentHeader != null) {
                record.setUserAgent(userAgentHeader);
                UserAgent ua = UserAgentUtil.parse(userAgentHeader);
                if (ua != null) {
                    if (ua.getPlatform() != null) {
                        record.setOs(ua.getPlatform().toString());
                    }
                    if (ua.getOs() != null) {
                        record.setDevice(ua.getOs().toString());
                    }
                    if (ua.getBrowser() != null) {
                        record.setBrowser(ua.getBrowser().toString());
                    }
                }
            }
        }
        
        return record;
    }

    /**
     * 保存锁定记录
     */
    private void saveRecord(LockRecord record, int lockMinutes) {
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        record.setLockEndTime(new Date(now.getTime() + TimeUnit.MINUTES.toMillis(lockMinutes)));
        
        try {
            mongoTemplate.save(record);
            log.info("锁定记录已保存: 类型={}, 用户={}, IP={}, 策略={}", 
                    record.getLockType(), record.getUsername(), record.getClientIp(), record.getPolicyName());
        } catch (Exception e) {
            log.error("保存锁定记录失败", e);
        }
    }

    /**
     * 解锁用户（手动解锁）
     */
    public void unlockUser(String username, String clientIp, String unlockReason) {
        Query query = new Query();
        
        if (StringUtils.hasLength(username)) {
            query.addCriteria(Criteria.where("username").is(username));
        }
        if (StringUtils.hasLength(clientIp)) {
            query.addCriteria(Criteria.where("clientIp").is(clientIp));
        }
        
        query.addCriteria(Criteria.where("lockStatus").is(LockRecord.STATUS_LOCKED));
        
        List<LockRecord> lockedRecords = mongoTemplate.find(query, LockRecord.class);
        
        Date now = new Date();
        for (LockRecord record : lockedRecords) {
            record.setLockStatus(LockRecord.STATUS_UNLOCKED);
            record.setUnlockTime(now);
            record.setUnlockMethod("manual");
            record.setUpdateTime(now);
            if (StringUtils.hasLength(unlockReason)) {
                record.setComments(record.getComments() + " | 手动解锁原因: " + unlockReason);
            }
            mongoTemplate.save(record);
        }
        
        log.info("已手动解锁用户: username={}, ip={}, 数量={}", username, clientIp, lockedRecords.size());
    }

    /**
     * 自动处理过期锁定记录
     */
    @Async
    public void processExpiredLocks() {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockStatus").is(LockRecord.STATUS_LOCKED));
        query.addCriteria(Criteria.where("lockEndTime").lt(new Date()));
        
        List<LockRecord> expiredRecords = mongoTemplate.find(query, LockRecord.class);
        
        Date now = new Date();
        for (LockRecord record : expiredRecords) {
            record.setLockStatus(LockRecord.STATUS_EXPIRED);
            record.setUnlockTime(now);
            record.setUnlockMethod("auto");
            record.setUpdateTime(now);
            mongoTemplate.save(record);
        }
        
        if (!expiredRecords.isEmpty()) {
            log.info("自动处理过期锁定记录: {} 条", expiredRecords.size());
        }
    }

    /**
     * 分页查询锁定记录
     */
    public PageResult<LockRecord> selectPage(LockRecordParam param) {
        Query query = new Query();
        
        if (StringUtils.hasLength(param.getUsername())) {
            query.addCriteria(Criteria.where("username").regex(param.getUsername(), "i"));
        }
        if (StringUtils.hasLength(param.getClientIp())) {
            query.addCriteria(Criteria.where("clientIp").is(param.getClientIp()));
        }
        if (param.getLockType() != null) {
            query.addCriteria(Criteria.where("lockType").is(param.getLockType()));
        }
        if (param.getLockStatus() != null) {
            query.addCriteria(Criteria.where("lockStatus").is(param.getLockStatus()));
        }
        if (StringUtils.hasLength(param.getCreateTimeStart())) {
            query.addCriteria(Criteria.where("createTime").gte(param.getCreateTimeStart()));
        }
        if (StringUtils.hasLength(param.getCreateTimeEnd())) {
            query.addCriteria(Criteria.where("createTime").lte(param.getCreateTimeEnd()));
        }
        
        long total = mongoTemplate.count(query, LockRecord.class);
        
        // 排序和分页
        Pageable pageable = PageRequest.of((int) (param.getPage() - 1), Math.toIntExact(param.getLimit()),
                Sort.by(Sort.Direction.DESC, "createTime"));
        query.with(pageable);
        
        List<LockRecord> records = mongoTemplate.find(query, LockRecord.class);
        
        return new PageResult<>(records, total);
    }

    /**
     * 获取用户当前锁定状态
     */
    public LockRecord getCurrentUserLock(String username, String clientIp) {
        Query query = new Query();
        query.addCriteria(Criteria.where("lockStatus").is(LockRecord.STATUS_LOCKED));
        query.addCriteria(Criteria.where("lockEndTime").gt(new Date()));
        
        if (StringUtils.hasLength(username)) {
            query.addCriteria(Criteria.where("username").is(username));
        }
        if (StringUtils.hasLength(clientIp)) {
            query.addCriteria(Criteria.where("clientIp").is(clientIp));
        }
        
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        query.limit(1);
        
        List<LockRecord> records = mongoTemplate.find(query, LockRecord.class);
        return records.isEmpty() ? null : records.get(0);
    }
} 