package com.sanythadmin.common.system.controller;

import com.alibaba.excel.EasyExcel;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.service.SysRoleService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Controller
@RequestMapping("/easyExcelTest")
public class EasyExcelTest {
    @Resource
    private SysRoleService roleService;

    @RequestMapping(value = "/export")
    public void export(HttpServletResponse response) {
        List<SysRole> list = roleService.list();
        try{
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition","attachment;filename="+ URLEncoder.encode("学生表.xlsx","utf-8"));

            //获取模板路径
            String fullFilePath = "C:\\Users\\<USER>\\Desktop\\test.xlsx";

            //输出excel
            EasyExcel
                    .write(response.getOutputStream())
                    .autoCloseStream(true)
                    .withTemplate(fullFilePath)
                    .build()
                    .fill(list, EasyExcel.writerSheet(0).build())
                    .finish();
        }catch (IOException e){
            e.printStackTrace();
        }
    }
}
