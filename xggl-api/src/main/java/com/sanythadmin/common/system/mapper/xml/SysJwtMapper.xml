<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.common.system.mapper.SysJwtMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TOKEN_EXPIRE_TIME, TOKEN_REFRESH_TIME, TOKEN_KEY, ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.tokenExpireTime != null">
                        AND a.TOKEN_EXPIRE_TIME = #{param.tokenExpireTime}
                    </if>
                    <if test="param.tokenRefreshTime != null">
                        AND a.TOKEN_REFRESH_TIME = #{param.tokenRefreshTime}
                    </if>
                    <if test="param.tokenKey != null">
                        AND a.TOKEN_KEY LIKE concat(concat('%',#{param.tokenKey), '%')
                    </if>
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
    </sql>
</mapper>
