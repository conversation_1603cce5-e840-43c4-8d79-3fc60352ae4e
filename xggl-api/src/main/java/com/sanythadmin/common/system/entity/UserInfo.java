package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.EncryptedTable;
import com.sanythadmin.common.core.annotation.FieldMask;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.Mask;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
@JsonIgnoreProperties({"userOrgMaps"})
@EncryptedTable
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_USER_INFO")
@Table(name = "SYT_USER_INFO")
public class UserInfo extends BaseUserInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 扩展字段
     */
    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;
    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;
    @Column(name = "BZ3")
    @TableField("BZ3")
    private String bz3;
    @Column(name = "BZ4")
    @TableField("BZ4")
    private String bz4;
    @Column(name = "BZ5")
    @TableField("BZ5")
    private String bz5;
    @Column(name = "BZ6")
    @TableField("BZ6")
    private String bz6;
    @Column(name = "BZ7")
    @TableField("BZ7")
    private String bz7;
    @Column(name = "BZ8")
    @TableField("BZ8")
    private String bz8;
    @Column(name = "BZ9")
    @TableField("BZ9")
    private String bz9;
    @Column(name = "BZ10")
    @TableField("BZ10")
    private String bz10;
    @Column(name = "BZ11")
    @TableField("BZ11")
    private String bz11;
    @Column(name = "BZ12")
    @TableField("BZ12")
    private String bz12;
    @Column(name = "BZ13")
    @TableField("BZ13")
    private String bz13;
    @Column(name = "BZ14")
    @TableField("BZ14")
    private String bz14;
    @Column(name = "BZ15")
    @TableField("BZ15")
    private String bz15;
    @Column(name = "BZ16")
    @TableField("BZ16")
    private String bz16;
    @Column(name = "BZ17")
    @TableField("BZ17")
    private String bz17;
    @Column(name = "BZ18")
    @TableField("BZ18")
    private String bz18;
    @Column(name = "BZ19")
    @TableField("BZ19")
    private String bz19;
    @Column(name = "BZ20")
    @TableField("BZ20")
    private String bz20;
    @Column(name = "BZ21")
    @TableField("BZ21")
    private String bz21;
    @Column(name = "BZ22")
    @TableField("BZ22")
    private String bz22;
    @Column(name = "BZ23")
    @TableField("BZ23")
    private String bz23;
    @Column(name = "BZ24")
    @TableField("BZ24")
    private String bz24;
    @Column(name = "BZ25")
    @TableField("BZ25")
    private String bz25;
    @Column(name = "BZ26")
    @TableField("BZ26")
    private String bz26;
    @Column(name = "BZ27")
    @TableField("BZ27")
    private String bz27;
    @Column(name = "BZ28")
    @TableField("BZ28")
    private String bz28;
    @Column(name = "BZ29")
    @TableField("BZ29")
    private String bz29;
    @Column(name = "BZ30")
    @TableField("BZ30")
    private String bz30;
    @Column(name = "BZ31")
    @TableField("BZ31")
    private String bz31;
    @Column(name = "BZ32")
    @TableField("BZ32")
    private String bz32;
    @Column(name = "BZ33")
    @TableField("BZ33")
    private String bz33;
    @Column(name = "BZ34")
    @TableField("BZ34")
    private String bz34;
    @Column(name = "BZ35")
    @TableField("BZ35")
    private String bz35;
    @Column(name = "BZ36")
    @TableField("BZ36")
    private String bz36;
    @Column(name = "BZ37")
    @TableField("BZ37")
    private String bz37;
    @Column(name = "BZ38")
    @TableField("BZ38")
    private String bz38;
    @Column(name = "BZ39")
    @TableField("BZ39")
    private String bz39;
    @Column(name = "BZ40")
    @TableField("BZ40")
    private String bz40;
    @Column(name = "BZ41")
    @TableField("BZ41")
    private String bz41;
    @Column(name = "BZ42")
    @TableField("BZ42")
    private String bz42;
    @Column(name = "BZ43")
    @TableField("BZ43")
    private String bz43;
    @Column(name = "BZ44")
    @TableField("BZ44")
    private String bz44;
    @Column(name = "BZ45")
    @TableField("BZ45")
    private String bz45;
    @Column(name = "BZ46")
    @TableField("BZ46")
    private String bz46;
    @Column(name = "BZ47")
    @TableField("BZ47")
    private String bz47;
    @Column(name = "BZ48")
    @TableField("BZ48")
    private String bz48;
    @Column(name = "BZ49")
    @TableField("BZ49")
    private String bz49;
    @Column(name = "BZ50")
    @TableField("BZ50")
    private String bz50;
    @Column(name = "BZ51")
    @TableField("BZ51")
    private String bz51;
    @Column(name = "BZ52")
    @TableField("BZ52")
    private String bz52;
    @Column(name = "BZ53")
    @TableField("BZ53")
    private String bz53;
    @Column(name = "BZ54")
    @TableField("BZ54")
    private String bz54;
    @Column(name = "BZ55")
    @TableField("BZ55")
    private String bz55;
    @Column(name = "BZ56")
    @TableField("BZ56")
    private String bz56;
    @Column(name = "BZ57")
    @TableField("BZ57")
    private String bz57;
    @Column(name = "BZ58")
    @TableField("BZ58")
    private String bz58;
    @Column(name = "BZ59")
    @TableField("BZ59")
    private String bz59;
    @Column(name = "BZ60")
    @TableField("BZ60")
    private String bz60;

    @Column(name = "BZ61")
    @TableField("BZ61")
    private String bz61;
    @Column(name = "BZ62")
    @TableField("BZ62")
    private String bz62;
    @Column(name = "BZ63")
    @TableField("BZ63")
    private String bz63;
    @Column(name = "BZ64")
    @TableField("BZ64")
    private String bz64;
    @Column(name = "BZ65")
    @TableField("BZ65")
    private String bz65;
    @Column(name = "BZ66")
    @TableField("BZ66")
    private String bz66;
    @Column(name = "BZ67")
    @TableField("BZ67")
    private String bz67;
    @Column(name = "BZ68")
    @TableField("BZ68")
    private String bz68;
    @Column(name = "BZ69")
    @TableField("BZ69")
    private String bz69;
    @Column(name = "BZ70")
    @TableField("BZ70")
    private String bz70;
    @Column(name = "BZ71")
    @TableField("BZ71")
    private String bz71;
    @Column(name = "BZ72")
    @TableField("BZ72")
    private String bz72;
    @Column(name = "BZ73")
    @TableField("BZ73")
    private String bz73;
    @Column(name = "BZ74")
    @TableField("BZ74")
    private String bz74;
    @Column(name = "BZ75")
    @TableField("BZ75")
    private String bz75;
    @Column(name = "BZ76")
    @TableField("BZ76")
    private String bz76;
    @Column(name = "BZ77")
    @TableField("BZ77")
    private String bz77;
    @Column(name = "BZ78")
    @TableField("BZ78")
    private String bz78;
    @Column(name = "BZ79")
    @TableField("BZ79")
    private String bz79;
    @Column(name = "BZ80")
    @TableField("BZ80")
    private String bz80;
    @Column(name = "BZ81")
    @TableField("BZ81")
    private String bz81;
    @Column(name = "BZ82")
    @TableField("BZ82")
    private String bz82;
    @Column(name = "BZ83")
    @TableField("BZ83")
    private String bz83;
    @Column(name = "BZ84")
    @TableField("BZ84")
    private String bz84;
    @Column(name = "BZ85")
    @TableField("BZ85")
    private String bz85;
    @Column(name = "BZ86")
    @TableField("BZ86")
    private String bz86;
    @Column(name = "BZ87")
    @TableField("BZ87")
    private String bz87;
    @Column(name = "BZ88")
    @TableField("BZ88")
    private String bz88;
    @Column(name = "BZ89")
    @TableField("BZ89")
    private String bz89;
    @Column(name = "BZ90")
    @TableField("BZ90")
    private String bz90;
    @Column(name = "BZ91")
    @TableField("BZ91")
    private String bz91;
    @Column(name = "BZ92")
    @TableField("BZ92")
    private String bz92;
    @Column(name = "BZ93")
    @TableField("BZ93")
    private String bz93;
    @Column(name = "BZ94")
    @TableField("BZ94")
    private String bz94;
    @Column(name = "BZ95")
    @TableField("BZ95")
    private String bz95;
    @Column(name = "BZ96")
    @TableField("BZ96")
    private String bz96;
    @Column(name = "BZ97")
    @TableField("BZ97")
    private String bz97;
    @Column(name = "BZ98")
    @TableField("BZ98")
    private String bz98;
    @Column(name = "BZ99")
    @TableField("BZ99")
    private String bz99;
    @Column(name = "BZ100")
    @TableField("BZ100")
    private String bz100;
}
