package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.EnableMask;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.param.SysAccountParam;
import com.sanythadmin.common.system.result.AccountRoleResult;
import com.sanythadmin.common.system.result.SysAccountResult;
import com.sanythadmin.common.system.service.SysAccountService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统账户信息控制器
 *
 * <AUTHOR>
 * @since 2024-03-19 16:17:42
 */
@RestController
@RequestMapping("/api/system/account")
public class SysAccountController extends BaseController {
    @Resource
    private SysAccountService sysAccountService;

    /**
     * 分页查询系统账户信息（权限标识：system:account:list）
     */
    @EnableMask
    @PreAuthorize("hasAuthority('system:account:list')")
    @GetMapping("/page")
    public PageResult<SysAccountResult> page(SysAccountParam param) {
        return sysAccountService.pageRel(param);
    }

    /**
     * 查询全部系统账户信息（权限标识：system:account:list）
     */
    @PreAuthorize("hasAuthority('system:account:list')")
    @GetMapping()
    public List<SysAccount> list(SysAccountParam param) {
        PageParam<SysAccount, SysAccountParam> page = new PageParam<>(param);
        return sysAccountService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询系统账户信息（权限标识：system:account:list）
     */
    @PreAuthorize("hasAuthority('system:account:list')")
    @GetMapping("/{id}")
    public SysAccount get(@PathVariable("id") Integer id) {
        return sysAccountService.getById(id);
    }

    /**
     * 添加系统账户信息（权限标识：system:account:operation）
     */
    @PreAuthorize("hasAuthority('system:account:operation')")
    @OperationLog(module = "系统账户信息", comments = "新增或修改系统账户信息")
    @PostMapping("/operation")
    public void save(@RequestBody SysAccount sysAccount) {
        sysAccountService.edit(sysAccount);
    }

    /**
     * 批量删除系统账户信息（权限标识：system:account:remove）
     */
    @PreAuthorize("hasAuthority('system:account:remove')")
    @OperationLog(module = "系统账户信息", comments = "批量删除系统账户信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        sysAccountService.removeAccount(ids);
    }

    /**
     * 重置密码 （权限标识：system:account:restPassword）
     *
     * @param account
     */
    @PreAuthorize("hasAuthority('system:account:restPassword')")
    @OperationLog(module = "系统账户信息", comments = "重置密码")
    @PostMapping("/restPassword")
    public void restPassword(@RequestBody SysAccountParam account) {
        sysAccountService.modifyPassword(account.getId(), account.getPassword());
    }

    @PreAuthorize("hasAuthority('system:account:getByUsername')")
    @OperationLog(module = "系统账户信息", comments = "根据用户名获取用户信息")
    @GetMapping("/getByUsername")
    public SysAccount getByUsername(@RequestParam(name = "username") String username) {
        return sysAccountService.getByUsernameAndRoleId(username, null);
    }

    @PreAuthorize("hasAuthority('system:account:list')")
    @GetMapping("/queryPage")
    public PageResult<AccountRoleResult> queryPage(SysAccountParam param) {
        return sysAccountService.queryAllAccount(param);
    }
}

