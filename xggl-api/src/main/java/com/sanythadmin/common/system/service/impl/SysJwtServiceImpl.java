package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.ConfigProperties;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.mapper.SysJwtMapper;
import com.sanythadmin.common.system.service.SysJwtService;
import com.sanythadmin.project.code.constant.CodeConstants;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * jwt配置信息Service实现
 *
 * <AUTHOR>
 * @since 2025-05-23 15:43:09
 */
@CacheConfig(cacheNames = CodeConstants.JWT_CONFIG)
@Service
public class SysJwtServiceImpl extends ServiceImpl<SysJwtMapper, SysJwt> implements SysJwtService {
    @Resource
    private SysJwtMapper mapper;
    @Resource
    private ConfigProperties configProperties;

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<SysJwt> list() {
        return mapper.selectList(new LambdaQueryWrapper<>());
    }

    @Transactional
    @Override
    public void checkAndUpdate() {
        Long count = mapper.selectCount(new LambdaQueryWrapper<>());
        if (count == null || count == 0)
            saveConfig();
    }

    private void saveConfig() {
        String tokenKey = JwtUtil.encodeKey(JwtUtil.randomKey());
        SysJwt config = new SysJwt();
        config.setTokenExpireTime((long) (60 * 60 * configProperties.getTokenExpireTime()));
        config.setTokenRefreshTime((int) (configProperties.getTokenRefreshTime() * 60));
        config.setTokenKey(tokenKey);
        config.setCreateTime(LocalDateTime.now());
        mapper.insert(config);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void removeAndUpdate() {
        mapper.delete(new LambdaQueryWrapper<>());
        saveConfig();
    }
}
