package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户组织关系映射
 *
 * <AUTHOR>
 * @since 2025-06-25 13:55:43
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_USER_ORG_MAP")
@Entity
@Table(name = "SYT_USER_ORG_MAP")
public class UserOrgMap implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 年级ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 学院ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 学号/工号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XGH")
    private String xgh;

}
