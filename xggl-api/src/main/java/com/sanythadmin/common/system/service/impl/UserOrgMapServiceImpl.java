package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.mapper.UserOrgMapMapper;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.common.system.service.UserOrgMapService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户组织关系映射Service实现
 *
 * <AUTHOR>
 * @since 2025-06-25 13:55:43
 */
@Service
public class UserOrgMapServiceImpl extends ServiceImpl<UserOrgMapMapper, UserOrgMap> implements UserOrgMapService {

    @Resource
    private UserOrgMapMapper mapper;

    @Override
    public List<UserOrgMap> listByUser(String username) {
        return mapper.selectList(new LambdaQueryWrapper<UserOrgMap>().eq(UserOrgMap::getXgh, username));
    }

    @Override
    public List<UserOrgMap> list(UserOrgMapParam param) {
        PageParam<UserOrgMap, UserOrgMapParam> page = new PageParam<>(param);
        return mapper.selectList(page.getWrapper());
    }
}
