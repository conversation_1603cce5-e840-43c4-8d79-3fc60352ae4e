package com.sanythadmin.common.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.param.SysAccountParam;
import com.sanythadmin.common.system.result.AccountRoleResult;
import com.sanythadmin.common.system.result.SysAccountResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统账户信息Mapper
 *
 * <AUTHOR>
 * @since 2024-03-05 15:44:06
 */
public interface SysAccountMapper extends MyMPJBaseMapper<SysAccount> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<SysAccount>
     */
    List<SysAccount> selectPageRel(@Param("page") IPage<SysAccount> page,
                                   @Param("param") SysAccountParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<SysAccountResult> selectListRel(@Param("page") IPage<SysAccountResult> page, @Param("param") SysAccountParam param);

    List<AccountRoleResult> queryAllAccount(@Param("page") IPage<AccountRoleResult> page, @Param("param") SysAccountParam param);
}
