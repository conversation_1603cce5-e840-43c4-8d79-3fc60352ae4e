package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.param.SysParamParam;
import com.sanythadmin.common.system.service.SysParamService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;

/**
 * 系统参数控制器
 *
 * <AUTHOR>
 * @since 2024-04-09 11:09:28
 */
@RestController
@RequestMapping("/api/system/sysParam")
public class SysParamController extends BaseController {
    @Resource
    private SysParamService sysParamService;

    /**
     * 分页查询系统参数（权限标识：system:sysParam:list）
     */
    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping("/page")
    public PageResult<SysParam> page(SysParamParam param) {
        PageParam<SysParam, SysParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = sysParamService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部系统参数（权限标识：system:sysParam:list）
     */
    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping()
    public List<SysParam> list(SysParamParam param) {
        PageParam<SysParam, SysParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return sysParamService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询系统参数（权限标识：system:sysParam:list）
     */
    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping("/{id}")
    public SysParam get(@PathVariable("id") String id) {
        return sysParamService.getById(id);
    }

    /**
     * 根据参数名称查询
     *
     * @param param
     * @return
     */
    @GetMapping("/getParam")
    public SysParam getParam(SysParamParam param) {
        if (!StringUtils.hasText(param.getParamName())
                || !StringUtils.hasText(param.getParamMode()))
            AssertUtil.throwMessage("参数名称和参数模块不能为空");
        List<SysParam> list = list(param);
        return CommonUtil.listGetOne(list);
    }

    /**
     * 添加或修改系统参数（权限标识：system:sysParam:operation）
     */
    @PreAuthorize("hasAuthority('system:sysParam:operation')")
    @OperationLog(module = "系统参数", comments = "保存系统参数")
    @PostMapping("/operation")
    public void save(MultipartHttpServletRequest request) {
        SysParam sysParam = new SysParam();
        CommonUtil.setValueFromRequest(sysParam, request);
        sysParamService.edit(sysParam, CommonUtil.getFileFromRequest(request));
    }

    /**
     * 批量删除系统参数（权限标识：system:sysParam:remove）
     */
    @PreAuthorize("hasAuthority('system:sysParam:remove')")
    @OperationLog(module = "系统参数", comments = "批量删除系统参数")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        sysParamService.removeByIds(ids.toArray(new String[]{}));
    }
}
