<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sanythadmin.common.system.mapper.SysMenuMapper">

    <select id="childList" resultType="com.sanythadmin.common.system.entity.Menu">
        select a.* from (select *
        from syt_sys_menu
        start with menu_id = #{menuId}
        connect by prior menu_id = parent_id
        ) a where a.deleted = 0
        <if test="menuType!=null">
            and a.menu_type = #{menuType}
        </if>
        and a.menu_id != #{menuId}
        order by a.sort_number asc
    </select>

    <select id="getAllChildList" resultType="com.sanythadmin.common.system.entity.Menu">
        SELECT *
        FROM syt_sys_menu START WITH menu_id = #{menuId}
        CONNECT BY PRIOR menu_id = parent_id
    </select>

    <select id="getAllParentList" resultType="com.sanythadmin.common.system.entity.Menu">
        SELECT *
        FROM syt_sys_menu START WITH menu_id = #{menuId}
        CONNECT BY PRIOR parent_id = menu_id
    </select>
</mapper>
