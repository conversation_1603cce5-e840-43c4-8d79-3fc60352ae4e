package com.sanythadmin.common.system.service;

import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.LoginRecord;
import com.sanythadmin.common.system.param.LoginRecordParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 登录日志Service
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:41
 */
@Service
public class LoginRecordService {
    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 异步保存登录记录
     * 安全处理request为null的情况
     *
     * @param username 用户名
     * @param type 登录类型
     * @param comments 备注
     * @param request HTTP请求对象，可能为null
     */
    @Async
    public void saveAsync(String username, Integer type, String comments, HttpServletRequest request) {
        if (username == null) {
            return;
        }
        
        LoginRecord loginRecord = new LoginRecord();
        loginRecord.setId(CommonUtil.getUUID());
        loginRecord.setUsername(username);
        loginRecord.setLoginType(type);
        loginRecord.setComments(comments);
        
        // 安全处理request为null的情况
        if (request != null) {
            // 获取User-Agent信息
            String userAgentHeader = JakartaServletUtil.getHeaderIgnoreCase(request, "User-Agent");
            if (userAgentHeader != null) {
                UserAgent ua = UserAgentUtil.parse(userAgentHeader);
                if (ua != null) {
                    if (ua.getPlatform() != null) {
                        loginRecord.setOs(ua.getPlatform().toString());
                    }
                    if (ua.getOs() != null) {
                        loginRecord.setDevice(ua.getOs().toString());
                    }
                    if (ua.getBrowser() != null) {
                        loginRecord.setBrowser(ua.getBrowser().toString());
                    }
                }
            }
            
            // 获取客户端IP
            loginRecord.setIp(JakartaServletUtil.getClientIP(request));
        } else {
            // 当request为null时设置默认值
            loginRecord.setOs("Unknown");
            loginRecord.setDevice("Unknown");
            loginRecord.setBrowser("Unknown");
            loginRecord.setIp("0.0.0.0");
        }
        
        loginRecord.setCreateTime(new Date());
        mongoTemplate.save(loginRecord);
    }

    /**
     * 提供一个不需要HttpServletRequest的重载方法
     * 用于在没有请求上下文的情况下记录登录信息
     *
     * @param username 用户名
     * @param type 登录类型
     * @param comments 备注
     */
    @Async
    public void saveAsync(String username, Integer type, String comments) {
        saveAsync(username, type, comments, null);
    }

    public PageResult<LoginRecord> queryPage(LoginRecordParam param) {
        Query query = getQuery(param);
        long count = mongoTemplate.count(query, LoginRecord.class);
        Pageable pageable = PageRequest.of(param.getPage().intValue() - 1, param.getLimit().intValue(),
                Sort.by(Sort.Order.desc("createTime")));
        query.with(pageable);
        List<LoginRecord> list = mongoTemplate.find(query, LoginRecord.class);
        return new PageResult(list, count);
    }

    public void remove(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, LoginRecord.class);
        }
    }

    private Query getQuery(LoginRecordParam param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(param.getId())) {
            criteria.and("id").equals(param.getId());
        }
        if (StringUtils.hasLength(param.getUsername())) {
            Pattern pattern = Pattern.compile("^.*" + param.getUsername() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("username").regex(pattern);
        }

        if (StringUtils.hasLength(param.getIp())) {
            Pattern pattern = Pattern.compile("^.*" + param.getIp() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("ip").regex(pattern);
        }
        if (param.getLoginType() != null) {
            criteria.and("loginType").equals(param.getLoginType());
        }
        if (StringUtils.hasLength(param.getComments())) {
            Pattern pattern = Pattern.compile("^.*" + param.getComments() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("comments").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
