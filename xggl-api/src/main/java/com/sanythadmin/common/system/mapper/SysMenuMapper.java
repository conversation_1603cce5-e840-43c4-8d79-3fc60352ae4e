package com.sanythadmin.common.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.common.system.entity.Menu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单Mapper
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:32
 */
public interface SysMenuMapper extends BaseMapper<Menu> {
    public List<Menu> childList(@Param("menuId") String menuId, @Param("menuType") Integer menuType);
    public List<Menu> getAllChildList(@Param("menuId") String menuId);
    public List<Menu> getAllParentList(@Param("menuId") String menuId);
}
