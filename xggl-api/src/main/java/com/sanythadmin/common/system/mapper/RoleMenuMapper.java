package com.sanythadmin.common.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.RoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单Mapper
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:21
 */
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {

    /**
     * 查询用户的菜单
     *
     * @param accountId 用户id
     * @param roleId    角色id
     * @param menuType  菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByAccount(@Param("accountId") String accountId, @Param("roleId") String roleId, @Param("menuType") Integer menuType);

    /**
     * 根据角色id查询菜单
     *
     * @param roleIds  角色id
     * @param menuType 菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByRoleIds(@Param("roleIds") List<String> roleIds, @Param("menuType") Integer menuType);

}
