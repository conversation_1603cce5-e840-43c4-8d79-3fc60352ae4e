package com.sanythadmin.common.system.result;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanythadmin.common.system.entity.SysAccount;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties({"password", "role", "roleList", "authorities"})
@Data
public class SysAccountResult extends SysAccount {

    private String roleName;
}
