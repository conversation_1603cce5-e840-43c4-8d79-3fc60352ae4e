package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2025-01-16 16:25:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysModuleSetupParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 模块编码
     */
    private String code;

    /**
     * 模块名称
     */
    private String name;

    /**
     * 父模块编码
     */
    private String parentCode;

    /**
     * 年份批次
     */
    private String year;

    /**
     * 是否关联流程
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfgllc;



}
