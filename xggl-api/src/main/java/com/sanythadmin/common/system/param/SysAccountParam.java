package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.EncryptedTable;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统账户信息查询参数
 *
 * <AUTHOR>
 * @since 2024-03-05 15:44:06
 */
@EncryptedTable
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysAccountParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(ignore = true)
    private String password;
    /**
     * 账户是否过期
     */
//    @QueryField(type = QueryType.EQ)
//    private Boolean accountNonExpired;
    /**
     * 是否锁定
     */
//    @QueryField(type = QueryType.EQ)
//    private Boolean accountNonLocked;
    /**
     * 密码是否过期
     */
//    @QueryField(type = QueryType.EQ)
//    private Boolean credentialsNonExpired;
    /**
     * 是否启用
     */
//    @QueryField(type = QueryType.EQ)
//    private Boolean enabled;
    /**
     * 用户名
     */
    @QueryField(type = QueryType.EQ)
    private String username;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 性别（男：0， 女：1）
     */
    @QueryField(type = QueryType.EQ)
    private Gender gender;
    /**
     * 激活标识
     */
//    private Boolean activeFlag;
    /**
     * 证件类型, 证件号码
     */
    private String idType;
    @EncryptedColumn
    @QueryField(type = QueryType.EQ)
    private String idCode;

    /**
     * 手机号
     */
    @QueryField(type = QueryType.EQ)
    private String telMobile;

    /**
     * 账户使用时间范围
     */
//    private LocalDateTime startTime;
//    private LocalDateTime endTime;
    /**
     * 最后更新密码时间
     */
    private LocalDateTime passwordLastUpdateTime;
    /**
     * 角色ID
     */
    @QueryField(ignore = true)
    private String roleId;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private State status;
    /**
     * 帐户过期时间
     */
    private LocalDateTime accountExpireTime;
    /**
     * 密码过期时间
     */
    private LocalDateTime passwordExpireTime;
    /**
     * 锁定状态
     */
    @QueryField(type = QueryType.EQ)
    private State lockStatus;
}
