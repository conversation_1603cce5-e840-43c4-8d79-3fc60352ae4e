package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.RoleMenu;
import com.sanythadmin.common.system.mapper.RoleMenuMapper;
import com.sanythadmin.common.system.mapper.SysMenuMapper;
import com.sanythadmin.common.system.param.MenuParam;
import com.sanythadmin.common.system.service.SysMenuService;
import com.sanythadmin.project.code.constant.CodeConstants;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单Service实现
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:10
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_MENU)
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, Menu> implements SysMenuService {

    @Resource
    private SysMenuMapper sysMenuMapper;
    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Override
    public List<Menu> childList(String menuId, Integer menuType) {
        return sysMenuMapper.childList(menuId, menuType);
    }

    @Override
    public List<Menu> getAllChildList(String menuId) {
        return sysMenuMapper.getAllChildList(menuId);
    }

    @Override
    public List<Menu> getAllParentList(String menuId) {
        return sysMenuMapper.getAllParentList(menuId);
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<Menu> list(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void saveMenu(Menu menu) {
        String defaultParentId = "00000000000000000000000000000000";
        if (menu.getSortNumber() == null) {
            SortHelper<Menu> sortHelper = new SortHelper<>(this, "sort_number");
            menu.setSortNumber(sortHelper.next());
        }
        if (!StringUtils.hasText(menu.getParentId()))
            menu.setParentId(defaultParentId);
        menu.setCreateTime(new Date());
        menu.setUpdateTime(new Date());
        menu.setDeleted(0);
        baseMapper.insert(menu);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void updateMenu(Menu menu) {
        Menu selectedMenu = baseMapper.selectById(menu.getMenuId());
        menu.setCreateTime(selectedMenu.getCreateTime());
        menu.setUpdateTime(new Date());
        menu.setDeleted(0);
        baseMapper.updateById(menu);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void deleteMenu(String id) {
        List<Menu> childList = getAllChildList(id);
        List<String> menuIdList = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
        baseMapper.delete(new LambdaQueryWrapper<Menu>().in(Menu::getMenuId, menuIdList));
        roleMenuMapper.delete(new LambdaQueryWrapper<RoleMenu>().in(RoleMenu::getMenuId, menuIdList));
    }
}
