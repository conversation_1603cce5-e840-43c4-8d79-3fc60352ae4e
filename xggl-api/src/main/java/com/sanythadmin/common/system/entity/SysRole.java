package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 角色
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:01
 */
@Data
@Entity
@TableName("SYT_SYS_ROLE")
@Table(name = "SYT_SYS_ROLE")
public class SysRole implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 角色类型：（学生/老师/其他）（暂时保留）
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 角色标识
     */
    @Column(name = "ROLE_SCOPE")
    @TableField("ROLE_SCOPE")
    private String roleScope;
    /**
     * 角色描述/备注
     */
    @Column(name = "remark")
    @TableField("remark")
    private String remark;

    /**
     * 用户ID
     */
    @Transient
    @TableField(exist = false)
    private String userId;
}
