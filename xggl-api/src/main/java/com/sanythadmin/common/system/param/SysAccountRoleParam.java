package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统用户角色关系查询参数
 *
 * <AUTHOR>
 * @since 2024-03-05 16:40:52
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysAccountRoleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 角色ID
     */
    private String roleId;

}
