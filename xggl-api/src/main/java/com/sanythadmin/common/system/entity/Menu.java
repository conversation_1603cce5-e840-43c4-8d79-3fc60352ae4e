package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.springframework.security.core.GrantedAuthority;

import jakarta.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 菜单
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:17
 */
@Entity
@Data
@TableName("SYT_SYS_MENU")
@Table(name = "SYT_SYS_MENU")
public class Menu implements GrantedAuthority {
    private static final long serialVersionUID = 1L;
    public static final int TYPE_MENU = 0;  // 菜单类型
    public static final int TYPE_BTN = 1;  // 按钮类型

    /**
     * 菜单id
     */
    @Id
    @Column(name = "menu_id", columnDefinition = ColumnType.CHAR_32)
    @TableId(type = IdType.ASSIGN_UUID)
    private String menuId;

    /**
     * 上级id, 0是顶级
     */
    @Column(name = "parent_id", columnDefinition = ColumnType.CHAR_32)
    private String parentId;

    /**
     * 菜单名称
     */
    @Column(name = "title")
    @TableField(value = "title",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String title;

    /**
     * 菜单路由地址
     */
    @Column(name = "path")
    @TableField(value = "path",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String path;

    /**
     * 菜单组件地址
     */
    @Column(name = "component")
    @TableField(value = "component",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String component;

    /**
     * 菜单类型, 0目录, 1菜单, 2按钮
     */
    @Column(name = "menu_type")
    private Integer menuType;

    /**
     * 排序号
     */
    @Column(name = "sort_number")
    @TableField("sort_number")
    private Integer sortNumber;

    /**
     * 权限标识
     */
    @Column(name = "authority")
    @TableField(value = "authority",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String authority;

    /**
     * 菜单图标
     */
    @Column(name = "icon")
    @TableField(value = "icon",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String icon;

    /**
     * 是否隐藏, 0否, 1是(仅注册路由不显示左侧菜单)
     */
    private Integer hide;

    /**
     * 路由元信息
     */
    @Column(name = "meta")
    @TableField(value = "meta",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String meta;
    /**
     * 路由别名
     */
    @Column(name = "path_name")
    @TableField(value = "path_name",jdbcType = JdbcType.VARCHAR,  updateStrategy = FieldStrategy.ALWAYS)
    private String pathName;

    /**
     * 是否删除, 0否, 1是
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 子菜单
     */
    @Transient
    @TableField(exist = false)
    private List<Menu> children;

    /**
     * 角色权限树选中状态
     */
    @Transient
    @TableField(exist = false)
    private Boolean checked;

}
