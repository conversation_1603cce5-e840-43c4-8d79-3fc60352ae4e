package com.sanythadmin.common.system.entity;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:33
 */
@Data
@Document(collection = "SYT_SYS_OPERATION_RECORD")
public class OperationRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    private String id;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 操作模块
     */
    private String module;

    /**
     * 操作模块
     */
    private String description;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 调用方法
     */
    private String method;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 返回结果
     */
    private String result;

    /**
     * 异常信息
     */
    private String error;

    /**
     * 消耗时间, 单位毫秒
     */
    private Long spendTime;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 设备名称
     */
    private String device;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 状态, 0成功, 1异常
     */
    private Integer status;
    /**
     * 操作时间
     */
    private Date createTime;
    /**
     * 用户昵称
     */
    private String realName;
    /**
     * 用户账号
     */
    private String username;
}
