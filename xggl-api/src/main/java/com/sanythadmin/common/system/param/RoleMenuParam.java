package com.sanythadmin.common.system.param;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class RoleMenuParam {

    @NotNull(message = "权限操作标识不能为空")
    private Boolean add;
    @NotEmpty(message = "角色不能为空")
    private String roleId;
    @NotNull(message = "权限信息不能为空")
    private String menuId;
}
