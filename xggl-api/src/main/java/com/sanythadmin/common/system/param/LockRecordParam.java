package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 锁定记录查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LockRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键id
     **/
    private String id;

    /**
     * 用户账号
     **/
    private String username;

    /**
     * IP地址
     **/
    private String clientIp;

    /**
     * 锁定类型（1:登录失败次数过多, 2:IP限流锁定, 3:特定路径限流锁定, 4:手动锁定）
     **/
    private Integer lockType;

    /**
     * 锁定策略名称
     **/
    private String policyName;

    /**
     * 触发锁定的路径
     **/
    private String triggerPath;

    /**
     * 锁定状态（1:锁定中, 2:已解锁, 3:自动过期）
     **/
    private Integer lockStatus;

    /**
     * 操作系统
     **/
    private String os;

    /**
     * 设备名称
     **/
    private String device;

    /**
     * 浏览器类型
     **/
    private String browser;

    /**
     * IP地址
     **/
    private String ip;

    /**
     * 备注信息
     **/
    private String comments;
} 