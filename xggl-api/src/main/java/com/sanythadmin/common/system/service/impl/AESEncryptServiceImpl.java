package com.sanythadmin.common.system.service.impl;

import com.sanythadmin.common.system.service.EncryptService;
import com.sanythadmin.common.system.service.SysParamService;

public class AESEncryptServiceImpl implements EncryptService {
//    @Resource
    private SysParamService sysParamService;

    @Override
    public String encrypt(String string) {
//        SysParam initVector = sysParamMapper.getByParamName(AESCryptoUtil.initVector);
//        if (initVector == null) {
//            initVector = new SysParam();
//            initVector.setParamName(AESCryptoUtil.initVector);
//            initVector.setParamValue(CommonUtil.randomCode(16));
//            initVector.setDescription("AES加密初始化向量");
//            sysParamMapper.insert(initVector);
//        }
//
//        SysParam encryptKey = sysParamMapper.getByParamName(AESCryptoUtil.encryptKey);
//        if (encryptKey == null) {
//            encryptKey = new SysParam();
//            encryptKey.setParamName(AESCryptoUtil.encryptKey);
//            encryptKey.setParamValue(CommonUtil.randomCode(16));
//            encryptKey.setDescription("AES加密密钥");
//            sysParamMapper.insert(encryptKey);
//        }
        return null;
    }

    @Override
    public String decrypt(String string) {
        return null;
    }
}
