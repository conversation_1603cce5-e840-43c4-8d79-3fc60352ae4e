package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.LoginRecord;
import com.sanythadmin.common.system.param.LoginRecordParam;
import com.sanythadmin.common.system.service.LoginRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 登录日志控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:31
 */
@RestController
@RequestMapping("/api/system/login-record")
public class LoginRecordController extends BaseController {
    @Resource
    private LoginRecordService loginRecordService;

    /**
     * 分页查询登录日志
     * 权限标识：sys:login-record:list
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('sys:login-record:list')")
    @OperationLog
    @GetMapping("/page")
    public PageResult<LoginRecord> page(LoginRecordParam param) {
        return loginRecordService.queryPage(param);
    }

    /**
     * 删除登录日志
     * 权限标识：sys:login-record:remove
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('sys:login-record:remove')")
    @OperationLog(module = "登录日志管理", comments = "批量删除")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        loginRecordService.remove(ids);
    }

}
