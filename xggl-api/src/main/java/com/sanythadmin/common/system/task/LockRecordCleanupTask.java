package com.sanythadmin.common.system.task;

import com.sanythadmin.common.system.service.LockRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 锁定记录清理定时任务
 * 自动处理过期的锁定记录
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Component
public class LockRecordCleanupTask {

    @Resource
    private LockRecordService lockRecordService;

    /**
     * 每天凌晨4:30执行一次，处理过期的锁定记录
     */
    @Scheduled(cron = "0 30 4 * * ?")
    public void processExpiredLocks() {
        try {
            log.debug("开始处理过期锁定记录");
            lockRecordService.processExpiredLocks();
            log.debug("处理过期锁定记录完成");
        } catch (Exception e) {
            log.error("处理过期锁定记录时发生错误", e);
        }
    }
} 