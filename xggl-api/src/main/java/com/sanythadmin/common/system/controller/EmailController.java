package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.system.entity.EmailRecord;
import com.sanythadmin.common.system.service.EmailRecordService;
import jakarta.mail.MessagingException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * 邮件功能控制器
 *
 * <AUTHOR>
 * @since 2020-03-21 00:37:11
 */
@RestController
@RequestMapping("/api/system/email")
public class EmailController extends BaseController {
    @Resource
    private EmailRecordService emailRecordService;

    @PreAuthorize("hasAuthority('sys:email:send')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> send(@RequestBody EmailRecord emailRecord) {
        try {
            emailRecordService.sendFullTextEmail(emailRecord.getTitle(), emailRecord.getContent(),
                    emailRecord.getReceiver().split(","));
            emailRecord.setCreateUserId(getLoginAccountId());
            emailRecordService.save(emailRecord);
            return success("发送成功");
        } catch (MessagingException e) {
            e.printStackTrace();
        }
        return fail("发送失败");
    }

}
