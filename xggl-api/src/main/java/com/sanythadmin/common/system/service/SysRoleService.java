package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.param.RoleParam;

import java.util.List;

/**
 * 角色Service
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:32
 */
public interface SysRoleService extends IService<SysRole> {
    public PageResult<SysRole> page(RoleParam param);
    public List<SysRole> list(RoleParam param);
    public void saveRole(SysRole sysRole);
    public void updateRole(SysRole sysRole);
    public void batchRemove(List<String> ids);
}
