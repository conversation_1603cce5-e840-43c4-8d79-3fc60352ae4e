package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色菜单
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:54
 */
@Entity
@Data
@TableName("SYT_SYS_ROLE_MENU")
@Table(name = "SYT_SYS_ROLE_MENU")
public class RoleMenu implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", columnDefinition = ColumnType.CHAR_32)
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色id
     */
    @Column(name = "role_id", columnDefinition = ColumnType.CHAR_32)
    @TableField("role_id")
    private String roleId;

    /**
     * 菜单id
     */
    @Column(name = "menu_id", columnDefinition = ColumnType.CHAR_32)
    @TableField("menu_id")
    private String menuId;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("create_time")
    private Date updateTime;

}
