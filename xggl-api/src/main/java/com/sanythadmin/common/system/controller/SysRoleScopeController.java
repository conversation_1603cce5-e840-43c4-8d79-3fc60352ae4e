package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.system.entity.SysRoleScope;
import com.sanythadmin.common.system.param.SysRoleScopeParam;
import com.sanythadmin.common.system.service.SysRoleScopeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 角色标识控制器
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
@RestController
@RequestMapping("/api/system/sysRoleScope")
public class SysRoleScopeController extends BaseController {
    @Resource
    private SysRoleScopeService sysRoleScopeService;

    /**
     * 分页查询角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:list')")
    @OperationLog
    @GetMapping("/page")
    public ApiResult<PageResult<SysRoleScope>> page(SysRoleScopeParam param) {
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(sysRoleScopeService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(sysRoleScopeService.pageRel(param));
    }

    /**
     * 查询全部角色标识
     */
    @OperationLog
    @GetMapping()
    public ApiResult<List<SysRoleScope>> list(SysRoleScopeParam param) {
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(sysRoleScopeService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(sysRoleScopeService.listRel(param));
    }

    /**
     * 根据id查询角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:list')")
    @OperationLog
    @GetMapping("/{id}")
    public ApiResult<SysRoleScope> get(@PathVariable("id") Integer id) {
        return success(sysRoleScopeService.getById(id));
        // 使用关联查询
        //return success(sysRoleScopeService.getByIdRel(id));
    }

    /**
     * 添加角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:save')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> save(@RequestBody SysRoleScope sysRoleScope) {
        if (sysRoleScopeService.save(sysRoleScope)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody SysRoleScope sysRoleScope) {
        if (sysRoleScopeService.updateById(sysRoleScope)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (sysRoleScopeService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量添加角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:save')")
    @OperationLog
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<SysRoleScope> list) {
        if (sysRoleScopeService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 批量修改角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:update')")
    @OperationLog
    @PutMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<SysRoleScope> batchParam) {
        if (batchParam.update(sysRoleScopeService, "NAME")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 批量删除角色标识
     */
    @PreAuthorize("hasAuthority('system:sysRoleScope:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (sysRoleScopeService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
