package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.param.SysParamParam;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统参数Service
 *
 * <AUTHOR>
 * @since 2024-04-09 11:09:28
 */
public interface SysParamService extends IService<SysParam> {

    List<SysParam> list(SysParamParam param);

    SysParam getByParamName(String paramName);

    String getParamValue(@NotEmpty String paramName);

    void edit(@Validated SysParam sysParam, Map<String, List<MultipartFile>> fileMap);

    void removeByIds(String... ids);

    Map<String, String> getMap(String... paramName);
}
