package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息表控制器
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
//@RestController
//@RequestMapping("/api/common.system/user-info")
public class UserInfoController extends BaseController {
//    @Resource
    private UserInfoService userInfoService;

    /**
     * 分页查询用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:list')")
    @OperationLog
    @GetMapping("/page")
    public ApiResult<PageResult<UserInfo>> page(UserInfoParam param) {
        PageParam<UserInfo, UserInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(userInfoService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(userInfoService.pageRel(param));
    }

    /**
     * 查询全部用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:list')")
    @OperationLog
    @GetMapping()
    public ApiResult<List<UserInfo>> list(UserInfoParam param) {
        PageParam<UserInfo, UserInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(userInfoService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(userInfoService.listRel(param));
    }

    /**
     * 根据id查询用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:list')")
    @OperationLog
    @GetMapping("/{id}")
    public ApiResult<UserInfo> get(@PathVariable("id") Integer id) {
        return success(userInfoService.getById(id));
        // 使用关联查询
        //return success(userInfoService.getByIdRel(id));
    }

    /**
     * 添加用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:save')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> save(@RequestBody UserInfo userInfo) {
        if (userInfoService.save(userInfo)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody UserInfo userInfo) {
        if (userInfoService.updateById(userInfo)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (userInfoService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量添加用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:save')")
    @OperationLog
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<UserInfo> list) {
        if (userInfoService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 批量修改用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:update')")
    @OperationLog
    @PutMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<UserInfo> batchParam) {
        if (batchParam.update(userInfoService, "XGH")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 批量删除用户信息表
     */
    @PreAuthorize("hasAuthority('common.system:userInfo:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (userInfoService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
