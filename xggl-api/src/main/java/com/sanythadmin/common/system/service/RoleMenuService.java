package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.RoleMenu;
import com.sanythadmin.common.system.param.RoleMenuParam;
import com.sanythadmin.common.system.param.SysRoleMenuParam;

import java.util.List;

/**
 * 角色菜单Service
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:44
 */
public interface RoleMenuService extends IService<RoleMenu> {

    /**
     * 查询用户对应的菜单
     *
     * @param accountId 用户id
     * @param menuType  菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByAccount(String accountId, String roleId, Integer menuType);

    /**
     * 查询用户对应的菜单
     *
     * @param roleIds  角色id
     * @param menuType 菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByRoleIds(List<String> roleIds, Integer menuType);

    List<RoleMenu> list(SysRoleMenuParam param);

    void operation(RoleMenuParam param);
}
