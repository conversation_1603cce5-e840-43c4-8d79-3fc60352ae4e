package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/3.
 */
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysRoleMenuParam extends BaseParam {
    @QueryField(type = QueryType.EQ)
    private String roleId;
    @QueryField(type = QueryType.EQ)
    private String menuId;

    public SysRoleMenuParam() {
    }

    public SysRoleMenuParam(String roleId) {
        this.roleId = roleId;
    }
}
