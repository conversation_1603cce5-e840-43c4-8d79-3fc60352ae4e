<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.common.system.mapper.SysAccountMapper">

    <sql id="Base_Column_List">
        a.ID, a.STATUS,a.LOCK_STATUS, a.PASSWORD, a.USERNAME, a.ACTIVE_FLAG,
          a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE,
            a.ACCOUNT_EXPIRE_TIME,a.PASSWORD_EXPIRE_TIME
    </sql>

    <sql id="Base_where_sql">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <!--<if test="param.accountNonExpired != null">
            AND a.ACCOUNT_NON_EXPIRED = #{param.accountNonExpired}
        </if>
        <if test="param.accountNonLocked != null">
            AND a.ACCOUNT_NON_LOCKED = #{param.accountNonLocked}
        </if>
        <if test="param.credentialsNonExpired != null">
            AND a.CREDENTIALS_NON_EXPIRED = #{param.credentialsNonExpired}
        </if>-->
        <if test="param.status != null">
            AND a.STATUS = #{param.status}
        </if>
        <if test="param.lockStatus != null">
            AND a.LOCK_STATUS = #{param.lockStatus}
        </if>
        <if test="param.username != null">
            AND a.USERNAME LIKE concat(concat('%', #{param.username}), '%')
        </if>
        <!--<if test="param.activeFlag != null">
            AND a.ACTIVE_FLAG = #{param.activeFlag}
        </if>-->
        <if test="param.idCode != null">
            AND a.ID_CODE LIKE concat(concat('%', #{param.idCode}), '%')
        </if>
        <if test="param.telMobile != null">
            AND a.TEL_MOBILE LIKE concat(concat('%', #{param.telMobile}), '%')
        </if>
        <if test="param.realName != null">
            AND a.REAL_NAME LIKE concat(concat('%', #{param.realName}), '%')
        </if>
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_SYS_ACCOUNT a
        <where>
            <include refid="Base_where_sql"/>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.common.system.entity.SysAccount">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.common.system.result.SysAccountResult">
        select<include refid="Base_Column_List"/>,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from syt_sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from syt_sys_account_role b, syt_sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
        <include refid="Base_where_sql"/>
        group by
        <include refid="Base_Column_List"/>
    </select>

    <select id="queryAllAccount" resultType="com.sanythadmin.common.system.result.AccountRoleResult">
        SELECT A.USERNAME,A.REAL_NAME, A.TEL_MOBILE, A.GENDER, C.NAME ROLE_NAME, C.ID ROLE_ID,C.ROLE_SCOPE
        FROM SYT_SYS_ACCOUNT A, SYT_SYS_ACCOUNT_ROLE B, SYT_SYS_ROLE C
        WHERE A.ID = B.ACCOUNT_ID
        AND B.ROLE_ID = C.ID
        <include refid="Base_where_sql"/>
        <if test="param.roleId != null">
            AND C.ID = #{param.roleId}
        </if>
    </select>
</mapper>
