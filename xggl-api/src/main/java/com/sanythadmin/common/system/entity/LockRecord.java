package com.sanythadmin.common.system.entity;

import jakarta.persistence.Id;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户锁定记录
 * 记录用户被哪个限流策略锁定的详细信息
 *
 */
@Data
@Document(collection = "SYT_SYS_LOCK_RECORD")
public class LockRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    
    // 锁定类型常量
    public static final int TYPE_LOGIN_ATTEMPTS = 1;     // 登录失败次数过多
    public static final int TYPE_RATE_LIMIT_IP = 2;      // IP限流锁定
    public static final int TYPE_RATE_LIMIT_PATH = 3;    // 特定路径限流锁定

    // 锁定状态常量
    public static final int STATUS_LOCKED = 1;           // 锁定中
    public static final int STATUS_UNLOCKED = 2;         // 已解锁
    public static final int STATUS_EXPIRED = 3;          // 自动过期

    /**
     * 主键id
     **/
    @Id
    private String id;

    /**
     * 用户账号（可能为空，如果是纯IP锁定）
     **/
    private String username;

    /**
     * IP地址
     **/
    private String clientIp;

    /**
     * 锁定类型（1:登录失败次数过多, 2:IP限流锁定, 3:特定路径限流锁定, 4:手动锁定）
     **/
    private Integer lockType;

    /**
     * 锁定策略名称
     **/
    private String policyName;

    /**
     * 锁定策略详情
     **/
    private String policyDetails;

    /**
     * 触发锁定的路径（如果适用）
     **/
    private String triggerPath;

    /**
     * 触发锁定时的访问次数或失败次数
     **/
    private Integer triggerCount;

    /**
     * 锁定阈值
     **/
    private Integer lockThreshold;

    /**
     * 锁定时间窗口（分钟）
     **/
    private Integer lockWindowMinutes;

    /**
     * 锁定状态（1:锁定中, 2:已解锁, 3:自动过期）
     **/
    private Integer lockStatus;

    /**
     * 锁定开始时间
     **/
    private Date lockStartTime;

    /**
     * 锁定结束时间
     **/
    private Date lockEndTime;

    /**
     * 解锁时间（实际解锁时间）
     **/
    private Date unlockTime;

    /**
     * 解锁方式（auto:自动过期, manual:手动解锁）
     **/
    private String unlockMethod;

    /**
     * 操作系统
     **/
    private String os;

    /**
     * 设备名称
     **/
    private String device;

    /**
     * 浏览器类型
     **/
    private String browser;

    /**
     * User-Agent
     **/
    private String userAgent;

    /**
     * 备注信息
     **/
    private String comments;

    /**
     * 创建时间
     **/
    private Date createTime;

    /**
     * 修改时间
     **/
    private Date updateTime;
} 