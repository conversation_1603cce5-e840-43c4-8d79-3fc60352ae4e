package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.param.MenuParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单Service
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:31
 */
public interface SysMenuService extends IService<Menu> {

    public List<Menu> childList(String menuId, Integer menuType);
    public List<Menu> getAllChildList(String menuId);
    public List<Menu> getAllParentList(String menuId);
    public List<Menu> list(MenuParam param);
    public void saveMenu(Menu menu);
    public void updateMenu(Menu menu);
    public void deleteMenu(String id);
}
