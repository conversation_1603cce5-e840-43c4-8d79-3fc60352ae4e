package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.EncryptedTable;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 用户信息表查询参数
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
@EncryptedTable
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Gender xb;

    /**
     * 院系ID
     */
    @QueryField(ignore = true)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(ignore = true)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(ignore = true)
    private String bjid;

    /**
     * 年级ID
     */
    @QueryField(ignore = true)
    private String njid;

    /**
     * 培养层次ID
     */
    @QueryField(ignore = true)
    private String pyccid;

    /**
     * 手机号
     */
    private String sjh;

    /**
     * 学号/工号，对应账户表username
     */
    @QueryField(type = QueryType.IN_STR)
    private String xgh;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 籍贯
     */
    private String jg;

    /**
     * 民族
     */
    @QueryField(type = QueryType.EQ)
    private String mzmc;


    /**
     * 政治面貌
     */
    private String zzmmmc;

    /**
     * 校区名称
     */
    @QueryField(type = QueryType.EQ)
    private String xqmc;

    /**
     * 证件号码
     */
    @EncryptedColumn
    @QueryField(type = QueryType.EQ)
    private String zjhm;

    /**
     * 证件类型
     */
    @QueryField(type = QueryType.EQ)
    private String zjlx;

    /**
     * 学生类别（本，硕，博，进修生，留学生等）
     */
    @QueryField(type = QueryType.EQ)
    private String xslb;

    /**
     * 学制类型（三年制，四年制，五年制等）
     */
    @QueryField(type = QueryType.EQ)
    private String xzlx;

    /**
     * 人员状态ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String ryztid;

    @QueryField(type = QueryType.EQ)
    private UserType userType;

    @QueryField(ignore = true)
    private String roleId;
    /**
     * 高级查询参数, json
     */
    @QueryField(ignore = true)
    private String customQueryData;
    /**
     * 图表数据字段
     */
    @QueryField(ignore = true)
    private String chartDataField;

    /**
     * 扩展字段
     */
    private String bz1;
    private String bz2;
    private String bz3;
    private String bz4;
    private String bz5;
    private String bz6;
    private String bz7;
    private String bz8;
    private String bz9;
    private String bz10;
    private String bz11;
    private String bz12;
    private String bz13;
    private String bz14;
    private String bz15;
    private String bz16;
    private String bz17;
    private String bz18;
    private String bz19;
    private String bz20;
    private String bz21;
    private String bz22;
    private String bz23;
    private String bz24;
    private String bz25;
    private String bz26;
    private String bz27;
    private String bz28;
    private String bz29;
    private String bz30;
    private String bz31;
    private String bz32;
    private String bz33;
    private String bz34;
    private String bz35;
    private String bz36;
    private String bz37;
    private String bz38;
    private String bz39;
    private String bz40;
    private String bz41;
    private String bz42;
    private String bz43;
    private String bz44;
    private String bz45;
    private String bz46;
    private String bz47;
    private String bz48;
    private String bz49;
    private String bz50;
    private String bz51;
    private String bz52;
    private String bz53;
    private String bz54;
    private String bz55;
    private String bz56;
    private String bz57;
    private String bz58;
    private String bz59;
    private String bz60;
    private String bz61;
    private String bz62;
    private String bz63;
    private String bz64;
    private String bz65;
    private String bz66;
    private String bz67;
    private String bz68;
    private String bz69;
    private String bz70;
    private String bz71;
    private String bz72;
    private String bz73;
    private String bz74;
    private String bz75;
    private String bz76;
    private String bz77;
    private String bz78;
    private String bz79;
    private String bz80;
    private String bz81;
    private String bz82;
    private String bz83;
    private String bz84;
    private String bz85;
    private String bz86;
    private String bz87;
    private String bz88;
    private String bz89;
    private String bz90;
    private String bz91;
    private String bz92;
    private String bz93;
    private String bz94;
    private String bz95;
    private String bz96;
    private String bz97;
    private String bz98;
    private String bz99;
    private String bz100;

    public UserInfoParam() {

    }

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "xgh desc";
    }
}
