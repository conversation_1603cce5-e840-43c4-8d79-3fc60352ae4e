package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.RoleMenu;
import com.sanythadmin.common.system.mapper.RoleMenuMapper;
import com.sanythadmin.common.system.param.RoleMenuParam;
import com.sanythadmin.common.system.param.SysRoleMenuParam;
import com.sanythadmin.common.system.service.RoleMenuService;
import com.sanythadmin.common.system.service.SysMenuService;
import com.sanythadmin.project.code.constant.CodeConstants;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色菜单Service实现
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:12
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_ROLE_MENU)
@AllArgsConstructor
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenu> implements RoleMenuService {

    @Resource
    private SysMenuService sysMenuService;

    @Override
    public List<Menu> listMenuByAccount(String accountId, String roleId, Integer menuType) {
        return baseMapper.listMenuByAccount(accountId, roleId, menuType);
    }

    @Override
    public List<Menu> listMenuByRoleIds(List<String> roleIds, Integer menuType) {
        return baseMapper.listMenuByRoleIds(roleIds, menuType);
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<RoleMenu> list(SysRoleMenuParam param) {
        PageParam<RoleMenu, SysRoleMenuParam> pageParam = new PageParam<>(param);
        return baseMapper.selectList(pageParam.getWrapper());
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void operation(RoleMenuParam param) {
        List<Menu> childList = sysMenuService.getAllChildList(param.getMenuId());
        List<Menu> menus = listMenuByRoleIds(Collections.singletonList(param.getRoleId()), null);
        if (param.getAdd()) {
            List<Menu> parentList = sysMenuService.getAllParentList(param.getMenuId());
            childList.addAll(parentList);
            Set<String> menuIds = childList.stream().map((Menu::getMenuId)).collect(Collectors.toSet());
            menuIds.stream().filter((m) -> !menus.stream().anyMatch((d) -> d.getMenuId().equals(m))).forEach((m) -> {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(param.getRoleId());
                roleMenu.setMenuId(m);
                baseMapper.insert(roleMenu);
            });
        } else {
            Optional<Menu> optional = childList.stream().filter((m) -> param.getMenuId().equals(m.getMenuId())).findFirst();
            List<String> childMenuIds = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
            List<String> roleMenuIds = menus.stream().map((Menu::getMenuId)).collect(Collectors.toList());
            baseMapper.delete(new LambdaQueryWrapper<RoleMenu>()
                    .eq(RoleMenu::getRoleId, param.getRoleId()).in(RoleMenu::getMenuId, childMenuIds));
            roleMenuIds.removeAll(childMenuIds);
            removeParent(param.getRoleId(), optional.get(), roleMenuIds);
        }
    }

    private void removeParent(String roleId, Menu menu, List<String> roleMenuIds) {
        while (true) {
            Menu parent = sysMenuService.getById(menu.getParentId());
            if (parent == null) break;
            List<Menu> childList = sysMenuService.getAllChildList(parent.getMenuId());
            if (!CollectionUtils.isEmpty(childList)) {
                List<String> ids = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
                List<RoleMenu> list = baseMapper.selectList(new LambdaQueryWrapper<RoleMenu>()
                        .eq(RoleMenu::getRoleId, roleId).in(RoleMenu::getMenuId, ids)
                        .notIn(RoleMenu::getMenuId, parent.getMenuId()));
                if (CollectionUtils.isEmpty(list)) {
                    baseMapper.delete(new LambdaQueryWrapper<RoleMenu>()
                            .eq(RoleMenu::getRoleId, roleId).eq(RoleMenu::getMenuId, parent.getMenuId()));
                    roleMenuIds.remove(parent.getMenuId());
                }
            }
            menu = parent;
        }
    }
}
