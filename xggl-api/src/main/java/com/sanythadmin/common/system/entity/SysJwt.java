package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * jwt配置信息
 *
 * <AUTHOR>
 * @since 2025-05-23 15:43:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SYS_JWT")
@Entity
@Table(name = "SYT_SYS_JWT")
public class SysJwt implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * token过期时间, 单位秒
     */
    @Column(name = "TOKEN_EXPIRE_TIME", columnDefinition = ColumnType.NUMBER_10)
    @TableField("TOKEN_EXPIRE_TIME")
    private Long tokenExpireTime;

    /**
     * token快要过期自动刷新时间, 单位分钟
     */
    @Column(name = "TOKEN_REFRESH_TIME", columnDefinition = ColumnType.NUMBER_10)
    @TableField("TOKEN_REFRESH_TIME")
    private Integer tokenRefreshTime;

    /**
     * 生成token的密钥Key的base64字符
     */
    @Column(name = "TOKEN_KEY", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("TOKEN_KEY")
    private String tokenKey;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;
}
