package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.LockRecord;
import com.sanythadmin.common.system.param.LockRecordParam;
import com.sanythadmin.common.system.service.LockRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 锁定记录管理控制器
 *
 */
@Slf4j
@RestController
@RequestMapping("/api/system/lock-record")
public class LockRecordController extends BaseController {

    @Resource
    private LockRecordService lockRecordService;

    /**
     * 分页查询锁定记录
     */
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:lock-record:list')")
    public PageResult<LockRecord> page(@Validated LockRecordParam param) {
        PageResult<LockRecord> pageResult = lockRecordService.selectPage(param);
        return pageResult;
    }

    /**
     * 查询用户当前锁定状态
     */
    @GetMapping("/current-lock")
    @PreAuthorize("hasAuthority('system:lock-record:list')")
    public LockRecord getCurrentLock(@RequestParam(required = false) String username,@RequestParam(required = false) String clientIp) {
        return lockRecordService.getCurrentUserLock(username, clientIp);
    }

    /**
     * 手动解锁用户
     */
    @PostMapping("/unlock")
    @PreAuthorize("hasAuthority('system:lock-record:unlock')")
    @OperationLog(module = "锁定记录", comments = "手动解锁用户")
    public ApiResult<?> unlockUser(@RequestParam(required = false) String username,
                                  @RequestParam(required = false) String clientIp,
                                  @RequestParam(required = false) String reason) {
        if (!StringUtils.hasLength(username) && !StringUtils.hasLength(clientIp)) {
            return fail("用户名和IP地址不能都为空");
        }
        
        try {
            lockRecordService.unlockUser(username, clientIp, reason);
            
            String target = "";
            if (StringUtils.hasLength(username)) {
                target += "用户:" + username;
            }
            if (StringUtils.hasLength(clientIp)) {
                if (!target.isEmpty()) target += ", ";
                target += "IP:" + clientIp;
            }
            
            log.info("管理员手动解锁: {}, 原因: {}", target, reason);
            return success("解锁成功");
        } catch (Exception e) {
            log.error("解锁用户失败", e);
            return fail("解锁失败: " + e.getMessage());
        }
    }

    /**
     * 处理过期锁定记录
     */
    @PostMapping("/process-expired")
    @PreAuthorize("hasAuthority('system:lock-record:manage')")
    @OperationLog(module = "锁定记录", comments = "处理过期锁定记录")
    public ApiResult<?> processExpiredLocks() {
        try {
            lockRecordService.processExpiredLocks();
            return success("处理完成");
        } catch (Exception e) {
            log.error("处理过期锁定记录失败", e);
            return fail("处理失败: " + e.getMessage());
        }
    }

} 