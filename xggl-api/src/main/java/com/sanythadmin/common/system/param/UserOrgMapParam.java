package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户组织关系映射查询参数
 *
 * <AUTHOR>
 * @since 2025-06-25 13:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserOrgMapParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String pyccid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String njid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String bjid;

    /**
     * 学号/工号
     */
    @QueryField(type = QueryType.IN_STR)
    private String xgh;

}
