<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.common.system.mapper.SysModuleSetupMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CODE, NAME, PARENT_CODE, YEAR, SEMESTER, SFGLLC
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.code != null">
                        AND a.CODE LIKE concat(concat('%',#{param.code), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.parentCode != null">
                        AND a.PARENT_CODE LIKE concat(concat('%',#{param.parentCode), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.semester != null">
                        AND a.SEMESTER LIKE concat(concat('%',#{param.semester), '%')
                    </if>
                    <if test="param.sfgllc != null">
                        AND a.SFGLLC = #{param.sfgllc}
                    </if>
    </sql>
</mapper>
