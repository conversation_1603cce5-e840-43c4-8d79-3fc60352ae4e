package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.SysAccountParam;
import com.sanythadmin.common.system.result.AccountRoleResult;
import com.sanythadmin.common.system.result.SysAccountResult;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 系统账户信息Service
 *
 * <AUTHOR>
 * @since 2024-03-05 15:44:06
 */
public interface SysAccountService extends IService<SysAccount> {

    SysAccount getAccount(String username);

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<SysAccountResult>
     */
    PageResult<SysAccountResult> pageRel(SysAccountParam param);

    /**
     * 根据用户名获取账户信息, 多角色随机获取角色并获取权限信息
     *
     * @param username
     * @return
     */
    SysAccount getByUsername(String username);

    /**
     * 根据用户名获取账户信息, 获取指定角色信息同时获取权限信息
     *
     * @param username
     * @return
     */
    SysAccount getByUsernameAndRoleId(String username, String roleId);

    /**
     * 新增或编辑账户信息
     *
     * @param account
     */
    void edit(SysAccount account);

    /**
     * 修改密码
     *
     * @param id
     * @param password
     */
    void modifyPassword(@NotNull String id, @NotNull String password);

    /**
     * 删除账户信息
     *
     * @param idList
     */
    void removeAccount(List<String> idList);

    /**
     * 查询全部账户信息
     */
    PageResult<AccountRoleResult> queryAllAccount(SysAccountParam param);

    void updateByProperty(String accountId, SFunction<SysAccount, ?> column, Object value);

    SysAccount checkAccountBasicInfoAndGet(UserInfo userInfo);
    void setAccountBasicInfo(@NotNull UserInfo userInfo, @NotNull SysAccount account);
    void updateAccountBasicInfo(UserInfo userInfo);
}
