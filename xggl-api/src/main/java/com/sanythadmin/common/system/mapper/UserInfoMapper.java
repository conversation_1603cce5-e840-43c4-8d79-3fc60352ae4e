package com.sanythadmin.common.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.common.system.entity.UserInfo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * 用户信息表Mapper
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
public interface UserInfoMapper extends MyMPJBaseMapper<UserInfo> {
    @DataPermission(alias = "SYT_USER_INFO")
    default IPage<UserInfo> selectPageDP(IPage page, Wrapper<UserInfo> queryWrapper) {
        return selectPage(page, queryWrapper);
    }

    UserInfo get(@Param("xgh") String xgh);

    @Override
    default UserInfo selectById(Serializable id){
        return get((String) id);
    }
}
