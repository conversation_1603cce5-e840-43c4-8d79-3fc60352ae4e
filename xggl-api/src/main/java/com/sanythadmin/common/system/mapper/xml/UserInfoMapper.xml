<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.common.system.mapper.UserInfoMapper">
    <resultMap id="UserInfoResultMap" type="com.sanythadmin.common.system.entity.UserInfo" autoMapping="true">
        <id property="userType" column="user_type"/>
        <collection property="userOrgMaps" ofType="com.sanythadmin.common.system.entity.UserOrgMap" autoMapping="true">
            <id property="id" column="id"/>
            <id property="pyccid" column="pyccid"/>
            <id property="njid" column="njid"/>
            <id property="xyid" column="xyid"/>
            <id property="zyid" column="zyid"/>
            <id property="bjid" column="bjid"/>
            <id property="xgh" column="xgh"/>
        </collection>
    </resultMap>

    <select id="get" parameterType="java.lang.String" resultMap="UserInfoResultMap">
        SELECT U.*, O.ID, O.XYID, O.ZYID, O.BJID, O.NJID, O.PYCCID
        FROM SYT_USER_INFO U
                 LEFT JOIN SYT_USER_ORG_MAP O ON U.XGH = O.XGH
        WHERE U.XGH = #{xgh}
    </select>
</mapper>
