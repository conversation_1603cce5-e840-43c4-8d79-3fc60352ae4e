package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysModuleSetup;
import com.sanythadmin.common.system.param.SysModuleSetupParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统设置/系统模块参数
 *
 * <AUTHOR>
 * @since 2025-01-16 16:25:33
 */
@RestController
@RequestMapping("/api/system/sys-module-setup")
public class SysModuleSetupController extends BaseController {
    @Resource
    private SysModuleSetupService sysModuleSetupService;

    /**
     * 分页查询（权限标识：system:sysModuleSetup:list）
     */
    @PreAuthorize("hasAuthority('system:sysModuleSetup:list')")
    @GetMapping("/page")
    public PageResult<SysModuleSetup> page(SysModuleSetupParam param) {
        PageParam<SysModuleSetup, SysModuleSetupParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = sysModuleSetupService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部（权限标识：system:sysModuleSetup:list）
     */
    @PreAuthorize("hasAuthority('system:sysModuleSetup:list')")
    @GetMapping()
    public List<SysModuleSetup> list(SysModuleSetupParam param) {
        PageParam<SysModuleSetup, SysModuleSetupParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return sysModuleSetupService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询（权限标识：system:sysModuleSetup:list）
     */
    @PreAuthorize("hasAuthority('system:sysModuleSetup:list')")
    @GetMapping("/{id}")
    public SysModuleSetup get(@PathVariable("id") String id) {
        return sysModuleSetupService.getById(id);
    }

    /**
     * 添加或修改（权限标识：system:sysModuleSetup:operation）
     */
    @PreAuthorize("hasAuthority('system:sysModuleSetup:operation')")
    @OperationLog(module = "", comments = "保存")
    @PostMapping("/operation")
    public void save(@RequestBody SysModuleSetup sysModuleSetup) {
        if (StringUtils.hasLength(sysModuleSetup.getId())) {
            sysModuleSetupService.updateById(sysModuleSetup);
        } else {
            sysModuleSetupService.save(sysModuleSetup);
        }
    }

    /**
     * 批量删除（权限标识：system:sysModuleSetup:remove）
     */
    @PreAuthorize("hasAuthority('system:sysModuleSetup:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        sysModuleSetupService.removeByIds(ids);
    }
}
