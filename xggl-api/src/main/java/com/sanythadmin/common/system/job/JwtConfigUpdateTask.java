package com.sanythadmin.common.system.job;

import com.sanythadmin.common.system.service.SysJwtService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * jwt配置信息更新任务
 *
 * <AUTHOR>
 * @since 2025-05-23 16:10:41
 */
@Component
public class JwtConfigUpdateTask {
    @Resource
    private SysJwtService sysJwtService;

    // 每天凌晨3:30执行
    @Scheduled(cron = "0 30 3 * * ?")
    public void execute() {
        sysJwtService.removeAndUpdate();
    }
}
