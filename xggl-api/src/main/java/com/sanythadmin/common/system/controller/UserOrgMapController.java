package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.system.service.UserOrgMapService;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户组织关系映射控制器
 *
 * <AUTHOR>
 * @since 2025-06-25 13:55:43
 */
@RestController
@RequestMapping("/api/system/user-org-map")
public class UserOrgMapController extends BaseController {
    @Resource
    private UserOrgMapService userOrgMapService;

    /**
     * 分页查询用户组织关系映射（权限标识：system:userOrgMap:list）
     */
    @PreAuthorize("hasAuthority('system:userOrgMap:list')")
    @GetMapping("/page")
    public PageResult<UserOrgMap> page(UserOrgMapParam param) {
        PageParam<UserOrgMap, UserOrgMapParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = userOrgMapService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户组织关系映射（权限标识：system:userOrgMap:list）
     */
    @PreAuthorize("hasAuthority('system:userOrgMap:list')")
    @GetMapping()
    public List<UserOrgMap> list(UserOrgMapParam param) {
        PageParam<UserOrgMap, UserOrgMapParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return userOrgMapService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户组织关系映射（权限标识：system:userOrgMap:list）
     */
    @PreAuthorize("hasAuthority('system:userOrgMap:list')")
    @GetMapping("/{id}")
    public UserOrgMap get(@PathVariable("id") String id) {
        return userOrgMapService.getById(id);
    }

    /**
     * 添加或修改用户组织关系映射（权限标识：system:userOrgMap:operation）
     */
    @PreAuthorize("hasAuthority('system:userOrgMap:operation')")
    @OperationLog(module = "用户组织关系映射", comments = "保存用户组织关系映射")
    @PostMapping("/operation")
    public void save(@RequestBody UserOrgMap userOrgMap) {
        if (StringUtils.hasLength(userOrgMap.getId())) {
            userOrgMapService.updateById(userOrgMap);
        } else {
            userOrgMapService.save(userOrgMap);
        }
    }

    /**
     * 批量删除用户组织关系映射（权限标识：system:userOrgMap:remove）
     */
    @PreAuthorize("hasAuthority('system:userOrgMap:remove')")
    @OperationLog(module = "用户组织关系映射", comments = "批量删除用户组织关系映射")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        userOrgMapService.removeByIds(ids);
    }
}
