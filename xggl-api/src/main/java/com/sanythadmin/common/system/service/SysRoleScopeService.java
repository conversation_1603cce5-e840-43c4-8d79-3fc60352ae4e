package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRoleScope;
import com.sanythadmin.common.system.param.SysRoleScopeParam;

import java.util.List;

/**
 * 角色标识Service
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
public interface SysRoleScopeService extends IService<SysRoleScope> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<SysRoleScope>
     */
    PageResult<SysRoleScope> pageRel(SysRoleScopeParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<SysRoleScope>
     */
    List<SysRoleScope> listRel(SysRoleScopeParam param);

    /**
     * 根据id查询
     *
     * @param name 角色标识名称
     * @return SysRoleScope
     */
    SysRoleScope getByIdRel(String name);

}
