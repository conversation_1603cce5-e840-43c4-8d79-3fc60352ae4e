<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.common.system.mapper.SysAccountRoleMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_SYS_ACCOUNT_ROLE a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.accountId != null">
                AND a.ACCOUNT_ID LIKE '%'||#{param.accountId}||'%'
            </if>
            <if test="param.roleId != null">
                AND a.ROLE_ID LIKE '%'||#{param.roleId}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.common.system.entity.SysAccountRole">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.common.system.entity.SysAccountRole">
        <include refid="selectSql"></include>
    </select>

    <select id="selectByAccountId" resultType="com.sanythadmin.common.system.entity.SysRole">
        SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = #{accountId}
        ) <if test="roleId!=null">
         AND id = #{roleId}
    </if>
    </select>
</mapper>
