package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRoleScope;
import com.sanythadmin.common.system.mapper.SysRoleScopeMapper;
import com.sanythadmin.common.system.param.SysRoleScopeParam;
import com.sanythadmin.common.system.service.SysRoleScopeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色标识Service实现
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
@Service
public class SysRoleScopeServiceImpl extends ServiceImpl<SysRoleScopeMapper, SysRoleScope> implements SysRoleScopeService {

    @Override
    public PageResult<SysRoleScope> pageRel(SysRoleScopeParam param) {
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<SysRoleScope> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<SysRoleScope> listRel(SysRoleScopeParam param) {
        List<SysRoleScope> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public SysRoleScope getByIdRel(String name) {
        SysRoleScopeParam param = new SysRoleScopeParam();
        param.setName(name);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
