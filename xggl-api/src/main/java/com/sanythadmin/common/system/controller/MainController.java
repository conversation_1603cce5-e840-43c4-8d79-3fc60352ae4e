package com.sanythadmin.common.system.controller;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.IgnoreResponseBodyAdvice;
import com.sanythadmin.common.core.security.JwtSubject;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.security.rsa.SecJS;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.MyMediaType;
import com.sanythadmin.common.system.entity.*;
import com.sanythadmin.common.system.param.LoginParam;
import com.sanythadmin.common.system.param.SysParamParam;
import com.sanythadmin.common.system.result.CaptchaResult;
import com.sanythadmin.common.system.result.LoginResult;
import com.sanythadmin.common.system.result.LoginUserResult;
import com.sanythadmin.common.system.service.*;
import com.wf.captcha.SpecCaptcha;
import io.jsonwebtoken.Claims;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 登录认证控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:11
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class MainController extends BaseController {
    @Resource
    private SysJwtService sysJwtService;
    @Resource
    private LoginRecordService loginRecordService;
    @Resource
    private SysAccountService accountService;
    @Resource
    private SysAccountRoleService accountRoleService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private LoginValidationService loginValidationService;
    @Resource
    private SysParamService sysParamService;

    @PostMapping(value = "/login")
//    @RateLimited()    //改用ConfigurableRateLimitInterceptor全局限流
    public LoginResult login(@RequestBody @Validated LoginParam param,
                             HttpServletRequest request) {

        String clientIp = CommonUtil.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");

        //防止批量分配
        loginValidationService.validateLoginParams(param);
        LoginValidationService.DecryptResult decryptResult = loginValidationService.decryptLoginParams(param);
        loginValidationService.validateCaptcha(decryptResult.getCid(), decryptResult.getCode());
        loginValidationService.checkUserLoginAttempts(decryptResult.getUsername(), clientIp, request);
        SysAccount account = loginValidationService.validateAccount(decryptResult.getUsername(),
                decryptResult.getPassword(),
                request, clientIp, userAgent);
        loginValidationService.clearFailedAttempts(decryptResult.getUsername(), clientIp);
        return loginValidationService.generateLoginResult(account);
    }


    /**
     * 用户登录
     *
     * @param param
     * @param request
     * @return
     */
    /*@PostMapping(value = "/login")
    public LoginResult login(@RequestBody LoginParam param, HttpServletRequest request) {
        AssertUtil.hasLength(param.getCid(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getCode(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getUsername(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getPassword(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        DecryptMap usernameDecryptMap = null;
        DecryptMap passwordDecryptMap = null;
        DecryptMap cidDecryptMap = null;
        DecryptMap codeDecryptMap = null;
        try {
            usernameDecryptMap = SecJS.checkDynamicKeyAndGet(param.getUsername());
            passwordDecryptMap = SecJS.checkDynamicKeyAndGet(param.getPassword());
            cidDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCid());
            codeDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCode());
        } catch (RSADecryptException e) {
            log.error(e.getMessage());
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        } finally {
            if (usernameDecryptMap != null)
                SecJS.clearDynamicKey(usernameDecryptMap.getKey());
        }

        String cid = cidDecryptMap.getValue();
        String code = codeDecryptMap.getValue();
        String username = usernameDecryptMap.getValue();
        String password = passwordDecryptMap.getValue();
        Object val = redisTemplate.opsForValue().get(cid);
        boolean mark = (val != null && StringUtils.hasLength(code) && String.valueOf(val).equalsIgnoreCase(code));
        if (val != null) redisTemplate.delete(cid);
        AssertUtil.isTrue(mark, ErrorInfo.CAPTCHA_ERROR_OR_EXPIRED);

        SysAccount account = accountService.getByUsername(username);
        if (account == null) {
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD, request);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }

        if (!UserInfoUtil.comparePassword(password, account.getPassword())) {
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD, request);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }
        loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGIN, null, request);
        // 签发token
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        JwtSubject subject = JwtSubject.genCacheKey(username, account.getRole().getId());
        Long expireTime = config.getTokenExpireTime();
        String access_token = JwtUtil.buildToken(subject, expireTime, config.getTokenKey());
        redisTemplate.opsForValue().set(subject.getCacheKey(), account, expireTime, TimeUnit.SECONDS);
        return LoginResult.build(access_token, account.getRealName(), account.getRole());
    }*/

    /**
     * 退出登录
     *
     * @param request
     */
    @GetMapping(value = "/logout")
    public void logout(HttpServletRequest request) {
        SysAccount account = getAccount();
        if (account != null) {
            SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
            Claims claims = JwtSubject.parseClaims(config.getTokenKey(), request);
            JwtSubject subject = JwtUtil.getJwtSubject(claims);
            loginRecordService.saveAsync(account.getUsername(), LoginRecord.TYPE_LOGOUT, null, request);
            if (subject != null)
                redisTemplate.delete(subject.getCacheKey());
            String key = String.format(Constants.USER_DATA_SCOPE_KEY, account.getUsername());
            redisTemplate.delete(key);
            long expiration = (claims.getExpiration().getTime() - new Date().getTime()) / 1000;
            redisTemplate.opsForValue().set(subject.logoutCacheKey(), DateUtil.getDateTime(),
                    expiration, TimeUnit.SECONDS);
        }
    }


    /**
     * 获取登录用户信息
     *
     * @return
     */
    @PostMapping(value = "/auth/user")
    public LoginUserResult userInfo() {
        SysAccount account = getAccount();
        UserInfo userInfo = userInfoService.get(account.getUsername());
        List<Menu> authorities = account.getAuthorities();
        String parentId = "11111111111111111111111111111111";   // 接口目录parentId
        if (!CollectionUtils.isEmpty(authorities))
            authorities.removeIf(m -> Objects.equals(0, m.getMenuType()) && Objects.equals(parentId, m.getParentId()));
        UserInfoUtil.codeTextSet(Collections.singletonList(userInfo));
        return new LoginUserResult(userInfo, account.getRole(), authorities);
    }

    /**
     * 获取用户角色列表
     *
     * @return
     */
    @PostMapping(value = "/auth/userRoleList")
    public List<SysRole> userRoleList() {
        SysAccount account = getAccount();
        return accountRoleService.selectByAccountId(account.getId(), null);
    }

    /**
     * 切换角色
     *
     * @param roleId
     */
    @IgnoreResponseBodyAdvice
    @PostMapping("/auth/switch-role/{roleId}")
    public ApiResult<?> switchRole(@PathVariable String roleId, HttpServletRequest request
            , HttpServletResponse response) {
        SysAccount account = getAccount();
        List<SysRole> roleList = accountRoleService.selectByAccountId(account.getId(), roleId);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(roleList), "操作失败, 角色错误");
        account = accountService.getByUsernameAndRoleId(account.getUsername(), roleId);
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        Claims claims = JwtSubject.parseClaims(config.getTokenKey(), request);
        JwtSubject subject = JwtUtil.getJwtSubject(claims);
        redisTemplate.opsForValue().set(subject.getCacheKey(), account, config.getTokenExpireTime(), TimeUnit.SECONDS);
        accountService.updateByProperty(account.getId(), SysAccount::getLastUsedRole, roleId);
        String token = JwtUtil.buildToken(new JwtSubject(subject.getCacheKey(), roleId), config.getTokenExpireTime(), config.getTokenKey());
        response.addHeader(Constants.TOKEN_HEADER_NAME, token);
        return success();
    }


    /**
     * 图形验证码
     *
     * @return
     */
    @GetMapping(value = "/captcha")
    public CaptchaResult captcha() {
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        String cid = "captcha:" + CommonUtil.getUUID();
        redisTemplate.opsForValue().set(cid, specCaptcha.text().toLowerCase(), 60, TimeUnit.SECONDS);
        return new CaptchaResult(cid, specCaptcha.toBase64());
    }

    @IgnoreResponseBodyAdvice
    @GetMapping(value = "/sec_js", produces = {MyMediaType.TEXT_JAVASCRIPT_VALUE})
    public String secJs() {
        return SecJS.newDynamicKeyScript();
    }

    /**
     * CAS认证登录
     *
     * @return
     */
    @PostMapping("/cas/loginUrl")
    public String casLoginUrl() {
        return loginValidationService.casLoginUrl();
    }

    @PostMapping("/cas/logoutUrl")
    public String casLogoutUrl() {
        return loginValidationService.casLogoutUrl();
    }

    @PostMapping("/cas/callback")
    public LoginResult casCallback(@RequestParam("ticket") String ticket, HttpServletRequest request) {
        return loginValidationService.casLoginCallback(ticket, request);
    }

    /**
     * 系统相关参数信息（学校名称,logo,地址等）
     *
     * @return
     */
    @PostMapping("/system-info")
    public List<SysParam> systemInfo(@RequestBody SysParamParam param) {
        param.setParamMode(Constants.PARAM_MODEL_SYSTEM_INFO);
        return sysParamService.list(param);
    }
}
