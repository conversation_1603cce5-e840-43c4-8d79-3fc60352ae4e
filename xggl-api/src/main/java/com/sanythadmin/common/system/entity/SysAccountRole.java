package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
/**
 * 系统用户角色关系
 *
 * <AUTHOR>
 * @since 2024-03-05 16:40:52
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SYS_ACCOUNT_ROLE")
@Table(name = "SYT_SYS_ACCOUNT_ROLE")
public class SysAccountRole implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 账户ID
     */
    @Column(name = "ACCOUNT_ID")
    @TableField("ACCOUNT_ID")
    private String accountId;

    /**
     * 角色ID
     */
    @Column(name = "ROLE_ID")
    @TableField("ROLE_ID")
    private String roleId;

    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;
//    @Column(name = "DELETED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
//    @TableLogic
//    private Integer deleted;

}
