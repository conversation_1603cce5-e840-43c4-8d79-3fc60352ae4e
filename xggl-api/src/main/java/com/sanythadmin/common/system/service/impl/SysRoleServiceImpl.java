package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.code.constant.CodeConstants;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色服务实现类
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:11
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_ROLE)
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Override
    public PageResult<SysRole> page(RoleParam param) {
        PageParam<SysRole, RoleParam> page = new PageParam<>(param);
        page = baseMapper.selectPage(page, page.getOrderWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<SysRole> list(RoleParam param) {
        PageParam<SysRole, RoleParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void saveRole(SysRole sysRole) {
        if (baseMapper.selectCount(new LambdaQueryWrapper<SysRole>().eq(SysRole::getName, sysRole.getName())) > 0)
            AssertUtil.throwMessage("角色名称已存在");
        baseMapper.insert(sysRole);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateRole(SysRole sysRole) {
        if (sysRole.getName() != null && baseMapper.selectCount(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getName, sysRole.getName())
                .ne(SysRole::getId, sysRole.getId())) > 0)
            AssertUtil.throwMessage("角色名称已存在");
        baseMapper.updateById(sysRole);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void batchRemove(List<String> ids) {
        baseMapper.deleteBatchIds(ids);
    }
}
