package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.FieldMask;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.Mask;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2024/7/15.
 */
@Data
@MappedSuperclass
public class BaseUserInfo implements Serializable {
    /**
     * 学号/工号，对应账户表username
     */
    @Id
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "XGH", type = IdType.ASSIGN_UUID)
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 性别（男：0， 女：1）
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;


    /**
     * 手机号
     */
    @Column(name = "SJH")
    @TableField("SJH")
    private String sjh;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ")
    @TableField("CSRQ")
    private String csrq;

    /**
     * 籍贯
     */
    @Column(name = "JG")
    @TableField("JG")
    private String jg;

    /**
     * 民族
     */
    @Column(name = "MZMC")
    @TableField("MZMC")
    private String mzmc;


    /**
     * 政治面貌
     */
    @Column(name = "ZZMMMC")
    @TableField("ZZMMMC")
    private String zzmmmc;

    /**
     * 校区名称
     */
    @Column(name = "XQMC")
    @TableField("XQMC")
    private String xqmc;

    /**
     * 证件号码
     */
    @FieldMask(value = Mask.ID_CARD)
    @EncryptedColumn
    @Column(name = "ZJHM")
    @TableField("ZJHM")
    private String zjhm;

    /**
     * 证件类型
     */
    @Column(name = "ZJLX")
    @TableField("ZJLX")
    private String zjlx;

    /**
     * 学生类别（本，硕，博，进修生，留学生等）
     */
    @Column(name = "XSLB")
    @TableField("XSLB")
    private String xslb;

    /**
     * 学制类型（三年制，四年制，五年制等）
     */
    @Column(name = "XZLX")
    @TableField("XZLX")
    private String xzlx;

    /**
     * 人员状态ID
     */
    @Column(name = "RYZTID")
    @TableField("RYZTID")
    private String ryztid;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;
    /**
     * 照片信息
     */
    @Column(name = "PHOTO")
    @TableField(value = "PHOTO")
    private String photo;


    // 扩展临时字段
    /**
     * 院系ID
     */
    @TableField(exist = false)
    @Transient
    private String xyid;

    /**
     * 专业ID
     */
    @TableField(exist = false)
    @Transient
    private String zyid;

    /**
     * 班级ID
     */
    @TableField(exist = false)
    @Transient
    private String bjid;

    /**
     * 年级ID
     */
    @TableField(exist = false)
    @Transient
    private String njid;

    /**
     * 培养层次ID
     */
    @TableField(exist = false)
    @Transient
    private String pyccid;

    /**
     * 角色
     */
    @Transient
    @TableField(exist = false)
    private String roleId;
    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;
    /**
     * 人员状态
     */
    @TableField(exist = false)
    @Transient
    private String ryzt;

    @TableField(exist = false)
    @Transient
    private List<UserOrgMap> userOrgMaps;

    public String getXyid() {
        return getOrgProperty(UserOrgMap::getXyid, xyid);
    }

    public String getZyid() {
        return getOrgProperty(UserOrgMap::getZyid, xyid);
    }

    public String getBjid() {
        return getOrgProperty(UserOrgMap::getBjid, bjid);
    }

    public String getNjid() {
        return getOrgProperty(UserOrgMap::getNjid, njid);
    }

    public String getPyccid() {
        return getOrgProperty(UserOrgMap::getPyccid, pyccid);
    }

    protected String getOrgProperty(Function<? super UserOrgMap, String> function, String oldValue) {
        List<UserOrgMap> orgMaps = getUserOrgMaps();
        if (!CollectionUtils.isEmpty(orgMaps)) {
            Set<String> collect = orgMaps.stream().map(function).
                    filter(StringUtils::hasText).collect(Collectors.toSet());
            return CommonUtil.strJoin(collect);
        }
        return oldValue;
    }
}
