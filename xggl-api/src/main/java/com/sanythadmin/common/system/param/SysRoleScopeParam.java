package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 角色标识查询参数
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysRoleScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 角色标识名称
     */
    @QueryField(type = QueryType.EQ)
    private String name;

    /**
     * 角色标识名称对应类型（暂时保留）
     */
    private String type;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort asc";
    }
}
