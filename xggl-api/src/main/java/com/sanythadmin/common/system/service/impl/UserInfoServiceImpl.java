package com.sanythadmin.common.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.enums.JoinRelation;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.mapper.SysAccountMapper;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.mapper.UserOrgMapMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.mapper.CodeBjbMapper;
import com.sanythadmin.project.code.mapper.CodeDwbMapper;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.userInfo.param.CustomQueryParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户信息表Service实现
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
@RequiredArgsConstructor
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {
    public final SysAccountMapper sysAccountMapper;
    public final SysAccountRoleMapper accountRoleMapper;
    public final SysParamService sysParamService;
    public final SysAccountService accountService;
    public final UserOrgMapMapper userOrgMapMapper;
    public final CodeBjbMapper codeBjbMapper;
    public final CodeDwbMapper codeDwbMapper;

    @Override
    public void saveUserInfo(UserInfo userInfo) {
        String paramValue = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
        createUserAndAccountInfo(userInfo, true, paramValue);
    }

    @Override
    public void removeUserInfo(String xgh) {
        sysAccountMapper.delete(new LambdaQueryWrapper<SysAccount>().eq(SysAccount::getUsername, xgh));
        baseMapper.delete(new LambdaQueryWrapper<UserInfo>().eq(UserInfo::getXgh, xgh));
        accountRoleMapper.delete(new LambdaQueryWrapper<SysAccountRole>().eq(SysAccountRole::getUsername, xgh));
    }

    @Transactional
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Override
    public void updateStatus(String username, String statusId) {
        baseMapper.update(new LambdaUpdateWrapper<UserInfo>()
                .eq(UserInfo::getXgh, username).set(UserInfo::getRyztid, statusId));
    }

    @Override
    public void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern) {
        createUserAndAccountInfo(userInfo, checkAccountInfo, passwordPattern,
                null, null);
    }

    @Override
    public void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos) {
        if (checkAccountInfo)
            checkUserInfo(userInfo);
        SysAccount account = new SysAccount();
        account.setUsername(userInfo.getXgh());
        accountService.setAccountBasicInfo(userInfo, account);
        String password = UserInfoUtil.buildInitPassword(account, passwordPattern);
        password = UserInfoUtil.encodePassword(password);
        account.setPassword(password);
        account.setStatus(State.ENABLED);
        sysAccountMapper.insert(account);
        String[] roleIdArray = CommonUtil.split(userInfo.getRoleId());
        for (String s : roleIdArray) {
            SysAccountRole accountRole = new SysAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(s);
            accountRole.setUsername(userInfo.getXgh());
            accountRoleMapper.insert(accountRole);
        }
        baseMapper.insert(userInfo);
        List<UserOrgMap> userOrgMap = getUserOrgMap(userInfo, classInfos, deptInfos);
        userOrgMap.forEach(userOrgMapMapper::insert);
    }

    @Override
    public void updateUserOrgMap(UserInfo userInfo) {
        List<UserOrgMap> userOrgMap = getUserOrgMap(userInfo, null, null);
        userOrgMapMapper.delete(new LambdaQueryWrapper<UserOrgMap>()
                .eq(UserOrgMap::getXgh, userInfo.getXgh()));
        userOrgMap.forEach(userOrgMapMapper::insert);
    }

    @Override
    public void updateUserOrgMap(UserInfo userInfo, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos) {
        if (StringUtils.hasText(userInfo.getBjid()) || StringUtils.hasText(userInfo.getXyid())) {
            List<UserOrgMap> userOrgMap = getUserOrgMap(userInfo, classInfos, deptInfos);
            userOrgMapMapper.delete(new LambdaQueryWrapper<UserOrgMap>()
                    .eq(UserOrgMap::getXgh, userInfo.getXgh()));
            userOrgMap.forEach(userOrgMapMapper::insert);
        }
    }

    protected List<UserOrgMap> getUserOrgMap(UserInfo userInfo, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos) {
        List<UserOrgMap> list = new ArrayList<>();
        if (Objects.equals(UserType.STUDENT, userInfo.getUserType())) {
            if (CollectionUtils.isEmpty(classInfos)) {
                CodeBjb codeBjb = codeBjbMapper.selectById(userInfo.getBjid());
                LinkedHashMap hashMap = JSON.parseObject(JSON.toJSONString(codeBjb), LinkedHashMap.class);
                classInfos = List.of(hashMap);
            }

            Map<Object, LinkedHashMap> classInfoMap = classInfos.stream()
                    .collect(Collectors.toMap(m -> m.get("id"), m -> m));
            String[] array = CommonUtil.split(userInfo.getBjid());
            for (String classId : array) {
                LinkedHashMap hashMap = classInfoMap.get(classId);
                UserOrgMap obj = new UserOrgMap(null, userInfo.getPyccid(), toStringVal(hashMap, "njid"),
                        toStringVal(hashMap, "xyid"), toStringVal(hashMap, "zyid"), toStringVal(hashMap, "id"),
                        userInfo.getXgh());
                list.add(obj);
            }
        } else {
            if (CollectionUtils.isEmpty(deptInfos)) {
                List<CodeDwb> codeDwbs = codeDwbMapper.selectBatchIds(Arrays.asList(CommonUtil.split(userInfo.getXyid())));
                deptInfos = JSONArray.parseArray(JSON.toJSONString(codeDwbs), LinkedHashMap.class);
            }
            Map<Object, LinkedHashMap> deptInfoMap = deptInfos.stream()
                    .collect(Collectors.toMap(m -> m.get("id"), m -> m));
            String[] array = CommonUtil.split(userInfo.getXyid());
            for (String id : array) {
                LinkedHashMap hashMap = deptInfoMap.get(id);
                UserOrgMap obj = new UserOrgMap(null, null, null,
                        toStringVal(hashMap, "id"), null, null, userInfo.getXgh());
                list.add(obj);
            }
        }
        return list;
    }

    private String toStringVal(LinkedHashMap hashMap, String key) {
        return hashMap.get(key) == null ? null : String.valueOf(hashMap.get(key));
    }

    private void checkUserInfo(UserInfo userInfo) {
        if (!StringUtils.hasText(userInfo.getRoleId()))
            AssertUtil.throwMessage("缺少角色信息");
        if (userInfo.getUserType() == null)
            AssertUtil.throwMessage("缺少用户类型");
        SysAccount account = accountService.getAccount(userInfo.getXgh());
        if (!Objects.isNull(account))
            AssertUtil.throwMessage("学号或工号已存在");
        if (StringUtils.hasText(userInfo.getSjh())) {
            account = accountService.getAccount(userInfo.getZjhm());
            if (!Objects.isNull(account))
                AssertUtil.throwMessage("手机号码已存在");
        }
        if (StringUtils.hasText(userInfo.getZjhm())) {
            account = accountService.getAccount(userInfo.getZjhm());
            if (!Objects.isNull(account))
                AssertUtil.throwMessage("证件号已存在");
        }
    }

    @Override
    public UserInfo get(String xgh) {
        UserInfoParam userInfoParam = new UserInfoParam();
        userInfoParam.setXgh(xgh);
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(userInfoParam, null, null, null);
        List<UserInfo> userInfos = baseMapper.selectJoinList(UserInfo.class, queryWrapper);
        return CommonUtil.listGetOne(userInfos);
    }

    @Override
    public List<UserInfo> list(UserInfoParam param) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, null, null, null);
        return baseMapper.selectJoinList(UserInfo.class, queryWrapper);
    }

    @Override
    public List<UserInfo> list(UserInfoParam param, SFunction<UserInfo, ?>... sFunction) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, null, sFunction, null);
        return baseMapper.selectJoinList(UserInfo.class, queryWrapper);
    }

    @Override
    public List<UserInfo> list(UserInfoParam param, CodeXsztParam codeXsztParam, SFunction<UserInfo, ?>... sFunction) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, codeXsztParam, sFunction);
        return baseMapper.selectJoinList(UserInfo.class, queryWrapper);
    }

    @Override
    public List<UserInfo> listWithPermission(UserInfoParam param, SFunction<UserInfo, ?>... sFunction) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, null, sFunction, null);
        return baseMapper.selectJoinListWithPermission(UserInfo.class, queryWrapper);
    }

    @Override
    public List<UserInfo> listWithPermission(UserInfoParam param, CodeXsztParam codeXsztParam, SFunction<UserInfo, ?>... sFunction) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, codeXsztParam, sFunction);
        return baseMapper.selectJoinListWithPermission(UserInfo.class, queryWrapper);
    }

    @Override
    public Long countWithPermission(UserInfoParam param, CodeXsztParam codeXsztParam) {
        MPJLambdaWrapper<UserInfo> queryWrapper = getQueryWrapper(param, codeXsztParam);
        queryWrapper.getSelectColumns().clear();
        return baseMapper.selectJoinCountWithPermission(queryWrapper);
    }

    @Override
    public MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, String... columns) {
        return getQueryWrapper(param, null, null, columns);
    }

    @Override
    public MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, CodeXsztParam codeXsztParam, String... columns) {
        return getQueryWrapper(param, codeXsztParam, null, columns);
    }

    @Override
    public MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, CodeXsztParam codeXsztParam, SFunction<UserInfo, ?>[] sFunction) {
        return getQueryWrapper(param, codeXsztParam, sFunction, null);
    }

    public MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, CodeXsztParam codeXsztParam, SFunction<UserInfo, ?>[] sFunction, String... columns) {
        Class<UserInfo> userInfoClass = UserInfo.class;
        Class<CodeXszt> stateClass = CodeXszt.class;
        Class<UserOrgMap> orgMapClass = UserOrgMap.class;
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.leftJoin(stateClass, CodeXszt::getId, UserInfo::getRyztid);
        wrapper.leftJoin(orgMapClass, UserOrgMap::getXgh, UserInfo::getXgh);
        Map<Class<?>, String> tableAliasMap = wrapper.getTableAliasMap();
        if (sFunction != null && sFunction.length > 0) {
            wrapper.select(sFunction);
        } else if (columns != null && columns.length > 0) {
            wrapper.selectColumn(null, columns);
        } else {
            wrapper.selectAll(userInfoClass);  // 查询user表全部字段
        }
        wrapper.selectCollection(orgMapClass, UserInfo::getUserOrgMaps);
        if (!Objects.isNull(param)) {
            String customQueryData = param.getCustomQueryData();
            if (StringUtils.hasText(customQueryData)) {
                wrapper.eq(UserInfo::getUserType, param.getUserType());
                wrapper.and(q -> appendCustomQueryCondition(q, wrapper, customQueryData, userInfoClass));
            }
            UserOrgMapParam userOrgParam = new UserOrgMapParam();
            BeanUtils.copyProperties(param, userOrgParam);
            wrapper.buildQueryCondition(tableAliasMap.get(orgMapClass), userOrgParam);
        }

        if (!Objects.isNull(codeXsztParam))
            wrapper.buildQueryCondition(tableAliasMap.get(stateClass), codeXsztParam);
        return wrapper;
    }

    private void appendCustomQueryCondition(MPJLambdaWrapper<UserInfo> wrapper, MyMPJLambdaWrapper<UserInfo, UserInfoParam> consumer
            , String customQueryData, Class<UserInfo> clazz) {
        List<CustomQueryParam> params = JSONArray.parseArray(customQueryData, CustomQueryParam.class);
        String alias = consumer.getAlias();
        int index = 0;
        int length = params.size();
        for (CustomQueryParam param : params) {
            String fieldName = alias + "." + param.getFieldName();
            consumer.buildQueryCondition(wrapper, param.getQueryType(), fieldName, param.getFieldValue(), clazz);
            if (param.getJoinRelation() == JoinRelation.OR && index < length - 1)
                wrapper.or(true);
            index++;
        }
    }
}
