package com.sanythadmin.common.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.system.entity.SysRoleScope;
import com.sanythadmin.common.system.param.SysRoleScopeParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色标识Mapper
 *
 * <AUTHOR>
 * @since 2024-03-06 10:14:39
 */
public interface SysRoleScopeMapper extends BaseMapper<SysRoleScope> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<SysRoleScope>
     */
    List<SysRoleScope> selectPageRel(@Param("page") IPage<SysRoleScope> page,
                             @Param("param") SysRoleScopeParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<SysRoleScope> selectListRel(@Param("param") SysRoleScopeParam param);

}
