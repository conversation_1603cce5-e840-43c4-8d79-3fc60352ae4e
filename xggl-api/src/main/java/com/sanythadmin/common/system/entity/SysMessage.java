package com.sanythadmin.common.system.entity;

import jakarta.persistence.Id;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统消息
 *
 * <AUTHOR>
 * @since 2024-10-10 16:10:33
 */
@Data
@Document(collection = "SYT_SYS_MESSAGE")
public class SysMessage implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 功能模块
     */
    private String module;

    /**
     * 消息内容
     */
    private String content;

    /**
     * PC功能点跳转URL
     */
    private String pcUrl;

    /**
     * 移动端功能点跳转URL
     */
    private String appUrl;

    /**
     * 已读标识（0：未读，1：已读）
     */
    private Integer readFlag;

    /**
     * 接收人
     */
    private String username;

    /**
     * 接收角色
     */
    private String roleName;

    /**
     * 接收人学院ID
     */
    private String xyid;

    /**
     * 接收人班级ID
     */
    private String bjid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
