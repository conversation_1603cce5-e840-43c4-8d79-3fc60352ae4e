package com.sanythadmin.common.system.entity;

import com.sanythadmin.common.enums.TemplateType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by JIANGPING on 2024/9/20.
 */
@Data
public class TemplateFile implements Serializable {
    @NotEmpty(message = "模板名称不能为空")
    private String module;
    @NotNull(message = "模板类型不能为空")
    private TemplateType templateType;
    /**
     * 下载文件名
     */
    private String fileName;

    public String getTemplateName() {
        return module + "_" + templateType.name();
    }
}
