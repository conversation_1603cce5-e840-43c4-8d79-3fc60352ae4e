package com.sanythadmin.common.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.EncryptedTable;
import com.sanythadmin.common.core.annotation.FieldMask;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.Mask;
import com.sanythadmin.common.enums.State;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统账户信息
 *
 * <AUTHOR>
 * @since 2024-03-05 15:44:06
 */
@JsonIgnoreProperties({"password"})
@EncryptedTable
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SYS_ACCOUNT")
@Table(name = "SYT_SYS_ACCOUNT")
public class SysAccount implements UserDetails {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 账户是否过期
     *//*
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ACCOUNT_NON_EXPIRED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("ACCOUNT_NON_EXPIRED")
    private Boolean accountNonExpired;
    *//**
     * 是否锁定
     *//*
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ACCOUNT_NON_LOCKED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("ACCOUNT_NON_LOCKED")
    private Boolean accountNonLocked;
    *//**
     * 密码是否过期
     *//*
    @Getter(value = AccessLevel.NONE)
    @Column(name = "CREDENTIALS_NON_EXPIRED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("CREDENTIALS_NON_EXPIRED")
    private Boolean credentialsNonExpired;
    *//**
     * 是否启用
     *//*
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ENABLED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("ENABLED")
    private Boolean enabled;*/
    /**
     * 密码
     */
    @Column(name = "PASSWORD")
    @TableField("PASSWORD")
    private String password;
    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;
    /**
     * 姓名
     */
    @Column(name = "REAL_NAME")
    @TableField("REAL_NAME")
    private String realName;
    /**
     * 性别（男：0， 女：1）
     */
    @Column(name = "GENDER", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "GENDER", jdbcType = JdbcType.INTEGER)
    private Gender gender;
    /**
     * 激活标识
     */
    /*@Column(name = "ACTIVE_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("ACTIVE_FLAG")
    private Boolean activeFlag;*/
    /**
     * 证件类型, 证件号码
     */
    @Column(name = "ID_TYPE")
    @TableField("ID_TYPE")
    private String idType;
    @FieldMask(value = Mask.ID_CARD)
    @EncryptedColumn
    @Column(name = "ID_CODE")
    @TableField("ID_CODE")
    private String idCode;
    /**
     * 所属部门/学院
     */
//    @Column(name = "DEPT_ID")
//    @TableField("DEPT_ID")
//    private String deptId;
    /**
     * 手机号
     */
//    @FieldMask(value = MaskEnum.MOBILE_PHONE)
    @Column(name = "TEL_MOBILE")
    @TableField("TEL_MOBILE")
    private String telMobile;
    /**
     * 邮件
     */
//    @Column(name = "EMAIL")
//    @TableField("EMAIL")
//    private String email;
    /**
     * 账户使用时间范围
     */
    /*@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "START_TIME")
    @TableField("START_TIME")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "END_TIME")
    @TableField("END_TIME")
    private LocalDateTime endTime;*/

    /**
     * 状态,0-停用, 1-启用
     */
    @Column(name = "STATUS")
    @TableField("STATUS")
    private State status;
    /**
     * 帐户过期时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "ACCOUNT_EXPIRE_TIME")
    @TableField("ACCOUNT_EXPIRE_TIME")
    private LocalDateTime accountExpireTime;
    /**
     * 密码过期时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "PASSWORD_EXPIRE_TIME")
    @TableField("PASSWORD_EXPIRE_TIME")
    private LocalDateTime passwordExpireTime;
    /**
     * 锁定状态,0-停用, 1-启用
     */
    @Column(name = "LOCK_STATUS")
    @TableField("LOCK_STATUS")
    private State lockStatus;


    /**
     * 最后更新密码时间
     */
    @Column(name = "PASSWORD_LAST_UPDATE_TIME")
    @TableField("PASSWORD_LAST_UPDATE_TIME")
    private LocalDateTime passwordLastUpdateTime;

    /**
     * 最后使用的角色
     */
    @Column(name = "LAST_USED_ROLE", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("LAST_USED_ROLE")
    private String lastUsedRole;

//    @Column(name = "DELETED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
//    @TableLogic
//    private Integer deleted;

    /**
     * 角色列表
     */
    @Transient
    @TableField(exist = false)
    private List<SysRole> roleList;
    /**
     * 当前角色
     */
    @Transient
    @TableField(exist = false)
    private SysRole role;
    /**
     * 权限列表
     */
    @Transient
    @TableField(exist = false)
    private List<Menu> authorities;

    @Override
    @JsonIgnore
    @Transient
    public boolean isAccountNonExpired() {
        if (accountExpireTime == null) {
            return true; // 没有设置过期时间，认为未过期
        }
        return accountExpireTime.isAfter(LocalDateTime.now());
    }

    @Override
    @JsonIgnore
    @Transient
    public boolean isAccountNonLocked() {
        return lockStatus == null || lockStatus.getValue() == 1;
    }

    @Override
    @JsonIgnore
    @Transient
    public boolean isCredentialsNonExpired() {
        if (passwordExpireTime == null) {
            return true; // 没有设置密码过期时间
        }
        return passwordExpireTime.isAfter(LocalDateTime.now());
    }

    @Override
    @JsonIgnore
    @Transient
    public boolean isEnabled() {
        return status != null && status.getValue() ==1;
    }
}
