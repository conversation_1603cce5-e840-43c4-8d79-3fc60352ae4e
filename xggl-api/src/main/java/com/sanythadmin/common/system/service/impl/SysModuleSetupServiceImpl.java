package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.system.entity.SysModuleSetup;
import com.sanythadmin.common.system.mapper.SysModuleSetupMapper;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2025-01-16 16:25:33
 */
@Service
public class SysModuleSetupServiceImpl extends ServiceImpl<SysModuleSetupMapper, SysModuleSetup> implements SysModuleSetupService {

    @Resource
    private SysModuleSetupMapper mapper;


    @Override
    public String getModuleYear(String codeOrName) {
        SysModuleSetup module = getOne(new LambdaQueryWrapper<SysModuleSetup>()
                .eq(SysModuleSetup::getCode, codeOrName).or().eq(SysModuleSetup::getName, codeOrName));

        if (module == null) {
            return null;
        }
        if (module.getParentCode() != null) {
            SysModuleSetup parentModule = getParentModule(module.getParentCode());
            return parentModule != null ? parentModule.getYear() : null;
        }
        return module.getYear();
    }

    @Override
    public SysModuleSetup getParentModule(String parentCode) {
        return getOne(new LambdaQueryWrapper<SysModuleSetup>()
                .eq(SysModuleSetup::getCode, parentCode));
    }
}
