package com.sanythadmin.common.system.param;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 登录参数
 *
 * <AUTHOR>
 * @since 2021-08-30 17:35:16
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "用户名不能为空")
    @Length(max = 256, message = "用户名长度不能超过256字符")
    @Pattern(regexp = "^[a-fA-F0-9]+$", message = "用户名格式不正确")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Length(max = 256, message = "密码长度不能超过256字符")
    @Pattern(regexp = "^[a-fA-F0-9]+$", message = "密码格式不正确")
    private String password;

    @NotBlank(message = "验证码ID不能为空")
    @Length(max = 256, message = "验证码ID长度不能超过256字符")
    @Pattern(regexp = "^[a-fA-F0-9]+$", message = "验证码ID格式不正确")
    private String cid;

    @NotBlank(message = "验证码不能为空")
    @Length(max = 256, message = "验证码长度不能超过256字符")
    @Pattern(regexp = "^[a-fA-F0-9]+$", message = "验证码格式不正确")
    private String code;

    @NotNull(message = "记住我选项不能为空")
    private Boolean remember = false;

    // 防止批量分配 - 使用白名单模式
    @JsonIgnore
    private String[] allowedFields = {"username", "password", "cid", "code", "remember"};

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        if (!Arrays.asList(allowedFields).contains(name)) {
            throw new IllegalArgumentException("不允许的参数: " + name);
        }
    }
}
