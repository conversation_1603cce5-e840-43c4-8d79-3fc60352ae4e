package com.sanythadmin.common.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.param.SysAccountRoleParam;
import com.sanythadmin.common.system.service.SysAccountRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统用户角色关系Service实现
 *
 * <AUTHOR>
 * @since 2024-03-05 16:40:52
 */
@Service
public class SysAccountRoleServiceImpl extends ServiceImpl<SysAccountRoleMapper, SysAccountRole> implements SysAccountRoleService {

    @Override
    public PageResult<SysAccountRole> pageRel(SysAccountRoleParam param) {
        PageParam<SysAccountRole, SysAccountRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<SysAccountRole> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<SysAccountRole> listRel(SysAccountRoleParam param) {
        List<SysAccountRole> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<SysAccountRole, SysAccountRoleParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public SysAccountRole getByIdRel(String id) {
        SysAccountRoleParam param = new SysAccountRoleParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Override
    public List<SysRole> selectByAccountId(String accountId, String roleId) {
        return baseMapper.selectByAccountId(accountId, roleId);
    }

}
