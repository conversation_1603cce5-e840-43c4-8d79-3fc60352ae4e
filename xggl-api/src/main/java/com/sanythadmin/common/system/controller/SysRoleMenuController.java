package com.sanythadmin.common.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.Menu;
import com.sanythadmin.common.system.entity.RoleMenu;
import com.sanythadmin.common.system.param.MenuParam;
import com.sanythadmin.common.system.param.RoleMenuParam;
import com.sanythadmin.common.system.service.RoleMenuService;
import com.sanythadmin.common.system.service.SysMenuService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色菜单控制器
 *
 * <AUTHOR>
 * @since 2018-12-24 16:10:01
 */
@RestController
@RequestMapping("/api/system/role-menu")
public class SysRoleMenuController extends BaseController {
    @Resource
    private RoleMenuService roleMenuService;
    @Resource
    private SysMenuService sysMenuService;

    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/list")
    public List<Menu> list(MenuParam param) {
        if (!StringUtils.hasLength(param.getRoleId()))
            AssertUtil.throwImportError("缺失必须参数");
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        List<Menu> menus = sysMenuService.list(page.getOrderWrapper());
        List<RoleMenu> roleMenus = roleMenuService.list(new LambdaQueryWrapper<RoleMenu>()
                .eq(RoleMenu::getRoleId, param.getRoleId()));
        menus.forEach((m) -> m.setChecked(roleMenus.stream().anyMatch((d) -> d.getMenuId().equals(m.getMenuId()))));
        return menus;
    }

    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色资源", comments = "更新角色资源信息")
    @PutMapping("/{id}")
    public ApiResult<?> update(@PathVariable("id") String roleId, @RequestBody List<String> menuIds) {
        roleMenuService.remove(new LambdaUpdateWrapper<RoleMenu>().eq(RoleMenu::getRoleId, roleId));
        if (menuIds != null && !menuIds.isEmpty()) {
            List<RoleMenu> roleMenuList = new ArrayList<>();
            for (String menuId : menuIds) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                roleMenuList.add(roleMenu);
            }
            if (!roleMenuService.saveBatch(roleMenuList)) {
                throw new BusinessException("保存失败");
            }
        }
        return success("保存成功");
    }

    @OperationLog(module = "角色资源", comments = "更新角色资源信息")
    @PostMapping("/operation")
    public void operation(@Validated @RequestBody RoleMenuParam param) {
        roleMenuService.operation(param);
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog
    @PostMapping("/{id}")
    public ApiResult<?> addRoleAuth(@PathVariable("id") String roleId, @RequestBody String menuId) {
        RoleMenu roleMenu = new RoleMenu();
        roleMenu.setRoleId(roleId);
        roleMenu.setMenuId(menuId);
        if (roleMenuService.save(roleMenu)) {
            return success();
        }
        return fail();
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色资源", comments = "删除角色资源信息")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") String roleId, @RequestBody String menuId) {
        if (roleMenuService.remove(new LambdaUpdateWrapper<RoleMenu>()
                .eq(RoleMenu::getRoleId, roleId).eq(RoleMenu::getMenuId, menuId))) {
            return success();
        }
        return fail();
    }
}
