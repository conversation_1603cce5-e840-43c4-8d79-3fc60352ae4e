package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum Required implements ValueTextBaseEnum {
    NO(0, "非必填", false), YES(1, "必填", true);
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private Boolean mark;

    Required(Integer value, String text, Boolean mark) {
        this.value = value;
        this.text = text;
        this.mark = mark;
    }

    public Boolean getMark() {
        return mark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
