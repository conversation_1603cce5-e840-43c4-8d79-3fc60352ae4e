package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import org.springframework.util.StringUtils;

/**
 * 用户类别
 */
public enum UserType implements ValueTextBaseEnum {
    ADMIN(0, "admin", "管理员"),
    STUDENT(1, "student", "学生"),
    TEACHER(2, "teacher", "教师"),
    OTHER(3, "other", "其他"),
    EMPLOYER(4, "employer", "用人单位"),
    CONSULTANT(5, "consultant", "心理咨询师"),
    ;
    
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private String remark;

    UserType(Integer value, String text, String remark) {
        this.value = value;
        this.text = text;
        this.remark = remark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static UserType get(String text) {
        if (!StringUtils.hasLength(text))
            return null;
        for (UserType userType : UserType.values()) {
            boolean equals = userType.getText().equals(text);
            if (equals) {
                return userType;
            }
        }
        return null;
    }
}
