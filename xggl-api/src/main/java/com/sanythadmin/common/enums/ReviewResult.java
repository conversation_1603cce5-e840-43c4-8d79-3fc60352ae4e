package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.Getter;

@Getter
public enum ReviewResult implements TextBaseEnum {
    Dai<PERSON>hen<PERSON>i("待审批"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("审批中"),
    <PERSON><PERSON><PERSON>("通过"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("不通过"),
    <PERSON><PERSON><PERSON><PERSON>("退回");

//    @EnumValue  // MyBatis-Plus 枚举值注解，用于数据库存储
    @JsonValue  // JSON 序列化注解，用于前端显示
    private final String text;

    ReviewResult(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }
}
