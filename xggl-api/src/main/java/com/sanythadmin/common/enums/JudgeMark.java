package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import org.springframework.util.StringUtils;

public enum JudgeMark implements ValueTextBaseEnum {

    NO(0, "否", false), YES(1, "是", true);

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private Boolean mark;

    JudgeMark(Integer value, String text, Boolean mark) {
        this.value = value;
        this.text = text;
        this.mark = mark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public Boolean getMark() {
        return mark;
    }

    public static JudgeMark get(String text) {
        if (!StringUtils.hasLength(text))
            return null;
        for (JudgeMark judgeMark : JudgeMark.values()) {
            boolean equals = judgeMark.getText().equals(text);
            if (equals) {
                return judgeMark;
            }
        }
        return null;
    }
}
