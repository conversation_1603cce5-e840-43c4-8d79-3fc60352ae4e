package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum ControlType implements ValueTextBaseEnum {
    input(0, "输入框"),
    select(1, "下拉"),
    multipleSelect(2, "下拉多选"),
    textarea(3, "文本域"),
    date(4, "日期选择"),
    datetime(5, "日期时间选择"),
    time(6, "时间选择"),
    inputNumber(7, "数字输入框");

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;

    ControlType(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
