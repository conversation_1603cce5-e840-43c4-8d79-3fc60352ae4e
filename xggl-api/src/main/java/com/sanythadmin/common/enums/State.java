package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum State implements ValueTextBaseEnum {
    ENABLED(1, "启用"),
    DISABLED(0, "停用");
    @EnumValue
    private final Integer value;
    @JsonValue
    private final String text;

    State(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    public static State get(String text) {
        for (State state : State.values()) {
            boolean equals = state.getText().equals(text);
            if (equals) {
                return state;
            }
        }
        return null;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
