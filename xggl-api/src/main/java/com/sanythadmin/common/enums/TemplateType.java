package com.sanythadmin.common.enums;

import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum TemplateType implements ValueTextBaseEnum {
    /**
     * 资助相关模板文件
     */
    sqb(0, "申请表模板"),
    hzb(1, "汇总表模板"),
    /**
     * 其他模板文件
     */
    other(2, "其他模板文件"),
    list(3, "列表数据导出");

    private Integer value;
    private String text;

    TemplateType(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
