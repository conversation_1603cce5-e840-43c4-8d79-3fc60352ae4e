package com.sanythadmin.common.integration.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PrePersist;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @since 2025/6/10 16:14
 */
@Data
@EqualsAndHashCode(of = "id")
@MappedSuperclass
public abstract class IntegrationEntitySuper {

    @Id
    @Column(length=42)
    private String id;

    @PrePersist
    public void ensureId() {
        if (this.id == null) {
            this.id = IntegrationUuidSequenceContext.nextId();
        }
    }

    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
}
