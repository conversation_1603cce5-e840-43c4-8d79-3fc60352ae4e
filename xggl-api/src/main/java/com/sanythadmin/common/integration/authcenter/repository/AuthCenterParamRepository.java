package com.sanythadmin.common.integration.authcenter.repository;

import com.sanythadmin.common.integration.authcenter.entity.AuthCenterParam;
import com.sanythadmin.common.integration.authcenter.enums.AuthCenterParamKey;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface AuthCenterParamRepository extends PagingAndSortingRepository<AuthCenterParam, String>, CrudRepository<AuthCenterParam, String>, JpaSpecificationExecutor<AuthCenterParam> {

    Optional<AuthCenterParam> findByKey(AuthCenterParamKey key);

    @Query("select key from AuthCenterParam")
    List<AuthCenterParamKey> allKey();
}
