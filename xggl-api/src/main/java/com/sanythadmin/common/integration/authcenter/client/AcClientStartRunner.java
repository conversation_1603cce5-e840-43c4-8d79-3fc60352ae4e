package com.sanythadmin.common.integration.authcenter.client;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @since 2025/6/19 16:44
 */
@Component
public class AcClientStartRunner {

    private final AcApiClient acApiClient;

    public AcClientStartRunner(AcApiClient acApiClient) {
        this.acApiClient = acApiClient;
    }

//    @Value("${AUTH_CENTER_IP:}")
//    private String authCenterIp;
//
//    @Value("${AUTH_CENTER_PORT:}")
//    private String authCenterPort;
//
//    @Value("${AUTH_CENTER_SELF_URL_ROOT:}")
//    private String authCenterSelfUrlRoot;
//
//    @Value("${AUTH_CENTER_MOBILE_INDEX:}")
//    private String authCenterMobileIndex;
//
//    @Value("${AUTH_CENTER_TOKEN:}")
//    private String authCenterToken;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        acApiClient.connect();
    }
}
