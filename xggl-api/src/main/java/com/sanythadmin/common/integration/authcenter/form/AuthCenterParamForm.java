package com.sanythadmin.common.integration.authcenter.form;

import com.sanythadmin.common.integration.authcenter.entity.AuthCenterParam;
import com.sanythadmin.common.integration.form.IntegrationFormSuper;
import com.sanythadmin.common.integration.form.IntegrationIdTextPairForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/6/11 14:15
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthCenterParamForm extends IntegrationFormSuper {

    public AuthCenterParamForm(AuthCenterParam app) {
        super(app);
        this.key = new IntegrationIdTextPairForm(app.getKey().name(), app.getKey().getText());
        this.value = app.getValue();
        this.description = app.getDescription();
    }

    /**
     * 参数类型 #不可修改
     */
    private IntegrationIdTextPairForm key;
    private String value;
    private String description;
}
