package com.sanythadmin.common.integration.entity;

import java.util.UUID;

/**
 * @since 2025/6/10 15:57
 */
public class IntegrationUuidSequenceContext {

    private static final ThreadLocal<UUID> baseUuid = ThreadLocal.withInitial(UUID::randomUUID);
    private static final ThreadLocal<Integer> counter = ThreadLocal.withInitial(() -> 1);

    public static String nextId() {
        UUID uuid = baseUuid.get();
        int index = counter.get();
        counter.set(index + 1);
        return String.format("%s-%05x", uuid.toString(), index);
    }

    public static void clear() {
        baseUuid.remove();
        counter.remove();
    }
}
