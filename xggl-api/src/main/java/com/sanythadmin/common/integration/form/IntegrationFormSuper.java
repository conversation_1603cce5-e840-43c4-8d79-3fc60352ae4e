package com.sanythadmin.common.integration.form;

import com.sanythadmin.common.integration.entity.IntegrationEntitySuper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/6/11 14:09
 */
@Data
@NoArgsConstructor
public abstract class IntegrationFormSuper {

    public IntegrationFormSuper(IntegrationEntitySuper baseEntity) {
        this.id = baseEntity.getId();
        this.createdAtText = baseEntity.getCreatedAt();
        this.updatedAtText = baseEntity.getUpdatedAt();
    }

    private String id;
    private LocalDateTime createdAtText;
    private LocalDateTime updatedAtText;
}
