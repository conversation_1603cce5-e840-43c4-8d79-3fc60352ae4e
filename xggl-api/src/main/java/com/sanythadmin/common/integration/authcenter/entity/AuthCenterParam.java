package com.sanythadmin.common.integration.authcenter.entity;

import com.sanythadmin.common.integration.authcenter.enums.AuthCenterParamKey;
import com.sanythadmin.common.integration.entity.IntegrationEntitySuper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 认证中心的参数
 *
 * @since 2025/6/10 14:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_integration_auth_center_param")
public class AuthCenterParam extends IntegrationEntitySuper {

    @Column(unique = true)
    @Enumerated(EnumType.STRING)
    private AuthCenterParamKey key;
    private String value;
    private String description;

}
