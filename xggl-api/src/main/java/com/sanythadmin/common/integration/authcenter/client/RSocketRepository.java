package com.sanythadmin.common.integration.authcenter.client;

import com.sanythadmin.common.integration.util.IntegrationUtil;
import lombok.Setter;
import org.springframework.messaging.rsocket.RSocketRequester;
import org.springframework.stereotype.Component;

/**
 * @since 2025/6/9 15:03
 */
@Setter
@Component
public class RSocketRepository {

    private RSocketRequester authRequester;

    public RSocketRequester getAuthRequester() {
        RSocketRequester globalRequester = authRequester;
        IntegrationUtil.checkTrue(globalRequester != null, "尚未连接");
        IntegrationUtil.checkTrue(!globalRequester.isDisposed(), "连接已断开");
        return authRequester;
    }

}
