package com.sanythadmin.common.integration.authcenter.client;

import com.sanythadmin.common.integration.authcenter.dto.AcMessageDto;
import com.sanythadmin.common.integration.authcenter.dto.RSocketJsapiReqDto;
import com.sanythadmin.common.integration.authcenter.dto.RSocketPayloadDto;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterConfig;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterUtil;
import io.rsocket.SocketAcceptor;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.messaging.rsocket.RSocketRequester;
import org.springframework.messaging.rsocket.RSocketStrategies;
import org.springframework.messaging.rsocket.annotation.support.RSocketMessageHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;

/**
 * @since 2025/6/7 14:25
 */
@AllArgsConstructor
@Component
public class AcApiClient {

    protected final Log logger = LogFactory.getLog(getClass());
    private final RSocketRepository rSocketRepository;
    private final XgHandler xgHandler;
    private final AuthCenterConfig config;

    public String connect() {
//        String token = System.getenv("AUTH_CENTER_TOKEN");
//        String ip = System.getenv("AUTH_CENTER_IP");
//        String port = System.getenv("AUTH_CENTER_PORT");
//        String selfUrlRoot = System.getenv("AUTH_CENTER_SELF_URL_ROOT");

        String token = config.getToken();
        String ip = config.getIp();
        String port = config.getPort();
        String selfUrlRoot = config.getSelfUrlRoot();

//        if (token == null || ip == null || port == null || selfUrlRoot == null) {
//            return "认证中心配置参数缺失";
//        }
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(ip) || StringUtils.isEmpty(port) || StringUtils.isEmpty(selfUrlRoot)) {
            return "认证中心配置参数缺失";
        }

        RSocketStrategies socketStrategies = RSocketStrategies.builder().encoder(new Jackson2JsonEncoder()).decoder(new Jackson2JsonDecoder()).build();
        SocketAcceptor responder = RSocketMessageHandler.responder(socketStrategies, xgHandler);

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(selfUrlRoot);
        uriComponentsBuilder.path(AuthCenterUtil.WEB_AUTH_INDEX);
        String webAuthIndexUrl = uriComponentsBuilder.toUriString();
        RSocketRequester requester = RSocketRequester.builder()
                .rsocketStrategies(socketStrategies)
                .rsocketConnector(connector -> {
                    connector.acceptor(responder); //让客户端也能接收请求
                    connector.reconnect(Retry.backoff(20, Duration.ofSeconds(30))); //断线重连
                })
                .dataMimeType(MimeTypeUtils.APPLICATION_JSON)
                .setupData(new RSocketPayloadDto(token, webAuthIndexUrl, null))
                .tcp(ip, Integer.parseInt(port));


        String result = requester.route("hello").data("I am xuegong5").retrieveMono(String.class).block();
//        System.out.println("auth center hello result = " + result);
        logger.info("hello result: " + result);

        rSocketRepository.setAuthRequester(requester);
        return "来自认证系统的响应：" + result;
    }

    public Mono<String> serverHello(String name) {
        RSocketRequester globalRequester = rSocketRepository.getAuthRequester();

//        System.out.println("prepared requester");
        logger.info("prepared requester");
        return globalRequester.route("server.hello").data(name).retrieveMono(String.class);
    }

    public Mono<Object> getJsapiConfig(String appId, String url) {
        RSocketRequester globalRequester = rSocketRepository.getAuthRequester();

//        System.out.println("prepared getJsapiConfig requester");
        logger.debug("prepared getJsapiConfig requester");
        return globalRequester.route("getJsapiConfig").data(new RSocketJsapiReqDto(appId, url)).retrieveMono(Object.class);
    }

    public Mono<String> pushMessageData(AcMessageDto data) {
        RSocketRequester globalRequester = rSocketRepository.getAuthRequester();
        logger.debug("prepared pushMessageData requester");
        return globalRequester.route("collectMessageData").data(data).retrieveMono(String.class);
    }
}
