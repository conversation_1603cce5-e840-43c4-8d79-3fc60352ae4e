package com.sanythadmin.common.integration.authcenter.dto;

import lombok.Data;

import java.util.Collection;

/**
 * @since 2025/6/24 15:49
 */
@Data
public class AcMessageDto {

//    private CmMsgType type = CmMsgType.text;
    private Collection<String> usernames;
    /**
     * 文本消息内容，建议500字符以内。
     */
    private String content;
    /**
     * 卡片消息标题，透出到会话列表和通知的文案。
     */
    private String title;
    /**
     * 卡片消息内容，支持markdown，语法参考标准markdown语法。建议1000个字符以内。
     */
    private String markdown;
    /**
     * 卡片消息链接地址，移动端内部路由
     */
    private String path;
    /**
     * 链接标题，不超过4个字符。
     */
    private String urlTitle = "查看详情";
    /**
     * 卡片消息链接跳转至系统的角色
     */
    private String roleId;

}
