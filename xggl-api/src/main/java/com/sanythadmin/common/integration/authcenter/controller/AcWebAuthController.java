package com.sanythadmin.common.integration.authcenter.controller;

import com.sanythadmin.common.core.config.MobileConfig;
import com.sanythadmin.common.integration.authcenter.dto.RSocketCheckDto;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterUtil;
import com.sanythadmin.common.integration.util.IntegrationUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.result.LoginResult;
import com.sanythadmin.common.system.service.LoginValidationService;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysSessionVarService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * @since 2025/6/11 15:12
 */
@Slf4j
@AllArgsConstructor
@Controller
@RequestMapping("/nonlogin/authcenter/web_auth")
public class AcWebAuthController {

    private final StringRedisTemplate stringRedisTemplate;
    private final SysAccountService sysAccountService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SysSessionVarService sysSessionVarService;
    private final LoginValidationService loginValidationService;
    private final MobileConfig mobileConfig;

//    @ResponseBody
    @GetMapping("index")
    public String index(String authCode, String state) {
        RSocketCheckDto checkData = (RSocketCheckDto) redisTemplate.opsForValue().getAndDelete(authCode);
        IntegrationUtil.checkTrue(checkData != null, "认证码无效");
        String username = checkData.getUsername();

        // 签发token
        SysAccount account = sysAccountService.getByUsername(username);
        String roleId = checkData.getRoleId();
        if (StringUtils.hasText(roleId)) {
            account.setRole(
                    account.getRoleList().stream().filter(r -> roleId.equals(r.getId()))
                    .findFirst()
                    .orElse(account.getRole()));
        }
        LoginResult loginResult = loginValidationService.generateLoginResult(account);
        String access_token = loginResult.getAccess_token();

        // 存储appId
        sysSessionVarService.setValue(access_token, AuthCenterUtil.AUTH_CENTER_APP_ID, checkData.getAppId());

        // 拼合跳转地址
        String urlRoot = mobileConfig.getUrlRoot();
        if (!StringUtils.hasText(urlRoot)) {
            urlRoot = "/";
        }
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(urlRoot);
        uriComponentsBuilder.path("mobile/home");
        uriComponentsBuilder.queryParam("t", access_token);
        if (StringUtils.hasText(checkData.getPath())) {
            uriComponentsBuilder.queryParam("u", checkData.getPath());
        }
        String redirectUrl = uriComponentsBuilder.toUriString();
        log.debug("跳转地址:{}", redirectUrl);
        return "redirect:" + redirectUrl;
    }

}
