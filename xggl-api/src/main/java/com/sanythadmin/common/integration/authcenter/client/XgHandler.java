package com.sanythadmin.common.integration.authcenter.client;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.integration.authcenter.dto.RSocketCheckDto;
import com.sanythadmin.common.integration.authcenter.dto.RSocketUserCheckDto;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.service.SysAccountService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

/**
 * @since 2025/6/9 14:59
 */
@AllArgsConstructor
@Component
public class XgHandler {

    private final SysAccountService sysAccountService;
    private StringRedisTemplate stringRedisTemplate;
    private RedisTemplate<String, Object> redisTemplate;

    @MessageMapping("checkUsername")
    public Mono<String> checkUsername(RSocketCheckDto checkDto) {
        QueryWrapper<SysAccount> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysAccount::getUsername, checkDto.getUsername());
        if (sysAccountService.exists(wrapper)) {
            String uuid = UUID.randomUUID().toString();
            redisTemplate.opsForValue().set(uuid, checkDto, Duration.of(1, ChronoUnit.MINUTES));
//            stringRedisTemplate.opsForValue().set(uuid, new AcRedisCheckDto(username, appId).toString(), Duration.of(1, ChronoUnit.MINUTES));
            return Mono.just(uuid);
        }
        return Mono.empty();
    }

    @MessageMapping("checkUsernameAndPassword")
    public Mono<String> checkUsernameAndPassword(RSocketUserCheckDto checkDto) {
        SysAccount account = sysAccountService.getAccount(checkDto.getUsername());
        if (account == null) {
            return Mono.just("用户不存在");
        }
        if (!UserInfoUtil.comparePassword(checkDto.getPassword(), account.getPassword())) {
            return Mono.just("密码错误");
        }
        return Mono.empty();
    }
}
