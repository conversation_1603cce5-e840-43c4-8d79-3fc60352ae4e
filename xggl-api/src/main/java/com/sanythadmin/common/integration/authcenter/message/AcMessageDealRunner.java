package com.sanythadmin.common.integration.authcenter.message;

import com.sanythadmin.common.integration.authcenter.client.AcApiClient;
import com.sanythadmin.common.integration.authcenter.dto.AcMessageDto;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * @since 2025/6/24 16:05
 */
@Slf4j
@AllArgsConstructor
public class AcMessageDealRunner implements Runnable{
    private final RedisTemplate<String, Object> redisTemplate;
    private static final String ACTION_CARD_TITLE = "学工系统业务通知";
    private static final String DEFAULT_URL_TEXT = "查看详情";
    private AcApiClient acApiClient;

    public String getActionCardTitle() {
//        if (actionCardTitle == null) {
//            DingtalkParam paramTitle = paramCache.getParamRepository().findFirstByKey(DingtalkParamKey.MessageActionCardTitle);
//            if (paramTitle != null && StringUtils.isNotEmpty(paramTitle.getValue())) {
//                actionCardTitle = paramTitle.getValue();
//            } else {
//                actionCardTitle = ACTION_CARD_TITLE;
//            }
//        }
//        return actionCardTitle;
        return ACTION_CARD_TITLE;
    }

    @Override
    public void run() {
        try {
            while (true) {
                AcMessageDto take = (AcMessageDto) redisTemplate.opsForList().rightPop(AuthCenterUtil.MESSAGE_QUEUE_KEY, 5, TimeUnit.SECONDS);
                if (take == null) {
                    break;
                }
                process(take);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void process(AcMessageDto dto) {
        if (StringUtils.isEmpty(dto.getTitle())) {
            dto.setTitle(getActionCardTitle()); // 默认卡片消息标题为学工系统业务通知
        }
        if (StringUtils.isEmpty(dto.getUrlTitle())) {
            dto.setUrlTitle(DEFAULT_URL_TEXT);
        }

        acApiClient.pushMessageData(dto).block();
        log.debug("消息数据已转送至认证中心:{}", dto);
    }
}
