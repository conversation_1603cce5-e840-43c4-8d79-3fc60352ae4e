package com.sanythadmin.common.integration.authcenter.controller;

import com.sanythadmin.common.integration.authcenter.client.AcApiClient;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @since 2025/6/7 14:26
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/integration/authcenter/rsocket")
public class AcRSocketController {

    private final AcApiClient acApiClient;

    @RequestMapping("/connect")
    public String connect() {
        return acApiClient.connect();
    }

    @RequestMapping("/hello")
    public String hello(String name) {
        return acApiClient.serverHello(name).block();
    }

}
