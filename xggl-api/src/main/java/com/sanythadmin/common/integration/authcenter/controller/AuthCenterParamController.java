package com.sanythadmin.common.integration.authcenter.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.integration.authcenter.entity.AuthCenterParam;
import com.sanythadmin.common.integration.authcenter.enums.AuthCenterParamKey;
import com.sanythadmin.common.integration.authcenter.form.AuthCenterParamForm;
import com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository;
import com.sanythadmin.common.integration.form.IntegrationIdTextPairForm;
import com.sanythadmin.common.integration.query.IntegrationQuerySuper;
import com.sanythadmin.common.integration.util.IntegrationUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 集成/认证中心/参数管理
 *
 * @since 2025/6/10 17:59
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/integration/authcenter/param")
public class AuthCenterParamController {

    private final AuthCenterParamRepository paramRepository;

    /**
     * 分页查询
     * 权限标识：integration:authcenter:param:list
     */
    @PreAuthorize("hasAuthority('integration:authcenter:param:list')")
    @GetMapping("/page")
    public PageResult<AuthCenterParamForm> page(IntegrationQuerySuper query, @RequestParam(required = false)AuthCenterParamKey key) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<AuthCenterParam> page = paramRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (key != null)
                        return criteriaBuilder.equal(root.get("key"), key);
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(AuthCenterParamForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('integration:authcenter:param:list')")
    @GetMapping("/{id}")
    public AuthCenterParamForm get(@PathVariable("id") String id) {
        return new AuthCenterParamForm(paramRepository.findById(id).orElseThrow());
    }

    /**
     * 可选参数Key
     * 权限标识：integration:authcenter:param:list
     */
    @PreAuthorize("hasAuthority('integration:authcenter:param:list')")
    @GetMapping("/optionalKey")
    public List<IntegrationIdTextPairForm> optionalKey() {
        AuthCenterParamKey[] paramKeys = AuthCenterParamKey.values();
        List<AuthCenterParamKey> keys = paramRepository.allKey();
        return Arrays.stream(paramKeys).filter(paramKey -> !keys.contains(paramKey)).map(paramKey -> new IntegrationIdTextPairForm(paramKey.name(), paramKey.getText())).toList();
    }

    /**
     * 添加或修改
     * 权限标识：integration:authcenter:param:operation
     */
    @PreAuthorize("hasAuthority('integration:authcenter:param:operation')")
    @PostMapping("/operation")
    public void operation(@RequestBody AuthCenterParamForm form) {
        AuthCenterParam entity;
        if (form.getId() != null) {
            entity = paramRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new AuthCenterParam();
            entity.setCreatedAt(LocalDateTime.now());

            IntegrationUtil.checkTrue(StringUtils.isNotEmpty(form.getKey().getId()), "key不能为空");
            AuthCenterParamKey key = AuthCenterParamKey.valueOf(form.getKey().getId());
            paramRepository.findByKey(key).ifPresent(param -> IntegrationUtil.throwException("不可添加重复KEY"));
            entity.setKey(key);
        }

        entity.setValue(form.getValue());
        entity.setDescription(form.getDescription());

        paramRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：integration:authcenter:param:remove
     */
    @PreAuthorize("hasAuthority('integration:authcenter:param:remove')")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        paramRepository.deleteAllById(ids);
    }
}
