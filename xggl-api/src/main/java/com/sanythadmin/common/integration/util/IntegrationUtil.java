package com.sanythadmin.common.integration.util;

import com.sanythadmin.common.core.exception.BusinessException;

/**
 * @since 2025/6/11 14:26
 */
public class IntegrationUtil {

    public static void throwException(String message) throws BusinessException {
        throw new BusinessException(message);
    }

    public static void checkTrue(boolean expression, String content) throws BusinessException {
        if (!expression) {
            throw new BusinessException(content);
        }
    }

}
