package com.sanythadmin.common.integration.authcenter.message;

import com.sanythadmin.common.integration.authcenter.client.AcApiClient;
import com.sanythadmin.common.integration.authcenter.dto.AcMessageDto;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2025/6/25 13:58
 */
@AllArgsConstructor
@Component
public class AcMessageComponent {

    private final AtomicBoolean isDeal = new AtomicBoolean(false);
    private final ExecutorService executorService = Executors.newCachedThreadPool();
    private final RedisTemplate<String, Object> redisTemplate;
    private final AcApiClient acApiClient;

    public void pushData(AcMessageDto data) {
        executorService.execute(new AcMessagePushRunner(isDeal, redisTemplate, data, acApiClient));
    }
}
