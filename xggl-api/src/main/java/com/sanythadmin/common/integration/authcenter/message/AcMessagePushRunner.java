package com.sanythadmin.common.integration.authcenter.message;

import com.sanythadmin.common.integration.authcenter.client.AcApiClient;
import com.sanythadmin.common.integration.authcenter.dto.AcMessageDto;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2025/6/24 15:48
 */
@AllArgsConstructor
public class AcMessagePushRunner implements Runnable {

    private final AtomicBoolean isDeal;
    private final RedisTemplate<String, Object> redisTemplate;
    private final AcMessageDto data;
    private final AcApiClient acApiClient;

    @Override
    public void run() {
        redisTemplate.opsForList().leftPush(AuthCenterUtil.MESSAGE_QUEUE_KEY, data);
        if (isDeal.compareAndSet(false, true)) {
            AcMessageDealRunner dealMessageRunner = new AcMessageDealRunner(redisTemplate, acApiClient);
            dealMessageRunner.run();
            isDeal.set(false);
        }
    }
}
