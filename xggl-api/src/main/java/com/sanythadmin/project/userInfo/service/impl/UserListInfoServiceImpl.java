package com.sanythadmin.project.userInfo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.userInfo.entity.UserListInfo;
import com.sanythadmin.project.userInfo.mapper.UserListInfoMapper;
import com.sanythadmin.project.userInfo.param.UserListInfoParam;
import com.sanythadmin.project.userInfo.service.UserListInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户多条信息Service实现
 *
 * <AUTHOR>
 * @since 2024-04-02 15:29:34
 */
@RequiredArgsConstructor
@Service
public class UserListInfoServiceImpl extends ServiceImpl<UserListInfoMapper, UserListInfo> implements UserListInfoService {

    private final UserInfoService userInfoService;

    @Override
    public void edit(UserListInfo userListInfo) {
        UserInfoParam param = new UserInfoParam();
        param.setXgh(userListInfo.getXgh());
        param.setUserType(userListInfo.getUserType());
        Long count = userInfoService.countWithPermission(param, null);
        if (count == null || count == 0) AssertUtil.throwMessage("无操作数据权限");
        userListInfo.setUpdateTime(LocalDateTime.now());
        if (StringUtils.hasLength(userListInfo.getId())) {
            UserListInfo listInfo = getById(userListInfo.getId());
            userListInfo.setCreateTime(listInfo.getCreateTime());
            baseMapper.updateById(userListInfo);
        } else {
            userListInfo.setCreateTime(LocalDateTime.now());
            baseMapper.insert(userListInfo);
        }
    }

    @Override
    public PageResult<UserListInfo> page(UserListInfoParam param) {
        PageParam<UserListInfo, UserListInfoParam> page = new PageParam<>(param);
        IPage<UserListInfo> iPage = baseMapper.selectPageWithPermission(page, page.getOrderWrapper());
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Override
    public List<UserListInfo> list(UserListInfoParam param) {
        PageParam<UserListInfo, UserListInfoParam> page = new PageParam<>(param);
        return baseMapper.selectListWithPermission(page.getOrderWrapper());
    }
}
