package com.sanythadmin.project.userInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 用户多条信息
 *
 * <AUTHOR>
 * @since 2024-04-02 15:29:34
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_USER_LIST_INFO")
@Table(name = "SYT_USER_LIST_INFO")
public class UserListInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学工号
     */
    @NotEmpty(message = "学号或工号不能为空")
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;

    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;

    @Column(name = "BZ3")
    @TableField("BZ3")
    private String bz3;

    @Column(name = "BZ4")
    @TableField("BZ4")
    private String bz4;

    @Column(name = "BZ5")
    @TableField("BZ5")
    private String bz5;

    @Column(name = "BZ6")
    @TableField("BZ6")
    private String bz6;

    @Column(name = "BZ7")
    @TableField("BZ7")
    private String bz7;

    @Column(name = "BZ8")
    @TableField("BZ8")
    private String bz8;

    @Column(name = "BZ9")
    @TableField("BZ9")
    private String bz9;

    @Column(name = "BZ10")
    @TableField("BZ10")
    private String bz10;

    @Column(name = "BZ11")
    @TableField("BZ11")
    private String bz11;

    @Column(name = "BZ12")
    @TableField("BZ12")
    private String bz12;

    @Column(name = "BZ13")
    @TableField("BZ13")
    private String bz13;

    @Column(name = "BZ14")
    @TableField("BZ14")
    private String bz14;

    @Column(name = "BZ15")
    @TableField("BZ15")
    private String bz15;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 用户类型（1：学生，2：教师）
     */
    @NotNull(message = "用户类型不能为空")
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

    /**
     * 类型（例：家庭成员/教育经历）
     */
    @Column(name = "GROUP_NAME")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 用户信息组ID
     */
    @NotEmpty(message = "用户信息组ID不能为空")
    @Column(name = "GROUP_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 删除标识
     */
//    @Column(name = "DELETED", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
//    @TableLogic
//    private Integer deleted;

    public static List<String> getEditableFields() {
        String[] ignoredField = new String[]{
                "serialVersionUID", "id", "xgh", "createTime", "updateTime", "groupId", "groupName", "userType"
        };
        List<String> ignoredFieldList = Arrays.asList(ignoredField);
        Field[] fields = CommonUtil.getAllFields(UserListInfo.class);
        List<String> list = new ArrayList<>();
        for (Field field : fields) {
            if (ignoredFieldList.contains(field.getName())) continue;
            list.add(field.getName());
        }
        return list;
    }
}
