package com.sanythadmin.project.userInfo.param;

import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JoinRelation;
import lombok.Data;

import java.io.Serializable;

/**
 * 自定义查询参数（高级查询）
 */
public class CustomQueryParam implements Serializable {
    private static final long serialVersionUID = 1L;
    private String fieldName;
    private Object fieldValue;
    private QueryType queryType;
    private JoinRelation joinRelation;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public Object getFieldValue() {
        return valueConvert();
    }

    public void setFieldValue(Object fieldValue) {
        this.fieldValue = fieldValue;
    }

    public QueryType getQueryType() {
        return queryType;
    }

    public void setQueryType(QueryType queryType) {
        this.queryType = queryType;
    }

    public JoinRelation getJoinRelation() {
        return joinRelation;
    }

    public void setJoinRelation(JoinRelation joinRelation) {
        this.joinRelation = joinRelation;
    }

    private Object valueConvert() {
        if ("xb".equalsIgnoreCase(fieldName))
            return Gender.get(fieldValue != null ? String.valueOf(fieldValue) : null);
        return fieldValue;
    }
}
