package com.sanythadmin.project.userInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.userInfo.service.PersonInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("teacherInfoService")
public class TeacherServiceImpl extends PersonInfoService {
    @Override
    protected UserType getUserType() {
        return UserType.TEACHER;
    }

    @Override
    protected void setRoleId(LinkedHashMap<Integer, UserInfo> userInfoMap, UserInfo userInfo, List<ExcelImportError> errors) {
        List<SysRole> sysRoles = sysRoleMapper.selectList(new QueryWrapper<>());
        Map<String, String> stringMap = sysRoles.stream().collect(Collectors.toMap(SysRole::getName, SysRole::getId));
        if (userInfoMap != null)
            userInfoMap.forEach((line, obj) -> setRoleId(line, obj, stringMap, errors));

        if (userInfo != null && StringUtils.hasText(userInfo.getRoleId()))
            setRoleId(null, userInfo, stringMap, errors);
    }

    private static void setRoleId(Integer row, UserInfo obj, Map<String, String> stringMap, List<ExcelImportError> errors) {
        String[] strings = CommonUtil.split(obj.getRoleId());
        StringBuilder ids = new StringBuilder();
        for (String role : strings) {
            String roleId = stringMap.get(role);
            if (!StringUtils.hasText(roleId) && errors != null) {
                errors.add(ExcelImportError.data(row, obj.getXgh(), "角色不存在"));
                continue;
            }
            ids.append(roleId).append(",");
        }
        if (!ids.isEmpty())
            ids.deleteCharAt(ids.length() - 1);
        obj.setRoleId(ids.toString());
    }

    @Override
    public LinkedHashMap<String, String> getDefaultImportFields() {
        LinkedHashMap<String, String> defaultImportFields = new LinkedHashMap<>();
        defaultImportFields.put("xgh", "工号");
        defaultImportFields.put("xm", "姓名");
        defaultImportFields.put("xb", "性别");
        defaultImportFields.put("xyid", "院系");
        defaultImportFields.put("roleId", "角色");
        return defaultImportFields;
    }

    @Override
    public Object[] importData(MultipartFile file) {
        return super.importData(file);
    }
}
