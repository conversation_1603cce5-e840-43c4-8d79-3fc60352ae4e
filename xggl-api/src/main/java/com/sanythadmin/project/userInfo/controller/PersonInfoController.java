package com.sanythadmin.project.userInfo.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.EnableMask;
import com.sanythadmin.common.core.annotation.IgnoreResponseBodyAdvice;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.enums.ContentTypeDisposition;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.common.system.service.UserOrgMapService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldLink;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldLinkService;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupEduLevelService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupService;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import com.sanythadmin.project.userInfo.service.PersonInfoService;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import com.sanythadmin.project.userInfo.vo.InstructorsVO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.parameters.P;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 人员（学生/教师）信息管理控制器
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api/personInfo")
public class PersonInfoController extends BaseController {
    private final PersonInfoFactory personInfoFactory;
    private final DictionaryGroupService dictionaryGroupService;
    private final DictionaryFieldService dictionaryFieldService;
    private final DictionaryFieldLinkService dictionaryFieldLinkService;
    private final DictionaryGroupEduLevelService dictionaryGroupEduLevelService;
    private final GridFsService gridFsService;
    private final UserInfoService userInfoService;
    private final UserDataScopeService userDataScopeService;
    private final SysRoleService sysRoleService;
    private final RedisTemplate redisTemplate;
    private final UserOrgMapService userOrgMapService;


    /**
     * 查询本人用户信息
     *
     * @return
     */
    @EnableMask
    @GetMapping
    public UserInfo userInfo() {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
        UserInfoUtil.codeTextSet(Collections.singletonList(userInfo));
        return userInfo;
    }

    @IgnoreResponseBodyAdvice
    @GetMapping("/{username}/photo")
    public void photo(@PathVariable("username") String username, HttpServletResponse response) {
        UserInfoParam param = new UserInfoParam();
        param.setXgh(username);
        List<UserInfo> list = userInfoService.listWithPermission(param, UserInfo::getPhoto);
        UserInfo userInfo = CommonUtil.listGetOne(list);
        List<FileInfo> fileInfos = (Objects.isNull(userInfo) || !StringUtils.hasText(userInfo.getPhoto()))
                ? null : JSON.parseArray(userInfo.getPhoto(), FileInfo.class);
        if (CollectionUtils.isEmpty(fileInfos))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "404");
        gridFsService.inlineOrDownload(fileInfos.get(0).getId(), ContentTypeDisposition.inline, response);
    }

    /**
     * 查询证件号码（权限标识：personInfo:idNumber）
     *
     * @param username
     * @return
     */
    @PreAuthorize("hasAuthority('personInfo:idNumber')")
    @GetMapping("/{username}/idNumber")
    public String idNumber(@PathVariable("username") String username) {
        UserInfo userInfo = userInfoService.get(username);
        return Objects.isNull(userInfo) ? null : userInfo.getZjhm();
    }

    @PreAuthorize("hasAuthority('personInfo:student:list')")
    @GetMapping("/{username}/advisor")
    public List<InstructorsVO> advisor(@PathVariable("username") String username) {
        UserInfo userInfo = personInfoFactory.apply(UserType.STUDENT, service -> service.get(username));
        return userDataScopeService.getStudentInstructors(userInfo, Constants.ROLE_SCOPE_BJ,
                UserInfo::getXm, UserInfo::getXgh, UserInfo::getSjh);
    }

    /**
     * 分页查询（权限标识：personInfo:'+(#param.userType.text)+':list）
     *
     * @param param
     * @return
     */
    @EnableMask
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':list')")
    @GetMapping("/{userType}/queryPage")
    public PageResult<UserInfo> queryPage(@P("param") UserInfoParam param, CodeXsztParam param1) {
        return personInfoFactory.apply(param.getUserType(), service -> service.queryPage(param, param1));
    }

    /**
     * 根据学工号查询（权限标识：personInfo:'+(#userType.text)+':list）
     *
     * @param userType
     * @param xgh
     * @return
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':list')")
    @GetMapping("/{userType}/{xgh}")
    public UserInfo get(@P("userType") @PathVariable("userType") UserType userType, @PathVariable("xgh") String xgh) {
        return personInfoFactory.apply(userType, service -> service.get(xgh));
    }


    /**
     * 字段信息查询
     *
     * @param fieldParam
     * @return
     */
    @GetMapping("/{userType}/fieldList")
    public List<DictionaryField> fieldList(DictionaryFieldParam fieldParam, String username) {
        fieldParam.setEduLevelId(getEduLevelId(username));
        fieldParam.setHideRoleId(SecurityUtil.getRole().getId());
        List<DictionaryField> fields = dictionaryFieldService.listRel(fieldParam);
        DictionaryFieldLinkParam linkParam = new DictionaryFieldLinkParam();
        linkParam.setUserType(fieldParam.getUserType());
        PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>(linkParam);
        List<DictionaryFieldLink> fieldLinks = dictionaryFieldLinkService.list(page.getOrderWrapper());
        if (!CollectionUtils.isEmpty(fieldLinks)) {
            Map<String, List<DictionaryFieldLink>> groupMap = fieldLinks.stream().collect(Collectors.groupingBy(DictionaryFieldLink::getFieldEn));
            fields.forEach(field -> field.setFieldLinks(groupMap.getOrDefault(field.getFieldEn(), null)));
        }
        return fields;
    }

    private String getEduLevelId(String username) {
        if (StringUtils.hasText(username)) {
            UserOrgMapParam userOrgMap = new UserOrgMapParam();
            userOrgMap.setXgh(username);
            List<UserOrgMap> orgMaps = userOrgMapService.list(userOrgMap);
            if (!CollectionUtils.isEmpty(orgMaps)) {
                Set<String> collect = orgMaps.stream().map(UserOrgMap::getPyccid).
                        filter(StringUtils::hasText).collect(Collectors.toSet());
                return CommonUtil.strJoin(collect);
            }
        }
        return null;
    }

    /**
     * 字段组查询
     *
     * @param groupParam
     * @return
     */
    @GetMapping("/{userType}/fieldGroupList")
    public List<DictionaryGroup> fieldGroupList(DictionaryGroupParam groupParam, String username) {
        List<DictionaryGroup> groupList = dictionaryGroupService.listRel(groupParam);
        groupParam.setEduLevelId(getEduLevelId(username));
        if (StringUtils.hasText(groupParam.getEduLevelId())) {
            List<String> objects = dictionaryGroupEduLevelService.listObjs(new LambdaQueryWrapper<DictionaryGroupEduLevel>()
                    .select(DictionaryGroupEduLevel::getGroupId)
                    .eq(DictionaryGroupEduLevel::getEduLevelId, groupParam.getEduLevelId()), String::valueOf);
            if (!CollectionUtils.isEmpty(objects)) groupList.removeIf(group -> !objects.contains(group.getId()));
        }
        return groupList;
    }

    /**
     * 数据导入模板下载
     *
     * @param response
     */
    @GetMapping("/{userType}/importTemplate")
    public void importTemplate(@PathVariable(name = "userType") UserType userType, HttpServletResponse response) {
        try {
            List<DictionaryField> list = personInfoFactory.apply(userType, PersonInfoService::getAllImportFields);
            List<String> titles = new ArrayList<>();
            for (DictionaryField field : list) {
                String fieldZh = field.getFieldZh();
                if (field.getImportFieldFlag().getMark()) fieldZh = fieldZh + "（必填）";
                titles.add(fieldZh);
            }
            EasyExcelHelper.exportExcel(null, titles.toArray(new String[]{}), null, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 添加或编辑（权限标识：personInfo:'+(#userType.text)+':operation）
     *
     * @param userType
     * @param request
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':operation')")
    @OperationLog(module = "人员信息管理", comments = "添加或编辑")
    @PostMapping("/{userType}/operation")
    public void operation(@P("userType") @PathVariable(name = "userType") UserType userType, HttpServletRequest request) {
        personInfoFactory.accept(userType, personInfoService -> personInfoService.operation(request));
    }


    /**
     * 数据导入（权限标识：personInfo:'+(#userType.text)+':importData）
     *
     * @param userType
     * @param file
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':importData')")
    @OperationLog(module = "人员信息管理", comments = "数据导入")
    @PostMapping("/{userType}/importData")
    public void importData(@P("userType") @PathVariable(name = "userType") UserType userType, @RequestParam(name = "file") MultipartFile file) {
        Object[] objects = personInfoFactory.apply(userType, service -> service.importData(file));
        String passwordPattern = objects[0] != null ? String.valueOf(objects[0]) : null;
        LinkedHashMap<Integer, UserInfo> userInfoMap = (LinkedHashMap<Integer, UserInfo>) objects[1];
        List<LinkedHashMap> classInfos = objects[3] == null ? null : (List<LinkedHashMap>) objects[2];
        List<LinkedHashMap> deptInfos = (List<LinkedHashMap>) objects[3];
        for (Map.Entry<Integer, UserInfo> entry : userInfoMap.entrySet()) {
            personInfoFactory.accept(userType, service -> service.importData(entry.getValue(), passwordPattern,
                    classInfos, deptInfos));
        }
    }

    /**
     * 数据导出（权限标识：personInfo:'+(#param.userType.text)+':exportData）
     *
     * @param param
     * @param response
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':exportData')")
    @OperationLog(module = "人员信息管理", comments = "数据导出")
    @GetMapping("/{userType}/exportData")
    public void exportData(@P("param") UserInfoParam param, CodeXsztParam param1, String fileName, String fields, HttpServletResponse response) {
        try {
            DictionaryFieldParam fieldParam = new DictionaryFieldParam();
            fieldParam.setFieldEn(fields);
            fieldParam.setUserType(param.getUserType());
            List<DictionaryField> fieldList = dictionaryFieldService.queryList(fieldParam);
            String[] fieldZhArray = fieldList.stream().map(DictionaryField::getFieldZh).toList().toArray(new String[]{});
            String[] fieldEnArray = fieldList.stream().map(DictionaryField::getFieldEn).toList().toArray(new String[]{});
            List<UserInfo> list = personInfoFactory.apply(param.getUserType(), service -> service.queryList(param, param1));
            List<String> newFields = new ArrayList<>();
            for (String f : fieldEnArray) {
                String val = UserInfoUtil.statFieldConvertMap.get(f);
                newFields.add(StringUtils.hasText(val) ? val : f);
            }
            EasyExcelHelper.exportExcel(list, fieldZhArray, newFields.toArray(new String[]{}), response, null, fileName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 删除（权限标识：personInfo:'+(#userType.text)+':remove）
     *
     * @param userType
     * @param ids
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':remove')")
    @OperationLog(module = "人员信息管理", comments = "用户信息删除")
    @PostMapping("/{userType}/remove")
    public void remove(@P("userType") @PathVariable("userType") UserType userType, @RequestBody List<String> ids) {
        removeParamCheck(ids);
        personInfoFactory.accept(userType, service -> service.remove(ids));
    }

    /**
     * 批量上传照片（权限标识：personInfo:'+(#userType.text)+':uploadPhoto）
     *
     * @param userType
     * @param files
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':uploadPhoto')")
    @OperationLog(module = "人员信息管理", comments = "照片打包上传")
    @PostMapping("/{userType}/uploadPhoto")
    public void uploadPhoto(@P("userType") @PathVariable("userType") UserType userType, @RequestParam("file") MultipartFile... files) {
        HttpServletRequest request = ServletUtils.getRequest();
        List<UserInfo> userInfoResults = personInfoFactory.apply(userType, service -> service.queryList(new UserInfoParam(), null));
        Map<String, String> stringMap = userInfoResults.stream().collect(Collectors.toMap(UserInfo::getXgh, obj -> Objects.nonNull(obj.getPhoto()) ? obj.getPhoto() : ""));
        if (files == null || files.length == 0) AssertUtil.throwMessage("请选择要上传的文件");
        for (MultipartFile file : files) {
            uploadPhoto(userType, stringMap, file, request);
        }
    }

    private void uploadPhoto(UserType userType, Map<String, String> stringMap, MultipartFile file, HttpServletRequest request) {
        String originalFilename = file.getOriginalFilename();
        int lastIndex = originalFilename.lastIndexOf(".");
        if (lastIndex == -1) AssertUtil.throwMessage("请选择正确的文件");
        File destFile = null;
        try {
            List<ExcelImportError> errorInfos = new ArrayList<>();
            if (CommonUtil.isZipFile(file)) {
                String separator = File.separator; // 获取系统分隔符
                String filePath = request.getSession().getServletContext().getRealPath("/") + "temp" + separator;
                File path = new File(filePath);
                if (!path.exists()) path.mkdirs();
                String name = CommonUtil.getUUID();
                String suffix = originalFilename.substring(lastIndex, originalFilename.length());
                destFile = new File(filePath + name + suffix);
                file.transferTo(destFile);
                CommonUtil.unzip(destFile.getAbsolutePath(), filePath + name);
                List<String> filePathList = CommonUtil.readFilePath(filePath + name);
                filePathList.forEach(photoPath -> {
                    File tempFile = new File(photoPath);
                    int lastIndexOf = tempFile.getName().lastIndexOf(".");
                    if (lastIndexOf == -1) {
                        errorInfos.add(new ExcelImportError(null, tempFile.getName(), "文件格式错误"));
                        tempFile.delete();
                        return;
                    }
                    String photoName = tempFile.getName().substring(0, lastIndexOf);
                    if (stringMap.containsKey(photoName)) {
                        String oldPhoto = stringMap.get(photoName);
                        personInfoFactory.accept(userType, service -> service.uploadPhoto(photoName, oldPhoto, tempFile));
                    } else {
                        errorInfos.add(new ExcelImportError(null, tempFile.getName(), "未找到对应人员"));
                        tempFile.delete();
                    }
                });
            } else if (CommonUtil.isImageFile(file)) {
                String photoName = originalFilename.substring(0, lastIndex);
                if (stringMap.containsKey(photoName)) {
                    String oldPhoto = stringMap.get(photoName);
                    personInfoFactory.accept(userType, service -> service.uploadPhoto(photoName, oldPhoto, file));
                } else {
                    errorInfos.add(new ExcelImportError(null, photoName, "未找到对应人员"));
                }
            } else {
                AssertUtil.throwMessage("请上传zip包或图片文件");
            }

            if (!CollectionUtils.isEmpty(errorInfos)) {
                String str = CommonUtil.writeErrorInfoExcel(errorInfos);
                AssertUtil.throwImportError(str);
            }
        } catch (Exception e) {
            if (e instanceof BusinessException) AssertUtil.throwMessage((BusinessException) e);
            log.error(e.getMessage(), e);
        } finally {
            if (destFile != null && destFile.exists()) destFile.delete();
        }
    }


    /**
     * 照片打包下载（权限标识：personInfo:'+(#userType.text)+':downloadPhoto）
     *
     * @param param
     * @param response
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':downloadPhoto')")
    @OperationLog(module = "人员信息管理", comments = "照片打包下载")
    @GetMapping("/{userType}/downloadPhoto")
    public void downloadPhoto(@P("param") UserInfoParam param, CodeXsztParam param1, HttpServletResponse response) {
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(System.currentTimeMillis() + ".zip", StandardCharsets.UTF_8));
            OutputStream out = response.getOutputStream();
            ZipOutputStream zos = new ZipOutputStream(out);
            zos.setMethod(ZipOutputStream.DEFLATED);
            BufferedOutputStream bos = new BufferedOutputStream(zos);
            List<UserInfo> userInfoResults = personInfoFactory.apply(param.getUserType(), service -> service.queryList(param, param1));
            Map<String, String> userPhotoMap = userInfoResults.stream().filter(obj -> StringUtils.hasLength(obj.getPhoto())).collect(Collectors.toMap(UserInfo::getXgh, UserInfo::getPhoto));
            for (String key : userPhotoMap.keySet()) {
                String string = userPhotoMap.get(key);
                List<FileInfo> fileInfos = JSONArray.parseArray(string, FileInfo.class);
                if (CollectionUtils.isEmpty(fileInfos)) continue;
                FileInfo fileInfo = fileInfos.get(0);
                if (StringUtils.hasLength(fileInfo.getId())) {
                    GridFsResource gridFsResource = gridFsService.get(fileInfo.getId());
                    if (gridFsResource != null) {
                        InputStream in = gridFsResource.getInputStream();
                        BufferedInputStream bis = new BufferedInputStream(in);
                        zos.putNextEntry(new ZipEntry(key + fileInfo.getOriginalFilename().substring(fileInfo.getOriginalFilename().lastIndexOf("."))));
                        int len = 0;
                        byte[] buf = new byte[10 * 1024];
                        while ((len = bis.read(buf, 0, buf.length)) != -1) {
                            bos.write(buf, 0, len);
                        }
                        bis.close();
                        bos.flush();
                    }
                }
            }
            bos.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 图表统计数据（权限标识：personInfo:'+(#param.userType.text)+':chartData）
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':chartData')")
    @GetMapping("/{userType}/chartData")
    public List<Map<String, Object>> chartData(@P("param") UserInfoParam param) {
        return personInfoFactory.apply(param.getUserType(), service -> service.chartData(param));
    }

    /**
     * 图表统计数据excel导出（权限标识：personInfo:'+(#param.userType.text)+':chartDataExport）
     *
     * @param param
     * @param response
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':chartDataExport')")
    @GetMapping("/{userType}/chartDataExport")
    public void chartDataExport(@P("param") UserInfoParam param, HttpServletResponse response) {

        List<Map<String, Object>> list = personInfoFactory.apply(param.getUserType(), service -> service.chartData(param));
    }

    /**
     * 批量更新导入模板下载
     *
     * @param userType
     * @param title
     * @param response
     */
    @GetMapping("/{userType}/exportUpdateTemplate")
    public void exportUpdateTemplate(@PathVariable("userType") UserType userType, @RequestParam("title") String title, HttpServletResponse response) {
        try {
            DictionaryFieldParam fieldParam = new DictionaryFieldParam();
            fieldParam.setUserType(userType);
            fieldParam.setFieldEn("xgh");
            List<DictionaryField> fields = dictionaryFieldService.queryList(fieldParam);
            List<String> list = fields.stream().map(DictionaryField::getFieldZh).collect(Collectors.toList());
            List<String> titleList = Arrays.asList(CommonUtil.split(title));
            titleList.forEach(s -> {
                if (!list.contains(s)) list.add(s);
            });
            EasyExcelHelper.exportExcel(null, list.toArray(new String[]{}), null, response, null, userType.getRemark() + "数据批量更新模板");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 批量更新导入（权限标识：personInfo:'+(#userType.text)+':importUpdateData）
     *
     * @param userType
     * @param file
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':importUpdateData')")
    @PostMapping("/{userType}/importUpdateData")
    @OperationLog(module = "人员信息管理", comments = "批量更新导入")
    public void importUpdateData(@P("userType") @PathVariable("userType") UserType userType, @RequestParam("file") MultipartFile file) {
        Object[] objects = personInfoFactory.apply(userType, service -> service.importUpdateDataCheckAndGet(file));
        List<UserInfo> userInfos = (List<UserInfo>) objects[0];
        Map<String, SysAccount> accountMap = (Map<String, SysAccount>) objects[1];
        List<LinkedHashMap> classInfos = objects[3] == null ? null : (List<LinkedHashMap>) objects[2];
        List<LinkedHashMap> deptInfos = (List<LinkedHashMap>) objects[3];
        for (UserInfo userInfo : userInfos) {
            SysAccount account = accountMap.get(userInfo.getXgh());
            personInfoFactory.accept(userType, service -> service.importUpdate(userInfo, account, classInfos, deptInfos));
        }
    }

    /**
     * 分页查询指定用户及角色数据权限下用户（权限标识：personInfo:'+(#param.userType.text)+':list）
     *
     * @param param
     * @return
     */
    @EnableMask
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':list')")
    @GetMapping("/{userType}/queryListByRoleDuty")
    public PageResult<UserInfo> queryListByRoleDuty(@P("param") UserInfoParam param, String username) {
        return personInfoFactory.apply(param.getUserType(), service -> service.queryListByRoleDuty(param, CodeXsztParam.currentStudent(), username));
    }

    @PreAuthorize("hasAuthority('personInfo:student:list')")
    @GetMapping("/getCountByRoleDuty")
    public Map<String, Long> getCountByRoleDuty(@RequestParam("username") String username, @RequestParam("roleId") String roleId) {
        Map<String, Long> countMap = new HashMap<>();
        String countKey = String.format(Constants.USER_DATA_SCOPE_COUNT_KEY, username);
        countMap = redisTemplate.opsForHash().entries(countKey);
        if (countMap != null && countMap.size() > 0)
            return countMap;

        UserDataScopeParam param = new UserDataScopeParam();
        param.setGlzXgh(username);
        param.setRoleId(roleId);
        List<UserDataScope> list = userDataScopeService.queryList(param);
        if (CollectionUtils.isEmpty(list)) return null;
        UserDataScope dataScope = userDataScopeService.list2Single(list, true);
        CodeXsztParam stateParam = CodeXsztParam.currentStudent();
        RoleParam roleParam = new RoleParam();
        roleParam.setId(roleId);
        SysRole role = CommonUtil.listGetOne(sysRoleService.list(roleParam));
        String[] fields = new String[]{"bjid", "zyid", "xyid", "njid", "pyccid"};
        UserInfoParam userInfoParam = new UserInfoParam();
        userInfoParam.setRoleId(roleId);
        for (String name : fields) {
            Object value = CommonUtil.getValue(name, dataScope);
            if (value != null && StringUtils.hasText(String.valueOf(value))) {
                String[] array = CommonUtil.split(String.valueOf(value));
                for (String id : array) {
                    CommonUtil.setValue(userInfoParam, name, id);
                    Long count = personInfoFactory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(userInfoParam, stateParam, username, roleId, role.getRoleScope()));
                    countMap.put(id, count);
                    CommonUtil.setValue(userInfoParam, name, null);
                }
            }
        }
        redisTemplate.opsForHash().putAll(countKey, countMap);
        return countMap;
    }

    @PreAuthorize("hasAuthority('personInfo:student:updateStudentStatus')")
    @PostMapping("/{statusId}/updateStudentStatus")
    public void updateStudentStatus(@PathVariable("statusId") String statusId, UserInfoParam param, CodeXsztParam param1) {
        param.setUserType(UserType.STUDENT);
        List<UserInfo> list = personInfoFactory.apply(param.getUserType(),
                service -> service.queryList(param, param1, UserInfo::getXgh));
        for (UserInfo userInfo : list) {
            userInfoService.updateStatus(userInfo.getXgh(), statusId);
        }
    }
}
