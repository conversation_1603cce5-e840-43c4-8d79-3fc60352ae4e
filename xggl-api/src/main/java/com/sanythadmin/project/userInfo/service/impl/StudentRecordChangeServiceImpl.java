package com.sanythadmin.project.userInfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.userInfo.entity.StudentRecordChange;
import com.sanythadmin.project.userInfo.mapper.StudentRecordChangeMapper;
import com.sanythadmin.project.userInfo.param.StudentRecordChangeParam;
import com.sanythadmin.project.userInfo.service.StudentRecordChangeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2025-07-21 17:36:41
 */
@Service
public class StudentRecordChangeServiceImpl extends ServiceImpl<StudentRecordChangeMapper, StudentRecordChange> implements StudentRecordChangeService {

    @Resource
    private StudentRecordChangeMapper mapper;

    @Override
    public PageResult<StudentRecordChange> page(StudentRecordChangeParam param) {
        PageParam<StudentRecordChange, StudentRecordChangeParam> page = new PageParam<>(param);
        page = baseMapper.selectPageWithPermission(page, page.getOrderWrapper(true));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
