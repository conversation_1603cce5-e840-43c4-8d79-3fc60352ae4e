<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.userInfo.mapper.UserDataScopeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , XYID, ZYID, BJID, NJID, PYCCID, XGH, GLZ_XGH, ROLE_ID, STATUS
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.xyid != null">
            AND a.XYID LIKE '%'||#{param.xyid }||'%'
        </if>
        <if test="param.zyid != null">
            AND a.ZYID LIKE '%'||#{param.zyid }||'%'
        </if>
        <if test="param.bjid != null">
            AND a.BJID LIKE '%'||#{param.bjid }||'%'
        </if>
        <if test="param.njid != null">
            AND a.NJID LIKE '%'||#{param.njid }||'%'
        </if>
        <if test="param.pyccid != null">
            AND a.PYCCID LIKE '%'||#{param.pyccid }||'%'
        </if>
        <if test="param.xgh != null">
            AND a.XGH LIKE '%'||#{param.xgh }||'%'
        </if>
        <if test="param.glzXgh != null">
            AND a.GLZ_XGH LIKE '%'||#{param.glzXgh }||'%'
        </if>
        <if test="param.roleId != null">
            AND a.ROLE_ID LIKE '%'||#{param.roleId }||'%'
        </if>
        <if test="param.status != null">
            AND a.STATUS = #{param.status}
        </if>
    </sql>
</mapper>
