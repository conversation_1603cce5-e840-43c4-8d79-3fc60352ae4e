package com.sanythadmin.project.userInfo.result;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class UserInfoResult extends UserInfo {
//    /**
//     * 学院名称
//     */
//    private String xymc;
//    /**
//     * 专业名称
//     */
//    private String zymc;
//    /**
//     * 班级名称
//     */
//    private String bjmc;
//    /**
//     * 年级名称
//     */
//    private String njmc;
//    /**
//     * 培养层次名称
//     */
//    private String pyccmc;
//    /**
//     * 人员状态
//     */
//    private String ryzt;
}
