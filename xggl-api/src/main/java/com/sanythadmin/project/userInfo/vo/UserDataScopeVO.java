package com.sanythadmin.project.userInfo.vo;

import com.sanythadmin.project.userInfo.entity.UserDataScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * Created by JIANGPING on 2024/11/26.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDataScopeVO extends UserDataScope {
    private String zymc;
    private String bjmc;
    private String xymc;
    private String njmc;
    private String pyccmc;
    private Map<String, String> countMap;
}
