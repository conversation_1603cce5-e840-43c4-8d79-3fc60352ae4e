<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.userInfo.mapper.StudentRecordChangeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, BJID, CLWH, CZR, SPZT, XYID, YDLB, YDQBJID, YDQXYID, YDQZYID, YDSJ, YDSM, YDYY, ZYID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.clwh != null">
                        AND a.CLWH LIKE concat(concat('%',#{param.clwh), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.spzt != null">
                        AND a.SPZT LIKE concat(concat('%',#{param.spzt), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.ydlb != null">
                        AND a.YDLB LIKE concat(concat('%',#{param.ydlb), '%')
                    </if>
                    <if test="param.ydqbjid != null">
                        AND a.YDQBJID LIKE concat(concat('%',#{param.ydqbjid), '%')
                    </if>
                    <if test="param.ydqxyid != null">
                        AND a.YDQXYID LIKE concat(concat('%',#{param.ydqxyid), '%')
                    </if>
                    <if test="param.ydqzyid != null">
                        AND a.YDQZYID LIKE concat(concat('%',#{param.ydqzyid), '%')
                    </if>
                    <if test="param.ydsj != null">
                        AND a.YDSJ LIKE concat(concat('%',#{param.ydsj), '%')
                    </if>
                    <if test="param.ydsm != null">
                        AND a.YDSM LIKE concat(concat('%',#{param.ydsm), '%')
                    </if>
                    <if test="param.ydyy != null">
                        AND a.YDYY LIKE concat(concat('%',#{param.ydyy), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
    </sql>
</mapper>
