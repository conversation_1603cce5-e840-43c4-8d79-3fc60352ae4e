package com.sanythadmin.project.userInfo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2025-07-21 17:36:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StudentRecordChangeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String xm;

    private Gender xb;

    private String mzmc;

    private String bjid;

    private String clwh;

    private String czr;

    private String spzt;

    private String xyid;

    private String ydlb;

    private String ydqbjid;

    private String ydqxyid;

    private String ydqzyid;

    private String ydsj;

    private String ydsm;

    private String ydyy;

    private String zyid;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "ydsj desc";
    }
}
