package com.sanythadmin.project.userInfo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户数据权限查询参数
 *
 * <AUTHOR>
 * @since 2024-04-22 13:37:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserDataScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(type = QueryType.EQ)
    private String xyid;
    @QueryField(type = QueryType.EQ)
    private String zyid;
    @QueryField(type = QueryType.EQ)
    private String bjid;
    @QueryField(type = QueryType.EQ)
    private String njid;
    @QueryField(type = QueryType.EQ)
    private String pyccid;
    @QueryField(type = QueryType.EQ)
    private String xgh;
    @QueryField(type = QueryType.EQ)
    private String glzXgh;
    @QueryField(type = QueryType.IN_STR)
    private String roleId;

    /**
     * 状态（1：有效，0：无效）
     */
    @QueryField(type = QueryType.EQ)
    private Integer status;

}
