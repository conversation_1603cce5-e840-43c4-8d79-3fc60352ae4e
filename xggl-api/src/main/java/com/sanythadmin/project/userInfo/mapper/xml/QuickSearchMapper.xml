<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.userInfo.mapper.QuickSearchMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, USERNAME, NAME, CONDITION, CREATE_TIME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%', #{param.username }), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%', #{param.name }), '%')
                    </if>
                    <if test="param.condition != null">
                        AND a.CONDITION LIKE concat(concat('%', #{param.condition }), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%', #{param.createTime }), '%')
                    </if>
    </sql>
</mapper>
