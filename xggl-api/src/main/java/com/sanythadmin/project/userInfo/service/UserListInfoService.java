package com.sanythadmin.project.userInfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.userInfo.entity.UserListInfo;
import com.sanythadmin.project.userInfo.param.UserListInfoParam;

import java.util.List;

/**
 * 用户多条信息Service
 *
 * <AUTHOR>
 * @since 2024-04-02 15:29:34
 */
public interface UserListInfoService extends IService<UserListInfo> {
    /**
     * 用户列表信息编辑
     *
     * @param userListInfo
     */
    void edit(UserListInfo userListInfo);
    PageResult<UserListInfo> page(UserListInfoParam param);
    List<UserListInfo> list(UserListInfoParam param);
}
