package com.sanythadmin.project.userInfo.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义查询条件记录查询参数
 *
 * <AUTHOR>
 * @since 2024-05-23 10:05:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QuickSearchParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(type = QueryType.EQ)
    private String username;

    private String name;

    private String condition;
}
