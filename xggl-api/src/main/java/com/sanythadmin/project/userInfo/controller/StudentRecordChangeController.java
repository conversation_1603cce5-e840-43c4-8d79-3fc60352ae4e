package com.sanythadmin.project.userInfo.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.userInfo.service.StudentRecordChangeService;
import com.sanythadmin.project.userInfo.entity.StudentRecordChange;
import com.sanythadmin.project.userInfo.param.StudentRecordChangeParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 学籍异动信息 控制器
 *
 * <AUTHOR>
 * @since 2025-07-21 17:36:41
 */
@RestController
@RequestMapping("/api/userInfo/student-record-change")
public class StudentRecordChangeController extends BaseController {
    @Resource
    private StudentRecordChangeService studentRecordChangeService;

    /**
     * 分页查询（权限标识：userInfo:studentRecordChange:list）
     */
    @PreAuthorize("hasAuthority('userInfo:studentRecordChange:list')")
    @GetMapping("/page")
    public PageResult<StudentRecordChange> page(StudentRecordChangeParam param) {
        return studentRecordChangeService.page(param);
    }

    /**
     * 查询全部（权限标识：userInfo:studentRecordChange:list）
     */
    @PreAuthorize("hasAuthority('userInfo:studentRecordChange:list')")
    @GetMapping()
    public List<StudentRecordChange> list(StudentRecordChangeParam param) {
        PageParam<StudentRecordChange, StudentRecordChangeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return studentRecordChangeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询（权限标识：userInfo:studentRecordChange:list）
     */
    @PreAuthorize("hasAuthority('userInfo:studentRecordChange:list')")
    @GetMapping("/{id}")
    public StudentRecordChange get(@PathVariable("id") String id) {
        return studentRecordChangeService.getById(id);
    }

    /**
     * 添加或修改（权限标识：userInfo:studentRecordChange:operation）
     */
    @PreAuthorize("hasAuthority('userInfo:studentRecordChange:operation')")
    @OperationLog(module = "", comments = "保存")
    @PostMapping("/operation")
    public void save(@RequestBody StudentRecordChange studentRecordChange) {
        if (StringUtils.hasLength(studentRecordChange.getId())) {
            studentRecordChangeService.updateById(studentRecordChange);
        } else {
            studentRecordChangeService.save(studentRecordChange);
        }
    }

    /**
     * 批量删除（权限标识：userInfo:studentRecordChange:remove）
     */
    @PreAuthorize("hasAuthority('userInfo:studentRecordChange:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        studentRecordChangeService.removeByIds(ids);
    }
}
