package com.sanythadmin.project.userInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.userInfo.service.PersonInfoService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.LinkedHashMap;
import java.util.List;

@Service("studentInfoService")
public class StudentInfoServiceImpl extends PersonInfoService {

    @Override
    protected UserType getUserType() {
        return UserType.STUDENT;
    }

    @Override
    public LinkedHashMap<String, String> getDefaultImportFields() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        map.put("xgh", "学号");
        map.put("xm", "姓名");
        map.put("xb", "性别");
        map.put("xyid", "院系");
        map.put("zyid", "专业");
        map.put("bjid", "班级");
        map.put("njid", "年级");
        map.put("zjlx", "证件类型");
        map.put("zjhm", "证件号码");
        map.put("ryztid", "人员状态");
        return map;
    }

    @Override
    public List<DictionaryField> getAllImportFields() {
        return super.getAllImportFields();
    }

    @Override
    protected boolean checkImportFields() {
        return true;
    }

    @Override
    public Object[] importData(MultipartFile file) {
        return super.importData(file);
    }

    @Override
    protected void setRoleId(LinkedHashMap<Integer, UserInfo> userInfoMap, UserInfo userInfo,List<ExcelImportError> errors) {
        List<SysRole> roles = sysRoleMapper.selectList(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_GR));
        SysRole role = roles.get(0);
        if (userInfoMap != null) {
            userInfoMap.forEach((k, obj) -> {
                obj.setRoleId(role.getId());
            });
        }

        if (userInfo != null) {
            userInfo.setRoleId(role.getId());
        }
    }
}
