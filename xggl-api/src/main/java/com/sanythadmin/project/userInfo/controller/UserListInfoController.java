package com.sanythadmin.project.userInfo.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.userInfo.entity.UserListInfo;
import com.sanythadmin.project.userInfo.param.UserListInfoParam;
import com.sanythadmin.project.userInfo.service.UserListInfoService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.parameters.P;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户多条信息控制器
 *
 * <AUTHOR>
 * @since 2024-04-02 15:29:34
 */
@RestController
@RequestMapping("/api/userListInfo")
public class UserListInfoController extends BaseController {
    @Resource
    private UserListInfoService userListInfoService;

    /**
     * 分页查询用户多条信息（权限标识：userListInfo:{userType}:list）
     */
    @PreAuthorize("hasAuthority('userListInfo:'+(#param.userType.text)+':list')")
    @GetMapping("/{userType}/page")
    public PageResult<UserListInfo> page(@P("param") UserListInfoParam param) {
        return userListInfoService.page(param);
    }

    /**
     * 查询全部用户多条信息（权限标识：userListInfo:{userType}:list）
     */
    @PreAuthorize("hasAuthority('userListInfo:'+(#param.userType.text)+':list')")
    @GetMapping("/{userType}")
    public List<UserListInfo> list(@P("param") UserListInfoParam param) {
        return userListInfoService.list(param);
    }

    /**
     * 根据id查询用户多条信息（权限标识：userListInfo:{userType}:list）
     */
    @PreAuthorize("hasAuthority('userListInfo:'+(#userType.text)+':list')")
    @GetMapping("/{userType}/{id}")
    public UserListInfo get(@P("userType") @PathVariable UserType userType, @PathVariable("id") Integer id) {
        return userListInfoService.getById(id);
    }

    /**
     * 添加或修改用户多条信息（权限标识：userListInfo:{userType}:operation）
     */
    @PreAuthorize("hasAuthority('userListInfo:'+(#userListInfo.userType.text)+':operation')")
    @OperationLog(module = "用户多条信息", comments = "保存用户多条信息")
    @PostMapping("/{userType}/operation")
    public void save(@P("userListInfo") @Validated @RequestBody UserListInfo userListInfo) {
        userListInfoService.edit(userListInfo);
    }

    /**
     * 批量删除用户多条信息（权限标识：userListInfo:{userType}:remove）
     */
    @PreAuthorize("hasAuthority('userListInfo:'+(#userType.text)+':remove')")
    @OperationLog(module = "用户多条信息", comments = "批量删除用户多条信息")
    @PostMapping("/{userType}/remove")
    public void remove(@P("userType") @PathVariable UserType userType, @RequestBody List<String> ids) {
        removeParamCheck(ids);
        userListInfoService.removeByIds(ids);
    }
}
