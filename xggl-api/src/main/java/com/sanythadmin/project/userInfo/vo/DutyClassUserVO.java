package com.sanythadmin.project.userInfo.vo;

import com.sanythadmin.common.system.result.AccountRoleResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025/6/9.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DutyClassUserVO extends AccountRoleResult {
    private Long maleCount;     // 男生人数
    private Long femaleCount;   // 女生人数
    private Long count;         // 总人数
}
