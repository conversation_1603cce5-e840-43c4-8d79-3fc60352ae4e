package com.sanythadmin.project.userInfo.controller;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.EnableMask;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.SysAccountParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.result.AccountRoleResult;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.userInfo.dto.UserDataScopeDTO;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import com.sanythadmin.project.userInfo.vo.DutyClassDetailVO;
import com.sanythadmin.project.userInfo.vo.DutyClassUserVO;
import com.sanythadmin.project.userInfo.vo.UserDataScopeVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户数据权限控制器
 *
 * <AUTHOR>
 * @since 2024-04-22 13:37:34
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/userInfo/userDataScope")
public class UserDataScopeController extends BaseController {
    private final UserDataScopeService userDataScopeService;
    private final SysAccountService sysAccountService;
    private final PersonInfoFactory factory;
    private final RedisTemplate redisTemplate;
    private final SysRoleService sysRoleService;


    /**
     * 分页查询用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping("/page")
    public PageResult<UserDataScope> page(UserDataScopeParam param) {
        PageParam<UserDataScope, UserDataScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = userDataScopeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 带班用户分页列表查询（权限标识：userInfo:userDataScope:listDutyClassUserPage）
     */
    @EnableMask
    @PreAuthorize("hasAuthority('userInfo:userDataScope:listDutyClassUserPage')")
    @GetMapping("/listDutyClassUserPage")
    public PageResult<DutyClassUserVO> listClassDutyUserPage(SysAccountParam param) {
        PageResult<AccountRoleResult> pageResult = sysAccountService.queryAllAccount(param);
        List<AccountRoleResult> results = pageResult.getList();
        List<DutyClassUserVO> dutyClassUserVOS = pageResult.getList() != null ? results.stream()
                .map(result -> CommonUtil.copyProperties(DutyClassUserVO.class, result))
                .toList() : null;
        if (!CollectionUtils.isEmpty(dutyClassUserVOS)) {
            CodeXsztParam state = CodeXsztParam.currentStudent();
            dutyClassUserVOS.forEach(vo -> {
                UserInfoParam user = new UserInfoParam();
                user.setXb(Gender.MALE);
                Long maleCount = factory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(user, state,
                        vo.getUsername(), vo.getRoleId(), vo.getRoleScope()));
                user.setXb(Gender.FEMALE);
                Long femaleCount = factory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(user, state,
                        vo.getUsername(), vo.getRoleId(), vo.getRoleScope()));
                vo.setMaleCount(maleCount);
                vo.setFemaleCount(femaleCount);
                vo.setCount(maleCount + femaleCount);
            });
        }
        return new PageResult<>(dutyClassUserVOS, pageResult.getCount());
    }

    @PreAuthorize("hasAuthority('userInfo:userDataScope:listDutyClassUserPage')")
    @GetMapping("/listClassDetailDutyPage")
    public PageResult<DutyClassDetailVO> listClassDetailDutyPage(String username, String roleId, String roleScope) {
        return factory.apply(UserType.STUDENT, service -> service.pageDutyClassDetail(null,
                CodeXsztParam.currentStudent(), username, roleId, roleScope));
    }

    /**
     * 查询全部用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping()
    public List<UserDataScope> list(UserDataScopeParam param) {
        return userDataScopeService.queryList(param);
    }

    /**
     * 添加或修改用户数据权限（权限标识：userInfo:userDataScope:operation）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:operation')")
    @OperationLog(module = "用户数据权限", comments = "保存用户数据权限")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody UserDataScope userDataScope) {
        userDataScopeService.edit(userDataScope);
    }

    /**
     * 批量删除用户数据权限（权限标识：userInfo:userDataScope:remove）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:remove')")
    @OperationLog(module = "用户数据权限", comments = "批量删除用户数据权限")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        userDataScopeService.removeByIds(ids);
    }

    /**
     * 获取用户数据权限（权限标识：userInfo:userDataScope:get）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:get')")
    @GetMapping("/{glzXgh}/{roleId}")
    public UserDataScopeVO get(@PathVariable("glzXgh") String glzXgh, @PathVariable("roleId") String roleId) {
        UserDataScopeParam param = new UserDataScopeParam();
        param.setGlzXgh(glzXgh);
        param.setRoleId(roleId);
        List<UserDataScope> list = userDataScopeService.queryList(param);
        if (CollectionUtils.isEmpty(list))
            return null;
        UserDataScope dataScope = userDataScopeService.list2Single(list, true);
        UserDataScopeVO vo = new UserDataScopeVO();
        BeanUtils.copyProperties(dataScope, vo);
        UserInfoUtil.codeTextSet(vo);
        Map<String, String> countMap = getCountByRoleDuty(glzXgh, roleId, dataScope);
        vo.setCountMap(countMap);
        return vo;
    }

    public Map<String, String> getCountByRoleDuty(String username, String roleId, UserDataScope dataScope) {
        Map<String, String> countMap;
        String countKey = String.format(Constants.USER_DATA_SCOPE_COUNT_KEY, username);
        countMap = redisTemplate.opsForHash().entries(countKey);
        if (countMap != null && !countMap.isEmpty())
            return countMap;
        CodeXsztParam stateParam = CodeXsztParam.currentStudent();
        RoleParam roleParam = new RoleParam();
        roleParam.setId(roleId);
        SysRole role = CommonUtil.listGetOne(sysRoleService.list(roleParam));
        String[] fields = new String[]{"bjid", "zyid", "xyid", "njid", "pyccid"};
        UserInfoParam userInfoParam = new UserInfoParam();
        userInfoParam.setRoleId(roleId);
        for (String name : fields) {
            Object value = CommonUtil.getValue(name, dataScope);
            if (value != null && StringUtils.hasText(String.valueOf(value))) {
                String[] array = CommonUtil.split(String.valueOf(value));
                for (String id : array) {
                    CommonUtil.setValue(userInfoParam, name, id);
                    Long count = factory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(userInfoParam, stateParam, username, roleId, role.getRoleScope()));
                    countMap.put(id, String.valueOf(count.longValue()));
                    CommonUtil.setValue(userInfoParam, name, null);
                }
            }
        }

        userInfoParam.setXb(Gender.MALE);
        Long maleCount = factory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(userInfoParam, stateParam,
                username, roleId, role.getRoleScope()));
        userInfoParam.setXb(Gender.FEMALE);
        Long femaleCount = factory.apply(UserType.STUDENT, service -> service.getUserCountByRoleDuty(userInfoParam, stateParam,
                username, roleId, role.getRoleScope()));
        countMap.put(Gender.MALE.toString().toLowerCase(), String.valueOf(maleCount.longValue()));
        countMap.put(Gender.FEMALE.toString().toLowerCase(), String.valueOf(femaleCount.longValue()));
        redisTemplate.opsForHash().putAll(countKey, countMap);
        return countMap;
    }

    private List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
        heads.add(new ExcelUtils.Head("教师工号", "glzXgh"));
        heads.add(new ExcelUtils.Head("教师姓名", "jsxm", false));
        heads.add(new ExcelUtils.Head("角色", "roleId"));
        heads.add(new ExcelUtils.Head("学号", "xgh", false));
        heads.add(new ExcelUtils.Head("学生姓名", "xsxm", false));
        heads.add(new ExcelUtils.Head("班级名称/代码", "bjid", false));
        heads.add(new ExcelUtils.Head("专业名称/代码", "zyid", false));
        heads.add(new ExcelUtils.Head("学院名称/代码", "xyid", false));
        heads.add(new ExcelUtils.Head("年级名称/代码", "njid", false));
        heads.add(new ExcelUtils.Head("培养层次名称/代码", "pyccid", false));
        return heads;
    }

    /**
     * 带生数据导入模板
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:dataImport')")
    @GetMapping("/dataImportTemplate")
    public void dataImportTemplate(HttpServletResponse response) {
        ExcelUtils.dataImportTemplate(getHeads(), response);
    }

    /**
     * 带生数据导入（权限标识：userInfo:userDataScope:dataImport）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:dataImport')")
    @OperationLog(module = "用户数据权限", comments = "带生数据导入")
    @PostMapping("/dataImport")
    public void dataImport(@RequestParam(name = "file") MultipartFile file) throws Exception {
        List<UserDataScopeDTO> infos = ExcelUtils.readExcelToEntity(UserDataScopeDTO.class, file.getInputStream(), file.getOriginalFilename(), getHeads());
        List<UserDataScope> scopes = userDataScopeService.dataImportCheck(infos);
        scopes.forEach(userDataScopeService::edit);
    }
}
