package com.sanythadmin.project.userInfo.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.userInfo.dto.DataScopeCache;
import com.sanythadmin.project.userInfo.dto.UserDataScopeDTO;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.vo.InstructorsVO;

import java.util.List;

/**
 * 用户数据权限Service
 *
 * <AUTHOR>
 * @since 2024-04-22 13:37:34
 */
public interface UserDataScopeService extends IService<UserDataScope> {

    public void edit(UserDataScope object);

    public List<UserDataScope> queryList(UserDataScopeParam param);

    public <T extends UserDataScope> UserDataScope list2Single(List<T> list, boolean showAll);

    public MPJLambdaWrapper<UserDataScope> buildUserScopeQuery(BaseUserInfo userInfo, String roleScope);
    public <T> void appendQueryScope(MPJLambdaWrapper<T> consumer, BaseUserInfo userInfo);

    public List<InstructorsVO> getStudentInstructors(BaseUserInfo userInfo, String roleScope, SFunction<UserInfo, ?>... selectFields);

    public List<UserDataScope> dataImportCheck(List<UserDataScopeDTO> infos);

    public DataScopeCache get(String username, String roleId);
}
