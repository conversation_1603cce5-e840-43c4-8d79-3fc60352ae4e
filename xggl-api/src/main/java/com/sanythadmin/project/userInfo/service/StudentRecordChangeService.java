package com.sanythadmin.project.userInfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.userInfo.entity.StudentRecordChange;
import com.sanythadmin.project.userInfo.param.StudentRecordChangeParam;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2025-07-21 17:36:41
 */
public interface StudentRecordChangeService extends IService<StudentRecordChange> {

    public PageResult<StudentRecordChange> page(StudentRecordChangeParam param);

}
