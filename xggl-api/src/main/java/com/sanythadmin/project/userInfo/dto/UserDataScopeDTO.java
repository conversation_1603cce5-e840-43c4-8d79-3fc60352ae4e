package com.sanythadmin.project.userInfo.dto;

import com.sanythadmin.project.userInfo.entity.UserDataScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * @since 2025/6/20.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDataScopeDTO extends UserDataScope {
    private String jsxm;
    private String xsxm;

    public boolean scopeIsEmpty() {
        return !StringUtils.hasText(getXgh()) && !StringUtils.hasText(getBjid())
                && !StringUtils.hasText(getXyid()) && !StringUtils.hasText(getZyid())
                && !StringUtils.hasText(getNjid()) && !StringUtils.hasText(getPyccid());
    }
}
