package com.sanythadmin.project.userInfo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 用户多条信息查询参数
 *
 * <AUTHOR>
 * @since 2024-04-02 15:29:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserListInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    private String bz1;

    private String bz2;

    private String bz3;

    private String bz4;

    private String bz5;

    private String bz6;

    private String bz7;

    private String bz8;

    private String bz9;

    private String bz10;

    private String bz11;

    private String bz12;

    private String bz13;

    private String bz14;

    private String bz15;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 用户类型（1：学生，2：教师）
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;

    /**
     * 类型（例：家庭成员/教育经历）
     */
    private String groupName;

    /**
     * 用户信息组ID
     */
    @QueryField(type = QueryType.EQ)
    private String groupId;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "createTime desc";
    }
}
