package com.sanythadmin.project.userInfo.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.handler.CustomDataPermissionHandlerImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.Required;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.*;
import com.sanythadmin.common.system.mapper.SysAccountMapper;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.mapper.CodeCommonMapper;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.mapper.DictionaryFieldMapper;
import com.sanythadmin.project.dictionary.mapper.DictionaryGroupMapper;
import com.sanythadmin.project.userInfo.vo.DutyClassDetailVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class PersonInfoService {
    @Resource
    protected SysAccountMapper sysAccountMapper;
    @Resource
    protected SysAccountRoleMapper accountRoleMapper;
    @Resource
    protected SysRoleMapper sysRoleMapper;
    @Resource
    protected UserInfoMapper userInfoMapper;
    @Resource
    protected CodeCommonMapper codeCommonMapper;
    @Resource
    protected DictionaryFieldMapper dictionaryFieldMapper;
    @Resource
    protected SysParamService sysParamService;
    @Resource
    protected GridFsService gridFsService;
    @Resource
    protected DictionaryGroupMapper dictionaryGroupMapper;
    @Resource
    protected UserInfoService userInfoService;
    @Resource
    private Environment env;
    @Resource
    private SysAccountService accountService;
    @Resource
    private SysRoleService roleService;

    protected abstract UserType getUserType();

    protected abstract void setRoleId(LinkedHashMap<Integer, UserInfo> userInfoMap, UserInfo userInfo, List<ExcelImportError> errors);

    public abstract LinkedHashMap<String, String> getDefaultImportFields();

    @Transactional
    public void operation(HttpServletRequest request) {
        FormDataWrapper<UserInfo> wrapper = new FormDataWrapper<>(request, UserInfo.class);
        Map<String, Object> textFields = wrapper.getTextFields();
        UserInfo param = wrapper.getObject();
        Map<String, List<MultipartFile>> fileFields = wrapper.getFileFields();
        param.setUserType(getUserType());
        if (!StringUtils.hasText(param.getXgh())) AssertUtil.throwMessage("学号/工号不能为空");
        UserInfo userInfo = userInfoMapper.selectById(param.getXgh());
        if (userInfo == null) AssertUtil.throwMessage("根据学号/工号未找到用户信息");
        for (Map.Entry<String, Object> entry : textFields.entrySet()) {
            CommonUtil.setValue(userInfo, entry.getKey(), CommonUtil.getValue(entry.getKey(), param));
        }
        accountService.updateAccountBasicInfo(userInfo);
        userInfoMapper.updateById(userInfo);
        userInfoService.updateUserOrgMap(userInfo);
        param = userInfo;
        HashMap<String, List<FileInfo>> hashMap = CommonUtil.multipartFileSave(param.getXgh(), param, fileFields, wrapper.getDeleteFileIds());
        if (!CollectionUtils.isEmpty(hashMap)) {
            UpdateWrapper<UserInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(UserInfo::getXgh, param.getXgh());
            for (Map.Entry<String, List<FileInfo>> entry : hashMap.entrySet()) {
                updateWrapper.set(entry.getKey(), JSON.toJSONString(entry.getValue()));
            }
            userInfoMapper.update(null, updateWrapper);
        }
    }

    public PageResult<UserInfo> queryPage(UserInfoParam param, CodeXsztParam param1) {
        param.setUserType(getUserType());
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = userInfoService.getQueryWrapper(param, param1);
        Page<UserInfo> page = wrapper.getPage();
        page = userInfoMapper.selectJoinPageWithPermission(page, UserInfo.class, wrapper, new MyMPQueryParams(getUserType()));
        List<UserInfo> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    public List<UserInfo> queryList(UserInfoParam param, CodeXsztParam param1) {
        param = Objects.isNull(param) ? new UserInfoParam() : param;
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param, param1);
        List<UserInfo> list = userInfoMapper.selectJoinListWithPermission(UserInfo.class, wrapper, new MyMPQueryParams(getUserType()));
        UserInfoUtil.codeTextSet(list);
        return list;
    }

    private MPJLambdaWrapper<UserInfo> userByRoleDutyQuery(UserInfoParam param, CodeXsztParam stateParam, String username, String roleId, String roleScope) {
        param = Objects.isNull(param) ? new UserInfoParam() : param;
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param, stateParam);
        List<Expression> expressions = new CustomDataPermissionHandlerImpl()
                .expressionList(wrapper.getAlias(), param.getUserType(), false,
                        null, username, roleId, roleScope);
        wrapper.and(!CollectionUtils.isEmpty(expressions), q -> {
            expressions.forEach(expression -> q.apply(expression.toString()));
        });
        return wrapper;
    }

    public PageResult<UserInfo> queryListByRoleDuty(UserInfoParam param, CodeXsztParam stateParam, String username) {
        if (!StringUtils.hasText(param.getRoleId()) || !StringUtils.hasText(username))
            AssertUtil.throwMessage(ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        RoleParam roleParam = new RoleParam();
        roleParam.setId(param.getRoleId());
        List<SysRole> list = roleService.list(roleParam);
        MPJLambdaWrapper<UserInfo> wrapper = userByRoleDutyQuery(param, stateParam, username, param.getRoleId(), list.get(0).getRoleScope());
        Page<UserInfo> page = new Page<>(param.getPage(), param.getLimit());
        IPage<UserInfo> iPage = userInfoMapper.selectJoinPage(page, UserInfo.class, wrapper);
        List<UserInfo> records = iPage.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, iPage.getTotal());
    }

    public Long getUserCountByRoleDuty(UserInfoParam param, CodeXsztParam stateParam, String username, String roleId, String roleScope) {
        MPJLambdaWrapper<UserInfo> wrapper = userByRoleDutyQuery(param, stateParam, username, roleId, roleScope);
        wrapper.getSelectColumns().clear();
        wrapper.getExpression().getOrderBy().clear();
        return userInfoMapper.selectJoinCount(wrapper);
    }

    public PageResult<DutyClassDetailVO> pageDutyClassDetail(UserInfoParam param, CodeXsztParam stateParam, String username, String roleId, String roleScope) {
        param = Objects.isNull(param) ? new UserInfoParam() : param;
        MPJLambdaWrapper<UserInfo> wrapper = userByRoleDutyQuery(param, stateParam, username, roleId, roleScope);
        wrapper.getSelectColumns().clear();
        wrapper.getExpression().getOrderBy().clear();
        wrapper.select(UserInfo::getPyccid, UserInfo::getNjid, UserInfo::getXyid, UserInfo::getZyid, UserInfo::getBjid)
                .select("COUNT(CASE WHEN XB = " + Gender.MALE.getValue() + " THEN 1 END) AS maleCount")
                .select("COUNT(CASE WHEN XB = " + Gender.FEMALE.getValue() + " THEN 1 END) AS femaleCount")
                .groupBy(UserInfo::getPyccid, UserInfo::getNjid, UserInfo::getXyid, UserInfo::getZyid, UserInfo::getBjid);
        Page<DutyClassDetailVO> page = new Page<>(param.getPage(), param.getLimit());
        page = userInfoMapper.selectJoinPage(page, DutyClassDetailVO.class, wrapper);
        List<DutyClassDetailVO> list = page.getRecords();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(vo -> {
                vo.setCount(vo.getMaleCount() + vo.getFemaleCount());
            });
            UserInfoUtil.codeTextSet(list);
        }
        return new PageResult<>(list, page.getTotal());
    }

    public List<UserInfo> queryList(UserInfoParam param, CodeXsztParam param1, SFunction<UserInfo, ?>... sFunction) {
        param = Objects.isNull(param) ? new UserInfoParam() : param;
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param, param1, sFunction);
        List<UserInfo> list = userInfoMapper.selectJoinListWithPermission(UserInfo.class, wrapper, new MyMPQueryParams(getUserType()));
        UserInfoUtil.codeTextSet(list);
        return list;
    }

    public UserInfo get(String xgh) {
        UserInfoParam param = new UserInfoParam();
        param.setXgh(xgh);
        List<UserInfo> list = queryList(param, null);
        return CommonUtil.listGetOne(list);
    }

    @Transactional
    public void remove(List<String> list) {
        for (String xgh : list) {
            List<SysAccount> accounts = sysAccountMapper.selectList(new LambdaQueryWrapper<SysAccount>().eq(SysAccount::getUsername, xgh));
            List<String> accountIds = accounts.stream().map(SysAccount::getId).collect(Collectors.toList());
            accountRoleMapper.delete(new LambdaQueryWrapper<SysAccountRole>().in(SysAccountRole::getAccountId, accountIds));
            sysAccountMapper.delete(new LambdaQueryWrapper<SysAccount>().in(SysAccount::getId, accountIds));
        }
        userInfoMapper.deleteBatchIds(list);
    }

    public List<DictionaryField> getAllImportFields() {
        List<DictionaryField> fieldList = new ArrayList<>();
        HashMap<String, String> defaultImportFields = getDefaultImportFields();
        Set<String> fieldSet = new HashSet<>();
        if (defaultImportFields != null) {
            fieldSet = defaultImportFields.keySet();
            List<DictionaryField> fields = dictionaryFieldMapper.selectList(new LambdaQueryWrapper<DictionaryField>().in(DictionaryField::getFieldEn, fieldSet).eq(DictionaryField::getUserType, getUserType()));
            Map<String, DictionaryField> fieldMap = fields.stream().collect((Collectors.toMap(DictionaryField::getFieldEn, Function.identity())));
            for (String key : fieldSet) {
                DictionaryField field = fieldMap.get(key);
                if (field == null) {
                    field = new DictionaryField();
                    field.setFieldEn(key);
                    field.setFieldZh(defaultImportFields.get(key));
                }
                field.setImportFieldFlag(Required.YES);
                fieldList.add(field);
            }
        }


        QueryWrapper<DictionaryField> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("import_field_flag");
        queryWrapper.eq("user_type", getUserType());
        queryWrapper.notIn("field_en", fieldSet);
        queryWrapper.eq("temp_field", JudgeMark.NO);
        queryWrapper.orderByAsc("sort");
        List<DictionaryField> list = dictionaryFieldMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) fieldList.addAll(list);
        return fieldList;
    }

    protected boolean checkImportFields() {
        return false;
    }

    private List<ExcelUtils.Head> getHeads(List<DictionaryField> importFields) {
        List<ExcelUtils.Head> heads = new ArrayList<>();
        for (DictionaryField field : importFields) {
            heads.add(new ExcelUtils.Head(field.getFieldZh(), field.getFieldEn(), field.getRequired() != null && field.getRequired().getMark()));
        }
        return heads;
    }

    public Object[] importData(MultipartFile file) {
        UserType userType = getUserType();
        if (checkImportFields()) {
            LinkedHashMap<String, String> defaultImportFields = getDefaultImportFields();
            QueryWrapper<DictionaryField> wrapper = new QueryWrapper<>();
            wrapper.eq("user_type", userType);
            wrapper.in("field_en", defaultImportFields.keySet());
            wrapper.orderByAsc("sort");
            List<DictionaryField> list = dictionaryFieldMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(list) || list.size() != defaultImportFields.size())
                AssertUtil.throwMessage("请先配置导入字段");
        }

        List<DictionaryField> importFields = getAllImportFields();
        SheetData sheetData = null;
        try {
            sheetData = EasyExcelHelper.read(file);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        List<RowData> rowData = sheetData.getRowData();
        List<DictionaryField> codeCommonLoadDataFields = new ArrayList<>();
        Map<String, List<CodeCommon>> codeCommonMap = new HashMap<>();
        HashMap<String, List<LinkedHashMap>> otherDataMap = new HashMap<>();
        setBaseData(importFields, codeCommonLoadDataFields, otherDataMap, codeCommonMap);

        LinkedHashMap<Integer, UserInfo> userInfoMap = new LinkedHashMap<>();
        List<ExcelImportError> errors = new ArrayList<>();
        for (RowData row : rowData) {
            Integer rowIndex = row.getIndex() + 1;
            HashMap<Integer, String> cellData = row.getCellData();
            UserInfo userInfo = new UserInfo();
            for (int cellIndex = 0; cellIndex < importFields.size(); cellIndex++) {
                DictionaryField dic = importFields.get(cellIndex);
                Field field = CommonUtil.getField(UserInfo.class, dic.getFieldEn());
                String value = cellData.get(cellIndex);
                if (field == null || isEmpty(dic, rowIndex, value, errors)) continue;
                value = value != null ? value.trim() : "";
                CheckResult result = importDataCheckAndGet(dic, rowIndex, value, errors, otherDataMap, codeCommonMap);
                if (result.getError()) continue;
                setCellValue(result.getValue(), userInfo, dic, field, rowIndex, errors);
            }
            userInfoMap.put(rowIndex, userInfo);
        }

        List<String> allAccountInfo = getAllAccountInfo();
        for (Integer rowIndex : userInfoMap.keySet()) {
            UserInfo userInfo = userInfoMap.get(rowIndex);
            if (allAccountInfo.contains(userInfo.getXgh())) {
                errors.add(ExcelImportError.data(rowIndex, userInfo.getXgh(), "学号/工号已存在或重复"));
            } else {
                allAccountInfo.add(userInfo.getXgh());
            }
            checkIsRepeat(userInfo.getZjhm(), allAccountInfo, errors, rowIndex, "证件号码已存在或重复");
            checkIsRepeat(userInfo.getSjh(), allAccountInfo, errors, rowIndex, "手机号码已存在或重复");
        }
        setRoleId(userInfoMap, null, errors);
        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }
        String passwordPattern = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
        return new Object[]{passwordPattern, userInfoMap, otherDataMap.get("bjid"), otherDataMap.get("xyid")};
    }

    @Transactional
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void importData(UserInfo userInfo, String passwordPattern, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos) {
        userInfo.setUserType(getUserType());
        userInfoService.createUserAndAccountInfo(userInfo, false, passwordPattern, classInfos, deptInfos);
    }

    private void setBaseData(List<DictionaryField> fields, List<DictionaryField> codeCommonLoadDataFields, HashMap<String, List<LinkedHashMap>> otherDataMap, Map<String, List<CodeCommon>> codeCommonMap) {
        HashSet<String> codeTypeSet = new HashSet<>();
        for (DictionaryField field : fields) {
            if (controlTypeMap.containsKey(field.getControlType())) {
                codeTypeSet.add(XingZhengQuHua);
            } else {
                if (StringUtils.hasText(field.getLoadDataType())) {
                    if (Constants.OTHER.equals(field.getLoadDataType())) {
                        if (!StringUtils.hasText(field.getLoadDataUrl())) continue;
                        JSONObject result = requestData(field.getLoadDataUrl());
                        List<LinkedHashMap> array = null;
                        if (result.getIntValue("code") == 0) array = result.getList("data", LinkedHashMap.class);
                        otherDataMap.put(field.getFieldEn(), array);
                    } else {
                        codeCommonLoadDataFields.add(field);
                        codeTypeSet.add(field.getLoadDataType());
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(codeCommonLoadDataFields)) {
            List<CodeCommon> codeCommons = codeCommonMapper.selectList(new LambdaQueryWrapper<CodeCommon>().in(CodeCommon::getCodeType, codeTypeSet));
            if (codeCommons != null)
                codeCommonMap.putAll(codeCommons.stream().collect(Collectors.groupingBy(CodeCommon::getCodeType)));
        }
    }

    @Data
    static class CheckResult {
        private Boolean error;
        private String value;

        public CheckResult(Boolean error) {
            this.error = error;
        }

        public CheckResult(Boolean error, String value) {
            this.error = error;
            this.value = value;
        }
    }

    // 特殊处理（省市区+详细地址,省市区选择,省市选择）
    protected static final String XingZhengQuHua = "xzqh";
    protected static final HashMap<String, Integer> controlTypeMap = new HashMap<>() {{
        put("regions", 3);
        put("citys", 3);
        put("provinceCity", 2);
    }};

    private CheckResult importDataCheckAndGet(DictionaryField dic, Integer rowIndex, String value, List<ExcelImportError> errors, HashMap<String, List<LinkedHashMap>> otherDataMap, Map<String, List<CodeCommon>> codeCommonMap) {
        if (controlTypeMap.containsKey(dic.getControlType())) {
            if (StringUtils.hasText(value)) {
                List<CodeCommon> codeCommons = codeCommonMap.get(XingZhengQuHua);
                if (CollectionUtils.isEmpty(codeCommons)) {
                    errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "数据源获取数据失败"));
                    return new CheckResult(true);
                }

                Integer size = controlTypeMap.get(dic.getControlType());
                String[] strings = value.replaceAll("，", ",").split(",");
                if (strings.length < size) {
                    errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "数据不完整"));
                    return new CheckResult(true);
                }

                List<String> list = codeCommons.stream().map(CodeCommon::getName).toList();
                for (int i = 0; i < size; i++) {
                    if (!list.contains(strings[i])) {
                        errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "“" + strings[i] + "”不存在"));
                        return new CheckResult(true);
                    }
                }
            }
            return new CheckResult(false, value);
        }

        if (StringUtils.hasText(dic.getLoadDataType())) {
            if (Constants.OTHER.equals(dic.getLoadDataType())) {
                List<LinkedHashMap> array = otherDataMap.get(dic.getFieldEn());
                if (array == null) {
                    errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "数据获取失败,请检查字段配置"));
                    return new CheckResult(true);
                }

                String[] values = CommonUtil.split(value);
                List<String> valueList = new ArrayList<>();
                boolean exists = true;
                for (String val : values) {
                    Optional<LinkedHashMap> matchObject = getMatchObject(array, dic.getTextField(), val);
                    if (!matchObject.isPresent()) {
                        exists = false;
                        break;
                    } else {
                        LinkedHashMap object = matchObject.get();
                        String tempValue = object.get(dic.getValueField()) != null ? String.valueOf(object.get(dic.getValueField())) : "";
                        valueList.add(tempValue);
                    }
                }

                if (!exists) {
                    errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "不存在"));
                    return new CheckResult(true);
                }
                value = CommonUtil.strJoin(valueList);
            } else {
                List<CodeCommon> temps = codeCommonMap.get(dic.getLoadDataType());
                Optional<CodeCommon> optional = getCodeCommon(temps, value);
                if (optional.isPresent()) {
                    String valueField = StringUtils.hasLength(dic.getValueField()) ? dic.getValueField() : "name";
                    Object fieldValue = CommonUtil.getValue(valueField, optional.get());
                    value = String.valueOf(fieldValue);
                } else {
                    errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "不存在"));
                    return new CheckResult(true);
                }
            }
        }
        return new CheckResult(false, value);
    }

    private void setCellValue(String value, UserInfo userInfo, DictionaryField dic, Field field, Integer rowIndex, List<ExcelImportError> errors) {
        if (field.getType() == Gender.class) {
            Gender gender = Gender.get(value);
            if (gender == null) {
                errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "错误"));
            } else {
                CommonUtil.setValue(userInfo, field, gender);
            }
        } else {
            CommonUtil.setValue(userInfo, field, value);
        }
    }


    private static void checkIsRepeat(String userInfo, List<String> allAccountInfo, List<ExcelImportError> errors, Integer rowIndex, String tipMessage) {
        if (StringUtils.hasText(userInfo)) {
            if (allAccountInfo.contains(userInfo)) {
                errors.add(ExcelImportError.data(rowIndex, userInfo, tipMessage));
            } else {
                allAccountInfo.add(userInfo);
            }
        }
    }

    private boolean isEmpty(DictionaryField dic, Integer rowIndex, String value, List<ExcelImportError> errors) {
        if (dic.getImportFieldFlag().getMark()) {
            if (!StringUtils.hasText(value)) {
                errors.add(ExcelImportError.data(rowIndex, value, dic.getFieldZh() + "不能为空"));
                return true;
            }
        }
        return false;
    }

    private List<String> getAllAccountInfo() {
        List<SysAccount> sysAccounts = sysAccountMapper.selectList(new LambdaQueryWrapper<SysAccount>()
                .select(SysAccount::getUsername, SysAccount::getIdCode, SysAccount::getTelMobile));
        List<String> strings = sysAccounts.stream().map(SysAccount::getUsername).collect(Collectors.toList());
        List<String> allIdCode = sysAccounts.stream().map(SysAccount::getIdCode).filter(StringUtils::hasLength).toList();
        List<String> allMobile = sysAccounts.stream().map(SysAccount::getTelMobile).filter(StringUtils::hasLength).toList();
        strings.addAll(allIdCode);
        strings.addAll(allMobile);
        return strings;
    }

    private static Optional<LinkedHashMap> getMatchObject(List<LinkedHashMap> array, String fieldName, String value) {
        return array.stream().filter(f -> f.get(fieldName).equals(value)).findAny();
    }

    private static Optional<CodeCommon> getCodeCommon(List<CodeCommon> temps, String value) {
        return temps.stream().filter(f -> f.getName().equals(value)).findAny();
    }

    private JSONObject requestData(String api) {
        HttpServletRequest request = ServletUtils.getRequest();
        String access_token = JwtUtil.getAccessToken(request);
        String domain = "http://127.0.0.1:" + env.getProperty("server.port") + "/api";

        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", "application/json");
        headers.add("Authorization", access_token);

        String url = domain + (api.startsWith("/") ? api : "/" + api);
        HttpEntity<String> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, JSONObject.class);
        return responseEntity.getBody();
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void uploadPhoto(String username, String oldPhoto, File file) {
        if (!file.exists()) return;
        InputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            List<FileInfo> fileInfos = gridFsService.save(username, file.getName(), file.getName(), Files.probeContentType(file.toPath()), fileInputStream);
            if (StringUtils.hasLength(oldPhoto)) gridFsService.removeByFileInfoStr(oldPhoto);
            userInfoMapper.update(null, new LambdaUpdateWrapper<UserInfo>().set(UserInfo::getPhoto, JSONArray.toJSONString(fileInfos)).eq(UserInfo::getXgh, username));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (fileInputStream != null) fileInputStream.close();
                if (file.exists()) file.delete();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void uploadPhoto(String username, String oldPhoto, MultipartFile file) {
        String fileInfos = gridFsService.save(username, file);
        if (StringUtils.hasLength(oldPhoto)) gridFsService.removeByFileInfoStr(oldPhoto);
        userInfoMapper.update(null, new LambdaUpdateWrapper<UserInfo>().set(UserInfo::getPhoto, fileInfos).eq(UserInfo::getXgh, username));
    }

    public List<Map<String, Object>> chartData(UserInfoParam param) {
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param, "1");
        Long count = userInfoMapper.selectJoinCount(wrapper);
        String alias = wrapper.getAlias();
        String groupByColumn = alias + ".xb";
        String columns = "decode(" + groupByColumn + ",1,'男',2,'女') " + " As name, COUNT(*) AS total,round(COUNT(*)/" + count + "*100,2) AS rate";
        if (StringUtils.hasLength(param.getChartDataField())) {
            String[] strings = CommonUtil.split(param.getChartDataField());
            StringBuilder builder = new StringBuilder();
            HashMap<String, String> convertField = UserInfoUtil.getConvertField(getUserType());
            for (String s : strings) {
                String f = UserInfoUtil.statFieldConvertMap.get(s.toLowerCase());
                if (StringUtils.hasLength(f)) {
                    f = convertField.get(f);
                } else {
                    f = convertField.get(s.toLowerCase());
                    f = StringUtils.hasLength(f) ? f : alias + "." + s;
                }
                builder.append(f).append(",");
            }
            groupByColumn = builder.deleteCharAt(builder.length() - 1).toString();
            columns = groupByColumn + "," + "COUNT(*) AS total," + "round(COUNT(*)/" + count + "*100,2) AS rate," + "sum(CASE WHEN " + alias + ".xb = 1 THEN 1 ELSE 0 END) AS male," + "sum(CASE WHEN " + alias + ".xb = 2 THEN 1 ELSE 0 END) AS female," + "round(sum(CASE WHEN " + alias + ".xb = 1 THEN 1 ELSE 0 END)/count(*)*100,2) AS male_rate," + "round(sum(CASE WHEN " + alias + ".xb = 2 THEN 1 ELSE 0 END)/count(*)*100,2) AS female_rate";
        }
        wrapper = userInfoService.getQueryWrapper(param, columns);
        wrapper.groupBy(groupByColumn).orderByDesc("total");
        return userInfoMapper.selectJoinMaps(wrapper);
    }

    public Object[] importUpdateDataCheckAndGet(MultipartFile file) {
        SheetData sheetData = null;
        try {
            sheetData = EasyExcelHelper.read(file);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        if (CollectionUtils.isEmpty(sheetData.getRowData()))
            AssertUtil.throwMessage("导入文件不能为空");
        HashMap<Integer, String> header = sheetData.getHeader();
        List<RowData> rowData = sheetData.getRowData();
        List<String> titleList = new ArrayList<>();
        Set<Integer> keySet = header.keySet();
        keySet.forEach(key -> {
            titleList.add(header.get(key));
        });

        List<String> listGroupIds = dictionaryGroupMapper.selectObjs(new LambdaQueryWrapper<DictionaryGroup>()
                .select(DictionaryGroup::getId).eq(DictionaryGroup::getUserType, getUserType())
                .eq(DictionaryGroup::getListFlag, JudgeMark.YES));
        List<DictionaryField> fields = dictionaryFieldMapper.selectList(new LambdaQueryWrapper<DictionaryField>()
                .eq(DictionaryField::getUserType, getUserType()).eq(DictionaryField::getTempField, JudgeMark.NO)
                .notIn(DictionaryField::getGroupId, listGroupIds)
                .in(DictionaryField::getFieldZh, titleList));
        if (CollectionUtils.isEmpty(fields) || fields.size() != titleList.size())
            AssertUtil.throwMessage("导入文件表头字段名称错误");
        Map<String, DictionaryField> titleMap = fields.stream().collect(Collectors.toMap(DictionaryField::getFieldZh, Function.identity()));
        DictionaryField xgh = dictionaryFieldMapper.selectOne(new LambdaQueryWrapper<DictionaryField>()
                .eq(DictionaryField::getUserType, getUserType())
                .eq(DictionaryField::getFieldEn, "xgh"));
        if (titleMap.get(xgh.getFieldZh()) == null)
            AssertUtil.throwMessage("导入文件表头缺失" + xgh.getFieldZh() + "字段");

        List<UserInfo> list = queryList(new UserInfoParam(), CodeXsztParam.currentStudent());
        Map<String, UserInfo> userInfoResultMap = list.stream().collect(Collectors.toMap(UserInfo::getXgh, Function.identity()));
        List<DictionaryField> codeCommonLoadDataFields = new ArrayList<>();
        Map<String, List<CodeCommon>> codeCommonMap = new HashMap<>();
        HashMap<String, List<LinkedHashMap>> otherDataMap = new HashMap<>();
        setBaseData(fields, codeCommonLoadDataFields, otherDataMap, codeCommonMap);
        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        List<UserInfo> userInfos = new CopyOnWriteArrayList<>();
        Map<String, SysAccount> accountMap = new ConcurrentHashMap<>();
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (RowData row : rowData) {
            HashMap<Integer, String> cellData = row.getCellData();
            Integer rowIndex = row.getIndex() + 1;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                UserInfo tempUserInfo = new UserInfo();
                for (Integer cellIndex : keySet) {
                    String title = header.get(cellIndex);
                    String value = cellData.get(cellIndex);
                    DictionaryField dic = titleMap.get(title);
                    Field field = CommonUtil.getField(tempUserInfo.getClass(), dic.getFieldEn());
                    if (field == null || isEmpty(dic, rowIndex, value, errors)) continue;
                    CheckResult checkResult = importDataCheckAndGet(dic, rowIndex, value, errors, otherDataMap, codeCommonMap);
                    if (checkResult.getError()) continue;
                    setCellValue(checkResult.getValue(), tempUserInfo, dic, field, rowIndex, errors);
                }

                UserInfo oldUserInfo = userInfoResultMap.get(tempUserInfo.getXgh());
                if (oldUserInfo == null) {
                    errors.add(ExcelImportError.data(rowIndex, tempUserInfo.getXgh(), xgh.getFieldZh() + "不存在"));
                    return;
                }

                try {
                    SysAccount account = accountService.checkAccountBasicInfoAndGet(tempUserInfo);
                    if (account == null)
                        account = accountService.getByUsername(tempUserInfo.getXgh());
                    accountMap.put(tempUserInfo.getXgh(), account);
                } catch (BusinessException e) {
                    errors.add(ExcelImportError.data(rowIndex, tempUserInfo.getXgh(), e.getMessage()));
                    return;
                }

                for (DictionaryField field : fields) {
                    Object value = CommonUtil.getValue(field.getFieldEn(), tempUserInfo);
                    CommonUtil.setValue(oldUserInfo, field.getFieldEn(), value);
                }
                userInfos.add(oldUserInfo);
            }, executor);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }
        return new Object[]{userInfos, accountMap, otherDataMap.get("bjid"), otherDataMap.get("xyid")};
    }


    @Transactional
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void importUpdate(UserInfo userInfo, SysAccount account, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos) {
        accountService.setAccountBasicInfo(userInfo, account);
        sysAccountMapper.updateById(account);
        userInfoMapper.updateById(userInfo);
        userInfoService.updateUserOrgMap(userInfo, classInfos, deptInfos);
    }
}
