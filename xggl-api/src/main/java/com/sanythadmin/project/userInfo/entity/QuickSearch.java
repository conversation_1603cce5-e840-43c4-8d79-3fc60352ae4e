package com.sanythadmin.project.userInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 自定义查询条件记录
 *
 * <AUTHOR>
 * @since 2024-05-23 10:05:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QUICK_SEARCH")
@Entity
@Table(name = "SYT_QUICK_SEARCH")
public class QuickSearch implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    @Column(name = "CONDITION", columnDefinition = ColumnType.CLOB)
    @TableField("CONDITION")
    private String condition;

    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

}
