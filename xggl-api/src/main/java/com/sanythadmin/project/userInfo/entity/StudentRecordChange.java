package com.sanythadmin.project.userInfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 学籍异动信息
 *
 * @since 2025/7/21.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_STUDENT_RECORD_CHANGE")
@Entity
@Table(name = "SYT_STUDENT_RECORD_CHANGE")
public class StudentRecordChange implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("XM")
    private String xm;

    /**
     * 性别（男：0， 女：1）
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 民族
     */
    @Column(name = "MZMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("MZMC")
    private String mzmc;

    /**
     * 异动类别
     */
    @Column(name = "ydlb", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ydlb")
    private String ydlb;
    /**
     * 异动时间
     */
    @Column(name = "ydsj", columnDefinition = ColumnType.DATE)
    @TableField("ydsj")
    private LocalDate ydsj;
    /**
     * 异动原因
     */
    @Column(name = "ydyy", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ydyy")
    private String ydyy;
    /**
     * 处理文号
     */
    @Column(name = "clwh", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("clwh")
    private String clwh;
    /**
     * 异动说明
     */
    @Column(name = "ydsm", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("ydsm")
    private String ydsm;
    /**
     * 异动前学院ID
     */
    @Column(name = "ydqxyid", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ydqxyid")
    private String ydqxyid;
    /**
     * 异动前专业ID
     */
    @Column(name = "ydqzyid", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ydqzyid")
    private String ydqzyid;
    /**
     * 异动前班级ID
     */
    @Column(name = "ydqbjid", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ydqbjid")
    private String ydqbjid;
    /**
     * 异动后学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;
    /**
     * 异动后专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;
    /**
     * 异动后班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;
    /**
     * 审批状态
     */
    @Column(name = "spzt", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("spzt")
    private String spzt;
    /**
     * 操作人
     */
    @Column(name = "czr", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("czr")
    private String czr;




    /**
     * 异动前学院名称
     */
    @Column(name = "ydqxymc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ydqxymc")
    private String ydqxymc;
    /**
     * 异动前专业名称
     */
    @Column(name = "ydqzymc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ydqzymc")
    private String ydqzymc;
    /**
     * 异动前班级名称
     */
    @Column(name = "ydqbjmc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ydqbjmc")
    private String ydqbjmc;

    /**
     * 异动后学院名称
     */
    @Column(name = "xymc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("xymc")
    private String xymc;
    /**
     * 异动后专业名称
     */
    @Column(name = "zymc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("zymc")
    private String zymc;
    /**
     * 异动后班级名称
     */
    @Column(name = "bjmc", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("bjmc")
    private String bjmc;
}
