package com.sanythadmin.project.userInfo.controller;

import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.userInfo.service.QuickSearchService;
import com.sanythadmin.project.userInfo.entity.QuickSearch;
import com.sanythadmin.project.userInfo.param.QuickSearchParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 自定义查询条件记录控制器
 *
 * <AUTHOR>
 * @since 2024-05-23 10:05:38
 */
@RestController
@RequestMapping("/api/userInfo/quick-search")
public class QuickSearchController extends BaseController {
    @Resource
    private QuickSearchService quickSearchService;

    /**
     * 分页查询自定义查询条件记录（权限标识：userInfo:quickSearch:list）
     */
    @PreAuthorize("hasAuthority('userInfo:quickSearch:list')")
    @GetMapping("/page")
    public PageResult<QuickSearch> page(QuickSearchParam param) {
        PageParam<QuickSearch, QuickSearchParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = quickSearchService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部自定义查询条件记录（权限标识：userInfo:quickSearch:list）
     */
    @PreAuthorize("hasAuthority('userInfo:quickSearch:list')")
    @GetMapping()
    public List<QuickSearch> list(QuickSearchParam param) {
        SysAccount account = SecurityUtil.getAccount();
        param.setUsername(account.getUsername());
        PageParam<QuickSearch, QuickSearchParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return quickSearchService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询自定义查询条件记录（权限标识：userInfo:quickSearch:list）
     */
    @PreAuthorize("hasAuthority('userInfo:quickSearch:list')")
    @GetMapping("/{id}")
    public QuickSearch get(@PathVariable("id") String id) {
        return quickSearchService.getById(id);
    }

    /**
     * 添加或修改自定义查询条件记录（权限标识：userInfo:quickSearch:operation）
     */
    @PreAuthorize("hasAuthority('userInfo:quickSearch:operation')")
    @OperationLog(module = "自定义查询条件记录", comments = "保存自定义查询条件记录")
    @PostMapping("/operation")
    public void save(@RequestBody QuickSearch quickSearch) {
        SysAccount account = SecurityUtil.getAccount();
        quickSearch.setUsername(account.getUsername());
        if (StringUtils.hasLength(quickSearch.getId())) {
            quickSearchService.updateById(quickSearch);
        } else {
            quickSearchService.save(quickSearch);
        }
    }

    /**
     * 批量删除自定义查询条件记录（权限标识：userInfo:quickSearch:remove）
     */
    @PreAuthorize("hasAuthority('userInfo:quickSearch:remove')")
    @OperationLog(module = "自定义查询条件记录", comments = "批量删除自定义查询条件记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        quickSearchService.removeByIds(ids);
    }
}
