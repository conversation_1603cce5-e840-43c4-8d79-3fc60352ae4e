package com.sanythadmin.project.resume.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.resume.entity.ResumeDataConfig;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.entity.UserResume;
import com.sanythadmin.project.resume.service.ResumeDataConfigService;
import com.sanythadmin.project.resume.service.ResumeDataService;
import com.sanythadmin.project.userInfo.entity.UserListInfo;
import com.sanythadmin.project.userInfo.service.UserListInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 简历数据服务实现
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeDataServiceImpl implements ResumeDataService {

    private final ResumeDataConfigService resumeDataConfigService;
    private final UserListInfoService userListInfoService;

    @Override
    public Map<String, Object> getDynamicData(String xgh) {
        Map<String, Object> dynamicData = new HashMap<>();
        
        // 获取所有启用的数据配置
        List<ResumeDataConfig> configs = resumeDataConfigService.list(
                new LambdaQueryWrapper<ResumeDataConfig>()
                        .eq(ResumeDataConfig::getIsActive, 1)
                        .orderByAsc(ResumeDataConfig::getSortOrder)
        );
        
        for (ResumeDataConfig config : configs) {
            try {
                Object moduleData = getModuleData(xgh, config.getConfigCode());
                if (moduleData != null) {
                    dynamicData.put(config.getConfigCode(), moduleData);
                }
            } catch (Exception e) {
                log.error("获取模块数据失败，配置代码：{}，用户：{}", config.getConfigCode(), xgh, e);
            }
        }
        
        return dynamicData;
    }

    @Override
    public Map<String, Object> renderResumeData(UserResume resume, ResumeTemplate template, UserInfo userInfo, Map<String, Object> dynamicData) {
        Map<String, Object> renderData = new HashMap<>();
        
        // 基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        if (userInfo != null) {
            basicInfo.put("name", userInfo.getXm());
            basicInfo.put("gender", userInfo.getXb());
            basicInfo.put("phone", userInfo.getSjh());
            basicInfo.put("birthDate", userInfo.getCsrq());
            basicInfo.put("nativePlace", userInfo.getJg());
            //TODO userinfo acoount增加邮箱字段
//            basicInfo.put("email", userInfo.getDzyx());
            basicInfo.put("photo", userInfo.getPhoto());
        }
        renderData.put("basicInfo", basicInfo);
        
        // 自定义数据
        if (StringUtils.hasLength(resume.getCustomData())) {
            try {
                JSONObject customData = JSON.parseObject(resume.getCustomData());
                renderData.put("customData", customData);
            } catch (Exception e) {
                log.error("解析自定义数据失败：{}", resume.getCustomData(), e);
            }
        }
        
        // 动态数据
        renderData.put("dynamicData", dynamicData);
        
        // 模板配置
        if (template != null && StringUtils.hasLength(template.getLayoutConfig())) {
            try {
                JSONObject layoutConfig = JSON.parseObject(template.getLayoutConfig());
                renderData.put("layoutConfig", layoutConfig);
            } catch (Exception e) {
                log.error("解析模板配置失败：{}", template.getLayoutConfig(), e);
            }
        }
        
        return renderData;
    }

    @Override
    public Object getModuleData(String xgh, String configCode) {
        ResumeDataConfig config = resumeDataConfigService.getOne(
                new LambdaQueryWrapper<ResumeDataConfig>()
                        .eq(ResumeDataConfig::getConfigCode, configCode)
                        .eq(ResumeDataConfig::getIsActive, 1)
        );
        
        if (config == null) {
            return null;
        }
        
        // 根据配置获取数据
        switch (config.getSourceTable()) {
            case "SYT_USER_LIST_INFO":
                return getUserListInfoData(xgh, config);
            // 可以扩展其他数据源
            default:
                log.warn("不支持的数据源表：{}", config.getSourceTable());
                return null;
        }
    }

    /**
     * 获取用户列表信息数据
     */
    private Object getUserListInfoData(String xgh, ResumeDataConfig config) {
        try {
            // 解析查询条件
            Map<String, Object> queryCondition = new HashMap<>();
            if (StringUtils.hasLength(config.getQueryCondition())) {
                queryCondition = JSON.parseObject(config.getQueryCondition(), Map.class);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<UserListInfo> wrapper = new LambdaQueryWrapper<UserListInfo>()
                    .eq(UserListInfo::getXgh, xgh);
            
            // 添加分组条件
            if (queryCondition.containsKey("groupName")) {
                wrapper.eq(UserListInfo::getGroupName, queryCondition.get("groupName"));
            }
            
            List<UserListInfo> userListInfos = userListInfoService.list(wrapper);
            
            // 根据字段映射转换数据
            if (StringUtils.hasLength(config.getFieldMapping())) {
                Map<String, String> fieldMapping = JSON.parseObject(config.getFieldMapping(), Map.class);
                return mapUserListInfoData(userListInfos, fieldMapping);
            }
            
            return userListInfos;
        } catch (Exception e) {
            log.error("获取用户列表信息数据失败，用户：{}，配置：{}", xgh, config.getConfigCode(), e);
            return null;
        }
    }

    /**
     * 映射用户列表信息数据
     */
    private Object mapUserListInfoData(List<UserListInfo> userListInfos, Map<String, String> fieldMapping) {
        // 根据字段映射规则转换数据结构
        // 这里可以根据具体需求实现数据转换逻辑
        return userListInfos;
    }
}
