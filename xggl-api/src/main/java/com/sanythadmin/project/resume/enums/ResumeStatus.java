package com.sanythadmin.project.resume.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 简历状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public enum ResumeStatus {
    
    /**
     * 草稿
     */
    DRAFT("DRAFT", "草稿"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已发布
     */
    PUBLISHED("PUBLISHED", "已发布"),
    
    /**
     * 已归档
     */
    ARCHIVED("ARCHIVED", "已归档");

    @EnumValue
    @JsonValue
    private final String code;
    
    private final String desc;

    ResumeStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
