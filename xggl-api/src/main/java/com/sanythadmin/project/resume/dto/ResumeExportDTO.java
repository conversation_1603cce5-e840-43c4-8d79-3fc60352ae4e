package com.sanythadmin.project.resume.dto;

import com.sanythadmin.project.resume.enums.ExportFormat;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 简历导出DTO
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
public class ResumeExportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 简历ID
     */
    @NotEmpty(message = "简历ID不能为空")
    private String resumeId;

    /**
     * 导出格式
     */
    private ExportFormat format = ExportFormat.PDF;

    /**
     * 模板ID（可选，覆盖默认模板）
     */
    private String templateId;

    /**
     * 文件名（不包含扩展名）
     */
    private String fileName;
}
