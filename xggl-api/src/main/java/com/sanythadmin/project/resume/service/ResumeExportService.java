package com.sanythadmin.project.resume.service;

import com.sanythadmin.project.resume.dto.ResumeExportDTO;
import com.sanythadmin.project.resume.vo.ResumeDetailVO;

/**
 * 简历导出服务
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface ResumeExportService {

    /**
     * 导出简历
     *
     * @param resumeDetail 简历详情
     * @param exportDTO 导出参数
     * @return 文件字节数组
     */
    byte[] exportResume(ResumeDetailVO resumeDetail, ResumeExportDTO exportDTO);

    /**
     * 导出为PDF
     *
     * @param resumeDetail 简历详情
     * @param fileName 文件名
     * @return PDF字节数组
     */
    byte[] exportToPdf(ResumeDetailVO resumeDetail, String fileName);

    /**
     * 导出为Word
     *
     * @param resumeDetail 简历详情
     * @param fileName 文件名
     * @return Word字节数组
     */
    byte[] exportToWord(ResumeDetailVO resumeDetail, String fileName);
}
