package com.sanythadmin.project.resume.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.resume.entity.ResumeTemplateType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 简历模板类型Mapper
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@Mapper
public interface ResumeTemplateTypeMapper extends BaseMapper<ResumeTemplateType> {

    /**
     * 根据类型代码查询模板类型
     *
     * @param typeCode 类型代码
     * @return 模板类型
     */
    ResumeTemplateType selectByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 检查类型代码是否存在
     *
     * @param typeCode 类型代码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 存在数量
     */
    int checkTypeCodeExists(@Param("typeCode") String typeCode, @Param("excludeId") String excludeId);
}
