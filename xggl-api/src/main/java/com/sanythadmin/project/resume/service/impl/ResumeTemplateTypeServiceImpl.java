package com.sanythadmin.project.resume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplateType;
import com.sanythadmin.project.resume.mapper.ResumeTemplateTypeMapper;
import com.sanythadmin.project.resume.param.ResumeTemplateTypeParam;
import com.sanythadmin.project.resume.service.ResumeTemplateTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简历模板类型Service实现
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@Service
@RequiredArgsConstructor
public class ResumeTemplateTypeServiceImpl extends ServiceImpl<ResumeTemplateTypeMapper, ResumeTemplateType> implements ResumeTemplateTypeService {

    @Override
    public PageResult<ResumeTemplateType> pageTemplateType(ResumeTemplateTypeParam param) {
        PageParam<ResumeTemplateType, ResumeTemplateTypeParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort_order asc, create_time desc");
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<ResumeTemplateType> listActiveTypes() {
        return list(new LambdaQueryWrapper<ResumeTemplateType>()
                .eq(ResumeTemplateType::getIsActive, 1)
                .orderByAsc(ResumeTemplateType::getSortOrder)
                .orderByDesc(ResumeTemplateType::getCreateTime));
    }

    @Override
    public ResumeTemplateType getByTypeCode(String typeCode) {
        if (!StringUtils.hasLength(typeCode)) {
            return null;
        }
        return baseMapper.selectByTypeCode(typeCode);
    }

    @Override
    public ResumeTemplateType saveTemplateType(ResumeTemplateType templateType) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();

        // 验证类型代码唯一性
        if (checkTypeCodeExists(templateType.getTypeCode(), templateType.getId())) {
            throw new BusinessException("类型代码已存在：" + templateType.getTypeCode());
        }

        if (StringUtils.hasLength(templateType.getId())) {
            // 更新
            templateType.setUpdateTime(now);
            templateType.setUpdateBy(currentUser);
            updateById(templateType);
        } else {
            // 新增
            templateType.setCreateTime(now);
            templateType.setUpdateTime(now);
            templateType.setCreateBy(currentUser);
            templateType.setUpdateBy(currentUser);
            
            // 设置默认值
            if (templateType.getIsActive() == null) {
                templateType.setIsActive(1);
            }
            if (templateType.getSortOrder() == null) {
                templateType.setSortOrder(0);
            }
            if (templateType.getIsSystem() == null) {
                templateType.setIsSystem(0);
            }
            
            save(templateType);
        }
        return templateType;
    }

    @Override
    public void removeTemplateTypes(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("删除的类型ID不能为空");
        }
        
        // 检查是否有系统内置类型
        List<ResumeTemplateType> types = listByIds(ids);
        for (ResumeTemplateType type : types) {
            if (type.getIsSystem() != null && type.getIsSystem() == 1) {
                throw new BusinessException("不能删除系统内置类型：" + type.getTypeName());
            }
        }
        
        // 检查是否有模板在使用这些类型
        // TODO: 添加关联检查逻辑
        
        removeByIds(ids);
    }

    @Override
    public void updateTypeStatus(String id, Integer isActive) {
        if (!StringUtils.hasLength(id)) {
            throw new BusinessException("类型ID不能为空");
        }
        
        ResumeTemplateType type = getById(id);
        if (type == null) {
            throw new BusinessException("模板类型不存在");
        }
        
        // 系统内置类型不能禁用
        if (type.getIsSystem() != null && type.getIsSystem() == 1 && isActive == 0) {
            throw new BusinessException("系统内置类型不能禁用");
        }
        
        update(new LambdaUpdateWrapper<ResumeTemplateType>()
                .eq(ResumeTemplateType::getId, id)
                .set(ResumeTemplateType::getIsActive, isActive)
                .set(ResumeTemplateType::getUpdateTime, LocalDateTime.now())
                .set(ResumeTemplateType::getUpdateBy, SecurityUtil.getUsername()));
    }

    @Override
    public boolean checkTypeCodeExists(String typeCode, String excludeId) {
        if (!StringUtils.hasLength(typeCode)) {
            return false;
        }
        return baseMapper.checkTypeCodeExists(typeCode, excludeId) > 0;
    }
}
