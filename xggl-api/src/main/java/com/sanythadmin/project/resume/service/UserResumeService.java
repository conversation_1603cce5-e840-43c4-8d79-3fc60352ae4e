package com.sanythadmin.project.resume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.dto.ResumeExportDTO;
import com.sanythadmin.project.resume.entity.UserResume;
import com.sanythadmin.project.resume.param.UserResumeParam;
import com.sanythadmin.project.resume.vo.ResumeDetailVO;

import java.util.List;

/**
 * 用户简历Service
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface UserResumeService extends IService<UserResume> {

    /**
     * 分页查询用户简历
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<UserResume> pageUserResume(UserResumeParam param);

    /**
     * 获取当前用户的简历列表
     *
     * @return 简历列表
     */
    List<UserResume> listMyResumes();

    /**
     * 保存用户简历
     *
     * @param userResume 用户简历
     * @return 保存后的简历
     */
    UserResume saveUserResume(UserResume userResume);

    /**
     * 删除用户简历
     *
     * @param ids 简历ID列表
     */
    void removeUserResumes(List<String> ids);

    /**
     * 获取简历详情（包含完整渲染数据）
     *
     * @param id 简历ID
     * @return 简历详情
     */
    ResumeDetailVO getResumeDetail(String id);

    /**
     * 生成简历分享链接
     *
     * @param id 简历ID
     * @param expireHours 过期小时数
     * @return 分享链接
     */
    String generateShareLink(String id, Integer expireHours);

    /**
     * 通过分享链接获取简历
     *
     * @param shareLink 分享链接
     * @return 简历详情
     */
    ResumeDetailVO getResumeByShareLink(String shareLink);

    /**
     * 设置默认简历
     *
     * @param id 简历ID
     */
    void setDefaultResume(String id);

    /**
     * 导出简历
     *
     * @param exportDTO 导出参数
     * @return 文件字节数组
     */
    byte[] exportResume(ResumeExportDTO exportDTO);
}
