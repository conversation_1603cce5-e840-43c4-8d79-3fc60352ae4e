package com.sanythadmin.project.resume.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 简历模板类型查询参数
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResumeTemplateTypeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 类型代码
     */
    @QueryField(type = QueryType.LIKE)
    private String typeCode;

    /**
     * 类型名称
     */
    @QueryField(type = QueryType.LIKE)
    private String typeName;

    /**
     * 是否启用
     */
    @QueryField(type = QueryType.EQ)
    private Integer isActive;

    /**
     * 是否系统内置
     */
    @QueryField(type = QueryType.EQ)
    private Integer isSystem;
}
