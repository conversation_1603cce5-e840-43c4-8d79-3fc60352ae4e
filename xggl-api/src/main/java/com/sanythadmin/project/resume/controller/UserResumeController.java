package com.sanythadmin.project.resume.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.dto.ResumeExportDTO;
import com.sanythadmin.project.resume.entity.UserResume;
import com.sanythadmin.project.resume.param.UserResumeParam;
import com.sanythadmin.project.resume.service.UserResumeService;
import com.sanythadmin.project.resume.vo.ResumeDetailVO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 用户简历控制器
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/api/resume")
@RequiredArgsConstructor
public class UserResumeController extends BaseController {

    private final UserResumeService userResumeService;

    /**
     * 分页查询用户简历（权限标识：resume:userResume:list）
     */
    @PreAuthorize("hasAuthority('resume:userResume:list')")
    @GetMapping("/page")
    public PageResult<UserResume> page(UserResumeParam param) {
        return userResumeService.pageUserResume(param);
    }

    /**
     * 获取当前用户的简历列表
     */
    @GetMapping("/my-resumes")
    public List<UserResume> listMyResumes() {
        return userResumeService.listMyResumes();
    }

    /**
     * 根据id查询简历详情
     */
    @GetMapping("/{id}")
    public ResumeDetailVO get(@PathVariable("id") String id) {
        return userResumeService.getResumeDetail(id);
    }

    /**
     * 添加或修改用户简历
     */
    @OperationLog(module = "用户简历", comments = "保存用户简历")
    @PostMapping
    public UserResume save(@Valid @RequestBody UserResume userResume) {
        return userResumeService.saveUserResume(userResume);
    }

    /**
     * 批量删除用户简历
     */
    @OperationLog(module = "用户简历", comments = "批量删除用户简历")
    @DeleteMapping
    public void remove(@RequestBody List<String> ids) {
        userResumeService.removeUserResumes(ids);
    }

    /**
     * 简历预览
     */
    @GetMapping("/{id}/preview")
    public ResumeDetailVO preview(@PathVariable("id") String id) {
        return userResumeService.getResumeDetail(id);
    }

    /**
     * 生成简历分享链接
     */
    @OperationLog(module = "用户简历", comments = "生成分享链接")
    @PostMapping("/{id}/share")
    public String generateShareLink(@PathVariable("id") String id, 
                                   @RequestParam(value = "expireHours", defaultValue = "24") Integer expireHours) {
        return userResumeService.generateShareLink(id, expireHours);
    }

    /**
     * 通过分享链接查看简历
     */
    @GetMapping("/share/{shareLink}")
    public ResumeDetailVO getByShareLink(@PathVariable("shareLink") String shareLink) {
        return userResumeService.getResumeByShareLink(shareLink);
    }

    /**
     * 设置默认简历
     */
    @OperationLog(module = "用户简历", comments = "设置默认简历")
    @PostMapping("/{id}/set-default")
    public void setDefault(@PathVariable("id") String id) {
        userResumeService.setDefaultResume(id);
    }

    /**
     * 导出简历
     */
    @OperationLog(module = "用户简历", comments = "导出简历")
    @PostMapping("/{id}/export")
    public void export(@PathVariable("id") String id, 
                      @Valid @RequestBody ResumeExportDTO exportDTO,
                      HttpServletResponse response) throws IOException {
        exportDTO.setResumeId(id);
        byte[] fileBytes = userResumeService.exportResume(exportDTO);
        
        // 设置响应头
        String fileName = (exportDTO.getFileName() != null ? exportDTO.getFileName() : "简历") 
                         + exportDTO.getFormat().getExtension();
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, 
                          "attachment; filename=\"" + encodedFileName + "\"");
        response.setContentLength(fileBytes.length);
        
        // 写入响应
        response.getOutputStream().write(fileBytes);
        response.getOutputStream().flush();
    }
}
