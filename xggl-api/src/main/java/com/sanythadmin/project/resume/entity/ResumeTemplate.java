package com.sanythadmin.project.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历模板
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_RESUME_TEMPLATE")
@Entity
@Table(name = "SYT_RESUME_TEMPLATE")
public class ResumeTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 模板名称
     */
    @NotEmpty(message = "模板名称不能为空")
    @Column(name = "TEMPLATE_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("TEMPLATE_NAME")
    private String templateName;

    /**
     * 模板类型ID
     */
    @Column(name = "TEMPLATE_TYPE_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("TEMPLATE_TYPE_ID")
    private String templateTypeId;

    /**
     * 布局配置（JSON格式）
     */
    @Column(name = "LAYOUT_CONFIG", columnDefinition = ColumnType.CLOB)
    @TableField("LAYOUT_CONFIG")
    private String layoutConfig;

    /**
     * 模板描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 是否启用（1：启用，0：禁用）
     */
    @Column(name = "IS_ACTIVE", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("IS_ACTIVE")
    private Integer isActive;

    /**
     * 排序号
     */
    @Column(name = "SORT_ORDER", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("UPDATE_BY")
    private String updateBy;
}
