package com.sanythadmin.project.resume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.mapper.ResumeTemplateMapper;
import com.sanythadmin.project.resume.param.ResumeTemplateParam;
import com.sanythadmin.project.resume.service.ResumeTemplateService;
import com.sanythadmin.project.resume.service.ResumeTemplateTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 简历模板Service实现
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
public class ResumeTemplateServiceImpl extends ServiceImpl<ResumeTemplateMapper, ResumeTemplate> implements ResumeTemplateService {

    private final ResumeTemplateTypeService templateTypeService;

    @Override
    public PageResult<ResumeTemplate> pageResumeTemplate(ResumeTemplateParam param) {
        // 处理类型代码查询的兼容性
        if (StringUtils.hasLength(param.getTemplateTypeCode()) && !StringUtils.hasLength(param.getTemplateTypeId())) {
            var templateType = templateTypeService.getByTypeCode(param.getTemplateTypeCode());
            if (templateType != null) {
                param.setTemplateTypeId(templateType.getId());
            }
        }

        PageParam<ResumeTemplate, ResumeTemplateParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort_order asc, create_time desc");
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<ResumeTemplate> listActiveTemplates() {
        return list(new LambdaQueryWrapper<ResumeTemplate>()
                .eq(ResumeTemplate::getIsActive, 1)
                .orderByAsc(ResumeTemplate::getSortOrder)
                .orderByDesc(ResumeTemplate::getCreateTime));
    }

    @Override
    public ResumeTemplate saveResumeTemplate(ResumeTemplate resumeTemplate) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();

        if (StringUtils.hasLength(resumeTemplate.getId())) {
            // 更新
            resumeTemplate.setUpdateTime(now);
            resumeTemplate.setUpdateBy(currentUser);
            updateById(resumeTemplate);
        } else {
            // 新增
            resumeTemplate.setCreateTime(now);
            resumeTemplate.setUpdateTime(now);
            resumeTemplate.setCreateBy(currentUser);
            resumeTemplate.setUpdateBy(currentUser);
            
            // 设置默认值
            if (resumeTemplate.getIsActive() == null) {
                resumeTemplate.setIsActive(1);
            }
            if (resumeTemplate.getSortOrder() == null) {
                resumeTemplate.setSortOrder(0);
            }
            
            save(resumeTemplate);
        }
        return resumeTemplate;
    }

    @Override
    public void removeResumeTemplates(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("删除的模板ID不能为空");
        }
        
        // 检查是否有简历在使用这些模板
        // TODO: 添加关联检查逻辑
        
        removeByIds(ids);
    }

    @Override
    public void updateTemplateStatus(String id, Integer isActive) {
        if (!StringUtils.hasLength(id)) {
            throw new BusinessException("模板ID不能为空");
        }
        
        ResumeTemplate template = getById(id);
        if (template == null) {
            throw new BusinessException("模板不存在");
        }
        
        update(new LambdaUpdateWrapper<ResumeTemplate>()
                .eq(ResumeTemplate::getId, id)
                .set(ResumeTemplate::getIsActive, isActive)
                .set(ResumeTemplate::getUpdateTime, LocalDateTime.now())
                .set(ResumeTemplate::getUpdateBy, SecurityUtil.getUsername()));
    }
}
