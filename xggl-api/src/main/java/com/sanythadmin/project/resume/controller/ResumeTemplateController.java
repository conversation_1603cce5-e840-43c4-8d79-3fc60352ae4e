package com.sanythadmin.project.resume.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.param.ResumeTemplateParam;
import com.sanythadmin.project.resume.service.ResumeTemplateService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 简历模板控制器
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@RestController
@RequestMapping("/api/resume/templates")
@RequiredArgsConstructor
public class ResumeTemplateController extends BaseController {

    private final ResumeTemplateService resumeTemplateService;

    /**
     * 分页查询简历模板（权限标识：resume:template:list）
     */
    @PreAuthorize("hasAuthority('resume:template:list')")
    @GetMapping("/page")
    public PageResult<ResumeTemplate> page(ResumeTemplateParam param) {
        return resumeTemplateService.pageResumeTemplate(param);
    }

    /**
     * 查询所有启用的简历模板（权限标识：resume:template:list）
     */
    @PreAuthorize("hasAuthority('resume:template:list')")
    @GetMapping("/active")
    public List<ResumeTemplate> listActive() {
        return resumeTemplateService.listActiveTemplates();
    }

    /**
     * 根据id查询简历模板（权限标识：resume:template:list）
     */
    @PreAuthorize("hasAuthority('resume:template:list')")
    @GetMapping("/{id}")
    public ResumeTemplate get(@PathVariable("id") String id) {
        return resumeTemplateService.getById(id);
    }

    /**
     * 添加或修改简历模板（权限标识：resume:template:operation）
     */
    @PreAuthorize("hasAuthority('resume:template:operation')")
    @OperationLog(module = "简历模板", comments = "保存简历模板")
    @PostMapping("/operation")
    public ResumeTemplate save(@Valid @RequestBody ResumeTemplate resumeTemplate) {
        return resumeTemplateService.saveResumeTemplate(resumeTemplate);
    }

    /**
     * 批量删除简历模板（权限标识：resume:template:remove）
     */
    @PreAuthorize("hasAuthority('resume:template:remove')")
    @OperationLog(module = "简历模板", comments = "批量删除简历模板")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        resumeTemplateService.removeResumeTemplates(ids);
    }

    /**
     * 启用/禁用模板（权限标识：resume:template:operation）
     */
    @PreAuthorize("hasAuthority('resume:template:operation')")
    @OperationLog(module = "简历模板", comments = "更新模板状态")
    @PostMapping("/{id}/status")
    public void updateStatus(@PathVariable("id") String id, @RequestParam("isActive") Integer isActive) {
        resumeTemplateService.updateTemplateStatus(id, isActive);
    }
}
