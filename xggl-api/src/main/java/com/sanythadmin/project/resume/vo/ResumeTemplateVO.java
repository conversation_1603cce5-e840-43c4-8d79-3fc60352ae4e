package com.sanythadmin.project.resume.vo;

import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.entity.ResumeTemplateType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 简历模板VO（包含类型信息）
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResumeTemplateVO extends ResumeTemplate {
    private static final long serialVersionUID = 1L;

    /**
     * 模板类型信息
     */
    private ResumeTemplateType templateType;

    /**
     * 类型代码（兼容性字段）
     */
    private String typeCode;

    /**
     * 类型名称（兼容性字段）
     */
    private String typeName;
}
