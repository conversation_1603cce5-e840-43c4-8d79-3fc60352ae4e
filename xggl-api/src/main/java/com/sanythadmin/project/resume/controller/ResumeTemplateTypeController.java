package com.sanythadmin.project.resume.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplateType;
import com.sanythadmin.project.resume.param.ResumeTemplateTypeParam;
import com.sanythadmin.project.resume.service.ResumeTemplateTypeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 简历模板类型控制器
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@RestController
@RequestMapping("/api/resume/template-types")
@RequiredArgsConstructor
public class ResumeTemplateTypeController extends BaseController {

    private final ResumeTemplateTypeService templateTypeService;

    /**
     * 分页查询模板类型（权限标识：resume:templateType:list）
     */
    @PreAuthorize("hasAuthority('resume:templateType:list')")
    @GetMapping("/page")
    public PageResult<ResumeTemplateType> page(ResumeTemplateTypeParam param) {
        return templateTypeService.pageTemplateType(param);
    }

    /**
     * 查询所有启用的模板类型（权限标识：resume:templateType:list）
     */
    @PreAuthorize("hasAuthority('resume:templateType:list')")
    @GetMapping("/active")
    public List<ResumeTemplateType> listActive() {
        return templateTypeService.listActiveTypes();
    }

    /**
     * 查询所有启用的模板类型（公开接口，用于简历创建）
     */
    @GetMapping("/public/active")
    public List<ResumeTemplateType> listActivePublic() {
        return templateTypeService.listActiveTypes();
    }

    /**
     * 根据id查询模板类型（权限标识：resume:templateType:list）
     */
    @PreAuthorize("hasAuthority('resume:templateType:list')")
    @GetMapping("/{id}")
    public ResumeTemplateType get(@PathVariable("id") String id) {
        return templateTypeService.getById(id);
    }

    /**
     * 根据类型代码查询模板类型（权限标识：resume:templateType:list）
     */
    @PreAuthorize("hasAuthority('resume:templateType:list')")
    @GetMapping("/code/{typeCode}")
    public ResumeTemplateType getByCode(@PathVariable("typeCode") String typeCode) {
        return templateTypeService.getByTypeCode(typeCode);
    }

    /**
     * 添加或修改模板类型（权限标识：resume:templateType:operation）
     */
    @PreAuthorize("hasAuthority('resume:templateType:operation')")
    @OperationLog(module = "简历模板类型", comments = "保存模板类型")
    @PostMapping("/operation")
    public ResumeTemplateType save(@Valid @RequestBody ResumeTemplateType templateType) {
        return templateTypeService.saveTemplateType(templateType);
    }

    /**
     * 批量删除模板类型（权限标识：resume:templateType:remove）
     */
    @PreAuthorize("hasAuthority('resume:templateType:remove')")
    @OperationLog(module = "简历模板类型", comments = "批量删除模板类型")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        templateTypeService.removeTemplateTypes(ids);
    }

    /**
     * 启用/禁用类型（权限标识：resume:templateType:operation）
     */
    @PreAuthorize("hasAuthority('resume:templateType:operation')")
    @OperationLog(module = "简历模板类型", comments = "更新类型状态")
    @PostMapping("/{id}/status")
    public void updateStatus(@PathVariable("id") String id, @RequestParam("isActive") Integer isActive) {
        templateTypeService.updateTypeStatus(id, isActive);
    }

    /**
     * 检查类型代码是否存在（权限标识：resume:templateType:list）
     */
    @PreAuthorize("hasAuthority('resume:templateType:list')")
    @GetMapping("/check-code")
    public boolean checkTypeCode(@RequestParam("typeCode") String typeCode, 
                                @RequestParam(value = "excludeId", required = false) String excludeId) {
        return templateTypeService.checkTypeCodeExists(typeCode, excludeId);
    }
}
