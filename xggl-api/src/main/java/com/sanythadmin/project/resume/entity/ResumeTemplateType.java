package com.sanythadmin.project.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历模板类型
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_RESUME_TEMPLATE_TYPE")
@Entity
@Table(name = "SYT_RESUME_TEMPLATE_TYPE")
public class ResumeTemplateType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 类型代码（唯一标识）
     */
    @NotEmpty(message = "类型代码不能为空")
    @Column(name = "TYPE_CODE", columnDefinition = ColumnType.VARCHAR2_50, unique = true)
    @TableField("TYPE_CODE")
    private String typeCode;

    /**
     * 类型名称
     */
    @NotEmpty(message = "类型名称不能为空")
    @Column(name = "TYPE_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("TYPE_NAME")
    private String typeName;

    /**
     * 类型描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 图标（可选）
     */
    @Column(name = "ICON", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("ICON")
    private String icon;

    /**
     * 是否启用（1：启用，0：禁用）
     */
    @Column(name = "IS_ACTIVE", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("IS_ACTIVE")
    private Integer isActive;

    /**
     * 排序号
     */
    @Column(name = "SORT_ORDER", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 是否系统内置（1：是，0：否）
     */
    @Column(name = "IS_SYSTEM", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField("IS_SYSTEM")
    private Integer isSystem;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("UPDATE_BY")
    private String updateBy;
}
