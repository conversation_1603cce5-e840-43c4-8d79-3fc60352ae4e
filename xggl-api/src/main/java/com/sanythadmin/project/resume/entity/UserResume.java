package com.sanythadmin.project.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.resume.enums.ResumeStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户简历
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_USER_RESUME")
@Entity
@Table(name = "SYT_USER_RESUME")
public class UserResume implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户学工号
     */
    @NotEmpty(message = "用户学工号不能为空")
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XGH")
    private String xgh;

    /**
     * 模板ID
     */
    @NotEmpty(message = "模板ID不能为空")
    @Column(name = "TEMPLATE_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("TEMPLATE_ID")
    private String templateId;

    /**
     * 简历标题
     */
    @NotEmpty(message = "简历标题不能为空")
    @Column(name = "RESUME_TITLE", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("RESUME_TITLE")
    private String resumeTitle;

    /**
     * 自定义数据（JSON格式）
     */
    @Column(name = "CUSTOM_DATA", columnDefinition = ColumnType.CLOB)
    @TableField("CUSTOM_DATA")
    private String customData;

    /**
     * 简历状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("STATUS")
    private ResumeStatus status;

    /**
     * 版本号
     */
    @Column(name = "VERSION", columnDefinition = ColumnType.NUMBER_10)
    @TableField("VERSION")
    private Integer version;

    /**
     * 是否默认简历（1：是，0：否）
     */
    @Column(name = "IS_DEFAULT", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField("IS_DEFAULT")
    private Integer isDefault;

    /**
     * 分享链接（用于简历分享）
     */
    @Column(name = "SHARE_LINK", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("SHARE_LINK")
    private String shareLink;

    /**
     * 分享过期时间
     */
    @Column(name = "SHARE_EXPIRE_TIME")
    @TableField("SHARE_EXPIRE_TIME")
    private LocalDateTime shareExpireTime;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("UPDATE_BY")
    private String updateBy;
}
