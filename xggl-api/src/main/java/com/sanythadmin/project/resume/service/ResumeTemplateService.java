package com.sanythadmin.project.resume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.param.ResumeTemplateParam;

import java.util.List;

/**
 * 简历模板Service
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface ResumeTemplateService extends IService<ResumeTemplate> {

    /**
     * 分页查询简历模板
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<ResumeTemplate> pageResumeTemplate(ResumeTemplateParam param);

    /**
     * 查询所有启用的简历模板
     *
     * @return 模板列表
     */
    List<ResumeTemplate> listActiveTemplates();

    /**
     * 保存简历模板
     *
     * @param resumeTemplate 简历模板
     * @return 保存后的模板
     */
    ResumeTemplate saveResumeTemplate(ResumeTemplate resumeTemplate);

    /**
     * 删除简历模板
     *
     * @param ids 模板ID列表
     */
    void removeResumeTemplates(List<String> ids);

    /**
     * 启用/禁用模板
     *
     * @param id 模板ID
     * @param isActive 是否启用
     */
    void updateTemplateStatus(String id, Integer isActive);
}
