package com.sanythadmin.project.resume.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 简历数据配置
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_RESUME_DATA_CONFIG")
@Entity
@Table(name = "SYT_RESUME_DATA_CONFIG")
public class ResumeDataConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 配置名称
     */
    @NotEmpty(message = "配置名称不能为空")
    @Column(name = "CONFIG_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("CONFIG_NAME")
    private String configName;

    /**
     * 配置代码
     */
    @NotEmpty(message = "配置代码不能为空")
    @Column(name = "CONFIG_CODE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CONFIG_CODE")
    private String configCode;

    /**
     * 数据源表名
     */
    @NotEmpty(message = "数据源表名不能为空")
    @Column(name = "SOURCE_TABLE", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("SOURCE_TABLE")
    private String sourceTable;

    /**
     * 字段映射配置（JSON格式）
     */
    @Column(name = "FIELD_MAPPING", columnDefinition = ColumnType.CLOB)
    @TableField("FIELD_MAPPING")
    private String fieldMapping;

    /**
     * 查询条件（JSON格式）
     */
    @Column(name = "QUERY_CONDITION", columnDefinition = ColumnType.CLOB)
    @TableField("QUERY_CONDITION")
    private String queryCondition;

    /**
     * 是否启用（1：启用，0：禁用）
     */
    @Column(name = "IS_ACTIVE", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("IS_ACTIVE")
    private Integer isActive;

    /**
     * 排序号
     */
    @Column(name = "SORT_ORDER", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("UPDATE_BY")
    private String updateBy;
}
