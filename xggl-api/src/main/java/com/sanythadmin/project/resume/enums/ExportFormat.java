package com.sanythadmin.project.resume.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 导出格式枚举
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public enum ExportFormat {
    
    /**
     * PDF格式
     */
    PDF("PDF", "PDF格式", ".pdf"),
    
    /**
     * Word格式
     */
    WORD("WORD", "Word格式", ".docx");

    @EnumValue
    @JsonValue
    private final String code;
    
    private final String desc;
    
    private final String extension;

    ExportFormat(String code, String desc, String extension) {
        this.code = code;
        this.desc = desc;
        this.extension = extension;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getExtension() {
        return extension;
    }
}
