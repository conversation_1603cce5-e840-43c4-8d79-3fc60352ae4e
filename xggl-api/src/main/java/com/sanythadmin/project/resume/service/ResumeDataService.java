package com.sanythadmin.project.resume.service;

import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.entity.UserResume;

import java.util.Map;

/**
 * 简历数据服务
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface ResumeDataService {

    /**
     * 获取用户的动态数据
     *
     * @param xgh 用户学工号
     * @return 动态数据
     */
    Map<String, Object> getDynamicData(String xgh);

    /**
     * 渲染简历数据
     *
     * @param resume 简历信息
     * @param template 模板信息
     * @param userInfo 用户信息
     * @param dynamicData 动态数据
     * @return 渲染后的完整数据
     */
    Map<String, Object> renderResumeData(UserResume resume, ResumeTemplate template, UserInfo userInfo, Map<String, Object> dynamicData);

    /**
     * 根据配置获取指定模块的数据
     *
     * @param xgh 用户学工号
     * @param configCode 配置代码
     * @return 模块数据
     */
    Object getModuleData(String xgh, String configCode);
}
