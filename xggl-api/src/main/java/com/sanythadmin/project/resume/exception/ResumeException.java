package com.sanythadmin.project.resume.exception;

import com.sanythadmin.common.core.exception.BusinessException;

/**
 * 简历业务异常
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public class ResumeException extends BusinessException {

    public ResumeException() {
        super();
    }

    public ResumeException(String message) {
        super(message);
    }

    public ResumeException(String message, String data) {
        super(message, data);
    }

    public ResumeException(Integer code, String message) {
        super(code, message);
    }

    public ResumeException(Integer code, String message, String data) {
        super(code, message, data);
    }

    public ResumeException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
