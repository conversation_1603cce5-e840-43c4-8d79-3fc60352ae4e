package com.sanythadmin.project.resume.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.resume.entity.ResumeTemplateType;
import com.sanythadmin.project.resume.param.ResumeTemplateTypeParam;

import java.util.List;

/**
 * 简历模板类型Service
 *
 * <AUTHOR>
 * @since 2025-01-22 10:30:00
 */
public interface ResumeTemplateTypeService extends IService<ResumeTemplateType> {

    /**
     * 分页查询模板类型
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<ResumeTemplateType> pageTemplateType(ResumeTemplateTypeParam param);

    /**
     * 查询所有启用的模板类型
     *
     * @return 模板类型列表
     */
    List<ResumeTemplateType> listActiveTypes();

    /**
     * 根据类型代码查询模板类型
     *
     * @param typeCode 类型代码
     * @return 模板类型
     */
    ResumeTemplateType getByTypeCode(String typeCode);

    /**
     * 保存模板类型
     *
     * @param templateType 模板类型
     * @return 保存后的类型
     */
    ResumeTemplateType saveTemplateType(ResumeTemplateType templateType);

    /**
     * 删除模板类型
     *
     * @param ids 类型ID列表
     */
    void removeTemplateTypes(List<String> ids);

    /**
     * 启用/禁用类型
     *
     * @param id 类型ID
     * @param isActive 是否启用
     */
    void updateTypeStatus(String id, Integer isActive);

    /**
     * 检查类型代码是否存在
     *
     * @param typeCode 类型代码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkTypeCodeExists(String typeCode, String excludeId);
}
