package com.sanythadmin.project.resume.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.resume.dto.ResumeExportDTO;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.entity.UserResume;
import com.sanythadmin.project.resume.enums.ResumeStatus;
import com.sanythadmin.project.resume.mapper.UserResumeMapper;
import com.sanythadmin.project.resume.param.UserResumeParam;
import com.sanythadmin.project.resume.service.ResumeDataService;
import com.sanythadmin.project.resume.service.ResumeExportService;
import com.sanythadmin.project.resume.service.ResumeTemplateService;
import com.sanythadmin.project.resume.service.UserResumeService;
import com.sanythadmin.project.resume.vo.ResumeDetailVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户简历Service实现
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
public class UserResumeServiceImpl extends ServiceImpl<UserResumeMapper, UserResume> implements UserResumeService {

    private final ResumeTemplateService resumeTemplateService;
    private final UserInfoService userInfoService;
    private final ResumeDataService resumeDataService;
    private final ResumeExportService resumeExportService;

    @Override
    public PageResult<UserResume> pageUserResume(UserResumeParam param) {
        PageParam<UserResume, UserResumeParam> page = new PageParam<>(param);
        page.setDefaultOrder("is_default desc, update_time desc");
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<UserResume> listMyResumes() {
        String currentUser = SecurityUtil.getUsername();
        return list(new LambdaQueryWrapper<UserResume>()
                .eq(UserResume::getXgh, currentUser)
                .orderByDesc(UserResume::getIsDefault)
                .orderByDesc(UserResume::getUpdateTime));
    }

    @Override
    public UserResume saveUserResume(UserResume userResume) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();

        if (StringUtils.hasLength(userResume.getId())) {
            // 更新
            userResume.setUpdateTime(now);
            userResume.setUpdateBy(currentUser);
            
            // 版本号递增
            UserResume existing = getById(userResume.getId());
            if (existing != null) {
                userResume.setVersion((existing.getVersion() == null ? 0 : existing.getVersion()) + 1);
            }
            
            updateById(userResume);
        } else {
            // 新增
            userResume.setXgh(currentUser);
            userResume.setCreateTime(now);
            userResume.setUpdateTime(now);
            userResume.setCreateBy(currentUser);
            userResume.setUpdateBy(currentUser);
            
            // 设置默认值
            if (userResume.getStatus() == null) {
                userResume.setStatus(ResumeStatus.DRAFT);
            }
            if (userResume.getVersion() == null) {
                userResume.setVersion(1);
            }
            if (userResume.getIsDefault() == null) {
                userResume.setIsDefault(0);
            }
            
            save(userResume);
        }
        
        // 如果设置为默认简历，需要取消其他简历的默认状态
        if (userResume.getIsDefault() != null && userResume.getIsDefault() == 1) {
            update(new LambdaUpdateWrapper<UserResume>()
                    .eq(UserResume::getXgh, currentUser)
                    .ne(UserResume::getId, userResume.getId())
                    .set(UserResume::getIsDefault, 0));
        }
        
        return userResume;
    }

    @Override
    public void removeUserResumes(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("删除的简历ID不能为空");
        }
        
        String currentUser = SecurityUtil.getUsername();
        
        // 检查权限：只能删除自己的简历
        List<UserResume> resumes = listByIds(ids);
        for (UserResume resume : resumes) {
            if (!currentUser.equals(resume.getXgh())) {
                throw new BusinessException("无权限删除他人简历");
            }
        }
        
        removeByIds(ids);
    }

    @Override
    public ResumeDetailVO getResumeDetail(String id) {
        UserResume resume = getById(id);
        if (resume == null) {
            throw new BusinessException("简历不存在");
        }
        
        // 检查权限
        String currentUser = SecurityUtil.getUsername();
        if (!currentUser.equals(resume.getXgh())) {
            throw new BusinessException("无权限查看他人简历");
        }
        
        return buildResumeDetail(resume);
    }

    @Override
    public String generateShareLink(String id, Integer expireHours) {
        UserResume resume = getById(id);
        if (resume == null) {
            throw new BusinessException("简历不存在");
        }
        
        // 检查权限
        String currentUser = SecurityUtil.getUsername();
        if (!currentUser.equals(resume.getXgh())) {
            throw new BusinessException("无权限分享他人简历");
        }
        
        // 生成分享链接
        String shareLink = CommonUtil.getUUID();
        LocalDateTime expireTime = LocalDateTime.now().plusHours(expireHours == null ? 24 : expireHours);
        
        update(new LambdaUpdateWrapper<UserResume>()
                .eq(UserResume::getId, id)
                .set(UserResume::getShareLink, shareLink)
                .set(UserResume::getShareExpireTime, expireTime));
        
        return shareLink;
    }

    @Override
    public ResumeDetailVO getResumeByShareLink(String shareLink) {
        UserResume resume = getOne(new LambdaQueryWrapper<UserResume>()
                .eq(UserResume::getShareLink, shareLink));
        
        if (resume == null) {
            throw new BusinessException("分享链接不存在");
        }
        
        // 检查是否过期
        if (resume.getShareExpireTime() != null && resume.getShareExpireTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("分享链接已过期");
        }
        
        return buildResumeDetail(resume);
    }

    @Override
    public void setDefaultResume(String id) {
        UserResume resume = getById(id);
        if (resume == null) {
            throw new BusinessException("简历不存在");
        }
        
        String currentUser = SecurityUtil.getUsername();
        if (!currentUser.equals(resume.getXgh())) {
            throw new BusinessException("无权限操作他人简历");
        }
        
        // 取消其他简历的默认状态
        update(new LambdaUpdateWrapper<UserResume>()
                .eq(UserResume::getXgh, currentUser)
                .set(UserResume::getIsDefault, 0));
        
        // 设置当前简历为默认
        update(new LambdaUpdateWrapper<UserResume>()
                .eq(UserResume::getId, id)
                .set(UserResume::getIsDefault, 1));
    }

    @Override
    public byte[] exportResume(ResumeExportDTO exportDTO) {
        ResumeDetailVO resumeDetail = getResumeDetail(exportDTO.getResumeId());
        return resumeExportService.exportResume(resumeDetail, exportDTO);
    }

    /**
     * 构建简历详情
     */
    private ResumeDetailVO buildResumeDetail(UserResume resume) {
        ResumeDetailVO detailVO = new ResumeDetailVO();
        detailVO.setResume(resume);
        
        // 获取模板信息
        ResumeTemplate template = resumeTemplateService.getById(resume.getTemplateId());
        detailVO.setTemplate(template);
        
        // 获取用户信息
        UserInfo userInfo = userInfoService.getById(resume.getXgh());
        detailVO.setUserInfo(userInfo);
        
        // 获取动态数据
        Map<String, Object> dynamicData = resumeDataService.getDynamicData(resume.getXgh());
        detailVO.setDynamicData(dynamicData);
        
        // 渲染完整数据
        Map<String, Object> renderData = resumeDataService.renderResumeData(resume, template, userInfo, dynamicData);
        detailVO.setRenderData(renderData);
        
        return detailVO;
    }
}
