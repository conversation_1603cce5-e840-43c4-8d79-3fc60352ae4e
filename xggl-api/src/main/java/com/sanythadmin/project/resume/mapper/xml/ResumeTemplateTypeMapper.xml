<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.resume.mapper.ResumeTemplateTypeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, ICON, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY
    </sql>

    <!-- 根据类型代码查询模板类型 -->
    <select id="selectByTypeCode" resultType="com.sanythadmin.project.resume.entity.ResumeTemplateType">
        SELECT <include refid="Base_Column_List"/>
        FROM SYT_RESUME_TEMPLATE_TYPE
        WHERE TYPE_CODE = #{typeCode}
        AND IS_ACTIVE = 1
    </select>

    <!-- 检查类型代码是否存在 -->
    <select id="checkTypeCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM SYT_RESUME_TEMPLATE_TYPE
        WHERE TYPE_CODE = #{typeCode}
        <if test="excludeId != null and excludeId != ''">
            AND ID != #{excludeId}
        </if>
    </select>

</mapper>
