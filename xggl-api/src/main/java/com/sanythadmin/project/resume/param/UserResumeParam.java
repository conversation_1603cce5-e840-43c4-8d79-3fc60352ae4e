package com.sanythadmin.project.resume.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.resume.enums.ResumeStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户简历查询参数
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserResumeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 用户学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 简历标题
     */
    @QueryField(type = QueryType.LIKE)
    private String resumeTitle;

    /**
     * 模板ID
     */
    @QueryField(type = QueryType.EQ)
    private String templateId;

    /**
     * 简历状态
     */
    @QueryField(type = QueryType.EQ)
    private ResumeStatus status;

    /**
     * 是否默认简历
     */
    @QueryField(type = QueryType.EQ)
    private Integer isDefault;
}
