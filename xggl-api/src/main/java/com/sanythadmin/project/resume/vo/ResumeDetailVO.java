package com.sanythadmin.project.resume.vo;

import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.resume.entity.ResumeTemplate;
import com.sanythadmin.project.resume.entity.UserResume;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 简历详情VO
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
public class ResumeDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 简历基本信息
     */
    private UserResume resume;

    /**
     * 模板信息
     */
    private ResumeTemplate template;

    /**
     * 用户基本信息
     */
    private UserInfo userInfo;

    /**
     * 动态数据（从各个业务模块获取）
     */
    private Map<String, Object> dynamicData;

    /**
     * 渲染后的完整数据
     */
    private Map<String, Object> renderData;
}
