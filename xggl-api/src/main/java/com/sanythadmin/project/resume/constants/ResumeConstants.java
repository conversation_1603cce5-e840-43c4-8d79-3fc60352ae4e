package com.sanythadmin.project.resume.constants;

/**
 * 简历模块常量
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public class ResumeConstants {

    /**
     * 默认分享链接过期时间（小时）
     */
    public static final int DEFAULT_SHARE_EXPIRE_HOURS = 24;

    /**
     * 最大分享链接过期时间（小时）
     */
    public static final int MAX_SHARE_EXPIRE_HOURS = 168; // 7天

    /**
     * 简历数据配置代码
     */
    public static class DataConfigCode {
        /**
         * 教育经历
         */
        public static final String EDUCATION = "EDUCATION";
        
        /**
         * 工作经历
         */
        public static final String WORK_EXPERIENCE = "WORK_EXPERIENCE";
        
        /**
         * 项目经历
         */
        public static final String PROJECT_EXPERIENCE = "PROJECT_EXPERIENCE";
        
        /**
         * 技能证书
         */
        public static final String SKILLS_CERTIFICATES = "SKILLS_CERTIFICATES";
        
        /**
         * 获奖情况
         */
        public static final String AWARDS = "AWARDS";
        
        /**
         * 社会实践
         */
        public static final String SOCIAL_PRACTICE = "SOCIAL_PRACTICE";
    }

    /**
     * 模板默认配置
     */
    public static class TemplateConfig {
        /**
         * 默认模板布局
         */
        public static final String DEFAULT_LAYOUT = """
            {
                "sections": [
                    {"type": "basicInfo", "title": "基本信息", "order": 1},
                    {"type": "education", "title": "教育经历", "order": 2},
                    {"type": "workExperience", "title": "工作经历", "order": 3},
                    {"type": "projectExperience", "title": "项目经历", "order": 4},
                    {"type": "skills", "title": "技能证书", "order": 5},
                    {"type": "awards", "title": "获奖情况", "order": 6}
                ],
                "style": {
                    "fontSize": "12px",
                    "fontFamily": "SimSun",
                    "lineHeight": "1.5",
                    "margin": "20px"
                }
            }
            """;
    }

    /**
     * 文件相关常量
     */
    public static class FileConstants {
        /**
         * 最大文件大小（字节）
         */
        public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
        
        /**
         * 支持的图片格式
         */
        public static final String[] SUPPORTED_IMAGE_FORMATS = {"jpg", "jpeg", "png", "gif"};
    }
}
