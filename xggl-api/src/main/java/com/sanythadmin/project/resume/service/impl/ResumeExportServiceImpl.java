package com.sanythadmin.project.resume.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.project.resume.dto.ResumeExportDTO;
import com.sanythadmin.project.resume.enums.ExportFormat;
import com.sanythadmin.project.resume.service.ResumeExportService;
import com.sanythadmin.project.resume.vo.ResumeDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 简历导出服务实现
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
public class ResumeExportServiceImpl implements ResumeExportService {

    @Override
    public byte[] exportResume(ResumeDetailVO resumeDetail, ResumeExportDTO exportDTO) {
        String fileName = generateFileName(resumeDetail, exportDTO);
        
        switch (exportDTO.getFormat()) {
            case PDF:
                return exportToPdf(resumeDetail, fileName);
            case WORD:
                return exportToWord(resumeDetail, fileName);
            default:
                throw new BusinessException("不支持的导出格式：" + exportDTO.getFormat());
        }
    }

    @Override
    public byte[] exportToPdf(ResumeDetailVO resumeDetail, String fileName) {
        try {
            // TODO: 实现PDF导出逻辑
            // 使用 iText 或其他PDF库生成PDF文件
            log.info("导出PDF简历：{}", fileName);
            
            // 临时返回空字节数组，实际实现时需要生成真实的PDF内容
            return new byte[0];
        } catch (Exception e) {
            log.error("导出PDF失败：{}", fileName, e);
            throw new BusinessException("PDF导出失败：" + e.getMessage());
        }
    }

    @Override
    public byte[] exportToWord(ResumeDetailVO resumeDetail, String fileName) {
        try {
            // TODO: 实现Word导出逻辑
            // 使用 Apache POI 生成Word文档
            log.info("导出Word简历：{}", fileName);
            
            // 临时返回空字节数组，实际实现时需要生成真实的Word内容
            return new byte[0];
        } catch (Exception e) {
            log.error("导出Word失败：{}", fileName, e);
            throw new BusinessException("Word导出失败：" + e.getMessage());
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(ResumeDetailVO resumeDetail, ResumeExportDTO exportDTO) {
        if (exportDTO.getFileName() != null) {
            return exportDTO.getFileName();
        }
        
        String userName = resumeDetail.getUserInfo() != null ? resumeDetail.getUserInfo().getXm() : "简历";
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        return userName + "_简历_" + dateStr;
    }
}
