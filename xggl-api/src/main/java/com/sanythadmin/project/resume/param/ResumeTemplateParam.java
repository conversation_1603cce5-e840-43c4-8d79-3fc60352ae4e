package com.sanythadmin.project.resume.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 简历模板查询参数
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResumeTemplateParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 模板名称
     */
    @QueryField(type = QueryType.LIKE)
    private String templateName;

    /**
     * 模板类型ID
     */
    @QueryField(type = QueryType.EQ)
    private String templateTypeId;

    /**
     * 模板类型代码（兼容性字段）
     */
    @QueryField(type = QueryType.EQ, ignore = true)
    private String templateTypeCode;

    /**
     * 是否启用
     */
    @QueryField(type = QueryType.EQ)
    private Integer isActive;
}
