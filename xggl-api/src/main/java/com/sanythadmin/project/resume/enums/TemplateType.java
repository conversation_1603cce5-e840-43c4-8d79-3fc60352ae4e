package com.sanythadmin.project.resume.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

/**
 * 简历模板类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public enum TemplateType {
    
    /**
     * 技术类
     */
    TECHNICAL("TECHNICAL", "技术类"),
    
    /**
     * 管理类
     */
    MANAGEMENT("MANAGEMENT", "管理类"),
    
    /**
     * 销售类
     */
    SALES("SALES", "销售类"),
    
    /**
     * 设计类
     */
    DESIGN("DESIGN", "设计类"),
    
    /**
     * 通用类
     */
    GENERAL("GENERAL", "通用类");

    @EnumValue
    @JsonValue
    private final String code;
    
    private final String desc;

    TemplateType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
