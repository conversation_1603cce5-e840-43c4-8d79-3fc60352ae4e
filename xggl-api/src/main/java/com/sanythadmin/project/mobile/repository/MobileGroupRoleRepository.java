package com.sanythadmin.project.mobile.repository;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.entity.MobileGroupRole;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

/**
 * @since 2025/3/27 15:53
 */
public interface MobileGroupRoleRepository  extends CrudRepository<MobileGroupRole, String> {

    @Query("SELECT gr FROM MobileGroupRole gr WHERE gr.group.id = :groupId AND gr.role = :role")
    MobileGroupRole findFirstByGroupIdAndRole(@Param("groupId") String groupId, @Param("role") SysRole role);

    @Query("SELECT gr FROM MobileGroupRole gr WHERE gr.group = :group AND gr.role = :role")
    MobileGroupRole findFirstByGroupAndRole(@Param("group") MobileGroup group, @Param("role") SysRole role);

    /**
     * 清除不再有应用绑定的分组角色关联记录
     */
    void deleteByAppRolesIsEmpty();
}
