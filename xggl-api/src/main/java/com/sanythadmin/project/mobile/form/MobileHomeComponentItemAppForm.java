package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileApp;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @since 2025/3/26 16:59
 */
@Data
@NoArgsConstructor
public class MobileHomeComponentItemAppForm {

    public MobileHomeComponentItemAppForm(MobileApp app) {
        this.id = app.getId();
        this.name = app.getName();
        this.path = app.getPath();
        this.icon = app.getIcon();
        this.iconColor = app.getIconColor();
    }

    private String id;
    private String name;
    private String path;
    private String icon;
    private String iconColor;
}
