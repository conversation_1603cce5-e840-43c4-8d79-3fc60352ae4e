package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import com.sanythadmin.project.mobile.vo.MobileOption;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/3/20 11:02
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileHomeComponentFormDto extends MobileBaseForm {

    public MobileHomeComponentFormDto(MobileHomeComponent entity) {
        super(entity);
        this.name = entity.getName();
//        if (entity.getType() != null) {
//            this.type = new MobileOption(entity.getType().toString(), entity.getType().getText());
//        }
        this.functionType = entity.getFunctionType();
        this.displayType = entity.getDisplayType();
        this.description = entity.getDescription();
        this.enabled = entity.isEnabled();
        this.sortOrder = entity.getSortOrder();
    }

    /**
     * 组件名称
     */
    @NotNull
    private String name;
//    /**
//     * 组件类型
//     */
//    private MobileOption type;
    /**
     * 组件功能描述或标识（仅适用于非特殊组件，如“待办”、“新闻”、“通知公告”等）
     */
    private String functionType;
    /**
     * 展示类型（仅适用于非特殊组件，如“列表”、“表格”、“统计图”等）
     */
    private String displayType;
    private String description;
    private boolean enabled;
    private int sortOrder;
    @NotNull
    private MobileOption role;
    private List<MobileHomeComponentItemFormDto> items = new ArrayList<>();
}
