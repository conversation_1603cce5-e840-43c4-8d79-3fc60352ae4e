package com.sanythadmin.project.mobile.service.impl;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.*;
import com.sanythadmin.project.mobile.form.MobileHomeComponentFormDto;
import com.sanythadmin.project.mobile.form.MobileHomeComponentItemFormDto;
import com.sanythadmin.project.mobile.repository.*;
import com.sanythadmin.project.mobile.service.MobileHomeComponentService;
import com.sanythadmin.project.mobile.util.MobileUtil;
import com.sanythadmin.project.mobile.vo.MobileGroupVo;
import com.sanythadmin.project.mobile.vo.MobileOption;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @since 2025/3/21 10:20
 */
@Service
public class MobileHomeComponentServiceImpl implements MobileHomeComponentService {

    private final MobileHomeComponentRepository mobileHomeComponentRepository;
    private final MobileSysRoleRepository mobileSysRoleRepository;
    private final MobileAppRepository appRepository;
    private final MobileHomeComponentItemRepository itemRepository;
    private final MobileAppRoleRepository appRoleRepository;
    private final MobileGroupRoleRepository groupRoleRepository;

    public MobileHomeComponentServiceImpl(MobileHomeComponentRepository mobileHomeComponentRepository, MobileSysRoleRepository mobileSysRoleRepository, MobileAppRepository appRepository, MobileHomeComponentItemRepository itemRepository, MobileAppRoleRepository appRoleRepository, MobileGroupRoleRepository groupRoleRepository) {
        this.mobileHomeComponentRepository = mobileHomeComponentRepository;
        this.mobileSysRoleRepository = mobileSysRoleRepository;
        this.appRepository = appRepository;
        this.itemRepository = itemRepository;
        this.appRoleRepository = appRoleRepository;
        this.groupRoleRepository = groupRoleRepository;
    }

    @Transactional
    @Override
    public void operation(MobileHomeComponentFormDto form) {
        MobileHomeComponent entity;
        LocalDateTime now = LocalDateTime.now();
        if (form.getId() != null) {
            entity = mobileHomeComponentRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(now);
        } else {
            entity = new MobileHomeComponent();
            entity.setCreatedAt(now);
        }

        entity.setName(form.getName());
//        if (form.getType() != null) {
//            entity.setType(MobileHomeComponentType.valueOf(form.getType().getId()));
//        } else {
//            entity.setType(MobileHomeComponentType.NORMAL);
//        }
        entity.setFunctionType(form.getFunctionType());
        entity.setDisplayType(form.getDisplayType());
        entity.setDescription(form.getDescription());
        entity.setEnabled(form.isEnabled());
        entity.setSortOrder(form.getSortOrder());

        // 处理表单中的角色
        MobileUtil.checkTrue(form.getRole() != null, "角色不能为空");
        SysRole role = mobileSysRoleRepository.findById(form.getRole().getId()).orElseThrow();
        entity.setRole(role);

        // 历史组件子项
        Map<String, MobileHomeComponentItem> oldItemMap = new HashMap<>();
        if (entity.getId() != null) {
            List<MobileHomeComponentItem> items = entity.getItems();
            for (MobileHomeComponentItem item : items) {
                oldItemMap.put(item.getId(), item);
            }
        }
        // 表单中组件子项
        List<MobileHomeComponentItemFormDto> itemFormDtoList = form.getItems();
        // 预存储的组件子项
        List<MobileHomeComponentItem> items = new ArrayList<>();
        for (MobileHomeComponentItemFormDto itemFormDto : itemFormDtoList) {
            MobileHomeComponentItem itemEntity;
            String itemFormDtoId = itemFormDto.getId();
            if (itemFormDtoId != null) {
                itemEntity = oldItemMap.remove(itemFormDtoId);
            } else {
                itemEntity = new MobileHomeComponentItem();
                itemEntity.setCreatedAt(now);
                itemEntity.setComponent(entity);
            }
            items.add(itemEntity);

            itemEntity.setTitle(itemFormDto.getTitle());
            itemEntity.setDescription(itemFormDto.getDescription());
            itemEntity.setIcon(itemFormDto.getIcon());
            itemEntity.setIconColor(itemFormDto.getIconColor());
            itemEntity.setEnabled(itemFormDto.isEnabled());
            itemEntity.setSortOrder(itemFormDto.getSortOrder());
            itemEntity.setUrl(itemFormDto.getUrl());

            // 组件子项的关联应用角色
            if (itemFormDto.getApp() != null) {
                MobileApp app = appRepository.findById(itemFormDto.getApp().getId()).orElseThrow();
                MobileAppRole appRole = appRoleRepository.findFirstByAppAndRole(app, role);
                MobileUtil.checkTrue(appRole != null, "关联应用不匹配此组件的角色");
                itemEntity.setAppRole(appRole);
            } else {
                itemEntity.setAppRole(null);
            }

            // 组件子项的关联应用分组角色
            if (itemFormDto.getGroup() != null) {
                MobileGroupRole groupRole = groupRoleRepository.findFirstByGroupIdAndRole(itemFormDto.getGroup().getId(), role);
                MobileUtil.checkTrue(groupRole != null, "关联分组不匹配此组件的角色");
                itemEntity.setGroupRole(groupRole);
            } else {
                itemEntity.setGroupRole(null);
            }

            // 组件子项的文件处理
            if (itemFormDto.isDeleteImage()) {
                itemEntity.setBin(null);
                itemEntity.setSha1(null);
                itemEntity.setFilename(null);
                itemEntity.setMime(null);
                itemEntity.setLength(null);
            } else {
                // 文件保存
                if (itemFormDto.getUploadImage() != null) {
                    MultipartFile document = itemFormDto.getUploadImage();

                    itemEntity.setFilename(document.getOriginalFilename());

                    try {
                        itemEntity.setBin(document.getBytes());
                    } catch (IOException e) {
                        MobileUtil.throwException(e.getMessage());
                    }
                    itemEntity.setMime(document.getContentType());
                    itemEntity.setLength(document.getSize());
                    itemEntity.setSha1(DigestUtils.sha1Hex(itemEntity.getBin()));
                }
            }
        }

        mobileHomeComponentRepository.save(entity);

        // 删除历史组件子项
        itemRepository.deleteAll(oldItemMap.values());
        // 保存组件子项
        itemRepository.saveAll(items);
    }

    @Transactional(readOnly = true)
    @Override
    public MobileHomeComponentFormDto getFormById(String id) {
        Optional<MobileHomeComponent> optionalEntity = mobileHomeComponentRepository.findById(id);
        MobileHomeComponent entity = optionalEntity.orElseThrow();
        MobileHomeComponentFormDto formDto = new MobileHomeComponentFormDto(entity);
        if (entity.getRole() != null) {
            formDto.setRole(new MobileOption(entity.getRole().getId(), entity.getRole().getName()));
        }
        for (MobileHomeComponentItem item : entity.getItems()) {
            MobileHomeComponentItemFormDto itemFormDto = new MobileHomeComponentItemFormDto(item);
            itemFormDto.setComponentId(item.getComponent().getId());
            if (item.getSha1() != null) {
                itemFormDto.setImageDataUrl(MobileUtil.imageDataUrl(item.getMime(), item.getBin()));
            }
            formDto.getItems().add(itemFormDto);
        }
        return formDto;
    }

    @Override
    public List<MobileGroupVo> optionalAppTree(String roleId) {
        List<MobileApp> apps = appRepository.findByRoleId(roleId);
        return MobileUtil.appListToTree(apps);
    }
}
