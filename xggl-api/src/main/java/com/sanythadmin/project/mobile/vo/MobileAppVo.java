package com.sanythadmin.project.mobile.vo;

import com.sanythadmin.project.mobile.entity.MobileApp;
import lombok.Data;

/**
 * @since 2025/3/26 15:04
 */
@Data
public class MobileAppVo {

    public MobileAppVo(MobileApp app) {
        this.id = app.getId();
        this.name = app.getName();
        this.path = app.getPath();
        this.icon = app.getIcon();
        this.iconColor = app.getIconColor();
        this.sortOrder = app.getSortOrder();
        this.component = app.getComponentPath();
    }

    private String id;
    private String name;
    private String path;
    private String icon;
    private String iconColor;
    private int sortOrder;
    private String component;
}
