package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/3/18 11:52
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileGroupForm extends MobileBaseForm {

    public MobileGroupForm(MobileGroup mobileGroup) {
        super(mobileGroup);
        this.name = mobileGroup.getName();
        this.description = mobileGroup.getDescription();
        this.icon = mobileGroup.getIcon();
        this.iconColor = mobileGroup.getIconColor();
        this.enabled = mobileGroup.isEnabled();
        this.sortOrder = mobileGroup.getSortOrder();
    }

    private String name;
    private String description;
    private String icon;
    private String iconColor;
    private boolean enabled;
    private Integer sortOrder;
}
