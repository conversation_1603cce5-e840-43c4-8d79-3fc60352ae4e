package com.sanythadmin.project.mobile.entity;

import com.sanythadmin.common.system.entity.SysRole;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 移动端-分组角色关联表
 *
 * @since 2025/3/27 15:13
 */
@Data
@Entity
@Table(name = "syt_mobile_group_role")
public class MobileGroupRole extends MobileBaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GROUP_ID")
    private MobileGroup group;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ROLE_ID")
    private SysRole role;

    @OneToMany(mappedBy = "groupRole", cascade = CascadeType.REMOVE)
    private List<MobileAppRole> appRoles;

    @OneToMany(mappedBy = "groupRole", cascade = CascadeType.REMOVE)
    private List<MobileHomeComponentItem> items;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass())
            return false;

        MobileGroupRole that = (MobileGroupRole) o;
        return Objects.equals(group, that.group) &&
                Objects.equals(role.getId(), that.role.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(group, role.getId());
    }
}
