package com.sanythadmin.project.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 移动端-应用
 *
 * @since 2025/3/17 10:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "SYT_MOBILE_APP")
public class MobileApp extends MobileBaseEntity {

    @ManyToOne
    @JoinColumn(name = "GROUP_ID")
    private MobileGroup group;

    @OneToMany(mappedBy = "app", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<MobileAppRole> roles = new ArrayList<>();

    /**
     * 名称
     */
    @Column(name = "NAME", nullable = false)
    @TableField("NAME")
    private String name;

    /**
     * 路由路径
     */
    @Column(name = "PATH")
    @TableField("PATH")
    private String path;

    /**
     * 说明
     */
    @Column(name = "DESCRIPTION")
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 图标标识
     */
    @Column(name = "ICON")
    @TableField("ICON")
    private String icon;

    /**
     * 图标颜色
     */
    @Column(name = "ICON_COLOR")
    private String iconColor;

    /**
     * 是否启用此路由
     */
    @Column(name = "IS_ENABLED", columnDefinition = "NUMBER(1) DEFAULT 0", nullable = false)
    @TableField("IS_ENABLED")
    private boolean isEnabled;

    /**
     * 排序号
     */
    @Column(name = "SORT_ORDER")
    @TableField("SORT_ORDER")
    private Integer sortOrder;

    /**
     * 组件路径
     */
    private String componentPath;
}
