package com.sanythadmin.project.mobile.repository;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileAppRole;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

/**
 * @since 2025/3/21 15:24
 */
public interface MobileAppRoleRepository extends CrudRepository<MobileAppRole, String> {

    @Query("SELECT mar FROM MobileAppRole mar WHERE mar.app = :app AND mar.groupRole.role = :role")
    MobileAppRole findFirstByAppAndRole(MobileApp app, SysRole role);
}
