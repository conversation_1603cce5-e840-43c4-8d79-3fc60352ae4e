package com.sanythadmin.project.mobile.repository;

import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import com.sanythadmin.project.mobile.entity.MobileHomeComponentItem;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface MobileHomeComponentItemRepository extends PagingAndSortingRepository<MobileHomeComponentItem, String>, CrudRepository<MobileHomeComponentItem, String>, JpaSpecificationExecutor<MobileHomeComponentItem> {

    List<MobileHomeComponentItem> findByComponent(MobileHomeComponent component);

    List<MobileHomeComponentItem> findByEnabledTrueAndComponentOrderBySortOrderAsc(MobileHomeComponent component);
}
