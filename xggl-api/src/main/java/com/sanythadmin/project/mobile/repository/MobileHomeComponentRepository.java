package com.sanythadmin.project.mobile.repository;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MobileHomeComponentRepository extends PagingAndSortingRepository<MobileHomeComponent, String>, CrudRepository<MobileHomeComponent, String>, JpaSpecificationExecutor<MobileHomeComponent> {

    @Query("SELECT c FROM MobileHomeComponent c " +
            "WHERE c.role = :role AND c.enabled = true " +
            "ORDER BY c.sortOrder ASC")
    List<MobileHomeComponent> findEnabledComponentsByRole(@Param("role") SysRole role);
}
