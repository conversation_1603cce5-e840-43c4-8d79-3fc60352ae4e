package com.sanythadmin.project.mobile.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import com.sanythadmin.project.mobile.enums.MobileHomeComponentType;
import com.sanythadmin.project.mobile.form.MobileHomeComponentFormDto;
import com.sanythadmin.project.mobile.query.MobileHomeComponentQuery;
import com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository;
import com.sanythadmin.project.mobile.repository.MobileSysRoleRepository;
import com.sanythadmin.project.mobile.service.MobileHomeComponentService;
import com.sanythadmin.project.mobile.vo.MobileGroupVo;
import com.sanythadmin.project.mobile.vo.MobileOption;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 移动端/首页组件管理
 *
 * @since 2025/3/20 11:07
 */
@RestController
@RequestMapping("/api/mobile/home/<USER>")
public class MobileHomeComponentController {

    private final MobileHomeComponentRepository mobileHomeComponentRepository;
    private final MobileSysRoleRepository mobileSysRoleRepository;
    private final MobileHomeComponentService mobileHomeComponentService;

    public MobileHomeComponentController(MobileHomeComponentRepository mobileHomeComponentRepository, MobileSysRoleRepository mobileSysRoleRepository, MobileHomeComponentService mobileHomeComponentService) {
        this.mobileHomeComponentRepository = mobileHomeComponentRepository;
        this.mobileSysRoleRepository = mobileSysRoleRepository;
        this.mobileHomeComponentService = mobileHomeComponentService;
    }

    /**
     * 分页查询
     * 权限标识：mobile:home:component:list
     */
    @PreAuthorize("hasAuthority('mobile:home:component:list')")
    @GetMapping("/page")
    public PageResult<MobileHomeComponentFormDto> page(MobileHomeComponentQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("sortOrder").ascending());

        Page<MobileHomeComponent> page = mobileHomeComponentRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (query.getRoleId() != null)
                        return criteriaBuilder.like(root.get("role").get("id"), query.getRoleId());
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(MobileHomeComponentFormDto::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('mobile:home:component:list')")
    @GetMapping("/{id}")
    public MobileHomeComponentFormDto get(@PathVariable("id") String id) {
        return mobileHomeComponentService.getFormById(id);
    }

    /**
     * 可选组件类型
     * 权限标识：mobile:home:component:list
     */
    @PreAuthorize("hasAuthority('mobile:home:component:list')")
    @GetMapping("optionalType")
    public List<MobileOption> optionalType() {
        List<MobileOption> options = new ArrayList<>();
        for (MobileHomeComponentType type : MobileHomeComponentType.values()) {
            options.add(new MobileOption(type.toString(), type.getText()));
        }
        return options;
    }

    /**
     * 可选角色
     * 权限标识：mobile:home:component:list
     */
    @PreAuthorize("hasAuthority('mobile:home:component:list')")
    @GetMapping("/optionalRole")
    public List<MobileOption> optionalRole() {
        Iterable<SysRole> all = mobileSysRoleRepository.findAll();
        List<MobileOption> list = new ArrayList<>();
        all.forEach(mobileGroup -> list.add(new MobileOption(mobileGroup.getId(), mobileGroup.getName())));
        return list;
    }

    /**
     * 可选关联应用
     * 权限标识：mobile:home:component:list
     */
    @PreAuthorize("hasAuthority('mobile:home:component:list')")
    @GetMapping("/optionalApp")
    public List<MobileGroupVo> optionalApp(String roleId) {
        return mobileHomeComponentService.optionalAppTree(roleId);
    }

    /**
     * 添加或修改
     * 权限标识：mobile:home:component:operation
     */
    @PreAuthorize("hasAuthority('mobile:home:component:operation')")
    @OperationLog(module = "移动端-首页-组件", comments = "保存")
    @PostMapping(path = "/operation", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public void operation(@ModelAttribute MobileHomeComponentFormDto form) {
        mobileHomeComponentService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：mobile:home:component:remove
     */
    @PreAuthorize("hasAuthority('mobile:home:component:remove')")
    @OperationLog(module = "移动端-首页-组件", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        mobileHomeComponentRepository.deleteAllById(ids);
    }
}
