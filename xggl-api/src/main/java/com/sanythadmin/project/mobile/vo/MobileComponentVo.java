package com.sanythadmin.project.mobile.vo;

import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/3/26 16:15
 */
@Data
public class MobileComponentVo {

    public MobileComponentVo(MobileHomeComponent component) {
        this.name = component.getName();
        this.sortOrder = component.getSortOrder();
        this.functionType = component.getFunctionType();
        this.displayType = component.getDisplayType();
    }

    private String name;
    private int sortOrder;
    private String functionType;
    private String displayType;
    private List<MobileComponentItemVo> items = new ArrayList<>();
}
