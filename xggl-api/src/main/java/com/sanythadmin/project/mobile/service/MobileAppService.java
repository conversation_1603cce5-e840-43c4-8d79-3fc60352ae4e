package com.sanythadmin.project.mobile.service;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mobile.form.MobileAppForm;
import com.sanythadmin.project.mobile.query.MobileAppQuery;

import java.util.List;

public interface MobileAppService {
    void operation(MobileAppForm form);

    MobileAppForm getFormById(String id);

    PageResult<MobileAppForm> page(MobileAppQuery query);

    void deleteAllById(List<String> ids);
}
