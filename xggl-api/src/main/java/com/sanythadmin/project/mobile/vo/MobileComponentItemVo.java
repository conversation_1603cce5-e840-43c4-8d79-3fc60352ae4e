package com.sanythadmin.project.mobile.vo;

import com.sanythadmin.project.mobile.entity.MobileHomeComponentItem;
import com.sanythadmin.project.mobile.util.MobileUtil;
import lombok.Data;

/**
 * @since 2025/3/26 16:21
 */
@Data
public class MobileComponentItemVo {

    public MobileComponentItemVo(MobileHomeComponentItem item) {
        this.title = item.getTitle();
        this.sortOrder = item.getSortOrder();
        this.description = item.getDescription();
        this.icon = item.getIcon();
        this.iconColor = item.getIconColor();
        this.url = item.getUrl();

        if (item.getAppRole() != null) {
            this.app = new MobileAppVo(item.getAppRole().getApp());
        }

        if (item.getGroupRole() != null) {
            this.groupVo = new MobileGroupVo(item.getGroupRole().getGroup());
        }

        if (item.getSha1() != null) {
            this.imageDataUrl = MobileUtil.imageDataUrl(item.getMime(), item.getBin());
        }
    }

    private String title;
    private MobileAppVo app;
    private MobileGroupVo groupVo;
    private int sortOrder;
    private String description;
    private String icon;
    private String iconColor;
    private String url;
    private String imageDataUrl;
}
