package com.sanythadmin.project.mobile.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileAppRole;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.entity.MobileGroupRole;
import com.sanythadmin.project.mobile.form.MobileAppForm;
import com.sanythadmin.project.mobile.query.MobileAppQuery;
import com.sanythadmin.project.mobile.repository.MobileAppRepository;
import com.sanythadmin.project.mobile.repository.MobileGroupRepository;
import com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository;
import com.sanythadmin.project.mobile.repository.MobileSysRoleRepository;
import com.sanythadmin.project.mobile.service.MobileAppService;
import com.sanythadmin.project.mobile.vo.MobileOption;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @since 2025/3/18 16:33
 */
@Service
public class MobileAppServiceImpl implements MobileAppService {

    private final MobileAppRepository mobileAppRepository;
    private final MobileGroupRepository mobileGroupRepository;
    private final MobileSysRoleRepository mobileSysRoleRepository;
    private final MobileGroupRoleRepository groupRoleRepository;

    public MobileAppServiceImpl(MobileAppRepository mobileAppRepository, MobileGroupRepository mobileGroupRepository, MobileSysRoleRepository mobileSysRoleRepository, MobileGroupRoleRepository groupRoleRepository) {
        this.mobileAppRepository = mobileAppRepository;
        this.mobileGroupRepository = mobileGroupRepository;
        this.mobileSysRoleRepository = mobileSysRoleRepository;
        this.groupRoleRepository = groupRoleRepository;
    }

    @Transactional
    @Override
    public void operation(MobileAppForm form) {
        MobileApp entity;
        LocalDateTime now = LocalDateTime.now();
        if (form.getId() != null) {
            entity = mobileAppRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(now);
        } else {
            entity = new MobileApp();
            entity.setCreatedAt(now);
        }

        entity.setName(form.getName());
        entity.setPath(form.getPath());
        entity.setIcon(form.getIcon());
        entity.setIconColor(form.getIconColor());
        entity.setDescription(form.getDescription());
        entity.setComponentPath(form.getComponent());
        if (form.getSortOrder() == null) {
            entity.setSortOrder(0);
        } else {
            entity.setSortOrder(form.getSortOrder());
        }
        entity.setEnabled(form.isEnabled());

        // 处理表单中的分组，分组必填
        if (form.getGroupId() == null) {
            throw new BusinessException("groupId为空");
        }
        entity.setGroup(mobileGroupRepository.findById(form.getGroupId()).orElseThrow());

        // 处理表单中的角色
        Set<String> roleIdSet = new HashSet<>();
        if (form.getRoles() != null) {
//            roleIdSet.addAll(Arrays.asList(form.getRoleIds()));
            for (MobileOption role : form.getRoles()) {
                roleIdSet.add(role.getId());
            }
        }
        List<MobileAppRole> removeAppRoles = new ArrayList<>();
        for (MobileAppRole appRole : entity.getRoles()) {
            String roleId = appRole.getGroupRole().getRole().getId();
            if (roleIdSet.contains(roleId)) {
                roleIdSet.remove(roleId);
            } else {
                removeAppRoles.add(appRole);
            }
        }
        // 移除已取消关联的角色
        for (MobileAppRole removeAppRole : removeAppRoles) {
            entity.getRoles().remove(removeAppRole);
        }
        // 关联表单中新增的角色
        for (String roleId : roleIdSet) {
            SysRole role = mobileSysRoleRepository.findById(roleId).orElseThrow();

            // 获取分组角色
            MobileGroupRole firstByRole = groupRoleRepository.findFirstByGroupAndRole(entity.getGroup(), role);
            if (firstByRole == null) {
                firstByRole = new MobileGroupRole();
                firstByRole.setGroup(entity.getGroup());
                firstByRole.setRole(role);
                firstByRole.setCreatedAt(now);
                groupRoleRepository.save(firstByRole);
            }

            MobileAppRole appRole = new MobileAppRole();
            appRole.setApp(entity);
            appRole.setGroupRole(firstByRole);
            appRole.setCreatedAt(now);
            entity.getRoles().add(appRole);
        }

        mobileAppRepository.save(entity);

        // 清除不再有应用绑定的分组角色关联记录
        groupRoleRepository.deleteByAppRolesIsEmpty();
    }

    @Transactional(readOnly = true)
    @Override
    public MobileAppForm getFormById(String id) {
        Optional<MobileApp> item = mobileAppRepository.findById(id);
        MobileApp app = item.orElseThrow();
        MobileAppForm form = new MobileAppForm(app);
        MobileGroup group = app.getGroup();
        if (group != null) {
            form.setGroupId(group.getId());
        }
//        if (!CollectionUtils.isEmpty(app.getRoles())) {
//            List<MobileAppRole> roles = app.getRoles();
//            roles.forEach(role -> form.getRoles().add(new MobileOption(role.getRole().getId(), role.getRole().getName())));
//        }
        return form;
    }

    @Transactional(readOnly = true)
    @Override
    public PageResult<MobileAppForm> page(MobileAppQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("sortOrder").ascending());

        Page<MobileApp> page = mobileAppRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (query.getGroupId() != null) {
//                        Subquery<MobileGroup> subquery = query1.subquery(MobileGroup.class);
//                        Root<MobileGroup> group = subquery.from(MobileGroup.class);
//                        Expression<Collection<MobileApp>> groupApps = group.get("apps");
//                        subquery.select(group);
//                        subquery.where(criteriaBuilder.equal(group.get("id"), query.getGroupId()), criteriaBuilder.isMember(root, groupApps));
//                        return criteriaBuilder.exists(subquery);
                        return criteriaBuilder.equal(root.get("group").get("id"), query.getGroupId());
                    }
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (query.getRoleId() != null) {
                        Subquery<MobileAppRole> subquery = query1.subquery(MobileAppRole.class);
                        Root<MobileAppRole> appRoleRoot = subquery.from(MobileAppRole.class);
                        Expression<Object> app = appRoleRoot.get("app");
                        subquery.select(appRoleRoot);
                        subquery.where(criteriaBuilder.equal(appRoleRoot.get("role").get("id"), query.getRoleId()), criteriaBuilder.equal(root, app));
                        return criteriaBuilder.exists(subquery);
                    }
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(MobileAppForm::new).toList(), page.getTotalElements());
    }

    @Transactional
    @Override
    public void deleteAllById(List<String> ids) {
        mobileAppRepository.deleteAllById(ids);

        // 清除不再有应用绑定的分组角色关联记录
        groupRoleRepository.deleteByAppRolesIsEmpty();
    }
}
