package com.sanythadmin.project.mobile.service.impl;

import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileHomeComponent;
import com.sanythadmin.project.mobile.entity.MobileHomeComponentItem;
import com.sanythadmin.project.mobile.repository.MobileAppRepository;
import com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository;
import com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository;
import com.sanythadmin.project.mobile.service.MobileViewService;
import com.sanythadmin.project.mobile.util.MobileUtil;
import com.sanythadmin.project.mobile.vo.MobileComponentItemVo;
import com.sanythadmin.project.mobile.vo.MobileComponentVo;
import com.sanythadmin.project.mobile.vo.MobileGroupVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @since 2025/3/26 15:13
 */
@Service
public class MobileViewServiceImpl implements MobileViewService {

    private final MobileAppRepository appRepository;
    private final MobileHomeComponentItemRepository homeComponentItemRepository;
    private final MobileHomeComponentRepository homeComponentRepository;

    public MobileViewServiceImpl(MobileAppRepository appRepository, MobileHomeComponentItemRepository homeComponentItemRepository, MobileHomeComponentRepository homeComponentRepository) {
        this.appRepository = appRepository;
        this.homeComponentItemRepository = homeComponentItemRepository;
        this.homeComponentRepository = homeComponentRepository;
    }

//    @Transactional(readOnly = true)
    @Override
    public List<MobileGroupVo> treeApps() {
        SysRole role = SecurityUtil.getRole();
        List<MobileApp> list = appRepository.findEnabledAppsByRole(role);

        return MobileUtil.appListToTree(list);
    }

    @Transactional(readOnly = true)
    @Override
    public List<MobileComponentVo> treeComponents() {
        SysRole role = SecurityUtil.getRole();
        List<MobileHomeComponent> components = homeComponentRepository.findEnabledComponentsByRole(role);
        List<MobileComponentVo> vos = new ArrayList<>();
        for (MobileHomeComponent component : components) {
            MobileComponentVo view = new MobileComponentVo(component);
            List<MobileHomeComponentItem> componentItems = homeComponentItemRepository.findByEnabledTrueAndComponentOrderBySortOrderAsc(component);
            for (MobileHomeComponentItem item : componentItems) {
                view.getItems().add(new MobileComponentItemVo(item));
            }
            view.getItems().sort(Comparator.comparingInt(MobileComponentItemVo::getSortOrder));
            vos.add(view);
        }
        return vos;
    }
}
