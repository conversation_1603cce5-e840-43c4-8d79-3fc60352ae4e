package com.sanythadmin.project.mobile.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @since 2025/3/18 9:59
 */
@Data
@EqualsAndHashCode(of = "id")
@MappedSuperclass
public abstract class MobileBaseEntity {

    @Id
//    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
//    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    @Column(length=36)
    @GeneratedValue(strategy= GenerationType.UUID)
    private String id;

    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT")
//    @TableField("CREATED_AT")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "UPDATED_AT")
//    @TableField("UPDATED_AT")
    private LocalDateTime updatedAt;
}
