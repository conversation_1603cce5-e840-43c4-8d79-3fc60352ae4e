package com.sanythadmin.project.mobile.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.form.MobileGroupForm;
import com.sanythadmin.project.mobile.query.MobileGroupQuery;
import com.sanythadmin.project.mobile.repository.MobileGroupRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 移动端/应用组管理
 *
 * @since 2025/3/18 11:49
 */
@RestController
@RequestMapping("/api/mobile/group")
public class MobileGroupController {

    private final MobileGroupRepository mobileGroupRepository;

    public MobileGroupController(MobileGroupRepository mobileGroupRepository) {
        this.mobileGroupRepository = mobileGroupRepository;
    }

    /**
     * 分页查询
     * 权限标识：mobile:group:list
     */
    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/page")
    public PageResult<MobileGroupForm> page(MobileGroupQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("sortOrder").ascending());

        Page<MobileGroup> page = mobileGroupRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(MobileGroupForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/{id}")
    public MobileGroupForm get(@PathVariable("id") String id) {
        Optional<MobileGroup> item = mobileGroupRepository.findById(id);
        return new MobileGroupForm(item.orElseThrow());
    }

    /**
     * 添加或修改
     * 权限标识：mobile:group:operation
     */
    @PreAuthorize("hasAuthority('mobile:group:operation')")
    @OperationLog(module = "移动端-应用组", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody MobileGroupForm form) {
        MobileGroup entity;
        if (form.getId() != null) {
            entity = mobileGroupRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new MobileGroup();
            entity.setCreatedAt(LocalDateTime.now());
        }

        entity.setName(form.getName());
        entity.setDescription(form.getDescription());
        entity.setIcon(form.getIcon());
        entity.setIconColor(form.getIconColor());
        entity.setEnabled(form.isEnabled());
        if (form.getSortOrder() == null) {
            entity.setSortOrder(0);
        } else {
            entity.setSortOrder(form.getSortOrder());
        }
        mobileGroupRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：mobile:group:remove
     */
    @PreAuthorize("hasAuthority('mobile:group:remove')")
    @OperationLog(module = "移动端-应用组", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        mobileGroupRepository.deleteAllById(ids);
    }
}
