package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.entity.MobileHomeComponentItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * @since 2025/3/21 15:05
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileHomeComponentItemFormDto extends MobileBaseForm {

    public MobileHomeComponentItemFormDto(MobileHomeComponentItem entity) {
        super(entity);
        this.title = entity.getTitle();
        this.description = entity.getDescription();
        this.icon = entity.getIcon();
        this.iconColor = entity.getIconColor();
        this.enabled = entity.isEnabled();
        this.sortOrder = entity.getSortOrder();
        this.url = entity.getUrl();

        if (entity.getAppRole() != null) {
            MobileApp app = entity.getAppRole().getApp();
            this.app = new MobileHomeComponentItemAppForm(app);
        }
        if (entity.getGroupRole() != null) {
            MobileGroup group = entity.getGroupRole().getGroup();
            this.group = new MobileHomeComponentItemGroupForm(group);
        }
    }

    /**
     * 组件编号
     */
    private String componentId;
    /**
     * 关联应用
     */
    private MobileHomeComponentItemAppForm app;
    /**
     * 关联分组
     */
    private MobileHomeComponentItemGroupForm group;
    /**
     * 标题或名称
     */
    private String title;
    private String description;
    /**
     * 图标标识
     */
    private String icon;
    /**
     * 图标颜色
     */
    private String iconColor;
    private int sortOrder;
    private boolean enabled;
    /**
     * 图片上传
     */
    private MultipartFile uploadImage;
    /**
     * 图片删除标记
     */
    private boolean deleteImage;
    /**
     * 图片显示
     */
    private String imageDataUrl;
    private String url;
}
