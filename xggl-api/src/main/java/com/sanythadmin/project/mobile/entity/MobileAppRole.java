package com.sanythadmin.project.mobile.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 移动端-应用角色关联表
 *
 * @since 2025/3/19 16:09
 */
@Data
@Entity
@Table(name = "syt_mobile_app_role")
@NoArgsConstructor
public class MobileAppRole extends MobileBaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "APP_ID")
    private MobileApp app;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GROUP_ROLE_ID")
    private MobileGroupRole groupRole;

    @OneToMany(mappedBy = "appRole", cascade = CascadeType.REMOVE)
    private List<MobileHomeComponentItem> items;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass())
            return false;

        MobileAppRole that = (MobileAppRole) o;
        return Objects.equals(app, that.app) &&
                Objects.equals(groupRole, that.groupRole);
    }

    @Override
    public int hashCode() {
        return Objects.hash(app, groupRole);
    }
}
