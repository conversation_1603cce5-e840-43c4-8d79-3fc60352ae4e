package com.sanythadmin.project.mobile.entity;

import com.sanythadmin.common.system.entity.SysRole;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import java.util.List;

/**
 * 移动端-首页组件
 *
 * @since 2025/3/20 9:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_mobile_home_component")
public class MobileHomeComponent extends MobileBaseEntity {

    @ManyToOne
    @JoinColumn(name = "ROLE_ID")
    private SysRole role;

    @OneToMany(mappedBy = "component", cascade = CascadeType.REMOVE)
    @OrderBy("sortOrder ASC")
    private List<MobileHomeComponentItem> items;

    /**
     * 组件名称
     */
    private String name;

//    /**
//     * 组件类型
//     * # 1-可点击图标组件；2-可点击卡片组件；3-普通组件
//     */
//    @NotNull
//    @Enumerated(EnumType.STRING)
//    private MobileHomeComponentType type;

    /**
     * 组件功能描述或标识
     * #仅适用于非特殊组件，如“待办”、“新闻”、“通知公告”等
     */
    @Column(name = "function_type")
    private String functionType;

    /**
     * 展示类型
     * #仅适用于非特殊组件，如“列表”、“表格”、“统计图”等
     */
    @Column(name = "display_type")
    private String displayType;

    /**
     * 说明
     */
    private String description;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    @ColumnDefault("0")
    @NotNull
    private boolean enabled;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder;
}
