package com.sanythadmin.project.mobile.repository;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileApp;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MobileAppRepository extends PagingAndSortingRepository<MobileApp, String>, CrudRepository<MobileApp, String>, JpaSpecificationExecutor<MobileApp> {

    @Query("SELECT ar.app FROM MobileAppRole ar WHERE ar.groupRole.role = :role")
    List<MobileApp> findAppsByRole(@Param("role") SysRole role);

    @Query("SELECT ar.app FROM MobileAppRole ar WHERE ar.groupRole.role.id = :roleId")
    List<MobileApp> findByRoleId(@Param("roleId") String roleId);

    @Query("SELECT mar.app FROM MobileAppRole mar JOIN mar.app a JOIN a.group g " +
            "WHERE mar.groupRole.role = :role AND a.isEnabled = true AND g.isEnabled = true ORDER BY g.sortOrder ASC, a.sortOrder ASC")
    List<MobileApp> findEnabledAppsByRole(@Param("role") SysRole role);
}
