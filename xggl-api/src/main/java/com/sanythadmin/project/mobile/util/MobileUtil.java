package com.sanythadmin.project.mobile.util;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.vo.MobileAppVo;
import com.sanythadmin.project.mobile.vo.MobileGroupVo;
import org.apache.commons.codec.binary.Base64;

import java.util.*;

/**
 * @since 2025/3/21 16:15
 */
public class MobileUtil {

    public static void throwException(String message) throws BusinessException {
        throw new BusinessException(message);
    }

    public static void checkTrue(boolean expression, String content) throws BusinessException {
        if (!expression) {
            throw new BusinessException(content);
        }
    }

    public static List<MobileGroupVo> appListToTree(List<MobileApp> list) {
        Map<MobileGroup, MobileGroupVo> map = new HashMap<>();
        for (MobileApp app : list) {
            MobileGroup group = app.getGroup();
            MobileGroupVo viewGroup = map.get(group);
            if (viewGroup == null) {
                viewGroup = new MobileGroupVo(group);
                map.put(group, viewGroup);
            }
            viewGroup.getApps().add(new MobileAppVo(app));
        }
        List<MobileGroupVo> tree = new ArrayList<>(map.values());
        tree.sort(Comparator.comparing(MobileGroupVo::getSortOrder).thenComparing(MobileGroupVo::getName));

        return tree;
    }

    public static String imageDataUrl(String mime, byte[] data) {
        return "data:" + mime + ";base64," + Base64.encodeBase64String(data);
    }
}
