package com.sanythadmin.project.mobile.controller;

import com.sanythadmin.project.mobile.service.MobileViewService;
import com.sanythadmin.project.mobile.vo.MobileComponentVo;
import com.sanythadmin.project.mobile.vo.MobileGroupVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 移动端/用户查看
 *
 * @since 2025/3/26 14:53
 */
@RestController
@RequestMapping("/api/mobile/view")
public class MobileViewController {

    private final MobileViewService mobileViewService;

    public MobileViewController(MobileViewService mobileViewService) {
        this.mobileViewService = mobileViewService;
    }

    @RequestMapping("/apps")
    public List<MobileGroupVo> apps() {
        return mobileViewService.treeApps();
    }

    @RequestMapping("/components")
    public List<MobileComponentVo> components() {
        return mobileViewService.treeComponents();
    }
}
