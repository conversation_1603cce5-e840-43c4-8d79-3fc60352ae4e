package com.sanythadmin.project.mobile.service.impl;

import com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository;
import com.sanythadmin.project.mobile.service.MobileGroupRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @since 2025/3/28 10:02
 */
@Service
public class MobileGroupRoleServiceImpl implements MobileGroupRoleService {

    private final MobileGroupRoleRepository groupRoleRepository;

    public MobileGroupRoleServiceImpl(MobileGroupRoleRepository groupRoleRepository) {
        this.groupRoleRepository = groupRoleRepository;
    }

    @Transactional
    @Override
    public void deleteByAppRolesIsEmpty() {
        groupRoleRepository.deleteByAppRolesIsEmpty();
    }
}
