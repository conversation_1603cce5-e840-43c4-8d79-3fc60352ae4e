package com.sanythadmin.project.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.annotations.ColumnDefault;

import java.util.List;

/**
 * 移动端-应用组
 *
 * @since 2025/3/17 15:21
 */

@Data
@Entity
@Table(name = "SYT_MOBILE_GROUP")
public class MobileGroup extends MobileBaseEntity {

    @OneToMany(mappedBy = "group", cascade = CascadeType.REMOVE)
    private List<MobileApp> apps;

    @OneToMany(mappedBy = "group", cascade = CascadeType.REMOVE)
    private List<MobileGroupRole> groupRoles;

    /**
     * 名称
     */
    @Column(name = "NAME", nullable = false)
    @TableField("NAME")
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 图标标识
     */
    @Column(name = "ICON")
    @TableField("ICON")
    private String icon;

    /**
     * 图标颜色
     */
    @Column(name = "ICON_COLOR")
    private String iconColor;

    /**
     * 是否启用
     */
    @Column(name = "IS_ENABLED", nullable = false)
    @ColumnDefault("0")
    @NotNull
    private boolean isEnabled;

    /**
     * 排序号
     */
    @Column(name = "SORT_ORDER")
    @TableField("SORT_ORDER")
    private Integer sortOrder;

}
