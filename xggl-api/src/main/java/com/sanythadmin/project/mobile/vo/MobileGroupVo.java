package com.sanythadmin.project.mobile.vo;

import com.sanythadmin.project.mobile.entity.MobileGroup;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/3/26 15:03
 */
@Data
public class MobileGroupVo {

    public MobileGroupVo(MobileGroup group) {
        this.id = group.getId();
        this.name = group.getName();
        this.description = group.getDescription();
        this.icon = group.getIcon();
        this.iconColor = group.getIconColor();
        this.sortOrder = group.getSortOrder();
    }

    private String id;
    private String name;
    private String description;
    private String icon;
    private String iconColor;
    private int sortOrder;
    private List<MobileAppVo> apps = new ArrayList<>();

}
