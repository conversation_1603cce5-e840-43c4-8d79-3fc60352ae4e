package com.sanythadmin.project.mobile.form;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileApp;
import com.sanythadmin.project.mobile.entity.MobileAppRole;
import com.sanythadmin.project.mobile.vo.MobileOption;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/3/17 16:56
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileAppForm extends MobileBaseForm {

    public MobileAppForm(MobileApp app) {
        super(app);
        this.name = app.getName();
        this.path = app.getPath();
        this.description = app.getDescription();
        this.icon = app.getIcon();
        this.iconColor = app.getIconColor();
        this.enabled = app.isEnabled();
        this.sortOrder = app.getSortOrder();
        if (!CollectionUtils.isEmpty(app.getRoles())) {
            List<MobileAppRole> roles = app.getRoles();
            roles.forEach(role -> {
                SysRole sysRole = role.getGroupRole().getRole();
                this.roles.add(new MobileOption(sysRole.getId(), sysRole.getName()));
            });
        }
        this.component = app.getComponentPath();
    }

    private String name;
    private String path;
    private String description;
    private String icon;
    private String iconColor;
    private boolean enabled;
    private Integer sortOrder;
    private String groupId;
    /**
     * 组件路径(兼容前端)
     */
    private String component;
    private List<MobileOption> roles = new ArrayList<>();
}
