package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileGroup;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @since 2025/3/27 16:37
 */
@Data
@NoArgsConstructor
public class MobileHomeComponentItemGroupForm {

    public MobileHomeComponentItemGroupForm(MobileGroup group) {
        this.id = group.getId();
        this.name = group.getName();
        this.description = group.getDescription();
        this.icon = group.getIcon();
        this.iconColor = group.getIconColor();
    }

    private String id;
    private String name;
    private String description;
    private String icon;
    private String iconColor;
}
