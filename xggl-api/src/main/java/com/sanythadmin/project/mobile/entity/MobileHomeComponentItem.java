package com.sanythadmin.project.mobile.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端-首页组件-子项
 *
 * @since 2025/3/21 14:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_mobile_home_component_item")
public class MobileHomeComponentItem extends MobileBaseEntity {

    @ManyToOne
    @JoinColumn(name = "COMPONENT_ID")
    private MobileHomeComponent component;

    /**
     * 关联应用角色
     */
    @ManyToOne
    @JoinColumn(name = "APP_ROLE_ID")
    private MobileAppRole appRole;

    /**
     * 关联分组角色
     */
    @ManyToOne
    @JoinColumn(name = "GROUP_ROLE_ID")
    private MobileGroupRole groupRole;


    private String title;
    private String description;

    private String icon;
    /**
     * 图标颜色
     */
    @Column(name = "ICON_COLOR")
    private String iconColor;

    @Column(name = "ENABLED", columnDefinition = "NUMBER(1) DEFAULT 0", nullable = false)
    private boolean enabled;

    @Column(name = "SORT_ORDER")
    private Integer sortOrder;

    private String filename;
//    private String extension;
    @Lob
    private byte[] bin;
    private String mime;
    private Long length;
    private String sha1;

    private String url;
}
