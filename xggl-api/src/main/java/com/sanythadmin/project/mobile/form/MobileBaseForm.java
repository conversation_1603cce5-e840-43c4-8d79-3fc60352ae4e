package com.sanythadmin.project.mobile.form;

import com.sanythadmin.project.mobile.entity.MobileBaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/3/20 10:56
 */
@Data
@NoArgsConstructor
public abstract class MobileBaseForm {

    public MobileBaseForm(MobileBaseEntity baseEntity) {
        this.id = baseEntity.getId();
        this.createdAtText = baseEntity.getCreatedAt();
        this.updatedAtText = baseEntity.getUpdatedAt();
    }

    private String id;
    private LocalDateTime createdAtText;
    private LocalDateTime updatedAtText;
}
