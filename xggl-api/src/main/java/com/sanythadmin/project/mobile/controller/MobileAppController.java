package com.sanythadmin.project.mobile.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.mobile.entity.MobileGroup;
import com.sanythadmin.project.mobile.form.MobileAppForm;
import com.sanythadmin.project.mobile.query.MobileAppQuery;
import com.sanythadmin.project.mobile.repository.MobileGroupRepository;
import com.sanythadmin.project.mobile.repository.MobileSysRoleRepository;
import com.sanythadmin.project.mobile.service.MobileAppService;
import com.sanythadmin.project.mobile.vo.MobileOption;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 移动端/应用管理
 *
 * @since 2025/3/17 16:03
 */
@RestController
@RequestMapping("/api/mobile/app")
public class MobileAppController {

    private final MobileGroupRepository mobileGroupRepository;
    private final MobileAppService mobileAppService;
    private final MobileSysRoleRepository mobileSysRoleRepository;

    public MobileAppController(MobileGroupRepository mobileGroupRepository, MobileAppService mobileAppService, MobileSysRoleRepository mobileSysRoleRepository) {
        this.mobileGroupRepository = mobileGroupRepository;
        this.mobileAppService = mobileAppService;
        this.mobileSysRoleRepository = mobileSysRoleRepository;
    }

    /**
     * 分页查询
     * 权限标识：mobile:group:list
     */
    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/page")
    public PageResult<MobileAppForm> page(MobileAppQuery query) {
        return mobileAppService.page(query);
    }

    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/optionalGroup")
    public List<MobileOption> optionalGroup() {
        Sort sort = Sort.by(Sort.Order.asc("sortOrder"), Sort.Order.desc("createdAt"));

        Iterable<MobileGroup> all = mobileGroupRepository.findAll(sort);
        List<MobileOption> list = new ArrayList<>();
        all.forEach(mobileGroup -> list.add(new MobileOption(mobileGroup.getId(), mobileGroup.getName())));
        return list;
    }

    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/optionalRole")
    public List<MobileOption> optionalRole() {
        Iterable<SysRole> all = mobileSysRoleRepository.findAll();
        List<MobileOption> list = new ArrayList<>();
        all.forEach(mobileGroup -> list.add(new MobileOption(mobileGroup.getId(), mobileGroup.getName())));
        return list;
    }

    @PreAuthorize("hasAuthority('mobile:group:list')")
    @GetMapping("/{id}")
    public MobileAppForm get(@PathVariable("id") String id) {
        return mobileAppService.getFormById(id);
    }

    /**
     * 添加或修改
     * 权限标识：mobile:group:operation
     */
    @PreAuthorize("hasAuthority('mobile:group:operation')")
    @OperationLog(module = "移动端-应用", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody MobileAppForm form) {
        mobileAppService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：mobile:group:remove
     */
    @PreAuthorize("hasAuthority('mobile:group:remove')")
    @OperationLog(module = "移动端-应用", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        mobileAppService.deleteAllById(ids);
    }
}
