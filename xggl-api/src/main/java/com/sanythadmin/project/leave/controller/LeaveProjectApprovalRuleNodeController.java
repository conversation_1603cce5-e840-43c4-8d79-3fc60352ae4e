package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.project.leave.enums.LeaveConditionOperator;
import com.sanythadmin.project.leave.enums.LeavePolicyAction;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeOperationForm;
import com.sanythadmin.project.leave.query.LeaveProjectApprovalRuleNodeQuery;
import com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository;
import com.sanythadmin.project.leave.service.LeaveProjectApprovalRuleNodeService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

/**
 * 请假模块/类型管理/审批流程规则节点
 *
 * @since 2025/5/7 9:26
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/project/approval_rule_node")
public class LeaveProjectApprovalRuleNodeController {

    private final LeaveProjectApprovalRuleNodeService ruleNodeService;
    private final LeaveProjectFieldRepository fieldRepository;

    /**
     * 列表查询
     * 权限标识：leave:project:approval_rule_node:list
     */
    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:list')")
    @GetMapping("/list")
    public List<List<LeaveProjectApprovalRuleNodeForm>> list(LeaveProjectApprovalRuleNodeQuery query) {
        return ruleNodeService.list(query);
    }

    /**
     * 条件的可选匹配规则
     * 权限标识：leave:project:approval_rule_node:list
     */
    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:list')")
    @GetMapping("/optionalConditionOperator")
    public List<LeaveIdTextPairForm> optionalConditionOperator() {
        return Stream.of(LeaveConditionOperator.EQUAL, LeaveConditionOperator.NOT_EQUAL).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        }).toList();
    }

    /**
     * 条件的可选字段
     * 权限标识：leave:project:approval_rule_node:list
     */
    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:list')")
    @GetMapping("/optionalConditionField")
    public Stream<LeaveIdTextPairForm> optionalConditionField(String projectId, LeaveRequestType requestType) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(projectId), "请假类型ID不能为空");
        LeaveUtil.checkTrue(requestType != null, "申请类型不能为空");

        return fieldRepository.findByProjectIdAndRequestTypeOrderBySortOrder(projectId, requestType).stream().map(
                f -> new LeaveIdTextPairForm(f.getId(), f.getName()));
    }

    /**
     * 条件的可选策略
     * 权限标识：leave:project:approval_rule_node:list
     */
    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:list')")
    @GetMapping("/optionalConditionAction")
    public Stream<LeaveIdTextPairForm> optionalConditionAction() {
        return Arrays.stream(LeavePolicyAction.values()).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        });
    }

    /**
     * 添加或修改
     * 权限标识：leave:project:approval_rule_node:operation
     */
    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:operation')")
    @OperationLog(module = "请假模块-类型管理-审批流程规则节点管理", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody LeaveProjectApprovalRuleNodeOperationForm form) {
        ruleNodeService.operation(form);
    }

//    /**
//     * 批量删除
//     * 权限标识：leave:project:approval_rule_node:remove
//     */
//    @PreAuthorize("hasAuthority('leave:project:approval_rule_node:remove')")
//    @OperationLog(module = "请假模块-类型管理-审批流程规则节点管理", comments = "批量删除")
//    @PostMapping("/remove")
//    public void remove(@RequestBody List<String> ids) {
//        formFieldRepository.deleteAllById(ids);
//    }
}
