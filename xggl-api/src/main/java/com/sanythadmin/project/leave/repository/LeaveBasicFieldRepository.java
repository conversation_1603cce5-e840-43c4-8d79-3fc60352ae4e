package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.leave.entity.LeaveBasicField;
import com.sanythadmin.project.leave.enums.LeaveBasicFieldScopeType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface LeaveBasicFieldRepository extends PagingAndSortingRepository<LeaveBasicField, String>, CrudRepository<LeaveBasicField, String>, JpaSpecificationExecutor<LeaveBasicField> {
    boolean existsByDicField(DictionaryField dicField);

    List<LeaveBasicField> findByScopesTypeOrderBySortOrder(LeaveBasicFieldScopeType type);

    @Query("select max(sortOrder) from LeaveBasicField")
    Integer findMaxSortOrder();

    @Query("from LeaveBasicField order by sortOrder")
    List<LeaveBasicField> findOrderBySortOrder();
}
