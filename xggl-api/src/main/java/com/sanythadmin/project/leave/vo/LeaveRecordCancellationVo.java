package com.sanythadmin.project.leave.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @since 2025/7/22 10:45
 */
@NoArgsConstructor
@Data
public class LeaveRecordCancellationVo {

    public LeaveRecordCancellationVo(LeaveApplication application) {
        this.application = application;

        this.id = application.getId();
        this.approvalStartTime = application.getApprovalStartTime();
        this.approvalEndTime = application.getApprovalEndTime();
        this.approved = application.getApproved();
        this.submitTime = application.getSubmitTime();
        this.createdAt = application.getCreatedAt();
    }

    @JsonIgnore
    private LeaveApplication application;
    private String id;
    /**
     * 审批开始时间
     */
    private LocalDateTime approvalStartTime;
    private LocalDateTime approvalEndTime;
    /**
     * 是否审批通过
     */
    private Boolean approved;
    private LocalDateTime submitTime;
    private LocalDateTime createdAt;
    /**
     * 申请字段
     */
    private List<LeaveApplicationFieldVo> applicationFields;
    /**
     * 流程信息
     */
    private List<LeaveApplicationFlowVo> flows;

    public String getStatus() {
        return LeaveUtil.getCancellationApprovalStatus(application);
    }

}
