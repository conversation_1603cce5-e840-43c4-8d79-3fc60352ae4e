package com.sanythadmin.project.leave.enums;

import com.sanythadmin.project.leave.util.LeaveEnumI;
import lombok.Getter;

/**
 * 时长计算模式
 *
 * @since 2025/4/25 16:01
 */
@Getter
public enum LeaveDurationMode implements LeaveEnumI {

    DEFAULT("默认"),
    NATURAL_DAY("按自然日计算"),
    ELAPSED_TIME("按实际经过时长向上补全天数");

    private final String text;

    LeaveDurationMode(String text) {
        this.text = text;
    }
}
