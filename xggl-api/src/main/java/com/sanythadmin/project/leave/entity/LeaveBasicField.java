package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.dictionary.entity.DictionaryField;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 请假基本字段
 *
 * @since 2025/4/25 17:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_basic_field")
public class LeaveBasicField extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "dic_field_ID")
    private DictionaryField dicField;
    private String alias;
    private Integer sortOrder;
    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveBasicFieldScope> scopes;
}
