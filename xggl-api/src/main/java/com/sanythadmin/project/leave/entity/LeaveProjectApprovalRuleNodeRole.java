package com.sanythadmin.project.leave.entity;

import com.sanythadmin.common.system.entity.SysRole;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 请假项目审批规则节点角色
 *
 * @since 2025/4/27 16:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_approval_rule_node_role")
public class LeaveProjectApprovalRuleNodeRole extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "NODE_ID")
    private LeaveProjectApprovalRuleNode node;
    @ManyToOne
    @JoinColumn(name = "ROLE_ID", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    private SysRole role;
}
