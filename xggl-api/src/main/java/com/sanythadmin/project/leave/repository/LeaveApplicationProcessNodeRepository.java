package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNode;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeStatus;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface LeaveApplicationProcessNodeRepository extends PagingAndSortingRepository<LeaveApplicationProcessNode, String>, CrudRepository<LeaveApplicationProcessNode, String>, JpaSpecificationExecutor<LeaveApplicationProcessNode> {

    List<LeaveApplicationProcessNode> findByApplicationAndSequence(LeaveApplication application, Integer sequence);

    Optional<LeaveApplicationProcessNode> findFirstByApplicationAndStatus(LeaveApplication application, LeaveProcessNodeStatus status);
}
