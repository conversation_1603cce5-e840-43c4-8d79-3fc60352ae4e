package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.leave.enums.LeaveFormFieldType;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.util.List;

/**
 * 请假项目表单字段
 *
 * @since 2025/4/27 11:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_field")
public class LeaveProjectField extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "PROJECT_ID")
    private LeaveProject project;
    private String name;
    @Enumerated(EnumType.STRING)
    private LeaveFormFieldType type;
    @Enumerated(EnumType.STRING)
    private LeaveRequestType requestType;
    @Column(nullable = false)
    @ColumnDefault("0")
    @NotNull
    private Boolean required;
    private Integer sortOrder;
    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveProjectFieldOption> options;
    /**
     * 提示占位符
     */
    private String placeholder;
    /**
     * 通用码表类型
     */
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private CodeType codeType;

}
