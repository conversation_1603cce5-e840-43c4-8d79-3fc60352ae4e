package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.leave.form.LeaveBasicFieldForm;
import com.sanythadmin.project.leave.query.LeaveBasicFieldQuery;
import com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository;
import com.sanythadmin.project.leave.service.LeaveBasicFieldService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 请假模块/基本字段管理
 *
 * @since 2025/4/30 16:40
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/basic_field")
public class LeaveBasicFieldController {

    private final LeaveBasicFieldRepository basicFieldRepository;
    private final LeaveBasicFieldService basicFieldService;
    private final DictionaryFieldService dictionaryFieldService;

    /**
     * 分页查询
     * 权限标识：leave:basic_field:list
     */
    @PreAuthorize("hasAuthority('leave:basic_field:list')")
    @GetMapping("/page")
    public PageResult<LeaveBasicFieldForm> page(LeaveBasicFieldQuery query) {
        return basicFieldService.page(query);
    }

    @PreAuthorize("hasAuthority('leave:basic_field:list')")
    @GetMapping("/{id}")
    public LeaveBasicFieldForm get(@PathVariable("id") String id) {
        return basicFieldService.getForm(id);
    }

    /**
     * 可选学生字典字段
     * 权限标识：leave:basic_field:list
     */
    @PreAuthorize("hasAuthority('leave:basic_field:list')")
    @GetMapping("/optionalDictionaryField")
    public List<DictionaryField> optionalDictionaryField() {
        DictionaryFieldParam param = new DictionaryFieldParam();
        param.setUserType(UserType.STUDENT);
        return dictionaryFieldService.queryList(param);
    }

    /**
     * 添加或修改
     * 权限标识：leave:basic_field:operation
     */
    @PreAuthorize("hasAuthority('leave:basic_field:operation')")
    @OperationLog(module = "请假模块-基本字段管理", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody LeaveBasicFieldForm form) {
        basicFieldService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：leave:basic_field:remove
     */
    @PreAuthorize("hasAuthority('leave:basic_field:remove')")
    @OperationLog(module = "请假模块-基本字段管理", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        basicFieldRepository.deleteAllById(ids);
    }
}
