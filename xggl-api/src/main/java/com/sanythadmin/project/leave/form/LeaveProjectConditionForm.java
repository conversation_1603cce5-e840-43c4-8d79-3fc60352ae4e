package com.sanythadmin.project.leave.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanythadmin.project.leave.util.LeaveBooleanToCnSerializer;
import com.sanythadmin.project.leave.util.LeaveCnToBooleanDeserializer;
import lombok.Data;

/**
 * @since 2025/5/7 14:21
 */
@Data
public class LeaveProjectConditionForm {

    /**
     * 匹配规则
     */
    private String operatorId;
    /**
     * 匹配字段
     */
    private String fieldId;
    /**
     * 匹配预期值
     */
    private String expectedValue;
    /**
     * 生效后的策略
     */
    private String actionId;
    /**
     * 是否匹配所有
     */
    @JsonSerialize(using = LeaveBooleanToCnSerializer.class)
    @JsonDeserialize(using = LeaveCnToBooleanDeserializer.class)
    private boolean matchAll;

}
