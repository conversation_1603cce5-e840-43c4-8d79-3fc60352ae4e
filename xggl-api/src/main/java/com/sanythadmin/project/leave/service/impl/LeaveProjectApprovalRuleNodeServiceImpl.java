package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNode;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNodeCondition;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNodeRole;
import com.sanythadmin.project.leave.enums.LeaveApprovalMode;
import com.sanythadmin.project.leave.enums.LeaveConditionOperator;
import com.sanythadmin.project.leave.enums.LeavePolicyAction;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalNodeConditionForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeOperationForm;
import com.sanythadmin.project.leave.query.LeaveProjectApprovalRuleNodeQuery;
import com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/5/6 16:00
 */
@AllArgsConstructor
@Service
public class LeaveProjectApprovalRuleNodeServiceImpl implements com.sanythadmin.project.leave.service.LeaveProjectApprovalRuleNodeService {

    private final LeaveProjectRepository projectRepository;
    private final LeaveProjectApprovalRuleNodeRepository ruleNodeRepository;
    private final SysRoleService sysRoleService;
    private final LeaveProjectFieldRepository fieldRepository;

    @Transactional(readOnly = true)
    @Override
    public List<List<LeaveProjectApprovalRuleNodeForm>> list(LeaveProjectApprovalRuleNodeQuery query) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(query.getProjectId()), "类型ID不能为空");
//        LeaveUtil.checkTrue(query.getRequestType() != null, "申请类型不能为空");
        if (query.getRequestType() == null) {
            query.setRequestType(LeaveRequestType.LEAVE);
        }

        List<LeaveProjectApprovalRuleNode> page = ruleNodeRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("project").get("id"), query.getProjectId()),
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("requestType"), query.getRequestType())
        ), Sort.by(Sort.Order.asc("sortOrder"), Sort.Order.asc("parallelOrder")));

        List<List<LeaveProjectApprovalRuleNodeForm>> result = new ArrayList<>();
        Integer stepIndex = null;
        List<LeaveProjectApprovalRuleNodeForm> lastStep = null;
        for (LeaveProjectApprovalRuleNode node : page) {
            if (!node.getSortOrder().equals(stepIndex)) {
                stepIndex = node.getSortOrder();
                lastStep = new ArrayList<>();
                result.add(lastStep);
            }
            lastStep.add(new LeaveProjectApprovalRuleNodeForm(node));
        }
        return result;
    }

    @Transactional
    @Override
    public void operation(LeaveProjectApprovalRuleNodeOperationForm operationForm) {
        LeaveRequestType requestType = operationForm.getRequestType();
        if (requestType == null) {
            requestType = LeaveRequestType.LEAVE;
        }

        String projectId = operationForm.getProjectId();
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(projectId), "projectId is required");
        LeaveProject project = projectRepository.findById(projectId).orElseThrow();

        // 清空旧流程
        ruleNodeRepository.deleteByProjectAndRequestType(project, requestType);

        // 处理新流程
        LocalDateTime now = LocalDateTime.now();
        int stepIndex = 0;
        for (List<LeaveProjectApprovalRuleNodeForm> nodeForms : operationForm.getNodeGroups()) {
            if (CollectionUtils.isEmpty(nodeForms)) {
                continue;
            }
            int parallelOrderindex = 0;
            for (LeaveProjectApprovalRuleNodeForm form : nodeForms) {
                LeaveProjectApprovalRuleNode entity = new LeaveProjectApprovalRuleNode();
                entity.setSortOrder(stepIndex);
                entity.setParallelOrder(parallelOrderindex++);
                entity.setCreatedAt(now);
                entity.setProject(project);
                entity.setRequestType(requestType);
                entity.setConditionEnabled(form.isConditionEnabled());

                LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getName()), "名称不能为空");
                entity.setName(form.getName());

                entity.setMinDays(form.getMinDays());
//                entity.setParallelOrder(form.getParallelOrder());
                entity.setApprovalMode(LeaveApprovalMode.FIRST);

                // 角色处理
                LeaveUtil.checkTrue(CollectionUtils.isNotEmpty(form.getRoles()), "角色不能为空");
                List<LeaveProjectApprovalRuleNodeRole> roles = new ArrayList<>();
                for (LeaveIdTextPairForm role : form.getRoles()) {
                    LeaveProjectApprovalRuleNodeRole roleEntity = new LeaveProjectApprovalRuleNodeRole();
                    roleEntity.setCreatedAt(now);
                    roleEntity.setNode(entity);
                    roleEntity.setRole(sysRoleService.getById(role.getId()));
                    LeaveUtil.checkTrue(roleEntity.getRole() != null, "角色不存在");
                    roles.add(roleEntity);
                }
                entity.setRoles(roles);

                // 条件处理
                List<LeaveProjectApprovalNodeConditionForm> conditions = form.getConditions();
                if (CollectionUtils.isNotEmpty(conditions)) {
                    int conditionIndex = 0;
                    List<LeaveProjectApprovalRuleNodeCondition> nodeConditions = new ArrayList<>();
                    for (LeaveProjectApprovalNodeConditionForm condition : conditions) {
                        LeaveProjectApprovalRuleNodeCondition nodeCondition = new LeaveProjectApprovalRuleNodeCondition();
                        nodeCondition.setNode(entity);
                        nodeCondition.setCreatedAt(now);
                        nodeCondition.setSortOrder(conditionIndex++);

                        // 策略
                        if (condition.getAction() != null && StringUtils.isNotEmpty(condition.getAction().getId())) {
                            nodeCondition.setAction(LeavePolicyAction.valueOf(condition.getAction().getId()));
                        } else {
                            nodeCondition.setAction(LeavePolicyAction.DENY);
                        }

                        // 是否匹配所有
                        nodeCondition.setMatchAll(condition.isMatchAll());

                        // 关联的表单字段（非匹配所有时存入）
                        if (!nodeCondition.getMatchAll()) {
                            LeaveUtil.checkTrue(condition.getField() != null && StringUtils.isNotEmpty(condition.getField().getId()), "关联的表单字段ID不能为空");
                            LeaveUtil.checkTrue(StringUtils.isNotEmpty(condition.getExpectedValue()), "预期值不能为空");
                            LeaveUtil.checkTrue(condition.getOperator() != null && StringUtils.isNotEmpty(condition.getOperator().getId()), "匹配规则不能为空");

                            nodeCondition.setField(fieldRepository.findById(condition.getField().getId()).orElseThrow());
                            LeaveUtil.checkTrue(nodeCondition.getField() != null, "表单字段不存在");
                            nodeCondition.setExpectedValue(condition.getExpectedValue());
                            nodeCondition.setOperator(LeaveConditionOperator.valueOf(condition.getOperator().getId()));
                        }

                        nodeConditions.add(nodeCondition);
                    }
                    entity.setConditions(nodeConditions);
                }

                ruleNodeRepository.save(entity);
            }
            stepIndex++;
        }

    }
}
