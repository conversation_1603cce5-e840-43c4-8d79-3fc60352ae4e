package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.code.service.CodeCommonService;
import com.sanythadmin.project.code.service.CodeDwbService;
import com.sanythadmin.project.code.service.CodeZybService;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.leave.entity.LeaveBasicField;
import com.sanythadmin.project.leave.enums.LeaveBasicFieldScopeType;
import com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository;
import com.sanythadmin.project.leave.vo.LeaveNameValuePairVo;
import lombok.AllArgsConstructor;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/5/9 15:54
 */
@AllArgsConstructor
@Service
public class LeaveCommonServiceImpl implements com.sanythadmin.project.leave.service.LeaveCommonService {

    private final LeaveBasicFieldRepository basicFieldRepository;
    private final UserInfoService userInfoService;
    private final CodeCommonService codeCommonService;
    private final CodeDwbService codeDwbService;
    private final CodeZybService codeZybService;
    private final CodeBjbService codeBjbService;

    @Transactional(readOnly = true)
    @Override
    public List<LeaveNameValuePairVo> basicFieldWithStudentInfo(String username) {
        List<LeaveNameValuePairVo> vos = new ArrayList<>();
        List<LeaveBasicField> fields = basicFieldRepository.findByScopesTypeOrderBySortOrder(LeaveBasicFieldScopeType.DISPLAY);

        UserInfo userInfo = userInfoService.get(username);
        for (LeaveBasicField field : fields) {
            DictionaryField dicField = field.getDicField();
            String userFieldValue;
            try {
                userFieldValue = (String) PropertyUtils.getProperty(userInfo, dicField.getFieldEn());
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
//                            throw new RuntimeException(e);
                throw new BusinessException("获取用户信息失败:" + e.getMessage());
            }

            String value = null;
            if ("other".equals(dicField.getLoadDataType())) {
                // 特殊关联
                switch (dicField.getFieldEn()) {
                    case "xyid" -> {
                        CodeDwb codeDwb = codeDwbService.getById(userFieldValue);
                        value = codeDwb.getName();
                    }
                    case "zyid" -> {
                        CodeZyb codeZyb = codeZybService.getById(userFieldValue);
                        value = codeZyb.getName();
                    }
                    case "bjid" -> {
                        CodeBjb codeBjb = codeBjbService.getById(userFieldValue);
                        value = codeBjb.getName();
                    }
                }
            } else if (StringUtils.isNotEmpty(dicField.getLoadDataType())) {
                // 通用码表
                if (userFieldValue != null) {
                    CodeCommonParam codeCommonParam = new CodeCommonParam(dicField.getLoadDataType());
                    String textField = dicField.getTextField();
                    String valueField = dicField.getValueField();

                    if (StringUtils.isEmpty(textField)) {
                        textField = "name";
                    }
                    if (StringUtils.isEmpty(valueField)) {
                        valueField = "name";
                    }

                    if ("id".equals(valueField)) {
                        CodeCommon codeCommon = codeCommonService.getById(userFieldValue);
                        switch (textField) {
                            case "name" -> value = codeCommon.getName();
                            case "code" -> value = codeCommon.getCode();
                        }
                    }
                }
            } else {
                // 普通字段
                value = userFieldValue;
            }

            String name = dicField.getFieldZh();
            if (StringUtils.isNotEmpty(field.getAlias())) {
                name = field.getAlias();
            }
                vos.add(new LeaveNameValuePairVo(name, value));
            }

        return vos;
    }
}
