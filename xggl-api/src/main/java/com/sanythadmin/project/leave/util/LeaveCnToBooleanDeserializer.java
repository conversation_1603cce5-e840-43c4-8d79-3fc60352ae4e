package com.sanythadmin.project.leave.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.sanythadmin.project.leave.enums.LeaveYesOrNo;

import java.io.IOException;

/**
 * @since 2025/5/22 13:36
 */
public class LeaveCnToBooleanDeserializer extends JsonDeserializer<Boolean> {

    @Override
    public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String text = p.getText();
        return LeaveYesOrNo.YES.getText().equals(text);
    }
}
