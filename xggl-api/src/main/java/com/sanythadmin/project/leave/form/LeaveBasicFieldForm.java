package com.sanythadmin.project.leave.form;

import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.leave.entity.LeaveBasicField;
import com.sanythadmin.project.leave.entity.LeaveBasicFieldScope;
import com.sanythadmin.project.leave.enums.LeaveYesOrNo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @since 2025/4/30 16:41
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveBasicFieldForm extends LeaveBaseForm {

    public LeaveBasicFieldForm(LeaveBasicField entity) {
        super(entity);
        DictionaryField dicField = entity.getDicField();
        this.fieldEn = dicField.getFieldEn();
        this.fieldZh = dicField.getFieldZh();

        this.alias = entity.getAlias();
        this.sortOrder = entity.getSortOrder();

        List<LeaveBasicFieldScope> scopes = entity.getScopes();
        for (LeaveBasicFieldScope scope : scopes) {
            switch (scope.getType()) {
                case DISPLAY -> this.display = LeaveYesOrNo.YES.getText();
                case CONDITION -> this.condition = LeaveYesOrNo.YES.getText();
            }
        }
    }

//    private LeaveOptionForm studentField;
    /**
     * 学生字段英文名
     */
    private String fieldEn;
    /**
     * 学生字段中文名（不可修改）
     */
    private String fieldZh;
    /**
     * 别名（置空则使用字段中文名）
     */
    private String alias;
    private Integer sortOrder;
    /**
     * 是否显示
     */
    private String display = LeaveYesOrNo.NO.getText();
    /**
     * 是否用于条件判断
     */
    private String condition = LeaveYesOrNo.NO.getText();

}
