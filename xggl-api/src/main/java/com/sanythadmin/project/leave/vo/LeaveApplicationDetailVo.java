package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @since 2025/6/11 10:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveApplicationDetailVo extends LeaveApplicationVo {

    public LeaveApplicationDetailVo(LeaveApplication application) {
        super(application);

        this.fields = LeaveUtil.viewApplicationFields(application);
        this.leaveId = application.getId();
    }

    private List<LeaveApplicationFieldVo> fields;
    private String leaveId;

}
