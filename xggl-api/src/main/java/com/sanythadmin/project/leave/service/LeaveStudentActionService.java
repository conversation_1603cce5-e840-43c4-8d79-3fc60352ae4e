package com.sanythadmin.project.leave.service;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveApplicationFieldForm;
import com.sanythadmin.project.leave.form.LeaveApplicationForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveWithdrawForm;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.vo.LeaveStudentActionDetailVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionLeavePageVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionProjectPageVo;

import java.util.List;

public interface LeaveStudentActionService {

    LeaveStudentActionProjectPageVo optionalProjectPage(String lastId, Integer limit);

    List<LeaveIdTextPairForm> optionalProjectAll();

    List<LeaveApplicationFieldForm> formFieldsForAdd(String projectId, LeaveRequestType requestType);

    void applicationSubmit(LeaveApplicationForm form);

    PageResult<LeaveStudentActionLeavePageVo> leavePage(LeaveQuerySuper query);

    void deleteById(String id);

    void onWithdraw(LeaveWithdrawForm form);

    LeaveStudentActionDetailVo leaveDetail(String leaveId);
}
