package com.sanythadmin.project.leave.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单选项类字段（单选、多选）的选中项
 *
 * @since 2025/4/29 15:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_field_option")
public class LeaveApplicationFieldOption extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "field_id")
    private LeaveApplicationField field;
    private String text;
//    private Boolean selected;

}
