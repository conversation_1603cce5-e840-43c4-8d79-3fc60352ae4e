package com.sanythadmin.project.leave.dto;

import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUserOrgMap;
import lombok.Data;

/**
 * @since 2025/6/11 11:44
 */
@Data
public class LeaveUserDataScopeDto {

    public LeaveUserDataScopeDto(LeaveImmutableUser immutableUser) {
        this.xgh = immutableUser.getXgh();
        this.xm = immutableUser.getXm();
//        this.xyid = immutableUser.getXyid();
//        this.zyid = immutableUser.getZyid();
//        this.bjid = immutableUser.getBjid();
//        this.njid = immutableUser.getNjid();
//        this.pyccid = immutableUser.getPyccid();
        LeaveImmutableUserOrgMap userOrgMap = immutableUser.getUserOrgMap();
        if (userOrgMap != null) {
            this.xyid = userOrgMap.getXyid();
            this.zyid = userOrgMap.getZyid();
            this.bjid = userOrgMap.getBjid();
            this.njid = userOrgMap.getNjid();
            this.pyccid = userOrgMap.getPyccid();
        }
    }

    private String xgh;
    private String xm;

    private String xyid;
    private String zyid;
    private String bjid;
    private String njid;
    private String pyccid;

    private String xymc;
    private String zymc;
    private String bjmc;
    private String njmc;
    private String pyccmc;

}
