package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNodeAssignee;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeAssigneeStatus;
import com.sanythadmin.project.leave.form.LeaveApprovalForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository;
import com.sanythadmin.project.leave.service.LeaveApprovalService;
import com.sanythadmin.project.leave.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 请假模块/审批操作
 *
 * @since 2025/5/14 16:40
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/approval")
public class LeaveApprovalController {

    private final LeaveApprovalService approvalService;
    private final LeaveApplicationProcessNodeAssigneeRepository assigneeRepository;
    private final LeaveApplicationProcessRecordRepository processRecordRepository;

    /**
     * 分页查询
     * 权限标识：leave:approval:list
     */
    @PreAuthorize("hasAuthority('leave:approval:list')")
    @GetMapping("page")
    public PageResult<LeaveApprovalPageVo> page(LeaveQuerySuper query, boolean approved, String xhm) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<LeaveApplicationProcessNodeAssignee> page = assigneeRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("role"), SecurityUtil.getRole()),
                (root, query1, criteriaBuilder) -> {
                    if (approved) {
                        return criteriaBuilder.or(
                                criteriaBuilder.equal(root.get("status"), LeaveProcessNodeAssigneeStatus.APPROVED),
                                criteriaBuilder.equal(root.get("status"), LeaveProcessNodeAssigneeStatus.REJECTED)
                        );
                    } else {
                        return criteriaBuilder.equal(root.get("status"), LeaveProcessNodeAssigneeStatus.PENDING);
                    }
                },
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(xhm)) {
                        // 学号或姓名模糊查询
                        return criteriaBuilder.or(
                                criteriaBuilder.like(root.get("node").get("application").get("user").get("xgh"), "%" + xhm + "%"),
                                criteriaBuilder.like(root.get("node").get("application").get("user").get("xm"), "%" + xhm + "%")
                        );
                    }
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveApprovalPageVo::new).toList(), page.getTotalElements());
    }

    /**
     * 可选审批操作类型
     * 权限标识：leave:approval:operation
     */
    @PreAuthorize("hasAuthority('leave:approval:operation')")
    @GetMapping("optionalAction")
    public List<LeaveIdTextPairForm> optionalAction(String nodeAssigneeId) {
        return approvalService.optionalAction(nodeAssigneeId);
    }

    /**
     * 提交审批
     * 权限标识：leave:approval:operation
     */
    @PreAuthorize("hasAuthority('leave:approval:operation')")
    @PostMapping("submit")
    public void submit(@RequestBody LeaveApprovalForm form) {
        approvalService.submit(form);
    }

    /**
     * 申请记录的学生基本信息
     * 权限标识：leave:approval:list
     */
    @PreAuthorize("hasAuthority('leave:approval:list')")
    @GetMapping("basic_info")
    public List<LeaveNameValuePairVo> basicInfo(String leaveId) {
        return approvalService.basicInfo(leaveId);
    }

    /**
     * 申请详情（请假、销假）
     * 权限标识：leave:approval:list
     */
    @PreAuthorize("hasAuthority('leave:approval:list')")
    @GetMapping("application_detail")
    public LeaveApplicationDetailVo applicationDetail(String applicationId) {
        return approvalService.applicationDetail(applicationId);
    }

    /**
     * 销假记录
     * 权限标识：leave:approval:list
     */
    @PreAuthorize("hasAuthority('leave:approval:list')")
    @GetMapping("cancellation_page")
    public PageResult<LeaveApplicationVo> cancellationPage(LeaveQuerySuper query, String leaveId) {
        return approvalService.cancellationPage(query, leaveId);
    }

    /**
     * 流程信息
     * 权限标识：leave:approval:list
     */
    @PreAuthorize("hasAuthority('leave:approval:list')")
    @GetMapping("flows")
    public List<LeaveApplicationFlowVo> flows(String applicationId) {
        return approvalService.flows(applicationId);
    }
}
