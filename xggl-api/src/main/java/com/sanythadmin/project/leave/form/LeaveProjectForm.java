package com.sanythadmin.project.leave.form;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.enums.LeaveDurationMode;
import com.sanythadmin.project.leave.enums.LeaveTriStateBoolean;
import com.sanythadmin.project.leave.util.LeaveUtil;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @since 2025/4/30 10:58
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveProjectForm extends LeaveBaseForm {

    public LeaveProjectForm(LeaveProject entity) {
        super(entity);
        this.adminName = entity.getAdminName();
        this.displayName = entity.getDisplayName();
        this.openFrom = entity.getOpenFrom();
        this.openTo = entity.getOpenTo();
        this.maxLeaveDays = entity.getMaxLeaveDays();
//        this.cancellationModeObj = entity.getCancellationMode();
//        this.allowExtensionObj = entity.getAllowExtension();
        this.disableNotificationObj = entity.getDisableNotification();
        this.durationModeObj = entity.getDurationMode();

        this.cancellationAfterLeaveEnd = LeaveUtil.booleanToJudgeMark(entity.getCancellationAfterLeaveEnd());
        this.cancellationApplyInArea = LeaveUtil.booleanToJudgeMark(entity.getCancellationApplyInArea());
        this.cancellationNoApproval = LeaveUtil.booleanToJudgeMark(entity.getCancellationNoApproval());
        this.cancellationAutoApproveInArea = LeaveUtil.booleanToJudgeMark(entity.getCancellationAutoApproveInArea());

        this.conditionEnabled = LeaveUtil.booleanToJudgeMark(entity.getConditionEnabled());
        this.conditions = entity.getConditions().stream().map(c -> {
            LeaveProjectConditionForm form = new LeaveProjectConditionForm();
            form.setOperatorId(c.getOperator().name());
            form.setFieldId(c.getField().getId());
            form.setExpectedValue(c.getExpectedValue());
            if (c.getAction() != null) {
                form.setActionId(c.getAction().name());
            }
            form.setMatchAll(c.getMatchAll());
            return form;
        }).toList();
    }

    /**
     * 管理名称
     */
    @NotNull
    private String adminName;
    /**
     * 显示名称
     */
    @NotNull
    private String displayName;
    /**
     * 开放开始时间
     */
    @NotNull
    private LocalDateTime openFrom;
    /**
     * 开放结束时间
     */
    @NotNull
    private LocalDateTime openTo;
    /**
     * 最大请假天数
     */
    @NotNull
    private Integer maxLeaveDays;
//    /**
//     * 销假模式
//     */
//    private LeaveCancellationMode cancellationModeObj = LeaveCancellationMode.DEFAULT;
//    /**
//     * 是否允许续假（三态布尔类型）
//     */
//    private LeaveTriStateBoolean allowExtensionObj = LeaveTriStateBoolean.DEFAULT;
    /**
     * 是否禁用消息通知（三态布尔类型）
     */
    private LeaveTriStateBoolean disableNotificationObj = LeaveTriStateBoolean.DEFAULT;
    /**
     * 请假时长计算模式
     */
    private LeaveDurationMode durationModeObj = LeaveDurationMode.DEFAULT;

    /**
     * 假期结束后自动销假(无需申请)
     */
    private JudgeMark cancellationAfterLeaveEnd;
    /**
     * 仅允许在地理范围内申请销假
     */
    private JudgeMark cancellationApplyInArea;
    /**
     * 无需审批销假
     */
    private JudgeMark cancellationNoApproval;
    /**
     * 在地理范围内销假自动通过
     */
    private JudgeMark cancellationAutoApproveInArea;

    /**
     * 条件启用
     */
    private JudgeMark conditionEnabled;
    /**
     * 条件
     */
    private List<LeaveProjectConditionForm> conditions;

}
