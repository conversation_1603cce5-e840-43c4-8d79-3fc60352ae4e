package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveProcessActionType;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程操作记录表
 *
 * @since 2025/4/29 11:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_process_record")
public class LeaveApplicationProcessRecord extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "application_id")
    private LeaveApplication application;
    @ManyToOne
    @JoinColumn(name = "node_assignee_id")
    private LeaveApplicationProcessNodeAssignee nodeAssignee;
    @ManyToOne
    @JoinColumn(name = "username", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    private LeaveImmutableUser user;
//    private String username;
    @Enumerated(EnumType.STRING)
    private LeaveProcessActionType actionType;
    private String content;

}
