package com.sanythadmin.project.leave.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/5/13 14:01
 */
@NoArgsConstructor
@Data
public class LeaveRecordPageVo {

    public LeaveRecordPageVo(LeaveApplication application) {
        this.application = application;

        this.id = application.getId();
        this.projectName = application.getProject().getDisplayName();
        this.applicant = new LeaveUserVo(application.getUser());
        this.startTime = application.getStartTime();
        this.endTime = application.getEndTime();
        this.approvalStartTime = application.getApprovalStartTime();
        this.approvalEndTime = application.getApprovalEndTime();
        this.approved = application.getApproved();
        this.submitTime = application.getSubmitTime();
        this.createdAt = application.getCreatedAt();
        this.durationDays = application.getDurationDays();
        this.withdrawAt = application.getWithdrawAt();
    }

    @JsonIgnore
    private LeaveApplication application;
    @JsonIgnore
    private LeaveApplication latestCancelledApplication;
    private String id;

    /**
     * 类型名称
     */
    private String projectName;
    /**
     * 申请人
     */
    private LeaveUserVo applicant;
    private LocalDateTime submitTime;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer durationDays;
    /**
     * 审批开始时间
     */
    private LocalDateTime approvalStartTime;
    private LocalDateTime approvalEndTime;
    /**
     * 是否审批通过
     */
    private Boolean approved;
    /**
     * 撤回时间
     */
    private LocalDateTime withdrawAt;

    private LocalDateTime createdAt;

    /**
     * 已销假
     */
    public boolean isCancelled() {
        return LeaveUtil.applicationCancelled(application);
    }

    public String getStatus() {
        return LeaveUtil.getLeaveApplicationStatus(application);
    }

    public String getCancellationApprovalStatus() {
        return LeaveUtil.getCancellationApprovalStatus(latestCancelledApplication);
    }

}
