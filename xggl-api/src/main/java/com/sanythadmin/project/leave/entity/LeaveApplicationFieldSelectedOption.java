package com.sanythadmin.project.leave.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 选中项记录表
 *
 * @since 2025/5/27 10:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_field_selected_option")
public class LeaveApplicationFieldSelectedOption extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "field_id")
    private LeaveApplicationField field;
    private String code;
    private String text;

}
