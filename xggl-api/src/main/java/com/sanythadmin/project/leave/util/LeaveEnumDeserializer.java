package com.sanythadmin.project.leave.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.ContextualDeserializer;

import java.io.IOException;

public class LeaveEnumDeserializer extends JsonDeserializer<LeaveEnumI> implements ContextualDeserializer {
    private Class<? extends LeaveEnumI> enumClass;

    public LeaveEnumDeserializer() {
    }

    public LeaveEnumDeserializer(Class<? extends LeaveEnumI> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public LeaveEnumI deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.readValueAsTree();
        String id;

        if (node.isTextual()) {
            id = node.asText();
        } else if (node.has("id")) {
            id = node.get("id").asText();
        } else {
            throw new IllegalArgumentException("Invalid enum input: " + node);
        }

        for (LeaveEnumI constant : enumClass.getEnumConstants()) {
            if (constant.name().equals(id)) {
                return constant;
            }
        }

        throw new IllegalArgumentException("Unknown id: " + id + " for enum: " + enumClass.getSimpleName());
    }

    @Override
    public JsonDeserializer<?> createContextual(DeserializationContext ctxt, BeanProperty property)
            throws JsonMappingException {
        JavaType type = property.getType();
        Class<?> rawClass = type.getRawClass();
        if (LeaveEnumI.class.isAssignableFrom(rawClass)) {
            @SuppressWarnings("unchecked")
            Class<? extends LeaveEnumI> enumClass = (Class<? extends LeaveEnumI>) rawClass;
            return new LeaveEnumDeserializer(enumClass);
        }
        return this;
    }
}
