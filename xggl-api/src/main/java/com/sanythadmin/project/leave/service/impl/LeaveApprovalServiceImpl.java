package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNode;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNodeAssignee;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessRecord;
import com.sanythadmin.project.leave.enums.LeaveProcessActionType;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeAssigneeStatus;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeStatus;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveApprovalForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository;
import com.sanythadmin.project.leave.repository.LeaveApplicationRepository;
import com.sanythadmin.project.leave.service.LeaveApplicationService;
import com.sanythadmin.project.leave.service.LeaveApprovalService;
import com.sanythadmin.project.leave.service.LeaveCommonService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import com.sanythadmin.project.leave.vo.LeaveApplicationDetailVo;
import com.sanythadmin.project.leave.vo.LeaveApplicationFlowVo;
import com.sanythadmin.project.leave.vo.LeaveApplicationVo;
import com.sanythadmin.project.leave.vo.LeaveNameValuePairVo;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/5/14 17:48
 */
@AllArgsConstructor
@Service
public class LeaveApprovalServiceImpl implements LeaveApprovalService {

    private final LeaveApplicationProcessNodeAssigneeRepository assigneeRepository;
    private final LeaveApplicationProcessRecordRepository recordRepository;
    private final LeaveApplicationProcessNodeRepository nodeRepository;
    private final LeaveApplicationRepository applicationRepository;
    private final LeaveCommonService commonService;
    private final LeaveApplicationService applicationService;

    @Transactional(readOnly = true)
    @Override
    public List<LeaveIdTextPairForm> optionalAction(String nodeAssigneeId) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(nodeAssigneeId), "nodeAssigneeId is required");
        LeaveApplicationProcessNodeAssignee nodeAssignee = assigneeRepository.findById(nodeAssigneeId).orElseThrow(() -> new BusinessException("流程节点不存在"));
        LeaveApplicationProcessNode firstNode = nodeAssignee.getNode().getApplication().getProcessNodes().get(0);
        boolean isStartNode = firstNode == nodeAssignee.getNode();

        List<LeaveProcessActionType> types = new ArrayList<>();
        types.add(LeaveProcessActionType.APPROVED);
        types.add(LeaveProcessActionType.REJECTED);
        if (!isStartNode) {
            types.add(LeaveProcessActionType.ROLLED_BACK_PREVIOUS);
        }
//        types.add(LeaveProcessActionType.ROLLED_BACK_STARTER);
        return types.stream().map(type -> new LeaveIdTextPairForm(type.name(), type.getText())).toList();
    }

    @Transactional
    @Override
    public void submit(LeaveApprovalForm form) {
        LeaveApplicationProcessNodeAssignee assignee = assigneeRepository.findById(form.getNodeAssigneeId()).orElseThrow();
        LeaveUtil.checkTrue(assignee.getStatus() == LeaveProcessNodeAssigneeStatus.PENDING, "当前审批节点不是待审批状态");

        LocalDateTime now = LocalDateTime.now();
        LeaveApplicationProcessNode node = assignee.getNode();
        LeaveApplication application = node.getApplication();
        String username = SecurityUtil.getUsername();

        // 审批开始，设置审批开始时间
        if (application.getApprovalStartTime() == null) {
            application.setApprovalStartTime(now);
            applicationRepository.save(application);
        }

        // 当前角色审批提交时间
        assignee.setSubmitTime(now);
        assignee.setUsername(username);

        switch (form.getActionType()) {
            case APPROVED -> {
                // 当前角色设置
                assignee.setStatus(LeaveProcessNodeAssigneeStatus.APPROVED);

                // 当前节点设置
                node.setStatus(LeaveProcessNodeStatus.APPROVED);
                nodeRepository.save(node);

                // 下一节点设置
                List<LeaveApplicationProcessNode> nextNodes = nodeRepository.findByApplicationAndSequence(application, node.getSequence() + 1);
                if (CollectionUtils.isNotEmpty(nextNodes)) {
                    LeaveApplicationProcessNode nextNode = nextNodes.get(0);
                    nextNode.setStatus(LeaveProcessNodeStatus.PENDING);
                    nextNode.setActivatedAt(now);
                    // 设置节点审批角色待审批
                    for (LeaveApplicationProcessNodeAssignee nextAssignee : nextNode.getAssignees()) {
                        nextAssignee.setStatus(LeaveProcessNodeAssigneeStatus.PENDING);
                    }
                    nodeRepository.save(nextNode);
                } else {
                    // 流程结束，审批通过
                    application.setApprovalEndTime(now);
                    application.setApproved(true);
                    applicationRepository.save(application);

                    // 如果此为销假申请记录
                    if (application.getRequestType() == LeaveRequestType.CANCELLATION) {
                        LeaveApplication originalApplication = application.getOriginalApplication();
                        originalApplication.setCancelledTime(now);
                        applicationRepository.save(originalApplication);
                    }
                }
            }
            case REJECTED -> {
                // 当前角色设置
                assignee.setStatus(LeaveProcessNodeAssigneeStatus.REJECTED);

                // 当前节点设置
                node.setStatus(LeaveProcessNodeStatus.REJECTED);
                nodeRepository.save(node);

                // 流程结束，审批不通过
                application.setApprovalEndTime(now);
                application.setApproved(false);
                applicationRepository.save(application);
            }
            case ROLLED_BACK_PREVIOUS -> {
                // 当前角色设置
                assignee.setStatus(LeaveProcessNodeAssigneeStatus.ROLLED_BACK);

                // 当前节点设置
                node.setStatus(LeaveProcessNodeStatus.ROLLED_BACK);
                nodeRepository.save(node);

                // 上一节点设置
                List<LeaveApplicationProcessNode> previousNodes = nodeRepository.findByApplicationAndSequence(application, node.getSequence() - 1);
                if (CollectionUtils.isNotEmpty(previousNodes)) {
                    LeaveApplicationProcessNode previousNode = previousNodes.get(0);
                    previousNode.setStatus(LeaveProcessNodeStatus.PENDING);
                    previousNode.setActivatedAt(now);
                    // 设置节点审批角色待审批
                    for (LeaveApplicationProcessNodeAssignee previousAssignee : previousNode.getAssignees()) {
                        previousAssignee.setStatus(LeaveProcessNodeAssigneeStatus.PENDING);
                    }
                    nodeRepository.save(previousNode);
                } else {
//                    // 无节点，设置申请人重新提交
//                    application.setNeedResubmit(now);
//                    applicationRepository.save(application);
                    // 无节点，不可退回
                    LeaveUtil.throwException("不可退回申请人");
                }
            }
//            case ROLLED_BACK_STARTER -> {
//                // 当前角色设置
//                assignee.setStatus(LeaveProcessNodeAssigneeStatus.ROLLED_BACK);
//
//                // 当前节点设置
//                node.setStatus(LeaveProcessNodeStatus.ROLLED_BACK);
//                nodeRepository.save(node);
//
//                // 设置申请人重新提交
//                application.setNeedResubmit(now);
//                applicationRepository.save(application);
//            }
            default -> LeaveUtil.throwException("不支持的操作");
        }

        // 节点其他角色状态设置为空
        for (LeaveApplicationProcessNodeAssignee otherAssignee : node.getAssignees()) {
            if (assignee != otherAssignee) {
                otherAssignee.setStatus(null);
            }
        }
        nodeRepository.save(node);

        // 审批记录
        LeaveApplicationProcessRecord record = new LeaveApplicationProcessRecord();
        record.setApplication(application);
        record.setNodeAssignee(assignee);
        LeaveImmutableUser user = new LeaveImmutableUser(username);
        record.setUser(user);
        record.setActionType(form.getActionType());
        record.setContent(form.getContent());
        record.setCreatedAt(now);
        recordRepository.save(record);
    }

    @Transactional(readOnly = true)
    @Override
    public List<LeaveNameValuePairVo> basicInfo(String leaveId) {
        LeaveApplication application = applicationRepository.findById(leaveId).orElseThrow(() -> new BusinessException("未找到数据"));
        String username = application.getUser().getXgh();
        return commonService.basicFieldWithStudentInfo(username);
    }

    @Transactional(readOnly = true)
    @Override
    public LeaveApplicationDetailVo applicationDetail(String applicationId) {
        LeaveApplication application = applicationRepository.findById(applicationId).orElseThrow(() -> new BusinessException("未找到数据"));
        return new LeaveApplicationDetailVo(application);
    }

    @Transactional(readOnly = true)
    @Override
    public List<LeaveApplicationFlowVo> flows(String applicationId) {
        return applicationService.flows(applicationRepository.findById(applicationId).orElseThrow(() -> new BusinessException("未找到数据")));
    }

    @Transactional(readOnly = true)
    @Override
    public PageResult<LeaveApplicationVo> cancellationPage(LeaveQuerySuper query, String leaveId) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(leaveId), "leaveId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());
//        Page<LeaveApplicationProcessRecord> page = processRecordRepository.findAll(Specification.allOf((root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("username"), username)), pageRequest);
        Page<LeaveApplication> page = applicationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("originalApplication").get("id"), leaveId)
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveApplicationVo::new).toList(), page.getTotalElements());
    }
}
