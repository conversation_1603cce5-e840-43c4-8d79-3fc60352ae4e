package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNode;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface LeaveProjectApprovalRuleNodeRepository extends PagingAndSortingRepository<LeaveProjectApprovalRuleNode, String>, CrudRepository<LeaveProjectApprovalRuleNode, String>, JpaSpecificationExecutor<LeaveProjectApprovalRuleNode> {
    void deleteByProjectAndRequestType(LeaveProject project, LeaveRequestType requestType);

    List<LeaveProjectApprovalRuleNode> findByProjectAndRequestTypeAndMinDaysLessThanEqualOrderBySortOrderAscParallelOrderAsc(LeaveProject project, LeaveRequestType requestType, Integer minDaysIsLessThan);

    List<LeaveProjectApprovalRuleNode> findByProjectAndRequestTypeOrderBySortOrderAscParallelOrderAsc(LeaveProject project, LeaveRequestType requestType);
}
