package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.leave.enums.LeaveFormFieldType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 请假申请表单数据
 *
 * @since 2025/4/28 15:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_field")
public class LeaveApplicationField extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "application_id")
    private LeaveApplication application;
    private String name;
    @Enumerated(EnumType.STRING)
    private LeaveFormFieldType type;
    @Column(nullable = false)
    @ColumnDefault("0")
    @NotNull
    private Boolean required;
    private Integer sortOrder;
    private String valueText;
    private Integer valueInteger;
    private Double valueDouble;
    private LocalDateTime valueDatetime;
    private LocalDate valueDate;
    @Lob
    private String valueLongText;
    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveApplicationFieldOption> options;
    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveApplicationFieldFile> files;
    /**
     * 提示占位符
     */
    private String placeholder;
    /**
     * 通用码表类型
     */
    @ManyToOne
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @NotFound(action = NotFoundAction.IGNORE)
    private CodeType codeType;
    @OneToMany(mappedBy = "field", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveApplicationFieldSelectedOption> selectedOptions;

}
