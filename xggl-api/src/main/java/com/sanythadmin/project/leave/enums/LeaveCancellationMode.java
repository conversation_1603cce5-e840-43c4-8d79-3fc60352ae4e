package com.sanythadmin.project.leave.enums;

import com.sanythadmin.project.leave.util.LeaveEnumI;
import lombok.Getter;

/**
 * 销假模式
 *
 * @since 2025/4/25 15:57
 */
@Getter
public enum LeaveCancellationMode implements LeaveEnumI {

    DEFAULT("默认"),
    AUTO_APPROVE("提交即自动通过"),
    AUTO_APPROVE_IN_AREA("提交且在范围内自动通过"),
    NO_CANCELLATION_REQUIRED("假期结束后自动销假(无需申请)"),
    APPLY_AND_APPROVE("需要申请和审批"),
    ;
    private final String text;

    LeaveCancellationMode(String text) {
        this.text = text;
    }
}
