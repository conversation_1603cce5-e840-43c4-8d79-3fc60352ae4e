package com.sanythadmin.project.leave.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class LeaveEnumSerializer extends JsonSerializer<LeaveEnumI> {

    @Override
    public void serialize(LeaveEnumI value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartObject();
        gen.writeStringField("id", value.name());
        gen.writeStringField("text", value.getText());
        gen.writeEndObject();
    }
}
