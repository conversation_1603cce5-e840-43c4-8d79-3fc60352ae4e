package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveDurationMode;
import com.sanythadmin.project.leave.enums.LeaveTriStateBoolean;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目表
 *
 * @since 2025/4/25 11:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project")
public class LeaveProject extends LeaveBaseEntity {

    /**
     * 管理名称
     */
    private String adminName;
    /**
     * 显示名称
     */
    private String displayName;
    private LocalDateTime openFrom;
    private LocalDateTime openTo;
    /**
     * 最大请假天数
     */
    private Integer maxLeaveDays;

//    /**
//     * 是否需要手动销假
//     */
//    private Boolean requireManualCancellation;
//    /**
//     * 申请销假免审批自动通过
//     */
//    private Boolean autoApproveCancellationRequest;
//    /**
//     * 在地理范围内提交销假申请自动通过
//     */
//    private Boolean autoApproveCancellationRequestInArea;
//    /**
//     * 销假模式是否继承全局配置
//     */
//    private Boolean inheritCancellationMode;

//    /**
//     * 销假模式
//     */
//    @Enumerated(EnumType.STRING)
//    private LeaveCancellationMode cancellationMode;
//    /**
//     * 是否允许续假
//     */
//    @Enumerated
//    private LeaveTriStateBoolean allowExtension;
    /**
     * 是否禁用消息通知
     */
    @Enumerated(EnumType.STRING)
    private LeaveTriStateBoolean disableNotification;
    /**
     * 请假时长计算模式
     */
    @Enumerated(EnumType.STRING)
    private LeaveDurationMode durationMode;

    /**
     * 假期结束后自动销假(无需申请)
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean cancellationAfterLeaveEnd;
    /**
     * 仅允许在地理范围内申请销假
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean cancellationApplyInArea;
    /**
     * 无需审批销假
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean cancellationNoApproval;
    /**
     * 在地理范围内销假自动通过
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean cancellationAutoApproveInArea;

    /**
     * 生效条件已启用
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean conditionEnabled;
    /**
     * 生效条件
     */
    @OrderBy("sortOrder")
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveProjectCondition> conditions;

}
