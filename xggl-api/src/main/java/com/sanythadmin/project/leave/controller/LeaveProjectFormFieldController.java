package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.service.CodeTypeService;
import com.sanythadmin.project.leave.entity.LeaveProjectField;
import com.sanythadmin.project.leave.enums.LeaveFormFieldType;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveProjectFieldForm;
import com.sanythadmin.project.leave.query.LeaveProjectFormFieldQuery;
import com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository;
import com.sanythadmin.project.leave.service.LeaveProjectFieldService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 请假模块/类型管理/表单字段管理
 *
 * @since 2025/5/6 10:28
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/project/form_field")
public class LeaveProjectFormFieldController {

    private final LeaveProjectFieldRepository formFieldRepository;
    private final LeaveProjectFieldService formFieldService;
    private final CodeTypeService codeTypeService;

    /**
     * 分页查询
     * 权限标识：leave:project:form_field:list
     */
    @PreAuthorize("hasAuthority('leave:project:form_field:list')")
    @GetMapping("/page")
    public PageResult<LeaveProjectFieldForm> page(LeaveProjectFormFieldQuery query) {
        return formFieldService.page(query);
    }

    @PreAuthorize("hasAuthority('leave:project:form_field:list')")
    @GetMapping("/{id}")
    public LeaveProjectFieldForm get(@PathVariable("id") String id) {
        Optional<LeaveProjectField> item = formFieldRepository.findById(id);
        return new LeaveProjectFieldForm(item.orElseThrow());
    }

    /**
     * 可选字段类型
     */
    @PreAuthorize("hasAuthority('leave:project:form_field:list')")
    @GetMapping("/optionalType")
    public Stream<LeaveIdTextPairForm> optionalType() {
        return Arrays.stream(LeaveFormFieldType.values()).map(
                item -> new LeaveIdTextPairForm(item.name(), item.getText()));
    }

    /**
     * 可选加载数据类型
     */
    @PreAuthorize("hasAuthority('leave:project:form_field:list')")
    @GetMapping("/optionalCodeType")
    public Stream<LeaveIdTextPairForm> optionalCodeType() {
        return codeTypeService.list().stream().map(item -> new LeaveIdTextPairForm(item.getId(), item.getCode() + "-" + item.getName()));
    }

    /**
     * 添加或修改
     * 权限标识：leave:project:form_field:operation
     */
    @PreAuthorize("hasAuthority('leave:project:form_field:operation')")
    @OperationLog(module = "请假模块-类型管理-表单字段管理", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody LeaveProjectFieldForm form) {
        formFieldService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：leave:project:form_field:remove
     */
    @PreAuthorize("hasAuthority('leave:project:form_field:remove')")
    @OperationLog(module = "请假模块-类型管理-表单字段管理", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formFieldRepository.deleteAllById(ids);
    }
}
