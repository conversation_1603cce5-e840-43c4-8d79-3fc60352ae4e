package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveApprovalMode;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 请假申请的流程节点
 *
 * @since 2025/4/29 11:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_process_node")
public class LeaveApplicationProcessNode extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "application_id")
    private LeaveApplication application;
//    @ManyToOne
//    @JoinColumn(name = "role_id")
//    private SysRole role;
    private Integer sequence;
    private String name;
    /**
     * 审批模式
     */
    @Enumerated(EnumType.STRING)
    private LeaveApprovalMode approvalMode;
    /**
     * 必须多少人通过（只有当PARTIAL时生效）
     */
    private String requiredApprovalCount;
    @OneToMany(mappedBy = "node", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveApplicationProcessNodeAssignee> assignees;
    @Enumerated(EnumType.STRING)
    private LeaveProcessNodeStatus status;
    /**
     * 激活时间
     */
    private LocalDateTime activatedAt;
}
