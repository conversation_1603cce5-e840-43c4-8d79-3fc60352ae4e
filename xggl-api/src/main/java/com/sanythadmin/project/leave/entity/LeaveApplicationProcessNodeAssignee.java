package com.sanythadmin.project.leave.entity;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeAssigneeStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 请假申请的流程节点角色表
 *
 * @since 2025/4/29 11:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_process_node_assignee")
public class LeaveApplicationProcessNodeAssignee extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "node_id")
    private LeaveApplicationProcessNode node;
    @ManyToOne
    @JoinColumn(name = "role_id", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    private SysRole role;
    //    @ManyToOne
//    @JoinColumn(name = "username")
//    private UserInfo user;
    private String username;
    @Enumerated(EnumType.STRING)
    private LeaveProcessNodeAssigneeStatus status;
    private LocalDateTime submitTime;

}
