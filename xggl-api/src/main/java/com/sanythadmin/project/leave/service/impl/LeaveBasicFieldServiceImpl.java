package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.leave.entity.LeaveBasicField;
import com.sanythadmin.project.leave.entity.LeaveBasicFieldScope;
import com.sanythadmin.project.leave.enums.LeaveBasicFieldScopeType;
import com.sanythadmin.project.leave.form.LeaveBasicFieldForm;
import com.sanythadmin.project.leave.query.LeaveBasicFieldQuery;
import com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/4/30 17:45
 */
@AllArgsConstructor
@Service
public class LeaveBasicFieldServiceImpl implements com.sanythadmin.project.leave.service.LeaveBasicFieldService {

    private final LeaveBasicFieldRepository basicFieldRepository;
    private final DictionaryFieldService dictionaryFieldService;

    @Transactional(readOnly = true)
    @Override
    public PageResult<LeaveBasicFieldForm> page(LeaveBasicFieldQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(),
                Sort.by(Sort.Order.asc("sortOrder"), Sort.Order.desc("createdAt")));

        Page<LeaveBasicField> page = basicFieldRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getFieldZhLike() != null)
                        return criteriaBuilder.like(root.get("dicField").get("fieldZh"), "%" + query.getFieldZhLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveBasicFieldForm::new).toList(), page.getTotalElements());
    }

    @Transactional(readOnly = true)
    @Override
    public LeaveBasicFieldForm getForm(String id) {
        Optional<LeaveBasicField> item = basicFieldRepository.findById(id);
        return new LeaveBasicFieldForm(item.orElseThrow());
    }

    @Override
    @Transactional
    public void operation(LeaveBasicFieldForm form) {
        LocalDateTime now = LocalDateTime.now();
        LeaveBasicField entity;
        if (form.getId() != null) {
            entity = basicFieldRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(now);
        } else {
            entity = new LeaveBasicField();
            entity.setCreatedAt(now);

            LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getFieldEn()), "字段英文名不能为空");
            DictionaryFieldParam fieldParam = new DictionaryFieldParam();
            fieldParam.setFieldEn(form.getFieldEn());
            fieldParam.setUserType(UserType.STUDENT);
            dictionaryFieldService.queryList(fieldParam).stream().findFirst().ifPresent(entity::setDicField);
            LeaveUtil.checkTrue(entity.getDicField() != null, "学生字段不存在");

            boolean b = basicFieldRepository.existsByDicField(entity.getDicField());
            LeaveUtil.checkTrue(!b, "学生字段不可重复关联");
        }

        entity.setAlias(form.getAlias());
//        entity.setSortOrder(form.getSortOrder());
        if (form.getSortOrder() != null) {
            entity.setSortOrder(form.getSortOrder());
        } else {
            Integer maxSortOrder = basicFieldRepository.findMaxSortOrder();
            entity.setSortOrder(maxSortOrder == null ? 0 : maxSortOrder + 1);
        }

        List<LeaveBasicFieldScope> scopes = entity.getScopes();
        if (scopes == null) {
            scopes = new ArrayList<>();
            entity.setScopes(scopes);
        } else {
            scopes.clear();
        }
        if (LeaveUtil.textToBoolean(form.getDisplay())) {
            LeaveBasicFieldScope scope = new LeaveBasicFieldScope();
            scope.setField(entity);
            scope.setType(LeaveBasicFieldScopeType.DISPLAY);
            scope.setCreatedAt(now);
            scopes.add(scope);
        }
        if (LeaveUtil.textToBoolean(form.getCondition())) {
            LeaveBasicFieldScope scope = new LeaveBasicFieldScope(entity, LeaveBasicFieldScopeType.CONDITION);
            scope.setCreatedAt(now);
            scopes.add(scope);
        }

        basicFieldRepository.save(entity);
    }

}
