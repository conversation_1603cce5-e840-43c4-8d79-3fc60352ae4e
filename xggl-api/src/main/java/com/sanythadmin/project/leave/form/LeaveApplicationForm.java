package com.sanythadmin.project.leave.form;

import com.sanythadmin.project.leave.enums.LeaveRequestType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @since 2025/5/12 15:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveApplicationForm extends LeaveBaseForm {

    private String projectId;
    private LeaveRequestType requestType;
    private String originalId;
    /**
     * yyyy-MM-dd HH:mm
     */
    private String startTime;
    /**
     * yyyy-MM-dd HH:mm
     */
    private String endTime;
    private List<LeaveApplicationFieldForm> fields;
    private String address;
    private Double longitude;
    private Double latitude;

}
