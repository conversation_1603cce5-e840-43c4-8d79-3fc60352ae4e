package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.IgnoreResponseBodyAdvice;
import com.sanythadmin.common.enums.ContentTypeDisposition;
import com.sanythadmin.project.leave.entity.LeaveApplicationFieldFile;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.stream.Stream;

/**
 * 请假模块/通用接口
 *
 * @since 2025/5/22 14:01
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/common")
public class LeaveCommonController {

    private final LeaveApplicationFieldFileRepository applicationFieldFileRepository;

    /**
     * 可选申请类型
     */
    @GetMapping("/optionalRequestType")
    public Stream<LeaveIdTextPairForm> optionalRequestType() {
        return Arrays.stream(LeaveRequestType.values()).map(
                item -> new LeaveIdTextPairForm(item.name(), item.getText()));
    }

    /**
     * 文件下载或预览
     *
     * @param disposition (attachment/inline)
     */
    @IgnoreResponseBodyAdvice
    @RequestMapping(path = "file/{disposition}/{id}", method = RequestMethod.GET)
    public ResponseEntity<ByteArrayResource> file(@PathVariable("disposition") ContentTypeDisposition disposition, @PathVariable("id") String id) {
        LeaveApplicationFieldFile fieldFile = applicationFieldFileRepository.findById(id).orElseThrow(() -> new RuntimeException("文件不存在"));

        ByteArrayResource byteArrayResource = new ByteArrayResource(fieldFile.getContent());
        HttpHeaders httpHeaders = new HttpHeaders();
        switch (fieldFile.getMime()) {
            case "image/png":
                httpHeaders.setContentType(MediaType.IMAGE_PNG);
                break;
            case "image/jpeg":
                httpHeaders.setContentType(MediaType.IMAGE_JPEG);
                break;
            case "image/gif":
                httpHeaders.setContentType(MediaType.IMAGE_GIF);
                break;
            default:
                httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                break;
        }
        httpHeaders.setContentLength(fieldFile.getLength());
        // localDateTime 转 timeinmillis
        httpHeaders.setLastModified(fieldFile.getCreatedAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        if (disposition == ContentTypeDisposition.inline) {
            httpHeaders.setContentDisposition(ContentDisposition.inline().build());
        } else {
            String filename = fieldFile.getFilename();
            httpHeaders.setContentDisposition(ContentDisposition.attachment().filename(URLEncoder.encode(filename, StandardCharsets.UTF_8)).build());
        }

        return new ResponseEntity<>(byteArrayResource, httpHeaders, HttpStatus.OK);
    }
}
