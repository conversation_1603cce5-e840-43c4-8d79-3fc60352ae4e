package com.sanythadmin.project.leave.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanythadmin.project.leave.util.LeaveBooleanToCnSerializer;
import com.sanythadmin.project.leave.util.LeaveCnToBooleanDeserializer;
import lombok.Data;

/**
 * @since 2025/5/22 14:55
 */
@Data
public class LeaveProjectApprovalNodeConditionForm {

    /**
     * 匹配规则
     */
    private LeaveIdTextPairForm operator;
    /**
     * 匹配字段
     */
    private LeaveIdTextPairForm field;
    /**
     * 匹配预期值
     */
    private String expectedValue;
    /**
     * 生效后的策略
     */
    private LeaveIdTextPairForm action;
    /**
     * 是否匹配所有
     */
    @JsonSerialize(using = LeaveBooleanToCnSerializer.class)
    @JsonDeserialize(using = LeaveCnToBooleanDeserializer.class)
    private boolean matchAll;

}
