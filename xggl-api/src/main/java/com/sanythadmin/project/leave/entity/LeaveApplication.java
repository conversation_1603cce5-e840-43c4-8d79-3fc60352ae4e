package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveDurationMode;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 请假申请
 *
 * @since 2025/4/28 14:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application")
public class LeaveApplication extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "PROJECT_ID")
    private LeaveProject project;
    @ManyToOne
    @JoinColumn(name = "username")
    private LeaveImmutableUser user;
    /**
     * YYYY-MM-DD HH:mm
     */
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    /**
     * 审批开始时间
     */
    private LocalDateTime approvalStartTime;
    private LocalDateTime approvalEndTime;
    /**
     * 是否审批通过
     */
    private Boolean approved;
    /**
     * 第几次申请
     */
    private Integer requestSequence;
//    /**
//     * 是否已销假
//     */
//    @Column(nullable = false)
//    @ColumnDefault("0")
//    @NotNull
//    private Boolean cancelled;
    /**
     * 实际销假时间
     */
    private LocalDateTime cancelledTime;
    @Enumerated(value = EnumType.STRING)
    private LeaveRequestType requestType;
    /**
     * 关联的原请假申请
     */
    @ManyToOne
    private LeaveApplication originalApplication;
    /**
     * 提交时的位置信息
     */
    private String address;
    /**
     * 用户提交时的经度
     */
    private Double longitude;
    /**
     * 用户提交时的纬度
     */
    private Double latitude;
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    /**
     * 请假天数计算模式
     */
    private LeaveDurationMode durationMode;
    /**
     * 经过天数
     */
    private Integer durationDays;
//    /**
//     * 是否已被续假过
//     */
//    private Boolean extended;
//    /**
//     * 被续假的时间
//     */
//    private LocalDateTime extendedTime;
    /**
     * 撤回时间
     */
    private LocalDateTime withdrawAt;
    private String withdrawReason;
    /**
     * 需要重新提交
     */
    private LocalDateTime needResubmit;
//    /**
//     * 无需申请销假
//     */
//    private Boolean noCancellationRequired;

    /**
     * 假期结束后自动销假(无需申请)
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean cancellationAfterLeaveEnd;

    @OrderBy("sortOrder")
    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveApplicationField> fields;
    @OrderBy("sequence")
    @OneToMany(mappedBy = "application", cascade = CascadeType.REMOVE)
    private List<LeaveApplicationProcessNode> processNodes;
    @OrderBy("createdAt")
    @OneToMany(mappedBy = "application", cascade = CascadeType.REMOVE)
    private List<LeaveApplicationProcessRecord> processRecords;
//    @OrderBy("createdAt")
    @OneToMany(mappedBy = "originalApplication", cascade = CascadeType.REMOVE)
    private List<LeaveApplication> children;

}
