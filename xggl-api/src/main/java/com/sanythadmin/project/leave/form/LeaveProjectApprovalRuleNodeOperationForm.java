package com.sanythadmin.project.leave.form;

import com.sanythadmin.project.leave.enums.LeaveRequestType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @since 2025/5/7 10:56
 */
@Data
public class LeaveProjectApprovalRuleNodeOperationForm {

    private List<List<LeaveProjectApprovalRuleNodeForm>> nodeGroups;
    /**
     * 请假类型Id
     */
    @NotNull
    private String projectId;
    /**
     * 申请类型
     */
    @NotNull
    private LeaveRequestType requestType;

}
