package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.entity.LeaveProjectField;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface LeaveProjectFieldRepository extends PagingAndSortingRepository<LeaveProjectField, String>, CrudRepository<LeaveProjectField, String>, JpaSpecificationExecutor<LeaveProjectField> {

    List<LeaveProjectField> findByProjectAndRequestTypeOrderBySortOrder(LeaveProject project, LeaveRequestType requestType);

    @Query("select max(sortOrder) from LeaveProjectField")
    Integer findMaxSortOrder();

    List<LeaveProjectField> findByProjectIdAndRequestTypeOrderBySortOrder(String projectId, LeaveRequestType requestType);
}
