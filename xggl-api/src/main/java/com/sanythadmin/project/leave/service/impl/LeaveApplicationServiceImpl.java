package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNode;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNodeAssignee;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessRecord;
import com.sanythadmin.project.leave.enums.LeaveProcessNodeStatus;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository;
import com.sanythadmin.project.leave.repository.LeaveApplicationRepository;
import com.sanythadmin.project.leave.service.LeaveApplicationService;
import com.sanythadmin.project.leave.vo.LeaveApplicationFlowVo;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @since 2025/6/11 13:47
 */
@AllArgsConstructor
@Service
public class LeaveApplicationServiceImpl implements LeaveApplicationService {

    private final LeaveApplicationProcessNodeRepository nodeRepository;
    private final LeaveApplicationRepository applicationRepository;

    @Transactional(readOnly = true)
    @Override
    public List<LeaveApplicationFlowVo> flows(LeaveApplication application) {
        List<LeaveApplicationProcessRecord> processRecords = application.getProcessRecords();
        List<LeaveApplicationFlowVo> flows = new ArrayList<>(processRecords.stream().map(record -> {
            LeaveApplicationFlowVo flow = new LeaveApplicationFlowVo();
            flow.setId(record.getId());
            flow.setCreatedAtText(record.getCreatedAt());
            LeaveApplicationProcessNodeAssignee nodeAssignee = record.getNodeAssignee();
            if (nodeAssignee == null) {
                flow.setRoleName("发起人");
            } else {
                flow.setRoleName(nodeAssignee.getRole().getName());
            }

            flow.setUser(new LeaveIdTextPairForm(record.getUser().getXgh(), record.getUser().getXm()));
            flow.setAction(new LeaveIdTextPairForm(record.getActionType().name(), record.getActionType().getText()));
            flow.setContent(record.getContent());
            return flow;
        }).toList());

        if (application.getApprovalEndTime() != null || application.getWithdrawAt() != null) {
            return flows;
        }

        Optional<LeaveApplicationProcessNode> optionalLeaveApplicationProcessNode = nodeRepository.findFirstByApplicationAndStatus(application, LeaveProcessNodeStatus.PENDING);
        LeaveApplicationFlowVo nextFlow = new LeaveApplicationFlowVo();
        nextFlow.setRoleName(optionalLeaveApplicationProcessNode.map(
                leaveApplicationProcessNode -> leaveApplicationProcessNode.getAssignees().stream().map(
                        LeaveApplicationProcessNodeAssignee::getRole).map(
                        SysRole::getName).collect(Collectors.joining("|"))).orElse("发起人"));
        flows.add(nextFlow);

        return flows;
    }

    @Transactional(readOnly = true)
    @Override
    public Map<LeaveApplication, LeaveApplication> getLatestChildMap(Page<LeaveApplication> page){
        List<String> parentIds = page.getContent().stream()
                .map(LeaveApplication::getId)
                .toList();

        List<LeaveApplication> latestChildren = applicationRepository.findLatestChildrenByParentIds(parentIds);

        return latestChildren.stream()
                .collect(Collectors.toMap(LeaveApplication::getOriginalApplication, Function.identity()));
    }
}
