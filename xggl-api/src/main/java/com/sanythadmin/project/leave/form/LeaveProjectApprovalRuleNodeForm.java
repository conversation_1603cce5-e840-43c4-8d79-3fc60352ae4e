package com.sanythadmin.project.leave.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNode;
import com.sanythadmin.project.leave.entity.LeaveProjectApprovalRuleNodeRole;
import com.sanythadmin.project.leave.util.LeaveBooleanToCnSerializer;
import com.sanythadmin.project.leave.util.LeaveCnToBooleanDeserializer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请假项目审批流程规则节点FORM
 *
 * @since 2025/5/7 8:58
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveProjectApprovalRuleNodeForm extends LeaveBaseForm {

    public LeaveProjectApprovalRuleNodeForm(LeaveProjectApprovalRuleNode node) {
        super(node);
//        this.projectId = node.getProject().getId();
//        this.requestType = node.getRequestType();
        this.name = node.getName();
//        this.parallelOrder = node.getParallelOrder();
        this.minDays = node.getMinDays();
        this.roles = node.getRoles().stream().map(LeaveProjectApprovalRuleNodeRole::getRole)
                .map(r -> new LeaveIdTextPairForm(r.getId(), r.getName())).toList();
        this.conditionEnabled = node.getConditionEnabled();
        this.conditions = node.getConditions().stream().map(c -> {
            LeaveProjectApprovalNodeConditionForm form = new LeaveProjectApprovalNodeConditionForm();
            if (c.getOperator() != null) {
                form.setOperator(new LeaveIdTextPairForm(c.getOperator().name(), c.getOperator().getText()));
            }
            if (c.getField() != null) {
                form.setField(new LeaveIdTextPairForm(c.getField().getId(), c.getField().getName()));
            }
            form.setExpectedValue(c.getExpectedValue());
            if (c.getAction() != null) {
                form.setAction(new LeaveIdTextPairForm(c.getAction().name(), c.getAction().getText()));
            }
            form.setMatchAll(c.getMatchAll());
            return form;
        }).toList();
    }

    //    /**
//     * 类型ID
//     */
//    private String projectId;
//    /**
//     * 申请类型
//     */
//    private LeaveRequestType requestType;
    @NotNull
    private String name;
//    /**
//     * 并行的匹配顺序
//     */
//    private Integer parallelOrder = 0;
    /**
     * 最小请假天数（含）才匹配此流程规则；单位：天
     */
    private Integer minDays = 0;
    /**
     * 关联角色
     */
    @NotNull
    private List<LeaveIdTextPairForm> roles;
//    /**
//     * 审批模式
//     */
//    private LeaveApprovalMode approvalMode;
//    /**
//     * 必须多少人通过（只有当PARTIAL时生效）
//     */
//    private Integer requiredApprovalCount;
    /**
     * 条件启用
     */
    @JsonSerialize(using = LeaveBooleanToCnSerializer.class)
    @JsonDeserialize(using = LeaveCnToBooleanDeserializer.class)
    private boolean conditionEnabled;
    /**
     * 条件
     */
    private List<LeaveProjectApprovalNodeConditionForm> conditions;
}
