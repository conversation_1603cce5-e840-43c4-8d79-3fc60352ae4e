package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.enums.*;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveProjectForm;
import com.sanythadmin.project.leave.query.LeaveProjectQuery;
import com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.service.LeaveProjectService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 请假模块/类型管理
 *
 * @since 2025/4/6 18:46
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/project")
public class LeaveProjectController {

    private final LeaveProjectRepository projectRepository;
    private final LeaveProjectService projectService;
    private final LeaveBasicFieldRepository basicFieldRepository;

    /**
     * 分页查询
     * 权限标识：leave:project:list
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/page")
    public PageResult<LeaveProjectForm> page(LeaveProjectQuery query) {
        return projectService.page(query);
    }

    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/{id}")
    public LeaveProjectForm get(@PathVariable("id") String id) {
        Optional<LeaveProject> item = projectRepository.findById(id);
        return new LeaveProjectForm(item.orElseThrow());
    }

    /**
     * 可选销假模式
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalCancellationMode")
    public List<LeaveIdTextPairForm> optionalCancellationMode() {
        return Arrays.stream(LeaveCancellationMode.values()).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        }).toList();
    }

    /**
     * 可选请假时长计算模式
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalDurationMode")
    public List<LeaveIdTextPairForm> optionalDurationMode() {
        return Arrays.stream(LeaveDurationMode.values()).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        }).toList();
    }

    /**
     * 可选的三态布尔类型（是或否）
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalTriStateBoolean")
    public List<LeaveIdTextPairForm> optionalTriStateBoolean() {
        return Arrays.stream(LeaveTriStateBoolean.values()).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        }).toList();
    }

    /**
     * 条件的可选的匹配规则
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalConditionOperator")
    public List<LeaveIdTextPairForm> optionalConditionOperator() {
        return Stream.of(LeaveConditionOperator.EQUAL, LeaveConditionOperator.NOT_EQUAL).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        }).toList();
    }

    /**
     * 条件的可选匹配字段
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalConditionField")
    public Stream<LeaveIdTextPairForm> optionalConditionField() {
        return basicFieldRepository.findOrderBySortOrder().stream().map(field -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(field.getId());
            DictionaryField dicField = field.getDicField();
            form.setText(dicField.getFieldEn() + "-" + dicField.getFieldZh());
            return form;
        });
    }

    /**
     * 条件的可选策略
     */
    @PreAuthorize("hasAuthority('leave:project:list')")
    @GetMapping("/optionalConditionAction")
    public Stream<LeaveIdTextPairForm> optionalConditionAction() {
        return Arrays.stream(LeavePolicyAction.values()).map(mode -> {
            LeaveIdTextPairForm form = new LeaveIdTextPairForm();
            form.setId(mode.name());
            form.setText(mode.getText());
            return form;
        });
    }

    /**
     * 添加或修改
     * 权限标识：leave:project:operation
     */
    @PreAuthorize("hasAuthority('leave:project:operation')")
    @OperationLog(module = "请假模块-类型管理", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody LeaveProjectForm form) {
        projectService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：leave:project:remove
     */
    @PreAuthorize("hasAuthority('leave:project:remove')")
    @OperationLog(module = "请假模块-类型管理", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        projectRepository.deleteAllById(ids);
    }
}
