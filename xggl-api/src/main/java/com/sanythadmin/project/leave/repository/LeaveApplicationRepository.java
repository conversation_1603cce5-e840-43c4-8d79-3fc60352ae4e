package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface LeaveApplicationRepository extends PagingAndSortingRepository<LeaveApplication, String>, CrudRepository<LeaveApplication, String>, JpaSpecificationExecutor<LeaveApplication> {

    @Query("""
            SELECT e FROM LeaveApplication e
            WHERE e.originalApplication.id IN :parentIds
            AND  e.createdAt = (
                    SELECT MAX(e2.createdAt) FROM LeaveApplication e2
                    WHERE e2.originalApplication = e.originalApplication
                )
            """)
    List<LeaveApplication> findLatestChildrenByParentIds(@Param("parentIds") List<String> parentIds);

    Optional<LeaveApplication> findByIdAndUserXgh(String id, String userXgh);
}
