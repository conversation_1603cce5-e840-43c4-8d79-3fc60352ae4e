package com.sanythadmin.project.leave.entity;


import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 销假无需审批的地理范围
 *
 * @since 2025/4/27 13:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_auto_cancel_location")
public class LeaveProjectAutoCancelLocation extends LeaveBaseEntity {

   @ManyToOne
   @JoinColumn(name = "PROJECT_ID")
   private LeaveProject project;
   private String name;
   private Double latitude;
   private Double longitude;
   private Integer radius;
   /**
    * 详细信息
    */
   private String detail;

}
