package com.sanythadmin.project.leave.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanythadmin.project.leave.entity.LeaveProjectField;
import com.sanythadmin.project.leave.entity.LeaveProjectFieldOption;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.util.LeaveBooleanToCnSerializer;
import com.sanythadmin.project.leave.util.LeaveCnToBooleanDeserializer;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/5/6 10:32
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveProjectFieldForm extends LeaveBaseForm {

    public LeaveProjectFieldForm(LeaveProjectField entity) {
        super(entity);

        this.name = entity.getName();
        this.type = new LeaveIdTextPairForm(entity.getType().name(), entity.getType().getText());
        this.options = entity.getOptions().stream().map(LeaveProjectFieldOption::getText).toArray(String[]::new);
        this.required = entity.getRequired();
        this.sortOrder = entity.getSortOrder();
        this.placeholder = entity.getPlaceholder();
        this.codeType = entity.getCodeType() == null ? null : new LeaveIdTextPairForm(entity.getCodeType().getId(), entity.getCodeType().getName());
    }

    /**
     * 请假类型ID
     */
    @NotNull
    private String projectId;
    /**
     * 申请类型
     */
    @NotNull
    private LeaveRequestType requestType;
    /**
     * 字段名
     */
    @NotNull
    private String name;
    /**
     * 字段类型
     */
    @NotNull
    private LeaveIdTextPairForm type;
    /**
     * 可选项
     */
    private String[] options;
    /**
     * 是否必填
     */
    @JsonSerialize(using = LeaveBooleanToCnSerializer.class)
    @JsonDeserialize(using = LeaveCnToBooleanDeserializer.class)
    private boolean required;
    private Integer sortOrder;
    /**
     * 提示占位符，用户未输入前显示
     */
    private String placeholder;
    /**
     * 加载数据类型
     */
    private LeaveIdTextPairForm codeType;
}
