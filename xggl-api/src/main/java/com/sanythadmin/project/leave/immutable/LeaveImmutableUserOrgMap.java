package com.sanythadmin.project.leave.immutable;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Immutable;

/**
 * @since 2025/7/20 17:10
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(exclude = "user")
@Immutable
@Entity
@Table(name = "SYT_USER_ORG_MAP")
public class LeaveImmutableUserOrgMap {

    @Id
    private String id;
    private String pyccid;
    private String njid;
    private String xyid;
    private String zyid;
    private String bjid;
    @OneToOne
    @JoinColumn(name = "XGH")
    private LeaveImmutableUser user;

}
