package com.sanythadmin.project.leave.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程节点角色状态
 *
 * @since 2025/4/29 11:54
 */
@AllArgsConstructor
@Getter
public enum LeaveProcessNodeAssigneeStatus {

    PENDING("待审批"),
    APPROVED("已通过"),
    REJECTED("不同意"),
    ROLLED_BACK("未审批即被回退"),
    SKIPPED("由于其他人已审批完成而跳过"),//（仅用于“或签”）
//    RECALLED("已撤回(审批人撤回自己的操作)"),
    ;
    private final String text;
}
