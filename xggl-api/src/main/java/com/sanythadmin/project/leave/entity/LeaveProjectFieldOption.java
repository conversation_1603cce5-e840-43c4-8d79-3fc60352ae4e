package com.sanythadmin.project.leave.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请假项目表单字段可选项
 *
 * @since 2025/4/29 15:17
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_field_option")
public class LeaveProjectFieldOption extends LeaveBaseEntity {

    public LeaveProjectFieldOption(LeaveProjectField field, String text) {
        this.field = field;
        this.text = text;
    }

    @ManyToOne
    @JoinColumn(name = "field_id")
    private LeaveProjectField field;
    private String text;

}
