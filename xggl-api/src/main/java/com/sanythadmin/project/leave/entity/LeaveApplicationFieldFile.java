package com.sanythadmin.project.leave.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 请假申请表单数据-文件
 *
 * @since 2025/4/29 10:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_application_field_file")
public class LeaveApplicationFieldFile extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "field_id")
    private LeaveApplicationField field;
    private String filename;
    private Long length;
    private String mime;
    private String sha1;
    @Lob
    private byte[] content;

}
