package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.LeaveApplicationRepository;
import com.sanythadmin.project.leave.service.LeaveApplicationService;
import com.sanythadmin.project.leave.service.LeaveCommonService;
import com.sanythadmin.project.leave.service.LeaveRecordService;
import com.sanythadmin.project.leave.vo.LeaveRecordDetailVo;
import com.sanythadmin.project.leave.vo.LeaveRecordPageVo;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 请假模块/汇总记录
 *
 * @since 2025/7/22 10:12
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/record")
public class LeaveRecordController {

    private final LeaveRecordService leaveRecordService;
    private final LeaveCommonService commonService;
    private final LeaveApplicationRepository applicationRepository;
    private final LeaveApplicationService applicationService;

    /**
     * 请假申请列表
     * 权限标识：leave:record:list
     */
    @PreAuthorize("hasAuthority('leave:record:list')")
    @GetMapping("leave_page")
    public PageResult<LeaveRecordPageVo> leavePage(LeaveQuerySuper query, String xhm) {
        return leaveRecordService.leavePage(query, xhm);
    }

    /**
     * 请假申请详情
     * 权限标识：leave:record:list
     */
    @PreAuthorize("hasAuthority('leave:record:list')")
    @GetMapping("leave_detail")
    public LeaveRecordDetailVo leaveDetail(@RequestParam String leaveId) {
        return leaveRecordService.leaveDetail(leaveId);
    }

}
