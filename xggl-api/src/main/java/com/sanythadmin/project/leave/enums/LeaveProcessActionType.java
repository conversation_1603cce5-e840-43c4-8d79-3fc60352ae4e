package com.sanythadmin.project.leave.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程记录操作类型
 *
 * @since 2025/4/29 11:51
 */
@AllArgsConstructor
@Getter
public enum LeaveProcessActionType {

    APPROVED("同意", true),
    REJECTED("不同意", true),
    ROLLED_BACK_PREVIOUS("回退至上一审批人", true),
    ROLLED_BACK_STARTER("回退至发起人", true),
    /**
     * （此为系统生成记录）
     */
    RESUBMITTED("发起人重新提交", false),
    /**
     * （此为系统生成记录）
     */
    WITHDRAWN("发起人撤回", false),
    ;
    private final String text;
    private final boolean approval;

}
