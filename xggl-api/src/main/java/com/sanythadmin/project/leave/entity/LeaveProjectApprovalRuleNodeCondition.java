package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveConditionOperator;
import com.sanythadmin.project.leave.enums.LeavePolicyAction;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

/**
 * 请假项目审批规则节点条件
 *
 * @since 2025/4/27 16:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_approval_rule_node_condition")
public class LeaveProjectApprovalRuleNodeCondition extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "NODE_ID")
    private LeaveProjectApprovalRuleNode node;
    @ManyToOne
    @JoinColumn(name = "FIELD_ID")
    private LeaveProjectField field;
    @Enumerated(EnumType.STRING)
    private LeaveConditionOperator operator;
    private String expectedValue;
    private Integer sortOrder;
    /**
     * 策略
     */
    private LeavePolicyAction action;
    /**
     * 是否匹配所有
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @NotNull
    private Boolean matchAll;

}
