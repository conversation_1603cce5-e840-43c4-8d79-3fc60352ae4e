package com.sanythadmin.project.leave.service;

import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeForm;
import com.sanythadmin.project.leave.form.LeaveProjectApprovalRuleNodeOperationForm;
import com.sanythadmin.project.leave.query.LeaveProjectApprovalRuleNodeQuery;

import java.util.List;

public interface LeaveProjectApprovalRuleNodeService {
    List<List<LeaveProjectApprovalRuleNodeForm>> list(LeaveProjectApprovalRuleNodeQuery query);

    void operation(LeaveProjectApprovalRuleNodeOperationForm operationForm);
}
