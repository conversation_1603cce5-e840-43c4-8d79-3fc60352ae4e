package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNodeAssignee;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @since 2025/5/15 9:42
 */
@NoArgsConstructor
@Data
public class LeaveApprovalPageVo {

    public LeaveApprovalPageVo(LeaveApplicationProcessNodeAssignee entity) {
        this.nodeAssigneeId = entity.getId();

        LeaveApplication application1 = entity.getNode().getApplication();
        this.requestType = application1.getRequestType();
        if (application1.getRequestType() == LeaveRequestType.LEAVE) {
            this.leave = new LeaveApplicationVo(application1);
            this.leaveId = application1.getId();
        }else if (application1.getRequestType() == LeaveRequestType.CANCELLATION) {
            this.leave = new LeaveApplicationVo(application1.getOriginalApplication());
            this.leaveId = application1.getOriginalApplication().getId();
        }
    }

    private String nodeAssigneeId;
    // 请假信息
    private LeaveApplicationVo leave;
    // 请假Id
    private String leaveId;
    private LeaveRequestType requestType;

}
