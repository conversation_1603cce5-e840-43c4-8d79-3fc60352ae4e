package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.query.LeaveApprovalQuery;
import com.sanythadmin.project.leave.repository.LeaveApplicationRepository;
import com.sanythadmin.project.leave.vo.LeaveApplicationVo;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 请假模块/申请记录
 *
 * @since 2025/5/17 13:39
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/application")
public class LeaveApplicationController {

    private final LeaveApplicationRepository applicationRepository;

    /**
     * 分页查询
     * 权限标识：leave:application:list
     */
    @PreAuthorize("hasAuthority('leave:application:list')")
    @GetMapping("/page")
    public PageResult<LeaveApplicationVo> page(LeaveApprovalQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<LeaveApplication> page = applicationRepository.findAll(Specification.allOf(), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveApplicationVo::new).toList(), page.getTotalElements());
    }

    /**
     * 批量删除
     * 权限标识：leave:application:remove
     */
    @PreAuthorize("hasAuthority('leave:application:remove')")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        applicationRepository.deleteAllById(ids);
    }

    /**
     * 清空
     * 权限标识：leave:application:clear
     */
    @PreAuthorize("hasAuthority('leave:application:clear')")
    @PostMapping("/clear")
    public void clear() {
        applicationRepository.deleteAll();
    }
}
