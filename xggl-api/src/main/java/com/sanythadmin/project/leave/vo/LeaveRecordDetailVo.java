package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @since 2025/7/23 08:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveRecordDetailVo extends LeaveRecordPageVo {

    public LeaveRecordDetailVo(LeaveApplication application) {
        super(application);
    }

    /**
     * 学生基本信息
     */
    private List<LeaveNameValuePairVo> basicInfo;
    /**
     * 申请字段
     */
    private List<LeaveApplicationFieldVo> applicationFields;
    /**
     * 流程信息
     */
    private List<LeaveApplicationFlowVo> flows;
    /**
     * 销假记录
     */
    private List<LeaveRecordCancellationVo> cancellations;

}
