package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.LeaveApplicationRepository;
import com.sanythadmin.project.leave.service.LeaveApplicationService;
import com.sanythadmin.project.leave.service.LeaveCommonService;
import com.sanythadmin.project.leave.service.LeaveRecordService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import com.sanythadmin.project.leave.vo.LeaveRecordCancellationVo;
import com.sanythadmin.project.leave.vo.LeaveRecordDetailVo;
import com.sanythadmin.project.leave.vo.LeaveRecordPageVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @since 2025/7/22 10:27
 */
@AllArgsConstructor
@Service
public class LeaveRecordServiceImpl implements LeaveRecordService {

    private final LeaveApplicationRepository applicationRepository;
    private final LeaveCommonService commonService;
    private final LeaveApplicationService applicationService;

    @Transactional(readOnly = true)
    @Override
    public PageResult<LeaveRecordPageVo> leavePage(LeaveQuerySuper query, String xhm) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<LeaveApplication> page = applicationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("requestType"), LeaveRequestType.LEAVE),
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.isNotEmpty(xhm)) {
                        return criteriaBuilder.or(
                                criteriaBuilder.like(root.get("user").get("xgh"), "%" + xhm + "%"),
                                criteriaBuilder.like(root.get("user").get("xm"), "%" + xhm + "%")
                        );
                    }
                    return null;
                }
        ), pageRequest);

        Map<LeaveApplication, LeaveApplication> latestChildMap = applicationService.getLatestChildMap(page);
        List<LeaveRecordPageVo> results = page.getContent().stream()
                .map(parent -> {
                    LeaveRecordPageVo vo = new LeaveRecordPageVo(parent);
                    vo.setLatestCancelledApplication(latestChildMap.get(parent));
                    return vo;
                })
                .toList();

        return new PageResult<>(results, page.getTotalElements());
    }

    @Transactional(readOnly = true)
    @Override
    public LeaveRecordDetailVo leaveDetail(String leaveId) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(leaveId), "leaveId is required");
        LeaveApplication application = applicationRepository.findById(leaveId).orElseThrow(() -> new BusinessException("未找到数据"));
        LeaveRecordDetailVo detailVo = new LeaveRecordDetailVo(application);
        detailVo.setBasicInfo(commonService.basicFieldWithStudentInfo(application.getUser().getXgh()));
        detailVo.setApplicationFields(LeaveUtil.viewApplicationFields(application));
        detailVo.setFlows(applicationService.flows(application));

        // 销假记录
        List<LeaveApplication> cancellationList = applicationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("originalApplication").get("id"), leaveId),
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("requestType"), LeaveRequestType.CANCELLATION)
        ), Sort.by("createdAt").descending());
        detailVo.setCancellations(cancellationList.stream().map(item -> {
            LeaveRecordCancellationVo vo = new LeaveRecordCancellationVo(item);
            vo.setApplicationFields(LeaveUtil.viewApplicationFields(item));
            vo.setFlows(applicationService.flows(item));
            return vo;
        }).toList());

        return detailVo;
    }

}
