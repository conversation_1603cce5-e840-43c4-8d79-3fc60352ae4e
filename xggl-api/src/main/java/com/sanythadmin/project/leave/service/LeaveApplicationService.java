package com.sanythadmin.project.leave.service;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.vo.LeaveApplicationFlowVo;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface LeaveApplicationService {
    List<LeaveApplicationFlowVo> flows(LeaveApplication application);

    Map<LeaveApplication, LeaveApplication> getLatestChildMap(Page<LeaveApplication> page);
}
