package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveApprovalMode;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

import java.util.List;

/**
 * 请假项目审批流程规则节点
 *
 * @since 2025/4/27 14:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project_approval_rule_node")
public class LeaveProjectApprovalRuleNode extends LeaveBaseEntity {

    @ManyToOne
    @JoinColumn(name = "PROJECT_ID")
    private LeaveProject project;
    @Enumerated(EnumType.STRING)
    private LeaveRequestType requestType;
    private String name;
    private Integer sortOrder;
    /**
     * 并行的子顺序
     */
    private Integer parallelOrder;
    /**
     * 最小请假天数（含）才匹配此流程规则；单位：天
     */
    private Integer minDays;
//    @ManyToOne
//    @JoinColumn(name = "PRE_NODE_ID")
//    private LeaveProjectApprovalRuleNode preNode;
//    @ManyToOne
//    @JoinColumn(name = "NEXT_NODE_ID")
//    private LeaveProjectApprovalRuleNode nextNode;
    @OneToMany(mappedBy = "node", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveProjectApprovalRuleNodeRole> roles;
    /**
     * 审批模式
     */
    @Enumerated(EnumType.STRING)
    private LeaveApprovalMode approvalMode;
    /**
     * 必须多少人通过（只有当PARTIAL时生效）
     */
    private Integer requiredApprovalCount;
    /**
     * 条件已启用
     */
    @Column(nullable = false)
    @ColumnDefault("0")
    @NotNull
    private Boolean conditionEnabled;
    /**
     * 条件
     */
    @OrderBy("sortOrder")
    @OneToMany(mappedBy = "node", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveProjectApprovalRuleNodeCondition> conditions;
}
