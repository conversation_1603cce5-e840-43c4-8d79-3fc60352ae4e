package com.sanythadmin.project.leave.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sanythadmin.project.leave.enums.LeaveYesOrNo;

import java.io.IOException;

/**
 * @since 2025/5/22 13:34
 */
public class LeaveBooleanToCnSerializer extends JsonSerializer<Boolean> {

    @Override
    public void serialize(Boolean value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(Boolean.TRUE.equals(value) ? LeaveYesOrNo.YES.getText() : LeaveYesOrNo.NO.getText());
    }
}
