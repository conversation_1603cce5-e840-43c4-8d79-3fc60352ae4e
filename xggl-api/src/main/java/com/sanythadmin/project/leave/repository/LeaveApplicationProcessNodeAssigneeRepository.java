package com.sanythadmin.project.leave.repository;

import com.sanythadmin.project.leave.entity.LeaveApplicationProcessNodeAssignee;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface LeaveApplicationProcessNodeAssigneeRepository extends PagingAndSortingRepository<LeaveApplicationProcessNodeAssignee, String>, CrudRepository<LeaveApplicationProcessNodeAssignee, String>, JpaSpecificationExecutor<LeaveApplicationProcessNodeAssignee> {
}
