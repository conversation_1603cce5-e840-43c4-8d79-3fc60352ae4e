package com.sanythadmin.project.leave.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.checkin.entity.CheckinAddress;
import com.sanythadmin.project.checkin.param.CheckinAddressParam;
import com.sanythadmin.project.checkin.service.CheckinAddressRealService;
import com.sanythadmin.project.checkin.service.CheckinAddressService;
import com.sanythadmin.project.leave.entity.LeaveProjectAutoCancelLocation;
import com.sanythadmin.project.leave.form.LeaveProjectAutoCancelLocationForm;
import com.sanythadmin.project.leave.query.LeaveProjectAutoCancelLocationQuery;
import com.sanythadmin.project.leave.repository.LeaveProjectAutoCancelLocationRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.service.LeaveProjectAutoCancelLocationService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 请假模块/类型管理/销假无需审批的地理范围
 *
 * @since 2025/4/6 18:46
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/leave/project/auto_cancel_location")
public class LeaveProjectAutoCancelLocationController {

    private final LeaveProjectRepository projectRepository;
    private final LeaveProjectAutoCancelLocationRepository locationRepository;
    private final CheckinAddressService checkinAddressService;
    private final CheckinAddressRealService checkinAddressRealService;
    private final LeaveProjectAutoCancelLocationService locationService;

    /**
     * 分页查询
     * 权限标识：leave:project:auto_cancel_location:list
     */
    @PreAuthorize("hasAuthority('leave:project:auto_cancel_location:list')")
    @GetMapping("/page")
    public PageResult<LeaveProjectAutoCancelLocationForm> page(LeaveProjectAutoCancelLocationQuery query) {
        LeaveUtil.checkTrue(StringUtils.isNotBlank(query.getProjectId()), "projectId is null");
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<LeaveProjectAutoCancelLocation> page = locationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("project").get("id"), query.getProjectId()),
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveProjectAutoCancelLocationForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('leave:project:auto_cancel_location:list')")
    @GetMapping("/{id}")
    public LeaveProjectAutoCancelLocationForm get(@PathVariable("id") String id) {
        Optional<LeaveProjectAutoCancelLocation> item = locationRepository.findById(id);
        return new LeaveProjectAutoCancelLocationForm(item.orElseThrow());
    }

    /**
     * 可选预设地址
     * 权限标识：leave:project:auto_cancel_location:list
     */
    @PreAuthorize("hasAuthority('leave:project:auto_cancel_location:list')")
    @GetMapping("selectablePresetAddressPage")
    public PageResult<CheckinAddress> selectablePresetAddressPage(CheckinAddressParam param) {
//        CheckinAddressParam param = new CheckinAddressParam();
//        param.setPage((long) query.getPage());
//        param.setLimit((long) query.getLimit());
//        param.setStatus(State.ENABLED);
//        param.setType("poi");
        return checkinAddressRealService.page(param);
    }

    /**
     * 添加或修改
     * 权限标识：leave:project:auto_cancel_location:operation
     */
    @PreAuthorize("hasAuthority('leave:project:auto_cancel_location:operation')")
    @OperationLog(module = "请假模块-类型管理-销假无需审批的地理范围", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody LeaveProjectAutoCancelLocationForm form) {
        locationService.operation(form);
    }

    /**
     * 批量删除
     * 权限标识：leave:project:auto_cancel_location:remove
     */
    @PreAuthorize("hasAuthority('leave:project:auto_cancel_location:remove')")
    @OperationLog(module = "请假模块-类型管理-销假无需审批的地理范围", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        locationRepository.deleteAllById(ids);
    }
}
