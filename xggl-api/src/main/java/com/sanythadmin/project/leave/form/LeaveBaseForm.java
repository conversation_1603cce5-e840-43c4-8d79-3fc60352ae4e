package com.sanythadmin.project.leave.form;

import com.sanythadmin.project.leave.entity.LeaveBaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/4/30 10:47
 */
@Data
@NoArgsConstructor
public abstract class LeaveBaseForm {

    public LeaveBaseForm(LeaveBaseEntity baseEntity) {
        this.id = baseEntity.getId();
        this.createdAtText = baseEntity.getCreatedAt();
        this.updatedAtText = baseEntity.getUpdatedAt();
    }

    private String id;
    private LocalDateTime createdAtText;
    private LocalDateTime updatedAtText;
}
