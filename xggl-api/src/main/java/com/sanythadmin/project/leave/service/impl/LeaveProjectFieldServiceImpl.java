package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.code.service.CodeTypeService;
import com.sanythadmin.project.leave.entity.LeaveProjectField;
import com.sanythadmin.project.leave.entity.LeaveProjectFieldOption;
import com.sanythadmin.project.leave.enums.LeaveFieldCategory;
import com.sanythadmin.project.leave.enums.LeaveFormFieldType;
import com.sanythadmin.project.leave.form.LeaveProjectFieldForm;
import com.sanythadmin.project.leave.query.LeaveProjectFormFieldQuery;
import com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.service.LeaveProjectFieldService;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/5/6 16:00
 */
@AllArgsConstructor
@Service
public class LeaveProjectFieldServiceImpl implements LeaveProjectFieldService {

    private final LeaveProjectFieldRepository formFieldRepository;
    private final LeaveProjectRepository projectRepository;
    private final CodeTypeService codeTypeService;

    @Override
    @Transactional(readOnly = true)
    public PageResult<LeaveProjectFieldForm> page(LeaveProjectFormFieldQuery query) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(query.getProjectId()), "projectId is required");
        LeaveUtil.checkTrue(query.getRequestType() != null, "requestType is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(),
                Sort.by(Sort.Order.asc("sortOrder"), Sort.Order.desc("createdAt"))
        );

        Page<LeaveProjectField> page = formFieldRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("project").get("id"), query.getProjectId()),
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("requestType"), query.getRequestType()),
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveProjectFieldForm::new).toList(), page.getTotalElements());
    }

    @Override
    @Transactional
    public void operation(LeaveProjectFieldForm form) {
        LeaveProjectField entity;
        LocalDateTime now = LocalDateTime.now();
        if (form.getId() != null) {
            entity = formFieldRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(now);
        } else {
            entity = new LeaveProjectField();
            entity.setCreatedAt(now);

            LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getProjectId()), "projectId is required");
            entity.setProject(projectRepository.findById(form.getProjectId()).orElseThrow());
            LeaveUtil.checkTrue(form.getRequestType() != null, "requestType is required");
            entity.setRequestType(form.getRequestType());
        }

        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getName()), "名称不能为空");
        entity.setName(form.getName());
        LeaveUtil.checkTrue(form.getType() != null && form.getType().getId() != null, "字段类型不能为空");
        entity.setType(LeaveFormFieldType.valueOf(form.getType().getId()));
        entity.setPlaceholder(form.getPlaceholder());
        if (form.getCodeType() != null && form.getCodeType().getId() != null) {
            CodeType codeType = codeTypeService.getById(form.getCodeType().getId());
            LeaveUtil.checkTrue(codeType != null, "加载数据类型不存在");
            entity.setCodeType(codeType);
        } else {
            entity.setCodeType(null);
        }

        // 选项处理
        List<LeaveProjectFieldOption> options = entity.getOptions();
        if (options == null) {
            options = new ArrayList<>();
            entity.setOptions(options);
        } else {
            options.clear();
        }
        if (form.getOptions() != null && entity.getType().getCategory() == LeaveFieldCategory.OPTION && entity.getCodeType() == null) {
            for (String text : form.getOptions()) {
                LeaveProjectFieldOption option = new LeaveProjectFieldOption(entity, text);
                option.setCreatedAt(now);
                options.add(option);
            }
        }

        entity.setRequired(form.isRequired());
        if (form.getSortOrder() == null) {
            Integer maxSortOrder = formFieldRepository.findMaxSortOrder();
            if (maxSortOrder == null) {
                maxSortOrder = 0;
            }
            entity.setSortOrder(maxSortOrder + 1);
        }else {
            entity.setSortOrder(form.getSortOrder());
        }

        formFieldRepository.save(entity);
    }
}
