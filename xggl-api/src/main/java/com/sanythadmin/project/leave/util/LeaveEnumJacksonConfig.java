package com.sanythadmin.project.leave.util;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.deser.BeanDeserializerModifier;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LeaveEnumJacksonConfig {

    @Bean
    public Module baseEnumModule() {
        SimpleModule module = new SimpleModule();

        // 所有实现 BaseEnum 的都统一用这个序列化器
        module.addSerializer(LeaveEnumI.class, new LeaveEnumSerializer());

//        // 自动反序列化所有 BaseEnum 子类
//        module.addDeserializer(LeaveEnumI.class, new LeaveContextualDeserializer());

        // 反序列化：自动适配所有 BaseEnum 子类
        module.setDeserializerModifier(new BeanDeserializerModifier() {
            @Override
            public JsonDeserializer<?> modifyEnumDeserializer(
                    DeserializationConfig config,
                    JavaType type,
                    BeanDescription beanDesc,
                    JsonDeserializer<?> deserializer) {

                Class<?> rawClass = type.getRawClass();
                if (LeaveEnumI.class.isAssignableFrom(rawClass)) {
                    @SuppressWarnings("unchecked")
                    Class<? extends LeaveEnumI> enumClass = (Class<? extends LeaveEnumI>) rawClass;
                    return new LeaveEnumDeserializer(enumClass);
                }
                return deserializer;
            }
        });

        return module;
    }
}
