package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveBasicFieldScopeType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请假基本字段使用范围
 *
 * @since 2025/4/27 10:07
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_basic_field_scope")
public class LeaveBasicFieldScope extends LeaveBaseEntity {

    public LeaveBasicFieldScope(LeaveBasicField field, LeaveBasicFieldScopeType type) {
        this.field = field;
        this.type = type;
    }

    @ManyToOne
    @JoinColumn(name = "FIELD_ID")
    private LeaveBasicField field;
    @Enumerated(EnumType.STRING)
    private LeaveBasicFieldScopeType type;
}
