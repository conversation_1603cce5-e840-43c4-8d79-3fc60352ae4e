package com.sanythadmin.project.leave.util;

public class LeaveGeoUtils {

    private static final double EARTH_RADIUS = 6371000; // 地球半径，单位：米

    public static double haversine(double lat1, double lon1, double lat2, double lon2) {
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double rLat1 = Math.toRadians(lat1);
        double rLat2 = Math.toRadians(lat2);

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(rLat1) * Math.cos(rLat2)
                * Math.sin(dLon / 2) * Math.sin(dLon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    /**
     * 判断坐标是否在指定范围内
     * @param centerLat 中心纬度
     * @param centerLon 中心经度
     * @param targetLat 要判断的纬度
     * @param targetLon 要判断的经度
     * @param radiusMeters 范围半径（单位：米）
     * @return 是否在范围内
     */
    public static boolean isWithinRange(double centerLat, double centerLon,
                                        double targetLat, double targetLon,
                                        double radiusMeters) {
        double distance = haversine(centerLat, centerLon, targetLat, targetLon);
        return distance <= radiusMeters;
    }

    public static void main(String[] args) {
        // 104.067541,30.708468
        double centerLat = 104.067541;
        double centerLon = 30.708468;
        double radius = 1000;       // 1000 米范围

        // 104.058551,30.709535
        double inputLat = 104.058551;
        double inputLon = 30.709535;

        boolean inRange = LeaveGeoUtils.isWithinRange(centerLat, centerLon, inputLat, inputLon, radius);
        System.out.println("是否在范围内: " + inRange);
    }
}
