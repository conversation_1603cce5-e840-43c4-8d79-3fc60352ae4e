package com.sanythadmin.project.leave.util;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.entity.LeaveApplicationFieldSelectedOption;
import com.sanythadmin.project.leave.enums.LeaveRequestType;
import com.sanythadmin.project.leave.enums.LeaveYesOrNo;
import com.sanythadmin.project.leave.vo.LeaveApplicationFieldVo;
import org.apache.commons.lang3.BooleanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @since 2025/4/30 12:01
 */
public class LeaveUtil {

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static void throwException(String message) throws BusinessException {
        throw new BusinessException(message);
    }

    public static void checkTrue(boolean expression, String content) throws BusinessException {
        if (!expression) {
            throw new BusinessException(content);
        }
    }

    public static boolean applicationCanDelete(LeaveApplication application) {
        return application.getApprovalStartTime() == null;
    }

    public static boolean applicationCanWithdraw(LeaveApplication application) {
        return application.getApprovalStartTime() != null && application.getApprovalEndTime() == null && application.getWithdrawAt() == null;
    }

    public static String booleanToText(Boolean booleanValue) {
        String falseText = LeaveYesOrNo.NO.getText();
        return BooleanUtils.toString(booleanValue, LeaveYesOrNo.YES.getText(), falseText, falseText);
    }

    public static boolean textToBoolean(String text) {
        return LeaveYesOrNo.YES.getText().equals(text);
    }

    /**
     * @since 2025/6/10 14:25
     */
    public static List<LeaveApplicationFieldVo> viewApplicationFields(LeaveApplication application) {
        return application.getFields().stream().map(f -> {
            LeaveApplicationFieldVo fieldVo = new LeaveApplicationFieldVo();
            fieldVo.setName(f.getName());
            String value = f.getValueText();
            switch (f.getType()) {
                case DATE: {
                    LocalDate v = f.getValueDate();
                    if (v == null) {
                        break;
                    }
                    value = v.format(LeaveUtil.DATE_FORMATTER);
                    break;
                }
                case DATETIME: {
                    LocalDateTime v = f.getValueDatetime();
                    if (v == null) {
                        break;
                    }
                    value = v.format(LeaveUtil.DATE_TIME_FORMATTER);
                    break;
                }
                case NUMBER: {
                    Integer v = f.getValueInteger();
                    if (v == null) {
                        break;
                    }
                    value = v.toString();
                    break;
                }
                case DECIMAL: {
                    Double v = f.getValueDouble();
                    if (v == null) {
                        break;
                    }
                    value = v.toString();
                    break;
                }
                case BOOLEAN: {
                    Integer v = f.getValueInteger();
                    if (v == null) {
                        break;
                    }
                    value = LeaveUtil.booleanToText(v == 1);
                    break;
                }
                case RADIO, CHECKBOX: {
                    value = f.getSelectedOptions().stream().map(LeaveApplicationFieldSelectedOption::getText).collect(Collectors.joining());
                    break;
                }
                case LONGTEXT: {
                    value = f.getValueLongText();
                    break;
                }
                case FILE: {
                    fieldVo.setFiles(f.getFiles().stream().map(file -> {
                        FileInfo fileInfo = new FileInfo();
                        fileInfo.setId(file.getId());
                        fileInfo.setContentType(file.getMime());
                        fileInfo.setOriginalFilename(file.getFilename());
                        return fileInfo;
                    }).toList());
                    break;
                }
            }
            fieldVo.setValue(value);
            return fieldVo;
        }).toList();
    }

    public static boolean applicationCanApplyCancel(LeaveApplication application) {
        if (application.getRequestType() != LeaveRequestType.LEAVE) {
            return false;
        }
        if (BooleanUtils.toBoolean(application.getCancellationAfterLeaveEnd())) {
            return false;
        }
        return application.getApprovalEndTime() != null && application.getApproved() && application.getCancelledTime() == null;
    }

    public static boolean applicationCancelled(LeaveApplication application) {
        if (application.getRequestType() != LeaveRequestType.LEAVE) {
            return false;
        }
        return application.getCancelledTime() != null || BooleanUtils.toBoolean(application.getCancellationAfterLeaveEnd());
    }

    public static String getLeaveApplicationStatus(LeaveApplication application) {
        if (application.getWithdrawAt() != null) {
            return "已撤回";
        }
        if (application.getApprovalEndTime() != null) {
            if (application.getApproved()) {
                if (BooleanUtils.toBoolean(application.getCancellationAfterLeaveEnd())) {
                    return "已销假";
                }
                if (application.getCancelledTime() != null) {
                    return "已销假";
                }
                return "假期中";
            }
            return "审批不通过";
        }
        if (application.getApprovalStartTime() != null) {
            return "审批中";
        }
        return "待审批";
    }

    public static String getCancellationApprovalStatus(LeaveApplication cancellationApplication) {
        if (cancellationApplication == null) {
            return "";
        }
        if (cancellationApplication.getApprovalEndTime() != null) {
            if (cancellationApplication.getApproved()) {
                return "审批通过";
            }
            return "审批不通过";
        }
        if (cancellationApplication.getApprovalStartTime() != null) {
            return "审批中";
        }

        return "已申请";
    }

    public static JudgeMark booleanToJudgeMark(Boolean booleanValue) {
        return BooleanUtils.toBoolean(booleanValue) ? JudgeMark.YES : JudgeMark.NO;
    }

    public static boolean judgeMarkToBoolean(JudgeMark judgeMark) {
        return judgeMark == JudgeMark.YES;
    }
}
