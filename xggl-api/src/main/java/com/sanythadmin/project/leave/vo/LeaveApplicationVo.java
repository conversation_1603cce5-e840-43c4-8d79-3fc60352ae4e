package com.sanythadmin.project.leave.vo;

import com.sanythadmin.project.leave.entity.LeaveApplication;
import com.sanythadmin.project.leave.form.LeaveBaseForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/5/20 16:12
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class LeaveApplicationVo extends LeaveBaseForm {

    public LeaveApplicationVo(LeaveApplication entity) {
        super(entity);

        this.applicant = new LeaveUserVo(entity.getUser());

        this.projectName = entity.getProject().getDisplayName();
        this.requestType = new LeaveIdTextPairForm(entity.getRequestType().name(), entity.getRequestType().getText());
        this.submitTime = entity.getSubmitTime();
        this.startTime = entity.getStartTime();
        this.endTime = entity.getEndTime();
        this.durationDays = entity.getDurationDays();

        this.approvalStartTime = entity.getApprovalStartTime();
        this.approvalEndTime = entity.getApprovalEndTime();
        this.approved = entity.getApproved();
        this.withdrawAt = entity.getWithdrawAt();
//        if (entity.getRequestType() != LeaveRequestType.LEAVE) {
//            LeaveApplication originalApplication = entity.getOriginalApplication();
//            this.originalId = originalApplication.getId();
//            this.startTime = originalApplication.getStartTime();
//            this.endTime = originalApplication.getEndTime();
//            this.durationDays = originalApplication.getDurationDays();
//        }
    }

    /**
     * 类型名称
     */
    private String projectName;
    /**
     * 请假/销假
     */
    private LeaveIdTextPairForm requestType;
    /**
     * 申请人
     */
    private LeaveUserVo applicant;
    private LocalDateTime submitTime;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer durationDays;
    /**
     * 审批开始时间
     */
    private LocalDateTime approvalStartTime;
    private LocalDateTime approvalEndTime;
    /**
     * 是否审批通过
     */
    private Boolean approved;
    /**
     * 撤回时间
     */
    private LocalDateTime withdrawAt;
    /**
     * 原始请假申请记录Id
     */
    private String originalId;
}
