package com.sanythadmin.project.leave.vo;

import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.project.leave.dto.LeaveUserDataScopeDto;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import lombok.Data;

/**
 * @since 2025/5/15 9:52
 */
@Data
public class LeaveUserVo {

    public LeaveUserVo(LeaveImmutableUser immutableUser) {
        LeaveUserDataScopeDto dto = new LeaveUserDataScopeDto(immutableUser);
        UserInfoUtil.codeTextSet(dto);

        this.xgh = dto.getXgh();
        this.xm = dto.getXm();
        this.xyName = dto.getXymc();
        this.zyName = dto.getZymc();
        this.bjName = dto.getBjmc();
        this.njName = dto.getNjmc();
        this.pyccName = dto.getPyccmc();
    }

    private String xgh;
    private String xm;
    private String xyName;
    private String zyName;
    private String bjName;
    private String njName;
    private String pyccName;

}
