package com.sanythadmin.project.leave.enums;

import lombok.Getter;

/**
 * 表单字段类型
 *
 * @since 2025/4/27 11:48
 */
@Getter
public enum LeaveFormFieldType {

    TEXT("文本", LeaveFieldCategory.INPUT),
    TEXTAREA("文本域", LeaveFieldCategory.INPUT),
    LONGTEXT("大文本", LeaveFieldCategory.INPUT),
    RADIO("单选", LeaveFieldCategory.OPTION),
    CHECKBOX("多选", LeaveFieldCategory.OPTION),
    BOOLEAN("是或否", LeaveFieldCategory.PICKER),
    NUMBER("整数", LeaveFieldCategory.INPUT),
    DECIMAL("小数", LeaveFieldCategory.INPUT),
    DATE("日期", LeaveFieldCategory.PICKER),
    DATETIME("日期和时间", LeaveFieldCategory.PICKER),
    FILE("文件", LeaveFieldCategory.UPLOAD)
    ;

    private final String text;
    private final LeaveFieldCategory category;

    LeaveFormFieldType(String text, LeaveFieldCategory category) {
        this.text = text;
        this.category = category;
    }
}
