package com.sanythadmin.project.leave.service;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.form.LeaveApprovalForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.vo.LeaveApplicationDetailVo;
import com.sanythadmin.project.leave.vo.LeaveApplicationFlowVo;
import com.sanythadmin.project.leave.vo.LeaveApplicationVo;
import com.sanythadmin.project.leave.vo.LeaveNameValuePairVo;

import java.util.List;

public interface LeaveApprovalService {

    List<LeaveIdTextPairForm> optionalAction(String nodeAssigneeId);

    void submit(LeaveApprovalForm form);

    List<LeaveNameValuePairVo> basicInfo(String leaveId);

    LeaveApplicationDetailVo applicationDetail(String applicationId);

    List<LeaveApplicationFlowVo> flows(String applicationId);

    PageResult<LeaveApplicationVo> cancellationPage(LeaveQuerySuper query, String leaveId);
}
