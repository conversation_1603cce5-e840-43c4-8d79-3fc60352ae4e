package com.sanythadmin.project.leave.immutable;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.springframework.data.annotation.Immutable;

/**
 * @since 2025/5/8 17:25
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(exclude = "userOrgMap")
@Immutable
@Entity
@Table(name = "SYT_USER_INFO")
public class LeaveImmutableUser {

    public LeaveImmutableUser(String xgh) {
        this.xgh = xgh;
    }

    @Id
    private String xgh;
    private String xm;
//    @ManyToOne
//    @JoinColumn(name = "xyid", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
//    private CodeDwb dwb;
//    @ManyToOne
//    @JoinColumn(name = "zyid", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
//    private CodeZyb zyb;
//    @ManyToOne
//    @JoinColumn(name = "bjid", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
//    private CodeBjb bjb;
//    @ManyToOne
//    @JoinColumn(name = "pyccid", foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
//    private CodeCommon pyccb;
//    private String xyid;
//    private String zyid;
//    private String bjid;
//    private String njid;
//    private String pyccid;
    @OneToOne(mappedBy = "user")
    @NotFound(action = NotFoundAction.IGNORE)
    private LeaveImmutableUserOrgMap userOrgMap;

}
