package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.enums.LeaveCancellationMode;
import com.sanythadmin.project.leave.service.LeaveParamService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @since 2025/5/21 17:06
 */
@AllArgsConstructor
@Service
public class LeaveParamServiceImpl implements LeaveParamService {

    private final SysParamService sysParamService;

    @Override
    public LeaveCancellationMode getLeaveCancellationMode(LeaveProject project) {
//        if (project.getCancellationMode() == LeaveCancellationMode.DEFAULT) {
//            SysParam sysParam = sysParamService.getByParamName("请假模块默认销假模式");
//            if (sysParam != null && StringUtils.isNotEmpty(sysParam.getParamValue())) {
//                LeaveCancellationMode[] values = LeaveCancellationMode.values();
//                for (LeaveCancellationMode mode : values) {
//                    if (mode == LeaveCancellationMode.DEFAULT) {
//                        continue;
//                    }
//
//                    if (mode.name().equals(sysParam.getParamValue())) {
//                        return mode;
//                    }
//                }
//            }
//            return LeaveCancellationMode.APPLY_AND_APPROVE;
//        }
//        return project.getCancellationMode();
        return LeaveCancellationMode.APPLY_AND_APPROVE;
    }
}
