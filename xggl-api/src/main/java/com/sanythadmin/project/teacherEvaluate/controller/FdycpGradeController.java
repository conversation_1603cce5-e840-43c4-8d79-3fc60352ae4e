package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.teacherEvaluate.service.FdycpGradeService;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpGrade;
import com.sanythadmin.project.teacherEvaluate.param.FdycpGradeParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 辅导员测评/成绩等级控制器
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-grade")
public class FdycpGradeController extends BaseController {
    @Resource
    private FdycpGradeService fdycpGradeService;

    /**
     * 分页查询辅导员测评成绩等级（权限标识：teacherEvaluate:fdycpGrade:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpGrade:list')")
    @GetMapping("/page")
    public PageResult<FdycpGrade> page(FdycpGradeParam param) {
        PageParam<FdycpGrade, FdycpGradeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = fdycpGradeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部辅导员测评成绩等级（权限标识：teacherEvaluate:fdycpGrade:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpGrade:list')")
    @GetMapping()
    public List<FdycpGrade> list(FdycpGradeParam param) {
        PageParam<FdycpGrade, FdycpGradeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdycpGradeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员测评成绩等级（权限标识：teacherEvaluate:fdycpGrade:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpGrade:list')")
    @GetMapping("/{id}")
    public FdycpGrade get(@PathVariable("id") String id) {
        return fdycpGradeService.getById(id);
    }

    /**
     * 添加或修改辅导员测评成绩等级（权限标识：teacherEvaluate:fdycpGrade:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpGrade:operation')")
    @OperationLog(module = "辅导员测评成绩等级", comments = "保存辅导员测评成绩等级")
    @PostMapping("/operation")
    public void save(@RequestBody FdycpGrade fdycpGrade) {
        if (StringUtils.hasLength(fdycpGrade.getId())) {
            fdycpGradeService.updateById(fdycpGrade);
        } else {
            fdycpGradeService.save(fdycpGrade);
        }
    }

    /**
     * 批量删除辅导员测评成绩等级（权限标识：teacherEvaluate:fdycpGrade:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpGrade:remove')")
    @OperationLog(module = "辅导员测评成绩等级", comments = "批量删除辅导员测评成绩等级")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        fdycpGradeService.removeByIds(ids);
    }
}
