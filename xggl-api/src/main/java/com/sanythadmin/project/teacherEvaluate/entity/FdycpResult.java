package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 辅导员测评结果
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_RESULT")
@Entity
@Table(name = "SYT_FDYCP_RESULT")
public class FdycpResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 年份
     */
    @Column(name = "NF")
    @TableField("NF")
    private String nf;

    /**
     * 被测评人
     */
    @Column(name = "BCPR")
    @TableField("BCPR")
    private String bcpr;

    /**
     * 被测评人姓名
     */
    @Column(name = "BCPRXM")
    @TableField("BCPRXM")
    private String bcprxm;

    /**
     * 被测评人单位
     */
    @Column(name = "BCPRDW")
    @TableField("BCPRDW")
    private String bcprdw;

    /**
     * 被测评人条件
     */
    @Column(name = "BCPRTYPE")
    @TableField("BCPRTYPE")
    private String bcprtype;

    /**
     * 答题人角色域
     */
    @Column(name = "DTRSCOPE")
    @TableField("DTRSCOPE")
    private String dtrscope;

    /**
     * 答题人条件
     */
    @Column(name = "DTRTYPE")
    @TableField("DTRTYPE")
    private String dtrtype;

    /**
     * 测评总分
     */
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 排名
     */
    @Column(name = "RANK")
    @TableField("RANK")
    private Integer rank;

    /**
     * 等级
     */
    @Column(name = "GRADE")
    @TableField("GRADE")
    private String grade;

    /**
     * 指标问卷ID
     */
//    @Column(name = "SURVEY_ITEM_ID", columnDefinition = ColumnType.CHAR_32)
//    @TableField("SURVEY_ITEM_ID")
//    private String surveyItemId;

    /**
     * 答卷ID
     */
//    @Column(name = "SID", columnDefinition = ColumnType.CHAR_32)
//    @TableField("SID")
//    private String sid;

    /**
     * 成绩详情（各项指标成绩）
     */
    @Column(name = "SCORE_DETAIL", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("SCORE_DETAIL")
    private String scoreDetail;
}
