package com.sanythadmin.project.teacherEvaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评结果查询参数
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpResultParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String nf;

    /**
     * 被测评人
     */
    @QueryField(type = QueryType.EQ)
    private String bcpr;

    /**
     * 被测评人姓名
     */
    private String bcprxm;

    /**
     * 被测评人单位
     */
    private String bdcrdw;

    /**
     * 被测评人条件
     */
    private String bcprtype;

    /**
     * 答题人角色域
     */
    private String dtrscope;

    /**
     * 答题人条件
     */
    private String dtrtype;

    /**
     * 指标ID
     */
//    @QueryField(type = QueryType.EQ)
//    private String surveyItemId;

    /**
     * 测评总分
     */
    @QueryField(type = QueryType.EQ)
    private Double score;

    /**
     * 排名
     */
    @QueryField(type = QueryType.EQ)
    private Integer rank;

    /**
     * 等级
     */
    private String grade;

    /**
     * 答卷ID
     */
//    @QueryField(type = QueryType.EQ)
//    private String sid;
}
