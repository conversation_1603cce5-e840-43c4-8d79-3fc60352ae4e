package com.sanythadmin.project.teacherEvaluate.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererSummaryScoreDTO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpResultDTO;
import com.sanythadmin.project.teacherEvaluate.dto.TesteeInfoDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpResultParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辅导员测评卷答案Mapper
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
public interface FdycpAnswerMapper extends MyMPJBaseMapper<FdycpAnswer> {
    List<FdycpResultDTO> testeeItemScoreSummary(@Param("param") FdycpAnswerParam param);

    List<AnswererSummaryScoreDTO> answererScoreSummary(@Param("page") IPage<AnswererSummaryScoreDTO> page, @Param("param") FdycpAnswerParam param);

    List<TesteeInfoDTO> testeeListQuery(@Param("page") IPage<TesteeInfoDTO> page, @Param("param") FdycpAnswerParam param);
}
