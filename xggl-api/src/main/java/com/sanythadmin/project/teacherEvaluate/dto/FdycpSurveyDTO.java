package com.sanythadmin.project.teacherEvaluate.dto;

import com.sanythadmin.project.survey.entity.Survey;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by JIANGPING on 2025/3/5.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FdycpSurveyDTO extends Survey {
    private FdycpItemDTO item;
    private String itemId;      // 指标库指标ID
    /**
     * 被测评人信息
     */
    private String bcpr;
    private String bcprxm;
    private String roleId;
    private String roleName;
    /**
     * 测评对象
     */
    private String cpdx;
    private String surveyItemId;
    private String nf;
    private String cpdxjsid;
}
