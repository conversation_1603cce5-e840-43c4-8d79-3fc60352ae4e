package com.sanythadmin.project.teacherEvaluate.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.SysAccountMapper;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyPquestionsOptions;
import com.sanythadmin.project.survey.mapper.SurveyMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsOptionsMapper;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.teacherEvaluate.constant.ConstantsTeacherEvaluate;
import com.sanythadmin.project.teacherEvaluate.dto.AnswerInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererSummaryScoreDTO;
import com.sanythadmin.project.teacherEvaluate.dto.TesteeInfoDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswerInfo;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerInfoMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerMapper;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerInfoService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerService;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 辅导员测评卷答案Service实现
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@AllArgsConstructor
@Service
public class FdycpAnswerServiceImpl extends ServiceImpl<FdycpAnswerMapper, FdycpAnswer> implements FdycpAnswerService {
    private final FdycpAnswerMapper fdycpAnswerMapper;
    private final UserInfoMapper userInfoMapper;
    private final SurveyMapper surveyMapper;
    private final SysAccountMapper sysAccountMapper;
    private final SysRoleService sysRoleService;
    private final SysParamService sysParamService;
    private final SysAccountRoleMapper accountRoleMapper;
    private final GridFsService gridFsService;
    private final FdycpAnswerInfoMapper answerInfoMapper;
    private final SurveyPquestionsOptionsMapper pquestionsOptionsMapper;
    private final FdycpAnswerInfoService answerInfoService;
    private final PersonInfoFactory personInfoFactory;
    private final UserInfoService userInfoService;

    @Transactional
    @Override
    public void saveAnswer(@Validated AnswerInfoDTO dto) {
        List<FdycpAnswer> answers = dto.getAnswers();
        UserInfo userInfo = SecurityUtil.getUserInfo();
        SysParam sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPTJ);
        SysParam yearParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPNF);
        String year = (yearParam == null || !StringUtils.hasText(yearParam.getParamValue())) ? String.valueOf(DateUtil.getCurrentYear()) : yearParam.getParamValue();
        Long count = answerInfoService.testeeUserCount(dto.getSid(), userInfo.getXgh(), dto.getBcpr());
        if (count > 0)
            AssertUtil.throwMessage("操作失败, 当前被测评人测评卷已填写");
        UserInfoUtil.codeTextSet(userInfo);
        Object value = CommonUtil.getValue(sysParam.getParamValue(), userInfo);
        UserInfo beiCePingRen = userInfoMapper.get(dto.getBcpr());
        UserInfoUtil.codeTextSet(beiCePingRen);
        Object beiCePingRen_type = CommonUtil.getValue(sysParam.getParamValue(), beiCePingRen);
        FdycpAnswerInfo answerInfo = new FdycpAnswerInfo();
        answerInfo.setNf(year);
        answerInfo.setCreateDate(LocalDateTime.now());
        answerInfo.setXgh(userInfo.getXgh());
        answerInfo.setXm(userInfo.getXm());
        answerInfo.setDtrxymc(userInfo.getXymc());
        answerInfo.setDtrbjmc(userInfo.getBjmc());
        answerInfo.setDtrscope(SecurityUtil.getRoleScope());
        answerInfo.setDtrtype(value == null ? null : String.valueOf(value));
        answerInfo.setIp(dto.getIp());
        answerInfo.setBcpr(dto.getBcpr());
        answerInfo.setBcprxm(beiCePingRen.getXm());
        answerInfo.setBcprdw(beiCePingRen.getXymc());
        answerInfo.setBcprtype(beiCePingRen_type == null ? null : String.valueOf(beiCePingRen_type));
        answerInfo.setSid(dto.getSid());
        answerInfo.setSurveyItemId(dto.getSurveyItemId());
        List<SurveyPquestionsOptions> options = pquestionsOptionsMapper.selectList(new LambdaQueryWrapper<SurveyPquestionsOptions>()
                .select(SurveyPquestionsOptions::getId, SurveyPquestionsOptions::getScore)
                .eq(SurveyPquestionsOptions::getSid, dto.getSid()));
        Map<String, Double> optionScoreMap = options.stream().collect(Collectors.toMap(SurveyPquestionsOptions::getId, SurveyPquestionsOptions::getScore));
        AtomicReference<Double> score = new AtomicReference<>(0d);
        for (FdycpAnswer answer : answers) {
            answer.setXgh(userInfo.getXgh());
            answer.setCreateDate(LocalDateTime.now());
            answer.setSurveyItemId(dto.getSurveyItemId());
            double point = 0;
            if (StringUtils.hasText(answer.getResultId())) {
                String[] resultIdArray = CommonUtil.split(answer.getResultId());
                for (String resultId : resultIdArray) {
                    point += optionScoreMap.get(resultId) == null ? 0 : optionScoreMap.get(resultId);
                }
            }
            answer.setPoint(point);
            score.updateAndGet(v -> v + answer.getPoint());
            if (CollectionUtils.isNotEmpty(answer.getResultFiles())) {
                List<MultipartFile> attachment = new ArrayList<>();
                List<FdycpAnswer.ResultItem> resultFiles = answer.getResultFiles();
                resultFiles.forEach(resultItem -> attachment.add(resultItem.getFile()));
                String fileInfoStr = gridFsService.save(attachment.toArray(new MultipartFile[]{}));
                List<FileInfo> fileInfos = new ArrayList<>(JSON.parseArray(fileInfoStr, FileInfo.class));
                if (!CollectionUtils.isEmpty(fileInfos))
                    answer.setResult(JSON.toJSONString(fileInfos));
            }
        }
        answerInfo.setScore(score.get());
        answerInfoMapper.insert(answerInfo);
        answers.forEach(answer -> {
            answer.setInfoId(answerInfo.getId());
            fdycpAnswerMapper.insert(answer);
        });
    }

    @Override
    public List<AnswererInfoDTO> answererInfoCount(FdycpAnswerInfoParam answer) {
        MyMPJLambdaWrapper<Survey, SurveyParam> query = new MyMPJLambdaWrapper<>();
        query.select(Survey::getName).select(FdycpSurveyItem::getNf).select(FdycpSurveyItem::getDjrjsid)
                .selectAs(Survey::getId, AnswererInfoDTO::getSid)
                .selectAs(Survey::getCreateTime, AnswererInfoDTO::getCreateDate)
                .selectAs(FdycpSurveyItem::getId, AnswererInfoDTO::getSurveyItemId)
                .select(FdycpSurveyItem::getCpdxjsid)
                .leftJoin(FdycpSurveyItem.class, FdycpSurveyItem::getSid, Survey::getId)
                .eq(FdycpSurveyItem::getItemId, answer.getItemId())
                .exists("SELECT 1 FROM SYT_FDYCP_ANSWER_INFO WHERE " + query.getAlias() + ".ID=SID AND BCPR={0} AND NF={1}"
                        , answer.getBcpr(), answer.getNf());
        List<AnswererInfoDTO> dtos = surveyMapper.selectJoinList(AnswererInfoDTO.class, query);
        for (AnswererInfoDTO dto : dtos) {
            dto.setBcpr(answer.getBcpr());
//            SysAccountParam param = new SysAccountParam();
//            param.setStatus(State.ENABLED);
//            param.setLockStatus(State.ENABLED);
//            MyMPJLambdaWrapper<SysAccount, SysAccountParam> wrapper = new MyMPJLambdaWrapper<>(param);
//            wrapper.exists("SELECT 1 FROM SYT_SYS_ACCOUNT_ROLE R WHERE R.ROLE_ID = '" + dto.getDjrjsid() + "' " +
//                    "AND R.ACCOUNT_ID = " + wrapper.getAlias() + ".ID");
            Long expectedCount = getExpectedCount(dto);
//            Long count = sysAccountMapper.selectJoinCount(wrapper); // 应参与人数
            Long total = answerInfoMapper.selectCount(new LambdaQueryWrapper<FdycpAnswerInfo>()
                    .eq(FdycpAnswerInfo::getSid, dto.getSid())
                    .eq(FdycpAnswerInfo::getNf, answer.getNf())
                    .eq(FdycpAnswerInfo::getSurveyItemId, dto.getSurveyItemId())
                    .eq(FdycpAnswerInfo::getBcpr, answer.getBcpr()));
            dto.setYcprs(expectedCount.intValue());
            dto.setSjcprs(total.intValue());
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            String percent = decimalFormat.format((double) dto.getSjcprs() / dto.getYcprs() * 100) + "%";
            dto.setSjcprszb(percent);
        }
        return dtos;
    }

    private Long getExpectedCount(AnswererInfoDTO dto) {
        UserInfoParam userInfoParam = new UserInfoParam();
//        userInfoParam.setUserType(UserType.TEACHER);
//        userInfoParam.setXgh(dto.getBcpr());
//        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = userInfoService.getQueryWrapper(userInfoParam);
//        UserInfo userInfo = CommonUtil.listGetOne(userInfoMapper.selectJoinList(wrapper));

        RoleParam roleParam = new RoleParam();
        roleParam.setId(dto.getDjrjsid());
        SysRole role = CommonUtil.listGetOne(sysRoleService.list(roleParam));
        UserType userType = Objects.equals(role.getRoleScope(), Constants.ROLE_SCOPE_GR) ? UserType.STUDENT : UserType.TEACHER;
        CodeXsztParam stateParam = Objects.equals(role.getRoleScope(), Constants.ROLE_SCOPE_GR) ? CodeXsztParam.currentStudent() : null;
        userInfoParam.setUserType(userType);
        long total = 0;
        String[] roleIds = CommonUtil.split(dto.getCpdxjsid());
        for (String roleId : roleIds) {
            Long count = accountRoleMapper.selectCount(new LambdaQueryWrapper<SysAccountRole>()
                    .eq(SysAccountRole::getUsername, dto.getBcpr())
                    .eq(SysAccountRole::getRoleId, roleId));
            if (count == 0) continue;
            roleParam = new RoleParam();
            roleParam.setId(roleId);
            role = CommonUtil.listGetOne(sysRoleService.list(roleParam));
            SysRole finalRole = role;
            total += personInfoFactory.apply(userType, service -> service.getUserCountByRoleDuty(userInfoParam, stateParam, dto.getBcpr(), roleId, finalRole.getRoleScope()));
        }
        return total;
    }

    @Override
    public PageResult<AnswererSummaryScoreDTO> answererScoreSummary(FdycpAnswerParam param) {
        PageParam<AnswererSummaryScoreDTO, FdycpAnswerParam> page = new PageParam<>(param);
        List<AnswererSummaryScoreDTO> list = fdycpAnswerMapper.answererScoreSummary(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public PageResult<TesteeInfoDTO> testeeListQuery(FdycpAnswerParam param) {
        PageParam<TesteeInfoDTO, FdycpAnswerParam> page = new PageParam<>(param);
        List<TesteeInfoDTO> list = fdycpAnswerMapper.testeeListQuery(page, param);
        return new PageResult<>(list, page.getTotal());
    }
}
