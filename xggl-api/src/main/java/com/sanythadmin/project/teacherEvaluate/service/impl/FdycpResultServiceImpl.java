package com.sanythadmin.project.teacherEvaluate.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.teacherEvaluate.constant.ConstantsTeacherEvaluate;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpResultDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpGrade;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpResult;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpGradeMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpResultMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpSurveyItemMapper;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpResultParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpResultService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 辅导员测评结果Service实现
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Service
public class FdycpResultServiceImpl extends ServiceImpl<FdycpResultMapper, FdycpResult> implements FdycpResultService {
    @Resource
    private FdycpResultMapper mapper;
    @Resource
    private FdycpAnswerMapper fdycpAnswerMapper;
    @Resource
    private FdycpSurveyItemMapper fdycpSurveyItemMapper;
    @Resource
    private FdycpGradeMapper fdycpGradeMapper;

    @Transactional
    @Override
    public void summarySave(FdycpResultParam param) {
        FdycpAnswerParam answerParam = new FdycpAnswerParam();
        answerParam.setNf(param.getNf());
        List<FdycpResultDTO> results = fdycpAnswerMapper.testeeItemScoreSummary(answerParam);
        if (!CollectionUtils.isEmpty(results)) {
            List<FdycpSurveyItem> surveyItems = fdycpSurveyItemMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, FdycpSurveyItem> surveyItemMap = surveyItems.stream().collect(Collectors.toMap(FdycpSurveyItem::getId, Function.identity()));
            HashMap<String, FdycpResult> resultMap = new HashMap<>();
            HashMap<String, JSONObject> itemScoreMap = new HashMap<>();
            for (FdycpResultDTO dto : results) {
                FdycpResult result = resultMap.get(dto.getBcpr());
                JSONObject jsonObject = itemScoreMap.get(dto.getBcpr());
                if (jsonObject == null) jsonObject = new JSONObject();
                if (result == null) {
                    result = new FdycpResult();
                    BeanUtils.copyProperties(dto, result);
                    result.setScore(0d);
                }

                double score = dto.getScore();
                FdycpSurveyItem surveyItem = surveyItemMap.get(dto.getSurveyItemId());
                if (JudgeMark.YES == surveyItem.getSfcyjs()) {
                    score = switch (surveyItem.getZblx()) {
                        case ConstantsTeacherEvaluate.CALC_METHOD_PERCENT ->
                                (dto.getScore() * surveyItem.getZbckz() / 100);
                        case ConstantsTeacherEvaluate.CALC_METHOD_VALUE -> dto.getScore() + surveyItem.getZbckz();
                        default -> score;
                    };
                }
                result.setScore(result.getScore() + score);
                if (jsonObject.containsKey(surveyItem.getItemId())) {
                    double value = jsonObject.getDoubleValue(surveyItem.getItemId());
                    score += value;
                }
                jsonObject.put(surveyItem.getItemId(), score);
                itemScoreMap.put(dto.getBcpr(), jsonObject);
                resultMap.put(dto.getBcpr(), result);
            }

            mapper.delete(new LambdaQueryWrapper<FdycpResult>().eq(FdycpResult::getNf, param.getNf()));

            List<FdycpGrade> grades = fdycpGradeMapper.selectList(new LambdaQueryWrapper<>());
            List<FdycpResult> values = new ArrayList<>(resultMap.values().stream().toList());
            values.sort((o1, o2) -> Double.compare(o2.getScore(), o1.getScore()));
            HashMap<String, Integer> rankMap = new HashMap<>();
            Integer rank = null;
            for (FdycpResult result : values) {
                String scoreStr = String.valueOf(result.getScore());
                if (rankMap.containsKey(scoreStr)) {
                    rank = rankMap.get(scoreStr);
                } else {
                    rank = rank == null ? 1 : rank + 1;
                    rankMap.put(scoreStr, rank);
                }

                for (FdycpGrade grade : grades) {
                    if (result.getScore() >= grade.getMinScore() && result.getScore() < grade.getMaxScore()) {
                        result.setGrade(grade.getGrade());
                        break;
                    }
                }
                result.setRank(rank);
                JSONObject object = itemScoreMap.get(result.getBcpr());
                result.setScoreDetail(object == null ? null : JSON.toJSONString(object));
                mapper.insert(result);
            }
        }
    }

    @Override
    public List<FdycpResultDTO> list(FdycpResultParam param) {
        PageParam<FdycpResult, FdycpResultParam> page = new PageParam<>(param);
        List<FdycpResult> results = mapper.selectList(page.getWrapper());
        return results != null ? results.stream().map(result -> CommonUtil.copyProperties(FdycpResultDTO.class, result)).toList() : null;
    }
}
