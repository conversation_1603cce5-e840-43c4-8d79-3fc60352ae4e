package com.sanythadmin.project.teacherEvaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherEvaluate.dto.AnswerInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererSummaryScoreDTO;
import com.sanythadmin.project.teacherEvaluate.dto.TesteeInfoDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;

import java.util.List;

/**
 * 辅导员测评卷答案Service
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
public interface FdycpAnswerService extends IService<FdycpAnswer> {
    public void saveAnswer(AnswerInfoDTO answerInfo);

    public List<AnswererInfoDTO> answererInfoCount(FdycpAnswerInfoParam answer);

    public PageResult<AnswererSummaryScoreDTO> answererScoreSummary(FdycpAnswerParam param);

    public PageResult<TesteeInfoDTO> testeeListQuery(FdycpAnswerParam param);
}
