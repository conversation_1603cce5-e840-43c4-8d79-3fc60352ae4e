package com.sanythadmin.project.teacherEvaluate.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评指标查询参数
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 考核批次
     */
    private String khpc;

    /**
     * 指标类型
     */
    @QueryField(type = QueryType.EQ)
    private String zblx;

    /**
     * 分值/百分比
     */
    @QueryField(type = QueryType.EQ)
    private String zbckz;

    /**
     * 是否参与计算
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfcyjs;

    /**
     * 答卷人角色
     */
    private String djrjs;

    /**
     * 测评对象
     */
    private String cpdx;

}
