package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.entity.BaseSurveyAnswerInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @since 2025/7/2.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_ANSWER_INFO")
@Entity
@Table(name = "SYT_FDYCP_ANSWER_INFO")
public class FdycpAnswerInfo extends BaseSurveyAnswerInfo implements Serializable {

    /**
     * 答题者姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 测评年份
     */
    @Column(name = "NF")
    @TableField("NF")
    private String nf;

    /**
     * 被测评人
     */
    @Column(name = "BCPR")
    @TableField("BCPR")
    private String bcpr;

    /**
     * 被测评人姓名
     */
    @Column(name = "BCPRXM")
    @TableField("BCPRXM")
    private String bcprxm;

    /**
     * 被测评人角色ID
     */
    @Column(name = "BCPRJSID")
    @TableField("BCPRJSID")
    private String bcprjsid;

    /**
     * 被测评人单位
     */
    @Column(name = "BCPRDW")
    @TableField("BCPRDW")
    private String bcprdw;

    /**
     * 被测评人测评条件
     */
    @Column(name = "BCPRTYPE")
    @TableField("BCPRTYPE")
    private String bcprtype;

    /**
     * 答题人角色域
     */
    @Column(name = "DTRSCOPE")
    @TableField("DTRSCOPE")
    private String dtrscope;

    /**
     * 答题人测评条件
     */
    @Column(name = "DTRTYPE")
    @TableField("DTRTYPE")
    private String dtrtype;

    /**
     * 测评指标ID
     */
    @Column(name = "SURVEY_ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("SURVEY_ITEM_ID")
    private String surveyItemId;

    /**
     * 答题人学院
     */
    @Column(name = "DTRXYMC")
    @TableField("DTRXYMC")
    private String dtrxymc;

    /**
     * 答题人班级
     */
    @Column(name = "DTRBJMC")
    @TableField("DTRBJMC")
    private String dtrbjmc;
}
