package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpSurveyDTO;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 辅导员测评/问卷管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-survey")
public class FdycpSurveyController extends BaseController {
    @Resource
    private SurveyService surveyService;
    @Resource
    private FdycpSurveyItemService surveyItemService;

    /**
     * 分页查询问卷管理（权限标识：teacherEvaluate:survey:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:survey:list')")
    @GetMapping("/page")
    public PageResult<FdycpSurveyDTO> page(SurveyParam param) {
        PageParam<Survey, SurveyParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_TIME desc");
        page = surveyService.page(page, page.getWrapper());
        List<FdycpSurveyDTO> list = surveyItemService.convert(page.getRecords());
        return new PageResult<>(list, page.getTotal());
    }

    /**
     * 查询全部问卷管理（权限标识：teacherEvaluate:survey:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:survey:list')")
    @GetMapping()
    public List<FdycpSurveyDTO> list(SurveyParam param) {
        PageParam<Survey, SurveyParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_TIME desc");
        List<Survey> list = surveyService.list(page.getOrderWrapper());
        return surveyItemService.convert(list);
    }

    /**
     * 根据id查询问卷管理（权限标识：teacherEvaluate:survey:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:survey:list')")
    @GetMapping("/{id}")
    public SurveyVO get(@PathVariable("id") String id) {
        return surveyService.getSurveyVO(id);
    }

    /**
     * 获取问卷答卷详情（权限标识：teacherEvaluate:survey:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/getSurveyVO")
    public SurveyVO getExamVO(SurveyAnswerInfo surveyAnswerInfo) {
        if (!StringUtils.hasLength(surveyAnswerInfo.getId())) {
            AssertUtil.throwMessage("答卷信息id不能为空");
        }
        return surveyService.getSurveyVO(surveyAnswerInfo.getSid(), surveyAnswerInfo);
    }

    /**
     * 添加或修改问卷管理（权限标识：teacherEvaluate:survey:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:survey:operation')")
    @OperationLog(module = "问卷管理", comments = "保存问卷管理")
    @PostMapping("/operation")
    public FdycpSurveyDTO save(@RequestBody FdycpSurveyDTO param) throws Exception {
        return surveyItemService.saveSurvey(param);
    }

    /**
     * 批量删除问卷管理（权限标识：teacherEvaluate:survey:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:survey:remove')")
    @OperationLog(module = "问卷管理", comments = "批量删除问卷管理")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyService.removePaperGroupQuestions(ids);
        surveyItemService.removeBySurvey(ids);
    }

    /**
     * 问卷状态(停用/启用)（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "问卷管理", comments = "问卷状态")
    @PostMapping("/state")
    public void sendPaper(@RequestBody Survey survey) {
        if (!StringUtils.hasLength(survey.getId())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (survey.getStatus() == null) {
            AssertUtil.throwMessage("问卷状态不能为空");
        }
        Survey byId = surveyService.getById(survey.getId());
        if (byId == null) {
            AssertUtil.throwMessage("未找到问卷");
        }
        byId.setStatus(survey.getStatus());
        surveyService.saveOrUpdate(byId);
    }

    /**
     * 答卷人分页查询可答问卷（权限标识：teacherEvaluate:surveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:surveyAnswer:list')")
    @GetMapping("/querySurveys")
    public PageResult<Survey> querySurveys(SurveyParam param) {
        return surveyService.querySurveys(param);
    }
}
