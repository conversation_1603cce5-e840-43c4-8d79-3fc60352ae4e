package com.sanythadmin.project.teacherEvaluate.dto;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * Created by JIANGPING on 2025/3/10.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FdycpResultDTO extends FdycpResult {
    private String surveyItemId;
    private JSONObject itemScoreObject;

    public JSONObject getItemScoreObject() {
        if (StringUtils.hasText(super.getScoreDetail()))
            itemScoreObject = JSON.parseObject(super.getScoreDetail());
        return itemScoreObject;
    }
}
