<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpResultMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NF, BCPR, BCPRXM, BDCRDW, BCPRTYPE, DTRSCOPE, DTRTYPE, ITEM_ID, SCORE, RANK, GRADE, INFO_ID, SCORE_DETAIL
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.nf != null">
                        AND a.NF LIKE concat(concat('%',#{param.nf), '%')
                    </if>
                    <if test="param.bcpr != null">
                        AND a.BCPR LIKE concat(concat('%',#{param.bcpr), '%')
                    </if>
                    <if test="param.bcprxm != null">
                        AND a.BCPRXM LIKE concat(concat('%',#{param.bcprxm), '%')
                    </if>
                    <if test="param.bdcrdw != null">
                        AND a.BDCRDW LIKE concat(concat('%',#{param.bdcrdw), '%')
                    </if>
                    <if test="param.bcprtype != null">
                        AND a.BCPRTYPE LIKE concat(concat('%',#{param.bcprtype), '%')
                    </if>
                    <if test="param.dtrscope != null">
                        AND a.DTRSCOPE LIKE concat(concat('%',#{param.dtrscope), '%')
                    </if>
                    <if test="param.dtrtype != null">
                        AND a.DTRTYPE LIKE concat(concat('%',#{param.dtrtype), '%')
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE = #{param.score}
                    </if>
                    <if test="param.rank != null">
                        AND a.RANK = #{param.rank}
                    </if>
                    <if test="param.grade != null">
                        AND a.GRADE LIKE concat(concat('%',#{param.grade), '%')
                    </if>
                    <if test="param.infoId != null">
                        AND a.INFO_ID LIKE concat(concat('%',#{param.infoId), '%')
                    </if>
                    <if test="param.scoreDetail != null">
                        AND a.SCORE_DETAIL LIKE concat(concat('%',#{param.scoreDetail), '%')
                    </if>
    </sql>
</mapper>
