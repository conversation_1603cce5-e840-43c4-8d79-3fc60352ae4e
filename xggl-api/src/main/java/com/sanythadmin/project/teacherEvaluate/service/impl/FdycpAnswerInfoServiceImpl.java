package com.sanythadmin.project.teacherEvaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswerInfo;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerInfoMapper;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @since 2025/7/3.
 */
@Service("fdycpAnswerInfoService")
public class FdycpAnswerInfoServiceImpl extends ServiceImpl<FdycpAnswerInfoMapper, FdycpAnswerInfo> implements FdycpAnswerInfoService {
    @Override
    public PageResult<FdycpAnswerInfo> page(FdycpAnswerInfoParam param) {
        PageParam<FdycpAnswerInfo, FdycpAnswerInfoParam> page = new PageParam<>(param);
        page = baseMapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FdycpAnswerInfo> list(FdycpAnswerInfoParam param) {
        PageParam<FdycpAnswerInfo, FdycpAnswerInfoParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getWrapper());
    }

    @Override
    public List<String> years() {
        return baseMapper.selectObjs(new LambdaQueryWrapper<FdycpAnswerInfo>()
                .select(FdycpAnswerInfo::getNf).groupBy(FdycpAnswerInfo::getNf)
                .orderByDesc(FdycpAnswerInfo::getNf));
    }

    @Override
    public Long testeeUserCount(String sid, String answererUser, String testeeUser) {
        return baseMapper.selectCount(new LambdaQueryWrapper<FdycpAnswerInfo>()
                .eq(FdycpAnswerInfo::getSid, sid)
                .eq(FdycpAnswerInfo::getBcpr, testeeUser)
                .eq(FdycpAnswerInfo::getXgh, answererUser));
    }
}
