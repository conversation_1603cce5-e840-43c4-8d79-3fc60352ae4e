package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 辅导员测评问卷和指标关系
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_SURVEY_ITEM")
@Entity
@Table(name = "SYT_FDYCP_SURVEY_ITEM")
public class FdycpSurveyItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 测评指标库指标ID
     */
    @Column(name = "ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 考核批次
     */
    @Column(name = "KHPC", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("KHPC")
    private String khpc;

    /**
     * 指标类型
     */
    @Column(name = "ZBLX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZBLX")
    private String zblx;

    /**
     * 固定分值/百分比值
     */
    @Column(name = "ZBCKZ", columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("ZBCKZ")
    private Double zbckz;

    /**
     * 是否参与计算（1：是， 0：否）
     */
    @Column(name = "SFCYJS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFCYJS")
    private JudgeMark sfcyjs;

    /**
     * 答卷人角色
     */
    @Column(name = "DJRJS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("DJRJS")
    private String djrjs;

    @Column(name = "DJRJSID", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("DJRJSID")
    private String djrjsid;

    /**
     * 问卷ID
     */
    @Column(name = "SID", columnDefinition = ColumnType.CHAR_32)
    @TableField("SID")
    private String sid;

    /**
     * 年份
     */
    @Column(name = "NF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("NF")
    private String nf;

    /**
     * 测评对象角色
     */
    @Column(name = "CPDXJS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CPDXJS")
    private String cpdxjs;
    @Column(name = "CPDXJSID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CPDXJSID")
    private String cpdxjsid;
}
