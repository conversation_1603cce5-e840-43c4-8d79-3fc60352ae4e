<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, KHPC, ZBLX, ZBCKZ, SFCYJS, DJRJS, CPDX, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.khpc != null">
                        AND a.KHPC LIKE concat(concat('%',#{param.khpc), '%')
                    </if>
                    <if test="param.zblx != null">
                        AND a.ZBLX = #{param.zblx}
                    </if>
                    <if test="param.zbckz != null">
                        AND a.ZBCKZ = #{param.zbckz}
                    </if>
                    <if test="param.sfcyjs != null">
                        AND a.SFCYJS = #{param.sfcyjs}
                    </if>
                    <if test="param.djrjs != null">
                        AND a.DJRJS LIKE concat(concat('%',#{param.djrjs), '%')
                    </if>
                    <if test="param.cpdx != null">
                        AND a.CPDX LIKE concat(concat('%',#{param.cpdx), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
