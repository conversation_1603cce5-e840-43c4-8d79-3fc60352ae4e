package com.sanythadmin.project.teacherEvaluate.dto;

import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * Created by JIANGPING on 2025/3/5.
 */
@Data
public class AnswerInfoDTO {
    /**
     * 测评卷ID
     */
    @NotEmpty(message = "测评卷ID不能为空")
    private String sid;
    /**
     * 测评卷关联指标ID
     */
    @NotEmpty(message = "测评卷指标ID不能为空")
    private String surveyItemId;
    /**
     * 被测评人账号
     */
    @NotEmpty(message = "被测评人不能为空")
    private String bcpr;

    /**
     * 答题人IP
     */
    private String ip;
    /**
     * 答题信息
     */
    @NotNull(message = "答题信息不能为空")
    private List<FdycpAnswer> answers;
}
