package com.sanythadmin.project.teacherEvaluate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswererSummaryScoreDTO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpResultDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswerInfo;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpResult;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpResultParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerInfoService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpResultService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 辅导员测评/汇总结果控制器
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Slf4j
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-result")
public class FdycpResultController extends BaseController {
    @Resource
    private FdycpResultService fdycpResultService;
    @Resource
    private FdycpAnswerInfoService fdycpAnswerInfoService;
    @Resource
    private FdycpAnswerService fdycpAnswerService;
    @Resource
    private FdycpSurveyItemService fdycpSurveyItemService;
    @Resource
    private SurveyService surveyService;

    /**
     * 分页查询辅导员测评结果（权限标识：teacherEvaluate:fdycpResult:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:list')")
    @GetMapping("/page")
    public PageResult<FdycpResult> page(FdycpResultParam param) {
        PageParam<FdycpResult, FdycpResultParam> page = new PageParam<>(param);
        page = fdycpResultService.page(page, page.getWrapper());
        List<FdycpResult> records = page.getRecords();
        return new PageResult<>(records, page.getTotal());
    }

    /**
     * 查询全部辅导员测评结果（权限标识：teacherEvaluate:fdycpResult:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:list')")
    @GetMapping()
    public List<FdycpResult> list(FdycpResultParam param) {
        PageParam<FdycpResult, FdycpResultParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdycpResultService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员测评结果（权限标识：teacherEvaluate:fdycpResult:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:list')")
    @GetMapping("/{id}")
    public FdycpResult get(@PathVariable("id") String id) {
        return fdycpResultService.getById(id);
    }

    /**
     * 辅导员测评结果汇总（权限标识：teacherEvaluate:fdycpResult:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:operation')")
    @OperationLog(module = "辅导员测评结果", comments = "保存辅导员测评结果")
    @PostMapping("/operation")
    public void save(@RequestBody FdycpResultParam param) {
        fdycpResultService.summarySave(param);
    }

    /**
     * 批量删除辅导员测评结果（权限标识：teacherEvaluate:fdycpResult:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:remove')")
    @OperationLog(module = "辅导员测评结果", comments = "批量删除辅导员测评结果")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        fdycpResultService.removeByIds(ids);
    }

    /**
     * 被测评人参与者信息统计
     *
     * @param param
     * @return
     */
    @GetMapping("/answererInfoCount")
    public List<AnswererInfoDTO> answererInfoCount(FdycpAnswerInfoParam param) {
        return fdycpAnswerService.answererInfoCount(param);
    }

    /**
     * 被测评人答题者分数情况汇总
     *
     * @param param
     * @return
     */
    @GetMapping("/answererScoreSummary")
    public PageResult<AnswererSummaryScoreDTO> answererScoreSummary(FdycpAnswerParam param) {
        return fdycpAnswerService.answererScoreSummary(param);
    }

    @GetMapping("/export")
    public void export(FdycpResultParam param, HttpServletResponse response) {
        List<FdycpResultDTO> list = fdycpResultService.list(param);
        List<FdycpSurveyItem> items = fdycpSurveyItemService.list();
        try {
            String[] property = {"bcpr", "bcprxm", "bdcrdw"};
            String[] title = {"工号", "姓名", "院系"};
            List<String> propertyList = new ArrayList<>(List.of(property));
            List<String> titleList = new ArrayList<>(List.of(title));
            if (!CollectionUtils.isEmpty(list)) {
                FdycpResultDTO dto = list.get(0);
                JSONObject itemScoreObject = dto.getItemScoreObject();
                Map<String, FdycpSurveyItem> surveyItemMap = items.stream()
                        .collect(Collectors.toMap(FdycpSurveyItem::getId, Function.identity()));
                if (itemScoreObject != null) {
                    for (Map.Entry<String, Object> entry : itemScoreObject.entrySet()) {
                        FdycpSurveyItem surveyItem = surveyItemMap.get(entry.getKey());
                        if (surveyItem != null)
                            titleList.add(surveyItem.getKhpc());
                        propertyList.add("itemScoreObject." + entry.getKey());
                    }
                }
            }
            propertyList.add("score");
            propertyList.add("rank");
            propertyList.add("grade");
            titleList.add("测评总分");
            titleList.add("排名");
            titleList.add("考核等次");
            EasyExcelHelper.exportExcel(list, titleList.toArray(new String[]{}), propertyList.toArray(new String[]{}),
                    response, null, "辅导员测评汇总数据");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @GetMapping("/years")
    public List<HashMap<String, String>> years() {
        List<String> years = fdycpAnswerInfoService.years();
        List<HashMap<String, String>> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(years)) {
            for (String year : years) {
                list.add(new HashMap<>() {{
                    put("name", year);
                }});
            }
        }
        return list;
    }

    @GetMapping("/surveyItems")
    public List<FdycpSurveyItem> surveyItems(@RequestParam("nf") String nf) {
        return fdycpSurveyItemService.listByAnswerInfo(nf);
    }

    /**
     * 根据问卷查询答卷信息
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpResult:list')")
    @GetMapping("/pageAnswerInfo")
    public PageResult<FdycpAnswerInfo> pageAnswerInfo(FdycpAnswerInfoParam param) {
        return fdycpAnswerInfoService.page(param);
    }

    /**
     * 根据答卷及测评记录查询答题结果
     */
    @GetMapping("/surveyAnswer")
    public SurveyVO surveyAnswer(String xgh, String bcpr, String surveyItemId) {
        if (!StringUtils.hasText(bcpr) || !StringUtils.hasText(surveyItemId) ||
                !StringUtils.hasText(xgh))
            AssertUtil.throwMessage(ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        FdycpAnswerInfoParam param = new FdycpAnswerInfoParam();
        param.setBcpr(bcpr);
        param.setSurveyItemId(surveyItemId);
        param.setXgh(xgh);
        List<FdycpAnswerInfo> list = fdycpAnswerInfoService.list(param);
        FdycpAnswerInfo answerInfo = CommonUtil.listGetOne(list);
        if (answerInfo == null) AssertUtil.throwMessage("答卷不存在");
        return surveyService.getSurveyVO(answerInfo.getSid(), answerInfo, FdycpAnswer.class);
    }
}
