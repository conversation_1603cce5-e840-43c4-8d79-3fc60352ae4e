package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 辅导员测评指标-测评对象
 * Created by JIANGPING on 2025/3/5.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_ITEM_STAFF")
@Entity
@Table(name = "SYT_FDYCP_ITEM_STAFF")
public class FdycpItemStaff implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 测评指标
     */
    @Column(name = "item_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField(value = "item_ID")
    private String itemId;
    /**
     * 测评对象
     */
    @Column(name = "CPDX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPDX")
    private String cpdx;
}
