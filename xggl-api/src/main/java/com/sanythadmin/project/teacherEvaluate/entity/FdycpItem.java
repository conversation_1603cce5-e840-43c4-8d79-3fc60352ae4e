package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 辅导员测评指标
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_ITEM")
@Entity
@Table(name = "SYT_FDYCP_ITEM")
public class FdycpItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 考核批次
     */
    @Column(name = "KHPC")
    @TableField("KHPC")
    private String khpc;

    /**
     * 指标类型
     */
    @Column(name = "ZBLX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZBLX")
    private String zblx;

    /**
     * 固定分值/百分比值
     */
    @Column(name = "ZBCKZ", columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("ZBCKZ")
    private Double zbckz;

    /**
     * 是否参与计算（1：是， 0：否）
     */
    @Column(name = "SFCYJS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFCYJS")
    private JudgeMark sfcyjs;

    /**
     * 答卷人角色
     */
    @Column(name = "DJRJS")
    @TableField("DJRJS")
    private String djrjs;

    /**
     * 测评对象
     */
//    @Column(name = "CPDX")
//    @TableField("CPDX")
//    private String cpdx;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 测评对象角色
     */
    @Column(name = "CPDXJS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CPDXJS")
    private String cpdxjs;
    @Column(name = "CPDXJSID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CPDXJSID")
    private String cpdxjsid;

    /**
     * 年份
     */
//    @Column(name = "nf")
//    @TableField("nf")
//    private String nf;
}
