package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 辅导员测评成绩等级
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_GRADE")
@Entity
@Table(name = "SYT_FDYCP_GRADE")
public class FdycpGrade implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 最小值
     */
    @Column(name = "MIN_SCORE", columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("MIN_SCORE")
    private Double minScore;

    /**
     * 最大值
     */
    @Column(name = "MAX_SCORE", columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("MAX_SCORE")
    private Double maxScore;

    /**
     * 等级
     */
    @Column(name = "GRADE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("GRADE")
    private String grade;

}
