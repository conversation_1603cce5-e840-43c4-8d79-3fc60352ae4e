package com.sanythadmin.project.teacherEvaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评问卷指标查询参数
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpSurveyItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 答卷人角色
     */
    @QueryField(type = QueryType.EQ)
    private String djrjs;

    /**
     * 考核批次
     */
    private String khpc;

    /**
     * 是否参与计算（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfcyjs;

    /**
     * 指标类型
     */
    @QueryField(type = QueryType.EQ)
    private String zblx;

    /**
     * 问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;
}
