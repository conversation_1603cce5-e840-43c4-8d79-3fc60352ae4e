package com.sanythadmin.project.teacherEvaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评卷答案查询参数
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpAnswerParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 答卷信息ID
     */
    @QueryField(type = QueryType.EQ)
    private String infoId;

    /**
     * 问卷试题ID
     */
    private String contentId;

    /**
     * 分组ID
     */
    private String gid;

    /**
     * 问题类型
     */
    private String qtype;

    /**
     * 问卷ID
     */
    private String sid;

    /**
     * 答题结果
     */
    private String result;

    /**
     * 答题者账号
     */
    private String xgh;

    /**
     * 单选多选答题选项ID
     */
    private String resultId;

    /**
     * 测评年份
     */
    @QueryField(ignore = true)
    private String nf;

    /**
     * 被测评人
     */
    private String bcpr;

    /**
     * 被测评人姓名
     */
    private String bcprxm;

    /**
     * 被测评人单位
     */
    private String bcprdw;

    /**
     * 被测评人测评条件
     */
    private String bcprtype;

    /**
     * 答题人角色域
     */
    private String dtrscope;

    /**
     * 答题人测评条件
     */
    private String dtrtype;

    /**
     * 测评指标ID
     */
    @QueryField(type = QueryType.EQ)
    private String surveyItemId;

    /**
     * 答题人学院
     */
    private String dtrxymc;

    /**
     * 答题人班级
     */
    private String dtrbjmc;

}
