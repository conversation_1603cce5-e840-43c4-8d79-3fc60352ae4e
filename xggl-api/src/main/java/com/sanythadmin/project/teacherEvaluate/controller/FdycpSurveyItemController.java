package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemService;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;
import com.sanythadmin.project.teacherEvaluate.param.FdycpSurveyItemParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 辅导员测评问卷指标控制器
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-survey-item")
public class FdycpSurveyItemController extends BaseController {
    @Resource
    private FdycpSurveyItemService fdycpSurveyItemService;

    /**
     * 分页查询辅导员测评问卷指标（权限标识：teacherEvaluate:fdycpSurveyItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItem:list')")
    @GetMapping("/page")
    public PageResult<FdycpSurveyItem> page(FdycpSurveyItemParam param) {
        PageParam<FdycpSurveyItem, FdycpSurveyItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = fdycpSurveyItemService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部辅导员测评问卷指标（权限标识：teacherEvaluate:fdycpSurveyItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItem:list')")
    @GetMapping()
    public List<FdycpSurveyItem> list(FdycpSurveyItemParam param) {
        PageParam<FdycpSurveyItem, FdycpSurveyItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdycpSurveyItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员测评问卷指标（权限标识：teacherEvaluate:fdycpSurveyItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItem:list')")
    @GetMapping("/{id}")
    public FdycpSurveyItem get(@PathVariable("id") String id) {
        return fdycpSurveyItemService.getById(id);
    }

    /**
     * 添加或修改辅导员测评问卷指标（权限标识：teacherEvaluate:fdycpSurveyItem:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItem:operation')")
    @OperationLog(module = "辅导员测评问卷指标", comments = "保存辅导员测评问卷指标")
    @PostMapping("/operation")
    public void save(@RequestBody FdycpSurveyItem fdycpSurveyItem) {
        if (StringUtils.hasLength(fdycpSurveyItem.getId())) {
            fdycpSurveyItemService.updateById(fdycpSurveyItem);
        } else {
            fdycpSurveyItemService.save(fdycpSurveyItem);
        }
    }

    /**
     * 批量删除辅导员测评问卷指标（权限标识：teacherEvaluate:fdycpSurveyItem:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItem:remove')")
    @OperationLog(module = "辅导员测评问卷指标", comments = "批量删除辅导员测评问卷指标")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        fdycpSurveyItemService.removeByIds(ids);
    }
}
