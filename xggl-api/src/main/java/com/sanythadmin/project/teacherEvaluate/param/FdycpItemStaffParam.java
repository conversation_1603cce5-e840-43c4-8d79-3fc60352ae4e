package com.sanythadmin.project.teacherEvaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评指标查询参数
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpItemStaffParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 指标ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;


    /**
     * 测评对象
     */
    @QueryField(type = QueryType.EQ)
    private String cpdx;

}
