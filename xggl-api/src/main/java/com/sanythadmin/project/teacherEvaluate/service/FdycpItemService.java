package com.sanythadmin.project.teacherEvaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDTO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpItemDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 辅导员测评指标Service
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
public interface FdycpItemService extends IService<FdycpItem> {

    public void editItem(FdycpItemDTO item);
    public List<Map<String, String>> evaluateStaff();
    public List<FdycpItemDTO> convert(Collection<FdycpItem> collection);
}
