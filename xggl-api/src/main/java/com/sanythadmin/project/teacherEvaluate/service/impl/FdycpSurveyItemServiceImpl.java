package com.sanythadmin.project.teacherEvaluate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.SqlUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.enums.SurveyType;
import com.sanythadmin.project.survey.mapper.SurveyMapper;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.teacherEvaluate.constant.ConstantsTeacherEvaluate;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpItemDTO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpSurveyDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpItem;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItemStaff;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpItemMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpSurveyItemMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpSurveyItemStaffMapper;
import com.sanythadmin.project.teacherEvaluate.param.FdycpSurveyItemParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpSurveyItemStaffParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerInfoService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemService;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 辅导员测评问卷指标Service实现
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@AllArgsConstructor
@Service
public class FdycpSurveyItemServiceImpl extends ServiceImpl<FdycpSurveyItemMapper, FdycpSurveyItem> implements FdycpSurveyItemService {
    private final FdycpSurveyItemMapper surveyItemMapper;
    private final FdycpSurveyItemStaffMapper surveyItemStaffMapper;
    private final FdycpAnswerMapper fdycpAnswerMapper;
    private final SurveyMapper surveyMapper;
    private final UserInfoMapper userInfoMapper;
    private final UserDataScopeService userDataScopeService;
    private final SysParamService sysParamService;
    private final SysRoleService sysRoleService;
    private final SysAccountRoleMapper sysAccountRoleMapper;
    private final FdycpAnswerInfoService answerInfoService;
    private final FdycpItemMapper fdycpItemMapper;


    @Override
    public List<FdycpSurveyDTO> convert(Collection<Survey> collection) {
        List<FdycpSurveyDTO> list = new ArrayList<>();
        for (Survey survey : collection) {
            FdycpSurveyDTO dto = new FdycpSurveyDTO();
            BeanUtils.copyProperties(survey, dto);
            List<FdycpSurveyItem> items = surveyItemMapper.selectList(new LambdaQueryWrapper<FdycpSurveyItem>()
                    .eq(FdycpSurveyItem::getSid, survey.getId()));
            if (!CollectionUtils.isEmpty(items)) {
                FdycpItemDTO item = new FdycpItemDTO();
                FdycpSurveyItem surveyItem = items.get(0);
                BeanUtils.copyProperties(surveyItem, item);
                List<FdycpSurveyItemStaff> surveyItemStaffs = surveyItemStaffMapper.selectList(new LambdaQueryWrapper<FdycpSurveyItemStaff>()
                        .eq(FdycpSurveyItemStaff::getSurveyItemId, item.getId())
                );
                Set<String> set = surveyItemStaffs.stream().map(FdycpSurveyItemStaff::getCpdx).collect(Collectors.toSet());
                item.setCpdx(CommonUtil.appendComma(set.toArray(new String[]{})));
                dto.setItemId(surveyItem.getItemId());
                dto.setItem(item);
                dto.setNf(surveyItem.getNf());
            }
            list.add(dto);
        }
        return list;
    }

    @Transactional
    @Override
    public FdycpSurveyDTO saveSurvey(FdycpSurveyDTO surveyDTO) {
        Survey survey = new Survey();
        BeanUtil.copyProperties(surveyDTO, survey);
        survey.setStatus(SurveyState.ENABLED);
        survey.setWjms(SurveyType.GDXT);
        if (StringUtils.hasText(survey.getId())) {
            // 判断是否有答卷记录
            Long count = fdycpAnswerMapper.selectCount(new LambdaQueryWrapper<FdycpAnswer>()
                    .eq(FdycpAnswer::getSid, survey.getId()));
            if (count > 0)
                AssertUtil.throwMessage("存在已答卷记录不可编辑");
            surveyMapper.updateById(survey);
            surveyItemMapper.delete(new LambdaQueryWrapper<FdycpSurveyItem>()
                    .eq(FdycpSurveyItem::getSid, survey.getId()));
            surveyItemStaffMapper.delete(new LambdaQueryWrapper<FdycpSurveyItemStaff>()
                    .eq(FdycpSurveyItemStaff::getSid, survey.getId()));
        } else {
            survey.setCreateTime(LocalDateTime.now());
            survey.setXgh(SecurityUtil.getUsername());
            survey.setXm(SecurityUtil.getRealName());
            surveyMapper.insert(survey);
        }

        FdycpItem fdycpItem = fdycpItemMapper.selectById(surveyDTO.getItemId());
        SysParam sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPNF);
        FdycpSurveyItem item = new FdycpSurveyItem();
        BeanUtil.copyProperties(surveyDTO.getItem(), item);
        item.setSid(survey.getId());
        item.setNf(sysParam.getParamValue());
        item.setItemId(surveyDTO.getItemId());
        item.setDjrjsid(fdycpItem.getDjrjs());
        item.setCpdxjsid(fdycpItem.getCpdxjsid());
        item.setCpdxjs(fdycpItem.getCpdxjs());
        item.setId(null);
        surveyItemMapper.insert(item);
        Arrays.stream(CommonUtil.split(surveyDTO.getItem().getCpdx()))
                .forEach(s -> surveyItemStaffMapper.insert(
                        new FdycpSurveyItemStaff(null, s, survey.getId(), item.getId())));
        surveyDTO.setId(survey.getId());
        return surveyDTO;
    }

    @Transactional
    @Override
    public void removeBySurvey(List<String> surveyId) {
        surveyItemMapper.delete(new LambdaQueryWrapper<FdycpSurveyItem>()
                .in(FdycpSurveyItem::getSid, surveyId));
        surveyItemStaffMapper.delete(new LambdaQueryWrapper<FdycpSurveyItemStaff>()
                .in(FdycpSurveyItemStaff::getSid, surveyId));
    }

    public String appendCondition(MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper, UserInfo userInfo, List<String> roleIds) {
        StringBuilder builder = new StringBuilder("SELECT 1 FROM SYT_USER_DATA_SCOPE DATA_SCOPE WHERE "
                + wrapper.getAlias() + ".XGH = DATA_SCOPE.GLZ_XGH AND DATA_SCOPE.ROLE_ID in (" + CommonUtil.str2SqlIn(roleIds.toArray(new String[]{})) + ") AND ");
        MyMPJLambdaWrapper<UserDataScope, UserDataScopeParam> userDataScopeWrapper = new MyMPJLambdaWrapper<>();
        userDataScopeWrapper.setAlias("DATA_SCOPE");
        userDataScopeService.appendQueryScope(userDataScopeWrapper, userInfo);
        builder.append(SqlUtil.getRealSql(userDataScopeWrapper));
        return builder.toString();
    }

    private List<UserInfo> advisors(UserInfo userInfo, SFunction<UserInfo, ?> sFunction, List<String> roleIds) {
        SysRole role = SecurityUtil.getRole();
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>();
        SysParam sysParam;
        switch (role.getRoleScope()) {
            case Constants.ROLE_SCOPE_GR:
                wrapper.exists(appendCondition(wrapper, userInfo, roleIds));
                break;
            case Constants.ROLE_SCOPE_BJ:
                sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYSFZP);
                if (!Objects.isNull(sysParam) && !Objects.equals(JudgeMark.YES.getText(), sysParam.getParamValue()))
                    wrapper.ne(UserInfo::getXgh, userInfo.getXgh());
                UserDataScopeParam param = new UserDataScopeParam();
                param.setGlzXgh(userInfo.getXgh());
                param.setRoleId(role.getId());
                UserDataScope dataScope = userDataScopeService.list2Single(userDataScopeService.queryList(param), false);
                userInfo = new UserInfo();
                userInfo.setXgh(dataScope.getXgh());
                userInfo.setPyccid(dataScope.getPyccid());
                userInfo.setNjid(dataScope.getNjid());
                userInfo.setXyid(dataScope.getXyid());
                userInfo.setZyid(dataScope.getZyid());
                userInfo.setBjid(dataScope.getBjid());
                wrapper.exists(appendCondition(wrapper, userInfo, roleIds));
                break;
            case Constants.ROLE_SCOPE_BY:
                sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYSFZP);
                if (!Objects.isNull(sysParam) && !Objects.equals(JudgeMark.YES.getText(), sysParam.getParamValue()))
                    wrapper.ne(UserInfo::getXgh, userInfo.getXgh());
                wrapper.exists("SELECT 1 FROM SYT_USER_ORG_MAP ORG WHERE ORG.XYID IN (" + CommonUtil.str2SqlIn(userInfo.getXyid()) + ") AND ORG.XGH = " + wrapper.getAlias() + ".XGH");
                break;
        }
        wrapper.select(UserInfo::getXgh, UserInfo::getXm, sFunction).isNotNull(sFunction);
        return userInfoMapper.selectJoinList(UserInfo.class, wrapper);
    }

    @Override
    public PageResult<FdycpSurveyDTO> pageSurveyByAnswerer(SurveyParam param) {
        SysParam sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPTJ);
        if (sysParam == null) return null;
        UserInfo userInfo = SecurityUtil.getUserInfo();
        String roleName = SecurityUtil.getRoleName();
        RoleParam roleParam = new RoleParam();
        roleParam.setRoleScope(Constants.ROLE_SCOPE_BJ);
        List<SysRole> roles = sysRoleService.list(roleParam);
        List<String> roleIds = roles.stream().map(SysRole::getId).toList();
        SFunction<UserInfo, String> sFunction = CommonUtil.getSFunction(UserInfo.class, sysParam.getParamValue());
        List<UserInfo> userInfos = advisors(userInfo, sFunction, roleIds);
        if (CollectionUtils.isEmpty(userInfos)) return null;
        List<SysAccountRole> accountRoles = sysAccountRoleMapper.selectList(new LambdaQueryWrapper<SysAccountRole>()
                .in(SysAccountRole::getUsername, userInfos.stream().map(UserInfo::getXgh).toList())
                .in(SysAccountRole::getRoleId, roleIds));
        Set<String> testStaff = new HashSet<>();
        userInfos.forEach(user -> {
            testStaff.add(String.valueOf(CommonUtil.getValue(sysParam.getParamValue(), user)));
            List<String> list = accountRoles.stream().filter(accountRole -> Objects.equals(user.getXgh(), accountRole.getUsername()))
                    .map(SysAccountRole::getRoleId).toList();
            user.setRoleId(CommonUtil.strJoin(list));
        });


        MyMPJLambdaWrapper<Survey, SurveyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAll(Survey.class).selectAs(FdycpSurveyItem::getId, FdycpSurveyDTO::getSurveyItemId)
                .selectAs(FdycpSurveyItemStaff::getCpdx, FdycpSurveyDTO::getCpdx).select(FdycpSurveyItem::getCpdxjsid)
                .leftJoin(FdycpSurveyItem.class, FdycpSurveyItem::getSid, Survey::getId)
                .leftJoin(FdycpSurveyItemStaff.class, FdycpSurveyItemStaff::getSurveyItemId, FdycpSurveyItem::getId)
                .eq(FdycpSurveyItem::getDjrjs, roleName).in(FdycpSurveyItemStaff::getCpdx, testStaff);
        List<FdycpSurveyDTO> list = surveyMapper.selectJoinList(FdycpSurveyDTO.class, wrapper);
        if (CollectionUtils.isEmpty(list)) return null;
        List<FdycpSurveyDTO> newlist = new ArrayList<>();
        for (UserInfo user : userInfos) {
            String staff = String.valueOf(CommonUtil.getValue(sysParam.getParamValue(), user));
            list.forEach(dto -> {
                List<String> tempList = new ArrayList<>(Arrays.asList(CommonUtil.split(dto.getCpdxjsid())));
                tempList.retainAll(Arrays.asList(CommonUtil.split(user.getRoleId())));
                if (Objects.equals(staff, dto.getCpdx()) && !CollectionUtils.isEmpty(tempList)) {
                    FdycpSurveyDTO obj = new FdycpSurveyDTO();
                    BeanUtils.copyProperties(dto, obj);
                    obj.setBcpr(user.getXgh());
                    obj.setBcprxm(user.getXm());
                    newlist.add(obj);
                }
            });
        }

        newlist.sort(Comparator.comparing(FdycpSurveyDTO::getCreateTime).reversed());
        int total = newlist.size();
        list = CommonUtil.pageList(newlist, param.getPage().intValue(), param.getLimit().intValue());
        list.forEach(survey -> {
            Long count = answerInfoService.testeeUserCount(survey.getId(), userInfo.getXgh(), survey.getBcpr());
            survey.setAnswered(count > 0);
        });
        return new PageResult<>(list, (long) total);
    }

    @Override
    public List<UserInfo> testeeUserList(String surveyId) {
        MyMPJLambdaWrapper<FdycpSurveyItemStaff, FdycpSurveyItemStaffParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.innerJoin(FdycpSurveyItem.class, FdycpSurveyItem::getId, FdycpSurveyItemStaff::getSurveyItemId);
        wrapper.eq(FdycpSurveyItem::getSid, surveyId);
        wrapper.eq(FdycpSurveyItemStaff::getSid, surveyId);
        wrapper.selectAll(FdycpSurveyItemStaff.class);
        List<FdycpSurveyItemStaff> surveyItemStaffs = surveyItemStaffMapper.selectJoinList(FdycpSurveyItemStaff.class, wrapper);

        SysRole role = SecurityUtil.getRole();
        UserInfo userInfo = SecurityUtil.getUserInfo();
        SysParam sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPTJ);
        SFunction<UserInfo, Object> sFunction = CommonUtil.getSFunction(UserInfo.class, sysParam.getParamValue());
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper1 = new MyMPJLambdaWrapper<>();
        wrapper1.in(sFunction, surveyItemStaffs.stream().map(FdycpSurveyItemStaff::getCpdx).toList());
        wrapper1.exists("select 1 from syt_sys_account_role acc_r where acc_r.username = " + wrapper1.getAlias() + ".xgh " +
                "and exists (select 1 from syt_sys_role r where r.id = acc_r.role_id and r.role_scope ={0})", Constants.ROLE_SCOPE_BJ);
        if (Objects.equals(role.getRoleScope(), Constants.ROLE_SCOPE_GR)) {
            String condition = userDataScopeCondition(userInfo, null, wrapper1.getAlias());
            wrapper1.exists(condition);
        } else {
            sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYSFZP);
            if (!Objects.isNull(sysParam) && !Objects.equals(JudgeMark.YES.getText(), sysParam.getParamValue()))
                wrapper1.ne(UserInfo::getXgh, userInfo.getXgh());
            if (Objects.equals(Constants.ROLE_SCOPE_BJ, role.getRoleScope())) {
                UserDataScopeParam param = new UserDataScopeParam();
                param.setGlzXgh(userInfo.getXgh());
                param.setRoleId(role.getId());
                List<UserDataScope> scopes = userDataScopeService.queryList(param);
                UserDataScope dataScope = userDataScopeService.list2Single(scopes, false);
                String condition = userDataScopeCondition(null, dataScope, wrapper1.getAlias());
                wrapper1.exists(condition);
            } else if (Objects.equals(Constants.ROLE_SCOPE_BY, role.getRoleScope())) {
                wrapper1.eq(UserInfo::getXyid, userInfo.getXyid());
            }
        }
        return userInfoMapper.selectJoinList(UserInfo.class, wrapper1);
    }

    @Override
    public List<FdycpSurveyItem> listByAnswerInfo(String nf) {
        MyMPJLambdaWrapper<FdycpSurveyItem, FdycpSurveyItemParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.select(FdycpSurveyItem::getItemId, FdycpSurveyItem::getKhpc)
                .exists("SELECT 1 FROM SYT_FDYCP_ANSWER_INFO WHERE NF='" + nf + "' " +
                        "AND " + wrapper.getAlias() + ".ID = SURVEY_ITEM_ID GROUP BY SURVEY_ITEM_ID")
                .groupBy(FdycpSurveyItem::getItemId, FdycpSurveyItem::getKhpc);
        return baseMapper.selectJoinList(wrapper);
    }

    private String userDataScopeCondition(UserInfo userInfo, UserDataScope userDataScope, String alias) {
        StringBuilder sql = new StringBuilder("SELECT 1 FROM syt_user_data_scope data_scope WHERE ");
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("data_scope.XGH", userInfo == null ? userDataScope.getXgh() : userInfo.getXgh());
        hashMap.put("data_scope.ZYID", userInfo == null ? userDataScope.getZyid() : userInfo.getZyid());
        hashMap.put("data_scope.BJID", userInfo == null ? userDataScope.getBjid() : userInfo.getBjid());
        hashMap.put("data_scope.XYID", userInfo == null ? userDataScope.getXyid() : userInfo.getXyid());
        hashMap.put("data_scope.NJID", userInfo == null ? userDataScope.getNjid() : userInfo.getNjid());
        hashMap.put("data_scope.PYCCID", userInfo == null ? userDataScope.getPyccid() : userInfo.getPyccid());
        int index = 0;
        for (Map.Entry<String, String> entry : hashMap.entrySet()) {
            index++;
            String key = entry.getKey();
            String value = entry.getValue();
            if (StringUtils.hasText(value)) {
                sql.append("(").append(key).append(" in (").append(CommonUtil.str2SqlIn(value)).append(") OR ").append(key).append(" IS NULL)");
            } else {
                sql.append("(").append(key).append(" IS NULL)");
            }
            if (index < hashMap.size())
                sql.append(" AND ");
        }
        sql.append(" AND data_scope.GLZ_XGH=").append(alias).append(".xgh");
        return sql.toString();
    }
}
