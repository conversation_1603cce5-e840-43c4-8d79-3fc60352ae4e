package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswer;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import com.sanythadmin.project.teacherEvaluate.dto.AnswerInfoDTO;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpSurveyDTO;
import com.sanythadmin.project.teacherEvaluate.dto.TesteeInfoDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswer;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswerInfo;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerInfoService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpAnswerService;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 辅导员测评/答题端管理控制器
 * Created by JIANGPING on 2025/3/6.
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-answerer")
public class FdycpAnswererController extends BaseController {
    @Resource
    private FdycpAnswerService fdycpAnswerService;
    @Resource
    private FdycpSurveyItemService fdycpSurveyItemService;
    @Resource
    private SurveyService surveyService;
    @Resource
    private FdycpAnswerInfoService answerInfoService;


    /**
     * 测评答卷分页列表（权限标识：teacherEvaluate:answerer:operation）
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:answerer:list')")
    @GetMapping("/pageSurveyList")
    public PageResult<FdycpSurveyDTO> pageSurveyList(SurveyParam param) {
        return fdycpSurveyItemService.pageSurveyByAnswerer(param);
    }

    /**
     * 根据指标测评对象查询测评人员
     *
     * @param surveyId
     * @return
     */
    @GetMapping("/testeeUserList")
    public List<UserInfo> testeeUserList(String surveyId) {
        return fdycpSurveyItemService.testeeUserList(surveyId);
    }

    /**
     * 答题结果/被测评人分页列表
     *
     * @param answer
     * @return
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:answerer:list')")
    @GetMapping("/pageTesteeList")
    public PageResult<TesteeInfoDTO> pageTesteeList(FdycpAnswerParam answer) {
        answer.setXgh(SecurityUtil.getUsername());
        return fdycpAnswerService.testeeListQuery(answer);
    }

    /**
     * 添加或修改辅导员测评卷答案（权限标识：teacherEvaluate:answerer:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:answerer:operation')")
    @OperationLog(module = "辅导员测评卷答案", comments = "保存辅导员测评卷答案")
    @PostMapping("/operation")
    public void operation(MultipartHttpServletRequest request) {
        Map<String, Object> parsedData = CommonUtil.parseFormDataRequest(request);
        AnswerInfoDTO answerInfoDTO = CommonUtil.toBean(parsedData, AnswerInfoDTO.class);
        answerInfoDTO.setIp(CommonUtil.getIpAddr(request));
        fdycpAnswerService.saveAnswer(answerInfoDTO);
    }

    /**
     * 根据答卷及测评记录查询答题结果
     */
    @GetMapping("/surveyAnswer")
    public SurveyVO surveyAnswer(String bcpr, String surveyItemId) {
        if (!StringUtils.hasText(bcpr) || !StringUtils.hasText(surveyItemId))
            AssertUtil.throwMessage(ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        FdycpAnswerInfoParam param = new FdycpAnswerInfoParam();
        param.setBcpr(bcpr);
        param.setSurveyItemId(surveyItemId);
        param.setXgh(SecurityUtil.getUsername());
        List<FdycpAnswerInfo> list = answerInfoService.list(param);
        FdycpAnswerInfo answerInfo = CommonUtil.listGetOne(list);
        if (answerInfo == null) AssertUtil.throwMessage("答卷不存在");
        return surveyService.getSurveyVO(answerInfo.getSid(), answerInfo, FdycpAnswer.class);
    }
}
