package com.sanythadmin.project.teacherEvaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 辅导员测评问卷指标对象
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDYCP_SURVEY_ITEM_STAFF")
@Entity
@Table(name = "SYT_FDYCP_SURVEY_ITEM_STAFF")
public class FdycpSurveyItemStaff implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 测评对象
     */
    @Column(name = "CPDX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPDX")
    private String cpdx;

    /**
     * 问卷ID
     */
    @Column(name = "SID", columnDefinition = ColumnType.CHAR_32)
    @TableField("SID")
    private String sid;

    /**
     * 答卷指标ID
     */
    @Column(name = "SURVEY_ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("SURVEY_ITEM_ID")
    private String surveyItemId;
}
