package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.teacherEvaluate.service.FdycpSurveyItemStaffService;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItemStaff;
import com.sanythadmin.project.teacherEvaluate.param.FdycpSurveyItemStaffParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 辅导员测评问卷指标对象控制器
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-survey-item-staff")
public class FdycpSurveyItemStaffController extends BaseController {
    @Resource
    private FdycpSurveyItemStaffService fdycpSurveyItemStaffService;

    /**
     * 分页查询辅导员测评问卷指标对象（权限标识：teacherEvaluate:fdycpSurveyItemStaff:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItemStaff:list')")
    @GetMapping("/page")
    public PageResult<FdycpSurveyItemStaff> page(FdycpSurveyItemStaffParam param) {
        PageParam<FdycpSurveyItemStaff, FdycpSurveyItemStaffParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = fdycpSurveyItemStaffService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部辅导员测评问卷指标对象（权限标识：teacherEvaluate:fdycpSurveyItemStaff:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItemStaff:list')")
    @GetMapping()
    public List<FdycpSurveyItemStaff> list(FdycpSurveyItemStaffParam param) {
        PageParam<FdycpSurveyItemStaff, FdycpSurveyItemStaffParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdycpSurveyItemStaffService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员测评问卷指标对象（权限标识：teacherEvaluate:fdycpSurveyItemStaff:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItemStaff:list')")
    @GetMapping("/{id}")
    public FdycpSurveyItemStaff get(@PathVariable("id") String id) {
        return fdycpSurveyItemStaffService.getById(id);
    }

    /**
     * 添加或修改辅导员测评问卷指标对象（权限标识：teacherEvaluate:fdycpSurveyItemStaff:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItemStaff:operation')")
    @OperationLog(module = "辅导员测评问卷指标对象", comments = "保存辅导员测评问卷指标对象")
    @PostMapping("/operation")
    public void save(@RequestBody FdycpSurveyItemStaff fdycpSurveyItemStaff) {
        if (StringUtils.hasLength(fdycpSurveyItemStaff.getId())) {
            fdycpSurveyItemStaffService.updateById(fdycpSurveyItemStaff);
        } else {
            fdycpSurveyItemStaffService.save(fdycpSurveyItemStaff);
        }
    }

    /**
     * 批量删除辅导员测评问卷指标对象（权限标识：teacherEvaluate:fdycpSurveyItemStaff:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpSurveyItemStaff:remove')")
    @OperationLog(module = "辅导员测评问卷指标对象", comments = "批量删除辅导员测评问卷指标对象")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        fdycpSurveyItemStaffService.removeByIds(ids);
    }
}
