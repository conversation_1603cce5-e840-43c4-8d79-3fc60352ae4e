package com.sanythadmin.project.teacherEvaluate.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评问卷指标对象查询参数
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpSurveyItemStaffParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 测评对象
     */
    private String cpdx;

    /**
     * 答卷指标ID
     */
    private String surveyItemId;

}
