<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpGradeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, MIN_SCORE, MAX_SCORE, GRADE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.minScore != null">
                        AND a.MIN_SCORE LIKE concat(concat('%',#{param.minScore), '%')
                    </if>
                    <if test="param.maxScore != null">
                        AND a.MAX_SCORE LIKE concat(concat('%',#{param.maxScore), '%')
                    </if>
                    <if test="param.grade != null">
                        AND a.GRADE LIKE concat(concat('%',#{param.grade), '%')
                    </if>
    </sql>
</mapper>
