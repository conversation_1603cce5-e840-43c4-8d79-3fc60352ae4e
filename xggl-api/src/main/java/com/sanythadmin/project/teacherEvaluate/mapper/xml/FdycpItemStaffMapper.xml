<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpItemStaffMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, item_ID, cpdx
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID LIKE concat(concat('%',#{param.id), '%')
        </if>
        <if test="param.itemId != null">
            AND a.item_ID LIKE concat(concat('%',#{param.itemId), '%')
        </if>
        <if test="param.cpdx != null">
            AND a.cpdx = #{cpdx}
        </if>
    </sql>
</mapper>
