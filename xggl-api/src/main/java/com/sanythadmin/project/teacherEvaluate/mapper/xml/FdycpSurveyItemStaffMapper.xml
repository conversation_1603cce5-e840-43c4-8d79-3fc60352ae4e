<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpSurveyItemStaffMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CPDX, SURVEY_ITEM_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.cpdx != null">
                        AND a.CPDX LIKE concat(concat('%',#{param.cpdx), '%')
                    </if>
                    <if test="param.surveyItemId != null">
                        AND a.SURVEY_ITEM_ID LIKE concat(concat('%',#{param.surveyItemId), '%')
                    </if>
    </sql>
</mapper>
