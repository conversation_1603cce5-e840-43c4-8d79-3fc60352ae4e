package com.sanythadmin.project.teacherEvaluate.dto;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * 答题者信息统计
 */
@Data
public class AnswererInfoDTO {

    /**
     * 测评年份
     */
    private String nf;

    /**
     * 被测评人
     */
    private String bcpr;

    /**
     * 答卷ID
     */
    private String sid;
    /**
     * 指标ID
     */
    private String surveyItemId;
    /**
     * 答卷名称
     */
    private String name;
    /**
     * 答卷发布时间
     */
    private LocalDateTime createDate;

    /**
     * 应测评人数
     */
    private Integer ycprs;
    /**
     * 实际测评人数
     */
    private Integer sjcprs;

    /**
     * 实际测评人数占比
     */
    private String sjcprszb;

    /**
     * 答卷人角色ID
     */
    private String djrjsid;

    /**
     * 测评对象角色ID
     */
    private String cpdxjsid;
}
