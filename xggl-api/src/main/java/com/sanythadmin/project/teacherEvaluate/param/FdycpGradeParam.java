package com.sanythadmin.project.teacherEvaluate.param;

import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 辅导员测评成绩等级查询参数
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpGradeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 等级
     */
    private String grade;

}
