package com.sanythadmin.project.teacherEvaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpSurveyDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpSurveyItem;

import java.security.spec.NamedParameterSpec;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 辅导员测评问卷指标Service
 *
 * <AUTHOR>
 * @since 2025-03-06 08:57:43
 */
public interface FdycpSurveyItemService extends IService<FdycpSurveyItem> {
    public List<FdycpSurveyDTO> convert(Collection<Survey> collection);
    public FdycpSurveyDTO saveSurvey(FdycpSurveyDTO survey);
    public void removeBySurvey(List<String> surveyId);
    public PageResult<FdycpSurveyDTO> pageSurveyByAnswerer(SurveyParam param);
    public List<UserInfo> testeeUserList(String surveyId);
    public List<FdycpSurveyItem> listByAnswerInfo(String nf);
}
