<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherEvaluate.mapper.FdycpAnswerMapper">
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.infoId != null">
                AND a.INFO_ID = #{param.infoId}
            </if>
            <if test="param.contentId != null">
                AND a.CONTENT_ID LIKE concat(concat('%',#{param.contentId), '%')
            </if>
            <if test="param.gid != null">
                AND a.GID LIKE concat(concat('%',#{param.gid), '%')
            </if>
            <if test="param.qtype != null">
                AND a.QTYPE LIKE concat(concat('%',#{param.qtype), '%')
            </if>
            <if test="param.sid != null">
                AND a.SID = #{param.sid}
            </if>
            <if test="param.result != null">
                AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
            </if>
            <if test="param.xgh != null">
                AND a.XGH = #{param.xgh}
            </if>
            <if test="param.resultId != null">
                AND a.RESULT_ID LIKE concat(concat('%',#{param.resultId), '%')
            </if>
    </sql>

    <sql id="answererScoreSummary_query">
        select b.nf,a.sid, a.survey_item_id, a.xgh, b.xm, b.bcpr, b.bcprxm, sum(point) score,b.bcprdw,b.bcprtype
        from syt_fdycp_answer a left join
        syt_fdycp_answer_info b on a.info_id = b.id
        <where>
            <include refid="Base_Query_Condition"/>
            <if test="param.nf != null">
                AND b.nf = #{param.nf}
            </if>
        </where>
        group by b.nf, a.sid, a.survey_item_id, a.xgh, b.xm, b.bcpr, b.bcprxm,b.bcprdw,b.bcprtype
    </sql>

    <select id="answererScoreSummary" parameterType="com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam">
        <include refid="answererScoreSummary_query"/>
    </select>

    <select id="testeeItemScoreSummary" parameterType="com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam">
        select t.nf, t.bcpr, t.bcprxm, t.sid, t.SURVEY_ITEM_ID, avg(score) score,t.bcprdw,t.bcprtype
        from (<include refid="answererScoreSummary_query"/>) t
        group by t.nf, t.bcpr, t.bcprxm, t.sid, t.SURVEY_ITEM_ID,t.bcprdw,t.bcprtype
    </select>

    <select id="testeeListQuery" parameterType="com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerParam">
        select a.nf, a.bcpr, a.bcprxm, a.bcprdw from syt_fdycp_answer a
        <where>
            <include refid="Base_Query_Condition"/>
        </where>
        group by a.nf, a.bcpr, a.bcprxm, a.bcprdw
    </select>
</mapper>
