package com.sanythadmin.project.teacherEvaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpAnswerInfo;
import com.sanythadmin.project.teacherEvaluate.param.FdycpAnswerInfoParam;

import java.util.List;

/**
 * @since 2025/7/3.
 */
public interface FdycpAnswerInfoService extends IService<FdycpAnswerInfo> {
    public PageResult<FdycpAnswerInfo> page(FdycpAnswerInfoParam param);
    public List<FdycpAnswerInfo> list(FdycpAnswerInfoParam param);
    public List<String> years();
    public Long testeeUserCount(String sid, String answererUser, String testeeUser);
}
