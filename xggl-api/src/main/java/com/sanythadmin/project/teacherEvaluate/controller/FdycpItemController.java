package com.sanythadmin.project.teacherEvaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpItemDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpItem;
import com.sanythadmin.project.teacherEvaluate.param.FdycpItemParam;
import com.sanythadmin.project.teacherEvaluate.service.FdycpItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 辅导员测评/测评指标控制器
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@RestController
@RequestMapping("/api/teacherEvaluate/fdycp-item")
public class FdycpItemController extends BaseController {
    @Resource
    private FdycpItemService fdycpItemService;

    /**
     * 分页查询辅导员测评指标（权限标识：teacherEvaluate:fdycpItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpItem:list')")
    @GetMapping("/page")
    public PageResult<FdycpItemDTO> page(FdycpItemParam param) {
        PageParam<FdycpItem, FdycpItemParam> page = new PageParam<>(param);
        page = fdycpItemService.page(page, page.getWrapper());
        List<FdycpItemDTO> dtos = fdycpItemService.convert(page.getRecords());
        return new PageResult<>(dtos, page.getTotal());
    }

    /**
     * 查询全部辅导员测评指标（权限标识：teacherEvaluate:fdycpItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpItem:list')")
    @GetMapping()
    public List<FdycpItemDTO> list(FdycpItemParam param) {
        PageParam<FdycpItem, FdycpItemParam> page = new PageParam<>(param);
        List<FdycpItem> list = fdycpItemService.list(page.getOrderWrapper());
        return fdycpItemService.convert(list);
    }

    /**
     * 根据id查询辅导员测评指标（权限标识：teacherEvaluate:fdycpItem:list）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpItem:list')")
    @GetMapping("/{id}")
    public FdycpItemDTO get(@PathVariable("id") String id) {
        FdycpItem item = fdycpItemService.getById(id);
        return CommonUtil.listGetOne(fdycpItemService.convert(Collections.singletonList(item)));
    }

    /**
     * 添加或修改辅导员测评指标（权限标识：teacherEvaluate:fdycpItem:operation）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpItem:operation')")
    @OperationLog(module = "辅导员测评指标", comments = "保存辅导员测评指标")
    @PostMapping("/operation")
    public void save(@RequestBody FdycpItemDTO itemDTO) {
        fdycpItemService.editItem(itemDTO);
    }

    /**
     * 批量删除辅导员测评指标（权限标识：teacherEvaluate:fdycpItem:remove）
     */
    @PreAuthorize("hasAuthority('teacherEvaluate:fdycpItem:remove')")
    @OperationLog(module = "辅导员测评指标", comments = "批量删除辅导员测评指标")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        fdycpItemService.removeByIds(ids);
    }


    /**
     * 指标测评对象
     *
     * @return
     */
    @GetMapping("/evaluateStaff")
    public List<Map<String, String>> evaluateStaff() {
        return fdycpItemService.evaluateStaff();
    }
}
