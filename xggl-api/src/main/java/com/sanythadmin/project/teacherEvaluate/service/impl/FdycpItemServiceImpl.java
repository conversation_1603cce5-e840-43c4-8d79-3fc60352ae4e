package com.sanythadmin.project.teacherEvaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.teacherEvaluate.constant.ConstantsTeacherEvaluate;
import com.sanythadmin.project.teacherEvaluate.dto.FdycpItemDTO;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpItem;
import com.sanythadmin.project.teacherEvaluate.entity.FdycpItemStaff;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpItemMapper;
import com.sanythadmin.project.teacherEvaluate.mapper.FdycpItemStaffMapper;
import com.sanythadmin.project.teacherEvaluate.service.FdycpItemService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 辅导员测评指标Service实现
 *
 * <AUTHOR>
 * @since 2025-01-14 17:42:32
 */
@Service
public class FdycpItemServiceImpl extends ServiceImpl<FdycpItemMapper, FdycpItem> implements FdycpItemService {
    @Resource
    private FdycpItemMapper mapper;
    @Resource
    private FdycpItemStaffMapper staffMapper;
    @Resource
    private SysParamService sysParamService;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private SysRoleService sysRoleService;

    @Transactional
    @Override
    public void editItem(FdycpItemDTO item) {
        RoleParam roleParam = new RoleParam();
        roleParam.setId(item.getCpdxjsid());
        List<SysRole> roles = sysRoleService.list(roleParam);
        item.setCpdxjs(CommonUtil.appendComma(roles.stream().map(SysRole::getName).toList().toArray(new String[]{})));
        if (StringUtils.hasText(item.getId())) {
            mapper.updateById(item);
            staffMapper.delete(new LambdaQueryWrapper<FdycpItemStaff>()
                    .eq(FdycpItemStaff::getItemId, item.getId()));
        } else {
            mapper.insert(item);
        }
        String[] strings = CommonUtil.split(item.getCpdx());
        Arrays.stream(strings).forEach(s -> staffMapper.insert(
                new FdycpItemStaff(null, item.getId(), s)));
    }

    @Override
    public List<Map<String, String>> evaluateStaff() {
        List<Map<String, String>> maps = new ArrayList<>();
        SysParam sysParam = sysParamService.getByParamName(ConstantsTeacherEvaluate.FDYCPTJ);
        if (sysParam != null) {
            SFunction<UserInfo, Object> sFunction = CommonUtil.getSFunction(UserInfo.class, sysParam.getParamValue());
            List<String> objects = userInfoMapper.selectObjs(new LambdaQueryWrapper<UserInfo>()
                    .select(sFunction).ne(UserInfo::getUserType, UserType.STUDENT)
                    .isNotNull(sFunction).groupBy(sFunction));
            for (String s : objects) {
                Map<String, String> map = new HashMap<>();
                map.put("name", s);
                maps.add(map);
            }
        }
        return maps;
    }

    @Override
    public List<FdycpItemDTO> convert(Collection<FdycpItem> collection) {
        List<FdycpItemDTO> list = new ArrayList<>();
        for (FdycpItem item : collection) {
            List<FdycpItemStaff> itemStaffs = staffMapper.selectList(new LambdaQueryWrapper<FdycpItemStaff>()
                    .eq(FdycpItemStaff::getItemId, item.getId()));
            Set<String> set = itemStaffs.stream().map(FdycpItemStaff::getCpdx).collect(Collectors.toSet());
            FdycpItemDTO dto = new FdycpItemDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setCpdx(CommonUtil.appendComma(set.toArray(new String[]{})));
            list.add(dto);
        }
        return list;
    }
}
