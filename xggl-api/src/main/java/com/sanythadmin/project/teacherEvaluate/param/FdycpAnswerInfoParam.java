package com.sanythadmin.project.teacherEvaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * @since 2025/7/4.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdycpAnswerInfoParam extends BaseParam {
    /**
     * 问卷id
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 答卷人账号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 答题者姓名
     */
    private String xm;

    /**
     * 测评年份
     */
    private String nf;

    /**
     * 被测评人
     */
    @QueryField(type = QueryType.EQ)
    private String bcpr;

    /**
     * 被测评人姓名
     */
    private String bcprxm;

    /**
     * 被测评人角色ID
     */
    private String bcprjsid;

    /**
     * 被测评人单位
     */
    private String bcprdw;

    /**
     * 被测评人测评条件
     */
    private String bcprtype;

    /**
     * 答题人角色域
     */
    private String dtrscope;

    /**
     * 答题人测评条件
     */
    private String dtrtype;

    /**
     * 测评指标ID
     */
    @QueryField(type = QueryType.EQ)
    private String surveyItemId;

    /**
     * 答题人学院
     */
    private String dtrxymc;

    /**
     * 答题人班级
     */
    private String dtrbjmc;

    @QueryField(ignore = true)
    private String itemId;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "CREATE_DATE desc";
    }
}
