package com.sanythadmin.project.checkin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.Resp;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.entity.CheckinRecord;
import com.sanythadmin.project.checkin.param.CheckinRecordParam;
import com.sanythadmin.project.userInfo.result.UserInfoResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 签到记录Service
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
public interface CheckinRecordService extends IService<CheckinRecord> {

    /**
     * 查询签到数据
     *
     * @param param 参数
     * @return {@link Page }<{@link UserInfoResult }>
     */
    PageResult<CheckinRecord> pageCheckinData(CheckinRecordParam param,PageParam page);

    /**
     * 查询签到人员数据
     *
     * @return {@link PageResult }<{@link UserInfo }>
     */
    IPage<UserInfo> pageCheckinUserInfoData(CheckinRecordParam param);

    String getCheckinStatus(CheckinRecord record);

    void importData(MultipartFile file, CheckinItem item, UserType userType);

    Resp saveCheckinRecord(CheckinRecordParam param);

    Resp addRecord(CheckinRecordParam param,String blrq);

    Resp getCheckinStatistics(CheckinRecordParam param, String column);

    /**
     * 批量查询项目的签到记录数
     * @param itemIds 项目ID列表
     * @return 项目ID -> 记录数的映射
     */
    Map<String, Integer> recordCountByItemIds(List<String> itemIds,String xgh);

    /**
     * 批量查询项目的签到人数
     * @param itemIds 项目ID列表
     * @return 项目ID -> 记录数的映射
     */
    Map<String, Integer> peopleCountByItemIds(List<String> itemIds);

}
