<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.checkin.mapper.CheckinRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, USERNAME, JD, WD, QDDZ, BZ, CREATE_DATE, DEVICE_ID, ITEM_ID, TYPE, TYPE_ZH, QDFS, QDTJ, IMG, MODIFY_DATE, SJZT, CZR, CZRXM
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE '%'||#{param.username }||'%'
                    </if>
                    <if test="param.jd != null">
                        AND a.JD LIKE '%'||#{param.jd }||'%'
                    </if>
                    <if test="param.wd != null">
                        AND a.WD LIKE '%'||#{param.wd }||'%'
                    </if>
                    <if test="param.qddz != null">
                        AND a.QDDZ LIKE '%'||#{param.qddz }||'%'
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE '%'||#{param.bz }||'%'
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE '%'||#{param.createDate }||'%'
                    </if>
                    <if test="param.deviceId != null">
                        AND a.DEVICE_ID LIKE '%'||#{param.deviceId }||'%'
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE '%'||#{param.itemId }||'%'
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE '%'||#{param.type }||'%'
                    </if>
                    <if test="param.typeZh != null">
                        AND a.TYPE_ZH LIKE '%'||#{param.typeZh }||'%'
                    </if>
                    <if test="param.qdfs != null">
                        AND a.QDFS LIKE '%'||#{param.qdfs }||'%'
                    </if>
                    <if test="param.qdtj != null">
                        AND a.QDTJ LIKE '%'||#{param.qdtj }||'%'
                    </if>
                    <if test="param.img != null">
                        AND a.IMG LIKE '%'||#{param.img }||'%'
                    </if>
                    <if test="param.modifyDate != null">
                        AND a.MODIFY_DATE LIKE '%'||#{param.modifyDate }||'%'
                    </if>
                    <if test="param.sjzt != null">
                        AND a.SJZT LIKE '%'||#{param.sjzt }||'%'
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE '%'||#{param.czr }||'%'
                    </if>
                    <if test="param.czrxm != null">
                        AND a.CZRXM LIKE '%'||#{param.czrxm }||'%'
                    </if>
    </sql>
</mapper>
