package com.sanythadmin.project.checkin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.checkin.entity.CheckinItemAddress;
import com.sanythadmin.project.checkin.mapper.CheckinItemAddressMapper;
import com.sanythadmin.project.checkin.service.CheckinItemAddressService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 签到项目地址Service实现
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Service
public class CheckinItemAddressServiceImpl extends ServiceImpl<CheckinItemAddressMapper, CheckinItemAddress> implements CheckinItemAddressService {

    @Resource
    private CheckinItemAddressMapper mapper;

}
