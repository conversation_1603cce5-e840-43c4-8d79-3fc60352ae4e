package com.sanythadmin.project.checkin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.entity.CheckinItemAddress;
import com.sanythadmin.project.checkin.entity.CheckinRecord;
import com.sanythadmin.project.checkin.mapper.CheckinItemMapper;
import com.sanythadmin.project.checkin.mapper.CheckinRecordMapper;
import com.sanythadmin.project.checkin.param.CheckinItemParam;
import com.sanythadmin.project.checkin.service.CheckinItemAddressService;
import com.sanythadmin.project.checkin.service.CheckinItemDataService;
import com.sanythadmin.project.checkin.service.CheckinItemService;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.param.SelectorDataParam;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.sanythadmin.common.core.utils.DateUtil.DEFAULT_DATE_FORMAT;

/**
 * 定位签到项目Service实现
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Service
public class CheckinItemServiceImpl extends ServiceImpl<CheckinItemMapper, CheckinItem> implements CheckinItemService {

    @Resource
    private CheckinItemMapper mapper;
    @Resource
    private SelectorDataService selectorDataService;
    @Resource
    private CheckinItemAddressService checkinItemAddressService;
    @Lazy
    @Resource
    private CheckinRecordMapper checkinRecordMapper;
    @Resource
    private CheckinItemDataService checkinItemDataService;
    @Resource
    private SelectorAccountService selectorAccountService;

    @Override
    public PageParam<CheckinItem, CheckinItemParam> queryPage(CheckinItemParam param,PageParam page) {
//        QueryWrapper<CheckinItem> wrapper = page.getWrapper();
        MyMPJLambdaWrapper<CheckinItem, CheckinItemParam> wrapper = new MyMPJLambdaWrapper<>();
//        wrapper.selectAll(CheckinItem.class);
        if (param.getUserInfoParam() != null) {
            wrapper.leftJoin(SelectorAccount.class, "acc", SelectorAccount::getItemId, CheckinItem::getId);
            if (StrUtil.isNotBlank(param.getUserInfoParam().getXgh())) {
                wrapper.eq("acc.xgh", param.getUserInfoParam().getXgh());
            }
        }

        wrapper.buildQueryCondition(null, param);
        if (StrUtil.isNotBlank(param.getKssj())) {
            wrapper.ge("cjsj", DateUtil.parse(param.getStartDate(), DEFAULT_DATE_FORMAT));
        }
        if (StrUtil.isNotBlank(param.getJssj())) {
            wrapper.le("cjsj", DateUtil.parse(param.getEndDate(), DEFAULT_DATE_FORMAT));
        }
        if (StrUtil.isNotBlank(param.getStatus())) {
//            Date date = param.getCjsj();
            Date date = new Date();
            SimpleDateFormat df = new SimpleDateFormat("HH:mm");// 设置时间格式
            String now = df.format(date); // 当前时间
            SimpleDateFormat sdfRq = new SimpleDateFormat("yyyy-MM-dd");// 设置日期格式
            String nowRq = sdfRq.format(date); // 当前日期
            StringBuilder sql = new StringBuilder();
            if ("wks".equals(param.getStatus())) { // 未开始
                // 当前日期小于开始日期
                sql.append(" and (ksrq>'").append(nowRq).append("' ");

                // 当前日期在开始结束日期内，且 当前时间小于开始时间 或者 当前时间大于结束时间
                sql.append("or ((kssj>'").append(now).append("' or jssj<'").append(now).append("') and ")
                        .append(" (ksrq<='").append(nowRq).append("' and jsrq>'").append(nowRq).append("')) ");

                // 当前日期在等于结束日期，且 当前时间小于开始时间
                sql.append("or ((kssj>'").append(now).append("' and jsrq='").append(nowRq).append("')) ");

                // 当前时间在开始结束时间内，且 签到日期中不包含当前日期，且当前日期在开始结束日期内
                sql.append("or (")
                        .append(" NOT REGEXP_LIKE(qdrq, '(^|,)' || '" + nowRq + "' || '(,|$)') and (kssj<='").append(now)
                        .append("' and jssj>='").append(now).append("') and ").append(" (ksrq<'").append(nowRq)
                        .append("' and jsrq>'").append(nowRq).append("')) )");
            }
            if ("jxz".equals(param.getStatus())) { // 进行中
                // (当前日期在签到日期内，且当前时间在开始结束时间内)
                sql.append("and ( (kssj<='").append(now).append("' and t.jssj>='").append(now).append("') and ")
                        .append(" ((ksrq<='").append(nowRq).append("' and jsrq>='").append(nowRq)
                        .append("' and qdsjlx='1') or").append(" (qdrq like'%").append(nowRq)
                        .append("%' and qdsjlx ='3')) or ")
                        .append(" ( and qdsjlx = '2' and EXISTS (\n" +
                                "    SELECT 1\n" +
                                "    FROM (\n" +
                                "        SELECT REGEXP_SUBSTR(QDSJ, '\\d{2}:\\d{2}-\\d{2}:\\d{2}', 1, LEVEL) AS time_range\n" +
                                "        FROM DUAL\n" +
                                "        CONNECT BY REGEXP_SUBSTR(QDSJ, '\\d{2}:\\d{2}-\\d{2}:\\d{2}', 1, LEVEL) IS NOT NULL\n" +
                                "    ) time_ranges\n" +
                                "    WHERE TO_DATE(SUBSTR(time_range, 1, 5), 'HH24:MI') <= TO_DATE(TO_CHAR(SYSDATE, 'HH24:MI'), 'HH24:MI')\n" +
                                "    AND TO_DATE(SUBSTR(time_range, -5), 'HH24:MI') >= TO_DATE(TO_CHAR(SYSDATE, 'HH24:MI'), 'HH24:MI')\n" +
                                "))")
                        .append(") ");
            }
            if ("yjs".equals(param.getStatus())) { // 已结束(当前日期等于结束日期，且结束时间小于当前时间)
                sql.append("and ( (jsrq='").append(nowRq).append("' and jssj<'").append(now).append("') or ")
                        .append(" (jsrq<'").append(nowRq).append("') ) ");
            }
            wrapper.apply(sql.toString());
        }

        SysRole role = SecurityUtil.getRole();
        if (Constants.ROLE_SCOPE_BY.equals(role.getRoleScope())) {
            StringBuilder sql = new StringBuilder();
            sql.append(" ( (cjr='").append(param.getCjr()).append("') or ").append(" (cjrjs='")
                    .append(Constants.ROLE_SCOPE_QX).append("') ");
            if (StrUtil.isNotBlank(param.getCjrdw())) {
                String[] split = param.getCjrdw().split(",");
                String tmp = "";
                for (String str : split) {
                    tmp += " REGEXP_LIKE(cjrdw, '(^|,)' || '" + str + "' || '(,|$)') or ";
//                    tmp += "'" + str + "' in (select * from table(FN_SPLIT(cjrdw,','))) or ";
                }
                if (tmp.endsWith("or "))
                    tmp = tmp.substring(0, tmp.length() - 4);
                sql.append(" or ((").append(tmp).append(") and cjrjs='")
                        .append(Constants.ROLE_SCOPE_BJ).append("') ");
            }
            sql.append(") ");
            wrapper.apply(sql.toString());
        } else if (Constants.ROLE_SCOPE_BJ.equals(role.getRoleScope())) {
            StringBuilder sql = new StringBuilder();
            sql.append(" ( (cjr='").append(param.getCjr()).append("') or ").append(" (cjrjs='")
                    .append(Constants.ROLE_SCOPE_QX).append("') ");
            if (StrUtil.isNotBlank(param.getCjrdw())) {
                String[] split = param.getCjrdw().split(",");
                String tmp = "";
                for (String str : split) {
                    tmp += " REGEXP_LIKE(cjrdw, '(^|,)' || '" + str + "' || '(,|$)') or ";
//                    tmp += "'" + str + "' in (select * from table(FN_SPLIT(cjrdw,','))) or ";
                }
                if (tmp.endsWith("or "))
                    tmp = tmp.substring(0, tmp.length() - 4);
                sql.append(" or ((").append(tmp).append(") and cjrjs='")
                        .append(Constants.ROLE_SCOPE_BY + "') ");
            }
            sql.append(") ");
            wrapper.apply(sql.toString());
        }

        PageParam pageParam = page(page, wrapper);
        return pageParam;
    }

    @Override
    public CheckinItemParam getCheckinItemParam(String id) {
        CheckinItem checkinItem = getById(id);
        if (checkinItem != null) {
            CheckinItemParam checkinItemParam = new CheckinItemParam();
            BeanUtils.copyProperties(checkinItem, checkinItemParam);
            //人员选择器
            SelectorDataParam selectorDataParam = new SelectorDataParam();
            selectorDataParam.setItemId(checkinItem.getId());
            PageParam<SelectorData, SelectorDataParam> dataParamPageParam = new PageParam<>(selectorDataParam);
            List<SelectorData> selectorDatas = selectorDataService.list(dataParamPageParam.getWrapper());
            checkinItemParam.setSelectorDatas(selectorDatas);
            //签到地址
            List<CheckinItemAddress> checkinItemAddresses = checkinItemAddressService.list(new LambdaQueryWrapper<CheckinItemAddress>().eq(CheckinItemAddress::getItemId,checkinItem.getId()));
            //循环checkinItemAddresses，取出addressId组合为新的list
//            List<String> addressIds = checkinItemAddresses.stream().map(CheckinItemAddress::getAddressId).collect(Collectors.toList());
//            checkinItemParam.setAddressesIds(addressIds);
//            List<CheckinAddress> checkinAddresses = checkinAddressService.listByIds(addressIds);
            checkinItemParam.setCheckinItemAddresses(checkinItemAddresses);
            return checkinItemParam;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCheckinItem(CheckinItemParam param) throws Exception {
        CheckinItem checkinItem = new CheckinItem();
        BeanUtils.copyProperties(param, checkinItem);
        SysAccount account = SecurityUtil.getAccount();
        UserInfo userInfo = SecurityUtil.getUserInfo();
        checkinItem.setCjr(account.getUsername());
        checkinItem.setCjrxm(account.getRealName());
        checkinItem.setCjrjs(account.getRole().getRoleScope());
        checkinItem.setCjrdw(userInfo.getXyid());
        if(StrUtil.isEmpty(checkinItem.getQdsjlx()))
            AssertUtil.throwMessage("签到时间类型为空");
        switch (checkinItem.getQdsjlx()) {
            case "jdms":   //简单模式
                if (StrUtil.isNotBlank(checkinItem.getKsrq()) && StrUtil.isNotBlank(checkinItem.getJsrq())) {
                    List<String> dates = DateUtil.findDates(DateUtil.parse(checkinItem.getKsrq(), DEFAULT_DATE_FORMAT)
                            , DateUtil.parse(checkinItem.getJsrq(), DEFAULT_DATE_FORMAT));
                    if (CollectionUtils.isNotEmpty(dates)) {
                        checkinItem.setQdrq(StrUtil.join(",", dates));
                    }
                }
                break;
            case "gjms":   //高级模式
                String[] minMaxDates = DateUtil.findMinMaxDates(checkinItem.getQdrq(), DEFAULT_DATE_FORMAT);
                String[] minMaxTimes = DateUtil.findMinMaxTime(checkinItem.getQdsj(), "HH:mm");
                if (minMaxDates != null && minMaxDates.length == 2) {
                    checkinItem.setKsrq(minMaxDates[0]);
                    checkinItem.setJsrq(minMaxDates[1]);
                }
                if (minMaxDates != null && minMaxDates.length == 2) {
                    checkinItem.setKssj(minMaxTimes[0]);
                    checkinItem.setJssj(minMaxTimes[1]);
                }
                break;
            case "zxhms":   //周期循环模式
                if (StrUtil.isNotBlank(checkinItem.getKsrq()) && StrUtil.isNotBlank(checkinItem.getJsrq())) {
                    List<String> dates = DateUtil.findDates(DateUtil.parse(checkinItem.getKsrq(), DEFAULT_DATE_FORMAT),
                            DateUtil.parse(checkinItem.getJsrq(), DEFAULT_DATE_FORMAT),
                            CommonUtil.toIntSet(checkinItem.getXhpc()));
                    if (CollectionUtils.isNotEmpty(dates)) {
                        checkinItem.setQdrq(StrUtil.join(",", dates));
                    }
                }
                break;
            default:
                break;
        }
        checkinItem.setType(checkinItem.getType().toUpperCase());
        if (StringUtils.hasLength(checkinItem.getId())) {
            updateById(checkinItem);
        } else {
            checkinItem.setCjsj(new Date());
            save(checkinItem);
        }
        checkinItemDataService.saveCheckinItermAddress(param, checkinItem);
    }




    @Override
    @Transactional
    public void removeCheckinItemByIds(List<String> ids) throws Exception {
        selectorDataService.removeSelectotData(ids);
        checkinItemAddressService.remove(new LambdaQueryWrapper<CheckinItemAddress>().in(CheckinItemAddress::getItemId, ids));
        checkinRecordMapper.delete(new LambdaQueryWrapper<CheckinRecord>().in(CheckinRecord::getItemId, ids));
        this.removeByIds(ids);
    }

    @Override
    public Map<String, Integer> selectPeopleCountByItemIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        QueryWrapper<SelectorAccount> wrapper = new QueryWrapper<>();
        wrapper.select("ITEM_ID as itemId, COUNT(DISTINCT XGH) as count")
               .in("ITEM_ID", ids)
               .groupBy("ITEM_ID");
        
        List<Map<String, Object>> countList = selectorAccountService.getBaseMapper().selectMaps(wrapper);
        Map<String, Integer> resultMap = new HashMap<>();
        ids.forEach(id -> resultMap.put(id, 0));
        for (Map<String, Object> map : countList) {
            String itemId = String.valueOf(map.get("ITEMID"));
            Integer count = map.get("COUNT") != null ? new BigDecimal(map.get("COUNT").toString()).intValue() : 0;
            resultMap.put(itemId, count);
        }
        
        return resultMap;
    }
}
