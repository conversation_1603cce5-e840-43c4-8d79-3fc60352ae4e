package com.sanythadmin.project.checkin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.param.CheckinItemParam;

/**
 * 定位签到项目Service
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
public interface CheckinItemDataService extends IService<CheckinItem> {

    public void saveCheckinItermAddress(CheckinItemParam param, CheckinItem checkinItem) throws Exception;

}
