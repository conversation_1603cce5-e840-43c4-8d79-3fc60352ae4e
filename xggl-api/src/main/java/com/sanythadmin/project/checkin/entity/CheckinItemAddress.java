package com.sanythadmin.project.checkin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 签到项目地址
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CHECKIN_ITEM_ADDRESS")
@Entity
@Table(name = "SYT_CHECKIN_ITEM_ADDRESS")
public class CheckinItemAddress implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 预设地址id
     */
    @Column(name = "ADDRESS_ID")
    @TableField("ADDRESS_ID")
    private String addressId;
    /**
     * 签到项目
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 描述
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 类型(圆形,多边形)
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 范围/半径
     */
    @Column(name = "FWBJ")
    @TableField("FWBJ")
    private Integer fwbj;

    /**
     * 经纬度坐标
     */
    @Column(name = "JWDZB")
    @TableField("JWDZB")
    private String jwdzb;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 是否来源地址库
     */
    @Column(name = "IS_FROM_MAP")
    @TableField("IS_FROM_MAP")
    private JudgeMark isFromMap;
}
