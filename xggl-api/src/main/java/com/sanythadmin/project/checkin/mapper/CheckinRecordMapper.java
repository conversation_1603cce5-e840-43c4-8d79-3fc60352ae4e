package com.sanythadmin.project.checkin.mapper;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.project.checkin.entity.CheckinRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 签到记录Mapper
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
public interface CheckinRecordMapper extends BaseMapper<CheckinRecord> {

    @DataPermission(alias = "SYT_CHECKIN_RECORD")
    default Page<CheckinRecord> selectPageDP(IPage page, Wrapper<CheckinRecord> queryWrapper){
        IPage p = this.selectPage(page, queryWrapper);
        Page<CheckinRecord> voPage = new Page<>(p.getCurrent(), p.getSize(), p.getTotal());
        voPage.setRecords(p.getRecords());
        return voPage;
    }

    @DataPermission(alias = "SYT_CHECKIN_RECORD")
    default List<CheckinRecord> selectListDP(Wrapper<CheckinRecord> queryWrapper){
        List<CheckinRecord> list = this.selectList(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return list;
    }

    /**
     * 获取签到统计信息
     * where条件示例: r.item_id = '6f1be7b5edf304859f25166b5d54e896'\n" +
     * "and r.create_date between '2024-04-01' and '2024-07-03'\n" +
     * "and u.XYID='4f11f74bd08c6847954309f6852a06f4'\n" +
     *
     * @param wrapper 包装物
     * @param itemId  项目id
     * @param page    页
     * @return {@link List }<{@link JSONObject }>
     */
    @DataPermission(alias = "r")
    @Select("<script>" +
            "SELECT\n" +
            "   ${column} as name,\n" +
            "   count(distinct a.XGH) as yqdrs,\n" +
            "   COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END) as qdrs,\n" +
            "   round(COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS qdbl,\n" +
            "   count(distinct a.XGH)-COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END) wqdrs,\n" +
            "   round((count(distinct a.XGH)-COUNT(DISTINCT CASE WHEN r.SJZT = '正常' THEN r.XGH END))/count(distinct a.XGH)*100,2) AS wddbl,\n" +
            "   COUNT(DISTINCT CASE WHEN r.SJZT = '异常' THEN r.XGH END) AS ycrs,\n" +
            "   round(COUNT(DISTINCT CASE WHEN r.SJZT = '异常' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS ycbl,\n" +
            "   COUNT(DISTINCT CASE WHEN r.SJZT = '补签' THEN r.XGH END) AS bqrs,\n" +
            "   round(COUNT(DISTINCT CASE WHEN r.SJZT = '补签' THEN r.XGH END)/count(distinct a.XGH)*100,2) AS bqbl\n" +
            "FROM SYT_CHECKIN_RECORD r\n" +
            "   left join SYT_USER_INFO u on r.XGH = u.XGH\n" +
            "   left join SYT_CODE_DWB a on a.ID=u.XYID\n" +
            "   left join SYT_CODE_BJB c on c.ID=u.BJID\n" +
            "   left join SYT_SELECTOR_ACCOUNT a on r.ITEM_ID=a.ITEM_ID\n" +
            "WHERE" +
            "   <if test=\"ew.emptyOfWhere == false\">${ew.sqlSegment}</if>" +
            "   <if test=\"ew.emptyOfWhere == true\"> 1=1 </if>" +
            "GROUP BY ${column}" +
            "</script>")
    List<JSONObject> getCheckinStatistics(@Param(Constants.WRAPPER) QueryWrapper<CheckinRecord> wrapper, @Param("itemId") String itemId, @Param("column") String column, Page<JSONObject> page);
}
