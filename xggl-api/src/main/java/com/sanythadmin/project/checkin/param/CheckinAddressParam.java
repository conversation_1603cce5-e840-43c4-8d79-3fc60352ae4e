package com.sanythadmin.project.checkin.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 预设签到位置查询参数
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckinAddressParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 描述
     */
    @QueryField(ignore = true)
    private String bz;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private State status;

    /**
     * 类型(圆形,多边形)
     */
    private String type;

    /**
     * 范围/半径
     */
    private String fwbj;

    /**
     * 多边形经纬度坐标
     */
    private String dbxjwdzb;

    /**
     * 圆形签到范围的圆心经纬度坐标
     */
    private String yxjwdzb;

    /**
     * 所属模块参数
     */
    @QueryField(type = QueryType.EQ)
    private String moduleParam;

    public String getModuleParam() {
        return StringUtils.isNoneBlank(moduleParam) ? moduleParam.toUpperCase() : moduleParam;
    }

    @Override
    public String getSort() {
        return StringUtils.isNotEmpty(super.getSort()) ? super.getSort() : "sort desc";
    }
}
