<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.checkin.mapper.CheckinItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, TYPE, NAME, QDFS, QDTJ, QDSJLX, KSSJ, JSSJ, QDRQ, QDSJ, XHPC, XHKSRQ, XHJSRQ, XHKSSJ, XHJSSJ, QDCS, QDSJGX, QDSJGXJG, BZ, CJSJ, CJR, CJRXM, CJRDW, CJRJS, QRXX, SFTX, TQTXSJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE '%'||#{param.type }||'%'
                    </if>
                    <if test="param.name != null">
                        AND a.NAME = #{param.name}
                    </if>
                    <if test="param.qdfs != null">
                        AND a.QDFS LIKE '%'||#{param.qdfs }||'%'
                    </if>
                    <if test="param.qdtj != null">
                        AND a.QDTJ LIKE '%'||#{param.qdtj }||'%'
                    </if>
                    <if test="param.qdsjlx != null">
                        AND a.QDSJLX LIKE '%'||#{param.qdsjlx }||'%'
                    </if>
                    <if test="param.kssj != null">
                        AND a.KSSJ LIKE '%'||#{param.kssj }||'%'
                    </if>
                    <if test="param.jssj != null">
                        AND a.JSSJ LIKE '%'||#{param.jssj }||'%'
                    </if>
                    <if test="param.qdrq != null">
                        AND a.QDRQ LIKE '%'||#{param.qdrq }||'%'
                    </if>
                    <if test="param.qdsj != null">
                        AND a.QDSJ LIKE '%'||#{param.qdsj }||'%'
                    </if>
                    <if test="param.xhpc != null">
                        AND a.XHPC LIKE '%'||#{param.xhpc }||'%'
                    </if>
                    <if test="param.xhksrq != null">
                        AND a.XHKSRQ LIKE '%'||#{param.xhksrq }||'%'
                    </if>
                    <if test="param.xhjsrq != null">
                        AND a.XHJSRQ LIKE '%'||#{param.xhjsrq }||'%'
                    </if>
                    <if test="param.xhkssj != null">
                        AND a.XHKSSJ LIKE '%'||#{param.xhkssj }||'%'
                    </if>
                    <if test="param.xhjssj != null">
                        AND a.XHJSSJ LIKE '%'||#{param.xhjssj }||'%'
                    </if>
                    <if test="param.qdcs != null">
                        AND a.QDCS = #{param.qdcs}
                    </if>
                    <if test="param.qdsjgx != null">
                        AND a.QDSJGX LIKE '%'||#{param.qdsjgx }||'%'
                    </if>
                    <if test="param.qdsjgxjg != null">
                        AND a.QDSJGXJG LIKE '%'||#{param.qdsjgxjg }||'%'
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE '%'||#{param.bz }||'%'
                    </if>
                    <if test="param.cjsj != null">
                        AND a.CJSJ LIKE '%'||#{param.cjsj }||'%'
                    </if>
                    <if test="param.cjr != null">
                        AND a.CJR LIKE '%'||#{param.cjr }||'%'
                    </if>
                    <if test="param.cjrxm != null">
                        AND a.CJRXM LIKE '%'||#{param.cjrxm }||'%'
                    </if>
                    <if test="param.cjrdw != null">
                        AND a.CJRDW LIKE '%'||#{param.cjrdw }||'%'
                    </if>
                    <if test="param.cjrjs != null">
                        AND a.CJRJS LIKE '%'||#{param.cjrjs }||'%'
                    </if>
                    <if test="param.qrxx != null">
                        AND a.QRXX LIKE '%'||#{param.qrxx }||'%'
                    </if>
                    <if test="param.sftx != null">
                        AND a.SFTX LIKE '%'||#{param.sftx }||'%'
                    </if>
                    <if test="param.tqtxsj != null">
                        AND a.TQTXSJ = #{param.tqtxsj}
                    </if>
    </sql>
</mapper>
