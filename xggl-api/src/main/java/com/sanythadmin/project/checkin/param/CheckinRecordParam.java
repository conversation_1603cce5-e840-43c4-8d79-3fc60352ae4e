package com.sanythadmin.project.checkin.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.system.param.UserInfoParam;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 签到记录查询参数
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckinRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 签到人账号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 经度
     */
    private Float jd;

    /**
     * 纬度
     */
    private Float wd;

    /**
     * 签到地址
     */
    private String qddz;

    /**
     * 备注
     */
    private String bz;

    /**
     * 签到时间
     */
    private String createDate;

    /**
     * 设备ID
     */
    @QueryField(type = QueryType.EQ)
    private String deviceId;

    /**
     * 签到任务ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 签到类型名称
     */
    @QueryField(type = QueryType.EQ)
    private String typeZh;

    /**
     * 签到方式
     */
    @QueryField(type = QueryType.EQ)
    private String qdfs;

    /**
     * 签到条件(是否范围内)
     */
    @QueryField(type = QueryType.EQ)
    private String qdtj;

    /**
     * 照片信息
     */
    private String img;

    /**
     * 更新时间
     */
    private String modifyDate;

    /**
     * 数据状态
     */
    private String sjzt;

    /**
     * 操作人账户
     */
    @QueryField(type = QueryType.EQ)
    private String czr;

    /**
     * 操作人姓名
     */
    @QueryField(type = QueryType.EQ)
    private String czrxm;

    /**
     * 二维码
     */
    @QueryField(ignore = true)
    private String qrValue;
    /**
     * 文件
     */
    @QueryField(ignore = true)
    private String fileString;
    /**
     * 图像
     */
    @QueryField(ignore = true)
    private String imageFileUrl;

    /**
     * 搜索开始日期
     */
    @QueryField(ignore = true)
    private String searchStartDate;
    /**
     * 搜索结束日期
     */
    @QueryField(ignore = true)
    private String searchEndDate;

    /**
     * 用户信息关联查询
     */
    @QueryField(ignore = true)
    private UserInfoParam userInfoParam;

    /**
     * 查询类型(all,yqd,wqd,)
     */
    @QueryField(ignore = true)
    private String cxlx;

    /**
     * 签到附件
     */
    @TableField(exist = false)
    @Transient
    private Map<String, List<MultipartFile>> fileMap;

}
