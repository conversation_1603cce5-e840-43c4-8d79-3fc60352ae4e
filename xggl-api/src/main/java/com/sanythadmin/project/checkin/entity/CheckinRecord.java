package com.sanythadmin.project.checkin.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.annotation.ColumnDefinition;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 * 签到记录
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CHECKIN_RECORD")
@Entity
@Table(name = "SYT_CHECKIN_RECORD")
public class CheckinRecord implements Serializable {
    private static final long serialVersionUID = 1L;


    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 签到人账号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 经度
     */
    @Column(name = "JD")
    @TableField("JD")
    private Float jd;

    /**
     * 纬度
     */
    @Column(name = "WD")
    @TableField("WD")
    private Float wd;

    /**
     * 签到地址
     */
    @Column(name = "QDDZ")
    @TableField("QDDZ")
    private String qddz;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 签到时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private String createDate;

    /**
     * 设备ID
     */
    @Column(name = "DEVICE_ID")
    @TableField("DEVICE_ID")
    private String deviceId;

    /**
     * 签到任务ID
     */
    @ColumnDefinition(foreignKey = true, references = "SYT_CHECKIN_ITEM")
    @Column(name = "ITEM_ID")
    @TableField(value = "ITEM_ID")
    private String itemId;

    /**
     * 签到任务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    public String getType() {
        return StrUtil.isNotBlank(type) ? type.toUpperCase() : type;
    }

    public void setType(String type) {
        this.type = StrUtil.isNotBlank(type) ? type.toUpperCase() : type;
    }

    /**
     * 签到类型名称
     */
    @Column(name = "TYPE_ZH")
    @TableField("TYPE_ZH")
    private String typeZh;

    /**
     * 签到方式
     */
    @Column(name = "QDFS")
    @TableField("QDFS")
    private String qdfs;

    /**
     * 签到条件(是否范围内)
     */
    @Column(name = "QDTJ")
    @TableField("QDTJ")
    private String qdtj;

    /**
     * 拍照
     */
    @Column(name = "IMG", columnDefinition = ColumnType.VARCHAR2_4000)
    @TableField(value = "IMG", jdbcType = JdbcType.VARCHAR)
    private String img;

    /**
     * 更新时间
     */
    @Column(name = "MODIFY_DATE")
    @TableField("MODIFY_DATE")
    private String modifyDate;

    /**
     * 数据状态
     */
    @Column(name = "SJZT")
    @TableField("SJZT")
    private String sjzt;

    /**
     * 操作人账户
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 操作人姓名
     */
    @Column(name = "CZRXM")
    @TableField("CZRXM")
    private String czrxm;

}
