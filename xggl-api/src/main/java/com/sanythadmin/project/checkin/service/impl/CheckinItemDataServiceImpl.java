package com.sanythadmin.project.checkin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.project.checkin.constant.ConstantsCheckin;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.entity.CheckinItemAddress;
import com.sanythadmin.project.checkin.mapper.CheckinItemMapper;
import com.sanythadmin.project.checkin.param.CheckinItemParam;
import com.sanythadmin.project.checkin.service.CheckinItemAddressService;
import com.sanythadmin.project.checkin.service.CheckinItemDataService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CheckinItemDataServiceImpl extends ServiceImpl<CheckinItemMapper, CheckinItem> implements CheckinItemDataService {

    @Resource
    private CheckinItemAddressService checkinItemAddressService;
    @Resource
    private SelectorDataService selectorDataService;

    @Override
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    public void saveCheckinItermAddress(CheckinItemParam param, CheckinItem checkinItem) throws Exception {
        if (!ConstantsCheckin.CHECKIN_QDFS_SMQD.equals(checkinItem.getQdfs())) {
            //签到地址
            List<CheckinItemAddress> checkinItemAddresses = param.getCheckinItemAddresses();
            if (CollectionUtils.isEmpty(checkinItemAddresses))
                AssertUtil.throwMessage("签到地址不能为空");

            checkinItemAddresses.forEach(checkinAddress->{
                checkinAddress.setAddressId(checkinAddress.getId());
                checkinAddress.setId(null);
                checkinAddress.setItemId(checkinItem.getId());
            });
            checkinItemAddressService.remove(new LambdaQueryWrapper<CheckinItemAddress>().eq(CheckinItemAddress::getItemId, checkinItem.getId()));
            checkinItemAddressService.saveOrUpdateBatch(checkinItemAddresses);
        }
        //人员选择器
        selectorDataService.saveSelectotData(checkinItem.getId(),param.getSfbl(),param.getSelectorDatas());

    }
}
