<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.checkin.mapper.CheckinItemAddressMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, TYPE, FWBJ, YXJWDZB, DBXJWDZB, ITEM_ID, TBYSDZ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE '%'||#{param.name }||'%'
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE '%'||#{param.type }||'%'
                    </if>
                    <if test="param.fwbj != null">
                        AND a.FWBJ LIKE '%'||#{param.fwbj }||'%'
                    </if>
                    <if test="param.yxjwdzb != null">
                        AND a.YXJWDZB LIKE '%'||#{param.yxjwdzb }||'%'
                    </if>
                    <if test="param.dbxjwdzb != null">
                        AND a.DBXJWDZB LIKE '%'||#{param.dbxjwdzb }||'%'
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE '%'||#{param.itemId }||'%'
                    </if>
                    <if test="param.tbysdz != null">
                        AND a.TBYSDZ LIKE '%'||#{param.tbysdz }||'%'
                    </if>
    </sql>
</mapper>
