package com.sanythadmin.project.checkin.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.checkin.entity.CheckinItemAddress;
import com.sanythadmin.project.checkin.param.CheckinItemAddressParam;
import com.sanythadmin.project.checkin.service.CheckinItemAddressService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 定位签到/签到项目地址控制器
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@RestController
@RequestMapping("/api/checkin/checkin-item-address")
public class CheckinItemAddressController extends BaseController {
    @Resource
    private CheckinItemAddressService checkinItemAddressService;

    /**
     * 分页查询签到项目地址（权限标识：checkin:checkinItemAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItemAddress:list')")
    @GetMapping("/page")
    public PageResult<CheckinItemAddress> page(CheckinItemAddressParam param) {
        PageParam<CheckinItemAddress, CheckinItemAddressParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = checkinItemAddressService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部签到项目地址（权限标识：checkin:checkinItemAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItemAddress:list')")
    @GetMapping()
    public List<CheckinItemAddress> list(CheckinItemAddressParam param) {
        PageParam<CheckinItemAddress, CheckinItemAddressParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return checkinItemAddressService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询签到项目地址（权限标识：checkin:checkinItemAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItemAddress:list')")
    @GetMapping("/{id}")
    public CheckinItemAddress get(@PathVariable("id") String id) {
        return checkinItemAddressService.getById(id);
    }

    /**
     * 添加或修改签到项目地址（权限标识：checkin:checkinItemAddress:operation）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItemAddress:operation')")
    @OperationLog(module = "签到项目地址", comments = "保存签到项目地址")
    @PostMapping("/operation")
    public void save(@RequestBody CheckinItemAddress checkinItemAddress) {
        if (StringUtils.hasLength(checkinItemAddress.getId())) {
            checkinItemAddressService.updateById(checkinItemAddress);
        } else {
            checkinItemAddressService.save(checkinItemAddress);
        }
    }

    /**
     * 批量删除签到项目地址（权限标识：checkin:checkinItemAddress:remove）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItemAddress:remove')")
    @OperationLog(module = "签到项目地址", comments = "批量删除签到项目地址")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        checkinItemAddressService.removeByIds(ids);
    }
}
