package com.sanythadmin.project.checkin.service.impl;

import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.checkin.entity.CheckinAddress;
import com.sanythadmin.project.checkin.param.CheckinAddressParam;
import com.sanythadmin.project.checkin.service.CheckinAddressRealService;
import com.sanythadmin.project.checkin.service.CheckinAddressService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @since 2025/6/4 15:31
 */
@AllArgsConstructor
@Service
public class CheckinAddressRealServiceImpl implements CheckinAddressRealService {

    private final CheckinAddressService checkinAddressService;

    @Override
    public CheckinAddress getById(String id) {
        return checkinAddressService.getById(id);
    }

    @Override
    public PageResult<CheckinAddress> page(CheckinAddressParam param) {
        PageParam<CheckinAddress, CheckinAddressParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = checkinAddressService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public void serviceSave(CheckinAddress checkinAddress) {
        if (StringUtils.hasLength(checkinAddress.getId())) {
            checkinAddressService.updateById(checkinAddress);
        } else {
            if (checkinAddress.getSort() == null) {
                SortHelper<CheckinAddress> sortHelper = new SortHelper<>(checkinAddressService, "sort");
                checkinAddress.setSort(sortHelper.next());
            }
            checkinAddressService.save(checkinAddress);
        }
    }
}
