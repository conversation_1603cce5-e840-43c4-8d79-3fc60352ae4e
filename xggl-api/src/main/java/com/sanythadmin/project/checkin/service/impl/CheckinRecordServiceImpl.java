package com.sanythadmin.project.checkin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.entity.CheckinRecord;
import com.sanythadmin.project.checkin.mapper.CheckinRecordMapper;
import com.sanythadmin.project.checkin.param.CheckinRecordParam;
import com.sanythadmin.project.checkin.service.CheckinItemService;
import com.sanythadmin.project.checkin.service.CheckinRecordService;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;
import static com.sanythadmin.project.checkin.constant.ConstantsCheckin.*;

/**
 * 签到记录Service实现
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Service
public class CheckinRecordServiceImpl extends ServiceImpl<CheckinRecordMapper, CheckinRecord> implements CheckinRecordService {

    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private CheckinRecordMapper mapper;
    @Resource
    private CheckinItemService checkinItemService;
    @Resource
    private SelectorDataService selectorDataService;
    @Resource
    private SelectorAccountService selectorAccountService;


    @Override
    public PageResult<CheckinRecord> pageCheckinData(CheckinRecordParam param,PageParam page) {
        MyMPJLambdaWrapper<CheckinRecord, CheckinRecordParam> wrapper = new MyMPJLambdaWrapper<>(param);
        PageParam pageParam = page(page, wrapper);
        return new PageResult<>(pageParam.getRecords(), pageParam.getTotal());
    }

    @Override
    public IPage<UserInfo> pageCheckinUserInfoData(CheckinRecordParam param) {
        PageParam<CheckinRecord, CheckinRecordParam> recordParamPageParam = new PageParam<>(param);
        QueryWrapper<CheckinRecord> checkinRecordQueryWrapper = recordParamPageParam.getWrapper();

        PageParam<UserInfo, UserInfoParam> userInfoParam = new PageParam<>(param.getUserInfoParam());
        QueryWrapper<UserInfo> wrapper = userInfoParam.getWrapper();

        if ("yqd".equals(param.getCxlx())) {
            wrapper.inSql("xgh", "select xgh from SYT_CHECKIN_RECORD where " + SqlUtil.getRealSql(checkinRecordQueryWrapper));
        } else if ("wqd".equals(param.getCxlx())) {
            wrapper.notInSql("xgh", "select xgh from SYT_CHECKIN_RECORD where " + SqlUtil.getRealSql(checkinRecordQueryWrapper));
        } else if ("yc".equals(param.getCxlx())) {
            /*String rqToday = param.getCreateDate();
            Date yesterday = DateUtil.addDays(DateUtil.parse(rqToday, "yyyy-MM-dd"), -1);
            String rqYesterday = DateUtil.getDateTime(yesterday, "yyyy-MM-dd");
            wrapper.inSql("xgh", "select xgh\n" +
                    " from (SELECT xgh, REPLACE(qddz, ' ', '') add1\n" +
                    "       from SYT_CHECKIN_RECORD\n" +
                    "       where qddz is not null\n" +
                    "         and CREATE_DATE= '" + rqToday + "'\n" +
                    "         and ITEM_ID = '" + param.getItemId() + "') t,\n" +
                    "      (SELECT xgh, REPLACE(qddz, ' ', '') add1\n" +
                    "       from SYT_CHECKIN_RECORD\n" +
                    "       where qddz is not null\n" +
                    "         and CREATE_DATE = '" + rqYesterday + "'\n" +
                    "         and ITEM_ID = '" + param.getItemId() + "') y\n" +
                    " where t.xgh = y.xgh\n" +
                    "   and (\n" +  //SYS.UTL_MATCH.edit_distance_similarity(t.add1, y.add1) < 30 or
                    "        instr(t.add1, substr(y.add1, 0, instr(y.add1, '市'))) = 0 or\n" +
                    "        instr(y.add1, substr(t.add1, 0, instr(t.add1, '市'))) = 0)");*/
            wrapper.eq("SJZT", CHECKIN_RECORD_SJZT_YC);
        }

        Page<UserInfo> page = new Page<>(param.getPage(), param.getLimit());
        return userInfoMapper.selectPageDP(page, wrapper);
    }

    @Override
    public String getCheckinStatus(CheckinRecord record) {
        String sjzt = CHECKIN_RECORD_SJZT_ZC;
        Date yesterday = DateUtil.addDays(new Date(), -1);
        String rqYesterday = DateUtil.getDateTime(yesterday, "yyyy-MM-dd");
        LambdaQueryWrapper<CheckinRecord> wrapper = new LambdaQueryWrapper<CheckinRecord>().eq(CheckinRecord::getItemId, record.getItemId())
                .eq(CheckinRecord::getXgh, record.getXgh())
                .isNotNull(CheckinRecord::getQddz)
                .eq(CheckinRecord::getCreateDate, rqYesterday)
                .orderByDesc(CheckinRecord::getCreateDate);
        List<CheckinRecord> list = list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            CheckinRecord checkinRecord = list.get(0);
            LevenshteinDistance levenshteinDistance = new LevenshteinDistance();
            int distance = levenshteinDistance.apply(checkinRecord.getQddz(), record.getQddz());

            double maxLen = Math.max(checkinRecord.getQddz().length(), record.getQddz().length());
            double similarity = (maxLen - distance) / maxLen * 100;
            if (similarity < 30) {
                sjzt = CHECKIN_RECORD_SJZT_YC;
            }
        }

        if (!record.getQddz().contains("市")) {
            sjzt = CHECKIN_RECORD_SJZT_YC;
        }
        return sjzt;
    }

    @Override
    @Transactional
    public void importData(MultipartFile file, CheckinItem item, UserType userType) {
        EasyExcelHelper<UserInfo> excelHelper = new EasyExcelHelper<>() {
        };
        List<UserInfo> data = null;
        try {
            data = excelHelper.read(file.getInputStream());
        } catch (IOException e) {
            log.error("导入数据失败", e);
        }
        excelHelper.importFileEmptyCheck(data);

        LambdaQueryWrapper<SelectorData> selectorDataWrapper = new LambdaQueryWrapper<SelectorData>().eq(SelectorData::getItemId, item.getId()).eq(SelectorData::getUserType, userType)
                .eq(SelectorData::getConfigKey, "xgh").eq(SelectorData::getConfigName, "person");

        List<SelectorData> selectorDataList = selectorDataService.list(selectorDataWrapper);
        HashSet<String> hashSet = new HashSet<>();
        for (SelectorData selData : selectorDataList) {
            hashSet.add(selData.getValue());
        }

        List<ExcelImportError> errors = new ArrayList<>();
        int line = 1;
        for (UserInfo obj : data) {
            line++;
            if (!StringUtils.hasLength(obj.getXgh()))
                errors.add(ExcelImportError.data(line, obj.getXgh(), "账号不能为空"));
            if (hashSet.contains(obj.getXgh()))
                errors.add(ExcelImportError.data(line, obj.getXm(), "账号已存在"));
            if (!StringUtils.hasLength(obj.getXm()))
                errors.add(ExcelImportError.data(line, obj.getXm(), "姓名不能为空"));
            hashSet.add(obj.getXgh());
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(errors)) {
            excelHelper.writeImportDataErrorInfo(errors);
        } else {
            for (UserInfo obj : data) {
                SelectorData selectorData = new SelectorData();
                selectorData.setItemId(item.getId());
                selectorData.setConfigKey("xgh");
                selectorData.setConfigName("person");
                selectorData.setValue(obj.getXgh());
                selectorDataService.save(selectorData);
                SelectorAccount selectorAccount = new SelectorAccount();
                selectorAccount.setItemId(item.getId());
                selectorAccount.setXgh(obj.getXgh());
                selectorAccountService.save(selectorAccount);
            }
        }
    }

    @Override
    public Resp saveCheckinRecord(CheckinRecordParam param) {
        SysAccount account = getAccount();
        CheckinItem checkinItem = checkinItemService.getById(param.getItemId());
        if (StringUtils.isEmpty(checkinItem)) {
            return Resp.error("项目不存在");
        }
        if ("已结束".equals(checkinItem.getStatus())) {
            return Resp.error("项目已结束");
        }
        if ("未开始".equals(checkinItem.getStatus())) {
            return Resp.error("未开始");
        }
        boolean b = selectorAccountService.checkExistSelectorAccount(param.getItemId(), account.getUsername());
        if (!b) {
            return Resp.error("未在签到名单范围内");
        }

        //判断手机是否签到
        if (StrUtil.isBlank(param.getId()) && StrUtil.isNotBlank(param.getDeviceId())) {
            CheckinRecordParam recordParam = new CheckinRecordParam();
            recordParam.setItemId(param.getItemId());
            recordParam.setDeviceId(param.getDeviceId());
            PageParam<CheckinRecord, CheckinRecordParam> page = new PageParam<>(recordParam);
            List<CheckinRecord> checkinRecordList = list(page.getWrapper());
            if (CollectionUtils.isNotEmpty(checkinRecordList)) {
                CheckinRecord checkinRecord = checkinRecordList.get(0);
                if (!Objects.equals(account.getUsername(), checkinRecord.getXgh())) {
                    return Resp.error("此设备已签到");
                }
            }
        }
        if (CHECKIN_QDFS_SMQD.equals(param.getQdfs())) {
            if (StrUtil.isNotBlank(param.getQrValue())) {
                String[] split = param.getQrValue().split("#&");
                if (!Objects.equals(param.getItemId(), split[1])) {
                    return Resp.error("签到任务错误");
                } else {
                    if (checkinItem.getQryxsj() != null && checkinItem.getQryxsj() != 0) { // 为0 或者空，则表示长期有效
                        Integer qryxsj = checkinItem.getQryxsj();
                        long t = qryxsj * 60 * 1000; // 有效时间
                        if (StrUtil.isNotBlank(split[3])) {
                            long createTime = Long.parseLong(split[3]);
                            long currentTime = new Date().getTime();
                            if (currentTime - createTime > t) {
                                return Resp.error("二维码无效");
                            }
                        }
                    }
                }
            } else {
                return Resp.error("扫码错误");
            }
        }
        CheckinRecord record = new CheckinRecord();
        BeanUtils.copyProperties(param, record);
        record.setType(checkinItem.getType());
        record.setXgh(account.getUsername());

        LocalDateTime[] startAndEndOfDay = DateUtil.getStartAndEndOfDay(LocalDate.now());
        LambdaQueryWrapper<CheckinRecord> lambdaQueryWrapper = new LambdaQueryWrapper<CheckinRecord>()
                .ge(CheckinRecord::getCreateDate, DateUtil.formatLocalDateTime(startAndEndOfDay[0], DateUtil.DEFAULT_DATETIME_FORMAT))
                .le(CheckinRecord::getCreateDate, DateUtil.formatLocalDateTime(startAndEndOfDay[1], DateUtil.DEFAULT_DATETIME_FORMAT))
                .eq(CheckinRecord::getXgh, account.getUsername())
                .eq(CheckinRecord::getItemId, param.getItemId());
        List<CheckinRecord> checkinRecordList = list(lambdaQueryWrapper);
        if (checkinRecordList.size() >= checkinItem.getQdcs()) {
            return Resp.error("已达到签到限制次数");
        }
        record.setJd(param.getJd() == null ? 0F : param.getJd());
        record.setWd(param.getWd() == null ? 0F : param.getWd());
        String date = DateUtil.getDateTime();
        if (StrUtil.isBlank(record.getId())) {
            record.setCreateDate(date);
        }

        if (!org.springframework.util.CollectionUtils.isEmpty(param.getFileMap())) {
            List<MultipartFile> files = new ArrayList<>();
            for (Map.Entry<String, List<MultipartFile>> entry : param.getFileMap().entrySet()) {
                files.addAll(entry.getValue());
            }
            GridFsService gridFsService = SpringContextUtil.getBean(GridFsService.class);
            String fileInfoStr = gridFsService.save(files.toArray(new MultipartFile[]{}));
            record.setImg(fileInfoStr);
        }

        record.setModifyDate(date);
        record.setCzr(account.getUsername());
        record.setCzrxm(account.getRealName());
        String checkinStatus = getCheckinStatus(record);
        record.setSjzt(checkinStatus);
        save(record);
        return Resp.success();
    }

    @Override
    @Transactional
    public Resp addRecord(CheckinRecordParam param,String blrq) {
        SysAccount account = getAccount();
        SysRole role = account.getRole();
        CheckinItem checkinItem = checkinItemService.getById(param.getItemId());
        if (StringUtils.isEmpty(checkinItem)) {
            return Resp.error("项目不存在");
        }
        String xgh = param.getXgh();
        if (Constants.ROLE_SCOPE_GR.equals(role.getRoleScope())) {
            xgh = account.getUsername();
        }
        boolean b = selectorAccountService.checkExistSelectorAccount(param.getItemId(), xgh);
        if (!b) {
            return Resp.error("未在签到名单范围内");
        }

        CheckinRecord record = new CheckinRecord();
        record.setQddz(param.getQddz());
        record.setQdtj(param.getQdtj());
        record.setQdfs(param.getQdfs());
        record.setImg(param.getImg());
        record.setType(checkinItem.getType());
        record.setTypeZh(param.getTypeZh());
        record.setItemId(checkinItem.getId());
        record.setXgh(xgh);
        record.setCzr(account.getUsername());
        record.setCzrxm(account.getRealName());
        record.setDeviceId(param.getDeviceId());
        record.setSjzt(CHECKIN_RECORD_SJZT_BQ);
        record.setBz(param.getBz());
        record.setCreateDate(blrq);
        record.setModifyDate(blrq);
        record.setJd(param.getJd() == null ? 0f : param.getJd());
        record.setWd(param.getWd() == null ? 0f : param.getWd());
        LocalDateTime[] startAndEndOfDay = DateUtil.getStartAndEndOfDay(LocalDate.now());
        LambdaQueryWrapper<CheckinRecord> lambdaQueryWrapper = new LambdaQueryWrapper<CheckinRecord>()
                .ge(CheckinRecord::getCreateDate, DateUtil.formatLocalDateTime(startAndEndOfDay[0], "yyyy-MM-dd"))
                .le(CheckinRecord::getCreateDate, DateUtil.formatLocalDateTime(startAndEndOfDay[1], "yyyy-MM-dd"))
                .eq(CheckinRecord::getXgh, account.getUsername())
                .eq(CheckinRecord::getItemId, param.getItemId());
        List<CheckinRecord> checkinRecordList = list(lambdaQueryWrapper);
        if (checkinItem.getQdcs() > checkinRecordList.size()) {
            List<CheckinRecord> list = new ArrayList<>();
            for (int i = 0; i < checkinItem.getQdcs() - checkinRecordList.size(); i++) {
                CheckinRecord recordNew = new CheckinRecord();
                BeanUtils.copyProperties(record, recordNew);
                list.add(recordNew);
            }
            saveBatch(list);
            return Resp.success();
        }
        return Resp.error("已达到签到次数");
    }

    @Override
    public Resp getCheckinStatistics(CheckinRecordParam param, String column) {
        AssertUtil.isTrue(StrUtil.isNotBlank(param.getItemId()), "项目id不能为空");
        QueryWrapper<CheckinRecord> wrapper = new QueryWrapper<CheckinRecord>().eq("r.ITEM_ID", param.getItemId());
        if (StrUtil.isNotBlank(param.getSearchStartDate()) && StrUtil.isNotBlank(param.getSearchEndDate())) {
            wrapper.between("r.CREATE_DATE", param.getSearchStartDate(), param.getSearchEndDate());
        }
        UserInfoParam userInfoParam = param.getUserInfoParam();
        if (userInfoParam != null) {
            if (StrUtil.isNotBlank(userInfoParam.getXyid())) {
                wrapper.eq("u.XYID", userInfoParam.getXyid());
            }
            if (StrUtil.isNotBlank(userInfoParam.getBjid())) {
                wrapper.eq("u.BJID", userInfoParam.getBjid());
            }
        }
        HashMap<String, String> convertField = UserInfoUtil.getConvertField(param.getUserInfoParam().getUserType());
        column = convertField.get(column);
        column = StrUtil.isEmpty(column) ? "CREATE_DATE" : column;
        Page<JSONObject> page = new Page<>(param.getPage(), param.getLimit());
        List<JSONObject> list = mapper.getCheckinStatistics(wrapper, param.getItemId(), column, page);
        return Resp.success(list);
    }

    @Override
    public Map<String, Integer> recordCountByItemIds(List<String> itemIds,String xgh) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        QueryWrapper<CheckinRecord> wrapper = new QueryWrapper<>();
        wrapper.select("ITEM_ID as itemId, COUNT(1) as count");
        wrapper.in("ITEM_ID", itemIds);
        if (StrUtil.isNotBlank(xgh)) {
            wrapper.eq("XGH", xgh);
        }
        wrapper.groupBy("ITEM_ID");
        
        List<Map<String, Object>> countList = mapper.selectMaps(wrapper);
        Map<String, Integer> resultMap = new HashMap<>();
        for (Map<String, Object> map : countList) {
            String itemId = String.valueOf(map.get("ITEMID"));
            Integer count = map.get("COUNT") != null ? new BigDecimal(map.get("COUNT").toString()).intValue() : 0;
            resultMap.put(itemId, count);
        }
        return resultMap;
    }

    @Override
    public Map<String, Integer> peopleCountByItemIds(List<String> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        
        // 查询每个项目的签到人数（去重计数）
        QueryWrapper<CheckinRecord> wrapper = new QueryWrapper<>();
        wrapper.select("ITEM_ID as itemId, COUNT(DISTINCT XGH) as count")
               .in("ITEM_ID", itemIds)
               .groupBy("ITEM_ID");
        
        List<Map<String, Object>> countList = mapper.selectMaps(wrapper);
        
        // 转换为Map
        Map<String, Integer> resultMap = new HashMap<>();
        for (Map<String, Object> map : countList) {
            String itemId = String.valueOf(map.get("ITEMID"));
            Integer count = map.get("COUNT") != null ? new BigDecimal(map.get("COUNT").toString()).intValue() : 0;
            resultMap.put(itemId, count);
        }
        
        return resultMap;
    }
}
