package com.sanythadmin.project.checkin.controller;

import cn.hutool.extra.qrcode.QrCodeUtil;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.param.CheckinItemParam;
import com.sanythadmin.project.checkin.service.CheckinItemService;
import com.sanythadmin.project.checkin.service.CheckinRecordService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定位签到/项目控制器
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@RestController
@RequestMapping("/api/checkin/checkin-item")
public class CheckinItemController extends BaseController {
    @Resource
    private CheckinItemService checkinItemService;
    @Resource
    private CheckinRecordService checkinRecordService;

    /**
     * 分页查询定位签到项目（权限标识：checkin:checkinItem:list）
     * 项目管理端
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:list')")
    @GetMapping("/page")
    public PageResult<CheckinItem> page(CheckinItemParam param) {
        SysRole role = getRole();
        SysAccount account = SecurityUtil.getAccount();
        UserInfo userInfo = SecurityUtil.getUserInfo();
        param.setCjr(account.getUsername());
        param.setCjrxm(account.getRealName());
        param.setCjrjs(account.getRole().getRoleScope());
        if (Constants.ROLE_SCOPE_BY.equals(role.getRoleScope())||Constants.ROLE_SCOPE_BJ.equals(role.getRoleScope())) {
            param.setCjrdw(userInfo.getXyid());
        }
        /*if(StrUtil.isNotEmpty(param.getStartDate())){
            param.setKssj(param.getStartDate());
        }
        if(StrUtil.isNotEmpty(param.getEndDate())){
            param.setJssj(param.getEndDate());
        }
        */
        PageParam<CheckinItem, CheckinItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = checkinItemService.queryPage(param,page);
        List<CheckinItem> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> itemIds = records.stream().map(CheckinItem::getId).collect(Collectors.toList());
            Map<String, Integer> selectPeopleCountMap = checkinItemService.selectPeopleCountByItemIds(itemIds);
            Map<String, Integer> recordCountMap = checkinRecordService.peopleCountByItemIds(itemIds);
            records.forEach(item -> {
                item.setSelectPeopleCount(selectPeopleCountMap.getOrDefault(item.getId(), 0));
                item.setPeopleCount(recordCountMap.getOrDefault(item.getId(), 0));
            });
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 签到对象查询签到项目（权限标识：checkin:checkinItem:queryPageImtes）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:queryPageImtes')")
    @GetMapping("/queryPageImtes")
    public PageResult<CheckinItem> queryPageImtes(CheckinItemParam param) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        param.setUserInfoParam(UserInfoUtil.convertUserInfoParam(userInfo));
        /*if(StrUtil.isNotEmpty(param.getStartDate())){
            param.setKssj(param.getStartDate());
        }
        if(StrUtil.isNotEmpty(param.getEndDate())){
            param.setJssj(param.getEndDate());
        }*/
        PageParam<CheckinItem, CheckinItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = checkinItemService.queryPage(param,page);
        List<CheckinItem> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> itemIds = records.stream().map(CheckinItem::getId).collect(Collectors.toList());
            String xgh = "";
            if (param.getUserInfoParam() != null) {
                xgh = param.getUserInfoParam().getXgh();
            }
            Map<String, Integer> recordCountMap = checkinRecordService.recordCountByItemIds(itemIds,xgh);
            records.forEach(item -> {
                item.setRecordCount(recordCountMap.getOrDefault(item.getId(), 0));
            });
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部定位签到项目（权限标识：checkin:checkinItem:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:list')")
    @GetMapping()
    public List<CheckinItem> list(CheckinItemParam param) {
        PageParam<CheckinItem, CheckinItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return checkinItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询定位签到项目（权限标识：checkin:checkinItem:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:list')")
    @GetMapping("/{id}")
    public CheckinItemParam get(@PathVariable("id") String id) {
        CheckinItemParam checkinItemParam = checkinItemService.getCheckinItemParam(id);
        Map<String, Integer> recordCountMap = checkinRecordService.peopleCountByItemIds(Collections.singletonList(id));
        checkinItemParam.setPeopleCount(recordCountMap.getOrDefault(checkinItemParam.getId(), 0));
        return checkinItemParam;
    }

    /**
     * 签到对象根据项目id查询定位签到项目（权限标识：checkin:checkinItem:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:list')")
    @GetMapping("/detail")
    public CheckinItemParam getDetail(@RequestParam("id") String id) {
        String username = getAccount().getUsername();
        CheckinItemParam checkinItemParam = checkinItemService.getCheckinItemParam(id);
        Map<String, Integer> recordCountMap = checkinRecordService.recordCountByItemIds(Collections.singletonList(id),username);
        checkinItemParam.setRecordCount(recordCountMap.getOrDefault(checkinItemParam.getId(), 0));
        return checkinItemParam;
    }

    /**
     * 添加或修改定位签到项目（权限标识：checkin:checkinItem:operation）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:operation')")
    @OperationLog(module = "定位签到项目", comments = "保存定位签到项目")
    @PostMapping("/operation")
    public void save(@RequestBody CheckinItemParam param) throws Exception {
        checkinItemService.saveCheckinItem(param);
    }

    /**
     * 批量删除定位签到项目（权限标识：checkin:checkinItem:remove）
     */
    @PreAuthorize("hasAuthority('checkin:checkinItem:remove')")
    @OperationLog(module = "定位签到项目", comments = "批量删除定位签到项目")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) throws Exception {
        checkinItemService.removeCheckinItemByIds(ids);
    }

    /**
     * 生成二维码
     */
    @OperationLog(module = "定位签到项目", comments = "生成二维码")
    @GetMapping("/generatingQRCode")
    public String generatingQRCode(HttpServletResponse response, String id, String xmmc) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("image/png");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode("二维码.png", "UTF-8"));
            Date currentDate = new Date();
            String codeText = "checkin_scan_result#&"+id+"#&"+xmmc+"#&"+currentDate.getTime();
            QrCodeUtil.generate(codeText, 250, 250, "png",outputStream);
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



}
