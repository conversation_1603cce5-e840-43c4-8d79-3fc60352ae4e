package com.sanythadmin.project.checkin.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.checkin.util.CheckinUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;
/**
 * 定位签到项目
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CHECKIN_ITEM")
@Entity
@Table(name = "SYT_CHECKIN_ITEM")
public class CheckinItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 签到类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    public String getType() {
        return StrUtil.isNotBlank(type) ? type.toUpperCase() : type;
    }

    /**
     * 签到名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 签到方式(定位签到,扫码签到,定位&扫码签到,定位&拍照签到)
     */
    @Column(name = "QDFS")
    @TableField("QDFS")
    private String qdfs;

    /**
     * 签到条件(1:范围内,2:范围外)
     */
    @Column(name = "QDTJ")
    @TableField("QDTJ")
    private String qdtj;

    /**
     * 签到时间类型(1:简单模式,2:高级模式,3:周期循环模式)
     */
    @Column(name = "QDSJLX")
    @TableField("QDSJLX")
    private String qdsjlx;

    /**
     * 开始日期
     */
    @Column(name = "KSRQ")
    @TableField("KSRQ")
    private String ksrq;

    /**
     * 结束日期
     */
    @Column(name = "JSRQ")
    @TableField("JSRQ")
    private String jsrq;
    /**
     * 开始时间
     */
    @Column(name = "KSSJ")
    @TableField("KSSJ")
    private String kssj;

    /**
     * 结束时间
     */
    @Column(name = "JSSJ")
    @TableField("JSSJ")
    private String jssj;

    /**
     * 签到日期(多选日期)
     */
    @Column(name = "QDRQ", columnDefinition = ColumnType.VARCHAR2_4000)
    @TableField(value = "QDRQ", jdbcType = JdbcType.VARCHAR)
    private String qdrq;
    /**
     * 签到时间(多选时间)
     */
    @Column(name = "QDSJ")
    @TableField("QDSJ")
    private String qdsj;

    /**
     * 循环频次
     */
    @Column(name = "XHPC")
    @TableField("XHPC")
    private String xhpc;


    /**
     * 限定签到次数
     */
    @Column(name = "QDCS")
    @TableField("QDCS")
    private Integer qdcs;

    /**
     * 签到数据是否更新（1：是， 0：否）
     */
    @Column(name = "QDSJGX", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "QDSJGX", jdbcType = JdbcType.INTEGER)
    private JudgeMark qdsjgx;

    /**
     * 更新时间间隔(分钟)
     */
    @Column(name = "QDSJGXJG" , columnDefinition = ColumnType.NUMBER_10)
    @TableField(value = "QDSJGXJG" , jdbcType = JdbcType.INTEGER)
    private Integer qdsjgxjg;

    /**
     * 签到说明
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 创建时间
     */
    @Column(name = "CJSJ")
    @TableField("CJSJ")
    private Date cjsj;

    /**
     * 创建人账号
     */
    @Column(name = "CJR")
    @TableField("CJR")
    private String cjr;

    /**
     * 创建人姓名
     */
    @Column(name = "CJRXM")
    @TableField("CJRXM")
    private String cjrxm;

    /**
     * 创建人部门
     */
    @Column(name = "CJRDW")
    @TableField("CJRDW")
    private String cjrdw;

    /**
     * 创建人角色
     */
    @Column(name = "CJRJS")
    @TableField("CJRJS")
    private String cjrjs;

    /**
     * 是否弹出确认消息（1：是， 0：否）
     */
    @Column(name = "QRXX", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "QRXX", jdbcType = JdbcType.INTEGER)
    private JudgeMark qrxx;

    /**
     * 是否提醒（1：是， 0：否）
     */
    @Column(name = "SFTX", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "SFTX", jdbcType = JdbcType.INTEGER)
    private JudgeMark sftx;

    /**
     * 提前提醒时间
     */
    @Column(name = "TQTXSJ", columnDefinition = ColumnType.NUMBER_10)
    @TableField("TQTXSJ")
    private Integer tqtxsj;

    /**
     * 二维码有效时间
     */
    @Column(name = "QRYXSJ", columnDefinition = ColumnType.NUMBER_10)
    @TableField("QRYXSJ")
    private Integer qryxsj;
    /**
     * 二维码名称
     */
    @Column(name = "QRMC")
    @TableField("QRMC")
    private String qrmc;
    /**
     * 二维码类型(1:动态,2:静态)
     */
    @Column(name = "QRLX")
    @TableField("QRLX")
    private String qrlx;



    /**
     * 人员选择器是否并联(1:是,0:否)
     */
    @Column(name = "SFBL", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFBL", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfbl;

    @TableField(exist = false)
    private String status;

    public String getStatus() {
        return CheckinUtil.getCheckinItemStatus(this);
    }

    /**
     * 已签到次数
     */
    @TableField(exist = false)
    private Integer recordCount;
    /**
     * 已签到人数
     */
    @TableField(exist = false)
    private Integer peopleCount;
    /**
     * 应签到人数
     */
    @TableField(exist = false)
    private Integer selectPeopleCount;
}
