package com.sanythadmin.project.checkin.util;

import cn.hutool.core.util.StrUtil;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CheckinUtil {

    private static final Logger log = LoggerFactory.getLogger(CheckinUtil.class);

    public static String getCheckinItemStatus(CheckinItem checkinItem) {
        String status = null;
        String ksrq = checkinItem.getKsrq();
        String jsrq = checkinItem.getJsrq();
        String kssj = checkinItem.getKssj();
        String jssj = checkinItem.getJssj();
        String qdrq = checkinItem.getQdrq();
        String qdsj = checkinItem.getQdsj();
        if (StrUtil.isNotBlank(ksrq) && StrUtil.isNotBlank(jsrq) && StrUtil.isNotBlank(kssj) && StrUtil.isNotBlank(jssj)) {
            try {
                Date date = new Date();
                SimpleDateFormat df = new SimpleDateFormat("HH:mm");//设置时间格式
                //格式化当前时间格式
                Date nowTime = df.parse(df.format(date));
                Date startTime = df.parse(kssj);
                Date endTime = df.parse(jssj);
                SimpleDateFormat sdfRq = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                String nowStr = sdfRq.format(date);
                Date nowRq = sdfRq.parse(sdfRq.format(date));
                Date ksDate = sdfRq.parse(ksrq);
                Date jsDate = sdfRq.parse(jsrq);

                if (nowRq.after(jsDate)) {
                    status = "已结束";
                } else if (nowRq.before(ksDate)) {
                    status = "未开始";
                } else {
                    switch (checkinItem.getQdsjlx()) {
                        case "jdms":   //简单模式
                            if (nowRq.equals(jsDate)) {
                                if (nowTime.before(startTime)) {//当前时间在开始时间之前
                                    status = "未开始";
                                } else if (nowTime.after(endTime)) {//当前时间在结束时间之后
                                    status = "已结束";
                                } else {
                                    status = "进行中";
                                }
                            } else {
                                if (nowTime.after(startTime) && nowTime.before(endTime)) {//是否在当前时间段内
                                    status = "进行中";
                                } else {
                                    status = "未开始";
                                }
                                if (nowTime.before(startTime) || nowTime.after(endTime)) {//当前时间在开始时间之前，或者结束时间之后
                                    status = "未开始";
                                } else {
                                    status = "进行中";
                                }
                            }
                            break;
                        case "gjms":   //高级模式:
                            if (qdrq.contains(nowStr)) {
                                if (nowRq.equals(jsDate)) {
                                    if (nowTime.before(startTime)) {//当前时间在开始时间之前
                                        status = "未开始";
                                    } else if (nowTime.after(endTime)) {//当前时间在结束时间之后
                                        status = "已结束";
                                    }
                                } else {
                                    if (nowTime.before(startTime) || nowTime.after(endTime)) {//当前时间在开始时间之前，或者结束时间之后
                                        status = "未开始";
                                    } else {
                                        String[] qdsjArr = qdsj.split(",");
                                        for (String qdsjs : qdsjArr) {
                                            String[] qdsjTime = qdsjs.split("-");
                                            Date startTime1 = df.parse(qdsjTime[0]);
                                            Date endTime1 = df.parse(qdsjTime[1]);
                                            if (nowTime.after(startTime1) && nowTime.before(endTime1)) {
                                                status = "进行中";
                                                break;
                                            }
                                        }
//                                        status = "进行中";
                                    }
                                }
                            } else {
                                status = "未开始";
                            }
                        case "zqxhms":   //周期循环模式:
                            if (qdrq.contains(nowStr)) {
                                if (nowRq.equals(jsDate)) {
                                    if (nowTime.before(startTime)) {//当前时间在开始时间之前
                                        status = "未开始";
                                    } else if (nowTime.after(endTime)) {//当前时间在结束时间之后
                                        status = "已结束";
                                    } else {
                                        status = "进行中";
                                    }
                                } else {
                                    if (nowTime.before(startTime) || nowTime.after(endTime)) {//当前时间在开始时间之前，或者结束时间之后
                                        status = "未开始";
                                    } else {
                                        status = "进行中";
                                    }
                                }
                            } else {
                                status = "未开始";
                            }
                            break;
                        default:
                            break;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return status;
    }
}
