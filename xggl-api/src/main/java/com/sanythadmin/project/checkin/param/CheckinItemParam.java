package com.sanythadmin.project.checkin.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.checkin.entity.CheckinItemAddress;
import com.sanythadmin.project.selector.entity.SelectorData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 定位签到项目查询参数
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckinItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 签到类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    public String getType() {
        return StringUtils.isNoneBlank(type) ? type.toUpperCase() : type;
    }

    /**
     * 签到名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 签到方式
     */
    @QueryField(type = QueryType.EQ)
    private String qdfs;

    /**
     * 签到条件
     */
    @QueryField(type = QueryType.EQ)
    private String qdtj;

    /**
     * 签到时间类型(1:简单模式,2:高级模式,3:周期循环模式)
     */
    @QueryField(type = QueryType.EQ)
    private String qdsjlx;

    /**
     * 开始日期
     */
    private String ksrq;

    /**
     * 结束日期
     */
    private String jsrq;
    
    /**
     * 开始时间
     */
    private String kssj;

    /**
     * 结束时间
     */
    private String jssj;

    /**
     * 签到日期(多选日期)
     */
    private String qdrq;
    /**
     * 签到时间(多选时间)
     */
    private String qdsj;

    /**
     * 循环频次
     */
    private String xhpc;

    /**
     * 限定签到次数
     */
    @QueryField(type = QueryType.EQ)
    private Integer qdcs;

    /**
     * 签到数据是否更新
     */
    private JudgeMark qdsjgx;

    /**
     * 更新时间间隔
     */
    private Integer qdsjgxjg;

    /**
     * 签到说明
     */
    private String bz;

    /**
     * 创建人账号
     */
    @QueryField(ignore = true)
    private String cjr;

    /**
     * 创建人姓名
     */
    @QueryField(ignore = true)
    private String cjrxm;

    /**
     * 创建人部门
     */
    @QueryField(ignore = true)
    private String cjrdw;

    /**
     * 创建人角色
     */
    @QueryField(ignore = true)
    private String cjrjs;

    /**
     * 是否弹出确认消息
     */
    private JudgeMark qrxx;

    /**
     * 是否提醒
     */
    private JudgeMark sftx;

    /**
     * 提前提醒时间
     */
    private Integer tqtxsj;

    /**
     * 二维码有效时间
     */
    private Integer qryxsj;

    /**
     * 二维码名称
     */
    private String qrmc;
    /**
     * 二维码类型(1:动态,2:静态)
     */
    private String qrlx;


    /**
     * 签到项目地址id(请求时使用，按[id]格式保存)
     */
    @QueryField(ignore = true)
    private List<String> addressesIds;

    /**
     * 签到项目地址(回显时使用,展示所有地址字段)
     */
    @QueryField(ignore = true)
    private List<CheckinItemAddress> checkinItemAddresses;

    /**
     * 人员选择器数据
     */
    @QueryField(ignore = true)
    private List<SelectorData> selectorDatas;

    /**
     * 人员选择器是否并联（1：是， 0：否）
     */
    @QueryField(ignore = true)
    private JudgeMark sfbl;
    /**
     * 同步地址（1：是， 0：否）
     */
    @QueryField(ignore = true)
    private JudgeMark tbdz;

    private String status;

    /**
     * 查询开始日期
     */
    @QueryField(ignore = true)
    private String startDate;
    /**
     * 查询结束日期
     */
    @QueryField(ignore = true)
    private String endDate;

    /**
     * 签到记录数量
     */
    @TableField(exist = false)
    private Integer recordCount;
    /**
     * 已签到人数
     */
    @TableField(exist = false)
    private Integer peopleCount;
    /**
     * 用户信息,判断是否在发布范围内
     */
    @QueryField(ignore = true)
    private UserInfoParam userInfoParam;

    @Override
    public String getSort() {
        return "cjsj";
    }

    @Override
    public String getOrder() {
        return "desc";
    }
}
