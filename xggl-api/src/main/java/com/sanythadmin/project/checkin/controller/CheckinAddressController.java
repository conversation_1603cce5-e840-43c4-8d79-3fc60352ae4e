package com.sanythadmin.project.checkin.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.checkin.entity.CheckinAddress;
import com.sanythadmin.project.checkin.param.CheckinAddressParam;
import com.sanythadmin.project.checkin.service.CheckinAddressRealService;
import com.sanythadmin.project.checkin.service.CheckinAddressService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 定位签到/预设签到位置控制器
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@RestController
@RequestMapping("/api/checkin/checkin-address")
public class CheckinAddressController extends BaseController {
    @Resource
    private CheckinAddressService checkinAddressService;
    @Resource
    private CheckinAddressRealService checkinAddressRealService;

    /**
     * 分页查询预设签到位置（权限标识：checkin:checkinAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinAddress:list')")
    @GetMapping("/page")
    public PageResult<CheckinAddress> page(CheckinAddressParam param) {
        return checkinAddressRealService.page(param);
    }

    /**
     * 查询全部预设签到位置（权限标识：checkin:checkinAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinAddress:list')")
    @GetMapping()
    public List<CheckinAddress> list(CheckinAddressParam param) {
        PageParam<CheckinAddress, CheckinAddressParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return checkinAddressService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询预设签到位置（权限标识：checkin:checkinAddress:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinAddress:list')")
    @GetMapping("/{id}")
    public CheckinAddress get(@PathVariable("id") String id) {
        return checkinAddressService.getById(id);
    }

    /**
     * 添加或修改预设签到位置（权限标识：checkin:checkinAddress:operation）
     */
    @PreAuthorize("hasAuthority('checkin:checkinAddress:operation')")
    @OperationLog(module = "预设签到位置", comments = "保存预设签到位置")
    @PostMapping("/operation")
    public ApiResult save(@RequestBody CheckinAddress checkinAddress) {
        checkinAddressRealService.serviceSave(checkinAddress);
        return success(checkinAddress);
    }

    /**
     * 批量删除预设签到位置（权限标识：checkin:checkinAddress:remove）
     */
    @PreAuthorize("hasAuthority('checkin:checkinAddress:remove')")
    @OperationLog(module = "预设签到位置", comments = "批量删除预设签到位置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        checkinAddressService.removeByIds(ids);
    }
}
