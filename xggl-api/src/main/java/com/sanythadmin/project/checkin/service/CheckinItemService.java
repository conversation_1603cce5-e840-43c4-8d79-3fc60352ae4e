package com.sanythadmin.project.checkin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.param.CheckinItemParam;

import java.util.List;
import java.util.Map;

/**
 * 定位签到项目Service
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
public interface CheckinItemService extends IService<CheckinItem> {

    PageParam<CheckinItem, CheckinItemParam> queryPage(CheckinItemParam param,PageParam page);
    CheckinItemParam getCheckinItemParam(String id);
    void saveCheckinItem(CheckinItemParam param) throws Exception;
    void removeCheckinItemByIds(List<String> ids) throws Exception;
    Map<String, Integer> selectPeopleCountByItemIds(List<String> ids);

}
