package com.sanythadmin.project.checkin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.checkin.entity.CheckinAddress;
import com.sanythadmin.project.checkin.mapper.CheckinAddressMapper;
import com.sanythadmin.project.checkin.service.CheckinAddressService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 预设签到位置Service实现
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Service
public class CheckinAddressServiceImpl extends ServiceImpl<CheckinAddressMapper, CheckinAddress> implements CheckinAddressService {

    @Resource
    private CheckinAddressMapper mapper;

}
