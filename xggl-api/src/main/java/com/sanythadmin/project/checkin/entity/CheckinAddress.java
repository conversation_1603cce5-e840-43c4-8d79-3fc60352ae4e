package com.sanythadmin.project.checkin.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.State;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
/**
 * 预设签到位置
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CHECKIN_ADDRESS")
@Entity
@Table(name = "SYT_CHECKIN_ADDRESS")
public class CheckinAddress implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 描述
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 状态（1：启用, 0: 停用）
     */
    @Column(name = "STATUS", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "STATUS", jdbcType = JdbcType.INTEGER)
    private State status;

    /**
     * 类型(圆形,多边形)
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 范围/半径
     */
    @Column(name = "FWBJ")
    @TableField("FWBJ")
    private Integer fwbj;

    /**
     * 经纬度坐标
     */
    @Column(name = "JWDZB")
    @TableField("JWDZB")
    private String jwdzb;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 所属模块参数
     */
    @Column(name = "MODULE_PARAM")
    @TableField("MODULE_PARAM")
    private String moduleParam;
}
