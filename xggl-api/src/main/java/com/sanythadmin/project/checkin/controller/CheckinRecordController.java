package com.sanythadmin.project.checkin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.checkin.entity.CheckinItem;
import com.sanythadmin.project.checkin.entity.CheckinRecord;
import com.sanythadmin.project.checkin.mapper.CheckinRecordMapper;
import com.sanythadmin.project.checkin.param.CheckinRecordParam;
import com.sanythadmin.project.checkin.service.CheckinItemService;
import com.sanythadmin.project.checkin.service.CheckinRecordService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 定位签到/签到记录控制器
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Slf4j
@RestController
@RequestMapping("/api/checkin/checkin-record")
public class CheckinRecordController extends BaseController {
    @Resource
    private CheckinRecordMapper checkinRecordMapper;
    @Resource
    private CheckinRecordService checkinRecordService;
    @Resource
    private CheckinItemService checkinItemService;



    /**
     * 分页查询签到记录（权限标识：checkin:checkinRecord:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:list')")
    @GetMapping("/page")
    public PageResult<CheckinRecord> page(CheckinRecordParam param) {
        PageParam<CheckinRecord, CheckinRecordParam> page = new PageParam<>(param);
        return checkinRecordService.pageCheckinData(param,page);
    }

    /**
     * 签到对象分页查询签到记录（权限标识：checkin:checkinRecord:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:list')")
    @GetMapping("/pageCheckinRecord")
    public PageResult<CheckinRecord> pageCheckinRecord(CheckinRecordParam param) {
        PageParam<CheckinRecord, CheckinRecordParam> page = new PageParam<>(param);
        param.setXgh(getAccount().getUsername());
        return checkinRecordService.pageCheckinData(param,page);
    }

    /**
     * 查询全部签到记录（权限标识：checkin:checkinRecord:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:list')")
    @GetMapping()
    public List<CheckinRecord> list(CheckinRecordParam param) {
        PageParam<CheckinRecord, CheckinRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
//        return checkinRecordService.list(page.getWrapper());
        return checkinRecordMapper.selectListDP(page.getWrapper());
    }

    /**
     * 根据id查询签到记录（权限标识：checkin:checkinRecord:list）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:list')")
    @GetMapping("/{id}")
    public CheckinRecord get(@PathVariable("id") String id) {
        return checkinRecordService.getById(id);
    }

    /**
     * 批量删除签到记录（权限标识：checkin:checkinRecord:remove）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:remove')")
    @OperationLog(module = "签到记录", comments = "批量删除签到记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        checkinRecordService.removeByIds(ids);
    }

    /**
     * 保存签到记录（权限标识：checkin:checkinRecord:operation）
     */
    /*@PreAuthorize("hasAuthority('checkin:checkinRecord:operation')")
    @OperationLog(module = "签到记录", comments = "保存签到记录")
    @PostMapping("/operation")
    public Resp save(@RequestBody CheckinRecordParam param) {
        return checkinRecordService.saveCheckinRecord(param);
    }*/

    /**
     * 保存签到记录（权限标识：checkin:checkinRecord:operation）
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:operation')")
    @OperationLog(module = "签到记录", comments = "保存签到记录")
    @PostMapping("/operation")
    public Resp save(HttpServletRequest request) {
        Map<String, List<MultipartFile>> multipartFileMap = CommonUtil.getFileFromRequest(request);
        CheckinRecordParam param = new CheckinRecordParam();
        CommonUtil.setValueFromRequest(param, request);
        param.setFileMap(multipartFileMap);
        return checkinRecordService.saveCheckinRecord(param);
    }

    /**
     * 补签（权限标识：checkin:checkinRecord:addRecord）
     *
     * @param param 参数
     * @return {@link ApiResult }
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:addRecord')")
    @OperationLog(module = "签到记录", comments = "补录签到记录")
    @PostMapping("/addRecord")
    public Resp addRecord(@RequestBody CheckinRecordParam param, String blrq) {
        return checkinRecordService.addRecord(param,blrq);
    }

    /**
     * 管理端查询签到任务的各状态的学生数据
     *
     * @param param 参数
     * @return {@link PageResult }<{@link CheckinRecord }>
     */
    @PreAuthorize("hasAuthority('checkin:checkinRecord:pageCheckinData')")
    @OperationLog(module = "签到记录", comments = "管理端补录签到")
    @GetMapping("/pageCheckinData")
    public IPage<UserInfo> pageData(CheckinRecordParam param) {
        String rq = param.getSearchStartDate();
        AssertUtil.isTrue(StrUtil.isNotBlank(param.getItemId()), "项目id不能为空");
        CheckinItem checkinItem = checkinItemService.getById(param.getItemId());
        AssertUtil.isTrue(checkinItem != null, "项目不存在");
        String qdrq = checkinItem.getQdrq();
        if (StrUtil.isNotBlank(qdrq)) {
            String[] rqs = qdrq.split(",");
            if (StrUtil.isBlank(rq)) {
                String currentRq = DateUtil.getDate();
                if (qdrq.indexOf(currentRq) > -1) {
                    rq = currentRq;    // 当前日期有签到任务
                } else {
                    rq = rqs[0];    // 查询签到任务第一天
                }
            }
        }
        param.setCreateDate(rq);
        IPage<UserInfo> pagedCheckinData = checkinRecordService.pageCheckinUserInfoData(param);
        return pagedCheckinData;
    }

    /**
     * 下载签到人员导入模板
     *
     * @param response
     */
//    @PreAuthorize("hasAuthority('checkin:checkinRecord:exportData')")
    @OperationLog(module = "签到记录", comments = "下载签到人员导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        try {
            EasyExcelHelper.exportExcel(null, new String[]{"账号", "姓名"}, new String[]{"xgh", "xm"},
                    response, null, "签到人员导入模板");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    /**
     * 导入签到人员数据
     *
     * @param file
     * @param response
     */
    @PostMapping("/importData")
    @OperationLog(module = "签到记录", comments = "导入签到人员数据")
    @PreAuthorize("hasAuthority('checkin:checkinRecord:import')")
    public void importData(@RequestParam(name = "file") MultipartFile file, CheckinItem item, UserType userType, HttpServletResponse response) throws IOException {
        checkinRecordService.importData(file,item,userType);
    }

    /**
     * 导出签到记录
     *
     * @param param
     * @param response
     */
    @GetMapping(value = "/exportData")
    @OperationLog(module = "签到记录", comments = "导出签到记录")
    @PreAuthorize("hasAuthority('checkin:checkinRecord:export')")
    public void exportData(CheckinRecordParam param, HttpServletResponse response) {
        try {
            PageParam<CheckinRecord, CheckinRecordParam> page = new PageParam<>(param);
            param.setPage(1L);
            param.setLimit(Integer.MAX_VALUE - 1L);
            param.setInternalCall(true);
            PageResult<CheckinRecord> pageResult = checkinRecordService.pageCheckinData(param,page);
            new EasyExcelHelper<CheckinRecord>() {
            }.out("签到记录", pageResult.getList(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取签到数据统计
     *
     * @param param 参数
     * @return {@link Resp }<{@link ? }>
     */
    @PostMapping(value = "/getCheckinStatistics")
    @OperationLog(module = "签到记录", comments = "获取签到数据统计")
    @PreAuthorize("hasAuthority('checkin:checkinRecord:statistics')")
    public Resp getCheckinStatistics(CheckinRecordParam param, String column) {
        return checkinRecordService.getCheckinStatistics(param, column);
    }

}
