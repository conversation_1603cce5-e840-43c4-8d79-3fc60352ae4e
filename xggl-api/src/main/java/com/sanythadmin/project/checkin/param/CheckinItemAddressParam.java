package com.sanythadmin.project.checkin.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 签到项目地址查询参数
 *
 * <AUTHOR>
 * @since 2024-05-11 16:18:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckinItemAddressParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 预设地址id
     */
    @QueryField(type = QueryType.EQ)
    private String addressId;
    /**
     * 签到项目
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型(圆形,多边形)
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 范围/半径
     */
    private Integer fwbj;

    /**
     * 经纬度坐标
     */
    private String jwdzb;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

    /**
     * 是否来源地址库
     */
    private JudgeMark isFromMap;


}
