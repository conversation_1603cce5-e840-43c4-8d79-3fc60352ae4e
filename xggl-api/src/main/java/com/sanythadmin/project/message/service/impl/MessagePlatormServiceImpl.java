package com.sanythadmin.project.message.service.impl;

import com.sanythadmin.project.message.config.MessagePlatformConfig;
import com.sanythadmin.project.message.dto.MessageRequestDTO;
import com.sanythadmin.project.message.service.MessagePlatormService;
import com.sanythadmin.project.message.service.MessagePlatormTokenService;
import com.sanythadmin.project.message.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 外部消息服务impl
 *
 * <AUTHOR>
 * @date 2025/02/11
 */
@Service
@Slf4j
public class MessagePlatormServiceImpl implements MessagePlatormService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MessagePlatormTokenService tokenService;

    @Autowired
    private MessagePlatformConfig config;

    @Autowired
    private RetryUtil retryUtil;

    @Override
    public void sendMessage(MessageRequestDTO request) {
        String token = tokenService.getToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + token);

        HttpEntity<MessageRequestDTO> entity = new HttpEntity<>(request, headers);

        retryUtil.executeWithRetry(() -> {
            ResponseEntity<String> response = restTemplate.exchange(
                    config.getSendUrl() + "?access_token=" + token,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("Message sent successfully");
                return true;
            } else {
                log.warn("Failed to send message: {}", response.getBody());
                throw new RuntimeException("Failed to send message: " + response.getBody());
            }
        }, "send message");
    }
}


