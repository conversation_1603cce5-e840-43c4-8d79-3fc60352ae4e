package com.sanythadmin.project.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageContentDTO {

    /**
     * 微信/钉钉等消息类型
     * "10", "文本"
     * "50", "文本卡片"
     */
    private String sendType;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     * 短信时，content要包含模板变量
     */
    private String content;

    public String toJsonString() {
        return "{\"sendType\":\"" + sendType + "\",\"title\":\"" + title + "\",\"content\":\"" + content + "\"}";
    }
}