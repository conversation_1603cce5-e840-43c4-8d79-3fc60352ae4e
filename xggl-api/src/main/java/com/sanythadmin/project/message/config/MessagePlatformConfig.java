package com.sanythadmin.project.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "message.platform")
@Data
public class MessagePlatformConfig {
    private String clientId;
    private String clientSecret;
    private String authTokenUrl;
    private String authRefreshUrl;
    private String sendUrl;
    private int connectionTimeout;
    private int readTimeout;
    private int maxRetries;
    private long retryInterval;
}
