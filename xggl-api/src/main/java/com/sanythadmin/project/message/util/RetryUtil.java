package com.sanythadmin.project.message.util;

import com.sanythadmin.project.message.config.MessagePlatformConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * 重试工具类
 *
 * <AUTHOR>
 * @date 2025/02/11
 */
@Component
@Slf4j
public class RetryUtil {
    @Autowired
    private MessagePlatformConfig config;

    public <T> T executeWithRetry(Supplier<T> operation, String operationName) throws RuntimeException {
        int retries = 0;
        while (retries < config.getMaxRetries()) {
            try {
                return operation.get();
            } catch (Exception e) {
                log.warn("Error occurred while {}. Attempt: {}", operationName, retries + 1, e);
                retries++;
                if (retries < config.getMaxRetries()) {
                    long waitTime = config.getRetryInterval() * (long) Math.pow(2, retries - 1);
                    log.info("Retrying {}. Attempt: {}. Waiting for {} ms", operationName, retries + 1, waitTime);
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("Retry interrupted", ie);
                    }
                }
            }
        }
        throw new RuntimeException("Failed to " + operationName + " after " + config.getMaxRetries() + " attempts");
    }
}
