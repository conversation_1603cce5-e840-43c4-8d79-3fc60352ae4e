package com.sanythadmin.project.message.controller;

import com.sanythadmin.project.message.dto.MessageRequestDTO;
import com.sanythadmin.project.message.service.MessagePlatormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息控制器
 *
 * <AUTHOR>
 * @date 2025/02/11
 */
@RestController
@RequestMapping("/api/messages")
public class MessagePlatormController {
    @Autowired
    private MessagePlatormService messageService;

    /**
     * 发送消息
     *
     * @param request 请求
     * @return {@link ResponseEntity }<{@link String }>
     */
    @PostMapping("/send")
    public ResponseEntity<String> sendMessage(@RequestBody MessageRequestDTO request) {
        messageService.sendMessage(request);
        return ResponseEntity.ok("消息发送成功");
    }
}
