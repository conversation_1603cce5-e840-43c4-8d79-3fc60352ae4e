package com.sanythadmin.project.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageParamDTO {
    /**
     * @Description: 接收者
     * 多个用,逗号号分隔开
     * 【不能大于1000个】
     * 必传
     */
    private String receiver;
    /**
     * 消息发送渠道(10.短信 20.Email 30.企业微信 40.钉钉)
     * 注:钉钉限流政策，给同一用户发相同内容消息一天仅允许一次
     */
    private Integer sendChannel;
    /**
     * 消息类型(10.文本 40.图文 50.文本卡片 80.markdown)
     */
    private Integer msgType;
    /**
     * 消息内容
     */
    private String msgContent;
    /**
     * 回调地址
     */
    private String redirectUri;
}