package com.sanythadmin.project.message.service;

import com.sanythadmin.project.message.config.MessagePlatformConfig;
import com.sanythadmin.project.message.dto.TokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class MessagePlatormTokenService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private MessagePlatformConfig config;

    private String accessToken;
    private String refreshToken;
    private long expirationTime;

    public String getToken() {
        if (accessToken == null || System.currentTimeMillis() > expirationTime) {
            refreshToken();
        }
        return accessToken;
    }

    private void refreshToken() {
        if (refreshToken != null) {
            try {
                ResponseEntity<TokenResponse> response = restTemplate.exchange(
                    config.getAuthRefreshUrl() + "?refreshToken=" + refreshToken,
                    HttpMethod.POST,
                    null,
                    TokenResponse.class
                );
                updateTokenInfo(response.getBody());
            } catch (Exception e) {
                log.warn("无法刷新令牌，正在获取新令牌", e);
                fetchNewToken();
            }
        } else {
            fetchNewToken();
        }
    }

    private void fetchNewToken() {
        ResponseEntity<TokenResponse> response = restTemplate.exchange(
            config.getAuthTokenUrl() + "?clientId=" + config.getClientId() + "&clientSecret=" + config.getClientSecret(),
            HttpMethod.POST,
            null,
            TokenResponse.class
        );
        updateTokenInfo(response.getBody());
    }

    private void updateTokenInfo(TokenResponse tokenResponse) {
        this.accessToken = tokenResponse.getData().getAccessToken();
        this.refreshToken = tokenResponse.getData().getRefreshToken();
        this.expirationTime = System.currentTimeMillis() + (tokenResponse.getData().getExpiresIn() * 1000);
    }
}
