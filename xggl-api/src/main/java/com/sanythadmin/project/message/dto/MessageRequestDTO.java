package com.sanythadmin.project.message.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageRequestDTO {
    /**
     * 执行业务类型(固定参数send)
     */
    private String code;
    /**
     * 业务方标识(xgxt,zsxt),区分业务和匹配渠道账号
     */
    private String bizId;
    /**
     * 业务方模块(zzgl),区分业务和匹配渠道账号
     */
    private String bizModule;
    /**
     * 调用类型 (10.即时 20.定时)
     */
    private Integer callType;
    /**
     * 期望发送时间：立即发送.10  定时任务以及周期任务.cron表达式
     */
    private String expectPushTime;
    /**
     * 屏蔽类型10.夜间不屏蔽 20.夜间屏蔽
     */
    private Integer shieldType;
    /**
     * 发送账号(作为记录，具体的发送是渠道账号)
     */
    private String sendAccount;
    /**
     * 消息相关的参数
     */
    private List<MessageParamDTO> messageParamList;
}