package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.project.workstudy.entity.QgzxKsb;
import com.sanythadmin.project.workstudy.param.QgzxKsbParam;
import com.sanythadmin.project.workstudy.service.QgzxKsbService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/课时表控制器
 *
 * <AUTHOR>
 * @since 2025-07-10 15:33:46
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-ksb")
@RequiredArgsConstructor
public class QgzxKsbController extends BaseController {

    private final QgzxKsbService qgzxKsbService;
    private final SysModuleSetupService sysModuleSetupService;

    /**
     * 分页查询勤工助学课时表（权限标识：workstudy:qgzxKsb:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxKsb:list')")
    @GetMapping("/page")
    public PageResult<QgzxKsb> page(QgzxKsbParam param) {
        PageParam<QgzxKsb, QgzxKsbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = qgzxKsbService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部勤工助学课时表（权限标识：workstudy:qgzxKsb:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxKsb:list')")
    @GetMapping()
    public List<QgzxKsb> list(QgzxKsbParam param) {
        PageParam<QgzxKsb, QgzxKsbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return qgzxKsbService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询勤工助学课时表（权限标识：workstudy:qgzxKsb:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxKsb:list')")
    @GetMapping("/{id}")
    public QgzxKsb get(@PathVariable("id") String id) {
        return qgzxKsbService.getById(id);
    }

    /**
     * 添加或修改勤工助学课时表（权限标识：workstudy:qgzxKsb:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxKsb:operation')")
    @OperationLog(module = "勤工助学课时表", comments = "保存勤工助学课时表")
    @PostMapping("/operation")
    public void save(@RequestBody QgzxKsb qgzxKsb) {
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        qgzxKsb.setXnxq(year);
        if (StringUtils.hasLength(qgzxKsb.getId())) {
            qgzxKsbService.updateById(qgzxKsb);
        } else {
            qgzxKsbService.save(qgzxKsb);
        }
    }

    /**
     * 批量删除勤工助学课时表（权限标识：workstudy:qgzxKsb:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxKsb:remove')")
    @OperationLog(module = "勤工助学课时表", comments = "批量删除勤工助学课时表")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        qgzxKsbService.removeByIds(ids);
    }
}
