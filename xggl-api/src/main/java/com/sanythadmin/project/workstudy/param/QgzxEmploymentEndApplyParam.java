package com.sanythadmin.project.workstudy.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.ReviewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用工结束申请查询参数
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QgzxEmploymentEndApplyParam extends BaseParam {

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 申请人学/工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 学生申请ID
     */
    @QueryField(type = QueryType.EQ)
    private String studentApplyId;
    /**
     * 岗位ID
     */
    @QueryField(type = QueryType.EQ)
    private String jobId;

    /**
     * 审批状态
     */
    @QueryField(type = QueryType.EQ)
    private ReviewResult spzt;

    /**
     * 学年学期
     */
    @QueryField(type = QueryType.EQ)
    private String xnxq;

    /**
     * 用工开始日期-开始
     */
    @QueryField(ignore = true)
    private LocalDate ygksrqStart;

    /**
     * 用工开始日期-结束
     */
    @QueryField(ignore = true)
    private LocalDate ygksrqEnd;

    /**
     * 用工结束日期-开始
     */
    @QueryField(ignore = true)
    private LocalDate ygjsrqStart;

    /**
     * 用工结束日期-结束
     */
    @QueryField(ignore = true)
    private LocalDate ygjsrqEnd;

    /**
     * 申请时间-开始
     */
    @QueryField(type = QueryType.GE, value = "sqsj")
    private LocalDateTime sqsjStart;

    /**
     * 申请时间-结束
     */
    @QueryField(type = QueryType.LE, value = "sqsj")
    private LocalDateTime sqsjEnd;

    /**
     * 结束事由
     */
    @QueryField(type = QueryType.LIKE)
    private String jssyy;

    /**
     * 岗位名称
     */
    @QueryField(type = QueryType.LIKE, value = "job_name")
    private String jobName;

    /**
     * 申请人姓名
     */
    @QueryField(type = QueryType.LIKE, value = "xm")
    private String xm;
}
