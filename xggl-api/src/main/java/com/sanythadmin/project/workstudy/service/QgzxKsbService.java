package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workstudy.entity.QgzxKsb;
import com.sanythadmin.project.workstudy.enums.TimeType;

import java.util.List;

/**
 * 勤工助学课时表Service
 *
 * <AUTHOR>
 * @since 2025-07-10 15:33:46
 */
public interface QgzxKsbService extends IService<QgzxKsb> {

    /**
     * 根据学年学期和时间段类型查询课时表
     *
     * @param xnxq 学年学期
     * @param timeType 时间段类型
     * @return 课时表列表
     */
    List<QgzxKsb> getByXnxqAndTimeType(String xnxq, TimeType timeType);

}
