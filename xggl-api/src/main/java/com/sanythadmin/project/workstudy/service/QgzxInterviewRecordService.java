package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxInterviewRecord;
import com.sanythadmin.project.workstudy.param.QgzxInterviewRecordParam;
import com.sanythadmin.project.workstudy.vo.QgzxInterviewRecordVO;

import java.util.List;

/**
 * 面试记录Service
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
public interface QgzxInterviewRecordService extends IService<QgzxInterviewRecord> {

    /**
     * 分页查询面试记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxInterviewRecordVO> page(QgzxInterviewRecordParam param);

    /**
     * 查询面试记录列表
     *
     * @param param 查询参数
     * @return 面试记录列表
     */
    List<QgzxInterviewRecordVO> list(QgzxInterviewRecordParam param);

    /**
     * 根据ID查询面试记录详情
     *
     * @param id 面试记录ID
     * @return 面试记录详情
     */
    QgzxInterviewRecordVO getDetail(String id);

    /**
     * 新增或修改面试记录
     *
     * @param record 面试记录
     */
    void edit(QgzxInterviewRecord record);

    /**
     * 删除面试记录
     *
     * @param ids 面试记录ID数组
     */
    void delete(String... ids);

    /**
     * 用人单位分页查询面试记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxInterviewRecordVO> pageByEmployer(QgzxInterviewRecordParam param);

    /**
     * 学生查询自己的面试记录
     *
     * @param param 查询参数
     * @return 面试记录列表
     */
    List<QgzxInterviewRecordVO> listByStudent(QgzxInterviewRecordParam param);

    /**
     * 根据学生申请ID查询面试记录
     *
     * @param studentApplyId 学生申请ID
     * @return 面试记录
     */
    QgzxInterviewRecord getByStudentApplyId(String studentApplyId);

    /**
     * 批量创建面试记录（用于批量安排面试）
     *
     * @param studentApplyIds 学生申请ID列表
     * @param interviewRecord 面试基础信息
     */
    void batchCreate(List<String> studentApplyIds, QgzxInterviewRecord interviewRecord);
}
