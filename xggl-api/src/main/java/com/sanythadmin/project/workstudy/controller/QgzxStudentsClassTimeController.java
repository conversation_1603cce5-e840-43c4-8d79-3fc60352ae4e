package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.project.workstudy.entity.QgzxStudentsClassTime;
import com.sanythadmin.project.workstudy.param.StudentsClassTimeParam;
import com.sanythadmin.project.workstudy.service.QgzxStudentsClassTimeService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/学生上课时间控制器
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-students-classtime")
@RequiredArgsConstructor
public class QgzxStudentsClassTimeController extends BaseController {

    private final QgzxStudentsClassTimeService qgzxStudentsClassTimeService;
    private final SysModuleSetupService sysModuleSetupService;

    /**
     * 分页查询学生上课时间（权限标识：workstudy:StudentsClassTime:list）
     */
    @PreAuthorize("hasAuthority('workstudy:StudentsClassTime:list')")
    @GetMapping("/page")
    public PageResult<QgzxStudentsClassTime> page(StudentsClassTimeParam param) {
        PageParam<QgzxStudentsClassTime, StudentsClassTimeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = qgzxStudentsClassTimeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部学生上课时间（权限标识：workstudy:qgzxStudentsClassTime:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentsClassTime:list')")
    @GetMapping()
    public List<QgzxStudentsClassTime> list(StudentsClassTimeParam param) {
        PageParam<QgzxStudentsClassTime, StudentsClassTimeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return qgzxStudentsClassTimeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询学生上课时间（权限标识：workstudy:qgzxStudentsClassTime:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentsClassTime:list')")
    @GetMapping("/{id}")
    public QgzxStudentsClassTime get(@PathVariable("id") String id) {
        return qgzxStudentsClassTimeService.getById(id);
    }

    /**
     * 添加或修改学生上课时间（权限标识：workstudy:qgzxStudentsClassTime:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentsClassTime:operation')")
    @OperationLog(module = "学生上课时间", comments = "保存学生上课时间")
    @PostMapping("/operation")
    public void save(@RequestBody QgzxStudentsClassTime qgzxStudentsClassTime) {
        SysAccount account = getAccount();
        qgzxStudentsClassTime.setXgh(account.getUsername());
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        qgzxStudentsClassTime.setXnxq(year);
        if (StringUtils.hasLength(qgzxStudentsClassTime.getId())) {
            qgzxStudentsClassTimeService.updateById(qgzxStudentsClassTime);
        } else {
            qgzxStudentsClassTimeService.save(qgzxStudentsClassTime);
        }
    }
    /**
     * 批量编辑学生上课时间（权限标识：workstudy:qgzxStudentsClassTime:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentsClassTime:operation')")
    @OperationLog(module = "学生上课时间", comments = "批量编辑学生上课时间")
    @PostMapping("/saveBatch")
    public void saveBatch(@RequestBody List<QgzxStudentsClassTime> availabilityList) {
        qgzxStudentsClassTimeService.saveQgzxStudentsClassTimeList(availabilityList);
    }

    /**
     * 批量删除学生上课时间（权限标识：workstudy:qgzxStudentsClassTime:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentsClassTime:remove')")
    @OperationLog(module = "学生上课时间", comments = "批量删除学生上课时间")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        qgzxStudentsClassTimeService.removeByIds(ids);
    }
}
