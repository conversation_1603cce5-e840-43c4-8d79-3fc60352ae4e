package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNode;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.dto.WorkflowNodeStateDTO;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeFormService;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.dto.QgzxApprovalMessage;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;


/**
 * 勤工助学/审核学生岗位申请
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-student-approval")
@RequiredArgsConstructor
public class QgzxStudentApprovalController extends BaseController {

    private final QgzxStudentApplyService qgzxStudentApplyService;
    private final WorkflowService workflowService;
    private final WorkflowNodeService workflowNodeService;
    private final SysModuleSetupService sysModuleSetupService;
    private final WorkflowNodeFormService workflowNodeFormService;
    private final RedissonClient redissonClient;


    /**
     * 审核人分页查询学生申请（权限标识：workstudy:qgzxStudentApproval:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:list')")
    @GetMapping("/page")
    public PageResult<QgzxStudentApply> page(QgzxStudentApplyParam param,
                                             UserInfoParam userInfoParam,
                                             WorkflowApprovalNodeParam approvalNodeParam) {
        return qgzxStudentApplyService.pageApprovalList(param,userInfoParam,approvalNodeParam);
    }

    /**
     * 审核人查询学生申请（权限标识：workstudy:qgzxStudentApproval:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:list')")
    @GetMapping()
    public List<QgzxStudentApply> list(QgzxStudentApplyParam param,
                                       UserInfoParam userInfoParam,
                                         WorkflowApprovalNodeParam approvalNodeParam) {
        return qgzxStudentApplyService.listApprovalList(param,userInfoParam,approvalNodeParam);
    }

    /**
     * 根据id查询岗位申报（权限标识：workstudy:qgzxStudentApproval:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:list')")
    @GetMapping("/{id}")
    public QgzxStudentApply get(@PathVariable("id") String id) {
        return qgzxStudentApplyService.getById(id);
    }

    /**
     * 当前用户审批节点
     *
     * @param xnxq 学年学期
     * @return
     */
    @GetMapping("/approvalNodeList")
    public List<WorkflowNode> approvalNodeList(String xnxq) {
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzxSqgw.getCode());
        return workflowNodeService.nodeListByFormInfoAndApprover(SysModule.qgzxSqgw.getCode()
                , StringUtils.hasText(xnxq) ? xnxq : year
                , CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId()));
    }

    /**
     * 根据申请信息查询当前用户审核表单字段
     *
     * @param applicationId 申请记录ID
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeFormFieldList")
    public List<WorkflowNodeForm> nodeFormFieldList(String workflowId, String applicationId, String nodeId) {
        return workflowNodeFormService.getNodeFormByUser(EvaluateApprovalNode.class, EvaluateApplicationInfo.class,
                applicationId, new WorkflowParam(workflowId), nodeId);
    }

    /**
     * 工作流审核流程
     *
     * @return
     */
    @GetMapping("/workflow")
    public WorkflowDTO workflow(String workflowId) {
        return workflowService.get(workflowId);
    }

    /**
     * 根据申请信息查询当前用户审核节点记录
     *
     * @param applicationId 申请记录ID
     * @param nodeId        审核节点ID
     * @return
     */
    @GetMapping("/nodeApprovalRecord")
    public List<WorkflowApprovalNodeRecord> nodeApprovalRecord(String applicationId, String nodeId) {
        return workflowService.approvalNodeRecordList(WorkflowApprovalNode.class, WorkflowApprovalNodeRecord.class,
                null, applicationId, nodeId);
    }

    /**
     * 根据申请信息ID查询所有审核节点，审核记录
     *
     * @param id 申请信息ID
     * @return
     */
    @GetMapping("/{id}/approvalNodes")
    public List<ApprovalNodeInfo<WorkflowApprovalNode, WorkflowApprovalNodeRecord>> approvalNodes(@PathVariable("id") String id) {
        return workflowService.approvalNodeInfo(WorkflowApprovalNode.class, WorkflowApprovalNodeRecord.class, id, null);
    }

    /**
     * 根据申请记录ID或项目ID,年份查询节点自定义状态
     *
     * @param applicationId 申请记录ID
     * @param param         工作流查询参数
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeState")
    public List<WorkflowNodeStateDTO> nodeState(String applicationId, WorkflowParam param, String nodeId) {
        param.setYear(sysModuleSetupService.getModuleYear(SysModule.qgzxSqgw.getCode()));
        return workflowService.nodeCustomState(WorkflowApprovalNode.class, param, nodeId, applicationId);
    }

    /**
     * 单个审核操作（权限标识：workstudy:qgzxStudentApproval:operation）
     *
     * @param request 请求体
     */
    @OperationLog(module = "岗位申请审核", comments = "单个审核学生岗位申请")
    @PreAuthorize("hasAuthority('evaluate:approval:operation')")
    @PostMapping("/operation")
    public String operation(HttpServletRequest request) throws ExecutionException, InterruptedException {
        FormDataWrapper<WorkflowApprovalNodeRecord> wrapper = new FormDataWrapper<>(request, WorkflowApprovalNodeRecord.class);
        Map<String, List<MultipartFile>> multipartFileMap = wrapper.getFileFields();
        WorkflowApprovalNodeRecord record = wrapper.getObject();
        record.setId(null);
        record.setFileMap(multipartFileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        CompletableFuture<String> future = qgzxStudentApplyService.approve(record, null, executor);
        CompletableFuture.allOf(future).join();
        return future.get();
    }

    /**
     * 批量审核操作（权限标识：workstudy:qgzxStudentApproval:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:operation')")
    @OperationLog(module = "岗位申请审核", comments = "批量审核学生岗位申请")
    @PostMapping("/batchOperation")
    public void batchOperation(HttpServletRequest request, WorkflowApprovalNodeRecord record,QgzxStudentApplyParam param,UserInfoParam userInfoParam,
                          WorkflowApprovalNodeParam approvalNodeParam) throws Exception {
        List<CompletableFuture<String>> futures = new ArrayList<>();
        List<QgzxApprovalMessage> errors = new ArrayList<>();
        Map<String, List<MultipartFile>> fileMap = CommonUtil.getFileFromRequest(request);
        param.setPage(1L);
        param.setLimit((long) (Integer.MAX_VALUE - 1));
        param.setInternalCall();
        UserInfoParam userInfoParam1 = new UserInfoParam();
        //TODO 有需要userInfoParam参数的话再单个赋值
        WorkflowApprovalNodeParam workflowApprovalNodeParam = new WorkflowApprovalNodeParam();
        //TODO approvalNodeParam同上
        PageResult<QgzxStudentApply> pageResult = qgzxStudentApplyService.pageApprovalList(param,userInfoParam1,workflowApprovalNodeParam);
        List<QgzxStudentApply> list = pageResult.getList();
        record.setId(null);
        record.setFileMap(fileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        for (QgzxStudentApply studentApply : list) {
            WorkflowApprovalNodeRecord temp = new WorkflowApprovalNodeRecord();
            BeanUtils.copyProperties(record, temp);
            temp.setApplicationId(studentApply.getId());
            CompletableFuture<String> future = qgzxStudentApplyService.approve(temp,studentApply, executor)
                    .handle((result, ex) -> {
                        if (ex != null)
                            errors.add(new QgzxApprovalMessage(studentApply.getXgh(),studentApply.getUserInfo().getXm(),
                                    studentApply.getJobApplication().getJobName(),ex.getCause().getMessage()));
                        return null;
                    });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).join();
        if (!CollectionUtils.isEmpty(errors))
            EasyExcelHelper.writeApproveErrorInfo(errors, new String[]{"工号", "姓名", "申请岗位名称", "失败原因"},
                    new String[]{"xgh", "xm", "content", "message"});
    }

    /**
     * 审核人批量删除岗位（权限标识：workstudy:qgzxStudentApproval:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:remove')")
    @OperationLog(module = "岗位申请审核", comments = "批量删除学生岗位申请")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        qgzxStudentApplyService.delete(ids.toArray(new String[]{}));
    }

    /**
     * 查询可调剂的岗位列表（权限标识：workstudy:qgzxStudentApproval:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApproval:list')")
    @GetMapping("/adjustableJobs/{studentApplyId}")
    public List<QgzxJobApplication> listAdjustableJobs(@PathVariable("studentApplyId") String studentApplyId) {
        return qgzxStudentApplyService.listAdjustableJobs(studentApplyId);
    }
}
