package com.sanythadmin.project.workstudy.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用人单位查询参数
 *
 * <AUTHOR>
 * @since 2025-07-14 13:48:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxEmployerParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 单位名称
     */
    private String name;

    /**
     * 单位描述
     */
    private String bz;

    /**
     * 办公电话
     */
    private String bgdh;
    /**
     * 单位地址
     */
    private String dwdz;
    /**
     * 资质证明
     */
    /*private String zzzz;*/

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 负责人
     */
    private String fzr;

    /**
     * 联系人类型
     */
    @Column(name = "LXRLX")
    @TableField("LXRLX")
    private String lxrlx;

    /**
     * 联系人账号
     */
    @NotEmpty(message = "联系人账号不能为空")
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 联系人姓名
     */
    private String xm;

    /**
     * 联系人性别
     */
    private String xb;
    /**
     * 手机号
     */
    private String sjh;
    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 所属部门
     */
    private String xyid;

    @QueryField(ignore = true)
    private UserType userType;

    public UserType getUserType() {
        return UserType.EMPLOYER;
    }
}
