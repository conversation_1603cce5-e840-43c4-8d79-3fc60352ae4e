package com.sanythadmin.project.workstudy.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryDetailVO;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 勤工助学报酬月度汇总Mapper
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface QgzxRemunerationSummaryMapper extends MyMPJBaseMapper<QgzxRemunerationApply> {

    /**
     * 月度总体汇总统计
     *
     * @param sbny 申报年月
     * @param wrapper 查询条件
     * @return 汇总统计结果
     */
    @Select("SELECT " +
            "    #{sbny} as sbny, " +
            "    COUNT(DISTINCT rd.XGH) as studentCount, " +
            "    SUM(rd.WORK_HOURS) as totalHours, " +
            "    SUM(rd.REMUNERATION_AMOUNT) as totalAmount, " +
            "    COUNT(DISTINCT ra.JOB_ID) as jobCount, " +
            "    COUNT(DISTINCT ja.EID) as employerCount, " +
            "    CASE WHEN SUM(rd.WORK_HOURS) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / SUM(rd.WORK_HOURS) ELSE 0 END as averageHourlyRate " +
            "FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_REMUNERATION_DETAIL rd ON ra.ID = rd.REMUNERATION_APPLY_ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID " +
            "INNER JOIN SYT_QGZX_EMPLOYER emp ON ja.EID = emp.ID " +
            "WHERE ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo' " +
            "${ew.customSqlSegment}")
    QgzxRemunerationSummaryVO selectMonthlySummary(@Param("sbny") String sbny, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 按岗位类别分组统计
     *
     * @param sbny 申报年月
     * @param wrapper 查询条件
     * @return 分组统计结果
     */
    @Select("SELECT " +
            "    jt.ID as groupId, " +
            "    jt.NAME as groupName, " +
            "    'JOB_TYPE' as groupType, " +
            "    COUNT(DISTINCT rd.XGH) as studentCount, " +
            "    SUM(rd.WORK_HOURS) as totalHours, " +
            "    SUM(rd.REMUNERATION_AMOUNT) as totalAmount, " +
            "    COUNT(DISTINCT ra.JOB_ID) as jobCount, " +
            "    CASE WHEN SUM(rd.WORK_HOURS) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / SUM(rd.WORK_HOURS) ELSE 0 END as averageHourlyRate, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.WORK_HOURS) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageHours, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageAmount " +
            "FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_REMUNERATION_DETAIL rd ON ra.ID = rd.REMUNERATION_APPLY_ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID " +
            "INNER JOIN SYT_QGZX_EMPLOYER emp ON ja.EID = emp.ID " +
            "WHERE ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo' " +
            "${ew.customSqlSegment} " +
            "GROUP BY jt.ID, jt.NAME " +
            "ORDER BY SUM(rd.REMUNERATION_AMOUNT) DESC")
    List<QgzxRemunerationSummaryDetailVO> selectSummaryByJobType(@Param("sbny") String sbny, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 按用人单位分组统计
     *
     * @param sbny 申报年月
     * @param wrapper 查询条件
     * @return 分组统计结果
     */
    @Select("SELECT " +
            "    emp.ID as groupId, " +
            "    emp.NAME as groupName, " +
            "    'EMPLOYER' as groupType, " +
            "    COUNT(DISTINCT rd.XGH) as studentCount, " +
            "    SUM(rd.WORK_HOURS) as totalHours, " +
            "    SUM(rd.REMUNERATION_AMOUNT) as totalAmount, " +
            "    COUNT(DISTINCT ra.JOB_ID) as jobCount, " +
            "    CASE WHEN SUM(rd.WORK_HOURS) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / SUM(rd.WORK_HOURS) ELSE 0 END as averageHourlyRate, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.WORK_HOURS) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageHours, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageAmount " +
            "FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_REMUNERATION_DETAIL rd ON ra.ID = rd.REMUNERATION_APPLY_ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID " +
            "INNER JOIN SYT_QGZX_EMPLOYER emp ON ja.EID = emp.ID " +
            "WHERE ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo' " +
            "${ew.customSqlSegment} " +
            "GROUP BY emp.ID, emp.NAME " +
            "ORDER BY SUM(rd.REMUNERATION_AMOUNT) DESC")
    List<QgzxRemunerationSummaryDetailVO> selectSummaryByEmployer(@Param("sbny") String sbny, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 按学院分组统计
     *
     * @param sbny 申报年月
     * @param wrapper 查询条件
     * @return 分组统计结果
     */
    @Select("SELECT " +
            "    uom.XYID as groupId, " +
            "    dwb.NAME as groupName, " +
            "    'COLLEGE' as groupType, " +
            "    COUNT(DISTINCT rd.XGH) as studentCount, " +
            "    SUM(rd.WORK_HOURS) as totalHours, " +
            "    SUM(rd.REMUNERATION_AMOUNT) as totalAmount, " +
            "    COUNT(DISTINCT ra.JOB_ID) as jobCount, " +
            "    CASE WHEN SUM(rd.WORK_HOURS) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / SUM(rd.WORK_HOURS) ELSE 0 END as averageHourlyRate, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.WORK_HOURS) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageHours, " +
            "    CASE WHEN COUNT(DISTINCT rd.XGH) > 0 THEN SUM(rd.REMUNERATION_AMOUNT) / COUNT(DISTINCT rd.XGH) ELSE 0 END as averageAmount " +
            "FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_REMUNERATION_DETAIL rd ON ra.ID = rd.REMUNERATION_APPLY_ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID " +
            "INNER JOIN SYT_QGZX_EMPLOYER emp ON ja.EID = emp.ID " +
            "INNER JOIN SYT_USER_INFO u ON rd.XGH = u.XGH " +
            "INNER JOIN SYT_USER_ORG_MAP uom ON rd.XGH = uom.XGH " +
            "INNER JOIN SYT_CODE_DWB dwb ON uom.XYID = dwb.ID " +
            "WHERE ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo' " +
            "${ew.customSqlSegment} " +
            "GROUP BY uom.XYID, dwb.NAME " +
            "ORDER BY SUM(rd.REMUNERATION_AMOUNT) DESC")
    List<QgzxRemunerationSummaryDetailVO> selectSummaryByCollege(@Param("sbny") String sbny, @Param(Constants.WRAPPER) Wrapper wrapper);

    /**
     * 获取月度明细数据（用于导出）
     *
     * @param sbny 申报年月
     * @param wrapper 查询条件
     * @return 明细数据列表
     */
    @Select("SELECT " +
            "    rd.XGH, " +
            "    u.XM as studentName, " +
            "    dwb.NAME as collegeName, " +
            "    ja.JOB_NAME as jobName, " +
            "    jt.NAME as jobTypeName, " +
            "    emp.NAME as employerName, " +
            "    rd.WORK_HOURS as workHours, " +
            "    rd.HOURLY_RATE as hourlyRate, " +
            "    rd.REMUNERATION_AMOUNT as remunerationAmount, " +
            "    ra.SBNY as sbny, " +
            "    ra.SPZT as spzt " +
            "FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_REMUNERATION_DETAIL rd ON ra.ID = rd.REMUNERATION_APPLY_ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "INNER JOIN SYT_QGZX_JOB_TYPE jt ON ja.JOB_TYPE_ID = jt.ID " +
            "INNER JOIN SYT_QGZX_EMPLOYER emp ON ja.EID = emp.ID " +
            "INNER JOIN SYT_USER_INFO u ON rd.XGH = u.XGH " +
            "LEFT JOIN SYT_USER_ORG_MAP uom ON rd.XGH = uom.XGH " +
            "LEFT JOIN SYT_CODE_DWB dwb ON uom.XYID = dwb.ID " +
            "WHERE ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo' " +
            "${ew.customSqlSegment} " +
            "ORDER BY rd.REMUNERATION_AMOUNT DESC, rd.XGH")
    List<QgzxRemunerationSummaryDetailVO> selectMonthlyDetails(@Param("sbny") String sbny, @Param(Constants.WRAPPER) Wrapper wrapper);
}
