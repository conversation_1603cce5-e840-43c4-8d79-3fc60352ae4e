package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.entity.QgzxInterviewRecord;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.enums.InterviewResult;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import com.sanythadmin.project.workstudy.mapper.QgzxInterviewRecordMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxJobApplicationMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxStudentApplyMapper;
import com.sanythadmin.project.workstudy.param.QgzxInterviewRecordParam;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import com.sanythadmin.project.workstudy.service.QgzxInterviewRecordService;
import com.sanythadmin.project.workstudy.vo.QgzxInterviewRecordVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 面试记录Service实现
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Service
@RequiredArgsConstructor
public class QgzxInterviewRecordServiceImpl extends ServiceImpl<QgzxInterviewRecordMapper, QgzxInterviewRecord> implements QgzxInterviewRecordService {

    private final QgzxInterviewRecordMapper mapper;
    private final QgzxJobApplicationMapper qgzxJobApplicationMapper;
    private final QgzxStudentApplyMapper qgzxStudentApplyMapper;
    private final QgzxEmployerService employerService;

    @Override
    public PageResult<QgzxInterviewRecordVO> page(QgzxInterviewRecordParam param) {
        MyMPJLambdaWrapper<QgzxInterviewRecord, QgzxInterviewRecordParam> wrapper = buildQueryWrapper(param);
        Page<QgzxInterviewRecordVO> page = new Page<>(param.getPage(), param.getLimit());
        page = mapper.selectJoinPage(page, QgzxInterviewRecordVO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxInterviewRecordVO> list(QgzxInterviewRecordParam param) {
        MyMPJLambdaWrapper<QgzxInterviewRecord, QgzxInterviewRecordParam> wrapper = buildQueryWrapper(param);
        List<QgzxInterviewRecordVO> list = mapper.selectJoinList(QgzxInterviewRecordVO.class, wrapper);
        return list;
    }

    @Override
    public QgzxInterviewRecordVO getDetail(String id) {
        QgzxInterviewRecordParam param = new QgzxInterviewRecordParam();
        param.setId(id);
        MyMPJLambdaWrapper<QgzxInterviewRecord, QgzxInterviewRecordParam> wrapper = buildQueryWrapper(param);
        QgzxInterviewRecordVO vo = mapper.selectJoinOne(QgzxInterviewRecordVO.class, wrapper);
        return vo;
    }

    @Transactional
    @Override
    public void edit(QgzxInterviewRecord record) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();
        if (StringUtils.hasText(record.getId())) {
            QgzxInterviewRecord existingRecord = getById(record.getId());
            AssertUtil.isTrue(existingRecord != null, "面试记录不存在");

            checkEditPermission(existingRecord);
            record.setUpdateTime(now);
            record.setUpdateBy(currentUser);
            updateById(record);
            record.setStudentApplyId(existingRecord.getStudentApplyId());
            updateStudentWorkStatusIfInterviewPassed(record);
        } else {
            AssertUtil.isTrue(StringUtils.hasText(record.getStudentApplyId()), "学生申请ID不能为空");
            QgzxInterviewRecord existing = getByStudentApplyId(record.getStudentApplyId());
            AssertUtil.isTrue(existing == null, "该学生申请已存在面试记录");
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(record.getStudentApplyId());
            AssertUtil.isTrue(studentApply != null, "学生申请不存在");
            QgzxJobApplication jobApplication = qgzxJobApplicationMapper.selectById(studentApply.getJobId());
            AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");

            if (record.getInterviewResult() == null) {
                record.setInterviewResult(InterviewResult.DaiMianShi);
            }

            record.setCreateTime(now);
            record.setCreateBy(currentUser);
            save(record);

            updateStudentWorkStatusIfInterviewPassed(record);
        }
    }

    @Transactional
    @Override
    public void delete(String... ids) {
        for (String id : ids) {
            QgzxInterviewRecord record = getById(id);
            AssertUtil.isTrue(record != null, "面试记录不存在");
            checkEditPermission(record);
        }
        removeBatchByIds(List.of(ids));
    }

    @Override
    public PageResult<QgzxInterviewRecordVO> pageByEmployer(QgzxInterviewRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        QgzxEmployer employer = employerService.getOne(
            new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, currentUser)
        );
        AssertUtil.isTrue(employer != null, "未找到用人单位信息");
        param.setEmployerId(employer.getId());
        return page(param);
    }

    @Override
    public List<QgzxInterviewRecordVO> listByStudent(QgzxInterviewRecordParam param) {
        String currentUser = SecurityUtil.getUsername();
        param.setXgh(currentUser);
        return list(param);
    }

    @Override
    public QgzxInterviewRecord getByStudentApplyId(String studentApplyId) {
        return getOne(new LambdaQueryWrapper<QgzxInterviewRecord>()
            .eq(QgzxInterviewRecord::getStudentApplyId, studentApplyId));
    }

    @Transactional
    @Override
    public void batchCreate(List<String> studentApplyIds, QgzxInterviewRecord baseRecord) {
        String currentUser = SecurityUtil.getUsername();
        LocalDateTime now = LocalDateTime.now();

        for (String studentApplyId : studentApplyIds) {
            QgzxInterviewRecord existing = getByStudentApplyId(studentApplyId);
            if (existing != null) {
                continue;
            }
            
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(studentApplyId);
            if (studentApply == null) {
                continue;
            }
            
            QgzxJobApplication jobApplication = qgzxJobApplicationMapper.selectById(studentApply.getJobId());
            if (jobApplication == null) {
                continue;
            }
            
            QgzxInterviewRecord record = new QgzxInterviewRecord();
            org.springframework.beans.BeanUtils.copyProperties(baseRecord, record);
            
            record.setId(null);
            record.setStudentApplyId(studentApplyId);
            record.setInterviewResult(InterviewResult.DaiMianShi);
            record.setCreateTime(now);
            record.setCreateBy(currentUser);

            save(record);
        }
    }

    /**
     * 构建查询条件
     */
    private MyMPJLambdaWrapper<QgzxInterviewRecord, QgzxInterviewRecordParam> buildQueryWrapper(QgzxInterviewRecordParam param) {
        MyMPJLambdaWrapper<QgzxInterviewRecord, QgzxInterviewRecordParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAll(QgzxInterviewRecord.class);
        wrapper.selectAssociation("sa", QgzxStudentApply.class, QgzxInterviewRecordVO::getStudentApply)
               .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxInterviewRecord::getStudentApplyId);
        wrapper.selectAssociation("u", UserInfo.class, QgzxInterviewRecordVO::getUserInfo)
               .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);
        wrapper.selectAssociation("j", QgzxJobApplication.class, QgzxInterviewRecordVO::getJobApplication)
               .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxInterviewRecordVO::getEmployer)
               .leftJoin(QgzxEmployer.class, "e", QgzxEmployer::getId, QgzxJobApplication::getEid);

        wrapper.selectAs(UserInfo::getXm, QgzxInterviewRecordVO::getStudentName);
        wrapper.selectAs(UserInfo::getSjh, QgzxInterviewRecordVO::getStudentContact);
        wrapper.selectAs(QgzxJobApplication::getJobName, QgzxInterviewRecordVO::getJobName);
        wrapper.selectAs(QgzxEmployer::getName, QgzxInterviewRecordVO::getEmployerName);
        wrapper.selectAs(QgzxStudentApply::getSqly, QgzxInterviewRecordVO::getApplyReason);
        wrapper.selectAs(QgzxStudentApply::getTcys, QgzxInterviewRecordVO::getAdvantages);
        if (StringUtils.hasText(param.getJobName())) {
            wrapper.like("j.JOB_NAME", param.getJobName());
        }
        if (StringUtils.hasText(param.getStudentName())) {
            wrapper.like("u.XM", param.getStudentName());
        }
        if (StringUtils.hasText(param.getEmployerName())) {
            wrapper.like("e.NAME", param.getEmployerName());
        }
        // 通过关联表查询的条件
        if (StringUtils.hasText(param.getXgh())) {
            wrapper.eq("sa.XGH", param.getXgh());
        }
        if (StringUtils.hasText(param.getJobId())) {
            wrapper.eq("sa.JOB_ID", param.getJobId());
        }
        if (StringUtils.hasText(param.getEmployerId())) {
            wrapper.eq("j.EID", param.getEmployerId());
        }

        wrapper.orderByDesc(QgzxInterviewRecord::getCreateTime);
        return wrapper;
    }

    /**
     * 检查编辑权限
     */
    private void checkEditPermission(QgzxInterviewRecord record) {
        String currentUser = SecurityUtil.getUsername();
        if (Objects.equals(currentUser, record.getCreateBy())) {
            return;
        }
        //授权角色可以编辑
        if (SecurityUtil.hasAuthority("workstudy:qgzxInterviewRecord:operation")) {
            return;
        }
        QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(record.getStudentApplyId());
        if (studentApply != null) {
            QgzxJobApplication jobApplication = qgzxJobApplicationMapper.selectById(studentApply.getJobId());
            if (jobApplication != null) {
                QgzxEmployer employer = employerService.getOne(
                    new LambdaQueryWrapper<QgzxEmployer>()
                        .eq(QgzxEmployer::getXgh, currentUser)
                        .eq(QgzxEmployer::getId, jobApplication.getEid())
                );
                if (employer != null) {
                    return;
                }
            }
        }

        AssertUtil.throwMessage("无权限操作此面试记录");
    }

    /**
     * 如果面试结果为通过，更新学生申请的用工状态
     */
    private void updateStudentWorkStatusIfInterviewPassed(QgzxInterviewRecord record) {
        if (InterviewResult.TongGuo.equals(record.getInterviewResult())) {
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(record.getStudentApplyId());
            if (studentApply != null) {
                if (ReviewResult.TongGuo.equals(studentApply.getSpzt())) {
                    QgzxJobApplication jobApplication = qgzxJobApplicationMapper.selectById(studentApply.getJobId());
                    if (jobApplication != null && JudgeMark.YES.equals(jobApplication.getSfms())) {
                        // 更新学生申请的用工状态为用工
                        qgzxStudentApplyMapper.update(new LambdaUpdateWrapper<QgzxStudentApply>()
                                .set(QgzxStudentApply::getYgzt, WorkStatus.YG)
                                .eq(QgzxStudentApply::getId, studentApply.getId()));
                    }
                }
            }
        }
    }
}
