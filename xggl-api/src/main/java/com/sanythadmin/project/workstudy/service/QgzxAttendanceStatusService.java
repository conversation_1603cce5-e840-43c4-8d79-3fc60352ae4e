package com.sanythadmin.project.workstudy.service;

import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;

import java.time.LocalDateTime;

/**
 * 考勤状态判断服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface QgzxAttendanceStatusService {

    /**
     * 根据业务规则自动判断打卡状态
     *
     * @param record 考勤记录
     * @param jobApplication 岗位信息
     * @return 打卡状态
     */
    AttendanceStatus determineAttendanceStatus(QgzxAttendanceRecord record, QgzxJobApplication jobApplication);

    /**
     * 根据打卡时间和规定时间判断状态
     *
     * @param clockTime 实际打卡时间
     * @param scheduledTime 规定时间
     * @param attendanceType 考勤类型（上班/下班）
     * @param toleranceMinutes 容忍时间（分钟）
     * @return 打卡状态
     */
    AttendanceStatus determineStatusByTime(LocalDateTime clockTime, 
                                         LocalDateTime scheduledTime, 
                                         AttendanceType attendanceType, 
                                         int toleranceMinutes);

    /**
     * 检查是否为请假状态
     *
     * @param studentApplyId 学生申请ID
     * @param attendanceDate 考勤日期
     * @return 是否请假
     */
    boolean isOnLeave(String studentApplyId, LocalDateTime attendanceDate);

    /**
     * 更新考勤记录的状态
     *
     * @param recordId 考勤记录ID
     * @param status 新状态
     */
    void updateAttendanceStatus(String recordId, AttendanceStatus status);

    /**
     * 批量更新考勤记录状态
     *
     * @param studentApplyId 学生申请ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    void batchUpdateAttendanceStatus(String studentApplyId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 检查并创建缺勤记录
     * 用于定时任务，检查应该打卡但未打卡的情况
     *
     * @param targetDate 检查的日期
     */
    void checkAndCreateAbsentRecords(LocalDateTime targetDate);

    /**
     * 为指定学生创建缺勤记录
     *
     * @param studentApplyId 学生申请ID
     * @param targetDate 缺勤日期
     * @param attendanceType 考勤类型（上班/下班）
     */
    void createAbsentRecord(String studentApplyId, LocalDateTime targetDate, AttendanceType attendanceType);
}
