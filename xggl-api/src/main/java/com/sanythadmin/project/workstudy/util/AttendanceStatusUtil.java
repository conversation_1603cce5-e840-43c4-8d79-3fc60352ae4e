package com.sanythadmin.project.workstudy.util;

import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 考勤状态工具类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
public class AttendanceStatusUtil {

    // 默认工作时间配置
    private static final LocalTime DEFAULT_START_TIME = LocalTime.of(9, 0);  // 09:00
    private static final LocalTime DEFAULT_END_TIME = LocalTime.of(17, 0);   // 17:00
    
    // 默认容忍时间（分钟）
    private static final int DEFAULT_LATE_TOLERANCE = 15;      // 迟到容忍时间
    private static final int DEFAULT_EARLY_TOLERANCE = 30;     // 早退容忍时间

    /**
     * 根据时间规则判断考勤状态
     * 注意：此方法主要用于已有打卡记录的状态判断
     * 缺勤状态应该通过其他方式判断（如定时任务检查未打卡记录）
     *
     * @param clockTime 实际打卡时间
     * @param attendanceType 考勤类型
     * @param workStartTime 工作开始时间
     * @param workEndTime 工作结束时间
     * @return 考勤状态
     */
    public static AttendanceStatus determineStatus(LocalDateTime clockTime,
                                                 AttendanceType attendanceType,
                                                 LocalTime workStartTime,
                                                 LocalTime workEndTime) {
        // 如果clockTime为null，说明这是一个缺勤记录（由系统创建）
        if (clockTime == null) {
            return AttendanceStatus.ABSENT;
        }

        LocalTime actualTime = clockTime.toLocalTime();

        if (attendanceType == AttendanceType.CLOCK_IN) {
            return determineClockInStatus(actualTime, workStartTime != null ? workStartTime : DEFAULT_START_TIME);
        } else if (attendanceType == AttendanceType.CLOCK_OUT) {
            return determineClockOutStatus(actualTime, workEndTime != null ? workEndTime : DEFAULT_END_TIME);
        }

        return AttendanceStatus.NORMAL;
    }

    /**
     * 判断是否应该创建缺勤记录
     * 用于定时任务检查
     *
     * @param targetDate 检查日期
     * @param hasClockInRecord 是否有上班打卡记录
     * @param hasClockOutRecord 是否有下班打卡记录
     * @param isWorkDay 是否为工作日
     * @return 是否应该创建缺勤记录
     */
    public static boolean shouldCreateAbsentRecord(LocalDateTime targetDate,
                                                 boolean hasClockInRecord,
                                                 boolean hasClockOutRecord,
                                                 boolean isWorkDay) {
        // 非工作日不创建缺勤记录
        if (!isWorkDay) {
            return false;
        }

        // 如果是工作日但没有任何打卡记录，则应该创建缺勤记录
        return !hasClockInRecord || !hasClockOutRecord;
    }

    /**
     * 判断上班打卡状态
     */
    private static AttendanceStatus determineClockInStatus(LocalTime actualTime, LocalTime workStartTime) {
        LocalTime lateThreshold = workStartTime.plusMinutes(DEFAULT_LATE_TOLERANCE);
        
        if (actualTime.isAfter(lateThreshold)) {
            return AttendanceStatus.LATE;
        }
        
        return AttendanceStatus.NORMAL;
    }

    /**
     * 判断下班打卡状态
     */
    private static AttendanceStatus determineClockOutStatus(LocalTime actualTime, LocalTime workEndTime) {
        LocalTime earlyThreshold = workEndTime.minusMinutes(DEFAULT_EARLY_TOLERANCE);
        
        if (actualTime.isBefore(earlyThreshold)) {
            return AttendanceStatus.EARLY_LEAVE;
        }
        
        return AttendanceStatus.NORMAL;
    }

    /**
     * 解析时间字符串为LocalTime
     *
     * @param timeStr 时间字符串，格式如 "09:00" 或 "9:00"
     * @return LocalTime对象，解析失败返回null
     */
    public static LocalTime parseTime(String timeStr) {
        try {
            if (timeStr == null || timeStr.trim().isEmpty()) {
                return null;
            }
            
            String[] parts = timeStr.trim().split(":");
            if (parts.length != 2) {
                return null;
            }
            
            int hour = Integer.parseInt(parts[0]);
            int minute = Integer.parseInt(parts[1]);
            
            return LocalTime.of(hour, minute);
        } catch (Exception e) {
            log.warn("解析时间字符串失败: {}", timeStr, e);
            return null;
        }
    }

    /**
     * 获取状态描述信息
     *
     * @param status 考勤状态
     * @return 状态描述
     */
    public static String getStatusDescription(AttendanceStatus status) {
        if (status == null) {
            return "未知状态";
        }
        
        Map<AttendanceStatus, String> descriptions = new HashMap<>();
        descriptions.put(AttendanceStatus.NORMAL, "按时打卡，状态正常");
        descriptions.put(AttendanceStatus.LATE, "超过规定时间打卡，状态为迟到");
        descriptions.put(AttendanceStatus.EARLY_LEAVE, "提前下班打卡，状态为早退");
        descriptions.put(AttendanceStatus.ABSENT, "未进行打卡，状态为缺勤");
        descriptions.put(AttendanceStatus.LEAVE, "已申请请假，状态为请假");
        
        return descriptions.getOrDefault(status, status.getText());
    }

    /**
     * 判断是否为异常状态
     *
     * @param status 考勤状态
     * @return 是否异常
     */
    public static boolean isAbnormalStatus(AttendanceStatus status) {
        return status == AttendanceStatus.LATE || 
               status == AttendanceStatus.EARLY_LEAVE || 
               status == AttendanceStatus.ABSENT;
    }

    /**
     * 获取状态优先级（用于排序）
     *
     * @param status 考勤状态
     * @return 优先级数值，越小优先级越高
     */
    public static int getStatusPriority(AttendanceStatus status) {
        if (status == null) {
            return 999;
        }
        
        switch (status) {
            case ABSENT:
                return 1;  // 缺勤优先级最高
            case LATE:
                return 2;  // 迟到次之
            case EARLY_LEAVE:
                return 3;  // 早退再次
            case LEAVE:
                return 4;  // 请假
            case NORMAL:
                return 5;  // 正常优先级最低
            default:
                return 999;
        }
    }

    /**
     * 计算迟到时间（分钟）
     *
     * @param clockTime 实际打卡时间
     * @param workStartTime 规定开始时间
     * @return 迟到分钟数，未迟到返回0
     */
    public static long calculateLateMinutes(LocalDateTime clockTime, LocalTime workStartTime) {
        if (clockTime == null || workStartTime == null) {
            return 0;
        }
        
        LocalTime actualTime = clockTime.toLocalTime();
        if (actualTime.isAfter(workStartTime)) {
            return java.time.Duration.between(workStartTime, actualTime).toMinutes();
        }
        
        return 0;
    }

    /**
     * 计算早退时间（分钟）
     *
     * @param clockTime 实际打卡时间
     * @param workEndTime 规定结束时间
     * @return 早退分钟数，未早退返回0
     */
    public static long calculateEarlyLeaveMinutes(LocalDateTime clockTime, LocalTime workEndTime) {
        if (clockTime == null || workEndTime == null) {
            return 0;
        }
        
        LocalTime actualTime = clockTime.toLocalTime();
        if (actualTime.isBefore(workEndTime)) {
            return java.time.Duration.between(actualTime, workEndTime).toMinutes();
        }
        
        return 0;
    }
}
