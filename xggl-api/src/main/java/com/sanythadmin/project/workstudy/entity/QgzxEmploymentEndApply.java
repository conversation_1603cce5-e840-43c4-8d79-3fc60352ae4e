package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用工结束申请实体
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_EMPLOYMENT_END_APPLY")
@Entity
@Table(name = "SYT_QGZX_EMPLOYMENT_END_APPLY")
public class QgzxEmploymentEndApply extends BaseApplicationInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 学生申请ID
     */
    @Column(name = "STUDENT_APPLY_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("STUDENT_APPLY_ID")
    private String studentApplyId;

    /**
     * 用工开始日期
     */
    @Column(name = "YGKSRQ")
    @TableField("YGKSRQ")
    private LocalDate ygksrq;

    /**
     * 用工结束日期
     */
    @Column(name = "YGJSRQ")
    @TableField("YGJSRQ")
    private LocalDate ygjsrq;

    /**
     * 结束事由
     */
    @Column(name = "JSSYY", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("JSSYY")
    private String jssyy;

    /**
     * 审批状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SPZT")
    private ReviewResult spzt;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 申请时间
     */
    @Column(name = "SQSJ")
    @TableField("SQSJ")
    private LocalDateTime sqsj;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 关联的学生申请信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxStudentApply studentApply;

    /**
     * 关联的岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication jobApplication;

    /**
     * 申请人信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

    /**
     * 初始化必要字段
     */
    public void initRequiredFields() {
        // 设置申请时的角色ID
        this.setRoleId(SecurityUtil.getRoleId());
        // 设置项目名称为用工结束申请
        if (this.jobApplication != null) {
            this.setXxmc("用工结束申请-" + this.jobApplication.getJobName());
        } else {
            this.setXxmc("用工结束申请");
        }
    }
}
