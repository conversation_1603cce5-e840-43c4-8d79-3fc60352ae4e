package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationSummaryParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationImportService;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationSummaryService;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryDetailVO;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryVO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 勤工助学/报酬月度汇总控制器
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-remuneration-summary")
@RequiredArgsConstructor
public class QgzxRemunerationSummaryController extends BaseController {

    private final QgzxRemunerationSummaryService qgzxRemunerationSummaryService;
    private final QgzxRemunerationImportService qgzxRemunerationImportService;

    /**
     * 分页查询汇总数据（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/page")
    public PageResult<QgzxRemunerationSummaryVO> page(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.pageSummaryList(param);
    }

    /**
     * 获取月度汇总统计（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/summary")
    public QgzxRemunerationSummaryVO getSummary(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.getMonthlySummary(param);
    }

    /**
     * 获取月度明细数据（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/details")
    public List<QgzxRemunerationSummaryDetailVO> getDetails(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.getMonthlyDetails(param);
    }

    /**
     * 导出汇总数据（权限标识：workstudy:qgzxRemunerationSummary:export）
     */
    @OperationLog(module = "报酬汇总", comments = "导出月度汇总数据")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:export')")
    @DataPermission(alias = "ra")
    @GetMapping("/export")
    public QgzxRemunerationSummaryVO exportData(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.exportSummaryData(param);
    }

    /**
     * 按岗位类别分组统计（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/by-job-type")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByJobType(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.getSummaryByJobType(param);
    }

    /**
     * 按用人单位分组统计（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/by-employer")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByEmployer(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.getSummaryByEmployer(param);
    }

    /**
     * 按学院分组统计（权限标识：workstudy:qgzxRemunerationSummary:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:list')")
    @DataPermission(alias = "ra")
    @GetMapping("/by-college")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByCollege(@Valid QgzxRemunerationSummaryParam param) {
        return qgzxRemunerationSummaryService.getSummaryByCollege(param);
    }

    /**
     * 模板导出（权限标识：workstudy:qgzxRemunerationSummary:export）
     */
    @OperationLog(module = "报酬汇总", comments = "模板导出月度汇总数据")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:export')")
    @DataPermission(alias = "ra")
    @PostMapping("/exportByTemplate")
    public void exportByTemplate(@Valid QgzxRemunerationSummaryParam param,
                                 @Valid @RequestBody TemplateFile templateFile,
                                 HttpServletResponse response) {
        qgzxRemunerationSummaryService.exportByTemplate(param, templateFile, response);
    }

    /**
     * 历史数据导入（权限标识：workstudy:qgzxRemunerationSummary:import）
     */
    @OperationLog(module = "报酬汇总", comments = "历史数据导入")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationSummary:import')")
    @PostMapping("/importHistoryData")
    public void importHistoryData(@RequestParam("file") MultipartFile file) {
        qgzxRemunerationImportService.importHistoryData(file);
    }
}
