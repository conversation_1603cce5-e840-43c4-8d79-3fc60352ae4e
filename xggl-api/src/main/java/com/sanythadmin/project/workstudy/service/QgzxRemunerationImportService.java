package com.sanythadmin.project.workstudy.service;

import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.project.workstudy.dto.QgzxRemunerationImportDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 勤工助学报酬历史数据导入服务
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface QgzxRemunerationImportService {

    /**
     * 读取Excel文件数据
     *
     * @param file Excel文件
     * @return 导入数据列表
     */
    List<QgzxRemunerationImportDTO> readExcelData(MultipartFile file);

    /**
     * 数据验证主方法
     *
     * @param importData 导入数据
     * @param errors 错误信息收集
     */
    void validateImportData(List<QgzxRemunerationImportDTO> importData, List<ExcelImportError> errors);

    /**
     * 并发数据验证
     *
     * @param importData 导入数据
     * @param errors 错误信息收集
     * @param executor 线程池执行器
     */
    void validateImportDataConcurrently(List<QgzxRemunerationImportDTO> importData, 
                                       List<ExcelImportError> errors, 
                                       Executor executor);

    /**
     * 单条数据验证
     *
     * @param dto 导入数据
     * @param executor 线程池执行器
     * @return 验证结果
     */
    CompletableFuture<Void> validateSingleData(QgzxRemunerationImportDTO dto, Executor executor);

    /**
     * 学号有效性验证
     *
     * @param xgh 学号
     * @return 是否有效
     */
    boolean checkStudentExists(String xgh);

    /**
     * 岗位信息验证
     *
     * @param jobName 岗位名称
     * @param employerName 用人单位名称
     * @param jobTypeName 岗位类别名称
     * @return 岗位ID，如果不存在返回null
     */
    String checkJobExists(String jobName, String employerName, String jobTypeName);

    /**
     * 工时合理性验证
     *
     * @param xgh 学号
     * @param jobId 岗位ID
     * @param workHours 工时
     * @param sbny 申报年月
     * @return 是否合理
     */
    boolean checkWorkHoursValid(String xgh, String jobId, java.math.BigDecimal workHours, String sbny);

    /**
     * 重复数据检查
     *
     * @param xgh 学号
     * @param jobId 岗位ID
     * @param sbny 申报年月
     * @return 是否重复
     */
    boolean checkDuplicateData(String xgh, String jobId, String sbny);

    /**
     * 报酬金额计算验证
     *
     * @param workHours 工时
     * @param hourlyRate 时薪
     * @param remunerationAmount 报酬金额
     * @return 是否正确
     */
    boolean checkAmountCalculation(java.math.BigDecimal workHours, 
                                  java.math.BigDecimal hourlyRate, 
                                  java.math.BigDecimal remunerationAmount);

    /**
     * 获取学生申请ID
     *
     * @param xgh 学号
     * @param jobId 岗位ID
     * @return 学生申请ID，如果不存在返回null
     */
    String getStudentApplyId(String xgh, String jobId);

    /**
     * 获取用人单位ID
     *
     * @param employerName 用人单位名称
     * @return 用人单位ID，如果不存在返回null
     */
    String getEmployerId(String employerName);

    /**
     * 获取岗位类别ID
     *
     * @param jobTypeName 岗位类别名称
     * @return 岗位类别ID，如果不存在返回null
     */
    String getJobTypeId(String jobTypeName);

    /**
     * 获取学院ID
     *
     * @param xgh 学号
     * @return 学院ID，如果不存在返回null
     */
    String getCollegeId(String xgh);

    /**
     * 批量数据导入
     *
     * @param importData 已验证的导入数据
     */
    void batchImportData(List<QgzxRemunerationImportDTO> importData);

    /**
     * 历史数据导入主方法
     *
     * @param file Excel文件
     */
    void importHistoryData(MultipartFile file);
}
