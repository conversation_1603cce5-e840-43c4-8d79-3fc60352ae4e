package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 勤工助学岗位地址
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_JOB_APPLICATION_ADDRESS")
@Entity
@Table(name = "SYT_QGZX_JOB_APPLICATION_ADDRESS")
public class QgzxJobApplicationAddress implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 预设地址ID（关联签到地址库）
     */
    @Column(name = "ADDRESS_ID")
    @TableField("ADDRESS_ID")
    private String addressId;

    /**
     * 岗位申报ID
     */
    @Column(name = "JOB_ID")
    @TableField("JOB_ID")
    private String jobId;

    /**
     * 地址名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 地址描述
     */
    @Column(name = "BZ", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("BZ")
    private String bz;

    /**
     * 地址类型（圆形、多边形）
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 范围/半径（米）
     */
    @Column(name = "FWBJ")
    @TableField("FWBJ")
    private Integer fwbj;

    /**
     * 经纬度坐标
     * 圆形：格式为 "经度,纬度"，如 "116.397128,39.916527"
     * 多边形：格式为 "经度1,纬度1;经度2,纬度2;..."
     */
    @Column(name = "JWDZB", columnDefinition = ColumnType.VARCHAR2_4000)
    @TableField("JWDZB")
    private String jwdzb;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 是否来源地址库
     */
    @Column(name = "IS_FROM_MAP", columnDefinition = ColumnType.NUMBER_1)
    @TableField("IS_FROM_MAP")
    private JudgeMark isFromMap;

    /**
     * 是否启用
     */
    @Column(name = "STATUS", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("STATUS")
    private JudgeMark status;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private java.time.LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private java.time.LocalDateTime updateTime;
}
