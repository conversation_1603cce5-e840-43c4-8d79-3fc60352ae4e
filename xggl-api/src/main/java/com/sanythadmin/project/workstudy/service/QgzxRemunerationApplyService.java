package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationApplyParam;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 勤工助学报酬申报服务
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QgzxRemunerationApplyService extends IService<QgzxRemunerationApply> {

    /**
     * 分页查询报酬申报列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxRemunerationApply> pageList(QgzxRemunerationApplyParam param);

    /**
     * 用人单位分页查询本单位的报酬申报
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxRemunerationApply> pageByEmployer(QgzxRemunerationApplyParam param);

    /**
     * 查询报酬申报列表
     *
     * @param param 查询参数
     * @return 申报列表
     */
    List<QgzxRemunerationApply> listApply(QgzxRemunerationApplyParam param);

    /**
     * 根据ID获取报酬申报详情（包含明细）
     *
     * @param id 申报ID
     * @return 申报详情
     */
    QgzxRemunerationApply getDetail(String id);

    /**
     * 保存报酬申报（包含明细）
     *
     * @param remunerationApply 报酬申报信息
     */
    void saveWithDetails(QgzxRemunerationApply remunerationApply);

    /**
     * 更新报酬申报（包含明细）
     *
     * @param remunerationApply 报酬申报信息
     */
    void updateWithDetails(QgzxRemunerationApply remunerationApply);

    /**
     * 删除报酬申报（包含明细）
     *
     * @param ids 申报ID数组
     */
    void deleteWithDetails(String... ids);

    /**
     * 自动生成报酬申报（基于考勤记录）
     *
     * @param jobId     岗位ID
     * @param sbny      申报年月
     * @return 生成的申报信息
     */
    QgzxRemunerationApply generateFromAttendance(String jobId, String sbny);

    /**
     * 自动生成并保存报酬申报（基于考勤记录）
     * 根据岗位的bcsfsh字段判断：
     * - bcsfsh为"是"：需要走工作流申请审核
     * - bcsfsh为"否"：直接添加报酬，无需审核
     *
     * @param jobId     岗位ID
     * @param sbny      申报年月
     * @return 保存后的申报信息
     */
    QgzxRemunerationApply generateAndSaveFromAttendance(String jobId, String sbny);

    /**
     * 检查是否可以编辑
     *
     * @param id 申报ID
     */
    void checkIsEdit(String id);

    /**
     * 根据岗位ID和申报年月查询申报记录
     *
     * @param jobId 岗位ID
     * @param sbny  申报年月
     * @return 申报记录
     */
    QgzxRemunerationApply getByJobIdAndSbny(String jobId, String sbny);

    /**
     * 用人单位申报报酬
     * 根据岗位的bcsfsh字段判断：
     * - bcsfsh为"是"：需要走工作流申请审核
     * - bcsfsh为"否"：直接添加报酬，无需审核
     *
     * @param remunerationApply 报酬申报信息
     */
    void applyByEmployer(QgzxRemunerationApply remunerationApply);

    /**
     * 审核操作
     *
     * @param record 审核记录
     * @param remunerationApply 报酬申报信息
     * @param executor 执行器
     * @return 异步结果
     */
    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxRemunerationApply remunerationApply, Executor executor);

    /**
     * 删除报酬申报
     *
     * @param ids 申报ID数组
     */
    void delete(String... ids);

    /**
     * 审核人分页查询报酬申报列表
     *
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @param approvalNodeParam 审核节点参数
     * @return 分页结果
     */
    PageResult<QgzxRemunerationApply> pageApprovalList(QgzxRemunerationApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    /**
     * 审核人查询报酬申报列表
     *
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @param approvalNodeParam 审核节点参数
     * @return 申报列表
     */
    List<QgzxRemunerationApply> listApprovalList(QgzxRemunerationApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    /**
     * 去重查询
     *
     * @param column 字段
     * @return 去重结果
     */
    <T> List<Map<String, Object>> distinct(SFunction<QgzxRemunerationApply, T> column);
}
