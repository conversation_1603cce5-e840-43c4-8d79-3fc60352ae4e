package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.ReviewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 勤工助学报酬申报查询参数
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxRemunerationApplyParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 岗位ID
     */
    @QueryField(type = QueryType.EQ)
    private String jobId;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 申报年月（格式：YYYY-MM）
     */
    private String sbny;

    /**
     * 申报开始日期
     */
    private LocalDate startDate;

    /**
     * 申报结束日期
     */
    private LocalDate endDate;

    /**
     * 审批状态
     */
    @QueryField(type = QueryType.EQ)
    private ReviewResult spzt;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 申请时间
     */
    private LocalDateTime sqsj;

    /**
     * 申报说明
     */
    private String sbsm;

    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 总报酬金额
     */
    private BigDecimal totalAmount;

    /**
     * 岗位名称（用于查询）
     */
    @QueryField(ignore = true)
    private String jobName;

    /**
     * 用人单位ID（用于查询）
     */
    @QueryField(ignore = true)
    private String eid;

    /**
     * 学工号（用于查询）
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "CREATE_TIME desc";
    }
}
