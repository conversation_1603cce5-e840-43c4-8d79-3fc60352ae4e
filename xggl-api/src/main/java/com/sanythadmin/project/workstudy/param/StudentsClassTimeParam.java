package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学生空余时间查询参数
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StudentsClassTimeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 学工号
     */
    private String xgh;

    /**
     * 工作日
     */
    private Integer weekDay;

    /**
     * 关联课时表ID
     */
    private String ksbId;
}
