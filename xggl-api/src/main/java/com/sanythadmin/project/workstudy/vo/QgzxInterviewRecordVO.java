package com.sanythadmin.project.workstudy.vo;

import com.sanythadmin.project.workstudy.entity.QgzxInterviewRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 面试记录VO
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QgzxInterviewRecordVO extends QgzxInterviewRecord {

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 用人单位名称
     */
    private String employerName;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 学生联系方式
     */
    private String studentContact;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 特长优势
     */
    private String advantages;
}
