package com.sanythadmin.project.workstudy.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 勤工助学报酬月度汇总结果VO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxRemunerationSummaryVO {

    /**
     * 申报年月
     */
    private String sbny;

    /**
     * 学生人数
     */
    private Integer studentCount;

    /**
     * 总工时
     */
    private BigDecimal totalHours;

    /**
     * 总报酬金额
     */
    private BigDecimal totalAmount;

    /**
     * 岗位数量
     */
    private Integer jobCount;

    /**
     * 用人单位数量
     */
    private Integer employerCount;

    /**
     * 平均时薪
     */
    private BigDecimal averageHourlyRate;

    /**
     * 按岗位类别分组的明细统计
     */
    private List<QgzxRemunerationSummaryDetailVO> jobTypeDetails;

    /**
     * 按用人单位分组的明细统计
     */
    private List<QgzxRemunerationSummaryDetailVO> employerDetails;

    /**
     * 按学院分组的明细统计
     */
    private List<QgzxRemunerationSummaryDetailVO> collegeDetails;
}
