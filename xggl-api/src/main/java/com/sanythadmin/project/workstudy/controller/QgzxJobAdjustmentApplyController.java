package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobAdjustmentApply;
import com.sanythadmin.project.workstudy.param.QgzxJobAdjustmentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxJobAdjustmentApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/岗位调剂申请控制器
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-job-adjustment-apply")
@RequiredArgsConstructor
public class QgzxJobAdjustmentApplyController extends BaseController {

    private final QgzxJobAdjustmentApplyService qgzxJobAdjustmentApplyService;

    /**
     * 分页查询调剂申请列表（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping("/page")
    public PageResult<QgzxJobAdjustmentApply> page(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        return qgzxJobAdjustmentApplyService.pageList(param, userInfoParam);
    }

    /**
     * 查询调剂申请列表（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping
    public List<QgzxJobAdjustmentApply> list(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        return qgzxJobAdjustmentApplyService.listApply(param, userInfoParam);
    }

    /**
     * 根据ID查询调剂申请详情（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping("/{id}")
    public QgzxJobAdjustmentApply get(@PathVariable("id") String id) {
        return qgzxJobAdjustmentApplyService.getDetail(id);
    }

    /**
     * 用人单位申请岗位调剂（权限标识：workstudy:qgzxJobAdjustmentApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:operation')")
    @OperationLog(module = "岗位调剂", comments = "用人单位申请岗位调剂")
    @PostMapping("/operation")
    public void operation(@RequestBody QgzxJobAdjustmentApply adjustmentApply) {
        qgzxJobAdjustmentApplyService.applyAdjustment(adjustmentApply.getStudentApplyId(),
                adjustmentApply.getToJobId(), adjustmentApply.getAdjustReason());
    }

    /**
     * 接收单位确认调剂申请（权限标识：workstudy:qgzxJobAdjustmentApply:confirm）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:confirm')")
    @OperationLog(module = "岗位调剂", comments = "接收单位确认调剂申请")
    @PostMapping("/confirmByReceiver")
    public void confirmByReceiver(@RequestBody QgzxJobAdjustmentApply adjustmentApply) {
        qgzxJobAdjustmentApplyService.confirmByReceiver(adjustmentApply.getId(),
                adjustmentApply.getReceiverResult().name(), adjustmentApply.getReceiverComment());
    }

    /**
     * 学生确认调剂申请（权限标识：workstudy:qgzxJobAdjustmentApply:confirm）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:confirm')")
    @OperationLog(module = "岗位调剂", comments = "学生确认调剂申请")
    @PostMapping("/confirmByStudent")
    public void confirmByStudent(@RequestBody QgzxJobAdjustmentApply adjustmentApply) {
        qgzxJobAdjustmentApplyService.confirmByStudent(adjustmentApply.getId(),
                adjustmentApply.getStudentResult().name(), adjustmentApply.getStudentComment());
    }

    /**
     * 查询用人单位待确认的调剂申请（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping("/pagePendingForEmployer")
    public PageResult<QgzxJobAdjustmentApply> pagePendingForEmployer(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        return qgzxJobAdjustmentApplyService.pagePendingForEmployer(param, userInfoParam);
    }

    /**
     * 查询学生待确认的调剂申请（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping("/pagePendingForStudent")
    public PageResult<QgzxJobAdjustmentApply> pagePendingForStudent(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        return qgzxJobAdjustmentApplyService.pagePendingForStudent(param, userInfoParam);
    }

    /**
     * 根据学生申请ID获取调剂申请（权限标识：workstudy:qgzxJobAdjustmentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:list')")
    @GetMapping("/getByStudentApplyId/{studentApplyId}")
    public QgzxJobAdjustmentApply getByStudentApplyId(@PathVariable("studentApplyId") String studentApplyId) {
        return qgzxJobAdjustmentApplyService.getByStudentApplyId(studentApplyId);
    }

    /**
     * 检查是否可以申请调剂（权限标识：workstudy:qgzxJobAdjustmentApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobAdjustmentApply:operation')")
    @GetMapping("/checkCanApply")
    public String checkCanApplyAdjustment(@RequestParam String studentApplyId, @RequestParam String toJobId) {
        return qgzxJobAdjustmentApplyService.checkCanApplyAdjustment(studentApplyId, toJobId);
    }
}
