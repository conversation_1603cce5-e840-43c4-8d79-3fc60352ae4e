package com.sanythadmin.project.workstudy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.SysAccountMapper;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.mapper.QgzxEmployerMapper;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用人单位Service实现
 *
 * <AUTHOR>
 * @since 2025-07-14 13:48:13
 */
@Service
public class QgzxEmployerServiceImpl extends ServiceImpl<QgzxEmployerMapper, QgzxEmployer> implements QgzxEmployerService {

    @Resource
    private QgzxEmployerMapper qgzxEmployerMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysAccountMapper sysAccountMapper;
    @Resource
    private SysAccountRoleMapper accountRoleMapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private SysParamService sysParamService;

    @Transactional
    @Override
    public QgzxEmployer saveQgzxEmployer(QgzxEmployerParam param) {
        String xgh = param.getXgh();
        if (StringUtils.isEmpty(param.getId())) {
            checkEmployer(param);
        }
        UserInfo info = userInfoService.get(xgh);
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_BDW));
        //系统内选人
        if (info != null) {
            //userInfo增加roleId
            info.setRoleId(info.getRoleId() + "," + sysRole.getId());
            userInfoService.saveOrUpdate(info);
            //联动accountRole
            SysAccount account = sysAccountMapper.selectOne(new LambdaQueryWrapper<SysAccount>().eq(SysAccount::getUsername, info.getXgh()));
            SysAccountRole accountRole = new SysAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(sysRole.getId());
            accountRoleMapper.insert(accountRole);
            BeanUtil.copyProperties(info, param);
        } else {
            info = BeanUtil.copyProperties(param, UserInfo.class);
            //createAccountInfo
            String paramValue = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
            info.setRoleId(sysRole.getId());
            info.setUserType(UserType.EMPLOYER);
            userInfoService.createUserAndAccountInfo(info, true, paramValue);
        }
        QgzxEmployer qgzxEmployer = BeanUtil.copyProperties(param, QgzxEmployer.class);
        qgzxEmployer.setCreateTime(LocalDateTime.now());
        saveOrUpdate(qgzxEmployer);
        qgzxEmployer.setUserInfo(info);
        return qgzxEmployer;
    }

    @Transactional
    @Override
    public void removeQgzxEmployer(List<String> ids) {
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_BDW));
        for (String id : ids) {
            QgzxEmployer qgzxEmployer = getById(id);
            UserInfo userInfo = userInfoMapper.selectOne(new LambdaQueryWrapper<UserInfo>().eq(UserInfo::getXgh, qgzxEmployer.getXgh()));
            userInfo.setRoleId(userInfo.getRoleId().replace("," + sysRole.getId(), "").replace(sysRole.getId(), ""));
            userInfoService.saveOrUpdate(userInfo);
            List<SysAccount> accounts = sysAccountMapper.selectList(new LambdaQueryWrapper<SysAccount>().eq(SysAccount::getUsername, qgzxEmployer.getXgh()));
            List<String> accountIds = accounts.stream().map(SysAccount::getId).collect(Collectors.toList());
            accountRoleMapper.delete(new LambdaQueryWrapper<SysAccountRole>().eq(SysAccountRole::getRoleId, sysRole.getId())
                    .in(SysAccountRole::getAccountId, accountIds));
            if (UserType.EMPLOYER.getText().equals(userInfo.getUserType().getText())) {
                userInfoMapper.delete(new LambdaQueryWrapper<UserInfo>().eq(UserInfo::getXgh, qgzxEmployer.getXgh()).eq(UserInfo::getUserType, UserType.EMPLOYER));
                sysAccountMapper.delete(new LambdaQueryWrapper<SysAccount>().in(SysAccount::getId, accountIds));
            }
        }
        qgzxEmployerMapper.deleteBatchIds(ids);
    }

    @Override
    public QgzxEmployer getQgzxEmployer(String id) {
        QgzxEmployer qgzxEmployer = getById(id);
        if (qgzxEmployer == null) {
            AssertUtil.throwMessage("用人单位不存在");
        }
        UserInfo userInfo = userInfoService.get(qgzxEmployer.getXgh());
        qgzxEmployer.setUserInfo(userInfo);
        return qgzxEmployer;
    }

    @Override
    public PageResult<QgzxEmployer> getQgzxEmployerPage(QgzxEmployerParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<QgzxEmployer, QgzxEmployerParam> wrapper =
                UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param, QgzxEmployer::getXgh, userInfoParam, null);
        Map<Class<?>, String> tableAliasMap = wrapper.getTableAliasMap();
        wrapper.selectAssociation(tableAliasMap.get(UserInfo.class), UserInfo.class, QgzxEmployer::getUserInfo);
        wrapper.orderByDesc("CREATE_TIME");
        Page<QgzxEmployer> page = wrapper.getPage();
        IPage<QgzxEmployer> iPage = qgzxEmployerMapper.selectJoinPageWithPermission(page, QgzxEmployer.class, wrapper, new MyMPQueryParams(QgzxEmployer.class));
        UserInfoUtil.codeTextSet(iPage.getRecords());
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    private void checkEmployer(QgzxEmployerParam param) {
        List<QgzxEmployer> employers = list(new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, param.getXgh()));
        if (!CollectionUtils.isEmpty(employers)) AssertUtil.throwMessage("（负责人账号）已存在");
    }
}
