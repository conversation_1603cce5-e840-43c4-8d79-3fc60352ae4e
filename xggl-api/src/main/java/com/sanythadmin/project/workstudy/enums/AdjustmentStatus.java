package com.sanythadmin.project.workstudy.enums;

import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

/**
 * 岗位调剂状态枚举
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public enum AdjustmentStatus implements TextBaseEnum {
    /**
     * 无调剂
     */
    WuTiaoJi("无调剂"),
    
    /**
     * 待接收单位确认
     */
    DaiJieShouDanWeiQueRen("待接收单位确认"),
    
    /**
     * 待学生确认
     */
    DaiXueShengQueRen("待学生确认"),
    
    /**
     * 调剂完成
     */
    TiaoJiWanCheng("调剂完成"),
    
    /**
     * 调剂被拒绝
     */
    TiaoJiBeiJuJue("调剂被拒绝");

    private final String text;

    AdjustmentStatus(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }
}
