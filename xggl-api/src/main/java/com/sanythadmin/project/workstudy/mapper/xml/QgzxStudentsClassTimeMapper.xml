<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxStudentsClassTimeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, WEEKDAY, KSSJ, JSSJ, DJJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.weekday != null">
                        AND a.WEEKDAY LIKE concat(concat('%',#{param.weekday), '%')
                    </if>
                    <if test="param.ksbId != null">
                        AND a.KSBID = #{param.ksbId}
                    </if>
    </sql>
</mapper>
