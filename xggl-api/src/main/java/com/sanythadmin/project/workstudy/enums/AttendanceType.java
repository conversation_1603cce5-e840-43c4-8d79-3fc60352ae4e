package com.sanythadmin.project.workstudy.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.Getter;

/**
 * 考勤类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
public enum AttendanceType implements TextBaseEnum {
    CLOCK_IN("上班"),
    CLOCK_OUT("下班");

    @JsonValue
    private final String text;

    AttendanceType(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }
}
