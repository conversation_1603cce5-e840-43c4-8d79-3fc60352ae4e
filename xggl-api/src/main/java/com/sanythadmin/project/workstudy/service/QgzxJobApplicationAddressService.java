package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;

import java.util.List;
import java.util.Map;

/**
 * 勤工助学岗位地址Service
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface QgzxJobApplicationAddressService extends IService<QgzxJobApplicationAddress> {

    /**
     * 根据岗位ID查询地址列表
     *
     * @param jobId 岗位ID
     * @return 地址列表
     */
    List<QgzxJobApplicationAddress> getByJobId(String jobId);

    /**
     * 根据岗位ID查询启用的地址列表
     *
     * @param jobId 岗位ID
     * @return 启用的地址列表
     */
    List<QgzxJobApplicationAddress> getEnabledByJobId(String jobId);

    /**
     * 保存岗位地址列表
     *
     * @param jobId 岗位ID
     * @param addresses 地址列表
     */
    void saveJobAddresses(String jobId, List<QgzxJobApplicationAddress> addresses);

    /**
     * 删除岗位的所有地址
     *
     * @param jobId 岗位ID
     */
    void deleteByJobId(String jobId);

    /**
     * 批量查询多个岗位的地址信息
     *
     * @param jobIds 岗位ID列表
     * @return 岗位ID与地址列表的映射
     */
    Map<String, List<QgzxJobApplicationAddress>> getByJobIds(List<String> jobIds);

}
