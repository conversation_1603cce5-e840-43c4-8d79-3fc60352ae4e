package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationDetail;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.mapper.QgzxJobApplicationMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxRemunerationApplyMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxRemunerationDetailMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxStudentApplyMapper;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationDetailParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationDetailService;
import com.sanythadmin.project.workstudy.service.QgzxWorkHoursCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 勤工助学报酬明细服务实现
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxRemunerationDetailServiceImpl extends ServiceImpl<QgzxRemunerationDetailMapper, QgzxRemunerationDetail> implements QgzxRemunerationDetailService {

    private final QgzxRemunerationDetailMapper remunerationDetailMapper;
    private final QgzxRemunerationApplyMapper remunerationApplyMapper;
    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;
    private final UserInfoService userInfoService;
    private final QgzxWorkHoursCalculationService workHoursCalculationService;

    @Override
    public PageResult<QgzxRemunerationDetail> pageList(QgzxRemunerationDetailParam param) {
        MyMPJLambdaWrapper<QgzxRemunerationDetail, QgzxRemunerationDetailParam> wrapper =
                new MyMPJLambdaWrapper<>(param);
        
        wrapper.selectAll(QgzxRemunerationDetail.class)
                .selectAssociation("sa", QgzxStudentApply.class, QgzxRemunerationDetail::getStudentApply)
                .selectAssociation("u", UserInfo.class, QgzxRemunerationDetail::getUserInfo)
                .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxRemunerationDetail::getStudentApplyId)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxRemunerationDetail::getXgh);
        
        Page<QgzxRemunerationDetail> page = page(wrapper.getPage(), wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxRemunerationDetail> listDetail(QgzxRemunerationDetailParam param) {
        MyMPJLambdaWrapper<QgzxRemunerationDetail, QgzxRemunerationDetailParam> wrapper =
                new MyMPJLambdaWrapper<>(param);
        
        wrapper.selectAll(QgzxRemunerationDetail.class)
                .selectAssociation("sa", QgzxStudentApply.class, QgzxRemunerationDetail::getStudentApply)
                .selectAssociation("u", UserInfo.class, QgzxRemunerationDetail::getUserInfo)
                .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxRemunerationDetail::getStudentApplyId)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxRemunerationDetail::getXgh);
        
        return list(wrapper);
    }

    @Override
    public QgzxRemunerationDetail getDetail(String id) {
        QgzxRemunerationDetail detail = getById(id);
        AssertUtil.isTrue(detail != null, "报酬明细不存在");
        if (detail.getStudentApplyId() != null) {
            QgzxStudentApply studentApply = studentApplyMapper.selectById(detail.getStudentApplyId());
            detail.setStudentApply(studentApply);
        }
        if (detail.getXgh() != null) {
            UserInfo userInfo = userInfoService.get(detail.getXgh());
            detail.setUserInfo(userInfo);
        }
        
        return detail;
    }

    @Override
    public List<QgzxRemunerationDetail> getDetailsByApplyId(String remunerationApplyId) {
        return remunerationDetailMapper.selectByRemunerationApplyId(remunerationApplyId);
    }

    @Transactional
    @Override
    public void addDetail(QgzxRemunerationDetail detail) {
        // 检查申报是否可以编辑
        checkIsEdit(detail.getRemunerationApplyId());
        
        // 设置基本信息
        detail.setCreateTime(LocalDateTime.now());
        
        // 计算报酬金额
        calculateRemunerationAmount(detail);
        
        // 验证数据
        validateDetail(detail);
        
        // 保存明细
        save(detail);
        
        // 重新计算申报总计
        recalculateApplyTotals(detail.getRemunerationApplyId());
        
        log.info("新增报酬明细成功: 明细ID={}, 申报ID={}, 学号={}", 
                detail.getId(), detail.getRemunerationApplyId(), detail.getXgh());
    }

    @Transactional
    @Override
    public void updateDetail(QgzxRemunerationDetail detail) {
        // 检查明细是否存在
        QgzxRemunerationDetail existingDetail = getById(detail.getId());
        AssertUtil.isTrue(existingDetail != null, "报酬明细不存在");
        
        // 检查申报是否可以编辑
        checkIsEdit(existingDetail.getRemunerationApplyId());
        
        // 设置更新时间
        detail.setUpdateTime(LocalDateTime.now());
        
        // 计算报酬金额
        calculateRemunerationAmount(detail);
        
        // 验证数据
        validateDetail(detail);
        
        // 更新明细
        updateById(detail);
        
        // 重新计算申报总计
        recalculateApplyTotals(existingDetail.getRemunerationApplyId());
        
        log.info("更新报酬明细成功: 明细ID={}, 申报ID={}, 学号={}", 
                detail.getId(), existingDetail.getRemunerationApplyId(), detail.getXgh());
    }

    @Transactional
    @Override
    public void deleteDetail(String... ids) {
        for (String id : ids) {
            QgzxRemunerationDetail detail = getById(id);
            AssertUtil.isTrue(detail != null, "报酬明细不存在：" + id);
            
            // 检查申报是否可以编辑
            checkIsEdit(detail.getRemunerationApplyId());
            
            // 删除明细
            removeById(id);
            
            // 重新计算申报总计
            recalculateApplyTotals(detail.getRemunerationApplyId());
            
            log.info("删除报酬明细成功: 明细ID={}, 申报ID={}, 学号={}", 
                    id, detail.getRemunerationApplyId(), detail.getXgh());
        }
    }

    @Override
    public void checkIsEdit(String remunerationApplyId) {
        QgzxRemunerationApply remunerationApply = remunerationApplyMapper.selectById(remunerationApplyId);
        AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在");
        AssertUtil.isTrue(ReviewResult.DaiShenPi.equals(remunerationApply.getSpzt()), 
                "只有待审批状态的申报才能编辑明细");
    }

    @Override
    public void calculateRemunerationAmount(QgzxRemunerationDetail detail) {
        if (detail.getWorkHours() != null && detail.getHourlyRate() != null) {
            BigDecimal amount = detail.getWorkHours().multiply(detail.getHourlyRate());
            detail.setRemunerationAmount(amount);
        }
    }

    @Transactional
    @Override
    public void recalculateApplyTotals(String remunerationApplyId) {
        // 查询所有明细
        List<QgzxRemunerationDetail> details = getDetailsByApplyId(remunerationApplyId);
        
        // 计算总工时和总报酬
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (QgzxRemunerationDetail detail : details) {
            if (detail.getWorkHours() != null) {
                totalHours = totalHours.add(detail.getWorkHours());
            }
            if (detail.getRemunerationAmount() != null) {
                totalAmount = totalAmount.add(detail.getRemunerationAmount());
            }
        }
        
        // 更新申报记录
        QgzxRemunerationApply remunerationApply = remunerationApplyMapper.selectById(remunerationApplyId);
        if (remunerationApply != null) {
            remunerationApply.setTotalHours(totalHours);
            remunerationApply.setTotalAmount(totalAmount);
            remunerationApply.setUpdateTime(LocalDateTime.now());
            remunerationApplyMapper.updateById(remunerationApply);
        }
    }

    /**
     * 验证明细数据
     */
    private void validateDetail(QgzxRemunerationDetail detail) {
        AssertUtil.isTrue(detail.getRemunerationApplyId() != null, "申报ID不能为空");
        AssertUtil.isTrue(detail.getXgh() != null, "学号不能为空");
        AssertUtil.isTrue(detail.getWorkHours() != null && detail.getWorkHours().compareTo(BigDecimal.ZERO) > 0, 
                "工时必须大于0");
        AssertUtil.isTrue(detail.getHourlyRate() != null && detail.getHourlyRate().compareTo(BigDecimal.ZERO) > 0, 
                "时薪必须大于0");
        
        // 获取申报信息
        QgzxRemunerationApply remunerationApply = remunerationApplyMapper.selectById(detail.getRemunerationApplyId());
        AssertUtil.isTrue(remunerationApply != null, "报酬申报不存在");
        
        // 验证学生工时限制
        boolean validStudentHours = workHoursCalculationService.validateStudentWorkHours(
                detail.getXgh(), remunerationApply.getJobId(), detail.getWorkHours(), remunerationApply.getSbny());
        AssertUtil.isTrue(validStudentHours, "学生 " + detail.getXgh() + " 的工时超出岗位限制");
    }
}
