package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 学生岗位申请Service
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
public interface QgzxStudentApplyService extends IService<QgzxStudentApply> {

    PageResult<QgzxJobApplicationVO> pageByApplicantList(QgzxJobApplicationParam param);
    List<QgzxJobApplicationVO> listApplicantList(QgzxJobApplicationParam param);
    public String checkApplyTime(QgzxJobApplication jobApplication);
    public String checkLimit(String xgh, QgzxJobApplication jobApplication);
    String checkIsEdit(String id);
    void editByApplicant(QgzxStudentApply studentApply);
    void delete(String... id);
    void deleteByApplicant(String... id);
    PageResult<QgzxStudentApply> pageByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam);
    List<QgzxStudentApply> listByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam);
    PageResult<QgzxStudentApply> pageByEmployer(QgzxStudentApplyParam param);
    QgzxStudentApply getDetail(String id);

    PageResult<QgzxStudentApply> pageApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    List<QgzxStudentApply> listApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record,QgzxStudentApply studentApply, Executor executor);

    /**
     * 查询需要面试的学生申请列表（用于用人单位安排面试）
     * @param param 查询参数
     * @return 学生申请列表
     */
    PageResult<QgzxStudentApply> pageForInterview(QgzxStudentApplyParam param);

    /**
     * 查询可调剂的岗位列表（用于用人单位申请调剂时选择目标岗位）
     * @param studentApplyId 学生申请ID
     * @return 可调剂的岗位列表
     */
    List<QgzxJobApplication> listAdjustableJobs(String studentApplyId);
}