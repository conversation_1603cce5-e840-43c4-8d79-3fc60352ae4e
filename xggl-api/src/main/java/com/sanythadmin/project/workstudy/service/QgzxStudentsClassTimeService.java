package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentsClassTime;

import java.time.LocalDate;
import java.util.List;

/**
 * 学生空余时间Service
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
public interface QgzxStudentsClassTimeService extends IService<QgzxStudentsClassTime> {

    void saveQgzxStudentsClassTimeList(List<QgzxStudentsClassTime> studentsClassTimeList);
    String check(UserInfo userInfo, QgzxJobApplication qgzxJobApplication);
    /**
     * 检查学生在指定日期是否与岗位时间冲突
     *
     * @param userInfo 用户信息
     * @param qgzxJobApplication 岗位信息
     * @param checkDate 检查日期，如果为null则不考虑周次
     * @return 冲突信息，null表示无冲突
     */
    String check(UserInfo userInfo, QgzxJobApplication qgzxJobApplication, LocalDate checkDate);
}
