package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;
import com.sanythadmin.project.workstudy.mapper.QgzxJobApplicationAddressMapper;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationAddressService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 勤工助学岗位地址Service实现
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QgzxJobApplicationAddressServiceImpl extends ServiceImpl<QgzxJobApplicationAddressMapper, QgzxJobApplicationAddress> implements QgzxJobApplicationAddressService {

    private final QgzxJobApplicationAddressMapper qgzxJobApplicationAddressMapper;

    @Override
    public List<QgzxJobApplicationAddress> getByJobId(String jobId) {
        return qgzxJobApplicationAddressMapper.selectByJobId(jobId);
    }

    @Override
    public List<QgzxJobApplicationAddress> getEnabledByJobId(String jobId) {
        return qgzxJobApplicationAddressMapper.selectEnabledByJobId(jobId);
    }

    @Override
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    public void saveJobAddresses(String jobId, List<QgzxJobApplicationAddress> addresses) {
        addresses.forEach(jobAddress->{
            jobAddress.setAddressId(jobAddress.getId());
            jobAddress.setId(null);
            jobAddress.setJobId(jobId);
        });
        qgzxJobApplicationAddressMapper.deleteByJobId(jobId);
        saveOrUpdateBatch(addresses);


    }

    @Override
    @Transactional
    public void deleteByJobId(String jobId) {
        qgzxJobApplicationAddressMapper.deleteByJobId(jobId);
    }

    @Override
    public Map<String, List<QgzxJobApplicationAddress>> getByJobIds(List<String> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return new HashMap<>();
        }

        // 批量查询所有地址
        List<QgzxJobApplicationAddress> allAddresses = qgzxJobApplicationAddressMapper.selectList(
            new LambdaQueryWrapper<QgzxJobApplicationAddress>()
                .in(QgzxJobApplicationAddress::getJobId, jobIds)
                .orderByAsc(QgzxJobApplicationAddress::getSort)
                .orderByDesc(QgzxJobApplicationAddress::getCreateTime)
        );

        // 按岗位ID分组
        return allAddresses.stream()
            .collect(Collectors.groupingBy(QgzxJobApplicationAddress::getJobId));
    }

}
