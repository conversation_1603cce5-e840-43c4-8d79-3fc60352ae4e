package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.workstudy.enums.InterviewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 面试记录查询参数
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxInterviewRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学生申请ID
     */
    @QueryField(type = QueryType.EQ)
    private String studentApplyId;

    /**
     * 岗位ID（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String jobId;

    /**
     * 学号（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String xgh;

    /**
     * 用人单位ID（用于查询条件，通过关联查询）
     */
    @QueryField(ignore = true)
    private String employerId;

    /**
     * 面试时间开始
     */
    @QueryField(type = QueryType.GE)
    private LocalDateTime interviewTimeStart;

    /**
     * 面试时间结束
     */
    @QueryField(type = QueryType.LE)
    private LocalDateTime interviewTimeEnd;

    /**
     * 面试地点
     */
    @QueryField(type = QueryType.LIKE)
    private String interviewLocation;

    /**
     * 面试官姓名
     */
    @QueryField(type = QueryType.LIKE)
    private String interviewerName;

    /**
     * 面试结果
     */
    @QueryField(type = QueryType.EQ)
    private InterviewResult interviewResult;

    /**
     * 最低评分
     */
    @QueryField(type = QueryType.GE)
    private Double minScore;

    /**
     * 最高评分
     */
    @QueryField(type = QueryType.LE)
    private Double maxScore;

    /**
     * 学年学期
     */
    @QueryField(type = QueryType.EQ)
    private String xnxq;

    /**
     * 创建人
     */
    @QueryField(type = QueryType.EQ)
    private String createBy;

    /**
     * 岗位名称（用于关联查询）
     */
    @QueryField(ignore = true)
    private String jobName;

    /**
     * 学生姓名（用于关联查询）
     */
    @QueryField(ignore = true)
    private String studentName;
    /**
     * 学生联系方式（用于关联查询）
     */
    @QueryField(ignore = true)
    private String studentContact;

    /**
     * 用人单位名称（用于关联查询）
     */
    @QueryField(ignore = true)
    private String employerName;
}
