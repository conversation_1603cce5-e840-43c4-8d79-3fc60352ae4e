package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.param.QgzxAttendanceRecordParam;
import com.sanythadmin.project.workstudy.vo.QgzxAttendanceRecordVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 勤工助学考勤记录Service
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface QgzxAttendanceRecordService extends IService<QgzxAttendanceRecord> {

    PageResult<QgzxAttendanceRecordVO> page(QgzxAttendanceRecordParam param);
    List<QgzxAttendanceRecordVO> list(QgzxAttendanceRecordParam param);
    QgzxAttendanceRecordVO getDetail(String id);
    void clockIn(QgzxAttendanceRecord record);
    void clockOut(QgzxAttendanceRecord record);
    void delete(String... ids);
    PageResult<QgzxAttendanceRecordVO> pageByEmployer(QgzxAttendanceRecordParam param);
    List<QgzxAttendanceRecordVO> listByStudent(QgzxAttendanceRecordParam param);
    PageResult<QgzxAttendanceRecordVO> pageByStudent(QgzxAttendanceRecordParam param);
    /**
     * 获取学生今日考勤状态
     *
     * @param studentApplyId 学生申请ID
     * @return 今日考勤状态信息
     */
    Map<String, Object> getTodayAttendanceStatus(String studentApplyId);

    /**
     * 统计学生工作时长
     *
     * @param studentApplyId 学生申请ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 工作时长统计（小时）
     */
    BigDecimal calculateWorkHours(String studentApplyId, LocalDate startDate, LocalDate endDate);

    /**
     * 检查是否可以打卡（位置、时间等验证）
     *
     * @param studentApplyId 学生申请ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 验证结果
     */
    Map<String, Object> validateClockIn(String studentApplyId, BigDecimal longitude, BigDecimal latitude);

    /**
     * 根据考勤状态查询记录
     *
     * @param param 查询参数
     * @return 考勤记录列表
     */
    List<QgzxAttendanceRecordVO> listByStatus(QgzxAttendanceRecordParam param);

    /**
     * 统计各种考勤状态的数量
     *
     * @param studentApplyId 学生申请ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 状态统计结果
     */
    Map<AttendanceStatus, Long> countByStatus(String studentApplyId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取异常考勤记录（迟到、早退、缺勤）
     *
     * @param param 查询参数
     * @return 异常考勤记录
     */
    PageResult<QgzxAttendanceRecordVO> getAbnormalRecords(QgzxAttendanceRecordParam param);
}
