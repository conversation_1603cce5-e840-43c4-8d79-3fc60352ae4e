package com.sanythadmin.project.workstudy.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 勤工助学报酬申报Mapper
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QgzxRemunerationApplyMapper extends MyMPJBaseMapper<QgzxRemunerationApply> {

    /**
     * 根据岗位ID和申报年月查询报酬申报
     */
    @Select("SELECT * FROM SYT_QGZX_REMUNERATION_APPLY WHERE JOB_ID = #{jobId} AND SBNY = #{sbny}")
    QgzxRemunerationApply selectByJobIdAndSbny(@Param("jobId") String jobId, @Param("sbny") String sbny);

    /**
     * 查询指定岗位类别在指定年月的总报酬金额
     */
    @Select("SELECT COALESCE(SUM(ra.TOTAL_AMOUNT), 0) FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "WHERE ja.JOB_TYPE_ID = #{jobTypeId} AND ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo'")
    BigDecimal selectTotalAmountByJobTypeAndSbny(@Param("jobTypeId") String jobTypeId, @Param("sbny") String sbny);

    /**
     * 查询指定岗位类别在指定年月的总工时
     */
    @Select("SELECT COALESCE(SUM(ra.TOTAL_HOURS), 0) FROM SYT_QGZX_REMUNERATION_APPLY ra " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "WHERE ja.JOB_TYPE_ID = #{jobTypeId} AND ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo'")
    BigDecimal selectTotalHoursByJobTypeAndSbny(@Param("jobTypeId") String jobTypeId, @Param("sbny") String sbny);

    /**
     * 查询指定岗位在指定年月的总工时
     */
    @Select("SELECT COALESCE(SUM(TOTAL_HOURS), 0) FROM SYT_QGZX_REMUNERATION_APPLY " +
            "WHERE JOB_ID = #{jobId} AND SBNY = #{sbny} AND SPZT = 'TongGuo'")
    BigDecimal selectTotalHoursByJobIdAndSbny(@Param("jobId") String jobId, @Param("sbny") String sbny);
}
