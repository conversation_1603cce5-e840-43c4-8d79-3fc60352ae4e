package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位类型查询参数
 *
 * <AUTHOR>
 * @since 2025-07-03 09:48:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxJobTypeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 每月最高报酬
     */
    @QueryField(type = QueryType.EQ)
    private Double yzgbc;

    /**
     * 月最大工时
     */
    @QueryField(type = QueryType.EQ)
    private Double yzdgs;

    /**
     * 同时在岗的最大岗位数(例如：学生在该类别下同时能在岗的岗位数)
     * 0为不限制，1为只能有一个在岗岗位(如固定岗)
     */
    @QueryField(type = QueryType.EQ)
    private Integer kyggws;


}
