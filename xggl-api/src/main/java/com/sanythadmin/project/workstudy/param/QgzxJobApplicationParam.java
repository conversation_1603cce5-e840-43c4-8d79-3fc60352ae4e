package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * 岗位申报查询参数
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxJobApplicationParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 用人单位ID
     */
    private String eid;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 岗位类别/类型(临时岗、固定岗、助教、助管 、助研、助工)
     */
    private String jobTypeId;

    /**
     * 开始日期
     */
    @QueryField(ignore = true)
    @DateTimeFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @QueryField(ignore = true)
    @DateTimeFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private LocalDate endDate;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 每月工作小时数
     */
    @QueryField(type = QueryType.EQ)
    private Double workHous;

    /**
     * 时薪
     */
    private Double hourlyRate;

    /**
     * 岗位描述
     */
    private String bz;

    /**
     * 所在校区
     */
    private String xqmc;

    /**
     * 联系方式
     */
    private String lxfs;

    /**
     * 是否对外发布
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark published;

    /**
     * 审批状态
     */
    @QueryField(type = QueryType.EQ)
    private ReviewResult spzt;

    /**
     * 困难生比例
     */
//    private Double pksbl;

    /**
     * 用工人数
     */
    @QueryField(type = QueryType.EQ)
    private Integer ygrs;

    /**
     * 是否校外岗
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxwg;

    /**
     * 工作日
     */
    private String workDays;

    /**
     * 打卡范围（米）- 允许的打卡距离范围，默认500米
     */
    private Integer dkfw;

    /**
     * 是否限制上课时间
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxzsksj;

    /**
     * 是否面试
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfms;

    /**
     * 面试地点
     */
    private String msdd;

    /**
     * 岗位职责
     */
    private String gwzz;

    /**
     * 岗位要求
     */
    private String gwyq;

    /**
     * 招聘条件
     */
    private String zptj;
    /**
     * 月最高报酬
     */
    private Double yzgbc;

    /**
     * 是否签到
     */
    private JudgeMark sfqd;
    /**
     * 用工结束是否审核
     */
    private JudgeMark ygjssfsh;

    /**
     * 报酬是否审核
     */
    private JudgeMark bcsfsh;

    /**
     * 岗位地址列表（用于前端维护）
     */
    @QueryField(ignore = true)
    private List<QgzxJobApplicationAddress> jobAddresses;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "CREATE_TIME desc";
    }
}
