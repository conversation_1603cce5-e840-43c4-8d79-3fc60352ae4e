package com.sanythadmin.project.workstudy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxStudentsClassTime;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 学生空余时间Mapper
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
public interface QgzxStudentsClassTimeMapper extends BaseMapper<QgzxStudentsClassTime> {


    @Delete("DELETE FROM SYT_QGZX_STUDENTS_CLASS_TIME WHERE xnxq = #{xnxq} AND xgh = #{xgh}")
    void deleteByXnxqAndXgh(@Param("xnxq") String xnxq,@Param("xgh") String xgh);

    @Select("SELECT * FROM SYT_QGZX_STUDENTS_CLASS_TIME WHERE xnxq = #{xnxq} AND xgh = #{xgh}")
    List<QgzxStudentsClassTime> selectXnxqAndXgh(@Param("xnxq") String xnxq, @Param("xgh") String xgh);
}
