package com.sanythadmin.project.workstudy.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

/**
 * 勤工助学工时计算服务
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QgzxWorkHoursCalculationService {

    /**
     * 根据考勤记录计算学生在指定时间段的工时
     *
     * @param studentApplyId 学生申请ID
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @return 工时（小时）
     */
    BigDecimal calculateWorkHoursByAttendance(String studentApplyId, LocalDate startDate, LocalDate endDate);

    /**
     * 批量计算多个学生的工时
     *
     * @param jobId     岗位ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 学生学号 -> 工时的映射
     */
    Map<String, BigDecimal> batchCalculateWorkHours(String jobId, LocalDate startDate, LocalDate endDate);

    /**
     * 验证学生工时是否超出岗位限制
     *
     * @param xgh       学号
     * @param jobId     岗位ID
     * @param workHours 申报工时
     * @param sbny      申报年月
     * @return 验证结果
     */
    boolean validateStudentWorkHours(String xgh, String jobId, BigDecimal workHours, String sbny);

    /**
     * 验证岗位总工时是否超出类别限制
     *
     * @param jobId     岗位ID
     * @param totalHours 总工时
     * @param sbny      申报年月
     * @return 验证结果
     */
    boolean validateJobTotalWorkHours(String jobId, BigDecimal totalHours, String sbny);

    /**
     * 验证岗位类别总报酬是否超出限制
     *
     * @param jobTypeId   岗位类别ID
     * @param totalAmount 总报酬金额
     * @param sbny        申报年月
     * @return 验证结果
     */
    boolean validateJobTypeTotalAmount(String jobTypeId, BigDecimal totalAmount, String sbny);

    /**
     * 获取学生在指定岗位的已申报工时
     *
     * @param xgh   学号
     * @param jobId 岗位ID
     * @param sbny  申报年月
     * @return 已申报工时
     */
    BigDecimal getStudentDeclaredHours(String xgh, String jobId, String sbny);

    /**
     * 获取岗位类别的已申报总工时
     *
     * @param jobTypeId 岗位类别ID
     * @param sbny      申报年月
     * @return 已申报总工时
     */
    BigDecimal getJobTypeDeclaredTotalHours(String jobTypeId, String sbny);

    /**
     * 获取岗位类别的已申报总报酬
     *
     * @param jobTypeId 岗位类别ID
     * @param sbny      申报年月
     * @return 已申报总报酬
     */
    BigDecimal getJobTypeDeclaredTotalAmount(String jobTypeId, String sbny);
}
