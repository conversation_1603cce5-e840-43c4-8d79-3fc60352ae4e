package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workstudy.entity.QgzxKsb;
import com.sanythadmin.project.workstudy.enums.TimeType;
import com.sanythadmin.project.workstudy.mapper.QgzxKsbMapper;
import com.sanythadmin.project.workstudy.service.QgzxKsbService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 勤工助学课时表Service实现
 *
 * <AUTHOR>
 * @since 2025-07-10 15:33:46
 */
@Service
public class QgzxKsbServiceImpl extends ServiceImpl<QgzxKsbMapper, QgzxKsb> implements QgzxKsbService {

    @Resource
    private QgzxKsbMapper mapper;

    @Override
    public List<QgzxKsb> getByXnxqAndTimeType(String xnxq, TimeType timeType) {
        QueryWrapper<QgzxKsb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("XNXQ", xnxq);
        if (timeType != null) {
            queryWrapper.eq("TIME_TYPE", timeType.getValue());
        }
        queryWrapper.orderByAsc("SORT", "WEEK_DAY", "KSSJ");
        return mapper.selectList(queryWrapper);
    }

}
