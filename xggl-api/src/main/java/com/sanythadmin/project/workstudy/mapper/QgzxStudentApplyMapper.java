package com.sanythadmin.project.workstudy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 学生岗位申请Mapper
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
public interface QgzxStudentApplyMapper extends BaseMapper<QgzxStudentApply> {

    /**
     * 根据学年学期和学号查询学生申请记录
     */
    @Select("SELECT * FROM SYT_QGZX_STUDENT_APPLY WHERE XNXQ = #{xnxq} AND XGH = #{xgh}")
    List<QgzxStudentApply> selectByXnxqAndXgh(@Param("xnxq") String xnxq, @Param("xgh") String xgh);

    /**
     * 根据岗位ID查询申请记录数量
     */
    @Select("SELECT COUNT(*) FROM SYT_QGZX_STUDENT_APPLY WHERE JOB_ID = #{jobId} AND XNXQ = #{xnxq}")
    Long countByJobIdAndXnxq(@Param("jobId") String jobId, @Param("xnxq") String xnxq);

    /**
     * 批量查询学生对指定岗位列表的申请状态
     */
    @Select("<script>" +
            "SELECT DISTINCT JOB_ID FROM SYT_QGZX_STUDENT_APPLY " +
            "WHERE XGH = #{xgh} AND XNXQ = #{xnxq} " +
            "AND JOB_ID IN " +
            "<foreach collection='jobIds' item='jobId' open='(' separator=',' close=')'>" +
            "#{jobId}" +
            "</foreach>" +
            "</script>")
    List<String> selectAppliedJobIds(@Param("xgh") String xgh,@Param("xnxq") String xnxq,@Param("jobIds") List<String> jobIds);

    /**
     * 批量查询多个岗位的申请人数
     */
    @Select("<script>" +
            "SELECT JOB_ID as jobId, COUNT(*) as applyCount " +
            "FROM SYT_QGZX_STUDENT_APPLY " +
            "WHERE XNXQ = #{xnxq} " +
            "AND JOB_ID IN " +
            "<foreach collection='jobIds' item='jobId' open='(' separator=',' close=')'>" +
            "#{jobId}" +
            "</foreach>" +
            " GROUP BY JOB_ID" +
            "</script>")
    List<Map<String, Object>> selectApplyCountsByJobIds(@Param("jobIds") List<String> jobIds,@Param("xnxq") String xnxq);
}