package com.sanythadmin.project.workstudy.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sanythadmin.project.workstudy.entity.QgzxStudentsClassTime;
import com.sanythadmin.project.workstudy.enums.WeekType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 周次计算工具类
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
public class WeekUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 从校历日期字符串中解析学期开始日期
     *
     * @param schoolCalendarDate 校历日期字符串，格式："2024-09-01,2025-06-02"
     * @return 学期开始日期
     */
    public static LocalDate parseTermStartDate(String schoolCalendarDate) {
        try {
            String[] dates = schoolCalendarDate.split(",");
            if (dates.length >= 1) {
                return LocalDate.parse(dates[0].trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        } catch (Exception e) {
            log.error("解析校历日期失败: {}", schoolCalendarDate, e);
        }
        return null;
    }

    /**
     * 从校历日期字符串中解析学期结束日期
     *
     * @param schoolCalendarDate 校历日期字符串，格式："2024-09-01,2025-06-02"
     * @return 学期结束日期
     */
    public static LocalDate parseTermEndDate(String schoolCalendarDate) {
        try {
            String[] dates = schoolCalendarDate.split(",");
            if (dates.length >= 2) {
                return LocalDate.parse(dates[1].trim(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
        } catch (Exception e) {
            log.error("解析校历日期失败: {}", schoolCalendarDate, e);
        }
        return null;
    }

    /**
     * 计算指定日期是学期的第几周
     *
     * @param date 指定日期
     * @param termStartDate 学期开始日期（第一周的周一）
     * @return 周次（从1开始）
     */
    public static int calculateWeekNumber(LocalDate date, LocalDate termStartDate) {
        long daysBetween = ChronoUnit.DAYS.between(termStartDate, date);
        int weekNumber = (int) Math.ceil((daysBetween + 1) / 7.0);
        return Math.max(1, weekNumber);
    }

    /**
     * 判断学生在指定周次是否有课
     * 
     * @param classTime 学生课时时间记录
     * @param weekNumber 周次
     * @return true-有课，false-没课
     */
    public static boolean hasClassInWeek(QgzxStudentsClassTime classTime, int weekNumber) {
        if (weekNumber < classTime.getZcStart() || weekNumber > classTime.getZcEnd()) {
            return false;
        }

        WeekType weekType = classTime.getZcType();
        if (weekType == null) {
            weekType = WeekType.ALL;
        }

        switch (weekType) {
            case ALL:
                return true;
            case ODD:
                return weekNumber % 2 == 1; // 单周
            case EVEN:
                return weekNumber % 2 == 0; // 双周
            case CUSTOM:
                return isInCustomWeeks(classTime.getZcCustom(), weekNumber);
            default:
                return true;
        }
    }

    /**
     * 判断周次是否在自定义周次列表中
     * 
     * @param zcCustom 自定义周次JSON字符串，如"[1,3,5,7,9]"
     * @param weekNumber 周次
     * @return true-在列表中，false-不在列表中
     */
    private static boolean isInCustomWeeks(String zcCustom, int weekNumber) {
        if (!StringUtils.hasText(zcCustom)) {
            return false;
        }

        try {
            List<Integer> customWeeks = objectMapper.readValue(zcCustom, new TypeReference<List<Integer>>() {});
            return customWeeks.contains(weekNumber);
        } catch (Exception e) {
            log.error("解析自定义周次失败: {}", zcCustom, e);
            return false;
        }
    }

    /**
     * 获取当前日期是星期几（1-7，周一到周日）
     * 
     * @param date 日期
     * @return 星期几（1-7）
     */
    public static int getDayOfWeek(LocalDate date) {
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek.getValue();
    }

    /**
     * 将自定义周次列表转换为JSON字符串
     * 
     * @param weeks 周次列表
     * @return JSON字符串
     */
    public static String weeksToJson(List<Integer> weeks) {
        try {
            return objectMapper.writeValueAsString(weeks);
        } catch (Exception e) {
            log.error("转换周次列表为JSON失败", e);
            return "[]";
        }
    }

    /**
     * 将JSON字符串转换为周次列表
     * 
     * @param json JSON字符串
     * @return 周次列表
     */
    public static List<Integer> jsonToWeeks(String json) {
        try {
            return objectMapper.readValue(json, new TypeReference<List<Integer>>() {});
        } catch (Exception e) {
            log.error("解析周次JSON失败: {}", json, e);
            return List.of();
        }
    }
}
