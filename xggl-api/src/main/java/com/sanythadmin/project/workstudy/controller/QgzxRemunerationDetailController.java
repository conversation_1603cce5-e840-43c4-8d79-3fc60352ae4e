package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationDetail;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationDetailParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/报酬明细控制器
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-remuneration-detail")
@RequiredArgsConstructor
public class QgzxRemunerationDetailController extends BaseController {

    private final QgzxRemunerationDetailService qgzxRemunerationDetailService;

    /**
     * 分页查询报酬明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:list')")
    @GetMapping("/page")
    public PageResult<QgzxRemunerationDetail> page(QgzxRemunerationDetailParam param) {
        return qgzxRemunerationDetailService.pageList(param);
    }

    /**
     * 查询报酬明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:list')")
    @GetMapping
    public List<QgzxRemunerationDetail> list(QgzxRemunerationDetailParam param) {
        return qgzxRemunerationDetailService.listDetail(param);
    }

    /**
     * 根据ID查询报酬明细详情（权限标识：workstudy:qgzxRemunerationDetail:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:list')")
    @GetMapping("/{id}")
    public QgzxRemunerationDetail get(@PathVariable String id) {
        return qgzxRemunerationDetailService.getDetail(id);
    }

    /**
     * 根据申报ID查询明细列表（权限标识：workstudy:qgzxRemunerationDetail:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:list')")
    @GetMapping("/by-apply/{remunerationApplyId}")
    public List<QgzxRemunerationDetail> getByApplyId(@PathVariable String remunerationApplyId) {
        return qgzxRemunerationDetailService.getDetailsByApplyId(remunerationApplyId);
    }

    /**
     * 添加或修改报酬明细（权限标识：workstudy:qgzxRemunerationDetail:operation）
     */
    @OperationLog(module = "报酬明细", comments = "保存报酬明细")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:operation')")
    @PostMapping("/operation")
    public void save(@RequestBody QgzxRemunerationDetail detail) {
        if (StringUtils.hasLength(detail.getId())) {
            qgzxRemunerationDetailService.updateDetail(detail);
        } else {
            qgzxRemunerationDetailService.addDetail(detail);
        }
    }

    /**
     * 删除报酬明细（权限标识：workstudy:qgzxRemunerationDetail:remove）
     */
    @OperationLog(module = "报酬明细", comments = "删除报酬明细")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:remove')")
    @DeleteMapping
    public void remove(@RequestBody String[] ids) {
        qgzxRemunerationDetailService.deleteDetail(ids);
    }

    /**
     * 重新计算申报总计（权限标识：workstudy:qgzxRemunerationDetail:edit）
     */
    @OperationLog(module = "报酬明细", comments = "重新计算申报总计")
    @PreAuthorize("hasAuthority('workstudy:qgzxRemunerationDetail:edit')")
    @PostMapping("/recalculate/{remunerationApplyId}")
    public void recalculateApplyTotals(@PathVariable String remunerationApplyId) {
        qgzxRemunerationDetailService.recalculateApplyTotals(remunerationApplyId);
    }
}
