package com.sanythadmin.project.workstudy.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.Getter;

@Getter
public enum WorkStatus implements TextBaseEnum {
    DSG("待上岗"),      // 已通过审核但尚未开始工作
    YG("用工"),        // 正在工作中
    LG("离岗"),        // 临时离岗
    YGJSDSH("用工结束待审核"), // 用工结束待审核
    YGJS("用工结束");   // 聘用结束

//    @EnumValue
    @JsonValue
    private final String text;

    WorkStatus(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据文本或枚举名称获取枚举值
     * @param value 文本值或枚举名称
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static WorkStatus get(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        for (WorkStatus status : WorkStatus.values()) {
            // 支持通过枚举名称获取
            if (status.name().equals(value)) {
                return status;
            }
            // 支持通过文本值获取
            if (status.getText().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
