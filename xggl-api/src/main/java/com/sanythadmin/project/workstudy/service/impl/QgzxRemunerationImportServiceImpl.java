package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.mapper.UserOrgMapMapper;
import com.sanythadmin.project.workstudy.dto.QgzxRemunerationImportDTO;
import com.sanythadmin.project.workstudy.entity.*;
import com.sanythadmin.project.workstudy.mapper.*;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationImportService;
import com.sanythadmin.project.workstudy.service.QgzxWorkHoursCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;

/**
 * 勤工助学报酬历史数据导入服务实现
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxRemunerationImportServiceImpl implements QgzxRemunerationImportService {

    private final UserInfoMapper userInfoMapper;
    private final UserOrgMapMapper userOrgMapMapper;
    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final QgzxEmployerMapper employerMapper;
    private final QgzxJobTypeMapper jobTypeMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;
    private final QgzxRemunerationApplyMapper remunerationApplyMapper;
    private final QgzxRemunerationDetailMapper remunerationDetailMapper;
    private final QgzxWorkHoursCalculationService workHoursCalculationService;

    @Override
    public List<QgzxRemunerationImportDTO> readExcelData(MultipartFile file) {
        try {
            EasyExcelHelper<QgzxRemunerationImportDTO> helper = new EasyExcelHelper<>(QgzxRemunerationImportDTO.class);
            List<QgzxRemunerationImportDTO> importData = helper.read(file.getInputStream());

            // 设置行号
            for (int i = 0; i < importData.size(); i++) {
                importData.get(i).setRowNumber(i + 2); // Excel从第2行开始（第1行是标题）
            }

            log.info("读取Excel数据成功，共{}条记录", importData.size());
            return importData;

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            AssertUtil.throwMessage("读取Excel文件失败：" + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public void validateImportData(List<QgzxRemunerationImportDTO> importData, List<ExcelImportError> errors) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        
        for (QgzxRemunerationImportDTO dto : importData) {
            validateSingleDataSync(dto, errors);
        }
        
        log.info("数据验证完成，共{}条记录，{}条错误", importData.size(), errors.size());
    }

    @Override
    public void validateImportDataConcurrently(List<QgzxRemunerationImportDTO> importData, 
                                              List<ExcelImportError> errors, 
                                              Executor executor) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (QgzxRemunerationImportDTO dto : importData) {
            CompletableFuture<Void> future = validateSingleData(dto, executor)
                    .handle((result, ex) -> {
                        if (ex != null) {
                            String message = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                            errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getXgh(), message));
                            dto.setValid(false);
                            dto.setErrorMessage(message);
                        }
                        return null;
                    });
            futures.add(future);
        }
        
        // 等待所有验证完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        log.info("并发数据验证完成，共{}条记录，{}条错误", importData.size(), errors.size());
    }

    @Override
    public CompletableFuture<Void> validateSingleData(QgzxRemunerationImportDTO dto, Executor executor) {
        return CompletableFuture.runAsync(() -> {
            List<ExcelImportError> tempErrors = new CopyOnWriteArrayList<>();
            validateSingleDataSync(dto, tempErrors);
            
            if (!tempErrors.isEmpty()) {
                throw new RuntimeException(tempErrors.get(0).getRemark());
            }
        }, executor);
    }

    /**
     * 同步验证单条数据
     */
    private void validateSingleDataSync(QgzxRemunerationImportDTO dto, List<ExcelImportError> errors) {
        try {
            // 1. 基础字段验证（已通过注解验证）
            
            // 2. 学号有效性验证
            if (!checkStudentExists(dto.getXgh())) {
                errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getXgh(), "学号不存在"));
                return;
            }
            
            // 3. 岗位信息验证
            String jobId = checkJobExists(dto.getJobName(), dto.getEmployerName(), dto.getJobTypeName());
            if (jobId == null) {
                errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getJobName(), 
                        "岗位不存在或岗位信息不匹配（岗位名称、用人单位、岗位类别）"));
                return;
            }
            dto.setJobId(jobId);
            
            // 4. 报酬金额计算验证
            if (!checkAmountCalculation(dto.getWorkHours(), dto.getHourlyRate(), dto.getRemunerationAmount())) {
                errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getRemunerationAmount(), 
                        "报酬金额计算错误，应为：" + dto.getWorkHours().multiply(dto.getHourlyRate())));
                return;
            }
            
            // 5. 工时合理性验证
            if (!checkWorkHoursValid(dto.getXgh(), jobId, dto.getWorkHours(), dto.getSbny())) {
                errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getWorkHours(), 
                        "工时超出岗位限制或学生个人工时限制"));
                return;
            }
            
            // 6. 重复数据检查
            if (checkDuplicateData(dto.getXgh(), jobId, dto.getSbny())) {
                errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getXgh(), 
                        "该学生在该岗位该月份已有报酬记录"));
                return;
            }
            
            // 7. 设置辅助字段
            dto.setStudentApplyId(getStudentApplyId(dto.getXgh(), jobId));
            dto.setEmployerId(getEmployerId(dto.getEmployerName()));
            dto.setJobTypeId(getJobTypeId(dto.getJobTypeName()));
            dto.setXyid(getCollegeId(dto.getXgh()));
            
            dto.setValid(true);
            
        } catch (Exception e) {
            log.error("验证数据时发生异常: 学号={}, 错误={}", dto.getXgh(), e.getMessage(), e);
            errors.add(ExcelImportError.data(dto.getRowNumber(), dto.getXgh(), "验证失败：" + e.getMessage()));
            dto.setValid(false);
            dto.setErrorMessage(e.getMessage());
        }
    }

    @Override
    public boolean checkStudentExists(String xgh) {
        if (!StringUtils.hasLength(xgh)) {
            return false;
        }
        
        Long count = userInfoMapper.selectCount(
                new LambdaQueryWrapper<UserInfo>()
                        .eq(UserInfo::getXgh, xgh)
        );
        
        return count != null && count > 0;
    }

    @Override
    public String checkJobExists(String jobName, String employerName, String jobTypeName) {
        if (!StringUtils.hasLength(jobName) || !StringUtils.hasLength(employerName) || !StringUtils.hasLength(jobTypeName)) {
            return null;
        }
        
        // 查询岗位，需要关联用人单位和岗位类别
        List<QgzxJobApplication> jobs = jobApplicationMapper.selectList(
                new LambdaQueryWrapper<QgzxJobApplication>()
                        .eq(QgzxJobApplication::getJobName, jobName)
                        .eq(QgzxJobApplication::getSpzt, ReviewResult.TongGuo) // 只查询已通过审核的岗位
        );
        
        if (CollectionUtils.isEmpty(jobs)) {
            return null;
        }
        
        // 验证用人单位和岗位类别
        for (QgzxJobApplication job : jobs) {
            // 验证用人单位
            QgzxEmployer employer = employerMapper.selectById(job.getEid());
            if (employer == null || !employerName.equals(employer.getName())) {
                continue;
            }
            
            // 验证岗位类别
            QgzxJobType jobType = jobTypeMapper.selectById(job.getJobTypeId());
            if (jobType == null || !jobTypeName.equals(jobType.getName())) {
                continue;
            }
            
            return job.getId();
        }
        
        return null;
    }

    @Override
    public boolean checkWorkHoursValid(String xgh, String jobId, BigDecimal workHours, String sbny) {
        if (workHours == null || workHours.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        try {
            // 使用现有的工时验证服务
            return workHoursCalculationService.validateStudentWorkHours(xgh, jobId, workHours, sbny);
        } catch (Exception e) {
            log.error("验证工时时发生异常: 学号={}, 岗位ID={}, 工时={}, 申报年月={}", xgh, jobId, workHours, sbny, e);
            return false;
        }
    }

    @Override
    public boolean checkDuplicateData(String xgh, String jobId, String sbny) {
        if (!StringUtils.hasLength(xgh) || !StringUtils.hasLength(jobId) || !StringUtils.hasLength(sbny)) {
            return false;
        }
        
        // 检查是否已有该学生该岗位该月份的报酬申报
        Long count = remunerationApplyMapper.selectCount(
                new LambdaQueryWrapper<QgzxRemunerationApply>()
                        .eq(QgzxRemunerationApply::getJobId, jobId)
                        .eq(QgzxRemunerationApply::getSbny, sbny)
                        .in(QgzxRemunerationApply::getSpzt, ReviewResult.DaiShenPi, ReviewResult.TongGuo)
        );
        
        if (count != null && count > 0) {
            // 进一步检查明细表中是否有该学生的记录
            Long detailCount = remunerationDetailMapper.selectStudentDetailCount(xgh, jobId, sbny);
            return detailCount != null && detailCount > 0;
        }
        
        return false;
    }

    @Override
    public boolean checkAmountCalculation(BigDecimal workHours, BigDecimal hourlyRate, BigDecimal remunerationAmount) {
        if (workHours == null || hourlyRate == null || remunerationAmount == null) {
            return false;
        }
        
        BigDecimal calculatedAmount = workHours.multiply(hourlyRate).setScale(2, RoundingMode.HALF_UP);
        return calculatedAmount.compareTo(remunerationAmount) == 0;
    }

    @Override
    public String getStudentApplyId(String xgh, String jobId) {
        if (!StringUtils.hasLength(xgh) || !StringUtils.hasLength(jobId)) {
            return null;
        }
        
        QgzxStudentApply studentApply = studentApplyMapper.selectOne(
                new LambdaQueryWrapper<QgzxStudentApply>()
                        .eq(QgzxStudentApply::getXgh, xgh)
                        .eq(QgzxStudentApply::getJobId, jobId)
                        .eq(QgzxStudentApply::getSpzt, ReviewResult.TongGuo)
                        .last("LIMIT 1")
        );
        
        return studentApply != null ? studentApply.getId() : null;
    }

    @Override
    public String getEmployerId(String employerName) {
        if (!StringUtils.hasLength(employerName)) {
            return null;
        }
        
        QgzxEmployer employer = employerMapper.selectOne(
                new LambdaQueryWrapper<QgzxEmployer>()
                        .eq(QgzxEmployer::getName, employerName)
                        .last("LIMIT 1")
        );
        
        return employer != null ? employer.getId() : null;
    }

    @Override
    public String getJobTypeId(String jobTypeName) {
        if (!StringUtils.hasLength(jobTypeName)) {
            return null;
        }
        
        QgzxJobType jobType = jobTypeMapper.selectOne(
                new LambdaQueryWrapper<QgzxJobType>()
                        .eq(QgzxJobType::getName, jobTypeName)
                        .last("LIMIT 1")
        );
        
        return jobType != null ? jobType.getId() : null;
    }

    @Override
    public String getCollegeId(String xgh) {
        if (!StringUtils.hasLength(xgh)) {
            return null;
        }

        // 通过 UserOrgMap 表获取学院ID
        UserOrgMap userOrgMap = userOrgMapMapper.selectOne(
                new LambdaQueryWrapper<UserOrgMap>()
                        .eq(UserOrgMap::getXgh, xgh)
                        .last("LIMIT 1")
        );

        return userOrgMap != null ? userOrgMap.getXyid() : null;
    }

    @Override
    @Transactional
    public void batchImportData(List<QgzxRemunerationImportDTO> importData) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        
        // 过滤出验证通过的数据
        List<QgzxRemunerationImportDTO> validData = importData.stream()
                .filter(QgzxRemunerationImportDTO::isValid)
                .toList();
        
        if (CollectionUtils.isEmpty(validData)) {
            log.warn("没有有效的数据可以导入");
            return;
        }
        
        log.info("开始批量导入数据，共{}条有效记录", validData.size());
        
        // 按岗位和申报年月分组，批量创建申报记录
        // 这里简化处理，实际应该按业务逻辑分组
        for (QgzxRemunerationImportDTO dto : validData) {
            try {
                importSingleRecord(dto);
            } catch (Exception e) {
                log.error("导入单条记录失败: 学号={}, 错误={}", dto.getXgh(), e.getMessage(), e);
                // 继续处理其他记录
            }
        }
        
        log.info("批量导入完成，成功导入{}条记录", validData.size());
    }

    /**
     * 导入单条记录
     */
    private void importSingleRecord(QgzxRemunerationImportDTO dto) {
        // 查找或创建报酬申报记录
        QgzxRemunerationApply remunerationApply = findOrCreateRemunerationApply(dto);
        
        // 创建报酬明细记录
        QgzxRemunerationDetail detail = new QgzxRemunerationDetail();
        detail.setRemunerationApplyId(remunerationApply.getId());
        detail.setXgh(dto.getXgh());
        detail.setStudentApplyId(dto.getStudentApplyId());
        detail.setWorkHours(dto.getWorkHours());
        detail.setHourlyRate(dto.getHourlyRate());
        detail.setRemunerationAmount(dto.getRemunerationAmount());
        detail.setRemark(dto.getWorkDescription());
        detail.setCreateTime(LocalDateTime.now());
        
        remunerationDetailMapper.insert(detail);
        
        // 更新申报记录的总计
        updateRemunerationApplyTotals(remunerationApply.getId());
    }

    /**
     * 查找或创建报酬申报记录
     */
    private QgzxRemunerationApply findOrCreateRemunerationApply(QgzxRemunerationImportDTO dto) {
        // 查找现有申报记录
        QgzxRemunerationApply existingApply = remunerationApplyMapper.selectOne(
                new LambdaQueryWrapper<QgzxRemunerationApply>()
                        .eq(QgzxRemunerationApply::getJobId, dto.getJobId())
                        .eq(QgzxRemunerationApply::getSbny, dto.getSbny())
                        .in(QgzxRemunerationApply::getSpzt, ReviewResult.DaiShenPi, ReviewResult.TongGuo)
                        .last("LIMIT 1")
        );
        
        if (existingApply != null) {
            return existingApply;
        }
        
        // 创建新的申报记录
        QgzxRemunerationApply newApply = new QgzxRemunerationApply();
        newApply.setJobId(dto.getJobId());
        newApply.setSbny(dto.getSbny());
        newApply.setXnxq(dto.getXnxq());
        newApply.setSpzt(ReviewResult.TongGuo); // 历史数据直接设为已通过
        newApply.setTotalHours(BigDecimal.ZERO);
        newApply.setTotalAmount(BigDecimal.ZERO);
        newApply.setCreateTime(LocalDateTime.now());
        
        remunerationApplyMapper.insert(newApply);
        return newApply;
    }

    /**
     * 更新申报记录的总计
     */
    private void updateRemunerationApplyTotals(String remunerationApplyId) {
        // 计算总工时和总报酬
        BigDecimal totalHours = remunerationDetailMapper.selectTotalHoursByApplyId(remunerationApplyId);
        BigDecimal totalAmount = remunerationDetailMapper.selectTotalAmountByApplyId(remunerationApplyId);
        
        // 更新申报记录
        QgzxRemunerationApply remunerationApply = new QgzxRemunerationApply();
        remunerationApply.setId(remunerationApplyId);
        remunerationApply.setTotalHours(totalHours != null ? totalHours : BigDecimal.ZERO);
        remunerationApply.setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO);
        remunerationApply.setUpdateTime(LocalDateTime.now());
        
        remunerationApplyMapper.updateById(remunerationApply);
    }

    @Override
    @Transactional
    public void importHistoryData(MultipartFile file) {
        log.info("开始导入历史报酬数据，文件名：{}", file.getOriginalFilename());

        // 1. 读取Excel数据
        List<QgzxRemunerationImportDTO> importData = readExcelData(file);
        if (CollectionUtils.isEmpty(importData)) {
            AssertUtil.throwMessage("导入文件为空或无有效数据");
        }

        log.info("读取到{}条数据，开始验证", importData.size());

        // 2. 并发验证数据
        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);

        validateImportDataConcurrently(importData, errors, executor);

        // 3. 检查验证错误
        if (!CollectionUtils.isEmpty(errors)) {
            log.warn("数据验证失败，共{}条错误", errors.size());
            String errorFileId = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(errorFileId);
        }

        // 4. 批量导入数据
        batchImportData(importData);

        log.info("历史报酬数据导入完成，成功导入{}条记录", importData.size());
    }
}
