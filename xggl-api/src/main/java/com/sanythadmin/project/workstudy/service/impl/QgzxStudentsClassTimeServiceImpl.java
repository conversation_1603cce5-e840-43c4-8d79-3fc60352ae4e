package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxKsb;
import com.sanythadmin.project.workstudy.entity.QgzxStudentsClassTime;
import com.sanythadmin.project.workstudy.mapper.QgzxKsbMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxStudentsClassTimeMapper;
import com.sanythadmin.project.workstudy.service.QgzxStudentsClassTimeService;
import com.sanythadmin.project.workstudy.util.WeekUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getUserInfo;

/**
 * 学生上课时间Service实现
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QgzxStudentsClassTimeServiceImpl extends ServiceImpl<QgzxStudentsClassTimeMapper, QgzxStudentsClassTime> implements QgzxStudentsClassTimeService {

    private final QgzxStudentsClassTimeMapper qgzxStudentsClassTimeMapper;
    private final SysModuleSetupService sysModuleSetupService;
    private final QgzxKsbMapper qgzxKsbMapper;
    private final SysParamService sysParamService;

    @Transactional
    @Override
    public void saveQgzxStudentsClassTimeList(List<QgzxStudentsClassTime> studentsClassTimeList) {
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        UserInfo userInfo = getUserInfo();
        qgzxStudentsClassTimeMapper.deleteByXnxqAndXgh(year, userInfo.getXgh());
        studentsClassTimeList.forEach(qgzxStudentsClassTime -> {
            qgzxStudentsClassTime.setXnxq(year);
            qgzxStudentsClassTime.setXgh(userInfo.getXgh());
        });
        saveOrUpdateBatch(studentsClassTimeList);
    }

    @Override
    public String check(UserInfo userInfo, QgzxJobApplication job) {
        return check(userInfo, job, null);
    }

    /**
     * 检查学生在指定日期是否与岗位时间冲突
     *
     * @param userInfo 用户信息
     * @param job 岗位信息
     * @param checkDate 检查日期，如果为null则不考虑周次
     * @return 冲突信息，null表示无冲突
     */
    public String check(UserInfo userInfo, QgzxJobApplication job, LocalDate checkDate) {
        if (job == null) {
            return "未找到对应岗位信息";
        }

        // 检查校历日期参数，如果为空则不限制学生时间
        String schoolCalendarDate = sysParamService.getParamValue(Constants.SCHOOL_CALENDAR_DATE);
        if (StringUtils.isEmpty(schoolCalendarDate)) {
            log.info("校历日期参数为空，不限制学生时间冲突检查，允许直接申请");
            return null; // 直接返回null，表示无冲突
        }

        // 检查学生上课时间是否重叠岗位时间
        boolean isAvailable = isStudentAvailableForJob(userInfo.getXgh(), job.getXnxq(), job, checkDate);
        if (!isAvailable) {
            return "您的上课时间与岗位工作时间不匹配，请查看您的上课时间设置";
        }
        return null;
    }

    /**
     * 判断学生上课时间是否与岗位时间冲突
     */
    private boolean isStudentAvailableForJob(String xgh, String xnxq, QgzxJobApplication job, LocalDate checkDate) {
        Set<Integer> jobWorkDays = parseWorkDays(job.getWorkDays());
        if (jobWorkDays.isEmpty()) {
            log.warn("岗位[{}]未设置工作日", job.getId());
            return false;
        }
        String jobStartTime = job.getStartTime();
        String jobEndTime = job.getEndTime();
        if (StringUtils.isEmpty(jobStartTime) || StringUtils.isEmpty(jobEndTime)) {
            log.warn("岗位[{}]未设置工作时间", job.getId());
            return false;
        }
        for (Integer workDay : jobWorkDays) {
            if (!isStudentAvailableOnDay(xgh, xnxq, workDay, jobStartTime, jobEndTime, checkDate)) {
                return false; // 只要有一天不匹配就返回false
            }
        }
        return true;
    }

    private Set<Integer> parseWorkDays(String workDays) {
        if (org.springframework.util.StringUtils.isEmpty(workDays)) {
            return Collections.emptySet();
        }
        try {
            return Arrays.stream(workDays.split(","))
                    .map(String::trim)
                    .filter(s -> !org.springframework.util.StringUtils.isEmpty(s))
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
        } catch (NumberFormatException e) {
            log.error("解析工作日异常: {}", workDays, e);
            return Collections.emptySet();
        }
    }


    /**
     * 判断学生在特定日期是否与岗位时间冲突
     *
     * @return true:不冲突/可用, false:冲突/不可用
     */
    private boolean isStudentAvailableOnDay(String xgh, String xnxq, Integer weekDay,
                                            String jobStartTime, String jobEndTime, LocalDate checkDate) {
        List<QgzxStudentsClassTime> classSlots = getStudentScheduleByDay(xgh, xnxq, weekDay, checkDate);
        if (classSlots.isEmpty()) {
            // 如果当天没有上课，则认为全天可用
            return true;
        }

        Map<String, QgzxKsb> ksbMap = getAllKsbMap(xnxq);
        LocalTime jobStart = LocalTime.parse(jobStartTime);
        LocalTime jobEnd = LocalTime.parse(jobEndTime);

        for (QgzxStudentsClassTime classSlot : classSlots) {
            QgzxKsb ksb = ksbMap.get(classSlot.getKsbId());
            if (ksb == null) {
                log.warn("未找到课时ID [{}] 对应的课时信息", classSlot.getKsbId());
                continue;
            }
            LocalTime classStart = LocalTime.parse(ksb.getKssj());
            LocalTime classEnd = LocalTime.parse(ksb.getJssj());
            // 判断时间段是否有交集: (start1 < end2) && (start2 < end1)
            if (jobStart.isBefore(classEnd) && classStart.isBefore(jobEnd)) {
                log.debug("学生[{}]在星期[{}]的上课时间[{}-{}]与岗位时间[{}-{}]冲突",xgh, weekDay, ksb.getKssj(), ksb.getJssj(), jobStartTime, jobEndTime);
                return false;
            }
        }
        return true;
    }

    /**
     * 获取学生某天上课时间
     */
    private List<QgzxStudentsClassTime> getStudentScheduleByDay(String xgh, String xnxq, Integer weekDay, LocalDate checkDate) {
        QueryWrapper<QgzxStudentsClassTime> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("XGH", xgh)
                .eq("XNXQ", xnxq)
                .eq("WEEK_DAY", weekDay);
        List<QgzxStudentsClassTime> allClassTimes = qgzxStudentsClassTimeMapper.selectList(queryWrapper);
        // 如果没有指定检查日期，返回所有记录
        if (checkDate == null) {
            return allClassTimes;
        }
        // 检查校历日期参数，如果为空则返回所有记录（不进行周次过滤）
        String schoolCalendarDate = sysParamService.getParamValue(Constants.SCHOOL_CALENDAR_DATE);
        if (!StringUtils.isEmpty(schoolCalendarDate)) {
            log.debug("校历日期参数为空，不进行周次过滤，返回所有课程记录");
            return allClassTimes;
        }
        LocalDate termStartDate = WeekUtils.parseTermStartDate(schoolCalendarDate);
        int weekNumber = WeekUtils.calculateWeekNumber(checkDate, termStartDate);
        return allClassTimes.stream()
                .filter(classTime -> WeekUtils.hasClassInWeek(classTime, weekNumber))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有课时表信息
     */
    private Map<String, QgzxKsb> getAllKsbMap(String xnxq) {
        QueryWrapper<QgzxKsb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("XNXQ", xnxq);
        List<QgzxKsb> ksbList = qgzxKsbMapper.selectList(queryWrapper);
        return ksbList.stream().collect(Collectors.toMap(QgzxKsb::getId, Function.identity(), (k1, k2) -> k1));
    }
}
