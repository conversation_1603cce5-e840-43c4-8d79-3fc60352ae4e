package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 勤工助学报酬申报实体
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_REMUNERATION_APPLY")
@Entity
@Table(name = "SYT_QGZX_REMUNERATION_APPLY")
public class QgzxRemunerationApply extends BaseApplicationInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 岗位ID
     */
    @Column(name = "JOB_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("JOB_ID")
    private String jobId;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 申报年月（格式：YYYY-MM）
     */
    @Column(name = "SBNY", columnDefinition = "VARCHAR2(7 CHAR)")
    @TableField("SBNY")
    private String sbny;

    /**
     * 申报开始日期
     */
    @Column(name = "START_DATE")
    @TableField("START_DATE")
    private LocalDate startDate;

    /**
     * 申报结束日期
     */
    @Column(name = "END_DATE")
    @TableField("END_DATE")
    private LocalDate endDate;

    /**
     * 审批状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPZT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SPZT")
    private ReviewResult spzt;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 申请时间
     */
    @Column(name = "SQSJ")
    @TableField("SQSJ")
    private LocalDateTime sqsj;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 申报说明
     */
    @Column(name = "SBSM", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("SBSM")
    private String sbsm;

    /**
     * 总工时
     */
    @Column(name = "TOTAL_HOURS", columnDefinition = "NUMBER(8,2)")
    @TableField("TOTAL_HOURS")
    private BigDecimal totalHours;

    /**
     * 总报酬金额
     */
    @Column(name = "TOTAL_AMOUNT", columnDefinition = "NUMBER(10,2)")
    @TableField("TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    /**
     * 关联的用户信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

    /**
     * 关联的岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication jobApplication;

    /**
     * 报酬明细列表
     */
    @Transient
    @TableField(exist = false)
    private List<QgzxRemunerationDetail> remunerationDetails;

    /**
     * 初始化必要字段
     */
    public void initRequiredFields() {
        this.setRoleId(SecurityUtil.getRoleId());
        if (this.jobApplication != null) {
            this.setXxmc("报酬申报-" + this.jobApplication.getJobName());
        } else {
            this.setXxmc("报酬申报");
        }
    }
}
