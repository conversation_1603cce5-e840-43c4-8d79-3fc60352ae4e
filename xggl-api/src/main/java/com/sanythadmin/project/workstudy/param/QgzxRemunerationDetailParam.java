package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 勤工助学报酬明细查询参数
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxRemunerationDetailParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 报酬申报ID
     */
    @QueryField(type = QueryType.EQ)
    private String remunerationApplyId;

    /**
     * 学生申请ID
     */
    @QueryField(type = QueryType.EQ)
    private String studentApplyId;

    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 工时来源类型
     */
    @QueryField(type = QueryType.EQ)
    private String hoursSourceType;

    /**
     * 工作时长（小时）
     */
    private BigDecimal workHours;

    /**
     * 时薪
     */
    private BigDecimal hourlyRate;

    /**
     * 报酬金额
     */
    private BigDecimal remunerationAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "CREATE_TIME desc";
    }
}
