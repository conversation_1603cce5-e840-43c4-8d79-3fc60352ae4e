package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.enums.InterviewResult;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 线下面试记录表
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_INTERVIEW_RECORD")
@Entity
@Table(name = "SYT_QGZX_INTERVIEW_RECORD")
public class QgzxInterviewRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学生申请ID
     */
    @Column(name = "STUDENT_APPLY_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("STUDENT_APPLY_ID")
    private String studentApplyId;

    /**
     * 面试时间
     */
    @Column(name = "INTERVIEW_TIME")
    @TableField("INTERVIEW_TIME")
    private LocalDateTime interviewTime;

    /**
     * 面试地点
     */
    @Column(name = "INTERVIEW_LOCATION", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("INTERVIEW_LOCATION")
    private String interviewLocation;

    /**
     * 面试官姓名
     */
    @Column(name = "INTERVIEWER_NAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("INTERVIEWER_NAME")
    private String interviewerName;

    /**
     * 面试官联系方式
     */
    @Column(name = "INTERVIEWER_CONTACT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("INTERVIEWER_CONTACT")
    private String interviewerContact;

    /**
     * 面试结果
     */
    @Column(name = "INTERVIEW_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("INTERVIEW_RESULT")
    private InterviewResult interviewResult;

    /**
     * 面试评分（1-10分）
     */
    @Column(name = "INTERVIEW_SCORE", columnDefinition = "NUMBER(3,1)")
    @TableField("INTERVIEW_SCORE")
    @DecimalMin(value = "1.0", message = "面试评分不能小于1分")
    @DecimalMax(value = "10.0", message = "面试评分不能大于10分")
    private Double interviewScore;

    /**
     * 面试评价
     */
    @Column(name = "INTERVIEW_COMMENT", columnDefinition = ColumnType.VARCHAR2_1024)
    @TableField("INTERVIEW_COMMENT")
    private String interviewComment;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 创建人（面试官工号）
     */
    @Column(name = "CREATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_BY", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    // 关联对象（不存储到数据库）
    
    /**
     * 学生申请信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxStudentApply studentApply;

    /**
     * 岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication jobApplication;

    /**
     * 学生信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

    /**
     * 用人单位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxEmployer employer;
}
