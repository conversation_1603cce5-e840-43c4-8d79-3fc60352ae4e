package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.*;
import com.sanythadmin.project.workstudy.enums.AdjustmentStatus;
import com.sanythadmin.project.workstudy.mapper.*;
import com.sanythadmin.project.workstudy.param.QgzxJobAdjustmentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxJobAdjustmentApplyService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 岗位调剂申请Service实现
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class QgzxJobAdjustmentApplyServiceImpl extends ServiceImpl<QgzxJobAdjustmentApplyMapper, QgzxJobAdjustmentApply> implements QgzxJobAdjustmentApplyService {

    private final QgzxJobAdjustmentApplyMapper qgzxJobAdjustmentApplyMapper;
    private final QgzxStudentApplyMapper qgzxStudentApplyMapper;
    private final QgzxJobApplicationMapper qgzxJobApplicationMapper;
    private final QgzxEmployerMapper qgzxEmployerMapper;
    private final UserInfoMapper userInfoMapper;

    @Override
    public PageResult<QgzxJobAdjustmentApply> pageList(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<QgzxJobAdjustmentApply, QgzxJobAdjustmentApplyParam> wrapper = buildQueryWrapper(param, userInfoParam);
        Page<QgzxJobAdjustmentApply> page = wrapper.getPage();
        page = page(page, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxJobAdjustmentApply> listApply(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<QgzxJobAdjustmentApply, QgzxJobAdjustmentApplyParam> wrapper = buildQueryWrapper(param, userInfoParam);
        return list(wrapper);
    }

    @Override
    @Transactional
    public void applyAdjustment(String studentApplyId, String toJobId, String adjustReason) {
        String username = SecurityUtil.getUsername();
        
        String checkResult = checkCanApplyAdjustment(studentApplyId, toJobId);
        AssertUtil.isTrue(StringUtils.isEmpty(checkResult), checkResult);
        
        QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(studentApplyId);
        AssertUtil.isTrue(studentApply != null, "学生申请不存在");

        QgzxJobApplication toJob = qgzxJobApplicationMapper.selectById(toJobId);
        AssertUtil.isTrue(toJob != null, "目标岗位不存在");

        QgzxJobApplication fromJob = qgzxJobApplicationMapper.selectById(studentApply.getJobId());
        AssertUtil.isTrue(fromJob != null, "原岗位不存在");

        QgzxEmployer fromEmployer = qgzxEmployerMapper.selectById(fromJob.getEid());
        AssertUtil.isTrue(fromEmployer != null && Objects.equals(fromEmployer.getXgh(), username), 
                "只有原岗位的用人单位负责人才能申请调剂");
        
        // 创建调剂申请
        QgzxJobAdjustmentApply adjustmentApply = new QgzxJobAdjustmentApply();
        adjustmentApply.setStudentApplyId(studentApplyId);
        adjustmentApply.setFromJobId(studentApply.getJobId());
        adjustmentApply.setToJobId(toJobId);
        adjustmentApply.setApplyUser(username);
        adjustmentApply.setApplyTime(LocalDateTime.now());
        adjustmentApply.setAdjustReason(adjustReason);
        adjustmentApply.setStatus(AdjustmentStatus.DaiJieShouDanWeiQueRen);
        adjustmentApply.setCreateTime(LocalDateTime.now());
        adjustmentApply.setUpdateTime(LocalDateTime.now());
        
        save(adjustmentApply);
        
        studentApply.setTjzt(AdjustmentStatus.DaiJieShouDanWeiQueRen);
        studentApply.setOriginalJobId(studentApply.getJobId());
        studentApply.setTjsqsj(LocalDateTime.now());
        qgzxStudentApplyMapper.updateById(studentApply);
    }

    @Override
    @Transactional
    public void confirmByReceiver(String adjustmentApplyId, String result, String comment) {
        String username = SecurityUtil.getUsername();
        
        QgzxJobAdjustmentApply adjustmentApply = getById(adjustmentApplyId);
        AssertUtil.isTrue(adjustmentApply != null, "调剂申请不存在");
        AssertUtil.isTrue(AdjustmentStatus.DaiJieShouDanWeiQueRen.equals(adjustmentApply.getStatus()), 
                "当前状态不允许接收单位确认");
        
        QgzxJobApplication toJob = qgzxJobApplicationMapper.selectById(adjustmentApply.getToJobId());
        AssertUtil.isTrue(toJob != null, "目标岗位不存在");

        QgzxEmployer toEmployer = qgzxEmployerMapper.selectById(toJob.getEid());
        AssertUtil.isTrue(toEmployer != null && Objects.equals(toEmployer.getXgh(), username), 
                "只有目标岗位的用人单位负责人才能确认接收");
        
        ReviewResult reviewResult = ReviewResult.valueOf(result);
        
        // 更新调剂申请
        adjustmentApply.setReceiverUser(username);
        adjustmentApply.setReceiverTime(LocalDateTime.now());
        adjustmentApply.setReceiverResult(reviewResult);
        adjustmentApply.setReceiverComment(comment);
        adjustmentApply.setUpdateTime(LocalDateTime.now());
        
        if (ReviewResult.TongGuo.equals(reviewResult)) {
            adjustmentApply.setStatus(AdjustmentStatus.DaiXueShengQueRen);
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(adjustmentApply.getStudentApplyId());
            studentApply.setTjzt(AdjustmentStatus.DaiXueShengQueRen);
            studentApply.setTjqrsj(LocalDateTime.now());
            qgzxStudentApplyMapper.updateById(studentApply);
        } else {
            adjustmentApply.setStatus(AdjustmentStatus.TiaoJiBeiJuJue);
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(adjustmentApply.getStudentApplyId());
            studentApply.setTjzt(AdjustmentStatus.WuTiaoJi);
            studentApply.setOriginalJobId(null);
            studentApply.setTjsqsj(null);
            studentApply.setTjqrsj(null);
            qgzxStudentApplyMapper.updateById(studentApply);
        }
        
        updateById(adjustmentApply);
    }

    @Override
    @Transactional
    public void confirmByStudent(String adjustmentApplyId, String result, String comment) {
        String username = SecurityUtil.getUsername();
        
        QgzxJobAdjustmentApply adjustmentApply = getById(adjustmentApplyId);
        AssertUtil.isTrue(adjustmentApply != null, "调剂申请不存在");
        AssertUtil.isTrue(AdjustmentStatus.DaiXueShengQueRen.equals(adjustmentApply.getStatus()), 
                "当前状态不允许学生确认");
        
        QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(adjustmentApply.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null && Objects.equals(studentApply.getXgh(), username),
                "只有申请的学生才能确认调剂");
        
        ReviewResult reviewResult = ReviewResult.valueOf(result);
        
        adjustmentApply.setStudentUser(username);
        adjustmentApply.setStudentTime(LocalDateTime.now());
        adjustmentApply.setStudentResult(reviewResult);
        adjustmentApply.setStudentComment(comment);
        adjustmentApply.setUpdateTime(LocalDateTime.now());
        
        if (ReviewResult.TongGuo.equals(reviewResult)) {
            adjustmentApply.setStatus(AdjustmentStatus.TiaoJiWanCheng);
            studentApply.setJobId(adjustmentApply.getToJobId());
            studentApply.setTjzt(AdjustmentStatus.TiaoJiWanCheng);
            studentApply.setXsqrsj(LocalDateTime.now());
            qgzxStudentApplyMapper.updateById(studentApply);
        } else {
            adjustmentApply.setStatus(AdjustmentStatus.TiaoJiBeiJuJue);
            studentApply.setTjzt(AdjustmentStatus.WuTiaoJi);
            studentApply.setOriginalJobId(null);
            studentApply.setTjsqsj(null);
            studentApply.setTjqrsj(null);
            qgzxStudentApplyMapper.updateById(studentApply);
        }
        
        updateById(adjustmentApply);
    }

    @Override
    public QgzxJobAdjustmentApply getByStudentApplyId(String studentApplyId) {
        return qgzxJobAdjustmentApplyMapper.selectByStudentApplyId(studentApplyId);
    }

    @Override
    public PageResult<QgzxJobAdjustmentApply> pagePendingForEmployer(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        String username = SecurityUtil.getUsername();
        
        // 查询当前用户负责的岗位
        LambdaQueryWrapper<QgzxEmployer> employerWrapper = new LambdaQueryWrapper<>();
        employerWrapper.eq(QgzxEmployer::getXgh, username);
        QgzxEmployer employer = qgzxEmployerMapper.selectOne(employerWrapper);
        AssertUtil.isTrue(employer != null, "当前用户不是用人单位负责人");
        
        param.setStatus(AdjustmentStatus.DaiJieShouDanWeiQueRen);
        param.setEmployerId(employer.getId());
        
        return pageList(param, userInfoParam);
    }

    @Override
    public PageResult<QgzxJobAdjustmentApply> pagePendingForStudent(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        String username = SecurityUtil.getUsername();
        param.setStatus(AdjustmentStatus.DaiXueShengQueRen);
        param.setXgh(username);
        
        return pageList(param, userInfoParam);
    }

    @Override
    public QgzxJobAdjustmentApply getDetail(String id) {
        QgzxJobAdjustmentApply adjustmentApply = getById(id);
        if (adjustmentApply == null) {
            return null;
        }
        loadAssociations(adjustmentApply);
        
        return adjustmentApply;
    }

    @Override
    public String checkCanApplyAdjustment(String studentApplyId, String toJobId) {
        QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(studentApplyId);
        if (studentApply == null) {
            return "学生申请不存在";
        }
        if (!ReviewResult.TongGuo.equals(studentApply.getSpzt())) {
            return "只能对已通过审核的学生申请进行调剂";
        }
        if (!AdjustmentStatus.WuTiaoJi.equals(studentApply.getTjzt())) {
            return "该学生申请已有调剂申请，不能重复申请";
        }

        QgzxJobApplication toJob = qgzxJobApplicationMapper.selectById(toJobId);
        if (toJob == null) {
            return "目标岗位不存在";
        }

        if (!ReviewResult.TongGuo.equals(toJob.getSpzt())) {
            return "目标岗位未通过审核，不能调剂";
        }

        if (Objects.equals(studentApply.getJobId(), toJobId)) {
            return "不能调剂到同一个岗位";
        }

        return null;
    }

    private MyMPJLambdaWrapper<QgzxJobAdjustmentApply, QgzxJobAdjustmentApplyParam> buildQueryWrapper(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<QgzxJobAdjustmentApply, QgzxJobAdjustmentApplyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAll(QgzxJobAdjustmentApply.class)
                .leftJoin(QgzxStudentApply.class, QgzxStudentApply::getId, QgzxJobAdjustmentApply::getStudentApplyId)
                .leftJoin(QgzxJobApplication.class, "fromJob", QgzxJobApplication::getId, QgzxJobAdjustmentApply::getFromJobId)
                .leftJoin(QgzxJobApplication.class, "toJob", QgzxJobApplication::getId, QgzxJobAdjustmentApply::getToJobId)
                .leftJoin(UserInfo.class, "student", UserInfo::getXgh, QgzxStudentApply::getXgh)
                .select("fromJob.JOB_NAME as fromJobName")
                .select("toJob.JOB_NAME as toJobName")
                .select("student.XM as studentName");
        if (StringUtils.isNotEmpty(param.getStatus().name())) {
            wrapper.eq(QgzxJobAdjustmentApply::getStatus, param.getStatus());
        }
        
        if (StringUtils.isNotEmpty(param.getXgh())) {
            wrapper.eq(QgzxStudentApply::getXgh, param.getXgh());
        }
        
        if (StringUtils.isNotEmpty(param.getEmployerId())) {
            wrapper.and(w -> w.eq("fromJob.EID", param.getEmployerId()).or().eq("toJob.EID", param.getEmployerId()));
        }
        
        wrapper.orderByDesc(QgzxJobAdjustmentApply::getCreateTime);
        
        return wrapper;
    }

    private void loadAssociations(QgzxJobAdjustmentApply adjustmentApply) {
        if (StringUtils.isNotEmpty(adjustmentApply.getStudentApplyId())) {
            QgzxStudentApply studentApply = qgzxStudentApplyMapper.selectById(adjustmentApply.getStudentApplyId());
            adjustmentApply.setStudentApply(studentApply);
            if (studentApply != null && StringUtils.isNotEmpty(studentApply.getXgh())) {
                UserInfo studentInfo = userInfoMapper.selectById(studentApply.getXgh());
                adjustmentApply.setStudentUserInfo(studentInfo);
            }
        }

        if (StringUtils.isNotEmpty(adjustmentApply.getFromJobId())) {
            QgzxJobApplication fromJob = qgzxJobApplicationMapper.selectById(adjustmentApply.getFromJobId());
            adjustmentApply.setFromJobApplication(fromJob);
        }

        if (StringUtils.isNotEmpty(adjustmentApply.getToJobId())) {
            QgzxJobApplication toJob = qgzxJobApplicationMapper.selectById(adjustmentApply.getToJobId());
            adjustmentApply.setToJobApplication(toJob);
        }
        
        if (StringUtils.isNotEmpty(adjustmentApply.getApplyUser())) {
            UserInfo applyUserInfo = userInfoMapper.selectById(adjustmentApply.getApplyUser());
            adjustmentApply.setApplyUserInfo(applyUserInfo);
        }
        if (StringUtils.isNotEmpty(adjustmentApply.getReceiverUser())) {
            UserInfo receiverUserInfo = userInfoMapper.selectById(adjustmentApply.getReceiverUser());
            adjustmentApply.setReceiverUserInfo(receiverUserInfo);
        }
    }
}
