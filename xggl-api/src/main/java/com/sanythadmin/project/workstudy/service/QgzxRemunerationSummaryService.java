package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationSummaryParam;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryDetailVO;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 勤工助学报酬月度汇总服务
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface QgzxRemunerationSummaryService extends IService<QgzxRemunerationApply> {

    /**
     * 获取月度汇总数据
     *
     * @param param 查询参数
     * @return 汇总数据
     */
    QgzxRemunerationSummaryVO getMonthlySummary(QgzxRemunerationSummaryParam param);

    /**
     * 获取月度明细数据
     *
     * @param param 查询参数
     * @return 明细数据列表
     */
    List<QgzxRemunerationSummaryDetailVO> getMonthlyDetails(QgzxRemunerationSummaryParam param);

    /**
     * 分页查询汇总列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxRemunerationSummaryVO> pageSummaryList(QgzxRemunerationSummaryParam param);

    /**
     * 导出汇总数据
     *
     * @param param 查询参数
     * @return 导出数据
     */
    QgzxRemunerationSummaryVO exportSummaryData(QgzxRemunerationSummaryParam param);

    /**
     * 按岗位类别分组统计
     *
     * @param param 查询参数
     * @return 分组统计结果
     */
    List<QgzxRemunerationSummaryDetailVO> getSummaryByJobType(QgzxRemunerationSummaryParam param);

    /**
     * 按用人单位分组统计
     *
     * @param param 查询参数
     * @return 分组统计结果
     */
    List<QgzxRemunerationSummaryDetailVO> getSummaryByEmployer(QgzxRemunerationSummaryParam param);

    /**
     * 按学院分组统计
     *
     * @param param 查询参数
     * @return 分组统计结果
     */
    List<QgzxRemunerationSummaryDetailVO> getSummaryByCollege(QgzxRemunerationSummaryParam param);

    /**
     * 验证查询参数
     *
     * @param param 查询参数
     */
    void validateSummaryParam(QgzxRemunerationSummaryParam param);

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return 查询条件字符串
     */
    String buildQueryConditions(QgzxRemunerationSummaryParam param);

    /**
     * 模板导出主方法
     *
     * @param param 查询参数
     * @param templateFile 模板文件信息
     * @param response HTTP响应
     */
    void exportByTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response);

    /**
     * 汇总表模板导出
     *
     * @param param 查询参数
     * @param templateFile 模板文件信息
     * @param response HTTP响应
     */
    void exportSummaryTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response);

    /**
     * 明细表模板导出
     *
     * @param param 查询参数
     * @param templateFile 模板文件信息
     * @param response HTTP响应
     */
    void exportDetailTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response);
}
