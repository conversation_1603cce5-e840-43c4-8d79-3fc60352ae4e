package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/岗位申报
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-job-apply")
@RequiredArgsConstructor
public class QgzxJobApplyController extends BaseController {

    private final QgzxJobApplicationService qgzxJobApplicationService;

    /**
     * 申报人分页查询岗位（权限标识：workstudy:qgzxJobApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:list')")
    @GetMapping("/page")
    public PageResult<QgzxJobApplication> page(QgzxJobApplicationParam param,
                                               QgzxEmployerParam employerParam) {
        employerParam.setXgh(SecurityUtil.getUsername());
        return qgzxJobApplicationService.pageByApplicantList(param,employerParam);
    }

    /**
     * 申报人查询全部岗位（权限标识：workstudy:qgzxJobApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:list')")
    @GetMapping()
    public List<QgzxJobApplication> list(QgzxJobApplicationParam param,
                                         QgzxEmployerParam employerParam) {
        employerParam.setXgh(SecurityUtil.getUsername());
        return qgzxJobApplicationService.listApplicantList(param,employerParam);
    }

    /**
     * 根据id查询岗位申报（权限标识：workstudy:qgzxJobApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:list')")
    @GetMapping("/{id}")
    public QgzxJobApplication get(@PathVariable("id") String id) {
        return qgzxJobApplicationService.getQgzxJobApplication(id);
    }

    /**
     * 申报人编辑岗位申报（包含地址）（权限标识：workstudy:qgzxJobApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:operation')")
    @OperationLog(module = "岗位申报", comments = "保存岗位申报（包含地址）")
    @PostMapping("/operation-with-addresses")
    public void saveWithAddresses(@RequestBody QgzxJobApplication jobApplication) {
        qgzxJobApplicationService.editByApplicant(jobApplication);
    }

    /**
     * 申报人批量删除岗位（权限标识：workstudy:qgzxJobApply:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:remove')")
    @OperationLog(module = "岗位申报", comments = "批量删除岗位申报")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        qgzxJobApplicationService.deleteByApplicant(ids.toArray(new String[]{}));
    }
}
