package com.sanythadmin.project.workstudy.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 勤工助学报酬历史数据导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
public class QgzxRemunerationImportDTO {

    /**
     * 学号（必填）
     */
    @NotBlank(message = "学号不能为空")
    @Pattern(regexp = "^[0-9]{10,20}$", message = "学号格式不正确")
    @ExcelProperty("学号")
    private String xgh;

    /**
     * 学生姓名（必填）
     */
    @NotBlank(message = "学生姓名不能为空")
    @ExcelProperty("学生姓名")
    private String studentName;

    /**
     * 岗位名称（必填）
     */
    @NotBlank(message = "岗位名称不能为空")
    @ExcelProperty("岗位名称")
    private String jobName;

    /**
     * 用人单位名称（必填）
     */
    @NotBlank(message = "用人单位名称不能为空")
    @ExcelProperty("用人单位名称")
    private String employerName;

    /**
     * 岗位类别名称（必填）
     */
    @NotBlank(message = "岗位类别名称不能为空")
    @ExcelProperty("岗位类别名称")
    private String jobTypeName;

    /**
     * 申报年月（必填，格式：YYYY-MM）
     */
    @NotBlank(message = "申报年月不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}$", message = "申报年月格式不正确，应为YYYY-MM格式")
    @ExcelProperty("申报年月")
    private String sbny;

    /**
     * 工作时长（必填，单位：小时）
     */
    @NotNull(message = "工作时长不能为空")
    @DecimalMin(value = "0.1", message = "工作时长必须大于0")
    @DecimalMax(value = "999.9", message = "工作时长不能超过999.9小时")
    @ExcelProperty("工作时长")
    private BigDecimal workHours;

    /**
     * 时薪（必填，单位：元/小时）
     */
    @NotNull(message = "时薪不能为空")
    @DecimalMin(value = "0.01", message = "时薪必须大于0")
    @DecimalMax(value = "999.99", message = "时薪不能超过999.99元")
    @ExcelProperty("时薪")
    private BigDecimal hourlyRate;

    /**
     * 报酬金额（必填，单位：元）
     */
    @NotNull(message = "报酬金额不能为空")
    @DecimalMin(value = "0.01", message = "报酬金额必须大于0")
    @DecimalMax(value = "99999.99", message = "报酬金额不能超过99999.99元")
    @ExcelProperty("报酬金额")
    private BigDecimal remunerationAmount;

    /**
     * 工作描述（可选）
     */
    @Size(max = 500, message = "工作描述不能超过500个字符")
    @ExcelProperty("工作描述")
    private String workDescription;

    /**
     * 学年学期（可选）
     */
    @ExcelProperty("学年学期")
    private String xnxq;

    /**
     * 学院名称（可选）
     */
    @ExcelProperty("学院名称")
    private String collegeName;

    /**
     * 专业名称（可选）
     */
    @ExcelProperty("专业名称")
    private String majorName;

    /**
     * 班级名称（可选）
     */
    @ExcelProperty("班级名称")
    private String className;

    /**
     * 备注（可选）
     */
    @Size(max = 200, message = "备注不能超过200个字符")
    @ExcelProperty("备注")
    private String remark;

    /**
     * 岗位ID（验证后设置）
     */
    private String jobId;

    /**
     * 学生申请ID（验证后设置）
     */
    private String studentApplyId;

    /**
     * 用人单位ID（验证后设置）
     */
    private String employerId;

    /**
     * 岗位类别ID（验证后设置）
     */
    private String jobTypeId;

    /**
     * 学院ID（验证后设置）
     */
    private String xyid;

    /**
     * 验证是否通过
     */
    private boolean valid = true;

    /**
     * 验证错误信息
     */
    private String errorMessage;

    /**
     * 行号（用于错误定位）
     */
    private Integer rowNumber;
}
