<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxEmployerMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, BZ, LXR, LXRDH, BGDH, FZR, CREATE_TIME, EMAIL
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE concat(concat('%',#{param.bz), '%')
                    </if>
                    <if test="param.lxr != null">
                        AND a.LXR LIKE concat(concat('%',#{param.lxr), '%')
                    </if>
                    <if test="param.lxrdh != null">
                        AND a.LXRDH LIKE concat(concat('%',#{param.lxrdh), '%')
                    </if>
                    <if test="param.bgdh != null">
                        AND a.BGDH LIKE concat(concat('%',#{param.bgdh), '%')
                    </if>
                    <if test="param.fzr != null">
                        AND a.FZR LIKE concat(concat('%',#{param.fzr), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.email != null">
                        AND a.EMAIL LIKE concat(concat('%',#{param.email), '%')
                    </if>
    </sql>
</mapper>
