package com.sanythadmin.project.workstudy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 勤工助学岗位地址Mapper
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface QgzxJobApplicationAddressMapper extends BaseMapper<QgzxJobApplicationAddress> {

    /**
     * 根据岗位ID删除地址
     *
     * @param jobId 岗位ID
     */
    @Delete("DELETE FROM SYT_QGZX_JOB_APPLICATION_ADDRESS WHERE JOB_ID = #{jobId}")
    void deleteByJobId(@Param("jobId") String jobId);

    /**
     * 根据岗位ID查询地址列表
     *
     * @param jobId 岗位ID
     * @return 地址列表
     */
    @Select("SELECT * FROM SYT_QGZX_JOB_APPLICATION_ADDRESS WHERE JOB_ID = #{jobId} ORDER BY SORT ASC, CREATE_TIME DESC")
    List<QgzxJobApplicationAddress> selectByJobId(@Param("jobId") String jobId);

    /**
     * 根据岗位ID查询启用的地址列表
     *
     * @param jobId 岗位ID
     * @return 启用的地址列表
     */
    @Select("SELECT * FROM SYT_QGZX_JOB_APPLICATION_ADDRESS WHERE JOB_ID = #{jobId} AND STATUS = 1 ORDER BY SORT ASC, CREATE_TIME DESC")
    List<QgzxJobApplicationAddress> selectEnabledByJobId(@Param("jobId") String jobId);
}
