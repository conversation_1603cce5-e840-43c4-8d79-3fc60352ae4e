package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.workstudy.enums.AdjustmentStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位调剂申请查询参数
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxJobAdjustmentApplyParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学生申请ID
     */
    @QueryField(type = QueryType.EQ)
    private String studentApplyId;

    /**
     * 原岗位ID
     */
    @QueryField(type = QueryType.EQ)
    private String fromJobId;

    /**
     * 目标岗位ID
     */
    @QueryField(type = QueryType.EQ)
    private String toJobId;

    /**
     * 调剂申请人
     */
    @QueryField(type = QueryType.EQ)
    private String applyUser;

    /**
     * 调剂状态
     */
    @QueryField(type = QueryType.EQ)
    private AdjustmentStatus status;

    /**
     * 接收单位确认人
     */
    @QueryField(type = QueryType.EQ)
    private String receiverUser;

    /**
     * 学生确认人
     */
    @QueryField(type = QueryType.EQ)
    private String studentUser;

    /**
     * 申请时间开始
     */
    @QueryField(value = "APPLY_TIME", type = QueryType.GE)
    private String applyTimeStart;

    /**
     * 申请时间结束
     */
    @QueryField(value = "APPLY_TIME", type = QueryType.LE)
    private String applyTimeEnd;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 学号（用于关联查询）
     */
    private String xgh;

    /**
     * 岗位名称（用于关联查询）
     */
    private String jobName;

    /**
     * 用人单位ID（用于关联查询）
     */
    private String employerId;
}
