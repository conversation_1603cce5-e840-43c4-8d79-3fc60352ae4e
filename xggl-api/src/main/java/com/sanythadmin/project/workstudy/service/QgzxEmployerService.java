package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;

import java.util.List;

/**
 * 用人单位Service
 *
 * <AUTHOR>
 * @since 2025-07-14 13:48:13
 */
public interface QgzxEmployerService extends IService<QgzxEmployer> {

    QgzxEmployer saveQgzxEmployer(QgzxEmployerParam param);

    void removeQgzxEmployer(List<String> ids);

    QgzxEmployer getQgzxEmployer(String id);

    PageResult<QgzxEmployer> getQgzxEmployerPage(QgzxEmployerParam param, UserInfoParam userInfoParam);
}
