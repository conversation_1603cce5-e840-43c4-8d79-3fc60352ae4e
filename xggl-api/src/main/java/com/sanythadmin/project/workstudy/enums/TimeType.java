package com.sanythadmin.project.workstudy.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时间段类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Getter
@AllArgsConstructor
public enum TimeType implements TextBaseEnum {
    
    /**
     * 课表时间
     */
    COURSE("COURSE", "课表时间"),
    
    /**
     * 空闲时间
     */
    FREE("FREE", "空闲时间");

    @EnumValue
    private final String value;
    
    @JsonValue
    private final String text;

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据value获取枚举
     */
    public static TimeType getByValue(String value) {
        for (TimeType timeType : values()) {
            if (timeType.getValue().equals(value)) {
                return timeType;
            }
        }
        return null;
    }
}
