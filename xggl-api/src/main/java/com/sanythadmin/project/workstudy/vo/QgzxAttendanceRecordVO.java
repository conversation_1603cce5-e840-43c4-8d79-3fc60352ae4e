package com.sanythadmin.project.workstudy.vo;

import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 勤工助学考勤记录VO
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QgzxAttendanceRecordVO extends QgzxAttendanceRecord {

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 学生联系方式
     */
    private String studentContact;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 用人单位名称
     */
    private String employerName;

}
