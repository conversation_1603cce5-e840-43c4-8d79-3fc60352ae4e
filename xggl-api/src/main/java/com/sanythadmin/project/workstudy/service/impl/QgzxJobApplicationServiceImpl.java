package com.sanythadmin.project.workstudy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.form.entity.FormApprovalNode;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.mapper.*;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.constants.ConstantsWorkStudy;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.mapper.QgzxJobApplicationMapper;
import com.sanythadmin.project.workstudy.mapper.QgzxStudentApplyMapper;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationAddressService;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationService;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getUserInfo;

/**
 * 岗位申报Service实现
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
@Service
@AllArgsConstructor
public class QgzxJobApplicationServiceImpl extends ServiceImpl<QgzxJobApplicationMapper, QgzxJobApplication> implements QgzxJobApplicationService {

    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final WorkflowApprovalNodeMapper approvalNodeMapper;
    private final WorkflowApprovalNodeRecordMapper approvalNodeRecordMapper;
    private final UserInfoMapper userInfoMapper;
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;

    private final WorkflowService workflowService;
    private final SysModuleSetupService sysModuleSetupService;
    private final QgzxEmployerService qgzxEmployerService;
    private final UserInfoService userInfoService;
    private final SysParamService sysParamService;
    private final FormRestrictService formRestrictService;
    private final QgzxStudentApplyService qgzxStudentApplyservice;
    private final QgzxJobApplicationAddressService qgzxJobApplicationAddressService;

    @Override
    public String checkIsEdit(String applicationId) {
        return workflowService.checkApplicationInfoIsCanEdit(WorkflowApprovalNode.class, WorkflowApprovalNodeRecord.class, applicationId);
    }

    @Transactional
    @Override
    public void edit(QgzxJobApplication jobApplication) {
        UserInfo userInfo = getUserInfo();
        jobApplication.setXgh(userInfo.getXgh());
        // 设置申请时的角色ID
        jobApplication.setRoleId(SecurityUtil.getRoleId());
        // 设置项目名称为岗位名称
        jobApplication.setXxmc(jobApplication.getJobName());
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzxGwsb.getCode());
        AssertUtil.isTrue(StringUtils.isNoneBlank(jobApplication.getEid()),"用人单位不能为空");

        if (StringUtils.isNotBlank(jobApplication.getId())) {
            QgzxJobApplication qgzxJobApplication = jobApplicationMapper.selectById(jobApplication.getId());
            BeanUtil.copyProperties(jobApplication, qgzxJobApplication, "id");
            qgzxJobApplication.setSpzt(ReviewResult.TongGuo);
            jobApplicationMapper.updateById(qgzxJobApplication);
        } else {
            jobApplication.setCreateTime(LocalDateTime.now());
            jobApplication.setXnxq(year);
            jobApplication.setSpzt(ReviewResult.TongGuo);
            save(jobApplication);
//            List<QgzxJobApplicationAddress> jobAddresses = jobApplication.getJobAddresses();
        }
        if (CollectionUtils.isEmpty(jobApplication.getJobAddresses()))
            AssertUtil.throwMessage("岗位地址不能为空");
        qgzxJobApplicationAddressService.saveJobAddresses(jobApplication.getId(), jobApplication.getJobAddresses());
    }

    @Transactional
    @Override
    public void editByApplicant(QgzxJobApplication jobApplication) {
        UserInfo userInfo = getUserInfo();
        jobApplication.setXgh(userInfo.getXgh());
        // 设置申请时的角色ID
        jobApplication.setRoleId(SecurityUtil.getRoleId());
        // 设置项目名称为岗位名称
        jobApplication.setXxmc(jobApplication.getJobName());
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        if (StringUtils.isNotBlank(jobApplication.getId())) {
            QgzxJobApplication qgzxJobApplication = jobApplicationMapper.selectById(jobApplication.getId());
            checkIsEdit(jobApplication.getId());
            BeanUtil.copyProperties(jobApplication, qgzxJobApplication, "id");
            jobApplicationMapper.updateById(qgzxJobApplication);
        } else {
            jobApplication.setCreateTime(LocalDateTime.now());
            jobApplication.setXnxq(year);
            QgzxEmployer employer = qgzxEmployerService.getOne(new LambdaQueryWrapper<QgzxEmployer>().eq(QgzxEmployer::getXgh, userInfo.getXgh()));
            AssertUtil.isTrue(employer != null, "未找到用人单位");
            jobApplication.setEid(employer.getId());
            jobApplication.setSpzt(ReviewResult.TongGuo);
            save(jobApplication);
            String sfsh = sysParamService.getParamValue(ConstantsWorkStudy.GANGWEI_SHIFOU_SHENHE);
            if (JudgeMark.YES.getText().equals(sfsh) || StringUtils.isEmpty(sfsh)) {
                String workflowId = workflowService.createApprovalNode(WorkflowApprovalNode.class, jobApplication,
                        new WorkflowParam(null, year, SysModule.qgzxGwsb.getCode()), userInfo);
                jobApplication.setWorkflowId(workflowId);
                jobApplication.setSpzt(ReviewResult.DaiShenPi);
                jobApplicationMapper.updateById(jobApplication);
            }
        }
        if (CollectionUtils.isEmpty(jobApplication.getJobAddresses()))
            AssertUtil.throwMessage("岗位地址不能为空");
        qgzxJobApplicationAddressService.saveJobAddresses(jobApplication.getId(), jobApplication.getJobAddresses());
    }

    @Transactional
    @Override
    public void delete(String... ids) {
        SysAccount account = SecurityUtil.getAccount();
        String approverId = CommonUtil.appendComma(account.getUsername(), account.getRole().getId());
        String year = sysModuleSetupService.getModuleYear(SysModule.qgzxGwsb.getCode());
        for (String id : ids) {
            String workflowId = workflowService.checkApplicationInfoIsCanDelete(FormApprovalNode.class,FormApprovalNodeRecord.class,SysModule.qgzxGwsb.getCode(),id, year,approverId);
            if (workflowId != null) {
                QgzxJobApplication jobApplication = getById(id);
                delete(jobApplication, workflowId);
            }
        }

    }

    @Transactional
    @Override
    public void deleteByApplicant(String... ids) {
        SysAccount account = SecurityUtil.getAccount();
        for (String id : ids) {
            QgzxJobApplication jobApplication = getById(id);
            QgzxEmployer employer = qgzxEmployerService.getById(jobApplication.getEid());
            if (Objects.equals(account.getUsername(), employer.getXgh())) { // 申请者删除申请信息
                String workflowId = checkIsEdit(id);
                delete(jobApplication, workflowId);
            }
        }
    }
    private void delete(QgzxJobApplication info, String workflowId) {
        approvalNodeRecordMapper.delete(new LambdaQueryWrapper<WorkflowApprovalNodeRecord>()
                .eq(WorkflowApprovalNodeRecord::getApplicationId, info.getId()).eq(WorkflowApprovalNodeRecord::getWorkflowId, workflowId));
        approvalNodeMapper.delete(new LambdaQueryWrapper<WorkflowApprovalNode>().eq(WorkflowApprovalNode::getApplicationId, info.getId()).
                eq(WorkflowApprovalNode::getWorkflowId, workflowId));

        // 删除岗位相关地址
        qgzxJobApplicationAddressService.deleteByJobId(info.getId());

        jobApplicationMapper.deleteById(info.getId());
    }


    @Override
    public PageResult<QgzxJobApplication> pageByApplicantList(QgzxJobApplicationParam param,QgzxEmployerParam employerParam) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                        emp->emp.association(UserInfo.class,QgzxEmployer::getUserInfo))
                .leftJoin(QgzxEmployer.class,"e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .innerJoin(UserInfo.class,UserInfo::getXgh, QgzxEmployer::getXgh);
        wrapper.buildQueryCondition("e", employerParam);
        Page<QgzxJobApplication> page = wrapper.getPage();
        page = page(page, wrapper);

        // 批量加载地址信息
        List<QgzxJobApplication> records = page.getRecords();
        if (!records.isEmpty()) {
            List<String> jobIds = records.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            Map<String, List<QgzxJobApplicationAddress>> addressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);

            for (QgzxJobApplication job : records) {
                job.setJobAddresses(addressMap.getOrDefault(job.getId(), new ArrayList<>()));
            }
        }

        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public List<QgzxJobApplication> listApplicantList(QgzxJobApplicationParam param,QgzxEmployerParam employerParam) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                        emp->emp.association(UserInfo.class,QgzxEmployer::getUserInfo))
                .leftJoin(QgzxEmployer.class,"e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .innerJoin(UserInfo.class,UserInfo::getXgh, QgzxEmployer::getXgh);
        List<QgzxJobApplication> jobList = list(wrapper);

        // 批量加载地址信息
        if (!jobList.isEmpty()) {
            List<String> jobIds = jobList.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            Map<String, List<QgzxJobApplicationAddress>> addressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);

            for (QgzxJobApplication job : jobList) {
                job.setJobAddresses(addressMap.getOrDefault(job.getId(), new ArrayList<>()));
            }
        }

        return jobList;
    }

    @Override
    public QgzxJobApplication getQgzxJobApplication(String id) {
        QgzxJobApplication jobApplication = getById(id);
        AssertUtil.isTrue(jobApplication!=null,"岗位不存在");
        QgzxEmployer qgzxEmployer = qgzxEmployerService.getById(jobApplication.getEid());
        qgzxEmployer.setUserInfo(userInfoService.get(qgzxEmployer.getXgh()));
        jobApplication.setEmployer(qgzxEmployer);

        // 加载岗位地址信息
        List<QgzxJobApplicationAddress> addresses = qgzxJobApplicationAddressService.getByJobId(id);
        jobApplication.setJobAddresses(addresses);

        return jobApplication;
    }

    @Override
    public List<Map<String, Object>> distinct(SFunction<QgzxJobApplication, String> field) {
        return jobApplicationMapper.selectMaps(new LambdaQueryWrapper<QgzxJobApplication>().select(field).groupBy(field).orderByDesc(field));
    }

    @Override
    public List<QgzxJobApplication> listApprovalList(QgzxJobApplicationParam param, QgzxEmployerParam employerParam, WorkflowApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = approvalListQueryWrapper(param, employerParam, approvalNodeParam);
        List<QgzxJobApplication> jobList = list(wrapper);

        if (!jobList.isEmpty()) {
            List<String> jobIds = jobList.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            Map<String, List<QgzxJobApplicationAddress>> addressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);

            for (QgzxJobApplication job : jobList) {
                job.setJobAddresses(addressMap.getOrDefault(job.getId(), new ArrayList<>()));
            }
        }

        return jobList;
    }

    @Override
    public PageResult<QgzxJobApplication> pageApprovalList(QgzxJobApplicationParam param, QgzxEmployerParam employerParam, WorkflowApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = approvalListQueryWrapper(param, employerParam, approvalNodeParam);
        Page<QgzxJobApplication> page = wrapper.getPage();
        page = page(page, wrapper);

        List<QgzxJobApplication> records = page.getRecords();
        if (!records.isEmpty()) {
            List<String> jobIds = records.stream().map(QgzxJobApplication::getId).collect(Collectors.toList());
            Map<String, List<QgzxJobApplicationAddress>> addressMap = qgzxJobApplicationAddressService.getByJobIds(jobIds);

            for (QgzxJobApplication job : records) {
                job.setJobAddresses(addressMap.getOrDefault(job.getId(), new ArrayList<>()));
            }
        }

        return new PageResult<>(records, page.getTotal());
    }

    public MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> approvalListQueryWrapper(QgzxJobApplicationParam param, QgzxEmployerParam employerParam, WorkflowApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                        emp->emp.association(UserInfo.class,QgzxEmployer::getUserInfo))
                .leftJoin(QgzxEmployer.class,"e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .innerJoin(UserInfo.class,UserInfo::getXgh, QgzxEmployer::getXgh);
        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzxGwsb.getCode()));
        }
        if (employerParam != null) {
            employerParam.setId(null);
        }
        if (approvalNodeParam != null) {
            approvalNodeParam.setId(null);
        }

        wrapper.buildQueryCondition("e", employerParam);
        workflowService.getApprovalListSubQueryWrapper(wrapper, nodeApproverMapper, SysModule.qgzxGwsb.getCode(), param.getXnxq(), approvalNodeParam.getNodeId(), approvalNodeParam.getResult());
        return wrapper;
    }

    @Transactional
    @Override
    public CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxJobApplication application, Executor executor) {
        final QgzxJobApplication info = application == null ? jobApplicationMapper.selectById(record.getApplicationId()) : application;
        return CompletableFuture.supplyAsync(() -> {
            UserInfo userInfo = userInfoMapper.get(info.getXgh());
            ApprovalInfo<WorkflowApprovalNode, WorkflowApprovalNodeRecord> approvalInfo = workflowService.approve(WorkflowApprovalNode.class,
                    record, info, new WorkflowParam(info.getWorkflowId()), userInfo);
            jobApplicationMapper.update(new LambdaUpdateWrapper<QgzxJobApplication>()
                    .set(QgzxJobApplication::getSpzt, approvalInfo.getReviewResult())
                    .eq(QgzxJobApplication::getId, info.getId()));
            return approvalInfo.getNextApprovableNodeId();
        }, executor);
    }

    @Override
    public PageResult<QgzxJobApplicationVO> pageByStudent(QgzxJobApplicationParam param) {
        String username = SecurityUtil.getUsername();
        UserInfo userInfo = userInfoMapper.selectById(username);
        param.setSpzt(ReviewResult.TongGuo);

        // 查询所有符合条件的岗位
        MyMPJLambdaWrapper<QgzxJobApplication, QgzxJobApplicationParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.selectAssociation("e", QgzxEmployer.class, QgzxJobApplication::getEmployer,
                        emp->emp.association(UserInfo.class,QgzxEmployer::getUserInfo))
                .leftJoin(QgzxEmployer.class,"e", QgzxEmployer::getId, QgzxJobApplication::getEid)
                .innerJoin(UserInfo.class,UserInfo::getXgh, QgzxEmployer::getXgh);
        
        List<QgzxJobApplication> jobApplications = list(wrapper);

        List<QgzxJobApplication> filteredList = new ArrayList<>();
        for (QgzxJobApplication job : jobApplications) {
            // 检查申请时间
            String error = qgzxStudentApplyservice.checkApplyTime(job);
            if (StringUtils.isNotEmpty(error)) continue;
            
            // 检查申请限制
            error = qgzxStudentApplyservice.checkLimit(username, job);
            if (StringUtils.isNotEmpty(error)) continue;
            
            // 检查限制条件
            error = formRestrictService.restrictCheck(job.getId(), userInfo, false);
            if (StringUtils.isNotEmpty(error)) continue;
            
            filteredList.add(job);
        }
        int total = filteredList.size();
        filteredList = CommonUtil.pageList(filteredList, param.getPage().intValue(), param.getLimit().intValue());
        
        List<QgzxJobApplicationVO> voList = new ArrayList<>();
        for (QgzxJobApplication job : filteredList) {
            // 查询申请人数
            Long applyCount = studentApplyMapper.selectCount(new LambdaQueryWrapper<QgzxStudentApply>()
                    .eq(QgzxStudentApply::getJobId, job.getId())
                    .eq(QgzxStudentApply::getXnxq, job.getXnxq()));
            
            QgzxJobApplicationVO vo = new QgzxJobApplicationVO();
            BeanUtils.copyProperties(job, vo);
            vo.setApplyCount(applyCount);
            voList.add(vo);
        }
        
        return new PageResult<>(voList, (long) total);
    }

    @Override
    public List<QgzxJobApplicationVO> listByStudent(QgzxJobApplicationParam param) {
        PageResult<QgzxJobApplicationVO> pageResult = pageByStudent(param);
        return pageResult.getList();
    }

}
