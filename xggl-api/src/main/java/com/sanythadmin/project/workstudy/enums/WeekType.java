package com.sanythadmin.project.workstudy.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 周次类型枚举
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Getter
@AllArgsConstructor
public enum WeekType {
    
    /**
     * 全部周次
     */
    ALL("ALL", "全部周次"),
    
    /**
     * 单周
     */
    ODD("ODD", "单周"),
    
    /**
     * 双周
     */
    EVEN("EVEN", "双周"),
    
    /**
     * 自定义周次
     */
    CUSTOM("CUSTOM", "自定义周次");

    @EnumValue
    @JsonValue
    private final String code;
    
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static WeekType getByCode(String code) {
        for (WeekType weekType : values()) {
            if (weekType.getCode().equals(code)) {
                return weekType;
            }
        }
        return null;
    }
}
