package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 勤工助学岗位地址查询参数
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxJobApplicationAddressParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 预设地址ID
     */
    @QueryField(type = QueryType.EQ)
    private String addressId;

    /**
     * 岗位申报ID
     */
    @QueryField(type = QueryType.EQ)
    private String jobId;

    /**
     * 地址名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 地址类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 范围/半径
     */
    private Integer fwbj;

    /**
     * 经纬度坐标
     */
    private String jwdzb;

    /**
     * 是否来源地址库
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark isFromMap;

    /**
     * 是否启用
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark status;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc, create_time desc";
    }
}
