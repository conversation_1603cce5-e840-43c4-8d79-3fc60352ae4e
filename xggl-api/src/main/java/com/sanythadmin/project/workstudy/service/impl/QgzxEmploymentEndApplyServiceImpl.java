package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.SysModule;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.SysModuleSetupService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workstudy.entity.QgzxEmploymentEndApply;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import com.sanythadmin.project.workstudy.mapper.QgzxEmploymentEndApplyMapper;
import com.sanythadmin.project.workstudy.param.QgzxEmploymentEndApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxEmploymentEndApplyService;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationService;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 用工结束申请Service实现
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class QgzxEmploymentEndApplyServiceImpl extends ServiceImpl<QgzxEmploymentEndApplyMapper, QgzxEmploymentEndApply> implements QgzxEmploymentEndApplyService {

    private final QgzxEmploymentEndApplyMapper employmentEndApplyMapper;
    private final UserInfoMapper userInfoMapper;
    private final QgzxStudentApplyService studentApplyService;
    private final QgzxJobApplicationService jobApplicationService;
    private final UserInfoService userInfoService;
    private final WorkflowService workflowService;
    private final SysModuleSetupService sysModuleSetupService;
    private final WorkflowNodeApproverMapper nodeApproverMapper;

    @Override
    public PageResult<QgzxEmploymentEndApply> pageList(QgzxEmploymentEndApplyParam param) {
        MPJLambdaWrapper<QgzxEmploymentEndApply> wrapper = new MPJLambdaWrapper<QgzxEmploymentEndApply>()
                .selectAll(QgzxEmploymentEndApply.class)
                .leftJoin(QgzxStudentApply.class, QgzxStudentApply::getId, QgzxEmploymentEndApply::getStudentApplyId)
                .leftJoin(QgzxJobApplication.class, QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .leftJoin(UserInfo.class, UserInfo::getXgh, QgzxStudentApply::getXgh)
                .select(QgzxJobApplication::getJobName)
                .select(UserInfo::getXm)
                .orderByDesc(QgzxEmploymentEndApply::getCreateTime);

        if (StringUtils.hasText(param.getXgh())) {
            wrapper.eq("sa.XGH", param.getXgh());
        }
        if (StringUtils.hasText(param.getStudentApplyId())) {
            wrapper.eq(QgzxEmploymentEndApply::getStudentApplyId, param.getStudentApplyId());
        }
        if (StringUtils.hasText(param.getJobId())) {
            wrapper.eq("sa.JOB_ID", param.getJobId());
        }
        if (param.getSpzt() != null) {
            wrapper.eq(QgzxEmploymentEndApply::getSpzt, param.getSpzt());
        }
        
        // 处理LocalDate日期范围查询（解决Oracle日期匹配问题）
        if (param.getYgksrqStart() != null) {
            wrapper.ge(QgzxEmploymentEndApply::getYgksrq, param.getYgksrqStart().atStartOfDay());
        }
        if (param.getYgksrqEnd() != null) {
            wrapper.le(QgzxEmploymentEndApply::getYgksrq, param.getYgksrqEnd().atTime(23, 59, 59));
        }
        if (param.getYgjsrqStart() != null) {
            wrapper.ge(QgzxEmploymentEndApply::getYgjsrq, param.getYgjsrqStart().atStartOfDay());
        }
        if (param.getYgjsrqEnd() != null) {
            wrapper.le(QgzxEmploymentEndApply::getYgjsrq, param.getYgjsrqEnd().atTime(23, 59, 59));
        }
        if (StringUtils.hasText(param.getXnxq())) {
            wrapper.eq("sa.XNXQ", param.getXnxq());
        }
        if (StringUtils.hasText(param.getJssyy())) {
            wrapper.like(QgzxEmploymentEndApply::getJssyy, param.getJssyy());
        }
        if (StringUtils.hasText(param.getJobName())) {
            wrapper.like(QgzxJobApplication::getJobName, param.getJobName());
        }
        if (StringUtils.hasText(param.getXm())) {
            wrapper.like(UserInfo::getXm, param.getXm());
        }

        Page<QgzxEmploymentEndApply> page = new Page<>(param.getPage(), param.getLimit());
        IPage<QgzxEmploymentEndApply> iPage = employmentEndApplyMapper.selectJoinPage(page, QgzxEmploymentEndApply.class, wrapper);
        
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Override
    public List<QgzxEmploymentEndApply> listApply(QgzxEmploymentEndApplyParam param) {
        MPJLambdaWrapper<QgzxEmploymentEndApply> wrapper = new MPJLambdaWrapper<QgzxEmploymentEndApply>()
                .selectAll(QgzxEmploymentEndApply.class)
                .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxEmploymentEndApply::getStudentApplyId);

        if (StringUtils.hasText(param.getXgh())) {
            wrapper.eq("sa.XGH", param.getXgh());
        }
        if (param.getSpzt() != null) {
            wrapper.eq(QgzxEmploymentEndApply::getSpzt, param.getSpzt());
        }
        if (StringUtils.hasText(param.getXnxq())) {
            wrapper.eq("sa.XNXQ", param.getXnxq());
        }

        wrapper.orderByDesc(QgzxEmploymentEndApply::getCreateTime);
        return list(wrapper);
    }

    @Transactional
    @Override
    public void edit(QgzxEmploymentEndApply employmentEndApply) {
        // 初始化必要字段
        employmentEndApply.initRequiredFields();

        if (StringUtils.hasText(employmentEndApply.getId())) {
            checkIsEdit(employmentEndApply.getId());
            updateById(employmentEndApply);
        } else {
            employmentEndApply.setCreateTime(LocalDateTime.now());
            employmentEndApply.setSqsj(LocalDateTime.now());
            employmentEndApply.setSpzt(ReviewResult.DaiShenPi);
            save(employmentEndApply);
        }
    }

    @Transactional
    @Override
    public void applyByEmployer(QgzxEmploymentEndApply employmentEndApply) {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
        QgzxStudentApply studentApply = studentApplyService.getById(employmentEndApply.getStudentApplyId());
        AssertUtil.isTrue(studentApply != null, "学生申请不存在");
        AssertUtil.isTrue(WorkStatus.YG.equals(studentApply.getYgzt()), "学生当前状态不是用工状态，无法申请用工结束");
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        AssertUtil.isTrue(jobApplication != null, "岗位信息不存在");
        AssertUtil.isTrue(JudgeMark.YES.equals(jobApplication.getYgjssfsh()), "该岗位未开启用工结束审核");
        QgzxEmploymentEndApply existingApply = getByStudentApplyId(employmentEndApply.getStudentApplyId());
        AssertUtil.isTrue(existingApply == null || !ReviewResult.DaiShenPi.equals(existingApply.getSpzt()), 
                "该学生已有待审核的用工结束申请，请勿重复提交");
        employmentEndApply.setJobApplication(jobApplication);

        employmentEndApply.initRequiredFields();

        employmentEndApply.setCreateTime(LocalDateTime.now());
        employmentEndApply.setSqsj(LocalDateTime.now());
        employmentEndApply.setSpzt(ReviewResult.DaiShenPi);
        save(employmentEndApply);
        studentApply.setYgzt(WorkStatus.YGJSDSH);
        studentApplyService.updateById(studentApply);

        String year = sysModuleSetupService.getModuleYear(SysModule.qgzx.getCode());
        String workflowId = workflowService.createApprovalNode(WorkflowApprovalNode.class, employmentEndApply,
                new WorkflowParam(null, year, SysModule.qgzxYgjs.getCode()), userInfo);
        employmentEndApply.setWorkflowId(workflowId);
        updateById(employmentEndApply);
        
        log.info("用工结束申请提交成功: 申请ID={}, 学生申请ID={}, 申请人={}", 
                employmentEndApply.getId(), employmentEndApply.getStudentApplyId(), userInfo.getXgh());
    }

    @Transactional
    @Override
    public CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxEmploymentEndApply employmentEndApply, Executor executor) {
        final QgzxEmploymentEndApply info = employmentEndApply == null ? employmentEndApplyMapper.selectById(record.getApplicationId()) : employmentEndApply;
        return CompletableFuture.supplyAsync(() -> {
            UserInfo userInfo = userInfoMapper.get(info.getXgh());
            QgzxStudentApply studentApply = studentApplyService.getById(employmentEndApply.getStudentApplyId());
            AssertUtil.isTrue(studentApply != null, "关联的学生申请不存在");

            ApprovalInfo<WorkflowApprovalNode, WorkflowApprovalNodeRecord> approvalInfo =
                    workflowService.approve(WorkflowApprovalNode.class, record, info, new WorkflowParam(info.getWorkflowId()), userInfo);
            if(JudgeMark.YES.getText().equals(approvalInfo.getCurrentApprovalNode().getEndNode().getText()) &&
                    ReviewResult.TongGuo.getText().equals(approvalInfo.getReviewResult().getText())){
                // 审核通过，最终确认用工结束
                employmentEndApply.setSpzt(approvalInfo.getReviewResult());
                studentApply.setYgzt(WorkStatus.YGJS);
                log.info("用工结束申请审核通过: 申请ID={}, 学生申请ID={}", record.getApplicationId(), employmentEndApply.getStudentApplyId());
            } else {
                // 审核不通过，恢复用工状态
                employmentEndApply.setSpzt(approvalInfo.getReviewResult());
                studentApply.setYgzt(WorkStatus.YG);
                log.info("用工结束申请审核不通过: 申请ID={}, 学生申请ID={}",record.getApplicationId(), employmentEndApply.getStudentApplyId());
            }
            updateById(employmentEndApply);
            studentApplyService.updateById(studentApply);
            return approvalInfo.getNextApprovableNodeId();
        }, executor);
    }

    @Override
    public void delete(String... ids) {
        for (String id : ids) {
            QgzxEmploymentEndApply employmentEndApply = getById(id);
            AssertUtil.isTrue(employmentEndApply != null, "用工结束申请不存在：" + id);
            checkIsEdit(id);
            
            // 如果删除待审核的申请，需要恢复学生申请状态
            if (ReviewResult.DaiShenPi.equals(employmentEndApply.getSpzt())) {
                QgzxStudentApply studentApply = studentApplyService.getById(employmentEndApply.getStudentApplyId());
                if (studentApply != null && WorkStatus.YGJSDSH.equals(studentApply.getYgzt())) {
                    studentApply.setYgzt(WorkStatus.YG);
                    studentApplyService.updateById(studentApply);
                }
            }
        }
        removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public void checkIsEdit(String id) {
        QgzxEmploymentEndApply employmentEndApply = getById(id);
        AssertUtil.isTrue(employmentEndApply != null, "用工结束申请不存在");
        AssertUtil.isTrue(ReviewResult.DaiShenPi.equals(employmentEndApply.getSpzt()), "只有待审批状态的申请才能编辑或删除");
    }

    @Override
    public QgzxEmploymentEndApply getByStudentApplyId(String studentApplyId) {
        Page<QgzxEmploymentEndApply> page = new Page<>(1, 1);
        IPage<QgzxEmploymentEndApply> result = page(page, new LambdaQueryWrapper<QgzxEmploymentEndApply>()
                .eq(QgzxEmploymentEndApply::getStudentApplyId, studentApplyId)
                .eq(QgzxEmploymentEndApply::getSpzt, ReviewResult.DaiShenPi)
                .orderByDesc(QgzxEmploymentEndApply::getCreateTime));

        return result.getRecords().isEmpty() ? null : result.getRecords().get(0);
    }

    @Override
    public boolean canApplyEmploymentEnd(String studentApplyId) {
        QgzxStudentApply studentApply = studentApplyService.getById(studentApplyId);
        if (studentApply == null || !WorkStatus.YG.equals(studentApply.getYgzt())) {
            return false;
        }
        
        QgzxJobApplication jobApplication = jobApplicationService.getById(studentApply.getJobId());
        if (jobApplication == null || !JudgeMark.YES.equals(jobApplication.getYgjssfsh())) {
            return false;
        }
        
        // 检查是否已有待审核的申请
        QgzxEmploymentEndApply existingApply = getByStudentApplyId(studentApplyId);
        return existingApply == null;
    }

    @Override
    public PageResult<QgzxEmploymentEndApply> pageApprovalList(QgzxEmploymentEndApplyParam param,
                                                              UserInfoParam userInfoParam,
                                                              WorkflowApprovalNodeParam approvalNodeParam) {
        // 构建查询条件
        MyMPJLambdaWrapper<QgzxEmploymentEndApply, QgzxEmploymentEndApplyParam> wrapper =
            buildApprovalListWrapper(param, userInfoParam, approvalNodeParam);

        // 分页查询
        Page<QgzxEmploymentEndApply> page = wrapper.getPage();
        page = page(page, wrapper);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<QgzxEmploymentEndApply> listApprovalList(QgzxEmploymentEndApplyParam param,
                                                        UserInfoParam userInfoParam,
                                                        WorkflowApprovalNodeParam approvalNodeParam) {
        // 构建查询条件
        MyMPJLambdaWrapper<QgzxEmploymentEndApply, QgzxEmploymentEndApplyParam> wrapper =
            buildApprovalListWrapper(param, userInfoParam, approvalNodeParam);

        return list(wrapper);
    }

    @Override
    public <T> List<Map<String, Object>> distinct(SFunction<QgzxEmploymentEndApply, T> column) {
        return employmentEndApplyMapper.selectMaps(new LambdaQueryWrapper<QgzxEmploymentEndApply>()
                .select(column)
                .groupBy(column)
                .orderByDesc(column));
    }

    /**
     * 构建审批列表查询条件
     */
    private MyMPJLambdaWrapper<QgzxEmploymentEndApply, QgzxEmploymentEndApplyParam> buildApprovalListWrapper(
            QgzxEmploymentEndApplyParam param,
            UserInfoParam userInfoParam,
            WorkflowApprovalNodeParam approvalNodeParam) {

        MyMPJLambdaWrapper<QgzxEmploymentEndApply, QgzxEmploymentEndApplyParam> wrapper =
            new MyMPJLambdaWrapper<>(param);

        wrapper.selectAssociation("sa", QgzxStudentApply.class, QgzxEmploymentEndApply::getStudentApply)
                .leftJoin(QgzxStudentApply.class, "sa", QgzxStudentApply::getId, QgzxEmploymentEndApply::getStudentApplyId)
                .selectAssociation("j", QgzxJobApplication.class, QgzxEmploymentEndApply::getJobApplication)
                .leftJoin(QgzxJobApplication.class, "j", QgzxJobApplication::getId, QgzxStudentApply::getJobId)
                .selectAssociation("u", UserInfo.class, QgzxEmploymentEndApply::getUserInfo)
                .leftJoin(UserInfo.class, "u", UserInfo::getXgh, QgzxStudentApply::getXgh);

        wrapper.selectAll(QgzxEmploymentEndApply.class);

        // 设置默认学年学期
        if (StringUtils.isEmpty(param.getXnxq())) {
            param.setXnxq(sysModuleSetupService.getModuleYear(SysModule.qgzxYgjs.getCode()));
        }

        wrapper.buildQueryCondition("u", userInfoParam);
        
        // 处理LocalDate日期范围查询（解决Oracle日期匹配问题）
        if (param.getYgksrqStart() != null) {
            wrapper.ge(QgzxEmploymentEndApply::getYgksrq, param.getYgksrqStart().atStartOfDay());
        }
        if (param.getYgksrqEnd() != null) {
            wrapper.le(QgzxEmploymentEndApply::getYgksrq, param.getYgksrqEnd().atTime(23, 59, 59));
        }
        if (param.getYgjsrqStart() != null) {
            wrapper.ge(QgzxEmploymentEndApply::getYgjsrq, param.getYgjsrqStart().atStartOfDay());
        }
        if (param.getYgjsrqEnd() != null) {
            wrapper.le(QgzxEmploymentEndApply::getYgjsrq, param.getYgjsrqEnd().atTime(23, 59, 59));
        }
        
        // 添加工作流审批节点相关的查询条件
        workflowService.getApprovalListSubQueryWrapper(wrapper, nodeApproverMapper,
            SysModule.qgzxYgjs.getCode(), param.getXnxq(),
            approvalNodeParam.getNodeId(), approvalNodeParam.getResult());

        return wrapper;
    }
}
