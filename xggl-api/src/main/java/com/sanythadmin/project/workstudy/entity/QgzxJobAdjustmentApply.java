package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.enums.AdjustmentStatus;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 岗位调剂申请实体
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_JOB_ADJUSTMENT_APPLY")
@Entity
@Table(name = "SYT_QGZX_JOB_ADJUSTMENT_APPLY")
public class QgzxJobAdjustmentApply implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学生申请ID
     */
    @Column(name = "STUDENT_APPLY_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("STUDENT_APPLY_ID")
    private String studentApplyId;

    /**
     * 原岗位ID
     */
    @Column(name = "FROM_JOB_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("FROM_JOB_ID")
    private String fromJobId;

    /**
     * 目标岗位ID
     */
    @Column(name = "TO_JOB_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("TO_JOB_ID")
    private String toJobId;

    /**
     * 调剂申请人（用人单位负责人学号）
     */
    @Column(name = "APPLY_USER", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("APPLY_USER")
    private String applyUser;

    /**
     * 调剂申请时间
     */
    @Column(name = "APPLY_TIME")
    @TableField("APPLY_TIME")
    private LocalDateTime applyTime;

    /**
     * 调剂理由
     */
    @Column(name = "ADJUST_REASON", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ADJUST_REASON")
    private String adjustReason;

    /**
     * 调剂状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("STATUS")
    private AdjustmentStatus status;

    /**
     * 接收单位确认人
     */
    @Column(name = "RECEIVER_USER", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("RECEIVER_USER")
    private String receiverUser;

    /**
     * 接收单位确认时间
     */
    @Column(name = "RECEIVER_TIME")
    @TableField("RECEIVER_TIME")
    private LocalDateTime receiverTime;

    /**
     * 接收单位确认结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "RECEIVER_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("RECEIVER_RESULT")
    private ReviewResult receiverResult;

    /**
     * 接收单位确认意见
     */
    @Column(name = "RECEIVER_COMMENT", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("RECEIVER_COMMENT")
    private String receiverComment;

    /**
     * 学生确认人
     */
    @Column(name = "STUDENT_USER", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("STUDENT_USER")
    private String studentUser;

    /**
     * 学生确认时间
     */
    @Column(name = "STUDENT_TIME")
    @TableField("STUDENT_TIME")
    private LocalDateTime studentTime;

    /**
     * 学生确认结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "STUDENT_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("STUDENT_RESULT")
    private ReviewResult studentResult;

    /**
     * 学生确认意见
     */
    @Column(name = "STUDENT_COMMENT", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("STUDENT_COMMENT")
    private String studentComment;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 学生申请信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxStudentApply studentApply;

    /**
     * 原岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication fromJobApplication;

    /**
     * 目标岗位信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxJobApplication toJobApplication;

    /**
     * 申请人信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo applyUserInfo;

    /**
     * 接收人信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo receiverUserInfo;

    /**
     * 学生信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo studentUserInfo;
}
