package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.UserInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用人单位
 *
 * <AUTHOR>
 * @since 2025-07-14 13:48:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_EMPLOYER")
@Entity
@Table(name = "SYT_QGZX_EMPLOYER")
public class QgzxEmployer implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 单位名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 单位描述
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 办公电话
     */
    @Column(name = "BGDH")
    @TableField("BGDH")
    private String bgdh;

    /**
     * 联系人类型
     */
    @Column(name = "LXRLX")
    @TableField("LXRLX")
    private String lxrlx;

    /**
     * 联系人
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;
    /**
     * 负责人
     */
    @Column(name = "FZR")
    @TableField("FZR")
    private String fzr;
    /**
     * 单位地址
     */
    @Column(name = "DWDZ")
    @TableField("DWDZ")
    private String dwdz;
    /**
     * 资质证明
     */
    /*@Column(name = "ZZZZ")
    @TableField("ZZZZ")
    private String zzzz;*/

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

}
