package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxJobType;
import com.sanythadmin.project.workstudy.param.QgzxJobTypeParam;
import com.sanythadmin.project.workstudy.service.QgzxJobTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/岗位类型控制器
 *
 * <AUTHOR>
 * @since 2025-07-16 09:48:14
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-job-type")
@RequiredArgsConstructor
public class QgzxJobTypeController extends BaseController {

    private final QgzxJobTypeService qgzxJobTypeService;

    /**
     * 分页查询岗位类型（权限标识：workstudy:qgzxJobType:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobType:list')")
    @GetMapping("/page")
    public PageResult<QgzxJobType> page(QgzxJobTypeParam param) {
        PageParam<QgzxJobType, QgzxJobTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = qgzxJobTypeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部岗位类型（权限标识：workstudy:qgzxJobType:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobType:list')")
    @GetMapping()
    public List<QgzxJobType> list(QgzxJobTypeParam param) {
        PageParam<QgzxJobType, QgzxJobTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return qgzxJobTypeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询岗位类型（权限标识：workstudy:qgzxJobType:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobType:list')")
    @GetMapping("/{id}")
    public QgzxJobType get(@PathVariable("id") String id) {
        return qgzxJobTypeService.getById(id);
    }

    /**
     * 添加或修改岗位类型（权限标识：workstudy:qgzxJobType:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobType:operation')")
    @OperationLog(module = "岗位类型", comments = "保存岗位类型")
    @PostMapping("/operation")
    public void save(@RequestBody QgzxJobType qgzxJobType) {
        if (StringUtils.hasLength(qgzxJobType.getId())) {
            qgzxJobTypeService.updateById(qgzxJobType);
        } else {
            qgzxJobTypeService.save(qgzxJobType);
        }
    }

    /**
     * 批量删除岗位类型（权限标识：workstudy:qgzxJobType:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobType:remove')")
    @OperationLog(module = "岗位类型", comments = "批量删除岗位类型")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        qgzxJobTypeService.removeByIds(ids);
    }
}
