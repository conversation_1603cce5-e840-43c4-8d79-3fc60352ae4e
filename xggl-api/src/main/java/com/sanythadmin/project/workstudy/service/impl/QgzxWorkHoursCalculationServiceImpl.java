package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.project.workstudy.entity.*;
import com.sanythadmin.project.workstudy.mapper.*;
import com.sanythadmin.project.workstudy.service.QgzxWorkHoursCalculationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 勤工助学工时计算服务实现
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
@RequiredArgsConstructor
public class QgzxWorkHoursCalculationServiceImpl implements QgzxWorkHoursCalculationService {

    private final QgzxAttendanceRecordMapper attendanceRecordMapper;
    private final QgzxStudentApplyMapper studentApplyMapper;
    private final QgzxJobApplicationMapper jobApplicationMapper;
    private final QgzxJobTypeMapper jobTypeMapper;
    private final QgzxRemunerationApplyMapper remunerationApplyMapper;
    private final QgzxRemunerationDetailMapper remunerationDetailMapper;

    @Override
    public BigDecimal calculateWorkHoursByAttendance(String studentApplyId, LocalDate startDate, LocalDate endDate) {
        // 查询考勤记录并计算总工时
        LambdaQueryWrapper<QgzxAttendanceRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                .between(QgzxAttendanceRecord::getAttendanceDate, startDate, endDate)
                .isNotNull(QgzxAttendanceRecord::getWorkDuration);

        List<QgzxAttendanceRecord> records = attendanceRecordMapper.selectList(wrapper);
        
        return records.stream()
                .map(QgzxAttendanceRecord::getWorkDuration)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Map<String, BigDecimal> batchCalculateWorkHours(String jobId, LocalDate startDate, LocalDate endDate) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        // 查询该岗位下的所有学生申请
        LambdaQueryWrapper<QgzxStudentApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QgzxStudentApply::getJobId, jobId);
        List<QgzxStudentApply> studentApplies = studentApplyMapper.selectList(wrapper);
        
        // 为每个学生计算工时
        for (QgzxStudentApply studentApply : studentApplies) {
            BigDecimal workHours = calculateWorkHoursByAttendance(studentApply.getId(), startDate, endDate);
            result.put(studentApply.getXgh(), workHours);
        }
        
        return result;
    }

    @Override
    public boolean validateStudentWorkHours(String xgh, String jobId, BigDecimal workHours, String sbny) {
        // 获取岗位信息
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(jobId);
        if (jobApplication == null || jobApplication.getWorkHous() == null) {
            return false;
        }
        
        // 获取学生已申报的工时
        BigDecimal declaredHours = getStudentDeclaredHours(xgh, jobId, sbny);
        
        // 验证：学生个人工时不能超出岗位工时
        return declaredHours.add(workHours).compareTo(BigDecimal.valueOf(jobApplication.getWorkHous())) <= 0;
    }

    @Override
    public boolean validateJobTotalWorkHours(String jobId, BigDecimal totalHours, String sbny) {
        // 获取岗位信息
        QgzxJobApplication jobApplication = jobApplicationMapper.selectById(jobId);
        if (jobApplication == null || jobApplication.getJobTypeId() == null) {
            return false;
        }
        
        // 获取岗位类别信息
        QgzxJobType jobType = jobTypeMapper.selectById(jobApplication.getJobTypeId());
        if (jobType == null || jobType.getYzdgs() == null) {
            return false;
        }
        
        // 获取该岗位类别已申报的总工时
        BigDecimal declaredTotalHours = getJobTypeDeclaredTotalHours(jobApplication.getJobTypeId(), sbny);
        
        // 验证：同一岗位所有学生的工时相加不能超过岗位类别设置的月最大工时
        return declaredTotalHours.add(totalHours).compareTo(BigDecimal.valueOf(jobType.getYzdgs())) <= 0;
    }

    @Override
    public boolean validateJobTypeTotalAmount(String jobTypeId, BigDecimal totalAmount, String sbny) {
        // 获取岗位类别信息
        QgzxJobType jobType = jobTypeMapper.selectById(jobTypeId);
        if (jobType == null || jobType.getYzgbc() == null) {
            return false;
        }
        
        // 获取该岗位类别已申报的总报酬
        BigDecimal declaredTotalAmount = getJobTypeDeclaredTotalAmount(jobTypeId, sbny);
        
        // 验证：所有学生的报酬金额相加不能超过岗位类别的月最大报酬金额
        return declaredTotalAmount.add(totalAmount).compareTo(BigDecimal.valueOf(jobType.getYzgbc())) <= 0;
    }

    @Override
    public BigDecimal getStudentDeclaredHours(String xgh, String jobId, String sbny) {
        return remunerationDetailMapper.selectStudentTotalHours(xgh, jobId, sbny);
    }

    @Override
    public BigDecimal getJobTypeDeclaredTotalHours(String jobTypeId, String sbny) {
        return remunerationApplyMapper.selectTotalHoursByJobTypeAndSbny(jobTypeId, sbny);
    }

    @Override
    public BigDecimal getJobTypeDeclaredTotalAmount(String jobTypeId, String sbny) {
        return remunerationApplyMapper.selectTotalAmountByJobTypeAndSbny(jobTypeId, sbny);
    }
}
