package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 勤工助学报酬明细实体
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_REMUNERATION_DETAIL")
@Entity
@Table(name = "SYT_QGZX_REMUNERATION_DETAIL")
public class QgzxRemunerationDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 报酬申报ID
     */
    @Column(name = "REMUNERATION_APPLY_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("REMUNERATION_APPLY_ID")
    private String remunerationApplyId;

    /**
     * 学生申请ID
     */
    @Column(name = "STUDENT_APPLY_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("STUDENT_APPLY_ID")
    private String studentApplyId;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 工时来源类型（AUTO_ATTENDANCE：考勤自动计算，MANUAL_INPUT：手工录入）
     */
    @Column(name = "HOURS_SOURCE_TYPE", columnDefinition = "VARCHAR2(20 CHAR)")
    @TableField("HOURS_SOURCE_TYPE")
    private String hoursSourceType;

    /**
     * 工作时长（小时）
     */
    @Column(name = "WORK_HOURS", columnDefinition = "NUMBER(8,2)")
    @TableField("WORK_HOURS")
    private BigDecimal workHours;

    /**
     * 时薪
     */
    @Column(name = "HOURLY_RATE", columnDefinition = "NUMBER(8,2)")
    @TableField("HOURLY_RATE")
    private BigDecimal hourlyRate;

    /**
     * 报酬金额
     */
    @Column(name = "REMUNERATION_AMOUNT", columnDefinition = "NUMBER(10,2)")
    @TableField("REMUNERATION_AMOUNT")
    private BigDecimal remunerationAmount;

    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 学生申请信息
     */
    @Transient
    @TableField(exist = false)
    private QgzxStudentApply studentApply;

    /**
     * 学生信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;
}
