package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 岗位类别/类型
 *
 * <AUTHOR>
 * @since 2025-07-16 09:48:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_JOB_TYPE")
@Entity
@Table(name = "SYT_QGZX_JOB_TYPE")
public class QgzxJobType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 每月最高报酬
     */
    @Column(name = "YZGBC",columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("YZGBC")
    private Double yzgbc;

    /**
     * 月最大工时
     */
    @Column(name = "YZDGS",columnDefinition = ColumnType.NUMBER_7_2)
    @TableField("YZDGS")
    private Double yzdgs;
    /**
     * 同时在岗的最大岗位数(例如：学生在该类别下同时能在岗的岗位数)
     * 0为不限制，1为只能有一个在岗岗位(如固定岗)
     */
    @Column(name = "KYGGWS",columnDefinition = ColumnType.NUMBER_5_DEFAULT_0)
    @TableField("KYGGWS")
    private Integer kyggws;

}
