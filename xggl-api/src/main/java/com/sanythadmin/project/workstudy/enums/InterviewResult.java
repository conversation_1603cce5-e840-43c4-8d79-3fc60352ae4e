package com.sanythadmin.project.workstudy.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.Getter;

/**
 * 面试结果枚举
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Getter
public enum InterviewResult implements TextBaseEnum {
    <PERSON><PERSON><PERSON><PERSON><PERSON>("待面试"),
    <PERSON><PERSON><PERSON>("通过"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("不通过"),
    Que<PERSON>i("缺席");

    @JsonValue  // JSON 序列化注解，用于前端显示
    private final String text;

    InterviewResult(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    /**
     * 根据文本或枚举名称获取枚举值
     * @param value 文本值或枚举名称
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static InterviewResult get(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        for (InterviewResult result : InterviewResult.values()) {
            // 支持通过枚举名称获取
            if (result.name().equals(value)) {
                return result;
            }
            // 支持通过文本值获取
            if (result.getText().equals(value)) {
                return result;
            }
        }
        return null;
    }
}
