package com.sanythadmin.project.workstudy.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.enums.WorkStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 学生岗位申请查询参数
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QgzxStudentApplyParam extends BaseParam {

    @QueryField(type = QueryType.IN_STR)
    private String id;
    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 岗位ID
     */
    @QueryField(type = QueryType.EQ)
    private String jobId;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 申请时间
     */
    private LocalDateTime sqsj;

    /**
     * 申请理由
     */
    private String sqly;

    /**
     * 审核状态
     */
    @QueryField(type = QueryType.EQ)
    private ReviewResult spzt;

    /**
     * 用工状态
     */
    @QueryField(type = QueryType.EQ)
    private WorkStatus ygzt;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 岗位信息
     */
    @QueryField(ignore = true)
    private QgzxJobApplication jobApplication;

    /**
     * 学生信息
     */
    @QueryField(ignore = true)
    private UserInfo userInfo;

    /**
     * 忽略父类的创建时间起始值字段
     */
    @QueryField(ignore = true)
    private String createTimeStart;

    /**
     * 忽略父类的创建时间结束值字段
     */
    @QueryField(ignore = true)
    private String createTimeEnd;

}