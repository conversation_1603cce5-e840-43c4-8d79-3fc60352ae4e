package com.sanythadmin.project.workstudy.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 勤工助学报酬明细Mapper
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
public interface QgzxRemunerationDetailMapper extends MyMPJBaseMapper<QgzxRemunerationDetail> {

    /**
     * 根据报酬申报ID查询明细列表
     */
    @Select("SELECT * FROM SYT_QGZX_REMUNERATION_DETAIL WHERE REMUNERATION_APPLY_ID = #{remunerationApplyId}")
    List<QgzxRemunerationDetail> selectByRemunerationApplyId(@Param("remunerationApplyId") String remunerationApplyId);

    /**
     * 根据报酬申报ID删除明细
     */
    @Delete("DELETE FROM SYT_QGZX_REMUNERATION_DETAIL WHERE REMUNERATION_APPLY_ID = #{remunerationApplyId}")
    int deleteByRemunerationApplyId(@Param("remunerationApplyId") String remunerationApplyId);

    /**
     * 查询学生在指定岗位和年月的总工时
     */
    @Select("SELECT COALESCE(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd " +
            "INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID " +
            "WHERE rd.XGH = #{xgh} AND ra.JOB_ID = #{jobId} AND ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo'")
    BigDecimal selectStudentTotalHours(@Param("xgh") String xgh, @Param("jobId") String jobId, @Param("sbny") String sbny);

    /**
     * 查询学生在指定岗位类别和年月的总工时
     */
    @Select("SELECT COALESCE(SUM(rd.WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL rd " +
            "INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID " +
            "INNER JOIN SYT_QGZX_JOB_APPLICATION ja ON ra.JOB_ID = ja.ID " +
            "WHERE rd.XGH = #{xgh} AND ja.JOB_TYPE_ID = #{jobTypeId} AND ra.SBNY = #{sbny} AND ra.SPZT = 'TongGuo'")
    BigDecimal selectStudentTotalHoursByJobType(@Param("xgh") String xgh, @Param("jobTypeId") String jobTypeId, @Param("sbny") String sbny);

    /**
     * 查询学生在指定岗位和年月的明细记录数量（用于重复数据检查）
     */
    @Select("SELECT COUNT(*) FROM SYT_QGZX_REMUNERATION_DETAIL rd " +
            "INNER JOIN SYT_QGZX_REMUNERATION_APPLY ra ON rd.REMUNERATION_APPLY_ID = ra.ID " +
            "WHERE rd.XGH = #{xgh} AND ra.JOB_ID = #{jobId} AND ra.SBNY = #{sbny} AND ra.SPZT IN ('DaiShenPi', 'TongGuo')")
    Long selectStudentDetailCount(@Param("xgh") String xgh, @Param("jobId") String jobId, @Param("sbny") String sbny);

    /**
     * 根据申报ID查询总工时
     */
    @Select("SELECT COALESCE(SUM(WORK_HOURS), 0) FROM SYT_QGZX_REMUNERATION_DETAIL WHERE REMUNERATION_APPLY_ID = #{remunerationApplyId}")
    BigDecimal selectTotalHoursByApplyId(@Param("remunerationApplyId") String remunerationApplyId);

    /**
     * 根据申报ID查询总报酬金额
     */
    @Select("SELECT COALESCE(SUM(REMUNERATION_AMOUNT), 0) FROM SYT_QGZX_REMUNERATION_DETAIL WHERE REMUNERATION_APPLY_ID = #{remunerationApplyId}")
    BigDecimal selectTotalAmountByApplyId(@Param("remunerationApplyId") String remunerationApplyId);
}
