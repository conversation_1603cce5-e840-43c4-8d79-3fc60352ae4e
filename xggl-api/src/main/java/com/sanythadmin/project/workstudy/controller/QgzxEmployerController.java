package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.EnableMask;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.QgzxEmployer;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;
import com.sanythadmin.project.workstudy.service.QgzxEmployerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/用人单位控制器
 *
 * <AUTHOR>
 * @since 2025-07-14 13:48:13
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-employer")
@RequiredArgsConstructor
public class QgzxEmployerController extends BaseController {
    private final QgzxEmployerService qgzxEmployerService;

    /**
     * 分页查询用人单位（权限标识：workstudy:qgzxEmployer:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxEmployer:list')")
    @GetMapping("/page")
    public PageResult<QgzxEmployer> page(QgzxEmployerParam param, UserInfoParam userInfoParam) {
        return qgzxEmployerService.getQgzxEmployerPage(param, userInfoParam);
    }


    /**
     * 根据id查询用人单位（权限标识：workstudy:qgzxEmployer:list）
     */
    @EnableMask
    @PreAuthorize("hasAuthority('workstudy:qgzxEmployer:list')")
    @GetMapping("/{id}")
    public QgzxEmployer get(@PathVariable("id") String id) {
        return qgzxEmployerService.getQgzxEmployer(id);
    }

    /**
     * 添加或修改用人单位（权限标识：workstudy:qgzxEmployer:operation）
     */
    @EnableMask
    @PreAuthorize("hasAuthority('workstudy:qgzxEmployer:operation')")
    @OperationLog(module = "用人单位", comments = "保存用人单位")
    @PostMapping("/operation")
    public QgzxEmployer save(@Valid @RequestBody QgzxEmployerParam param) {
        return qgzxEmployerService.saveQgzxEmployer(param);
    }

    /**
     * 批量删除用人单位（权限标识：workstudy:qgzxEmployer:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxEmployer:remove')")
    @OperationLog(module = "用人单位", comments = "批量删除用人单位")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        qgzxEmployerService.removeQgzxEmployer(ids);
    }
}
