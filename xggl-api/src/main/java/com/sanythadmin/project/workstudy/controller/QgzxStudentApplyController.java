package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 勤工助学/学生岗位申请控制器
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-student-apply")
@RequiredArgsConstructor
public class QgzxStudentApplyController extends BaseController {

    private final QgzxStudentApplyService qgzxStudentApplyService;


    /**
     * 学生分页查询可申请岗位（权限标识：workstudy:qgzxJobApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:list')")
    @GetMapping("/pageJob")
    public PageResult<QgzxJobApplicationVO> pageJob(QgzxJobApplicationParam param) {
        return qgzxStudentApplyService.pageByApplicantList(param);
    }

    /**
     * 学生查询全部可申请岗位（权限标识：workstudy:qgzxJobApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApply:list')")
    @GetMapping("/listJob")
    public List<QgzxJobApplicationVO> list(QgzxJobApplicationParam param) {
        return qgzxStudentApplyService.listApplicantList(param);
    }

    /**
     * 学生分页查询申请记录(我的岗位)（权限标识：workstudy:qgzxStudentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:list')")
    @GetMapping("/page")
    public PageResult<QgzxStudentApply> page(QgzxStudentApplyParam param, UserInfoParam userInfoParam) {
        userInfoParam.setXgh(getAccount().getUsername());
        return qgzxStudentApplyService.pageByStudent(param, userInfoParam);
    }

    /**
     * 学生查询申请记录(我的岗位)（权限标识：workstudy:qgzxStudentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:list')")
    @GetMapping()
    public List<QgzxStudentApply> list(QgzxStudentApplyParam param, UserInfoParam userInfoParam) {
        userInfoParam.setXgh(getAccount().getUsername());
        return qgzxStudentApplyService.listByStudent(param,userInfoParam);
    }

    /**
     * 根据ID查询申请详情（权限标识：workstudy:qgzxStudentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:list')")
    @GetMapping("/{id}")
    public QgzxStudentApply get(@PathVariable("id") String id) {
        return qgzxStudentApplyService.getDetail(id);
    }

    /**
     * 学生申请岗位（权限标识：workstudy:qgzxStudentApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:operation')")
    @OperationLog(module = "申请岗位", comments = "学生保存岗位申请")
    @PostMapping("/operation")
    public void operation(@RequestBody QgzxStudentApply studentApply) {
        qgzxStudentApplyService.editByApplicant(studentApply);
    }

    /**
     * 学生取消申请（权限标识：workstudy:qgzxStudentApply:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:operation')")
    @OperationLog(module = "申请岗位", comments = "学生取消岗位申请")
    @PostMapping("/remove")
    public void delete(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        qgzxStudentApplyService.deleteByApplicant(ids.toArray(new String[]{}));
    }

    /**
     * 查询可调剂的岗位列表（权限标识：workstudy:qgzxStudentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:list')")
    @GetMapping("/adjustableJobs/{studentApplyId}")
    public List<QgzxJobApplication> listAdjustableJobs(@PathVariable("studentApplyId") String studentApplyId) {
        return qgzxStudentApplyService.listAdjustableJobs(studentApplyId);
    }

    /**
     * 用人单位查询需要面试的学生申请（权限标识：workstudy:qgzxStudentApply:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxStudentApply:list')")
    @GetMapping("/pageForInterview")
    public PageResult<QgzxStudentApply> pageForInterview(QgzxStudentApplyParam param) {
        return qgzxStudentApplyService.pageForInterview(param);
    }

}