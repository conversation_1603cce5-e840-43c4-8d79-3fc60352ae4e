package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationDetail;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationDetailParam;

import java.util.List;

/**
 * 勤工助学报酬明细服务
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface QgzxRemunerationDetailService extends IService<QgzxRemunerationDetail> {

    /**
     * 分页查询报酬明细列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<QgzxRemunerationDetail> pageList(QgzxRemunerationDetailParam param);

    /**
     * 查询报酬明细列表
     *
     * @param param 查询参数
     * @return 明细列表
     */
    List<QgzxRemunerationDetail> listDetail(QgzxRemunerationDetailParam param);

    /**
     * 根据ID获取报酬明细详情
     *
     * @param id 明细ID
     * @return 明细详情
     */
    QgzxRemunerationDetail getDetail(String id);

    /**
     * 根据申报ID获取明细列表
     *
     * @param remunerationApplyId 申报ID
     * @return 明细列表
     */
    List<QgzxRemunerationDetail> getDetailsByApplyId(String remunerationApplyId);

    /**
     * 新增报酬明细
     *
     * @param detail 明细信息
     */
    void addDetail(QgzxRemunerationDetail detail);

    /**
     * 更新报酬明细
     *
     * @param detail 明细信息
     */
    void updateDetail(QgzxRemunerationDetail detail);

    /**
     * 删除报酬明细
     *
     * @param ids 明细ID数组
     */
    void deleteDetail(String... ids);

    /**
     * 检查明细是否可以编辑
     *
     * @param id 明细ID
     */
    void checkIsEdit(String id);

    /**
     * 计算并设置报酬金额
     *
     * @param detail 明细信息
     */
    void calculateRemunerationAmount(QgzxRemunerationDetail detail);

    /**
     * 重新计算申报的总工时和总报酬
     *
     * @param remunerationApplyId 申报ID
     */
    void recalculateApplyTotals(String remunerationApplyId);
}
