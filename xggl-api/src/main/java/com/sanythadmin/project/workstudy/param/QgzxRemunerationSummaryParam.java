package com.sanythadmin.project.workstudy.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.ReviewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 勤工助学报酬月度汇总查询参数
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxRemunerationSummaryParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 申报年月（格式：YYYY-MM）- 必选条件
     */
    @QueryField(type = QueryType.EQ)
    private String sbny;

    /**
     * 岗位类别ID（可选）
     */
    @QueryField(ignore = true)
    private String jobTypeId;

    /**
     * 用人单位ID（可选）
     */
    @QueryField(ignore = true)
    private String employerId;

    /**
     * 学院ID（可选）
     */
    @QueryField(ignore = true)
    private String xyid;

    /**
     * 审核状态（可选，默认只统计已通过的数据）
     */
    @QueryField(type = QueryType.EQ)
    private ReviewResult spzt = ReviewResult.TongGuo;

    /**
     * 学年学期（可选）
     */
    @QueryField(ignore = true)
    private String xnxq;

    /**
     * 岗位名称（用于模糊查询）
     */
    @QueryField(ignore = true)
    private String jobName;

    /**
     * 用人单位名称（用于模糊查询）
     */
    @QueryField(ignore = true)
    private String employerName;

    /**
     * 学院名称（用于模糊查询）
     */
    @QueryField(ignore = true)
    private String collegeName;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "SBNY desc";
    }
}
