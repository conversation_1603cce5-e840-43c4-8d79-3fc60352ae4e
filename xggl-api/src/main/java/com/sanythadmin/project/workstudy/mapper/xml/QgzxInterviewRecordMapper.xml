<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxInterviewRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STUDENT_APPLY_ID, INTERVIEW_TIME,
        INTERVIEW_LOCATION, INTERVIEWER_NAME, INTERVIEWER_CONTACT, INTERVIEW_RESULT,
        INTERVIEW_SCORE, INTERVIEW_COMMENT, CREATE_TIME, CREATE_BY,
        UPDATE_TIME, UPDATE_BY, REMARK
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null and param.id != ''">
            AND a.ID = #{param.id}
        </if>
        <if test="param.studentApplyId != null and param.studentApplyId != ''">
            AND a.STUDENT_APPLY_ID = #{param.studentApplyId}
        </if>
        <if test="param.jobId != null and param.jobId != ''">
            AND a.JOB_ID = #{param.jobId}
        </if>
        <if test="param.studentXgh != null and param.studentXgh != ''">
            AND a.STUDENT_XGH = #{param.studentXgh}
        </if>
        <if test="param.employerId != null and param.employerId != ''">
            AND a.EMPLOYER_ID = #{param.employerId}
        </if>
        <if test="param.interviewTimeStart != null">
            AND a.INTERVIEW_TIME &gt;= #{param.interviewTimeStart}
        </if>
        <if test="param.interviewTimeEnd != null">
            AND a.INTERVIEW_TIME &lt;= #{param.interviewTimeEnd}
        </if>
        <if test="param.interviewLocation != null and param.interviewLocation != ''">
            AND a.INTERVIEW_LOCATION LIKE CONCAT('%', #{param.interviewLocation}, '%')
        </if>
        <if test="param.interviewerName != null and param.interviewerName != ''">
            AND a.INTERVIEWER_NAME LIKE CONCAT('%', #{param.interviewerName}, '%')
        </if>
        <if test="param.interviewResult != null">
            AND a.INTERVIEW_RESULT = #{param.interviewResult}
        </if>
        <if test="param.minScore != null">
            AND a.INTERVIEW_SCORE &gt;= #{param.minScore}
        </if>
        <if test="param.maxScore != null">
            AND a.INTERVIEW_SCORE &lt;= #{param.maxScore}
        </if>
        <if test="param.xnxq != null and param.xnxq != ''">
            AND a.XNXQ = #{param.xnxq}
        </if>
        <if test="param.createTimeStart != null">
            AND a.CREATE_TIME &gt;= #{param.createTimeStart}
        </if>
        <if test="param.createTimeEnd != null">
            AND a.CREATE_TIME &lt;= #{param.createTimeEnd}
        </if>
        <if test="param.createBy != null and param.createBy != ''">
            AND a.CREATE_BY = #{param.createBy}
        </if>
    </sql>

</mapper>
