package com.sanythadmin.project.workstudy.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workstudy.enums.WeekType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 学生上课时间
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_QGZX_STUDENTS_CLASS_TIME")
@Entity
@Table(name = "SYT_QGZX_STUDENTS_CLASS_TIME")
public class QgzxStudentsClassTime implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ")
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 学工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 1-7表示周一到周日
     */
    @Column(name = "WEEK_DAY")
    @TableField("WEEK_DAY")
    private Integer weekDay;

    /**
     * 关联课时表ID
     */
    @Column(name = "KSB_ID")
    @TableField("KSB_ID")
    private String ksbId;

    /**
     * 开始周次
     */
    @Column(name = "ZC_START")
    @TableField("ZC_START")
    private Integer zcStart;

    /**
     * 结束周次
     */
    @Column(name = "ZC_END")
    @TableField("ZC_END")
    private Integer zcEnd;

    /**
     * 周次类型：ALL-全部周次，ODD-单周，EVEN-双周，CUSTOM-自定义周次
     */
    @Column(name = "ZC_TYPE")
    @TableField("ZC_TYPE")
    private WeekType zcType;

    /**
     * 自定义周次列表，JSON格式存储，如"[1,3,5,7,9]"
     */
    @Column(name = "ZC_CUSTOM")
    @TableField("ZC_CUSTOM")
    private String zcCustom;

}
