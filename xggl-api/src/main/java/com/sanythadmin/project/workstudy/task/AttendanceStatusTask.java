package com.sanythadmin.project.workstudy.task;

import com.sanythadmin.project.workstudy.service.QgzxAttendanceStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 考勤状态定时任务
 * 用于处理缺勤记录的自动生成和状态更新
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "workstudy.attendance.task.enabled", havingValue = "true", matchIfMissing = false)
public class AttendanceStatusTask {

    private final QgzxAttendanceStatusService attendanceStatusService;

    /**
     * 每天晚上23:00检查当天的缺勤记录
     * 为未打卡的学生创建缺勤记录
     */
    @Scheduled(cron = "0 0 23 * * ?")
    public void checkDailyAbsentRecords() {
        try {
            log.info("开始执行每日缺勤记录检查任务");
            
            LocalDateTime today = LocalDateTime.now();
            attendanceStatusService.checkAndCreateAbsentRecords(today);
            
            log.info("每日缺勤记录检查任务执行完成");
        } catch (Exception e) {
            log.error("每日缺勤记录检查任务执行失败", e);
        }
    }

    /**
     * 每天凌晨1:00检查前一天的缺勤记录（补充检查）
     * 防止23:00任务执行时还有学生在加班等情况
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void checkPreviousDayAbsentRecords() {
        try {
            log.info("开始执行前一天缺勤记录补充检查任务");
            
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            attendanceStatusService.checkAndCreateAbsentRecords(yesterday);
            
            log.info("前一天缺勤记录补充检查任务执行完成");
        } catch (Exception e) {
            log.error("前一天缺勤记录补充检查任务执行失败", e);
        }
    }

    /**
     * 每周一凌晨2:00执行批量状态更新（可选）
     * 重新计算上周的考勤状态，确保数据准确性
     */
    @Scheduled(cron = "0 0 2 ? * MON")
    public void weeklyStatusUpdate() {
        try {
            log.info("开始执行每周考勤状态批量更新任务");
            
            // TODO: 实现批量状态更新逻辑
            // 可以根据实际需求决定是否启用此功能
            
            log.info("每周考勤状态批量更新任务执行完成");
        } catch (Exception e) {
            log.error("每周考勤状态批量更新任务执行失败", e);
        }
    }
}
