package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobAdjustmentApply;
import com.sanythadmin.project.workstudy.param.QgzxJobAdjustmentApplyParam;

import java.util.List;

/**
 * 岗位调剂申请Service
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface QgzxJobAdjustmentApplyService extends IService<QgzxJobAdjustmentApply> {

    /**
     * 分页查询调剂申请列表
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @return 分页结果
     */
    PageResult<QgzxJobAdjustmentApply> pageList(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam);

    /**
     * 查询调剂申请列表
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @return 调剂申请列表
     */
    List<QgzxJobAdjustmentApply> listApply(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam);

    /**
     * 用人单位申请岗位调剂
     * @param studentApplyId 学生申请ID
     * @param toJobId 目标岗位ID
     * @param adjustReason 调剂理由
     */
    void applyAdjustment(String studentApplyId, String toJobId, String adjustReason);

    /**
     * 接收单位确认调剂申请
     * @param adjustmentApplyId 调剂申请ID
     * @param result 确认结果（通过/不通过）
     * @param comment 确认意见
     */
    void confirmByReceiver(String adjustmentApplyId, String result, String comment);

    /**
     * 学生确认调剂申请
     * @param adjustmentApplyId 调剂申请ID
     * @param result 确认结果（通过/不通过）
     * @param comment 确认意见
     */
    void confirmByStudent(String adjustmentApplyId, String result, String comment);

    /**
     * 根据学生申请ID获取调剂申请
     * @param studentApplyId 学生申请ID
     * @return 调剂申请
     */
    QgzxJobAdjustmentApply getByStudentApplyId(String studentApplyId);

    /**
     * 查询用人单位待确认的调剂申请
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @return 分页结果
     */
    PageResult<QgzxJobAdjustmentApply> pagePendingForEmployer(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam);

    /**
     * 查询学生待确认的调剂申请
     * @param param 查询参数
     * @param userInfoParam 用户信息参数
     * @return 分页结果
     */
    PageResult<QgzxJobAdjustmentApply> pagePendingForStudent(QgzxJobAdjustmentApplyParam param, UserInfoParam userInfoParam);

    /**
     * 获取调剂申请详情
     * @param id 调剂申请ID
     * @return 调剂申请详情
     */
    QgzxJobAdjustmentApply getDetail(String id);

    /**
     * 检查是否可以申请调剂
     * @param studentApplyId 学生申请ID
     * @param toJobId 目标岗位ID
     * @return 检查结果消息，为空表示可以申请
     */
    String checkCanApplyAdjustment(String studentApplyId, String toJobId);
}
