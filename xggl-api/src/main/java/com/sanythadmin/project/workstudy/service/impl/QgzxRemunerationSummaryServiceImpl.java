package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.JxlsUtil;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.TemplateType;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.common.system.service.UserInfoService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import com.sanythadmin.project.workstudy.entity.QgzxRemunerationApply;
import com.sanythadmin.project.workstudy.mapper.QgzxRemunerationSummaryMapper;
import com.sanythadmin.project.workstudy.param.QgzxRemunerationSummaryParam;
import com.sanythadmin.project.workstudy.service.QgzxRemunerationSummaryService;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryDetailVO;
import com.sanythadmin.project.workstudy.vo.QgzxRemunerationSummaryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 勤工助学报酬月度汇总服务实现
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxRemunerationSummaryServiceImpl extends ServiceImpl<QgzxRemunerationSummaryMapper, QgzxRemunerationApply> implements QgzxRemunerationSummaryService {

    private final QgzxRemunerationSummaryMapper remunerationSummaryMapper;
    private final UserInfoService userInfoService;
    private final GridFsService gridFsService;

    @Override
    @DataPermission(alias = "ra")
    public QgzxRemunerationSummaryVO getMonthlySummary(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QueryWrapper<QgzxRemunerationApply> wrapper = new QueryWrapper<>();
        String customConditions = buildQueryConditions(param);
        if (StringUtils.hasLength(customConditions)) {
            wrapper.apply(customConditions);
        }
        QgzxRemunerationSummaryVO summaryVO = remunerationSummaryMapper.selectMonthlySummary(param.getSbny(), wrapper);
        if (summaryVO == null) {
            summaryVO = new QgzxRemunerationSummaryVO();
            summaryVO.setSbny(param.getSbny());
            summaryVO.setStudentCount(0);
            summaryVO.setTotalHours(BigDecimal.ZERO);
            summaryVO.setTotalAmount(BigDecimal.ZERO);
            summaryVO.setJobCount(0);
            summaryVO.setEmployerCount(0);
            summaryVO.setAverageHourlyRate(BigDecimal.ZERO);
        }
        summaryVO.setJobTypeDetails(getSummaryByJobType(param));
        summaryVO.setEmployerDetails(getSummaryByEmployer(param));
        summaryVO.setCollegeDetails(getSummaryByCollege(param));
        log.info("获取月度汇总数据成功: 申报年月={}, 学生人数={}, 总报酬={}",param.getSbny(), summaryVO.getStudentCount(), summaryVO.getTotalAmount());
        return summaryVO;
    }

    @Override
    @DataPermission(alias = "ra")
    public List<QgzxRemunerationSummaryDetailVO> getMonthlyDetails(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QueryWrapper<QgzxRemunerationApply> wrapper = new QueryWrapper<>();
        String customConditions = buildQueryConditions(param);
        if (StringUtils.hasLength(customConditions)) {
            wrapper.apply(customConditions);
        }
        List<QgzxRemunerationSummaryDetailVO> details = remunerationSummaryMapper.selectMonthlyDetails(param.getSbny(), wrapper);
        log.info("获取月度明细数据成功: 申报年月={}, 明细数量={}", param.getSbny(), details.size());
        return details;
    }

    @Override
    public PageResult<QgzxRemunerationSummaryVO> pageSummaryList(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QgzxRemunerationSummaryVO summaryVO = getMonthlySummary(param);
        List<QgzxRemunerationSummaryVO> records = new ArrayList<>();
        records.add(summaryVO);
        
        return new PageResult<>(records, 1L);
    }

    @Override
    public QgzxRemunerationSummaryVO exportSummaryData(QgzxRemunerationSummaryParam param) {
        return getMonthlySummary(param);
    }

    @Override
    @DataPermission(alias = "ra")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByJobType(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QueryWrapper<QgzxRemunerationApply> wrapper = new QueryWrapper<>();
        String customConditions = buildQueryConditions(param);
        if (StringUtils.hasLength(customConditions)) {
            wrapper.apply(customConditions);
        }
        List<QgzxRemunerationSummaryDetailVO> details = remunerationSummaryMapper.selectSummaryByJobType(param.getSbny(), wrapper);
        calculatePercentages(details);
        return details;
    }

    @Override
    @DataPermission(alias = "ra")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByEmployer(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QueryWrapper<QgzxRemunerationApply> wrapper = new QueryWrapper<>();
        String customConditions = buildQueryConditions(param);
        if (StringUtils.hasLength(customConditions)) {
            wrapper.apply(customConditions);
        }
        List<QgzxRemunerationSummaryDetailVO> details = remunerationSummaryMapper.selectSummaryByEmployer(param.getSbny(), wrapper);
        calculatePercentages(details);
        return details;
    }

    @Override
    @DataPermission(alias = "ra")
    public List<QgzxRemunerationSummaryDetailVO> getSummaryByCollege(QgzxRemunerationSummaryParam param) {
        validateSummaryParam(param);
        QueryWrapper<QgzxRemunerationApply> wrapper = new QueryWrapper<>();
        String customConditions = buildQueryConditions(param);
        if (StringUtils.hasLength(customConditions)) {
            wrapper.apply(customConditions);
        }
        List<QgzxRemunerationSummaryDetailVO> details = remunerationSummaryMapper.selectSummaryByCollege(param.getSbny(), wrapper);
        calculatePercentages(details);
        
        return details;
    }

    @Override
    public void validateSummaryParam(QgzxRemunerationSummaryParam param) {
        AssertUtil.isTrue(param != null, "查询参数不能为空");
        AssertUtil.isTrue(StringUtils.hasLength(param.getSbny()), "申报年月不能为空");
        String sbny = param.getSbny();
        AssertUtil.isTrue(sbny.matches("\\d{4}-\\d{2}"), "申报年月格式不正确，应为YYYY-MM格式");
        log.debug("参数验证通过: 申报年月={}", param.getSbny());
    }

    @Override
    public String buildQueryConditions(QgzxRemunerationSummaryParam param) {
        List<String> conditions = new ArrayList<>();
        if (StringUtils.hasLength(param.getJobTypeId())) {
            conditions.add("jt.ID = '" + param.getJobTypeId() + "'");
        }
        if (StringUtils.hasLength(param.getEmployerId())) {
            conditions.add("emp.ID = '" + param.getEmployerId() + "'");
        }
        // 学院条件 - 修改为使用 UserOrgMap 表
        if (StringUtils.hasLength(param.getXyid())) {
            conditions.add("uom.XYID = '" + param.getXyid() + "'");
        }

        if (StringUtils.hasLength(param.getJobName())) {
            conditions.add("ja.JOB_NAME LIKE '%" + param.getJobName() + "%'");
        }
        if (StringUtils.hasLength(param.getEmployerName())) {
            conditions.add("emp.NAME LIKE '%" + param.getEmployerName() + "%'");
        }
        // 学院名称模糊查询 - 修改为使用 CodeDwb 表
        if (StringUtils.hasLength(param.getCollegeName())) {
            conditions.add("dwb.NAME LIKE '%" + param.getCollegeName() + "%'");
        }
        return conditions.isEmpty() ? "" : " AND " + String.join(" AND ", conditions);
    }

    /**
     * 计算百分比
     *
     * @param details 明细列表
     */
    private void calculatePercentages(List<QgzxRemunerationSummaryDetailVO> details) {
        if (details == null || details.isEmpty()) {
            return;
        }
        BigDecimal totalAmount = details.stream()
                .map(QgzxRemunerationSummaryDetailVO::getTotalAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        for (int i = 0; i < details.size(); i++) {
            QgzxRemunerationSummaryDetailVO detail = details.get(i);
            if (detail.getTotalAmount() != null && totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal percentage = detail.getTotalAmount()
                        .divide(totalAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                detail.setPercentage(percentage);
            } else {
                detail.setPercentage(BigDecimal.ZERO);
            }
            detail.setSortOrder(i + 1);
        }
    }

    @Override
    public void exportByTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response) {
        validateSummaryParam(param);
        AssertUtil.isTrue(templateFile != null, "模板文件信息不能为空");
        if (TemplateType.hzb.equals(templateFile.getTemplateType())) {
            exportSummaryTemplate(param, templateFile, response);
        } else if (TemplateType.sqb.equals(templateFile.getTemplateType())) {
            exportDetailTemplate(param, templateFile, response);
        } else {
            AssertUtil.throwMessage("不支持的模板类型：" + templateFile.getTemplateType());
        }
        log.info("模板导出完成: 申报年月={}, 模板类型={}", param.getSbny(), templateFile.getTemplateType());
    }

    @Override
    public void exportSummaryTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response) {
        try {
            String templateName = templateFile.getTemplateName();
            GridFsResource fsResource = gridFsService.getByFilename(templateName);
            AssertUtil.isTrue(fsResource != null, "模板文件不存在：" + templateName);
            InputStream templateInputStream = fsResource.getInputStream();
            QgzxRemunerationSummaryVO summaryData = getMonthlySummary(param);
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("summary", summaryData);
            objectMap.put("sbny", param.getSbny());
            objectMap.put("exportTime", DateUtil.getDateTime());
            // 添加分组统计数据
            if (summaryData.getJobTypeDetails() != null) {
                objectMap.put("jobTypeList", summaryData.getJobTypeDetails());
            }
            if (summaryData.getEmployerDetails() != null) {
                objectMap.put("employerList", summaryData.getEmployerDetails());
            }
            if (summaryData.getCollegeDetails() != null) {
                objectMap.put("collegeList", summaryData.getCollegeDetails());
            }
            String filename = URLEncoder.encode("报酬汇总表_" + param.getSbny() + "_" +
                    DateUtil.getDateTime(DateUtil.DEFAULT_DATEMS_FORMAT), StandardCharsets.UTF_8);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xlsx");
            ServletOutputStream outputStream = response.getOutputStream();
            JxlsUtil.export(objectMap, templateInputStream, outputStream);
            response.flushBuffer();
            log.info("汇总表导出成功: 申报年月={}, 文件名={}", param.getSbny(), filename);
        } catch (IOException e) {
            log.error("汇总表导出失败: 申报年月={}", param.getSbny(), e);
            AssertUtil.throwMessage("导出失败：" + e.getMessage());
        }
    }

    @Override
    public void exportDetailTemplate(QgzxRemunerationSummaryParam param, TemplateFile templateFile, HttpServletResponse response) {
        try {
            String templateName = templateFile.getTemplateName();
            GridFsResource fsResource = gridFsService.getByFilename(templateName);
            AssertUtil.isTrue(fsResource != null, "模板文件不存在：" + templateName);
            InputStream templateInputStream = fsResource.getInputStream();
            List<QgzxRemunerationSummaryDetailVO> detailData = getMonthlyDetails(param);
            QgzxRemunerationSummaryVO summaryData = getMonthlySummary(param);
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("list", detailData);
            objectMap.put("summary", summaryData);
            objectMap.put("sbny", param.getSbny());
            objectMap.put("exportTime", DateUtil.getDateTime());
            objectMap.put("totalCount", detailData.size());
            String filename = URLEncoder.encode("报酬明细表_" + param.getSbny() + "_" +
                    DateUtil.getDateTime(DateUtil.DEFAULT_DATEMS_FORMAT), StandardCharsets.UTF_8);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xlsx");
            ServletOutputStream outputStream = response.getOutputStream();
            JxlsUtil.export(objectMap, templateInputStream, outputStream);
            response.flushBuffer();
            log.info("明细表导出成功: 申报年月={}, 记录数={}, 文件名={}", param.getSbny(), detailData.size(), filename);
        } catch (IOException e) {
            log.error("明细表导出失败: 申报年月={}", param.getSbny(), e);
            AssertUtil.throwMessage("导出失败：" + e.getMessage());
        }
    }
}
