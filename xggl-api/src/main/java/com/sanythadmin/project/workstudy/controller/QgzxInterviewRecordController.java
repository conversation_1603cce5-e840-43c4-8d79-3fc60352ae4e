package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxInterviewRecord;
import com.sanythadmin.project.workstudy.param.QgzxInterviewRecordParam;
import com.sanythadmin.project.workstudy.service.QgzxInterviewRecordService;
import com.sanythadmin.project.workstudy.vo.QgzxInterviewRecordVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/面试记录控制器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-interview-record")
@RequiredArgsConstructor
public class QgzxInterviewRecordController extends BaseController {

    private final QgzxInterviewRecordService interviewRecordService;

    /**
     * 分页查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/page")
    public PageResult<QgzxInterviewRecordVO> page(QgzxInterviewRecordParam param) {
        return interviewRecordService.page(param);
    }

    /**
     * 查询面试记录列表（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/list")
    public List<QgzxInterviewRecordVO> list(QgzxInterviewRecordParam param) {
        return interviewRecordService.list(param);
    }

    /**
     * 根据ID查询面试记录详情（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/{id}")
    public QgzxInterviewRecordVO getDetail(@PathVariable String id) {
        return interviewRecordService.getDetail(id);
    }

    /**
     * 新增或修改面试记录（权限标识：workstudy:qgzxInterviewRecord:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:operation')")
    @OperationLog(module = "面试记录", comments = "保存面试记录")
    @PostMapping("/operation")
    public void edit(@RequestBody QgzxInterviewRecord record) {
        interviewRecordService.edit(record);
    }

    /**
     * 删除面试记录（权限标识：workstudy:qgzxInterviewRecord:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:remove')")
    @OperationLog(module = "面试记录", comments = "删除面试记录")
    @PostMapping("/remove")
    public void delete(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        interviewRecordService.delete(ids.toArray(new String[]{}));
    }

    /**
     * 用人单位分页查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/employer/page")
    public PageResult<QgzxInterviewRecordVO> pageByEmployer(QgzxInterviewRecordParam param) {
        return interviewRecordService.pageByEmployer(param);
    }

    /**
     * 学生查询自己的面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/student/list")
    public List<QgzxInterviewRecordVO> listByStudent(QgzxInterviewRecordParam param) {
        return interviewRecordService.listByStudent(param);
    }

    /**
     * 根据学生申请ID查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:list')")
    @GetMapping("/by-apply/{studentApplyId}")
    public QgzxInterviewRecord getByStudentApplyId(@PathVariable String studentApplyId) {
        return interviewRecordService.getByStudentApplyId(studentApplyId);
    }

    /**
     * 批量创建面试记录（权限标识：workstudy:qgzxInterviewRecord:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxInterviewRecord:operation')")
    @OperationLog(module = "面试记录", comments = "批量创建面试记录")
    @PostMapping("/batch-create")
    public void batchCreate(@RequestBody BatchCreateInterviewRequest request) {
        interviewRecordService.batchCreate(request.getStudentApplyIds(), request.getInterviewRecord());
    }

    /**
     * 批量创建面试记录请求体
     */
    public static class BatchCreateInterviewRequest {
        private List<String> studentApplyIds;
        private QgzxInterviewRecord interviewRecord;

        public List<String> getStudentApplyIds() {
            return studentApplyIds;
        }

        public void setStudentApplyIds(List<String> studentApplyIds) {
            this.studentApplyIds = studentApplyIds;
        }

        public QgzxInterviewRecord getInterviewRecord() {
            return interviewRecord;
        }

        public void setInterviewRecord(QgzxInterviewRecord interviewRecord) {
            this.interviewRecord = interviewRecord;
        }
    }
}
