package com.sanythadmin.project.workstudy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sanythadmin.common.core.utils.LocalDateQueryUtil;
import com.sanythadmin.project.workstudy.entity.QgzxAttendanceRecord;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.enums.AttendanceStatus;
import com.sanythadmin.project.workstudy.enums.AttendanceType;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceRecordService;
import com.sanythadmin.project.workstudy.service.QgzxAttendanceStatusService;
import com.sanythadmin.project.workstudy.service.QgzxStudentApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 考勤状态判断服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QgzxAttendanceStatusServiceImpl implements QgzxAttendanceStatusService {

    private final QgzxAttendanceRecordService attendanceRecordService;
    private final QgzxStudentApplyService studentApplyService;

    // 默认容忍时间配置（分钟）
    private static final int DEFAULT_LATE_TOLERANCE = 15;      // 迟到容忍时间
    private static final int DEFAULT_EARLY_TOLERANCE = 30;     // 早退容忍时间

    @Override
    public AttendanceStatus determineAttendanceStatus(QgzxAttendanceRecord record, QgzxJobApplication jobApplication) {
        // 缺勤状态应该通过其他方式判断，比如：
        // 1. 定时任务检查未打卡的记录
        // 2. 批量处理时发现某天没有打卡记录

        // 检查是否请假
        if (isOnLeave(record.getStudentApplyId(), record.getClockTime())) {
            return AttendanceStatus.LEAVE;
        }

        // 如果没有配置工作时间，默认为正常
        if (!StringUtils.hasText(jobApplication.getStartTime()) ||
            !StringUtils.hasText(jobApplication.getEndTime())) {
            return AttendanceStatus.NORMAL;
        }

        // 根据考勤类型和时间规则判断状态
        return determineStatusByWorkSchedule(record, jobApplication);
    }

    @Override
    public AttendanceStatus determineStatusByTime(LocalDateTime clockTime, 
                                                LocalDateTime scheduledTime, 
                                                AttendanceType attendanceType, 
                                                int toleranceMinutes) {
        if (clockTime == null) {
            return AttendanceStatus.ABSENT;
        }

        if (attendanceType == AttendanceType.CLOCK_IN) {
            // 上班打卡：超过规定时间 + 容忍时间为迟到
            if (clockTime.isAfter(scheduledTime.plusMinutes(toleranceMinutes))) {
                return AttendanceStatus.LATE;
            }
        } else if (attendanceType == AttendanceType.CLOCK_OUT) {
            // 下班打卡：早于规定时间 - 容忍时间为早退
            if (clockTime.isBefore(scheduledTime.minusMinutes(toleranceMinutes))) {
                return AttendanceStatus.EARLY_LEAVE;
            }
        }

        return AttendanceStatus.NORMAL;
    }

    @Override
    public boolean isOnLeave(String studentApplyId, LocalDateTime attendanceDate) {
        // TODO: 实现请假状态检查逻辑
        return false;
    }

    @Transactional
    @Override
    public void updateAttendanceStatus(String recordId, AttendanceStatus status) {
        LambdaUpdateWrapper<QgzxAttendanceRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(QgzxAttendanceRecord::getId, recordId)
                    .set(QgzxAttendanceRecord::getAttendanceStatus, status);
        
        boolean updated = attendanceRecordService.update(updateWrapper);
        if (updated) {
            log.info("更新考勤记录状态成功，记录ID: {}, 新状态: {}", recordId, status.getText());
        } else {
            log.warn("更新考勤记录状态失败，记录ID: {}", recordId);
        }
    }

    @Transactional
    @Override
    public void batchUpdateAttendanceStatus(String studentApplyId, LocalDateTime startDate, LocalDateTime endDate) {
        // 查询指定时间范围内的考勤记录
        LambdaQueryWrapper<QgzxAttendanceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                   .ge(QgzxAttendanceRecord::getAttendanceDate, startDate.toLocalDate())
                   .le(QgzxAttendanceRecord::getAttendanceDate, endDate.toLocalDate());

        List<QgzxAttendanceRecord> records = attendanceRecordService.list(queryWrapper);
        
        // TODO: 获取岗位信息用于状态判断
        // QgzxJobApplication jobApplication = getJobApplicationByStudentApply(studentApplyId);
        
        int updatedCount = 0;
        for (QgzxAttendanceRecord record : records) {
            // AttendanceStatus newStatus = determineAttendanceStatus(record, jobApplication);
            // 暂时使用简单逻辑
            AttendanceStatus newStatus = determineSimpleStatus(record);
            
            if (newStatus != record.getAttendanceStatus()) {
                updateAttendanceStatus(record.getId(), newStatus);
                updatedCount++;
            }
        }
        
        log.info("批量更新考勤状态完成，学生申请ID: {}, 更新记录数: {}", studentApplyId, updatedCount);
    }

    /**
     * 根据工作时间安排判断考勤状态
     */
    private AttendanceStatus determineStatusByWorkSchedule(QgzxAttendanceRecord record, QgzxJobApplication jobApplication) {
        try {
            LocalDateTime clockTime = record.getClockTime();
            LocalDate attendanceDate = record.getAttendanceDate();
            AttendanceType attendanceType = record.getAttendanceType();

            // 解析工作时间
            LocalTime startTime = LocalTime.parse(jobApplication.getStartTime());
            LocalTime endTime = LocalTime.parse(jobApplication.getEndTime());

            LocalDateTime scheduledDateTime;
            int toleranceMinutes;

            if (attendanceType == AttendanceType.CLOCK_IN) {
                // 上班打卡
                scheduledDateTime = attendanceDate.atTime(startTime);
                toleranceMinutes = DEFAULT_LATE_TOLERANCE;
            } else {
                // 下班打卡
                scheduledDateTime = attendanceDate.atTime(endTime);
                toleranceMinutes = DEFAULT_EARLY_TOLERANCE;
            }

            return determineStatusByTime(clockTime, scheduledDateTime, attendanceType, toleranceMinutes);

        } catch (Exception e) {
            log.warn("解析工作时间失败，使用默认状态判断逻辑，记录ID: {}, 错误: {}", record.getId(), e.getMessage());
            return AttendanceStatus.NORMAL;
        }
    }

    /**
     * 简单的状态判断逻辑（用于批量更新时的临时方案）
     */
    private AttendanceStatus determineSimpleStatus(QgzxAttendanceRecord record) {
        if (record.getClockTime() == null) {
            return AttendanceStatus.ABSENT;
        }
        
        // 基于isNormal字段的简单判断
        if (record.getIsNormal() != null && record.getIsNormal().getValue() == 0) {
            // 如果isNormal为false，可能是迟到或早退，这里简化为迟到
            return AttendanceStatus.LATE;
        }
        
        return AttendanceStatus.NORMAL;
    }

    @Transactional
    @Override
    public void checkAndCreateAbsentRecords(LocalDateTime targetDate) {
        LocalDate checkDate = targetDate.toLocalDate();

        // TODO: 这里需要根据实际业务需求实现
        // 1. 查询所有应该在指定日期打卡的学生申请
        // 2. 检查每个学生是否有打卡记录
        // 3. 如果没有打卡记录，创建缺勤记录

        log.info("开始检查 {} 的缺勤记录", checkDate);

        // 示例实现逻辑：
        // List<QgzxStudentApply> activeStudents = studentApplyService.getActiveStudents(checkDate);
        // for (QgzxStudentApply student : activeStudents) {
        //     checkStudentAttendance(student, checkDate);
        // }

        log.info("完成检查 {} 的缺勤记录", checkDate);
    }

    @Transactional
    @Override
    public void createAbsentRecord(String studentApplyId, LocalDateTime targetDate, AttendanceType attendanceType) {
        LocalDate attendanceDate = targetDate.toLocalDate();

        // 检查是否已存在该日期和类型的考勤记录
        LambdaQueryWrapper<QgzxAttendanceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QgzxAttendanceRecord::getStudentApplyId, studentApplyId)
                   .eq(QgzxAttendanceRecord::getAttendanceType, attendanceType);
        LocalDateQueryUtil.eqLocalDate(queryWrapper, QgzxAttendanceRecord::getAttendanceDate, attendanceDate);

        QgzxAttendanceRecord existingRecord = attendanceRecordService.getOne(queryWrapper);
        if (existingRecord != null) {
            log.debug("考勤记录已存在，跳过创建缺勤记录：studentApplyId={}, date={}, type={}",
                     studentApplyId, attendanceDate, attendanceType);
            return;
        }

        // 检查是否请假
        if (isOnLeave(studentApplyId, targetDate)) {
            // 创建请假记录
            createLeaveRecord(studentApplyId, attendanceDate, attendanceType);
            return;
        }

        // 创建缺勤记录
        QgzxAttendanceRecord absentRecord = new QgzxAttendanceRecord();
        absentRecord.setStudentApplyId(studentApplyId);
        absentRecord.setAttendanceDate(attendanceDate);
        absentRecord.setAttendanceType(attendanceType);
        absentRecord.setClockTime(null); // 缺勤记录没有打卡时间
        absentRecord.setAttendanceStatus(AttendanceStatus.ABSENT);
        absentRecord.setCreateTime(LocalDateTime.now());
        absentRecord.setRemark("系统自动生成的缺勤记录");

        attendanceRecordService.save(absentRecord);

        log.info("创建缺勤记录成功：studentApplyId={}, date={}, type={}",
                studentApplyId, attendanceDate, attendanceType);
    }

    /**
     * 创建请假记录
     */
    private void createLeaveRecord(String studentApplyId, LocalDate attendanceDate, AttendanceType attendanceType) {
        QgzxAttendanceRecord leaveRecord = new QgzxAttendanceRecord();
        leaveRecord.setStudentApplyId(studentApplyId);
        leaveRecord.setAttendanceDate(attendanceDate);
        leaveRecord.setAttendanceType(attendanceType);
        leaveRecord.setClockTime(null); // 请假记录没有打卡时间
        leaveRecord.setAttendanceStatus(AttendanceStatus.LEAVE);
        leaveRecord.setCreateTime(LocalDateTime.now());
        leaveRecord.setRemark("系统自动生成的请假记录");

        attendanceRecordService.save(leaveRecord);

        log.info("创建请假记录成功：studentApplyId={}, date={}, type={}",
                studentApplyId, attendanceDate, attendanceType);
    }
}
