package com.sanythadmin.project.workstudy.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 勤工助学报酬月度汇总明细结果VO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QgzxRemunerationSummaryDetailVO {

    /**
     * 分组ID（岗位类别ID、用人单位ID、学院ID等）
     */
    private String groupId;

    /**
     * 分组名称（岗位类别名称、用人单位名称、学院名称等）
     */
    private String groupName;

    /**
     * 分组类型（JOB_TYPE、EMPLOYER、COLLEGE等）
     */
    private String groupType;

    /**
     * 该分组下的学生人数
     */
    private Integer studentCount;

    /**
     * 该分组下的总工时
     */
    private BigDecimal totalHours;

    /**
     * 该分组下的总报酬金额
     */
    private BigDecimal totalAmount;

    /**
     * 该分组下的岗位数量
     */
    private Integer jobCount;

    /**
     * 该分组下的平均时薪
     */
    private BigDecimal averageHourlyRate;

    /**
     * 该分组下的平均工时
     */
    private BigDecimal averageHours;

    /**
     * 该分组下的平均报酬
     */
    private BigDecimal averageAmount;

    /**
     * 占总体的百分比
     */
    private BigDecimal percentage;

    /**
     * 排序号
     */
    private Integer sortOrder;

    // ========== 明细数据字段（用于导出） ==========

    /**
     * 学号
     */
    private String xgh;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 学院名称
     */
    private String collegeName;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 岗位类别名称
     */
    private String jobTypeName;

    /**
     * 用人单位名称
     */
    private String employerName;

    /**
     * 工作时长
     */
    private BigDecimal workHours;

    /**
     * 时薪
     */
    private BigDecimal hourlyRate;

    /**
     * 报酬金额
     */
    private BigDecimal remunerationAmount;

    /**
     * 申报年月
     */
    private String sbny;

    /**
     * 审核状态
     */
    private String spzt;
}
