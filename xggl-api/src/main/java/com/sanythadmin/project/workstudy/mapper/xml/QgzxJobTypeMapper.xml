<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxJobTypeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, YZGBC, YZDGS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.yzgbc != null">
                        AND a.YZGBC LIKE concat(concat('%',#{param.yzgbc), '%')
                    </if>
                    <if test="param.yzdgs != null">
                        AND a.YZDGS = #{param.yzdgs}
                    </if>
    </sql>
</mapper>
