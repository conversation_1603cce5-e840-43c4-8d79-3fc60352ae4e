<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workstudy.mapper.QgzxKsbMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XNXQ, NAME, KSSJ, JSSJ, SORT, WEEK_DAY
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xnxq != null">
                        AND a.XNXQ LIKE concat(concat('%',#{param.xnxq), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.kssj != null">
                        AND a.KSSJ LIKE concat(concat('%',#{param.kssj), '%')
                    </if>
                    <if test="param.jssj != null">
                        AND a.JSSJ LIKE concat(concat('%',#{param.jssj), '%')
                    </if>
                    <if test="param.weekDay != null">
                        AND a.WEEK_DAY = #{param.weekDay}
                    </if>
    </sql>
</mapper>
