package com.sanythadmin.project.workstudy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workstudy.entity.QgzxJobAdjustmentApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 岗位调剂申请Mapper
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface QgzxJobAdjustmentApplyMapper extends BaseMapper<QgzxJobAdjustmentApply> {

    /**
     * 根据学生申请ID查询调剂申请记录
     */
    @Select("SELECT * FROM SYT_QGZX_JOB_ADJUSTMENT_APPLY WHERE STUDENT_APPLY_ID = #{studentApplyId}")
    QgzxJobAdjustmentApply selectByStudentApplyId(@Param("studentApplyId") String studentApplyId);

    /**
     * 根据目标岗位ID查询待确认的调剂申请
     */
    @Select("SELECT * FROM SYT_QGZX_JOB_ADJUSTMENT_APPLY WHERE TO_JOB_ID = #{toJobId} AND STATUS = 'DaiJieShouDanWeiQueRen'")
    List<QgzxJobAdjustmentApply> selectPendingByToJobId(@Param("toJobId") String toJobId);

    /**
     * 根据学生学号查询待确认的调剂申请
     */
    @Select("SELECT a.* FROM SYT_QGZX_JOB_ADJUSTMENT_APPLY a " +
            "INNER JOIN SYT_QGZX_STUDENT_APPLY s ON a.STUDENT_APPLY_ID = s.ID " +
            "WHERE s.XGH = #{xgh} AND a.STATUS = 'DaiXueShengQueRen'")
    List<QgzxJobAdjustmentApply> selectPendingByStudentXgh(@Param("xgh") String xgh);
}
