package com.sanythadmin.project.workstudy.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplicationAddress;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationAddressParam;
import com.sanythadmin.project.workstudy.service.QgzxJobApplicationAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 勤工助学/岗位地址控制器
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@RestController
@RequestMapping("/api/workstudy/qgzx-job-application-address")
@RequiredArgsConstructor
public class QgzxJobApplicationAddressController extends BaseController {

    private final QgzxJobApplicationAddressService qgzxJobApplicationAddressService;

    /**
     * 分页查询岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:list')")
    @GetMapping("/page")
    public PageResult<QgzxJobApplicationAddress> page(QgzxJobApplicationAddressParam param) {
        PageParam<QgzxJobApplicationAddress, QgzxJobApplicationAddressParam> page = new PageParam<>(param);
        page = qgzxJobApplicationAddressService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:list')")
    @GetMapping()
    public List<QgzxJobApplicationAddress> list(QgzxJobApplicationAddressParam param) {
        PageParam<QgzxJobApplicationAddress, QgzxJobApplicationAddressParam> page = new PageParam<>(param);
        return qgzxJobApplicationAddressService.list(page.getOrderWrapper());
    }

    /**
     * 根据岗位ID查询地址列表（权限标识：workstudy:qgzxJobApplicationAddress:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:list')")
    @GetMapping("/job/{jobId}")
    public List<QgzxJobApplicationAddress> getByJobId(@PathVariable("jobId") String jobId) {
        return qgzxJobApplicationAddressService.getByJobId(jobId);
    }

    /**
     * 根据岗位ID查询启用的地址列表（权限标识：workstudy:qgzxJobApplicationAddress:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:list')")
    @GetMapping("/job/{jobId}/enabled")
    public List<QgzxJobApplicationAddress> getEnabledByJobId(@PathVariable("jobId") String jobId) {
        return qgzxJobApplicationAddressService.getEnabledByJobId(jobId);
    }

    /**
     * 根据id查询岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:list）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:list')")
    @GetMapping("/{id}")
    public QgzxJobApplicationAddress get(@PathVariable("id") String id) {
        return qgzxJobApplicationAddressService.getById(id);
    }

    /**
     * 添加或修改岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:operation')")
    @OperationLog(module = "岗位地址", comments = "保存岗位地址")
    @PostMapping("/operation")
    public void save(@RequestBody QgzxJobApplicationAddress qgzxJobApplicationAddress) {
        if (StringUtils.hasLength(qgzxJobApplicationAddress.getId())) {
            qgzxJobApplicationAddressService.updateById(qgzxJobApplicationAddress);
        } else {
            qgzxJobApplicationAddressService.save(qgzxJobApplicationAddress);
        }
    }

    /**
     * 批量保存岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:operation）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:operation')")
    @OperationLog(module = "岗位地址", comments = "批量保存岗位地址")
    @PostMapping("/batch-save")
    public void batchSave(@RequestParam("jobId") String jobId, 
                         @RequestBody List<QgzxJobApplicationAddress> addresses) {
        qgzxJobApplicationAddressService.saveJobAddresses(jobId, addresses);
    }

    /**
     * 批量删除岗位地址（权限标识：workstudy:qgzxJobApplicationAddress:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:remove')")
    @OperationLog(module = "岗位地址", comments = "批量删除岗位地址")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        qgzxJobApplicationAddressService.removeByIds(ids);
    }

    /**
     * 删除岗位的所有地址（权限标识：workstudy:qgzxJobApplicationAddress:remove）
     */
    @PreAuthorize("hasAuthority('workstudy:qgzxJobApplicationAddress:remove')")
    @OperationLog(module = "岗位地址", comments = "删除岗位的所有地址")
    @DeleteMapping("/job/{jobId}")
    public void removeByJobId(@PathVariable("jobId") String jobId) {
        qgzxJobApplicationAddressService.deleteByJobId(jobId);
    }

}
