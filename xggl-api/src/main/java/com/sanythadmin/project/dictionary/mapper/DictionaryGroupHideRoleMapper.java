package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryGroupHideRoleParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息组不可见角色Mapper
 *
 * <AUTHOR>
 * @since 2024-03-28 17:07:40
 */
public interface DictionaryGroupHideRoleMapper extends BaseMapper<DictionaryGroupHideRole> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryGroupHideRole>
     */
    List<DictionaryGroupHideRole> selectPageRel(@Param("page") IPage<DictionaryGroupHideRole> page,
                             @Param("param") DictionaryGroupHideRoleParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryGroupHideRole> selectListRel(@Param("param") DictionaryGroupHideRoleParam param);

}
