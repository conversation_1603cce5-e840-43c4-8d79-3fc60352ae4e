package com.sanythadmin.project.dictionary.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupEduLevelService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupHideRoleService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 用户信息组控制器
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryGroup")
public class DictionaryGroupController extends BaseController {
    @Resource
    private DictionaryGroupService dictionaryGroupService;
    @Resource
    private DictionaryGroupHideRoleService dictionaryGroupHideRoleService;
    @Resource
    private DictionaryGroupEduLevelService dictionaryGroupEduLevelService;

    /**
     * 分页查询用户信息组（权限标识：dictionary:dictionaryGroup:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroup:list')")
    @GetMapping("/page")
    public PageResult<DictionaryGroup> page(DictionaryGroupParam param) {
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>(param);
        page = dictionaryGroupService.page(page, page.getWrapper());
        List<DictionaryGroup> records = page.getRecords();
        records.forEach(group -> {
            extracted(group);
        });
        return new PageResult<>(records, page.getTotal());
    }

    private void extracted(DictionaryGroup group) {
        List<DictionaryGroupHideRole> hideRoles = dictionaryGroupHideRoleService.list(new QueryWrapper<DictionaryGroupHideRole>().eq("GROUP_ID", group.getId()));
        List<DictionaryGroupEduLevel> eduLevels = dictionaryGroupEduLevelService.list(new QueryWrapper<DictionaryGroupEduLevel>().eq("GROUP_ID", group.getId()));
        group.setHideRoles(hideRoles);
        group.setEduLevels(eduLevels);
    }

    /**
     * 查询全部用户信息组（权限标识：dictionary:dictionaryGroup:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroup:list')")
    @GetMapping()
    public List<DictionaryGroup> list(DictionaryGroupParam param) {
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>(param);
        return dictionaryGroupService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息组（权限标识：dictionary:dictionaryGroup:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroup:list')")
    @GetMapping("/{id}")
    public DictionaryGroup get(@PathVariable("id") String id) {
        DictionaryGroup group = dictionaryGroupService.getById(id);
        extracted(group);
        return group;
    }

    /**
     * 添加或修改用户信息组（权限标识：dictionary:dictionaryGroup:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroup:operation')")
    @OperationLog(module = "用户信息组", comments = "保存用户信息组")
    @PostMapping("/operation")
    public void save(@Validated DictionaryGroup group, MultipartHttpServletRequest request) {
        Map<String, MultipartFile> fileMap = request.getFileMap();
        dictionaryGroupService.edit(group, fileMap);
    }

    /**
     * 批量删除用户信息组（权限标识：dictionary:dictionaryGroup:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroup:remove')")
    @OperationLog(module = "用户信息组", comments = "批量删除用户信息组")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryGroupService.remove(ids);
    }
}
