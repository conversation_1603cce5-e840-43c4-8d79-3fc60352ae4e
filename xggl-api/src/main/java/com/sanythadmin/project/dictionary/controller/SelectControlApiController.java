package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.dictionary.service.SelectControlApiService;
import com.sanythadmin.project.dictionary.entity.SelectControlApi;
import com.sanythadmin.project.dictionary.param.SelectControlApiParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 下拉控件数据源配置控制器
 *
 * <AUTHOR>
 * @since 2024-07-05 16:30:47
 */
@RestController
@RequestMapping("/api/dictionary/select-control-api")
public class SelectControlApiController extends BaseController {
    @Resource
    private SelectControlApiService selectControlApiService;

    /**
     * 分页查询下拉控件数据源配置（权限标识：dictionary:selectControlApi:list）
     */
    @PreAuthorize("hasAuthority('dictionary:selectControlApi:list')")
    @GetMapping("/page")
    public PageResult<SelectControlApi> page(SelectControlApiParam param) {
        PageParam<SelectControlApi, SelectControlApiParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = selectControlApiService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部下拉控件数据源配置（权限标识：dictionary:selectControlApi:list）
     */
    @PreAuthorize("hasAuthority('dictionary:selectControlApi:list')")
    @GetMapping()
    public List<SelectControlApi> list(SelectControlApiParam param) {
        PageParam<SelectControlApi, SelectControlApiParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return selectControlApiService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询下拉控件数据源配置（权限标识：dictionary:selectControlApi:list）
     */
    @PreAuthorize("hasAuthority('dictionary:selectControlApi:list')")
    @GetMapping("/{id}")
    public SelectControlApi get(@PathVariable("id") String id) {
        return selectControlApiService.getById(id);
    }

    /**
     * 添加或修改下拉控件数据源配置（权限标识：dictionary:selectControlApi:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:selectControlApi:operation')")
    @OperationLog(module = "下拉控件数据源配置", comments = "保存下拉控件数据源配置")
    @PostMapping("/operation")
    public void save(@RequestBody SelectControlApi selectControlApi) {
        if (selectControlApi.getSort() == null) {
            SortHelper<SelectControlApi> sortHelper = new SortHelper<>(selectControlApiService, "sort");
            selectControlApi.setSort(sortHelper.next());
        }

        if (StringUtils.hasLength(selectControlApi.getId())) {
            selectControlApiService.updateById(selectControlApi);
        } else {
            selectControlApiService.save(selectControlApi);
        }
    }

    /**
     * 批量删除下拉控件数据源配置（权限标识：dictionary:selectControlApi:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:selectControlApi:remove')")
    @OperationLog(module = "下拉控件数据源配置", comments = "批量删除下拉控件数据源配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        selectControlApiService.removeByIds(ids);
    }
}
