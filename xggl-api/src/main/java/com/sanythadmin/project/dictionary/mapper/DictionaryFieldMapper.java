package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息字段Mapper
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
public interface DictionaryFieldMapper extends BaseMapper<DictionaryField> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryField>
     */
    List<DictionaryField> selectPageRel(@Param("page") IPage<DictionaryField> page,
                             @Param("param") DictionaryFieldParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryField> selectListRel(@Param("param") DictionaryFieldParam param);

}
