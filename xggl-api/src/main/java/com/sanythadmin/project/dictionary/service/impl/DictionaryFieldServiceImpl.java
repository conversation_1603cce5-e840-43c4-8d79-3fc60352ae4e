package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.Required;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.dictionary.entity.*;
import com.sanythadmin.project.dictionary.mapper.*;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import com.sanythadmin.project.userInfo.service.PersonInfoService;
import jakarta.annotation.Resource;
import jakarta.persistence.Transient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 用户信息字段Service实现
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
@Service
public class DictionaryFieldServiceImpl extends ServiceImpl<DictionaryFieldMapper, DictionaryField> implements DictionaryFieldService {
    @Resource
    private DictionaryGroupMapper dictionaryGroupMapper;
    @Resource
    private DictionaryFieldHideRoleMapper fieldHideRoleMapper;
    @Resource
    private DictionaryFieldEduLevelMapper fieldEduLevelMapper;
    @Resource
    private DictionaryFieldLinkMapper fieldLinkMapper;
    @Resource
    private PersonInfoFactory personInfoFactory;

    @Override
    public PageResult<DictionaryField> pageRel(DictionaryFieldParam param) {
        PageParam<DictionaryField, DictionaryFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryField> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryField> listRel(DictionaryFieldParam param) {
        List<DictionaryField> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryField, DictionaryFieldParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public List<DictionaryField> queryList(DictionaryFieldParam param) {
        PageParam<DictionaryField, DictionaryFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return list(page.getOrderWrapper());
    }


    @Transactional
    @Override
    public void edit(DictionaryField field) {
        if (StringUtils.hasLength(field.getId())) {
            fieldHideRoleMapper.delete(new QueryWrapper<DictionaryFieldHideRole>().eq("FIELD_ID", field.getId()));
            fieldEduLevelMapper.delete(new QueryWrapper<DictionaryFieldEduLevel>().eq("FIELD_ID", field.getId()));
        }

        if (field.getSort() == null) {
            QueryWrapper<DictionaryField> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("USER_TYPE", field.getUserType());
            SortHelper<DictionaryField> sortHelper = new SortHelper<>(this, "sort", queryWrapper);
            field.setSort((long) sortHelper.next());
        }

        DictionaryGroup group = dictionaryGroupMapper.selectById(field.getGroupId());
        if (!Objects.equals(JudgeMark.YES, group.getListFlag())) {
            LinkedHashMap<String, String> defaultImportFields = personInfoFactory.apply(field.getUserType(),
                    PersonInfoService::getDefaultImportFields);
            if (!CollectionUtils.isEmpty(defaultImportFields) && defaultImportFields.containsKey(field.getFieldEn()))
                field.setImportFieldFlag(Required.YES);
            Field[] fields = CommonUtil.getAllFields(UserInfo.class);
            Set<String> keySet = UserInfoUtil.codeFieldMap.keySet();
            for (Field f : fields) {
                Transient annotation = f.getAnnotation(Transient.class);
                if (annotation != null && Objects.equals(f.getName(), field.getFieldEn())
                        && keySet.contains(f.getName())) {
                    field.setTempField(JudgeMark.YES);
                    field.setShowFlag(JudgeMark.NO);
                    field.setRequired(JudgeMark.NO);
                    field.setImportFieldFlag(null);
                    field.setSelfModifyFlag(JudgeMark.NO);
                    field.setSelfModifyVerifyFlag(JudgeMark.NO);
                    break;
                }
            }
        }
        saveOrUpdate(field);
        List<DictionaryFieldHideRole> hideRoles = field.getHideRoles();
        if (!CollectionUtils.isEmpty(hideRoles)) {
            hideRoles.forEach(hideRole -> {
                hideRole.setFieldId(field.getId());
                fieldHideRoleMapper.insert(hideRole);
            });
        }

        List<DictionaryFieldEduLevel> eduLevels = field.getEduLevels();
        if (!CollectionUtils.isEmpty(eduLevels)) {
            eduLevels.forEach(eduLevel -> {
                eduLevel.setFieldId(field.getId());
                fieldEduLevelMapper.insert(eduLevel);
            });
        }
    }

    @Transactional
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            fieldHideRoleMapper.delete(new QueryWrapper<DictionaryFieldHideRole>().eq("FIELD_ID", id));
            fieldEduLevelMapper.delete(new QueryWrapper<DictionaryFieldEduLevel>().eq("FIELD_ID", id));
            fieldLinkMapper.delete(new QueryWrapper<DictionaryFieldLink>().eq("FIELD_ID", id));
            removeById(id);
        }
    }
}
