package com.sanythadmin.project.dictionary.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下拉控件数据源配置查询参数
 *
 * <AUTHOR>
 * @since 2024-07-05 16:30:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SelectControlApiParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 接口
     */
    private String url;

    private String textField;

    private String valueField;

    /**
     * 名称
     */
    private String name;

    @Override
    public String getSort() {
        return super.getSort();
    }
}
