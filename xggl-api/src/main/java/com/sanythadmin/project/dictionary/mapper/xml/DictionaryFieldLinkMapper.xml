<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.DictionaryFieldLinkMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, FIELD_EN, FIELD_VAL, SHOW_FLAG, LINK_FIELD_DATA, LINK_FIELD_DATA_URL, FIELD_ID, LINK_FIELD, GROUP_ID, USER_TYPE,FIELD_VAL_ID
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_DICTIONARY_FIELD_LINK a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.fieldEn != null">
                AND a.FIELD_EN LIKE '%'||#{param.fieldEn}||'%'
            </if>
            <if test="param.fieldVal != null">
                AND a.FIELD_VAL LIKE '%'||#{param.fieldVal}||'%'
            </if>
            <if test="param.showFlag != null">
                AND a.SHOW_FLAG = #{param.showFlag}
            </if>
            <if test="param.linkFieldData != null">
                AND a.LINK_FIELD_DATA LIKE '%'||#{param.linkFieldData}||'%'
            </if>
            <if test="param.linkFieldDataUrl != null">
                AND a.LINK_FIELD_DATA_URL LIKE '%'||#{param.linkFieldDataUrl}||'%'
            </if>
            <if test="param.fieldId != null">
                AND a.FIELD_ID LIKE '%'||#{param.fieldId}||'%'
            </if>
            <if test="param.linkField != null">
                AND a.LINK_FIELD LIKE '%'||#{param.linkField}||'%'
            </if>
            <if test="param.groupId != null">
                AND a.GROUP_ID LIKE '%'||#{param.groupId}||'%'
            </if>
            <if test="param.userType != null">
                AND a.USER_TYPE = #{param.userType}
            </if>
            <if test="param.fieldValId != null">
                AND a.FIELD_VAL_ID =#{param.fieldValId}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryFieldLink">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryFieldLink">
        <include refid="selectSql"></include>
    </select>

</mapper>
