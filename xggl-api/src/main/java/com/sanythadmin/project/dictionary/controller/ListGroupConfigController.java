package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.ListGroupConfig;
import com.sanythadmin.project.dictionary.param.ListGroupConfigParam;
import com.sanythadmin.project.dictionary.service.ListGroupConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 多条数据组配置控制器
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
@RestController
@RequestMapping("/api/dictionary/listGroupConfig")
public class ListGroupConfigController extends BaseController {
    @Resource
    private ListGroupConfigService listGroupConfigService;

    /**
     * 分页查询多条数据组配置（权限标识：dictionary:listGroupConfig:list）
     */
    @PreAuthorize("hasAuthority('dictionary:listGroupConfig:list')")
    @GetMapping("/page")
    public PageResult<ListGroupConfig> page(ListGroupConfigParam param) {
        PageParam<ListGroupConfig, ListGroupConfigParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = listGroupConfigService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部多条数据组配置（权限标识：dictionary:listGroupConfig:list）
     */
    @PreAuthorize("hasAuthority('dictionary:listGroupConfig:list')")
    @GetMapping()
    public List<ListGroupConfig> list(ListGroupConfigParam param) {
        PageParam<ListGroupConfig, ListGroupConfigParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return listGroupConfigService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询多条数据组配置（权限标识：dictionary:listGroupConfig:list）
     */
    @PreAuthorize("hasAuthority('dictionary:listGroupConfig:list')")
    @GetMapping("/{id}")
    public ListGroupConfig get(@PathVariable("id") String id) {
        return listGroupConfigService.getById(id);
    }

    /**
     * 添加或修改多条数据组配置（权限标识：dictionary:listGroupConfig:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:listGroupConfig:operation')")
    @OperationLog(module = "多条数据组配置", comments = "保存多条数据组配置")
    @PostMapping("/operation")
    public void save(@RequestBody ListGroupConfig listGroupConfig) {
        if (StringUtils.hasLength(listGroupConfig.getId())) {
            listGroupConfigService.updateById(listGroupConfig);
        } else {
            listGroupConfigService.save(listGroupConfig);
        }
    }

    /**
     * 批量删除多条数据组配置（权限标识：dictionary:listGroupConfig:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:listGroupConfig:remove')")
    @OperationLog(module = "多条数据组配置", comments = "批量删除多条数据组配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        listGroupConfigService.removeByIds(ids);
    }
}
