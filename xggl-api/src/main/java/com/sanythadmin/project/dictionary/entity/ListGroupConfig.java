package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 多条数据组配置
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_LIST_GROUP_CONFIG")
@Entity
@Table(name = "SYT_LIST_GROUP_CONFIG")
public class ListGroupConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 组名称
     */
    @Column(name = "GROUP_NAME")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 加载数据接口
     */
    @Column(name = "LOAD_DATA_URL")
    @TableField("LOAD_DATA_URL")
    private String loadDataUrl;

    /**
     * 新增及修改数据接口
     */
    @Column(name = "OPERATION_DATA_URL")
    @TableField("OPERATION_DATA_URL")
    private String operationDataUrl;

    /**
     * 删除数据接口
     */
    @Column(name = "DELETE_DATA_URL")
    @TableField("DELETE_DATA_URL")
    private String deleteDataUrl;
    /**
     * 字段配置接口
     */
    @Column(name = "DICTIONARY_FIELD_URL")
    @TableField("DICTIONARY_FIELD_URL")
    private String dictionaryFieldUrl;
}
