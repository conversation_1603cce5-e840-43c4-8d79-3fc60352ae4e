package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryGroupEduLevelParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息组与学生培养层次关系Mapper
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
public interface DictionaryGroupEduLevelMapper extends BaseMapper<DictionaryGroupEduLevel> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryGroupEduLevel>
     */
    List<DictionaryGroupEduLevel> selectPageRel(@Param("page") IPage<DictionaryGroupEduLevel> page,
                             @Param("param") DictionaryGroupEduLevelParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryGroupEduLevel> selectListRel(@Param("param") DictionaryGroupEduLevelParam param);

}
