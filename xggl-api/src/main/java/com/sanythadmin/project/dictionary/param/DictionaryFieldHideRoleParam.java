package com.sanythadmin.project.dictionary.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息字段不可见角色查询参数
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictionaryFieldHideRoleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 用户字段ID
     */
    private String fieldId;

    /**
     * 角色ID
     */
    private String roleId;

}
