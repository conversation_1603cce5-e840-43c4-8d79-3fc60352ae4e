package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 下拉控件数据源配置
 *
 * <AUTHOR>
 * @since 2024-07-05 16:30:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SELECT_CONTROL_API")
@Entity
@Table(name = "SYT_SELECT_CONTROL_API")
public class SelectControlApi implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 接口
     */
    @Column(name = "URL")
    @TableField("URL")
    private String url;

    @Column(name = "TEXT_FIELD")
    @TableField("TEXT_FIELD")
    private String textField;

    @Column(name = "VALUE_FIELD")
    @TableField("VALUE_FIELD")
    private String valueField;

    /**
     * 名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
