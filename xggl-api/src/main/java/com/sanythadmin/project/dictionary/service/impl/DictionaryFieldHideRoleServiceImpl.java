package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldHideRole;
import com.sanythadmin.project.dictionary.mapper.DictionaryFieldHideRoleMapper;
import com.sanythadmin.project.dictionary.param.DictionaryFieldHideRoleParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldHideRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息字段不可见角色Service实现
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
@Service
public class DictionaryFieldHideRoleServiceImpl extends ServiceImpl<DictionaryFieldHideRoleMapper, DictionaryFieldHideRole> implements DictionaryFieldHideRoleService {

    @Override
    public PageResult<DictionaryFieldHideRole> pageRel(DictionaryFieldHideRoleParam param) {
        PageParam<DictionaryFieldHideRole, DictionaryFieldHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryFieldHideRole> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryFieldHideRole> listRel(DictionaryFieldHideRoleParam param) {
        List<DictionaryFieldHideRole> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryFieldHideRole, DictionaryFieldHideRoleParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryFieldHideRole getByIdRel(String id) {
        DictionaryFieldHideRoleParam param = new DictionaryFieldHideRoleParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
