<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.DictionaryGroupMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT <include refid="Base_Column_List"/>
        FROM SYT_DICTIONARY_GROUP a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.groupName != null">
                AND a.GROUP_NAME LIKE '%'||#{param.groupName}||'%'
            </if>
            <if test="param.showFlag != null">
                AND a.SHOW_FLAG = #{param.showFlag}
            </if>
            <if test="param.activeFlag != null">
                AND a.ACTIVE_FLAG = #{param.activeFlag}
            </if>
            <if test="param.addFlag != null">
                AND a.ADD_FLAG = #{param.addFlag}
            </if>
            <if test="param.listFlag != null">
                AND a.LIST_FLAG = #{param.listFlag}
            </if>
            <if test="param.modifyFlag != null">
                AND a.MODIFY_FLAG = #{param.modifyFlag}
            </if>
            <if test="param.emptyDataHide != null">
                AND a.EMPTY_DATA_HIDE = #{param.emptyDataHide}
            </if>
            <if test="param.userType != null">
                AND a.USER_TYPE = #{param.userType}
            </if>
            <if test="param.listGroupId != null">
                AND a.LIST_GROUP_ID = #{param.listGroupId}
            </if>
        </where>
        ORDER BY a.SORT asc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryGroup">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryGroup">
        <include refid="selectSql"></include>
    </select>

</mapper>
