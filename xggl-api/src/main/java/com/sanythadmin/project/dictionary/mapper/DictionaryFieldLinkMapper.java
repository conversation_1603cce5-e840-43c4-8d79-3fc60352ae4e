package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldLink;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息字段关联关系Mapper
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
public interface DictionaryFieldLinkMapper extends BaseMapper<DictionaryFieldLink> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryFieldLink>
     */
    List<DictionaryFieldLink> selectPageRel(@Param("page") IPage<DictionaryFieldLink> page,
                             @Param("param") DictionaryFieldLinkParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryFieldLink> selectListRel(@Param("param") DictionaryFieldLinkParam param);

}
