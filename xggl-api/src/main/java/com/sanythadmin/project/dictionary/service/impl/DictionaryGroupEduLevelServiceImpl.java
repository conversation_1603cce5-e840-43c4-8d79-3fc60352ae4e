package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.mapper.DictionaryGroupEduLevelMapper;
import com.sanythadmin.project.dictionary.param.DictionaryGroupEduLevelParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupEduLevelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息组与学生培养层次关系Service实现
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
@Service
public class DictionaryGroupEduLevelServiceImpl extends ServiceImpl<DictionaryGroupEduLevelMapper, DictionaryGroupEduLevel> implements DictionaryGroupEduLevelService {

    @Override
    public PageResult<DictionaryGroupEduLevel> pageRel(DictionaryGroupEduLevelParam param) {
        PageParam<DictionaryGroupEduLevel, DictionaryGroupEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryGroupEduLevel> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryGroupEduLevel> listRel(DictionaryGroupEduLevelParam param) {
        List<DictionaryGroupEduLevel> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryGroupEduLevel, DictionaryGroupEduLevelParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryGroupEduLevel getByIdRel(String id) {
        DictionaryGroupEduLevelParam param = new DictionaryGroupEduLevelParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
