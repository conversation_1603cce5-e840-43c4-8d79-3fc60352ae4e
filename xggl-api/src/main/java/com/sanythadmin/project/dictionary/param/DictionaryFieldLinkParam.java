package com.sanythadmin.project.dictionary.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import jakarta.persistence.Column;

/**
 * 用户信息字段关联关系查询参数
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictionaryFieldLinkParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 字段英文名
     */
    @QueryField(type = QueryType.EQ)
    private String fieldEn;

    /**
     * 字段值
     */
    private String fieldVal;

    /**
     * 关联字段是否显示（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark showFlag;

    /**
     * 关联字段数据
     */
    private String linkFieldData;

    /**
     * 关联字段加载数据url
     */
    private String linkFieldDataUrl;

    /**
     * 字段ID
     */
    @QueryField(type = QueryType.EQ)
    private String fieldId;

    /**
     * 关联字段英文名
     */
    private String linkField;

    /**
     * 组ID
     */
    @QueryField(type = QueryType.EQ)
    private String groupId;

    @QueryField(type = QueryType.EQ)
    private UserType userType;

    /**
     * 字段值对应Id
     */
    @QueryField(type = QueryType.EQ)
    private String fieldValId;
}
