package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldLink;
import com.sanythadmin.project.dictionary.mapper.DictionaryFieldLinkMapper;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldLinkService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户信息字段关联关系Service实现
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
@Service
public class DictionaryFieldLinkServiceImpl extends ServiceImpl<DictionaryFieldLinkMapper, DictionaryFieldLink> implements DictionaryFieldLinkService {

    @Override
    public PageResult<DictionaryFieldLink> pageRel(DictionaryFieldLinkParam param) {
        PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryFieldLink> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryFieldLink> listRel(DictionaryFieldLinkParam param) {
        List<DictionaryFieldLink> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryFieldLink getByIdRel(String id) {
        DictionaryFieldLinkParam param = new DictionaryFieldLinkParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Override
    public void removeByFieldId(String fieldId) {
        baseMapper.delete(new LambdaQueryWrapper<DictionaryFieldLink>().eq(DictionaryFieldLink::getFieldId, fieldId));
    }

    @Transactional
    @Override
    public void edit(List<DictionaryFieldLink> fieldLinks) {
        DictionaryFieldLink link = fieldLinks.get(0);
        String fieldId = link.getFieldId();
        removeByFieldId(fieldId);
        for (DictionaryFieldLink fieldLink : fieldLinks) {
            baseMapper.insert(fieldLink);
        }
    }
}
