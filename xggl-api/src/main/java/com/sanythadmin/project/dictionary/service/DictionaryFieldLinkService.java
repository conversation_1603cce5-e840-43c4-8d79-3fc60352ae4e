package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldLink;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;

import java.util.List;

/**
 * 用户信息字段关联关系Service
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
public interface DictionaryFieldLinkService extends IService<DictionaryFieldLink> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryFieldLink>
     */
    PageResult<DictionaryFieldLink> pageRel(DictionaryFieldLinkParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryFieldLink>
     */
    List<DictionaryFieldLink> listRel(DictionaryFieldLinkParam param);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return DictionaryFieldLink
     */
    DictionaryFieldLink getByIdRel(String id);

    /**
     * 根据field删除
     *
     * @param fieldId
     */
    void removeByFieldId(String fieldId);

    /**
     * 编辑
     *
     * @param fieldLinks
     */
    void edit(List<DictionaryFieldLink> fieldLinks);
}
