package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.ListGroupConfig;
import com.sanythadmin.project.dictionary.mapper.ListGroupConfigMapper;
import com.sanythadmin.project.dictionary.param.ListGroupConfigParam;
import com.sanythadmin.project.dictionary.service.ListGroupConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 多条数据组配置Service实现
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
@Service
public class ListGroupConfigServiceImpl extends ServiceImpl<ListGroupConfigMapper, ListGroupConfig> implements ListGroupConfigService {

    @Override
    public PageResult<ListGroupConfig> pageRel(ListGroupConfigParam param) {
        PageParam<ListGroupConfig, ListGroupConfigParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<ListGroupConfig> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<ListGroupConfig> listRel(ListGroupConfigParam param) {
        List<ListGroupConfig> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<ListGroupConfig, ListGroupConfigParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public ListGroupConfig getByIdRel(String id) {
        ListGroupConfigParam param = new ListGroupConfigParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
