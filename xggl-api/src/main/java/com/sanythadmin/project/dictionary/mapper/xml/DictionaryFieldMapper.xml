<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.DictionaryFieldMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.ID, a.FIELD_ZH, a.FIELD_EN, a.REQUIRED, a.SHOW_FLAG, a.GROUP_ID, a.SORT, a.START_TIME, a.END_TIME,
          a.CONTROL_TYPE, a.REG_EXPRESSION, a.LOAD_DATA_URL, a.COUNT_FLAG, a.TIP_TEXT, a.SELF_MODIFY_FLAG, a.MODIFY_ROLE,
          a.SELF_MODIFY_VERIFY_FLAG, a.LOAD_DATA_TYPE, a.VALUE_FIELD, a.TEXT_FIELD,a.IMPORT_FIELD_FLAG,a.USER_TYPE,a.FIELD_STYLE,
          a.LIST_SHOW_FLAG,a.QUERY_SHOW_FLAG,a.EXPORT_FIELD_FLAG,a.TEMP_FIELD,a.LOAD_DATA_URL_ID,a.WORKFLOW_NODE_FLOW_CONDITION,
            a.sortable, a.fixed
    </sql>

    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.fieldZh != null">
            AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh}), '%')
        </if>
        <if test="param.fieldEn != null">
            AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn}), '%')
        </if>
        <if test="param.required != null">
            AND a.REQUIRED = #{param.required}
        </if>
        <if test="param.showFlag != null">
            AND a.SHOW_FLAG = #{param.showFlag}
        </if>
        <if test="param.groupId != null">
            AND a.GROUP_ID in
            <foreach item="item" index="index" collection="param.groupId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.controlType != null">
            AND a.CONTROL_TYPE = #{param.controlType}
        </if>
        <if test="param.countFlag != null">
            AND a.COUNT_FLAG = #{param.countFlag}
        </if>
        <if test="param.tipText != null">
            AND a.TIP_TEXT LIKE concat(concat('%',#{param.tipText}), '%')
        </if>
        <if test="param.selfModifyFlag != null">
            AND a.SELF_MODIFY_FLAG = #{param.selfModifyFlag}
        </if>
        <if test="param.modifyRole != null">
            AND a.MODIFY_ROLE LIKE concat(concat('%',#{param.modifyRole}), '%')
        </if>
        <if test="param.selfModifyVerifyFlag != null">
            AND a.SELF_MODIFY_VERIFY_FLAG = #{param.selfModifyVerifyFlag}
        </if>
        <if test="param.loadDataType != null">
            AND a.LOAD_DATA_TYPE = #{param.loadDataType}
        </if>
        <if test="param.userType != null">
            AND a.USER_TYPE = #{param.userType}
        </if>
        <if test="param.importFieldFlag != null">
            AND a.IMPORT_FIELD_FLAG = #{param.importFieldFlag}
        </if>
        <if test="param.listShowFlag != null">
            AND a.LIST_SHOW_FLAG = #{param.listShowFlag}
        </if>
        <if test="param.queryShowFlag != null">
            AND a.QUERY_SHOW_FLAG = #{param.queryShowFlag}
        </if>
        <if test="param.exportFieldFlag != null">
            AND a.EXPORT_FIELD_FLAG = #{param.exportFieldFlag}
        </if>
        <if test="param.tempField != null">
            AND a.TEMP_FIELD = #{param.tempField}
        </if>
        <if test="param.listFlag != null">
            AND b.LIST_FLAG = #{param.listFlag}
        </if>
        <if test="param.workflowNodeFlowCondition != null">
            AND a.WORKFLOW_NODE_FLOW_CONDITION = #{param.workflowNodeFlowCondition}
        </if>
        <if test="param.sortable != null">
            AND a.sortable = #{param.sortable}
        </if>
        <if test="param.fixed != null">
            AND a.fixed = #{param.fixed}
        </if>
    </sql>

    <sql id="selectSql">
        SELECT <include refid="Base_Column_List"/>
        FROM syt_dictionary_field a, syt_dictionary_group b WHERE a.group_id = b.id
        <if test="param.eduLevelId != null">
            and exists (select 1 from syt_dictionary_field_edu_level d
            where a.id = d.field_id and d.edu_level_id = #{param.eduLevelId})
        </if>
        <if test="param.hideRoleId != null">
            and not exists(SELECT 1 FROM syt_dictionary_field_hide_role e
            WHERE a.id = e.field_id and e.role_id= #{param.hideRoleId})
        </if>
        <include refid="Base_Query_Condition"/> ORDER BY a.sort asc
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryField">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryField">
        <include refid="selectSql"></include>
    </select>
</mapper>
