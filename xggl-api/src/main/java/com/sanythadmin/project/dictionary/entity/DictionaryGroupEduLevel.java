package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 用户信息组与学生培养层次关系
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_GROUP_EDU_LEVEL")
@Entity
@Table(name = "SYT_DICTIONARY_GROUP_EDU_LEVEL")
public class DictionaryGroupEduLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户信息组ID
     */
    @Column(name = "GROUP_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 关联培养层次ID
     */
    @Column(name = "EDU_LEVEL_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("EDU_LEVEL_ID")
    private String eduLevelId;

}
