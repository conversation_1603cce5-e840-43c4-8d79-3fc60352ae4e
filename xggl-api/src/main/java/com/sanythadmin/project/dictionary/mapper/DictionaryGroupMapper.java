package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息组Mapper
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
public interface DictionaryGroupMapper extends BaseMapper<DictionaryGroup> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryGroup>
     */
    List<DictionaryGroup> selectPageRel(@Param("page") IPage<DictionaryGroup> page,
                             @Param("param") DictionaryGroupParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryGroup> selectListRel(@Param("param") DictionaryGroupParam param);

}
