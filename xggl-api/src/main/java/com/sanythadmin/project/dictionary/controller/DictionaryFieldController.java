package com.sanythadmin.project.dictionary.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.ControlType;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.dictionary.entity.*;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.service.*;
import com.sanythadmin.project.userInfo.entity.UserListInfo;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户信息字段控制器
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryField")
public class DictionaryFieldController extends BaseController {
    @Resource
    private DictionaryFieldService dictionaryFieldService;
    @Resource
    private DictionaryFieldHideRoleService dictionaryFieldHideRoleService;
    @Resource
    private DictionaryFieldEduLevelService dictionaryFieldEduLevelService;
    @Resource
    private DictionaryGroupService dictionaryGroupService;
    @Resource
    private DictionaryFieldLinkService dictionaryFieldLinkService;


    /**
     * 分页查询用户信息字段（权限标识：dictionary:dictionaryField:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryField:list')")
    @GetMapping("/page")
    public PageResult<DictionaryField> page(DictionaryFieldParam param) {
        PageResult<DictionaryField> pageResult = dictionaryFieldService.pageRel(param);
        if (pageResult.getList() != null)
            pageResult.getList().forEach(this::extracted);
        return pageResult;
    }

    private void extracted(DictionaryField field) {
        List<DictionaryFieldHideRole> list = dictionaryFieldHideRoleService.list(new LambdaQueryWrapper<DictionaryFieldHideRole>()
                .eq(DictionaryFieldHideRole::getFieldId, field.getId()));
        field.setHideRoles(list);

        List<DictionaryFieldEduLevel> eduLevels = dictionaryFieldEduLevelService.list(new LambdaQueryWrapper<DictionaryFieldEduLevel>()
                .eq(DictionaryFieldEduLevel::getFieldId, field.getId()));
        field.setEduLevels(eduLevels);
    }

    /**
     * 查询全部用户信息字段（权限标识：dictionary:dictionaryField:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryField:list')")
    @GetMapping()
    public List<DictionaryField> list(DictionaryFieldParam fieldParam) {
        List<DictionaryField> fieldList = dictionaryFieldService.listRel(fieldParam);
        if (StringUtils.hasLength(fieldParam.getCurrentLinkFieldEn())) {
            DictionaryFieldLinkParam fieldLinkParam = new DictionaryFieldLinkParam();
            fieldLinkParam.setUserType(fieldParam.getUserType());
            PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>(fieldLinkParam);
            List<DictionaryFieldLink> fieldLinks = dictionaryFieldLinkService.list(page.getOrderWrapper());
            boolean flag = false;
            for (DictionaryFieldLink fieldLink : fieldLinks) {
                if (fieldParam.getCurrentLinkFieldEn().equals(fieldLink.getFieldEn())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                Set<String> collect = fieldLinks.stream().map(DictionaryFieldLink::getFieldEn).collect(Collectors.toSet());
                collect.add(fieldParam.getCurrentLinkFieldEn());
                fieldList.removeIf(field -> collect.contains(field.getFieldEn()));
            }
        }
        return fieldList;
    }

    /**
     * 根据id查询用户信息字段（权限标识：dictionary:dictionaryField:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryField:list')")
    @GetMapping("/{id}")
    public DictionaryField get(@PathVariable("id") String id) {
        DictionaryField field = dictionaryFieldService.getById(id);
        if (field != null)
            extracted(field);
        return field;
    }

    /**
     * 添加或修改用户信息字段（权限标识：dictionary:dictionaryField:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryField:operation')")
    @OperationLog(module = "用户信息字段", comments = "保存用户信息字段")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody DictionaryField dictionaryField) {
        dictionaryFieldService.edit(dictionaryField);
    }


    /**
     * 批量删除用户信息字段（权限标识：dictionary:dictionaryField:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryField:remove')")
    @OperationLog(module = "用户信息字段", comments = "批量删除用户信息字段")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryFieldService.remove(ids);
    }


    /**
     * 控件类型
     *
     * @return
     */
//    @GetMapping("/controlType")
    public Map<String, ControlType> controlType() {
        Map<String, ControlType> typeEnumMap = Arrays.stream(ControlType.values())
                .collect(Collectors.toMap(ControlType::name, Function.identity()));
        return typeEnumMap;
    }

    /**
     * 用户信息字段
     *
     * @param groupId
     * @return
     */
    @GetMapping("/userInfo/{groupId}")
    public List<String> getUserInfoField(@PathVariable("groupId") String groupId) {
        String[] ignoredField = new String[]{"serialVersionUID", "userType", "deleted", "roleId", "userOrgMaps"};
        List<String> ignoredFieldList = Arrays.asList(ignoredField);
        Field[] allFields = CommonUtil.getAllFields(BaseUserInfo.class, true);
        List<String> editableFields = new ArrayList<>();
        for (Field field : allFields) {
            if (ignoredFieldList.contains(field.getName())) continue;
            editableFields.add(field.getName());
        }

        allFields = CommonUtil.getAllFields(UserInfo.class, true);
        for (Field field : allFields) {
            if (ignoredFieldList.contains(field.getName())) continue;
            editableFields.add(field.getName());
        }

        DictionaryGroup group = dictionaryGroupService.getById(groupId);
        List<DictionaryGroup> groups = dictionaryGroupService.list(new LambdaQueryWrapper<DictionaryGroup>().
                eq(DictionaryGroup::getUserType, group.getUserType()));
        if (!CollectionUtils.isEmpty(groups)) {
            List<String> list = groups.stream().map(DictionaryGroup::getId).collect(Collectors.toList());
            List<DictionaryField> fieldList = dictionaryFieldService.list(new QueryWrapper<DictionaryField>()
                    .in("group_id", list).eq("user_type", group.getUserType()));
            List<String> fields = fieldList.stream().map(DictionaryField::getFieldEn).toList();
            editableFields.removeAll(fields);
        }
        return editableFields;
    }

    /**
     * 用户列表信息字段
     *
     * @param groupId
     * @return
     */
    @GetMapping("/userListInfo/{groupId}")
    public List<String> getUserListInfoField(@PathVariable("groupId") String groupId) {
        List<String> editableFields = UserListInfo.getEditableFields();
        DictionaryGroup group = dictionaryGroupService.getById(groupId);
        List<DictionaryField> fieldList = dictionaryFieldService.list(
                new QueryWrapper<DictionaryField>().eq("group_id", group.getId()));
        List<String> fields = fieldList.stream().map(DictionaryField::getFieldEn).toList();
        editableFields.removeAll(fields);
        return editableFields;
    }
}
