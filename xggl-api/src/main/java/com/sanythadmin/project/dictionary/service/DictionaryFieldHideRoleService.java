package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryFieldHideRoleParam;

import java.util.List;

/**
 * 用户信息字段不可见角色Service
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
public interface DictionaryFieldHideRoleService extends IService<DictionaryFieldHideRole> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryFieldHideRole>
     */
    PageResult<DictionaryFieldHideRole> pageRel(DictionaryFieldHideRoleParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryFieldHideRole>
     */
    List<DictionaryFieldHideRole> listRel(DictionaryFieldHideRoleParam param);

    /**
     * 根据id查询
     *
     * @param id 主键ID
     * @return DictionaryFieldHideRole
     */
    DictionaryFieldHideRole getByIdRel(String id);

}
