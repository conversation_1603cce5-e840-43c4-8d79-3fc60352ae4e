package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldLink;
import com.sanythadmin.project.dictionary.param.DictionaryFieldLinkParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldLinkService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户信息字段关联关系控制器
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryFieldLink")
public class DictionaryFieldLinkController extends BaseController {
    @Resource
    private DictionaryFieldLinkService dictionaryFieldLinkService;

    /**
     * 分页查询用户信息字段关联关系（权限标识：dictionary:dictionaryFieldLink:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldLink:list')")
    @GetMapping("/page")
    public PageResult<DictionaryFieldLink> page(DictionaryFieldLinkParam param) {
        PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = dictionaryFieldLinkService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户信息字段关联关系（权限标识：dictionary:dictionaryFieldLink:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldLink:list')")
    @GetMapping()
    public List<DictionaryFieldLink> list(DictionaryFieldLinkParam param) {
        PageParam<DictionaryFieldLink, DictionaryFieldLinkParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return dictionaryFieldLinkService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息字段关联关系（权限标识：dictionary:dictionaryFieldLink:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldLink:list')")
    @GetMapping("/{id}")
    public DictionaryFieldLink get(@PathVariable("id") String id) {
        return dictionaryFieldLinkService.getById(id);
    }

    /**
     * 添加或修改用户信息字段关联关系（权限标识：dictionary:dictionaryFieldLink:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldLink:operation')")
    @OperationLog(module = "用户信息字段关联关系", comments = "保存用户信息字段关联关系")
    @PostMapping("/operation")
    public void operation(@RequestBody List<DictionaryFieldLink> fieldLinks) {
        dictionaryFieldLinkService.edit(fieldLinks);
    }

    /**
     * 删除字段关联关系（权限标识：dictionary:dictionaryFieldLink:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldLink:remove')")
    @OperationLog(module = "删除字段关联关系", comments = "删除字段关联关系")
    @PostMapping("/removeByFieldId")
    public void removeByFieldId(@RequestBody String fieldId) {
        dictionaryFieldLinkService.removeByFieldId(fieldId);
    }
}
