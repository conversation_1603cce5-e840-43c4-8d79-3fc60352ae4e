package com.sanythadmin.project.dictionary.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息组与学生培养层次关系查询参数
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictionaryGroupEduLevelParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 用户信息组ID
     */
    private String groupId;

    /**
     * 关联培养层次ID
     */
    private String eduLevelId;

}
