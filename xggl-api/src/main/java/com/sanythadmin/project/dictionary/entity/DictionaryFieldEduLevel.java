package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户信息字段与学生培养层次关系
 *
 * <AUTHOR>
 * @since 2024-04-03 10:36:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_FIELD_EDU_LEVEL")
@Entity
@Table(name = "SYT_DICTIONARY_FIELD_EDU_LEVEL")
public class DictionaryFieldEduLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户信息字段ID
     */
    @Column(name = "FIELD_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("FIELD_ID")
    private String fieldId;

    /**
     * 关联培养层次ID
     */
    @Column(name = "EDU_LEVEL_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("EDU_LEVEL_ID")
    private String eduLevelId;

}
