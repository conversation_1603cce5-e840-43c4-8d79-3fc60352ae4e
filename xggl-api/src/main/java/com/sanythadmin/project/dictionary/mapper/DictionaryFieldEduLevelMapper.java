package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryFieldEduLevelParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息字段与学生培养层次关系Mapper
 *
 * <AUTHOR>
 * @since 2024-04-03 10:36:49
 */
public interface DictionaryFieldEduLevelMapper extends BaseMapper<DictionaryFieldEduLevel> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryFieldEduLevel>
     */
    List<DictionaryFieldEduLevel> selectPageRel(@Param("page") IPage<DictionaryFieldEduLevel> page,
                             @Param("param") DictionaryFieldEduLevelParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryFieldEduLevel> selectListRel(@Param("param") DictionaryFieldEduLevelParam param);

}
