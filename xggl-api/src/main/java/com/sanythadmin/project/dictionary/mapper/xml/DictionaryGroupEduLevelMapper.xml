<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.DictionaryGroupEduLevelMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, GROUP_ID, EDU_LEVEL_ID
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_DICTIONARY_GROUP_EDU_LEVEL a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.groupId != null">
                AND a.GROUP_ID LIKE '%'||#{param.groupId}||'%'
            </if>
            <if test="param.eduLevelId != null">
                AND a.EDU_LEVEL_ID LIKE '%'||#{param.eduLevelId}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel">
        <include refid="selectSql"></include>
    </select>

</mapper>
