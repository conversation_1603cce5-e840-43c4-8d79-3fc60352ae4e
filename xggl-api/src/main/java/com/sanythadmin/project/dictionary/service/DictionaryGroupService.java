package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 用户信息组Service
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
public interface DictionaryGroupService extends IService<DictionaryGroup> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryGroup>
     */
    PageResult<DictionaryGroup> pageRel(DictionaryGroupParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryGroup>
     */
    List<DictionaryGroup> listRel(DictionaryGroupParam param);

    /**
     * 根据id查询
     *
     * @param id 主键ID
     * @return DictionaryGroup
     */
    DictionaryGroup getByIdRel(String id);

    /**
     * 新增或更新
     *
     * @param dictionaryGroup 实体类
     * @param fileMap 文件集合
     * @return boolean
     */
    void edit(DictionaryGroup dictionaryGroup, Map<String, MultipartFile> fileMap);

    void remove(List<String> ids);
}
