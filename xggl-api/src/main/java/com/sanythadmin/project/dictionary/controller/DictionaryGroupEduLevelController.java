package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryGroupEduLevelParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupEduLevelService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户信息组与学生培养层次关系控制器
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryGroupEduLevel")
public class DictionaryGroupEduLevelController extends BaseController {
    @Resource
    private DictionaryGroupEduLevelService dictionaryGroupEduLevelService;

    /**
     * 分页查询用户信息组与学生培养层次关系（权限标识：dictionary:dictionaryGroupEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupEduLevel:list')")
    @GetMapping("/page")
    public PageResult<DictionaryGroupEduLevel> page(DictionaryGroupEduLevelParam param) {
        PageParam<DictionaryGroupEduLevel, DictionaryGroupEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = dictionaryGroupEduLevelService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户信息组与学生培养层次关系（权限标识：dictionary:dictionaryGroupEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupEduLevel:list')")
    @GetMapping()
    public List<DictionaryGroupEduLevel> list(DictionaryGroupEduLevelParam param) {
        PageParam<DictionaryGroupEduLevel, DictionaryGroupEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return dictionaryGroupEduLevelService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息组与学生培养层次关系（权限标识：dictionary:dictionaryGroupEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupEduLevel:list')")
    @GetMapping("/{id}")
    public DictionaryGroupEduLevel get(@PathVariable("id") Integer id) {
        return dictionaryGroupEduLevelService.getById(id);
    }

    /**
     * 添加或修改用户信息组与学生培养层次关系（权限标识：dictionary:dictionaryGroupEduLevel:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupEduLevel:operation')")
    @OperationLog(module = "用户信息组与学生培养层次关系", comments = "保存用户信息组与学生培养层次关系")
    @PostMapping("/operation")
    public void save(@RequestBody DictionaryGroupEduLevel dictionaryGroupEduLevel) {
        if (StringUtils.hasLength(dictionaryGroupEduLevel.getId())) {
            dictionaryGroupEduLevelService.updateById(dictionaryGroupEduLevel);
        } else {
            dictionaryGroupEduLevelService.save(dictionaryGroupEduLevel);
        }
    }

    /**
     * 批量删除用户信息组与学生培养层次关系（权限标识：dictionary:dictionaryGroupEduLevel:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupEduLevel:remove')")
    @OperationLog(module = "用户信息组与学生培养层次关系", comments = "批量删除用户信息组与学生培养层次关系")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryGroupEduLevelService.removeByIds(ids);
    }
}
