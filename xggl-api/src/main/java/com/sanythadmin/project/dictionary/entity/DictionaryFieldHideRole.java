package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 用户信息字段不可见角色
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_FIELD_HIDE_ROLE")
@Entity
@Table(name = "SYT_DICTIONARY_FIELD_HIDE_ROLE")
public class DictionaryFieldHideRole implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户字段ID
     */
    @Column(name = "FIELD_ID")
    @TableField("FIELD_ID")
    private String fieldId;

    /**
     * 角色ID
     */
    @Column(name = "ROLE_ID")
    @TableField("ROLE_ID")
    private String roleId;

}
