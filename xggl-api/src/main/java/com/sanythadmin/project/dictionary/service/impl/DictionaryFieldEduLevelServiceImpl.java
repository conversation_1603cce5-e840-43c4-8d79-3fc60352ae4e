package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldEduLevel;
import com.sanythadmin.project.dictionary.mapper.DictionaryFieldEduLevelMapper;
import com.sanythadmin.project.dictionary.param.DictionaryFieldEduLevelParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldEduLevelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息字段与学生培养层次关系Service实现
 *
 * <AUTHOR>
 * @since 2024-04-03 10:36:49
 */
@Service
public class DictionaryFieldEduLevelServiceImpl extends ServiceImpl<DictionaryFieldEduLevelMapper, DictionaryFieldEduLevel> implements DictionaryFieldEduLevelService {

    @Override
    public PageResult<DictionaryFieldEduLevel> pageRel(DictionaryFieldEduLevelParam param) {
        PageParam<DictionaryFieldEduLevel, DictionaryFieldEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryFieldEduLevel> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryFieldEduLevel> listRel(DictionaryFieldEduLevelParam param) {
        List<DictionaryFieldEduLevel> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryFieldEduLevel, DictionaryFieldEduLevelParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryFieldEduLevel getByIdRel(String id) {
        DictionaryFieldEduLevelParam param = new DictionaryFieldEduLevelParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
