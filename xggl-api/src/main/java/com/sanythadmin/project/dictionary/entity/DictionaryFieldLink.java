package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
/**
 * 用户信息字段关联关系
 *
 * <AUTHOR>
 * @since 2024-04-03 15:21:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_FIELD_LINK")
@Entity
@Table(name = "SYT_DICTIONARY_FIELD_LINK")
public class DictionaryFieldLink implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段值
     */
    @Column(name = "FIELD_VAL")
    @TableField("FIELD_VAL")
    private String fieldVal;
    /**
     * 字段值对应Id
     */
    @Column(name = "FIELD_VAL_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("FIELD_VAL_ID")
    private String fieldValId;
    /**
     * 关联字段是否显示（1：是，0：否）
     */
    @Column(name = "SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SHOW_FLAG")
    private JudgeMark showFlag;

    /**
     * 关联字段数据（来源公共码表）
     */
    @Column(name = "LINK_FIELD_DATA_TYPE")
    @TableField("LINK_FIELD_DATA_TYPE")
    private String linkFieldDataType;

    /**
     * 关联字段加载数据url
     */
    @Column(name = "LINK_FIELD_DATA_URL")
    @TableField("LINK_FIELD_DATA_URL")
    private String linkFieldDataUrl;

    /**
     * 字段ID
     */
    @Column(name = "FIELD_ID")
    @TableField("FIELD_ID")
    private String fieldId;

    /**
     * 关联字段英文名
     */
    @Column(name = "LINK_FIELD")
    @TableField("LINK_FIELD")
    private String linkField;

    /**
     * 信息组ID
     */
    @Column(name = "GROUP_ID")
    @TableField("GROUP_ID")
    private String groupId;
    /**
     * 用户类型1：学生，2：教师
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

}
