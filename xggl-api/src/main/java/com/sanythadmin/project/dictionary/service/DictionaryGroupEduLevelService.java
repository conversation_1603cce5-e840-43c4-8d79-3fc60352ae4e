package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryGroupEduLevelParam;

import java.util.List;

/**
 * 用户信息组与学生培养层次关系Service
 *
 * <AUTHOR>
 * @since 2024-03-28 17:32:29
 */
public interface DictionaryGroupEduLevelService extends IService<DictionaryGroupEduLevel> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryGroupEduLevel>
     */
    PageResult<DictionaryGroupEduLevel> pageRel(DictionaryGroupEduLevelParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryGroupEduLevel>
     */
    List<DictionaryGroupEduLevel> listRel(DictionaryGroupEduLevelParam param);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return DictionaryGroupEduLevel
     */
    DictionaryGroupEduLevel getByIdRel(String id);

}
