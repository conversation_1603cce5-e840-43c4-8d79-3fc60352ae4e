package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 用户信息组不可见角色
 *
 * <AUTHOR>
 * @since 2024-03-28 17:07:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_GROUP_HIDE_ROLE")
@Entity
@Table(name = "SYT_DICTIONARY_GROUP_HIDE_ROLE")
public class DictionaryGroupHideRole implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户信息组ID
     */
    @Column(name = "GROUP_ID")
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 角色ID
     */
    @Column(name = "ROLE_ID")
    @TableField("ROLE_ID")
    private String roleId;

}
