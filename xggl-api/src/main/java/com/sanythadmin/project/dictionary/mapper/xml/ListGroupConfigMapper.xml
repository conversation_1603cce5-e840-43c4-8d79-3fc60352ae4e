<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.ListGroupConfigMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL,DICTIONARY_FIELD_URL
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT <include refid="Base_Column_List"/>
        FROM SYT_LIST_GROUP_CONFIG a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.groupName != null">
                AND a.GROUP_NAME LIKE '%'||#{param.groupName}||'%'
            </if>
            <if test="param.loadDataUrl != null">
                AND a.LOAD_DATA_URL LIKE '%'||#{param.loadDataUrl}||'%'
            </if>
            <if test="param.operationDataUrl != null">
                AND a.OPERATION_DATA_URL LIKE '%'||#{param.operationDataUrl}||'%'
            </if>
            <if test="param.deleteDataUrl != null">
                AND a.DELETE_DATA_URL LIKE '%'||#{param.deleteDataUrl}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.dictionary.entity.ListGroupConfig">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.dictionary.entity.ListGroupConfig">
        <include refid="selectSql"></include>
    </select>

</mapper>
