package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.List;

/**
 * 用户信息组
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_GROUP")
@Entity
@Table(name = "SYT_DICTIONARY_GROUP")
public class DictionaryGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户信息组名称
     */
    @NotBlank(message = "用户信息组名称不能为空")
    @Column(name = "GROUP_NAME")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 是否显示（1：是， 0：否）
     */
    @Column(name = "SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SHOW_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark showFlag;

    /**
     * 是否需要激活（1：是，0：否）
     */
    @Column(name = "ACTIVE_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "ACTIVE_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark activeFlag;

    /**
     * 是否可添加（1：是，0：否）
     */
    @Column(name = "ADD_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "ADD_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark addFlag;

    /**
     * 最少添加条数
     */
    @Column(name = "MIN_NUM")
    @TableField("MIN_NUM")
    private Long minNum;

    /**
     * 最多添加条数
     */
    @Column(name = "MAX_NUM")
    @TableField("MAX_NUM")
    private Long maxNum;

    /**
     * 是否列表（1：是，0：否）
     */
    @NotNull(message = "是否列表不能为空")
    @Column(name = "LIST_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "LIST_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark listFlag;

    /**
     * 是否可修改（1：是，0：否）
     */
    @Column(name = "MODIFY_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "MODIFY_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark modifyFlag;

    /**
     * 组icon
     */
    @Column(name = "ICON")
    @TableField("ICON")
    private String icon;

    /**
     * 空数据隐藏（1：是，0：否）
     */
    @Column(name = "EMPTY_DATA_HIDE", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "EMPTY_DATA_HIDE", jdbcType = JdbcType.INTEGER)
    private JudgeMark emptyDataHide;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 用户类型1：学生，2：教师
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

    /**
     * 列表组关联ID
     */
    @Column(name = "LIST_GROUP_ID")
    @TableField("LIST_GROUP_ID")
    private String listGroupId;
    /**
     * 不可见角色
     */
    @Transient
    @TableField(exist = false)
    private List<DictionaryGroupHideRole> hideRoles;
    /**
     * 培养层次
     */
    @Transient
    @TableField(exist = false)
    private List<DictionaryGroupEduLevel> eduLevels;
}
