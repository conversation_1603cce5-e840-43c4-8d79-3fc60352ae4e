package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.dictionary.mapper.SelectControlApiMapper;
import com.sanythadmin.project.dictionary.service.SelectControlApiService;
import com.sanythadmin.project.dictionary.entity.SelectControlApi;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 下拉控件数据源配置Service实现
 *
 * <AUTHOR>
 * @since 2024-07-05 16:30:47
 */
@Service
public class SelectControlApiServiceImpl extends ServiceImpl<SelectControlApiMapper, SelectControlApi> implements SelectControlApiService {

    @Resource
    private SelectControlApiMapper mapper;

}
