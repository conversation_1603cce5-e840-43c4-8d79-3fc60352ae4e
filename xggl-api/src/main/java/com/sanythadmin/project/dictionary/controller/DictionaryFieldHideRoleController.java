package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryFieldHideRoleParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldHideRoleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户信息字段不可见角色控制器
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryFieldHideRole")
public class DictionaryFieldHideRoleController extends BaseController {
    @Resource
    private DictionaryFieldHideRoleService dictionaryFieldHideRoleService;

    /**
     * 分页查询用户信息字段不可见角色（权限标识：dictionary:dictionaryFieldHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldHideRole:list')")
    @GetMapping("/page")
    public PageResult<DictionaryFieldHideRole> page(DictionaryFieldHideRoleParam param) {
        PageParam<DictionaryFieldHideRole, DictionaryFieldHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = dictionaryFieldHideRoleService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户信息字段不可见角色（权限标识：dictionary:dictionaryFieldHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldHideRole:list')")
    @GetMapping()
    public List<DictionaryFieldHideRole> list(DictionaryFieldHideRoleParam param) {
        PageParam<DictionaryFieldHideRole, DictionaryFieldHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return dictionaryFieldHideRoleService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息字段不可见角色（权限标识：dictionary:dictionaryFieldHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldHideRole:list')")
    @GetMapping("/{id}")
    public DictionaryFieldHideRole get(@PathVariable("id") Integer id) {
        return dictionaryFieldHideRoleService.getById(id);
    }

    /**
     * 添加或修改用户信息字段不可见角色（权限标识：dictionary:dictionaryFieldHideRole:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldHideRole:operation')")
    @OperationLog(module = "用户信息字段不可见角色", comments = "保存用户信息字段不可见角色")
    @PostMapping("/operation")
    public void save(@RequestBody DictionaryFieldHideRole dictionaryFieldHideRole) {
        if (StringUtils.hasLength(dictionaryFieldHideRole.getId())) {
            dictionaryFieldHideRoleService.updateById(dictionaryFieldHideRole);
        } else {
            dictionaryFieldHideRoleService.save(dictionaryFieldHideRole);
        }
    }

    /**
     * 批量删除用户信息字段不可见角色（权限标识：dictionary:dictionaryFieldHideRole:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldHideRole:remove')")
    @OperationLog(module = "用户信息字段不可见角色", comments = "批量删除用户信息字段不可见角色")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryFieldHideRoleService.removeByIds(ids);
    }
}
