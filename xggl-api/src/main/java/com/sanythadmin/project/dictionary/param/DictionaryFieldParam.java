package com.sanythadmin.project.dictionary.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 用户信息字段查询参数
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictionaryFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 字段英文名
     */
    @QueryField(type = QueryType.IN_STR)
    private String fieldEn;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark required;

    /**
     * 是否显示（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark showFlag;

    /**
     * 所属信息组ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String groupId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 截至时间
     */
    private String endTime;

    /**
     * 控件类型（短文本, 单选/多选，文本域, 日期, 小数，整数，附件等）
     */
    @QueryField(type = QueryType.EQ)
    private String controlType;

    /**
     * 是否统计（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark countFlag;

    /**
     * 提示文本
     */
    private String tipText;

    /**
     * 本人是否可修改（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark selfModifyFlag;

    /**
     * 可修改角色
     */
    private String modifyRole;

    /**
     * 本人修改是否提交审核验证（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark selfModifyVerifyFlag;

    /**
     * 加载数据类型（来自公共码表code）
     */
    @QueryField(type = QueryType.EQ)
    private String loadDataType;
    /**
     * 是否导入字段（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark importFieldFlag;
    /**
     * 用户类型1：学生，2：教师
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;
    /**
     * 培养层次
     */
    @QueryField(ignore = true)
    private String eduLevelId;
    /**
     * 角色隐藏ID
     */
    @QueryField(ignore = true)
    private String hideRoleId;

    /**
     * 列表显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark listShowFlag;
    /**
     * 查询显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark queryShowFlag;

    @QueryField(type = QueryType.EQ)
    private JudgeMark exportFieldFlag;

    @QueryField(type = QueryType.EQ)
    private JudgeMark tempField;

    /**
     * group listFlag （是否列表）
     */
    @QueryField(ignore = true)
    private JudgeMark listFlag;
    /**
     * 当前设置级联的字段
     */
    @QueryField(ignore = true)
    private String currentLinkFieldEn;

    /**
     * 流程节点流转条件
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark workflowNodeFlowCondition;

    @QueryField(type = QueryType.EQ)
    private JudgeMark sortable;

    /**
     * 列配置是否可固定列（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark fixed;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
