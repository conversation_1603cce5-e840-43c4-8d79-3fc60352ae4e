package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;

import java.util.List;

/**
 * 用户信息字段Service
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
public interface DictionaryFieldService extends IService<DictionaryField> {
    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryField>
     */
    PageResult<DictionaryField> pageRel(DictionaryFieldParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryField>
     */
    List<DictionaryField> listRel(DictionaryFieldParam param);

    /**
     * 非关联查询
     *
     * @param param
     * @return
     */
    List<DictionaryField> queryList(DictionaryFieldParam param);

    /**
     * 新增或编辑
     *
     * @param param
     */
    public void edit(DictionaryField param);

    /**
     * 删除
     *
     * @param ids
     */
    public void remove(List<String> ids);
}
