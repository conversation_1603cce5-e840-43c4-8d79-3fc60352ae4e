package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryFieldHideRoleParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息字段不可见角色Mapper
 *
 * <AUTHOR>
 * @since 2024-03-29 15:09:11
 */
public interface DictionaryFieldHideRoleMapper extends BaseMapper<DictionaryFieldHideRole> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<DictionaryFieldHideRole>
     */
    List<DictionaryFieldHideRole> selectPageRel(@Param("page") IPage<DictionaryFieldHideRole> page,
                             @Param("param") DictionaryFieldHideRoleParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<DictionaryFieldHideRole> selectListRel(@Param("param") DictionaryFieldHideRoleParam param);

}
