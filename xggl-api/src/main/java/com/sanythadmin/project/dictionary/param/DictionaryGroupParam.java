package com.sanythadmin.project.dictionary.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 用户信息组查询参数
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DictionaryGroupParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 用户信息组名称
     */
    private String groupName;

    /**
     * 是否显示（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark showFlag;

    /**
     * 是否需要激活（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark activeFlag;

    /**
     * 是否可添加（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark addFlag;

    /**
     * 最少添加条数
     */
    private Long minNum;

    /**
     * 最多添加条数
     */
    private Long maxNum;

    /**
     * 是否列表（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark listFlag;

    /**
     * 是否可修改（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark modifyFlag;

    /**
     * 组icon
     */
    private String icon;

    /**
     * 空数据隐藏（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark emptyDataHide;

    /**
     * 用户类型1：学生，2：教师
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;

    /**
     * 列表组关联ID
     */
    private String listGroupId;
    /**
     * 培养层次
     */
    @QueryField(ignore = true)
    private String eduLevelId;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
