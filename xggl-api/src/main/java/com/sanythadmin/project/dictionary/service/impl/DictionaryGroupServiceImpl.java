package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.*;
import com.sanythadmin.project.dictionary.mapper.*;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 用户信息组Service实现
 *
 * <AUTHOR>
 * @since 2024-03-28 09:17:26
 */
@Service
public class DictionaryGroupServiceImpl extends ServiceImpl<DictionaryGroupMapper, DictionaryGroup> implements DictionaryGroupService {
    @Resource
    private GridFsService gridFsService;
    @Resource
    private DictionaryGroupHideRoleMapper groupHideRoleMapper;
    @Resource
    private DictionaryGroupEduLevelMapper groupEduLevelMapper;
    @Resource
    private DictionaryFieldMapper fieldMapper;
    @Resource
    private DictionaryFieldLinkMapper fieldLinkMapper;

    @Override
    public PageResult<DictionaryGroup> pageRel(DictionaryGroupParam param) {
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryGroup> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryGroup> listRel(DictionaryGroupParam param) {
        List<DictionaryGroup> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryGroup getByIdRel(String id) {
        DictionaryGroupParam param = new DictionaryGroupParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Transactional
    @Override
    public void edit(DictionaryGroup group, Map<String, MultipartFile> fileMap) {
        if (StringUtils.hasText(group.getId())) {
            DictionaryGroup dictionaryGroup = getById(group.getId());
            if (!CollectionUtils.isEmpty(fileMap)) {
                if (StringUtils.hasText(group.getIcon())) {
                    gridFsService.removeByFileInfoStr(dictionaryGroup.getIcon());
                } else {
                    group.setIcon(dictionaryGroup.getIcon());
                }
            }
            groupHideRoleMapper.delete(new QueryWrapper<DictionaryGroupHideRole>().eq("GROUP_ID", group.getId()));
            groupEduLevelMapper.delete(new QueryWrapper<DictionaryGroupEduLevel>().eq("GROUP_ID", group.getId()));
        }

        if (group.getSort() == null) {
            SortHelper<DictionaryGroup> sortHelper = new SortHelper<>(this, "sort");
            group.setSort((long) sortHelper.next());
        }

        if (!CollectionUtils.isEmpty(fileMap)) {
            for (String string : fileMap.keySet()) {
                MultipartFile file = fileMap.get(string);
                String fileString = gridFsService.save(file);
                group.setIcon(fileString);
            }
        }

        saveOrUpdate(group);
        List<DictionaryGroupHideRole> hideRoles = group.getHideRoles();
        List<DictionaryGroupEduLevel> eduLevels = group.getEduLevels();
        if (hideRoles != null) {
            hideRoles.forEach(hideRole -> {
                hideRole.setGroupId(group.getId());
                groupHideRoleMapper.insert(hideRole);
            });
        }

        if (eduLevels != null) {
            eduLevels.forEach(eduLevel -> {
                eduLevel.setGroupId(group.getId());
                groupEduLevelMapper.insert(eduLevel);
            });
        }
    }

    @Transactional
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            DictionaryGroup group = getById(id);
            groupHideRoleMapper.delete(new QueryWrapper<DictionaryGroupHideRole>().eq("GROUP_ID", id));
            groupEduLevelMapper.delete(new QueryWrapper<DictionaryGroupEduLevel>().eq("GROUP_ID", id));
            fieldMapper.delete(new QueryWrapper<DictionaryField>().eq("GROUP_ID", id));
            fieldLinkMapper.delete(new QueryWrapper<DictionaryFieldLink>().eq("GROUP_ID", id));
            removeById(id);
            if (StringUtils.hasLength(group.getIcon()))
                gridFsService.removeByFileInfoStr(group.getIcon());
        }
    }
}
