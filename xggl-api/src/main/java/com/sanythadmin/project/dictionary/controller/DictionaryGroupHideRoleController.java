package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryGroupHideRoleParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupHideRoleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户信息组不可见角色控制器
 *
 * <AUTHOR>
 * @since 2024-03-28 17:07:40
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryGroupHideRole")
public class DictionaryGroupHideRoleController extends BaseController {
    @Resource
    private DictionaryGroupHideRoleService dictionaryGroupHideRoleService;

    /**
     * 分页查询用户信息组不可见角色（权限标识：dictionary:dictionaryGroupHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupHideRole:list')")
    @GetMapping("/page")
    public PageResult<DictionaryGroupHideRole> page(DictionaryGroupHideRoleParam param) {
        PageParam<DictionaryGroupHideRole, DictionaryGroupHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = dictionaryGroupHideRoleService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户信息组不可见角色（权限标识：dictionary:dictionaryGroupHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupHideRole:list')")
    @GetMapping()
    public List<DictionaryGroupHideRole> list(DictionaryGroupHideRoleParam param) {
        PageParam<DictionaryGroupHideRole, DictionaryGroupHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return dictionaryGroupHideRoleService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息组不可见角色（权限标识：dictionary:dictionaryGroupHideRole:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupHideRole:list')")
    @GetMapping("/{id}")
    public DictionaryGroupHideRole get(@PathVariable("id") Integer id) {
        return dictionaryGroupHideRoleService.getById(id);
    }

    /**
     * 添加或修改用户信息组不可见角色（权限标识：dictionary:dictionaryGroupHideRole:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupHideRole:operation')")
    @OperationLog(module = "用户信息组不可见角色", comments = "保存用户信息组不可见角色")
    @PostMapping("/operation")
    public void save(@RequestBody DictionaryGroupHideRole dictionaryGroupHideRole) {
        if (StringUtils.hasLength(dictionaryGroupHideRole.getId())) {
            dictionaryGroupHideRoleService.updateById(dictionaryGroupHideRole);
        } else {
            dictionaryGroupHideRoleService.save(dictionaryGroupHideRole);
        }
    }

    /**
     * 批量删除用户信息组不可见角色（权限标识：dictionary:dictionaryGroupHideRole:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryGroupHideRole:remove')")
    @OperationLog(module = "用户信息组不可见角色", comments = "批量删除用户信息组不可见角色")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryGroupHideRoleService.removeByIds(ids);
    }
}
