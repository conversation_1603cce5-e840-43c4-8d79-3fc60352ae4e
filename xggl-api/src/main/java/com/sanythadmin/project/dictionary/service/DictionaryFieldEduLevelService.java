package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryFieldEduLevelParam;

import java.util.List;

/**
 * 用户信息字段与学生培养层次关系Service
 *
 * <AUTHOR>
 * @since 2024-04-03 10:36:49
 */
public interface DictionaryFieldEduLevelService extends IService<DictionaryFieldEduLevel> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryFieldEduLevel>
     */
    PageResult<DictionaryFieldEduLevel> pageRel(DictionaryFieldEduLevelParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryFieldEduLevel>
     */
    List<DictionaryFieldEduLevel> listRel(DictionaryFieldEduLevelParam param);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return DictionaryFieldEduLevel
     */
    DictionaryFieldEduLevel getByIdRel(String id);

}
