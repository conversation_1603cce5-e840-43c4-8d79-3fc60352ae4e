package com.sanythadmin.project.dictionary.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 多条数据组配置查询参数
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ListGroupConfigParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 加载数据接口
     */
    private String loadDataUrl;

    /**
     * 新增及修改数据接口
     */
    private String operationDataUrl;

    /**
     * 删除数据接口
     */
    private String deleteDataUrl;

}
