package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupHideRole;
import com.sanythadmin.project.dictionary.param.DictionaryGroupHideRoleParam;

import java.util.List;

/**
 * 用户信息组不可见角色Service
 *
 * <AUTHOR>
 * @since 2024-03-28 17:07:40
 */
public interface DictionaryGroupHideRoleService extends IService<DictionaryGroupHideRole> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<DictionaryGroupHideRole>
     */
    PageResult<DictionaryGroupHideRole> pageRel(DictionaryGroupHideRoleParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<DictionaryGroupHideRole>
     */
    List<DictionaryGroupHideRole> listRel(DictionaryGroupHideRoleParam param);

    /**
     * 根据id查询
     *
     * @param id 主键ID
     * @return DictionaryGroupHideRole
     */
    DictionaryGroupHideRole getByIdRel(String id);

}
