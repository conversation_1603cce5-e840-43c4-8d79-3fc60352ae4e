package com.sanythadmin.project.dictionary.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.Required;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息字段
 *
 * <AUTHOR>
 * @since 2024-03-29 15:08:45
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_DICTIONARY_FIELD")
@Table(name = "SYT_DICTIONARY_FIELD")
public class DictionaryField implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段中文名
     */
    @NotEmpty(message = "字段中文名不能为空")
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 字段英文名
     */
    @NotEmpty(message = "字段英文名不能为空")
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @NotNull(message = "是否必填不能为空")
    @Column(name = "REQUIRED", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "REQUIRED", jdbcType = JdbcType.INTEGER)
    private JudgeMark required;

    /**
     * 是否显示（1：是,  0：否）
     */
    @NotNull(message = "是否显示不能为空")
    @Column(name = "SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "SHOW_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark showFlag;

    /**
     * 所属信息组ID
     */
    @NotEmpty(message = "所属信息组不能为空")
    @Column(name = "GROUP_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 开始时间
     */
    @Column(name = "START_TIME")
    @TableField("START_TIME")
    private LocalDateTime startTime;

    /**
     * 截至时间
     */
    @Column(name = "END_TIME")
    @TableField("END_TIME")
    private LocalDateTime endTime;

    /**
     * 控件类型（短文本, 单选/多选，文本域, 日期, 小数，整数，附件等）
     */
    @NotEmpty(message = "控件类型不能为空")
    @Column(name = "CONTROL_TYPE")
    @TableField(value = "CONTROL_TYPE")
    private String controlType;

    /**
     * 正则验证表达式
     */
    @Column(name = "REG_EXPRESSION")
    @TableField("REG_EXPRESSION")
    private String regExpression;

    /**
     * 下拉控件加载数据URL ID
     */
    @Column(name = "LOAD_DATA_URL_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;

    /**
     * 加载数据URL
     */
    @Column(name = "LOAD_DATA_URL")
    @TableField("LOAD_DATA_URL")
    private String loadDataUrl;
    /**
     * value字段名
     */
    @Column(name = "VALUE_FIELD")
    @TableField("VALUE_FIELD")
    private String valueField;
    /**
     * text字段名
     */
    @Column(name = "TEXT_FIELD")
    @TableField("TEXT_FIELD")
    private String textField;

    /**
     * 是否统计（1：是，0：否）
     */
    @Column(name = "COUNT_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "COUNT_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark countFlag;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 本人是否可修改（1：是,  0：否）
     */
    @Column(name = "SELF_MODIFY_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "SELF_MODIFY_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark selfModifyFlag;

    /**
     * 可修改角色
     */
    @Column(name = "MODIFY_ROLE")
    @TableField("MODIFY_ROLE")
    private String modifyRole;

    /**
     * 本人修改是否提交审核验证（1：是,  0：否）
     */
    @Column(name = "SELF_MODIFY_VERIFY_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "SELF_MODIFY_VERIFY_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark selfModifyVerifyFlag;

    /**
     * 加载数据类型（来自公共码表code）
     */
    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;
    /**
     * 数据导入设置 （0：非必填，1：必填）
     */
    @Column(name = "import_field_flag", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "import_field_flag", jdbcType = JdbcType.INTEGER)
    private Required importFieldFlag;
    /**
     * 用户类型1：学生，2：教师
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;
    /**
     * 字段style
     */
    @Column(name = "FIELD_STYLE")
    @TableField("FIELD_STYLE")
    private String fieldStyle;
    /**
     * 列表显示（1：是,  0：否）
     */
    @Column(name = "LIST_SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "LIST_SHOW_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark listShowFlag;
    /**
     * 查询条件显示（1：是,  0：否）
     */
    @Column(name = "QUERY_SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "QUERY_SHOW_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark queryShowFlag;

    /**
     * 自定义列导出（1：是,  0：否）
     */
    @Column(name = "EXPORT_FIELD_FLAG", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "EXPORT_FIELD_FLAG", jdbcType = JdbcType.INTEGER)
    private JudgeMark exportFieldFlag;

    /**
     * 临时字段，非userInfo表字段仅显示（1：是，0：否）
     */
    @Column(name = "TEMP_FIELD", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "TEMP_FIELD", jdbcType = JdbcType.INTEGER)
    private JudgeMark tempField;

    /**
     * 工作流节点流转条件（1：是，0：否）
     */
    @Column(name = "WORKFLOW_NODE_FLOW_CONDITION", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "WORKFLOW_NODE_FLOW_CONDITION", jdbcType = JdbcType.INTEGER)
    private JudgeMark workflowNodeFlowCondition;

    /**
     * 列配置是否可以排序（1：是，0：否）
     */
    @Column(name = "sortable", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "sortable", jdbcType = JdbcType.INTEGER)
    private JudgeMark sortable;

    /**
     * 列配置是否可固定列（1：是，0：否）
     */
    @Column(name = "fixed", columnDefinition = ColumnType.NUMBER_1_DEFAULT_0)
    @TableField(value = "fixed", jdbcType = JdbcType.INTEGER)
    private JudgeMark fixed;

    /**
     * 不可见角色
     */
    @Transient
    @TableField(exist = false)
    private List<DictionaryFieldHideRole> hideRoles;
    /**
     * 培养层次
     */
    @Transient
    @TableField(exist = false)
    private List<DictionaryFieldEduLevel> eduLevels;
    /**
     * 级联关系字段集合
     */
    @Transient
    @TableField(exist = false)
    private List<DictionaryFieldLink> fieldLinks;
}
