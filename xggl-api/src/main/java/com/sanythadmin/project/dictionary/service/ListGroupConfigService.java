package com.sanythadmin.project.dictionary.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.ListGroupConfig;
import com.sanythadmin.project.dictionary.param.ListGroupConfigParam;

import java.util.List;

/**
 * 多条数据组配置Service
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
public interface ListGroupConfigService extends IService<ListGroupConfig> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<ListGroupConfig>
     */
    PageResult<ListGroupConfig> pageRel(ListGroupConfigParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<ListGroupConfig>
     */
    List<ListGroupConfig> listRel(ListGroupConfigParam param);

    /**
     * 根据id查询
     *
     * @param id 主键ID
     * @return ListGroupConfig
     */
    ListGroupConfig getByIdRel(String id);

}
