<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.dictionary.mapper.SelectControlApiMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, URL, TEXT_FIELD, VALUE_FIELD, NAME, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.url != null">
                        AND a.URL LIKE concat(concat('%',#{param.url), '%')
                    </if>
                    <if test="param.textField != null">
                        AND a.TEXT_FIELD LIKE concat(concat('%',#{param.textField), '%')
                    </if>
                    <if test="param.valueField != null">
                        AND a.VALUE_FIELD LIKE concat(concat('%',#{param.valueField), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
