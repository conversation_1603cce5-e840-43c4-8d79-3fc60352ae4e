package com.sanythadmin.project.dictionary.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryFieldEduLevel;
import com.sanythadmin.project.dictionary.param.DictionaryFieldEduLevelParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldEduLevelService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 用户信息字段与学生培养层次关系控制器
 *
 * <AUTHOR>
 * @since 2024-04-03 10:36:49
 */
@RestController
@RequestMapping("/api/dictionary/dictionaryFieldEduLevel")
public class DictionaryFieldEduLevelController extends BaseController {
    @Resource
    private DictionaryFieldEduLevelService dictionaryFieldEduLevelService;

    /**
     * 分页查询用户信息字段与学生培养层次关系（权限标识：dictionary:dictionaryFieldEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldEduLevel:list')")
    @GetMapping("/page")
    public PageResult<DictionaryFieldEduLevel> page(DictionaryFieldEduLevelParam param) {
        PageParam<DictionaryFieldEduLevel, DictionaryFieldEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = dictionaryFieldEduLevelService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户信息字段与学生培养层次关系（权限标识：dictionary:dictionaryFieldEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldEduLevel:list')")
    @GetMapping()
    public List<DictionaryFieldEduLevel> list(DictionaryFieldEduLevelParam param) {
        PageParam<DictionaryFieldEduLevel, DictionaryFieldEduLevelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return dictionaryFieldEduLevelService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询用户信息字段与学生培养层次关系（权限标识：dictionary:dictionaryFieldEduLevel:list）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldEduLevel:list')")
    @GetMapping("/{id}")
    public DictionaryFieldEduLevel get(@PathVariable("id") Integer id) {
        return dictionaryFieldEduLevelService.getById(id);
    }

    /**
     * 添加或修改用户信息字段与学生培养层次关系（权限标识：dictionary:dictionaryFieldEduLevel:operation）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldEduLevel:operation')")
    @OperationLog(module = "用户信息字段与学生培养层次关系", comments = "保存用户信息字段与学生培养层次关系")
    @PostMapping("/operation")
    public void save(@RequestBody DictionaryFieldEduLevel dictionaryFieldEduLevel) {
        if (StringUtils.hasLength(dictionaryFieldEduLevel.getId())) {
            dictionaryFieldEduLevelService.updateById(dictionaryFieldEduLevel);
        } else {
            dictionaryFieldEduLevelService.save(dictionaryFieldEduLevel);
        }
    }

    /**
     * 批量删除用户信息字段与学生培养层次关系（权限标识：dictionary:dictionaryFieldEduLevel:remove）
     */
    @PreAuthorize("hasAuthority('dictionary:dictionaryFieldEduLevel:remove')")
    @OperationLog(module = "用户信息字段与学生培养层次关系", comments = "批量删除用户信息字段与学生培养层次关系")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dictionaryFieldEduLevelService.removeByIds(ids);
    }
}
