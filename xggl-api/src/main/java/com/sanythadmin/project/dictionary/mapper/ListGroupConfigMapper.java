package com.sanythadmin.project.dictionary.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.dictionary.entity.ListGroupConfig;
import com.sanythadmin.project.dictionary.param.ListGroupConfigParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 多条数据组配置Mapper
 *
 * <AUTHOR>
 * @since 2024-03-28 11:22:08
 */
public interface ListGroupConfigMapper extends BaseMapper<ListGroupConfig> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<ListGroupConfig>
     */
    List<ListGroupConfig> selectPageRel(@Param("page") IPage<ListGroupConfig> page,
                             @Param("param") ListGroupConfigParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<ListGroupConfig> selectListRel(@Param("param") ListGroupConfigParam param);

}
