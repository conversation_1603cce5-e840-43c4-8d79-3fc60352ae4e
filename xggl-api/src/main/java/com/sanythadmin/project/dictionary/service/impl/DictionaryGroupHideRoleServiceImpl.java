package com.sanythadmin.project.dictionary.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.dictionary.entity.DictionaryGroupHideRole;
import com.sanythadmin.project.dictionary.mapper.DictionaryGroupHideRoleMapper;
import com.sanythadmin.project.dictionary.param.DictionaryGroupHideRoleParam;
import com.sanythadmin.project.dictionary.service.DictionaryGroupHideRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户信息组不可见角色Service实现
 *
 * <AUTHOR>
 * @since 2024-03-28 17:07:40
 */
@Service
public class DictionaryGroupHideRoleServiceImpl extends ServiceImpl<DictionaryGroupHideRoleMapper, DictionaryGroupHideRole> implements DictionaryGroupHideRoleService {

    @Override
    public PageResult<DictionaryGroupHideRole> pageRel(DictionaryGroupHideRoleParam param) {
        PageParam<DictionaryGroupHideRole, DictionaryGroupHideRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<DictionaryGroupHideRole> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<DictionaryGroupHideRole> listRel(DictionaryGroupHideRoleParam param) {
        List<DictionaryGroupHideRole> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<DictionaryGroupHideRole, DictionaryGroupHideRoleParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public DictionaryGroupHideRole getByIdRel(String id) {
        DictionaryGroupHideRoleParam param = new DictionaryGroupHideRoleParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
