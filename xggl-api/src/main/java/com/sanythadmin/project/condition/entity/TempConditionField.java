package com.sanythadmin.project.condition.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 限制条件字段配置
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_TEMP_CONDITION_FIELD")
@Entity
@Table(name = "SYT_TEMP_CONDITION_FIELD")
public class TempCondition<PERSON>ield implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @Column(name = "REQUIRED", columnDefinition = ColumnType.NUMBER_1)
    @TableField("REQUIRED")
    private JudgeMark required;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 控件类型
     */
    @Column(name = "CONTROL_TYPE")
    @TableField("CONTROL_TYPE")
    private String controlType;

    /**
     * 正则验证表达式
     */
    @Column(name = "REG_EXPRESSION")
    @TableField("REG_EXPRESSION")
    private String regExpression;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;

    /**
     * 加载数据URL
     */
    @Column(name = "LOAD_DATA_URL")
    @TableField("LOAD_DATA_URL")
    private String loadDataUrl;

    @Column(name = "LOAD_DATA_URL_ID")
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;

    /**
     * 条件ID
     */
    @Column(name = "CONDITION_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CONDITION_ID")
    private String conditionId;

}
