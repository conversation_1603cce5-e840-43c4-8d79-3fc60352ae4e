<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.condition.mapper.TempConditionFieldMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, FIELD_EN, FIELD_ZH, REQUIRED, TIP_TEXT, CONTROL_TYPE, REG_EXPRESSION, SORT, LOAD_DATA_TYPE, LOAD_DATA_URL_ID, CONDITION_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.fieldEn != null">
                        AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
                    </if>
                    <if test="param.fieldZh != null">
                        AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh), '%')
                    </if>
                    <if test="param.required != null">
                        AND a.REQUIRED = #{param.required}
                    </if>
                    <if test="param.tipText != null">
                        AND a.TIP_TEXT LIKE concat(concat('%',#{param.tipText), '%')
                    </if>
                    <if test="param.controlType != null">
                        AND a.CONTROL_TYPE LIKE concat(concat('%',#{param.controlType), '%')
                    </if>
                    <if test="param.regExpression != null">
                        AND a.REG_EXPRESSION LIKE concat(concat('%',#{param.regExpression), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.loadDataType != null">
                        AND a.LOAD_DATA_TYPE LIKE concat(concat('%',#{param.loadDataType), '%')
                    </if>
                    <if test="param.loadDataUrlId != null">
                        AND a.LOAD_DATA_URL_ID LIKE concat(concat('%',#{param.loadDataUrlId), '%')
                    </if>
                    <if test="param.conditionId != null">
                        AND a.CONDITION_ID LIKE concat(concat('%',#{param.conditionId), '%')
                    </if>
    </sql>
</mapper>
