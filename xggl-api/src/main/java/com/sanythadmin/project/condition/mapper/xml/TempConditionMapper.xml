<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.condition.mapper.TempConditionMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONDITION_NAME, SORT, MODULE, CONDITION, DESCRIPTION, STATE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.conditionName != null">
                        AND a.CONDITION_NAME LIKE concat(concat('%',#{param.conditionName), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.module != null">
                        AND a.MODULE LIKE concat(concat('%',#{param.module), '%')
                    </if>
                    <if test="param.condition != null">
                        AND a.CONDITION LIKE concat(concat('%',#{param.condition), '%')
                    </if>
                    <if test="param.description != null">
                        AND a.DESCRIPTION LIKE concat(concat('%',#{param.description), '%')
                    </if>
                    <if test="param.state != null">
                        AND a.STATE = #{param.state}
                    </if>
    </sql>
</mapper>
