package com.sanythadmin.project.condition.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 限制条件字段配置查询参数
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TempConditionFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark required;

    /**
     * 提示文本
     */
    private String tipText;

    /**
     * 控件类型
     */
    private String controlType;

    /**
     * 正则验证表达式
     */
    private String regExpression;

    private String loadDataType;

    private String loadDataUrlId;

    /**
     * 条件ID
     */
    @QueryField(type = QueryType.EQ)
    private String conditionId;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
