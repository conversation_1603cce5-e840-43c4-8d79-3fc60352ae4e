package com.sanythadmin.project.condition.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.condition.entity.TempConditionField;
import com.sanythadmin.project.condition.param.TempConditionFieldParam;
import com.sanythadmin.project.condition.service.TempConditionFieldService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 限制条件字段配置控制器
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@RestController
@RequestMapping("/api/condition/temp-condition-field")
public class TempConditionFieldController extends BaseController {
    @Resource
    private TempConditionFieldService tempConditionFieldService;

    /**
     * 分页查询限制条件字段配置（权限标识：condition:tempConditionField:list）
     */
    @PreAuthorize("hasAuthority('condition:tempConditionField:list')")
    @GetMapping("/page")
    public PageResult<TempConditionField> page(TempConditionFieldParam param) {
        PageParam<TempConditionField, TempConditionFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = tempConditionFieldService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部限制条件字段配置（权限标识：condition:tempConditionField:list）
     */
    @PreAuthorize("hasAuthority('condition:tempConditionField:list')")
    @GetMapping()
    public List<TempConditionField> list(TempConditionFieldParam param) {
        PageParam<TempConditionField, TempConditionFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return tempConditionFieldService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询限制条件字段配置（权限标识：condition:tempConditionField:list）
     */
    @PreAuthorize("hasAuthority('condition:tempConditionField:list')")
    @GetMapping("/{id}")
    public TempConditionField get(@PathVariable("id") String id) {
        return tempConditionFieldService.getById(id);
    }

    /**
     * 添加或修改限制条件字段配置（权限标识：condition:tempConditionField:operation）
     */
    @PreAuthorize("hasAuthority('condition:tempConditionField:operation')")
    @OperationLog(module = "限制条件字段配置", comments = "保存限制条件字段配置")
    @PostMapping("/operation")
    public void save(@RequestBody List<TempConditionField> fields) {
        for (TempConditionField tempConditionField : fields) {
            if (tempConditionField.getSort() == null) {
                SortHelper<TempConditionField> sortHelper = new SortHelper<>(tempConditionFieldService, "sort");
                tempConditionField.setSort(sortHelper.next());
            }

            if (StringUtils.hasLength(tempConditionField.getId())) {
                tempConditionFieldService.updateById(tempConditionField);
            } else {
                tempConditionFieldService.save(tempConditionField);
            }
        }
    }

    /**
     * 批量删除限制条件字段配置（权限标识：condition:tempConditionField:remove）
     */
    @PreAuthorize("hasAuthority('condition:tempConditionField:remove')")
    @OperationLog(module = "限制条件字段配置", comments = "批量删除限制条件字段配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        tempConditionFieldService.removeByIds(ids);
    }
}
