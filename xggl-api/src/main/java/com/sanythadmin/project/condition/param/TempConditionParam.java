package com.sanythadmin.project.condition.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 限制条件查询参数
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TempConditionParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * 限制条件别名
     */
    @QueryField(type = QueryType.EQ)
    private String conditionKey;
    /**
     * 限制条件名称
     */
    private String conditionName;

    /**
     * 业务模块
     */
    private String module;

    /**
     * 条件, 逗号分割
     */
    private String condition;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态：启用，停用
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark enable;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
