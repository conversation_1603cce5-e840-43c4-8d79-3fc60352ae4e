package com.sanythadmin.project.condition.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.condition.entity.TempConditionField;
import com.sanythadmin.project.condition.mapper.TempConditionFieldMapper;
import com.sanythadmin.project.condition.mapper.TempConditionMapper;
import com.sanythadmin.project.condition.service.TempConditionService;
import com.sanythadmin.project.condition.entity.TempCondition;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 限制条件Service实现
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Service
public class TempConditionServiceImpl extends ServiceImpl<TempConditionMapper, TempCondition> implements TempConditionService {

    @Resource
    private TempConditionMapper mapper;
    @Resource
    private TempConditionFieldMapper tempConditionFieldMapper;

    @Transactional
    @Override
    public void delete(List<String> ids) {
        tempConditionFieldMapper.delete(new LambdaQueryWrapper<TempConditionField>()
                .in(TempConditionField::getConditionId, ids));
        mapper.deleteBatchIds(ids);
    }
}
