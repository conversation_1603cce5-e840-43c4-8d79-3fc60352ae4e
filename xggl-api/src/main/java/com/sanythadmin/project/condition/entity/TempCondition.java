package com.sanythadmin.project.condition.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 限制条件
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_TEMP_CONDITION")
@Entity
@Table(name = "SYT_TEMP_CONDITION")
public class TempCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 限制条件别名
     */
    @Column(name = "CONDITION_KEY")
    @TableField("CONDITION_KEY")
    private String conditionKey;

    /**
     * 限制条件名称
     */
    @Column(name = "CONDITION_NAME")
    @TableField("CONDITION_NAME")
    private String conditionName;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 业务模块
     */
    @Column(name = "MODULE")
    @TableField("MODULE")
    private String module;

    /**
     * 条件
     */
    @Column(name = "CONDITION", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONDITION")
    private String condition;

    /**
     * 描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 状态：启用
     */
    @Column(name = "ENABLE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("ENABLE")
    private JudgeMark enable;

}
