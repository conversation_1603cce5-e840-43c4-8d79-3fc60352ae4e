package com.sanythadmin.project.condition.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.condition.mapper.TempConditionFieldMapper;
import com.sanythadmin.project.condition.service.TempConditionFieldService;
import com.sanythadmin.project.condition.entity.TempConditionField;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 限制条件字段配置Service实现
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@Service
public class TempConditionFieldServiceImpl extends ServiceImpl<TempConditionFieldMapper, TempConditionField> implements TempConditionFieldService {

    @Resource
    private TempConditionFieldMapper mapper;

}
