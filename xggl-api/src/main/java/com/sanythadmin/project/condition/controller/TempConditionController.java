package com.sanythadmin.project.condition.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.condition.entity.TempCondition;
import com.sanythadmin.project.condition.param.TempConditionParam;
import com.sanythadmin.project.condition.service.TempConditionService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 限制条件控制器
 *
 * <AUTHOR>
 * @since 2024-09-06 14:44:04
 */
@RestController
@RequestMapping("/api/condition/temp-condition")
public class TempConditionController extends BaseController {
    @Resource
    private TempConditionService tempConditionService;

    /**
     * 分页查询限制条件（权限标识：condition:tempCondition:list）
     */
    @PreAuthorize("hasAuthority('condition:tempCondition:list')")
    @GetMapping("/page")
    public PageResult<TempCondition> page(TempConditionParam param) {
        PageParam<TempCondition, TempConditionParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = tempConditionService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部限制条件（权限标识：condition:tempCondition:list）
     */
    @PreAuthorize("hasAuthority('condition:tempCondition:list')")
    @GetMapping()
    public List<TempCondition> list(TempConditionParam param) {
        PageParam<TempCondition, TempConditionParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return tempConditionService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询限制条件（权限标识：condition:tempCondition:list）
     */
    @PreAuthorize("hasAuthority('condition:tempCondition:list')")
    @GetMapping("/{id}")
    public TempCondition get(@PathVariable("id") String id) {
        return tempConditionService.getById(id);
    }

    /**
     * 添加或修改限制条件（权限标识：condition:tempCondition:operation）
     */
    @PreAuthorize("hasAuthority('condition:tempCondition:operation')")
    @OperationLog(module = "限制条件", comments = "保存限制条件")
    @PostMapping("/operation")
    public void save(@RequestBody TempCondition tempCondition) {
        if (tempCondition.getSort() == null) {
            SortHelper<TempCondition> sortHelper = new SortHelper<>(tempConditionService, "sort");
            tempCondition.setSort(sortHelper.next());
        }

        TempConditionParam tempConditionParam = new TempConditionParam();
        tempConditionParam.setConditionKey(tempCondition.getConditionKey());
        List<TempCondition> list = list(tempConditionParam);
        if (StringUtils.hasLength(tempCondition.getId())) {
            if (!CollectionUtils.isEmpty(list) && !Objects.equals(list.get(0).getId(), tempCondition.getId()))
                AssertUtil.throwMessage("限制条件英文名称已存在");
            tempConditionService.updateById(tempCondition);
        } else {
            if (!CollectionUtils.isEmpty(list))
                AssertUtil.throwMessage("限制条件英文名称已存在");
            tempConditionService.save(tempCondition);
        }
    }

    /**
     * 批量删除限制条件（权限标识：condition:tempCondition:remove）
     */
    @PreAuthorize("hasAuthority('condition:tempCondition:remove')")
    @OperationLog(module = "限制条件", comments = "批量删除限制条件")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        tempConditionService.delete(ids);
    }
}
