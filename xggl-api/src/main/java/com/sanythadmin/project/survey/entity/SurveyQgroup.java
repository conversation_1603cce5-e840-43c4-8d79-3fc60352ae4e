package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 问卷问题组
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_QGROUP")
@Entity
@Table(name = "SYT_SURVEY_QGROUP")
public class SurveyQgroup implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问卷id
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 分组名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 问题数
     */
    @Column(name = "Q_SUM")
    @TableField("Q_SUM")
    private Integer qSum;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
