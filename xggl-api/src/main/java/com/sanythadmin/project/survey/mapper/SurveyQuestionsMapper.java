package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyQuestions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷题库问题Mapper
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
public interface SurveyQuestionsMapper extends BaseMapper<SurveyQuestions> {

    @Delete("<script>" +
            "delete from SYT_SURVEY_QUESTIONS where CID in "+
            "<foreach collection=\"cids\" item=\"cid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{cid}" +
            "</foreach>"+
            "</script>")
    void deleteByClassIds(@Param("cids") List<String> cids);

}
