package com.sanythadmin.project.survey.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

public enum Mdlx implements TextBaseEnum {
    YD("应答"),
    WD("未答")
    ;

    @EnumValue
    @JsonValue
    private final String text;

    Mdlx(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    public static Mdlx get(String text) {
        for (Mdlx mdlx : Mdlx.values()) {
            boolean equals = mdlx.getText().equals(text);
            if (equals) {
                return mdlx;
            }
        }
        return null;
    }
}
