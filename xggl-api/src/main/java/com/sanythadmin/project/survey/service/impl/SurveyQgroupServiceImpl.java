package com.sanythadmin.project.survey.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyQgroup;
import com.sanythadmin.project.survey.enums.SurveyType;
import com.sanythadmin.project.survey.mapper.SurveyMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsOptionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyQgroupMapper;
import com.sanythadmin.project.survey.param.SurveyQgroupParam;
import com.sanythadmin.project.survey.service.SurveyQgroupService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * 问卷问题组Service实现
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Service
public class SurveyQgroupServiceImpl extends ServiceImpl<SurveyQgroupMapper, SurveyQgroup> implements SurveyQgroupService {

    @Resource
    private SurveyQgroupMapper mapper;
    @Resource
    private SurveyPquestionsMapper surveyPquestionsMapper;
    @Resource
    private SurveyPquestionsOptionsMapper surveyPquestionsOptionsMapper;
    @Resource
    private SurveyService surveyService;
    @Resource
    private SurveyMapper surveyMapper;

    @Override
    public SurveyVO removeSurveyQgroup(SurveyQgroupParam param) {
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        String ids = param.getId();
        surveyPquestionsMapper.deleteByGid(Arrays.asList(ids.split(",")));
        surveyPquestionsOptionsMapper.deleteByGid(Arrays.asList(ids.split(",")));
        removeByIds(Arrays.asList(ids.split(",")));
        Survey survey = surveyService.getById(param.getSid());
        if(SurveyType.GDXT.getText().equals(survey.getWjms())){
            surveyMapper.updateSum(param.getSid());
        }else{
            surveyMapper.updateSumByRule(param.getSid());
        }
        return surveyService.getSurveyVO(param.getSid());
    }
}
