package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyPquestionsRule;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.service.SurveyPquestionsRuleService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷试题抽题规则控制器
 *
 * <AUTHOR>
 * @since 2024-12-12 16:33:48
 */
@RestController
@RequestMapping("/api/survey/survey-pquestions-rule")
public class SurveyPquestionsRuleController extends BaseController {
    @Resource
    private SurveyPquestionsRuleService surveyPquestionsRuleService;

    /**
     * 分页查询问卷试题抽题规则（权限标识：survey:surveyPquestionsRule:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:list')")
    @GetMapping("/page")
    public PageResult<SurveyPquestionsRule> page(SurveyPquestionsRuleParam param) {
        PageParam<SurveyPquestionsRule, SurveyPquestionsRuleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyPquestionsRuleService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷试题抽题规则（权限标识：survey:surveyPquestionsRule:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:list')")
    @GetMapping()
    public List<SurveyPquestionsRule> list(SurveyPquestionsRuleParam param) {
        PageParam<SurveyPquestionsRule, SurveyPquestionsRuleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyPquestionsRuleService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷试题抽题规则（权限标识：survey:surveyPquestionsRule:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:list')")
    @GetMapping("/{id}")
    public SurveyPquestionsRule get(@PathVariable("id") String id) {
        return surveyPquestionsRuleService.getById(id);
    }

    /**
     * 添加或修改问卷试题抽题规则（权限标识：survey:surveyPquestionsRule:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:operation')")
    @OperationLog(module = "问卷试题抽题规则", comments = "保存问卷试题抽题规则")
    @PostMapping("/operation")
    public SurveyVO save(@RequestBody SurveyPquestionsRuleParam param) {
        return surveyPquestionsRuleService.saveSurveyPquestionsRule(param);
    }

    /**
     * 批量添加或修改试卷试题抽题规则（权限标识：survey:surveyPquestionsRule:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:operation')")
    @OperationLog(module = "问卷试题抽题规则", comments = "批量保存试卷试题抽题规则")
    @PostMapping("/operationBatch")
    public SurveyVO saveBatch(@RequestBody List<SurveyPquestionsRuleParam> param) {
        return surveyPquestionsRuleService.saveBatchExamPaperQuestionsRule(param);
    }

    /**
     * 批量删除问卷试题抽题规则（权限标识：survey:surveyPquestionsRule:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsRule:remove')")
    @OperationLog(module = "问卷试题抽题规则", comments = "批量删除问卷试题抽题规则")
    @PostMapping("/remove")
    public SurveyVO remove(@RequestBody SurveyPquestionsRuleParam param) {
        return surveyPquestionsRuleService.removeSurveyPquestionsRule(param);
    }
}
