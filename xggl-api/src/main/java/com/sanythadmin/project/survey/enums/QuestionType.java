package com.sanythadmin.project.survey.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

public enum QuestionType implements TextBaseEnum {
    DX("单选题"),
    MX("多选题"),
    TK("填空题"),
    PF("评分题"),
    SC("上传题");

    @JsonValue
    @EnumValue
    private String text;

    QuestionType(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    public static QuestionType get(String text) {
        for (QuestionType questionType : QuestionType.values()) {
            boolean equals = questionType.getText().equals(text);
            if (equals) {
                return questionType;
            }
        }
        return null;
    }

}