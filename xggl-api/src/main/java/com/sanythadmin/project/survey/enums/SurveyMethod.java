package com.sanythadmin.project.survey.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

public enum SurveyMethod implements TextBaseEnum {
    SM("实名"),
    NM("匿名")
    ;

    @EnumValue
    @JsonValue
    private final String text;

    SurveyMethod(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    public static SurveyMethod get(String text) {
        for (SurveyMethod surveyMethod : SurveyMethod.values()) {
            boolean equals = surveyMethod.getText().equals(text);
            if (equals) {
                return surveyMethod;
            }
        }
        return null;
    }
}
