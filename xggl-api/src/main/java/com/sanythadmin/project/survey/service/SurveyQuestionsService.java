package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyQuestions;
import com.sanythadmin.project.survey.param.SurveyQuestionsParam;
import com.sanythadmin.project.survey.vo.SurveyQuestionsVO;
import jakarta.servlet.ServletOutputStream;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 问卷题库问题Service
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
public interface SurveyQuestionsService extends IService<SurveyQuestions> {

    void saveQuestionsAndOptions(SurveyQuestions param);

    void removeQuestionsAndOptions(List<String> ids);

    SurveyQuestionsVO getByIdAndOptions(String id);

    void importSurveyQuestions(MultipartFile file);

    void exportTemplate(ServletOutputStream outputStream);

    /**
     * 分页查询不在问卷中的试题
     *
     * @param param param
     * @return {@link PageResult }<{@link SurveyQuestions }>
     */
    PageResult<SurveyQuestions> getQuestionsNotInPaper(SurveyQuestionsParam param);
}
