package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.survey.entity.SurveyPquestionsOptions;
import com.sanythadmin.project.survey.enums.QuestionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 问卷问题查询参数
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyPquestionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 问题名称
     */
    private String name;

    /**
     * 问题类型
     */
    @QueryField(type = QueryType.EQ)
    private QuestionType qtype;

    /**
     * 问题分类id
     */
    @QueryField(type = QueryType.EQ)
    private String cid;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    @QueryField(type = QueryType.EQ)
    private String createUser;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
    /**
     * 是否必填
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfbt;

    /**
     * 评分最小分值
     */
    private Double minScore;

    /**
     * 评分最大分值
     */
    private Double maxScore;

    /**
     * 题库问题id
     */
    @QueryField(type = QueryType.EQ)
    private String qid;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

    /**
     * 问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 分组ID
     */
    @QueryField(type = QueryType.EQ)
    private String gid;

    @QueryField(ignore = true)
    private List<SurveyPquestionsOptions> surveyPquestionsOptionsList;
}
