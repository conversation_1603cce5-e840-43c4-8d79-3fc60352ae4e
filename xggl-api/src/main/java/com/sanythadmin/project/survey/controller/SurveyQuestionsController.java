package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.survey.entity.SurveyQuestions;
import com.sanythadmin.project.survey.param.SurveyQuestionsParam;
import com.sanythadmin.project.survey.service.SurveyQuestionsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 问卷调查/题库问题控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@RestController
@RequestMapping("/api/survey/survey-questions")
public class SurveyQuestionsController extends BaseController {
    @Resource
    private SurveyQuestionsService surveyQuestionsService;
    @Resource
    private UserInfoService userInfoService;

    /**
     * 分页查询问卷题库问题（权限标识：survey:surveyQuestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:list')")
    @GetMapping("/page")
    public PageResult<SurveyQuestions> page(SurveyQuestionsParam param) {
        PageParam<SurveyQuestions, SurveyQuestionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyQuestionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 分页查询不在问卷的试题（权限标识：survey:surveyQuestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:list')")
    @GetMapping("/getQuestionsNotInPaper")
    public PageResult<SurveyQuestions> getQuestionsNotInPaper(SurveyQuestionsParam param) {
        return surveyQuestionsService.getQuestionsNotInPaper(param);
    }

    /**
     * 查询全部问卷题库问题（权限标识：survey:surveyQuestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:list')")
    @GetMapping()
    public List<SurveyQuestions> list(SurveyQuestionsParam param) {
        PageParam<SurveyQuestions, SurveyQuestionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyQuestionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷题库问题（权限标识：survey:surveyQuestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:list')")
    @GetMapping("/{id}")
    public SurveyQuestions get(@PathVariable("id") String id) {
        return surveyQuestionsService.getByIdAndOptions(id);
    }

    /**
     * 添加或修改问卷题库问题（权限标识：survey:surveyQuestions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:operation')")
    @OperationLog(module = "问卷题库问题", comments = "保存问卷题库问题")
    @PostMapping("/operation")
    public void save(@RequestBody SurveyQuestions param) {
        surveyQuestionsService.saveQuestionsAndOptions(param);
    }

    /**
     * 批量删除问卷题库问题（权限标识：survey:surveyQuestions:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:remove')")
    @OperationLog(module = "问卷题库问题", comments = "批量删除问卷题库问题")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyQuestionsService.removeQuestionsAndOptions(ids);
    }

    /**
     * 获取题数
     */
//    @PreAuthorize("hasAuthority('survey:surveyQuestions:getSum')")
    @OperationLog(module = "试题", comments = "获取题数")
    @GetMapping("/getSum")
    public long getSum(SurveyQuestionsParam param) {
        PageParam<SurveyQuestions, SurveyQuestionsParam> page = new PageParam<>(param);
        return surveyQuestionsService.count(page.getOrderWrapper());
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @OperationLog(module = "试题", comments = "下载导入模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("问题导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        surveyQuestionsService.exportTemplate(response.getOutputStream());
    }

    /**
     * 导入试题 （权限标识：survey:surveyQuestions:import）
     *
     * @param file 导入文件
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestions:import')")
    @PostMapping("/import")
    @OperationLog(module = "试题", comments = "导入试题")
    public void importData(@RequestParam(name = "file") MultipartFile file){
        surveyQuestionsService.importSurveyQuestions(file);
    }
}
