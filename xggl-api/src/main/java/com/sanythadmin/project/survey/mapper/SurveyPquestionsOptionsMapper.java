package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyPquestionsOptions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷试题选项Mapper
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
public interface SurveyPquestionsOptionsMapper extends BaseMapper<SurveyPquestionsOptions> {


    @Delete("<script>" +
            "delete from SYT_SURVEY_PQUESTIONS_OPTIONS where sid in "+
            "<foreach collection=\"sids\" item=\"sid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{sid}" +
            "</foreach>"+
            "</script>")
    void deleteBySid(@Param("sids") List<String> sids);

    @Delete("<script>" +
            "delete from SYT_SURVEY_PQUESTIONS_OPTIONS where gid in "+
            "<foreach collection=\"gids\" item=\"gid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{gid}" +
            "</foreach>"+
            "</script>")
    void deleteByGid(@Param("gids") List<String> gids);

    @Delete("<script>" +
            "delete from SYT_SURVEY_PQUESTIONS_OPTIONS where S_QID in "+
            "<foreach collection=\"qids\" item=\"qid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{qid}" +
            "</foreach>"+
            "</script>")
    void deleteByQid(@Param("qids") List<String> qids);
}
