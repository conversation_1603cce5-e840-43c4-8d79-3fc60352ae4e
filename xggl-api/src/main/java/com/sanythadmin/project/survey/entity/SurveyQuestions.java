package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.enums.QuestionType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷题库问题
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_QUESTIONS")
@Entity
@Table(name = "SYT_SURVEY_QUESTIONS")
public class SurveyQuestions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问题名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 问题类型
     */
    @Column(name = "QTYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "QTYPE",jdbcType = JdbcType.VARCHAR)
    private QuestionType qtype;

    /**
     * 问题分类ID
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 是否必填
     */
    @Column(name = "SFBT")
    @TableField("SFBT")
    private String sfbt;

    /**
     * 最小分值
     */
    @Column(name = "MIN_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MIN_SCORE")
    private Double minScore;

    /**
     * 最大分值
     */
    @Column(name = "MAX_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MAX_SCORE")
    private Double maxScore;
    /**
     * 排序值
     */
    @Column(name = "sort", columnDefinition = ColumnType.NUMBER_10)
    @TableField("sort")
    private Integer sort;
    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;
    /**
     * 备注1
     */
    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;
    /**
     * 备注2
     */
    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;

    /**
     * 分组(传值给SurveyPquestions)
     */
    @Transient
    @TableField(exist = false)
    private String gname;

    /**
     * 题库试题ID(传值给SurveyPquestions)
     */
    @Transient
    @TableField(exist = false)
    private String qid;

    @Transient
    @TableField(exist = false)
    private List<SurveyQuestionsOptions> surveyQuestionsOptionsList;

}
