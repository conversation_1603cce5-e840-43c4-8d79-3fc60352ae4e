package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷问题组查询参数
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyQgroupParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 问卷id
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 问题数
     */
    @QueryField(ignore = true)
    private Integer qSum;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

}
