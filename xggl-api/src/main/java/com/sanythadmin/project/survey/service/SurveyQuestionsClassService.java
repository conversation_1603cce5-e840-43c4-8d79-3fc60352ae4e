package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.survey.entity.SurveyQuestionsClass;

import java.util.List;
import java.util.Map;

/**
 * 问卷题库问题分类Service
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
public interface SurveyQuestionsClassService extends IService<SurveyQuestionsClass> {

    Map<String, String> getAllNameIdMap();
    void removeQuestionsClassByIds(List<String> ids);
}
