package com.sanythadmin.project.survey.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyAnswer;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.mapper.SurveyAnswerMapper;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.service.SurveyAnswerInfoService;
import com.sanythadmin.project.survey.service.SurveyAnswerService;
import com.sanythadmin.project.survey.service.SurveyService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;

/**
 * 问卷答卷信息Service实现
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Service
public class SurveyAnswerServiceImpl extends ServiceImpl<SurveyAnswerMapper, SurveyAnswer> implements SurveyAnswerService {

    @Resource
    private SurveyAnswerMapper surveyAnswerMapper;
    @Resource
    private SurveyService surveyService;
    @Resource
    private SurveyAnswerInfoService surveyAnswerInfoService;
    @Resource
    private SelectorAccountService selectorAccountService;
    @Resource
    private GridFsService gridFsService;

    @Transactional
    @Override
    public void saveAnswerAndHistory(SurveyAnswerInfoParam param) {
        Survey survey = surveyService.getById(param.getSid());
        if (survey == null) {
            AssertUtil.throwMessage("未找到问卷");
        }
//        SurveyType wjms = survey.getWjms();
        SysAccount account = getAccount();
       /* boolean b = selectorAccountService.checkExistSelectorAccount(survey.getId(), account.getUsername());
        if (!b) {
            AssertUtil.throwMessage("不在答卷名单内");
        }*/

        long count = surveyAnswerInfoService.count(new LambdaQueryWrapper<SurveyAnswerInfo>().eq(SurveyAnswerInfo::getSid,survey.getId())
                .eq(SurveyAnswerInfo::getXgh,account.getUsername()));
        if (count > 0) {
            AssertUtil.throwMessage("该问卷已答过");
        }

        SurveyAnswerInfo surveyAnswerInfo = new SurveyAnswerInfo();
        surveyAnswerInfo.setSid(survey.getId());
        surveyAnswerInfo.setXgh(account.getUsername());
        surveyAnswerInfo.setId(CommonUtil.getUUID());
        surveyAnswerInfo.setUsedTime(param.getUsedTime());
        surveyAnswerInfo.setCreateDate(LocalDateTime.now());

        List<SurveyAnswer> answers = param.getAnswers();
        AtomicReference<Double> score = new AtomicReference<>(0d);
        answers.forEach(answer -> {
            answer.setInfoId(surveyAnswerInfo.getId());
            answer.setSid(param.getSid());
            answer.setXgh(account.getUsername());
            answer.setCreateDate(LocalDateTime.now());
            if (answer.getPoint() != null) {
                score.updateAndGet(v -> v + answer.getPoint());
            }
            //处理附件
            if (CollectionUtils.isNotEmpty(answer.getResultFiles())) {
                List<MultipartFile> attachment = new ArrayList<>();
                List<SurveyAnswer.ResultItem> resultFiles = answer.getResultFiles();
                resultFiles.forEach(resultItem -> attachment.add(resultItem.getFile()));
                String fileInfoStr = gridFsService.save(answer.getId(),attachment.toArray(new MultipartFile[]{}));
                answer.setResult(fileInfoStr);
            }
        });
        Double aDouble = score.get();
        surveyAnswerInfo.setScore(aDouble);
        surveyAnswerInfoService.save(surveyAnswerInfo);
        this.saveBatch(answers);
    }

    @Override
    public boolean isAnswered(SurveyAnswer answer) {
        long count = count(new LambdaQueryWrapper<SurveyAnswer>().eq(SurveyAnswer::getSid, answer.getSid())
                .eq(StringUtils.hasLength(answer.getXgh()), SurveyAnswer::getXgh, answer.getXgh()));
        return count > 0;
    }
}
