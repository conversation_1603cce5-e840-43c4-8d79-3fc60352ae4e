package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyAnswer;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.param.SurveyAnswerParam;
import com.sanythadmin.project.survey.service.SurveyAnswerService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 问卷调查/问卷答卷信息控制器
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@RestController
@RequestMapping("/api/survey/survey-answer")
public class SurveyAnswerController extends BaseController {
    @Resource
    private SurveyAnswerService surveyAnswerService;

    /**
     * 分页查询问卷答卷信息（权限标识：survey:surveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:list')")
    @GetMapping("/page")
    public PageResult<SurveyAnswer> page(SurveyAnswerParam param) {
        PageParam<SurveyAnswer, SurveyAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyAnswerService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷答卷信息（权限标识：survey:surveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:list')")
    @GetMapping()
    public List<SurveyAnswer> list(SurveyAnswerParam param) {
        PageParam<SurveyAnswer, SurveyAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyAnswerService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷答卷信息（权限标识：survey:surveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:list')")
    @GetMapping("/{id}")
    public SurveyAnswer get(@PathVariable("id") String id) {
        return surveyAnswerService.getById(id);
    }

    /**
     * 答卷接口（权限标识：survey:surveyAnswer:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:operation')")
    @OperationLog(module = "问卷答卷信息", comments = "答卷接口")
    @PostMapping("/operation")
    public void save(MultipartHttpServletRequest request) {
        Map<String, Object> parsedData = CommonUtil.parseFormDataRequest(request);
        SurveyAnswerInfoParam param = CommonUtil.toBean(parsedData, SurveyAnswerInfoParam.class);
        surveyAnswerService.saveAnswerAndHistory(param);
    }

    /**
     * 批量删除问卷答卷信息（权限标识：survey:surveyAnswer:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:remove')")
    @OperationLog(module = "问卷答卷信息", comments = "批量删除问卷答卷信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyAnswerService.removeByIds(ids);
    }

}
