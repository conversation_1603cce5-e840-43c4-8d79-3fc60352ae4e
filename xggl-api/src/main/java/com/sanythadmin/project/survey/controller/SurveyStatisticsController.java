package com.sanythadmin.project.survey.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.Resp;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.enums.ExportType;
import com.sanythadmin.project.export.param.ExportParams;
import com.sanythadmin.project.export.service.AsyncExportService;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.mapper.SurveyStatisticsMapper;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.param.SurveyAnswerParam;
import com.sanythadmin.project.survey.service.SurveyStatisticsService;
import com.sanythadmin.project.survey.vo.SurveyStatisticsVO;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.util.Map;

/**
 * 问卷调查/问卷统计
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Slf4j
@RestController
@RequestMapping("/api/survey/survey-statistics")
public class SurveyStatisticsController extends BaseController {

    @Resource
    private SurveyStatisticsMapper surveyStatisticsMapper;
    @Resource
    private SurveyStatisticsService surveyStatisticsService;
    @Resource
    private AsyncExportService asyncExportService;


    /**
     * 获取问卷统计 （权限标识：survey:survey:statistics）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics')")
    @OperationLog(module = "问卷统计", comments = "获取问卷统计")
    @GetMapping("/selectSurveyWithStats")
    public PageResult<SurveyStatisticsVO> selectSurveyWithStats(@Valid SurveyAnswerInfoParam param) {
        PageParam<SurveyAnswerInfo, SurveyAnswerInfoParam> page = new PageParam<>(param);
        Page<SurveyStatisticsVO> voPage = surveyStatisticsMapper.selectSurveyWithStats(param.getSid(), page);
        return new PageResult<>(voPage.getRecords(), voPage.getTotal());
    }

    /**
     * 参与人员名单 （权限标识：survey:survey:statistics）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics')")
    @OperationLog(module = "问卷统计", comments = "参与人员名单")
    @GetMapping("/pageAnsweredUserInfo")
    public PageResult<UserInfo> pageAnsweredUserInfo(@Valid SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        return surveyStatisticsService.pageAnsweredUserInfo(param, userInfoParam);
    }

    /**
     * 导出参与人员名单 （权限标识：survey:survey:statistics:export）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics:export')")
    @OperationLog(module = "问卷统计", comments = "导出参与人员名单")
    @GetMapping(value = "/exportAnsweredUserInfo")
    public void exportAnsweredUserInfo(@Valid SurveyAnswerInfoParam param, UserInfoParam userInfoParam, HttpServletResponse response) {
        try {
            param.setPage(1L);
            param.setLimit(Integer.MAX_VALUE - 1L);
            param.setInternalCall(true);
            PageResult<UserInfo> pageResult = surveyStatisticsService.pageAnsweredUserInfo(param, userInfoParam);
            new EasyExcelHelper<UserInfo>() {
            }.out("参与人员名单", pageResult.getList(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 未参与人员名单 （权限标识：survey:survey:statistics）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics')")
    @OperationLog(module = "问卷统计", comments = "未参与人员名单")
    @GetMapping("/pageNoAnsweredUserInfo")
    public PageResult<UserInfo> pageNoAnsweredUserInfo(@Valid SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        return surveyStatisticsService.pageNoAnsweredUserInfo(param, userInfoParam);
    }

    /**
     * 导出未参与人员名单 （权限标识：survey:survey:statistics:export）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics:export')")
    @OperationLog(module = "问卷统计", comments = "导出未参与人员名单")
    @GetMapping(value = "/exportNoAnsweredUserInfo")
    public void exportNoAnsweredUserInfo(@Valid SurveyAnswerInfoParam param, UserInfoParam userInfoParam, HttpServletResponse response) {
        try {
            param.setPage(1L);
            param.setLimit(Integer.MAX_VALUE - 1L);
            param.setInternalCall(true);
            PageResult<UserInfo> pageResult = surveyStatisticsService.pageNoAnsweredUserInfo(param, userInfoParam);
            new EasyExcelHelper<UserInfo>() {
            }.out("未参与人员名单", pageResult.getList(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 问卷选项答题统计 （权限标识：survey:survey:statistics）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics')")
    @OperationLog(module = "问卷统计", comments = "导出参与人员名单")
    @GetMapping("/getAnswerStatistics")
    public SurveyVO getAnswerStatistics(@Valid SurveyAnswerInfoParam param) {
        return surveyStatisticsService.getAnswerStatistics(param);
    }

    /*
     * 下钻问卷选项答题统计人员名单 （权限标识：survey:survey:statistics）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics')")
    @OperationLog(module = "问卷统计", comments = "导出参与人员名单")
    @GetMapping("/pageAnswerStatisticsUserInfo")
    public PageResult<UserInfo> pageAnswerStatisticsUserInfo(@Valid SurveyAnswerParam param, UserInfoParam userInfoParam) {
        return surveyStatisticsService.pageAnswerStatisticsUserInfo(param, userInfoParam);
    }

    /**
     * 导出问卷选项答题统计人员名单 （权限标识：survey:survey:statistics:export）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics:export')")
    @OperationLog(module = "问卷统计", comments = "导出问卷选项答题统计人员名单")
    @GetMapping(value = "/exportAnswerStatisticsUserInfo")
    public void exportAnswerStatisticsUserInfo(@Valid SurveyAnswerParam param, UserInfoParam userInfoParam, HttpServletResponse response) {
        try {
            param.setPage(1L);
            param.setLimit(Integer.MAX_VALUE - 1L);
            param.setInternalCall(true);
            PageResult<UserInfo> pageResult = surveyStatisticsService.pageAnswerStatisticsUserInfo(param, userInfoParam);
            new EasyExcelHelper<UserInfo>() {
            }.out("问卷选项答题统计人员名单", pageResult.getList(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 导出问卷答题情况 （权限标识：survey:survey:statistics:export）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics:export')")
    @OperationLog(module = "问卷统计", comments = "导出问卷答题情况")
    @GetMapping(value = "/exportSurveyAnswers")
    public void exportSurveyAnswers(@Valid SurveyAnswerParam param, @RequestParam ExportType exportType, HttpServletResponse response) {
        try {
//            byte[] fileContent = surveyStatisticsService.exportSurveyAnswers(param, exportType);
            Map<String, Object> objectMap = surveyStatisticsService.exportSurveyAnswers(param, exportType);
            byte[] fileContent = (byte[]) objectMap.get("fileContent");

            String fileName = URLEncoder.encode("问卷答题情况.zip", "UTF-8");
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setContentLength(fileContent.length);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(fileContent);
            outputStream.flush();
        } catch (Exception e) {
            log.error("导出问卷答题情况失败", e);
            throw new BusinessException("导出问卷答题情况失败");
        }
    }

    /**
     * 异步导出问卷答题情况 （权限标识：survey:survey:statistics:export）
     */
    @PreAuthorize("hasAuthority('survey:survey:statistics:export')")
    @OperationLog(module = "问卷统计", comments = "异步导出问卷答题情况")
    @GetMapping(value = "/asyncExportSurveyAnswers")
    public Resp asyncExportSurveyAnswers(@Valid SurveyAnswerParam param, @RequestParam ExportType exportType, HttpServletResponse response) {
        ExportParams params = ExportParams.builder()
                .taskName("问卷答题情况导出")
                .businessType("survey")
                .exportType(exportType)
                .queryParams(param)
//                .expireTime(Date.from(Instant.now().plus(1, ChronoUnit.DAYS)))      //1天过期
                .build();
        ExportTask task = asyncExportService.createExportTask(params);
        return Resp.success(task);
    }
}
