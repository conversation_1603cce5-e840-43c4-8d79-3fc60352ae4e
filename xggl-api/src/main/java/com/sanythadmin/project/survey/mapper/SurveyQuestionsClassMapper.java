package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyQuestionsClass;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 问卷题库问题分类Mapper
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
public interface SurveyQuestionsClassMapper extends BaseMapper<SurveyQuestionsClass> {

    @Select("SELECT name as name FROM SYT_SURVEY_QUESTIONS_CALSS")
    List<String> getAllNames();
}
