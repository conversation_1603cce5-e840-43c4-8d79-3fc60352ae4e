package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.vo.SurveyAnswerStatistics;
import com.sanythadmin.project.survey.vo.SurveyStatisticsVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SurveyStatisticsMapper extends MyMPJBaseMapper<SurveyAnswerInfo> {

    @Select("SELECT s.ID as id, s.NAME as name, s.XGH as xgh, s.XM as xm, s.FJRS as fjrs, " +
            "COUNT(DISTINCT sa.XGH) AS actual_attend_count, " +
            "(MAX(s.FJRS) - COUNT(DISTINCT sa.XGH)) AS absent_count " +
            "FROM SYT_SURVEY s " +
            "LEFT JOIN SYT_SURVEY_ANSWER_INFO sa ON s.ID = sa.SID " +
            "WHERE s.ID = #{sid} " +
            "GROUP BY s.ID, s.NAME, s.XGH, s.XM, s.FJRS")
    @DataPermission(alias = "sa")
    Page<SurveyStatisticsVO> selectSurveyWithStats(@Param("sid") String sid, Page<SurveyAnswerInfo> page);

    @Select("SELECT q.id as question_id,q.NAME as question_name,qo.ID as option_id, qo.NAME as option_content, COUNT(DISTINCT sa.XGH) as answer_count, " +
            "sum(CASE WHEN q.QTYPE = '单选题' THEN CASE WHEN sa.RESULT = qo.CODE THEN 1 ELSE 0 END " +
            "WHEN q.QTYPE = '多选题' THEN CASE WHEN INSTR(sa.RESULT, qo.CODE) > 0 THEN 1 ELSE 0 END ELSE 0 END) option_answer_count," +
            " ROUND(sum(CASE WHEN q.QTYPE = '单选题' THEN CASE WHEN sa.RESULT = qo.CODE THEN 1 ELSE 0 END " +
            "WHEN q.QTYPE = '多选题' THEN CASE WHEN INSTR(sa.RESULT, qo.CODE) > 0 THEN 1 ELSE 0 END ELSE 0 END) * 100.0 / COUNT(DISTINCT sa.XGH), 2) as percentage " +
            "FROM SYT_SURVEY_PQUESTIONS q JOIN SYT_SURVEY_ANSWER sa ON q.ID = sa.CONTENT_ID LEFT JOIN SYT_SURVEY_PQUESTIONS_OPTIONS qo " +
            "ON qo.S_QID=sa.CONTENT_ID WHERE sa.SID = #{sid} and sa.RESULT is not null GROUP BY q.ID, q.NAME, q.QTYPE, q.SORT,qo.ID, qo.NAME " +
            "ORDER BY q.SORT, option_content")
    @DataPermission(alias = "sa")
    List<SurveyAnswerStatistics> getAnswerStatistics(@Param("sid") String sid);


}
