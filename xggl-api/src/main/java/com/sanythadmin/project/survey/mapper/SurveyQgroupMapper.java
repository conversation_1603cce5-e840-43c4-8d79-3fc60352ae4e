package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyQgroup;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 问卷问题组Mapper
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
public interface SurveyQgroupMapper extends BaseMapper<SurveyQgroup> {

    @Delete("<script>" +
            "delete from SYT_SURVEY_QGROUP where sid in "+
            "<foreach collection=\"sids\" item=\"sid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{sid}" +
            "</foreach>"+
            "</script>")
    void deleteBySid(@Param("sids") List<String> sids);

    @Update("UPDATE SYT_SURVEY_QGROUP " +
            "SET q_sum = (SELECT COUNT(*) FROM SYT_SURVEY_PQUESTIONS WHERE GID = #{id}) " +
            "WHERE id = #{id}")
    void updateSum(@Param("id") String id);

    @Update("UPDATE SYT_SURVEY_QGROUP " +
            "SET q_sum = (SELECT sum(Q_SUM) FROM SYT_SURVEY_PQUESTIONS_RULE WHERE GID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumByRule(@Param("id") String id);
}
