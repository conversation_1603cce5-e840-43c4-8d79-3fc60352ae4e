package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyClass;
import com.sanythadmin.project.survey.param.SurveyClassParam;
import com.sanythadmin.project.survey.service.SurveyClassService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷分类控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@RestController
@RequestMapping("/api/survey/survey-class")
public class SurveyClassController extends BaseController {
    @Resource
    private SurveyClassService surveyClassService;

    /**
     * 分页查询问卷分类（权限标识：survey:surveyClass:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyClass:list')")
    @GetMapping("/page")
    public PageResult<SurveyClass> page(SurveyClassParam param) {
        PageParam<SurveyClass, SurveyClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyClassService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷分类（权限标识：survey:surveyClass:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyClass:list')")
    @GetMapping()
    public List<SurveyClass> list(SurveyClassParam param) {
        PageParam<SurveyClass, SurveyClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyClassService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷分类（权限标识：survey:surveyClass:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyClass:list')")
    @GetMapping("/{id}")
    public SurveyClass get(@PathVariable("id") String id) {
        return surveyClassService.getById(id);
    }

    /**
     * 添加或修改问卷分类（权限标识：survey:surveyClass:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyClass:operation')")
    @OperationLog(module = "问卷分类", comments = "保存问卷分类")
    @PostMapping("/operation")
    public void save(@RequestBody SurveyClass surveyClass) {
        if (StringUtils.hasLength(surveyClass.getId())) {
            surveyClassService.updateById(surveyClass);
        } else {
            surveyClassService.save(surveyClass);
        }
    }

    /**
     * 批量删除问卷分类（权限标识：survey:surveyClass:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyClass:remove')")
    @OperationLog(module = "问卷分类", comments = "批量删除问卷分类")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyClassService.removeByIds(ids);
    }
}
