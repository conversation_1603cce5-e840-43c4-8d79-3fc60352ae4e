package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.vo.SurveyVO;

import java.util.List;

/**
 * 问卷调查/问卷答卷信息Service
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
public interface SurveyAnswerInfoService extends IService<SurveyAnswerInfo> {

    PageResult<SurveyAnswerInfo> getSurveyAnswerInfoPage(SurveyAnswerInfoParam param, UserInfoParam userInfoParam);

    void removeSurveyAnswerInfoByIds(List<String> ids);

    SurveyAnswerInfo getSurveyAnswerInfoById(String id);

    SurveyVO getSurveyAnswerInfo(SurveyAnswerInfo surveyAnswerInfo);
}
