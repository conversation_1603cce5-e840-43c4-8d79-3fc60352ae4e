package com.sanythadmin.project.survey.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷调查/问卷答卷信息Mapper
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
public interface SurveyAnswerInfoMapper extends MyMPJBaseMapper<SurveyAnswerInfo> {

    @Delete("<script>" +
            "DELETE FROM SYT_SURVEY_ANSWER_INFO WHERE SID IN " +
            "<foreach item='item' index='index' collection='surveyIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    void removeBySurveyIds(@Param("surveyIds") List<String> surveyIds);

}
