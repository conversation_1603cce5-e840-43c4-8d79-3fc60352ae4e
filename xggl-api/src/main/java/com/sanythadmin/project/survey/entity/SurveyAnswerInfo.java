package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.system.entity.UserInfo;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 问卷调查/问卷答卷信息
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_ANSWER_INFO")
@Entity
@Table(name = "SYT_SURVEY_ANSWER_INFO")
public class SurveyAnswerInfo extends BaseSurveyAnswerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

}
