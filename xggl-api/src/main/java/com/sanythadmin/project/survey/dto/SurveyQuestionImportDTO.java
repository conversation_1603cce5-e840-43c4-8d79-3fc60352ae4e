package com.sanythadmin.project.survey.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class SurveyQuestionImportDTO {

    @ExcelProperty("问题分类")
    private String cname;

    @ExcelProperty("问题名称")
    private String name;

    @ExcelProperty("问题类型")
    private String qtype;

    @ExcelProperty("是否必答")
    private String sfbt;

    @ExcelProperty("评分最小值")
    private Double minScore;

    @ExcelProperty("评分最大分值")
    private Double maxScore;

    @ExcelProperty("问题选项(选项#分值)")
    private String options;
}
