package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.survey.enums.QuestionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷答卷信息查询参数
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyAnswerParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 答卷信息id
     */
    @QueryField(type = QueryType.EQ)
    private String infoId;

    /**
     * 问卷试题id
     */
    @QueryField(type = QueryType.EQ)
    private String contentId;

    /**
     * 排序
     */
    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

    /**
     * 分组id
     */
    @QueryField(type = QueryType.EQ)
    private String gid;

    /**
     * 问题类型
     */
    @QueryField(type = QueryType.EQ)
    private QuestionType qtype;

    /**
     * 问卷id
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 答题得分
     */
    private Double point;

    /**
     * 答题结果
     */
    private String result;

    /**
     * 答题选项id
     */
    private String resultId;

    /**
     * 答题者账号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 答题时间
     */
    private String createDate;

    @QueryField(ignore = true)
    private Long usedTime;

}
