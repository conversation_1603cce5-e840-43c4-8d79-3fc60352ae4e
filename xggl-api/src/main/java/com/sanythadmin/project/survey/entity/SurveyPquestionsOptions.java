package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 问卷试题选项
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_PQUESTIONS_OPTIONS")
@Entity
@Table(name = "SYT_SURVEY_PQUESTIONS_OPTIONS")
public class SurveyPquestionsOptions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 选项编码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 选项名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 问题ID
     */
    @Column(name = "S_QID")
    @TableField("S_QID")
    private String sQid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 分数
     */
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 问卷id
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 题库试题id(传值使用)
     */
    @Transient
    @TableField(exist = false)
    private String qid;

    /**
     * 选项答题人数
     */
    @Transient
    @TableField(exist = false)
    private Integer optionAnswerCount;
    /**
     * 选项答题人数百分比
     */
    @Transient
    @TableField(exist = false)
    private String percentage;

}
