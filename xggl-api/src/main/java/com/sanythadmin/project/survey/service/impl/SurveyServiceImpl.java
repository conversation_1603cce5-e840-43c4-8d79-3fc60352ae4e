package com.sanythadmin.project.survey.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.util.QuestionSelector;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.param.SelectorDataParam;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import com.sanythadmin.project.survey.entity.*;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.enums.SurveyType;
import com.sanythadmin.project.survey.mapper.*;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.service.*;
import com.sanythadmin.project.survey.vo.SurveyPquestionsVO;
import com.sanythadmin.project.survey.vo.SurveyQgroupVO;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import net.sf.jsqlparser.expression.Expression;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.sanythadmin.common.core.utils.SecurityUtil.getUserInfo;

/**
 * 问卷管理Service实现
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@Service
public class SurveyServiceImpl extends ServiceImpl<SurveyMapper, Survey> implements SurveyService {

    @Resource
    private SurveyMapper surveyMapper;
    @Resource
    private SurveyQgroupMapper surveyQgroupMapper;
    @Resource
    private SurveyPquestionsMapper surveyPquestionsMapper;
    @Resource
    private SurveyPquestionsOptionsMapper surveyPquestionsOptionsMapper;
    @Resource
    private SurveyAnswerInfoMapper surveyAnswerInfoMapper;
    @Resource
    private SurveyAnswerMapper surveyAnswerMapper;
    @Resource
    private SelectorDataService selectorDataService;
    @Lazy
    @Resource
    private SurveyQgroupService surveyQgroupService;
    @Resource
    private SelectorAccountService selectorAccountService;
    @Lazy
    @Resource
    private SurveyAnswerService surveyAnswerService;
    @Lazy
    @Resource
    private SurveyPquestionsService surveyPquestionsService;
    @Resource
    private SurveyPquestionsOptionsService surveyPquestionsOptionsService;
    @Resource
    private SurveyPquestionsRuleService surveyPquestionsRuleService;
    @Resource
    private SurveyQuestionsService surveyQuestionsService;
    @Resource
    private SurveyQuestionsOptionsService surveyQuestionsOptionsService;

    @Override
    public Survey saveSurvey(SurveyParam param) throws Exception {
        Survey survey = new Survey();
        BeanUtil.copyProperties(param, survey);
        survey.setStatus(SurveyState.ENABLED);
        if (StringUtils.hasLength(survey.getId())) {
            //判断是否有答卷记录
            Long l = surveyAnswerInfoMapper.selectCount(new LambdaQueryWrapper<SurveyAnswerInfo>().eq(SurveyAnswerInfo::getSid, survey.getId()));
            if (l > 0) {
                AssertUtil.throwMessage("存在已答卷记录不可编辑");
            }
            updateById(survey);
        } else {
            survey.setCreateTime(LocalDateTime.now());
            survey.setXgh(SecurityUtil.getUsername());
            survey.setXm(SecurityUtil.getRealName());
            save(survey);
        }
        selectorDataService.saveSelectotData(survey.getId(), param.getSfbl(), param.getSelectorDatas());
        List<UserInfo> userInfos = selectorDataService.queryUserListBySelDataWithPermission(param.getSelectorDatas());
        survey.setFjrs(userInfos.size());
        updateById(survey);
        return survey;
    }

    @Override
    public PageResult<Survey> querySurveys(SurveyParam param) {
        UserInfo userInfo = getUserInfo();
        param.setUserType(userInfo.getUserType());
        MyMPJLambdaWrapper<Survey, SurveyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.leftJoin(SelectorAccount.class, "acc", SelectorAccount::getItemId, Exam::getId);
        List<Expression> expressionList = selectorAccountService.expressionList("acc", getUserInfo().getUserType());
        wrapper.eq(Survey::getStatus, State.ENABLED.getValue());
        if (CollectionUtils.isNotEmpty(expressionList)) {
            wrapper.nested(w -> {
                w.and(a -> {
                    for (Expression expression : expressionList) {
                        a.apply(expression.toString());
                    }
                });
                w.or(a -> a.isNull("acc.ITEM_ID"));
            });
        }
        wrapper.distinct();
        wrapper.orderByDesc(Survey::getCreateTime);
        Page<Survey> page = new Page<>(param.getPage(), param.getLimit());
        Page<Survey> iPage = surveyMapper.selectJoinPage(page, Survey.class, wrapper);
        List<Survey> records = iPage.getRecords();

        records.forEach(survey -> {
            SurveyAnswer answer = new SurveyAnswer(survey.getId(), userInfo.getXgh());
            survey.setAnswered(surveyAnswerService.isAnswered(answer));
        });
        return new PageResult<>(records, iPage.getTotal());
    }

    @Override
    public SurveyVO getSurveyVO(String id) {
        return getSurveyVO(id, null);
    }

    @Override
    public SurveyVO getSurveyVO(String id, SurveyAnswerInfo surveyAnswerInfo) {
        return getSurveyVO(id, surveyAnswerInfo, SurveyAnswer.class);
    }

    @Override
    public <T extends BaseSurveyAnswerInfo, U extends BaseSurveyAnswer> SurveyVO getSurveyVO(String id, T surveyAnswerInfo, Class<U> uClass) {
        if (!StringUtils.hasLength(id)) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
//        if (!StringUtils.hasLength(surveyAnswerInfo.getId())) {
//            AssertUtil.throwMessage("答卷信息id不能为空");
//        }
        Survey survey = getById(id);
        if (survey == null) {
            AssertUtil.throwMessage("未找到问卷");
        }
        SurveyType wjms = survey.getWjms();

        SurveyVO surveyVO = new SurveyVO();
        BeanUtils.copyProperties(survey, surveyVO);
        List<SurveyQgroup> surveyQgroupList = surveyQgroupService.list(new LambdaQueryWrapper<SurveyQgroup>().eq(SurveyQgroup::getSid, id)
                .orderByAsc(SurveyQgroup::getSort));
        List<SurveyQgroupVO> surveyQgroupVOS = BeanUtil.copyToList(surveyQgroupList, SurveyQgroupVO.class);
        LinkedList<SurveyPquestionsVO> surveyPquestionsVOLinkedList = new LinkedList<>();
//        if (CollectionUtils.isNotEmpty(surveyQgroupVOS)) {
        if (surveyAnswerInfo != null) {
            buildFBXT(surveyQgroupVOS, surveyAnswerInfo, surveyPquestionsVOLinkedList, wjms, uClass);
        } else {
            if (SurveyType.GDXT.getText().equals(survey.getWjms().getText())) {
                buildGDXT(id, surveyQgroupVOS, surveyPquestionsVOLinkedList);
            } else if (SurveyType.SJCT.getText().equals(survey.getWjms().getText())) {
                buildSJCT(surveyQgroupVOS, surveyPquestionsVOLinkedList);
            }
        }
//        }
        surveyVO.setQuestionsGroupList(new LinkedList<>(surveyQgroupVOS));
        IntStream.range(0, surveyPquestionsVOLinkedList.size()).forEach(i -> surveyPquestionsVOLinkedList.get(i).setSort(i + 1));
        surveyVO.setSurveyPaperQuestionsVOList(surveyPquestionsVOLinkedList);
        //人员选择器
        SelectorDataParam selectorDataParam = new SelectorDataParam();
        selectorDataParam.setItemId(survey.getId());
        PageParam<SelectorData, SelectorDataParam> dataParamPageParam = new PageParam<>(selectorDataParam);
        List<SelectorData> selectorDatas = selectorDataService.list(dataParamPageParam.getWrapper());
        surveyVO.setSelectorDatas(selectorDatas);
        return surveyVO;
    }

    private <T extends BaseSurveyAnswerInfo, U extends BaseSurveyAnswer> void buildFBXT(List<SurveyQgroupVO> surveyQgroupVOS, T surveyAnswerInfo, LinkedList<SurveyPquestionsVO> surveyPquestionsVOLinkedList, SurveyType wjms, Class<U> uClass) {
        SqlSession sqlSession = null;
        try {
            SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
            sqlSession = sqlSessionFactory.openSession();
            BaseMapper<U> answerMapper = SqlHelper.getMapper(uClass, sqlSession);
            QueryWrapper<U> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("INFO_ID", surveyAnswerInfo.getId());
            List<U> surveyAnswerList = answerMapper.selectList(queryWrapper);
            Map<String, U> surveyAnswerByPid = surveyAnswerList.stream().collect(Collectors.toMap(U::getContentId, answer -> answer));
            Map<String, List<U>> surveyAnswerByGid = surveyAnswerList.stream().collect(Collectors.groupingBy(U::getGid, HashMap::new, Collectors.toList()));
            List<String> surveyAnswerContentIds = surveyAnswerList.stream().map(surveyAnswer -> surveyAnswer.getContentId()).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(surveyAnswerContentIds)) {
                List<SurveyPquestions> surveyPquestionsList = new LinkedList<>();
                if (SurveyType.GDXT.getText().equals(wjms.getText())) {
                    surveyPquestionsList = surveyPquestionsService.list(new LambdaQueryWrapper<SurveyPquestions>().in(SurveyPquestions::getId, surveyAnswerContentIds).orderByAsc(SurveyPquestions::getSort));
                } else if (SurveyType.SJCT.getText().equals(wjms.getText())) {
                    List<SurveyQuestions> surveyQuestionsList = surveyQuestionsService.list(new LambdaQueryWrapper<SurveyQuestions>().in(SurveyQuestions::getId, surveyAnswerContentIds));
                    //关联答题表查询题库试题，并之前的答题顺序排序
                    surveyPquestionsList = BeanUtil.copyToList(surveyQuestionsList, SurveyPquestions.class);
                }


                List<SurveyPquestionsVO> surveyPquestionsVOList = BeanUtil.copyToList(surveyPquestionsList, SurveyPquestionsVO.class);

                //问题选项
                List<SurveyPquestionsOptions> surveyPquestionsOptionsList = new LinkedList<>();
                Map<String, List<SurveyPquestionsOptions>> questionsOptionsByQId = new HashMap<>();
                if (SurveyType.GDXT.getText().equals(wjms.getText())) {
                    surveyPquestionsOptionsList = surveyPquestionsOptionsService.list(new LambdaQueryWrapper<SurveyPquestionsOptions>()
                            .in(SurveyPquestionsOptions::getSQid, surveyAnswerContentIds).orderByAsc(SurveyPquestionsOptions::getSort));
                } else if (SurveyType.SJCT.getText().equals(wjms.getText())) {
                    List<SurveyQuestionsOptions> surveyQuestionsOptions = surveyQuestionsOptionsService.list(new LambdaQueryWrapper<SurveyQuestionsOptions>()
                            .in(SurveyQuestionsOptions::getQid, surveyAnswerContentIds).orderByAsc(SurveyQuestionsOptions::getSort));
                    surveyPquestionsOptionsList = new ArrayList<>();
                    for (SurveyQuestionsOptions questionsOptions : surveyQuestionsOptions) {
                        SurveyPquestionsOptions paperOption = new SurveyPquestionsOptions();
                        paperOption.setSQid(questionsOptions.getQid());  // 手动处理字段不一致情况
                        BeanUtil.copyProperties(questionsOptions, paperOption);
                        surveyPquestionsOptionsList.add(paperOption);
                    }
                }

                questionsOptionsByQId = surveyPquestionsOptionsList.stream()
                        .collect(Collectors.groupingBy(SurveyPquestionsOptions::getSQid,
                                LinkedHashMap::new,
                                Collectors.toList()));
                for (SurveyPquestionsVO surveyPquestionsVO : surveyPquestionsVOList) {
                    List<SurveyPquestionsOptions> surveyPquestionsOptions = questionsOptionsByQId.getOrDefault(surveyPquestionsVO.getId(), new ArrayList<>());
                    surveyPquestionsVO.setSurveyPquestionsOptionsList(new LinkedList<>(surveyPquestionsOptions));
                }

                Map<String, List<SurveyPquestionsVO>> questionsByGroupId;
                if (SurveyType.GDXT.getText().equals(wjms.getText())) {
                    questionsByGroupId = surveyPquestionsVOList.stream()
                            .collect(Collectors.groupingBy(SurveyPquestionsVO::getGid,
                                    LinkedHashMap::new,
                                    Collectors.toList()));
                } else {
                    questionsByGroupId = new HashMap<>();
                    if (SurveyType.SJCT.getText().equals(wjms.getText())) {
                        surveyAnswerByGid.forEach((key, value) -> {
                            List<SurveyPquestionsVO> subSurveyPaperQuestionsVO = value.stream()
                                    .flatMap(p1 -> surveyPquestionsVOList.stream()
                                            .filter(p2 -> Objects.equals(p1.getContentId(), p2.getId()))
                                            .peek(p2 -> {
                                                p2.setSid(p1.getSid());
                                                p2.setQtype(p1.getQtype());
                                                p2.setGid(p1.getGid());
                                                p2.setSort(p1.getSort());
                                            })
                                            .map(p2 -> p2)
                                    )
                                    .toList();
                            //创建副本排序，按Answer的sort排序
                            List<SurveyPquestionsVO> modifiableSubSurveyPaperQuestionsVO = new ArrayList<>(subSurveyPaperQuestionsVO);
                            modifiableSubSurveyPaperQuestionsVO.sort(Comparator.comparing(SurveyPquestionsVO::getSort));
                            questionsByGroupId.put(key, modifiableSubSurveyPaperQuestionsVO);
                        });
                    }
                }

                if (CollectionUtils.isEmpty(surveyQgroupVOS)) {
                    for (Map.Entry<String, List<SurveyPquestionsVO>> entry : questionsByGroupId.entrySet()) {
                        List<SurveyPquestionsVO> surveyPquestions = entry.getValue();
                        //回显考试结果
                        surveyPquestions.forEach(surveyPaperQuestion -> {
                            setResultForSurveyPquestions(surveyPaperQuestion, surveyAnswerByPid);
                        });
                        surveyPquestionsVOLinkedList.addAll(new LinkedList<>(surveyPquestions));
                    }

                } else {
                    for (SurveyQgroupVO groupVO : surveyQgroupVOS) {
                        List<SurveyPquestionsVO> surveyPquestions = new LinkedList<>();
                        if (SurveyType.GDXT.getText().equals(wjms.getText())) {
                            surveyPquestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());
                        } else if (SurveyType.SJCT.getText().equals(wjms.getText())) {
                            surveyPquestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());
                        }
                        //回显考试结果
                        surveyPquestions.forEach(surveyPaperQuestion -> {
                            setResultForSurveyPquestions(surveyPaperQuestion, surveyAnswerByPid);
                        });
                        groupVO.setSurveyPquestionsVOList(new LinkedList<>(surveyPquestions));
                        surveyPquestionsVOLinkedList.addAll(new LinkedList<>(surveyPquestions));
                    }
                }
            }
        } finally {
            if (!Objects.isNull(sqlSession))
                sqlSession.close();
        }
    }

    private <U extends BaseSurveyAnswer> void setResultForSurveyPquestions(SurveyPquestionsVO surveyPaperQuestion, Map<String, U> surveyAnswerByPid) {
        if (MapUtil.isNotEmpty(surveyAnswerByPid)) {
            U answer = surveyAnswerByPid.get(surveyPaperQuestion.getId());
            surveyPaperQuestion.setResult(answer.getResult());
        }
    }

    private void buildGDXT(String id, List<SurveyQgroupVO> surveyQgroupVOS, LinkedList<SurveyPquestionsVO> surveyPquestionsVOLinkedList) {
        List<SurveyPquestions> surveyPquestionsList = surveyPquestionsService.list(new LambdaQueryWrapper<SurveyPquestions>().eq(SurveyPquestions::getSid, id)
                .orderByAsc(SurveyPquestions::getSort));
        if (CollectionUtils.isNotEmpty(surveyPquestionsList)) {
            List<SurveyPquestionsVO> surveyPquestionsVOList = BeanUtil.copyToList(surveyPquestionsList, SurveyPquestionsVO.class);

            List<SurveyPquestionsOptions> surveyPquestionsOptionsList = surveyPquestionsOptionsService.list(new LambdaQueryWrapper<SurveyPquestionsOptions>()
                    .eq(SurveyPquestionsOptions::getSid, id).orderByAsc(SurveyPquestionsOptions::getSort));
            if (CollectionUtils.isNotEmpty(surveyPquestionsOptionsList)) {
                Map<String, List<SurveyPquestionsOptions>> questionsOptionsByQId = surveyPquestionsOptionsList.stream()
                        .collect(Collectors.groupingBy(SurveyPquestionsOptions::getSQid,
                                LinkedHashMap::new,
                                Collectors.toList()));
                for (SurveyPquestionsVO surveyPquestionsVO : surveyPquestionsVOList) {
                    List<SurveyPquestionsOptions> surveyPquestionsOptions = questionsOptionsByQId.getOrDefault(surveyPquestionsVO.getId(), new ArrayList<>());
                    surveyPquestionsVO.setSurveyPquestionsOptionsList(new LinkedList<>(surveyPquestionsOptions));
                }
                Map<String, List<SurveyPquestionsVO>> questionsByGroupId = surveyPquestionsVOList.stream()
                        .collect(Collectors.groupingBy(SurveyPquestionsVO::getGid,
                                LinkedHashMap::new,
                                Collectors.toList()));
                if (CollectionUtils.isEmpty(surveyQgroupVOS)) {
                    for (Map.Entry<String, List<SurveyPquestionsVO>> entry : questionsByGroupId.entrySet()) {
                        surveyPquestionsVOLinkedList.addAll(entry.getValue());
                    }
                } else {
                    for (SurveyQgroupVO groupVO : surveyQgroupVOS) {
                        List<SurveyPquestionsVO> surveyPquestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());

                        groupVO.setSurveyPquestionsVOList(new LinkedList<>(surveyPquestions));
                        surveyPquestionsVOLinkedList.addAll(new LinkedList<>(surveyPquestions));
                    }
                }
            }

        }

    }

    private void buildSJCT(List<SurveyQgroupVO> surveyQgroupVOS, LinkedList<SurveyPquestionsVO> surveyPquestionsVOLinkedList) {
        Set<String> selectedQuestionIds = new HashSet<>();
        surveyQgroupVOS.forEach(questionsGroupVO -> {
            List<SurveyPquestionsRule> questionsRulesList = surveyPquestionsRuleService.list(new LambdaQueryWrapper<SurveyPquestionsRule>()
                    .eq(SurveyPquestionsRule::getGid, questionsGroupVO.getId()));
            if (CollectionUtils.isNotEmpty(questionsRulesList)) {
                List<SurveyQuestions> allQuestions = surveyQuestionsService.list();
                QuestionSelector<SurveyQuestions> questionSelector = new QuestionSelector<>(
                        allQuestions,
                        (question, rule) -> {
                            SurveyPquestionsRule surveyPquestionsRule = (SurveyPquestionsRule) rule;
                            return question.getQtype().getText().equals(surveyPquestionsRule.getQtype().getText())
                                    && question.getCid().equals(surveyPquestionsRule.getCid());
                        },
                        (question, rule) -> {
                            SurveyPquestionsRule surveyPquestionsRule = (SurveyPquestionsRule) rule;
//                            question.setScore(surveyPquestionsRule.getQscore());
                            question.setGname(surveyPquestionsRule.getGname());
                            question.setQid(question.getId());
                        }
                );
                List<SurveyQuestions> surveyQuestionsList = questionSelector.selectQuestionsForPaper(questionsRulesList, selectedQuestionIds, SurveyQuestions::getId);
                List<String> surveyQuestionsIds = surveyQuestionsList.stream().map(questions -> questions.getId()).collect(Collectors.toList());

                List<SurveyPquestions> surveyPquestionsList = BeanUtil.copyToList(surveyQuestionsList, SurveyPquestions.class);
                List<SurveyPquestionsVO> surveyPquestionsVOList = BeanUtil.copyToList(surveyPquestionsList, SurveyPquestionsVO.class);

                surveyPquestionsVOList.forEach(questions -> {
                    questions.setGid(questionsGroupVO.getId());
                });

                if (CollectionUtils.isNotEmpty(surveyQuestionsIds)) {
                    //问题选项
                    List<SurveyQuestionsOptions> surveyQuestionsOptionsList = surveyQuestionsOptionsService.list(new LambdaQueryWrapper<SurveyQuestionsOptions>()
                            .in(SurveyQuestionsOptions::getQid, surveyQuestionsIds).orderByAsc(SurveyQuestionsOptions::getSort));
                    Map<String, List<SurveyQuestionsOptions>> questionsOptionsByQId = surveyQuestionsOptionsList.stream()
                            .collect(Collectors.groupingBy(SurveyQuestionsOptions::getQid,
                                    LinkedHashMap::new,
                                    Collectors.toList()));
                    for (SurveyPquestionsVO surveyPquestionsVO : surveyPquestionsVOList) {
                        List<SurveyQuestionsOptions> surveyQuestionsOptions = questionsOptionsByQId.getOrDefault(surveyPquestionsVO.getId(), new ArrayList<>());
                        List<SurveyPquestionsOptions> surveyPquestionsOptionsList = BeanUtil.copyToList(surveyQuestionsOptions, SurveyPquestionsOptions.class);
                        surveyPquestionsVO.setSurveyPquestionsOptionsList(new LinkedList<>(surveyPquestionsOptionsList));
                    }
                }
                questionsGroupVO.setSurveyPquestionsVOList(new LinkedList<>(surveyPquestionsVOList));
                surveyPquestionsVOLinkedList.addAll(new LinkedList<>(surveyPquestionsVOList));
            }
        });
    }

    @Transactional
    @Override
    public void removePaperGroupQuestions(List<String> ids) {
        //TODO 问卷删除是否判断？
        removeSurvey(ids);
    }

    @Override
    public void removeSurvey(List<String> ids) {
        surveyQgroupMapper.deleteBySid(ids);
        surveyPquestionsMapper.deleteBySid(ids);
        surveyPquestionsOptionsMapper.deleteBySid(ids);
        surveyAnswerInfoMapper.removeBySurveyIds(ids);
        surveyAnswerMapper.removeBySurveyIds(ids);
        selectorDataService.removeSelectotData(ids);
        ids.forEach(id -> baseMapper.deleteById(id));
    }

}
