package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 问卷分类
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_CLASS")
@Entity
@Table(name = "SYT_SURVEY_CLASS")
public class SurveyClass implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 分类名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

}
