package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@MappedSuperclass
public class BaseSurveyAnswerInfo {
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问卷id
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 答卷用时
     */
    @Column(name = "USED_TIME")
    @TableField("USED_TIME")
    private Long usedTime;

    /**
     * 答卷得分
     */
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 答卷时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 答卷人账号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;


    /**
     * 答卷人IP
     */
    @Column(name = "IP", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("IP")
    private String ip;
}
