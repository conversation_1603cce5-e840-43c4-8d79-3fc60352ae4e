package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.survey.enums.QuestionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷试题抽题规则查询参数
 *
 * <AUTHOR>
 * @since 2024-12-12 16:33:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyPquestionsRuleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 分组ID
     */
    @QueryField(type = QueryType.EQ)
    private String gid;

    /**
     * 问题类型
     */
    @QueryField(type = QueryType.EQ)
    private QuestionType qtype;

    /**
     * 问题分类
     */
    @QueryField(type = QueryType.EQ)
    private String cid;

    /**
     * 抽题数
     */
    @QueryField(ignore = true)
    private Integer qSum;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

}
