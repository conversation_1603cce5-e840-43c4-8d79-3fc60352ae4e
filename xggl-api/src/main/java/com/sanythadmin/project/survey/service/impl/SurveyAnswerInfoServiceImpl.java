package com.sanythadmin.project.survey.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.selector.service.SelectorDataService;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.enums.SurveyMethod;
import com.sanythadmin.project.survey.mapper.SurveyAnswerInfoMapper;
import com.sanythadmin.project.survey.mapper.SurveyAnswerMapper;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.service.SurveyAnswerInfoService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;

/**
 * 问卷调查/问卷答卷信息Service实现
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Service
public class SurveyAnswerInfoServiceImpl extends ServiceImpl<SurveyAnswerInfoMapper, SurveyAnswerInfo> implements SurveyAnswerInfoService {

    @Resource
    private SurveyAnswerInfoMapper surveyAnswerInfoMapper;
    @Resource
    private SurveyAnswerMapper surveyAnswerMapper;
    @Resource
    private SelectorDataService selectorDataService;
    @Resource
    private SurveyService surveyService;

    @Override
    public PageResult<SurveyAnswerInfo> getSurveyAnswerInfoPage(SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        Survey survey = surveyService.getById(param.getSid());
        if (survey == null) {
            AssertUtil.throwMessage("未找到问卷");
        }
        List<UserType> userTypes = selectorDataService.getDistinctUserTypesByItemId(param.getSid());
        MyMPJLambdaWrapper<SurveyAnswerInfo, SurveyAnswerInfoParam> wrapper =
                UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param, SurveyAnswerInfo::getXgh, userInfoParam, userTypes.toArray(new UserType[0]));
        Class<UserInfo> userInfoClass = UserInfo.class;
        Class<SurveyAnswerInfo> surveyAnswerInfoClass = SurveyAnswerInfo.class;
        SFunction<UserInfo, Object>[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(UserInfo.class), false);
        SFunction<SurveyAnswerInfo, Object>[] sFunctions1 = CommonUtil.buildSFunctions(surveyAnswerInfoClass, CommonUtil.getAllFields(SurveyAnswerInfo.class), true);
        wrapper.select(sFunctions).select(sFunctions1);
        Map<Class<?>, String> tableAliasMap = wrapper.getTableAliasMap();
        wrapper.selectAssociation(tableAliasMap.get(userInfoClass), userInfoClass, SurveyAnswerInfo::getUserInfo);

        wrapper.orderByDesc("CREATE_DATE");
        Page<SurveyAnswerInfo> page = wrapper.getPage();
        IPage<SurveyAnswerInfo> iPage = surveyAnswerInfoMapper.selectJoinPageWithPermission(page, SurveyAnswerInfo.class, wrapper, new MyMPQueryParams(SurveyAnswerInfo.class));
        UserInfoUtil.codeTextSet(iPage.getRecords());
        if (SurveyMethod.NM.getText().equals(survey.getDcfs().getText())) {
            iPage.getRecords().forEach(surveyAnswerInfo -> {
                surveyAnswerInfo.setXgh("****");
                UserInfo userInfo = new UserInfo();
                userInfo.setXm("****");
                surveyAnswerInfo.setUserInfo(userInfo);
            });
        }
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Override
    public void removeSurveyAnswerInfoByIds(List<String> ids) {
        surveyAnswerInfoMapper.removeBySurveyIds(ids);
        surveyAnswerMapper.removeBySurveyIds(ids);
    }

    @Override
    public SurveyAnswerInfo getSurveyAnswerInfoById(String id) {
        SurveyAnswerInfo surveyAnswerInfo = getById(id);
        Survey survey = surveyService.getById(surveyAnswerInfo.getSid());
        if (SurveyMethod.NM.getText().equals(survey.getDcfs().getText())) {
            surveyAnswerInfo.setXgh("****");
        }
        return surveyAnswerInfo;
    }

    @Override
    public SurveyVO getSurveyAnswerInfo(SurveyAnswerInfo surveyAnswerInfo) {
        if (!StringUtils.hasLength(surveyAnswerInfo.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        SysAccount account = getAccount();
        SurveyAnswerInfo answerInfo = this.getOne(new LambdaQueryWrapper<SurveyAnswerInfo>().eq(SurveyAnswerInfo::getSid, surveyAnswerInfo.getSid())
                .eq(SurveyAnswerInfo::getXgh, account.getUsername()));
        return surveyService.getSurveyVO(surveyAnswerInfo.getSid(),answerInfo);
    }
}
