<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.survey.mapper.SurveyPquestionsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, QTYPE, CID, CREATE_TIME, CREATE_USER, TYPE, SFBT, MIN_SCORE, MAX_SCORE, QID, SORT, SID, GID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.qtype != null">
                        AND a.QTYPE LIKE concat(concat('%',#{param.qtype), '%')
                    </if>
                    <if test="param.cid != null">
                        AND a.CID LIKE concat(concat('%',#{param.cid), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.createUser != null">
                        AND a.CREATE_USER LIKE concat(concat('%',#{param.createUser), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.sfbt != null">
                        AND a.SFBT LIKE concat(concat('%',#{param.sfbt), '%')
                    </if>
                    <if test="param.minScore != null">
                        AND a.MIN_SCORE LIKE concat(concat('%',#{param.minScore), '%')
                    </if>
                    <if test="param.maxScore != null">
                        AND a.MAX_SCORE LIKE concat(concat('%',#{param.maxScore), '%')
                    </if>
                    <if test="param.qid != null">
                        AND a.QID LIKE concat(concat('%',#{param.qid), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
    </sql>
</mapper>
