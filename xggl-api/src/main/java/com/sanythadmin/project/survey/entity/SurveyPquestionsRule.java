package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.enums.QuestionType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
/**
 * 问卷试题抽题规则
 *
 * <AUTHOR>
 * @since 2024-12-12 16:33:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_PQUESTIONS_RULE")
@Entity
@Table(name = "SYT_SURVEY_PQUESTIONS_RULE")
public class SurveyPquestionsRule implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问卷ID
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 分组名称
     */
    @TableField(exist = false)
    private String gname;

    /**
     * 问题类型
     */
    @Column(name = "QTYPE",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "QTYPE",jdbcType = JdbcType.VARCHAR)
    private QuestionType qtype;

    /**
     * 问题分类
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 抽题数
     */
    @Column(name = "Q_SUM")
    @TableField("Q_SUM")
    private Integer qSum;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
