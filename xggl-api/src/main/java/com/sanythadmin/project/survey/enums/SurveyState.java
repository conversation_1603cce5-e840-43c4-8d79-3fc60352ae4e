package com.sanythadmin.project.survey.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum SurveyState implements ValueTextBaseEnum {
    ENABLED(1, "启用"),
    END(0, "结束");
    @EnumValue
    private final Integer value;
    @JsonValue
    private final String text;

    SurveyState(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    public static SurveyState get(String text) {
        for (SurveyState state : SurveyState.values()) {
            boolean equals = state.getText().equals(text);
            if (equals) {
                return state;
            }
        }
        return null;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
