package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷题库问题分类查询参数
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyQuestionsClassParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 分类名称
     */
    private String name;


    /**
     * 备注
     */
    private String bz;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
