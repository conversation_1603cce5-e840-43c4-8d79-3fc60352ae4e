package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.survey.entity.SurveyAnswer;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 问卷调查/问卷答卷信息查询参数
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyAnswerInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 问卷id
     */
    @NotBlank(message = "问卷id不能为空")
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 答卷用时
     */
    private Long usedTime;

    /**
     * 答卷得分
     */
    private Double score;

    /**
     * 答卷时间
     */
    private String createDate;

    /**
     * 答卷人账号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    @QueryField(ignore = true)
    private List<SurveyAnswer> answers;

}
