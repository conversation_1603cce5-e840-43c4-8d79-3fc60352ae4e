package com.sanythadmin.project.survey.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.project.survey.entity.SurveyPquestionsRule;
import com.sanythadmin.project.survey.mapper.SurveyMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsRuleMapper;
import com.sanythadmin.project.survey.mapper.SurveyQgroupMapper;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.service.SurveyPquestionsRuleService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 问卷试题抽题规则Service实现
 *
 * <AUTHOR>
 * @since 2024-12-12 16:33:48
 */
@Service
public class SurveyPquestionsRuleServiceImpl extends ServiceImpl<SurveyPquestionsRuleMapper, SurveyPquestionsRule> implements SurveyPquestionsRuleService {

    @Resource
    private SurveyPquestionsRuleMapper mapper;
    @Resource
    private SurveyMapper surveyMapper;
    @Resource
    private SurveyQgroupMapper surveyQgroupMapper;
    @Lazy
    @Resource
    private SurveyService surveyService;

    @Transactional
    @Override
    public SurveyVO saveSurveyPquestionsRule(SurveyPquestionsRuleParam param) {
        SurveyPquestionsRule surveyPquestionsRule = new SurveyPquestionsRule();
        BeanUtil.copyProperties(param, surveyPquestionsRule);
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        surveyPquestionsRule.setGid(param.getGid());

        if (surveyPquestionsRule.getSort() == null) {
            SortHelper<SurveyPquestionsRule> sortHelper = new SortHelper<>(this, "sort");
            surveyPquestionsRule.setSort(sortHelper.next());
        }
        saveOrUpdate(surveyPquestionsRule);
        surveyMapper.updateSumByRule(param.getSid());
        surveyQgroupMapper.updateSumByRule(param.getGid());
        return surveyService.getSurveyVO(param.getSid());
    }
    @Transactional
    @Override
    public SurveyVO saveBatchExamPaperQuestionsRule(List<SurveyPquestionsRuleParam> param) {
        if (param.size() > 0) {
            SurveyPquestionsRuleParam ruleParam = param.get(0);
            if (StringUtils.isEmpty(ruleParam.getSid())) {
                AssertUtil.throwMessage("问卷id不能为空");
            }
            if (StringUtils.isEmpty(ruleParam.getGid())) {
                AssertUtil.throwMessage("问卷问题组id不能为空");
            }
            List<SurveyPquestionsRule> surveyPquestionsRules = BeanUtil.copyToList(param, SurveyPquestionsRule.class);
            surveyPquestionsRules.forEach(surveyPquestionsRule -> {
                if (surveyPquestionsRule.getSort() == null) {
                    SortHelper<SurveyPquestionsRule> sortHelper = new SortHelper<>(this, "sort");
                    surveyPquestionsRule.setSort(sortHelper.next());
                }
            });
            saveOrUpdateBatch(surveyPquestionsRules);
            surveyMapper.updateSumByRule(ruleParam.getSid());
            surveyQgroupMapper.updateSumByRule(ruleParam.getGid());
            return surveyService.getSurveyVO(ruleParam.getSid());
        }
        return new SurveyVO();
    }
    @Transactional
    @Override
    public SurveyVO removeSurveyPquestionsRule(SurveyPquestionsRuleParam param) {
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        String ids = param.getId();
        removeByIds(Arrays.asList(ids.split(",")));
        surveyMapper.updateSumByRule(param.getSid());
        surveyQgroupMapper.updateSumByRule(param.getGid());
        return surveyService.getSurveyVO(param.getSid());
    }
}
