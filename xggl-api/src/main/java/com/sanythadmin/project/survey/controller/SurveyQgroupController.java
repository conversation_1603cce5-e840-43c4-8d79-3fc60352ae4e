package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyQgroup;
import com.sanythadmin.project.survey.param.SurveyQgroupParam;
import com.sanythadmin.project.survey.service.SurveyQgroupService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷问题组控制器
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@RestController
@RequestMapping("/api/survey/survey-qgroup")
public class SurveyQgroupController extends BaseController {
    @Resource
    private SurveyQgroupService surveyQgroupService;

    /**
     * 分页查询问卷问题组（权限标识：survey:surveyQgroup:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQgroup:list')")
    @GetMapping("/page")
    public PageResult<SurveyQgroup> page(SurveyQgroupParam param) {
        PageParam<SurveyQgroup, SurveyQgroupParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyQgroupService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷问题组（权限标识：survey:surveyQgroup:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQgroup:list')")
    @GetMapping()
    public List<SurveyQgroup> list(SurveyQgroupParam param) {
        PageParam<SurveyQgroup, SurveyQgroupParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyQgroupService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷问题组（权限标识：survey:surveyQgroup:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQgroup:list')")
    @GetMapping("/{id}")
    public SurveyQgroup get(@PathVariable("id") String id) {
        return surveyQgroupService.getById(id);
    }

    /**
     * 添加或修改问卷问题组（权限标识：survey:surveyQgroup:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyQgroup:operation')")
    @OperationLog(module = "问卷问题组", comments = "保存问卷问题组")
    @PostMapping("/operation")
    public SurveyQgroup save(@RequestBody SurveyQgroup surveyQgroup) {
        if (StringUtils.hasLength(surveyQgroup.getId())) {
            surveyQgroupService.updateById(surveyQgroup);
        } else {
            if (surveyQgroup.getSort() == null) {
                SortHelper<SurveyQgroup> sortHelper = new SortHelper<>(surveyQgroupService, "sort");
                surveyQgroup.setSort(sortHelper.next());
            }
            surveyQgroupService.save(surveyQgroup);
        }
        return surveyQgroup;
    }

    /**
     * 批量删除问卷问题组（权限标识：survey:surveyQgroup:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyQgroup:remove')")
    @OperationLog(module = "问卷问题组", comments = "批量删除问卷问题组")
    @PostMapping("/remove")
    public SurveyVO remove(@RequestBody SurveyQgroupParam param) {
        return surveyQgroupService.removeSurveyQgroup(param);
    }
}
