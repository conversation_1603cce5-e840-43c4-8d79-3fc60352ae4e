package com.sanythadmin.project.survey.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.survey.dto.SurveyQuestionImportDTO;
import com.sanythadmin.project.survey.entity.SurveyQuestions;
import com.sanythadmin.project.survey.entity.SurveyQuestionsOptions;
import com.sanythadmin.project.survey.enums.QuestionType;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsOptionsMapper;
import com.sanythadmin.project.survey.param.SurveyQuestionsParam;
import com.sanythadmin.project.survey.service.SurveyQuestionsClassService;
import com.sanythadmin.project.survey.service.SurveyQuestionsOptionsService;
import com.sanythadmin.project.survey.service.SurveyQuestionsService;
import com.sanythadmin.project.survey.vo.SurveyQuestionsVO;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;
import static com.sanythadmin.project.survey.constant.ConstantsSurvey.*;
import static com.sanythadmin.project.survey.enums.QuestionType.*;

/**
 * 问卷题库问题Service实现
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Slf4j
@Service
public class SurveyQuestionsServiceImpl extends ServiceImpl<SurveyQuestionsMapper, SurveyQuestions> implements SurveyQuestionsService {

    @Resource
    private SurveyQuestionsMapper surveyQuestionsMapper;
    @Resource
    private SurveyQuestionsOptionsMapper surveyQuestionsOptionsMapper;
    @Resource
    private SurveyQuestionsOptionsService surveyQuestionsOptionsService;
    @Resource
    private SurveyQuestionsClassService surveyQuestionsClassService;


    @Transactional
    @Override
    public void saveQuestionsAndOptions(SurveyQuestions questions) {
//        SurveyQuestions questions = new SurveyQuestions();
//        BeanUtil.copyProperties(param, questions);

        if (StringUtils.hasLength(questions.getId())) {
            surveyQuestionsOptionsMapper.deleteByQid(Collections.singletonList(questions.getId()));
            this.updateById(questions);
        } else {
            questions.setCreateTime(LocalDateTime.now());
            questions.setCreateUser(SecurityUtil.getUsername());
            save(questions);
        }
        List<SurveyQuestionsOptions> surveyQuestionsOptionsList = questions.getSurveyQuestionsOptionsList();
        if (CollectionUtils.isNotEmpty(surveyQuestionsOptionsList)) {
            AtomicInteger index = new AtomicInteger(0);
            surveyQuestionsOptionsList.forEach(surveyQuestionsOptions -> {
                int currentIndex = index.getAndIncrement(); // 获取当前下标并递增
                surveyQuestionsOptions.setQid(questions.getId());
                if (surveyQuestionsOptions.getSort() == null) {
                    SortHelper<SurveyQuestionsOptions> sortHelper = new SortHelper<>(surveyQuestionsOptionsService, "sort");
                    surveyQuestionsOptions.setSort(sortHelper.next() + currentIndex);
                }
            });
        }

        surveyQuestionsOptionsService.saveOrUpdateBatch(surveyQuestionsOptionsList);
    }
    @Transactional
    @Override
    public void removeQuestionsAndOptions(List<String> ids) {
        surveyQuestionsOptionsMapper.deleteByQid(ids);
        removeByIds(ids);
    }

    @Override
    public SurveyQuestionsVO getByIdAndOptions(String id) {
        SurveyQuestions surveyQuestions = getById(id);
        SurveyQuestionsVO surveyQuestionsVO = new SurveyQuestionsVO();
        BeanUtil.copyProperties(surveyQuestions, surveyQuestionsVO);
        List<SurveyQuestionsOptions> examQuestionsOptionsList = surveyQuestionsOptionsService.list(new LambdaQueryWrapper<SurveyQuestionsOptions>().eq(SurveyQuestionsOptions::getQid, id));
        surveyQuestionsVO.setSurveyQuestionsOptionsList(examQuestionsOptionsList);
        return surveyQuestionsVO;
    }

    @Override
    public void exportTemplate(ServletOutputStream outputStream) {
        List<String> headers = Arrays.asList(
                "问题分类", "问题名称", "问题类型", "是否必答","评分最小值","评分最大分值", "问题选项(选项编码#选项名称#分值)"
        );

        List<List<String>> headList = headers.stream()
                .map(Arrays::asList)
                .collect(Collectors.toList());

        List<List<Object>> dataList = Arrays.asList(
                Arrays.asList("示例：单选题", DX.getText(), DX.getText(), JudgeMark.YES.getText(),0, 0,"A#选项A#1,B#选项B#2,C#选项C#3,D#选项D#4"),
                Arrays.asList("示例：多选题", MX.getText(), MX.getText(), JudgeMark.YES.getText(),0, 0,"A#选项A#1,B#选项B#2,C#选项C#3,D#选项D#4,E#选项E#5,F#选项F#6"),
                Arrays.asList("示例：填空题", TK.getText(), TK.getText(), JudgeMark.YES.getText(),0, 0,""),
                Arrays.asList("示例：评分题", TK.getText(), TK.getText(), JudgeMark.YES.getText(),0, 10,""),
                Arrays.asList("示例：上传题", QuestionType.SC.getText(), QuestionType.SC.getText(), JudgeMark.YES.getText(),0, 0,"")
        );

        EasyExcelHelper.exportExcel(outputStream, "问题导入模板", headList, dataList);
    }
    @Transactional
    @Override
    public void importSurveyQuestions(MultipartFile file) {
        Map<String, String> allNameIdMap = surveyQuestionsClassService.getAllNameIdMap();

        try {
            EasyExcel.read(file.getInputStream(), SurveyQuestionImportDTO.class, new ReadListener<SurveyQuestionImportDTO>() {
                private List<SurveyQuestions> questions = new ArrayList<>();
                private List<SurveyQuestionsOptions> options = new ArrayList<>();

                @Override
                public void invoke(SurveyQuestionImportDTO data, AnalysisContext context) {
                    try {
                        SurveyQuestions question = convertToSurveyQuestions(data, allNameIdMap);
                        questions.add(question);
                        options.addAll(convertToSurveyQuestionOptions(data, question.getId()));
                    } catch (Exception e) {
                        log.error("处理导入数据时出错，行号：{}，错误：{}", context.readRowHolder().getRowIndex(), e.getMessage());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    saveBatch(questions);
                    surveyQuestionsOptionsService.saveBatch(options);
                    log.info("成功导入 {} 道题目和 {} 个选项", questions.size(), options.size());
                }
            }).sheet().doRead();
        } catch (IOException e) {
            log.error("导入数据失败", e);
            throw new RuntimeException("导入数据失败", e);
        }
    }

    private SurveyQuestions convertToSurveyQuestions(SurveyQuestionImportDTO dto,Map<String, String> allNameIdMap) {
        SurveyQuestions question = new SurveyQuestions();
        question.setId(CommonUtil.getUUID());
        question.setName(dto.getName());
        question.setQtype(QuestionType.get(dto.getQtype()));
        String cid = allNameIdMap.getOrDefault(dto.getCname(), "");
        if (!StringUtils.hasLength(cid)) {
            AssertUtil.throwMessage("试题分类不存在");
        }
        question.setCid(cid);
        question.setSfbt(dto.getSfbt());
        question.setMinScore(dto.getMinScore());
        question.setMaxScore(dto.getMaxScore());
        question.setCreateTime(LocalDateTime.now());
        question.setCreateUser(getAccount().getUsername());
        return question;
    }

    private List<SurveyQuestionsOptions> convertToSurveyQuestionOptions(SurveyQuestionImportDTO dto, String questionId) {
        List<SurveyQuestionsOptions> options = new ArrayList<>();

        switch (dto.getQtype()) {
            case SURVEY_QTYPE_DX:
            case SURVEY_QTYPE_MX:
                if (StringUtils.hasLength(dto.getOptions())) {
                    String[] split = dto.getOptions().split(",");
                    for (int i = 0; i < split.length; i++) {
                        String[] split1 = split[i].split("#");
                        if (ArrayUtil.isNotEmpty(split1)) {
                            addOptionIfNotNull(options, questionId, split1[0], split1[1], i + 1, Double.valueOf(split1[2]));
                        }
                    }
                }
                break;
            case SURVEY_QTYPE_TK:
            case SURVEY_QTYPE_PF:
            case SURVEY_QTYPE_SC:
                break;
            default:
                log.warn("未知的题目类型: {}",dto.getQtype());
        }
        return options;
    }

    private void addOptionIfNotNull(List<SurveyQuestionsOptions> options, String questionId, String code, String name, int sort,Double score) {
        if (name != null && !name.isEmpty()) {
            SurveyQuestionsOptions option = new SurveyQuestionsOptions();
            option.setQid(questionId);
            option.setCode(code);
            option.setName(name);
            option.setSort(sort);
            option.setScore(score);
            options.add(option);
        }
    }


    @Override
    public PageResult<SurveyQuestions> getQuestionsNotInPaper(SurveyQuestionsParam param) {
        PageParam<SurveyQuestions, SurveyQuestionsParam> page = new PageParam<>(param);
        QueryWrapper<SurveyQuestions> wrapper = page.getOrderWrapper();
//        QueryWrapper<SurveyQuestions> queryWrapper = new QueryWrapper<>();
        wrapper.notExists("select 1 from SYT_SURVEY_PQUESTIONS " +
                "where SYT_SURVEY_QUESTIONS.id = SYT_SURVEY_PQUESTIONS.qid " +
                "and SYT_SURVEY_PQUESTIONS.SID = {0}", param.getSid());
        PageParam<SurveyQuestions, SurveyQuestionsParam> iPage = surveyQuestionsMapper.selectPage(page, wrapper);
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }
}
