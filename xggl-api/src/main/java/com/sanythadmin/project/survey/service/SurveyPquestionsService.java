package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.survey.entity.SurveyPquestions;
import com.sanythadmin.project.survey.param.SurveyPquestionsParam;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.vo.SurveyPquestionsVO;
import com.sanythadmin.project.survey.vo.SurveyVO;

/**
 * 问卷问题Service
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
public interface SurveyPquestionsService extends IService<SurveyPquestions> {


    SurveyPquestionsVO getSurveyPquestions(String id);

    SurveyVO saveSurveyPquestions(SurveyPquestionsParam param);

    SurveyVO saveSurveyPquestionsByQuestions(SurveyPquestionsParam param);

    SurveyVO removeSurveyPquestions(SurveyPquestionsParam param);

    SurveyVO saveSurveyPquestionsRules(SurveyPquestionsRuleParam param);

    SurveyVO removeSurveyPquestionsRules(SurveyPquestionsRuleParam param);
}
