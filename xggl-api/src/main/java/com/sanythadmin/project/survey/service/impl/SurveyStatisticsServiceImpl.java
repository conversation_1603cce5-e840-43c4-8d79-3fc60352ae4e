package com.sanythadmin.project.survey.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.SqlUtil;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.export.enums.ExportType;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.survey.entity.*;
import com.sanythadmin.project.survey.mapper.SurveyAnswerInfoMapper;
import com.sanythadmin.project.survey.mapper.SurveyStatisticsMapper;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.param.SurveyAnswerParam;
import com.sanythadmin.project.survey.service.SurveyAnswerInfoService;
import com.sanythadmin.project.survey.service.SurveyAnswerService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.service.SurveyStatisticsService;
import com.sanythadmin.project.survey.vo.SurveyAnswerStatistics;
import com.sanythadmin.project.survey.vo.SurveyPquestionsVO;
import com.sanythadmin.project.survey.vo.SurveyQgroupVO;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.DateUtil.DEFAULT_DATEMS_FORMAT;


@RequiredArgsConstructor
@Service
public class SurveyStatisticsServiceImpl extends ServiceImpl<SurveyAnswerInfoMapper,SurveyAnswerInfo> implements SurveyStatisticsService {

    @Resource
    private SurveyStatisticsMapper surveyStatisticsMapper;
    private final UserInfoMapper userInfoMapper;
    private final SurveyService surveyService;
    private final SurveyAnswerService surveyAnswerService;
    private final SurveyAnswerInfoService surveyAnswerInfoService;
    private final GridFsService gridFsService;

    @Override
    public PageResult<UserInfo> pageAnsweredUserInfo(SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>(userInfoParam);
        wrapper.innerJoin(SelectorAccount.class, "acc", SelectorAccount::getXgh, SurveyAnswerInfo::getXgh);
        wrapper.eq("acc.ITEM_ID", param.getSid());
        MyMPJLambdaWrapper<SurveyAnswerInfo, SurveyAnswerInfoParam> surveyAnswerInfoWrapper = new MyMPJLambdaWrapper<>();
        surveyAnswerInfoWrapper.buildQueryCondition("info", param);
        StringBuilder existsSql = new StringBuilder("SELECT 1 FROM SYT_SURVEY_ANSWER_INFO info WHERE ")
                .append(wrapper.getAlias() + ".XGH=info.XGH and ")
                .append(SqlUtil.getRealSql(surveyAnswerInfoWrapper));
        wrapper.exists(existsSql.toString());
        Class<UserInfo> aClass = UserInfo.class;
        Page<UserInfo> page = userInfoMapper.selectJoinPageWithPermission(wrapper.getPage(), aClass, wrapper, new MyMPQueryParams(aClass));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<UserInfo> pageNoAnsweredUserInfo(SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>(userInfoParam);
        wrapper.innerJoin(SelectorAccount.class, "acc", SelectorAccount::getXgh, SurveyAnswerInfo::getXgh);
        wrapper.eq("acc.ITEM_ID", param.getSid());
        MyMPJLambdaWrapper<SurveyAnswerInfo, SurveyAnswerInfoParam> surveyAnswerInfoWrapper = new MyMPJLambdaWrapper<>();
        surveyAnswerInfoWrapper.buildQueryCondition("info", param);
        StringBuilder notExistsSql = new StringBuilder("SELECT 1 FROM SYT_SURVEY_ANSWER_INFO info WHERE ")
                .append(wrapper.getAlias() + ".XGH=info.XGH and ")
                .append(SqlUtil.getRealSql(surveyAnswerInfoWrapper));
        wrapper.notExists(notExistsSql.toString());
        Class<UserInfo> aClass = UserInfo.class;
        Page<UserInfo> page = userInfoMapper.selectJoinPageWithPermission(wrapper.getPage(), aClass, wrapper, new MyMPQueryParams(aClass));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public SurveyVO getAnswerStatistics(SurveyAnswerInfoParam param) {
        List<SurveyAnswerStatistics> answerStatistics = surveyStatisticsMapper.getAnswerStatistics(param.getSid());
        Map<String, SurveyAnswerStatistics> answerStatisticsMap = answerStatistics.stream()
                .collect(Collectors.toMap(
                        answer -> answer.getQuestionId() + "_" + answer.getOptionId(),
                        answer -> answer
                ));
        SurveyVO surveyVO = surveyService.getSurveyVO(param.getSid());
        surveyVO.getSurveyPaperQuestionsVOList().forEach(surveyPquestionsVO -> {
            if (CollectionUtils.isNotEmpty(surveyPquestionsVO.getSurveyPquestionsOptionsList())) {
                surveyPquestionsVO.getSurveyPquestionsOptionsList().forEach(surveyPquestionsOptions -> {
                    SurveyAnswerStatistics surveyAnswerStatistics = answerStatisticsMap.get(surveyPquestionsOptions.getSQid() + "_" + surveyPquestionsOptions.getId());
                    if (surveyAnswerStatistics != null) {
                        surveyPquestionsOptions.setOptionAnswerCount(surveyAnswerStatistics.getOptionAnswerCount());
                        surveyPquestionsOptions.setPercentage(surveyAnswerStatistics.getPercentage());
                    }
                });
            } else {
                SurveyAnswerStatistics surveyAnswerStatistics = answerStatisticsMap.get(surveyPquestionsVO.getId() + "_null");
                surveyPquestionsVO.setAnswerCount(surveyAnswerStatistics.getAnswerCount());
            }

        });
        return surveyVO;
    }

    @Override
    public PageResult<UserInfo> pageAnswerStatisticsUserInfo(SurveyAnswerParam param, UserInfoParam userInfoParam) {
        if (StringUtils.isEmpty(param.getContentId())) {
            AssertUtil.throwMessage("问题id不能为空");
        }
        /*if (StringUtils.isEmpty(param.getResultId())) {
            AssertUtil.throwMessage("问题选项id不能为空");
        }*/
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>(userInfoParam);
        wrapper.leftJoin(SurveyAnswer.class, "sa", SurveyAnswer::getXgh, UserInfo::getXgh);
        wrapper.buildQueryCondition("sa", param);
        Class<UserInfo> aClass = UserInfo.class;
        Page<UserInfo> page = userInfoMapper.selectJoinPageWithPermission(wrapper.getPage(), aClass, wrapper, new MyMPQueryParams(aClass));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public Map<String,Object> exportSurveyAnswers(SurveyAnswerParam param, ExportType exportType) throws IOException {
        // 创建临时文件夹存储生成的文件
        String tempDir = System.getProperty("java.io.tmpdir") + "/survey_export_" + System.currentTimeMillis() + "/";
        String documentsDir = tempDir + "documents/";  // 存放导出的文档
        String attachmentsDir = tempDir + "attachments/"; // 存放附件
        Arrays.asList(tempDir, documentsDir, attachmentsDir).forEach(dir -> {
            File directory = new File(dir);
            if (!directory.exists()) {
                directory.mkdirs();
            }
        });

        List<String> filePathList = new ArrayList<>();
        List<SurveyAnswerInfo> surveyAnswerInfoList = surveyAnswerInfoService.list(new LambdaQueryWrapper<SurveyAnswerInfo>().eq(SurveyAnswerInfo::getSid, param.getSid()));
        List<SurveyAnswer> answers = surveyAnswerService.list(new LambdaQueryWrapper<SurveyAnswer>().eq(SurveyAnswer::getSid, param.getSid()));
        surveyAnswerInfoList.forEach(surveyAnswerInfo -> {
            SurveyVO surveyVO = surveyService.getSurveyVO(param.getSid(),surveyAnswerInfo);
            LinkedList<SurveyQgroupVO> qgroupVOS = surveyVO.getQuestionsGroupList();
            LinkedList<SurveyPquestionsVO> pquestionsVOS = surveyVO.getSurveyPaperQuestionsVOList();
            LinkedList<SurveyPquestionsOptions> options = pquestionsVOS.stream()
                    .flatMap(surveyPquestionsVO -> surveyPquestionsVO.getSurveyPquestionsOptionsList().stream())
                    .collect(Collectors.toCollection(LinkedList::new));

            byte[] content = exportType == ExportType.WORD ?
                    exportToWord(surveyVO, qgroupVOS, pquestionsVOS, options, answers,surveyAnswerInfoList.size()) :
                    exportToExcel(surveyVO, qgroupVOS, pquestionsVOS, options, answers,surveyAnswerInfoList.size());

            String docFileName = documentsDir + surveyAnswerInfo.getXgh() + "_" + surveyVO.getName() +
                    (exportType == ExportType.WORD ? ".docx" : ".xlsx");
            try {
                Files.write(Paths.get(docFileName), content);
                filePathList.add(docFileName);
            } catch (Exception e) {
                throw new BusinessException("导出问卷答题情况失败");
            }
        });
        // 处理该答卷的所有附件
        exportAttachments(answers, attachmentsDir, filePathList);
        String zipFileName = "问卷答题情况_" + DateUtil.getDateTime(DEFAULT_DATEMS_FORMAT) + ".zip";
        CommonUtil.createZipFile(filePathList, tempDir + zipFileName);

        byte[] zipContent = Files.readAllBytes(Paths.get(tempDir + zipFileName));
        FileUtils.deleteDirectory(new File(tempDir));
        Map<String, Object> map = new HashMap<>();
        map.put("fileName", zipFileName);
        map.put("contentType", "application/zip");
        map.put("fileContent", zipContent);
        return map;
    }

    private byte[] exportToWord(SurveyVO survey, LinkedList<SurveyQgroupVO> groups,
                                LinkedList<SurveyPquestionsVO> questions, LinkedList<SurveyPquestionsOptions> options,
                                List<SurveyAnswer> answers,Integer cyrs) {
        try {
            XWPFDocument document = new XWPFDocument();
            // 问卷基本信息
            addBasicInfo(document, survey,cyrs);
            // 按分组导出问题及答题情况
            for (SurveyQgroup group : groups) {
                // 分组标题
                XWPFParagraph groupPara = document.createParagraph();
                groupPara.setStyle("Heading2");
                XWPFRun groupRun = groupPara.createRun();
                groupRun.setText(group.getName());
                groupRun.setBold(true);
                groupRun.setFontSize(14);

                // 该分组下的问题
                List<SurveyPquestionsVO> groupQuestions = questions.stream()
                        .filter(q -> group.getId().equals(q.getGid()))
                        .collect(Collectors.toList());

                for (int i = 0; i < groupQuestions.size(); i++) {
                    SurveyPquestionsVO question = groupQuestions.get(i);
                    addQuestionAnswers(document, question, options, answers, i + 1);
                }
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new BusinessException("导出Word文档失败");
        }
    }

    private void addBasicInfo(XWPFDocument document, Survey survey,Integer cyrs) {
        // 标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(survey.getName());
        titleRun.setBold(true);
        titleRun.setFontSize(16);

        // 基本信息表格
        XWPFTable table = document.createTable(4, 2);
        table.setWidth("100%");

        addTableRow(table, 0, "问卷名称", survey.getName());
        addTableRow(table, 1, "问卷说明", survey.getBz());
        addTableRow(table, 2, "创建人", survey.getXm());
        addTableRow(table, 3, "创建时间", survey.getCreateTime().toString());
//        addTableRow(table, 4, "发卷人数", survey.getFjrs().toString());
//        addTableRow(table, 5, "参与人数", cyrs.toString());
    }

    private void addQuestionAnswers(XWPFDocument document, SurveyPquestionsVO question,
                                    LinkedList<SurveyPquestionsOptions> options, List<SurveyAnswer> answers, int index) {
        // 问题标题
        XWPFParagraph questionPara = document.createParagraph();
        XWPFRun questionRun = questionPara.createRun();
        questionRun.setText(index + ". " + question.getName() + "（" + question.getQtype().getText() + "）");
        questionRun.setFontSize(12);

        // 获取该题的所有答案
        List<SurveyAnswer> questionAnswers = answers.stream()
                .filter(a -> a.getContentId().equals(question.getId()))
                .collect(Collectors.toList());

        // 根据题目类型显示答题情况
        switch (question.getQtype()) {
            case DX:
            case MX:
                addOptionAnswers(document, question, options, questionAnswers);
                break;
            case TK:
            case PF:
                addTextAnswers(document, questionAnswers);
                break;
            case SC:
                break;
        }
    }

    private void addTableRow(XWPFTable table, int rowIndex, String label, String value) {
        XWPFTableRow row = table.getRow(rowIndex);
        // 设置第一列（标签）
        XWPFTableCell labelCell = row.getCell(0);
        labelCell.setWidth("30%");
        XWPFParagraph labelPara = labelCell.getParagraphs().get(0);
        XWPFRun labelRun = labelPara.createRun();
        labelRun.setText(label);
        labelRun.setBold(true);

        // 设置第二列（值）
        XWPFTableCell valueCell = row.getCell(1);
        valueCell.setWidth("70%");
        XWPFParagraph valuePara = valueCell.getParagraphs().get(0);
        XWPFRun valueRun = valuePara.createRun();
        valueRun.setText(StringUtils.defaultString(value, ""));
    }

    /**
     * 添加选择题答题情况
     */
    private void addOptionAnswers(XWPFDocument document, SurveyPquestionsVO question,
                                  LinkedList<SurveyPquestionsOptions> options, List<SurveyAnswer> questionAnswers) {
        // 获取该题的所有选项
        List<SurveyPquestionsOptions> questionOptions = options.stream()
                .filter(opt -> opt.getSQid().equals(question.getId()))
                .collect(Collectors.toList());

        // 创建选项统计表格
        XWPFTable table = document.createTable(questionOptions.size() + 1, 1);
        table.setWidth("100%");

        // 设置表头
        XWPFTableRow headerRow = table.getRow(0);
        headerRow.getCell(0).setText("选项");

        for (int i = 0; i < questionOptions.size(); i++) {
            SurveyPquestionsOptions option = questionOptions.get(i);
            XWPFTableRow row = table.getRow(i + 1);

            // 选项内容
            row.getCell(0).setText(option.getCode() + ". " + option.getName());
        }

        // 添加答题详情标题
        XWPFParagraph detailTitle = document.createParagraph();
        XWPFRun detailRun = detailTitle.createRun();
        if (questionAnswers.size() > 0) {
            SurveyAnswer answer = questionAnswers.get(0);
            // 获取选项内容
            if (StringUtils.isNotBlank(answer.getResultId())) {
                String optionContent = Arrays.stream(answer.getResultId().split(","))
                        .map(optionId -> questionOptions.stream()
                                .filter(opt -> opt.getId().equals(optionId))
                                .map(opt -> opt.getName())
                                .findFirst()
                                .orElse(""))
                        .collect(Collectors.joining("、"));

                detailRun.setText(String.format("答题详情: %s", StringUtils.isEmpty(optionContent) ? "" : optionContent));
            }
        }
        detailRun.setBold(true);
    }

    /**
     * 添加填空等题答题情况
     */
    private void addTextAnswers(XWPFDocument document, List<SurveyAnswer> answers) {
        // 创建答题详情标题
        XWPFParagraph detailTitle = document.createParagraph();
        XWPFRun detailRun = detailTitle.createRun();
        if (!answers.isEmpty()) {
            SurveyAnswer answer = answers.get(0);
            detailRun.setText(String.format("答题详情: %s", StringUtils.isEmpty(answer.getResult()) ? "" : answer.getResult()));
        }
        detailRun.setBold(true);
    }


    private byte[] exportToExcel(SurveyVO survey, LinkedList<SurveyQgroupVO> groups,
                                 LinkedList<SurveyPquestionsVO> questions, LinkedList<SurveyPquestionsOptions> options,
                                 List<SurveyAnswer> answers,Integer cyrs) {
        try {
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("问卷答题情况");

            // 设置列宽
            sheet.setColumnWidth(0, 10 * 256);
            sheet.setColumnWidth(1, 40 * 256);
            sheet.setColumnWidth(2, 50 * 256);

            int rowNum = 0;

            // 问卷基本信息
            rowNum = addExcelBasicInfo(sheet, survey, rowNum,cyrs);

            // 按分组展示问题及答题情况
            for (SurveyQgroup group : groups) {
                // 分组标题
                Row groupRow = sheet.createRow(rowNum++);
                Cell groupCell = groupRow.createCell(0);
                groupCell.setCellValue(group.getName());

                // 该分组下的问题
                List<SurveyPquestionsVO> groupQuestions = questions.stream()
                        .filter(q -> group.getId().equals(q.getGid()))
                        .collect(Collectors.toList());

                for (SurveyPquestionsVO question : groupQuestions) {
                    rowNum = addExcelQuestionAnswers(sheet, question, options, answers, rowNum);
                }
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("导出Excel文档失败", e);
            throw new BusinessException("导出Excel文档失败");
        }
    }

    private int addExcelBasicInfo(XSSFSheet sheet, Survey survey, int rowNum,Integer cyrs) {
        Row titleRow = sheet.createRow(rowNum++);
        titleRow.createCell(0).setCellValue("问卷名称：");
        titleRow.createCell(1).setCellValue(survey.getName());

        Row descRow = sheet.createRow(rowNum++);
        descRow.createCell(0).setCellValue("问卷说明：");
        descRow.createCell(1).setCellValue(survey.getBz());

        Row authorRow = sheet.createRow(rowNum++);
        authorRow.createCell(0).setCellValue("创建人：");
        authorRow.createCell(1).setCellValue(survey.getXm());

        Row timeRow = sheet.createRow(rowNum++);
        timeRow.createCell(0).setCellValue("创建时间：");
        timeRow.createCell(1).setCellValue(survey.getCreateTime().toString());

        /*Row countRow = sheet.createRow(rowNum++);
        countRow.createCell(0).setCellValue("发卷人数：");
        countRow.createCell(1).setCellValue(survey.getFjrs());

        Row cyrsRow = sheet.createRow(rowNum++);
        cyrsRow.createCell(0).setCellValue("参与人数：");
        cyrsRow.createCell(1).setCellValue(cyrs);*/

        rowNum++; // 空行
        return rowNum;
    }

    private int addExcelQuestionAnswers(XSSFSheet sheet, SurveyPquestionsVO question,
                                        LinkedList<SurveyPquestionsOptions> options, List<SurveyAnswer> answers, int rowNum) {
        // 问题标题
        Row questionRow = sheet.createRow(rowNum++);
        questionRow.createCell(0).setCellValue(question.getName());
        questionRow.createCell(1).setCellValue(question.getQtype().getText());

        List<SurveyPquestionsOptions> questionOptions = options.stream()
                .filter(opt -> opt.getSQid().equals(question.getId()))
                .collect(Collectors.toList());

        for (SurveyPquestionsOptions questionOption : questionOptions) {
            Row optionsRow = sheet.createRow(rowNum++);
            optionsRow.createCell(1).setCellValue(questionOption.getCode() + "." + questionOption.getName());
        }

        // 获取该题的所有答案
        List<SurveyAnswer> questionAnswers = answers.stream()
                .filter(a -> a.getContentId().equals(question.getId()))
                .toList();

        // 根据题目类型显示答题情况
        switch (question.getQtype()) {
            case DX:
            case MX:
            case TK:
            case PF:
                rowNum = addAnswers(sheet, questionAnswers, rowNum, questionOptions);
                break;
//            case SC:
//                break;
        }
        rowNum++; // 空行
        return rowNum;
    }

    private static int addAnswers(XSSFSheet sheet, List<SurveyAnswer> questionAnswers, int rowNum, List<SurveyPquestionsOptions> questionOptions) {
        if (!questionAnswers.isEmpty()) {
            SurveyAnswer answer = questionAnswers.get(0);
            // 获取选项内容
            if (StringUtils.isNotBlank(answer.getResultId())) {
                String optionContent = Arrays.stream(answer.getResultId().split(","))
                        .map(optionId -> questionOptions.stream()
                                .filter(opt -> opt.getId().equals(optionId))
                                .map(SurveyPquestionsOptions::getName)
                                .findFirst()
                                .orElse(""))
                        .collect(Collectors.joining("、"));

                Row answerRow = sheet.createRow(rowNum++);
                answerRow.createCell(0).setCellValue("答题详情:");
                answerRow.createCell(1).setCellValue(StringUtils.isEmpty(optionContent) ? "" : optionContent);
            }
        }
        return rowNum;
    }

    /**
     * 导出附件
     */
    private void exportAttachments(List<SurveyAnswer> answers, String attachmentsDir, List<String> allFiles) {
        answers.stream()
                .filter(answer -> StringUtils.isNotBlank(answer.getResult()) && answer.getResult().startsWith("[{"))
                .forEach(answer -> {
                    try {
                        List<FileInfo> fileInfos = JSONArray.parseArray(answer.getResult(), FileInfo.class);
                        for (FileInfo item : fileInfos) {
                            GridFsResource resource = gridFsService.get(item.getId());
                            if (resource != null) {
                                String fileName = answer.getXgh() + "_" + answer.getContentId() + "_" + item.getOriginalFilename();
                                String filePath = attachmentsDir + fileName;
                                try (InputStream is = resource.getInputStream();
                                     FileOutputStream fos = new FileOutputStream(filePath)) {
                                    IOUtils.copy(is, fos);
                                    allFiles.add(filePath);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("导出附件失败: {}", e);
                    }
                });
    }
}
