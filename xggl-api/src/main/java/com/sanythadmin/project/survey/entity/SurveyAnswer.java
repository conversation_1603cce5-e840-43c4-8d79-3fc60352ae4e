package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问卷答卷信息
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_ANSWER")
@Entity
@Table(name = "SYT_SURVEY_ANSWER")
public class SurveyAnswer extends BaseSurveyAnswer implements Serializable {
    private static final long serialVersionUID = 1L;

    public SurveyAnswer(String sid, String xgh) {
        super(sid, xgh);
    }

    public SurveyAnswer() {

    }
}
