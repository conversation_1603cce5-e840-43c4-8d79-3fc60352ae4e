package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.survey.enums.Mdlx;
import com.sanythadmin.project.survey.enums.SurveyMethod;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.enums.SurveyType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 问卷管理查询参数
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    @QueryField(type = QueryType.EQ)
    private UserType userType;

    /**
     * 问卷名称
     */
    private String name;

    /**
     * 问题总数
     */
    private Integer subjectSum;

    /**
     * 问卷模式
     */
    @QueryField(type = QueryType.EQ)
    private SurveyType wjms;

    /**
     * 问卷分类id
     */
    @QueryField(type = QueryType.EQ)
    private String cid;

    /**
     * 备注说明
     */
    private String bz;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 发布人
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 发布人姓名
     */
    private String xm;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private SurveyState status;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 调查方式(实名、匿名)
     */
    @QueryField(type = QueryType.EQ)
    private SurveyMethod dcfs;

    /**
     * 是否可评论
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfkpl;

    /**
     * 是否可匿名评论
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfknmpl;

    /**
     * 是否记分
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfjf;

    /**
     * 人员选择器数据
     */
    @QueryField(ignore = true)
    private List<SelectorData> selectorDatas;

    /**
     * 人员选择器是否并联（1：是， 0：否）
     */
    @QueryField(ignore = true)
    private JudgeMark sfbl;

    /**
     * 名单类型(应答,未答)
     */
    @QueryField(type = QueryType.EQ)
    private Mdlx mdlx;


    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "create_time desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
