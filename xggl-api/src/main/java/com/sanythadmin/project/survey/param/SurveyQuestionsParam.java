package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.survey.entity.SurveyQuestionsOptions;
import com.sanythadmin.project.survey.enums.QuestionType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 问卷题库问题查询参数
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyQuestionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 问题名称
     */
    private String name;

    /**
     * 问题类型
     */
    @QueryField(type = QueryType.EQ)
    private QuestionType qtype;

    /**
     * 问题分类ID
     */
    @QueryField(type = QueryType.EQ)
    private String cid;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }

    /**
     * 是否必填
     */
    @QueryField(type = QueryType.EQ)
    private String sfbt;

    /**
     * 最小分值
     */
    private Double minScore;

    /**
     * 最大分值
     */
    private Double maxScore;

    @QueryField(ignore = true)
    private List<SurveyQuestionsOptions> surveyQuestionsOptionsList;

    /**
     * 试卷ID,用于过滤掉已选试题
     */
    @QueryField(ignore = true)
    private String sid;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort asc";
    }
}
