<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.survey.mapper.SurveyPquestionsOptionsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CODE, NAME, S_QID, SORT, SCORE, SID, GID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.code != null">
                        AND a.CODE LIKE concat(concat('%',#{param.code), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.sQid != null">
                        AND a.S_QID LIKE concat(concat('%',#{param.sQid), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE LIKE concat(concat('%',#{param.score), '%')
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
    </sql>
</mapper>
