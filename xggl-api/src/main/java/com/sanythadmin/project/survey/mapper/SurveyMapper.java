package com.sanythadmin.project.survey.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.survey.entity.Survey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 问卷管理Mapper
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
public interface SurveyMapper extends MyMPJBaseMapper<Survey> {

    @Update("UPDATE SYT_SURVEY " +
            "SET SUBJECT_SUM = (SELECT COUNT(*) FROM SYT_SURVEY_PQUESTIONS WHERE SID = #{id}) " +
            "WHERE id = #{id}")
    void updateSum(@Param("id") String id);

    @Update("UPDATE SYT_SURVEY " +
            "SET SUBJECT_SUM = (SELECT sum(Q_SUM) FROM SYT_SURVEY_PQUESTIONS_RULE WHERE SID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumByRule(@Param("id") String id);

}
