package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.survey.enums.QuestionType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷问题
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_PQUESTIONS")
@Entity
@Table(name = "SYT_SURVEY_PQUESTIONS")
public class SurveyPquestions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问题名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 问题类型
     */
    @Column(name = "QTYPE",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "QTYPE",jdbcType = JdbcType.VARCHAR)
    private QuestionType qtype;

    /**
     * 问题分类id
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 是否必填
     */
    @Column(name = "SFBT", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFBT", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfbt;

    /**
     * 评分最小分值
     */
    @Column(name = "MIN_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MIN_SCORE")
    private Double minScore;

    /**
     * 评分最大分值
     */
    @Column(name = "MAX_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MAX_SCORE")
    private Double maxScore;

    /**
     * 题库问题id
     */
    @Column(name = "QID")
    @TableField("QID")
    private String qid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 问卷ID
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;
    /**
     * 备注1
     */
    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;
    /**
     * 备注2
     */
    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;

    /**
     * 答案
     */
    @Transient
    @TableField(exist = false)
    private String result;

}
