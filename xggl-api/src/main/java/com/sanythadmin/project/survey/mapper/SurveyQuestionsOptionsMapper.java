package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyQuestionsOptions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷题库问题选项Mapper
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
public interface SurveyQuestionsOptionsMapper extends BaseMapper<SurveyQuestionsOptions> {

    @Delete("<script>" +
            "delete from SYT_SURVEY_QUESTIONS_OPTIONS where QID in "+
            "<foreach collection=\"qids\" item=\"qid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{qid}" +
            "</foreach>"+
            "</script>")
    void deleteByQid(@Param("qids") List<String> qids);

    @Delete("<script>" +
            "DELETE FROM SYT_SURVEY_QUESTIONS_OPTIONS\n" +
            "    WHERE QID IN (SELECT ID FROM SYT_SURVEY_QUESTIONS WHERE CID IN  " +
            "<foreach collection=\"cids\" item=\"cid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{cid}" +
            "</foreach>" +
            ")" +
            "</script>")
    void deleteByClassIds(@Param("cids") List<String> cids);

}
