package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyPquestions;
import com.sanythadmin.project.survey.param.SurveyPquestionsParam;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.service.SurveyPquestionsService;
import com.sanythadmin.project.survey.vo.SurveyPquestionsVO;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷问题控制器
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@RestController
@RequestMapping("/api/survey/survey-pquestions")
public class SurveyPquestionsController extends BaseController {
    @Resource
    private SurveyPquestionsService surveyPquestionsService;

    /**
     * 分页查询问卷问题（权限标识：survey:surveyPquestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:list')")
    @GetMapping("/page")
    public PageResult<SurveyPquestions> page(SurveyPquestionsParam param) {
        PageParam<SurveyPquestions, SurveyPquestionsParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort asc");
        page = surveyPquestionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷问题（权限标识：survey:surveyPquestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:list')")
    @GetMapping()
    public List<SurveyPquestions> list(SurveyPquestionsParam param) {
        PageParam<SurveyPquestions, SurveyPquestionsParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort asc");
        return surveyPquestionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷问题（权限标识：survey:surveyPquestions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:list')")
    @GetMapping("/{id}")
    public SurveyPquestionsVO get(@PathVariable("id") String id) {
        return surveyPquestionsService.getSurveyPquestions(id);
    }

    /**
     * 固定选题，保存试卷试题和试题选项（权限标识：survey:surveyPquestions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:operation')")
    @OperationLog(module = "问卷问题", comments = "保存问卷问题")
    @PostMapping("/saveSurveyPquestions")
    public SurveyVO saveSurveyPquestions(@RequestBody SurveyPquestionsParam param) {
        return surveyPquestionsService.saveSurveyPquestions(param);
    }

    /**
     * 固定选题，试卷维护试题,从题库选题（权限标识：exam:examPaperQuestions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:operation')")
    @OperationLog(module = "问卷问题", comments = "问卷维护试题,从题库选题")
    @PostMapping("/saveSurveyPquestionsByQuestions")
    public SurveyVO saveSurveyPquestionsByQuestions(@RequestBody SurveyPquestionsParam param) {
        return surveyPquestionsService.saveSurveyPquestionsByQuestions(param);

    }

    /**
     * 批量删除问卷问题（权限标识：survey:surveyPquestions:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:remove')")
    @OperationLog(module = "问卷问题", comments = "批量删除问卷问题")
    @PostMapping("/remove")
    public SurveyVO remove(@RequestBody SurveyPquestionsParam param) {
        return surveyPquestionsService.removeSurveyPquestions(param);
    }

    /**
     * 随机抽题，问卷维护试题,保存随机抽题规则（权限标识：survey:surveyPquestions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:operation')")
    @OperationLog(module = "问卷问题", comments = "问卷维护问题,保存随机抽题规则")
    @PostMapping("/saveSurveyPquestionsRules")
    public SurveyVO saveSurveyPquestionsRules(@RequestBody SurveyPquestionsRuleParam param) {
        return surveyPquestionsService.saveSurveyPquestionsRules(param);

    }

    /**
     * 批量删除问卷问题规则（权限标识：survey:surveyPquestions:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestions:remove')")
    @OperationLog(module = "问卷问题", comments = "批量删除问卷问题规则")
    @PostMapping("/removeSurveyPquestionsRules")
    public SurveyVO removeSurveyPquestionsRules(@RequestBody SurveyPquestionsRuleParam param) {
        return surveyPquestionsService.removeSurveyPquestionsRules(param);
    }
}
