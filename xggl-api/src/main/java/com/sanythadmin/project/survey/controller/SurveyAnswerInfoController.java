package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.service.SurveyAnswerInfoService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷答卷信息控制器
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
@RestController
@RequestMapping("/api/survey/survey-answer-info")
public class SurveyAnswerInfoController extends BaseController {
    @Resource
    private SurveyAnswerInfoService surveyAnswerInfoService;

    /**
     * 管理者分页查询（权限标识：survey:surveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:list')")
    @GetMapping("/page")
    public PageResult<SurveyAnswerInfo> page(@Valid SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        return surveyAnswerInfoService.getSurveyAnswerInfoPage(param,userInfoParam);
    }


    /**
     * 根据id查询答卷信息(不带问卷详情)（权限标识：survey:surveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:list')")
    @GetMapping("/{id}")
    public SurveyAnswerInfo get(@PathVariable("id") String id) {
        return surveyAnswerInfoService.getSurveyAnswerInfoById(id);
    }
    /**
     * 答卷人查询答卷信息(带问卷详情)（权限标识：survey:surveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:list')")
    @GetMapping("/getSurveyAnswerInfo")
    public SurveyVO getSurveyAnswerInfo(SurveyAnswerInfo surveyAnswerInfo) {
        return surveyAnswerInfoService.getSurveyAnswerInfo(surveyAnswerInfo);
    }


    /**
     * 批量删除,级联删除答题记录（权限标识：survey:surveyAnswerInfo:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:remove')")
    @OperationLog(module = "问卷答卷信息", comments = "批量删除问卷答卷信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyAnswerInfoService.removeSurveyAnswerInfoByIds(ids);
    }

    /**
     * 个人查看答卷信息列表（权限标识：survey:surveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:personallist')")
    @GetMapping("/pagePersonal")
    public PageResult<SurveyAnswerInfo> pagePersonal(SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        param.setXgh(getAccount().getUsername());
        return surveyAnswerInfoService.getSurveyAnswerInfoPage(param,userInfoParam);
    }

    /**
     * 参与人员列表（权限标识：survey:surveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswerInfo:personallist')")
    @GetMapping("/pageAnsweredList")
    public PageResult<SurveyAnswerInfo> pageAnsweredList(SurveyAnswerInfoParam param, UserInfoParam userInfoParam) {
        return surveyAnswerInfoService.getSurveyAnswerInfoPage(param,userInfoParam);
    }
}
