package com.sanythadmin.project.survey.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.survey.entity.SurveyQuestionsClass;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsClassMapper;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsOptionsMapper;
import com.sanythadmin.project.survey.service.SurveyQuestionsClassService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 问卷题库问题分类Service实现
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Service
public class SurveyQuestionsClassServiceImpl extends ServiceImpl<SurveyQuestionsClassMapper, SurveyQuestionsClass> implements SurveyQuestionsClassService {

    @Resource
    private SurveyQuestionsClassMapper mapper;
    @Resource
    private SurveyQuestionsOptionsMapper surveyQuestionsOptionsMapper;
    @Resource
    private SurveyQuestionsMapper surveyQuestionsMapper;

    @Override
    public Map<String, String> getAllNameIdMap() {
        List<SurveyQuestionsClass> list = list();
        return list.stream()
                .collect(Collectors.toMap(
                        SurveyQuestionsClass::getName,
                        SurveyQuestionsClass::getId,
                        (existing, replacement) -> existing
                ));
    }

    @Override
    public void removeQuestionsClassByIds(List<String> ids) {
        surveyQuestionsOptionsMapper.deleteByClassIds(ids);
        surveyQuestionsMapper.deleteByClassIds(ids);
        removeByIds(ids);
    }
}
