package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 问卷题库问题选项
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_QUESTIONS_OPTIONS")
@Entity
@Table(name = "SYT_SURVEY_QUESTIONS_OPTIONS")
public class SurveyQuestionsOptions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 选项编号
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 选项名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试题ID
     */
    @Column(name = "QID")
    @TableField("QID")
    private String qid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 分数
     */
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

}
