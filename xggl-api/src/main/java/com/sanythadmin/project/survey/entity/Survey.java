package com.sanythadmin.project.survey.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.converter.ValueTextBaseEnumConverter;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.survey.enums.SurveyMethod;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.enums.SurveyType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 问卷管理
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY")
@Entity
@Table(name = "SYT_SURVEY")
public class Survey implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

    /**
     * 问卷名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 问题总数
     */
    @Column(name = "SUBJECT_SUM")
    @TableField("SUBJECT_SUM")
    private Integer subjectSum;

    /**
     * 问卷模式
     * @link SurveyMethod
     */
    @Column(name = "WJMS",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("WJMS")
    private SurveyType wjms;

    /**
     * 问卷分类id
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 备注说明
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 发布人
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 发布人姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 状态（1：启用, 0: 停用）
     */
    @Column(name = "STATUS",columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "STATUS", jdbcType = JdbcType.INTEGER)
    @ExcelProperty(value = "状态", converter = ValueTextBaseEnumConverter.class)
    private SurveyState status;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 调查方式(实名、匿名)
     */
    @Column(name = "DCFS",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("DCFS")
    private SurveyMethod dcfs;

    /**
     * 是否可评论
     */
    @Column(name = "SFKPL", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFKPL", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfkpl;

    /**
     * 是否可匿名评论
     */
    @Column(name = "SFKNMPL", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFKNMPL", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfknmpl;

    /**
     * 是否记分
     */
    @Column(name = "SFJF", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFJF", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfjf;

    /**
     * 发卷人数
     */
    @Column(name = "FJRS")
    @TableField("FJRS")
    private Integer fjrs;

    /**
     * 是否已答
     */
    @Transient
    @TableField(exist = false)
    private boolean answered;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
