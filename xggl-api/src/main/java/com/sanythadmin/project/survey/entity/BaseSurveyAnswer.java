package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.enums.QuestionType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
@NoArgsConstructor
@AllArgsConstructor
@Data
@MappedSuperclass
public class BaseSurveyAnswer {

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 答卷信息id
     */
    @Column(name = "INFO_ID")
    @TableField("INFO_ID")
    private String infoId;

    /**
     * 问卷试题id
     */
    @Column(name = "CONTENT_ID")
    @TableField("CONTENT_ID")
    private String contentId;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 分组id
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 问题类型
     */
    @Column(name = "QTYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "QTYPE", jdbcType = JdbcType.VARCHAR)
    private QuestionType qtype;

    /**
     * 问卷id
     */
    @Column(name = "SID")
    @TableField("SID")
    private String sid;

    /**
     * 答题得分
     */
    @Column(name = "POINT", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("POINT")
    private Double point;

    /**
     * 答题结果
     */
    @Column(name = "RESULT", columnDefinition = ColumnType.VARCHAR2_4000)
    @TableField("RESULT")
    private String result;

    /**
     * 单选多选答题选项ID
     */
    @Column(name = "RESULT_ID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("RESULT_ID")
    private String resultId;

    /**
     * 答题者账号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 答题时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 上传题接收附件使用
     */
    @Transient
    @TableField(exist = false)
    private List<SurveyAnswer.ResultItem> resultFiles;

    @Data
    public static class ResultItem {
        private String key;
        private String name;
        private MultipartFile file;
    }

    public BaseSurveyAnswer(String sid, String xgh) {
        this.sid = sid;
        this.xgh = xgh;
    }
}
