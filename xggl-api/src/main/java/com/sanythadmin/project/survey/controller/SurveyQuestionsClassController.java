package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyQuestionsClass;
import com.sanythadmin.project.survey.param.SurveyQuestionsClassParam;
import com.sanythadmin.project.survey.service.SurveyQuestionsClassService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/题库问题分类控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@RestController
@RequestMapping("/api/survey/survey-questions-calss")
public class SurveyQuestionsClassController extends BaseController {
    @Resource
    private SurveyQuestionsClassService surveyQuestionsClassService;

    /**
     * 分页查询问卷题库问题分类（权限标识：survey:surveyQuestionsCalss:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsCalss:list')")
    @GetMapping("/page")
    public PageResult<SurveyQuestionsClass> page(SurveyQuestionsClassParam param) {
        PageParam<SurveyQuestionsClass, SurveyQuestionsClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyQuestionsClassService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷题库问题分类（权限标识：survey:surveyQuestionsCalss:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsCalss:list')")
    @GetMapping()
    public List<SurveyQuestionsClass> list(SurveyQuestionsClassParam param) {
        PageParam<SurveyQuestionsClass, SurveyQuestionsClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyQuestionsClassService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷题库问题分类（权限标识：survey:surveyQuestionsCalss:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsCalss:list')")
    @GetMapping("/{id}")
    public SurveyQuestionsClass get(@PathVariable("id") String id) {
        return surveyQuestionsClassService.getById(id);
    }

    /**
     * 添加或修改问卷题库问题分类（权限标识：survey:surveyQuestionsCalss:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsCalss:operation')")
    @OperationLog(module = "问卷题库问题分类", comments = "保存问卷题库问题分类")
    @PostMapping("/operation")
    public void save(@RequestBody SurveyQuestionsClass surveyQuestionsCalss) {
        if (StringUtils.hasLength(surveyQuestionsCalss.getId())) {
            surveyQuestionsClassService.updateById(surveyQuestionsCalss);
        } else {
            if (surveyQuestionsCalss.getSort() == null) {
                SortHelper<SurveyQuestionsClass> sortHelper = new SortHelper<>(surveyQuestionsClassService, "sort");
                surveyQuestionsCalss.setSort(sortHelper.next());
            }
            surveyQuestionsClassService.save(surveyQuestionsCalss);
        }
    }

    /**
     * 批量删除问卷题库问题分类（权限标识：survey:surveyQuestionsCalss:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsCalss:remove')")
    @OperationLog(module = "问卷题库问题分类", comments = "批量删除问卷题库问题分类")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyQuestionsClassService.removeByIds(ids);
//        surveyQuestionsClassService.removeQuestionsClassByIds(ids);
    }
}
