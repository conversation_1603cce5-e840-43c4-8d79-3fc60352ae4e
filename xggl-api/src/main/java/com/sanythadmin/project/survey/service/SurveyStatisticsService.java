package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.export.enums.ExportType;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;
import com.sanythadmin.project.survey.param.SurveyAnswerParam;
import com.sanythadmin.project.survey.vo.SurveyVO;

import java.io.IOException;
import java.util.Map;

public interface SurveyStatisticsService extends IService<SurveyAnswerInfo> {

    PageResult<UserInfo> pageNoAnsweredUserInfo(SurveyAnswerInfoParam param, UserInfoParam userInfoParam);

    PageResult<UserInfo> pageAnsweredUserInfo(SurveyAnswerInfoParam param, UserInfoParam userInfoParam);

    SurveyVO getAnswerStatistics(SurveyAnswerInfoParam param);

    PageResult<UserInfo> pageAnswerStatisticsUserInfo(SurveyAnswerParam param, UserInfoParam userInfoParam);

    Map<String,Object> exportSurveyAnswers(SurveyAnswerParam param, ExportType exportType) throws IOException;
}
