<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.survey.mapper.SurveyAnswerMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, INFO_ID, CONTENT_ID, SORT, GID, QTYPE, SID, POINT, RESULT, XGH, CREATE_DATE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.infoId != null">
                        AND a.INFO_ID LIKE concat(concat('%',#{param.infoId), '%')
                    </if>
                    <if test="param.contentId != null">
                        AND a.CONTENT_ID LIKE concat(concat('%',#{param.contentId), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
                    <if test="param.qtype != null">
                        AND a.QTYPE LIKE concat(concat('%',#{param.qtype), '%')
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.point != null">
                        AND a.POINT LIKE concat(concat('%',#{param.point), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
    </sql>
</mapper>
