package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.survey.entity.SurveyAnswer;
import com.sanythadmin.project.survey.param.SurveyAnswerInfoParam;

/**
 * 问卷答卷信息Service
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
public interface SurveyAnswerService extends IService<SurveyAnswer> {

    void saveAnswerAndHistory(SurveyAnswerInfoParam param);

    boolean isAnswered(SurveyAnswer answer);

}
