package com.sanythadmin.project.survey.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.project.survey.entity.*;
import com.sanythadmin.project.survey.mapper.SurveyMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsOptionsMapper;
import com.sanythadmin.project.survey.mapper.SurveyQgroupMapper;
import com.sanythadmin.project.survey.param.SurveyPquestionsParam;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.service.*;
import com.sanythadmin.project.survey.vo.SurveyPquestionsVO;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 问卷问题Service实现
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Service
public class SurveyPquestionsServiceImpl extends ServiceImpl<SurveyPquestionsMapper, SurveyPquestions> implements SurveyPquestionsService {

    @Resource
    private SurveyPquestionsMapper surveyPquestionsMapper;
    @Resource
    private SurveyMapper surveyMapper;
    @Resource
    private SurveyQgroupMapper surveyQgroupMapper;
    @Resource
    private SurveyPquestionsOptionsMapper surveyPquestionsOptionsMapper;
    @Resource
    private SurveyPquestionsOptionsService surveyPquestionsOptionsService;
    @Resource
    private SurveyService surveyService;
    @Resource
    private SurveyQuestionsService surveyQuestionsService;
    @Resource
    private SurveyQuestionsOptionsService surveyQuestionsOptionsService;
    @Resource
    private SurveyPquestionsRuleService surveyPquestionsRuleService;

    @Override
    public SurveyPquestionsVO getSurveyPquestions(String id) {
        SurveyPquestions surveyPquestions = getById(id);
        if (surveyPquestions == null) {
            AssertUtil.throwMessage("问卷问题不存在");
        }
        SurveyPquestionsVO surveyPquestionsVO = BeanUtil.copyProperties(surveyPquestions, SurveyPquestionsVO.class);
        List<SurveyPquestionsOptions> surveyPquestionsOptionsList = surveyPquestionsOptionsService.list(new LambdaQueryWrapper<SurveyPquestionsOptions>().eq(SurveyPquestionsOptions::getSQid, id)
                .orderByAsc(SurveyPquestionsOptions::getSort));
        surveyPquestionsVO.setSurveyPquestionsOptionsList(new LinkedList<>(surveyPquestionsOptionsList));
        return surveyPquestionsVO;
    }

    @Transactional
    @Override
    public SurveyVO saveSurveyPquestions(SurveyPquestionsParam param) {
        SurveyPquestions surveyPquestions = BeanUtil.copyProperties(param, SurveyPquestions.class,"sort");
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题分组id不能为空");
        }
        surveyPquestions.setGid(param.getGid());
        if (surveyPquestions.getSort() == null) {
            SortHelper<SurveyPquestions> sortHelper = new SortHelper<>(this, "sort");
            surveyPquestions.setSort(sortHelper.next());
        }
        saveOrUpdate(surveyPquestions);
        surveyMapper.updateSum(param.getSid());
        surveyQgroupMapper.updateSum(param.getGid());

        List<SurveyPquestionsOptions> surveyPquestionsOptionsList = param.getSurveyPquestionsOptionsList();
        if (CollectionUtils.isNotEmpty(surveyPquestionsOptionsList)) {
            AtomicInteger index = new AtomicInteger(0);
            surveyPquestionsOptionsList.forEach(surveyPquestionsOptions -> {
                int currentIndex = index.getAndIncrement(); // 获取当前下标并递增
                surveyPquestionsOptions.setSQid(surveyPquestions.getId());
                surveyPquestionsOptions.setSid(surveyPquestions.getSid());
                if (surveyPquestionsOptions.getSort() == null) {
                    SortHelper<SurveyPquestionsOptions> sortHelper = new SortHelper<>(surveyPquestionsOptionsService, "sort");
                    surveyPquestionsOptions.setSort(sortHelper.next() + currentIndex);
                }
            });
        }

        surveyPquestionsOptionsService.saveOrUpdateBatch(surveyPquestionsOptionsList);
        return surveyService.getSurveyVO(param.getSid());
    }

    @Transactional
    @Override
    public SurveyVO saveSurveyPquestionsByQuestions(SurveyPquestionsParam param) {
        SurveyPquestions surveyPquestions = new SurveyPquestions();
        BeanUtil.copyProperties(param, surveyPquestions,"sort");
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        if (StringUtils.isEmpty(param.getQid())) {
            AssertUtil.throwMessage("问卷问题id不能为空");
        }

        List<SurveyQuestions> surveyQuestionsList = surveyQuestionsService.list(new LambdaQueryWrapper<SurveyQuestions>()
                .in(SurveyQuestions::getId, (Object[]) param.getQid().split(","))
                .orderByAsc(SurveyQuestions::getSort));
        List<SurveyPquestions> surveyPquestionsList = BeanUtil.copyToList(surveyQuestionsList, SurveyPquestions.class);
        AtomicInteger index = new AtomicInteger(1);
        surveyPquestionsList.forEach(pQuestions->{
            pQuestions.setSid(param.getSid());
            pQuestions.setGid(param.getGid());
//            pQuestions.setScore(param.getScore());
            pQuestions.setSort(index.getAndIncrement());
            pQuestions.setQid(pQuestions.getId());
            pQuestions.setId(null);
        });
        saveOrUpdateBatch(surveyPquestionsList);
        surveyMapper.updateSum(param.getSid());
        surveyQgroupMapper.updateSum(param.getGid());

        Map<String, String> pqMap = new HashMap<>();
        surveyPquestionsList.forEach(questions->{
            pqMap.put(questions.getQid(), questions.getId());
        });

        List<String> surveyQuestionsIds = surveyQuestionsList.stream().map(SurveyQuestions::getId).toList();

        List<SurveyQuestionsOptions> surveyQuestionsOptionsList = surveyQuestionsOptionsService.list(new LambdaQueryWrapper<SurveyQuestionsOptions>()
                .in(SurveyQuestionsOptions::getQid, surveyQuestionsIds));
        List<SurveyPquestionsOptions> surveyPquestionsOptionsList = BeanUtil.copyToList(surveyQuestionsOptionsList, SurveyPquestionsOptions.class);
        surveyPquestionsOptionsList.forEach(pQuestionsOptions->{
            pQuestionsOptions.setId(null);
            pQuestionsOptions.setSid(param.getSid());
            pQuestionsOptions.setSQid(pqMap.get(pQuestionsOptions.getQid()));
        });
        surveyPquestionsOptionsService.saveOrUpdateBatch(surveyPquestionsOptionsList);

        return surveyService.getSurveyVO(param.getSid());
    }

    @Transactional
    @Override
    public SurveyVO removeSurveyPquestions(SurveyPquestionsParam param) {
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        String ids = param.getId();
        surveyPquestionsOptionsMapper.deleteByQid(Arrays.asList(ids.split(",")));
        removeByIds(Arrays.asList(ids.split(",")));
        surveyMapper.updateSum(param.getSid());
        surveyQgroupMapper.updateSum(param.getGid());
        return surveyService.getSurveyVO(param.getSid());
    }

    @Transactional
    @Override
    public SurveyVO saveSurveyPquestionsRules(SurveyPquestionsRuleParam param) {
        SurveyPquestionsRule surveyPquestionsRule = new SurveyPquestionsRule();
        BeanUtil.copyProperties(param, surveyPquestionsRule);
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        surveyPquestionsRule.setGid(param.getGid());
        if (surveyPquestionsRule.getSort() == null) {
            SortHelper<SurveyPquestionsRule> sortHelper = new SortHelper<>(surveyPquestionsRuleService, "sort");
            surveyPquestionsRule.setSort(sortHelper.next());
        }
        surveyPquestionsRuleService.saveOrUpdate(surveyPquestionsRule);

        surveyMapper.updateSumByRule(param.getSid());
        surveyQgroupMapper.updateSumByRule(param.getGid());

        return surveyService.getSurveyVO(param.getSid());
    }

    @Transactional
    @Override
    public SurveyVO removeSurveyPquestionsRules(SurveyPquestionsRuleParam param) {
        if (StringUtils.isEmpty(param.getSid())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("问卷问题组id不能为空");
        }
        String ids = param.getId();
        surveyPquestionsRuleService.removeByIds(Arrays.asList(ids.split(",")));
        surveyMapper.updateSumByRule(param.getSid());
        surveyQgroupMapper.updateSumByRule(param.getGid());
        return surveyService.getSurveyVO(param.getSid());
    }
}
