<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.survey.mapper.SurveyMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, SUBJECT_SUM, WJMS, CID, BZ, CREATE_TIME, XGH, XM, STATUS, TYPE, DCFS, SFKPL, SFKNMPL, SFJF
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.subjectSum != null">
                        AND a.SUBJECT_SUM = #{param.subjectSum}
                    </if>
                    <if test="param.wjms != null">
                        AND a.WJMS LIKE concat(concat('%',#{param.wjms), '%')
                    </if>
                    <if test="param.cid != null">
                        AND a.CID LIKE concat(concat('%',#{param.cid), '%')
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE concat(concat('%',#{param.bz), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.status != null">
                        AND a.STATUS LIKE concat(concat('%',#{param.status), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.dcfs != null">
                        AND a.DCFS LIKE concat(concat('%',#{param.dcfs), '%')
                    </if>
                    <if test="param.sfkpl != null">
                        AND a.SFKPL LIKE concat(concat('%',#{param.sfkpl), '%')
                    </if>
                    <if test="param.sfknmpl != null">
                        AND a.SFKNMPL LIKE concat(concat('%',#{param.sfknmpl), '%')
                    </if>
                    <if test="param.sfjf != null">
                        AND a.SFJF LIKE concat(concat('%',#{param.sfjf), '%')
                    </if>
    </sql>
</mapper>
