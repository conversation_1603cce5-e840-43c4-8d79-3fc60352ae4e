package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyAnswer;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷答卷信息Mapper
 *
 * <AUTHOR>
 * @since 2024-12-17 10:27:32
 */
public interface SurveyAnswerMapper extends BaseMapper<SurveyAnswer> {


    @Delete("<script>" +
            "DELETE FROM SYT_SURVEY_ANSWER WHERE SID IN " +
            "<foreach item='item' index='index' collection='surveyIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    void removeBySurveyIds(@Param("surveyIds") List<String> surveyIds);
}
