package com.sanythadmin.project.survey.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 问卷题库问题分类
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SURVEY_QUESTIONS_CLASS")
@Entity
@Table(name = "SYT_SURVEY_QUESTIONS_CLASS")
public class SurveyQuestionsClass implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 分类名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 拍学
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 备注
     */
    @Column(name = "BZ", columnDefinition = ColumnType.VARCHAR2_1024)
    @TableField("BZ")
    private String bz;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 分类
     */
    @Column(name = "category")
    @TableField("category")
    private String category;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
