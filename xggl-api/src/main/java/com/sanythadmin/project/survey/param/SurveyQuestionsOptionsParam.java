package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷题库问题选项查询参数
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyQuestionsOptionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 选项编号
     */
    @QueryField(type = QueryType.EQ)
    private String code;

    /**
     * 选项名称
     */
    private String name;

    /**
     * 试题ID
     */
    @QueryField(type = QueryType.EQ)
    private String qid;

    /**
     * 分数
     */
    @QueryField(ignore = true)
    private Double score;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
