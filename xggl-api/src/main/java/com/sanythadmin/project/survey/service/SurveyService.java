package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.BaseSurveyAnswer;
import com.sanythadmin.project.survey.entity.BaseSurveyAnswerInfo;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.vo.SurveyVO;

import java.util.List;

/**
 * 问卷管理Service
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
public interface SurveyService extends IService<Survey> {

    SurveyVO getSurveyVO(String id);

    SurveyVO getSurveyVO(String id,SurveyAnswerInfo surveyAnswerInfo);

    <T extends BaseSurveyAnswerInfo, U extends BaseSurveyAnswer> SurveyVO getSurveyVO(String id, T surveyAnswerInfo, Class<U> uClass);

    void removePaperGroupQuestions(List<String> ids);
    void removeSurvey(List<String> ids);

    Survey saveSurvey(SurveyParam param) throws Exception;

    PageResult<Survey> querySurveys(SurveyParam param);
}
