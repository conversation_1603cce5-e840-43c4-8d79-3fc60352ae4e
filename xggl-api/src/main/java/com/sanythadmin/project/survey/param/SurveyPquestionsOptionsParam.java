package com.sanythadmin.project.survey.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 问卷试题选项查询参数
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SurveyPquestionsOptionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 选项编码
     */
    @QueryField(type = QueryType.EQ)
    private String code;

    /**
     * 选项名称
     */
    private String name;

    /**
     * 问题ID
     */
    @QueryField(type = QueryType.EQ)
    private String sQid;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

    /**
     * 分数
     */
    private Double score;

    /**
     * 问卷id
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 分组ID
     */
    @QueryField(type = QueryType.EQ)
    private String gid;

}
