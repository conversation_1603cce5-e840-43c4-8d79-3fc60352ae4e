package com.sanythadmin.project.survey.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.survey.entity.SurveyPquestionsRule;
import com.sanythadmin.project.survey.param.SurveyPquestionsRuleParam;
import com.sanythadmin.project.survey.vo.SurveyVO;

import java.util.List;

/**
 * 问卷试题抽题规则Service
 *
 * <AUTHOR>
 * @since 2024-12-12 16:33:48
 */
public interface SurveyPquestionsRuleService extends IService<SurveyPquestionsRule> {

    SurveyVO saveSurveyPquestionsRule(SurveyPquestionsRuleParam param);

    SurveyVO saveBatchExamPaperQuestionsRule(List<SurveyPquestionsRuleParam> param);

    SurveyVO removeSurveyPquestionsRule(SurveyPquestionsRuleParam param);
}
