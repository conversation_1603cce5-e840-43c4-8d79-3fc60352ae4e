package com.sanythadmin.project.survey.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

public enum SurveyType implements TextBaseEnum {
    GDXT("固定选题"),
    SJCT("随机抽题")
    ;

    @EnumValue
    @JsonValue
    private final String text;

    SurveyType(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    public static SurveyType get(String text) {
        for (SurveyType surveyType : SurveyType.values()) {
            boolean equals = surveyType.getText().equals(text);
            if (equals) {
                return surveyType;
            }
        }
        return null;
    }
}
