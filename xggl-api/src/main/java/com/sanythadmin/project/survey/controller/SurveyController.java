package com.sanythadmin.project.survey.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.Survey;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.service.SurveyAnswerInfoService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷管理控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 16:38:45
 */
@RestController
@RequestMapping("/api/survey/survey")
public class SurveyController extends BaseController {
    @Resource
    private SurveyService surveyService;
    @Resource
    private SurveyAnswerInfoService surveyAnswerInfoService;

    /**
     * 分页查询问卷管理（权限标识：survey:survey:list）
     */
    @PreAuthorize("hasAuthority('survey:survey:list')")
    @GetMapping("/page")
    public PageResult<Survey> page(SurveyParam param) {
        PageParam<Survey, SurveyParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_TIME desc");
        page = surveyService.page(page, page.getWrapper());
        page.getRecords().forEach(survey -> {
            long count = surveyAnswerInfoService.count(new LambdaQueryWrapper<SurveyAnswerInfo>().eq(SurveyAnswerInfo::getSid, survey.getId()));
            survey.setAnswered(count > 0);
        });
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷管理（权限标识：survey:survey:list）
     */
    @PreAuthorize("hasAuthority('survey:survey:list')")
    @GetMapping()
    public List<Survey> list(SurveyParam param) {
        PageParam<Survey, SurveyParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_TIME desc");
        return surveyService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷管理（权限标识：survey:survey:list）
     */
    @PreAuthorize("hasAuthority('survey:survey:list')")
    @GetMapping("/{id}")
    public SurveyVO get(@PathVariable("id") String id) {
        return surveyService.getSurveyVO(id);
    }

    /**
     * 获取问卷答卷详情（权限标识：survey:survey:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/getSurveyVO")
    public SurveyVO getExamVO(SurveyAnswerInfo surveyAnswerInfo) {
        if (!StringUtils.hasLength(surveyAnswerInfo.getId())) {
            AssertUtil.throwMessage("答卷信息id不能为空");
        }
        return surveyService.getSurveyVO(surveyAnswerInfo.getSid(),surveyAnswerInfo);
    }

    /**
     * 添加或修改问卷管理（权限标识：survey:survey:operation）
     */
    @PreAuthorize("hasAuthority('survey:survey:operation')")
    @OperationLog(module = "问卷管理", comments = "保存问卷管理")
    @PostMapping("/operation")
    public Survey save(@RequestBody SurveyParam param) throws Exception {
        return surveyService.saveSurvey(param);
    }

    /**
     * 批量删除问卷管理（权限标识：survey:survey:remove）
     */
    @PreAuthorize("hasAuthority('survey:survey:remove')")
    @OperationLog(module = "问卷管理", comments = "批量删除问卷管理")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyService.removePaperGroupQuestions(ids);
    }

    /**
     * 问卷状态(停用/启用)（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "问卷管理", comments = "问卷状态")
    @PostMapping("/state")
    public void sendPaper(@RequestBody Survey survey) {
        if (!StringUtils.hasLength(survey.getId())) {
            AssertUtil.throwMessage("问卷id不能为空");
        }
        if (survey.getStatus() == null) {
            AssertUtil.throwMessage("问卷状态不能为空");
        }
        Survey byId = surveyService.getById(survey.getId());
        if (byId == null) {
            AssertUtil.throwMessage("未找到问卷");
        }
        byId.setStatus(survey.getStatus());
        surveyService.saveOrUpdate(byId);
    }

    /**
     * 答卷人分页查询可答问卷（权限标识：survey:surveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyAnswer:list')")
    @GetMapping("/querySurveys")
    public PageResult<Survey> querySurveys(SurveyParam param) {
        return surveyService.querySurveys(param);
    }
}
