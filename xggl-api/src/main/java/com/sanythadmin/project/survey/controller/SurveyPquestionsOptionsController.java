package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyPquestionsOptions;
import com.sanythadmin.project.survey.param.SurveyPquestionsOptionsParam;
import com.sanythadmin.project.survey.service.SurveyPquestionsOptionsService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/问卷试题选项控制器
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
@RestController
@RequestMapping("/api/survey/survey-pquestions-options")
public class SurveyPquestionsOptionsController extends BaseController {
    @Resource
    private SurveyPquestionsOptionsService surveyPquestionsOptionsService;

    /**
     * 分页查询问卷试题选项（权限标识：survey:surveyPquestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsOptions:list')")
    @GetMapping("/page")
    public PageResult<SurveyPquestionsOptions> page(SurveyPquestionsOptionsParam param) {
        PageParam<SurveyPquestionsOptions, SurveyPquestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyPquestionsOptionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷试题选项（权限标识：survey:surveyPquestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsOptions:list')")
    @GetMapping()
    public List<SurveyPquestionsOptions> list(SurveyPquestionsOptionsParam param) {
        PageParam<SurveyPquestionsOptions, SurveyPquestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyPquestionsOptionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷试题选项（权限标识：survey:surveyPquestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsOptions:list')")
    @GetMapping("/{id}")
    public SurveyPquestionsOptions get(@PathVariable("id") String id) {
        return surveyPquestionsOptionsService.getById(id);
    }

    /**
     * 添加或修改问卷试题选项（权限标识：survey:surveyPquestionsOptions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsOptions:operation')")
    @OperationLog(module = "问卷试题选项", comments = "保存问卷试题选项")
    @PostMapping("/operation")
    public void save(@RequestBody SurveyPquestionsOptions surveyPquestionsOptions) {
        if (StringUtils.hasLength(surveyPquestionsOptions.getId())) {
            surveyPquestionsOptionsService.updateById(surveyPquestionsOptions);
        } else {
            if (surveyPquestionsOptions.getSort() == null) {
                SortHelper<SurveyPquestionsOptions> sortHelper = new SortHelper<>(surveyPquestionsOptionsService, "sort");
                surveyPquestionsOptions.setSort(sortHelper.next());
            }
            surveyPquestionsOptionsService.save(surveyPquestionsOptions);
        }
    }

    /**
     * 批量删除问卷试题选项（权限标识：survey:surveyPquestionsOptions:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyPquestionsOptions:remove')")
    @OperationLog(module = "问卷试题选项", comments = "批量删除问卷试题选项")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyPquestionsOptionsService.removeByIds(ids);
    }
}
