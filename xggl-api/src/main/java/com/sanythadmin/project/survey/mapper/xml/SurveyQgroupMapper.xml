<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.survey.mapper.SurveyQgroupMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SID, NAME, Q_SUM, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.qSum != null">
                        AND a.Q_SUM = #{param.qSum}
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
