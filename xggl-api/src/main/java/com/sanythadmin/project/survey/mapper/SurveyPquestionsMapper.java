package com.sanythadmin.project.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.survey.entity.SurveyPquestions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问卷问题Mapper
 *
 * <AUTHOR>
 * @since 2024-12-12 16:22:59
 */
public interface SurveyPquestionsMapper extends BaseMapper<SurveyPquestions> {


    @Delete("<script>" +
            "delete from SYT_SURVEY_PQUESTIONS where sid in "+
            "<foreach collection=\"sids\" item=\"sid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{sid}" +
            "</foreach>"+
            "</script>")
    void deleteBySid(@Param("sids") List<String> sids);

    @Delete("<script>" +
            "delete from SYT_SURVEY_PQUESTIONS where gid in "+
            "<foreach collection=\"gids\" item=\"gid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{gid}" +
            "</foreach>"+
            "</script>")
    void deleteByGid(@Param("gids") List<String> gids);
}
