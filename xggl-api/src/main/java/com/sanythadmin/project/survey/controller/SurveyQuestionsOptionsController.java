package com.sanythadmin.project.survey.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.survey.entity.SurveyQuestionsOptions;
import com.sanythadmin.project.survey.param.SurveyQuestionsOptionsParam;
import com.sanythadmin.project.survey.service.SurveyQuestionsOptionsService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 问卷调查/题库问题选项控制器
 *
 * <AUTHOR>
 * @since 2024-12-11 14:01:22
 */
@RestController
@RequestMapping("/api/survey/survey-questions-options")
public class SurveyQuestionsOptionsController extends BaseController {
    @Resource
    private SurveyQuestionsOptionsService surveyQuestionsOptionsService;

    /**
     * 分页查询问卷题库问题选项（权限标识：survey:surveyQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsOptions:list')")
    @GetMapping("/page")
    public PageResult<SurveyQuestionsOptions> page(SurveyQuestionsOptionsParam param) {
        PageParam<SurveyQuestionsOptions, SurveyQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = surveyQuestionsOptionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部问卷题库问题选项（权限标识：survey:surveyQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsOptions:list')")
    @GetMapping()
    public List<SurveyQuestionsOptions> list(SurveyQuestionsOptionsParam param) {
        PageParam<SurveyQuestionsOptions, SurveyQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return surveyQuestionsOptionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询问卷题库问题选项（权限标识：survey:surveyQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsOptions:list')")
    @GetMapping("/{id}")
    public SurveyQuestionsOptions get(@PathVariable("id") String id) {
        return surveyQuestionsOptionsService.getById(id);
    }

    /**
     * 添加或修改问卷题库问题选项（权限标识：survey:surveyQuestionsOptions:operation）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsOptions:operation')")
    @OperationLog(module = "问卷题库问题选项", comments = "保存问卷题库问题选项")
    @PostMapping("/operation")
    public void save(@RequestBody SurveyQuestionsOptions surveyQuestionsOptions) {
        if (StringUtils.hasLength(surveyQuestionsOptions.getId())) {
            surveyQuestionsOptionsService.updateById(surveyQuestionsOptions);
        } else {
            surveyQuestionsOptionsService.save(surveyQuestionsOptions);
        }
    }

    /**
     * 批量删除问卷题库问题选项（权限标识：survey:surveyQuestionsOptions:remove）
     */
    @PreAuthorize("hasAuthority('survey:surveyQuestionsOptions:remove')")
    @OperationLog(module = "问卷题库问题选项", comments = "批量删除问卷题库问题选项")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        surveyQuestionsOptionsService.removeByIds(ids);
    }
}
