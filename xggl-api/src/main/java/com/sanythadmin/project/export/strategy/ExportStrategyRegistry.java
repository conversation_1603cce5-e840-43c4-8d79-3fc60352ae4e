package com.sanythadmin.project.export.strategy;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ExportStrategyRegistry {
    private final Map<String, ExportStrategy> strategies = new ConcurrentHashMap<>();


    public void register(ExportStrategy strategy) {
        strategies.put(strategy.getSupportedBusinessType(), strategy);
    }


    public ExportStrategy getStrategy(String businessType) {
        ExportStrategy strategy = strategies.get(businessType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的业务类型导出: " + businessType);
        }
        return strategy;
    }
}