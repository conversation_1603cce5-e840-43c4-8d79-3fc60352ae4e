package com.sanythadmin.project.export.strategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.project.export.enums.ExportType;
import com.sanythadmin.project.export.param.ExportParams;
import com.sanythadmin.project.survey.entity.SurveyAnswerInfo;
import com.sanythadmin.project.survey.param.SurveyAnswerParam;
import com.sanythadmin.project.survey.service.SurveyStatisticsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Component
public class SurveyExportStrategy implements ExportStrategy, InitializingBean {
    @Autowired
    private SurveyStatisticsService surveyStatisticsService;

    @Autowired
    private ExportStrategyRegistry strategyRegistry;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 在Spring bean初始化后自动注册策略
        strategyRegistry.register(this);
    }

    @Override
    public Map<String, Object> export(Object param, ExportType exportType) throws IOException {
        // 强制类型转换
        if (!(param instanceof ExportParams)) {
            throw new IllegalArgumentException("参数类型错误");
        }
        ExportParams exportParams = (ExportParams) param;
        SurveyAnswerParam surveyParam = (SurveyAnswerParam) exportParams.getQueryParams();
        return surveyStatisticsService.exportSurveyAnswers(surveyParam, exportType);
    }


    @Override
    public String getSupportedBusinessType() {
        return "survey"; // 与ExportTask中的businessType对应
    }


    @Override
    public Long countTotalRecords(Object param) {
        if (!(param instanceof ExportParams)) {
            throw new IllegalArgumentException("参数类型错误");
        }
        ExportParams exportParams = (ExportParams) param;
        SurveyAnswerParam surveyParam = (SurveyAnswerParam) exportParams.getQueryParams();
        return surveyStatisticsService.count(
            new LambdaQueryWrapper<SurveyAnswerInfo>()
                .eq(SurveyAnswerInfo::getSid, surveyParam.getSid())
        );
    }


}