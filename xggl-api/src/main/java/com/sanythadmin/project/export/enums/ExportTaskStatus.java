package com.sanythadmin.project.export.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

public enum ExportTaskStatus implements TextBaseEnum {
    PENDING("待处理"),
    PROCESSING("处理中"),
    COMPLETED("完成"),
    FAILED("失败")
    ;
    /**
     * 名称
     */
    @EnumValue
    @JsonValue
    private final String text;

    ExportTaskStatus(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

}