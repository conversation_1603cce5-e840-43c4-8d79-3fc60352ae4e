package com.sanythadmin.project.export.entity;

import com.sanythadmin.project.export.enums.ExportTaskStatus;
import com.sanythadmin.project.export.enums.ExportType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 导出任务
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@Data
@Document(collection = "SYT_EXPORT_TASKS")
public class ExportTask implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    private String id;
    private String userId;
    private String taskName;
    private String businessType; // 业务类型
    private ExportType exportType; // 导出类型
    private ExportTaskStatus status;
    private String fileId; // MongoDB文件ID
    private String fileName;
    private Long totalRecords;
    private Long processedRecords;
    private String errorMessage;
    private Date createTime;
    private Date expireTime; // 过期时间
}