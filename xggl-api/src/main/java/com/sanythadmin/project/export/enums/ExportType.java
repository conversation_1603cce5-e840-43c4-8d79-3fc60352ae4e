package com.sanythadmin.project.export.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导出类型枚举
 */
@Getter
@AllArgsConstructor
public enum ExportType implements TextBaseEnum {
    
    WORD("word", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".docx"),
    EXCEL("excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".xlsx");

    /**
     * 类型名称
     */
    @EnumValue
    @JsonValue
    private final String name;
    
    /**
     * 内容类型
     */
    private final String contentType;
    
    /**
     * 文件后缀
     */
    private final String suffix;

    /**
     * 根据名称获取导出类型
     */
    public static ExportType getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        
        for (ExportType type : ExportType.values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 是否支持该导出类型
     */
    public static boolean isSupported(String name) {
        return getByName(name) != null;
    }

    /**
     * 获取所有支持的导出类型名称
     */
    public static List<String> getSupportedTypes() {
        return Arrays.stream(ExportType.values())
                .map(ExportType::getName)
                .collect(Collectors.toList());
    }

    @Override
    public String getText() {
        return name;
    }
}
