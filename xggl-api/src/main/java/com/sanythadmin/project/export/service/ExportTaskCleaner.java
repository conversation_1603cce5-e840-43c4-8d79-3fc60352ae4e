package com.sanythadmin.project.export.service;

import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.repository.ExportTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class ExportTaskCleaner {
    @Autowired
    private ExportTaskRepository exportTaskRepository;
    
    @Autowired
    private GridFsTemplate gridFsTemplate;


    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void cleanExpiredTasks() {
        Date now = new Date();
        List<ExportTask> expiredTasks = exportTaskRepository.findByExpireTimeLessThan(now);
        
        for (ExportTask task : expiredTasks) {
            // 删除MongoDB中的文件
            if (StringUtils.isNotBlank(task.getFileId())) {
                gridFsTemplate.delete(new Query(Criteria.where("_id").is(task.getFileId())));
            }
            
            // 删除任务记录
            exportTaskRepository.delete(task);
            
            log.info("清理过期导出任务：{}", task.getId());
        }
    }
}