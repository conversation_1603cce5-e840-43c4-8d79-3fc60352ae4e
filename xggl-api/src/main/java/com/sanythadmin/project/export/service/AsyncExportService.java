package com.sanythadmin.project.export.service;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.enums.ExportTaskStatus;
import com.sanythadmin.project.export.event.ExportEvent;
import com.sanythadmin.project.export.param.ExportParams;
import com.sanythadmin.project.export.repository.ExportTaskRepository;
import com.sanythadmin.project.export.strategy.ExportStrategy;
import com.sanythadmin.project.export.strategy.ExportStrategyRegistry;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;

@Service
@Slf4j
public class AsyncExportService {
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private ExportTaskRepository exportTaskRepository;
    @Autowired
    private GridFsTemplate gridFsTemplate;
    @Autowired
    private ExportStrategyRegistry strategyRegistry;

    public ExportTask createExportTask(ExportParams params){
        ExportTask task = new ExportTask();
        task.setUserId(getAccount().getUsername());
        task.setTaskName(params.getTaskName());
        task.setBusinessType(params.getBusinessType());
        task.setExportType(params.getExportType());
        task.setStatus(ExportTaskStatus.PENDING);
        task.setCreateTime(new Date());
        task.setExpireTime(params.getExpireTime() == null ? calculateExpireTime() : params.getExpireTime());
        task.setTotalRecords(params.getTotalRecords());
        task = exportTaskRepository.save(task);
        ExportEvent event = new ExportEvent();
        event.setTaskId(task.getId());
        event.setBusinessType(params.getBusinessType());
        event.setExportParams(params);
        eventPublisher.publishEvent(event);
        return task;
    }

    private Date calculateExpireTime() {
        // 默认1个月后过期
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireDateTime = now.plusMonths(1);
        return Date.from(expireDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Async
    @EventListener
    public void handleExportEvent(ExportEvent event) {
        ExportStrategy strategy = strategyRegistry.getStrategy(event.getBusinessType());

        ExportTask task = exportTaskRepository.findById(event.getTaskId())
                .orElseThrow(() -> new BusinessException("任务不存在"));
        try {
            // 更新任务状态为处理中
            task.setStatus(ExportTaskStatus.PROCESSING);
            task.setTotalRecords(strategy.countTotalRecords(event.getExportParams()));
            exportTaskRepository.save(task);

            // 执行导出
            Map<String, Object> exportMap = strategy.export(
                    event.getExportParams(),
                    task.getExportType()
            );
            String fileName = (String) exportMap.get("fileName");
            String contentType = (String) exportMap.get("contentType");
            byte[] exportContent = (byte[]) exportMap.get("fileContent");

                    // 将文件存储到MongoDB
            ObjectId fileId = storeFileToMongoDB(exportContent, fileName,contentType);

            // 更新任务状态
            task.setStatus(ExportTaskStatus.COMPLETED);
            task.setFileId(fileId.toString());
            task.setFileName(fileName);
            exportTaskRepository.save(task);

        } catch (Exception e) {
            // 处理失败
            task.setStatus(ExportTaskStatus.FAILED);
            task.setErrorMessage(e.getMessage());
            exportTaskRepository.save(task);
            log.error("导出任务失败", e);
        }
    }

    private ObjectId storeFileToMongoDB(byte[] content,String fileName,String contentType) {
        return gridFsTemplate.store(new ByteArrayInputStream(content),fileName,contentType);
    }

}
