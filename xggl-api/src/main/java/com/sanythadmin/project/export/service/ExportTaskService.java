package com.sanythadmin.project.export.service;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.param.ExportTaskParam;
import jakarta.annotation.Resource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.regex.Pattern;

@Service
public class ExportTaskService {

    @Resource
    private MongoTemplate mongoTemplate;

    public PageResult<ExportTask> queryPage(ExportTaskParam param) {
        Query query = getQuery(param);
        long count = mongoTemplate.count(query, ExportTask.class);
        Pageable pageable = PageRequest.of(param.getPage().intValue() - 1, param.getLimit().intValue(),
                Sort.by(Sort.Order.desc("createTime")));
        query.with(pageable);
        List<ExportTask> list = mongoTemplate.find(query, ExportTask.class);
        return new PageResult(list, count);
    }

    public void remove(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, ExportTask.class);
        }
    }

    private Query getQuery(ExportTaskParam param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(param.getId())) {
            criteria.and("id").is(param.getId());
        }
        if (StringUtils.hasLength(param.getUserId())) {
            criteria.and("userId").is(param.getUserId());
        }
        if (StringUtils.hasLength(param.getBusinessType())) {
            criteria.and("businessType").is(param.getBusinessType());
        }
        if (param.getStatus() != null) {
            criteria.and("status").is(param.getStatus());
        }
        if (StringUtils.hasLength(param.getTaskName())) {
            Pattern pattern = Pattern.compile("^.*" + param.getTaskName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("taskName").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
