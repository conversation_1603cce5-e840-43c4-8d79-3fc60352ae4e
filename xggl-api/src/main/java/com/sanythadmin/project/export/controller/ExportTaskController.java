package com.sanythadmin.project.export.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.enums.ExportTaskStatus;
import com.sanythadmin.project.export.param.ExportParams;
import com.sanythadmin.project.export.param.ExportTaskParam;
import com.sanythadmin.project.export.repository.ExportTaskRepository;
import com.sanythadmin.project.export.service.AsyncExportService;
import com.sanythadmin.project.export.service.ExportTaskService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;

/**
 * 文件导出任务
 *
 * <AUTHOR>
 * @date 2025/01/07
 */
@RestController
@RequestMapping("/api/system/export-task")
public class ExportTaskController {

    @Autowired
    private ExportTaskRepository exportTaskRepository;
    @Autowired
    private GridFsService gridFsService;
    @Autowired
    private AsyncExportService asyncExportService;
    @Autowired
    private ExportTaskService exportTaskService;

    /**
     * 分页查询自己的导出任务
     * 权限标识：sys:export-task:list
     */
    @PreAuthorize("hasAuthority('sys:export-task:list')")
    @OperationLog(module = "文件导出任务管理", comments = "批量删除")
    @GetMapping("/page")
    public PageResult<ExportTask> page(ExportTaskParam param) {
        param.setUserId(getAccount().getUsername());
        return exportTaskService.queryPage(param);
    }

    /**
     * 批量删除导出任务
     * 权限标识：sys:export-task:remove
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('sys:export-task:remove')")
    @OperationLog(module = "文件导出任务管理", comments = "批量删除")
    @PostMapping("/removeBatch")
    public void removeBatch(@RequestBody List<String> ids) {
        exportTaskService.remove(ids);
    }

    /**
     * 创建导出任务
     *
     * @param params 参数
     * @return {@link ExportTask }
     */
    @PostMapping("/create")
    public ExportTask createExportTask(@RequestBody ExportParams params) {
        return asyncExportService.createExportTask(params);
    }

    /**
     * 下载导出文件
     * 权限标识：sys:export-task:download
     * @param taskId 任务id
     * @return {@link ResponseEntity }<{@link GridFsResource }>
     */
    @PreAuthorize("hasAuthority('sys:export-task:download')")
    @OperationLog(module = "文件导出任务管理", comments = "下载导出文件")
    @GetMapping("/download/{taskId}")
    public void downloadExportFile(@PathVariable String taskId, HttpServletResponse response) throws IOException {
        ExportTask task = exportTaskRepository.findById(taskId)
            .orElseThrow(() -> new BusinessException("任务不存在"));

        if (task.getStatus() != ExportTaskStatus.COMPLETED) {
            throw new BusinessException("文件未生成完成");
        }
        // 检查过期时间
        if (task.getExpireTime() != null && task.getExpireTime().before(new Date())) {
            throw new BusinessException("文件已过期");
        }
        GridFsResource resource = gridFsService.get(task.getFileId());
        byte[] content = resource.getContentAsByteArray();
        String fileName = URLEncoder.encode(task.getFileName(), "UTF-8");
        response.setContentType(resource.getContentType());
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setContentLength(content.length);
        ServletOutputStream outputStream = response.getOutputStream();
        outputStream.write(content);
        outputStream.flush();
    }

}