package com.sanythadmin.project.export.strategy;

import com.sanythadmin.project.export.enums.ExportType;

import java.io.IOException;
import java.util.Map;

public interface ExportStrategy {
    /**
     * 执行具体的导出逻辑
     *
     * @param param      导出参数
     * @param exportType 导出类型
     * @return 导出的字节数组
     * @throws IOException 导出异常
     */
    Map<String, Object> export(Object param, ExportType exportType) throws IOException;


    /**
     * 获取支持的业务类型
     * @return 业务类型标识
     */
    String getSupportedBusinessType();


    /**
     * 计算总记录数
     * @param param 导出参数
     * @return 总记录数
     */
    Long countTotalRecords(Object param);
}