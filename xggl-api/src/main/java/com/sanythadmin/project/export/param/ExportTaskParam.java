package com.sanythadmin.project.export.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.export.enums.ExportTaskStatus;
import com.sanythadmin.project.export.enums.ExportType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExportTaskParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String id;
    private String userId;
    private String taskName;
    private String businessType; // 业务类型
    private ExportType exportType; // 导出类型
    private ExportTaskStatus status;
    private String fileId; // MongoDB文件ID
    private String fileName;
    private Long totalRecords;
    private Long processedRecords;
    private String errorMessage;
    private Date createTime;
    private Date expireTime; // 过期时间
}
