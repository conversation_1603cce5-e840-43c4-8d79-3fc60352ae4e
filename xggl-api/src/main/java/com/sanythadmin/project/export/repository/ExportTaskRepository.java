package com.sanythadmin.project.export.repository;

import com.sanythadmin.project.export.entity.ExportTask;
import com.sanythadmin.project.export.enums.ExportTaskStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ExportTaskRepository extends MongoRepository<ExportTask, String> {
    List<ExportTask> findByUserIdAndStatus(String userId, ExportTaskStatus status);

    List<ExportTask> findByExpireTimeLessThan(Date now);

    List<ExportTask> findByUserId(String username);

}