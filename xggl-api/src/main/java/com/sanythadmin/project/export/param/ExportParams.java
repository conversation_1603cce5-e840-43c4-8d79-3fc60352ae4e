package com.sanythadmin.project.export.param;

import com.sanythadmin.project.export.enums.ExportType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportParams implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    @NotBlank(message = "导出任务名称不能为空")
    private String taskName;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 导出类型
     */
    @NotNull(message = "导出类型不能为空")
    private ExportType exportType;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 过期时间
     */
    private Date expireTime;

    private Object queryParams;

}
