package com.sanythadmin.project.exam.vo;

import lombok.Data;

/**
 * 考试统计
 *
 * <AUTHOR>
 * @date 2024/11/05
 */
@Data
public class ExamStatisticsVO {
    private String examId;          // 考试ID
    private String examName;        // 考试名称
    private String xyid;         //分组ID
    private String xymc;       //分组名称
    private Integer shouldAttendCount;  // 应考人数
    private Integer actualAttendCount;  // 实际参考人数
    private Integer absentCount;        // 缺考人数
    private Integer passCount;          // 及格人数
    private Integer failCount;          // 不及格人数
    private Double passRate;            // 及格率
    private Double highestScore;        // 最高分
    private Double lowestScore;         // 最低分
    private Double averageScore;        // 平均分
}
