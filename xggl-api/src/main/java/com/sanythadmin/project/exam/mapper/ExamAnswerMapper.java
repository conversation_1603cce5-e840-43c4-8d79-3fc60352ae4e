package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamAnswer;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 答卷答案Mapper
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
public interface ExamAnswerMapper extends BaseMapper<ExamAnswer> {


    @Select("select CONTENT_ID from SYT_EXAM_ANSWER where EXAM_ID=#{examId} and XGH=${xgh} and kscs=${kscs}")
    List<String> getQidList(@Param("examId") String examId,@Param("xgh") String xgh,@Param("kscs") Integer kscs);

    @Delete("<script>" +
            "DELETE FROM SYT_EXAM_ANSWER WHERE exam_id IN " +
            "<foreach item='item' index='index' collection='examIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    void removeByExamIds(@Param("examIds") List<String> examIds);
}
