package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.UserInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_ANSWER_INFO")
@Entity
@Table(name = "SYT_EXAM_ANSWER_INFO")
public class ExamAnswerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Column(name = "EXAM_ID")
    @TableField("EXAM_ID")
    private String examId;

    /**
     * 试卷id
     */
    @Column(name = "PAPER_ID")
    @TableField("PAPER_ID")
    private String paperId;

    /**
     * 考试用时
     */
    @Column(name = "USED_TIME",columnDefinition = ColumnType.NUMBER_10)
    @TableField(value = "USED_TIME",jdbcType = JdbcType.NUMERIC)
    private Long usedTime;

    /**
     * 答对题
     */
    @Column(name = "DDT")
    @TableField("DDT")
    private Integer ddt;

    /**
     * 答错数
     */
    @Column(name = "DCS")
    @TableField("DCS")
    private Integer dcs;

    /**
     * 考试得分
     */
    @Column(name = "SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 是否合格
     */
    @Column(name = "SFHG")
    @TableField("SFHG")
    private String sfhg;

    /**
     * 考试时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 答题者用户名
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 考试次数
     */
    @Column(name = "KSCS")
    @TableField("KSCS")
    private Integer kscs;
    /**
     * 阅卷状态(待阅卷.已阅卷)
     */
    @Column(name = "STATUS")
    @TableField("STATUS")
    private String status;

    /**
     * 考试用时
     */
    @Transient
    @TableField(exist = false)
    private String ksys;

    public String getKsys() {
        return usedTime != null ? DateUtil.formatDuration(usedTime) : "";
    }

    /**
     * 用户信息
     */
    @Transient
    @TableField(exist = false)
    private UserInfo userInfo;

    /**
     * 排名
     */
    @Transient
    @TableField(exist = false)
    private Integer rank;


}
