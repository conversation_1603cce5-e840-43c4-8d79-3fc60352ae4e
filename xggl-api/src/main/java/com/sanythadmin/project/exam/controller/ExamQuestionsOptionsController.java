package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamQuestionsOptions;
import com.sanythadmin.project.exam.param.ExamQuestionsOptionsParam;
import com.sanythadmin.project.exam.service.ExamQuestionsOptionsService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/试题选项控制器
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@RestController
@RequestMapping("/api/exam/exam-questions-options")
public class ExamQuestionsOptionsController extends BaseController {
    @Resource
    private ExamQuestionsOptionsService examQuestionsOptionsService;

    /**
     * 分页查询试题选项（权限标识：exam:examQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsOptions:list')")
    @GetMapping("/page")
    public PageResult<ExamQuestionsOptions> page(ExamQuestionsOptionsParam param) {
        PageParam<ExamQuestionsOptions, ExamQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examQuestionsOptionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试题选项（权限标识：exam:examQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsOptions:list')")
    @GetMapping()
    public List<ExamQuestionsOptions> list(ExamQuestionsOptionsParam param) {
        PageParam<ExamQuestionsOptions, ExamQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examQuestionsOptionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试题选项（权限标识：exam:examQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsOptions:list')")
    @GetMapping("/{id}")
    public ExamQuestionsOptions get(@PathVariable("id") String id) {
        return examQuestionsOptionsService.getById(id);
    }

    /**
     * 添加或修改试题选项（权限标识：exam:examQuestionsOptions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsOptions:operation')")
    @OperationLog(module = "试题选项", comments = "保存试题选项")
    @PostMapping("/operation")
    public void save(@RequestBody ExamQuestionsOptions examQuestionsOptions) {
        if (StringUtils.hasLength(examQuestionsOptions.getId())) {
            examQuestionsOptionsService.updateById(examQuestionsOptions);
        } else {
            if (examQuestionsOptions.getSort() == null) {
                SortHelper<ExamQuestionsOptions> sortHelper = new SortHelper<>(examQuestionsOptionsService, "sort");
                examQuestionsOptions.setSort(sortHelper.next());
            }
            examQuestionsOptionsService.save(examQuestionsOptions);
        }
    }

    /**
     * 批量删除试题选项（权限标识：exam:examQuestionsOptions:remove）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsOptions:remove')")
    @OperationLog(module = "试题选项", comments = "批量删除试题选项")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examQuestionsOptionsService.removeByIds(ids);
    }
}
