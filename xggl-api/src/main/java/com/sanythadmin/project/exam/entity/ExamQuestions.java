package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 试题
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_QUESTIONS")
@Entity
@Table(name = "SYT_EXAM_QUESTIONS")
public class ExamQuestions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试题名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试题类型
     */
    @Column(name = "STTYPE")
    @TableField("STTYPE")
    private String sttype;

    /**
     * 试题分类ID
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 打分最大分值
     */
    @Column(name = "MAX_SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MAX_SCORE")
    private Double maxScore;

    /**
     * 解析
     */
    @Column(name = "ANALYSIS")
    @TableField("ANALYSIS")
    private String analysis;

    /**
     * 答案
     */
    @Column(name = "ANSWER")
    @TableField("ANSWER")
    private String answer;

    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 分值
     */
    @Column(name = "SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 分组id(传值给ExamPaperQuestions)
     */
    @Transient
    @TableField(exist = false)
    private String gid;
    /**
     * 分组(传值给ExamPaperQuestions)
     */
    @Transient
    @TableField(exist = false)
    private String gname;

    /**
     * 题库试题ID(传值给ExamPaperQuestions)
     */
    @Transient
    @TableField(exist = false)
    private String qid;

}
