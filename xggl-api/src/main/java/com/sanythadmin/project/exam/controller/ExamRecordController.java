package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamRecord;
import com.sanythadmin.project.exam.param.ExamRecordParam;
import com.sanythadmin.project.exam.service.ExamRecordService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/考试记录控制器
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@RestController
@RequestMapping("/api/exam/exam-record")
public class ExamRecordController extends BaseController {
    @Resource
    private ExamRecordService examRecordService;

    /**
     * 分页查询考试记录（权限标识：exam:examRecord:list）
     */
    @PreAuthorize("hasAuthority('exam:examRecord:list')")
    @GetMapping("/page")
    public PageResult<ExamRecord> page(ExamRecordParam param) {
        PageParam<ExamRecord, ExamRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examRecordService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部考试记录（权限标识：exam:examRecord:list）
     */
    @PreAuthorize("hasAuthority('exam:examRecord:list')")
    @GetMapping()
    public List<ExamRecord> list(ExamRecordParam param) {
        PageParam<ExamRecord, ExamRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examRecordService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询考试记录（权限标识：exam:examRecord:list）
     */
    @PreAuthorize("hasAuthority('exam:examRecord:list')")
    @GetMapping("/{id}")
    public ExamRecord get(@PathVariable("id") String id) {
        return examRecordService.getById(id);
    }

    /**
     * 添加或修改考试记录（权限标识：exam:examRecord:operation）
     */
    @PreAuthorize("hasAuthority('exam:examRecord:operation')")
    @OperationLog(module = "考试记录", comments = "保存考试记录")
    @PostMapping("/operation")
    public void save(@RequestBody ExamRecord examRecord) {
        if (StringUtils.hasLength(examRecord.getId())) {
            examRecordService.updateById(examRecord);
        } else {
            examRecordService.save(examRecord);
        }
    }

    /**
     * 批量删除考试记录（权限标识：exam:examRecord:remove）
     */
    @PreAuthorize("hasAuthority('exam:examRecord:remove')")
    @OperationLog(module = "考试记录", comments = "批量删除考试记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examRecordService.removeByIds(ids);
    }
}
