package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考试记录查询参数
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 考试id
     */
    private String examId;

    /**
     * 考试次数
     */
    private Long kscs;

    /**
     * 试卷id
     */
    private String paperId;

    /**
     * 账号
     */
    private String xgh;

}
