package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamPaperQuestions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷试题Mapper
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamPaperQuestionsMapper extends BaseMapper<ExamPaperQuestions> {


    //根据试卷id删除试题组
    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QUESTIONS where pid in "+
            "<foreach collection=\"pids\" item=\"pid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{pid}" +
            "</foreach>"+
            "</script>")
    void deleteByPid(@Param("pids") List<String> pids);

    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QUESTIONS where gid in "+
            "<foreach collection=\"gids\" item=\"gid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{gid}" +
            "</foreach>"+
            "</script>")
    void deleteByGid(@Param("gids") List<String> gids);

}
