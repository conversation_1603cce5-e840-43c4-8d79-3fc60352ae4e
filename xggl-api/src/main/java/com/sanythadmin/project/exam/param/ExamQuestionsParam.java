package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.exam.entity.ExamQuestionsOptions;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 试题查询参数
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamQuestionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 试题名称
     */
    private String name;

    /**
     * 试题类型
     */
    private String sttype;

    /**
     * 试题分类ID
     */
    private String cid;

    /**
     * 分值
     */
    private Double score;

    /**
     * 打分最大分值
     */
    @QueryField(type = QueryType.EQ)
    private Integer maxScore;

    /**
     * 解析
     */
    private String analysis;

    /**
     * 答案
     */
    private String answer;

    @QueryField(ignore = true)
    private List<ExamQuestionsOptions> examQuestionsOptionsList;

    /**
     * 试卷ID,用于过滤掉已选试题
     */
    @QueryField(ignore = true)
    private String pid;

}
