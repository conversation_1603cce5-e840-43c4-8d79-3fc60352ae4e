package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.exam.entity.ExamAnswer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 答卷答案查询参数
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamAnswerParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 考试信息id
     */
    private String examInfoId;
    /**
     * 试卷试题id
     */
    private String contentId;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 考试id
     */
    private String examId;

    /**
     * 试卷id
     */
    private String paperId;

    /**
     * 答题得分
     */
    private Double point;

    /**
     * 答题者名字
     */
    private String realName;

    /**
     * 答题结果
     */
    private String result;

    /**
     * 答题者用户名
     */
    private String xgh;

    /**
     * 考试次数
     */
    private Long kscs;

    /**
     * 考试用时
     */
    @QueryField(ignore = true)
    private Long usedTime;

    private String sfpmcj;

    @QueryField(ignore = true)
    private List<ExamAnswer> answers;

}
