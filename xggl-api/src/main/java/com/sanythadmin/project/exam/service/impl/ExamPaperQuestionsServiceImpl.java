package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.*;
import com.sanythadmin.project.exam.mapper.*;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsParam;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.service.*;
import com.sanythadmin.project.exam.vo.ExamPaperQuestionsVO;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 试卷试题Service实现
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Service
public class ExamPaperQuestionsServiceImpl extends ServiceImpl<ExamPaperQuestionsMapper, ExamPaperQuestions> implements ExamPaperQuestionsService {

    @Resource
    private ExamPaperQuestionsMapper examPaperQuestionsMapper;
    @Resource
    private ExamPaperQuestionsOptionsMapper examPaperQuestionsOptionsMapper;
    @Resource
    private ExamPaperQuestionsOptionsService examPaperQuestionsOptionsService;
    @Resource
    private ExamQuestionsService examQuestionsService;
    @Resource
    private ExamQuestionsOptionsService examQuestionsOptionsService;
    @Resource
    private ExamPaperQgroupService examPaperQgroupService;
    @Lazy
    @Resource
    private ExamPaperService examPaperService;
    @Resource
    private ExamPaperMapper examPaperMapper;
    @Resource
    private ExamPaperQgroupMapper examPaperQgroupMapper;
    @Resource
    private ExamPaperQuestionsRuleService examPaperQuestionsRuleService;
    @Resource
    private ExamPaperQuestionsRuleMapper examPaperQuestionsRuleMapper;

    @Transactional
    @Override
    public ExamPaperVO saveExamPaperQuestions(ExamPaperQuestionsParam param) {
        ExamPaperQuestions examPaperQuestions = new ExamPaperQuestions();
        BeanUtil.copyProperties(param, examPaperQuestions);
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
//        ExamPaperQgroup examPaperQgroup = examPaperQgroupService.getById(param.getGid());

        examPaperQuestions.setGid(param.getGid());

        if (examPaperQuestions.getSort() == null) {
            SortHelper<ExamPaperQuestions> sortHelper = new SortHelper<>(this, "sort");
            examPaperQuestions.setSort(sortHelper.next());
        }
        saveOrUpdate(examPaperQuestions);

        examPaperMapper.updateSumAndScore(param.getPid());
        examPaperQgroupMapper.updateSumAndScore(param.getGid());

        List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = param.getExamPaperQuestionsOptionsList();
        if (CollectionUtils.isNotEmpty(examPaperQuestionsOptionsList)) {
            AtomicInteger index = new AtomicInteger(0);
            examPaperQuestionsOptionsList.forEach(examPaperQuestionsOptions -> {
                int currentIndex = index.getAndIncrement(); // 获取当前下标并递增
                examPaperQuestionsOptions.setPqid(examPaperQuestions.getId());
                examPaperQuestionsOptions.setPid(examPaperQuestions.getPid());
                if (examPaperQuestionsOptions.getSort() == null) {
                    SortHelper<ExamPaperQuestionsOptions> sortHelper = new SortHelper<>(examPaperQuestionsOptionsService, "sort");
                    examPaperQuestionsOptions.setSort(sortHelper.next() + currentIndex);
                }
            });
        }

        examPaperQuestionsOptionsService.saveOrUpdateBatch(examPaperQuestionsOptionsList);
        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Transactional
    @Override
    public ExamPaperVO setExamPaperQuestionsScores(ExamPaperQuestionsParam param) {
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        ExamPaper examPaper = examPaperService.getById(param.getPid());
        if (!ConstantsExam.EXAM_PAPER_GDXT.equals(examPaper.getSjms())) {
            AssertUtil.throwMessage("非固定试题不可批量设置分数");
        }
        update(new LambdaUpdateWrapper<ExamPaperQuestions>()
                .eq(ExamPaperQuestions::getPid, param.getPid())
                .eq(ExamPaperQuestions::getGid, param.getGid())
                .set(ExamPaperQuestions::getScore, param.getScore()));
        examPaperMapper.updateSumAndScore(param.getPid());
        examPaperQgroupMapper.updateSumAndScore(param.getGid());
        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Transactional
    @Override
    public ExamPaperVO saveExamPaperQuestionsByQuestions(ExamPaperQuestionsParam param) {
        ExamPaperQuestions examPaperQuestions = new ExamPaperQuestions();
        BeanUtil.copyProperties(param, examPaperQuestions,"sort");
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        if (StringUtils.isEmpty(param.getQid())) {
            AssertUtil.throwMessage("试卷试题id不能为空");
        }

//        ExamPaperQgroup examPaperQgroup = examPaperQgroupService.getById(param.getGid());

        List<ExamQuestions> examQuestionsList = examQuestionsService.list(new LambdaQueryWrapper<ExamQuestions>().in(ExamQuestions::getId, param.getQid().split(",")));
        List<ExamPaperQuestions> examPaperQuestionsList = BeanUtil.copyToList(examQuestionsList, ExamPaperQuestions.class);
        AtomicInteger index = new AtomicInteger(1);
        examPaperQuestionsList.forEach(paperQuestions->{
            paperQuestions.setPid(param.getPid());
            paperQuestions.setGid(param.getGid());
//            paperQuestions.setScore(paperQuestions.getQscore());
            // param.getScore()是题库选题时，页面批量设置的分数
            paperQuestions.setScore(param.getScore());
            paperQuestions.setSort(index.getAndIncrement());
            paperQuestions.setQid(paperQuestions.getId());
            paperQuestions.setId(null);
        });
        saveOrUpdateBatch(examPaperQuestionsList);
        examPaperMapper.updateSumAndScore(param.getPid());
        examPaperQgroupMapper.updateSumAndScore(param.getGid());

        Map<String, String> pqMap = new HashMap<>();
        examPaperQuestionsList.forEach(questions->{
            pqMap.put(questions.getQid(), questions.getId());
        });

        List<String> examQuestionsIds = examQuestionsList.stream().map(ExamQuestions::getId).toList();

        List<ExamQuestionsOptions> examQuestionsOptionsList = examQuestionsOptionsService.list(new LambdaQueryWrapper<ExamQuestionsOptions>()
                .in(ExamQuestionsOptions::getQid, examQuestionsIds));
        List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = BeanUtil.copyToList(examQuestionsOptionsList, ExamPaperQuestionsOptions.class);
        examPaperQuestionsOptionsList.forEach(paperQuestionsOptions->{
            paperQuestionsOptions.setId(null);
            paperQuestionsOptions.setPid(param.getPid());
            paperQuestionsOptions.setPqid(pqMap.get(paperQuestionsOptions.getQid()));
        });
        examPaperQuestionsOptionsService.saveOrUpdateBatch(examPaperQuestionsOptionsList);

        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Transactional
    @Override
    public ExamPaperVO removeExamPaperQuestions(ExamPaperQuestionsParam param) {
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        String ids = param.getId();
        examPaperQuestionsOptionsMapper.deleteByQid(Arrays.asList(ids.split(",")));
        removeByIds(Arrays.asList(ids.split(",")));
        examPaperMapper.updateSumAndScore(param.getPid());
        examPaperQgroupMapper.updateSumAndScore(param.getGid());
        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Transactional
    @Override
    public ExamPaperVO saveExamPaperQuestionsRules(ExamPaperQuestionsRuleParam param) {
        ExamPaperQuestionsRule examPaperQuestionsRule = new ExamPaperQuestionsRule();
        BeanUtil.copyProperties(param, examPaperQuestionsRule);
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
//        ExamPaperQgroup examPaperQgroup = examPaperQgroupService.getById(param.getGid());

        examPaperQuestionsRule.setGid(param.getGid());

        if (examPaperQuestionsRule.getSort() == null) {
            SortHelper<ExamPaperQuestionsRule> sortHelper = new SortHelper<>(examPaperQuestionsRuleService, "sort");
            examPaperQuestionsRule.setSort(sortHelper.next());
        }
        examPaperQuestionsRuleService.saveOrUpdate(examPaperQuestionsRule);

        examPaperMapper.updateSumAndScoreByRule(param.getPid());
        examPaperQgroupMapper.updateSumAndScoreByRule(param.getGid());

        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Override
    public ExamPaperVO removeExamPaperQuestionsRules(ExamPaperQuestionsRuleParam param) {
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        String ids = param.getId();
        examPaperQuestionsRuleService.removeByIds(Arrays.asList(ids.split(",")));
        examPaperMapper.updateSumAndScoreByRule(param.getPid());
        examPaperQgroupMapper.updateSumAndScoreByRule(param.getGid());
        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Override
    public ExamPaperQuestionsVO getExamPaperQuestions(String id) {
        ExamPaperQuestions examPaperQuestions = getById(id);
        ExamPaperQuestionsVO examPaperQuestionsVO = BeanUtil.copyProperties(examPaperQuestions, ExamPaperQuestionsVO.class);
        List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = examPaperQuestionsOptionsService.list(new LambdaQueryWrapper<ExamPaperQuestionsOptions>().eq(ExamPaperQuestionsOptions::getPqid, id)
                .orderByAsc(ExamPaperQuestionsOptions::getSort));
        examPaperQuestionsVO.setExamPaperQuestionsOptionsList(new LinkedList<>(examPaperQuestionsOptionsList));
        return examPaperQuestionsVO;
    }

}
