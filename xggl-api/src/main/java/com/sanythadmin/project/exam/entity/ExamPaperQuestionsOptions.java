package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 试卷试题选项
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_PAPER_QUESTIONS_OPTIONS")
@Entity
@Table(name = "SYT_EXAM_PAPER_QUESTIONS_OPTIONS")
public class ExamPaperQuestionsOptions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试题编号
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 试题名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试卷ID
     */
    @Column(name = "PID")
    @TableField("PID")
    private String pid;

    /**
     * 试卷试题ID
     */
    @Column(name = "P_QID")
    @TableField("P_QID")
    private String pqid;

    /**
     * 试卷试题名称
     */
    @Transient
    @TableField(exist = false)
    private String pqname;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 题库试题id(传值使用)
     */
    @Transient
    @TableField(exist = false)
    private String qid;

}
