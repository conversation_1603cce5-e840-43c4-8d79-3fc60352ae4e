package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswer;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.mapper.ExamAnswerInfoMapper;
import com.sanythadmin.project.exam.mapper.ExamAnswerMapper;
import com.sanythadmin.project.exam.mapper.ExamMapper;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;
import com.sanythadmin.project.exam.param.ExamParam;
import com.sanythadmin.project.exam.service.ExamAnswerInfoService;
import com.sanythadmin.project.exam.service.ExamAnswerService;
import com.sanythadmin.project.exam.service.ExamPaperService;
import com.sanythadmin.project.exam.service.ExamService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import com.sanythadmin.project.exam.vo.ExamVO;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.param.SelectorDataParam;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import net.sf.jsqlparser.expression.Expression;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;
import static com.sanythadmin.common.core.utils.SecurityUtil.getUserInfo;

/**
 * 在线考试Service实现
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Service
public class ExamServiceImpl extends ServiceImpl<ExamMapper, Exam> implements ExamService {

    @Resource
    private ExamMapper examMapper;
    @Resource
    private SelectorDataService selectorDataService;
    @Resource
    private SelectorAccountService selectorAccountService;
    @Lazy
    @Resource
    private ExamPaperService examPaperService;
    @Lazy
    @Resource
    private ExamAnswerService examAnswerService;
    @Lazy
    @Resource
    private ExamAnswerInfoService examAnswerInfoService;
    @Resource
    private ExamAnswerMapper examAnswerMapper;
    @Resource
    private ExamAnswerInfoMapper examAnswerInfoMapper;
    @Resource
    private UserInfoMapper userInfoMapper;


    @Override
    @Transactional
    public void saveExam(ExamParam param) throws Exception {
        Exam exam = new Exam();
        BeanUtil.copyProperties(param, exam);
        exam.setFjqk(ConstantsExam.EXAM_FJQK_WFJ);
        if (StringUtils.hasLength(exam.getId())) {
            updateById(exam);
        } else {
            exam.setCreateDate(LocalDateTime.now());
            exam.setCzr(SecurityUtil.getUsername());
            save(exam);
        }
        selectorDataService.saveSelectotData(exam.getId(), param.getSfbl(), param.getSelectorDatas());
        List<UserInfo> userInfos = selectorDataService.queryUserListBySelDataWithPermission(param.getSelectorDatas());
        exam.setFjrs(userInfos.size());
        updateById(exam);
    }

    @Override
    @Transactional
    public void removeExamByIds(List<String> ids) {
        List<Exam> examList = list(new LambdaQueryWrapper<Exam>().in(Exam::getId, ids));
        boolean b = examList.stream().anyMatch(exam -> ConstantsExam.EXAM_FJQK_YFJ.equals(exam.getFjqk()));
        if (b) {
            AssertUtil.throwMessage("存在已发卷的考试,不可删除");
        }
        examAnswerInfoMapper.removeByExamIds(ids);
        examAnswerMapper.removeByExamIds(ids);
        selectorDataService.removeSelectotData(ids);
        removeByIds(ids);

    }

    @Override
    public ExamVO getExamVO(ExamAnswerInfo examAnswerInfo) {
        SysAccount account = getAccount();
        Exam exam = getById(examAnswerInfo.getExamId());
        if(exam==null) return new ExamVO();
        ExamAnswerInfo answerInfo = null;
        if (StringUtils.hasLength(examAnswerInfo.getId())) {
            answerInfo = examAnswerInfoService.getById(examAnswerInfo.getId());
        }

        ExamPaperVO examPaperVO = examPaperService.getExamPaperVO(exam.getPid(), answerInfo, exam);
        ExamVO examVO = BeanUtil.copyProperties(exam, ExamVO.class);
        examVO.setExamPaperVO(examPaperVO);
        if (answerInfo != null) {
            examVO.setExamAnswerInfo(answerInfo);
            examVO.setAnswered(true);
        }
        examVO.setKscsIsCorrect(examAnswerService.checkNumberOfExam(exam, account));
        return examVO;
    }

    @Override
    public PageResult<Exam> queryPage(ExamParam param) {
        MyMPJLambdaWrapper<Exam, ExamParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.leftJoin(SelectorAccount.class, "acc", SelectorAccount::getItemId, Exam::getId);
        List<Expression> expressionList = selectorAccountService.expressionList("acc", getUserInfo().getUserType());
        Date now = new Date();
        wrapper.le(Exam::getKssj, now)
                .ge(Exam::getJssj, now)
                .ne(Exam::getFjqk, ConstantsExam.EXAM_FJQK_WFJ)
                .and(w -> w.eq(Exam::getSfzdry, JudgeMark.NO.getText())
                .or(o -> o.eq(Exam::getSfzdry, JudgeMark.YES.getText())
                        .and(CollectionUtils.isNotEmpty(expressionList),a -> {
                            for (Expression expression : expressionList) {
                                a.apply(expression.toString());
                            }
                        })));
        wrapper.distinct();
        wrapper.orderByDesc(Exam::getCreateDate);
        Page<Exam> page = new Page<>(param.getPage(), param.getLimit());
//        Page<Exam> iPage = examMapper.selectJoinPageWithPermission(page, Exam.class, wrapper, new MyMPQueryParams("acc", getUserInfo().getUserType()));
        Page<Exam> iPage = examMapper.selectJoinPage(page, Exam.class, wrapper);
        List<Exam> records = iPage.getRecords();
        SysAccount account = getAccount();
        records.forEach(exam -> {
            ExamAnswer answer = new ExamAnswer(exam.getId(), account.getUsername());
            exam.setAnswered(examAnswerService.isAnswered(answer));
            exam.setKscsIsCorrect(examAnswerService.checkNumberOfExam(exam,account));
            exam.setYkcs(examAnswerService.getMaxNumberOfExams(exam.getId(),account.getUsername()));
        });
        return new PageResult<>(records, iPage.getTotal());
    }

    @Override
    public ExamVO getExamVOById(String id) {
        Exam exam = getById(id);
        if(exam==null) return new ExamVO();
        ExamVO examVO = BeanUtil.copyProperties(exam, ExamVO.class);
        //人员选择器
        SelectorDataParam selectorDataParam = new SelectorDataParam();
        selectorDataParam.setItemId(exam.getId());
        PageParam<SelectorData, SelectorDataParam> dataParamPageParam = new PageParam<>(selectorDataParam);
        List<SelectorData> selectorDatas = selectorDataService.list(dataParamPageParam.getWrapper());
        examVO.setSelectorDatas(selectorDatas);
        return examVO;
    }

    @Override
    public PageResult<UserInfo> pageExamPersonnelList(ExamParam param, UserInfoParam userInfoParam) {
        if (!StringUtils.hasLength(param.getId())) {
            AssertUtil.throwMessage("考试id不能为空");
        }
//        MPJLambdaWrapper<UserInfo> userInfoMPJLambdaWrapper = new MPJLambdaWrapper<>();
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> userInfoMPJLambdaWrapper = new MyMPJLambdaWrapper<>(userInfoParam);
        userInfoMPJLambdaWrapper.leftJoin(SelectorAccount.class, "acc", SelectorAccount::getXgh, UserInfo::getXgh);
        userInfoMPJLambdaWrapper.eq(SelectorAccount::getItemId, param.getId());
        if (ConstantsExam.EXAM_MDLX_QK.equals(param.getMdlx())) {
            userInfoMPJLambdaWrapper.notExists("SELECT 1 FROM SYT_EXAM_ANSWER_INFO WHERE EXAM_ID = {0} AND XGH = t.XGH", param.getId());
        }
        Page<UserInfo> page = new Page<>(param.getPage(), param.getLimit());
        Page<UserInfo> iPage = userInfoMapper.selectJoinPageWithPermission(page, UserInfo.class, userInfoMPJLambdaWrapper, new MyMPQueryParams(UserInfo.class));
        List<UserInfo> records = iPage.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, iPage.getTotal());
    }

    @Override
    public PageResult pageExamRankingList(ExamAnswerInfoParam param) {
        Exam exam = this.getById(param.getExamId());
        if (exam == null) {
            AssertUtil.throwMessage("未找到考试");
        }
        PageParam<ExamAnswerInfo, ExamAnswerInfoParam> page = new PageParam<>(param);
        Page<ExamAnswerInfo> iPage = examAnswerInfoMapper.pageExamScoresWithRankings(exam.getId(), page);
        List<ExamAnswerInfo> records = iPage.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, iPage.getTotal());
    }

}
