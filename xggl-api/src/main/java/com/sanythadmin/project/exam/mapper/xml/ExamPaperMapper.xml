<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_DATE, CZR, NAME, SCORE, SUBJECT_SUM, SJMS, PAPER_TYPE, PAPER_BZ, PC_IMAGE, MOBILE_IMAGE, BACKGROUND_COLOR
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE LIKE concat(concat('%',#{param.score), '%')
                    </if>
                    <if test="param.subjectSum != null">
                        AND a.SUBJECT_SUM = #{param.subjectSum}
                    </if>
                    <if test="param.sjms != null">
                        AND a.SJMS LIKE concat(concat('%',#{param.sjms), '%')
                    </if>
                    <if test="param.paperType != null">
                        AND a.PAPER_TYPE LIKE concat(concat('%',#{param.paperType), '%')
                    </if>
                    <if test="param.paperBz != null">
                        AND a.PAPER_BZ LIKE concat(concat('%',#{param.paperBz), '%')
                    </if>
                    <if test="param.pcImage != null">
                        AND a.PC_IMAGE LIKE concat(concat('%',#{param.pcImage), '%')
                    </if>
                    <if test="param.mobileImage != null">
                        AND a.MOBILE_IMAGE LIKE concat(concat('%',#{param.mobileImage), '%')
                    </if>
                    <if test="param.backgroundColor != null">
                        AND a.BACKGROUND_COLOR LIKE concat(concat('%',#{param.backgroundColor), '%')
                    </if>
    </sql>
</mapper>
