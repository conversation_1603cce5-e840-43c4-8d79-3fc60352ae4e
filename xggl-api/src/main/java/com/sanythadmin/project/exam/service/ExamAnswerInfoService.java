package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
public interface ExamAnswerInfoService extends IService<ExamAnswerInfo> {

    PageResult<ExamAnswerInfo> getExamAnswerInfoPage(ExamAnswerInfoParam param, UserInfoParam userInfoParam);

    void removeExamAnswerInfoByIds(List<String> ids);
}
