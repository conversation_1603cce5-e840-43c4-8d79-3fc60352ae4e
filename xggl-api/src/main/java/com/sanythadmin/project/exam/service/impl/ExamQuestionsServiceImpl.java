package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.dto.ExamQuestionImportDTO;
import com.sanythadmin.project.exam.entity.ExamQuestions;
import com.sanythadmin.project.exam.entity.ExamQuestionsOptions;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsClassMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsOptionsMapper;
import com.sanythadmin.project.exam.param.ExamQuestionsParam;
import com.sanythadmin.project.exam.service.ExamQuestionsClassService;
import com.sanythadmin.project.exam.service.ExamQuestionsOptionsService;
import com.sanythadmin.project.exam.service.ExamQuestionsService;
import com.sanythadmin.project.exam.vo.ExamQuestionsVO;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;
import static com.sanythadmin.project.exam.constant.ConstantsExam.*;

/**
 * 试题Service实现
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Slf4j
@Service
public class ExamQuestionsServiceImpl extends ServiceImpl<ExamQuestionsMapper, ExamQuestions> implements ExamQuestionsService {

    @Resource
    private ExamQuestionsMapper examQuestionsMapper;
    @Resource
    private ExamQuestionsClassMapper examQuestionsClassMapper;
    @Resource
    private ExamQuestionsOptionsMapper examQuestionsOptionsMapper;
    @Resource
    private ExamQuestionsOptionsService examQuestionsOptionsService;
    @Resource
    private ExamQuestionsClassService examQuestionsClassService;
    @Resource
    private ExamPaperQuestionsMapper examPaperQuestionsMapper;



    @Transactional
    @Override
    public void saveQuestionsAndOptions(ExamQuestionsParam param) {
        ExamQuestions questions = new ExamQuestions();
        BeanUtil.copyProperties(param, questions);
        if (StringUtils.hasLength(questions.getId())) {
            examQuestionsOptionsMapper.deleteByQid(Collections.singletonList(param.getId()));
            this.updateById(questions);
        } else {
            questions.setCreateDate(LocalDateTime.now());
            questions.setCzr(SecurityUtil.getUsername());
            save(questions);
        }
        List<ExamQuestionsOptions> examQuestionsOptionsList = param.getExamQuestionsOptionsList();
        if (CollectionUtils.isNotEmpty(examQuestionsOptionsList)) {
            AtomicInteger index = new AtomicInteger(0);
            examQuestionsOptionsList.forEach(examQuestionsOptions -> {
                int currentIndex = index.getAndIncrement(); // 获取当前下标并递增
                examQuestionsOptions.setQid(questions.getId());
                if (examQuestionsOptions.getSort() == null) {
                    SortHelper<ExamQuestionsOptions> sortHelper = new SortHelper<>(examQuestionsOptionsService, "sort");
                    examQuestionsOptions.setSort(sortHelper.next() + currentIndex);
                }
            });
        }

        examQuestionsOptionsService.saveOrUpdateBatch(examQuestionsOptionsList);
    }

    @Transactional
    @Override
    public void removeQuestionsAndOptions(List<String> ids) {
        examQuestionsOptionsMapper.deleteByQid(ids);
        removeByIds(ids);
    }

    @Override
    public ExamQuestionsVO getByIdAndOptions(String id) {
        ExamQuestions examQuestions = getById(id);
        ExamQuestionsVO examQuestionsVO = new ExamQuestionsVO();
        BeanUtil.copyProperties(examQuestions, examQuestionsVO);
        List<ExamQuestionsOptions> examQuestionsOptionsList = examQuestionsOptionsService.list(new LambdaQueryWrapper<ExamQuestionsOptions>().eq(ExamQuestionsOptions::getQid, id));
        examQuestionsVO.setExamQuestionsOptionsList(examQuestionsOptionsList);
        return examQuestionsVO;
    }

    @Override
    public void exportTemplate(ServletOutputStream outputStream) {
        List<String> headers = Arrays.asList(
                "试题分类", "试题名称", "试题类型", "答案", "解析", "分值","打分最大分值",
                "选项"
                );

        List<List<String>> headList = headers.stream()
                .map(Arrays::asList)
                .collect(Collectors.toList());

        List<List<Object>> dataList = Arrays.asList(
                Arrays.asList("示例：单选题", EXAM_STTYPE_DX, EXAM_STTYPE_DX, "A", "这是一道单选题", 10, 10,"A,B,C,D"),
                Arrays.asList("示例：多选题", EXAM_STTYPE_MX, EXAM_STTYPE_MX, "A,B", "这是一道多选题",  20, 20,"A,B,C,D,E,F"),
                Arrays.asList("示例：判断题", EXAM_STTYPE_PD, EXAM_STTYPE_PD, "对", "这是一道判断题",  10, 10,""),
                Arrays.asList("示例：问答题", EXAM_STTYPE_WD, EXAM_STTYPE_WD, "标准答案", "这是一道问答题", 30, 30,"")
        );

        EasyExcelHelper.exportExcel(outputStream, "试题导入模板", headList, dataList);
    }

    @Override
    public PageResult<ExamQuestions> getQuestionsNotInPaper(ExamQuestionsParam param) {
        PageParam<ExamQuestions, ExamQuestionsParam> page = new PageParam<>(param);
//        QueryWrapper<ExamQuestions> queryWrapper = new QueryWrapper<>();
        QueryWrapper<ExamQuestions> wrapper = page.getOrderWrapper();
        wrapper.notExists("select 1 from SYT_EXAM_PAPER_QUESTIONS " +
                "where SYT_EXAM_QUESTIONS.id = SYT_EXAM_PAPER_QUESTIONS.qid " +
                "and SYT_EXAM_PAPER_QUESTIONS.PID = {0}", param.getPid());
        PageParam<ExamQuestions, ExamQuestionsParam> iPage = examQuestionsMapper.selectPage(page, wrapper);
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Transactional
    @Override
    public void importExamQuestions(MultipartFile file){
        Map<String, String> allNameIdMap = examQuestionsClassService.getAllNameIdMap();

        try {
            EasyExcel.read(file.getInputStream(), ExamQuestionImportDTO.class, new ReadListener<ExamQuestionImportDTO>() {
                private List<ExamQuestions> questions = new ArrayList<>();
                private List<ExamQuestionsOptions> options = new ArrayList<>();

                @Override
                public void invoke(ExamQuestionImportDTO data, AnalysisContext context) {
                    try {
                        ExamQuestions question = convertToExamQuestion(data, allNameIdMap);
                        questions.add(question);
                        options.addAll(convertToExamQuestionOptions(data, question.getId()));
                    } catch (Exception e) {
                        log.error("处理导入数据时出错，行号：{}，错误：{}", context.readRowHolder().getRowIndex(), e.getMessage());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    saveBatch(questions);
                    examQuestionsOptionsService.saveBatch(options);
                    log.info("成功导入 {} 道题目和 {} 个选项", questions.size(), options.size());
                }
            }).sheet().doRead();
        } catch (IOException e) {
            log.error("导入数据失败", e);
            throw new RuntimeException("导入数据失败", e);
        }
    }

    private ExamQuestions convertToExamQuestion(ExamQuestionImportDTO dto,Map<String, String> allNameIdMap) {
        ExamQuestions question = new ExamQuestions();
        question.setId(CommonUtil.getUUID());
        question.setName(dto.getName());
        question.setSttype(dto.getSttype());
        String cid = allNameIdMap.getOrDefault(dto.getCname(), "");
        if (!StringUtils.hasLength(cid)) {
            AssertUtil.throwMessage("试题分类不存在");
        }
        question.setCid(cid);
        question.setMaxScore(dto.getMaxScore());
        question.setAnalysis(dto.getAnalysis());
        question.setCreateDate(LocalDateTime.now());
        question.setCzr(getAccount().getUsername());
        question.setScore(dto.getScore());
        if (!StringUtils.hasLength(dto.getAnswer())) {
            AssertUtil.throwMessage("试题答案不能为空");
        }
        question.setAnswer(dto.getAnswer());
        return question;
    }

    private List<ExamQuestionsOptions> convertToExamQuestionOptions(ExamQuestionImportDTO dto, String questionId) {
        List<ExamQuestionsOptions> options = new ArrayList<>();

        switch (dto.getSttype()) {
            case EXAM_STTYPE_DX:
            case EXAM_STTYPE_MX:
                if (StringUtils.hasLength(dto.getOptions())) {
                    String[] split = dto.getOptions().split(",");
                    for (int i = 0; i < split.length; i++) {
                        addOptionIfNotNull(options, questionId, split[i], split[i], i + 1);
                    }
                }
                break;
            case EXAM_STTYPE_PD:// 判断问答题不需要选项
               /* addOptionIfNotNull(options, questionId, "对", "对", 1);
                addOptionIfNotNull(options, questionId, "错", "错", 2);
                break;*/
            case EXAM_STTYPE_WD:
                break;
            default:
                log.warn("未知的题目类型: {}",dto.getSttype());
        }

        return options;
    }

    private void addOptionIfNotNull(List<ExamQuestionsOptions> options, String questionId, String code, String name, int sort) {
        if (name != null && !name.isEmpty()) {
            ExamQuestionsOptions option = new ExamQuestionsOptions();
            option.setQid(questionId);
            option.setCode(code);
            option.setName(name);
            option.setSort(sort);
            options.add(option);
        }
    }
}
