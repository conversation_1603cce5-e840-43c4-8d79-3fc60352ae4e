package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsRule;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.service.ExamPaperQuestionsRuleService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/试卷试题抽题规则控制器
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@RestController
@RequestMapping("/api/exam/exam-paper-questions-rule")
public class ExamPaperQuestionsRuleController extends BaseController {
    @Resource
    private ExamPaperQuestionsRuleService examPaperQuestionsRuleService;

    /**
     * 分页查询试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:list')")
    @GetMapping("/page")
    public PageResult<ExamPaperQuestionsRule> page(ExamPaperQuestionsRuleParam param) {
        PageParam<ExamPaperQuestionsRule, ExamPaperQuestionsRuleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examPaperQuestionsRuleService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:list')")
    @GetMapping()
    public List<ExamPaperQuestionsRule> list(ExamPaperQuestionsRuleParam param) {
        PageParam<ExamPaperQuestionsRule, ExamPaperQuestionsRuleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examPaperQuestionsRuleService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:list')")
    @GetMapping("/{id}")
    public ExamPaperQuestionsRule get(@PathVariable("id") String id) {
        return examPaperQuestionsRuleService.getById(id);
    }

    /**
     * 添加或修改试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:operation')")
    @OperationLog(module = "试卷试题抽题规则", comments = "保存试卷试题抽题规则")
    @PostMapping("/operation")
    public ExamPaperVO save(@RequestBody ExamPaperQuestionsRuleParam param) {
        return examPaperQuestionsRuleService.saveExamPaperQuestionsRule(param);
    }
    /**
     * 批量添加或修改试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:operation')")
    @OperationLog(module = "试卷试题抽题规则", comments = "批量保存试卷试题抽题规则")
    @PostMapping("/operationBatch")
    public ExamPaperVO saveBatch(@RequestBody List<ExamPaperQuestionsRuleParam> param) {
        return examPaperQuestionsRuleService.saveBatchExamPaperQuestionsRule(param);
    }

    /**
     * 批量删除试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsRule:remove')")
    @OperationLog(module = "试卷试题抽题规则", comments = "批量删除试卷试题抽题规则")
    @PostMapping("/remove")
    public ExamPaperVO remove(@RequestBody ExamPaperQuestionsRuleParam param) {
        return examPaperQuestionsRuleService.removeExamPaperQuestionsRule(param);
    }
}
