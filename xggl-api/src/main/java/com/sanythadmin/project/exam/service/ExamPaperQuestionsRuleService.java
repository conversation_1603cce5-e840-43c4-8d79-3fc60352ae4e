package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsRule;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.vo.ExamPaperVO;

import java.util.List;

/**
 * 试卷试题抽题规则Service
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
public interface ExamPaperQuestionsRuleService extends IService<ExamPaperQuestionsRule> {

    ExamPaperVO saveExamPaperQuestionsRule(ExamPaperQuestionsRuleParam param);

    ExamPaperVO removeExamPaperQuestionsRule(ExamPaperQuestionsRuleParam param);

    ExamPaperVO saveBatchExamPaperQuestionsRule(List<ExamPaperQuestionsRuleParam> param);
}
