package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswer;
import com.sanythadmin.project.exam.param.ExamAnswerParam;
import com.sanythadmin.project.exam.param.ExamParam;
import com.sanythadmin.project.exam.service.ExamAnswerService;
import com.sanythadmin.project.exam.service.ExamService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/答卷答案控制器
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@RestController
@RequestMapping("/api/exam/exam-answer")
public class ExamAnswerController extends BaseController {
    @Resource
    private ExamAnswerService examAnswerService;
    @Resource
    private ExamService examService;

    /**
     * 分页查询答卷答案（权限标识：exam:examAnswer:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:list')")
    @GetMapping("/page")
    public PageResult<ExamAnswer> page(ExamAnswerParam param) {
        PageParam<ExamAnswer, ExamAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examAnswerService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部答卷答案（权限标识：exam:examAnswer:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:list')")
    @GetMapping()
    public List<ExamAnswer> list(ExamAnswerParam param) {
        PageParam<ExamAnswer, ExamAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examAnswerService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询答卷答案（权限标识：exam:examAnswer:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:list')")
    @GetMapping("/{id}")
    public ExamAnswer get(@PathVariable("id") String id) {
        return examAnswerService.getById(id);
    }

    /**
     * 添加或修改答卷答案（权限标识：exam:examAnswer:operation）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:operation')")
    @OperationLog(module = "答卷答案", comments = "保存答卷答案")
    @PostMapping("/operation")
    public void save(@RequestBody ExamAnswerParam param) {
        examAnswerService.saveAnswerAndHistory(param);
    }

    /**
     * 批量删除答卷答案（权限标识：exam:examAnswer:remove）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:remove')")
    @OperationLog(module = "答卷答案", comments = "批量删除答卷答案")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examAnswerService.removeByIds(ids);
    }

    /**
     * 分页查询我的考试（权限标识：exam:examAnswer:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:list')")
    @GetMapping("/queryExams")
    public PageResult<Exam> queryExams(ExamParam param) {
        return examService.queryPage(param);
    }

    /**
     * 人工阅卷打分（权限标识：exam:examAnswer:operation）
     */
    @PreAuthorize("hasAuthority('exam:examAnswer:operation')")
    @OperationLog(module = "答卷答案", comments = "答卷打分")
    @PostMapping("/scoring")
    public void scoring(@RequestBody ExamAnswerParam param) {
        examAnswerService.saveAnswerScoring(param);
    }
}
