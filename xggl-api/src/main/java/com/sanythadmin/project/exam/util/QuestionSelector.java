package com.sanythadmin.project.exam.util;

import com.sanythadmin.project.exam.entity.ExamPaperQuestionsRule;
import com.sanythadmin.project.survey.entity.SurveyPquestionsRule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class QuestionSelector<T> {

    private final List<T> allQuestions;
    private final QuestionMatcher<T> matcher;
    private final QuestionUpdater<T> updater;
    // 定义一个函数式接口用于匹配条件
    @FunctionalInterface
    public interface QuestionMatcher<T> {
        boolean matches(T item, Object rule);
    }

    // 定义一个函数式接口用于更新项目属性
    @FunctionalInterface
    public interface QuestionUpdater<T> {
        void update(T item, Object rule);
    }

    public QuestionSelector(List<T> allItems, QuestionMatcher<T> matcher, QuestionUpdater<T> updater) {
        this.allQuestions = allItems;
        this.matcher = matcher;
        this.updater = updater;
    }

    public List<T> selectQuestions(Object rule, int requiredCount) {
        // 根据规则过滤符合条件的题目
        List<T> eligibleQuestions = allQuestions.stream()
                .filter(q -> matcher.matches(q, rule))
                .collect(Collectors.toList());

        // 如果符合条件的题目数量小于需要抽取的数量，抛出异常或返回所有符合条件的题目
        if (eligibleQuestions.size() < requiredCount) {
             return new ArrayList<>(eligibleQuestions);
        }

        // 随机打乱符合条件的题目列表
        Collections.shuffle(eligibleQuestions);

        // 返回所需数量的题目
        return eligibleQuestions.subList(0, requiredCount);
    }

    public List<T> selectQuestionsForPaper(List<? extends Object> rules,Set<String> selectedQuestionIds, IdExtractor<T> idExtractor) {
        List<T> selectedQuestions = new ArrayList<>();

        for (Object rule : rules) {
            int requiredCount = getRequiredCount(rule);
            List<T> questionsForRule = selectQuestions(rule,requiredCount);
            
            // 确保不重复选择题目
            List<T> uniqueQuestionsForRule = questionsForRule.stream()
                    .filter(q -> !selectedQuestionIds.contains(idExtractor.getId(q)))
                    .collect(Collectors.toList());

            // 如果去重后的题目数量不足，可以再次随机选择补足
            while (uniqueQuestionsForRule.size() < requiredCount) {
                T additionalQuestion = selectAdditionalQuestion(rule, selectedQuestionIds,idExtractor);
                if (additionalQuestion != null) {
                    uniqueQuestionsForRule.add(additionalQuestion);
                }
            }
            //设置题分数
            uniqueQuestionsForRule.forEach(uniqueQuestions -> updater.update(uniqueQuestions, rule));

            selectedQuestions.addAll(uniqueQuestionsForRule);
            selectedQuestionIds.addAll(uniqueQuestionsForRule.stream()
                    .map(idExtractor::getId)
                    .collect(Collectors.toSet()));
        }

        return selectedQuestions;
    }

    private T selectAdditionalQuestion(Object rule, Set<String> selectedQuestionIds, IdExtractor<T> idExtractor) {
        return allQuestions.stream()
                .filter(q -> matcher.matches(q, rule))
                .filter(q -> !selectedQuestionIds.contains(idExtractor.getId(q)))
                .findFirst()
                .orElse(null);
    }
    @FunctionalInterface
    public interface IdExtractor<T> {
        String getId(T item);
    }

    private int getRequiredCount(Object rule) {
        if (rule instanceof ExamPaperQuestionsRule) {
            return ((ExamPaperQuestionsRule) rule).getQsum();
        } else if (rule instanceof SurveyPquestionsRule) {
            return ((SurveyPquestionsRule) rule).getQSum();
        }
        throw new IllegalArgumentException("Unsupported rule type");
    }
}