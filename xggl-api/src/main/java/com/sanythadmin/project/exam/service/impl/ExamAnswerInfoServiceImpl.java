package com.sanythadmin.project.exam.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.mapper.ExamAnswerInfoMapper;
import com.sanythadmin.project.exam.mapper.ExamAnswerMapper;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;
import com.sanythadmin.project.exam.service.ExamAnswerInfoService;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
@Service
public class ExamAnswerInfoServiceImpl extends ServiceImpl<ExamAnswerInfoMapper, ExamAnswerInfo> implements ExamAnswerInfoService {

    @Resource
    private ExamAnswerInfoMapper examAnswerInfoMapper;
    @Resource
    private ExamAnswerMapper examAnswerMapper;
    @Resource
    private SelectorDataService selectorDataService;

    @Override
    public PageResult<ExamAnswerInfo> getExamAnswerInfoPage(ExamAnswerInfoParam param, UserInfoParam userInfoParam) {
        String status = param.getStatus();
        param.setStatus(null);
        //根据考试id查询选择器数据用户类型，构建查询条件
        List<UserType> userTypes = selectorDataService.getDistinctUserTypesByItemId(param.getExamId());
        MyMPJLambdaWrapper<ExamAnswerInfo, ExamAnswerInfoParam> wrapper =
                UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param, ExamAnswerInfo::getXgh, userInfoParam, userTypes.toArray(new UserType[0]));
        Class<UserInfo> userInfoClass = UserInfo.class;
        Class<ExamAnswerInfo> examAnswerInfoClass = ExamAnswerInfo.class;
        SFunction<UserInfo, Object>[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(UserInfo.class), false);
        SFunction<ExamAnswerInfo, Object>[] sFunctions1 = CommonUtil.buildSFunctions(examAnswerInfoClass, CommonUtil.getAllFields(ExamAnswerInfo.class), true);
        wrapper.select(sFunctions).select(sFunctions1);
        Map<Class<?>, String> tableAliasMap = wrapper.getTableAliasMap();
        wrapper.selectAssociation(tableAliasMap.get(userInfoClass), userInfoClass, ExamAnswerInfo::getUserInfo);

        //根据状态查询
        if (StringUtils.hasLength(param.getXgh())) {
//            wrapper.nested(qOr -> qOr.isNull("status").or().eq("status", ConstantsExam.EXAM_YJZT_YYJ));
        } else {
            if (ConstantsExam.EXAM_YJZT_DYJ.equals(status)) {
                wrapper.eq("status", ConstantsExam.EXAM_YJZT_DYJ);
            } else {
                wrapper.nested(qOr -> qOr.isNull("status").or().eq("status", ConstantsExam.EXAM_YJZT_YYJ));
            }
        }
        wrapper.orderByDesc("CREATE_DATE");
        Page<ExamAnswerInfo> page = wrapper.getPage();
        IPage<ExamAnswerInfo> iPage = examAnswerInfoMapper.selectJoinPageWithPermission(page, ExamAnswerInfo.class, wrapper, new MyMPQueryParams(ExamAnswerInfo.class));
        UserInfoUtil.codeTextSet(iPage.getRecords());
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    @Transactional
    @Override
    public void removeExamAnswerInfoByIds(List<String> ids) {
        examAnswerInfoMapper.removeByExamIds(ids);
        examAnswerMapper.removeByExamIds(ids);
    }

}
