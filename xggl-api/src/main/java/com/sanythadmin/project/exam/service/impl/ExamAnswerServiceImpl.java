package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.ArraysUtil;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.*;
import com.sanythadmin.project.exam.mapper.ExamAnswerInfoMapper;
import com.sanythadmin.project.exam.mapper.ExamAnswerMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsMapper;
import com.sanythadmin.project.exam.param.ExamAnswerParam;
import com.sanythadmin.project.exam.service.*;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.sanythadmin.common.core.utils.SecurityUtil.getAccount;

/**
 * 答卷答案Service实现
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Service
public class ExamAnswerServiceImpl extends ServiceImpl<ExamAnswerMapper, ExamAnswer> implements ExamAnswerService {

    @Resource
    private ExamAnswerMapper examAnswerMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Lazy
    @Resource
    private ExamService examService;
    @Resource
    private ExamPaperService examPaperService;
    @Resource
    private ExamAnswerInfoMapper examAnswerInfoMapper;
    @Resource
    private ExamAnswerInfoService examAnswerInfoService;
    @Resource
    private ExamPaperQuestionsService examPaperQuestionsService;
    @Resource
    private ExamQuestionsService examQuestionsService;
    @Resource
    private ExamPaperQuestionsRuleService examPaperQuestionsRuleService;
    @Autowired
    private ExamQuestionsMapper examQuestionsMapper;

    @Override
    public Integer getMaxNumberOfExams(String examId, String xgh) {
        String lockKey = "exam:maxnumber:lock:" + examId + ":" + xgh;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待  秒，锁定时间为 5秒
            if (lock.tryLock(3, 5, TimeUnit.SECONDS)) {
                Integer maxKscs = examAnswerInfoMapper.getMaxNumberOfExams(examId, xgh);
                return maxKscs != null ? maxKscs : 0;
            } else {
                // 获取锁失败，处理并发失败的情况
                throw new RuntimeException("获取锁失败，操作被阻止");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException("获取锁时发生异常", e);
        } finally {
            // 确保锁被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional
    @Override
    public void saveAnswerAndHistory(ExamAnswerParam param) {
        Exam exam = examService.getById(param.getExamId());
        if (exam == null) {
            AssertUtil.throwMessage("未找到考试");
        }
        ExamPaper examPaper = examPaperService.getById(param.getPaperId());
        if (examPaper == null) {
            AssertUtil.throwMessage("未找到试卷");
        }
        SysAccount account = getAccount();
        Integer maxNumberOfExams = this.getMaxNumberOfExams(param.getExamId(), account.getUsername());
        if (ConstantsExam.EXAM_KSMS_KSMS.equals(exam.getKsms()) && maxNumberOfExams >= exam.getCkcs()) {
            AssertUtil.throwMessage("考试次数已用完");
        }

        ExamAnswerInfo examAnswerInfo = new ExamAnswerInfo();
        examAnswerInfo.setId(CommonUtil.getUUID());
        examAnswerInfo.setExamId(param.getExamId());
        examAnswerInfo.setPaperId(param.getPaperId());
        examAnswerInfo.setUsedTime(param.getUsedTime());
        examAnswerInfo.setKscs(maxNumberOfExams + 1);
        examAnswerInfo.setXgh(account.getUsername());
        examAnswerInfo.setCreateDate(LocalDateTime.now());
        examAnswerInfo.setUsedTime(param.getUsedTime());
        if (JudgeMark.YES.getText().equals(exam.getRgyj())) {
            examAnswerInfo.setStatus(ConstantsExam.EXAM_YJZT_DYJ);
        }
//        examAnswerInfoService.save(examAnswerInfo);


        List<ExamAnswer> answers = param.getAnswers();
        List<String> answersIds = answers.stream().map((ExamAnswer::getContentId)).collect(Collectors.toList());
        //查出试题，用于答案判断
        String sjms = examPaper.getSjms();
        //判断固定还是随机，固定取试卷试题 随机取题库试题
        List<ExamPaperQuestions> examPaperQuestionsList = new ArrayList<>();
        Map<String, Double> sjQScoreMap;
        if (ConstantsExam.EXAM_PAPER_GDXT.equals(sjms)) {
            sjQScoreMap = new HashMap<>();
            examPaperQuestionsList = examPaperQuestionsService.list(new LambdaQueryWrapper<ExamPaperQuestions>().in(ExamPaperQuestions::getId, answersIds));
        } else if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
            // 分数应该从抽题规则来
            List<ExamQuestions> examQuestionsList = examQuestionsService.list(new LambdaQueryWrapper<ExamQuestions>().in(ExamQuestions::getId, answersIds));
            examPaperQuestionsList = BeanUtil.copyToList(examQuestionsList, ExamPaperQuestions.class);
            List<ExamPaperQuestionsRule> examPaperQuestionsRules = examPaperQuestionsRuleService.list(new LambdaQueryWrapper<ExamPaperQuestionsRule>()
                    .eq(ExamPaperQuestionsRule::getPid, examPaper.getId()));
            sjQScoreMap = examPaperQuestionsRules.stream()
                    .collect(Collectors.toMap(
                            rule -> rule.getPid() + "_" + rule.getGid() + "_" + rule.getSttype(),
                            ExamPaperQuestionsRule::getQscore
                    ));
        } else {
            sjQScoreMap = new HashMap<>();
        }

        Map<String, ExamPaperQuestions> examPaperQuestionsMap = examPaperQuestionsList.stream()
                .collect(Collectors.toMap(
                        examPaperQuestions -> examPaperQuestions.getId(),
                        examPaperQuestions -> examPaperQuestions));
        AtomicReference<Integer> dds = new AtomicReference<>(0);        //答对数
        AtomicReference<Integer> dcs = new AtomicReference<>(0);        //答错数
        AtomicReference<Double> score = new AtomicReference<>(0d);

        answers.forEach(answer -> {
            answer.setExamInfoId(examAnswerInfo.getId());
            answer.setExamId(param.getExamId());
            answer.setPaperId(param.getPaperId());
            answer.setXgh(account.getUsername());
            answer.setCreateDate(LocalDateTime.now());
            answer.setKscs(maxNumberOfExams + 1);
            // 判断对错得分,记录答案对错
            Boolean resultCorrect = false;
            ExamPaperQuestions questions = examPaperQuestionsMap.get(answer.getContentId());
            Double aDouble = questions != null && questions.getScore() != null ? questions.getScore() : 0d;
            if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
                aDouble = sjQScoreMap.get(answer.getPaperId() + "_" + answer.getGid() + "_" + answer.getSttype());
            }
            if (ConstantsExam.EXAM_STTYPE_DX.equals(questions.getSttype())||ConstantsExam.EXAM_STTYPE_MX.equals(questions.getSttype())) {
                if (StringUtils.hasLength(answer.getResult())) {
                    String[] correctOptions = questions.getAnswer().split(",");
                    String[] currentSelected = answer.getResult().split(",");
                    boolean isCorrect = ArraysUtil.areAllSelectedCorrect(correctOptions, currentSelected);
                    if (isCorrect) {
                        resultCorrect = true;
                        dds.updateAndGet(count -> count + 1);
                        answer.setPoint(aDouble);
                        score.updateAndGet(v -> v + answer.getPoint());
                    } else {
                        resultCorrect = false;
                        dcs.updateAndGet(count -> count + 1);
                    }
                } else {
                    resultCorrect = false;
                    dcs.updateAndGet(count -> count + 1);
                }

            } else if (ConstantsExam.EXAM_STTYPE_PD.equals(questions.getSttype())) {
                boolean b = Objects.equals(answer.getResult(), questions.getAnswer());
                if (b) {
                    resultCorrect = true;
                    dds.updateAndGet(count -> count + 1);
                    answer.setPoint(aDouble);
                    score.updateAndGet(v -> v + answer.getPoint());
                }else {
                    resultCorrect = false;
                    dcs.updateAndGet(count -> count + 1);
                }

            } else {
                //问答题算分
                if (!JudgeMark.YES.getText().equals(exam.getRgyj())) {
                    //不是人工阅卷的自动打分
                    /*Double wdScore = AutoKeywordGrading.calculateScore(answer.getResult(), AutoKeywordGrading.extractKeywords(questions.getAnswer()), aDouble);
                    answer.setPoint(wdScore);
                    score.updateAndGet(v -> v + answer.getPoint());*/
                }
            }
            answer.setResultCorrect(resultCorrect);
            if (resultCorrect) {
                answer.setManualScoring(answer.getPoint() == null ? 0d : answer.getPoint());
            }
        });
        Double aDouble = score.get();
        examAnswerInfo.setScore(aDouble);
        examAnswerInfo.setSfhg(aDouble >= exam.getHgx() ? JudgeMark.YES.getText() : JudgeMark.NO.getText());
        examAnswerInfo.setDdt(dds.get());
        examAnswerInfo.setDcs(dcs.get());
        examAnswerInfoService.save(examAnswerInfo);
        this.saveBatch(answers);

    }

    @Override
    public boolean isAnswered(ExamAnswer examAnswer) {
        long count = count(new LambdaQueryWrapper<ExamAnswer>().eq(ExamAnswer::getExamId, examAnswer.getExamId())
                .eq(StringUtils.hasLength(examAnswer.getXgh()), ExamAnswer::getXgh, examAnswer.getXgh()));
        return count > 0;
    }

    @Override
    public boolean checkNumberOfExam(Exam exam,SysAccount account) {
        if (ConstantsExam.EXAM_KSMS_KSMS.equals(exam.getKsms())) {
            Integer maxNumberOfExams = this.getMaxNumberOfExams(exam.getId(), account.getUsername());
            if (maxNumberOfExams < exam.getCkcs()) {
                return true;
            }
        } else {
            return true;
        }
        return false;
    }

    @Override
    public Map<String, ExamAnswer> getAnswerMap(ExamAnswerInfo examAnswerInfo) {
        Map<String, ExamAnswer> map = new HashMap<>();
        List<ExamAnswer> list = list(new LambdaQueryWrapper<ExamAnswer>().eq(ExamAnswer::getExamInfoId, examAnswerInfo.getId()));
        list.forEach(answer -> {
            map.put(answer.getContentId(), answer);
        });
        return map;
    }

    @Transactional
    @Override
    public void saveAnswerScoring(ExamAnswerParam param) {
        Exam exam = examService.getById(param.getExamId());
        if (exam == null) {
            AssertUtil.throwMessage("未找到考试");
        }
        ExamPaper examPaper = examPaperService.getById(param.getPaperId());
        if (examPaper == null) {
            AssertUtil.throwMessage("未找到试卷");
        }
        String sjms = examPaper.getSjms();
        AtomicReference<Double> score = new AtomicReference<>(0d);
        List<ExamAnswer> answersList = param.getAnswers();
        //分模式查找题，找到最大打分
//        List<String> answersIds = answersList.stream().map((ExamAnswer::getContentId)).collect(Collectors.toList());
//        List<ExamPaperQuestions> examPaperQuestionsList = new ArrayList<>();
        Map<String, Double> sjQScoreMap;
        if (ConstantsExam.EXAM_PAPER_GDXT.equals(examPaper.getSjms())) {
            sjQScoreMap = new HashMap<>();
//            examPaperQuestionsList = examPaperQuestionsService.list(new LambdaQueryWrapper<ExamPaperQuestions>().in(ExamPaperQuestions::getId, answersIds));
        }else if(ConstantsExam.EXAM_PAPER_SJCT.equals(examPaper.getSjms())){
//            List<ExamQuestions> examQuestionsList = examQuestionsService.list(new LambdaQueryWrapper<ExamQuestions>().in(ExamQuestions::getId, answersIds));
//            examPaperQuestionsList = BeanUtil.copyToList(examQuestionsList, ExamPaperQuestions.class);
            List<ExamPaperQuestionsRule> examPaperQuestionsRules = examPaperQuestionsRuleService.list(new LambdaQueryWrapper<ExamPaperQuestionsRule>()
                    .eq(ExamPaperQuestionsRule::getPid, examPaper.getId()));
            sjQScoreMap = examPaperQuestionsRules.stream()
                    .collect(Collectors.toMap(
                            rule -> rule.getPid() + "_" + rule.getGid() + "_" + rule.getSttype(),
                            ExamPaperQuestionsRule::getQscore
                    ));
        } else {
            sjQScoreMap = new HashMap<>();
        }
        /*Map<String, Double> maxScoreMap = new HashMap<>();
        examPaperQuestionsList.stream()
                .collect(Collectors.toMap(
                        epq -> epq.getId(),
                        ExamPaperQuestions::getMaxScore
                ));*/
        List<ExamAnswer> examAnswerList = this.list(new LambdaQueryWrapper<ExamAnswer>().eq(ExamAnswer::getExamInfoId, param.getExamInfoId())
                .eq(ExamAnswer::getSttype, ConstantsExam.EXAM_STTYPE_WD));
        examAnswerList.forEach(examAnswer -> {
            answersList.forEach(answers->{
                if (ConstantsExam.EXAM_STTYPE_WD.equals(examAnswer.getSttype()) && Objects.equals(examAnswer.getContentId(), answers.getContentId())) {
                    //默认取试题分数判断最大打分
                    Double aDouble = examAnswer.getPoint();
                    //随机题按抽题规则分数判断最大打分
                    if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
                        aDouble = sjQScoreMap.get(examAnswer.getPaperId() + "_" + examAnswer.getGid() + "_" + examAnswer.getSttype());
                    }
                    if (answers.getManualScoring() <= aDouble) {
//                        examAnswer.setPoint(answers.getPoint());
                        //写入手工打分字段
                        examAnswer.setManualScoring(answers.getManualScoring());
                        score.updateAndGet(v -> v + answers.getPoint());
                    } else {
                        AssertUtil.throwMessage("超出最大打分值:" + aDouble);
                    }
                }
            });
        });
        ExamAnswerInfo examAnswerInfo = examAnswerInfoService.getById(param.getExamInfoId());
        if (CollectionUtils.isNotEmpty(examAnswerList)) {
            this.saveOrUpdateBatch(examAnswerList);
            examAnswerInfo.setScore(score.get());
        }
        examAnswerInfo.setStatus(ConstantsExam.EXAM_YJZT_YYJ);
        examAnswerInfoService.updateById(examAnswerInfo);

    }

}
