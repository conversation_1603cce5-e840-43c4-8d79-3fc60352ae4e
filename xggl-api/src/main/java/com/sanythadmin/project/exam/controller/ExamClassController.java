package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamClass;
import com.sanythadmin.project.exam.param.ExamClassParam;
import com.sanythadmin.project.exam.service.ExamClassService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/考试分类控制器
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@RestController
@RequestMapping("/api/exam/exam-class")
public class ExamClassController extends BaseController {
    @Resource
    private ExamClassService examClassService;

    /**
     * 分页查询考试分类（权限标识：exam:examClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examClass:list')")
    @GetMapping("/page")
    public PageResult<ExamClass> page(ExamClassParam param) {
        PageParam<ExamClass, ExamClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examClassService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部考试分类（权限标识：exam:examClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examClass:list')")
    @GetMapping()
    public List<ExamClass> list(ExamClassParam param) {
        PageParam<ExamClass, ExamClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examClassService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询考试分类（权限标识：exam:examClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examClass:list')")
    @GetMapping("/{id}")
    public ExamClass get(@PathVariable("id") String id) {
        return examClassService.getById(id);
    }

    /**
     * 添加或修改考试分类（权限标识：exam:examClass:operation）
     */
    @PreAuthorize("hasAuthority('exam:examClass:operation')")
    @OperationLog(module = "考试分类", comments = "保存考试分类")
    @PostMapping("/operation")
    public void save(@RequestBody ExamClass examClass) {
        if (StringUtils.hasLength(examClass.getId())) {
            examClassService.updateById(examClass);
        } else {
            if (examClass.getSort() == null) {
                SortHelper<ExamClass> sortHelper = new SortHelper<>(examClassService, "sort");
                examClass.setSort(sortHelper.next());
            }
            examClassService.save(examClass);
        }
    }

    /**
     * 批量删除考试分类（权限标识：exam:examClass:remove）
     */
    @PreAuthorize("hasAuthority('exam:examClass:remove')")
    @OperationLog(module = "考试分类", comments = "批量删除考试分类")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examClassService.removeByIds(ids);
    }
}
