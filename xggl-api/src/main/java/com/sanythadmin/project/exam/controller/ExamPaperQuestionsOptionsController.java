package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsOptions;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsOptionsParam;
import com.sanythadmin.project.exam.service.ExamPaperQuestionsOptionsService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/试卷试题选项控制器
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@RestController
@RequestMapping("/api/exam/exam-paper-questions-options")
public class ExamPaperQuestionsOptionsController extends BaseController {
    @Resource
    private ExamPaperQuestionsOptionsService examPaperQuestionsOptionsService;

    /**
     * 分页查询试卷试题选项（权限标识：exam:examPaperQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsOptions:list')")
    @GetMapping("/page")
    public PageResult<ExamPaperQuestionsOptions> page(ExamPaperQuestionsOptionsParam param) {
        PageParam<ExamPaperQuestionsOptions, ExamPaperQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examPaperQuestionsOptionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试卷试题选项（权限标识：exam:examPaperQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsOptions:list')")
    @GetMapping()
    public List<ExamPaperQuestionsOptions> list(ExamPaperQuestionsOptionsParam param) {
        PageParam<ExamPaperQuestionsOptions, ExamPaperQuestionsOptionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examPaperQuestionsOptionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试卷试题选项（权限标识：exam:examPaperQuestionsOptions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsOptions:list')")
    @GetMapping("/{id}")
    public ExamPaperQuestionsOptions get(@PathVariable("id") String id) {
        return examPaperQuestionsOptionsService.getById(id);
    }

    /**
     * 添加或修改试卷试题选项（权限标识：exam:examPaperQuestionsOptions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsOptions:operation')")
    @OperationLog(module = "试卷试题选项", comments = "保存试卷试题选项")
    @PostMapping("/operation")
    public void save(@RequestBody ExamPaperQuestionsOptions examPaperQuestionsOptions) {
        if (StringUtils.hasLength(examPaperQuestionsOptions.getId())) {
            examPaperQuestionsOptionsService.updateById(examPaperQuestionsOptions);
        } else {
            if (examPaperQuestionsOptions.getSort() == null) {
                SortHelper<ExamPaperQuestionsOptions> sortHelper = new SortHelper<>(examPaperQuestionsOptionsService, "sort");
                examPaperQuestionsOptions.setSort(sortHelper.next());
            }
            examPaperQuestionsOptionsService.save(examPaperQuestionsOptions);
        }
    }

    /**
     * 批量删除试卷试题选项（权限标识：exam:examPaperQuestionsOptions:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestionsOptions:remove')")
    @OperationLog(module = "试卷试题选项", comments = "批量删除试卷试题选项")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examPaperQuestionsOptionsService.removeByIds(ids);
    }
}
