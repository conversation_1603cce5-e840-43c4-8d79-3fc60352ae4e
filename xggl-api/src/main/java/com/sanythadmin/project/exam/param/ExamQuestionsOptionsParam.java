package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 试题选项查询参数
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamQuestionsOptionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 试题号
     */
    private String code;

    /**
     * 试题名
     */
    @QueryField(type = QueryType.EQ)
    private String name;

    /**
     * 试题ID
     */
    private String qid;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

}
