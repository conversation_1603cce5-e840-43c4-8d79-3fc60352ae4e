package com.sanythadmin.project.exam.util;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 问答题初步打分,暂时使用,不复合学校需求的话,可以舍弃
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
public class AutoKeywordGrading {

    // 停用词列表，用于过滤掉常见无关词
    private static final Set<String> STOP_WORDS = new HashSet<>(Arrays.asList(
        "的", "了", "和", "是", "因为", "但是", "一个", "所以", "而且", "并且", "不", "都", "也"
    ));

    public static Set<String> extractKeywords(String correctAnswer) {
        String cleanAnswer = correctAnswer.toLowerCase().replaceAll("[^\\w\\s]", "");
        String[] words = cleanAnswer.split("\\s+");
        return Arrays.stream(words)
                .filter(word -> !STOP_WORDS.contains(word))
                .collect(Collectors.toSet());
    }

    public static Double calculateScore(String userAnswer, Set<String> keywords, Double totalScore) {
        String cleanUserAnswer = userAnswer.toLowerCase().replaceAll("[^\\w\\s]", "");
        Set<String> userWords = new HashSet<>(Arrays.asList(cleanUserAnswer.split("\\s+")));
        long matchedKeywords = keywords.stream().filter(userWords::contains).count();
        return ((matchedKeywords / keywords.size()) * totalScore);
    }

    public static void main(String[] args) {
        String correctAnswer = "鲁迅不会打周树人，因为他们是同一个人";
        Set<String> keywords = extractKeywords(correctAnswer);
        System.out.println("Extracted keywords: " + keywords);
        String userAnswer = "鲁迅和周树人是同一人";
        double totalScore = 10d;
        double score = calculateScore(userAnswer, keywords, totalScore);
        System.out.println("Student score: " + score + " out of " + totalScore);
    }
}