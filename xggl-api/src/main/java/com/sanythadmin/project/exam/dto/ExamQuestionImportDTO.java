package com.sanythadmin.project.exam.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExamQuestionImportDTO {

    @ExcelProperty("试题分类")
    private String cname;

    @ExcelProperty("试题名称")
    private String name;

    @ExcelProperty("试题类型")
    private String sttype;

    @ExcelProperty("答案")
    private String answer;

    @ExcelProperty("解析")
    private String analysis;

    @ExcelProperty("分值")
    private Double score;

    @ExcelProperty("打分最大分值")
    private Double maxScore;

    @ExcelProperty("选项")
    private String options;



}
