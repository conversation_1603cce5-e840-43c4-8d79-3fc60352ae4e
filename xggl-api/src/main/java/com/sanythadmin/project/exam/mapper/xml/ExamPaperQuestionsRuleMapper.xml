<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperQuestionsRuleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PID, GID, STTYPE, TKNAME, Q_SUM, Q_SCORE, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.pid != null">
                        AND a.PID LIKE concat(concat('%',#{param.pid), '%')
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
                    <if test="param.sttype != null">
                        AND a.STTYPE LIKE concat(concat('%',#{param.sttype), '%')
                    </if>
                    <if test="param.tkname != null">
                        AND a.TKNAME LIKE concat(concat('%',#{param.tkname), '%')
                    </if>
                    <if test="param.qSum != null">
                        AND a.Q_SUM = #{param.qSum}
                    </if>
                    <if test="param.qScore != null">
                        AND a.Q_SCORE = #{param.qScore}
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
