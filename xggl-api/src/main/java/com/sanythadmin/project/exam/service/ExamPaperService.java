package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.entity.ExamPaper;
import com.sanythadmin.project.exam.param.ExamPaperParam;
import com.sanythadmin.project.exam.vo.ExamPaperVO;

import java.util.List;

/**
 * 试卷Service
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
public interface ExamPaperService extends IService<ExamPaper> {

    ExamPaperVO getExamPaperVO(String id);

    ExamPaperVO getExamPaperVO(String id, ExamAnswerInfo examAnswerInfo, Exam exam);

    /**
     * 一次提交
     *
     * @param param param
     */
    void savePaperGroupQuestions(ExamPaperParam param);

    /**
     * 一次删除
     *
     * @param ids ids
     */
    void removePaperGroupQuestions(List<String> ids);


}
