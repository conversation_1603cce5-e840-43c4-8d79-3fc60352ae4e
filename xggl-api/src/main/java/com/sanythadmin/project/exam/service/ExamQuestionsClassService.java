package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.exam.entity.ExamQuestionsClass;

import java.util.List;
import java.util.Map;

/**
 * 试题分类Service
 *
 * <AUTHOR>
 * @since 2024-09-10 17:50:26
 */
public interface ExamQuestionsClassService extends IService<ExamQuestionsClass> {

    public Map<String, String> getAllNameIdMap();

    void removeQuestionsClassByIds(List<String> ids);

}
