package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamQuestions;
import com.sanythadmin.project.exam.param.ExamQuestionsParam;
import com.sanythadmin.project.exam.service.ExamQuestionsService;
import com.sanythadmin.project.exam.vo.ExamQuestionsVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 在线考试/试题控制器
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Slf4j
@RestController
@RequestMapping("/api/exam/exam-questions")
public class ExamQuestionsController extends BaseController {
    @Resource
    private ExamQuestionsService examQuestionsService;

    /**
     * 分页查询试题（权限标识：exam:examQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:list')")
    @GetMapping("/page")
    public PageResult<ExamQuestions> page(ExamQuestionsParam param) {
        PageParam<ExamQuestions, ExamQuestionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examQuestionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 分页查询不在试卷的试题（权限标识：exam:examQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:list')")
    @GetMapping("/getQuestionsNotInPaper")
    public PageResult<ExamQuestions> getQuestionsNotInPaper(ExamQuestionsParam param) {
        return examQuestionsService.getQuestionsNotInPaper(param);
    }

    /**
     * 查询全部试题（权限标识：exam:examQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:list')")
    @GetMapping()
    public List<ExamQuestions> list(ExamQuestionsParam param) {
        PageParam<ExamQuestions, ExamQuestionsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examQuestionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试题（权限标识：exam:examQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:list')")
    @GetMapping("/{id}")
    public ExamQuestionsVO get(@PathVariable("id") String id) {
        return examQuestionsService.getByIdAndOptions(id);
    }

    /**
     * 添加或修改试题（权限标识：exam:examQuestions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:operation')")
    @OperationLog(module = "试题", comments = "保存试题")
    @PostMapping("/operation")
    public void save(@RequestBody ExamQuestionsParam param) {
        examQuestionsService.saveQuestionsAndOptions(param);
    }

    /**
     * 批量删除试题（权限标识：exam:examQuestions:remove）
     */
    @PreAuthorize("hasAuthority('exam:examQuestions:remove')")
    @OperationLog(module = "试题", comments = "批量删除试题")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examQuestionsService.removeQuestionsAndOptions(ids);
    }

    /**
     * 获取题数
     */
//    @PreAuthorize("hasAuthority('exam:examQuestions:getSum')")
    @OperationLog(module = "试题", comments = "获取题数")
    @GetMapping("/getSum")
    public long getSum(ExamQuestionsParam param) {
        PageParam<ExamQuestions, ExamQuestionsParam> page = new PageParam<>(param);
        return examQuestionsService.count(page.getOrderWrapper());
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @OperationLog(module = "试题", comments = "下载导入模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("试题导入模板", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        examQuestionsService.exportTemplate(response.getOutputStream());

    }

    /**
     * 导入试题
     *
     * @param file 导入文件
     */
    @PostMapping("/importExamQuestions")
    @OperationLog(module = "试题", comments = "导入试题")
    @PreAuthorize("hasAuthority('exam:examQuestions:import')")
    public void importData(@RequestParam(name = "file") MultipartFile file){
        examQuestionsService.importExamQuestions(file);
    }

}
