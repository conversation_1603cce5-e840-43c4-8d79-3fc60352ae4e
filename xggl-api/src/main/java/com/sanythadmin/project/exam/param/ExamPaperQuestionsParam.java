package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsOptions;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 试卷试题查询参数
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamPaperQuestionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 试题类型
     */
    private String sttype;

    /**
     * 试题分类ID
     */
    private String cid;

    /**
     * 试题名称
     */
    private String name;

    /**
     * 答案解析
     */
    private String analysis;

    /**
     * 答案
     */
    private String answer;

    /**
     * 试卷ID
     */
    private String pid;

    /**
     * 题库试题ID
     */
    private String qid;

    /*@Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }*/

    /**
     * 分组ID
     */
    private String gid;

    private Double score;

    /**
     * 打分最大分值
     */
    private Double maxScore;

    @QueryField(ignore = true)
    private List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList;

}
