package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsOptions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷试题选项Mapper
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamPaperQuestionsOptionsMapper extends BaseMapper<ExamPaperQuestionsOptions> {


    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QUESTIONS_OPTIONS where pid in "+
            "<foreach collection=\"pids\" item=\"pid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{pid}" +
            "</foreach>"+
            "</script>")
    void deleteByPid(@Param("pids") List<String> pids);

    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QUESTIONS_OPTIONS where gid in "+
            "<foreach collection=\"gids\" item=\"gid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{gid}" +
            "</foreach>"+
            "</script>")
    void deleteByGid(@Param("gids") List<String> gids);

    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QUESTIONS_OPTIONS where P_QID in "+
            "<foreach collection=\"qids\" item=\"qid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{qid}" +
            "</foreach>"+
            "</script>")
    void deleteByQid(@Param("qids") List<String> qids);
}
