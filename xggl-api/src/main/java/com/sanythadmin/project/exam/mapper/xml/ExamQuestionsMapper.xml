<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamQuestionsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ANALYSIS, ANSWER, CREATE_DATE, CZR, NAME, STTYPE, CID, MAX_SCORE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.analysis != null">
                        AND a.ANALYSIS LIKE concat(concat('%',#{param.analysis), '%')
                    </if>
                    <if test="param.answer != null">
                        AND a.ANSWER LIKE concat(concat('%',#{param.answer), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.sttype != null">
                        AND a.STTYPE LIKE concat(concat('%',#{param.sttype), '%')
                    </if>
                    <if test="param.cid != null">
                        AND a.CID LIKE concat(concat('%',#{param.cid), '%')
                    </if>
                    <if test="param.maxScore != null">
                        AND a.MAX_SCORE = #{param.maxScore}
                    </if>
    </sql>
</mapper>
