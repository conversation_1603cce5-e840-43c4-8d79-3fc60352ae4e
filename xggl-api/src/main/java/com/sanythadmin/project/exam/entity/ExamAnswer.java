package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 答卷答案
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_ANSWER")
@Entity
@Table(name = "SYT_EXAM_ANSWER")
public class ExamAnswer implements Serializable {
    private static final long serialVersionUID = 1L;


    public ExamAnswer(String examId, String xgh) {
        this.examId = examId;
        this.xgh = xgh;
    }

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 考试信息id
     */
    @Column(name = "EXAM_INFO_ID")
    @TableField("EXAM_INFO_ID")
    private String examInfoId;
    /**
     * 试卷试题id
     */
    @Column(name = "CONTENT_ID")
    @TableField("CONTENT_ID")
    private String contentId;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;
    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 试题类型
     */
    @Column(name = "STTYPE")
    @TableField("STTYPE")
    private String sttype;

    /**
     * 考试id
     */
    @Column(name = "EXAM_ID")
    @TableField("EXAM_ID")
    private String examId;

    /**
     * 试卷id
     */
    @Column(name = "PAPER_ID")
    @TableField("PAPER_ID")
    private String paperId;

    /**
     * 答题得分
     */
    @Column(name = "POINT",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("POINT")
    private Double point;

    /**
     * 人工打分
     */
    @Column(name = "MANUAL_SCORING",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MANUAL_SCORING")
    private Double manualScoring;

    /**
     * 答题结果
     */
    @Column(name = "RESULT")
    @TableField("RESULT")
    private String result;
    /**
     * 答题结果是否正确
     */
    @Column(name = "RESULT_CORRECT")
    @TableField("RESULT_CORRECT")
    private Boolean resultCorrect;

    /**
     * 答题者用户名
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 考试次数
     */
    @Column(name = "KSCS",columnDefinition = "int default 1")
    @TableField("KSCS")
    private Integer kscs;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

}
