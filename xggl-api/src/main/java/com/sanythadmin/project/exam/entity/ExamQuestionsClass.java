package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 试题分类
 *
 * <AUTHOR>
 * @since 2024-09-10 17:50:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_QUESTIONS_CLASS")
@Entity
@Table(name = "SYT_EXAM_QUESTIONS_CLASS")
public class ExamQuestionsClass implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 分类名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
