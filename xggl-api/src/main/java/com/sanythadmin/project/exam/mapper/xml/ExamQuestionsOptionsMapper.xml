<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamQuestionsOptionsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CODE, NAME, SCORE, QID, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.code != null">
                        AND a.CODE LIKE concat(concat('%',#{param.code), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME = #{param.name}
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE LIKE concat(concat('%',#{param.score), '%')
                    </if>
                    <if test="param.qid != null">
                        AND a.QID LIKE concat(concat('%',#{param.qid), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
