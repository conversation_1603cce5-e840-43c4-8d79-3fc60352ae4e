package com.sanythadmin.project.exam.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.exam.entity.ExamQuestions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试题Mapper
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamQuestionsMapper extends MyMPJBaseMapper<ExamQuestions> {


    @Delete("<script>" +
            "delete from SYT_EXAM_QUESTIONS where CID in "+
            "<foreach collection=\"cids\" item=\"cid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{cid}" +
            "</foreach>"+
            "</script>")
    void deleteByClassIds(@Param("cids") List<String> cids);
}
