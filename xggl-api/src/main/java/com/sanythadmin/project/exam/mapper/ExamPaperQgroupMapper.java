package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamPaperQgroup;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 试卷试题组Mapper
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
public interface ExamPaperQgroupMapper extends BaseMapper<ExamPaperQgroup> {

    //根据试卷id删除试题组
    @Delete("<script>" +
            "delete from SYT_EXAM_PAPER_QGROUP where pid in "+
            "<foreach collection=\"pids\" item=\"pid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{pid}" +
            "</foreach>"+
            "</script>")
    void deleteByPid(@Param("pids") List<String> pids);

    @Update("UPDATE SYT_EXAM_PAPER_QGROUP " +
            "SET q_sum = (SELECT COUNT(*) FROM SYT_EXAM_PAPER_QUESTIONS WHERE GID = #{id}), " +
            "TOTAL_SCORE = (SELECT SUM(SCORE) FROM SYT_EXAM_PAPER_QUESTIONS WHERE GID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumAndScore(@Param("id") String id);

    @Update("UPDATE SYT_EXAM_PAPER_QGROUP " +
            "SET q_sum = (SELECT sum(Q_SUM) FROM SYT_EXAM_PAPER_QUESTIONS_RULE WHERE GID = #{id}), " +
            "TOTAL_SCORE = (SELECT SUM(Q_SCORE*Q_SUM) FROM SYT_EXAM_PAPER_QUESTIONS_RULE WHERE GID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumAndScoreByRule(@Param("id") String id);
}
