package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.*;
import com.sanythadmin.project.exam.mapper.ExamPaperMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQgroupMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsOptionsMapper;
import com.sanythadmin.project.exam.param.ExamPaperParam;
import com.sanythadmin.project.exam.service.*;
import com.sanythadmin.project.exam.util.QuestionSelector;
import com.sanythadmin.project.exam.vo.ExamPaperQgroupVO;
import com.sanythadmin.project.exam.vo.ExamPaperQuestionsVO;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 试卷Service实现
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Service
public class ExamPaperServiceImpl extends ServiceImpl<ExamPaperMapper, ExamPaper> implements ExamPaperService {

    @Resource
    private ExamPaperMapper examPaperMapper;
    @Resource
    private ExamPaperQgroupMapper examPaperQgroupMapper;
    @Resource
    private ExamPaperQuestionsMapper examPaperQuestionsMapper;
    @Resource
    private ExamPaperQuestionsOptionsMapper examPaperQuestionsOptionsMapper;
    @Resource
    private ExamPaperQgroupService examPaperQgroupService;
    @Resource
    private ExamPaperQuestionsService examPaperQuestionsService;
    @Resource
    private ExamPaperQuestionsOptionsService examPaperQuestionsOptionsService;
    @Resource
    private ExamPaperQuestionsRuleService examPaperQuestionsRuleService;
    @Resource
    private ExamQuestionsService examQuestionsService;
    @Resource
    private ExamQuestionsOptionsService examQuestionsOptionsService;
    @Lazy
    @Resource
    private ExamService examService;
    @Lazy
    @Resource
    private ExamAnswerService examAnswerService;


    @Override
    public ExamPaperVO getExamPaperVO(String id) {
        return this.getExamPaperVO(id, null,null);
    }

    @Override
    public ExamPaperVO getExamPaperVO(String id,ExamAnswerInfo examAnswerInfo, Exam exam) {
        ExamPaper examPaper = getById(id);
        if (examPaper == null) {
            AssertUtil.throwMessage("未找到试卷");
        }

        String sfsxda = JudgeMark.NO.getText();
        String ksms = "";
        if (exam != null) {
            sfsxda = exam.getSfxsda();
            ksms = exam.getKsms();
        }

        ExamPaperVO paperVO = new ExamPaperVO();
        BeanUtil.copyProperties(examPaper, paperVO);
        List<ExamPaperQgroup> examPaperQgroupList = examPaperQgroupService.list(new LambdaQueryWrapper<ExamPaperQgroup>().eq(ExamPaperQgroup::getPid, id)
                .orderByAsc(ExamPaperQgroup::getSort));
        List<ExamPaperQgroupVO> examPaperQgroupVOS = BeanUtil.copyToList(examPaperQgroupList, ExamPaperQgroupVO.class);
        LinkedList<ExamPaperQuestionsVO> examPaperQuestionsVOLinkedList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(examPaperQgroupVOS)) {

            if (examAnswerInfo != null) {
                buildFBXT(examPaperQgroupVOS, examAnswerInfo, examPaperQuestionsVOLinkedList, ksms, sfsxda, examPaper.getSjms());
            } else {
                if (ConstantsExam.EXAM_PAPER_GDXT.equals(examPaper.getSjms())) {
                    buildGDXT(id, examPaperQgroupVOS, examPaperQuestionsVOLinkedList, ksms);
                } else if (ConstantsExam.EXAM_PAPER_SJCT.equals(examPaper.getSjms())) {
                    buildSJCT(examPaperQgroupVOS, examPaperQuestionsVOLinkedList, ksms);
                }
            }
            paperVO.setQuestionsGroupList(new LinkedList<>(examPaperQgroupVOS));
            IntStream.range(0, examPaperQuestionsVOLinkedList.size()).forEach(i -> examPaperQuestionsVOLinkedList.get(i).setSort(i));
            paperVO.setExamPaperQuestionsVOList(examPaperQuestionsVOLinkedList);
        }
        return paperVO;
    }

    /**
     * 构建固定选题
     *
     * @param id                 身份证件
     * @param examPaperQgroupVOS qgroup vos试卷
     */
    private void buildGDXT(String id, List<ExamPaperQgroupVO> examPaperQgroupVOS,LinkedList<ExamPaperQuestionsVO> examPaperQuestionsVOLinkedList,String ksms) {
        List<ExamPaperQuestions> examPaperQuestionsList = examPaperQuestionsService.list(new LambdaQueryWrapper<ExamPaperQuestions>().eq(ExamPaperQuestions::getPid, id)
                .orderByAsc(ExamPaperQuestions::getSort));
        if (CollectionUtils.isNotEmpty(examPaperQuestionsList)) {
            List<ExamPaperQuestionsVO> examPaperQuestionsVOList = BeanUtil.copyToList(examPaperQuestionsList, ExamPaperQuestionsVO.class);

            List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = examPaperQuestionsOptionsService.list(new LambdaQueryWrapper<ExamPaperQuestionsOptions>()
                    .eq(ExamPaperQuestionsOptions::getPid, id).orderByAsc(ExamPaperQuestionsOptions::getSort));
            if (CollectionUtils.isNotEmpty(examPaperQuestionsOptionsList)) {
                Map<String, List<ExamPaperQuestionsOptions>> questionsOptionsByQId = examPaperQuestionsOptionsList.stream()
                        .collect(Collectors.groupingBy(ExamPaperQuestionsOptions::getPqid,
                                LinkedHashMap::new,
                                Collectors.toList()));
                for (ExamPaperQuestionsVO examPaperQuestionsVO  : examPaperQuestionsVOList) {
                    List<ExamPaperQuestionsOptions> examPaperQuestionsOptions = questionsOptionsByQId.getOrDefault(examPaperQuestionsVO.getId(), new ArrayList<>());
                    examPaperQuestionsVO.setExamPaperQuestionsOptionsList(new LinkedList<>(examPaperQuestionsOptions));
                }
                Map<String, List<ExamPaperQuestionsVO>> questionsByGroupId = examPaperQuestionsVOList.stream()
                        .collect(Collectors.groupingBy(ExamPaperQuestionsVO::getGid,
                                LinkedHashMap::new,
                                Collectors.toList()));
                for (ExamPaperQgroupVO groupVO : examPaperQgroupVOS) {
                    List<ExamPaperQuestionsVO> examPaperQuestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());
                    //考试模式显示答案处理
                    if (ConstantsExam.EXAM_KSMS_KSMS.equals(ksms)) {
                        examPaperQuestions.forEach(questions->{
                            questions.setAnswer(null);
                            questions.setAnalysis(null);
                        });
                    }
                    groupVO.setExamPaperQuestionsVOList(new LinkedList<>(examPaperQuestions));
                    examPaperQuestionsVOLinkedList.addAll(new LinkedList<>(examPaperQuestions));
                }
            }

        }
    }

    /**
     * 构建副本选题
     *
     * @param examAnswerInfo 考试信息
     */
    private void buildFBXT(List<ExamPaperQgroupVO> examPaperQgroupVOS,ExamAnswerInfo examAnswerInfo,LinkedList<ExamPaperQuestionsVO> examPaperQuestionsVOLinkedList
            ,String ksms,String sfxsda,String sjms) {
        //重考查询副本 区分固定，随机
        List<ExamAnswer> examAnswerList = examAnswerService.list(new LambdaQueryWrapper<ExamAnswer>().eq(ExamAnswer::getExamInfoId, examAnswerInfo.getId()));

        Map<String, ExamAnswer> exanAnswerByPid = examAnswerList.stream().collect(Collectors.toMap(ExamAnswer::getContentId,answer -> answer));
        Map<String, List<ExamAnswer>> exanAnswerByGid = examAnswerList.stream().collect(Collectors.groupingBy(ExamAnswer::getGid, HashMap::new, Collectors.toList()));
        List<String> examAnswerContentIds = examAnswerList.stream().map(examAnswer1 -> examAnswer1.getContentId()).collect(Collectors.toList());
        Map<String, Double> sjQScoreMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(examAnswerContentIds)) {
            List<ExamPaperQuestions> examPaperQuestionsList = new LinkedList<>();
            if (ConstantsExam.EXAM_PAPER_GDXT.equals(sjms)) {
                examPaperQuestionsList = examPaperQuestionsService.list(new LambdaQueryWrapper<ExamPaperQuestions>().in(ExamPaperQuestions::getId, examAnswerContentIds).orderByAsc(ExamPaperQuestions::getSort));

            } else if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
                List<ExamQuestions> examQuestionsList = examQuestionsService.list(new LambdaQueryWrapper<ExamQuestions>().in(ExamQuestions::getId, examAnswerContentIds));
                //关联答题表查询题库试题，并之前的答题顺序排序
//                List<ExamQuestions> examQuestionsList = examAnswerService.getAnsweredExamQuestionsForSJ(examAnswerContentIds,examAnswer.getKscs());
                examPaperQuestionsList = BeanUtil.copyToList(examQuestionsList, ExamPaperQuestions.class);
                List<ExamPaperQuestionsRule> examPaperQuestionsRules = examPaperQuestionsRuleService.list(new LambdaQueryWrapper<ExamPaperQuestionsRule>()
                        .eq(ExamPaperQuestionsRule::getPid, examAnswerInfo.getPaperId()));
                sjQScoreMap = examPaperQuestionsRules.stream()
                        .collect(Collectors.toMap(
                                rule -> rule.getPid() + "_" + rule.getGid() + "_" + rule.getSttype(),
                                ExamPaperQuestionsRule::getQscore
                        ));
            }


            List<ExamPaperQuestionsVO> examPaperQuestionsVOList = BeanUtil.copyToList(examPaperQuestionsList, ExamPaperQuestionsVO.class);

            //问题选项
            List<ExamPaperQuestionsOptions> examQuestionsOptionsList = new LinkedList<>();
            Map<String, List<ExamPaperQuestionsOptions>> questionsOptionsByQId = new HashMap<>();
            if (ConstantsExam.EXAM_PAPER_GDXT.equals(sjms)) {
                examQuestionsOptionsList = examPaperQuestionsOptionsService.list(new LambdaQueryWrapper<ExamPaperQuestionsOptions>()
                                .in(ExamPaperQuestionsOptions::getPqid, examAnswerContentIds).orderByAsc(ExamPaperQuestionsOptions::getSort));
            } else if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)){
                List<ExamQuestionsOptions> examQuestionsOptions = examQuestionsOptionsService.list(new LambdaQueryWrapper<ExamQuestionsOptions>()
                        .in(ExamQuestionsOptions::getQid, examAnswerContentIds).orderByAsc(ExamQuestionsOptions::getSort));
//                examQuestionsOptionsList = BeanUtil.copyToList(examQuestionsOptions, ExamPaperQuestionsOptions.class);
                examQuestionsOptionsList = new ArrayList<>();
                for (ExamQuestionsOptions examOption : examQuestionsOptions) {
                    ExamPaperQuestionsOptions paperOption = new ExamPaperQuestionsOptions();
                    paperOption.setPqid(examOption.getQid());  // 手动处理字段不一致情况
                    BeanUtil.copyProperties(examOption, paperOption);
                    examQuestionsOptionsList.add(paperOption);
                }
            }

            questionsOptionsByQId = examQuestionsOptionsList.stream()
                    .collect(Collectors.groupingBy(ExamPaperQuestionsOptions::getPqid,
                            LinkedHashMap::new,
                            Collectors.toList()));
            for (ExamPaperQuestionsVO examPaperQuestionsVO : examPaperQuestionsVOList) {
                List<ExamPaperQuestionsOptions> examPaperQuestionsOptions = questionsOptionsByQId.getOrDefault(examPaperQuestionsVO.getId(), new ArrayList<>());
//                List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = BeanUtil.copyToList(examPaperQuestionsOptions, ExamPaperQuestionsOptions.class);
                examPaperQuestionsVO.setExamPaperQuestionsOptionsList(new LinkedList<>(examPaperQuestionsOptions));
            }

            //如果随机题库的试题没有gid下面方法查不到
            /*Map<String, List<ExamPaperQuestionsVO>> questionsByGroupId = examPaperQuestionsVOList.stream()
                    .collect(Collectors.groupingBy(ExamPaperQuestionsVO::getGid,
                            LinkedHashMap::new,
                            Collectors.toList()));*/
            //区分sjms得到examPaperQuestionsList后，匹配exanAnswerByGid转为Map<String,List<ExamPaperQuestions>>

            Map<String, List<ExamPaperQuestionsVO>> questionsByGroupId;
            if (ConstantsExam.EXAM_PAPER_GDXT.equals(sjms)) {
                questionsByGroupId = examPaperQuestionsVOList.stream()
                        .collect(Collectors.groupingBy(ExamPaperQuestionsVO::getGid,
                                LinkedHashMap::new,
                                Collectors.toList()));
            } else {
                questionsByGroupId = new HashMap<>();
                if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
                    Map<String, Double> finalSjQScoreMap = sjQScoreMap;
                    exanAnswerByGid.forEach((key, value) -> {
                        List<ExamPaperQuestionsVO> subExamPaperQuestionsVO = value.stream()
                                .flatMap(p1 -> examPaperQuestionsVOList.stream()
                                        .filter(p2 -> Objects.equals(p1.getContentId(), p2.getId()))
                                        .peek(p2->{
                                            p2.setPid(p1.getPaperId());
                                            p2.setSttype(p1.getSttype());
                                            p2.setGid(p1.getGid());
                                            p2.setSort(p1.getSort());
                                            p2.setScore(finalSjQScoreMap.get(p1.getPaperId() + "_" + p1.getGid() + "_" + p1.getSttype()));
                                        })
                                        .map(p2 -> p2)
                                )
                                .toList();
                        //创建副本排序，按Answer的sort排序
                        List<ExamPaperQuestionsVO> modifiableSubExamPaperQuestionsVO = new ArrayList<>(subExamPaperQuestionsVO);
                        modifiableSubExamPaperQuestionsVO.sort(Comparator.comparing(ExamPaperQuestionsVO::getSort));
                        questionsByGroupId.put(key, modifiableSubExamPaperQuestionsVO);
                    });
                }
            }

            for (ExamPaperQgroupVO groupVO : examPaperQgroupVOS) {
                List<ExamPaperQuestionsVO> examPaperQuestions = new LinkedList<>();
                if (ConstantsExam.EXAM_PAPER_GDXT.equals(sjms)) {
                    examPaperQuestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());
                } else
                if (ConstantsExam.EXAM_PAPER_SJCT.equals(sjms)) {
                    examPaperQuestions = questionsByGroupId.getOrDefault(groupVO.getId(), new ArrayList<>());
                }
                //考试模式显示答案处理
                /*if (ConstantsExam.EXAM_KSMS_KSMS.equals(ksms) && JudgeMark.NO.getText().equals(sfxsda)) {
                    examPaperQuestions.forEach(questions -> {
                        questions.setAnswer(null);
                        questions.setAnalysis(null);
                    });
                }*/
                //回显考试结果
                examPaperQuestions.forEach(examPaperQuestion->{
                    setResultForExamPaperQuestions(examPaperQuestion, exanAnswerByPid);
                });
                groupVO.setExamPaperQuestionsVOList(new LinkedList<>(examPaperQuestions));
                examPaperQuestionsVOLinkedList.addAll(new LinkedList<>(examPaperQuestions));
            }

        }

    }

    /**
     * 回显考试答题结果
     *
     */
    private ExamPaperQuestions setResultForExamPaperQuestions(ExamPaperQuestions examPaperQuestions,  Map<String, ExamAnswer> exanAnswerByPid) {
        if (MapUtil.isNotEmpty(exanAnswerByPid)) {
            ExamAnswer answer = exanAnswerByPid.get(examPaperQuestions.getId());
            examPaperQuestions.setResult(answer.getResult());
            examPaperQuestions.setManualScoring(answer.getManualScoring());
            examPaperQuestions.setResultCorrect(answer.getResultCorrect());
        }
        return examPaperQuestions;
    }

    /**
     * 构建随机抽题
     *
     * @param examPaperQgroupVOS 问题组vo
     */
    private void buildSJCT(List<ExamPaperQgroupVO> examPaperQgroupVOS,LinkedList<ExamPaperQuestionsVO> examPaperQuestionsVOLinkedList,String ksms) {
        Set<String> selectedQuestionIds = new HashSet<>();
        examPaperQgroupVOS.forEach(questionsGroupVO->{
            List<ExamPaperQuestionsRule> questionsRulesList = examPaperQuestionsRuleService.list(new LambdaQueryWrapper<ExamPaperQuestionsRule>()
                    .eq(ExamPaperQuestionsRule::getGid, questionsGroupVO.getId()));
            if (CollectionUtils.isNotEmpty(questionsRulesList)) {
                List<ExamQuestions> allQuestions = examQuestionsService.list();
                QuestionSelector<ExamQuestions> questionSelector = new QuestionSelector<>(
                    allQuestions,
                    (question, rule) -> {
                        ExamPaperQuestionsRule examRule = (ExamPaperQuestionsRule) rule;
                        return question.getSttype().equals(examRule.getSttype())
                                && question.getCid().equals(examRule.getCid());
                    },
                    (question, rule) -> {
                        ExamPaperQuestionsRule examRule = (ExamPaperQuestionsRule) rule;
                        question.setScore(examRule.getQscore());
                        question.setGname(examRule.getGname());
                        question.setQid(question.getId());
                    }
                );
                List<ExamQuestions> examQuestionsList = questionSelector.selectQuestionsForPaper(questionsRulesList,selectedQuestionIds,ExamQuestions::getId);
                List<String> examQuestionsIds = examQuestionsList.stream().map(questions -> questions.getId()).collect(Collectors.toList());

                List<ExamPaperQuestions> examPaperQuestionsList = BeanUtil.copyToList(examQuestionsList, ExamPaperQuestions.class);
                List<ExamPaperQuestionsVO> examPaperQuestionsVOList = BeanUtil.copyToList(examPaperQuestionsList, ExamPaperQuestionsVO.class);

                examPaperQuestionsVOList.forEach(questions->{
                    //考试模式显示答案处理
                    if (ConstantsExam.EXAM_KSMS_KSMS.equals(ksms)) {
                        questions.setAnswer(null);
                        questions.setAnalysis(null);
                    }
                    questions.setGid(questionsGroupVO.getId());
                });

                if (CollectionUtils.isNotEmpty(examQuestionsIds)) {
                    //问题选项
                    List<ExamQuestionsOptions> examQuestionsOptionsList = examQuestionsOptionsService.list(new LambdaQueryWrapper<ExamQuestionsOptions>()
                            .in(ExamQuestionsOptions::getQid, examQuestionsIds).orderByAsc(ExamQuestionsOptions::getSort));
                    Map<String, List<ExamQuestionsOptions>> questionsOptionsByQId = examQuestionsOptionsList.stream()
                            .collect(Collectors.groupingBy(ExamQuestionsOptions::getQid,
                                    LinkedHashMap::new,
                                    Collectors.toList()));
                    for (ExamPaperQuestionsVO examPaperQuestionsVO  : examPaperQuestionsVOList) {
                        List<ExamQuestionsOptions> examQuestionsOptions = questionsOptionsByQId.getOrDefault(examPaperQuestionsVO.getId(), new ArrayList<>());
                        List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = BeanUtil.copyToList(examQuestionsOptions, ExamPaperQuestionsOptions.class);
                        examPaperQuestionsVO.setExamPaperQuestionsOptionsList(new LinkedList<>(examPaperQuestionsOptionsList));
                    }
                }
                questionsGroupVO.setExamPaperQuestionsVOList(new LinkedList<>(examPaperQuestionsVOList));
                examPaperQuestionsVOLinkedList.addAll(new LinkedList<>(examPaperQuestionsVOList));
            }
        });

    }

    @Transactional
    @Override
    public void savePaperGroupQuestions(ExamPaperParam param) {
        AssertUtil.hasLength(param.getId(), "试卷id不能为空");
        ExamPaper examPaper= getById(param.getId());
        if (StringUtils.hasLength(param.getId())) {
            examPaperQgroupMapper.deleteByPid(Collections.singletonList(param.getId()));
            examPaperQuestionsMapper.deleteByPid(Collections.singletonList(param.getId()));
            examPaperQuestionsOptionsMapper.deleteByPid(Collections.singletonList(param.getId()));
        }

        List<ExamPaperQgroupVO> questionsGroupVOList = param.getExamPaperQgroupVOList();
        AtomicReference<Integer> totalSum = new AtomicReference<>(0);
        AtomicReference<Double> totalScore = new AtomicReference<>(0d);

        List<ExamPaperQgroup> examPaperQgroupList = BeanUtil.copyToList(questionsGroupVOList, ExamPaperQgroup.class);
        examPaperQgroupList.forEach(qGroup->{
            qGroup.setPid(examPaper.getId());
        });
        examPaperQgroupService.saveOrUpdateBatch(examPaperQgroupList);

        Map<String, String> gqMap = new HashMap<>();
        examPaperQgroupList.forEach(qGroup->{
            gqMap.put(qGroup.getName(), qGroup.getId());
        });

        //设置试题
        List<ExamPaperQuestions> questionsList;
        List<ExamPaperQuestionsVO> questionsVOList = new ArrayList<>();
        if (ConstantsExam.EXAM_PAPER_GDXT.equals(examPaper.getSjms())) {
            //计算每组试题数和分数，以及试卷总题数和分数
            questionsGroupVOList.forEach(questionsGroupVO->{
                questionsGroupVO.setQsum(questionsGroupVO.getExamPaperQuestionsVOList().size());
                //固定试题的每题分数在分组上设置，单个题上没有分数
                /*double qScore = questionsGroupVO.getExamPaperQuestionsVOList()
                        .stream()
                        .mapToDouble(ExamPaperQuestionsVO::getScore)
                        .sum();*/
//                questionsGroupVO.setQscore(qScore);
                questionsGroupVO.setTotalScore(questionsGroupVO.getQsum() * questionsGroupVO.getQscore());
                totalSum.updateAndGet(v -> v + questionsGroupVO.getQsum());
                totalScore.updateAndGet(v -> v + questionsGroupVO.getTotalScore());     //每个分组的totalScore之和
                //把分组的每题分数给到试卷试题分数字段
                questionsGroupVO.getExamPaperQuestionsVOList().forEach(examPaperQuestionsVO -> examPaperQuestionsVO.setScore(questionsGroupVO.getQscore()));
                questionsVOList.addAll(questionsGroupVO.getExamPaperQuestionsVOList());
            });
            questionsList = BeanUtil.copyToList(questionsVOList, ExamPaperQuestions.class);
            AtomicInteger index = new AtomicInteger(1);
            questionsList.forEach(questions->{
                questions.setPid(examPaper.getId());
                questions.setGid(gqMap.get(questions.getGname()));
                questions.setSort(index.getAndIncrement());
            });
            examPaperQuestionsService.saveOrUpdateBatch(questionsList);

            Map<String, String> pqMap = new HashMap<>();
            questionsList.forEach(questions->{
                pqMap.put(questions.getName(), questions.getId());
            });
            //设置试题项
            List<ExamPaperQuestionsOptions> examPaperQuestionsOptionsList = new ArrayList<>();
            questionsVOList.forEach(questionsVO->{
                examPaperQuestionsOptionsList.addAll(questionsVO.getExamPaperQuestionsOptionsList());
            });
            examPaperQuestionsOptionsList.forEach(paperQuestionsOptions->{
                paperQuestionsOptions.setPid(examPaper.getId());
                paperQuestionsOptions.setPqid(pqMap.get(paperQuestionsOptions.getPqname()));
            });
            examPaperQuestionsOptionsService.saveOrUpdateBatch(examPaperQuestionsOptionsList);
        } else {
            if (ConstantsExam.EXAM_PAPER_SJCT.equals(examPaper.getSjms())) {
                //随机抽题
                questionsGroupVOList.forEach(questionsGroupVO -> {
                    int qSum = questionsGroupVO.getQuestionsRulesList().stream().mapToInt(ExamPaperQuestionsRule::getQsum).sum();
                    questionsGroupVO.setQsum(qSum);
                    //随机抽题的每题分数在抽题规则上设置
                    double qScore = questionsGroupVO.getQuestionsRulesList().stream().mapToDouble(ExamPaperQuestionsRule::getQscore).sum();
                    questionsGroupVO.setQscore(qScore);
                    questionsGroupVO.setTotalScore(questionsGroupVO.getQsum() * questionsGroupVO.getQscore());

                    totalSum.updateAndGet(v -> v + questionsGroupVO.getQsum());
                    totalScore.updateAndGet(v -> v + qScore);

                    List<ExamPaperQuestionsRule> questionsRulesList = questionsGroupVO.getQuestionsRulesList();
                    questionsRulesList.forEach(examPaperQuestionsRule -> {
                        examPaperQuestionsRule.setGid(gqMap.get(examPaperQuestionsRule.getGname()));
                    });
                    //保存抽题规则
                    examPaperQuestionsRuleService.saveOrUpdateBatch(questionsRulesList);
                });
            }

        }


        examPaper.setScore(totalScore.get());
        examPaper.setSubjectSum(totalSum.get());
        this.updateById(examPaper);




    }

    @Transactional
    @Override
    public void removePaperGroupQuestions(List<String> ids) {
        ids.forEach(id->{
            long count = examService.count(new LambdaQueryWrapper<Exam>().eq(Exam::getPid, id));
            if (count > 0) {
                AssertUtil.throwMessage("存在已关联考试的试卷，不可删除");
            }
        });
        examPaperQgroupMapper.deleteByPid(ids);
        examPaperQuestionsMapper.deleteByPid(ids);
        examPaperQuestionsOptionsMapper.deleteByPid(ids);
        removeByIds(ids);
    }

}
