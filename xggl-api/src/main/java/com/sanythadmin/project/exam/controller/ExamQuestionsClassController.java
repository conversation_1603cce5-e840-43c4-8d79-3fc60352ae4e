package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamQuestionsClass;
import com.sanythadmin.project.exam.param.ExamQuestionsClassParam;
import com.sanythadmin.project.exam.service.ExamQuestionsClassService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 在线考试/试题分类控制器
 *
 * <AUTHOR>
 * @since 2024-09-10 17:50:26
 */
@RestController
@RequestMapping("/api/exam/exam-questions-class")
public class ExamQuestionsClassController extends BaseController {
    @Resource
    private ExamQuestionsClassService examQuestionsClassService;

    /**
     * 分页查询试题分类（权限标识：exam:examQuestionsClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsClass:list')")
    @GetMapping("/page")
    public PageResult<ExamQuestionsClass> page(ExamQuestionsClassParam param) {
        PageParam<ExamQuestionsClass, ExamQuestionsClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examQuestionsClassService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试题分类（权限标识：exam:examQuestionsClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsClass:list')")
    @GetMapping()
    public List<ExamQuestionsClass> list(ExamQuestionsClassParam param) {
        PageParam<ExamQuestionsClass, ExamQuestionsClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examQuestionsClassService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试题分类（权限标识：exam:examQuestionsClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsClass:list')")
    @GetMapping("/{id}")
    public ExamQuestionsClass get(@PathVariable("id") String id) {
        return examQuestionsClassService.getById(id);
    }

    /**
     * 添加或修改试题分类（权限标识：exam:examQuestionsClass:operation）
     */
//    @PreAuthorize("hasAuthority('exam:examQuestionsClass:operation')")
    @OperationLog(module = "试题分类", comments = "保存试题分类")
    @PostMapping("/operation")
    public void save(@RequestBody ExamQuestionsClass examQuestionsClass) {

        if (StringUtils.hasLength(examQuestionsClass.getId())) {
            examQuestionsClassService.updateById(examQuestionsClass);
        } else {
            if (examQuestionsClass.getSort() == null) {
                SortHelper<ExamQuestionsClass> sortHelper = new SortHelper<>(examQuestionsClassService, "sort");
                examQuestionsClass.setSort(sortHelper.next());
            }
            examQuestionsClass.setCreateDate(LocalDateTime.now());
            examQuestionsClassService.save(examQuestionsClass);
        }
    }

    /**
     * 批量删除试题分类（权限标识：exam:examQuestionsClass:remove）
     */
    @PreAuthorize("hasAuthority('exam:examQuestionsClass:remove')")
    @OperationLog(module = "试题分类", comments = "批量删除试题分类")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examQuestionsClassService.removeByIds(ids);
//        examQuestionsClassService.removeQuestionsClassByIds(ids);
    }
}
