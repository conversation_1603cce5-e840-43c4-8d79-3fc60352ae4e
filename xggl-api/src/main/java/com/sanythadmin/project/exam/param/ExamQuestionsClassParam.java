package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 试题分类查询参数
 *
 * <AUTHOR>
 * @since 2024-09-10 17:50:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamQuestionsClassParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    private String bz;

    private String createDate;

    /**
     * 分类名称
     */
    private String name;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

}
