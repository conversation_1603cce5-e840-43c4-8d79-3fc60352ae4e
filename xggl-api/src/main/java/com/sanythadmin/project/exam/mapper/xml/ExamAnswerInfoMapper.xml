<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamAnswerInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, EXAM_ID, PAPER_ID, USED_TIME, DDT, DCS, SCORE, SFHG, CREATE_DATE, XGH, KSCS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.examId != null">
                        AND a.EXAM_ID LIKE concat(concat('%',#{param.examId), '%')
                    </if>
                    <if test="param.paperId != null">
                        AND a.PAPER_ID LIKE concat(concat('%',#{param.paperId), '%')
                    </if>
                    <if test="param.usedTime != null">
                        AND a.USED_TIME = #{param.usedTime}
                    </if>
                    <if test="param.ddt != null">
                        AND a.DDT = #{param.ddt}
                    </if>
                    <if test="param.dcs != null">
                        AND a.DCS = #{param.dcs}
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE = #{param.score}
                    </if>
                    <if test="param.sfhg != null">
                        AND a.SFHG LIKE concat(concat('%',#{param.sfhg), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH = #{param.xgh}
                    </if>
                    <if test="param.kscs != null">
                        AND a.KSCS = #{param.kscs}
                    </if>
    </sql>
</mapper>
