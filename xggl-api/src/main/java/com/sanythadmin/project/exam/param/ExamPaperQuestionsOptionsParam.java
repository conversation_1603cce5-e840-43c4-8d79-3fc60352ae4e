package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 试卷试题选项查询参数
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamPaperQuestionsOptionsParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 试题编号
     */
    private String code;

    /**
     * 试题名称
     */
    private String name;


    /**
     * 试卷试题ID
     */
    private String pQid;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

}
