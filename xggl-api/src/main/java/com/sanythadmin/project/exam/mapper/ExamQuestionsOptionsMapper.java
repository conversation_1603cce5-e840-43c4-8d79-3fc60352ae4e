package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamQuestionsOptions;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试题选项Mapper
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamQuestionsOptionsMapper extends BaseMapper<ExamQuestionsOptions> {

    @Delete("<script>" +
            "delete from SYT_EXAM_QUESTIONS_OPTIONS where QID in "+
            "<foreach collection=\"qids\" item=\"qid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{qid}" +
            "</foreach>"+
            "</script>")
    void deleteByQid(@Param("qids") List<String> qids);

    @Delete("<script>" +
            "DELETE FROM SYT_EXAM_QUESTIONS_OPTIONS\n" +
            "    WHERE QID IN (SELECT ID FROM SYT_EXAM_QUESTIONS WHERE CID IN  " +
            "<foreach collection=\"cids\" item=\"cid\" index=\"index\" open=\"(\" separator=\",\" close=\")\">" +
            "#{cid}" +
            "</foreach>" +
            ")" +
            "</script>")
    void deleteByClassIds(@Param("cids") List<String> cids);
}
