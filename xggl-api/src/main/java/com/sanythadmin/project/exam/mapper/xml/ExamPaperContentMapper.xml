<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperContentMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PID, QID, XGH, EXAM_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.pid != null">
                        AND a.PID LIKE concat(concat('%',#{param.pid), '%')
                    </if>
                    <if test="param.qid != null">
                        AND a.QID LIKE concat(concat('%',#{param.qid), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.examId != null">
                        AND a.EXAM_ID LIKE concat(concat('%',#{param.examId), '%')
                    </if>
    </sql>
</mapper>
