package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 试卷试题抽题规则
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_PAPER_QUESTIONS_RULE")
@Entity
@Table(name = "SYT_EXAM_PAPER_QUESTIONS_RULE")
public class ExamPaperQuestionsRule implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试卷ID
     */
    @Column(name = "PID")
    @TableField("PID")
    private String pid;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 分组名称
     */
    @TableField(exist = false)
    private String gname;

    /**
     * 试题类型
     */
    @Column(name = "STTYPE")
    @TableField("STTYPE")
    private String sttype;

    /**
     * 试题分类id
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 抽题数
     */
    @Column(name = "Q_SUM")
    @TableField("Q_SUM")
    private Integer qsum;

    /**
     * 每题分数
     */
    @Column(name = "Q_SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("Q_SCORE")
    private Double qscore;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
