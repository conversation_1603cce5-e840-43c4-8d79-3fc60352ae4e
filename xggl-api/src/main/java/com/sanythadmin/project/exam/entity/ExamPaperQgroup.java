package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 试卷试题组
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_PAPER_QGROUP")
@Entity
@Table(name = "SYT_EXAM_PAPER_QGROUP")
public class ExamPaperQgroup implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试卷id
     */
    @Column(name = "PID")
    @TableField("PID")
    private String pid;

    /**
     * 组名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试题数
     */
    @Column(name = "Q_SUM")
    @TableField("Q_SUM")
    private Integer qsum;

    /**
     * 每题分数
     */
    @Column(name = "Q_SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("Q_SCORE")
    private Double qscore;

    /**
     * 总分
     */
    @Column(name = "TOTAL_SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("TOTAL_SCORE")
    private Double totalScore;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
