package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 考试记录
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_RECORD")
@Entity
@Table(name = "SYT_EXAM_RECORD")
public class ExamRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 开始时间
     */
    @Column(name = "BEGIN_DATE")
    @TableField("BEGIN_DATE")
    private LocalDateTime beginDate;

    /**
     * 结束时间
     */
    @Column(name = "END_DATE")
    @TableField("END_DATE")
    private LocalDateTime endDate;

    /**
     * 考试id
     */
    @Column(name = "EXAM_ID")
    @TableField("EXAM_ID")
    private String examId;

    /**
     * 考试次数
     */
    @Column(name = "KSCS")
    @TableField("KSCS")
    private Long kscs;

    /**
     * 试卷id
     */
    @Column(name = "PAPER_ID")
    @TableField("PAPER_ID")
    private String paperId;

    /**
     * 账号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

}
