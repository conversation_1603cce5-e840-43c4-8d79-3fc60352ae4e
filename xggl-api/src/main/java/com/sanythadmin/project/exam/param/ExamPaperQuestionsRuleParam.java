package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 试卷试题抽题规则查询参数
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamPaperQuestionsRuleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 试卷ID
     */
    private String pid;

    /**
     * 分组ID
     */
    private String gid;

    /**
     * 试题类型
     */
    private String sttype;

    /**
     * 试题分类
     */
    private String cid;

    /**
     * 抽题数
     */
    @QueryField(type = QueryType.EQ)
    private Integer qsum;

    /**
     * 每题分数
     */
    @QueryField(type = QueryType.EQ)
    private Integer qscore;

    /*@Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }*/

}
