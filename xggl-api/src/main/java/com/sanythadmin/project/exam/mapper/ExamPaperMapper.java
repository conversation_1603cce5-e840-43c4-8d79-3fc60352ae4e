package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.exam.entity.ExamPaper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 试卷Mapper
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
public interface ExamPaperMapper extends BaseMapper<ExamPaper> {

    @Update("UPDATE syt_exam_paper " +
            "SET SUBJECT_SUM = (SELECT COUNT(*) FROM SYT_EXAM_PAPER_QUESTIONS WHERE PID = #{id}), " +
            "SCORE = (SELECT SUM(SCORE) FROM SYT_EXAM_PAPER_QUESTIONS WHERE PID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumAndScore(@Param("id") String id);

    @Update("UPDATE syt_exam_paper " +
            "SET SUBJECT_SUM = (SELECT sum(Q_SUM) FROM SYT_EXAM_PAPER_QUESTIONS_RULE WHERE PID = #{id}), " +
            "SCORE = (SELECT SUM(Q_SCORE*Q_SUM) FROM SYT_EXAM_PAPER_QUESTIONS_RULE WHERE PID = #{id}) " +
            "WHERE id = #{id}")
    void updateSumAndScoreByRule(@Param("id") String id);

}
