<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperQgroupMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PID, NAME, Q_SUM, Q_SCORE, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.pid != null">
                        AND a.PID LIKE concat(concat('%',#{param.pid), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.qSum != null">
                        AND a.Q_SUM = #{param.qSum}
                    </if>
                    <if test="param.qScore != null">
                        AND a.Q_SCORE LIKE concat(concat('%',#{param.qScore), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
