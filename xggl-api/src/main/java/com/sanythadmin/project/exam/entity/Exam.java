package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 在线考试
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM")
@Entity
@Table(name = "SYT_EXAM")
public class Exam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 考试名称
     */
    @Column(name = "KSMC")
    @TableField("KSMC")
    private String ksmc;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "KSSJ")
    @TableField("KSSJ")
    private LocalDateTime kssj;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "JSSJ")
    @TableField("JSSJ")
    private LocalDateTime jssj;

    /**
     * 试卷id
     */
    @Column(name = "PID")
    @TableField("PID")
    private String pid;

    /**
     * 发卷情况
     */
    @Column(name = "FJQK")
    @TableField("FJQK")
    private String fjqk;

    /**
     * 合格线
     */
    @Column(name = "HGX",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("HGX")
    private Double hgx;

    /**
     * 设置时长(分钟)
     */
    @Column(name = "UPTIME")
    @TableField("UPTIME")
    private Long uptime;

    /**
     * 考试模式
     */
    @Column(name = "KSMS")
    @TableField("KSMS")
    private String ksms;
    /**
     * 是否显示答案
     */
    @Column(name = "SFXSDA")
    @TableField("SFXSDA")
    private String sfxsda;

    /**
     * 答题模式
     */
    @Column(name = "DTMS")
    @TableField("DTMS")
    private String dtms;

    /**
     * 启用人工阅卷
     */
    @Column(name = "RGYJ")
    @TableField("RGYJ")
    private String rgyj;

    /**
     * 考试次数
     */
    @Column(name = "CKCS")
    @TableField("CKCS")
    private Integer ckcs;

    /**
     * 是否指定人员
     */
    @Column(name = "SFZDRY")
    @TableField("SFZDRY")
    private String sfzdry;

    /**
     * 考试分类id
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 发卷人数
     */
    @Column(name = "FJRS")
    @TableField("FJRS")
    private Integer fjrs;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 操作人
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 考试说明
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 成绩排名方式
     */
    @Column(name = "CJPMFS")
    @TableField("CJPMFS")
    private String cjpmfs;

    /**
     * 时间是否合法
     *
     * @return
     */
    @Transient
    @TableField(exist = false)
    private boolean timeIsCorrect;
    /**
     * 考试次数是否有效
     */
    @Transient
    @TableField(exist = false)
    private boolean kscsIsCorrect;
    /**
     * 是否已答
     */
    @Transient
    @TableField(exist = false)
    private boolean answered;

    /**
     * 已考次数
     */
    @Transient
    @TableField(exist = false)
    private Integer ykcs;


    @Transient
    @TableField(exist = false)
    private String status;

    public boolean isTimeIsCorrect() {
        return DateUtil.localDateTimeIsCorrect(kssj, jssj);
    }

    public String getStatus() {
        LocalDateTime now = LocalDateTime.now();
        if (kssj.isAfter(now)) {
            // 开始时间在当前时间之后，考试未开始
            return "未开始";
        } else if (isTimeIsCorrect()) {
            return fjqk;
        } else if (now.isAfter(jssj)) {
            return "已结束";
        }
        return "";
    }


}
