package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaperQuestions;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsParam;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.service.ExamPaperQuestionsService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/试卷试题控制器
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@RestController
@RequestMapping("/api/exam/exam-paper-questions")
public class ExamPaperQuestionsController extends BaseController {
    @Resource
    private ExamPaperQuestionsService examPaperQuestionsService;

    /**
     * 分页查询试卷试题（权限标识：exam:examPaperQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:list')")
    @GetMapping("/page")
    public PageResult<ExamPaperQuestions> page(ExamPaperQuestionsParam param) {
        PageParam<ExamPaperQuestions, ExamPaperQuestionsParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort asc");
        page = examPaperQuestionsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试卷试题（权限标识：exam:examPaperQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:list')")
    @GetMapping()
    public List<ExamPaperQuestions> list(ExamPaperQuestionsParam param) {
        PageParam<ExamPaperQuestions, ExamPaperQuestionsParam> page = new PageParam<>(param);
        page.setDefaultOrder("sort asc");
        return examPaperQuestionsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试卷试题（权限标识：exam:examPaperQuestions:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:list')")
    @GetMapping("/{id}")
    public ExamPaperQuestions get(@PathVariable("id") String id) {
        return examPaperQuestionsService.getExamPaperQuestions(id);
    }

    /**
     * 固定选题，保存试卷试题和试题选项（权限标识：exam:examPaperQuestions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:operation')")
    @OperationLog(module = "试卷试题", comments = "保存试卷试题和试题选项")
    @PostMapping("/saveExamPaperQuestions")
    public ExamPaperVO saveExamPaperQuestions(@RequestBody ExamPaperQuestionsParam param) {
        return examPaperQuestionsService.saveExamPaperQuestions(param);

    }
    /**
     * 固定选题,批量设置分数（权限标识：exam:examPaperQuestions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:operation')")
    @OperationLog(module = "试卷试题", comments = "批量设置分数")
    @PostMapping("/setExamPaperQuestionsScores")
    public ExamPaperVO setExamPaperQuestionsScores(@RequestBody ExamPaperQuestionsParam param) {
        return examPaperQuestionsService.setExamPaperQuestionsScores(param);

    }

    /**
     * 固定选题，试卷维护试题,从题库选题（权限标识：exam:examPaperQuestions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:operation')")
    @OperationLog(module = "试卷试题", comments = "试卷维护试题,从题库选题")
    @PostMapping("/saveExamPaperQuestionsByQuestions")
    public ExamPaperVO saveExamPaperQuestionsByQuestions(@RequestBody ExamPaperQuestionsParam param) {
        return examPaperQuestionsService.saveExamPaperQuestionsByQuestions(param);

    }

    /**
     * 批量删除试卷试题（权限标识：exam:examPaperQuestions:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:remove')")
    @OperationLog(module = "试卷试题", comments = "批量删除试卷试题")
    @PostMapping("/remove")
    public ExamPaperVO remove(@RequestBody ExamPaperQuestionsParam param) {
        return examPaperQuestionsService.removeExamPaperQuestions(param);
    }

    /**
     * 随机抽题，试卷维护试题,保存随机抽题规则（权限标识：exam:examPaperQuestions:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:operation')")
    @OperationLog(module = "试卷试题", comments = "试卷维护试题,保存随机抽题规则")
    @PostMapping("/saveExamPaperQuestionsRules")
    public ExamPaperVO saveExamPaperQuestionsRules(@RequestBody ExamPaperQuestionsRuleParam param) {
        return examPaperQuestionsService.saveExamPaperQuestionsRules(param);

    }

    /**
     * 批量删除试卷试题规则（权限标识：exam:examPaperQuestions:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQuestions:remove')")
    @OperationLog(module = "试卷试题", comments = "批量删除试卷试题规则")
    @PostMapping("/removeExamPaperQuestionsRules")
    public ExamPaperVO removeExamPaperQuestionsRules(@RequestBody ExamPaperQuestionsRuleParam param) {
        return examPaperQuestionsService.removeExamPaperQuestionsRules(param);
    }
}
