package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 试卷
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_PAPER")
@Entity
@Table(name = "SYT_EXAM_PAPER")
public class ExamPaper implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 操作人
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 试卷名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试卷总分
     */
    @Column(name = "SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 试题总数
     */
    @Column(name = "SUBJECT_SUM")
    @TableField("SUBJECT_SUM")
    private Integer subjectSum;

    /**
     * 试卷模式（固定、随机）
     */
    @Column(name = "SJMS")
    @TableField("SJMS")
    private String sjms;

    /**
     * 试卷分类id
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 试卷说明
     */
    @Column(name = "PAPER_BZ")
    @TableField("PAPER_BZ")
    private String paperBz;

    /**
     * 电脑端头图
     */
    @Column(name = "PC_IMAGE")
    @TableField("PC_IMAGE")
    private String pcImage;

    /**
     * 移动端头图
     */
    @Column(name = "MOBILE_IMAGE")
    @TableField("MOBILE_IMAGE")
    private String mobileImage;

    /**
     * 背景色
     */
    @Column(name = "BACKGROUND_COLOR")
    @TableField("BACKGROUND_COLOR")
    private String backgroundColor;

}
