package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 试题选项
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_QUESTIONS_OPTIONS")
@Entity
@Table(name = "SYT_EXAM_QUESTIONS_OPTIONS")
public class ExamQuestionsOptions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 选项编号
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 选项名
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 试题ID
     */
    @Column(name = "QID")
    @TableField("QID")
    private String qid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
