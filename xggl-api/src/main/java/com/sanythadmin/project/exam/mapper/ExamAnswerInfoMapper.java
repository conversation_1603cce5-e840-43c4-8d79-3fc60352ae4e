package com.sanythadmin.project.exam.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.vo.ExamStatisticsVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
public interface ExamAnswerInfoMapper extends MyMPJBaseMapper<ExamAnswerInfo> {

    @Select("select max(distinct KSCS) as kscs from SYT_EXAM_ANSWER_INFO where EXAM_ID=#{examId} and XGH=#{xgh}")
    Integer getMaxNumberOfExams(@Param("examId") String examId, @Param("xgh") String xgh);

    @Delete("<script>" +
            "DELETE FROM SYT_EXAM_ANSWER_INFO WHERE exam_id IN " +
            "<foreach item='item' index='index' collection='examIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    void removeByExamIds(@Param("examIds") List<String> examIds);

    @Select("SELECT " +
            "    t1.XGH as xgh, \n" +
            "    t1.SCORE as score, \n" +
            "    t1.KSCS as kscs, \n" +
            "    t1.USED_TIME as usedTime, \n" +
            "    t1.DDT as ddt, \n" +
            "    t1.DCS as dcs, \n" +
            "    t1.SFHG as sfhg, \n" +
            "    t1.CREATE_DATE as createDate,\n" +
            "    CASE \n" +
            "        WHEN e.CJPMFS = '递增' THEN DENSE_RANK() OVER (ORDER BY t1.SCORE DESC)\n" +
            "        ELSE RANK() OVER (ORDER BY t1.SCORE DESC)\n" +
            "    END AS rank\n" +
            "FROM \n" +
            "    SYT_EXAM_ANSWER_INFO t1\n" +
            "JOIN (\n" +
            "    SELECT XGH, MAX(KSCS) AS max_kscs\n" +
            "    FROM SYT_EXAM_ANSWER_INFO\n" +
            "    WHERE EXAM_ID=#{examId} " +
            "    GROUP BY XGH\n" +
            ") t2 ON t1.XGH = t2.XGH AND t1.KSCS = t2.max_kscs\n" +
            "JOIN SYT_EXAM e ON t1.EXAM_ID = e.ID WHERE t1.EXAM_ID=#{examId} ORDER BY t1.SCORE DESC;")
    @Results({
            @Result(property = "id",column = "ID"),
            @Result(property = "userInfo", column = "XGH",one = @One(select = "com.sanythadmin.common.system.mapper.UserInfoMapper.selectById"))
    })
    Page<ExamAnswerInfo> pageExamScoresWithRankings(@Param("examId") String examId, Page<ExamAnswerInfo> page);


    @Select("SELECT \n" +
            "    e.ID as exam_id,\n" +
            "    e.KSMC as exam_name,\n" +
            "    e.FJRS as should_attend_count,\n" +
            "    COUNT(DISTINCT ea.XGH) as actual_attend_count,\n" +
            "    (e.FJRS - COUNT(DISTINCT ea.XGH)) as absent_count,\n" +
            "    SUM(CASE WHEN ea.SFHG = '1' THEN 1 ELSE 0 END) as pass_count,\n" +
            "    SUM(CASE WHEN ea.SFHG = '0' THEN 1 ELSE 0 END) as fail_count,\n" +
            "    ROUND(SUM(CASE WHEN ea.SFHG = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(DISTINCT ea.XGH), 2) as pass_rate,\n" +
            "    MAX(ea.SCORE) as highest_score,\n" +
            "    MIN(ea.SCORE) as lowest_score,\n" +
            "    ROUND(AVG(ea.SCORE), 2) as average_score\n" +
            "FROM \n" +
            "    SYT_EXAM e\n" +
            "LEFT JOIN \n" +
            "    SYT_EXAM_ANSWER_INFO ea ON e.ID = ea.EXAM_ID\n" +
            "WHERE \n" +
            "    e.ID = #{examId}\n" +
            "GROUP BY \n" +
            "    e.ID, e.KSMC, e.FJRS\n" +
            "\n")
    ExamStatisticsVO getExamStatistics(@Param("examId") String examId);

    @Select("SELECT\n" +
            "    u.XYID as xyid,\n" +
            "    dwb.NAME as xymc,\n" +
            "    COUNT(DISTINCT u.XGH) as should_attend_count,\n" +
            "    COUNT(DISTINCT ea.XGH) as actual_attend_count,\n" +
            "    (COUNT(DISTINCT u.XGH) - COUNT(DISTINCT ea.XGH)) as absent_count,\n" +
            "    SUM(CASE WHEN ea.SFHG = '1' THEN 1 ELSE 0 END) as pass_count,\n" +
            "    SUM(CASE WHEN ea.SFHG = '0' THEN 1 ELSE 0 END) as fail_count,\n" +
            "    ROUND(SUM(CASE WHEN ea.SFHG = '1' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(DISTINCT ea.XGH), 0), 2) as pass_rate,\n" +
            "    MAX(ea.SCORE) as highest_score,\n" +
            "    MIN(ea.SCORE) as lowest_score,\n" +
            "    ROUND(AVG(ea.SCORE), 2) as average_score\n" +
            "FROM\n" +
            "    SYT_USER_INFO u\n" +
            "LEFT JOIN\n" +
            "    SYT_EXAM_ANSWER_INFO ea ON u.XGH = ea.XGH\n" +
            "    AND ea.EXAM_ID = #{examId}\n" +
            "inner join SYT_SELECTOR_ACCOUNT SSA on SSA.ITEM_ID=ea.EXAM_ID and u.XGH = SSA.XGH\n" +
            "left join SYT_CODE_DWB dwb on u.XYID=dwb.ID\n" +
            "WHERE\n" +
            "    u.DELETED = 0\n" +
            "GROUP BY\n" +
            "    u.XYID,dwb.NAME")
    Page<ExamStatisticsVO> getExamStatisticsGroup(@Param("examId") String examId,Page<ExamAnswerInfo> page);


}
