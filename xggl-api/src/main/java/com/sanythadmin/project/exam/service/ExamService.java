package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;
import com.sanythadmin.project.exam.param.ExamParam;
import com.sanythadmin.project.exam.vo.ExamVO;

import java.util.List;

/**
 * 在线考试Service
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
public interface ExamService extends IService<Exam> {

    void saveExam(ExamParam param) throws Exception;

    void removeExamByIds(List<String> ids);

    PageResult<Exam> queryPage(ExamParam param);

    /**
     * 获取考试详情+人员选择器数据
     *
     * @param id
     * @return {@link ExamVO }
     */
    ExamVO getExamVOById(String id);

    /**
     * 获取考试信息带试卷信息或答卷详情
     *
     * @param examAnswerInfo 考试答案信息
     * @return {@link ExamVO }
     */
    ExamVO getExamVO(ExamAnswerInfo examAnswerInfo);

    PageResult<UserInfo> pageExamPersonnelList(ExamParam param, UserInfoParam userInfoParam);

    PageResult<ExamAnswerInfo> pageExamRankingList(ExamAnswerInfoParam param);
}
