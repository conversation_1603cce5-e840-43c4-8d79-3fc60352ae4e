package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.exam.entity.ExamPaperQuestions;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsParam;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.vo.ExamPaperQuestionsVO;
import com.sanythadmin.project.exam.vo.ExamPaperVO;

/**
 * 试卷试题Service
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamPaperQuestionsService extends IService<ExamPaperQuestions> {

    ExamPaperVO saveExamPaperQuestions(ExamPaperQuestionsParam param);

    ExamPaperVO saveExamPaperQuestionsByQuestions(ExamPaperQuestionsParam param);

    ExamPaperVO removeExamPaperQuestions(ExamPaperQuestionsParam param);

    ExamPaperVO saveExamPaperQuestionsRules(ExamPaperQuestionsRuleParam param);

    ExamPaperVO removeExamPaperQuestionsRules(ExamPaperQuestionsRuleParam param);

    ExamPaperQuestionsVO getExamPaperQuestions(String id);

    ExamPaperVO setExamPaperQuestionsScores(ExamPaperQuestionsParam param);

}
