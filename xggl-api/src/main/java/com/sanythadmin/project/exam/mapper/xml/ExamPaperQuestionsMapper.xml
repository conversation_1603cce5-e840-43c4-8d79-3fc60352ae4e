<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperQuestionsMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STTYPE, CID, NAME, ANALYSIS, ANSWER, PID, QID, SORT, GID, SCORE, MAX_SCORE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.sttype != null">
                        AND a.STTYPE LIKE concat(concat('%',#{param.sttype), '%')
                    </if>
                    <if test="param.cid != null">
                        AND a.CID LIKE concat(concat('%',#{param.cid), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.analysis != null">
                        AND a.ANALYSIS LIKE concat(concat('%',#{param.analysis), '%')
                    </if>
                    <if test="param.answer != null">
                        AND a.ANSWER LIKE concat(concat('%',#{param.answer), '%')
                    </if>
                    <if test="param.pid != null">
                        AND a.PID LIKE concat(concat('%',#{param.pid), '%')
                    </if>
                    <if test="param.qid != null">
                        AND a.QID LIKE concat(concat('%',#{param.qid), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE LIKE concat(concat('%',#{param.score), '%')
                    </if>
                    <if test="param.maxScore != null">
                        AND a.MAX_SCORE LIKE concat(concat('%',#{param.maxScore), '%')
                    </if>
    </sql>
</mapper>
