<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamAnswerMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONTENT_ID, CREATE_DATE, EXAM_ID, PAPER_ID, POINT, REAL_NAME, RESULT, XGH, KSCS, USED_TIME, SFPMCJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.contentId != null">
                        AND a.CONTENT_ID LIKE concat(concat('%',#{param.contentId), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.examId != null">
                        AND a.EXAM_ID LIKE concat(concat('%',#{param.examId), '%')
                    </if>
                    <if test="param.paperId != null">
                        AND a.PAPER_ID LIKE concat(concat('%',#{param.paperId), '%')
                    </if>
                    <if test="param.point != null">
                        AND a.POINT LIKE concat(concat('%',#{param.point), '%')
                    </if>
                    <if test="param.realName != null">
                        AND a.REAL_NAME LIKE concat(concat('%',#{param.realName), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.kscs != null">
                        AND a.KSCS LIKE concat(concat('%',#{param.kscs), '%')
                    </if>
                    <if test="param.usedTime != null">
                        AND a.USED_TIME = #{param.usedTime}
                    </if>
                    <if test="param.sfpmcj != null">
                        AND a.SFPMCJ LIKE concat(concat('%',#{param.sfpmcj), '%')
                    </if>
    </sql>
</mapper>
