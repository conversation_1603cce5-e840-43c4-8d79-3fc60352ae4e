<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_DATE, CZR, FJQK, HGX, JSSJ, KSMC, KSSJ, PID, PNAME, PSCORE, SFXSDA, STTOTAL, UPTIME, KSMS, CKCS, SFZDRY, KSFL, FJRS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.fjqk != null">
                        AND a.FJQK LIKE concat(concat('%',#{param.fjqk), '%')
                    </if>
                    <if test="param.hgx != null">
                        AND a.HGX LIKE concat(concat('%',#{param.hgx), '%')
                    </if>
                    <if test="param.jssj != null">
                        AND a.JSSJ LIKE concat(concat('%',#{param.jssj), '%')
                    </if>
                    <if test="param.ksmc != null">
                        AND a.KSMC LIKE concat(concat('%',#{param.ksmc), '%')
                    </if>
                    <if test="param.kssj != null">
                        AND a.KSSJ LIKE concat(concat('%',#{param.kssj), '%')
                    </if>
                    <if test="param.pid != null">
                        AND a.PID LIKE concat(concat('%',#{param.pid), '%')
                    </if>
                    <if test="param.pname != null">
                        AND a.PNAME LIKE concat(concat('%',#{param.pname), '%')
                    </if>
                    <if test="param.pscore != null">
                        AND a.PSCORE LIKE concat(concat('%',#{param.pscore), '%')
                    </if>
                    <if test="param.sfxsda != null">
                        AND a.SFXSDA LIKE concat(concat('%',#{param.sfxsda), '%')
                    </if>
                    <if test="param.sttotal != null">
                        AND a.STTOTAL LIKE concat(concat('%',#{param.sttotal), '%')
                    </if>
                    <if test="param.uptime != null">
                        AND a.UPTIME = #{param.uptime}
                    </if>
                    <if test="param.ksms != null">
                        AND a.KSMS LIKE concat(concat('%',#{param.ksms), '%')
                    </if>
                    <if test="param.ckcs != null">
                        AND a.CKCS = #{param.ckcs}
                    </if>
                    <if test="param.sfzdry != null">
                        AND a.SFZDRY LIKE concat(concat('%',#{param.sfzdry), '%')
                    </if>
                    <if test="param.ksfl != null">
                        AND a.KSFL LIKE concat(concat('%',#{param.ksfl), '%')
                    </if>
                    <if test="param.fjrs != null">
                        AND a.FJRS = #{param.fjrs}
                    </if>
    </sql>
</mapper>
