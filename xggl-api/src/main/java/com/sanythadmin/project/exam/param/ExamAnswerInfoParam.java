package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamAnswerInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String id;

    @QueryField(type = QueryType.EQ)
    private String examId;

    /**
     * 试卷id
     */
    @QueryField(type = QueryType.EQ)
    private String paperId;

    /**
     * 考试用时
     */
    @QueryField(type = QueryType.EQ)
    private Long usedTime;

    /**
     * 答对数
     */
    @QueryField(type = QueryType.EQ)
    private Integer ddt;

    /**
     * 答错数
     */
    @QueryField(type = QueryType.EQ)
    private Integer dcs;

    /**
     * 考试得分
     */
    @QueryField(type = QueryType.EQ)
    private Double score;

    /**
     * 是否合格
     */
    private String sfhg;

    /**
     * 考试时间
     */
    private String createDate;

    /**
     * 答题者用户名
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 考试次数
     */
    @QueryField(type = QueryType.EQ)
    private Integer kscs;

    /**
     * 阅卷状态(待阅卷.已阅卷)
     */
    @QueryField(type = QueryType.EQ)
    private String status;

}
