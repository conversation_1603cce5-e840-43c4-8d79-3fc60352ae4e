package com.sanythadmin.project.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 试卷试题
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EXAM_PAPER_QUESTIONS")
@Entity
@Table(name = "SYT_EXAM_PAPER_QUESTIONS")
public class ExamPaperQuestions implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试题类型
     */
    @Column(name = "STTYPE")
    @TableField("STTYPE")
    private String sttype;

    /**
     * 试题分类ID
     */
    @Column(name = "CID")
    @TableField("CID")
    private String cid;

    /**
     * 试题名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 答案解析
     */
    @Column(name = "ANALYSIS")
    @TableField("ANALYSIS")
    private String analysis;

    /**
     * 答案
     */
    @Column(name = "ANSWER")
    @TableField("ANSWER")
    private String answer;

    /**
     * 试卷ID
     */
    @Column(name = "PID")
    @TableField("PID")
    private String pid;

    /**
     * 题库试题ID
     */
    @Column(name = "QID")
    @TableField("QID")
    private String qid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 分组ID
     */
    @Column(name = "GID")
    @TableField("GID")
    private String gid;

    /**
     * 分组名称
     */
    @Transient
    @TableField(exist = false)
    private String gname;

    @Column(name = "SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 打分最大分值
     */
    @Column(name = "MAX_SCORE",columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MAX_SCORE")
    private Double maxScore;

    /**
     * 答案
     */
    @Transient
    @TableField(exist = false)
    private String result;
    /**
     * 答案对错
     */
    @Transient
    @TableField(exist = false)
    private Boolean resultCorrect;
    /**
     * 得分
     */
    @Transient
    @TableField(exist = false)
    private Double manualScoring;




}
