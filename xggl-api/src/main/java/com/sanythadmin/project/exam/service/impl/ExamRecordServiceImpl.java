package com.sanythadmin.project.exam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.exam.entity.ExamRecord;
import com.sanythadmin.project.exam.mapper.ExamRecordMapper;
import com.sanythadmin.project.exam.service.ExamRecordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 考试记录Service实现
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Service
public class ExamRecordServiceImpl extends ServiceImpl<ExamRecordMapper, ExamRecord> implements ExamRecordService {

    @Resource
    private ExamRecordMapper mapper;

}
