package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;
import com.sanythadmin.project.exam.param.ExamParam;
import com.sanythadmin.project.exam.service.ExamService;
import com.sanythadmin.project.exam.vo.ExamVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/在线考试控制器
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@RestController
@RequestMapping("/api/exam/exam")
public class ExamController extends BaseController {
    @Resource
    private ExamService examService;

    /**
     * 分页查询在线考试（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/page")
    public PageResult<Exam> page(ExamParam param) {
        PageParam<Exam, ExamParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_DATE desc");
        page = examService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部在线考试（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping()
    public List<Exam> list(ExamParam param) {
        PageParam<Exam, ExamParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_DATE desc");
        return examService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询在线考试（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/{id}")
    public ExamVO get(@PathVariable("id") String id) {
        return examService.getExamVOById(id);
    }

    /**
     * 获取考试详情（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/getExamVO")
    public ExamVO getExamVO(ExamAnswerInfo examAnswerInfo) {
        return examService.getExamVO(examAnswerInfo);
    }

    /**
     * 添加或修改在线考试（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "在线考试", comments = "保存在线考试")
    @PostMapping("/operation")
    public void save(@RequestBody ExamParam param) throws Exception {
        examService.saveExam(param);
    }

    /**
     * 检查发卷情况（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "在线考试", comments = "检查发卷情况")
    @PostMapping("/checkFjqk")
    public void checkFjqk(@RequestBody String id) {
        if (!StringUtils.hasLength(id)) {
            AssertUtil.throwMessage("考试id不能为空");
        }
        Exam exam = examService.getById(id);
        if (exam == null) {
            AssertUtil.throwMessage("未找到考试");
        }
        if (!ConstantsExam.EXAM_FJQK_WFJ.equals(exam.getFjqk())) {
            AssertUtil.throwMessage(String.format("%s,不可编辑", exam.getFjqk()));
        }
    }

    /**
     * 批量删除在线考试（权限标识：exam:exam:remove）
     */
    @PreAuthorize("hasAuthority('exam:exam:remove')")
    @OperationLog(module = "在线考试", comments = "批量删除在线考试")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examService.removeExamByIds(ids);
    }

    /**
     * 发卷（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "在线考试", comments = "发卷")
    @PostMapping("/sendPaper")
    public void sendPaper(@RequestBody String id) {
        if (!StringUtils.hasLength(id)) {
            AssertUtil.throwMessage("考试id不能为空");
        }
        Exam exam = examService.getById(id);
        if (exam == null) {
            AssertUtil.throwMessage("未找到考试");
        }
        if (exam.isTimeIsCorrect()) {
            exam.setFjqk(ConstantsExam.EXAM_FJQK_YFJ);
            examService.updateById(exam);
        } else {
            AssertUtil.throwMessage("不在考试时间内");
        }

    }

    /**
     * 结束考试（权限标识：exam:exam:operation）
     */
    @PreAuthorize("hasAuthority('exam:exam:operation')")
    @OperationLog(module = "在线考试", comments = "结束考试")
    @PostMapping("/endExam")
    public void endExam(@RequestBody String id) {
        if (!StringUtils.hasLength(id)) {
            AssertUtil.throwMessage("考试id不能为空");
        }
            Exam exam = examService.getById(id);
            if (exam == null) {
                AssertUtil.throwMessage("未找到考试");
            }
            exam.setFjqk(ConstantsExam.EXAM_FJQK_YSJ);
            examService.updateById(exam);
    }

    /**
     * 分页查询应考/缺考名单（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/pageExamPersonnelList")
    public PageResult<UserInfo> pageExamPersonnelList(ExamParam param, UserInfoParam userInfoParam) {
        return examService.pageExamPersonnelList(param,userInfoParam);
    }

    /**
     * 分页查询考试成绩及排名（权限标识：exam:exam:list）
     */
    @PreAuthorize("hasAuthority('exam:exam:list')")
    @GetMapping("/pageExamRankingList")
    public PageResult<ExamAnswerInfo> pageExamRankingList(ExamAnswerInfoParam param) {
        return examService.pageExamRankingList(param);
    }

}
