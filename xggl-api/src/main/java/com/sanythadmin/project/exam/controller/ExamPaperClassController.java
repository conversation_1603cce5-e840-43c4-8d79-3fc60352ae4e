package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaperClass;
import com.sanythadmin.project.exam.param.ExamPaperClassParam;
import com.sanythadmin.project.exam.service.ExamPaperClassService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 在线考试/试卷分类控制器
 *
 * <AUTHOR>
 * @since 2024-09-11 16:39:44
 */
@RestController
@RequestMapping("/api/exam/exam-paper-class")
public class ExamPaperClassController extends BaseController {
    @Resource
    private ExamPaperClassService examPaperClassService;

    /**
     * 分页查询（权限标识：exam:examPaperClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperClass:list')")
    @GetMapping("/page")
    public PageResult<ExamPaperClass> page(ExamPaperClassParam param) {
        PageParam<ExamPaperClass, ExamPaperClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = examPaperClassService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部（权限标识：exam:examPaperClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperClass:list')")
    @GetMapping()
    public List<ExamPaperClass> list(ExamPaperClassParam param) {
        PageParam<ExamPaperClass, ExamPaperClassParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return examPaperClassService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询（权限标识：exam:examPaperClass:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperClass:list')")
    @GetMapping("/{id}")
    public ExamPaperClass get(@PathVariable("id") String id) {
        return examPaperClassService.getById(id);
    }

    /**
     * 添加或修改（权限标识：exam:examPaperClass:operation）
     */
//    @PreAuthorize("hasAuthority('exam:examPaperClass:operation')")
    @OperationLog(module = "", comments = "保存")
    @PostMapping("/operation")
    public void save(@RequestBody ExamPaperClass examPaperClass) {
        if (StringUtils.hasLength(examPaperClass.getId())) {
            examPaperClassService.updateById(examPaperClass);
        } else {
            if (examPaperClass.getSort() == null) {
                SortHelper<ExamPaperClass> sortHelper = new SortHelper<>(examPaperClassService, "sort");
                examPaperClass.setSort(sortHelper.next());
            }
            examPaperClass.setCreateDate(LocalDateTime.now());
            examPaperClassService.save(examPaperClass);
        }
    }

    /**
     * 批量删除（权限标识：exam:examPaperClass:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperClass:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examPaperClassService.removeByIds(ids);
    }
}
