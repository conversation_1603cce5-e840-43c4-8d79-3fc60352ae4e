<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamPaperClassMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, BZ, CREATE_DATE, NAME, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE concat(concat('%',#{param.bz), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
    </sql>
</mapper>
