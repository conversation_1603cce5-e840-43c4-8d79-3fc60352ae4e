<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.exam.mapper.ExamRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, BEGIN_DATE, END_DATE, EXAM_ID, KSCS, PAPER_ID, XGH
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.beginDate != null">
                        AND a.BEGIN_DATE LIKE concat(concat('%',#{param.beginDate), '%')
                    </if>
                    <if test="param.endDate != null">
                        AND a.END_DATE LIKE concat(concat('%',#{param.endDate), '%')
                    </if>
                    <if test="param.examId != null">
                        AND a.EXAM_ID LIKE concat(concat('%',#{param.examId), '%')
                    </if>
                    <if test="param.kscs != null">
                        AND a.KSCS LIKE concat(concat('%',#{param.kscs), '%')
                    </if>
                    <if test="param.paperId != null">
                        AND a.PAPER_ID LIKE concat(concat('%',#{param.paperId), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
    </sql>
</mapper>
