package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.selector.entity.SelectorData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 在线考试查询参数
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 发卷情况
     */
    private String fjqk;

    /**
     * 合格线
     */
    private Double hgx;

    /**
     * 结束时间
     */
    @QueryField(ignore = true)
    private String jssj;

    /**
     * 考试名称
     */
    private String ksmc;

    /**
     * 开始时间
     */
    @QueryField(ignore = true)
    private String kssj;

    /**
     * 试卷id
     */
    private String pid;

    /**
     * 试卷名称
     */
    private String pname;

    /**
     * 是否显示答案
     */
    private String sfxsda;

    /**
     * 设置时长(分钟)
     */
    @QueryField(type = QueryType.EQ)
    private Long uptime;

    /**
     * 考试模式
     */
    private String ksms;

    /**
     * 考试次数
     */
    @QueryField(type = QueryType.EQ)
    private Integer ckcs;

    /**
     * 是否指定人员
     */
    private String sfzdry;

    /**
     * 考试分类
     */
    private String ksfl;

    /**
     * 发卷人数
     */
    @QueryField(type = QueryType.EQ)
    private Integer fjrs;

    /**
     * 答题模式
     */
    private String dtms;

    /**
     * 成绩排名方式
     */
    private String cjpmfs;

    /**
     * 启用人工阅卷
     */
    private String rgyj;

    private String bz;


    /**
     * 人员选择器数据
     */
    @QueryField(ignore = true)
    private List<SelectorData> selectorDatas;

    /**
     * 人员选择器是否并联（1：是， 0：否）
     */
    @QueryField(ignore = true)
    private JudgeMark sfbl;

    /**
     * 用户信息,判断是否在发布范围内
     */
    @QueryField(ignore = true)
    private UserInfoParam userInfoParam;

    /**
     * 名单类型(应考名单,缺考名单)
     */
    private String mdlx;

}
