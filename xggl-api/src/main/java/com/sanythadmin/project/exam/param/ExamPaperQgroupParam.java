package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 试卷试题组查询参数
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamPaperQgroupParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 试卷id
     */
    private String pid;

    /**
     * 组名称
     */
    private String name;

    /**
     * 试题数
     */
    @QueryField(ignore = true)
    private Integer qsum;

    /**
     * 每题分数
     */
    @QueryField(ignore = true)
    private Double qscore;

    /**
     * 总分
     */
    @QueryField(ignore = true)
    private Double totalScore;

    public Double getTotalScore() {
        return qsum != null && qscore != null ? qsum * qscore : 0d;
    }

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort";
    }

}
