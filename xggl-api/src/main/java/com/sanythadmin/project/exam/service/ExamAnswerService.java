package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.exam.entity.Exam;
import com.sanythadmin.project.exam.entity.ExamAnswer;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.param.ExamAnswerParam;

import java.util.Map;

/**
 * 答卷答案Service
 *
 * <AUTHOR>
 * @since 2024-09-20 16:01:55
 */
public interface ExamAnswerService extends IService<ExamAnswer> {

    public Integer getMaxNumberOfExams(String examId, String xgh);

    public void saveAnswerAndHistory(ExamAnswerParam param);

    boolean isAnswered(ExamAnswer examAnswer);

    boolean checkNumberOfExam(Exam exam, SysAccount account);

    Map<String, ExamAnswer> getAnswerMap(ExamAnswerInfo examAnswerInfo);


    void saveAnswerScoring(ExamAnswerParam param);

}
