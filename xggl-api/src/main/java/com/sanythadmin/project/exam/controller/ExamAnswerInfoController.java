package com.sanythadmin.project.exam.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.exam.entity.ExamAnswerInfo;
import com.sanythadmin.project.exam.mapper.ExamAnswerInfoMapper;
import com.sanythadmin.project.exam.param.ExamAnswerInfoParam;
import com.sanythadmin.project.exam.service.ExamAnswerInfoService;
import com.sanythadmin.project.exam.vo.ExamStatisticsVO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/人工阅卷,考生成绩控制器
 *
 * <AUTHOR>
 * @since 2024-10-21 17:12:02
 */
@RestController
@RequestMapping("/api/exam/exam-answer-info")
public class ExamAnswerInfoController extends BaseController {
    @Resource
    private ExamAnswerInfoService examAnswerInfoService;
    @Resource
    private ExamAnswerInfoMapper examAnswerInfoMapper;

    /**
     * 管理者分页查询（权限标识：exam:examAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:list')")
    @GetMapping("/page")
    public PageResult<ExamAnswerInfo> page(ExamAnswerInfoParam param, UserInfoParam userInfoParam) {
        return examAnswerInfoService.getExamAnswerInfoPage(param,userInfoParam);
    }

    /**
     * 根据id查询考试信息(不带试卷详情)（权限标识：exam:examAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:list')")
    @GetMapping("/{id}")
    public ExamAnswerInfo get(@PathVariable("id") String id) {
        return examAnswerInfoService.getById(id);
    }

    /**
     * 批量删除,级联删除答题记录（权限标识：exam:examAnswerInfo:remove）
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examAnswerInfoService.removeExamAnswerInfoByIds(ids);
    }

    /**
     * 个人查看考试成绩（权限标识：exam:examAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:personallist')")
    @GetMapping("/pagePersonal")
    public PageResult<ExamAnswerInfo> pagePersonal(ExamAnswerInfoParam param, UserInfoParam userInfoParam) {
        param.setXgh(getAccount().getUsername());
        return examAnswerInfoService.getExamAnswerInfoPage(param,userInfoParam);
    }

    /**
     * 获取考试统计数据
     *
     * @param examId 考试id
     * @return {@link ExamStatisticsVO }
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:list')")
    @GetMapping("/getExamStatistics")
    public ExamStatisticsVO getExamStatistics(@RequestParam("examId") String examId) {
        if (StringUtils.isEmpty(examId)) {
            AssertUtil.throwMessage("考试id不能为空");
        }
        return examAnswerInfoMapper.getExamStatistics(examId);
    }

    /**
     * 获取考试按学院分组统计数据
     *
     * @param examId 考试id
     * @return {@link ExamStatisticsVO }
     */
    @PreAuthorize("hasAuthority('exam:examAnswerInfo:list')")
    @GetMapping("/getExamStatisticsGroup")
    public PageResult<ExamStatisticsVO> getExamStatisticsGroup(@RequestParam("examId") String examId) {
        if (StringUtils.isEmpty(examId)) {
            AssertUtil.throwMessage("考试id不能为空");
        }
        PageParam<ExamAnswerInfo, ExamAnswerInfoParam> page = new PageParam<>();
        Page<ExamStatisticsVO> voPage = examAnswerInfoMapper.getExamStatisticsGroup(examId, page);
        return new PageResult<>(voPage.getRecords(), voPage.getTotal());
    }
}
