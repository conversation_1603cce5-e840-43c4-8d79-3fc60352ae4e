package com.sanythadmin.project.exam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.project.exam.constant.ConstantsExam;
import com.sanythadmin.project.exam.entity.ExamPaper;
import com.sanythadmin.project.exam.entity.ExamPaperQgroup;
import com.sanythadmin.project.exam.mapper.ExamPaperMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQgroupMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsOptionsMapper;
import com.sanythadmin.project.exam.param.ExamPaperQgroupParam;
import com.sanythadmin.project.exam.service.ExamPaperQgroupService;
import com.sanythadmin.project.exam.service.ExamPaperService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * 试卷试题组Service实现
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Service
public class ExamPaperQgroupServiceImpl extends ServiceImpl<ExamPaperQgroupMapper, ExamPaperQgroup> implements ExamPaperQgroupService {

    @Resource
    private ExamPaperQgroupMapper mapper;
    @Resource
    private ExamPaperMapper examPaperMapper;
    @Resource
    private ExamPaperQuestionsMapper examPaperQuestionsMapper;
    @Resource
    private ExamPaperQuestionsOptionsMapper examPaperQuestionsOptionsMapper;
    @Lazy
    @Resource
    private ExamPaperService examPaperService;

    @Transactional
    @Override
    public ExamPaperVO removeExamPaperQgroup(ExamPaperQgroupParam param) {
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        String ids = param.getId();
        examPaperQuestionsMapper.deleteByGid(Arrays.asList(ids.split(",")));
        examPaperQuestionsOptionsMapper.deleteByGid(Arrays.asList(ids.split(",")));
        removeByIds(Arrays.asList(ids.split(",")));
        ExamPaper examPaper = examPaperService.getById(param.getPid());
        if(ConstantsExam.EXAM_PAPER_GDXT.equals(examPaper.getSjms())){
            examPaperMapper.updateSumAndScore(param.getPid());
        }else{
            examPaperMapper.updateSumAndScoreByRule(param.getPid());
        }
        return examPaperService.getExamPaperVO(param.getPid());
    }
}
