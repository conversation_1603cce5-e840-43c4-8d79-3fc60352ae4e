package com.sanythadmin.project.exam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.exam.entity.ExamQuestionsClass;
import com.sanythadmin.project.exam.mapper.ExamQuestionsClassMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsMapper;
import com.sanythadmin.project.exam.mapper.ExamQuestionsOptionsMapper;
import com.sanythadmin.project.exam.service.ExamQuestionsClassService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 试题分类Service实现
 *
 * <AUTHOR>
 * @since 2024-09-10 17:50:26
 */
@Service
public class ExamQuestionsClassServiceImpl extends ServiceImpl<ExamQuestionsClassMapper, ExamQuestionsClass> implements ExamQuestionsClassService {

    @Resource
    private ExamQuestionsClassMapper mapper;
    @Resource
    private ExamQuestionsOptionsMapper examQuestionsOptionsMapper;
    @Resource
    private ExamQuestionsMapper examQuestionsMapper;

    @Override
    public Map<String, String> getAllNameIdMap() {
        List<ExamQuestionsClass> list = list();
        return list.stream()
                .collect(Collectors.toMap(
                        ExamQuestionsClass::getName,
                        ExamQuestionsClass::getId,
                        (existing, replacement) -> existing // 如果有重复的 name，保留第一个
                ));
    }

    @Transactional
    @Override
    public void removeQuestionsClassByIds(List<String> ids) {
        // 先删除选项
        examQuestionsOptionsMapper.deleteByClassIds(ids);
        // 再删除试题
        examQuestionsMapper.deleteByClassIds(ids);
        // 最后删除分类
        removeByIds(ids);
    }
}
