package com.sanythadmin.project.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.project.exam.entity.ExamPaperQuestionsRule;
import com.sanythadmin.project.exam.mapper.ExamPaperMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQgroupMapper;
import com.sanythadmin.project.exam.mapper.ExamPaperQuestionsRuleMapper;
import com.sanythadmin.project.exam.param.ExamPaperQuestionsRuleParam;
import com.sanythadmin.project.exam.service.ExamPaperQuestionsRuleService;
import com.sanythadmin.project.exam.service.ExamPaperService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 试卷试题抽题规则Service实现
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Service
public class ExamPaperQuestionsRuleServiceImpl extends ServiceImpl<ExamPaperQuestionsRuleMapper, ExamPaperQuestionsRule> implements ExamPaperQuestionsRuleService {

    @Resource
    private ExamPaperQuestionsRuleMapper mapper;
    @Resource
    private ExamPaperMapper examPaperMapper;
    @Resource
    private ExamPaperQgroupMapper examPaperQgroupMapper;
    @Lazy
    @Resource
    private ExamPaperService examPaperService;


    @Transactional
    @Override
    public ExamPaperVO saveExamPaperQuestionsRule(ExamPaperQuestionsRuleParam param) {
        ExamPaperQuestionsRule examPaperQuestionsRule = new ExamPaperQuestionsRule();
        BeanUtil.copyProperties(param, examPaperQuestionsRule);
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        examPaperQuestionsRule.setGid(param.getGid());

        if (examPaperQuestionsRule.getSort() == null) {
            SortHelper<ExamPaperQuestionsRule> sortHelper = new SortHelper<>(this, "sort");
            examPaperQuestionsRule.setSort(sortHelper.next());
        }
        saveOrUpdate(examPaperQuestionsRule);
        examPaperMapper.updateSumAndScoreByRule(param.getPid());
        examPaperQgroupMapper.updateSumAndScoreByRule(param.getGid());
        return examPaperService.getExamPaperVO(param.getPid());

    }
    @Transactional
    @Override
    public ExamPaperVO removeExamPaperQuestionsRule(ExamPaperQuestionsRuleParam param) {
        if (StringUtils.isEmpty(param.getPid())) {
            AssertUtil.throwMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(param.getGid())) {
            AssertUtil.throwMessage("试卷试题组id不能为空");
        }
        String ids = param.getId();
        removeByIds(Arrays.asList(ids.split(",")));
        examPaperMapper.updateSumAndScoreByRule(param.getPid());
        examPaperQgroupMapper.updateSumAndScoreByRule(param.getGid());
        return examPaperService.getExamPaperVO(param.getPid());
    }

    @Transactional
    @Override
    public ExamPaperVO saveBatchExamPaperQuestionsRule(List<ExamPaperQuestionsRuleParam> param) {
        if (param.size() > 0) {
            ExamPaperQuestionsRuleParam ruleParam = param.get(0);
            if (StringUtils.isEmpty(ruleParam.getPid())) {
                AssertUtil.throwMessage("试卷id不能为空");
            }
            if (StringUtils.isEmpty(ruleParam.getGid())) {
                AssertUtil.throwMessage("试卷试题组id不能为空");
            }
            List<ExamPaperQuestionsRule> examPaperQuestionsRules = BeanUtil.copyToList(param, ExamPaperQuestionsRule.class);
            examPaperQuestionsRules.forEach(examPaperQuestionsRule -> {
                if (examPaperQuestionsRule.getSort() == null) {
                    SortHelper<ExamPaperQuestionsRule> sortHelper = new SortHelper<>(this, "sort");
                    examPaperQuestionsRule.setSort(sortHelper.next());
                }
            });
            saveOrUpdateBatch(examPaperQuestionsRules);
            examPaperMapper.updateSumAndScoreByRule(ruleParam.getPid());
            examPaperQgroupMapper.updateSumAndScoreByRule(ruleParam.getGid());
            return examPaperService.getExamPaperVO(ruleParam.getPid());
        }
        return new ExamPaperVO();
    }

}
