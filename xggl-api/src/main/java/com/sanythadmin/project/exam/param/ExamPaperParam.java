package com.sanythadmin.project.exam.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.exam.vo.ExamPaperQgroupVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 试卷查询参数
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExamPaperParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 操作人
     */
    private String czr;

    /**
     * 试卷名称
     */
    private String name;

    /**
     * 试卷总分
     */
    private Double score;

    /**
     * 试题总数
     */
    @QueryField(type = QueryType.EQ)
    private Integer subjectSum;

    /**
     * 试卷模式（固定、随机）
     */
    private String sjms;

    /**
     * 试卷分类id
     */
    private String cid;
    /**
     * 试卷分类
     */
    private String cname;

    /**
     * 试题组列表
     */
    @QueryField(ignore = true)
    private List<ExamPaperQgroupVO> examPaperQgroupVOList;

}
