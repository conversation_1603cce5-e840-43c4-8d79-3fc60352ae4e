package com.sanythadmin.project.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamQuestions;
import com.sanythadmin.project.exam.param.ExamQuestionsParam;
import com.sanythadmin.project.exam.vo.ExamQuestionsVO;
import jakarta.servlet.ServletOutputStream;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 试题Service
 *
 * <AUTHOR>
 * @since 2024-09-23 13:41:05
 */
public interface ExamQuestionsService extends IService<ExamQuestions> {

    void saveQuestionsAndOptions(ExamQuestionsParam param);

    void removeQuestionsAndOptions(List<String> ids);

    ExamQuestionsVO getByIdAndOptions(String id);

    void importExamQuestions(MultipartFile file);

    void exportTemplate(ServletOutputStream outputStream);

    /**
     * 分页查询不在试卷中的试题
     *
     * @param param param
     * @return {@link PageResult }<{@link ExamQuestions }>
     */
    PageResult<ExamQuestions> getQuestionsNotInPaper(ExamQuestionsParam param);


}
