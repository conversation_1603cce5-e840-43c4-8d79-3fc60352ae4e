package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaper;
import com.sanythadmin.project.exam.param.ExamPaperParam;
import com.sanythadmin.project.exam.service.ExamPaperService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 在线考试/试卷控制器
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@RestController
@RequestMapping("/api/exam/exam-paper")
public class ExamPaperController extends BaseController {
    @Resource
    private ExamPaperService examPaperService;

    /**
     * 分页查询试卷（权限标识：exam:examPaper:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaper:list')")
    @GetMapping("/page")
    public PageResult<ExamPaper> page(ExamPaperParam param) {
        PageParam<ExamPaper, ExamPaperParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_DATE desc");
        page = examPaperService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试卷（权限标识：exam:examPaper:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaper:list')")
    @GetMapping()
    public List<ExamPaper> list(ExamPaperParam param) {
        PageParam<ExamPaper, ExamPaperParam> page = new PageParam<>(param);
        page.setDefaultOrder("CREATE_DATE desc");
        return examPaperService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试卷（权限标识：exam:examPaper:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaper:list')")
    @GetMapping("/{id}")
    public ExamPaperVO get(@PathVariable("id") String id) {
        return examPaperService.getExamPaperVO(id);
    }

    /**
     * 添加或修改试卷（权限标识：exam:examPaper:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaper:operation')")
    @OperationLog(module = "试卷", comments = "保存试卷")
    @PostMapping("/operation")
    public ExamPaper save(@RequestBody ExamPaper examPaper) {
        if (StringUtils.hasLength(examPaper.getId())) {
            examPaperService.updateById(examPaper);
        } else {
            examPaper.setCreateDate(LocalDateTime.now());
            examPaper.setCzr(SecurityUtil.getUsername());
            examPaperService.save(examPaper);
        }
        return examPaper;
    }

    /**
     * [一次提交]保存试卷，试题组，试题(权限标识：exam:examPaper:operation）
     *
     * @param param param
     */
    @PreAuthorize("hasAuthority('exam:examPaper:operation')")
    @OperationLog(module = "试卷", comments = "保存试卷，试题组，试题")
    @PostMapping("/savePaperGroupQuestions")
    public void savePaperGroupQuestions(@RequestBody ExamPaperParam param) {
        examPaperService.savePaperGroupQuestions(param);
    }

    /**
     * 批量删除试卷（权限标识：exam:examPaper:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaper:remove')")
    @OperationLog(module = "试卷", comments = "批量删除试卷")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        examPaperService.removePaperGroupQuestions(ids);
    }

}
