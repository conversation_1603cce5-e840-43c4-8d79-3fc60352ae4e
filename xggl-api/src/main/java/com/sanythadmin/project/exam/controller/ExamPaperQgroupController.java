package com.sanythadmin.project.exam.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.exam.entity.ExamPaperQgroup;
import com.sanythadmin.project.exam.param.ExamPaperQgroupParam;
import com.sanythadmin.project.exam.service.ExamPaperQgroupService;
import com.sanythadmin.project.exam.vo.ExamPaperVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线考试/试卷试题组控制器
 *
 * <AUTHOR>
 * @since 2024-09-18 14:55:43
 */
@RestController
@RequestMapping("/api/exam/exam-paper-qgroup")
public class ExamPaperQgroupController extends BaseController {
    @Resource
    private ExamPaperQgroupService examPaperQgroupService;

    /**
     * 分页查询试卷试题组（权限标识：exam:examPaperQgroup:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQgroup:list')")
    @GetMapping("/page")
    public PageResult<ExamPaperQgroup> page(ExamPaperQgroupParam param) {
        PageParam<ExamPaperQgroup, ExamPaperQgroupParam> page = new PageParam<>(param);
//        page.setDefaultOrder("SORT");
        page = examPaperQgroupService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部试卷试题组（权限标识：exam:examPaperQgroup:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQgroup:list')")
    @GetMapping()
    public List<ExamPaperQgroup> list(ExamPaperQgroupParam param) {
        PageParam<ExamPaperQgroup, ExamPaperQgroupParam> page = new PageParam<>(param);
//        page.setDefaultOrder("SORT");
        return examPaperQgroupService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询试卷试题组（权限标识：exam:examPaperQgroup:list）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQgroup:list')")
    @GetMapping("/{id}")
    public ExamPaperQgroup get(@PathVariable("id") String id) {
        return examPaperQgroupService.getById(id);
    }

    /**
     * 添加或修改试卷试题组（权限标识：exam:examPaperQgroup:operation）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQgroup:operation')")
    @OperationLog(module = "试卷试题组", comments = "保存试卷试题组")
    @PostMapping("/operation")
    public ExamPaperQgroup save(@RequestBody ExamPaperQgroup examPaperQgroup) {
        if (StringUtils.hasLength(examPaperQgroup.getId())) {
            examPaperQgroupService.updateById(examPaperQgroup);
        } else {
            if (examPaperQgroup.getSort() == null) {
                SortHelper<ExamPaperQgroup> sortHelper = new SortHelper<>(examPaperQgroupService, "sort");
                examPaperQgroup.setSort(sortHelper.next());
            }
            examPaperQgroupService.save(examPaperQgroup);
        }
        return examPaperQgroup;
    }

    /**
     * 批量删除试卷试题组（权限标识：exam:examPaperQgroup:remove）
     */
    @PreAuthorize("hasAuthority('exam:examPaperQgroup:remove')")
    @OperationLog(module = "试卷试题组", comments = "批量删除试卷试题组")
    @PostMapping("/remove")
    public ExamPaperVO remove(@RequestBody ExamPaperQgroupParam param) {
        return examPaperQgroupService.removeExamPaperQgroup(param);
    }
}
