package com.sanythadmin.project.selector.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import com.sanythadmin.project.selector.param.SelectorAccountParam;
import com.sanythadmin.project.selector.service.SelectorAccountService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 人员选择器/所选人员名单控制器
 *
 * <AUTHOR>
 * @since 2024-05-27 14:32:46
 */
@RestController
@RequestMapping("/api/selector/selector-account")
public class SelectorAccountController extends BaseController {
    @Resource
    private SelectorAccountService selectorAccountService;

    /**
     * 分页查询（权限标识：selector:selectorAccount:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorAccount:list')")
    @GetMapping("/page")
    public PageResult<SelectorAccount> page(SelectorAccountParam param) {
        PageParam<SelectorAccount, SelectorAccountParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = selectorAccountService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部（权限标识：selector:selectorAccount:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorAccount:list')")
    @GetMapping()
    public List<SelectorAccount> list(SelectorAccountParam param) {
        PageParam<SelectorAccount, SelectorAccountParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return selectorAccountService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询（权限标识：selector:selectorAccount:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorAccount:list')")
    @GetMapping("/{id}")
    public SelectorAccount get(@PathVariable("id") String id) {
        return selectorAccountService.getById(id);
    }

    /**
     * 添加或修改（权限标识：selector:selectorAccount:operation）
     */
    @PreAuthorize("hasAuthority('selector:selectorAccount:operation')")
    @OperationLog(module = "", comments = "保存")
    @PostMapping("/operation")
    public void save(@RequestBody SelectorAccount selectorAccount) {
        if (StringUtils.hasLength(selectorAccount.getId())) {
            selectorAccountService.updateById(selectorAccount);
        } else {
            selectorAccountService.save(selectorAccount);
        }
    }

    /**
     * 批量删除（权限标识：selector:selectorAccount:remove）
     */
    @PreAuthorize("hasAuthority('selector:selectorAccount:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        selectorAccountService.removeByIds(ids);
    }
}
