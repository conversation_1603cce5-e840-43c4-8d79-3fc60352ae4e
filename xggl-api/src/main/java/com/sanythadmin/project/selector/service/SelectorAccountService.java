package com.sanythadmin.project.selector.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.selector.entity.SelectorAccount;
import net.sf.jsqlparser.expression.Expression;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2024-05-27 14:32:46
 */
public interface SelectorAccountService extends IService<SelectorAccount> {

    boolean checkExistSelectorAccount(String itemId, String xgh);

    List<Expression> expressionList(String alias, UserType userType);
}
