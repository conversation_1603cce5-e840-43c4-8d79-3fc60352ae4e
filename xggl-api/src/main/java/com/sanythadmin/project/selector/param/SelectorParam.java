package com.sanythadmin.project.selector.param;

import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 选择器查询参数
 *
 * <AUTHOR>
 * @date 2024/05/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SelectorParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    private String itemId;
    /**
     * 是否并联
     */
    private JudgeMark sfbl;

    /**
     * 学号/工号，对应账户表username
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别（男：0， 女：1）
     */
    private Gender xb;

    /**
     * 角色ID
     */
    private String roleId;
    /**
     * 院系ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    private String zyid;

    /**
     * 班级ID
     */
    private String bjid;

    /**
     * 年级ID
     */
    private String njid;

    /**
     * 名族名称
     */
    private String mzmc;
    /**
     * 培养层次ID
     */
    private String pyccid;

    /**
     * 政治面貌
     */
    private String zzmmmc;

    /**
     * 学生类别（本，硕，博，进修生，留学生等）
     */
    private String xslb;

    /**
     * 学制类型（三年制，四年制，五年制等）
     */
    private String xzlx;

    /**
     * 用户类别
     */
    private String userType;
}
