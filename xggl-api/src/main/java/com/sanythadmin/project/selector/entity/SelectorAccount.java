package com.sanythadmin.project.selector.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 *  选择器所选人员名单
 *
 * <AUTHOR>
 * @since 2024-05-27 14:32:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SELECTOR_ACCOUNT")
@Entity
@Table(name = "SYT_SELECTOR_ACCOUNT")
public class SelectorAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 关联项目id
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 账号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

}
