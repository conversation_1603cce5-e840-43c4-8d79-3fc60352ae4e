package com.sanythadmin.project.selector.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
/**
 * 选择器数据
 *
 * <AUTHOR>
 * @since 2024-05-10 15:13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SELECTOR_DATA")
@Entity
@Table(name = "SYT_SELECTOR_DATA")
public class SelectorData implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 选择项名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 选择项值
     */
    @Column(name = "VALUE")
    @TableField("VALUE")
    private String value;

    /**
     * 选择器类型id
     */
    @Column(name = "CONFIG_ID")
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 选择器类型key
     */
    @Column(name = "CONFIG_KEY")
    @TableField("CONFIG_KEY")
    private String configKey;

    /**
     * 选择器类型名称
     */
    @Column(name = "CONFIG_NAME")
    @TableField("CONFIG_NAME")
    private String configName;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 关联项目id
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 是否并联(1:是,0:否)
     */
    @Column(name = "SFBL", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField(value = "SFBL", jdbcType = JdbcType.INTEGER)
    private JudgeMark sfbl;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

}
