package com.sanythadmin.project.selector.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.param.SelectorDataParam;
import com.sanythadmin.project.selector.service.SelectorDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 人员选择器/数据控制器
 *
 * <AUTHOR>
 * @since 2024-05-10 15:13:59
 */
@RestController
@RequestMapping("/api/selector/selector-data")
public class SelectorDataController extends BaseController {
    @Resource
    private SelectorDataService selectorDataService;

    /**
     * 分页查询选择器数据（权限标识：selector:selectorData:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorData:list')")
    @GetMapping("/page")
    public PageResult<SelectorData> page(SelectorDataParam param) {
        PageParam<SelectorData, SelectorDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = selectorDataService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部选择器数据（权限标识：selector:selectorData:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorData:list')")
    @GetMapping()
    public List<SelectorData> list(SelectorDataParam param) {
        PageParam<SelectorData, SelectorDataParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return selectorDataService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询选择器数据（权限标识：selector:selectorData:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorData:list')")
    @GetMapping("/{id}")
    public SelectorData get(@PathVariable("id") String id) {
        return selectorDataService.getById(id);
    }

    /**
     * 添加或修改选择器数据（权限标识：selector:selectorData:operation）
     */
    @PreAuthorize("hasAuthority('selector:selectorData:operation')")
    @OperationLog(module = "选择器数据", comments = "保存选择器数据")
    @PostMapping("/operation")
    public void save(@RequestBody SelectorData selectorData) {
        if (StringUtils.hasLength(selectorData.getId())) {
            selectorDataService.updateById(selectorData);
        } else {
            if (selectorData.getSort() == null) {
                SortHelper<SelectorData> sortHelper = new SortHelper<>(selectorDataService, "sort");
                selectorData.setSort(sortHelper.next());
            }
            selectorDataService.save(selectorData);
        }
    }

    /**
     * 保存选择器数据
     *
     * @param selectorDatas 选择器数据
     * @param itemId        项目id
     * @param sfbl          是否并联
     */
    @PreAuthorize("hasAuthority('selector:selectorData:operation')")
    @OperationLog(module = "选择器数据", comments = "保存选择器数据")
    @PostMapping("/submit")
    public void submit(@RequestBody List<SelectorData> selectorDatas,
                       @RequestParam(name = "itemId") String itemId,
                       @RequestParam(name = "sfbl") JudgeMark sfbl) throws Exception {
        selectorDataService.saveSelectotData(itemId, sfbl, selectorDatas);
    }

    /**
     * 批量删除选择器数据（权限标识：selector:selectorData:remove）
     */
    @PreAuthorize("hasAuthority('selector:selectorData:remove')")
    @OperationLog(module = "选择器数据", comments = "批量删除选择器数据")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        selectorDataService.removeByIds(ids);
    }
}
