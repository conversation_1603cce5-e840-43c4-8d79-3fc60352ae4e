package com.sanythadmin.project.selector.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 选择器配置查询参数
 *
 * <AUTHOR>
 * @since 2024-05-10 15:13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SelectorConfigParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 选项标识
     */
    @QueryField(type = QueryType.EQ)
    private String key;

    /**
     * 选项名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 数据接口
     */
    private String url;

    /**
     * 字段配置
     */
    private String columns;

    /**
     * 授权角色
     */
    @QueryField(type = QueryType.IN_STR)
    private String role;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private String status;
    /**
     * 用户类别
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;
    /**
     * 参数模块
     */
    @QueryField(ignore = true)
    private String paramMode;


    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort";
    }
}
