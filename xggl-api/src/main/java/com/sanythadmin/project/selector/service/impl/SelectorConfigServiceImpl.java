package com.sanythadmin.project.selector.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.selector.mapper.SelectorConfigMapper;
import com.sanythadmin.project.selector.service.SelectorConfigService;
import com.sanythadmin.project.selector.entity.SelectorConfig;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 选择器配置Service实现
 *
 * <AUTHOR>
 * @since 2024-05-10 15:13:59
 */
@Service
public class SelectorConfigServiceImpl extends ServiceImpl<SelectorConfigMapper, SelectorConfig> implements SelectorConfigService {

    @Resource
    private SelectorConfigMapper mapper;

}
