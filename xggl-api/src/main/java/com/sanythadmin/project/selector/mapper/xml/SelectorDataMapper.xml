<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.selector.mapper.SelectorDataMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, CONFIG_KEY, CONFIG_NAME, SORT, ITEM_ID, SFBL
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE '%'||#{param.name }||'%'
                    </if>
                    <if test="param.configKey != null">
                        AND a.CONFIG_KEY LIKE '%'||#{param.configKey }||'%'
                    </if>
                    <if test="param.configName != null">
                        AND a.CONFIG_NAME LIKE '%'||#{param.configName }||'%'
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE '%'||#{param.itemId }||'%'
                    </if>
                    <if test="param.sfbl != null">
                        AND a.SFBL LIKE '%'||#{param.sfbl }||'%'
                    </if>
    </sql>

</mapper>
