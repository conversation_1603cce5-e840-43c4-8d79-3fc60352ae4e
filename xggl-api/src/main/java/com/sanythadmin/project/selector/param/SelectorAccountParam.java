package com.sanythadmin.project.selector.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2024-05-27 14:32:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SelectorAccountParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 关联项目id
     */
    private String itemId;

    /**
     * 账号
     */
    private String xgh;

}
