<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.selector.mapper.SelectorConfigMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, KEY, NAME, URL, COLUMNS, ROLE, SORT, STATUS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.key != null">
                        AND a.KEY LIKE '%'||#{param.key }||'%'
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE '%'||#{param.name }||'%'
                    </if>
                    <if test="param.url != null">
                        AND a.URL LIKE '%'||#{param.url }||'%'
                    </if>
                    <if test="param.columns != null">
                        AND a.COLUMNS LIKE '%'||#{param.columns }||'%'
                    </if>
                    <if test="param.role != null">
                        AND a.ROLE LIKE '%'||#{param.role }||'%'
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.status != null">
                        AND a.STATUS LIKE '%'||#{param.status }||'%'
                    </if>
    </sql>
</mapper>
