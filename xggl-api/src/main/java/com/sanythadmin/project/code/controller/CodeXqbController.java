package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeXqb;
import com.sanythadmin.project.code.param.CodeXqbParam;
import com.sanythadmin.project.code.service.CodeXqbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 校区代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
//@RestController
//@RequestMapping("/api/code/codeXqb")
public class CodeXqbController extends BaseController {
    @Resource
    private CodeXqbService codeXqbService;

    /**
     * 分页查询校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:list')")
    @GetMapping("/page")
    public PageResult<CodeXqb> page(CodeXqbParam param) {
        PageParam<CodeXqb, CodeXqbParam> page = new PageParam<>(param);
        page = codeXqbService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:list')")
    @GetMapping()
    public List<CodeXqb> list(CodeXqbParam param) {
        PageParam<CodeXqb, CodeXqbParam> page = new PageParam<>(param);
        return codeXqbService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:list')")
    @GetMapping("/{id}")
    public CodeXqb get(@PathVariable("id") Integer id) {
        return codeXqbService.getById(id);
        // 使用关联查询
        //return codeXqbService.getByIdRel(id);
    }

    /**
     * 添加校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:save')")
    @OperationLog(module = "校区代码", comments = "保存校区代码")
    @PostMapping()
    public void save(@RequestBody CodeXqb codeXqb) {
        if (codeXqb.getSort() == null) {
            SortHelper<CodeXqb> sortHelper = new SortHelper<>(codeXqbService, "sort");
            codeXqb.setSort(sortHelper.next());
        }
        codeXqbService.save(codeXqb);
    }

    /**
     * 修改校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:update')")
    @OperationLog(module = "校区代码", comments = "修改{table.comment!}")
    @PutMapping()
    public void update(@RequestBody CodeXqb codeXqb) {
        codeXqbService.updateById(codeXqb);
    }

    /**
     * 删除校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:remove')")
    @OperationLog(module = "校区代码", comments = "删除{table.comment!}")
    @DeleteMapping("/{id}")
    public void remove(@PathVariable("id") Integer id) {
        codeXqbService.removeById(id);
    }

    /**
     * 批量删除校区代码
     */
    @PreAuthorize("hasAuthority('code:codeXqb:remove')")
    @OperationLog(module = "校区代码", comments = "批量删除{table.comment!}")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        codeXqbService.removeByIds(ids);
    }
}
