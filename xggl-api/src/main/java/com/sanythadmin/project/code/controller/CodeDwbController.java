package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.param.CodeDwbParam;
import com.sanythadmin.project.code.service.CodeDwbService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 学院代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
@Slf4j
@RestController
@RequestMapping("/api/code/codeDwb")
public class CodeDwbController extends BaseController {
    @Resource
    private CodeDwbService codeDwbService;

    /**
     * 分页查询学院代码
     */
//    @PreAuthorize("hasAuthority('code:codeDwb:list')")
    @GetMapping("/page")
    public PageResult<CodeDwb> page(CodeDwbParam param) {
        return codeDwbService.page(param);
    }

    /**
     * 查询全部学院代码
     */
//    @PreAuthorize("hasAuthority('code:codeDwb:list')")
    @GetMapping()
    public List<CodeDwb> list(CodeDwbParam param) {
        param.setSfjxdw(StringUtils.hasText(param.getSfjxdw()) ? param.getSfjxdw() : JudgeMark.YES.getText());
        UserInfoUtil.setQueryScope(param, "id", null);
        return codeDwbService.list(param);
    }

    @GetMapping("/listAll")
    public List<CodeDwb> listAll() {
        return codeDwbService.list(new CodeDwbParam());
    }

    /**
     * 根据id查询学院代码
     */
//    @PreAuthorize("hasAuthority('code:codeDwb:list')")
    @GetMapping("/{id}")
    public CodeDwb get(@PathVariable("id") String id) {
        return codeDwbService.get(id);
    }

    /**
     * 添加学院代码
     */
    @PreAuthorize("hasAuthority('code:codeDwb:save')")
    @OperationLog(module = "学院代码", comments = "新增学院代码")
    @PostMapping()
    public void save(@RequestBody CodeDwb codeDwb) {
        codeDwbService.saveObj(codeDwb);
    }

    /**
     * 修改学院代码
     */
    @PreAuthorize("hasAuthority('code:codeDwb:update')")
    @OperationLog(module = "学院代码", comments = "修改学院代码")
    @PutMapping()
    public void update(@RequestBody CodeDwb codeDwb) {
        codeDwbService.updateObj(codeDwb);
    }

    /**
     * 批量删除学院代码
     */
    @PreAuthorize("hasAuthority('code:codeDwb:remove')")
    @OperationLog(module = "学院代码", comments = "批量删除学院代码")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        codeDwbService.removeBatch(ids);
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        new EasyExcelHelper<CodeDwb>() {
        }.out("学院代码导入模板", null, response);
    }

    /**
     * 导入数据
     *
     * @param file
     * @param response
     */
    @PostMapping("/importData")
    @OperationLog(module = "学院代码", comments = "导入数据")
    @PreAuthorize("hasAuthority('code:codeDwb:import')")
    public void importData(@RequestParam(name = "file") MultipartFile file, HttpServletResponse response) {
        codeDwbService.importData(file);
    }

    /**
     * 学院代码数据导出
     *
     * @param param
     * @param response
     */
    @GetMapping(value = "/exportData")
    @OperationLog(module = "学院代码", comments = "导出数据")
    @PreAuthorize("hasAuthority('code:codeDwb:export')")
    public void exportData(CodeDwbParam param, HttpServletResponse response) {
        try {
            List<CodeDwb> list = codeDwbService.list(param);
            new EasyExcelHelper<CodeDwb>() {
            }.out("学院代码", list, response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
