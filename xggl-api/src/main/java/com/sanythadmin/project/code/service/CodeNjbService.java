package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeNjb;
import com.sanythadmin.project.code.param.CodeNjbParam;

import java.util.List;

/**
 * 年级代码Service
 *
 * <AUTHOR>
 * @since 2024-03-10 12:32:53
 */
public interface CodeNjbService extends IService<CodeNjb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeNjb>
     */
    PageResult<CodeNjb> pageRel(CodeNjbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeNjb>
     */
    List<CodeNjb> listRel(CodeNjbParam param);

    /**
     * 根据id查询
     *
     * @param id 
     * @return CodeNjb
     */
    CodeNjb getByIdRel(String id);

}
