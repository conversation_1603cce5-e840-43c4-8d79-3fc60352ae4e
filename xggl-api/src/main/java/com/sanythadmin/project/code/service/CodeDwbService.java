package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.param.CodeDwbParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 学院代码Service
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
public interface CodeDwbService extends IService<CodeDwb> {
    void saveObj(CodeDwb obj);

    void updateObj(CodeDwb obj);

    CodeDwb get(String id);

    PageResult<CodeDwb> page(CodeDwbParam param);

    List<CodeDwb> list(CodeDwbParam param);

    void removeBatch(List<String> ids);

    void importData(MultipartFile file);
}
