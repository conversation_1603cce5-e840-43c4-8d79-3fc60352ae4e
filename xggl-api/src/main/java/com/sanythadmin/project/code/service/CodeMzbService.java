package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeMzb;
import com.sanythadmin.project.code.param.CodeMzbParam;

import java.util.List;

/**
 * Service
 *
 * <AUTHOR>
 * @since 2024-03-12 09:09:26
 */
public interface CodeMzbService extends IService<CodeMzb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeMzb>
     */
    PageResult<CodeMzb> pageRel(CodeMzbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeMzb>
     */
    List<CodeMzb> listRel(CodeMzbParam param);

    /**
     * 根据id查询
     *
     * @param id 
     * @return CodeMzb
     */
    CodeMzb getByIdRel(String id);

}
