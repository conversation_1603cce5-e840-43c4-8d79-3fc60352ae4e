<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.ScoreSourceConfigMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, TABLE_NAME, XH_COLUMN, CPNF_COLUMN, SCORE_COLUMN, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.name != null">
                        AND a.NAME LIKE concat(concat('%',#{param.name), '%')
                    </if>
                    <if test="param.tableName != null">
                        AND a.TABLE_NAME LIKE concat(concat('%',#{param.tableName), '%')
                    </if>
                    <if test="param.xhColumn != null">
                        AND a.XH_COLUMN LIKE concat(concat('%',#{param.xhColumn), '%')
                    </if>
                    <if test="param.cpnfColumn != null">
                        AND a.CPNF_COLUMN LIKE concat(concat('%',#{param.cpnfColumn), '%')
                    </if>
                    <if test="param.scoreColumn != null">
                        AND a.SCORE_COLUMN LIKE concat(concat('%',#{param.scoreColumn), '%')
                    </if>
    </sql>
</mapper>
