package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.result.CodeBjbResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 班级代码Service
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
public interface CodeBjbService extends IService<CodeBjb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeBjb>
     */
    PageResult<CodeBjbResult> pageRel(CodeBjbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeBjb>
     */
    List<CodeBjb> listRel(CodeBjbParam param);

    /**
     * 根据id查询
     *
     * @param id
     * @return CodeBjb
     */
    CodeBjb getByIdRel(String id);
    List<CodeBjb> list(CodeBjbParam param);
    void saveObj(CodeBjb codeBjb);

    void updateObj(CodeBjb codeBjb);
    void removeBatch(List<String> ids);
    void importData(MultipartFile file);
    CodeBjb get(String classNameOrCode, String majorNameOrCode, String deptNameOrCode);
}
