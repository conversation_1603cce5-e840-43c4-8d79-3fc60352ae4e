<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeZybMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_ZYB a
        <where>
            <if test="param.bz != null">
                AND a.BZ LIKE '%' || #{param.bz} || '%'
            </if>
            <if test="param.code != null">
                AND a.CODE LIKE '%' || #{param.code} || '%'
            </if>
            <if test="param.name != null">
                AND a.NAME LIKE '%' || #{param.name} || '%'
            </if>
            <if test="param.xyid != null">
                AND a.xyid in
                <foreach item="item" index="index" collection="param.xyid.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.code.result.CodeZybResult">
        select * from (select b.*,c.name xymc from (<include refid="selectSql"/>) b left join SYT_CODE_DWB c on b.xyid = c.id)
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.code.entity.CodeZyb">
        <include refid="selectSql"></include>
    </select>

</mapper>
