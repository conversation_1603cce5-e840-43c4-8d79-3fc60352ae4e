package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 培养层次代码查询参数
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodePyccbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 备注
     */
    private String bz;

    /**
     * 培养层次代码
     */
    private String code;

    /**
     * 培养层次名称
     */
    private String name;
}
