package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.constant.CodeConstants;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.mapper.CodeBjbMapper;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.result.CodeBjbResult;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.code.service.CodeCommonService;
import com.sanythadmin.project.code.service.CodeDwbService;
import com.sanythadmin.project.code.service.CodeZybService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * 班级代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_CLASS)
@Service
public class CodeBjbServiceImpl extends ServiceImpl<CodeBjbMapper, CodeBjb> implements CodeBjbService {
    @Resource
    CodeBjbMapper codeBjbMapper;
    @Resource
    CodeDwbService codeDwbService;
    @Resource
    CodeZybService codeZybService;
    @Resource
    CodeCommonService codeCommonService;

    @Override
    public PageResult<CodeBjbResult> pageRel(CodeBjbParam param) {
        PageParam<CodeBjb, CodeBjbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeBjbResult> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeBjb> listRel(CodeBjbParam param) {
        List<CodeBjb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeBjb, CodeBjbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeBjb getByIdRel(String id) {
        CodeBjbParam param = new CodeBjbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CodeBjb> list(CodeBjbParam param) {
        PageParam<CodeBjb, CodeBjbParam> page = new PageParam<>(param);
        return codeBjbMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void saveObj(CodeBjb codeBjb) {
        if (codeBjb.getSort() == null) {
            SortHelper<CodeBjb> sortHelper = new SortHelper<>(this, "sort");
            codeBjb.setSort(sortHelper.next());
        }
        codeBjbMapper.insert(codeBjb);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateObj(CodeBjb codeBjb) {
        codeBjbMapper.updateById(codeBjb);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        codeBjbMapper.deleteBatchIds(ids);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void importData(MultipartFile file) {
        EasyExcelHelper<CodeBjbResult> excelHelper = new EasyExcelHelper<CodeBjbResult>() {
        };
        List<CodeBjbResult> data = null;
        try {
            data = excelHelper.read(file.getInputStream());
        } catch (IOException e) {
            log.error("导入数据失败", e);
        }
        excelHelper.importFileEmptyCheck(data);

        HashMap<String, CodeDwb> hashMap = new HashMap<>();
        List<CodeDwb> codeDwbs = codeDwbService.list();
        for (CodeDwb codeDwb : codeDwbs) {
            hashMap.put(codeDwb.getName(), codeDwb);
            hashMap.put(codeDwb.getId(), codeDwb);
        }

        HashMap<String, CodeCommon> hashMap1 = new HashMap<>();
        List<CodeCommon> codeNjbs = codeCommonService.list(new LambdaQueryWrapper<CodeCommon>()
                .eq(CodeCommon::getCodeType, "nj"));
        for (CodeCommon codeNjb : codeNjbs) {
            hashMap1.put(codeNjb.getName(), codeNjb);
            hashMap1.put(codeNjb.getId(), codeNjb);
        }

        HashMap<String, CodeZyb> hashMap2 = new HashMap<>();
        List<CodeZyb> codeZybs = codeZybService.list();
        for (CodeZyb codeZyb : codeZybs) {
            hashMap2.put(codeZyb.getName(), codeZyb);
            hashMap2.put(codeZyb.getId(), codeZyb);
        }

        HashSet<String> hashSet = new HashSet<>();
        List<CodeBjb> list = list(new CodeBjbParam());
        for (CodeBjb obj : list) {
            hashSet.add(obj.getCode());
            CodeDwb codeDwb = hashMap.get(obj.getXyid());
            CodeCommon codeCommon = hashMap1.get(obj.getNjid());
            CodeZyb codeZyb = hashMap2.get(obj.getZyid());
            hashSet.add(obj.getName().concat(codeDwb.getName()).concat(codeCommon.getName()).concat(codeZyb.getName()));
        }

        List<ExcelImportError> errors = new ArrayList<>();
        int line = 1;
        for (CodeBjbResult obj : data) {
            line++;
            checkData(obj, errors, line, hashSet, hashMap, hashMap1, hashMap2);
        }

        if (!CollectionUtils.isEmpty(errors)) {
            excelHelper.writeImportDataErrorInfo(errors);
        } else {
            SortHelper<CodeBjb> sortHelper = new SortHelper<>(this, "sort");
            Integer total = sortHelper.getTotal();
            Integer sort = sortHelper.getLast();
            for (CodeBjbResult obj : data) {
                sort = sortHelper.next(total, sort);
                obj.setSort(sort);
                CodeBjb codeBjb = new CodeBjb();
                BeanUtils.copyProperties(obj, codeBjb);
                codeBjbMapper.insert(codeBjb);
                total++;
            }
        }
    }

    @Override
    public CodeBjb get(String classNameOrCode, String majorNameOrCode, String deptNameOrCode) {
        MyMPJLambdaWrapper<CodeBjb, CodeBjbParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.selectAll(CodeBjb.class);
        wrapper.leftJoin(CodeZyb.class, CodeZyb::getId, CodeBjb::getZyid);
        wrapper.leftJoin(CodeDwb.class, CodeDwb::getId, CodeBjb::getXyid);
        wrapper.and(q -> q.eq(CodeBjb::getCode, classNameOrCode).or().eq(CodeBjb::getName, classNameOrCode));
        wrapper.and(q -> q.eq(CodeZyb::getCode, majorNameOrCode).or().eq(CodeZyb::getName, majorNameOrCode));
        wrapper.and(q -> q.eq(CodeDwb::getCode, deptNameOrCode).or().eq(CodeDwb::getName, deptNameOrCode));
        return codeBjbMapper.selectJoinOne(CodeBjb.class, wrapper);
    }

    private static void checkData(CodeBjbResult obj, List<ExcelImportError> errors, int line, HashSet<String> hashSet, HashMap<String, CodeDwb> hashMap, HashMap<String, CodeCommon> hashMap1, HashMap<String, CodeZyb> hashMap2) {
        if (!StringUtils.hasLength(obj.getCode()))
            errors.add(ExcelImportError.data(line, obj.getCode(), "班级代码不能为空"));
        if (hashSet.contains(obj.getCode()))
            errors.add(ExcelImportError.data(line, obj.getCode(), "班级代码已存在"));
        if (!StringUtils.hasLength(obj.getName()))
            errors.add(ExcelImportError.data(line, obj.getName(), "班级名称不能为空"));
        CodeDwb codeDwb = hashMap.get(obj.getXymc());
        if (codeDwb == null) {
            errors.add(ExcelImportError.data(line, obj.getXymc(), "根据学院名称未找到学院信息"));
            return;
        } else {
            obj.setXyid(codeDwb.getId());
        }

        CodeCommon codeNjb = hashMap1.get(obj.getNjmc());
        if (codeNjb == null) {
            errors.add(ExcelImportError.data(line, obj.getNjmc(), "根据年级名称未找到年级信息"));
            return;
        } else {
            obj.setNjid(codeNjb.getId());
        }

        CodeZyb codeZyb = hashMap2.get(obj.getZymc());
        if (codeZyb == null) {
            errors.add(ExcelImportError.data(line, obj.getZymc(), "根据专业名称未找到专业信息"));
            return;
        } else {
            obj.setZyid(codeZyb.getId());
        }
        String bjmc = obj.getName().concat(codeDwb.getName()).concat(codeNjb.getName()).concat(codeZyb.getName());
        if (hashSet.contains(bjmc)) {
            errors.add(ExcelImportError.data(line, obj.getName(), "班级名称已存在"));
            return;
        }
        hashSet.add(obj.getCode());
        hashSet.add(bjmc);
    }
}
