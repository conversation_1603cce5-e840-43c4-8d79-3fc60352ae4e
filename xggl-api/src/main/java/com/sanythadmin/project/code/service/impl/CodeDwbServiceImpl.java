package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.constant.CodeConstants;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.mapper.CodeDwbMapper;
import com.sanythadmin.project.code.param.CodeDwbParam;
import com.sanythadmin.project.code.service.CodeDwbService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 学院代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_DEPT)
@Service
public class CodeDwbServiceImpl extends ServiceImpl<CodeDwbMapper, CodeDwb> implements CodeDwbService {
    @Resource
    private CodeDwbMapper codeDwbMapper;

    @CacheEvict(allEntries = true)
    @Override
    public void saveObj(CodeDwb codeDwb) {
        QueryWrapper<CodeDwb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", codeDwb.getName());
        List<CodeDwb> list = codeDwbMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list))
            AssertUtil.throwMessage("添加失败, 学院名称已存在");
        queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", codeDwb.getCode());
        list = codeDwbMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list))
            AssertUtil.throwMessage("添加失败, 学院代码已存在");
        if (codeDwb.getSort() == null) {
            SortHelper<CodeDwb> sortHelper = new SortHelper<>(this, "sort");
            codeDwb.setSort(sortHelper.next());
        }
        codeDwbMapper.insert(codeDwb);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateObj(CodeDwb obj) {
        codeDwbMapper.updateById(obj);
    }

    @Override
    public CodeDwb get(String id) {
        return codeDwbMapper.selectById(id);
    }

    @Override
    public PageResult<CodeDwb> page(CodeDwbParam param) {
        PageParam<CodeDwb, CodeDwbParam> page = new PageParam<>(param);
        page = codeDwbMapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CodeDwb> list(CodeDwbParam param) {
        PageParam<CodeDwb, CodeDwbParam> page = new PageParam<>(param);
        return codeDwbMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        codeDwbMapper.deleteBatchIds(ids);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void importData(MultipartFile file) {
        EasyExcelHelper<CodeDwb> excelHelper = new EasyExcelHelper<CodeDwb>() {
        };
        List<CodeDwb> data = null;
        try {
            data = excelHelper.read(file.getInputStream());
        } catch (IOException e) {
            log.error("导入数据失败", e);
        }
        excelHelper.importFileEmptyCheck(data);

        HashSet<String> hashSet = new HashSet<>();
        List<CodeDwb> list = list();
        for (CodeDwb obj : list) {
            hashSet.add(obj.getCode());
            hashSet.add(obj.getName());
        }

        List<ExcelImportError> errors = new ArrayList<>();
        int line = 1;
        for (CodeDwb obj : data) {
            line++;
            if (!StringUtils.hasLength(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "学院代码不能为空"));
            if (hashSet.contains(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "学院代码已存在"));
            if (!StringUtils.hasLength(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "学院名称不能为空"));
            if (hashSet.contains(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "学院名称已存在"));
            if (!StringUtils.hasLength(obj.getSfjxdw()))
                errors.add(ExcelImportError.data(line, obj.getSfjxdw(), "是否教学单位不能为空"));
            hashSet.add(obj.getCode());
            hashSet.add(obj.getName());
        }

        if (!CollectionUtils.isEmpty(errors)) {
            excelHelper.writeImportDataErrorInfo(errors);
        } else {
            SortHelper<CodeDwb> sortHelper = new SortHelper<>(this, "sort");
            Integer total = sortHelper.getTotal();
            Integer sort = sortHelper.getLast();
            for (CodeDwb obj : data) {
                sort = sortHelper.next(total, sort);
                obj.setSort(sort);
                codeDwbMapper.insert(obj);
                total++;
            }
        }
    }
}
