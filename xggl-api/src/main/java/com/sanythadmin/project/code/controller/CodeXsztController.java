package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.code.service.CodeXsztService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生状态代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-21 17:45:41
 */
@RestController
@RequestMapping("/api/code/codeXszt")
public class CodeXsztController extends BaseController {
    @Resource
    private CodeXsztService codeXsztService;

    /**
     * 分页查询学生状态代码（权限标识：code:codeXszt:list）
     */
//    @PreAuthorize("hasAuthority('code:codeXszt:list')")
    @GetMapping("/page")
    public PageResult<CodeXszt> page(CodeXsztParam param) {
        PageParam<CodeXszt, CodeXsztParam> page = new PageParam<>(param);
        page = codeXsztService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部学生状态代码（权限标识：code:codeXszt:list）
     */
//    @PreAuthorize("hasAuthority('code:codeXszt:list')")
    @GetMapping()
    public List<CodeXszt> list(CodeXsztParam param) {
        return codeXsztService.list(param);
    }

    /**
     * 根据id查询学生状态代码（权限标识：code:codeXszt:list）
     */
//    @PreAuthorize("hasAuthority('code:codeXszt:list')")
    @GetMapping("/{id}")
    public CodeXszt get(@PathVariable("id") Integer id) {
        return codeXsztService.getById(id);
    }

    /**
     * 添加或修改学生状态代码（权限标识：code:codeXszt:operation）
     */
    @PreAuthorize("hasAuthority('code:codeXszt:operation')")
    @OperationLog(module = "学生状态代码", comments = "保存学生状态代码")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody CodeXszt codeXszt) {
        codeXsztService.operation(codeXszt);
    }

    /**
     * 批量删除学生状态代码（权限标识：code:codeXszt:remove）
     */
    @PreAuthorize("hasAuthority('code:codeXszt:remove')")
    @OperationLog(module = "学生状态代码", comments = "批量删除学生状态代码")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        codeXsztService.removeBatch(ids);
    }
}
