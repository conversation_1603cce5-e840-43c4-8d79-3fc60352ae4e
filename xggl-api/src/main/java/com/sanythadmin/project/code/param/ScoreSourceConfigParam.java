package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 综测指标成绩来源查询参数
 *
 * <AUTHOR>
 * @since 2024-12-06 14:57:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ScoreSourceConfigParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 成绩类型名称
     */
    private String name;

    /**
     * 存储成绩表名
     */
    private String tableName;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort desc";
    }
}
