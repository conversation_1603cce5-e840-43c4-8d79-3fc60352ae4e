package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeZzmmb;
import com.sanythadmin.project.code.param.CodeZzmmbParam;

import java.util.List;

/**
 * 政治面貌代码表Service
 *
 * <AUTHOR>
 * @since 2024-03-12 15:37:58
 */
public interface CodeZzmmbService extends IService<CodeZzmmb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeZzmmb>
     */
    PageResult<CodeZzmmb> pageRel(CodeZzmmbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeZzmmb>
     */
    List<CodeZzmmb> listRel(CodeZzmmbParam param);

    /**
     * 根据id查询
     *
     * @param id 
     * @return CodeZzmmb
     */
    CodeZzmmb getByIdRel(String id);

}
