package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodePyccb;
import com.sanythadmin.project.code.param.CodePyccbParam;
import com.sanythadmin.project.code.service.CodePyccbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 培养层次代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
//@RestController
//@RequestMapping("/api/code/codePyccb")
public class CodePyccbController extends BaseController {
    @Resource
    private CodePyccbService codePyccbService;

    /**
     * 分页查询培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:list')")
    @GetMapping("/page")
    public ApiResult<PageResult<CodePyccb>> page(CodePyccbParam param) {
        PageParam<CodePyccb, CodePyccbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codePyccbService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(codePyccbService.pageRel(param));
    }

    /**
     * 查询全部培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:list')")
    @GetMapping()
    public ApiResult<List<CodePyccb>> list(CodePyccbParam param) {
        PageParam<CodePyccb, CodePyccbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codePyccbService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(codePyccbService.listRel(param));
    }

    /**
     * 根据id查询培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:list')")
    @GetMapping("/{id}")
    public ApiResult<CodePyccb> get(@PathVariable("id") Integer id) {
        return success(codePyccbService.getById(id));
        // 使用关联查询
        //return success(codePyccbService.getByIdRel(id));
    }

    /**
     * 添加培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:save')")
    @OperationLog
    @PostMapping()
    public void save(@RequestBody CodePyccb codePyccb) {
        if (codePyccb.getSort() == null) {
            SortHelper<CodePyccb> sortHelper = new SortHelper<>(codePyccbService, "sort");
            codePyccb.setSort(sortHelper.next());
        }
        codePyccbService.save(codePyccb);
    }

    /**
     * 修改培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody CodePyccb codePyccb) {
        if (codePyccbService.updateById(codePyccb)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (codePyccbService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量删除培养层次代码
     */
    @PreAuthorize("hasAuthority('code:codePyccb:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (codePyccbService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
