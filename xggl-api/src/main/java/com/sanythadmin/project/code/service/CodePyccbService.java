package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodePyccb;
import com.sanythadmin.project.code.param.CodePyccbParam;

import java.util.List;

/**
 * 培养层次代码Service
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
public interface CodePyccbService extends IService<CodePyccb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodePyccb>
     */
    PageResult<CodePyccb> pageRel(CodePyccbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodePyccb>
     */
    List<CodePyccb> listRel(CodePyccbParam param);

    /**
     * 根据id查询
     *
     * @param id 
     * @return CodePyccb
     */
    CodePyccb getByIdRel(String id);

}
