<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeMzbMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_MZB a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.bz != null">
                AND a.BZ LIKE '%'||#{param.bz}||'%'
            </if>
            <if test="param.code != null">
                AND a.CODE LIKE '%'||#{param.code}||'%'
            </if>
            <if test="param.name != null">
                AND a.NAME LIKE '%'||#{param.name}||'%'
            </if>
            <if test="param.sort != null">
                AND a.SORT LIKE '%'||#{param.sort}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.code.entity.CodeMzb">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.code.entity.CodeMzb">
        <include refid="selectSql"></include>
    </select>

</mapper>
