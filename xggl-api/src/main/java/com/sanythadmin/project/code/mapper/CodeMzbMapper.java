package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeMzb;
import com.sanythadmin.project.code.param.CodeMzbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 09:09:26
 */
public interface CodeMzbMapper extends BaseMapper<CodeMzb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeMzb>
     */
    List<CodeMzb> selectPageRel(@Param("page") IPage<CodeMzb> page,
                             @Param("param") CodeMzbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeMzb> selectListRel(@Param("param") CodeMzbParam param);

}
