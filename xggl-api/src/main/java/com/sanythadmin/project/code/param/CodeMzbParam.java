package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2024-03-12 09:09:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeMzbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 备注
     */
    private String bz;

    /**
     * 民族代码
     */
    private String code;

    /**
     * 民族名称
     */
    private String name;

}
