package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 校区代码查询参数
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeXqbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 备注
     */
    private String bz;

    /**
     * 校区代码
     */
    private String code;

    /**
     * 校区名称
     */
    private String name;

    /**
     * 校区地址
     */
    private String xqdz;

}
