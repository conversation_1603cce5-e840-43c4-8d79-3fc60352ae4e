package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeXqb;
import com.sanythadmin.project.code.param.CodeXqbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 校区代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
public interface CodeXqbMapper extends BaseMapper<CodeXqb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeXqb>
     */
    List<CodeXqb> selectPageRel(@Param("page") IPage<CodeXqb> page,
                             @Param("param") CodeXqbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeXqb> selectListRel(@Param("param") CodeXqbParam param);

}
