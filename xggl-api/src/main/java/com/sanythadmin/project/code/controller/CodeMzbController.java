package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeMzb;
import com.sanythadmin.project.code.param.CodeMzbParam;
import com.sanythadmin.project.code.service.CodeMzbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 民族代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-12 09:09:26
 */
//@RestController
//@RequestMapping("/api/code/codeMzb")
public class CodeMzbController extends BaseController {
    @Resource
    private CodeMzbService codeMzbService;

    /**
     * 分页查询
     */
    @PreAuthorize("hasAuthority('code:codeMzb:list')")
    @GetMapping("/page")
    public ApiResult<PageResult<CodeMzb>> page(CodeMzbParam param) {
        PageParam<CodeMzb, CodeMzbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeMzbService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(codeMzbService.pageRel(param));
    }

    /**
     * 查询全部
     */
    @PreAuthorize("hasAuthority('code:codeMzb:list')")
    @GetMapping()
    public ApiResult<List<CodeMzb>> list(CodeMzbParam param) {
        PageParam<CodeMzb, CodeMzbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeMzbService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(codeMzbService.listRel(param));
    }

    /**
     * 根据id查询
     */
    @PreAuthorize("hasAuthority('code:codeMzb:list')")
    @GetMapping("/{id}")
    public ApiResult<CodeMzb> get(@PathVariable("id") Integer id) {
        return success(codeMzbService.getById(id));
        // 使用关联查询
        //return success(codeMzbService.getByIdRel(id));
    }

    /**
     * 添加
     */
    @PreAuthorize("hasAuthority('code:codeMzb:save')")
    @OperationLog(module = "民族代码", comments = "新增民族代码")
    @PostMapping()
    public ApiResult<?> save(@RequestBody CodeMzb codeMzb) {
        if (codeMzb.getSort() == null) {

            SortHelper<CodeMzb> sortHelper = new SortHelper<>(codeMzbService, "sort");
            codeMzb.setSort(sortHelper.next());
        }
        if (codeMzbService.save(codeMzb)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改
     */
    @PreAuthorize("hasAuthority('code:codeMzb:update')")
    @OperationLog(module = "民族代码", comments = "修改民族代码")
    @PutMapping()
    public ApiResult<?> update(@RequestBody CodeMzb codeMzb) {
        if (codeMzbService.updateById(codeMzb)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除
     */
    @PreAuthorize("hasAuthority('code:codeMzb:remove')")
    @OperationLog(module = "民族代码", comments = "删除民族代码")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (codeMzbService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量删除
     */
    @PreAuthorize("hasAuthority('code:codeMzb:remove')")
    @OperationLog(module = "民族代码", comments = "批量删除民族代码")
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (codeMzbService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
