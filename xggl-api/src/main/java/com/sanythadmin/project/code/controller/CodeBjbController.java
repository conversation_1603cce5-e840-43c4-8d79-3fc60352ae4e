package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.result.CodeBjbResult;
import com.sanythadmin.project.code.service.CodeBjbService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 班级代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
@Slf4j
@RestController
@RequestMapping("/api/code/codeBjb")
public class CodeBjbController extends BaseController {
    @Resource
    private CodeBjbService codeBjbService;

    /**
     * 分页查询班级代码
     */
//    @PreAuthorize("hasAuthority('code:codeBjb:list')")
    @GetMapping("/page")
    public PageResult<CodeBjbResult> page(CodeBjbParam param) {
        return codeBjbService.pageRel(param);
    }

    /**
     * 查询全部班级代码
     */
//    @PreAuthorize("hasAuthority('code:codeBjb:list')")
    @GetMapping()
    public List<CodeBjb> list(CodeBjbParam param) {
        UserInfoUtil.setQueryScope(param);
        return codeBjbService.list(param);
    }

    /**
     * 根据id查询班级代码
     */
//    @PreAuthorize("hasAuthority('code:codeBjb:list')")
    @GetMapping("/{id}")
    public CodeBjb get(@PathVariable("id") Integer id) {
        return codeBjbService.getById(id);
    }

    /**
     * 添加班级代码
     */
    @PreAuthorize("hasAuthority('code:codeBjb:save')")
    @OperationLog(module = "班级代码", comments = "新增班级代码")
    @PostMapping()
    public void save(@RequestBody CodeBjb codeBjb) {
        codeBjbService.saveObj(codeBjb);
    }

    /**
     * 修改班级代码
     */
    @PreAuthorize("hasAuthority('code:codeBjb:update')")
    @OperationLog(module = "班级代码", comments = "修改班级代码")
    @PutMapping()
    public void update(@RequestBody CodeBjb codeBjb) {
        codeBjbService.updateObj(codeBjb);
    }

    /**
     * 批量删除班级代码
     */
    @PreAuthorize("hasAuthority('code:codeBjb:remove')")
    @OperationLog(module = "班级代码", comments = "批量删除班级代码")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        codeBjbService.removeBatch(ids);
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        new EasyExcelHelper<CodeBjbResult>() {
        }.out("班级代码导入模板", null, response);
    }

    /**
     * 导入数据
     *
     * @param file
     */
    @PostMapping("/importData")
    @PreAuthorize("hasAuthority('code:codeBjb:import')")
    @OperationLog(module = "班级代码", comments = "导入班级代码")
    public void importData(@RequestParam(name = "file") MultipartFile file) {
        codeBjbService.importData(file);
    }

    /**
     * 班级代码数据导出
     *
     * @param param
     * @param response
     */
    @GetMapping(value = "/exportData")
    @OperationLog(module = "班级代码", comments = "导出班级代码数据")
    @PreAuthorize("hasAuthority('code:codeBjb:export')")
    public void exportData(CodeBjbParam param, HttpServletResponse response) {
        try {
            param.setPage(1l);
            param.setLimit(Integer.MAX_VALUE - 1l);
            param.setInternalCall(true);
            PageResult<CodeBjbResult> pageResult = codeBjbService.pageRel(param);
            new EasyExcelHelper<CodeBjbResult>() {
            }.out("班级代码", pageResult.getList(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
