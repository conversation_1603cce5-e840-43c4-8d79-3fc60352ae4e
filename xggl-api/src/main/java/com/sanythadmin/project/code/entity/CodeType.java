package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;

import java.io.Serializable;

/**
 * 代码类型
 *
 * <AUTHOR>
 * @since 2024-04-08 10:58:40
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CODE_TYPE")
@Table(name = "SYT_CODE_TYPE")
public class CodeType implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 代码
     */
    @NotEmpty(message = "类型代码不能为空")
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 名称
     */
    @NotEmpty(message = "类型名称不能为空")
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * text取值字段名
     */
    @Column(name = "TEXT_FIELD")
    @TableField("TEXT_FIELD")
    private String textField;
    /**
     * value取值字段名
     */
    @Column(name = "VALUE_FIELD")
    @TableField("VALUE_FIELD")
    private String valueField;
}
