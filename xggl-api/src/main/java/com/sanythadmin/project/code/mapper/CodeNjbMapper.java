package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeNjb;
import com.sanythadmin.project.code.param.CodeNjbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 年级代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-10 12:32:53
 */
public interface CodeNjbMapper extends BaseMapper<CodeNjb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeNjb>
     */
    List<CodeNjb> selectPageRel(@Param("page") IPage<CodeNjb> page,
                             @Param("param") CodeNjbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeNjb> selectListRel(@Param("param") CodeNjbParam param);

}
