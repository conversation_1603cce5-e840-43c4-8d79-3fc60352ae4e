package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.param.CodeZybParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专业代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
public interface CodeZybMapper extends MyMPJBaseMapper<CodeZyb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeZyb>
     */
    List<com.sanythadmin.project.code.result.CodeZybResult> selectPageRel(@Param("page") IPage<CodeZyb> page,
                                                                          @Param("param") CodeZybParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeZyb> selectListRel(@Param("param") CodeZybParam param);

}
