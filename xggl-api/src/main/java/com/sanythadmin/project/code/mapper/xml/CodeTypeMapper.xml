<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeTypeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CODE, NAME,TEXT_FIELD, VALUE_FIELD
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_TYPE a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.code != null">
                AND a.CODE LIKE '%'||#{param.code}||'%'
            </if>
            <if test="param.name != null">
                AND a.NAME LIKE '%'||#{param.name}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.code.entity.CodeType">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.code.entity.CodeType">
        <include refid="selectSql"></include>
    </select>

</mapper>
