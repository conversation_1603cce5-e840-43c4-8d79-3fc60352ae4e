package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.result.RegionResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公共代码表Mapper
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
public interface CodeCommonMapper extends BaseMapper<CodeCommon> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeCommon>
     */
    List<CodeCommon> selectPageRel(@Param("page") IPage<CodeCommon> page,
                                   @Param("param") CodeCommonParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeCommon> selectListRel(@Param("param") CodeCommonParam param);

    List<RegionResult> selectRegionList(@Param("likeStr") String likeStr, @Param("code") String code);
}
