package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeNjb;
import com.sanythadmin.project.code.param.CodeNjbParam;
import com.sanythadmin.project.code.service.CodeNjbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 年级代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-10 12:32:53
 */
//@RestController
//@RequestMapping("/api/code/codeNjb")
public class CodeNjbController extends BaseController {
    @Resource
    private CodeNjbService codeNjbService;

    /**
     * 分页查询年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:list')")
    @GetMapping("/page")
    public ApiResult<PageResult<CodeNjb>> page(CodeNjbParam param) {
        PageParam<CodeNjb, CodeNjbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeNjbService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(codeNjbService.pageRel(param));
    }

    /**
     * 查询全部年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:list')")
    @GetMapping()
    public ApiResult<List<CodeNjb>> list(CodeNjbParam param) {
        PageParam<CodeNjb, CodeNjbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeNjbService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(codeNjbService.listRel(param));
    }

    /**
     * 根据id查询年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:list')")
    @GetMapping("/{id}")
    public ApiResult<CodeNjb> get(@PathVariable("id") Integer id) {
        return success(codeNjbService.getById(id));
        // 使用关联查询
        //return success(codeNjbService.getByIdRel(id));
    }

    /**
     * 添加年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:save')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> save(@RequestBody CodeNjb codeNjb) {
        if (codeNjb.getSort() == null) {
            SortHelper<CodeNjb> sortHelper = new SortHelper<>(codeNjbService, "sort");
            codeNjb.setSort(sortHelper.next());
        }
        if (codeNjbService.save(codeNjb)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody CodeNjb codeNjb) {
        if (codeNjbService.updateById(codeNjb)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") String id) {
        if (codeNjbService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量删除年级代码
     */
    @PreAuthorize("hasAuthority('code:codeNjb:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (codeNjbService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
