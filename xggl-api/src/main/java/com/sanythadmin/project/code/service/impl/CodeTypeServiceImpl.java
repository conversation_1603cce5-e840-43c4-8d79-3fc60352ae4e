package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.code.mapper.CodeCommonMapper;
import com.sanythadmin.project.code.mapper.CodeTypeMapper;
import com.sanythadmin.project.code.param.CodeTypeParam;
import com.sanythadmin.project.code.service.CodeTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 代码类型Service实现
 *
 * <AUTHOR>
 * @since 2024-04-08 10:58:40
 */
@Service
public class CodeTypeServiceImpl extends ServiceImpl<CodeTypeMapper, CodeType> implements CodeTypeService {

    @Resource
    CodeCommonMapper codeCommonMapper;

    @Override
    public PageResult<CodeType> pageRel(CodeTypeParam param) {
        PageParam<CodeType, CodeTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeType> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeType> listRel(CodeTypeParam param) {
        List<CodeType> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeType, CodeTypeParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeType getByIdRel(String id) {
        CodeTypeParam param = new CodeTypeParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Override
    public CodeType getByCode(String code) {
        List<CodeType> list = list(new LambdaQueryWrapper<CodeType>().eq(CodeType::getCode, code));
        if (!CollectionUtils.isEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Transactional
    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            CodeType codeType = getById(id);
            codeCommonMapper.delete(new QueryWrapper<CodeCommon>().eq("CODE_TYPE", codeType.getCode()));
            removeById(id);
        }
    }
}
