package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 公共代码表查询参数
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeCommonParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 代码ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 代码
     */
    private String code;

    /**
     * 代码名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 备注
     */
    private String description;

    /**
     * 代码类型
     */
    @QueryField(type = QueryType.IN_STR)
    private String codeType;

    public CodeCommonParam() {
    }

    public CodeCommonParam(String codeType) {
        this.codeType = codeType;
    }

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
