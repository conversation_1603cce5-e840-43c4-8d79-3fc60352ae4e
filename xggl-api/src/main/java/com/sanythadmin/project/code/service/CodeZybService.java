package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.param.CodeZybParam;
import com.sanythadmin.project.code.result.CodeZybResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 专业代码Service
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
public interface CodeZybService extends IService<CodeZyb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeZyb>
     */
    PageResult<CodeZybResult> pageRel(CodeZybParam param);

    List<CodeZyb> list(CodeZybParam param);

    void saveObj(CodeZyb obj);

    void updateObj(CodeZyb obj);

    void removeBatch(List<String> ids);

    void importData(MultipartFile file);

    public CodeZyb get(String majorNameOrCode, String deptNameOrCode);
}
