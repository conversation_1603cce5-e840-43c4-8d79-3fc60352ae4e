package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeNjb;
import com.sanythadmin.project.code.mapper.CodeNjbMapper;
import com.sanythadmin.project.code.param.CodeNjbParam;
import com.sanythadmin.project.code.service.CodeNjbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 年级代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-10 12:32:53
 */
@Service
public class CodeNjbServiceImpl extends ServiceImpl<CodeNjbMapper, CodeNjb> implements CodeNjbService {

    @Override
    public PageResult<CodeNjb> pageRel(CodeNjbParam param) {
        PageParam<CodeNjb, CodeNjbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeNjb> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeNjb> listRel(CodeNjbParam param) {
        List<CodeNjb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeNjb, CodeNjbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeNjb getByIdRel(String id) {
        CodeNjbParam param = new CodeNjbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
