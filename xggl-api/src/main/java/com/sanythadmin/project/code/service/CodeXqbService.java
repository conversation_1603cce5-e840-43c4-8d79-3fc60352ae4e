package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeXqb;
import com.sanythadmin.project.code.param.CodeXqbParam;

import java.util.List;

/**
 * 校区代码Service
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
public interface CodeXqbService extends IService<CodeXqb> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeXqb>
     */
    PageResult<CodeXqb> pageRel(CodeXqbParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeXqb>
     */
    List<CodeXqb> listRel(CodeXqbParam param);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return CodeXqb
     */
    CodeXqb getByIdRel(String id);

}
