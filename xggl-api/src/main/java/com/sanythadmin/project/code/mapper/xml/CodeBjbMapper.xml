<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeBjbMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_BJB a
        <where>
            <if test="param.bz != null">
                AND a.BZ a.CODE LIKE '%' || #{param.bz} || '%'
            </if>
            <if test="param.code != null">
                AND a.CODE LIKE '%' || #{param.code} || '%'
            </if>
            <if test="param.name != null">
                AND a.NAME LIKE '%' || #{param.name} || '%'
            </if>
            <if test="param.njid != null">
                AND a.NJID in
                <foreach item="item" index="index" collection="param.njid.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.status != null">
                AND a.STATUS = #{param.status}
            </if>
            <if test="param.xyid != null">
                AND a.xyid in
                <foreach item="item" index="index" collection="param.xyid.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.zyid != null">
                AND a.ZYID in
                <foreach item="item" index="index" collection="param.zyid.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.code.result.CodeBjbResult">
        select * from (select b.*,c.name xymc,d.name zymc, e.name njmc from (<include refid="selectSql"/>) b left join
        syt_code_dwb c
        on b.xyid = c.id left join syt_code_zyb d on b.zyid = d.id left join (select id, name from syt_code_common where code_type = 'nj') e
        on b.njid = e.id)
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.code.entity.CodeBjb">
        <include refid="selectSql"></include>
    </select>

</mapper>
