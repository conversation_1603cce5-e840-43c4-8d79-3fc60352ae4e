package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 综测指标成绩来源
 *
 * <AUTHOR>
 * @since 2024-12-06 14:57:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SCORE_SOURCE_CONFIG")
@Entity
@Table(name = "SYT_SCORE_SOURCE_CONFIG")
public class ScoreSourceConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 成绩类型名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 存储成绩表名
     */
    @Column(name = "TABLE_NAME")
    @TableField("TABLE_NAME")
    private String tableName;

    /**
     * 学号字段
     */
    @Column(name = "XH_COLUMN")
    @TableField("XH_COLUMN")
    private String xhColumn;

    /**
     * 测评年份字段
     */
    @Column(name = "CPNF_COLUMN")
    @TableField("CPNF_COLUMN")
    private String cpnfColumn;

    /**
     * 成绩字段
     */
    @Column(name = "SCORE_COLUMN")
    @TableField("SCORE_COLUMN")
    private String scoreColumn;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;
}
