package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import java.io.Serializable;
/**
 * 校区代码
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
//@Entity
@Data
@EqualsAndHashCode(callSuper = false)
//@TableName("SYT_CODE_XQB")
//@Table(name = "SYT_CODE_XQB")
public class CodeXqb implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 校区代码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 校区名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 校区地址
     */
    @Column(name = "XQDZ")
    @TableField("XQDZ")
    private String xqdz;

}
