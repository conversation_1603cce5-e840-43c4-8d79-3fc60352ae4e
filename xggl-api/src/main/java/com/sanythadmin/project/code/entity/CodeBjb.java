package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

/**
 * 班级代码
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CODE_BJB")
@Table(name = "SYT_CODE_BJB")
public class CodeBjb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 班级代码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 班级名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NJID")
    private String njid;

    /**
     * 状态（1：启用, 0: 停用）
     */
    @Column(name = "STATUS", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "STATUS", jdbcType = JdbcType.INTEGER)
    private State status;

    /**
     * 学院ID
     */
    @Column(name = "xyid", columnDefinition = ColumnType.CHAR_32)
    @TableField("xyid")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
