package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import java.io.Serializable;
/**
 * 培养层次代码
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
//@Entity
@Data
@EqualsAndHashCode(callSuper = false)
//@TableName("SYT_CODE_PYCCB")
//@Table(name = "SYT_CODE_PYCCB")
public class CodePyccb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 培养层次代码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 培养层次名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
