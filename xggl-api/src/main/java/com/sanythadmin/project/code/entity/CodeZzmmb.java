package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import java.io.Serializable;
/**
 * 政治面貌代码表
 *
 * <AUTHOR>
 * @since 2024-03-12 15:37:58
 */
//@Entity
@Data
@EqualsAndHashCode(callSuper = false)
//@TableName("SYT_CODE_ZZMMB")
//@Table(name = "SYT_CODE_ZZMMB")
public class CodeZzmmb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 政治面貌代码
     */
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 政治面貌名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

}
