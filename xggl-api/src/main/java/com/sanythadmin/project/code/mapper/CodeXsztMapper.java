package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.param.CodeXsztParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生状态代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-21 17:45:41
 */
public interface CodeXsztMapper extends BaseMapper<CodeXszt> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeXszt>
     */
    List<CodeXszt> selectPageRel(@Param("page") IPage<CodeXszt> page,
                             @Param("param") CodeXsztParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeXszt> selectListRel(@Param("param") CodeXsztParam param);

}
