package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeMzb;
import com.sanythadmin.project.code.mapper.CodeMzbMapper;
import com.sanythadmin.project.code.param.CodeMzbParam;
import com.sanythadmin.project.code.service.CodeMzbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2024-03-12 09:09:26
 */
@Service
public class CodeMzbServiceImpl extends ServiceImpl<CodeMzbMapper, CodeMzb> implements CodeMzbService {

    @Override
    public PageResult<CodeMzb> pageRel(CodeMzbParam param) {
        PageParam<CodeMzb, CodeMzbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeMzb> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeMzb> listRel(CodeMzbParam param) {
        List<CodeMzb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeMzb, CodeMzbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeMzb getByIdRel(String id) {
        CodeMzbParam param = new CodeMzbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
