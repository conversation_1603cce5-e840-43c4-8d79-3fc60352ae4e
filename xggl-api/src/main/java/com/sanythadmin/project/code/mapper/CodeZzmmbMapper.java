package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeZzmmb;
import com.sanythadmin.project.code.param.CodeZzmmbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 政治面貌代码表Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 15:37:58
 */
public interface CodeZzmmbMapper extends BaseMapper<CodeZzmmb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeZzmmb>
     */
    List<CodeZzmmb> selectPageRel(@Param("page") IPage<CodeZzmmb> page,
                             @Param("param") CodeZzmmbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeZzmmb> selectListRel(@Param("param") CodeZzmmbParam param);

}
