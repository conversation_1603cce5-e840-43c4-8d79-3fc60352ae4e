package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.ScoreSourceConfig;
import com.sanythadmin.project.code.param.ScoreSourceConfigParam;
import com.sanythadmin.project.code.service.ScoreSourceConfigService;
import com.sanythadmin.project.code.vo.ScoreSourceVO;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 综合测评/指标成绩来源控制器
 *
 * <AUTHOR>
 * @since 2024-12-06 14:57:57
 */
@RestController
@RequestMapping("/api/code/score-source-config")
public class ScoreSourceConfigController extends BaseController {
    @Resource
    private ScoreSourceConfigService scoreSourceConfigService;

    /**
     * 分页查询综测指标成绩来源（权限标识：code:scoreSourceConfig:list）
     */
    @PreAuthorize("hasAuthority('code:scoreSourceConfig:list')")
    @GetMapping("/page")
    public PageResult<ScoreSourceConfig> page(ScoreSourceConfigParam param) {
        PageParam<ScoreSourceConfig, ScoreSourceConfigParam> page = new PageParam<>(param);
        page = scoreSourceConfigService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部综测指标成绩来源
     */
    @GetMapping()
    public List<ScoreSourceVO> list(ScoreSourceConfigParam param) {
        PageParam<ScoreSourceConfig, ScoreSourceConfigParam> page = new PageParam<>(param);
        List<ScoreSourceConfig> list = scoreSourceConfigService.list(page.getOrderWrapper());
        List<ScoreSourceVO> vos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (ScoreSourceConfig scoreSourceConfig : list) {
                ScoreSourceVO scoreSourceVO = new ScoreSourceVO();
                BeanUtils.copyProperties(scoreSourceConfig, scoreSourceVO);
                vos.add(scoreSourceVO);
            }
        }
        return vos;
    }

    /**
     * 根据id查询综测指标成绩来源（权限标识：code:scoreSourceConfig:list）
     */
    @PreAuthorize("hasAuthority('code:scoreSourceConfig:list')")
    @GetMapping("/{id}")
    public ScoreSourceConfig get(@PathVariable("id") String id) {
        return scoreSourceConfigService.getById(id);
    }

    /**
     * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
     */
    @PreAuthorize("hasAuthority('code:scoreSourceConfig:operation')")
    @OperationLog(module = "综测指标成绩来源", comments = "保存综测指标成绩来源")
    @PostMapping("/operation")
    public void save(@RequestBody ScoreSourceConfig scoreSourceConfig) {
        if (scoreSourceConfig.getSort() == null) {
            SortHelper<ScoreSourceConfig> sortHelper = new SortHelper<>(scoreSourceConfigService, "sort");
            scoreSourceConfig.setSort(sortHelper.next());
        }

        if (StringUtils.hasLength(scoreSourceConfig.getId())) {
            scoreSourceConfigService.updateById(scoreSourceConfig);
        } else {
            scoreSourceConfigService.save(scoreSourceConfig);
        }
    }

    /**
     * 批量删除综测指标成绩来源（权限标识：code:scoreSourceConfig:remove）
     */
    @PreAuthorize("hasAuthority('code:scoreSourceConfig:remove')")
    @OperationLog(module = "综测指标成绩来源", comments = "批量删除综测指标成绩来源")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        scoreSourceConfigService.removeByIds(ids);
    }
}
