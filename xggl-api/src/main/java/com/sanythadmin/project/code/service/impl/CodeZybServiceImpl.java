package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.constant.CodeConstants;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.mapper.CodeZybMapper;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.param.CodeZybParam;
import com.sanythadmin.project.code.result.CodeZybResult;
import com.sanythadmin.project.code.service.CodeDwbService;
import com.sanythadmin.project.code.service.CodeZybService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * 专业代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_MAJOR)
@Service
public class CodeZybServiceImpl extends ServiceImpl<CodeZybMapper, CodeZyb> implements CodeZybService {
    @Resource
    private CodeZybMapper codeZybMapper;
    @Resource
    private CodeDwbService codeDwbService;

    @Override
    public PageResult<CodeZybResult> pageRel(CodeZybParam param) {
        PageParam<CodeZyb, CodeZybParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeZybResult> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CodeZyb> list(CodeZybParam param) {
        PageParam<CodeZyb, CodeZybParam> page = new PageParam<>(param);
        return codeZybMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void saveObj(CodeZyb codeZyb) {
        if (codeZyb.getSort() == null) {
            SortHelper<CodeZyb> sortHelper = new SortHelper<>(this, "sort");
            codeZyb.setSort(sortHelper.next());
        }
        codeZybMapper.insert(codeZyb);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateObj(CodeZyb obj) {
        codeZybMapper.updateById(obj);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        codeZybMapper.deleteBatchIds(ids);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void importData(MultipartFile file) {
        EasyExcelHelper<CodeZyb> excelHelper = new EasyExcelHelper<CodeZyb>() {
        };
        List<CodeZyb> data = null;
        try {
            data = excelHelper.read(file.getInputStream());
        } catch (IOException e) {
            log.error("导入数据失败", e);
        }
        excelHelper.importFileEmptyCheck(data);

        HashMap<String, CodeDwb> hashMap = new HashMap<>();
        List<CodeDwb> codeDwbs = codeDwbService.list();
        for (CodeDwb codeDwb : codeDwbs) {
            hashMap.put(codeDwb.getName(), codeDwb);
            hashMap.put(codeDwb.getId(), codeDwb);
        }

        HashSet<String> hashSet = new HashSet<>();
        List<CodeZyb> list = list();
        for (CodeZyb obj : list) {
            hashSet.add(obj.getCode());
            CodeDwb codeDwb = hashMap.get(obj.getXyid());
            hashSet.add(obj.getName().concat(codeDwb.getName()));
        }

        List<ExcelImportError> errors = new ArrayList<>();
        int line = 1;
        for (CodeZyb obj : data) {
            line++;
            if (!StringUtils.hasLength(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "专业代码不能为空"));
            if (hashSet.contains(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "专业代码已存在"));
            if (!StringUtils.hasLength(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "专业名称不能为空"));
            if (hashSet.contains(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "专业名称已存在"));
            CodeDwb codeDwb = hashMap.get(obj.getXymc());
            if (codeDwb == null) {
                errors.add(ExcelImportError.data(line, obj.getXymc(), "根据学院名称未找到学院"));
            } else {
                obj.setXyid(codeDwb.getId());
            }
            hashSet.add(obj.getCode());
            hashSet.add(obj.getName().concat(codeDwb.getName()));
        }

        if (!CollectionUtils.isEmpty(errors)) {
            excelHelper.writeImportDataErrorInfo(errors);
        } else {
            SortHelper<CodeZyb> sortHelper = new SortHelper<>(this, "sort");
            Integer total = sortHelper.getTotal();
            Integer sort = sortHelper.getLast();
            for (CodeZyb obj : data) {
                sort = sortHelper.next(total, sort);
                obj.setSort(sort);
                codeZybMapper.insert(obj);
                total++;
            }
        }
    }

    @Override
    public CodeZyb get(String majorNameOrCode, String deptNameOrCode) {
        MyMPJLambdaWrapper<CodeZyb, CodeZybParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.selectAll(CodeZyb.class);
        wrapper.leftJoin(CodeDwb.class, CodeDwb::getId, CodeZyb::getXyid);
        wrapper.and(q -> q.eq(CodeZyb::getCode, majorNameOrCode).or().eq(CodeZyb::getName, majorNameOrCode));
        wrapper.and(q -> q.eq(CodeDwb::getCode, deptNameOrCode).or().eq(CodeDwb::getName, deptNameOrCode));
        return codeZybMapper.selectJoinOne(CodeZyb.class, wrapper);
    }
}
