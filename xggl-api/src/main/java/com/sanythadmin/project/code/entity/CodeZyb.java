package com.sanythadmin.project.code.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 专业代码
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CODE_ZYB")
@Table(name = "SYT_CODE_ZYB")
public class CodeZyb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ExcelIgnore
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 4)
    @ColumnWidth(value = 20)
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 专业代码
     */
    @ExcelProperty(value = "专业代码", index = 1)
    @ColumnWidth(value = 20)
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 专业名称
     */
    @ExcelProperty(value = "专业名称", index = 0)
    @ColumnWidth(value = 20)
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 学院ID
     */
    @ExcelIgnore
    @Column(name = "xyid")
    @TableField("xyid")
    private String xyid;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序", index = 3)
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    @ExcelProperty(value = "学院名称", index = 2)
    @ColumnWidth(value = 20)
    @Transient
    @TableField(exist = false)
    private String xymc;
}
