package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.param.CodeDwbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学院代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
public interface CodeDwbMapper extends BaseMapper<CodeDwb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeDwb>
     */
    List<CodeDwb> selectPageRel(@Param("page") IPage<CodeDwb> page,
                             @Param("param") CodeDwbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeDwb> selectListRel(@Param("param") CodeDwbParam param);

}
