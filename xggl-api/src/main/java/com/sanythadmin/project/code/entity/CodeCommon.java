package com.sanythadmin.project.code.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 公共代码表
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CODE_COMMON")
@Table(name = "SYT_CODE_COMMON")
public class CodeCommon implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 代码
     */
    @ExcelProperty(value = "代码", index = 0)
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 代码名称
     */
    @NotEmpty(message = "名称不能为空")
    @ExcelProperty(value = "名称", index = 1)
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序", index = 2)
    @Column(name = "SORT", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT")
    private Long sort;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 3)
    @Column(name = "DESCRIPTION", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 代码类型
     */
    @NotEmpty(message = "代码类型不能为空")
    @ExcelIgnore
    @Column(name = "CODE_TYPE")
    @TableField(value = "CODE_TYPE")
    private String codeType;

}
