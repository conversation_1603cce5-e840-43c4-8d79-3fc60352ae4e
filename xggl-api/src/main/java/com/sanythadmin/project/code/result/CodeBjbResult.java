package com.sanythadmin.project.code.result;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.Serializable;

@Data
public class CodeBjbResult implements Serializable {
    @ExcelIgnore
    private String id;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 7)
    private String bz;

    /**
     * 班级代码
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "班级代码", index = 1)
    private String code;

    /**
     * 班级名称
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "班级名称", index = 0)
    private String name;

    /**
     * 年级ID
     */
    @ExcelIgnore
    private String njid;
    /**
     * 年级名称
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "年级名称", index = 2)
    private String njmc;

    /**
     * 状态（1：启用, 0: 停用）
     */
    @ExcelIgnore
    private State status;
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "状态（启用, 停用）", index = 6)
    private String statusStr;

    /**
     * 学院ID
     */
    @ExcelIgnore
    private String xyid;
    /**
     * 学院名称
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "学院名称", index = 4)
    private String xymc;

    /**
     * 专业ID
     */
    @ExcelIgnore
    private String zyid;

    /**
     * 专业名称
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "专业名称", index = 3)
    private String zymc;

    /**
     * 排序
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "排序", index = 5)
    private Integer sort;

    public State getStatus() {
        if (status != null) return status;
        String str = this.statusStr;
        if (!StringUtils.hasLength(str))
            return null;
        return State.get(str);
    }

    public String getStatusStr() {
        if (StringUtils.hasLength(statusStr))
            return statusStr;
        State state = this.status;
        if (state != null)
            return state.getText();
        return null;
    }
}
