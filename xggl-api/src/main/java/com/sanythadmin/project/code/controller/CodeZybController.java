package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.param.CodeZybParam;
import com.sanythadmin.project.code.result.CodeZybResult;
import com.sanythadmin.project.code.service.CodeZybService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 专业代码控制器
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
@Slf4j
@RestController
@RequestMapping("/api/code/codeZyb")
public class CodeZybController extends BaseController {
    @Resource
    private CodeZybService codeZybService;

    /**
     * 分页查询专业代码
     */
//    @PreAuthorize("hasAuthority('code:codeZyb:list')")
    @GetMapping("/page")
    public PageResult<CodeZybResult> page(CodeZybParam param) {
        return codeZybService.pageRel(param);
    }

    /**
     * 查询全部专业代码
     */
//    @PreAuthorize("hasAuthority('code:codeZyb:list')")
    @GetMapping()
    public List<CodeZyb> list(CodeZybParam param) {
        UserInfoUtil.setQueryScope(param);
        return codeZybService.list(param);
    }

    /**
     * 根据id查询专业代码
     */
//    @PreAuthorize("hasAuthority('code:codeZyb:list')")
    @GetMapping("/{id}")
    public CodeZyb get(@PathVariable("id") Integer id) {
        return codeZybService.getById(id);
    }

    /**
     * 添加专业代码
     */
    @PreAuthorize("hasAuthority('code:codeZyb:save')")
    @OperationLog(module = "专业代码", comments = "保存")
    @PostMapping()
    public void save(@RequestBody CodeZyb codeZyb) {
        codeZybService.saveObj(codeZyb);
    }

    /**
     * 修改专业代码
     */
    @PreAuthorize("hasAuthority('code:codeZyb:update')")
    @OperationLog(module = "专业代码", comments = "修改")
    @PutMapping()
    public void update(@RequestBody CodeZyb codeZyb) {
        codeZybService.updateObj(codeZyb);
    }

    /**
     * 批量删除专业代码
     */
    @PreAuthorize("hasAuthority('code:codeZyb:remove')")
    @OperationLog(module = "专业代码", comments = "批量删除")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        codeZybService.removeBatch(ids);
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        new EasyExcelHelper<CodeZyb>() {
        }.out("专业代码导入模板", null, response);
    }

    /**
     * 导入数据
     *
     * @param file
     */
    @PostMapping("/importData")
    @PreAuthorize("hasAuthority('code:codeZyb:import')")
    @OperationLog(module = "专业代码", comments = "导入数据")
    public void importData(@RequestParam(name = "file") MultipartFile file) {
        codeZybService.importData(file);
    }

    /**
     * 专业代码数据导出
     *
     * @param param
     * @param response
     */
    @GetMapping(value = "/exportData")
    @PreAuthorize("hasAuthority('code:codeZyb:export')")
    @OperationLog(module = "专业代码", comments = "导出数据")
    public void exportData(CodeZybParam param, HttpServletResponse response) {
        try {
            String[] title = {"专业名称", "专业代码", "学院名称", "排序", "备注"};
            String[] property = {"name", "code", "xymc", "sort", "bz"};
            param.setPage(1l);
            param.setLimit(Integer.MAX_VALUE - 1l);
            param.setInternalCall(true);
            PageResult<CodeZybResult> pageResult = page(param);
            EasyExcelHelper.exportExcel("专业代码", pageResult.getList(), title, property, response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
