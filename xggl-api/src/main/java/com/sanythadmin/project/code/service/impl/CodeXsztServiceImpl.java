package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.constant.CodeConstants;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.mapper.CodeXsztMapper;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.code.service.CodeXsztService;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学生状态代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-21 17:45:41
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_STATE)
@Service
public class CodeXsztServiceImpl extends ServiceImpl<CodeXsztMapper, CodeXszt> implements CodeXsztService {
    @Resource
    CodeXsztMapper codeXsztMapper;

    @CacheEvict(allEntries = true)
    @Override
    public void operation(CodeXszt codeXszt) {
        if (StringUtils.hasLength(codeXszt.getId())) {
            codeXsztMapper.updateById(codeXszt);
        } else {
            SortHelper<CodeXszt> sortHelper = new SortHelper<>(this, "sort");
            codeXszt.setSort(sortHelper.next());
            codeXsztMapper.insert(codeXszt);
        }
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        codeXsztMapper.deleteBatchIds(ids);
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CodeXszt> list(CodeXsztParam param) {
        PageParam<CodeXszt, CodeXsztParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }
}
