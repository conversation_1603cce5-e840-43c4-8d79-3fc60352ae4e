<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeXsztMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_XSZT a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.ztmc != null">
                AND a.ZTMC LIKE '%'||#{param.ztmc}||'%'
            </if>
            <if test="param.sfzx != null">
                AND a.SFZX = #{param.sfzx}
            </if>
            <if test="param.sort != null">
                AND a.SORT = #{param.sort}
            </if>
            <if test="param.sfzj != null">
                AND a.sfzj = #{param.sfzj}
            </if>
            <if test="param.sfzd != null">
                AND a.SFZD = #{param.sfzd}
            </if>
            <if test="param.sfby != null">
                AND a.SFBY = #{param.sfby}
            </if>
            <if test="param.bz != null">
                AND a.BZ LIKE '%'||#{param.bz}||'%'
            </if>
        </where>
    </sql>
</mapper>
