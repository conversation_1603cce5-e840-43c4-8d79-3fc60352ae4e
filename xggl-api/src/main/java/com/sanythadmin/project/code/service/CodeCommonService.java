package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.result.RegionResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 公共代码表Service
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
public interface CodeCommonService extends IService<CodeCommon> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeCommon>
     */
    PageResult<CodeCommon> pageRel(CodeCommonParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeCommon>
     */
    List<CodeCommon> listRel(CodeCommonParam param);

    /**
     * 根据id查询
     *
     * @param id
     * @return CodeCommon
     */
    CodeCommon getByIdRel(String id);
    List<RegionResult> selectRegionList(String likeStr, String code);
    List<CodeCommon> list(CodeCommonParam param);
    void operation(CodeCommon codeCommon);
    void removeBatch(List<String> ids);
    void importData(MultipartFile file, String codeType);
}
