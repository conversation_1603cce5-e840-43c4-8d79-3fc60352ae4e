package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.ApiResult;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeZzmmb;
import com.sanythadmin.project.code.param.CodeZzmmbParam;
import com.sanythadmin.project.code.service.CodeZzmmbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 政治面貌代码表控制器
 *
 * <AUTHOR>
 * @since 2024-03-12 15:37:58
 */
//@RestController
//@RequestMapping("/api/code/codeZzmmb")
public class CodeZzmmbController extends BaseController {
    @Resource
    private CodeZzmmbService codeZzmmbService;

    /**
     * 分页查询政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:list')")
    @GetMapping("/page")
    public ApiResult<PageResult<CodeZzmmb>> page(CodeZzmmbParam param) {
        PageParam<CodeZzmmb, CodeZzmmbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeZzmmbService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(codeZzmmbService.pageRel(param));
    }

    /**
     * 查询全部政治面貌代码表
     */
//    @PreAuthorize("hasAuthority('code:codeZzmmb:list')")
    @GetMapping()
    public ApiResult<List<CodeZzmmb>> list(CodeZzmmbParam param) {
        PageParam<CodeZzmmb, CodeZzmmbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(codeZzmmbService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(codeZzmmbService.listRel(param));
    }

    /**
     * 根据id查询政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:list')")
    @GetMapping("/{id}")
    public ApiResult<CodeZzmmb> get(@PathVariable("id") Integer id) {
        return success(codeZzmmbService.getById(id));
        // 使用关联查询
        //return success(codeZzmmbService.getByIdRel(id));
    }

    /**
     * 添加政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:save')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> save(@RequestBody CodeZzmmb codeZzmmb) {
        if (codeZzmmbService.save(codeZzmmb)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody CodeZzmmb codeZzmmb) {
        if (codeZzmmbService.updateById(codeZzmmb)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (codeZzmmbService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量删除政治面貌代码表
     */
    @PreAuthorize("hasAuthority('code:codeZzmmb:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (codeZzmmbService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
