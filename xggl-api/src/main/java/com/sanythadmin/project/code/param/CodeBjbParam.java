package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 班级代码查询参数
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeBjbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 备注
     */
    private String bz;

    /**
     * 班级代码
     */
    private String code;

    /**
     * 班级名称
     */
    private String name;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String njid;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private State status;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String zyid;


    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
