package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.code.param.CodeTypeParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代码类型Mapper
 *
 * <AUTHOR>
 * @since 2024-04-08 10:58:40
 */
public interface CodeTypeMapper extends BaseMapper<CodeType> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeType>
     */
    List<CodeType> selectPageRel(@Param("page") IPage<CodeType> page,
                             @Param("param") CodeTypeParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeType> selectListRel(@Param("param") CodeTypeParam param);

}
