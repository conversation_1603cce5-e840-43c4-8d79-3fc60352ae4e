package com.sanythadmin.project.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.param.CodeXsztParam;

import java.util.List;

/**
 * 学生状态代码Service
 *
 * <AUTHOR>
 * @since 2024-03-21 17:45:41
 */
public interface CodeXsztService extends IService<CodeXszt> {
    void operation(CodeXszt param);

    void removeBatch(List<String> ids);

    List<CodeXszt> list(CodeXsztParam param);
}
