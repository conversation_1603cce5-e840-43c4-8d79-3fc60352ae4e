package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodePyccb;
import com.sanythadmin.project.code.mapper.CodePyccbMapper;
import com.sanythadmin.project.code.param.CodePyccbParam;
import com.sanythadmin.project.code.service.CodePyccbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 培养层次代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
@Service
public class CodePyccbServiceImpl extends ServiceImpl<CodePyccbMapper, CodePyccb> implements CodePyccbService {

    @Override
    public PageResult<CodePyccb> pageRel(CodePyccbParam param) {
        PageParam<CodePyccb, CodePyccbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodePyccb> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodePyccb> listRel(CodePyccbParam param) {
        List<CodePyccb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodePyccb, CodePyccbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodePyccb getByIdRel(String id) {
        CodePyccbParam param = new CodePyccbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
