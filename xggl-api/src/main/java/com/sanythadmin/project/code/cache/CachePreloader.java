package com.sanythadmin.project.code.cache;

import com.sanythadmin.common.system.param.MenuParam;
import com.sanythadmin.common.system.param.RoleParam;
import com.sanythadmin.common.system.param.SysRoleMenuParam;
import com.sanythadmin.common.system.service.RoleMenuService;
import com.sanythadmin.common.system.service.SysJwtService;
import com.sanythadmin.common.system.service.SysMenuService;
import com.sanythadmin.common.system.service.SysRoleService;
import com.sanythadmin.project.code.param.*;
import com.sanythadmin.project.code.service.*;
import lombok.AllArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 初始化加载码表数据
 * Created by JIANGPING on 2024/7/15.
 */
@AllArgsConstructor
@Component
public class CachePreloader implements ApplicationListener<ApplicationReadyEvent> {
    private final CodeDwbService codeDwbService;
    private final CodeZybService codeZybService;
    private final CodeBjbService codeBjbService;
    private final CodeCommonService codeCommonService;
    private final CodeXsztService codeXsztService;
    private final RoleMenuService roleMenuService;
    private final SysMenuService sysMenuService;
    private final SysJwtService sysJwtService;
    private final SysRoleService sysRoleService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        codeDwbService.list(new CodeDwbParam());
        codeZybService.list(new CodeZybParam());
        codeBjbService.list(new CodeBjbParam());
        codeCommonService.list(new CodeCommonParam());
        codeXsztService.list(new CodeXsztParam());
        sysMenuService.list(new MenuParam());
        roleMenuService.list(new SysRoleMenuParam());
        sysJwtService.checkAndUpdate();
        sysRoleService.list(new RoleParam());
    }
}
