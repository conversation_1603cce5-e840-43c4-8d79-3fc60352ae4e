package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.project.code.entity.CodePyccb;
import com.sanythadmin.project.code.param.CodePyccbParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 培养层次代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-11 15:12:54
 */
public interface CodePyccbMapper extends BaseMapper<CodePyccb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodePyccb>
     */
    List<CodePyccb> selectPageRel(@Param("page") IPage<CodePyccb> page,
                             @Param("param") CodePyccbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodePyccb> selectListRel(@Param("param") CodePyccbParam param);

}
