<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.code.mapper.CodeCommonMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYT_CODE_COMMON a
        <where>
            <if test="param.id != null">
                AND a.ID = #{param.id}
            </if>
            <if test="param.code != null">
                AND a.CODE = #{param.code}
            </if>
            <if test="param.name != null">
                AND a.NAME = #{param.name}
            </if>
            <!--            <if test="param.sort != null">-->
            <!--                AND a.SORT LIKE '%'||#{param.sort}||'%'-->
            <!--            </if>-->
            <!--            <if test="param.description != null">-->
            <!--                AND a.DESCRIPTION LIKE '%'||#{param.description}||'%'-->
            <!--            </if>-->
            <if test="param.codeType != null">
                AND a.CODE_TYPE = #{param.codeType}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.sanythadmin.project.code.entity.CodeCommon">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.sanythadmin.project.code.entity.CodeCommon">
        <include refid="selectSql"></include>
    </select>

    <select id="selectRegionList" resultType="com.sanythadmin.project.code.result.RegionResult">
        select code as value, name as label from syt_code_common where code_type = 'xzqh'
        <if test="likeStr != null">
            and code like #{likeStr}
        </if>
        <if test="code != null">
            and code not in (#{code})
        </if>
        order by sort asc
    </select>
</mapper>
