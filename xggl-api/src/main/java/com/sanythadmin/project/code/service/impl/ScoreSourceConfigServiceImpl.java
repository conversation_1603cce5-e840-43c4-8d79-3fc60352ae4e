package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.code.mapper.ScoreSourceConfigMapper;
import com.sanythadmin.project.code.service.ScoreSourceConfigService;
import com.sanythadmin.project.code.entity.ScoreSourceConfig;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 综测指标成绩来源Service实现
 *
 * <AUTHOR>
 * @since 2024-12-06 14:57:57
 */
@Service
public class ScoreSourceConfigServiceImpl extends ServiceImpl<ScoreSourceConfigMapper, ScoreSourceConfig> implements ScoreSourceConfigService {

    @Resource
    private ScoreSourceConfigMapper mapper;

}
