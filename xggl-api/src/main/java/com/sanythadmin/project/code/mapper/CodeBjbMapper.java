package com.sanythadmin.project.code.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.result.CodeBjbResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
public interface CodeBjbMapper extends MyMPJBaseMapper<CodeBjb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeBjb>
     */
    List<CodeBjbResult> selectPageRel(@Param("page") IPage<CodeBjb> page,
                                      @Param("param") CodeBjbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeBjb> selectListRel(@Param("param") CodeBjbParam param);

}
