package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.constant.CodeConstants;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.mapper.CodeCommonMapper;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.result.RegionResult;
import com.sanythadmin.project.code.service.CodeCommonService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 公共代码表Service实现
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_COMMON)
@Service
public class CodeCommonServiceImpl extends ServiceImpl<CodeCommonMapper, CodeCommon> implements CodeCommonService {
    @Resource
    CodeCommonMapper codeCommonMapper;

    @Override
    public PageResult<CodeCommon> pageRel(CodeCommonParam param) {
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeCommon> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeCommon> listRel(CodeCommonParam param) {
        List<CodeCommon> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeCommon getByIdRel(String id) {
        CodeCommonParam param = new CodeCommonParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Cacheable
    @Override
    public List<RegionResult> selectRegionList(String likeStr, String code) {
        return baseMapper.selectRegionList(likeStr, code);
    }

    @Cacheable(unless = "#result == null || #result.isEmpty()")
    @Override
    public List<CodeCommon> list(CodeCommonParam param) {
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
        return codeCommonMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void operation(CodeCommon code) {
        CodeCommonParam param = new CodeCommonParam();
        param.setCodeType(code.getCodeType());
        param.setCode(code.getCode());
        List<CodeCommon> list = listRel(param);
        if (!CollectionUtils.isEmpty(list)) {
            if (code.getId() == null || !list.get(0).getId().equals(code.getId()))
                AssertUtil.throwMessage("操作失败, 代码已存在");
        }

        param.setCode(null);
        param.setName(code.getName());
        list = listRel(param);
        if (!CollectionUtils.isEmpty(list)) {
            if (!list.get(0).getName().equals(code.getName()))
                AssertUtil.throwMessage("操作失败, 代码名称已存在");
        }
        if (code.getSort() == null) {
            QueryWrapper<CodeCommon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code_type", code.getCodeType());
            SortHelper<CodeCommon> sortHelper = new SortHelper<>(this, "sort", queryWrapper);
            code.setSort((long) sortHelper.next());
        }
        if (StringUtils.hasText(code.getId())) {
            codeCommonMapper.updateById(code);
        } else {
            codeCommonMapper.insert(code);
        }
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        codeCommonMapper.deleteBatchIds(ids);
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void importData(MultipartFile file, String codeType) {
        EasyExcelHelper<CodeCommon> excelHelper = new EasyExcelHelper<CodeCommon>() {
        };
        List<CodeCommon> data = null;
        try {
            data = excelHelper.read(file.getInputStream());
        } catch (IOException e) {
            log.error("导入数据失败", e);
        }
        excelHelper.importFileEmptyCheck(data);

        HashSet<String> hashSet = new HashSet<>();
        CodeCommonParam code = new CodeCommonParam();
        code.setCodeType(codeType);
        List<CodeCommon> list = listRel(code);
        for (CodeCommon obj : list) {
            hashSet.add(obj.getCode());
            hashSet.add(obj.getName());
        }

        List<ExcelImportError> errors = new ArrayList<>();
        int line = 1;
        for (CodeCommon obj : data) {
            line++;
            if (!StringUtils.hasLength(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "代码不能为空"));
            if (hashSet.contains(obj.getCode()))
                errors.add(ExcelImportError.data(line, obj.getCode(), "代码已存在"));
            if (!StringUtils.hasLength(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "名称不能为空"));
            if (hashSet.contains(obj.getName()))
                errors.add(ExcelImportError.data(line, obj.getName(), "名称已存在"));
            hashSet.add(obj.getCode());
            hashSet.add(obj.getName());
        }

        if (!CollectionUtils.isEmpty(errors)) {
            excelHelper.writeImportDataErrorInfo(errors);
        } else {
            SortHelper<CodeCommon> sortHelper = new SortHelper<>(this, "sort");
            Integer total = sortHelper.getTotal();
            Integer sort = sortHelper.getLast();
            for (CodeCommon obj : data) {
                sort = sortHelper.next(total, sort);
                obj.setSort(Long.valueOf(sort));
                obj.setCodeType(codeType);
                codeCommonMapper.insert(obj);
                total++;
            }
        }
    }
}
