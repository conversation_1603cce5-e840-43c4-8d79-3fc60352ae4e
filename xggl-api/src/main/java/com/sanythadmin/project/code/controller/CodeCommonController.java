package com.sanythadmin.project.code.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeType;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.result.RegionResult;
import com.sanythadmin.project.code.service.CodeCommonService;
import com.sanythadmin.project.code.service.CodeTypeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 公共代码表控制器
 *
 * <AUTHOR>
 * @since 2024-03-18 10:23:59
 */
@Slf4j
@RestController
@RequestMapping("/api/code/codeCommon")
public class CodeCommonController extends BaseController {
    @Resource
    private CodeCommonService codeCommonService;
    @Resource
    private CodeTypeService codeTypeService;

    /**
     * 分页查询公共代码表（权限标识：code:codeCommon:list）
     */
//    @PreAuthorize("hasAuthority('code:codeCommon:list')")
    @GetMapping("/page")
    public PageResult<CodeCommon> page(CodeCommonParam param) {
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
        page = codeCommonService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 分页查询公共代码表（权限标识：code:codeCommon:list）
     */
//    @PreAuthorize("hasAuthority('code:codeCommon:list')")
    @GetMapping()
    public List<CodeCommon> list(CodeCommonParam param) {
        return codeCommonService.list(param);
    }

    /**
     * 根据id查询公共代码表（权限标识：code:codeCommon:list）
     */
//    @PreAuthorize("hasAuthority('code:codeCommon:list')")
    @GetMapping("/{id}")
    public CodeCommon get(@PathVariable("id") Integer id) {
        return codeCommonService.getById(id);
    }

    /**
     * 添加公共代码表（权限标识：code:codeCommon:save）
     */
    @PreAuthorize("hasAuthority('code:codeCommon:save')")
    @OperationLog(module = "公共代码表", comments = "保存公共代码")
    @PostMapping("/operation")
    public void operation(@Validated @RequestBody CodeCommon code) {
        codeCommonService.operation(code);
    }


    /**
     * 删除公共代码表（权限标识：code:codeCommon:remove）
     */
    @PreAuthorize("hasAuthority('code:codeCommon:remove')")
    @OperationLog(module = "公共代码表", comments = "删除公共代码")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        codeCommonService.removeBatch(ids);
    }


    /**
     * 下载导入模板
     *
     * @param codeType
     * @param response
     */
    @GetMapping("/importTemplate")
    public void importTemplate(@RequestParam(name = "codeType") String codeType, HttpServletResponse response) {
        CodeType byCode = codeTypeService.getByCode(codeType);
        new EasyExcelHelper<CodeCommon>() {
        }.out(byCode.getName() + "导入模板", null, response);
    }

    /**
     * 导入数据（权限标识：code:codeCommon:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('code:codeCommon:import')")
    @PostMapping("/importData")
    @OperationLog(module = "公共代码表", comments = "导入公共代码")
    public void importData(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "codeType") String codeType) {
        codeCommonService.importData(file, codeType);
    }

    /**
     * 学院代码数据导出（权限标识：code:codeCommon:export）
     *
     * @param param
     * @param response
     */
    @PreAuthorize("hasAuthority('code:codeCommon:export')")
    @GetMapping(value = "/exportData")
    @OperationLog(module = "公共代码表", comments = "导出公共代码数据")
    public void exportData(CodeCommonParam param, HttpServletResponse response) {
        try {
            String fileName = "公共代码数据";
            if (StringUtils.hasLength(param.getCodeType())) {
                CodeType byCode = codeTypeService.getByCode(param.getCodeType());
                fileName = byCode != null ? byCode.getName() : fileName;
            }
            param.setPage(1l);
            param.setLimit(Integer.MAX_VALUE - 1l);
            param.setInternalCall(true);
            PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
            page = codeCommonService.page(page, page.getWrapper());
            new EasyExcelHelper<CodeCommon>() {
            }.out(fileName, page.getRecords(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @GetMapping(value = "/regionList1")
    public List<RegionResult> regionList1(String code) {
        String likeStr = "%0000";
        if (!StringUtils.hasLength(code))
            return codeCommonService.selectRegionList(likeStr, null);
        likeStr = code.substring(0, 4).concat("%");
        List<RegionResult> results = codeCommonService.selectRegionList(likeStr, code);
        if (CollectionUtils.isEmpty(results)) {
            likeStr = code.substring(0, 2).concat("%").concat("00");
            results = codeCommonService.selectRegionList(likeStr, code);
        }
        return results;
    }

    private List<CodeCommon> getCodeCommons() {
        CodeCommonParam param = new CodeCommonParam();
        param.setCodeType("xzqh");
        param.setSort("sort asc");
        return codeCommonService.list(param);
    }

    @GetMapping(value = "/regionList")
    public List<RegionResult> regionList() {
        List<CodeCommon> commons = getCodeCommons();
        List<RegionResult> provinceList = new ArrayList<>();
        commons.forEach(common -> {
            if (common.getCode().endsWith("0000")) {
                RegionResult province = new RegionResult();
                province.setLabel(common.getName());
                province.setValue(common.getCode());
                province.setChildren(cityList(province.getValue(), commons, true));
                provinceList.add(province);
            }
        });
        return provinceList;
    }

    private List<RegionResult> cityList(String provinceCode, List<CodeCommon> commons, boolean getCounty) {
        List<RegionResult> cityList = new ArrayList<>();
        String prefix = provinceCode.substring(0, 2);
        commons.forEach(common -> {
            boolean flag = common.getCode().startsWith(prefix) && common.getCode().endsWith("00") && !common.getCode().equals(provinceCode);
            if (flag) {
                RegionResult city = new RegionResult();
                city.setLabel(common.getName());
                city.setValue(common.getCode());
                if (getCounty)
                    city.setChildren(countyList(city.getValue(), commons));
                cityList.add(city);
            }
        });
        return cityList;
    }

    private List<RegionResult> countyList(String cityCode, List<CodeCommon> commons) {
        List<RegionResult> countyList = new ArrayList<>();
        String prefix = cityCode.substring(0, 4);
        commons.forEach(common -> {
            if (common.getCode().startsWith(prefix) && !common.getCode().equals(cityCode)) {
                RegionResult county = new RegionResult();
                county.setLabel(common.getName());
                county.setValue(common.getCode());
                countyList.add(county);
            }
        });
        return countyList;
    }
}
