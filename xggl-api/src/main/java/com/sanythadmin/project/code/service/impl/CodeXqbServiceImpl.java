package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeXqb;
import com.sanythadmin.project.code.mapper.CodeXqbMapper;
import com.sanythadmin.project.code.param.CodeXqbParam;
import com.sanythadmin.project.code.service.CodeXqbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 校区代码Service实现
 *
 * <AUTHOR>
 * @since 2024-03-12 17:42:25
 */
@Service
public class CodeXqbServiceImpl extends ServiceImpl<CodeXqbMapper, CodeXqb> implements CodeXqbService {

    @Override
    public PageResult<CodeXqb> pageRel(CodeXqbParam param) {
        PageParam<CodeXqb, CodeXqbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeXqb> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeXqb> listRel(CodeXqbParam param) {
        List<CodeXqb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeXqb, CodeXqbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeXqb getByIdRel(String id) {
        CodeXqbParam param = new CodeXqbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
