package com.sanythadmin.project.code.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeZzmmb;
import com.sanythadmin.project.code.mapper.CodeZzmmbMapper;
import com.sanythadmin.project.code.param.CodeZzmmbParam;
import com.sanythadmin.project.code.service.CodeZzmmbService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 政治面貌代码表Service实现
 *
 * <AUTHOR>
 * @since 2024-03-12 15:37:58
 */
@Service
public class CodeZzmmbServiceImpl extends ServiceImpl<CodeZzmmbMapper, CodeZzmmb> implements CodeZzmmbService {

    @Override
    public PageResult<CodeZzmmb> pageRel(CodeZzmmbParam param) {
        PageParam<CodeZzmmb, CodeZzmmbParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeZzmmb> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeZzmmb> listRel(CodeZzmmbParam param) {
        List<CodeZzmmb> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeZzmmb, CodeZzmmbParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeZzmmb getByIdRel(String id) {
        CodeZzmmbParam param = new CodeZzmmbParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
