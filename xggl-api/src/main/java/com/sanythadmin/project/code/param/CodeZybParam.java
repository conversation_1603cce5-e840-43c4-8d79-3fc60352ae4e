package com.sanythadmin.project.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 专业代码查询参数
 *
 * <AUTHOR>
 * @since 2024-03-12 13:52:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeZybParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 专业代码
     */
    private String code;

    /**
     * 专业名称
     */
    private String name;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;
    /**
     * 备注信息
     */
    private String bz;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
