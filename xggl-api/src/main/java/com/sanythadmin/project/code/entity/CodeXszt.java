package com.sanythadmin.project.code.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import java.io.Serializable;

/**
 * 学生状态代码
 *
 * <AUTHOR>
 * @since 2024-03-21 17:45:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("SYT_CODE_XSZT")
@Table(name = "SYT_CODE_XSZT")
public class CodeXszt implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 状态名称
     */
    @NotEmpty(message = "状态名称不能为空")
    @Column(name = "ZTMC")
    @TableField("ZTMC")
    private String ztmc;

    /**
     * 是否在校（1：是， 0：否）
     */
    @NotNull(message = "是否在校不能为空")
    @Column(name = "SFZX", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFZX")
    private JudgeMark sfzx;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 是否在籍（1：是， 0：否）
     */
    @NotNull(message = "是否在籍不能为空")
    @Column(name = "SFZJ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFZJ")
    private JudgeMark sfzj;

    /**
     * 是否在读（1：是， 0：否）
     */
    @NotNull(message = "是否在读不能为空")
    @Column(name = "SFZD", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFZD")
    private JudgeMark sfzd;

    /**
     * 是否毕业（1：是， 0：否）
     */
    @NotNull(message = "是否毕业不能为空")
    @Column(name = "SFBY", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFBY")
    private JudgeMark sfby;

    /**
     * 备注信息
     */
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

}
