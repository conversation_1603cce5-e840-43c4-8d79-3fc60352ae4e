<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormGroupMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, GROUP_NAME, LIST_FLAG, MIN_NUM, MAX_NUM, SORT, DIC_GROUP_ID, PROJECT_ID, YEAR, TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.groupName != null">
                        AND a.GROUP_NAME LIKE concat(concat('%',#{param.groupName), '%')
                    </if>
                    <if test="param.listFlag != null">
                        AND a.LIST_FLAG = #{param.listFlag}
                    </if>
                    <if test="param.minNum != null">
                        AND a.MIN_NUM LIKE concat(concat('%',#{param.minNum), '%')
                    </if>
                    <if test="param.maxNum != null">
                        AND a.MAX_NUM LIKE concat(concat('%',#{param.maxNum), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.dicGroupId != null">
                        AND a.DIC_GROUP_ID LIKE concat(concat('%',#{param.dicGroupId), '%')
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
    </sql>
</mapper>
