package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupService;
import com.sanythadmin.project.form.dto.BaseFormApplicationInfoDTO;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.param.*;
import com.sanythadmin.project.form.service.*;
import com.sanythadmin.project.form.vo.FormProjectVO;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 表单管理模块/表单申请管理控制器
 *
 * <AUTHOR>
 * @since 2024-07-02 10:38:48
 */
@RestController
@RequestMapping("/api/form/apply")
public class FormApplyController extends BaseController {
    @Resource
    private FormProjectService projectService;
    @Resource
    private FormApplicationInfoService applicationInfoService;
    @Resource
    private FormApplicationListInfoService applicationListInfoService;
    @Resource
    private FormGroupService groupService;
    @Resource
    private FormTemplateFieldService templateFieldService;
    @Resource
    private DictionaryGroupService dictionaryGroupService;
    @Resource
    private DictionaryFieldService dictionaryFieldService;
    @Resource
    private WorkflowService workflowService;

    /**
     * 分页查询表单项目列表（权限标识：form:apply:list）
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('form:apply:list')")
    @GetMapping("/{type}/pageProjectInfo")
    public PageResult<FormProjectVO> pageProjectInfo(FormProjectParam param) {
        return projectService.pageByApplicant(param);
    }

    /**
     * 分页查询表单申请记录（权限标识：form:apply:list）
     */
    @PreAuthorize("hasAuthority('form:apply:list')")
    @GetMapping("/{type}/pageApplicationInfo")
    public PageResult<BaseFormApplicationInfoDTO> pageApplicationInfo(FormApplicationInfoParam param) {
        return applicationInfoService.pageByApplicant(param);
    }

    /**
     * 根据项目ID或申请记录ID查询表单申请信息（权限标识：form:apply:list）
     */
    @PreAuthorize("hasAuthority('form:apply:list')")
    @GetMapping("/applicationInfo")
    public FormApplicationInfo applicationInfo(@RequestParam(name = "projectId", required = false) String projectId
            , @RequestParam(name = "applicationId", required = false) String applicationId) {
        if (!StringUtils.hasText(projectId) && !StringUtils.hasText(applicationId))
            AssertUtil.throwMessage("项目ID或申请记录ID不能为空");
        FormApplicationInfoParam param = new FormApplicationInfoParam();
        param.setXgh(SecurityUtil.getAccount().getUsername());
        if (StringUtils.hasText(applicationId)) {
            param.setId(applicationId);
        } else {
            FormProject project = projectService.getById(projectId);
            param.setProjectId(projectId);
            param.setYear(project.getYear());
        }

        List<FormApplicationInfo> list = applicationInfoService.list(param);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }


    /**
     * 申请表单子列表信息
     *
     * @param info
     * @return
     */
    @PreAuthorize("hasAuthority('form:apply:list')")
    @GetMapping("/applicationListInfos")
    public List<FormApplicationListInfo> applicationListInfos(FormApplicationListInfoParam info) {
        return applicationListInfoService.list(info);
    }

    /**
     * 根据项目ID查询项目信息（权限标识：form:apply:list）
     */
    @PreAuthorize("hasAuthority('form:apply:list')")
    @GetMapping("/{id}/projectInfo")
    public FormProject projectInfo(@PathVariable("id") String id) {
        return projectService.getById(id);
    }

    /**
     * 根据项目ID或申请记录ID查询申请表单信息组
     *
     * @param projectId
     * @param applicationId
     * @return
     */
    @GetMapping("/fieldGroupList")
    public List<FormGroup> getGroupList(@RequestParam(name = "projectId", required = false) String projectId
            , @RequestParam(name = "applicationId", required = false) String applicationId) {
        return groupService.list(projectId, applicationId);
    }

    /**
     * 根据申请记录ID或模板信息查询申请表单字段
     *
     * @param applicationId
     * @param fieldParam
     * @return
     */
    @GetMapping("/fieldList")
    public List<FormTemplateField> getFieldList(@RequestParam(name = "applicationId", required = false) String applicationId
            , FormTemplateFieldParam fieldParam) {
//        fieldParam.setShowFlag(JudgeMark.YES);
        return templateFieldService.list(fieldParam, applicationId);
    }

    /**
     * 根据项目ID或申请记录ID查询用户字典列表信息组
     *
     * @param projectId
     * @param applicationId
     * @return
     */
    @GetMapping("/dicListGroup")
    public List<DictionaryGroup> dicListGroup(@RequestParam(name = "projectId", required = false) String projectId
            , @RequestParam(name = "applicationId", required = false) String applicationId) {
        List<FormGroup> list = getFormGroups(projectId, applicationId);
        if (CollectionUtils.isEmpty(list)) return null;
        String[] dicGroupIds = list.stream().map(FormGroup::getDicGroupId).toArray(String[]::new);
        DictionaryGroupParam groupParam = new DictionaryGroupParam();
        groupParam.setId(CommonUtil.strJoin(Arrays.stream(dicGroupIds).toList()));
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>(groupParam);
        return dictionaryGroupService.list(page.getOrderWrapper());
    }

    private List<FormGroup> getFormGroups(String projectId, String applicationId) {
        FormGroupParam param = new FormGroupParam();
        param.setDicGroupIdIsNull(false);
        param.setListFlag(JudgeMark.YES);
        if (StringUtils.hasText(applicationId)) {
            FormApplicationInfo info = applicationInfoService.getById(applicationId);
            param.setProjectId(projectId);
            param.setYear(info.getYear());
        } else {
            FormProject project = projectService.getById(projectId);
            param.setYear(project.getYear());
        }
        return groupService.list(param);
    }

    /**
     * 根据项目ID或申请记录ID查询用户字典列表组字段
     *
     * @param projectId
     * @param applicationId
     * @param dicGroupId
     * @return
     */
    @GetMapping("/dicListGroupField")
    public List<DictionaryField> dictionaryFieldList(@RequestParam(name = "projectId", required = false) String projectId
            , @RequestParam(name = "applicationId", required = false) String applicationId
            , @RequestParam(required = false, name = "dicGroupId") String dicGroupId) {
        String dicGroupIds = dicGroupId;
        if (!StringUtils.hasText(dicGroupIds)) {
            List<FormGroup> groups = getFormGroups(projectId, applicationId);
            if (CollectionUtils.isEmpty(groups)) return null;
            dicGroupIds = CommonUtil.strJoin(groups.stream().map(FormGroup::getDicGroupId).toList());
        }
        DictionaryFieldParam fieldParam = new DictionaryFieldParam();
        fieldParam.setGroupId(dicGroupIds);
        PageParam<DictionaryField, DictionaryFieldParam> page = new PageParam<>(fieldParam);
        return dictionaryFieldService.list(page.getOrderWrapper());
    }


    /**
     * 添加或修改表单申请信息（权限标识：form:apply:operation）
     */
    @PreAuthorize("hasAuthority('form:apply:operation')")
    @OperationLog(module = "表单申请记录", comments = "保存表单申请记录")
    @PostMapping("/operation")
    public void applicationInfoOperation(HttpServletRequest request) {
        applicationInfoService.edit(request, true);
    }

    /**
     * 表单列表组信息添加或修改（权限标识：form:apply:operation）
     */
    @PreAuthorize("hasAuthority('form:apply:operation')")
    @OperationLog(module = "表单申请记录", comments = "表单列表记录维护")
    @PostMapping("/listInfoOperation")
    public void listInfoOperation(@RequestBody FormApplicationListInfo listInfo) {
        SysAccount account = SecurityUtil.getAccount();
        listInfo.setXgh(account.getUsername());
        applicationListInfoService.operation(listInfo, true);
    }

    /**
     * 批量删除表单申请记录（权限标识：form:apply:remove）
     */
    @PreAuthorize("hasAuthority('form:apply:remove')")
    @OperationLog(module = "表单申请记录", comments = "批量删除表单申请记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        applicationInfoService.deleteByApplicant(ids.toArray(new String[]{}));
    }

    /**
     * 批量删除表单列表信息记录（权限标识：form:apply:remove）
     */
    @PreAuthorize("hasAuthority('form:apply:remove')")
    @OperationLog(module = "表单申请记录", comments = "批量删除表单列表信息记录")
    @PostMapping("/listInfoRemove")
    public void listInfoRemove(@RequestBody List<String> ids) {
        applicationListInfoService.delete(ids, true);
    }

    /**
     * 根据项目ID查询工作流
     */
    @GetMapping("/{id}/workflow")
    public Workflow workflow(@PathVariable("id") String id) {
        FormProject project = projectService.getById(id);
        return workflowService.get(project.getId(), project.getYear());
    }


    /**
     * 根据申请记录ID查询审核记录
     */
    @GetMapping("/{id}/workflowApprovalNodes")
    public List<ApprovalNodeInfo<FormApprovalNode, FormApprovalNodeRecord>> getApprovalNodes(@PathVariable("id") String id) {
        return workflowService.approvalNodeInfo(FormApprovalNode.class, FormApprovalNodeRecord.class, id, null);
    }
}
