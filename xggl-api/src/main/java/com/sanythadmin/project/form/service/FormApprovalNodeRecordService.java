package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeRecordParam;

import java.util.List;

/**
 * 审批记录Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface FormApprovalNodeRecordService extends IService<FormApprovalNodeRecord> {

    public List<FormApprovalNodeRecord> list(WorkflowApprovalNodeRecordParam param);

    /**
     * 根据项目及审核人信息查询节点审核记录
     *
     * @param applicationId 申请ID
     * @param projectId     项目ID
     * @param year          年份
     * @param username      审核人用户名
     * @param roleId        审核人角色ID
     * @return
     */
//    public List<FormApprovalNodeRecord> getApprovalNodeRecordByUser(String applicationId, String projectId
//            , String year, String username, String roleId);

    /**
     * 根据项目及审核人信息查询上一节点审核记录
     *
     * @param applicationId 申请ID
     * @param projectId     项目ID
     * @param year          年份
     * @param username      审核人用户名
     * @param roleId        审核人角色ID
     * @return
     */
    public List<FormApprovalNodeRecord> getPreNodeApprovalRecordByUser(String applicationId, String projectId
            , String year, String username, String roleId);
}
