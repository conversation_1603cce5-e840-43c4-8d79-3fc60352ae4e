package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.form.entity.FormApprovalNode;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;

import java.util.List;

/**
 * 申请审批节点Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface FormApprovalNodeService extends IService<FormApprovalNode> {
    public List<FormApprovalNode> list(WorkflowApprovalNodeParam param);
}
