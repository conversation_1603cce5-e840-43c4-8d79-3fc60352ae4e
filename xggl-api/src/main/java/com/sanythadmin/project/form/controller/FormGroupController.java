package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.entity.FormTemplateField;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;
import com.sanythadmin.project.form.service.FormGroupService;
import com.sanythadmin.project.form.entity.FormGroup;
import com.sanythadmin.project.form.param.FormGroupParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.project.form.service.FormTemplateFieldService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 表单管理模块/表单信息组控制器
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@RestController
@RequestMapping("/api/form/form-group")
public class FormGroupController extends BaseController {
    @Resource
    private FormGroupService formGroupService;
    @Resource
    private FormTemplateFieldService templateFieldService;

    /**
     * 分页查询表单信息组（权限标识：form:formGroup:list）
     */
    @PreAuthorize("hasAuthority('form:formGroup:list')")
    @GetMapping("/page")
    public PageResult<FormGroup> page(FormGroupParam param) {
        return formGroupService.page(param);
    }

    /**
     * 查询全部表单信息组（权限标识：form:formGroup:list）
     */
    @PreAuthorize("hasAuthority('form:formGroup:list')")
    @GetMapping()
    public List<FormGroup> list(FormGroupParam param) {
        return formGroupService.list(param);
    }

    /**
     * 根据id查询表单信息组（权限标识：form:formGroup:list）
     */
    @PreAuthorize("hasAuthority('form:formGroup:list')")
    @GetMapping("/{id}")
    public FormGroup get(@PathVariable("id") String id) {
        FormGroup group = formGroupService.getById(id);
        setTemplateField(group);
        return group;
    }

    private void setTemplateField(FormGroup group) {
        if (group != null) {
            FormTemplateFieldParam param = new FormTemplateFieldParam();
            param.setGroupId(group.getId());
            param.setProjectId(group.getProjectId());
            param.setType(group.getType());
            List<FormTemplateField> fields = templateFieldService.list(param);
            group.setFormTemplateFields(fields);
        }
    }

    /**
     * 添加或修改表单信息组（权限标识：form:formGroup:operation）
     */
    @PreAuthorize("hasAuthority('form:formGroup:operation')")
    @OperationLog(module = "表单信息组", comments = "保存表单信息组")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody FormGroup formGroup) {
        formGroupService.edit(formGroup);
    }

    /**
     * 添加或修改表单信息组（权限标识：form:formGroup:operation）
     */
    @PreAuthorize("hasAuthority('form:formGroup:operation')")
    @OperationLog(module = "表单信息组", comments = "保存表单基础信息")
    @PostMapping("/editBaseInfo/{projectId}")
    public void editBaseInfo(@PathVariable("projectId") String projectId, @RequestBody List<FormGroup> formGroup) {
        formGroupService.editBaseInfo(projectId, formGroup);
    }

    /**
     * 批量删除表单信息组（权限标识：form:formGroup:remove）
     */
    @PreAuthorize("hasAuthority('form:formGroup:remove')")
    @OperationLog(module = "表单信息组", comments = "批量删除表单信息组")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formGroupService.removeByIds(ids);
    }
}
