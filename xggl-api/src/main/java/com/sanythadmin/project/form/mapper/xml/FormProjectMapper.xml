<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormProjectMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XMMC, SQKSSJ, SQJZSJ, SFXSZC, KSQTJ, XSSQBXZAN, XSHZBXZAN, SFYJCK, QYSQYZM, SQBDCGS, FYXMSQXX, QZXM, HCXM, XMJJ, YDXZ, TYPE, SFKQDW, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xmmc != null">
                        AND a.XMMC LIKE concat(concat('%',#{param.xmmc), '%')
                    </if>
                    <if test="param.sqkssj != null">
                        AND a.SQKSSJ LIKE concat(concat('%',#{param.sqkssj), '%')
                    </if>
                    <if test="param.sqjzsj != null">
                        AND a.SQJZSJ LIKE concat(concat('%',#{param.sqjzsj), '%')
                    </if>
                    <if test="param.sfxszc != null">
                        AND a.SFXSZC = #{param.sfxszc}
                    </if>
                    <if test="param.ksqtj != null">
                        AND a.KSQTJ = #{param.ksqtj}
                    </if>
                    <if test="param.xssqbxzan != null">
                        AND a.XSSQBXZAN = #{param.xssqbxzan}
                    </if>
                    <if test="param.xshzbxzan != null">
                        AND a.XSHZBXZAN = #{param.xshzbxzan}
                    </if>
                    <if test="param.sfyjck != null">
                        AND a.SFYJCK = #{param.sfyjck}
                    </if>
                    <if test="param.qysqyzm != null">
                        AND a.QYSQYZM = #{param.qysqyzm}
                    </if>
                    <if test="param.sqbdcgs != null">
                        AND a.SQBDCGS LIKE concat(concat('%',#{param.sqbdcgs), '%')
                    </if>
                    <if test="param.fyxmsqxx != null">
                        AND a.FYXMSQXX LIKE concat(concat('%',#{param.fyxmsqxx), '%')
                    </if>
                    <if test="param.qzxm != null">
                        AND a.QZXM LIKE concat(concat('%',#{param.qzxm), '%')
                    </if>
                    <if test="param.hcxm != null">
                        AND a.HCXM LIKE concat(concat('%',#{param.hcxm), '%')
                    </if>
                    <if test="param.xmjj != null">
                        AND a.XMJJ LIKE concat(concat('%',#{param.xmjj), '%')
                    </if>
                    <if test="param.ydxz != null">
                        AND a.YDXZ LIKE concat(concat('%',#{param.ydxz), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.sfkqdw != null">
                        AND a.SFKQDW = #{param.sfkqdw}
                    </if>
    </sql>
</mapper>
