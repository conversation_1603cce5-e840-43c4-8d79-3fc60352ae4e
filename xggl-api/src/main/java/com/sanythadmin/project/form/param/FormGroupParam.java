package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.form.enums.InfoType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 表单信息组查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormGroupParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 组名称
     */
    @QueryField(type = QueryType.LIKE)
    private String groupName;

    /**
     * 是否列表（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark listFlag;

    /**
     * 引用字典组ID
     */
    @QueryField(type = QueryType.EQ)
    private String dicGroupId;

    /**
     * 表单项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 信息类型
     */
    @QueryField(type = QueryType.IN_LIST)
    private List<InfoType> infoType;

    @QueryField(ignore = true)
    private Boolean dicGroupIdIsNull;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
