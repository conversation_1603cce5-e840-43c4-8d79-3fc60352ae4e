package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.form.entity.FormCustomField;
import com.sanythadmin.project.form.enums.CustomType;
import com.sanythadmin.project.form.param.FormCustomFieldParam;

import java.util.List;

/**
 * 审核列表自定义列/自定义查询条件字段Service
 *
 * <AUTHOR>
 * @since 2024-09-04 15:23:57
 */
public interface FormCustomFieldService extends IService<FormCustomField> {
    public void remove(String projectId, CustomType customType);
    public List<FormCustomField> list(FormCustomFieldParam param);

}
