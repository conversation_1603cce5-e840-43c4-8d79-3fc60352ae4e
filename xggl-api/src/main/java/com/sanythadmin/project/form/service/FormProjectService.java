package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.param.FormProjectParam;
import com.sanythadmin.project.form.vo.FormProjectVO;

import java.util.List;

/**
 * 表单项目Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:07:26
 */
public interface FormProjectService extends IService<FormProject> {
    public void edit(FormProject param);
    public List<FormProject> list(FormProjectParam param);
    public PageResult<FormProject> page(FormProjectParam param);

    /**
     * 根据申请人信息查询项目列表
     * @param param
     * @return
     */
    public PageResult<FormProjectVO> pageByApplicant(FormProjectParam param);

    /**
     * 根据审核人信息查询项目列表
     * @param param
     * @return
     */
    public PageResult<FormProject> pageByReviewer(FormProjectParam param);
}
