package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.form.mapper.FormPublicityFeedbackMapper;
import com.sanythadmin.project.form.service.FormPublicityFeedbackService;
import com.sanythadmin.project.form.entity.FormPublicityFeedback;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 公示意见反馈Service实现
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Service
public class FormPublicityFeedbackServiceImpl extends ServiceImpl<FormPublicityFeedbackMapper, FormPublicityFeedback> implements FormPublicityFeedbackService {

    @Resource
    private FormPublicityFeedbackMapper mapper;

}
