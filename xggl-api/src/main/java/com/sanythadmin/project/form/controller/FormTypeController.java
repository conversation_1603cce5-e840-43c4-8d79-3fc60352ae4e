package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.service.FormTypeService;
import com.sanythadmin.project.form.entity.FormType;
import com.sanythadmin.project.form.param.FormTypeParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 表单管理模块/通用表单业务类型控制器
 *
 * <AUTHOR>
 * @since 2024-07-01 16:53:19
 */
@RestController
@RequestMapping("/api/form/form-type")
public class FormTypeController extends BaseController {
    @Resource
    private FormTypeService formTypeService;

    /**
     * 分页查询通用表单业务类型（权限标识：form:formType:list）
     */
    @PreAuthorize("hasAuthority('form:formType:list')")
    @GetMapping("/page")
    public PageResult<FormType> page(FormTypeParam param) {
        PageParam<FormType, FormTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formTypeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部通用表单业务类型（权限标识：form:formType:list）
     */
    @PreAuthorize("hasAuthority('form:formType:list')")
    @GetMapping()
    public List<FormType> list(FormTypeParam param) {
        PageParam<FormType, FormTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formTypeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询通用表单业务类型（权限标识：form:formType:list）
     */
    @PreAuthorize("hasAuthority('form:formType:list')")
    @GetMapping("/{id}")
    public FormType get(@PathVariable("id") String id) {
        return formTypeService.getById(id);
    }

    /**
     * 添加或修改通用表单业务类型（权限标识：form:formType:operation）
     */
    @PreAuthorize("hasAuthority('form:formType:operation')")
    @OperationLog(module = "通用表单业务类型", comments = "保存通用表单业务类型")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody FormType formType) {
        formTypeService.edit(formType);
    }

    /**
     * 批量删除通用表单业务类型（权限标识：form:formType:remove）
     */
    @PreAuthorize("hasAuthority('form:formType:remove')")
    @OperationLog(module = "通用表单业务类型", comments = "批量删除通用表单业务类型")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formTypeService.removeByIds(ids);
    }
}
