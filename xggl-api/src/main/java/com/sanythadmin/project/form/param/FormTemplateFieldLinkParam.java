package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 表单字段关联关系查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormTemplateFieldLinkParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 字段值
     */
    private String fieldVal;

    /**
     * 关联字段是否显示（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark showFlag;

    /**
     * 关联字段数据
     */
    private String linkFieldDataType;

    /**
     * 关联字段加载数据url
     */
    private String linkFieldDataUrl;

    /**
     * 字段ID
     */
    @QueryField(type = QueryType.EQ)
    private String fieldId;

    /**
     * 关联字段英文名
     */
    private String linkField;

    /**
     * 组ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String groupId;

    /**
     * 字段ID
     */
    private String fieldValId;

}
