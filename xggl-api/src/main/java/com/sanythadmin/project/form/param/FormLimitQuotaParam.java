package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 限制名额配置查询参数
 *
 * <AUTHOR>
 * @since 2024-09-25 10:25:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormLimitQuotaParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.EQ)
    private String pyccid;
    @QueryField(type = QueryType.IS_NULL, value = "pyccid")
    private Boolean pyccidIsNull;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.EQ)
    private String njid;
    @QueryField(type = QueryType.IS_NULL, value = "njid")
    private Boolean njidIsNull;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;
    @QueryField(type = QueryType.IS_NULL, value = "xyid")
    private Boolean xyidIsNull;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;
    @QueryField(type = QueryType.IS_NULL, value = "zyid")
    private Boolean zyidIsNull;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;
    @QueryField(type = QueryType.IS_NULL, value = "bjid")
    private Boolean bjidIsNull;
}
