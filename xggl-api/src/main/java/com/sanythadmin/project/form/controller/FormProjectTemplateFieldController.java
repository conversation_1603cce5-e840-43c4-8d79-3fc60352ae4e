package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormProjectTemplateField;
import com.sanythadmin.project.form.param.FormProjectTemplateFieldParam;
import com.sanythadmin.project.form.service.FormProjectTemplateFieldService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表单管理模块/表单项目模板配置控制器
 *
 * <AUTHOR>
 * @since 2024-12-12 10:54:30
 */
@RestController
@RequestMapping("/api/form/form-project-template-field")
public class FormProjectTemplateFieldController extends BaseController {
    @Resource
    private FormProjectTemplateFieldService formProjectTemplateFieldService;

    /**
     * 分页查询表单项目模板配置（权限标识：form:formProjectTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectTemplateField:list')")
    @GetMapping("/page")
    public PageResult<FormProjectTemplateField> page(FormProjectTemplateFieldParam param) {
        PageParam<FormProjectTemplateField, FormProjectTemplateFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formProjectTemplateFieldService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部表单项目模板配置（权限标识：form:formProjectTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectTemplateField:list')")
    @GetMapping()
    public List<FormProjectTemplateField> list(FormProjectTemplateFieldParam param) {
        PageParam<FormProjectTemplateField, FormProjectTemplateFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formProjectTemplateFieldService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询表单项目模板配置（权限标识：form:formProjectTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectTemplateField:list')")
    @GetMapping("/{id}")
    public FormProjectTemplateField get(@PathVariable("id") String id) {
        return formProjectTemplateFieldService.getById(id);
    }

    /**
     * 添加或修改表单项目模板配置（权限标识：form:formProjectTemplateField:operation）
     */
    @PreAuthorize("hasAuthority('form:formProjectTemplateField:operation')")
    @OperationLog(module = "表单项目模板配置", comments = "保存表单项目模板配置")
    @PostMapping("/operation")
    public void save(@RequestBody List<FormProjectTemplateField> list) {
        for (FormProjectTemplateField templateField : list) {
            if (templateField.getSort() == null) {
                SortHelper<FormProjectTemplateField> helper = new SortHelper<>(formProjectTemplateFieldService, "sort");
                templateField.setSort((long) helper.next());
            }

            if (!StringUtils.hasText(templateField.getId())) {
                formProjectTemplateFieldService.save(templateField);
            } else {
                formProjectTemplateFieldService.updateById(templateField);
            }
        }
    }

    /**
     * 批量删除表单项目模板配置（权限标识：form:formProjectTemplateField:remove）
     */
    @PreAuthorize("hasAuthority('form:formProjectTemplateField:remove')")
    @OperationLog(module = "表单项目模板配置", comments = "批量删除表单项目模板配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formProjectTemplateFieldService.removeByIds(ids);
    }

    /**
     * 项目扩展字段
     *
     * @return
     */
//    @GetMapping("/extendFieldList")
    public List<String> extendFieldList() {
        List<String> list = new ArrayList<>();
        Class aClass = FormProject.class;
        String reg = "bz\\d";
        Field[] fields = CommonUtil.getAllFields(aClass);
        Pattern pattern = Pattern.compile(reg);
        for (Field field : fields) {
            Matcher matcher = pattern.matcher(field.getName());
            if (matcher.find()) list.add(matcher.group());
        }
        return list;
    }
}
