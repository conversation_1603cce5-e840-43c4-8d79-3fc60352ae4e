package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormApplicationListInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.mapper.FormApplicationInfoMapper;
import com.sanythadmin.project.form.mapper.FormApplicationListInfoMapper;
import com.sanythadmin.project.form.mapper.FormProjectMapper;
import com.sanythadmin.project.form.param.FormApplicationListInfoParam;
import com.sanythadmin.project.form.service.FormApplicationInfoService;
import com.sanythadmin.project.form.service.FormApplicationListInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 表单列表申请记录Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Service
public class FormApplicationListInfoServiceImpl extends ServiceImpl<FormApplicationListInfoMapper, FormApplicationListInfo> implements FormApplicationListInfoService {
    @Resource
    private FormProjectMapper formProjectMapper;
    @Resource
    private FormApplicationInfoMapper applicationInfoMapper;
    @Resource
    private FormApplicationInfoService applicationInfoService;

    @Override
    public void operation(FormApplicationListInfo listInfo, boolean apply) {
        if (!StringUtils.hasText(listInfo.getProjectId()) || !StringUtils.hasText(listInfo.getXgh())
                || !StringUtils.hasText(listInfo.getGroupId()))
            AssertUtil.throwMessage("缺失必须参数");
        FormProject project = formProjectMapper.selectById(listInfo.getProjectId());
        String error = applicationInfoService.checkApplyTime(project);
        if (StringUtils.hasText(error))
            AssertUtil.throwMessage(error);
        listInfo.setType(project.getType());
        listInfo.setYear(project.getYear());
        if (StringUtils.hasText(listInfo.getApplicationId())) {
            List<FormApplicationInfo> infos = applicationInfoMapper.selectList(new LambdaQueryWrapper<FormApplicationInfo>()
                    .eq(FormApplicationInfo::getXgh, listInfo.getXgh())
                    .eq(FormApplicationInfo::getProjectId, listInfo.getProjectId())
                    .eq(FormApplicationInfo::getYear, listInfo.getYear()));
            listInfo.setApplicationId(infos.get(0).getId());
        }
        if (StringUtils.hasText(listInfo.getId())) {
            applicationInfoService.checkIsEdit(listInfo.getApplicationId());
            baseMapper.updateById(listInfo);
        } else {
            baseMapper.insert(listInfo);
        }
    }

    @Override
    public void delete(List<String> ids, boolean apply) {
        FormApplicationListInfoParam listInfoParam = new FormApplicationListInfoParam();
        listInfoParam.setId(CommonUtil.strJoin(ids));
        if (apply) listInfoParam.setXgh(SecurityUtil.getAccount().getUsername());
        PageParam<FormApplicationListInfo, FormApplicationListInfoParam> page = new PageParam<>(listInfoParam);
        List<FormApplicationListInfo> list = list(page.getOrderWrapper());
        if (!CollectionUtils.isEmpty(list)) {
            FormApplicationListInfo listInfo = list.get(0);
            if (apply && StringUtils.hasText(listInfo.getApplicationId())) {
                FormProject project = formProjectMapper.selectById(listInfo.getProjectId());
                applicationInfoService.checkApplyTime(project);
                applicationInfoService.checkIsEdit(listInfo.getApplicationId());
            }
            List<String> strings = list.stream().map(FormApplicationListInfo::getId).collect(Collectors.toList());
            baseMapper.deleteBatchIds(strings);
        }
    }

    @Override
    public List<FormApplicationListInfo> list(FormApplicationListInfoParam param) {
        if (!StringUtils.hasText(param.getApplicationId())) {
            FormProject project = formProjectMapper.selectById(param.getProjectId());
            param.setYear(project.getYear());
        }
        PageParam<FormApplicationListInfo, FormApplicationListInfoParam> page = new PageParam<>(param);
        return baseMapper.selectListWithPermission(page.getOrderWrapper());
    }
}
