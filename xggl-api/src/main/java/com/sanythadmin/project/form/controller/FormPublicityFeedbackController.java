package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.service.FormPublicityFeedbackService;
import com.sanythadmin.project.form.entity.FormPublicityFeedback;
import com.sanythadmin.project.form.param.FormPublicityFeedbackParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 公示意见反馈控制器
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@RestController
@RequestMapping("/api/form/form-publicity-feedback")
public class FormPublicityFeedbackController extends BaseController {
    @Resource
    private FormPublicityFeedbackService formPublicityFeedbackService;

    /**
     * 分页查询公示意见反馈（权限标识：form:formPublicityFeedback:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityFeedback:list')")
    @GetMapping("/page")
    public PageResult<FormPublicityFeedback> page(FormPublicityFeedbackParam param) {
        PageParam<FormPublicityFeedback, FormPublicityFeedbackParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formPublicityFeedbackService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部公示意见反馈（权限标识：form:formPublicityFeedback:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityFeedback:list')")
    @GetMapping()
    public List<FormPublicityFeedback> list(FormPublicityFeedbackParam param) {
        PageParam<FormPublicityFeedback, FormPublicityFeedbackParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formPublicityFeedbackService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询公示意见反馈（权限标识：form:formPublicityFeedback:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityFeedback:list')")
    @GetMapping("/{id}")
    public FormPublicityFeedback get(@PathVariable("id") String id) {
        return formPublicityFeedbackService.getById(id);
    }

    /**
     * 添加或修改公示意见反馈（权限标识：form:formPublicityFeedback:operation）
     */
    @PreAuthorize("hasAuthority('form:formPublicityFeedback:operation')")
    @OperationLog(module = "公示意见反馈", comments = "保存公示意见反馈")
    @PostMapping("/operation")
    public void save(@RequestBody FormPublicityFeedback formPublicityFeedback) {
        if (StringUtils.hasLength(formPublicityFeedback.getId())) {
            formPublicityFeedbackService.updateById(formPublicityFeedback);
        } else {
            formPublicityFeedbackService.save(formPublicityFeedback);
        }
    }

    /**
     * 批量删除公示意见反馈（权限标识：form:formPublicityFeedback:remove）
     */
    @PreAuthorize("hasAuthority('form:formPublicityFeedback:remove')")
    @OperationLog(module = "公示意见反馈", comments = "批量删除公示意见反馈")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formPublicityFeedbackService.removeByIds(ids);
    }
}
