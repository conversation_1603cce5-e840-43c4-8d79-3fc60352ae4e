package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 表单项目
 *
 * <AUTHOR>
 * @since 2024-07-02 10:07:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PROJECT")
@Entity
@Table(name = "SYT_FORM_PROJECT")
public class FormProject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 项目名称
     */
    @Column(name = "XMMC")
    @TableField("XMMC")
    private String xmmc;

    /**
     * 申请开始时间
     */
    @Column(name = "SQKSSJ")
    @TableField("SQKSSJ")
    private LocalDate sqkssj;

    /**
     * 申请截至时间
     */
    @Column(name = "SQJZSJ")
    @TableField("SQJZSJ")
    private LocalDate sqjzsj;

    /**
     * 是否显示暂存（1：是，0：否）
     */
    @Column(name = "SFXSZC", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXSZC")
    private JudgeMark sfxszc;

    /**
     * 可申请条数
     */
    @Column(name = "KSQTS", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("KSQTS")
    private Integer ksqts;

    /**
     * 每日申请条数
     */
    @Column(name = "MRKSQTS", columnDefinition = ColumnType.NUMBER_1_DEFAULT_1)
    @TableField("MRKSQTS")
    private Integer mrksqts;

    /**
     * 显示申请表下载按钮（1：是，0：否）
     */
    @Column(name = "XSSQBXZAN", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XSSQBXZAN")
    private JudgeMark xssqbxzan;

    /**
     * 显示汇总表下载按钮（1：是，0：否）
     */
    @Column(name = "XSHZBXZAN", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XSHZBXZAN")
    private JudgeMark xshzbxzan;

    /**
     * 是否越级查看（1：是，0：否）
     */
    @Column(name = "SFYJCK", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFYJCK")
    private JudgeMark sfyjck;

    /**
     * 启用申请验证码（1：是，0：否）
     */
    @Column(name = "QYSQYZM", columnDefinition = ColumnType.NUMBER_1)
    @TableField("QYSQYZM")
    private JudgeMark qysqyzm;

    /**
     * 是否可修改申请信息（1：是，0：否）
     */
    @Column(name = "SFKXGSQXX", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFKXGSQXX")
    private JudgeMark sfkxgsqxx;

    /**
     * 是否集体项目
     */
    @Column(name = "SFJTXM", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFJTXM")
    private JudgeMark sfjtxm;

    /**
     * 申请表导出格式
     */
    @Column(name = "SQBDCGS")
    @TableField("SQBDCGS")
    private String sqbdcgs;

    /**
     * 复用项目申请信息（关联项目ID）
     */
    @Column(name = "FYXMSQXX")
    @TableField("FYXMSQXX")
    private String fyxmsqxx;

    /**
     * 前置项目ID
     */
    @Column(name = "QZXMID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("QZXMID")
    private String qzxmid;

    /**
     * 前置项目关系(并/或)
     */
    @Column(name = "QZXMGX")
    @TableField("QZXMGX")
    private String qzxmgx;

    /**
     * 前置项目项目数
     */
    @Column(name = "QZXMS")
    @TableField("QZXMS")
    private Integer qzxms;

    /**
     * 强制前置项目ID
     */
    @Column(name = "QZQZXMID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("QZQZXMID")
    private String qzqzxmid;

    /**
     * 互斥项目
     */
    @Column(name = "HCXMID", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("HCXMID")
    private String hcxmid;

    /**
     * 项目简介
     */
    @Column(name = "XMJJ", columnDefinition = ColumnType.CLOB)
    @TableField("XMJJ")
    private String xmjj;

    /**
     * 阅读须知
     */
    @Column(name = "YDXZ", columnDefinition = ColumnType.CLOB)
    @TableField("YDXZ")
    private String ydxz;

    /**
     * 业务类型
     */
    @Column(name = "TYPE",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    /**
     * 是否开启定位（1：是，0：否）
     */
    @Column(name = "SFKQDW", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFKQDW")
    private JudgeMark sfkqdw;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 操作人
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 操作人姓名
     */
    @Column(name = "CZRXM")
    @TableField("CZRXM")
    private String czrxm;

    /**
     * 用户类型
     */
    @Column(name = "user_type")
    @TableField("user_type")
    private UserType userType;

    /**
     * 是否生成模板（1：是，0：否）
     */
    @Column(name = "SFSCMB", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFSCMB")
    private JudgeMark sfscmb;

    /**
     * 是否显示（1：是，0：否）
     */
    @Column(name = "SFXS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXS")
    private JudgeMark sfxs;

    /**
     * 审核列表是否按学籍异动前查询
     */
    @Column(name = "SFAXJYDQCX", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFAXJYDQCX")
    private JudgeMark sfaxjydqcx;

    /**
     * 审批状态
     */
    @Column(name = "SPZT")
    @TableField("SPZT")
    private String spzt;

    /**
     * 是否毕业生（1：是，0：否）
     */
    @Column(name = "SFBYS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFBYS")
    private JudgeMark sfbys;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 扩展字段
     @Column(name = "BZ1")
     @TableField("BZ1") private String bz1;
     @Column(name = "BZ2")
     @TableField("BZ2") private String bz2;
     @Column(name = "BZ3")
     @TableField("BZ3") private String bz3;
     @Column(name = "BZ4")
     @TableField("BZ4") private String bz4;
     @Column(name = "BZ5")
     @TableField("BZ5") private String bz5;
     @Column(name = "BZ6")
     @TableField("BZ6") private String bz6;
     @Column(name = "BZ7")
     @TableField("BZ7") private String bz7;
     @Column(name = "BZ8")
     @TableField("BZ8") private String bz8;
     @Column(name = "BZ9")
     @TableField("BZ9") private String bz9;
     @Column(name = "BZ10")
     @TableField("BZ10") private String bz10;
     */
    /**
     * 申请时间是否合法
     *
     * @return
     */
    public boolean applyTimeIsCorrect() {
        return DateUtil.localDateIsCorrect(sqkssj, sqjzsj);
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
