package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 * 表单限制条件
 *
 * <AUTHOR>
 * @since 2024-09-06 15:54:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_RESTRICT")
@Entity
@Table(name = "SYT_FORM_RESTRICT")
public class FormRestrict implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 限制条件别名
     */
    @Column(name = "CONDITION_KEY")
    @TableField("CONDITION_KEY")
    private String conditionKey;
    /**
     * 限制条件名称
     */
    @Column(name = "CONDITION_NAME")
    @TableField("CONDITION_NAME")
    private String conditionName;

    /**
     * 模块
     */
    @Column(name = "MODULE")
    @TableField("MODULE")
    private String module;

    /**
     * 条件
     */
    @Column(name = "CONDITION", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField(value = "CONDITION", jdbcType = JdbcType.VARCHAR)
    private String condition;

    /**
     * 限制条件说明
     */
    @Column(name = "DESCRIPTION")
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 限制条件值
     */
    @Column(name = "CONDITION_VALUE", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CONDITION_VALUE")
    private String conditionValue;

    /**
     * 对应条件库条件ID
     */
    @Column(name = "CONDITION_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CONDITION_ID")
    private String conditionId;
}
