package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.form.enums.ApplyState;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 表单项目查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:07:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormProjectParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    @QueryField(type = QueryType.NOT_IN_STR, value = "id")
    private String idNotIn;

    @QueryField(type = QueryType.EQ)
    private String year;
    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 申请开始时间
     */
    private String sqkssj;

    /**
     * 申请截至时间
     */
    private String sqjzsj;

    /**
     * 是否显示暂存（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxszc;

    /**
     * 可申请条数
     */
    @QueryField(type = QueryType.EQ)
    private Integer ksqtj;

    /**
     * 显示申请表下载按钮（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark xssqbxzan;

    /**
     * 显示汇总表下载按钮（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark xshzbxzan;

    /**
     * 是否越级查看（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfyjck;

    /**
     * 启用申请验证码（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark qysqyzm;

    /**
     * 申请表导出格式
     */
    private String sqbdcgs;

    /**
     * 复用项目申请信息（关联项目ID）
     */
    private String fyxmsqxx;

    /**
     * 前置项目
     */
    private String qzxmid;

    /**
     * 互斥项目
     */
    private String hcxmid;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 是否开启定位（1：是，0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfkqdw;

    /**
     * 用户类型
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;

    /**
     * 是否生成模板
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfscmb;

    /**
     * 是否显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxs;

    /**
     * 审批状态
     */
    @QueryField(type = QueryType.EQ)
    private String spzt;

    /**
     * 是否毕业生
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfbys;

    /**
     * 申请状态
     */
    @QueryField(ignore = true)
    private ApplyState sqzt;
    /**
     * 操作人
     */
    @QueryField(type = QueryType.EQ)
    private String czr;

    @Override
    public String getSort() {
        return "sort desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
