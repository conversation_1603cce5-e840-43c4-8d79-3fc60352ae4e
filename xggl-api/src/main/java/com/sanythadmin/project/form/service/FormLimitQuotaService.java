package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormLimitQuota;
import com.sanythadmin.project.form.entity.FormProject;

/**
 * 限制名额配置Service
 *
 * <AUTHOR>
 * @since 2024-09-25 10:25:42
 */
public interface FormLimitQuotaService extends IService<FormLimitQuota> {

    public void checkQuota(FormProject project, UserInfo userInfo);

}
