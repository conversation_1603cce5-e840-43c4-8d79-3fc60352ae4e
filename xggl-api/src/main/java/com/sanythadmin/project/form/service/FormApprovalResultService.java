package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormApprovalResult;
import com.sanythadmin.project.form.param.FormApprovalResultParam;

import java.util.List;

/**
 * 审批结果记录Service
 *
 * <AUTHOR>
 * @since 2024-08-13 11:41:42
 */
public interface FormApprovalResultService extends IService<FormApprovalResult> {

    PageResult<FormApprovalResult> page(FormApprovalResultParam param);

    List<FormApprovalResult> list(FormApprovalResultParam param);

}
