package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormTemplateField;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;

import java.util.List;

/**
 * 表单模板配置Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
public interface FormTemplateFieldService extends IService<FormTemplateField> {
    public void edit(List<FormTemplateField> list);
    public PageResult<FormTemplateField> page(FormTemplateFieldParam param);
    public List<FormTemplateField> list(FormTemplateFieldParam param);
    public List<FormTemplateField> list(FormTemplateFieldParam param, String applicationId);
}
