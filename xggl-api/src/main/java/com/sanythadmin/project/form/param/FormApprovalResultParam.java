package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 审批结果记录查询参数
 *
 * <AUTHOR>
 * @since 2024-08-13 11:41:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormApprovalResultParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 关联表单申请ID
     */
    private String applicationId;

    /**
     * 创建时间
     */
    private String createTime;

    private String bz1;

    private String bz2;

    private String bz3;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 姓名
     */
    @QueryField(type = QueryType.LIKE)
    private String xm;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.EQ)
    private String pyccid;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.EQ)
    private String njid;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Gender xb;

    /**
     * 项目名称
     */
    @QueryField(type = QueryType.LIKE)
    private String xmmc;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 校区名称
     */
    @QueryField(type = QueryType.LIKE)
    private String xqmc;

    /**
     * 年份
     */
    @QueryField(type = QueryType.LIKE)
    private String year;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 用户类别
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;
    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
