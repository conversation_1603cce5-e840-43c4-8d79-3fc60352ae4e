<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormApprovalResultMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APPLICATION_ID, CREATE_TIME, BZ1, BZ2, BZ3, XGH, XM, XYID, ZYID, BJID, NJID, XB, XMMC, TYPE, XQMC, YEAR, PROJECT_ID, USER_TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.applicationId != null">
                        AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.bz1 != null">
                        AND a.BZ1 LIKE concat(concat('%',#{param.bz1), '%')
                    </if>
                    <if test="param.bz2 != null">
                        AND a.BZ2 LIKE concat(concat('%',#{param.bz2), '%')
                    </if>
                    <if test="param.bz3 != null">
                        AND a.BZ3 LIKE concat(concat('%',#{param.bz3), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
                    <if test="param.xb != null">
                        AND a.XB = #{param.xb}
                    </if>
                    <if test="param.xmmc != null">
                        AND a.XMMC LIKE concat(concat('%',#{param.xmmc), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.xqmc != null">
                        AND a.XQMC LIKE concat(concat('%',#{param.xqmc), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.userType != null">
                        AND a.USER_TYPE = #{param.userType}
                    </if>
    </sql>
</mapper>
