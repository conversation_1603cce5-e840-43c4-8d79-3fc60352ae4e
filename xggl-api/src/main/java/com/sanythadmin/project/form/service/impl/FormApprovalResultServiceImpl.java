package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormApprovalResult;
import com.sanythadmin.project.form.mapper.FormApprovalResultMapper;
import com.sanythadmin.project.form.param.FormApprovalResultParam;
import com.sanythadmin.project.form.service.FormApprovalResultService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批结果记录Service实现
 *
 * <AUTHOR>
 * @since 2024-08-13 11:41:42
 */
@Service
public class FormApprovalResultServiceImpl extends ServiceImpl<FormApprovalResultMapper, FormApprovalResult> implements FormApprovalResultService {
    @Resource
    private FormApprovalResultMapper approvalRecordMapper;

    @Override
    public PageResult<FormApprovalResult> page(FormApprovalResultParam param) {
        PageParam<FormApprovalResult, FormApprovalResultParam> pageParam = new PageParam<>(param);
        IPage<FormApprovalResult> page = approvalRecordMapper.selectPageWithPermission(pageParam, pageParam.getWrapper()
                , new MyMPQueryParams(FormApprovalResult.class));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FormApprovalResult> list(FormApprovalResultParam param) {
        PageParam<FormApprovalResult, FormApprovalResultParam> pageParam = new PageParam<>(param);
        return approvalRecordMapper.selectListWithPermission(pageParam.getWrapper()
                , new MyMPQueryParams(FormApprovalResult.class));
    }
}
