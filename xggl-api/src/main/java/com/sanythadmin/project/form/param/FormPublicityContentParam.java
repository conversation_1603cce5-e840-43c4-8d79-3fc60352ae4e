package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 公示内容查询参数
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormPublicityContentParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 表单项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 业务类型
     */
    private String type;

    /**
     * 年份
     */
    private String year;

    /**
     * 学工号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 培养层次
     */
    private String pyccmc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 操作人
     */
    private String username;

    /**
     * 操作人角色标识
     */
    private String roleScope;

    /**
     * 公示项目ID
     */
    private String itemId;
    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
