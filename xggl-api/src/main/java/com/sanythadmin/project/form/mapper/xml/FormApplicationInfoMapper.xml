<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormApplicationInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PROJECT_ID, YEAR, TYPE, XGH, SFZC, CREATE_TIME, C1, C2, C3, C4, C5, C6, C7, LOC<PERSON>ION_LONGITUDE, LOCATION_LATITUDE, LOCATION_ADDRESS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.sfzc != null">
                        AND a.SFZC = #{param.sfzc}
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.c1 != null">
                        AND a.C1 LIKE concat(concat('%',#{param.c1), '%')
                    </if>
                    <if test="param.c2 != null">
                        AND a.C2 LIKE concat(concat('%',#{param.c2), '%')
                    </if>
                    <if test="param.c3 != null">
                        AND a.C3 LIKE concat(concat('%',#{param.c3), '%')
                    </if>
                    <if test="param.c4 != null">
                        AND a.C4 LIKE concat(concat('%',#{param.c4), '%')
                    </if>
                    <if test="param.c5 != null">
                        AND a.C5 LIKE concat(concat('%',#{param.c5), '%')
                    </if>
                    <if test="param.c6 != null">
                        AND a.C6 LIKE concat(concat('%',#{param.c6), '%')
                    </if>
                    <if test="param.c7 != null">
                        AND a.C7 LIKE concat(concat('%',#{param.c7), '%')
                    </if>
                    <if test="param.locationLongitude != null">
                        AND a.LOCATION_LONGITUDE LIKE concat(concat('%',#{param.locationLongitude), '%')
                    </if>
                    <if test="param.locationLatitude != null">
                        AND a.LOCATION_LATITUDE LIKE concat(concat('%',#{param.locationLatitude), '%')
                    </if>
                    <if test="param.locationAddress != null">
                        AND a.LOCATION_ADDRESS LIKE concat(concat('%',#{param.locationAddress), '%')
                    </if>
    </sql>
</mapper>
