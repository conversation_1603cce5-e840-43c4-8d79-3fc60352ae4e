<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormRestrictMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONDITION_NAME, MODULE, CONDITION, DESCRIPTION, PROJECT_ID, YEAR, CONDITION_VALUE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.conditionName != null">
                        AND a.CONDITION_NAME LIKE concat(concat('%',#{param.conditionName), '%')
                    </if>
                    <if test="param.module != null">
                        AND a.MODULE LIKE concat(concat('%',#{param.module), '%')
                    </if>
                    <if test="param.condition != null">
                        AND a.CONDITION LIKE concat(concat('%',#{param.condition), '%')
                    </if>
                    <if test="param.description != null">
                        AND a.DESCRIPTION LIKE concat(concat('%',#{param.description), '%')
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.conditionValue != null">
                        AND a.CONDITION_VALUE LIKE concat(concat('%',#{param.conditionValue), '%')
                    </if>
    </sql>
</mapper>
