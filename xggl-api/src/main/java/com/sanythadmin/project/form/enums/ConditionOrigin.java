package com.sanythadmin.project.form.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * Created by JIANGPING on 2024/7/19.
 */
public enum ConditionOrigin implements ValueTextBaseEnum {
    yhxx(0, "yhxx", "用户信息"),
    sqxx(1, "sqxx", "申请信息");
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private String remark;

    ConditionOrigin(Integer value, String text, String remark) {
        this.value = value;
        this.text = text;
        this.remark = remark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
