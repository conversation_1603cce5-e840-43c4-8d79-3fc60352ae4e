package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.form.entity.FormCustomField;
import com.sanythadmin.project.form.enums.CustomType;
import com.sanythadmin.project.form.mapper.FormCustomFieldMapper;
import com.sanythadmin.project.form.param.FormCustomFieldParam;
import com.sanythadmin.project.form.service.FormCustomFieldService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审核列表自定义列字段Service实现
 *
 * <AUTHOR>
 * @since 2024-09-04 15:23:57
 */
@Service
public class FormCustomFieldServiceImpl extends ServiceImpl<FormCustomFieldMapper, FormCustomField> implements FormCustomFieldService {

    @Override
    public void remove(String projectId, CustomType customType) {
        remove(new LambdaQueryWrapper<FormCustomField>().eq(FormCustomField::getProjectId, projectId)
                .eq(FormCustomField::getCustomType, customType)
        );
    }

    @Override
    public List<FormCustomField> list(FormCustomFieldParam param) {
        PageParam<FormCustomField, FormCustomFieldParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }
}
