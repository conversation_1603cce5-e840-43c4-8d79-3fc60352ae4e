package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.service.FormPublicityContentService;
import com.sanythadmin.project.form.entity.FormPublicityContent;
import com.sanythadmin.project.form.param.FormPublicityContentParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 公示内容控制器
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@RestController
@RequestMapping("/api/form/form-publicity-content")
public class FormPublicityContentController extends BaseController {
    @Resource
    private FormPublicityContentService formPublicityContentService;

    /**
     * 分页查询公示内容（权限标识：form:formPublicityContent:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityContent:list')")
    @GetMapping("/page")
    public PageResult<FormPublicityContent> page(FormPublicityContentParam param) {
        PageParam<FormPublicityContent, FormPublicityContentParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formPublicityContentService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部公示内容（权限标识：form:formPublicityContent:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityContent:list')")
    @GetMapping()
    public List<FormPublicityContent> list(FormPublicityContentParam param) {
        PageParam<FormPublicityContent, FormPublicityContentParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formPublicityContentService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询公示内容（权限标识：form:formPublicityContent:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityContent:list')")
    @GetMapping("/{id}")
    public FormPublicityContent get(@PathVariable("id") String id) {
        return formPublicityContentService.getById(id);
    }

    /**
     * 添加或修改公示内容（权限标识：form:formPublicityContent:operation）
     */
    @PreAuthorize("hasAuthority('form:formPublicityContent:operation')")
    @OperationLog(module = "公示内容", comments = "保存公示内容")
    @PostMapping("/operation")
    public void save(@RequestBody FormPublicityContent formPublicityContent) {
        if (StringUtils.hasLength(formPublicityContent.getId())) {
            formPublicityContentService.updateById(formPublicityContent);
        } else {
            formPublicityContentService.save(formPublicityContent);
        }
    }

    /**
     * 批量删除公示内容（权限标识：form:formPublicityContent:remove）
     */
    @PreAuthorize("hasAuthority('form:formPublicityContent:remove')")
    @OperationLog(module = "公示内容", comments = "批量删除公示内容")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formPublicityContentService.removeByIds(ids);
    }
}
