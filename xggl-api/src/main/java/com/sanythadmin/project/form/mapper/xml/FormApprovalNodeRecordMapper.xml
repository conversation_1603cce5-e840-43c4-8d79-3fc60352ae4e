<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormApprovalNodeRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , WORKFLOW_ID, NODE_ID, APPROVER_ID, CREATE_TIME, APPLICATION_ID, RESULT, XGH, BZ1, BZ2, BZ3, BZ4, BZ5, XM
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.workflowId != null">
            AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
        </if>
        <if test="param.nodeId != null">
            AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
        </if>
        <if test="param.approverId != null">
            AND a.APPROVER_ID LIKE concat(concat('%',#{param.approverId), '%')
        </if>
        <if test="param.applicationId != null">
            AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
        </if>
        <if test="param.result != null">
            AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
        </if>
        <if test="param.xgh != null">
            AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
        </if>
        <if test="param.bz1 != null">
            AND a.BZ1 LIKE concat(concat('%',#{param.bz1), '%')
        </if>
        <if test="param.bz2 != null">
            AND a.BZ2 LIKE concat(concat('%',#{param.bz2), '%')
        </if>
        <if test="param.bz3 != null">
            AND a.BZ3 LIKE concat(concat('%',#{param.bz3), '%')
        </if>
        <if test="param.bz4 != null">
            AND a.BZ4 LIKE concat(concat('%',#{param.bz4), '%')
        </if>
        <if test="param.bz5 != null">
            AND a.BZ5 LIKE concat(concat('%',#{param.bz5), '%')
        </if>
        <if test="param.xm != null">
            AND a.XM LIKE concat(concat('%',#{param.xm), '%')
        </if>
    </sql>
</mapper>
