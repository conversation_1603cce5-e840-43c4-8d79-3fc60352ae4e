package com.sanythadmin.project.form.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.ErrorInfo;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.*;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.form.dto.ApplyInfoImportDTO;
import com.sanythadmin.project.form.dto.BaseFormApplicationInfoDTO;
import com.sanythadmin.project.form.dto.SummaryExportInfoDTOForm;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.enums.FileType;
import com.sanythadmin.project.form.enums.InfoType;
import com.sanythadmin.project.form.mapper.*;
import com.sanythadmin.project.form.param.FormApplicationInfoParam;
import com.sanythadmin.project.form.param.FormApprovalNodeParam;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;
import com.sanythadmin.project.form.service.FormApplicationInfoService;
import com.sanythadmin.project.form.service.FormLimitQuotaService;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.form.service.FormTemplateFieldService;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.ApproverType;
import com.sanythadmin.project.workflow.enums.ReturnWay;
import com.sanythadmin.project.workflow.enums.ReviewType;
import com.sanythadmin.project.workflow.mapper.WorkflowConditionDetailMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeMapper;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workflow.util.WorkflowUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 表单申请记录Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:38:48
 */
@AllArgsConstructor
@Service
public class FormApplicationInfoServiceImpl extends ServiceImpl<FormApplicationInfoMapper, FormApplicationInfo> implements FormApplicationInfoService {
    private final FormProjectMapper projectMapper;
    private final FormApplicationInfoMapper applicationInfoMapper;
    private final FormApplicationListInfoMapper applicationListInfoMapper;
    private final GridFsService gridFsService;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final FormApprovalNodeMapper approvalNodeMapper;
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final UserInfoMapper userInfoMapper;
    private final WorkflowConditionDetailMapper conditionDetailMapper;
    private final FormApprovalNodeRecordMapper approvalNodeRecordMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final FormApprovalResultMapper approvalRecordMapper;
    private final FormTemplateFieldService templateFieldService;
    private final FormRestrictService restrictService;
    private final FormLimitQuotaService limitQuotaService;
    private final WorkflowService workflowService;
    private final FormProjectSpecialListMapper specialListMapper;

    @Transactional
    @Override
    public void edit(HttpServletRequest request, boolean apply) {
        FormDataWrapper<FormApplicationInfo> wrapper = new FormDataWrapper<>(request, FormApplicationInfo.class);
        Map<String, Object> paramMap = wrapper.getTextFields(true);
        FormApplicationInfo info = wrapper.getObject();
        List<String> fields = wrapper.getFields();
        AssertUtil.isTrue(StringUtils.hasText(info.getProjectId()), "缺失必须参数");
        FormProject project = projectMapper.selectById(info.getProjectId());
        checkCaptcha(project, paramMap);
        String error = checkApplyTime(project);
        AssertUtil.isTrue(!StringUtils.hasText(error), error);
        String username = SecurityUtil.getUsername();
        UserInfo userInfo = userInfoMapper.get(username);
        if (StringUtils.hasText(info.getId())) {
            if (apply) checkIsEdit(info.getId());
            FormApplicationInfo applicationInfo = applicationInfoMapper.selectById(info.getId());
            applicationInfo.setYear(project.getYear());
            // 设置申请时的角色ID
            applicationInfo.setRoleId(SecurityUtil.getRoleId());
            // 设置项目名称
            applicationInfo.setXxmc(project.getXmmc());
            for (String key : paramMap.keySet()) {
                if (!fields.contains(key)) continue;
                CommonUtil.setValue(applicationInfo, key, CommonUtil.getValue(key, info));
            }
            applicationInfoMapper.updateById(applicationInfo);
            info = applicationInfo;
        } else {
            error = checkLimit(username, project);
            AssertUtil.isTrue(!StringUtils.hasText(error), error);
            Long count = specialListMapper.selectCount(new LambdaQueryWrapper<FormProjectSpecialList>()
                    .eq(FormProjectSpecialList::getProjectId, project.getId())
                    .eq(FormProjectSpecialList::getYear, project.getYear())
                    .eq(FormProjectSpecialList::getXgh, username));
            if (count == 0) {
                error = checkPreItem(username, project);
                AssertUtil.isTrue(!StringUtils.hasText(error), error);
                error = restrictService.restrictCheck(project.getId(), userInfo, true);
                AssertUtil.isTrue(!StringUtils.hasText(error), error);
            }
            setBaseInfoFromUserInfo(info, userInfo, project);
            info.setXgh(username);
            // 设置申请时的角色ID
            info.setRoleId(SecurityUtil.getRoleId());
            info.setYear(project.getYear());
            info.setType(project.getType());
            info.setUserType(project.getUserType());
            info.setCreateTime(LocalDateTime.now());
            info.setSpjg(ReviewResult.DaiShenPi);
            // 设置项目名称
            info.setXxmc(project.getXmmc());
            applicationInfoMapper.insert(info);
        }

        HashMap<String, List<FileInfo>> fileMap = CommonUtil.multipartFileSave(info.getXgh(), info, wrapper.getFileFields(), wrapper.getDeleteFileIds());
        if (!CollectionUtils.isEmpty(fileMap)) {
            UpdateWrapper<FormApplicationInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(FormApplicationInfo::getId, info.getId());
            for (Map.Entry<String, List<FileInfo>> entry : fileMap.entrySet()) {
                updateWrapper.set(entry.getKey(), JSON.toJSONString(entry.getValue()));
            }
            applicationInfoMapper.update(null, updateWrapper);
        }

        UpdateWrapper<FormApplicationListInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(FormApplicationListInfo::getXgh, info.getXgh())
                .eq(FormApplicationListInfo::getProjectId, info.getProjectId())
                .eq(FormApplicationListInfo::getYear, info.getYear())
                .eq(FormApplicationListInfo::getType, info.getType())
                .isNull(FormApplicationListInfo::getApplicationId)
                .set(FormApplicationListInfo::getApplicationId, info.getId());
        applicationListInfoMapper.update(null, updateWrapper);
        if (apply && Objects.equals(JudgeMark.NO, info.getSfzc())) {
            workflowService.createApprovalNode(FormApprovalNode.class, info,
                    new WorkflowParam(info.getProjectId(), info.getYear(), null),
                    userInfo);
        }
    }

    /**
     * 前置/互斥项目校验，是否符合申请
     *
     * @param xgh
     * @param project
     */
    @Override
    public String checkPreItem(String xgh, FormProject project) {
        String error = null;
        if (StringUtils.hasText(project.getQzqzxmid()) || StringUtils.hasText(project.getQzxmid())) {
            error = "未完成前置项目申请";
            List<String> objects = approvalRecordMapper.selectObjs(new LambdaQueryWrapper<FormApprovalResult>()
                    .select(FormApprovalResult::getProjectId).eq(FormApprovalResult::getXgh, xgh)
                    .eq(FormApprovalResult::getYear, project.getYear()));
            Set<String> approvalRecordItems = !CollectionUtils.isEmpty(objects) ?
                    objects.stream().map(String::valueOf).collect(Collectors.toSet()) : new HashSet<>();
            // 强制前置项目/前置项目“并或”逻辑
            if (StringUtils.hasText(project.getQzqzxmid())) {
                List<String> strings = Arrays.stream(CommonUtil.split(project.getQzqzxmid()))
                        .filter(item -> !approvalRecordItems.contains(item)).toList();
                if (!CollectionUtils.isEmpty(strings))
                    return error;
            }

            if (StringUtils.hasText(project.getQzxmid())) {
                List<String> preItems = Arrays.asList(CommonUtil.split(project.getQzxmid()));
                if (Objects.equals(JoinRelation.AND.name(), project.getQzxmgx())) {
                    List<String> strings = preItems.stream().filter(item -> !approvalRecordItems.contains(item)).toList();
                    if (!CollectionUtils.isEmpty(strings))
                        return error;
                } else if (Objects.equals(JoinRelation.OR.name(), project.getQzxmgx())) {
                    if (project.getQzxms() != null && project.getQzxms() > 0) {
                        List<String> strings = preItems.stream().filter(approvalRecordItems::contains).toList();
                        if (strings.size() < project.getQzxms())
                            return error;
                    }
                }
            }
        }

        // 互斥项目
        if (StringUtils.hasText(project.getHcxmid())) {
            List<String> objects = applicationInfoMapper.selectObjs(new LambdaQueryWrapper<FormApplicationInfo>()
                    .select(FormApplicationInfo::getProjectId).eq(FormApplicationInfo::getXgh, xgh)
                    .eq(FormApplicationInfo::getYear, project.getYear()));
            Set<String> applyItems = !CollectionUtils.isEmpty(objects) ?
                    objects.stream().map(String::valueOf).collect(Collectors.toSet()) : new HashSet<>();
            List<String> strings = Arrays.stream(CommonUtil.split(project.getHcxmid()))
                    .filter(applyItems::contains).toList();
            if (!CollectionUtils.isEmpty(strings))
                error = "已申请项目存在互斥";
        }
        return error;
    }

    public void checkCaptcha(FormProject project, Map<String, Object> paramMap) {
        if (!Objects.isNull(project.getQysqyzm()) && project.getQysqyzm().getMark()) {
            String cid = paramMap.get("cid") == null ? null : String.valueOf(paramMap.get("cid"));
            String code = paramMap.get("code") == null ? null : String.valueOf(paramMap.get("code"));
            if (!StringUtils.hasText(cid) || !StringUtils.hasText(code))
                AssertUtil.throwMessage(ErrorInfo.CAPTCHA_ERROR_OR_EXPIRED);
            Object val = redisTemplate.opsForValue().get(cid);
            boolean mark = !Objects.isNull(val) && String.valueOf(val).equalsIgnoreCase(code);
            redisTemplate.delete(cid);
            AssertUtil.isTrue(mark, ErrorInfo.CAPTCHA_ERROR_OR_EXPIRED);
        }
    }

    @Override
    public String checkApplyTime(FormProject project) {
        return !project.applyTimeIsCorrect() ? "未在申请时间范围内" : null;
    }

    @Override
    public String checkLimit(String xgh, FormProject project) {
        String error = "已达项目最大申请条数";
        QueryWrapper<FormApplicationInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FormApplicationInfo::getXgh, xgh)
                .eq(FormApplicationInfo::getProjectId, project.getId())
                .eq(FormApplicationInfo::getYear, project.getYear()).eq(FormApplicationInfo::getSfzc, JudgeMark.NO);
        if (!Objects.isNull(project.getKsqts())) {
            Long count = applicationInfoMapper.selectCount(queryWrapper);
            if (count >= project.getKsqts())
                return error;
        }

        if (!Objects.isNull(project.getMrksqts())) {
            queryWrapper.eq("TO_CHAR(CREATE_TIME,'yyyy-MM-dd')", DateUtil.getDate());
            Long count = applicationInfoMapper.selectCount(queryWrapper);
            if (count >= project.getMrksqts())
                return error;
        }
        return null;
    }

    @Override
    public String checkIsEdit(String applicationId) {
        return workflowService.checkApplicationInfoIsCanEdit(FormApprovalNode.class, FormApprovalNodeRecord.class, applicationId);
    }

    @Transactional
    @Override
    public String approve(FormApprovalNodeRecord record) {
        FormApplicationInfo info = applicationInfoMapper.selectById(record.getApplicationId());
        UserInfo userInfo = userInfoMapper.get(info.getXgh());
        setBaseInfoFromApplicationInfo(info, userInfo);
        ApprovalInfo<FormApprovalNode, FormApprovalNodeRecord> approvalInfo = workflowService.approve(FormApprovalNode.class,
                record, info, new WorkflowParam(info.getProjectId(), info.getYear(), null),
                userInfo);
        Map<SFunction<FormApplicationInfo, ?>, Object> setParamMap = new HashMap<>() {{
            put(FormApplicationInfo::getSpjg, approvalInfo.getReviewResult());
        }};

        FormApprovalNode approvalNode = approvalInfo.getCurrentApprovalNode();
        if (Objects.equals(ConstantsWorkflow.STATE_PASS, approvalNode.getResult())) {
            FormProject project = projectMapper.selectById(info.getProjectId());
            limitQuotaService.checkQuota(project, userInfo);                // 节点审核通过时校验限制名额
            if (approvalNode.getEndNode().getMark()) {
                UserInfoUtil.codeTextSet(Collections.singletonList(userInfo));
                FormApprovalResult approvalResult = getApprovalResult(info, project, userInfo, record);
                approvalRecordMapper.insert(approvalResult);
            }
        } else {
            if (Objects.equals(ConstantsWorkflow.STATE_RETURN, approvalNode.getResult())) {
                if (Objects.equals(approvalNode.getReturnWay(), ReturnWay.APPLICANT))
                    setParamMap.put(FormApplicationInfo::getSfzc, JudgeMark.YES);
            }
            approvalRecordMapper.delete(new LambdaQueryWrapper<FormApprovalResult>()
                    .eq(FormApprovalResult::getApplicationId, info.getId())
                    .eq(FormApprovalResult::getXgh, info.getXgh()));
        }

        LambdaUpdateWrapper<FormApplicationInfo> applicationInfoWrapper = new LambdaUpdateWrapper<>();
        for (Map.Entry<SFunction<FormApplicationInfo, ?>, Object> entry : setParamMap.entrySet()) {
            applicationInfoWrapper.set(entry.getKey(), entry.getValue());
        }
        applicationInfoWrapper.eq(FormApplicationInfo::getId, info.getId());
        applicationInfoMapper.update(applicationInfoWrapper);
        return approvalInfo.getNextApprovableNodeId();
    }

    @Override
    public List<FormApplicationInfo> list(FormApplicationInfoParam param) {
        PageParam<FormApplicationInfo, FormApplicationInfoParam> page = new PageParam<>(param);
        return applicationInfoMapper.selectListWithPermission(page.getOrderWrapper());
    }

    private FormApprovalResult getApprovalResult(FormApplicationInfo info, FormProject project, UserInfo userInfo,
                                                 FormApprovalNodeRecord record) {
        FormApprovalResult result = new FormApprovalResult();
        result.setApplicationId(info.getId());
        result.setType(info.getType());
        result.setYear(info.getYear());
        result.setProjectId(info.getProjectId());
        result.setUserType(project.getUserType());
        result.setXmmc(project.getXmmc());
        result.setXgh(userInfo.getXgh());
        result.setXm(userInfo.getXm());
        result.setXb(userInfo.getXb());
        result.setPyccid(userInfo.getPyccid());
        result.setPyccmc(userInfo.getPyccmc());
        result.setXyid(userInfo.getXyid());
        result.setXymc(userInfo.getXymc());
        result.setZyid(userInfo.getZyid());
        result.setZymc(userInfo.getZymc());
        result.setBjid(userInfo.getBjid());
        result.setBjmc(userInfo.getBjmc());
        result.setNjid(userInfo.getNjid());
        result.setNjmc(userInfo.getNjmc());
        result.setXqmc(userInfo.getXqmc());
        result.setSfjtxm(project.getSfjtxm());
        result.setCreateTime(LocalDateTime.now());
        List<String> extendsField = FormApprovalNodeRecord.getExtendsField();
        for (String fieldName : extendsField) {
            Field field = CommonUtil.getField(result.getClass(), fieldName);
            if (field == null) continue;
            Object value = CommonUtil.getValue(fieldName, record);
            CommonUtil.setValue(result, fieldName, value);
        }
        return result;
    }

    private static void setBaseInfoFromUserInfo(FormApplicationInfo info, UserInfo userInfo, FormProject project) {
        info.setXyid(userInfo.getXyid());
        info.setZyid(userInfo.getZyid());
        info.setBjid(userInfo.getBjid());
        info.setNjid(userInfo.getNjid());
        info.setPyccid(userInfo.getPyccid());
        if (Objects.equals(JudgeMark.YES, project.getSfaxjydqcx())) {
            // 查询最新学籍异动记录设置学院, 专业, 班级信息

        }
    }

    private static void setBaseInfoFromApplicationInfo(FormApplicationInfo info, UserInfo userInfo) {
        userInfo.setXyid(info.getXyid());
        userInfo.setZyid(info.getZyid());
        userInfo.setBjid(info.getBjid());
        userInfo.setNjid(info.getNjid());
    }

    @Override
    public PageResult<FormApplicationInfo> page(FormApplicationInfoParam param) {
        PageParam<FormApplicationInfo, FormApplicationInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FormApplicationInfo> approvalList(FormApplicationInfoParam param, UserInfoParam userInfoParam, FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam) {
        MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> wrapper = approvalListQueryWrapper(param, userInfoParam, approvalNodeParam, codeXsztParam);
        List<FormApplicationInfo> infos = applicationInfoMapper.selectJoinListWithPermission(FormApplicationInfo.class, wrapper, new MyMPQueryParams(FormApplicationInfo.class));
        UserInfoUtil.codeTextSet(infos);
        return infos;
    }

    @Override
    public PageResult<FormApplicationInfo> pageApprovalList(FormApplicationInfoParam param, UserInfoParam userInfoParam, FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam) {
        MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> wrapper = approvalListQueryWrapper(param, userInfoParam, approvalNodeParam, codeXsztParam);
        Page<FormApplicationInfo> page = wrapper.getPage();
        Long count = nodeApproverMapper.selectCount(new LambdaQueryWrapper<WorkflowNodeApprover>()
                .eq(WorkflowNodeApprover::getNodeId, approvalNodeParam.getNodeId())
                .eq(WorkflowNodeApprover::getApproverType, ApproverType.user)
                .in(WorkflowNodeApprover::getApproverId, (Object[]) CommonUtil.strArray(SecurityUtil.getUsername(), SecurityUtil.getRoleId()))
        );
        page = count > 0 ? applicationInfoMapper.selectJoinPage(page, FormApplicationInfo.class, wrapper) :
                applicationInfoMapper.selectJoinPageWithPermission(page, FormApplicationInfo.class, wrapper, new MyMPQueryParams(FormApplicationInfo.class));
        List<FormApplicationInfo> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    private MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> approvalListQueryWrapper(FormApplicationInfoParam param
            , UserInfoParam userInfoParam, FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam) {
        FormProject project = projectMapper.selectOne(new LambdaQueryWrapper<FormProject>().eq(FormProject::getId, param.getProjectId()));
        param.setType(project.getType());
        param.setYear(project.getYear());
        param.setSfzc(JudgeMark.NO);
        userInfoParam.setUserType(project.getUserType());
        UserState userState = null;
        if (Objects.equals(JudgeMark.YES, project.getSfbys()))
            userState = UserState.biYeSheng;

        MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> wrapper = UserInfoUtil.buildInnerJoinUserInfoWrapper(userState
                , param, FormApplicationInfo::getXgh, userInfoParam, project.getUserType());
        Class<UserInfo> userInfoClass = UserInfo.class;
        Class<FormApplicationInfo> applicationInfoClass = FormApplicationInfo.class;
        SFunction<UserInfo, Object>[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(UserInfo.class), false);
        SFunction<FormApplicationInfo, Object>[] sFunctions1 = CommonUtil.buildSFunctions(applicationInfoClass, CommonUtil.getAllFields(FormApplicationInfo.class), "userType");
        wrapper.select(sFunctions).select(sFunctions1);
        Map<Class<?>, String> tableAliasMap = wrapper.getTableAliasMap();
        wrapper.selectAssociation(tableAliasMap.get(userInfoClass), userInfoClass, FormApplicationInfo::getUserInfo);
        appendSubQuery(project, wrapper, approvalNodeParam);
        return wrapper;
    }

    private void appendSubQuery(FormProject project, MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> wrapper, FormApprovalNodeParam approvalNodeParam) {
        String nodeId = approvalNodeParam.getNodeId();
        if (!StringUtils.hasText(nodeId)) {
            String s = CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId());
            List<WorkflowNodeApprover> list = nodeApproverMapper.nodeListByFormInfoAndApprover(project.getId(), project.getYear(), s);
            if (!CollectionUtils.isEmpty(list)) {
                List<String> strings = list.stream().map(WorkflowNodeApprover::getNodeId).toList();
                nodeId = CommonUtil.str2SqlIn(strings.toArray(new String[]{}));
            }
        } else {
            nodeId = CommonUtil.str2SqlIn(nodeId);
        }

        StringBuilder existsSql = new StringBuilder("SELECT 1 FROM SYT_FORM_APPROVAL_NODE WHERE " +
                "NODE_ID IN (" + nodeId + ") AND " + wrapper.getAlias() + ".ID = APPLICATION_ID");
        if (StringUtils.hasText(approvalNodeParam.getResult()))
            existsSql.append(" AND RESULT = ").append(CommonUtil.str2SqlIn(approvalNodeParam.getResult()));
        wrapper.exists(existsSql.toString());
    }

    @Override
    public PageResult<BaseFormApplicationInfoDTO> pageByApplicant(FormApplicationInfoParam param) {
        param.setXgh(SecurityUtil.getUsername());
        param.setSfzc(JudgeMark.NO);
        MyMPJLambdaWrapper<FormApplicationInfo, FormApplicationInfoParam> wrapper = new MyMPJLambdaWrapper<>(param);
        SFunction<FormApplicationInfo, Object>[] sFunctions = CommonUtil.buildSFunctions(FormApplicationInfo.class, CommonUtil.getAllFields(BaseFormApplicationInfo.class), true);
        wrapper.select(sFunctions);
        wrapper.innerJoin(FormProject.class, FormProject::getId, FormApplicationInfo::getProjectId);
        wrapper.select(FormProject::getXmmc, FormProject::getQysqyzm);
        wrapper.orderByDesc(FormApplicationInfo::getCreateTime);
        Page<BaseFormApplicationInfoDTO> page = new Page<>(param.getPage(), param.getLimit());
        page = applicationInfoMapper.selectJoinPage(page, BaseFormApplicationInfoDTO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public CompletableFuture<UserInfo> dataImportCheck(ApplyInfoImportDTO dto, Executor executor) {
        FormApplicationInfo info = dto.getInfo();
        List<WorkflowNode> workflowNodes = dto.getWorkflowNodes();
        UserInfo userInfo = userInfoMapper.get(info.getXgh());
        UserInfoUtil.codeTextSet(userInfo);
        setBaseInfoFromUserInfo(info, userInfo, dto.getProject());
        // 设置申请时的角色ID
        info.setRoleId(SecurityUtil.getRoleId());
        // 设置项目名称
        info.setXxmc(dto.getProject().getXmmc());
        return CompletableFuture.supplyAsync(() -> {
            String error = checkLimit(info.getXgh(), dto.getProject());
            if (StringUtils.hasText(error))
                AssertUtil.throwMessage(error);

            if (!dto.getSpecialListStudent().contains(info.getXgh())) {
                error = checkPreItem(info.getXgh(), dto.getProject());
                if (StringUtils.hasText(error))
                    AssertUtil.throwMessage(error);

                error = restrictService.restrictCheck(dto.getProject().getId(), userInfo, true);
                if (StringUtils.hasText(error))
                    AssertUtil.throwMessage(error);
                WorkflowNode currentUserLastNode = dto.getApproverNodes().get(dto.getApproverNodes().size() - 1);
                List<WorkflowNode> preNodes = workflowNodes.stream().filter(node -> node.getSort() <= currentUserLastNode.getSort()).toList();
                int total = 0;
                for (WorkflowNode node : preNodes) {
                    if (WorkflowUtil.checkConditionIsMatch(node, info, userInfo, dto.getConditionMap()))
                        total++;
                }

                if (total == 0)
                    AssertUtil.throwMessage("没有符合条件的审批节点");
            }

            return userInfo;
        }, executor);
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void dataImport(ApplyInfoImportDTO dto) {
        SysAccount account = dto.getAccount();
        FormApplicationInfo info = dto.getInfo();
        List<WorkflowNode> workflowNodes = dto.getWorkflowNodes();
        UserInfo userInfo = dto.getUserInfo();
        applicationInfoMapper.insert(info);
        WorkflowNode currentUserLastNode = dto.getApproverNodes().get(dto.getApproverNodes().size() - 1);
        List<WorkflowNode> preNodes = workflowNodes.stream().filter(node -> node.getSort() <= currentUserLastNode.getSort()).toList();
        List<FormApprovalNode> approvalNodes = new ArrayList<>();
        for (WorkflowNode node : preNodes) {
            if (WorkflowUtil.checkConditionIsMatch(node, info, userInfo, dto.getConditionMap())) {
                FormApprovalNode approvalNode = WorkflowUtil.getApprovalNode(FormApprovalNode.class, node, info.getId(), CollectionUtils.isEmpty(approvalNodes));
                approvalNodes.add(approvalNode);
            }
        }

        WorkflowNode nextNode = WorkflowUtil.nextNode(info.getXgh(), currentUserLastNode.getSort(), workflowNodes, info, userInfo);
        boolean currentUserLastNodeIsPass = true;
        int size = approvalNodes.size();
        FormApprovalNodeRecord lastNodeApprovalRecord = null;
        for (int index = 0; index < size; index++) {
            FormApprovalNode approvalNode = approvalNodes.get(index);
            approvalNode.setUpdateTime(LocalDateTime.now());
            if (index > 0)
                approvalNode.setPreNodeResult(ConstantsWorkflow.STATE_PASS);
            if (index == size - 1 && Objects.isNull(nextNode)) {
                approvalNode.setNextNodeResult(null);
                approvalNode.setEndNode(JudgeMark.YES);
            } else {
                approvalNode.setNextNodeResult(ConstantsWorkflow.STATE_PASS);
            }

            ReviewType reviewType = dto.getWorkflowNodeMap().get(approvalNode.getNodeId()).getReviewType();
            approvalNode.setResult(ConstantsWorkflow.STATE_PASS);
            if (!Objects.isNull(reviewType) && Objects.equals(ReviewType.huiQian, reviewType)
                    && approvalNode.getNodeId().equals(currentUserLastNode.getId())) {
                List<WorkflowNodeApprover> nodeApprovers = dto.getNodeApproverMap().get(approvalNode.getNodeId());
                for (WorkflowNodeApprover approver : nodeApprovers) {
                    if (!Objects.equals(approver.getApproverId(), account.getRole().getId())
                            && !Objects.equals(approver.getApproverId(), account.getUsername())) {
                        approvalNode.setResult(ConstantsWorkflow.STATE_PENDING);
                        currentUserLastNodeIsPass = false;
                        break;
                    }
                }
            }

            WorkflowNodeApprover nodeApprover = WorkflowUtil.getNodeApprover(dto.getApproverList(), approvalNode);
            FormApprovalNodeRecord record = new FormApprovalNodeRecord();
            record.setCreateTime(LocalDateTime.now());
            record.setResult(ConstantsWorkflow.STATE_PASS);
            record.setApplicationId(info.getId());
            record.setNodeId(approvalNode.getNodeId());
            record.setWorkflowId(approvalNode.getWorkflowId());
            record.setUsername(account.getUsername());
            record.setRealName(account.getRealName());
            record.setRoleId(account.getRole().getId());
            record.setNodeApproverId(nodeApprover.getId());
            record.setApproverId(nodeApprover.getApproverId());
            record.setStage(approvalNode.getStage());
            approvalNodeRecordMapper.insert(record);
            approvalNodeMapper.insert(approvalNode);
            lastNodeApprovalRecord = record;
        }

        try {
            if (Objects.isNull(nextNode) || !currentUserLastNodeIsPass) return;
            FormApprovalNode nextApprovalNode = WorkflowUtil.getApprovalNode(FormApprovalNode.class, nextNode, info.getId(), false);
            nextApprovalNode.setPreNodeResult(ConstantsWorkflow.STATE_PASS);
            approvalNodeMapper.insert(nextApprovalNode);
        } finally {
            ReviewResult result = Objects.isNull(nextNode) && currentUserLastNodeIsPass ? ReviewResult.TongGuo : ReviewResult.ShenPiZhong;
            baseMapper.update(new LambdaUpdateWrapper<FormApplicationInfo>()
                    .set(FormApplicationInfo::getSpjg, result)
                    .eq(FormApplicationInfo::getId, info.getId()));
            if (Objects.equals(result, ReviewResult.TongGuo)) {
                FormApprovalResult approvalResult = getApprovalResult(info, dto.getProject(), userInfo, lastNodeApprovalRecord);
                approvalRecordMapper.insert(approvalResult);
            }
        }
    }

    @Transactional
    @Override
    public void delete(String... ids) {
        SysAccount account = SecurityUtil.getAccount();
        String approverId = CommonUtil.appendComma(account.getUsername(), account.getRole().getId());
        HashMap<String, List<WorkflowNode>> nodesMap = new HashMap<>();
        HashMap<String, List<WorkflowNode>> currentUserNodesMap = new HashMap<>();
        HashMap<String, List<WorkflowConditionDetail>> conditionDetailMap = new HashMap<>();
        for (String id : ids) {
            FormApplicationInfo info = baseMapper.selectById(id);
            List<FormApprovalNode> formApprovalNodes = null;
            UserInfo userInfo = userInfoMapper.get(info.getXgh());
            String key = CommonUtil.appendComma(info.getProjectId(), info.getYear());
            List<WorkflowNode> currentUserNodes = currentUserNodesMap.get(key);
            if (CollectionUtils.isEmpty(currentUserNodes)) {
                currentUserNodes = workflowNodeMapper.nodeListByFormInfoAndApprover(
                        info.getProjectId(), info.getYear(), approverId);
                if (CollectionUtils.isEmpty(currentUserNodes)) continue;
                currentUserNodesMap.put(key, currentUserNodes);
            }

            String workflowId = currentUserNodes.get(0).getWorkflowId();
            List<WorkflowNode> nodes = nodesMap.get(workflowId);
            if (CollectionUtils.isEmpty(nodes)) {
                nodes = workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>()
                        .eq(WorkflowNode::getWorkflowId, workflowId).orderByAsc(WorkflowNode::getSort));
                nodesMap.put(workflowId, nodes);
            }

            List<WorkflowConditionDetail> conditionDetails = null;
            if (!conditionDetailMap.containsKey(workflowId)) {
                conditionDetails = conditionDetailMapper.selectList(new LambdaQueryWrapper<WorkflowConditionDetail>().
                        eq(WorkflowConditionDetail::getWorkflowId, workflowId));
                conditionDetailMap.put(workflowId, conditionDetails);
            } else {
                conditionDetails = conditionDetailMap.get(workflowId);
            }

            Map<String, List<WorkflowConditionDetail>> conditionMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(conditionDetails))
                conditionMap = conditionDetails.stream().collect(Collectors.groupingBy(WorkflowConditionDetail::getConditionId));
            List<WorkflowNode> approvalNodes = new ArrayList<>();
            for (WorkflowNode node : nodes) {
                if (WorkflowUtil.checkConditionIsMatch(node, info, userInfo, conditionMap))
                    approvalNodes.add(node);
            }
            currentUserNodes.removeIf(node -> !approvalNodes.stream().map(WorkflowNode::getId)
                    .collect(Collectors.toSet()).contains(node.getId()));

            int lastIndex = approvalNodes.size() - 1;
            if (lastIndex < 0) continue;
            WorkflowNode node = approvalNodes.get(lastIndex);
            Optional<WorkflowNode> optional = currentUserNodes.stream().filter(n -> n.getId().equals(node.getId())).findFirst();
            boolean permitDelete = optional.isPresent();  //  true：直接删除申请相关信息
            if (!permitDelete) {
                formApprovalNodes = approvalNodeMapper.selectList(new LambdaQueryWrapper<FormApprovalNode>()
                        .eq(FormApprovalNode::getWorkflowId, workflowId).eq(FormApprovalNode::getApplicationId, info.getId())
                        .orderByAsc(FormApprovalNode::getSort));
                WorkflowNode currentUserLastNode = currentUserNodes.get(currentUserNodes.size() - 1);
                FormApprovalNode approvalNode = formApprovalNodes.get(formApprovalNodes.size() - 1);
                if (currentUserLastNode.getSort() >= approvalNode.getSort()) {
                    permitDelete = true;
                } else {
                    int size = currentUserNodes.size();
                    Integer index = null;
                    for (int j = size - 1; j >= 0; j--) {
                        WorkflowNode workflowNode = currentUserNodes.get(j);
                        for (int i = 0; i < formApprovalNodes.size(); i++) {
                            FormApprovalNode formApprovalNode = formApprovalNodes.get(i);
                            if (Objects.equals(workflowNode.getId(), formApprovalNode.getNodeId())) {
                                index = i;
                                break;
                            }
                        }
                        if (Objects.nonNull(index)) break;
                    }

                    FormApprovalNode currentUserNextNode = formApprovalNodes.get(index + 1);
                    permitDelete = Objects.equals(ConstantsWorkflow.STATE_PENDING, currentUserNextNode.getResult());
                }
            }

            if (!permitDelete)
                AssertUtil.throwMessage("操作失败，下一级已审批“" + userInfo.getXm() + "”申请记录");
            delete(info, workflowId);
        }
    }

    @Transactional
    @Override
    public void deleteByApplicant(String... ids) {
        SysAccount account = SecurityUtil.getAccount();
        for (String id : ids) {
            FormApplicationInfo info = getById(id);
            if (Objects.equals(account.getUsername(), info.getXgh())) { // 申请者删除申请信息
                FormProject project = projectMapper.selectById(info.getProjectId());
                String error = checkApplyTime(project);
                if (StringUtils.hasText(error))
                    AssertUtil.throwMessage(error);
                String workflowId = checkIsEdit(id);
                delete(info, workflowId);
            }
        }
    }

    private void delete(FormApplicationInfo info, String workflowId) {
        approvalRecordMapper.delete(new LambdaQueryWrapper<FormApprovalResult>()
                .eq(FormApprovalResult::getApplicationId, info.getId()).eq(FormApprovalResult::getXgh, info.getXgh()));
        approvalNodeRecordMapper.delete(new LambdaQueryWrapper<FormApprovalNodeRecord>()
                .eq(FormApprovalNodeRecord::getApplicationId, info.getId()).eq(FormApprovalNodeRecord::getWorkflowId, workflowId));
        approvalNodeMapper.delete(new LambdaQueryWrapper<FormApprovalNode>().eq(FormApprovalNode::getApplicationId, info.getId()).
                eq(FormApprovalNode::getWorkflowId, workflowId));
        FormTemplateFieldParam param = new FormTemplateFieldParam();
        param.setProjectId(info.getProjectId());
        param.setYear(info.getYear());
        param.setType(info.getType());
        param.setInfoType(Collections.singletonList(InfoType.apply));
        param.setControlType("fileUpload,imageUpload");
        List<FormTemplateField> fields = templateFieldService.list(param);
        if (!CollectionUtils.isEmpty(fields)) {
            for (FormTemplateField field : fields) {
                Object value = CommonUtil.getValue(field.getFieldEn(), info);
                if (value == null) continue;
                gridFsService.removeByFileInfoStr(String.valueOf(value));
            }
        }
        applicationInfoMapper.deleteById(info.getId());
        applicationListInfoMapper.delete(new LambdaQueryWrapper<FormApplicationListInfo>()
                .eq(FormApplicationListInfo::getProjectId, info.getProjectId())
                .eq(FormApplicationListInfo::getXgh, info.getXgh())
                .eq(FormApplicationListInfo::getYear, info.getYear()));
    }


    @Override
    public void exportByTemplate(FormProject project, TemplateFile templateFile, List<FormApplicationInfo> infos
            , HttpServletResponse response) {
        String template = templateFile.getTemplateName();
        GridFsResource fsResource = gridFsService.getByFilename(template);
        InputStream inputStream = null;
        try {
            inputStream = fsResource.getInputStream();
        } catch (IOException e) {
            log.error("获取模板文件失败", e);
        }

        if (Objects.equals(templateFile.getTemplateType(), TemplateType.sqb)) {
            if (CollectionUtils.isEmpty(infos)) return;
            downloadApplyInfoFile(infos, inputStream, project, response);
        } else if (Objects.equals(templateFile.getTemplateType(), TemplateType.hzb)) {
            exportSummaryData(project, infos, inputStream, response);
        }
    }

    @Override
    public Long getApplyCount(FormApplicationInfoParam infoParam) {
        PageParam<FormApplicationInfo, FormApplicationInfoParam> pageParam = new PageParam<>(infoParam);
        return baseMapper.selectCountWithPermission(pageParam.getWrapper(), new MyMPQueryParams(FormApplicationInfo.class));
    }

    private Map<String, Object> buildApplyInfoMap(FormApplicationInfo applicationInfo) throws IOException {
        Map<String, Object> objectMap = new HashMap<>();
        UserInfo userInfo = applicationInfo.getUserInfo();
        objectMap.put("object", applicationInfo);
        if (StringUtils.hasText(userInfo.getPhoto())) {
            List<FileInfo> fileInfos = JSONArray.parseArray(userInfo.getPhoto(), FileInfo.class);
            GridFsResource gridFsResource = gridFsService.get(fileInfos.get(0));
            if (gridFsResource != null)
                objectMap.put("photo", IOUtils.toByteArray(gridFsResource.getInputStream()));
        }
        return objectMap;
    }

    private void exportSummaryData(FormProject project, List<FormApplicationInfo> infos,
                                   InputStream templateInputStream, HttpServletResponse response) {
        List<SummaryExportInfoDTOForm> summaryExportInfoDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(infos)) {
            List<FormApprovalResult> approvalRecords = approvalRecordMapper.selectListWithPermission(
                    new LambdaQueryWrapper<FormApprovalResult>().eq(FormApprovalResult::getProjectId, project.getId())
                            .eq(FormApprovalResult::getYear, project.getYear()));
            HashMap<String, FormApprovalResult> map = new HashMap<>();
            if (!CollectionUtils.isEmpty(approvalRecords))
                approvalRecords.forEach(record -> map.put(record.getApplicationId(), record));
            for (FormApplicationInfo applicationInfo : infos) {
                SummaryExportInfoDTOForm summaryExportInfoDTO = new SummaryExportInfoDTOForm();
                BeanUtils.copyProperties(applicationInfo, summaryExportInfoDTO);
                summaryExportInfoDTO.setResult(map.getOrDefault(applicationInfo.getId(), null));
                summaryExportInfoDTOS.add(summaryExportInfoDTO);
            }
        }

        try {
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("list", summaryExportInfoDTOS);
            String filename = URLEncoder.encode(project.getXmmc() + "_" + DateUtil.getDateTime(DateUtil.DEFAULT_DATEMS_FORMAT), StandardCharsets.UTF_8);
            ServletOutputStream outputStream = response.getOutputStream();
            JxlsUtil.export(objectMap, templateInputStream, outputStream);
            response.setHeader("Content-Disposition", "attachment; filename=" + filename + ".xlsx");
            response.flushBuffer();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    private void downloadApplyInfoFile(List<FormApplicationInfo> applicationInfos, InputStream templateInputStream, FormProject project, HttpServletResponse response) {
        String excel = ".xlsx";
        String pdf = ".pdf";
        response.setContentType("application/octet-stream");
        if (applicationInfos.size() == 1) {
            try {
                FormApplicationInfo applicationInfo = applicationInfos.get(0);
                Map<String, Object> objectMap = buildApplyInfoMap(applicationInfo);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                JxlsUtil.export(objectMap, templateInputStream, outputStream);
                String filename = URLEncoder.encode(applicationInfo.getUserInfo().getXm(), StandardCharsets.UTF_8);
                if (!StringUtils.hasText(project.getSqbdcgs()) || FileType.excel.name().equals(project.getSqbdcgs())) {
                    response.setHeader("Content-Disposition", "attachment; filename=" + filename + excel);
                    response.getOutputStream().write(outputStream.toByteArray());
                    response.flushBuffer();
                } else {
                    response.setHeader("Content-Disposition", "attachment; filename=" + filename + pdf);
                    PdfUtil.excel2Pdf(response.getOutputStream(), new ByteArrayInputStream(outputStream.toByteArray()));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            try {
                byte[] templateBytes = IOUtils.toByteArray(templateInputStream);
                response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(project.getXmmc() + "_申请表_" + DateUtil.getDateTime("yyyyMMddHHmmss") + ".zip", "utf-8"));
                OutputStream out = response.getOutputStream();
                ZipOutputStream zos = new ZipOutputStream(out);
                zos.setMethod(ZipOutputStream.DEFLATED);
                BufferedOutputStream bos = new BufferedOutputStream(zos);
                HashMap<String, Integer> indexMap = new HashMap<>();
                for (FormApplicationInfo info : applicationInfos) {
                    indexMap.put(info.getXgh(), indexMap.get(info.getXgh()) == null ? 1 : indexMap.get(info.getXgh()) + 1);
                    Map<String, Object> objectMap = buildApplyInfoMap(info);
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    JxlsUtil.export(objectMap, new ByteArrayInputStream(templateBytes), outputStream);
                    BufferedInputStream bufferedInputStream = new BufferedInputStream(new ByteArrayInputStream(outputStream.toByteArray()));
                    String filename = info.getUserInfo().getXgh() + "_" + info.getUserInfo().getXm() + "(" + indexMap.get(info.getXgh()) + ")";
                    if (!StringUtils.hasText(project.getSqbdcgs()) || FileType.excel.name().equals(project.getSqbdcgs())) {
                        zos.putNextEntry(new ZipEntry(filename + excel));
                    } else {
                        ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
                        PdfUtil.excelToPdf(tempOutputStream, bufferedInputStream);
                        zos.putNextEntry(new ZipEntry(filename + pdf));
                        zos.write(tempOutputStream.toByteArray());
                        zos.closeEntry();
                    }
                    IOUtils.copy(bufferedInputStream, bos);
                    bufferedInputStream.close();
                    bos.flush();
                }
                bos.close();
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
