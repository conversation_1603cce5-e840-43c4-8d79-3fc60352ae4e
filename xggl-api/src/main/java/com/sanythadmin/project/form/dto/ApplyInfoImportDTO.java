package com.sanythadmin.project.form.dto;

import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormTemplateField;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2024/8/21.
 */
@Data
public class ApplyInfoImportDTO {
    private SysAccount account;
    private FormApplicationInfo info;
    private FormProject project;
    private List<WorkflowNode> workflowNodes;
    private List<WorkflowNodeApprover> approverList;
    private Map<String, WorkflowNode> workflowNodeMap;
    private List<WorkflowNode> approverNodes;
    private Map<String, List<WorkflowConditionDetail>> conditionMap;
    private Map<String, List<WorkflowNodeApprover>> nodeApproverMap;
    private List<FormTemplateField> fields;
    private List<String> specialListStudent;
    private UserInfo userInfo;

    public ApplyInfoImportDTO(SysAccount account, FormApplicationInfo info, FormProject project, List<WorkflowNode> workflowNodes, List<WorkflowNode> approverNodes,
                              List<WorkflowConditionDetail> conditionDetails, List<WorkflowNodeApprover> approverList, List<FormTemplateField> fields,
                              List<String> specialListStudent, UserInfo userInfo) {
        this.account = account;
        this.info = info;
        this.project = project;
        this.workflowNodes = workflowNodes;
        this.approverNodes = approverNodes;
        this.workflowNodeMap = workflowNodes.stream().collect(Collectors.toMap(WorkflowNode::getId, Function.identity()));
        Map<String, List<WorkflowConditionDetail>> conditionMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(conditionDetails))
            conditionMap = conditionDetails.stream().collect(Collectors.groupingBy(WorkflowConditionDetail::getConditionId));
        this.conditionMap = conditionMap;

        Map<String, List<WorkflowNodeApprover>> approverMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(approverList))
            approverMap = approverList.stream().collect(Collectors.groupingBy(WorkflowNodeApprover::getNodeId));
        this.nodeApproverMap = approverMap;
        this.approverList = approverList;
        this.fields = fields;
        this.specialListStudent = specialListStudent;
        this.userInfo = userInfo;
    }
}
