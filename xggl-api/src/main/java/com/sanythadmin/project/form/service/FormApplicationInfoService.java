package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.form.dto.ApplyInfoImportDTO;
import com.sanythadmin.project.form.dto.BaseFormApplicationInfoDTO;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.param.FormApplicationInfoParam;
import com.sanythadmin.project.form.param.FormApprovalNodeParam;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 表单申请记录Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:38:48
 */
public interface FormApplicationInfoService extends IService<FormApplicationInfo> {
    public void edit(HttpServletRequest request, boolean apply);

    public String approve(FormApprovalNodeRecord record);

    public List<FormApplicationInfo> list(FormApplicationInfoParam param);

    /**
     * 分页相关查询
     *
     * @param param
     * @return
     */
    public PageResult<FormApplicationInfo> page(FormApplicationInfoParam param);

    public List<FormApplicationInfo> approvalList(FormApplicationInfoParam param, UserInfoParam userInfoParam,
                                                  FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam);

    public PageResult<FormApplicationInfo> pageApprovalList(FormApplicationInfoParam param, UserInfoParam userInfoParam,
                                                            FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam);

    public PageResult<BaseFormApplicationInfoDTO> pageByApplicant(FormApplicationInfoParam param);

    public void dataImport(ApplyInfoImportDTO dto);
    public CompletableFuture<UserInfo> dataImportCheck(ApplyInfoImportDTO dto, Executor executor);

    /**
     * 删除操作
     *
     * @param ids
     */
    public void delete(String... ids);

    public void deleteByApplicant(String... ids);

    /**
     * 限制条件检查
     */
    public String checkApplyTime(FormProject project);

    public String checkPreItem(String xgh, FormProject project);

    public String checkLimit(String xgh, FormProject project);

    public String checkIsEdit(String applicationId);

    /**
     * 数据导出根据模板
     */
    public void exportByTemplate(FormProject project, TemplateFile templateFile
            , List<FormApplicationInfo> infos, HttpServletResponse response);

    public Long getApplyCount(FormApplicationInfoParam infoParam);
}
