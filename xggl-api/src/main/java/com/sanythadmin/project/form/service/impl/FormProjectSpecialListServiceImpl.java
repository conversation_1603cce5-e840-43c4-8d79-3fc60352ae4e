package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormProjectSpecialList;
import com.sanythadmin.project.form.mapper.FormProjectSpecialListMapper;
import com.sanythadmin.project.form.param.FormProjectSpecialListParam;
import com.sanythadmin.project.form.service.FormProjectService;
import com.sanythadmin.project.form.service.FormProjectSpecialListService;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.service.SelectorDataService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特殊名单人员Service实现
 *
 * <AUTHOR>
 * @since 2024-07-09 11:36:55
 */
@Service
public class FormProjectSpecialListServiceImpl extends ServiceImpl<FormProjectSpecialListMapper, FormProjectSpecialList> implements FormProjectSpecialListService {
    @Resource
    private FormProjectSpecialListMapper specialListMapper;
    @Resource
    private FormProjectService projectService;
    @Resource
    private SelectorDataService selectorDataService;

    @Transactional
    @Override
    public void add(String id, List<SelectorData> param) {
        FormProject project = projectService.getById(id);
        List<FormProjectSpecialList> lists = specialListMapper.selectList(new LambdaQueryWrapper<FormProjectSpecialList>()
                .eq(FormProjectSpecialList::getProjectId, id).eq(FormProjectSpecialList::getYear, project.getYear()));
        List<UserInfo> userInfos = selectorDataService.queryUserListBySelDataWithPermission(param);
        Set<String> hashSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(lists))
            hashSet = lists.stream().map(FormProjectSpecialList::getXgh).collect(Collectors.toSet());
        for (UserInfo userInfo : userInfos) {
            if (hashSet.contains(userInfo.getXgh()))
                continue;
            FormProjectSpecialList object = new FormProjectSpecialList();
            object.setProjectId(id);
            object.setYear(project.getYear());
            object.setXgh(userInfo.getXgh());
            specialListMapper.insert(object);
            hashSet.add(userInfo.getXgh());
        }
    }

    @Override
    public void importTemplate(HttpServletResponse response) {
        try {
            String[] titles = new String[]{"学工号（必填）", "姓名", "备注"};
            EasyExcelHelper.exportExcel(null, titles, null, response, null);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Transactional
    @Override
    public void importData(String projectId, MultipartFile file) {
        FormProject project = projectService.getById(projectId);
        List<FormProjectSpecialList> lists = specialListMapper.selectList(new LambdaQueryWrapper<FormProjectSpecialList>()
                .eq(FormProjectSpecialList::getProjectId, projectId).eq(FormProjectSpecialList::getYear, project.getYear()));
        Set<String> hashSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(lists))
            hashSet = lists.stream().map(FormProjectSpecialList::getXgh).collect(Collectors.toSet());
        SheetData sheetData = null;
        try {
            sheetData = EasyExcelHelper.read(file);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        List<RowData> rowData = sheetData.getRowData();
        List<FormProjectSpecialList> list = new ArrayList<>();
        List<ExcelImportError> errors = new ArrayList<>();
        for (RowData row : rowData) {
            HashMap<Integer, String> cellData = row.getCellData();
            FormProjectSpecialList object = new FormProjectSpecialList();
            object.setXgh(cellData.get(0));
            object.setRemark(cellData.get(2));
            object.setProjectId(projectId);
            object.setYear(project.getYear());
            list.add(object);
            if (!StringUtils.hasText(object.getXgh())) {
                errors.add(ExcelImportError.data(row.getIndex() + 1, object.getXgh(), "学工号不能为空"));
                continue;
            }
            if (hashSet.contains(object.getXgh())) {
                errors.add(ExcelImportError.data(row.getIndex() + 1, object.getXgh(), "学工号重复或已存在"));
                continue;
            }
            hashSet.add(object.getXgh());
        }

        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }

        if (!CollectionUtils.isEmpty(list))
            list.forEach(specialListMapper::insert);
    }

    @Override
    public List<FormProjectSpecialList> list(FormProjectSpecialListParam param) {
        PageParam<FormProjectSpecialList, FormProjectSpecialListParam> pageParam = new PageParam<>(param);
        return specialListMapper.selectList(pageParam.getOrderWrapper());
    }

    @Override
    public PageResult<FormProjectSpecialList> queryPage(FormProjectSpecialListParam param) {
        MyMPJLambdaWrapper<FormProjectSpecialList, FormProjectSpecialListParam> wrapper = UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param,
                FormProjectSpecialList::getXgh, null);
        Class<UserInfo> userInfoClass = UserInfo.class;
        SFunction[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(BaseUserInfo.class), false);
        wrapper.selectAll(FormProjectSpecialList.class).select(sFunctions);
        wrapper.selectAssociation(userInfoClass, FormProjectSpecialList::getUserInfo);
        Page<FormProjectSpecialList> page = wrapper.getPage();
        page = specialListMapper.selectJoinPage(page, FormProjectSpecialList.class, wrapper);
        UserInfoUtil.codeTextSet(page.getRecords());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<Map<String, Object>> groupCountByProject(FormProject... projects) {
        return specialListMapper.selectMaps(new QueryWrapper<FormProjectSpecialList>()
                .select("PROJECT_ID", "YEAR", "COUNT(*) AS TOTAL")
                .in("PROJECT_ID", Arrays.stream(projects).map(FormProject::getId).toList())
                .in("YEAR", Arrays.stream(projects).map(FormProject::getYear).toList())
                .groupBy("PROJECT_ID", "YEAR"));
    }
}
