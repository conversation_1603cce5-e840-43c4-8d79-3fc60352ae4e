package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormGroup;
import com.sanythadmin.project.form.param.FormGroupParam;

import java.util.List;

/**
 * 表单信息组Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
public interface FormGroupService extends IService<FormGroup> {
    public void edit(FormGroup formGroup);
    public void editBaseInfo(String projectId, List<FormGroup> formGroups);
    public void delete(List<String> ids);
    PageResult<FormGroup> page(FormGroupParam param);
    List<FormGroup> list(FormGroupParam param);
    List<FormGroup> list(String projectId, String applicationId);
}
