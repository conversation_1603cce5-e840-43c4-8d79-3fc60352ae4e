package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公示项目查询参数
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormPublicityItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 截至时间
     */
    private String endDate;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 创建用户
     */
    @QueryField(type = QueryType.EQ)
    private String username;

    /**
     * 角色标识
     */
    @QueryField(type = QueryType.EQ)
    private String roleScope;

}
