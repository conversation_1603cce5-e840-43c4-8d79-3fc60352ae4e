package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 审批记录查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormApprovalNodeRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 工作流程ID
     */
    @QueryField(type = QueryType.EQ)
    private String workflowId;

    /**
     * 审批节点ID
     */
    @QueryField(type = QueryType.EQ)
    private String nodeId;

    /**
     * 审批对象ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String approverId;

    /**
     * 关联申请表记录ID
     */
    @QueryField(type = QueryType.EQ)
    private String applicationId;

    /**
     * 审批结果
     */
    @QueryField(type = QueryType.EQ)
    private String result;

    /**
     * 审批人
     */
    @QueryField(type = QueryType.EQ)
    private String username;
    /**
     * 审批人
     */
    @QueryField(type = QueryType.LIKE)
    private String realName;

    private String bz1;

    private String bz2;

    private String bz3;

    private String bz4;

    private String bz5;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "createTime desc";
    }
}
