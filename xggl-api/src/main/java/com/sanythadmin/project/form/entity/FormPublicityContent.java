package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 公示内容
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PUBLICITY_CONTENT")
@Entity
@Table(name = "SYT_FORM_PUBLICITY_CONTENT")
public class FormPublicityContent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 表单项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 学工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB")
    @TableField("XB")
    private String xb;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC")
    @TableField("NJMC")
    private String njmc;

    /**
     * 培养层次
     */
    @Column(name = "PYCCMC")
    @TableField("PYCCMC")
    private String pyccmc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC")
    @TableField("BJMC")
    private String bjmc;

    /**
     * 操作人
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 操作人角色标识
     */
    @Column(name = "ROLE_SCOPE")
    @TableField("ROLE_SCOPE")
    private String roleScope;

    /**
     * 公示项目ID
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
