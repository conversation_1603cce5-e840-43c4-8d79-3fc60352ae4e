package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 表单申请记录查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:38:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormApplicationInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 是否暂存（是：1， 否：0）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzc;
    /**
     * 定位地址
     */
    private String locationAddress;

    private String c1;

    private String c2;

    private String c3;

    private String c4;

    private String c5;

    private String c6;

    private String c7;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "createTime desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
