package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
/**
 * 表单项目模板配置
 *
 * <AUTHOR>
 * @since 2024-12-12 10:54:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PROJECT_TEMPLATE_FIELD")
@Entity
@Table(name = "SYT_FORM_PROJECT_TEMPLATE_FIELD")
public class FormProjectTemplate<PERSON>ield implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 控件类型
     */
    @Column(name = "CONTROL_TYPE")
    @TableField("CONTROL_TYPE")
    private String controlType;

    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 默认值
     */
    @Column(name = "DEFAULT_VAL")
    @TableField("DEFAULT_VAL")
    private String defaultVal;

    /**
     * 默认值提取字段（用户信息）
     */
    @Column(name = "DEFAULT_VAL_FIELD")
    @TableField("DEFAULT_VAL_FIELD")
    private String defaultValField;

    /**
     * 加载数据类型
     */
    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;

    /**
     * 加载数据URL
     */
    @Column(name = "LOAD_DATA_URL_ID")
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;

}
