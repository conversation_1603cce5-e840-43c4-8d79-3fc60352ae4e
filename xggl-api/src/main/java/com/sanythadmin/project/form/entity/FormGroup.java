package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.form.enums.InfoType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 表单信息组
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_GROUP")
@Entity
@Table(name = "SYT_FORM_GROUP")
public class FormGroup implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 组名称
     */
    @NotEmpty(message = "组名称不能为空")
    @Column(name = "GROUP_NAME")
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 是否列表（1：是，0：否）
     */
    @NotNull(message = "是否列表不能为空")
    @Column(name = "LIST_FLAG")
    @TableField("LIST_FLAG")
    private JudgeMark listFlag;

    /**
     * 最少条数
     */
    @Column(name = "MIN_NUM")
    @TableField("MIN_NUM")
    private Long minNum;

    /**
     * 最多条数
     */
    @Column(name = "MAX_NUM")
    @TableField("MAX_NUM")
    private Long maxNum;

    /**
     * 序号
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 引用字典组ID
     */
    @Column(name = "DIC_GROUP_ID")
    @TableField("DIC_GROUP_ID")
    private String dicGroupId;

    /**
     * 表单项目ID
     */
    @NotEmpty(message = "项目ID不能为空")
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 业务类型
     */
    @NotEmpty(message = "业务类型不能为空")
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 1: 用户基础字段， 2: 用户多条信息组,  3: 申请表单字段
     */
    @Column(name = "INFO_TYPE")
    @TableField("INFO_TYPE")
    private InfoType infoType;

    /**
     * 表单模板配置
     */
    @TableField(exist = false)
    @Transient
    private List<FormTemplateField> formTemplateFields;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
