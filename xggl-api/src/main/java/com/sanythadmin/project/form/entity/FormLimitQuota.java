package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.form.enums.QuotaType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 限制名额配置
 *
 * <AUTHOR>
 * @since 2024-09-25 10:25:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_LIMIT_QUOTA")
@Entity
@Table(name = "SYT_FORM_LIMIT_QUOTA")
public class FormLimitQuota implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @NotEmpty(message = "项目ID不能为空")
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @NotEmpty(message = "项目年份不能为空")
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 名额或百分比例
     */
    @Column(name = "QUOTA")
    @TableField("QUOTA")
    private Integer quota;

    /**
     * 名额类型（名额， 比例）
     */
    @Column(name = "QUOTA_TYPE", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("QUOTA_TYPE")
    private String quotaType;

    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;
}
