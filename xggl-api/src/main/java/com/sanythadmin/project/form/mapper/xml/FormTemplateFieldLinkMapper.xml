<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormTemplateFieldLinkMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, FIELD_EN, FIELD_VAL, SHOW_FLAG, LINK_FIELD_DATA_TYPE, LINK_FIELD_DATA_URL, FIELD_ID, LINK_FIELD, GROUP_ID, FIELD_VAL_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.fieldEn != null">
                        AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
                    </if>
                    <if test="param.fieldVal != null">
                        AND a.FIELD_VAL LIKE concat(concat('%',#{param.fieldVal), '%')
                    </if>
                    <if test="param.showFlag != null">
                        AND a.SHOW_FLAG = #{param.showFlag}
                    </if>
                    <if test="param.linkFieldDataType != null">
                        AND a.LINK_FIELD_DATA_TYPE LIKE concat(concat('%',#{param.linkFieldDataType), '%')
                    </if>
                    <if test="param.linkFieldDataUrl != null">
                        AND a.LINK_FIELD_DATA_URL LIKE concat(concat('%',#{param.linkFieldDataUrl), '%')
                    </if>
                    <if test="param.fieldId != null">
                        AND a.FIELD_ID LIKE concat(concat('%',#{param.fieldId), '%')
                    </if>
                    <if test="param.linkField != null">
                        AND a.LINK_FIELD LIKE concat(concat('%',#{param.linkField), '%')
                    </if>
                    <if test="param.groupId != null">
                        AND a.GROUP_ID LIKE concat(concat('%',#{param.groupId), '%')
                    </if>
                    <if test="param.fieldValId != null">
                        AND a.FIELD_VAL_ID LIKE concat(concat('%',#{param.fieldValId), '%')
                    </if>
    </sql>
</mapper>
