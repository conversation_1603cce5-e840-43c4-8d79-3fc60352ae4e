package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.mapper.*;
import com.sanythadmin.project.form.service.FormTypeService;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.service.WorkflowService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 通用表单业务类型Service实现
 *
 * <AUTHOR>
 * @since 2024-07-01 16:53:19
 */
@Service
public class FormTypeServiceImpl extends ServiceImpl<FormTypeMapper, FormType> implements FormTypeService {
    @Resource
    private FormTypeMapper formTypeMapper;
    @Resource
    private FormProjectMapper formProjectMapper;
    @Resource
    private FormGroupMapper formGroupMapper;
    @Resource
    private FormTemplateFieldMapper formTemplateFieldMapper;
    @Resource
    private FormTemplateFieldLinkMapper formTemplateFieldLinkMapper;
    @Resource
    private WorkflowService workflowService;

    @Transactional
    @Override
    public void edit(FormType object) {
        if (object.getSort() == null) {
            SortHelper<FormType> sortHelper = new SortHelper<>(this, "sort");
            object.setSort(sortHelper.next());
        }

        object.setType(object.getType().toUpperCase());
        FormType type = formTypeMapper.selectOne(new LambdaQueryWrapper<FormType>().eq(FormType::getType, object.getType()));
        if (type != null) {
            if (!StringUtils.hasText(object.getId()) || !Objects.equals(type.getId(), object.getId()))
                AssertUtil.throwMessage("业务类型已存在");
        }

        if (StringUtils.hasText(object.getId())) {
            FormType formType = getById(object.getId());
            if (!Objects.equals(formType.getYear(), object.getYear())) {
                List<FormProject> list = formProjectMapper.selectList(new LambdaQueryWrapper<FormProject>()
                        .eq(FormProject::getType, object.getType()));
                if (!CollectionUtils.isEmpty(list)) {
                    list.forEach(item -> {
                        UpdateWrapper<FormProject> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.set("year", object.getYear());
                        updateWrapper.eq("id", item.getId());
                        formProjectMapper.update(null, updateWrapper);
                        Map<String, String> groupIdMap = copyGroup(item.getId(), object.getYear(), formType.getYear());
                        Map<String, String> fieldIdMap = copyField(item.getId(), object.getYear(), formType.getYear(), groupIdMap);
                        copyFieldLink(fieldIdMap, groupIdMap);
                        Workflow workflow = workflowService.getOne(new LambdaQueryWrapper<Workflow>()
                                .eq(Workflow::getProjectId, item.getId()).eq(Workflow::getYear, formType.getYear()));
                        if (workflow != null)
                            workflowService.copyWorkflow(workflow, item.getId(), object.getYear());
                    });
                }
            }
            formTypeMapper.updateById(object);
        } else {
            formTypeMapper.insert(object);
        }
    }

    private Map<String, String> copyGroup(String projectId, String newYear, String oldYear) {
        Map<String, String> groupIdMap = new HashMap<>();
        List<FormGroup> groups = formGroupMapper.selectList(new LambdaQueryWrapper<FormGroup>()
                .eq(FormGroup::getProjectId, projectId).eq(FormGroup::getYear, oldYear));
        if (!CollectionUtils.isEmpty(groups)) {
            Long count = formGroupMapper.selectCount(new LambdaQueryWrapper<FormGroup>()
                    .eq(FormGroup::getProjectId, projectId).eq(FormGroup::getYear, newYear));
            if (count == null || count == 0) {
                groups.forEach(group -> {
                    String groupId = group.getId();
                    group.setId(null);
                    group.setYear(newYear);
                    formGroupMapper.insert(group);
                    groupIdMap.put(groupId, group.getId());
                });
            }
        }
        return groupIdMap;
    }

    private Map<String, String> copyField(String projectId, String newYear, String oldYear, Map<String, String> groupIdMap) {
        Map<String, String> fieldIdMap = new HashMap<>();
        List<FormTemplateField> fields = formTemplateFieldMapper.selectList(new LambdaQueryWrapper<FormTemplateField>()
                .eq(FormTemplateField::getProjectId, projectId).eq(FormTemplateField::getYear, oldYear));
        if (!CollectionUtils.isEmpty(fields)) {
            Long count = formTemplateFieldMapper.selectCount(new LambdaQueryWrapper<FormTemplateField>()
                    .eq(FormTemplateField::getProjectId, projectId).eq(FormTemplateField::getYear, newYear));
            if (count == null || count == 0) {
                fields.forEach(field -> {
                    String fieldId = field.getId();
                    field.setId(null);
                    field.setYear(newYear);
                    field.setGroupId(groupIdMap.get(field.getGroupId()));
                    formTemplateFieldMapper.insert(field);
                    fieldIdMap.put(fieldId, field.getId());
                });
            }
        }
        return fieldIdMap;
    }

    private void copyFieldLink(Map<String, String> fieldIdMap, Map<String, String> groupIdMap) {
        List<FormTemplateFieldLink> fieldLinks = formTemplateFieldLinkMapper.selectList(new LambdaQueryWrapper<FormTemplateFieldLink>()
                .in(FormTemplateFieldLink::getFieldId, fieldIdMap.keySet()));
        if (!CollectionUtils.isEmpty(fieldLinks)) {
            fieldLinks.forEach(fieldLink -> {
                fieldLink.setId(null);
                fieldLink.setFieldId(fieldIdMap.get(fieldLink.getFieldId()));
                fieldLink.setGroupId(groupIdMap.get(fieldLink.getGroupId()));
                formTemplateFieldLinkMapper.insert(fieldLink);
            });
        }
    }
}
