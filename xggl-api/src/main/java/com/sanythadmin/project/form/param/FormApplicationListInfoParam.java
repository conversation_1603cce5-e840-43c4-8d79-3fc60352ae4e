package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 表单列表申请记录查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormApplicationListInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 表单项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    private String c1;

    private String c2;

    private String c3;

    private String c4;

    private String c5;

    private String c6;

    private String c7;

    private String c8;

    private String c9;

    /**
     * 关联申请记录ID
     */
    @QueryField(type = QueryType.EQ)
    private String applicationId;

    /**
     * 列表信息组ID
     */
    @QueryField(type = QueryType.EQ)
    private String groupId;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
