package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 通用表单业务类型查询参数
 *
 * <AUTHOR>
 * @since 2024-07-01 16:53:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormTypeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 业务名称
     */
    private String name;

    /**
     * 业务类型
     */
    private String type;

    /**
     * 年份
     */
    private String year;

    /**
     * 用户类型
     */
    @QueryField(type = QueryType.EQ)
    private UserType userType;

    @Override
    public String getSort() {
        return "sort desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
