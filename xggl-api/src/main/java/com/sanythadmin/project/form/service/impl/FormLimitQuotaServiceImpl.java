package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.form.entity.FormApprovalResult;
import com.sanythadmin.project.form.entity.FormLimitQuota;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.enums.QuotaType;
import com.sanythadmin.project.form.mapper.FormApprovalResultMapper;
import com.sanythadmin.project.form.mapper.FormLimitQuotaMapper;
import com.sanythadmin.project.form.param.FormApprovalResultParam;
import com.sanythadmin.project.form.service.FormLimitQuotaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 限制名额配置Service实现
 *
 * <AUTHOR>
 * @since 2024-09-25 10:25:42
 */
@RequiredArgsConstructor
@Service
public class FormLimitQuotaServiceImpl extends ServiceImpl<FormLimitQuotaMapper, FormLimitQuota> implements FormLimitQuotaService {

    private final FormLimitQuotaMapper formLimitQuotaMapper;

    private final FormApprovalResultMapper approvalRecordMapper;

    private final UserInfoMapper userInfoMapper;

    @Override
    public void checkQuota(FormProject project, UserInfo userInfo) {
        List<FormLimitQuota> limitQuotas = formLimitQuotaMapper.selectList(new LambdaQueryWrapper<FormLimitQuota>()
                .eq(FormLimitQuota::getProjectId, project.getId())
                .eq(FormLimitQuota::getYear, project.getYear()));
        if (CollectionUtils.isEmpty(limitQuotas))
            return;
        List<String> fieldList = Arrays.asList("bjid", "zyid", "xyid", "njid", "pyccid");
        for (FormLimitQuota limitQuota : limitQuotas) {
            boolean flag = true;
            for (String fieldName : fieldList) {
                Object limitQuotaVal = CommonUtil.getValue(fieldName, limitQuota);
                Object userInfoVal = CommonUtil.getValue(fieldName, userInfo);
                if (limitQuotaVal != null && !Objects.equals(limitQuotaVal, userInfoVal)) {
                    flag = false;
                    break;
                }
            }

            if (!flag) continue;
            long resultCount = approvalResultCount(project, limitQuota);
            Integer limit = limitQuota.getQuota();
            if (Objects.equals(QuotaType.BL.getText(), limitQuota.getQuotaType())) {
                for (String fieldName : fieldList) {
                    Object limitQuotaVal = CommonUtil.getValue(fieldName, limitQuota);
                    Object userInfoVal = CommonUtil.getValue(fieldName, userInfo);
                    if (limitQuotaVal != null && Objects.equals(limitQuotaVal, userInfoVal)) {
                        UserInfoParam param = new UserInfoParam();
                        param.setUserType(userInfo.getUserType());
                        CommonUtil.setValue(param, fieldName, limitQuotaVal);
                        PageParam<UserInfo, UserInfoParam> page = new PageParam<>(param);
                        long count = userInfoMapper.selectCount(page.getWrapper());
                        limit = (int) count * limit / 100;
                        break;
                    }
                }
            }

            if (resultCount >= limit)
                AssertUtil.throwMessage("已达限制名额上限，不可审批");
        }
    }

    private long approvalResultCount(FormProject project, FormLimitQuota quota) {
        FormApprovalResultParam param = new FormApprovalResultParam();
        param.setProjectId(project.getId());
        param.setYear(project.getYear());
        param.setPyccid(quota.getPyccid());
        param.setNjid(quota.getNjid());
        param.setXyid(quota.getXyid());
        param.setZyid(quota.getZyid());
        param.setBjid(quota.getBjid());
        PageParam<FormApprovalResult, FormApprovalResultParam> page = new PageParam<>(param);
        return approvalRecordMapper.selectCount(page.getWrapper());
    }
}
