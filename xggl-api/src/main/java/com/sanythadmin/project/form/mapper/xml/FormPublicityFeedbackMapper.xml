<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormPublicityFeedbackMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONTENT, ITEM_ID, PARENT_ID, NEXT_ID, ATTACHMENT, USERNAME, REAL_NAME, CREATE_DATE, ROOT_ID, READ_FLAG
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.content != null">
                        AND a.CONTENT LIKE concat(concat('%',#{param.content), '%')
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
                    </if>
                    <if test="param.parentId != null">
                        AND a.PARENT_ID LIKE concat(concat('%',#{param.parentId), '%')
                    </if>
                    <if test="param.nextId != null">
                        AND a.NEXT_ID LIKE concat(concat('%',#{param.nextId), '%')
                    </if>
                    <if test="param.attachment != null">
                        AND a.ATTACHMENT LIKE concat(concat('%',#{param.attachment), '%')
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
                    </if>
                    <if test="param.realName != null">
                        AND a.REAL_NAME LIKE concat(concat('%',#{param.realName), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.rootId != null">
                        AND a.ROOT_ID LIKE concat(concat('%',#{param.rootId), '%')
                    </if>
                    <if test="param.readFlag != null">
                        AND a.READ_FLAG = #{param.readFlag}
                    </if>
    </sql>
</mapper>
