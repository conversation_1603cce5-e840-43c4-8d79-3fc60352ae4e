package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.form.enums.InfoType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 表单模板配置查询参数
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormTemplateFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 字段英文名
     */
    @QueryField(type = QueryType.IN_STR)
    private String fieldEn;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 1: 用户基础字段， 2: 用户多条信息组,  3: 申请表单字段
     */
    @QueryField(type = QueryType.IN_LIST)
    private List<InfoType> infoType;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark required;


    /**
     * 控件类型
     */
    @QueryField(type = QueryType.IN_STR)
    private String controlType;

    /**
     * 关联申请表单组 ID
     */
    @QueryField(type = QueryType.EQ)
    private String groupId;

    /**
     * 是否显示（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark showFlag;

    /**
     * 是否可编辑（1：是,  0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark editFlag;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
