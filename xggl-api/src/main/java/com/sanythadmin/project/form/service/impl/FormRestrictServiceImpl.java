package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormRestrict;
import com.sanythadmin.project.form.mapper.FormRestrictMapper;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.restrict.service.RestrictCheckService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 表单限制条件Service实现
 *
 * <AUTHOR>
 * @since 2024-09-06 15:54:27
 */
@Service
public class FormRestrictServiceImpl extends ServiceImpl<FormRestrictMapper, FormRestrict> implements FormRestrictService {
    @Resource
    private FormRestrictMapper formRestrictMapper;

    @Override
    public String restrictCheck(String projectId, UserInfo userInfo) {
        return restrictCheck(projectId, userInfo, true);
    }

    @Override
    public String restrictCheck(String projectId, UserInfo userInfo, boolean checkAll) {
        List<FormRestrict> formRestricts = formRestrictMapper.selectList(new LambdaQueryWrapper<FormRestrict>()
                .eq(FormRestrict::getProjectId, projectId));
        if (!CollectionUtils.isEmpty(formRestricts)) {
            List<String> errorList = new ArrayList<>();
            for (FormRestrict restrict : formRestricts) {
                RestrictCheckService restrictCheckService = SpringContextUtil.getBean(restrict.getModule()
                        , RestrictCheckService.class);
                String error = restrictCheckService.check(userInfo, restrict);
                if (StringUtils.hasText(error)) {
                    errorList.add(error);
                    if (!checkAll) break;
                }
            }

            if (!CollectionUtils.isEmpty(errorList))
                return CommonUtil.strJoin(errorList);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> groupCountByProject(FormProject... projects) {
        return formRestrictMapper.selectMaps(new QueryWrapper<FormRestrict>()
                .select("PROJECT_ID", "COUNT(*) AS TOTAL")
                .in("PROJECT_ID", Arrays.stream(projects).map(FormProject::getId).toList())
                .groupBy("PROJECT_ID"));
    }
}
