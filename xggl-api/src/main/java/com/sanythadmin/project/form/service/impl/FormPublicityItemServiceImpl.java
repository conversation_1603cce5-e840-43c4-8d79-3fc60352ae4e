package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.form.dto.FormPublicityItemDTO;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormPublicityContent;
import com.sanythadmin.project.form.entity.FormPublicityItem;
import com.sanythadmin.project.form.mapper.FormPublicityContentMapper;
import com.sanythadmin.project.form.mapper.FormPublicityItemMapper;
import com.sanythadmin.project.form.service.FormApplicationInfoService;
import com.sanythadmin.project.form.service.FormPublicityItemService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 公示项目Service实现
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Service
public class FormPublicityItemServiceImpl extends ServiceImpl<FormPublicityItemMapper, FormPublicityItem> implements FormPublicityItemService {
    @Resource
    private FormPublicityItemMapper mapper;
    @Resource
    private FormPublicityContentMapper formPublicityContentMapper;
    @Resource
    private FormApplicationInfoService formApplicationInfoService;

    @Transactional
    @Override
    public void edit(FormPublicityItemDTO dto) {
        SysAccount account = SecurityUtil.getAccount();
        FormPublicityItem formPublicityItem = dto.getFormPublicityItem();
        List<FormApplicationInfo> infos = formApplicationInfoService.approvalList(dto.getApplicationInfoParam(),
                dto.getUserInfoParam(), dto.getApprovalNodeParam(), dto.getCodeXsztParam());
        if (CollectionUtils.isEmpty(infos))
            return;

        UserInfoUtil.codeTextSet(infos);
        formPublicityContentMapper.delete(new LambdaQueryWrapper<FormPublicityContent>()
                .eq(FormPublicityContent::getUsername, account.getUsername())
                .eq(FormPublicityContent::getProjectId, formPublicityItem.getProjectId())
                .eq(FormPublicityContent::getYear, formPublicityItem.getYear())
                .eq(FormPublicityContent::getRoleScope, account.getRole().getRoleScope()));
        mapper.delete(new LambdaQueryWrapper<FormPublicityItem>()
                .eq(FormPublicityItem::getUsername, account.getUsername())
                .eq(FormPublicityItem::getProjectId, formPublicityItem.getProjectId())
                .eq(FormPublicityItem::getYear, formPublicityItem.getYear())
                .eq(FormPublicityItem::getRoleScope, account.getRole().getRoleScope()));
        mapper.insert(formPublicityItem);
        for (FormApplicationInfo info : infos) {
            FormPublicityContent content = new FormPublicityContent();
            content.setUsername(account.getUsername());
            content.setProjectId(formPublicityItem.getProjectId());
            content.setType(formPublicityItem.getType());
            content.setYear(formPublicityItem.getYear());
            content.setRoleScope(account.getRole().getRoleScope());
            content.setXgh(info.getXgh());
            content.setXm(info.getUserInfo().getXm());
            content.setXb(info.getUserInfo().getXb().getText());
            content.setXymc(info.getUserInfo().getXymc());
            content.setZymc(info.getUserInfo().getZymc());
            content.setNjmc(info.getUserInfo().getNjmc());
            content.setBjmc(info.getUserInfo().getBjmc());
            content.setPyccmc(info.getUserInfo().getPyccmc());
            content.setItemId(formPublicityItem.getId());
            formPublicityContentMapper.insert(content);
        }
    }
}
