package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.UserInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单申请记录
 *
 * <AUTHOR>
 * @since 2024-07-02 10:38:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_APPLICATION_INFO")
@Entity
@Table(name = "SYT_FORM_APPLICATION_INFO")
public class FormApplicationInfo extends BaseFormApplicationInfo {
    private static final long serialVersionUID = 1L;

    @Column(name = "C1")
    @TableField("C1")
    private String c1;

    @Column(name = "C2")
    @TableField("C2")
    private String c2;

    @Column(name = "C3")
    @TableField("C3")
    private String c3;

    @Column(name = "C4")
    @TableField("C4")
    private String c4;

    @Column(name = "C5")
    @TableField("C5")
    private String c5;

    @Column(name = "C6")
    @TableField("C6")
    private String c6;

    @Column(name = "C7")
    @TableField("C7")
    private String c7;


    @Column(name = "L1", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L1")
    private String l1;
    @Column(name = "L2", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L2")
    private String l2;
    @Column(name = "L3", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L3")
    private String l3;
    @Column(name = "L4", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L4")
    private String l4;
    @Column(name = "L5", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L5")
    private String l5;
    @Column(name = "L6", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L6")
    private String l6;
    @Column(name = "L7", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L7")
    private String l7;
    @Column(name = "L8", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L8")
    private String l8;
    @Column(name = "L9", columnDefinition = ColumnType.CLOB, nullable = true)
    @TableField("L9")
    private String l9;

    @Override
    public UserInfo getUserInfo() {
        UserInfo userInfo = super.getUserInfo();
        if (userInfo != null) {
            userInfo.setPyccid(getPyccid());
            userInfo.setNjid(getNjid());
            userInfo.setXyid(getXyid());
            userInfo.setZyid(getZyid());
            userInfo.setBjid(getBjid());
        }
        return userInfo;
    }
}
