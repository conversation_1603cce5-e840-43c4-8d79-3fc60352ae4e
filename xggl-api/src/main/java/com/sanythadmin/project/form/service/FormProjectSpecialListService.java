package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormProjectSpecialList;
import com.sanythadmin.project.form.param.FormProjectSpecialListParam;
import com.sanythadmin.project.selector.entity.SelectorData;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * 特殊名单人员Service
 *
 * <AUTHOR>
 * @since 2024-07-09 11:36:55
 */
public interface FormProjectSpecialListService extends IService<FormProjectSpecialList> {
    public void add(String id, List<SelectorData> specialListParams);

    public void importTemplate(HttpServletResponse response);

    public void importData(String projectId, MultipartFile file);

    public List<FormProjectSpecialList> list(FormProjectSpecialListParam param);

    public PageResult<FormProjectSpecialList> queryPage(FormProjectSpecialListParam param);

    public List<Map<String, Object>> groupCountByProject(FormProject... projects);
}
