package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 申请审批节点
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_APPROVAL_NODE")
@Entity
@Table(name = "SYT_FORM_APPROVAL_NODE")
public class FormApprovalNode extends BaseApprovalNode {

}
