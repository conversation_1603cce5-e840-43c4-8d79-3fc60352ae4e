package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公示意见反馈查询参数
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormPublicityFeedbackParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 问题或回复内容
     */
    private String content;

    /**
     * 公示项目ID
     */
    private String itemId;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 子节点ID
     */
    private String nextId;

    /**
     * 附件信息
     */
    private String attachment;

    /**
     * 用户名
     */
    private String username;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 根节点ID
     */
    private String rootId;

    /**
     * 是否已读（0：否，1: 是）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark readFlag;

}
