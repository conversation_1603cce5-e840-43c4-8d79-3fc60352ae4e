package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.form.enums.InfoType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 表单模板配置
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_TEMPLATE_FIELD")
@Entity
@Table(name = "SYT_FORM_TEMPLATE_FIELD")
public class FormTemplateField implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 1: 用户基础字段， 2: 用户多条信息组,  3: 申请表单字段
     */
    @Column(name = "INFO_TYPE")
    @TableField("INFO_TYPE")
    private InfoType infoType;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @Column(name = "REQUIRED")
    @TableField("REQUIRED")
    private JudgeMark required;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 控件类型
     */
    @Column(name = "CONTROL_TYPE")
    @TableField("CONTROL_TYPE")
    private String controlType;

    /**
     * 正则验证表达式
     */
    @Column(name = "REG_EXPRESSION")
    @TableField("REG_EXPRESSION")
    private String regExpression;

    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 关联申请表单组 ID
     */
    @Column(name = "GROUP_ID")
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 是否显示（1：是,  0：否）
     */
    @Column(name = "SHOW_FLAG")
    @TableField("SHOW_FLAG")
    private JudgeMark showFlag;

    /**
     * 默认值
     */
    @Column(name = "DEFAULT_VAL")
    @TableField("DEFAULT_VAL")
    private String defaultVal;

    /**
     * 默认值提取字段（用户信息）
     */
    @Column(name = "DEFAULT_VAL_FIELD")
    @TableField("DEFAULT_VAL_FIELD")
    private String defaultValField;

    /**
     * 是否可编辑（1：是,  0：否）
     */
    @Column(name = "EDIT_FLAG")
    @TableField("EDIT_FLAG")
    private JudgeMark editFlag;
    /**
     * 加载数据类型（来自公共码表code）
     */
    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;
    /**
     * 下拉控件加载数据URL ID
     */
    @Column(name = "LOAD_DATA_URL_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;

    /**
     * 关联信息集合
     */
    @Transient
    @TableField(exist = false)
    private List<FormTemplateFieldLink> fieldLinks;

    public FormTemplateField() {

    }

    public FormTemplateField(String projectId, String year, String fieldEn, String fieldZh, JudgeMark required) {
        this.projectId = projectId;
        this.year = year;
        this.fieldEn = fieldEn;
        this.fieldZh = fieldZh;
        this.required = required;
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
