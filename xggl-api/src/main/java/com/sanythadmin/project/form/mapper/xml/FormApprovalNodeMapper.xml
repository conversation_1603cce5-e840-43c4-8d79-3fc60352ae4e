<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormApprovalNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, WORKFLOW_ID, NODE_ID, CREATE_TIME, UPDATE_TIME, APPLICATION_ID, NEXT_NODE_RESULT, PRE_NODE_RESULT, RESULT, SORT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.workflowId != null">
            AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
        </if>
        <if test="param.nodeId != null">
            AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
        </if>
        <if test="param.applicationId != null">
            AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
        </if>
        <if test="param.nextNodeResult != null">
            AND a.NEXT_NODE_RESULT LIKE concat(concat('%',#{param.nextNodeResult), '%')
        </if>
        <if test="param.preNodeResult != null">
            AND a.PRE_NODE_RESULT LIKE concat(concat('%',#{param.preNodeResult), '%')
        </if>
        <if test="param.result != null">
            AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
        </if>
        <if test="param.sort != null">
            AND a.SORT = #{param.sort}
        </if>
    </sql>
</mapper>
