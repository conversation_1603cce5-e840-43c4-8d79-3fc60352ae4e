package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单限制条件查询参数
 *
 * <AUTHOR>
 * @since 2024-09-06 15:54:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormRestrictParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * 限制条件别名
     */
    @QueryField(type = QueryType.EQ)
    private String conditionKey;
    /**
     * 条件ID
     */
    @QueryField(type = QueryType.EQ)
    private String conditionId;
    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;
}
