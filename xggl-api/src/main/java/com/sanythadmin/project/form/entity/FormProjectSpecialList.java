package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 特殊名单人员
 *
 * <AUTHOR>
 * @since 2024-07-09 11:36:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PROJECT_SPECIAL_LIST")
@Entity
@Table(name = "SYT_FORM_PROJECT_SPECIAL_LIST")
public class FormProjectSpecialList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @TableField("REMARK")
    private String remark;

    /**
     * 用户信息
     */
    @TableField(exist = false)
    @Transient
    private BaseUserInfo userInfo;
}
