package com.sanythadmin.project.form.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.*;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.dto.ApplyInfoImportDTO;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormProjectSpecialList;
import com.sanythadmin.project.form.entity.FormTemplateField;
import com.sanythadmin.project.form.enums.InfoType;
import com.sanythadmin.project.form.param.FormProjectParam;
import com.sanythadmin.project.form.param.FormProjectSpecialListParam;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;
import com.sanythadmin.project.form.service.*;
import com.sanythadmin.project.form.vo.FormProjectVO;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.NodeType;
import com.sanythadmin.project.workflow.service.WorkflowConditionDetailService;
import com.sanythadmin.project.workflow.service.WorkflowNodeApproverService;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;


/**
 * 表单管理模块/表单项目控制器
 *
 * <AUTHOR>
 * @since 2024-07-02 10:07:26
 */
@Slf4j
@RestController
@RequestMapping("/api/form/form-project")
public class FormProjectController extends BaseController {
    @Resource
    private FormProjectService formProjectService;
    @Resource
    private WorkflowService workflowService;
    @Resource
    private FormTemplateFieldService formTemplateFieldService;
    @Resource
    private WorkflowNodeService workflowNodeService;
    @Resource
    private WorkflowConditionDetailService conditionDetailService;
    @Resource
    private WorkflowNodeApproverService nodeApproverService;
    @Resource
    private FormApplicationInfoService applicationInfoService;
    @Resource
    private FormProjectSpecialListService specialListService;
    @Resource
    private FormRestrictService formRestrictService;

    /**
     * 分页查询表单项目（权限标识：form:formProject:list）
     */
    @PreAuthorize("hasAuthority('form:formProject:list')")
    @GetMapping("/page")
    public PageResult<FormProjectVO> page(FormProjectParam param) {
        setQueryScope(param);
        PageParam<FormProject, FormProjectParam> page = new PageParam<>(param);
        page = formProjectService.page(page, page.getWrapper());
        List<FormProject> records = page.getRecords();
        List<FormProjectVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            FormProject[] projects = records.toArray(new FormProject[]{});
            List<Map<String, Object>> maps = specialListService.groupCountByProject(projects);
            List<Map<String, Object>> maps1 = formRestrictService.groupCountByProject(projects);
            records.forEach(record -> {
                FormProjectVO vo = new FormProjectVO();
                BeanUtils.copyProperties(record, vo);
                vo.setSpecialListCount(0L);
                vo.setRestrictCount(0L);
                if (!Objects.isNull(maps)) {
                    List<Map<String, Object>> mapList = maps.stream().filter(map -> Objects.equals(record.getId(), String.valueOf(map.get("PROJECT_ID"))) && Objects.equals(record.getYear(), String.valueOf(map.get("YEAR")))).toList();
                    if (!CollectionUtils.isEmpty(mapList))
                        vo.setSpecialListCount(Long.valueOf(String.valueOf(mapList.get(0).get("TOTAL"))));
                }

                if (!Objects.isNull(maps1)) {
                    List<Map<String, Object>> mapList = maps1.stream().filter(map -> Objects.equals(record.getId(), String.valueOf(map.get("PROJECT_ID")))).toList();
                    if (!CollectionUtils.isEmpty(mapList))
                        vo.setRestrictCount(Long.valueOf(String.valueOf(mapList.get(0).get("TOTAL"))));
                }
                list.add(vo);
            });
        }
        return new PageResult<>(list, page.getTotal());
    }

    private void setQueryScope(FormProjectParam param) {
        if (Objects.equals(SecurityUtil.getRole().getRoleScope(), Constants.ROLE_SCOPE_BY))
            param.setCzr(SecurityUtil.getAccount().getUsername());
    }

    /**
     * 查询全部表单项目（权限标识：form:formProject:list）
     */
    @PreAuthorize("hasAuthority('form:formProject:list')")
    @GetMapping()
    public List<FormProject> list(FormProjectParam param) {
        param.setSpzt(ConstantsWorkflow.STATE_PASS);
        PageParam<FormProject, FormProjectParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formProjectService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询表单项目（权限标识：form:formProject:list）
     */
    @PreAuthorize("hasAuthority('form:formProject:list')")
    @GetMapping("/{id}")
    public FormProject get(@PathVariable("id") String id) {
        return formProjectService.getById(id);
    }

    /**
     * 添加或修改表单项目（权限标识：form:formProject:operation）
     */
    @PreAuthorize("hasAuthority('form:formProject:operation')")
    @OperationLog(module = "表单项目", comments = "保存表单项目")
    @PostMapping("/operation")
    public FormProject save(@RequestBody FormProject formProject) {
        formProjectService.edit(formProject);
        return formProject;
    }

    /**
     * 项目审核（权限标识：form:formProject:approve）
     *
     * @param formProject
     */
    @PreAuthorize("hasAuthority('form:formProject:approve')")
    @OperationLog(module = "表单项目", comments = "审核表单项目")
    @PostMapping("/approve")
    public void approve(@RequestBody FormProject formProject) {
        FormProject project = formProjectService.getById(formProject.getId());
        if (project == null) AssertUtil.throwMessage("项目不存在");
        project.setSpzt(formProject.getSpzt());
        formProjectService.updateById(project);
    }

    /**
     * 项目发布（权限标识：form:formProject:operation）
     *
     * @param id
     */
    @PreAuthorize("hasAuthority('form:formProject:operation')")
    @OperationLog(module = "表单项目", comments = "项目发布")
    @PostMapping("/{id}/publish")
    public void publish(@PathVariable("id") String id) {
        FormProject project = formProjectService.getById(id);
        FormTemplateFieldParam param = new FormTemplateFieldParam();
        param.setProjectId(id);
        param.setType(project.getType());
        param.setYear(project.getYear());
        param.setInfoType(Collections.singletonList(InfoType.apply));
        List<FormTemplateField> fields = formTemplateFieldService.list(param);
        if (CollectionUtils.isEmpty(fields)) AssertUtil.throwMessage("申请表单未配置");
        Workflow workflow = workflowService.getOne(new LambdaQueryWrapper<Workflow>().eq(Workflow::getProjectId, id).eq(Workflow::getYear, project.getYear()));
        if (workflow == null) AssertUtil.throwMessage("工作流未配置");
        project.setSfscmb(JudgeMark.YES);
        formProjectService.updateById(project);
    }

    /**
     * 批量删除表单项目（权限标识：form:formProject:remove）
     */
    @PreAuthorize("hasAuthority('form:formProject:remove')")
    @OperationLog(module = "表单项目", comments = "批量删除表单项目")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formProjectService.removeByIds(ids);
    }


    /**
     * 根据项目查询工作流（权限标识：form:formProject:list）
     */
    @PreAuthorize("hasAuthority('form:formProject:list')")
    @GetMapping("/{id}/workflow")
    public Workflow workflow(@PathVariable("id") String id) {
        FormProject project = formProjectService.getById(id);
        return workflowService.getOne(new LambdaQueryWrapper<Workflow>().eq(Workflow::getProjectId, id).eq(Workflow::getYear, project.getYear()));
    }

    /**
     * 项目工作流添加或修改（权限标识：form:formProject:operation）
     */
    @PreAuthorize("hasAuthority('form:formProject:operation')")
    @OperationLog(module = "表单项目", comments = "工作流编辑")
    @PostMapping("/{id}/workflowEdit")
    public void workflowEdit(@PathVariable("id") String id, @RequestBody WorkflowDTO workflowDTO) {
        FormProject project = formProjectService.getById(id);
        workflowService.copyWorkflow(workflowDTO, id, project.getYear(), project.getXmmc());
        workflowService.editWorkflow(workflowDTO);
    }


    /**
     * 历史数据导入模板
     */
    @GetMapping("/{id}/dataImportTemplate")
    public void dataImportTemplate(@PathVariable("id") String id, HttpServletResponse response) {
        try {
            FormProject project = formProjectService.getById(id);
            List<FormTemplateField> fields = getFormTemplateFields(project);
            List<String> titles = new ArrayList<>();
            for (FormTemplateField field : fields) {
                String fieldZh = field.getFieldZh();
                titles.add(field.getRequired().getMark() ? fieldZh + "(必填)" : fieldZh);
            }
            EasyExcelHelper.exportExcel(null, titles.toArray(new String[]{}), null, response, null);
        } catch (Exception e) {
            log.error("导出数据失败", e);
        }
    }

    private List<FormTemplateField> getFormTemplateFields(FormProject project) {
        List<FormTemplateField> fields = new ArrayList<>();
        fields.add(new FormTemplateField(project.getId(), null, "xgh", Objects.equals(UserType.STUDENT, project.getUserType()) ? "学号" : "工号", JudgeMark.YES));
        fields.add(new FormTemplateField(project.getId(), null, null, "姓名", JudgeMark.NO));
        FormTemplateFieldParam param = new FormTemplateFieldParam();
        param.setProjectId(project.getId());
        param.setShowFlag(JudgeMark.YES);
        param.setInfoType(Collections.singletonList(InfoType.apply));
        List<FormTemplateField> formTemplateFields = formTemplateFieldService.list(param);
        if (!CollectionUtils.isEmpty(formTemplateFields)) {
            List<String> excludeControlType = Arrays.asList("imageUpload", "smoothSignature");
            formTemplateFields.removeIf(field -> excludeControlType.contains(field.getControlType()));
            if (!CollectionUtils.isEmpty(formTemplateFields))
                fields.addAll(formTemplateFields);
        }
        return fields;
    }


    /**
     * 历史数据导入（权限标识：form:formProject:dataImport）
     */
    @PreAuthorize("hasAuthority('form:formProject:dataImport')")
    @OperationLog(module = "表单项目", comments = "历史数据导入")
    @PostMapping("/{id}/dataImport")
    public void dataImport(@PathVariable("id") String id, @RequestParam(name = "file") MultipartFile file) {
        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        Map<String, UserInfo> userInfoMap = new ConcurrentHashMap<>();
        FormProject project = formProjectService.getById(id);
        List<FormTemplateField> fields = getFormTemplateFields(project);
        LinkedHashMap<Integer, FormApplicationInfo> infoMap = readData(file, fields, errors, project);
        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }

        SysAccount account = SecurityUtil.getAccount();
        List<WorkflowNode> approverNodes = workflowNodeService.nodeListByFormInfoAndApprover(id, project.getYear(), CommonUtil.appendComma(account.getUsername(), account.getRole().getId()));
        if (CollectionUtils.isEmpty(approverNodes)) AssertUtil.throwMessage("无审核权限");
        String workflowId = approverNodes.get(0).getWorkflowId();
        List<WorkflowConditionDetail> conditionDetails = conditionDetailService.listByWorkflow(workflowId);
        List<WorkflowNode> nodes = workflowNodeService.listByWorkflow(workflowId);
        List<WorkflowNodeApprover> approverList = nodeApproverService.listByWorkflow(workflowId);
        FormProjectSpecialListParam specialListParam = new FormProjectSpecialListParam();
        specialListParam.setProjectId(project.getId());
        specialListParam.setYear(project.getYear());
        List<FormProjectSpecialList> specialListList = specialListService.list(specialListParam);
        List<String> specialListStudent = new ArrayList<>();
        if (!CollectionUtils.isEmpty(specialListList))
            specialListStudent = specialListList.stream().map(FormProjectSpecialList::getXgh).toList();
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        List<CompletableFuture<UserInfo>> futures = new ArrayList<>();
        for (Map.Entry<Integer, FormApplicationInfo> entry : infoMap.entrySet()) {
            Integer line = entry.getKey();
            FormApplicationInfo applicationInfo = entry.getValue();
            ApplyInfoImportDTO dto = new ApplyInfoImportDTO(null, applicationInfo, project, nodes, approverNodes,
                    conditionDetails, approverList, fields, specialListStudent, null);
            CompletableFuture<UserInfo> future = applicationInfoService.dataImportCheck(dto, executor)
                    .handle((result, ex) -> {
                        if (ex != null) {
                            String message = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                            errors.add(new ExcelImportError(line, applicationInfo.getXgh(), message));
                        } else {
                            userInfoMap.put(result.getXgh(), result);
                        }
                        return null;
                    });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }

        for (Map.Entry<Integer, FormApplicationInfo> entry : infoMap.entrySet()) {
            FormApplicationInfo applicationInfo = entry.getValue();
            ApplyInfoImportDTO dto = new ApplyInfoImportDTO(account, applicationInfo, project, nodes, approverNodes,
                    conditionDetails, approverList, fields, specialListStudent, userInfoMap.get(applicationInfo.getXgh()));
            applicationInfoService.dataImport(dto);
        }
    }

    private LinkedHashMap<Integer, FormApplicationInfo> readData(MultipartFile file, List<FormTemplateField> fields, List<ExcelImportError> errors, FormProject project) {
        SheetData sheetData = null;
        try {
            sheetData = EasyExcelHelper.read(file);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        LinkedHashMap<Integer, FormApplicationInfo> applicationInfoMap = new LinkedHashMap<>();
        int length = fields.size();
        List<RowData> rows = sheetData.getRowData();
        for (RowData rowData : rows) {
            FormApplicationInfo info = new FormApplicationInfo();
            Integer rowNumber = rowData.getIndex() + 1;
            HashMap<Integer, String> cellData = rowData.getCellData();
            for (int cellIndex = 0; cellIndex < length; cellIndex++) {
                FormTemplateField field = fields.get(cellIndex);
                if (!StringUtils.hasText(field.getFieldEn())) continue;
                String cellValue = cellData.get(cellIndex);
                if (!StringUtils.hasText(cellValue) && field.getRequired().getMark()) {
                    errors.add(ExcelImportError.data(rowNumber, cellValue, field.getFieldZh() + "不能为空"));
                    continue;
                }
                CommonUtil.setValue(info, field.getFieldEn(), cellValue);
            }
            info.setUserType(project.getUserType());
            info.setSfzc(JudgeMark.NO);
            info.setType(project.getType());
            info.setProjectId(project.getId());
            info.setYear(project.getYear());
            info.setCreateTime(LocalDateTime.now());
            info.setSpjg(ReviewResult.DaiShenPi);
            applicationInfoMap.put(rowNumber, info);
        }
        return applicationInfoMap;
    }
}
