package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.form.enums.CustomType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 审核列表自定义列字段查询参数
 *
 * <AUTHOR>
 * @since 2024-09-04 15:23:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormCustomFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 字段英文名
     */
    private String fielden;

    /**
     * 字段中文名
     */
    private String fieldzh;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 是否显示查询
     */
    @QueryField(type = QueryType.EQ)
    private CustomType customType;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 字段类型来源
     */
    @QueryField(type = QueryType.EQ)
    private Integer infoType;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort asc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
