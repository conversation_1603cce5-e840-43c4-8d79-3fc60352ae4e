package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.service.FormTemplateFieldLinkService;
import com.sanythadmin.project.form.entity.FormTemplateFieldLink;
import com.sanythadmin.project.form.param.FormTemplateFieldLinkParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 表单管理模块/表单字段关联关系控制器
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@RestController
@RequestMapping("/api/form/form-template-field-link")
public class FormTemplateFieldLinkController extends BaseController {
    @Resource
    private FormTemplateFieldLinkService formTemplateFieldLinkService;

    /**
     * 分页查询表单字段关联关系（权限标识：form:formTemplateFieldLink:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateFieldLink:list')")
    @GetMapping("/page")
    public PageResult<FormTemplateFieldLink> page(FormTemplateFieldLinkParam param) {
        PageParam<FormTemplateFieldLink, FormTemplateFieldLinkParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formTemplateFieldLinkService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部表单字段关联关系（权限标识：form:formTemplateFieldLink:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateFieldLink:list')")
    @GetMapping()
    public List<FormTemplateFieldLink> list(FormTemplateFieldLinkParam param) {
        PageParam<FormTemplateFieldLink, FormTemplateFieldLinkParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formTemplateFieldLinkService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询表单字段关联关系（权限标识：form:formTemplateFieldLink:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateFieldLink:list')")
    @GetMapping("/{id}")
    public FormTemplateFieldLink get(@PathVariable("id") String id) {
        return formTemplateFieldLinkService.getById(id);
    }

    /**
     * 添加或修改表单字段关联关系（权限标识：form:formTemplateFieldLink:operation）
     */
    @PreAuthorize("hasAuthority('form:formTemplateFieldLink:operation')")
    @OperationLog(module = "表单字段关联关系", comments = "保存表单字段关联关系")
    @PostMapping("/operation")
    public void save(@RequestBody List<FormTemplateFieldLink> formTemplateFieldLinks) {
        formTemplateFieldLinkService.edit(formTemplateFieldLinks);
    }

    /**
     * 删除字段关联关系（权限标识：form:formTemplateFieldLink:remove）
     */
    @PreAuthorize("hasAuthority('form:formTemplateFieldLink:remove')")
    @OperationLog(module = "表单字段关联关系", comments = "删除字段关联关系")
    @PostMapping("/removeByFieldId")
    public void remove(@RequestBody String fieldId) {
        formTemplateFieldLinkService.removeByFieldId(fieldId);
    }
}
