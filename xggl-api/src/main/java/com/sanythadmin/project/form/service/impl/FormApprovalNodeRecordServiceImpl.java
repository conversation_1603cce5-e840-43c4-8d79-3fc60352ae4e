package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.form.entity.FormApprovalNode;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.form.mapper.FormApprovalNodeRecordMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeRecordParam;
import com.sanythadmin.project.form.service.FormApprovalNodeRecordService;
import com.sanythadmin.project.form.service.FormApprovalNodeService;
import com.sanythadmin.project.workflow.util.WorkflowUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 审批记录Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Service
@RequiredArgsConstructor
public class FormApprovalNodeRecordServiceImpl extends ServiceImpl<FormApprovalNodeRecordMapper, FormApprovalNodeRecord> implements FormApprovalNodeRecordService {

    private final FormApprovalNodeRecordMapper mapper;
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final FormApprovalNodeService approvalNodeService;


    @Override
    public List<FormApprovalNodeRecord> list(WorkflowApprovalNodeRecordParam param) {
        PageParam<FormApprovalNodeRecord, WorkflowApprovalNodeRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return mapper.selectList(page.getOrderWrapper());
    }

//    @Override
//    public List<FormApprovalNodeRecord> getApprovalNodeRecordByUser(String applicationId, String projectId, String year, String username, String roleId) {
//        String strJoin = CommonUtil.strJoin(username, roleId);
//        List<WorkflowNodeApprover> nodeApprovers = nodeApproverMapper.nodeListByFormInfoAndApprover(projectId, year, strJoin);
//        WorkflowApprovalNodeParam param = new WorkflowApprovalNodeParam();
//        param.setApplicationId(applicationId);
//        List<FormApprovalNode> list = approvalNodeService.list(param);
//        FormApprovalNode approvalNode = WorkflowUtil.getApprovalNode(nodeApprovers, list);
//        if (approvalNode == null) return null;
//        WorkflowApprovalNodeRecordParam recordParam = new WorkflowApprovalNodeRecordParam();
//        recordParam.setNodeId(approvalNode.getId());
//        recordParam.setApplicationId(applicationId);
//        recordParam.setApproverId(strJoin);
//        recordParam.setUsername(username);
//        return list(recordParam);
//    }

    @Override
    public List<FormApprovalNodeRecord> getPreNodeApprovalRecordByUser(String applicationId, String projectId, String year, String username, String roleId) {
        String strJoin = CommonUtil.strJoin(username, roleId);
        List<WorkflowNodeApprover> nodeApprovers = nodeApproverMapper.nodeListByFormInfoAndApprover(projectId, year, strJoin);
        WorkflowApprovalNodeParam param = new WorkflowApprovalNodeParam();
        param.setApplicationId(applicationId);
        List<FormApprovalNode> list = approvalNodeService.list(param);
        FormApprovalNode approvalNode = WorkflowUtil.getApprovalNode(nodeApprovers, list);
        if (approvalNode == null) return null;
        FormApprovalNode preApprovalNode = WorkflowUtil.preApprovalNode(approvalNode, list);
        if (preApprovalNode == null) return null;
        WorkflowApprovalNodeRecordParam recordParam = new WorkflowApprovalNodeRecordParam();
        recordParam.setNodeId(preApprovalNode.getId());
        recordParam.setApplicationId(applicationId);
        return list(recordParam);
    }
}
