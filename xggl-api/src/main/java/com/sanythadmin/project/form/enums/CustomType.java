package com.sanythadmin.project.form.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * Created by JIANGPING on 2024/11/4.
 */
public enum CustomType implements ValueTextBaseEnum {
    Query(0, "查询", "查询"),
    List(1, "列表", "列表");

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private String remark;

    CustomType(Integer value, String text, String remark) {
        this.value = value;
        this.text = text;
        this.remark = remark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
