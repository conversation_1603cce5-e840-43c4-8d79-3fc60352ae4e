package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批结果记录
 *
 * <AUTHOR>
 * @since 2024-08-13 11:41:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_APPROVAL_RESULT")
@Entity
@Table(name = "SYT_FORM_APPROVAL_RESULT")
public class FormApprovalResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 关联表单申请ID
     */
    @Column(name = "APPLICATION_ID")
    @TableField("APPLICATION_ID")
    private String applicationId;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 学工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;
    /**
     * 培养层次名称
     */
    @Column(name = "PYCCMC")
    @TableField("PYCCMC")
    private String pyccmc;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 班级名称
     */
    @Column(name = "BJMC")
    @TableField("BJMC")
    private String bjmc;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 年级名称
     */
    @Column(name = "NJMC")
    @TableField("NJMC")
    private String njmc;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XB")
    private Gender xb;

    /**
     * 项目名称
     */
    @Column(name = "XMMC")
    @TableField("XMMC")
    private String xmmc;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 校区名称
     */
    @Column(name = "XQMC")
    @TableField("XQMC")
    private String xqmc;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 是否集体项目
     */
    @Column(name = "SFJTXM", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFJTXM")
    private JudgeMark sfjtxm;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("USER_TYPE")
    private UserType userType;

    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;

    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;

    @Column(name = "BZ3")
    @TableField("BZ3")
    private String bz3;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
