package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.FormDataWrapper;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.entity.DictionaryGroup;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.param.DictionaryGroupParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.dictionary.service.DictionaryGroupService;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.enums.CustomType;
import com.sanythadmin.project.form.enums.InfoType;
import com.sanythadmin.project.form.param.*;
import com.sanythadmin.project.form.service.*;
import com.sanythadmin.project.form.vo.FormCustomFieldVO;
import com.sanythadmin.project.form.vo.FormProjectVO;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.dto.WorkflowNodeStateDTO;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeFormService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 表单管理模块/表单审核端管理 控制器
 * Created by JIANGPING on 2024/7/31.
 */
@RestController
@RequestMapping("/api/form/approval")
@RequiredArgsConstructor
public class FormApprovalController extends BaseController {
    private final FormProjectService projectService;
    private final FormApplicationInfoService applicationInfoService;
    private final FormApplicationListInfoService applicationListInfoService;
    private final FormGroupService groupService;
    private final FormTemplateFieldService templateFieldService;
    private final DictionaryGroupService dictionaryGroupService;
    private final DictionaryFieldService dictionaryFieldService;
    private final FormCustomFieldService customFieldService;
    private final WorkflowService workflowService;
    private final WorkflowNodeFormService workflowNodeFormService;
    private final FormApprovalNodeRecordService approvalNodeRecordService;
    private final RedissonClient redissonClient;


    /**
     * 分页查询表单项目记录（权限标识：form:approval:list）
     *
     * @param param 项目查询参数
     * @return
     */
    @PreAuthorize("hasAuthority('form:approval:list')")
    @GetMapping("/{type}/pageProjectInfo")
    public PageResult<FormProjectVO> pageProjectInfo(FormProjectParam param) {
        PageResult<FormProject> pageResult = projectService.pageByReviewer(param);
        PageResult<FormProjectVO> voPageResult = new PageResult<>();
        if (pageResult != null && !CollectionUtils.isEmpty(pageResult.getList())) {
            List<FormProjectVO> list = new ArrayList<>();
            for (FormProject project : pageResult.getList()) {
                FormApplicationInfoParam infoParam = new FormApplicationInfoParam();
                infoParam.setProjectId(project.getId());
                infoParam.setYear(project.getYear());
                infoParam.setSfzc(JudgeMark.NO);
                Long applyCount = applicationInfoService.getApplyCount(infoParam);
                FormProjectVO formProjectVO = new FormProjectVO();
                BeanUtils.copyProperties(project, formProjectVO);
                formProjectVO.setApplyCount(applyCount);
                list.add(formProjectVO);
            }
            voPageResult.setList(list);
            voPageResult.setCount(pageResult.getCount());
        }
        return voPageResult;
    }


    /**
     * 分页查询申请审核列表
     *
     * @param param             申请查询参数
     * @param userInfoParam     用户信息参数
     * @param approvalNodeParam 审批节点参数
     * @param codeXsztParam     学生状态参数
     * @return
     */
    @PreAuthorize("hasAuthority('form:approval:list')")
    @GetMapping("/{type}/pageApprovalList")
    public PageResult<FormApplicationInfo> pageApprovalList(FormApplicationInfoParam param, UserInfoParam userInfoParam,
                                                            FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam) {
        return applicationInfoService.pageApprovalList(param, userInfoParam, approvalNodeParam, codeXsztParam);
    }

    /**
     * 根据申请信息查询当前用户审核表单字段
     *
     * @param applicationId 申请记录ID
     * @param param         工作流参数
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeFormFieldList")
    public List<WorkflowNodeForm> nodeFormFieldList(String applicationId, WorkflowParam param, String nodeId) {
        return workflowNodeFormService.getNodeFormByUser(FormApprovalNode.class, applicationId, param, nodeId);
    }


    /**
     * 根据申请信息查询当前用户审核节点记录
     *
     * @param applicationId 申请记录ID
     * @param param         工作流参数
     * @param nodeId        审核节点ID
     * @return
     */
    @GetMapping("/nodeApprovalRecord")
    public List<FormApprovalNodeRecord> nodeApprovalRecord(String applicationId, WorkflowParam param, String nodeId) {
        return workflowService.approvalNodeRecordList(FormApprovalNode.class, FormApprovalNodeRecord.class, param, applicationId, nodeId);
    }


    /**
     * 根据申请信息查询当前用户上一节点审核记录
     *
     * @param id        申请记录ID
     * @param projectId 项目ID
     * @param year      年份
     * @return
     */
    @GetMapping("/preNodeApprovalRecord")
    public List<FormApprovalNodeRecord> preNodeApprovalRecord(String id, String projectId, String year) {
        return approvalNodeRecordService.getPreNodeApprovalRecordByUser(id, projectId, year,
                SecurityUtil.getUsername(), SecurityUtil.getRoleId());
    }

    /**
     * 根据项目ID查询工作流
     *
     * @param projectId 项目ID
     * @return
     */
    @GetMapping("/{projectId}/workflow")
    public Workflow workflow(@PathVariable("projectId") String projectId) {
        FormProject project = projectService.getById(projectId);
        return workflowService.get(project.getId(), project.getYear());
    }


    /**
     * 根据申请信息ID查询所有审核节点，审核记录
     *
     * @param id 申请记录ID
     * @return
     */
    @GetMapping("/{id}/approvalNodes")
    public List<ApprovalNodeInfo<FormApprovalNode, FormApprovalNodeRecord>> approvalNodes(@PathVariable("id") String id) {
        return workflowService.approvalNodeInfo(FormApprovalNode.class, FormApprovalNodeRecord.class, id, null);
    }

    /**
     * 根据申请记录ID或项目ID,年份查询节点自定义状态
     *
     * @param applicationId 申请记录ID
     * @param param         工作流查询参数
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeState")
    public List<WorkflowNodeStateDTO> nodeState(String applicationId, WorkflowParam param, String nodeId) {
        if (!StringUtils.hasText(param.getProjectId()) || !StringUtils.hasText(param.getYear()))
            AssertUtil.throwMessage("项目ID和年份不能为空");
        return workflowService.nodeCustomState(FormApprovalNode.class, param, nodeId, applicationId);
    }

    /**
     * 单个或批量审核操作（权限标识：form:approval:operation）
     *
     * @param request 请求体
     * @param id      申请记录ID
     */
    @OperationLog(module = "表单审核管理", comments = "审核申请记录")
    @PreAuthorize("hasAuthority('form:approval:operation')")
    @PostMapping("/operation")
    public String operation(HttpServletRequest request, String... id) {
        FormDataWrapper<FormApprovalNodeRecord> wrapper = new FormDataWrapper<>(request, FormApprovalNodeRecord.class);
        Map<String, List<MultipartFile>> fileMap = wrapper.getFileFields();
        FormApprovalNodeRecord object = wrapper.getObject();
        String nextApprovableNodeId = null;
        for (String str : id) {
            String lockKey = "form:approval:operation:lock:" + str;
            RLock lock = redissonClient.getLock(lockKey);
            try {
                if (lock.tryLock(3, 5, TimeUnit.SECONDS)) {
                    FormApprovalNodeRecord record = new FormApprovalNodeRecord();
                    BeanUtils.copyProperties(object, record);
                    record.setId(null);
                    record.setApplicationId(str);
                    record.setFileMap(fileMap);
                    nextApprovableNodeId = applicationInfoService.approve(record);
                } else {
                    AssertUtil.throwMessage("获取锁失败，操作被阻止");
                }
            } catch (InterruptedException e) {
                throw new RuntimeException("获取锁时发生异常", e);
            } finally {
                if (lock.isHeldByCurrentThread())
                    lock.unlock();
            }
        }
        return nextApprovableNodeId;
    }

    /**
     * 根据申请ID查询表单申请信息（权限标识：form:approval:list）
     */
    @PreAuthorize("hasAuthority('form:approval:list')")
    @GetMapping("/{id}/applicationInfo")
    public FormApplicationInfo applicationInfo(@PathVariable("id") String id) {
        FormApplicationInfoParam param = new FormApplicationInfoParam();
        param.setId(id);
        List<FormApplicationInfo> list = applicationInfoService.list(param);
        return CommonUtil.listGetOne(list);
    }

    /**
     * 查询申请表单子列表信息
     *
     * @param info
     * @return
     */
    @PreAuthorize("hasAuthority('form:approval:list')")
    @GetMapping("/applicationListInfos")
    public List<FormApplicationListInfo> applicationListInfos(FormApplicationListInfoParam info) {
        return applicationListInfoService.list(info);
    }

    /**
     * 根据申请记录ID查询申请表单信息组
     *
     * @param id 申请记录ID
     * @return
     */
    @GetMapping("/fieldGroupList")
    public List<FormGroup> getGroupList(@RequestParam(name = "id") String id) {
        return groupService.list(null, id);
    }

    /**
     * 根据申请记录ID或模板信息查询申请表单字段
     *
     * @param id         申请记录ID
     * @param fieldParam
     * @return
     */
    @GetMapping("/fieldList")
    public List<FormTemplateField> getFieldList(@RequestParam(required = false, name = "id") String id
            , FormTemplateFieldParam fieldParam) {
        fieldParam.setShowFlag(JudgeMark.YES);
        return templateFieldService.list(fieldParam, id);
    }

    /**
     * 根据申请记录ID查询用户字典列表信息组
     *
     * @param id 申请记录ID
     * @return
     */
    @GetMapping("/dicListGroup")
    public List<DictionaryGroup> dicListGroup(@RequestParam(name = "id") String id) {
        List<FormGroup> list = getFormGroups(null, id);
        if (CollectionUtils.isEmpty(list)) return null;
        String[] dicGroupIds = list.stream().map(FormGroup::getDicGroupId).toArray(String[]::new);
        DictionaryGroupParam groupParam = new DictionaryGroupParam();
        groupParam.setId(CommonUtil.strJoin(Arrays.stream(dicGroupIds).toList()));
        PageParam<DictionaryGroup, DictionaryGroupParam> page = new PageParam<>(groupParam);
        return dictionaryGroupService.list(page.getOrderWrapper());
    }

    private List<FormGroup> getFormGroups(String projectId, String applicationId) {
        FormGroupParam param = new FormGroupParam();
        param.setDicGroupIdIsNull(false);
        param.setListFlag(JudgeMark.YES);
        if (StringUtils.hasText(applicationId)) {
            FormApplicationInfo info = applicationInfoService.getById(applicationId);
            param.setProjectId(info.getProjectId());
            param.setYear(info.getYear());
        } else {
            FormProject project = projectService.getById(projectId);
            param.setYear(project.getYear());
        }
        return groupService.list(param);
    }

    /**
     * 根据申请记录ID或组ID查询用户字典列表组字段
     *
     * @param id         申请记录ID
     * @param dicGroupId
     * @return
     */
    @GetMapping("/dicListGroupField")
    public List<DictionaryField> dictionaryFieldList(@RequestParam(name = "id") String id,
                                                     @RequestParam(required = false, name = "dicGroupId") String dicGroupId) {
        String dicGroupIds = dicGroupId;
        if (!StringUtils.hasText(dicGroupIds)) {
            List<FormGroup> groups = getFormGroups(null, id);
            if (CollectionUtils.isEmpty(groups)) return null;
            dicGroupIds = CommonUtil.strJoin(groups.stream().map(FormGroup::getDicGroupId).toList());
        }
        DictionaryFieldParam fieldParam = new DictionaryFieldParam();
        fieldParam.setGroupId(dicGroupIds);
        PageParam<DictionaryField, DictionaryFieldParam> page = new PageParam<>(fieldParam);
        return dictionaryFieldService.list(page.getOrderWrapper());
    }


    /**
     * 自定义列表/查询条件字段维护（权限标识：form:approval:customTemplateField）
     *
     * @param projectId    项目ID
     * @param customFields 自定义列表
     */
    @OperationLog(module = "表单审核管理", comments = "自定义列表字段维护")
    @PreAuthorize("hasAuthority('form:approval:customTemplateField')")
    @PostMapping("/{projectId}/customField")
    public void customField(@PathVariable("projectId") String projectId, @RequestBody List<FormCustomField> customFields) {
        if (CollectionUtils.isEmpty(customFields)) AssertUtil.throwMessage("缺失必填参数项");
        FormProject project = projectService.getById(projectId);
        FormCustomField customField = customFields.get(0);
        customFieldService.remove(projectId, customField.getCustomType());
        customFields.forEach(customList -> {
            customList.setProjectId(projectId);
            customList.setType(project.getType());
        });
        customFieldService.saveBatch(customFields);
    }


    /**
     * 自定义列表/查询条件字段查询
     *
     * @param projectId
     * @return
     */
    @GetMapping("/{projectId}/customField")
    public List<FormCustomFieldVO> customField(@PathVariable("projectId") String projectId, CustomType customType) {
        FormCustomFieldParam param = new FormCustomFieldParam();
        param.setProjectId(projectId);
        param.setCustomType(customType);
        List<FormCustomField> list = customFieldService.list(param);
        if (CollectionUtils.isEmpty(list)) return null;
        List<FormCustomField> queryFieldList = list.stream().filter(formCustomField ->
                Objects.equals(CustomType.Query, formCustomField.getCustomType())).toList();

        List<FormCustomFieldVO> formCustomFieldVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(queryFieldList)) {
            FormProject project = projectService.getById(projectId);
            List<FormCustomField> baseFieldList = queryFieldList.stream().filter(formCustomField ->
                    Objects.equals(InfoType.base, formCustomField.getInfoType())).toList();
            List<FormCustomField> applyFieldList = queryFieldList.stream().filter(formCustomField ->
                    Objects.equals(InfoType.apply, formCustomField.getInfoType())).toList();

            if (!CollectionUtils.isEmpty(baseFieldList)) {
                DictionaryFieldParam fieldParam = new DictionaryFieldParam();
                fieldParam.setFieldEn(CommonUtil.strJoin(baseFieldList.stream().map(FormCustomField::getFieldEn).toList()));
                fieldParam.setUserType(project.getUserType());
                List<DictionaryField> fields = dictionaryFieldService.queryList(fieldParam);
                for (DictionaryField field : fields) {
                    FormCustomFieldVO vo = new FormCustomFieldVO();
                    BeanUtils.copyProperties(field, vo);
                    vo.setInfoType(InfoType.base);
                    vo.setCustomType(CustomType.Query);
                    formCustomFieldVOS.add(vo);
                }
            }

            if (!CollectionUtils.isEmpty(applyFieldList)) {
                FormTemplateFieldParam templateFieldParam = new FormTemplateFieldParam();
                templateFieldParam.setProjectId(projectId);
                templateFieldParam.setYear(project.getYear());
                templateFieldParam.setFieldEn(CommonUtil.strJoin(applyFieldList.stream().map(FormCustomField::getFieldEn).toList()));
                List<FormTemplateField> templateFields = templateFieldService.list(templateFieldParam);
                for (FormTemplateField field : templateFields) {
                    FormCustomFieldVO vo = new FormCustomFieldVO();
                    BeanUtils.copyProperties(field, vo);
                    vo.setInfoType(InfoType.apply);
                    vo.setCustomType(CustomType.Query);
                    formCustomFieldVOS.add(vo);
                }
            }
        }


        List<FormCustomField> fields = list.stream().filter(formCustomField ->
                Objects.equals(CustomType.List, formCustomField.getCustomType())).toList();
        if (!CollectionUtils.isEmpty(fields)) {
            for (FormCustomField field : fields) {
                FormCustomFieldVO vo = new FormCustomFieldVO();
                BeanUtils.copyProperties(field, vo);
                formCustomFieldVOS.add(vo);
            }
        }
        return formCustomFieldVOS;
    }


    /**
     * 申请表及汇总表导出（权限标识：form:approval:list）
     *
     * @param templateFile      模板信息
     * @param param             申请信息参数
     * @param userInfoParam     用户信息参数
     * @param approvalNodeParam 审批节点参数
     * @param codeXsztParam     学生状态参数
     * @param response          响应
     */
    @PreAuthorize("hasAuthority('form:approval:list')")
    @GetMapping("/{templateType}/exportByTemplate")
    public void exportByTemplate(TemplateFile templateFile, FormApplicationInfoParam param, UserInfoParam userInfoParam
            , FormApprovalNodeParam approvalNodeParam, CodeXsztParam codeXsztParam
            , HttpServletResponse response) {
        if (!StringUtils.hasText(param.getProjectId()))
            AssertUtil.throwMessage("缺失必填参数项");
        FormProject project = projectService.getById(param.getProjectId());
        param.setYear(StringUtils.hasText(param.getYear()) ? param.getYear() : project.getYear());
        List<FormApplicationInfo> infos = applicationInfoService.approvalList(param, userInfoParam, approvalNodeParam, codeXsztParam);
        applicationInfoService.exportByTemplate(project, templateFile, infos, response);
    }
}
