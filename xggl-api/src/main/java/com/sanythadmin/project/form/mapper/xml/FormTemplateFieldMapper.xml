<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormTemplateFieldMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PROJECT_ID, FIELD_EN, FIELD_ZH, INFO_TYPE, TYPE, YEAR, REQUIRED, TIP_TEXT, CONTROL_TYPE, REG_EXPRESSION, SORT, GROUP_ID, SHOW_FLAG, DEFAULT_VAL, DEFAULT_VAL_FIELD, EDIT_FLAG
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID LIKE concat(concat('%',#{param.id), '%')
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.fieldEn != null">
                        AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
                    </if>
                    <if test="param.fieldZh != null">
                        AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh), '%')
                    </if>
                    <if test="param.infoType != null">
                        AND a.INFO_TYPE = #{param.infoType}
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.required != null">
                        AND a.REQUIRED = #{param.required}
                    </if>
                    <if test="param.tipText != null">
                        AND a.TIP_TEXT LIKE concat(concat('%',#{param.tipText), '%')
                    </if>
                    <if test="param.controlType != null">
                        AND a.CONTROL_TYPE LIKE concat(concat('%',#{param.controlType), '%')
                    </if>
                    <if test="param.regExpression != null">
                        AND a.REG_EXPRESSION LIKE concat(concat('%',#{param.regExpression), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.groupId != null">
                        AND a.GROUP_ID LIKE concat(concat('%',#{param.groupId), '%')
                    </if>
                    <if test="param.showFlag != null">
                        AND a.SHOW_FLAG = #{param.showFlag}
                    </if>
                    <if test="param.defaultVal != null">
                        AND a.DEFAULT_VAL LIKE concat(concat('%',#{param.defaultVal), '%')
                    </if>
                    <if test="param.defaultValField != null">
                        AND a.DEFAULT_VAL_FIELD LIKE concat(concat('%',#{param.defaultValField), '%')
                    </if>
                    <if test="param.editFlag != null">
                        AND a.EDIT_FLAG = #{param.editFlag}
                    </if>
    </sql>
</mapper>
