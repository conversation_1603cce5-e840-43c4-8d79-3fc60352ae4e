package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormProjectSpecialList;
import com.sanythadmin.project.form.param.FormProjectSpecialListParam;
import com.sanythadmin.project.form.service.FormProjectSpecialListService;
import com.sanythadmin.project.selector.entity.SelectorData;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 表单管理模块/特殊名单人员控制器
 *
 * <AUTHOR>
 * @since 2024-07-09 11:36:55
 */
@RestController
@RequestMapping("/api/form/form-project-special-list")
public class FormProjectSpecialListController extends BaseController {
    @Resource
    private FormProjectSpecialListService formProjectSpecialListService;

    /**
     * 分页查询特殊名单人员（权限标识：form:formProjectSpecialList:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:list')")
    @GetMapping("/page")
    public PageResult<FormProjectSpecialList> page(FormProjectSpecialListParam param) {
        return formProjectSpecialListService.queryPage(param);
    }

    /**
     * 查询全部特殊名单人员（权限标识：form:formProjectSpecialList:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:list')")
    @GetMapping()
    public List<FormProjectSpecialList> list(FormProjectSpecialListParam param) {
        PageParam<FormProjectSpecialList, FormProjectSpecialListParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formProjectSpecialListService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询特殊名单人员（权限标识：form:formProjectSpecialList:list）
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:list')")
    @GetMapping("/{id}")
    public FormProjectSpecialList get(@PathVariable("id") String id) {
        return formProjectSpecialListService.getById(id);
    }

    /**
     * 添加或修改特殊名单人员（权限标识：form:formProjectSpecialList:operation）
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:operation')")
    @OperationLog(module = "特殊名单人员", comments = "保存特殊名单人员")
    @PostMapping("/{id}/add")
    public void add(@PathVariable("id") String id, @RequestBody List<SelectorData> specialListParams) {
        formProjectSpecialListService.add(id, specialListParams);
    }

    /**
     * 批量删除特殊名单人员（权限标识：form:formProjectSpecialList:remove）
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:remove')")
    @OperationLog(module = "特殊名单人员", comments = "批量删除特殊名单人员")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formProjectSpecialListService.removeByIds(ids);
    }

    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        formProjectSpecialListService.importTemplate(response);
    }

    /**
     * 特殊名单人员导入 （权限标识：form:formProjectSpecialList:importData）
     *
     * @param id
     * @param file
     */
    @PreAuthorize("hasAuthority('form:formProjectSpecialList:importData')")
    @PostMapping("/{id}/importData")
    @OperationLog(module = "特殊名单人员", comments = "导入特殊名单人员")
    public void importData(@PathVariable("id") String id, @RequestParam(name = "file") MultipartFile file) {
        formProjectSpecialListService.importData(id, file);
    }
}
