package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.dto.FormPublicityItemDTO;
import com.sanythadmin.project.form.entity.FormPublicityItem;
import com.sanythadmin.project.form.param.FormPublicityItemParam;
import com.sanythadmin.project.form.service.FormPublicityItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公示项目控制器
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@RestController
@RequestMapping("/api/form/form-publicity-item")
public class FormPublicityItemController extends BaseController {
    @Resource
    private FormPublicityItemService formPublicityItemService;

    /**
     * 分页查询公示项目（权限标识：form:formPublicityItem:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityItem:list')")
    @GetMapping("/page")
    public PageResult<FormPublicityItem> page(FormPublicityItemParam param) {
        PageParam<FormPublicityItem, FormPublicityItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formPublicityItemService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部公示项目（权限标识：form:formPublicityItem:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityItem:list')")
    @GetMapping()
    public List<FormPublicityItem> list(FormPublicityItemParam param) {
        PageParam<FormPublicityItem, FormPublicityItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formPublicityItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询公示项目（权限标识：form:formPublicityItem:list）
     */
    @PreAuthorize("hasAuthority('form:formPublicityItem:list')")
    @GetMapping("/{id}")
    public FormPublicityItem get(@PathVariable("id") String id) {
        return formPublicityItemService.getById(id);
    }

    /**
     * 添加或修改公示项目（权限标识：form:formPublicityItem:operation）
     */
    @PreAuthorize("hasAuthority('form:formPublicityItem:operation')")
    @OperationLog(module = "公示项目", comments = "保存公示项目")
    @PostMapping("/operation")
    public void save(@RequestBody FormPublicityItemDTO dto) {
        formPublicityItemService.edit(dto);
    }

    /**
     * 批量删除公示项目（权限标识：form:formPublicityItem:remove）
     */
    @PreAuthorize("hasAuthority('form:formPublicityItem:remove')")
    @OperationLog(module = "公示项目", comments = "批量删除公示项目")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formPublicityItemService.removeByIds(ids);
    }
}
