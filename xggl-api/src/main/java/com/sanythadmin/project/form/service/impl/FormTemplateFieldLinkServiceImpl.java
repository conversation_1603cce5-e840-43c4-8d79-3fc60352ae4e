package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.form.entity.FormTemplateFieldLink;
import com.sanythadmin.project.form.mapper.FormTemplateFieldLinkMapper;
import com.sanythadmin.project.form.param.FormTemplateFieldLinkParam;
import com.sanythadmin.project.form.service.FormTemplateFieldLinkService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 表单字段关联关系Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Service
public class FormTemplateFieldLinkServiceImpl extends ServiceImpl<FormTemplateFieldLinkMapper, FormTemplateFieldLink> implements FormTemplateFieldLinkService {

    @Resource
    private FormTemplateFieldLinkMapper mapper;

    @Override
    public void removeByFieldId(String fieldId) {
        baseMapper.delete(new LambdaQueryWrapper<FormTemplateFieldLink>().eq(FormTemplateFieldLink::getFieldId, fieldId));
    }

    @Override
    public void edit(List<FormTemplateFieldLink> fieldLinks) {
        FormTemplateFieldLink link = fieldLinks.get(0);
        String fieldId = link.getFieldId();
        removeByFieldId(fieldId);
        for (FormTemplateFieldLink fieldLink : fieldLinks) {
            baseMapper.insert(fieldLink);
        }
    }

    @Override
    public List<FormTemplateFieldLink> list(FormTemplateFieldLinkParam param) {
        PageParam<FormTemplateFieldLink, FormTemplateFieldLinkParam> pageParam = new PageParam<>(param);
        return list(pageParam.getOrderWrapper());
    }
}
