package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.form.entity.FormApplicationListInfo;
import com.sanythadmin.project.form.param.FormApplicationListInfoParam;

import java.util.List;

/**
 * 表单列表申请记录Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
public interface FormApplicationListInfoService extends IService<FormApplicationListInfo> {
    public void operation(FormApplicationListInfo listInfo, boolean apply);
    public void delete(List<String> ids, boolean apply);
    public List<FormApplicationListInfo> list(FormApplicationListInfoParam param);
}
