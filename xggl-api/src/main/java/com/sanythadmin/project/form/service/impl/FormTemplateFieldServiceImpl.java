package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.mapper.*;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;
import com.sanythadmin.project.form.service.FormTemplateFieldService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 表单模板配置Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Service
public class FormTemplateFieldServiceImpl extends ServiceImpl<FormTemplateFieldMapper, FormTemplateField> implements FormTemplateFieldService {
    @Resource
    private FormTemplateFieldMapper formTemplateFieldMapper;
    @Resource
    private FormTypeMapper formTypeMapper;
    @Resource
    private FormProjectMapper formProjectMapper;
    @Resource
    private FormApplicationInfoMapper applicationInfoMapper;
    @Resource
    private FormTemplateFieldLinkMapper templateFieldLinkMapper;

    @Transactional
    @Override
    public void edit(List<FormTemplateField> list) {
        FormTemplateField field = list.get(0);
        FormType formType = formTypeMapper.selectOne(new LambdaQueryWrapper<FormType>()
                .eq(FormType::getType, field.getType()));
        for (FormTemplateField templateField : list) {
            templateField.setYear(formType.getYear());
            if (StringUtils.hasText(templateField.getId())) {
                formTemplateFieldMapper.updateById(templateField);
            } else {
                formTemplateFieldMapper.insert(templateField);
            }
        }
    }

    @Override
    public PageResult<FormTemplateField> page(FormTemplateFieldParam param) {
        PageParam<FormTemplateField, FormTemplateFieldParam> page = new PageParam<>(param);
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FormTemplateField> list(FormTemplateFieldParam param) {
        if (!StringUtils.hasText(param.getProjectId()))
            AssertUtil.throwMessage("参数错误");
        if (StringUtils.hasText(param.getType()) && StringUtils.hasText(param.getYear())) {
            param.setType(param.getType());
            param.setYear(param.getYear());
        } else {
            FormProject project = formProjectMapper.selectById(param.getProjectId());
            param.setType(project.getType());
            param.setYear(project.getYear());
        }
        PageParam<FormTemplateField, FormTemplateFieldParam> page = new PageParam<>(param);
        return formTemplateFieldMapper.selectList(page.getOrderWrapper());
    }

    @Override
    public List<FormTemplateField> list(FormTemplateFieldParam param, String applicationId) {
        if (StringUtils.hasText(applicationId)) {
            FormApplicationInfo info = applicationInfoMapper.selectById(applicationId);
            param.setProjectId(info.getProjectId());
            param.setYear(info.getYear());
        } else {
            FormProject project = formProjectMapper.selectById(param.getProjectId());
            param.setYear(project.getYear());
        }

        PageParam<FormTemplateField, FormTemplateFieldParam> page = new PageParam<>(param);
        List<FormTemplateField> fields = formTemplateFieldMapper.selectList(page.getOrderWrapper());
        if (!CollectionUtils.isEmpty(fields)) {
            Set<String> stringSet = fields.stream().map(FormTemplateField::getGroupId).collect(Collectors.toSet());
            List<FormTemplateFieldLink> fieldLinks = templateFieldLinkMapper.selectList(new LambdaQueryWrapper<FormTemplateFieldLink>()
                    .in(FormTemplateFieldLink::getGroupId, stringSet));
            if (!CollectionUtils.isEmpty(fieldLinks)) {
                Map<String, List<FormTemplateFieldLink>> groupMap = fieldLinks.stream().collect(Collectors.groupingBy(FormTemplateFieldLink::getFieldEn));
                fields.forEach(field -> field.setFieldLinks(groupMap.getOrDefault(field.getFieldEn(), null)));
            }
        }
        return fields;
    }
}
