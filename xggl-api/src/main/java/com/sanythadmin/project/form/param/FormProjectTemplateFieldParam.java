package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单项目模板配置查询参数
 *
 * <AUTHOR>
 * @since 2024-12-12 10:54:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormProjectTemplateFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 提示文本
     */
    private String tipText;

    /**
     * 控件类型
     */
    private String controlType;

    /**
     * 默认值
     */
    private String defaultVal;

    /**
     * 默认值提取字段（用户信息）
     */
    private String defaultValField;

    /**
     * 加载数据类型
     */
    private String loadDataType;

    /**
     * 加载数据URL
     */
    private String loadDataUrlId;

}
