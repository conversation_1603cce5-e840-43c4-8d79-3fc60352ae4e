package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.springframework.util.StringUtils;

/**
 * 公示项目
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PUBLICITY_ITEM")
@Entity
@Table(name = "SYT_FORM_PUBLICITY_ITEM")
public class FormPublicityItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 开始时间
     */
    @Column(name = "START_DATE")
    @TableField("START_DATE")
    private LocalDateTime startDate;

    /**
     * 截至时间
     */
    @Column(name = "END_DATE")
    @TableField("END_DATE")
    private LocalDateTime endDate;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 创建用户
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 角色标识
     */
    @Column(name = "ROLE_SCOPE")
    @TableField("ROLE_SCOPE")
    private String roleScope;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
