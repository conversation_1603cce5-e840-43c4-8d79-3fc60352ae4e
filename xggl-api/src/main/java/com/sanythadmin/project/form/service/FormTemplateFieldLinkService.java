package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.form.entity.FormTemplateFieldLink;
import com.sanythadmin.project.form.param.FormTemplateFieldLinkParam;

import java.util.List;

/**
 * 表单字段关联关系Service
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
public interface FormTemplateFieldLinkService extends IService<FormTemplateFieldLink> {
    /**
     * 根据field删除
     *
     * @param fieldId
     */
    void removeByFieldId(String fieldId);

    /**
     * 编辑
     *
     * @param fieldLinks
     */
    void edit(List<FormTemplateFieldLink> fieldLinks);

    /**
     * 根据条件查询
     *
     * @param param
     * @return
     */
    List<FormTemplateFieldLink> list(FormTemplateFieldLinkParam param);
}
