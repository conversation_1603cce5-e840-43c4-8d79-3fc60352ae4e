package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.FormApplicationInfo;
import com.sanythadmin.project.form.entity.FormApplicationListInfo;
import com.sanythadmin.project.form.entity.FormTemplateField;
import com.sanythadmin.project.form.param.FormTemplateFieldParam;
import com.sanythadmin.project.form.service.FormTemplateFieldService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表单管理模块/表单模板配置控制器
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@RestController
@RequestMapping("/api/form/form-template-field")
public class FormTemplateFieldController extends BaseController {
    @Resource
    private FormTemplateFieldService formTemplateFieldService;

    /**
     * 分页查询表单模板配置（权限标识：form:formTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateField:list')")
    @GetMapping("/page")
    public PageResult<FormTemplateField> page(FormTemplateFieldParam param) {
        return formTemplateFieldService.page(param);
    }

    /**
     * 查询全部表单模板配置（权限标识：form:formTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateField:list')")
    @GetMapping()
    public List<FormTemplateField> list(FormTemplateFieldParam param) {
        return formTemplateFieldService.list(param);
    }

    /**
     * 根据id查询表单模板配置（权限标识：form:formTemplateField:list）
     */
    @PreAuthorize("hasAuthority('form:formTemplateField:list')")
    @GetMapping("/{id}")
    public FormTemplateField get(@PathVariable("id") String id) {
        return formTemplateFieldService.getById(id);
    }

    /**
     * 添加或修改表单模板配置（权限标识：form:formTemplateField:operation）
     */
    @PreAuthorize("hasAuthority('form:formTemplateField:operation')")
    @OperationLog(module = "表单模板配置", comments = "保存表单模板配置")
    @PostMapping("/operation")
    public void save(@RequestBody List<FormTemplateField> formTemplateFields) {
        formTemplateFieldService.edit(formTemplateFields);
    }

    /**
     * 批量删除表单模板配置（权限标识：form:formTemplateField:remove）
     */
    @PreAuthorize("hasAuthority('form:formTemplateField:remove')")
    @OperationLog(module = "表单模板配置", comments = "批量删除表单模板配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formTemplateFieldService.removeByIds(ids);
    }

    /**
     * 可配置字段列表
     *
     * @return
     */
    @GetMapping("/fieldList")
    public List<String> fieldList(@RequestParam(name = "listFlag", defaultValue = "false", required = false) Boolean listFlag,
                                  @RequestParam(name = "notUsed", defaultValue = "false", required = false) Boolean notUsed,
                                  FormTemplateFieldParam param) {
        List<String> list = new ArrayList<>();
        Class<?> aClass = listFlag ? FormApplicationListInfo.class : FormApplicationInfo.class;
        String reg = listFlag ? "c\\d" : "[cl]\\d";
        Field[] fields = CommonUtil.getAllFields(aClass);
        Pattern pattern = Pattern.compile(reg);
        for (Field field : fields) {
            Matcher matcher = pattern.matcher(field.getName());
            if (matcher.find()) list.add(matcher.group());
        }

        if (notUsed) {
            List<FormTemplateField> usedList = formTemplateFieldService.list(param);
            list.removeAll(usedList.stream().map(FormTemplateField::getFieldEn).toList());
        }
        return list;
    }
}
