package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.condition.entity.TempCondition;
import com.sanythadmin.project.condition.service.TempConditionService;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.form.entity.FormRestrict;
import com.sanythadmin.project.form.param.FormRestrictParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 表单管理模块/表单限制条件控制器
 *
 * <AUTHOR>
 * @since 2024-09-06 15:54:27
 */
@RestController
@RequestMapping("/api/form/form-restrict")
@RequiredArgsConstructor
public class FormRestrictController extends BaseController {

    private final FormRestrictService formRestrictService;

    private final TempConditionService tempConditionService;

    /**
     * 分页查询表单限制条件（权限标识：form:formRestrict:list）
     */
    @PreAuthorize("hasAuthority('form:formRestrict:list')")
    @GetMapping("/page")
    public PageResult<FormRestrict> page(FormRestrictParam param) {
        PageParam<FormRestrict, FormRestrictParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formRestrictService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部表单限制条件（权限标识：form:formRestrict:list）
     */
    @PreAuthorize("hasAuthority('form:formRestrict:list')")
    @GetMapping()
    public List<FormRestrict> list(FormRestrictParam param) {
        PageParam<FormRestrict, FormRestrictParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formRestrictService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询表单限制条件（权限标识：form:formRestrict:list）
     */
    @PreAuthorize("hasAuthority('form:formRestrict:list')")
    @GetMapping("/{id}")
    public FormRestrict get(@PathVariable("id") String id) {
        return formRestrictService.getById(id);
    }

    /**
     * 添加或修改表单限制条件（权限标识：form:formRestrict:operation）
     */
    @PreAuthorize("hasAuthority('form:formRestrict:operation')")
    @OperationLog(module = "表单限制条件", comments = "保存表单限制条件")
    @PostMapping("/operation")
    public void operation(@RequestBody FormRestrict formRestrict) {
        if (!StringUtils.hasText(formRestrict.getConditionId()) || !StringUtils.hasText(formRestrict.getProjectId()))
            AssertUtil.throwMessage("缺失必须项参数");
        TempCondition tempCondition = tempConditionService.getById(formRestrict.getConditionId());
        formRestrict.setDescription(tempCondition.getDescription());
        formRestrict.setConditionKey(tempCondition.getConditionKey());
        formRestrict.setModule(tempCondition.getModule());
        formRestrict.setCondition(tempCondition.getCondition());
        formRestrict.setConditionName(tempCondition.getConditionName());
        if (!StringUtils.hasText(formRestrict.getId())) {
            FormRestrictParam param = new FormRestrictParam();
            param.setProjectId(formRestrict.getProjectId());
            param.setConditionId(formRestrict.getConditionId());
            List<FormRestrict> list = list(param);
            if (!CollectionUtils.isEmpty(list))
                formRestrict.setId(list.get(0).getId());
        }

        if (StringUtils.hasLength(formRestrict.getId())) {
            formRestrictService.updateById(formRestrict);
        } else {
            formRestrictService.save(formRestrict);
        }
    }

    /**
     * 批量删除表单限制条件（权限标识：form:formRestrict:remove）
     */
    @PreAuthorize("hasAuthority('form:formRestrict:remove')")
    @OperationLog(module = "表单限制条件", comments = "批量删除表单限制条件")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formRestrictService.removeByIds(ids);
    }
}
