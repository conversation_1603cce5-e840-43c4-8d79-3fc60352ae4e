package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 表单字段关联关系
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_TEMPLATE_FIELD_LINK")
@Entity
@Table(name = "SYT_FORM_TEMPLATE_FIELD_LINK")
public class FormTemplateFieldLink implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段值
     */
    @Column(name = "FIELD_VAL")
    @TableField("FIELD_VAL")
    private String fieldVal;

    /**
     * 关联字段是否显示（1：是，0：否）
     */
    @Column(name = "SHOW_FLAG")
    @TableField("SHOW_FLAG")
    private JudgeMark showFlag;

    /**
     * 关联字段数据
     */
    @Column(name = "LINK_FIELD_DATA_TYPE")
    @TableField("LINK_FIELD_DATA_TYPE")
    private String linkFieldDataType;

    /**
     * 关联字段加载数据url
     */
    @Column(name = "LINK_FIELD_DATA_URL")
    @TableField("LINK_FIELD_DATA_URL")
    private String linkFieldDataUrl;

    /**
     * 字段ID
     */
    @Column(name = "FIELD_ID")
    @TableField("FIELD_ID")
    private String fieldId;

    /**
     * 关联字段英文名
     */
    @Column(name = "LINK_FIELD")
    @TableField("LINK_FIELD")
    private String linkField;

    /**
     * 组ID
     */
    @Column(name = "GROUP_ID")
    @TableField("GROUP_ID")
    private String groupId;

    /**
     * 字段值ID
     */
    @Column(name = "FIELD_VAL_ID")
    @TableField("FIELD_VAL_ID")
    private String fieldValId;
}
