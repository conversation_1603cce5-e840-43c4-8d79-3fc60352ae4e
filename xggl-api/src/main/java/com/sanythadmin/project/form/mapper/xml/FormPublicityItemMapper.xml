<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormPublicityItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PROJECT_ID, YEAR, TYPE, START_DATE, END_DATE, CREATE_DATE, USERNAME, ROLE_SCOPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.startDate != null">
                        AND a.START_DATE LIKE concat(concat('%',#{param.startDate), '%')
                    </if>
                    <if test="param.endDate != null">
                        AND a.END_DATE LIKE concat(concat('%',#{param.endDate), '%')
                    </if>
                    <if test="param.createDate != null">
                        AND a.CREATE_DATE LIKE concat(concat('%',#{param.createDate), '%')
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
                    </if>
                    <if test="param.roleScope != null">
                        AND a.ROLE_SCOPE LIKE concat(concat('%',#{param.roleScope), '%')
                    </if>
    </sql>
</mapper>
