package com.sanythadmin.project.form.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 申请审批节点查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormApprovalNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 工作流程ID
     */
    @QueryField(type = QueryType.EQ)
    private String workflowId;

    /**
     * 审批节点ID
     */
    @QueryField(type = QueryType.EQ)
    private String nodeId;

    /**
     * 关联申请表记录ID
     */
    @QueryField(type = QueryType.EQ)
    private String applicationId;

    /**
     * 下级节点审批结果
     */
    private String nextNodeResult;

    /**
     * 上级节点审批结果
     */
    private String preNodeResult;

    /**
     * 审批结果
     */
    @QueryField(type = QueryType.EQ)
    private String result;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort asc";
    }
}
