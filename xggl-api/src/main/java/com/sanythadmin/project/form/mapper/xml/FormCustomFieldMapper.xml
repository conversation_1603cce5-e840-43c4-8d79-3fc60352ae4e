<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.form.mapper.FormCustomFieldMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, FIELDEN, FIELDZH, PROJECT_ID, YEAR, SORT, TYPE, INFO_TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.fielden != null">
                        AND a.FIELDEN LIKE concat(concat('%',#{param.fielden), '%')
                    </if>
                    <if test="param.fieldzh != null">
                        AND a.FIELDZH LIKE concat(concat('%',#{param.fieldzh), '%')
                    </if>
                    <if test="param.projectId != null">
                        AND a.PROJECT_ID LIKE concat(concat('%',#{param.projectId), '%')
                    </if>
                    <if test="param.year != null">
                        AND a.YEAR LIKE concat(concat('%',#{param.year), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.infoType != null">
                        AND a.INFO_TYPE = #{param.infoType}
                    </if>
    </sql>
</mapper>
