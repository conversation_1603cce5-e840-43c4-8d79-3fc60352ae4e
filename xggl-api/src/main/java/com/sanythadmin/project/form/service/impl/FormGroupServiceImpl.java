package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.enums.InfoType;
import com.sanythadmin.project.form.mapper.*;
import com.sanythadmin.project.form.param.FormGroupParam;
import com.sanythadmin.project.form.service.FormGroupService;
import com.sanythadmin.project.form.service.FormProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 表单信息组Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Service
public class FormGroupServiceImpl extends ServiceImpl<FormGroupMapper, FormGroup> implements FormGroupService {
    @Resource
    private FormGroupMapper formGroupMapper;
    @Resource
    private FormTemplateFieldMapper formTemplateFieldMapper;
    @Resource
    private FormTemplateFieldLinkMapper formTemplateFieldLinkMapper;
    @Resource
    private FormTypeMapper formTypeMapper;
    @Resource
    private FormApplicationInfoMapper applicationInfoMapper;
    @Resource
    private FormProjectService projectService;

    @Transactional
    @Override
    public void edit(FormGroup formGroup) {
        if (formGroup.getSort() == null) {
            SortHelper<FormGroup> sortHelper = new SortHelper<>(this, "sort");
            formGroup.setSort(sortHelper.next());
        }

        FormType formType = formTypeMapper.selectOne(new LambdaQueryWrapper<FormType>()
                .eq(FormType::getType, formGroup.getType()));
        formGroup.setYear(formType.getYear());
        if (!StringUtils.hasText(formGroup.getId())) {
            formGroupMapper.insert(formGroup);
        } else {
            formGroupMapper.updateById(formGroup);
        }
    }

    @Transactional
    @Override
    public void editBaseInfo(String projectId, List<FormGroup> formGroups) {
        FormProject project = projectService.getById(projectId);
        formGroupMapper.delete(new LambdaQueryWrapper<FormGroup>().eq(FormGroup::getInfoType, InfoType.base)
                .eq(FormGroup::getProjectId, projectId).eq(FormGroup::getYear, project.getYear()));
        formGroupMapper.delete(new LambdaQueryWrapper<FormGroup>().eq(FormGroup::getInfoType, InfoType.list)
                .eq(FormGroup::getProjectId, projectId).eq(FormGroup::getYear, project.getYear())
                .isNotNull(FormGroup::getDicGroupId));
        formTemplateFieldMapper.delete(new LambdaQueryWrapper<FormTemplateField>()
                .eq(FormTemplateField::getProjectId, projectId).eq(FormTemplateField::getInfoType, InfoType.base)
                .eq(FormTemplateField::getYear, project.getYear()));
        if (CollectionUtils.isEmpty(formGroups)) return;
        SortHelper<FormGroup> sortHelper = new SortHelper<>(this, "sort");
        Integer total = sortHelper.getTotal();
        Integer sort = sortHelper.getLast();
        for (FormGroup formGroup : formGroups) {
            sort = sortHelper.next(total, sort);
            formGroup.setId(null);
            formGroup.setYear(project.getYear());
            formGroup.setType(project.getType());
            formGroup.setProjectId(projectId);
            formGroup.setSort(sort);
            formGroupMapper.insert(formGroup);
            total++;
            List<FormTemplateField> fields = formGroup.getFormTemplateFields();
            if (CollectionUtils.isEmpty(fields)) continue;
            for (int i = 0; i < fields.size(); i++) {
                FormTemplateField field = fields.get(i);
                field.setId(null);
                field.setGroupId(formGroup.getId());
                field.setYear(formGroup.getYear());
                field.setProjectId(formGroup.getProjectId());
                field.setSort(i + 1L);
                field.setType(formGroup.getType());
                formTemplateFieldMapper.insert(field);
            }
        }
    }

    @Transactional
    @Override
    public void delete(List<String> ids) {
        formTemplateFieldMapper.delete(new LambdaQueryWrapper<FormTemplateField>().in(FormTemplateField::getGroupId, ids));
        formTemplateFieldLinkMapper.delete(new LambdaQueryWrapper<FormTemplateFieldLink>().in(FormTemplateFieldLink::getGroupId, ids));
        formGroupMapper.deleteBatchIds(ids);
    }

    @Override
    public PageResult<FormGroup> page(FormGroupParam param) {
        PageParam<FormGroup, FormGroupParam> page = new PageParam<>(param);
        QueryWrapper<FormGroup> queryWrapper = appendWrapper(page.getOrderWrapper(), param);
        page = page(page, queryWrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<FormGroup> list(FormGroupParam param) {
        if (!StringUtils.hasText(param.getProjectId()))
            AssertUtil.throwMessage("参数错误");
        FormProject project = projectService.getById(param.getProjectId());
        param.setType(project.getType());
        param.setYear(project.getYear());
        PageParam<FormGroup, FormGroupParam> page = new PageParam<>(param);
        QueryWrapper<FormGroup> queryWrapper = appendWrapper(page.getOrderWrapper(), param);
        return formGroupMapper.selectList(queryWrapper);
    }

    @Override
    public List<FormGroup> list(String projectId, String applicationId) {
        if (!StringUtils.hasText(projectId) && !StringUtils.hasText(applicationId))
            AssertUtil.throwMessage("参数错误");
        FormGroupParam groupParam = new FormGroupParam();
        if (StringUtils.hasText(applicationId)) {
            FormApplicationInfo info = applicationInfoMapper.selectById(applicationId);
            groupParam.setProjectId(info.getProjectId());
            groupParam.setYear(info.getYear());
        } else {
            FormProject project = projectService.getById(projectId);
            groupParam.setProjectId(projectId);
            groupParam.setYear(project.getYear());
        }
        PageParam<FormGroup, FormGroupParam> page = new PageParam<>(groupParam);
        QueryWrapper<FormGroup> queryWrapper = appendWrapper(page.getOrderWrapper(), groupParam);
        return formGroupMapper.selectList(queryWrapper);
    }

    private QueryWrapper<FormGroup> appendWrapper(QueryWrapper<FormGroup> wrapper, FormGroupParam param) {
        if (param.getDicGroupIdIsNull() != null) {
            if (param.getDicGroupIdIsNull()) {
                wrapper.isNull("dic_group_id");
            } else {
                wrapper.isNotNull("dic_group_id");
            }
        }
        return wrapper;
    }
}
