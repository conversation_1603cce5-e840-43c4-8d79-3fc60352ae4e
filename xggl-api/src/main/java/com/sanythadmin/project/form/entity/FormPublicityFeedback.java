package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公示意见反馈
 *
 * <AUTHOR>
 * @since 2024-09-30 10:10:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_PUBLICITY_FEEDBACK")
@Entity
@Table(name = "SYT_FORM_PUBLICITY_FEEDBACK")
public class FormPublicityFeedback implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 问题或回复内容
     */
    @Column(name = "CONTENT")
    @TableField("CONTENT")
    private String content;

    /**
     * 公示项目ID
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 父节点ID
     */
    @Column(name = "PARENT_ID")
    @TableField("PARENT_ID")
    private String parentId;

    /**
     * 子节点ID
     */
    @Column(name = "NEXT_ID")
    @TableField("NEXT_ID")
    private String nextId;

    /**
     * 附件信息
     */
    @Column(name = "ATTACHMENT")
    @TableField("ATTACHMENT")
    private String attachment;

    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 姓名
     */
    @Column(name = "REAL_NAME")
    @TableField("REAL_NAME")
    private String realName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_DATE")
    @TableField("CREATE_DATE")
    private LocalDateTime createDate;

    /**
     * 根节点ID
     */
    @Column(name = "ROOT_ID")
    @TableField("ROOT_ID")
    private String rootId;

    /**
     * 是否已读（0：否，1: 是）
     */
    @Column(name = "READ_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("READ_FLAG")
    private JudgeMark readFlag;

}
