package com.sanythadmin.project.form.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 特殊名单人员查询参数
 *
 * <AUTHOR>
 * @since 2024-07-09 11:36:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormProjectSpecialListParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

}
