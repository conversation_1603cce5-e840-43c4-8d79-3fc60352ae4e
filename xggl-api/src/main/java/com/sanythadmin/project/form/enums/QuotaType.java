package com.sanythadmin.project.form.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * Created by JIANGPING on 2024/9/25.
 */
public enum QuotaType implements ValueTextBaseEnum {
    ME("名额", 0),
    BL("比例", 1);

    @JsonValue
    private String text;
    @EnumValue
    private Integer value;

    QuotaType(String text, Integer value) {
        this.text = text;
        this.value = value;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
