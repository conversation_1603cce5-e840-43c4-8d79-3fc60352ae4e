package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.dictionary.param.DictionaryFieldParam;
import com.sanythadmin.project.dictionary.service.DictionaryFieldService;
import com.sanythadmin.project.form.entity.*;
import com.sanythadmin.project.form.enums.ApplyState;
import com.sanythadmin.project.form.enums.CustomType;
import com.sanythadmin.project.form.enums.InfoType;
import com.sanythadmin.project.form.mapper.*;
import com.sanythadmin.project.form.param.FormApplicationInfoParam;
import com.sanythadmin.project.form.param.FormProjectParam;
import com.sanythadmin.project.form.service.FormApplicationInfoService;
import com.sanythadmin.project.form.service.FormProjectService;
import com.sanythadmin.project.form.service.FormRestrictService;
import com.sanythadmin.project.form.vo.FormProjectVO;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 表单项目Service实现
 *
 * <AUTHOR>
 * @since 2024-07-02 10:07:26
 */
@Service
@RequiredArgsConstructor
public class FormProjectServiceImpl extends ServiceImpl<FormProjectMapper, FormProject> implements FormProjectService {
    private final FormProjectMapper formProjectMapper;
    private final FormGroupMapper formGroupMapper;
    private final FormTypeMapper formTypeMapper;
    private final UserInfoMapper userInfoMapper;
    private final FormApplicationInfoService applicationInfoService;
    private final FormRestrictService formRestrictService;
    private final FormProjectSpecialListMapper projectSpecialListMapper;
    private final FormCustomFieldMapper formCustomFieldMapper;
    private final DictionaryFieldService dictionaryFieldService;

    @Transactional
    @Override
    public void edit(FormProject param) {
        String username = SecurityUtil.getUsername();
        String realName = SecurityUtil.getRealName();
        String roleScope = SecurityUtil.getRoleScope();
        FormType formType = formTypeMapper.selectOne(new LambdaQueryWrapper<FormType>().eq(FormType::getType, param.getType()));
        if (formType == null) AssertUtil.throwMessage("未设置表单业务参数信息");
        param.setCzr(username);
        param.setCzrxm(realName);
        param.setYear(formType.getYear());
        param.setUserType(formType.getUserType());
        if (StringUtils.hasText(param.getId())) {
            FormProject project = getById(param.getId());
            param.setSort(project.getSort());
            param.setSfscmb(project.getSfscmb());
            param.setSpzt(project.getSpzt());
            param.setXyid(project.getXyid());
            formProjectMapper.updateById(param);
        } else {
            String status = ConstantsWorkflow.STATE_PASS;
            if (Objects.equals(ConstantsWorkflow.ROLE_SCOPE_BY, roleScope)) {
                UserInfo userInfo = userInfoMapper.get(username);
                param.setXyid(userInfo.getXyid());
                status = ConstantsWorkflow.STATE_PENDING;
            }
            SortHelper<FormProject> sortHelper = new SortHelper<>(this, "sort");
            param.setSort(sortHelper.next());
            param.setSfscmb(JudgeMark.NO);
            param.setSpzt(status);
            formProjectMapper.insert(param);
        }

        if (StringUtils.hasText(param.getHcxmid())) {
            String[] strings = CommonUtil.split(param.getHcxmid());
            for (String id : strings) {
                FormProject project = getById(id);
                if (!Objects.isNull(project)) {
                    List<String> ids = new ArrayList<>();
                    ids.add(project.getId());
                    if (StringUtils.hasText(project.getHcxmid()))
                        ids.addAll(Arrays.asList(CommonUtil.split(project.getHcxmid())));
                    project.setHcxmid(CommonUtil.strJoin(ids));
                    formProjectMapper.updateById(project);
                }
            }
        }

        Long count = formGroupMapper.selectCount(new LambdaQueryWrapper<FormGroup>()
                .eq(FormGroup::getProjectId, param.getId()).eq(FormGroup::getInfoType, InfoType.apply)
                .eq(FormGroup::getListFlag, JudgeMark.NO).eq(FormGroup::getYear, formType.getYear()));
        if (count == 0) {
            FormGroup formGroup = new FormGroup();
            formGroup.setGroupName("申请信息");
            formGroup.setListFlag(JudgeMark.NO);
            formGroup.setProjectId(param.getId());
            formGroup.setType(param.getType());
            formGroup.setYear(formType.getYear());
            formGroup.setSort(1);
            formGroup.setInfoType(InfoType.apply);
            formGroupMapper.insert(formGroup);
        }

        count = formCustomFieldMapper.selectCount(new LambdaQueryWrapper<FormCustomField>()
                .eq(FormCustomField::getProjectId, param.getId()));
        if (count == 0) {
            DictionaryFieldParam fieldParam = new DictionaryFieldParam();
            fieldParam.setUserType(formType.getUserType());
            fieldParam.setFieldEn("xgh,xm,xb,xymc");
            List<DictionaryField> fields = dictionaryFieldService.queryList(fieldParam);
            long sort = 1;
            for (DictionaryField field : fields) {
                FormCustomField formCustomField = new FormCustomField();
                formCustomField.setProjectId(param.getId());
                formCustomField.setCustomType(CustomType.List);
                formCustomField.setFieldEn(field.getFieldEn());
                formCustomField.setFieldZh(field.getFieldZh());
                formCustomField.setInfoType(InfoType.base);
                formCustomField.setSort(sort++);
                formCustomField.setType(param.getType());
                formCustomFieldMapper.insert(formCustomField);

                FormCustomField formCustomField1 = new FormCustomField();
                BeanUtils.copyProperties(formCustomField, formCustomField1);
                formCustomField1.setId(null);
                formCustomField1.setCustomType(CustomType.Query);
                formCustomFieldMapper.insert(formCustomField1);
            }
        }
    }

    @Override
    public List<FormProject> list(FormProjectParam param) {
        PageParam<FormProject, FormProjectParam> page = new PageParam<>(param);
        return baseMapper.selectList(page.getOrderWrapper());
    }

    @Override
    public PageResult<FormProject> page(FormProjectParam param) {
        PageParam<FormProject, FormProjectParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<FormProjectVO> pageByApplicant(FormProjectParam param) {
        String username = SecurityUtil.getUsername();
        UserInfo userInfo = userInfoMapper.get(username);
        param.setUserType(userInfo.getUserType());
        param.setSfscmb(JudgeMark.YES);
        param.setSfxs(JudgeMark.YES);
        param.setSpzt(ConstantsWorkflow.STATE_PASS);
        PageParam<FormProject, FormProjectParam> pageParam = new PageParam<>(param);
        QueryWrapper<FormProject> orderWrapper = pageParam.getOrderWrapper();
        orderWrapper.lambda().and(wrapper -> wrapper.eq(FormProject::getXyid, userInfo.getXyid()).or().isNull(FormProject::getXyid));
        List<FormProject> formProjects = formProjectMapper.selectList(orderWrapper);
        List<FormProjectSpecialList> specialLists = new ArrayList<>();
        if (!CollectionUtils.isEmpty(formProjects)) {
            List<String> projectId = formProjects.stream().map(FormProject::getId).toList();
            specialLists = projectSpecialListMapper.selectList(new LambdaQueryWrapper<FormProjectSpecialList>()
                    .eq(FormProjectSpecialList::getXgh, userInfo.getXgh())
                    .in(FormProjectSpecialList::getProjectId, projectId));
        }


        // 条件过滤，逻辑分页
        List<FormProject> list = new ArrayList<>();
        if (Objects.equals(ApplyState.ksq, param.getSqzt())) {
            for (FormProject project : formProjects) {
                String error = applicationInfoService.checkApplyTime(project);
                if (StringUtils.hasText(error)) continue;
                error = applicationInfoService.checkLimit(username, project);
                if (StringUtils.hasText(error)) continue;
                Optional<FormProjectSpecialList> optional = specialLists.stream().filter(
                        obj -> Objects.equals(obj.getProjectId(), project.getId())
                                && Objects.equals(project.getYear(), obj.getYear())).findAny();
                if (optional.isPresent()) {
                    list.add(project);
                    continue;
                }
                error = applicationInfoService.checkPreItem(username, project);
                if (StringUtils.hasText(error)) continue;
                error = formRestrictService.restrictCheck(project.getId(), userInfo, false);
                if (StringUtils.hasText(error)) continue;
                list.add(project);
            }
        } else if (Objects.equals(ApplyState.ysq, param.getSqzt())) {
            for (FormProject project : formProjects) {
                String error = applicationInfoService.checkLimit(username, project);
                if (StringUtils.hasText(error))
                    list.add(project);
            }
        } else if (Objects.equals(ApplyState.bksq, param.getSqzt())) {
            for (FormProject project : formProjects) {
                String error = applicationInfoService.checkApplyTime(project);
                if (StringUtils.hasText(error)) {
                    list.add(project);
                    continue;
                }
                error = applicationInfoService.checkPreItem(username, project);
                if (StringUtils.hasText(error)) {
                    list.add(project);
                    continue;
                }
                error = applicationInfoService.checkLimit(username, project);
                if (StringUtils.hasText(error)) {
                    list.add(project);
                    continue;
                }

                error = formRestrictService.restrictCheck(project.getId(), userInfo, false);
                if (StringUtils.hasText(error))
                    list.add(project);
            }
        }

        int total = list.size();
        list = CommonUtil.pageList(list, param.getPage().intValue(), param.getLimit().intValue());
        List<FormProjectVO> formProjectVOS = new ArrayList<>();
        for (FormProject project : list) {
            FormApplicationInfoParam infoParam = new FormApplicationInfoParam();
            infoParam.setProjectId(project.getId());
            infoParam.setYear(project.getYear());
            infoParam.setSfzc(JudgeMark.NO);
            PageParam<FormApplicationInfo, FormApplicationInfoParam> formProjectParamPageParam = new PageParam<>(infoParam);
            Long applyCount = applicationInfoService.count(formProjectParamPageParam.getWrapper());//.getApplyCount(infoParam);
            FormProjectVO formProjectVO = new FormProjectVO();
            BeanUtils.copyProperties(project, formProjectVO);
            formProjectVO.setApplyCount(applyCount);
            formProjectVOS.add(formProjectVO);
        }
        return new PageResult<>(formProjectVOS, (long) total);
    }

    @Override
    public PageResult<FormProject> pageByReviewer(FormProjectParam param) {
        param.setSpzt(ConstantsWorkflow.STATE_PASS);
        String s = CommonUtil.str2SqlIn(SecurityUtil.getUsername(), SecurityUtil.getRoleId());
        PageParam<FormProject, FormProjectParam> page = new PageParam<>(param);
        QueryWrapper<FormProject> wrapper = page.getOrderWrapper();
        wrapper.inSql("id", "select w.project_id from syt_workflow w where exists " +
                "(select 1 from syt_workflow_node_approver where approver_id in (" + s + ") and w.id = workflow_id)");
        page = formProjectMapper.selectPage(page, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
