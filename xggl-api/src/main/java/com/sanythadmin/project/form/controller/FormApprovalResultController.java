package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.form.service.FormApprovalResultService;
import com.sanythadmin.project.form.entity.FormApprovalResult;
import com.sanythadmin.project.form.param.FormApprovalResultParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 表单管理模块/审批结果记录控制器
 *
 * <AUTHOR>
 * @since 2024-08-13 11:41:42
 */
@Slf4j
@RestController
@RequestMapping("/api/form/form-approval-result")
public class FormApprovalResultController extends BaseController {
    @Resource
    private FormApprovalResultService formApprovalResultService;

    /**
     * 分页查询审批结果记录（权限标识：form:formApprovalResult:list）
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:list')")
    @GetMapping("/page")
    public PageResult<FormApprovalResult> page(FormApprovalResultParam param) {
        return formApprovalResultService.page(param);
    }

    /**
     * 查询全部审批结果记录（权限标识：form:formApprovalResult:list）
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:list')")
    @GetMapping()
    public List<FormApprovalResult> list(FormApprovalResultParam param) {
        PageParam<FormApprovalResult, FormApprovalResultParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return formApprovalResultService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询审批结果记录（权限标识：form:formApprovalResult:list）
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:list')")
    @GetMapping("/{id}")
    public FormApprovalResult get(@PathVariable("id") String id) {
        return formApprovalResultService.getById(id);
    }

    /**
     * 添加或修改审批结果记录（权限标识：form:formApprovalResult:operation）
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:operation')")
    @OperationLog(module = "审批结果记录", comments = "保存审批结果记录")
    @PostMapping("/operation")
    public void save(@RequestBody FormApprovalResult formApprovalResult) {
        if (StringUtils.hasLength(formApprovalResult.getId())) {
            formApprovalResultService.updateById(formApprovalResult);
        } else {
            formApprovalResultService.save(formApprovalResult);
        }
    }

    /**
     * 批量删除审批结果记录（权限标识：form:formApprovalResult:remove）
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:remove')")
    @OperationLog(module = "审批结果记录", comments = "批量删除审批结果记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        formApprovalResultService.removeByIds(ids);
    }

    /**
     * 数据导出
     */
    @PreAuthorize("hasAuthority('form:formApprovalResult:exportData')")
    @GetMapping("/exportData")
    public void exportData(FormApprovalResultParam param, HttpServletRequest request, HttpServletResponse response) {
        try {
            String fileName = request.getParameter("fileName");
            List<FormApprovalResult> list = formApprovalResultService.list(param);
            fileName = StringUtils.hasLength(fileName) ? fileName : "审批结果记录";
            String[] title = {"学/工号", "姓名", "性别", "学院", "专业", "班级", "年级", "校区", "项目名称", "年份", "是否集体项目"};
            String[] property = {"xgh", "xm", "xb", "xymc", "zymc", "bjmc", "njmc", "xqmc", "xmmc", "year", "sfjtxm"};
            EasyExcelHelper.exportExcel(list, title, property, response, null, fileName);
        } catch (Exception e) {
            log.error("导出数据失败", e);
        }
    }
}
