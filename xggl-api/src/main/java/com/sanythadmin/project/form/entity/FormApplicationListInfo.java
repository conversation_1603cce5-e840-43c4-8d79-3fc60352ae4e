package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
/**
 * 表单列表申请记录
 *
 * <AUTHOR>
 * @since 2024-07-02 10:36:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_APPLICATION_LIST_INFO")
@Entity
@Table(name = "SYT_FORM_APPLICATION_LIST_INFO")
public class FormApplicationListInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 表单项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 学工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    @Column(name = "C1")
    @TableField("C1")
    private String c1;

    @Column(name = "C2")
    @TableField("C2")
    private String c2;

    @Column(name = "C3")
    @TableField("C3")
    private String c3;

    @Column(name = "C4")
    @TableField("C4")
    private String c4;

    @Column(name = "C5")
    @TableField("C5")
    private String c5;

    @Column(name = "C6")
    @TableField("C6")
    private String c6;

    @Column(name = "C7")
    @TableField("C7")
    private String c7;

    @Column(name = "C8")
    @TableField("C8")
    private String c8;

    @Column(name = "C9")
    @TableField("C9")
    private String c9;

    /**
     * 关联申请记录ID
     */
    @Column(name = "APPLICATION_ID")
    @TableField("APPLICATION_ID")
    private String applicationId;

    /**
     * 申请记录所在分组
     */
    @Column(name = "GROUP_ID")
    @TableField("GROUP_ID")
    private String groupId;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
