package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.form.enums.CustomType;
import com.sanythadmin.project.form.enums.InfoType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 审核列表自定义列/自定义查询条件字段
 *
 * <AUTHOR>
 * @since 2024-09-04 15:23:57
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_CUSTOM_FIELD")
@Entity
@Table(name = "SYT_FORM_CUSTOM_FIELD")
public class Form<PERSON>ustomField implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 排序值
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 字段类型来源 0: 用户基础字段， 1: 用户多条信息组,  2: 申请表单字段
     */
    @Column(name = "INFO_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("INFO_TYPE")
    private InfoType infoType;

    /**
     * 自定义字段类型
     */
    @Column(name = "CUSTOM_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("CUSTOM_TYPE")
    private CustomType customType;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
