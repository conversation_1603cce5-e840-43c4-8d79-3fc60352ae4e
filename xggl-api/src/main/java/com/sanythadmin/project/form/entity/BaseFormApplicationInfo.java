package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * Created by JIANGPING on 2024/7/16.
 */
@Data
@MappedSuperclass
public class BaseFormApplicationInfo extends BaseApplicationInfo {

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 院系ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.CHAR_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 是否暂存（是：1， 否：0）
     */
    @Column(name = "SFZC")
    @TableField("SFZC")
    private JudgeMark sfzc;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 定位经度
     */
    @Column(name = "LOCATION_LONGITUDE")
    @TableField("LOCATION_LONGITUDE")
    private String locationLongitude;

    /**
     * 定位纬度
     */
    @Column(name = "LOCATION_LATITUDE")
    @TableField("LOCATION_LATITUDE")
    private String locationLatitude;

    /**
     * 定位地址
     */
    @Column(name = "LOCATION_ADDRESS")
    @TableField("LOCATION_ADDRESS")
    private String locationAddress;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE")
    @TableField("USER_TYPE")
    private UserType userType;

    /**
     * 审批结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPJG", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SPJG")
    private ReviewResult spjg;

    @TableField(exist = false)
    @Transient
    private UserInfo userInfo;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
