package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 节点审批记录
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_APPROVAL_NODE_RECORD")
@Entity
@Table(name = "SYT_FORM_APPROVAL_NODE_RECORD")
public class FormApprovalNodeRecord extends BaseApprovalNodeRecord {

}
