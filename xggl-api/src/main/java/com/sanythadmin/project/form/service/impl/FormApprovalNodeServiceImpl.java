package com.sanythadmin.project.form.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.form.entity.FormApprovalNode;
import com.sanythadmin.project.form.mapper.FormApprovalNodeMapper;
import com.sanythadmin.project.form.service.FormApprovalNodeService;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 申请审批节点Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Service
@RequiredArgsConstructor
public class FormApprovalNodeServiceImpl extends ServiceImpl<FormApprovalNodeMapper, FormApprovalNode> implements FormApprovalNodeService {

    private final FormApprovalNodeMapper mapper;

    @Override
    public List<FormApprovalNode> list(WorkflowApprovalNodeParam param) {
        PageParam<FormApprovalNode, WorkflowApprovalNodeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return mapper.selectList(page.getOrderWrapper());
    }
}
