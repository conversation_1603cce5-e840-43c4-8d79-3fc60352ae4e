package com.sanythadmin.project.form.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 通用表单业务类型
 *
 * <AUTHOR>
 * @since 2024-07-01 16:53:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FORM_TYPE")
@Entity
@Table(name = "SYT_FORM_TYPE")
public class FormType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 业务名称
     */
    @NotEmpty(message = "业务名称不能为空")
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 业务类型
     */
    @NotEmpty(message = "业务类型不能为空")
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 年份
     */
    @NotEmpty(message = "年份不能为空")
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 用户类型
     */
    @Column(name = "USER_TYPE")
    @TableField("USER_TYPE")
    private UserType userType;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;
}
