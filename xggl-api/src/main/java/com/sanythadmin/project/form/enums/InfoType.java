package com.sanythadmin.project.form.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * 表单字段类别
 * Created by JIANGPING on 2024/7/17.
 */
public enum InfoType implements ValueTextBaseEnum {
    base(0, "base", "用户基础信息"),
    list(1, "list", "多条信息"),
    apply(2, "apply", "申请表单");

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;
    private String remark;

    InfoType(Integer value, String text, String remark) {
        this.value = value;
        this.text = text;
        this.remark = remark;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
