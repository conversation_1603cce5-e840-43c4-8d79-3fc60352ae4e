package com.sanythadmin.project.form.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.code.service.CodeZybService;
import com.sanythadmin.project.form.entity.FormLimitQuota;
import com.sanythadmin.project.form.param.FormLimitQuotaParam;
import com.sanythadmin.project.form.service.FormLimitQuotaService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 表单管理模块/项目限制名额配置控制器
 *
 * <AUTHOR>
 * @since 2024-09-25 10:25:42
 */
@RestController
@RequestMapping("/api/form/form-limit-quota")
public class FormLimitQuotaController extends BaseController {
    @Resource
    private FormLimitQuotaService formLimitQuotaService;
    @Resource
    private CodeBjbService codeBjbService;
    @Resource
    private CodeZybService codeZybService;

    /**
     * 分页查询限制名额配置
     */
    @GetMapping("/page")
    public PageResult<FormLimitQuota> page(FormLimitQuotaParam param) {
        PageParam<FormLimitQuota, FormLimitQuotaParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = formLimitQuotaService.page(page, page.getWrapper());
        UserInfoUtil.codeTextSet(page.getRecords());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部限制名额配置
     */
    @GetMapping()
    public List<FormLimitQuota> list(FormLimitQuotaParam param) {
        PageParam<FormLimitQuota, FormLimitQuotaParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<FormLimitQuota> list = formLimitQuotaService.list(page.getOrderWrapper());
        UserInfoUtil.codeTextSet(list);
        return list;
    }

    /**
     * 根据id查询限制名额配置
     */
    @GetMapping("/{id}")
    public FormLimitQuota get(@PathVariable("id") String id) {
        return formLimitQuotaService.getById(id);
    }

    /**
     * 添加或修改限制名额配置（权限标识：form:formLimitQuota:operation）
     */
    @PreAuthorize("hasAuthority('form:formLimitQuota:operation')")
    @OperationLog(module = "限制名额配置", comments = "保存限制名额配置")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody FormLimitQuota formLimitQuota) {
        if (StringUtils.hasLength(formLimitQuota.getId())) {
            List<FormLimitQuota> list = getFormLimitQuotas(formLimitQuota);
            if (!CollectionUtils.isEmpty(list) && !Objects.equals(list.get(0).getId(), formLimitQuota.getId()))
                AssertUtil.throwMessage("该限制名额范围已存在");
            formLimitQuotaService.updateById(formLimitQuota);
        } else {
            List<FormLimitQuota> quotas = new ArrayList<>();
            if (StringUtils.hasText(formLimitQuota.getBjid())) {
                String[] strings = CommonUtil.split(formLimitQuota.getBjid());
                for (String id : strings) {
                    CodeBjb codeBjb = codeBjbService.getById(id);
                    FormLimitQuota quota = getFormLimitQuota(formLimitQuota, id, codeBjb.getZyid(), codeBjb.getXyid(), codeBjb.getNjid(), null);
                    quotas.add(quota);
                }
                quotas = getFormLimitQuota(quotas, formLimitQuota.getPyccid(), "pyccid");
            } else if (StringUtils.hasText(formLimitQuota.getZyid())) {
                String[] strings = CommonUtil.split(formLimitQuota.getZyid());
                for (String id : strings) {
                    CodeZyb codeZyb = codeZybService.getById(id);
                    FormLimitQuota quota = getFormLimitQuota(formLimitQuota, null, id, codeZyb.getXyid(), null, null);
                    quotas.add(quota);
                }
                quotas = getFormLimitQuota(quotas, formLimitQuota.getNjid(), "njid");
                quotas = getFormLimitQuota(quotas, formLimitQuota.getPyccid(), "pyccid");
            } else if (StringUtils.hasText(formLimitQuota.getXyid())) {
                String[] strings = CommonUtil.split(formLimitQuota.getXyid());
                for (String id : strings) {
                    FormLimitQuota quota = getFormLimitQuota(formLimitQuota, null, null, id, null, null);
                    quotas.add(quota);
                }
                quotas = getFormLimitQuota(quotas, formLimitQuota.getNjid(), "njid");
                quotas = getFormLimitQuota(quotas, formLimitQuota.getPyccid(), "pyccid");
            } else if (StringUtils.hasText(formLimitQuota.getNjid())) {
                String[] strings = CommonUtil.split(formLimitQuota.getNjid());
                for (String id : strings) {
                    FormLimitQuota quota = getFormLimitQuota(formLimitQuota, null, null, null, id, null);
                    quotas.add(quota);
                }
                quotas = getFormLimitQuota(quotas, formLimitQuota.getPyccid(), "pyccid");
            } else if (StringUtils.hasText(formLimitQuota.getPyccid())) {
                String[] strings = CommonUtil.split(formLimitQuota.getPyccid());
                for (String id : strings) {
                    FormLimitQuota quota = getFormLimitQuota(formLimitQuota, null, null, null, null, id);
                    quotas.add(quota);
                }
            }

            for (FormLimitQuota quota : quotas) {
                List<FormLimitQuota> list = getFormLimitQuotas(quota);
                if (!CollectionUtils.isEmpty(list))
                    continue;
                formLimitQuotaService.save(quota);
            }
        }
    }

    private List<FormLimitQuota> getFormLimitQuotas(FormLimitQuota quota) {
        List<String> fieldList = Arrays.asList("bjid", "zyid", "xyid", "njid", "pyccid");
        FormLimitQuotaParam param = new FormLimitQuotaParam();
        BeanUtils.copyProperties(quota, param);
        for (String s : fieldList) {
            Object value = CommonUtil.getValue(s, param);
            if (value == null)
                CommonUtil.setValue(param, s + "IsNull", true);
        }
        return list(param);
    }

    private List<FormLimitQuota> getFormLimitQuota(List<FormLimitQuota> list, String codeIds, String fieldName) {
        if (!StringUtils.hasText(codeIds))
            return list;
        List<FormLimitQuota> newlist = new ArrayList<>();
        String[] strings = CommonUtil.split(codeIds);
        for (FormLimitQuota limitQuota : list) {
            for (String id : strings) {
                FormLimitQuota obj = new FormLimitQuota();
                BeanUtils.copyProperties(limitQuota, obj);
                CommonUtil.setValue(obj, fieldName, id);
                newlist.add(obj);
            }
        }
        return newlist;
    }


    private FormLimitQuota getFormLimitQuota(FormLimitQuota param, String bjid, String zyid, String xyid, String njid, String pyccid) {
        FormLimitQuota obj = new FormLimitQuota();
        obj.setProjectId(param.getProjectId());
        obj.setYear(param.getYear());
        obj.setQuota(param.getQuota());
        obj.setQuotaType(param.getQuotaType());
        obj.setBjid(bjid);
        obj.setZyid(zyid);
        obj.setXyid(xyid);
        obj.setNjid(njid);
        obj.setPyccid(pyccid);
        return obj;
    }

    /**
     * 批量删除限制名额配置（权限标识：form:formLimitQuota:remove）
     */
    @PreAuthorize("hasAuthority('form:formLimitQuota:remove')")
    @OperationLog(module = "限制名额配置", comments = "批量删除限制名额配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        formLimitQuotaService.removeByIds(ids);
    }
}
