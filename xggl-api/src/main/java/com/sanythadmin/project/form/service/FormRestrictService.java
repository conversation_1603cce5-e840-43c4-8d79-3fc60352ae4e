package com.sanythadmin.project.form.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormProject;
import com.sanythadmin.project.form.entity.FormRestrict;

import java.util.List;
import java.util.Map;

/**
 * 表单限制条件Service
 *
 * <AUTHOR>
 * @since 2024-09-06 15:54:27
 */
public interface FormRestrictService extends IService<FormRestrict> {

    public String restrictCheck(String projectId, UserInfo userInfo);

    public String restrictCheck(String projectId, UserInfo userInfo, boolean checkAll);

    public List<Map<String, Object>> groupCountByProject(FormProject... projects);
}
