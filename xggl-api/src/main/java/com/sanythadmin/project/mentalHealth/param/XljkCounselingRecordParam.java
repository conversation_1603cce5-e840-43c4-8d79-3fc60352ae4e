package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 心理咨询记录查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCounselingRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 预约记录ID
     */
    @QueryField(type = QueryType.EQ)
    private String appointmentInfoId;

    /**
     * 咨询地点
     */
    private String zxdd;


    /**
     * 操作人
     */
    private String czr;

    /**
     * 操作人账号
     */
    private String czrzh;


//
//    /**
//     * 期待解决的问题
//     */
//    private String qdjjdwt;
//
//    /**
//     * （咨询过程记录）采用的咨询方法和技术
//     */
//    private String xlcsffhjs;
//
//    /**
//     * （咨询过程记录）到访者反馈与反应
//     */
//    private String dfzfkyfy;
//
//    /**
//     * （建议与计划）后续跟进计划（如是否需要进一步咨询、转介等）
//     */
//    private String hxgjjh;
//
//    /**
//     * （总结与备注）备注信息
//     */
//    private String bzxx;
//
//
//    /**
//     * 是否存在自杀自伤倾向
//     */
//    @QueryField(type = QueryType.EQ)
//    private JudgeMark sfczzszsqx;
//
//    /**
//     * （咨询过程记录）讨论的主要内容
//     */
//    private String tldzynr;
//
//    /**
//     * （评估与分析）咨询师的观察与评估（包括学生的情绪状态、行为表现等）
//     */
//    private String zxsgcypg;
//
//    /**
//     * （评估与分析）初步诊断或问题分析
//     */
//    private String cbzdhwtfx;
//
//    /**
//     * （建议与计划）给予的建议或指导
//     */
//    private String gydjyhzd;
//
//    /**
//     * （总结与备注）咨询师总结
//     */
//    private String zxszj;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "UPDATE_TIME DESC";
    }
}
