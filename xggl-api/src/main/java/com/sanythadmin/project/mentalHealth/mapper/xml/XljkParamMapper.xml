<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkParamMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SYCS, NZDYYCS, ZQTYS
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.sycs != null">
                        AND a.SYCS = #{param.sycs}
                    </if>
                    <if test="param.nzdyycs != null">
                        AND a.NZDYYCS = #{param.nzdyycs}
                    </if>
                    <if test="param.zqtys != null">
                        AND a.ZQTYS LIKE concat(concat('%',#{param.zqtys), '%')
                    </if>
    </sql>
</mapper>
