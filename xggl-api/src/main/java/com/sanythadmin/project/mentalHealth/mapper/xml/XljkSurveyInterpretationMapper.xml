<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkSurveyInterpretationMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XSID, SID, MIN_SCORE, MIN_SCORE_OPERATOR, MAX_SCORE, MAX_SCORE_OPERATOR, REMARK, ITEM_NAME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.xsid != null">
            AND a.XSID LIKE concat(concat('%',#{param.xsid), '%')
        </if>
        <if test="param.sid != null">
            AND a.SID LIKE concat(concat('%',#{param.sid), '%')
        </if>
        <if test="param.minScore != null">
            AND a.MIN_SCORE = #{param.minScore}
        </if>
        <if test="param.minScoreOperator != null">
            AND a.MIN_SCORE_OPERATOR LIKE concat(concat('%',#{param.minScoreOperator), '%')
        </if>
        <if test="param.maxScore != null">
            AND a.MAX_SCORE = #{param.maxScore}
        </if>
        <if test="param.maxScoreOperator != null">
            AND a.MAX_SCORE_OPERATOR LIKE concat(concat('%',#{param.maxScoreOperator), '%')
        </if>
        <if test="param.remark != null">
            AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
        </if>
        <if test="param.itemName != null">
            AND a.ITEM_NAME LIKE concat(concat('%',#{param.itemName), '%')
        </if>
    </sql>
</mapper>
