package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 心理危机上报审核记录查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCrisisReportApprovalNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 角色名称
     */
    @QueryField(type = QueryType.EQ)
    private String roleName;

    /**
     * 审核结果
     */
    @QueryField(type = QueryType.EQ)
    private String result;

    /**
     * 危机上报ID
     */
    @QueryField(type = QueryType.EQ)
    private String crisisReportId;

    /**
     * 审核意见
     */
    private String remark;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sort asc";
    }
}
