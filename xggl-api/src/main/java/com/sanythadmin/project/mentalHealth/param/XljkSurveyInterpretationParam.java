package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 测评卷测评结果解释查询参数
 *
 * <AUTHOR>
 * @since 2025-04-25 10:55:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkSurveyInterpretationParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 心理问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String xsid;

    /**
     * 问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 分数解释
     */
    private String remark;

    /**
     * 指标名称
     */
    private String itemName;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "create_time asc";
    }
}
