package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyReportService;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyReport;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyReportParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 心理测评卷测评报告控制器
 *
 * <AUTHOR>
 * @since 2025-04-27 14:37:31
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey-report")
public class XljkSurveyReportController extends BaseController {
    @Resource
    private XljkSurveyReportService xljkSurveyReportService;

    /**
     * 分页查询心理测评卷测评报告（权限标识：mentalHealth:xljkSurveyReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyReport:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyReport> page(XljkSurveyReportParam param) {
        PageParam<XljkSurveyReport, XljkSurveyReportParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkSurveyReportService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理测评卷测评报告（权限标识：mentalHealth:xljkSurveyReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyReport:list')")
    @GetMapping()
    public List<XljkSurveyReport> list(XljkSurveyReportParam param) {
        PageParam<XljkSurveyReport, XljkSurveyReportParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkSurveyReportService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理测评卷测评报告（权限标识：mentalHealth:xljkSurveyReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyReport:list')")
    @GetMapping("/{id}")
    public XljkSurveyReport get(@PathVariable("id") String id) {
        return xljkSurveyReportService.getById(id);
    }

    /**
     * 添加或修改心理测评卷测评报告（权限标识：mentalHealth:xljkSurveyReport:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyReport:operation')")
    @OperationLog(module = "心理测评卷测评报告", comments = "保存心理测评卷测评报告")
    @PostMapping("/operation")
    public void save(@RequestBody XljkSurveyReport xljkSurveyReport) {
        if (StringUtils.hasLength(xljkSurveyReport.getId())) {
            xljkSurveyReportService.updateById(xljkSurveyReport);
        } else {
            xljkSurveyReportService.save(xljkSurveyReport);
        }
    }

    /**
     * 批量删除心理测评卷测评报告（权限标识：mentalHealth:xljkSurveyReport:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyReport:remove')")
    @OperationLog(module = "心理测评卷测评报告", comments = "批量删除心理测评卷测评报告")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkSurveyReportService.removeByIds(ids);
    }
}
