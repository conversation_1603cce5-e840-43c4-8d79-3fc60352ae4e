package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import com.sanythadmin.project.mentalHealth.service.XljkConsultantService;
import com.sanythadmin.project.teacherEvaluate.dto.AnswerInfoDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 心理健康教育/咨询师信息控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 16:44:47
 */
@Slf4j
@RestController
@RequestMapping("/api/mentalHealth/xljk-consultant")
public class XljkConsultantController extends BaseController {
    @Resource
    private XljkConsultantService xljkConsultantService;

    /**
     * 分页查询咨询师信息（权限标识：mentalHealth:xljkConsultant:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:list')")
    @GetMapping("/page")
    public PageResult<XljkConsultant> page(XljkConsultantParam param) {
        return xljkConsultantService.page(param);
    }

    /**
     * 查询全部咨询师信息（权限标识：mentalHealth:xljkConsultant:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:list')")
    @GetMapping()
    public List<XljkConsultant> list(XljkConsultantParam param) {
        return xljkConsultantService.list(param);
    }

    /**
     * 根据id查询咨询师信息（权限标识：mentalHealth:xljkConsultant:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:list')")
    @GetMapping("/{id}")
    public XljkConsultant get(@PathVariable("id") String id) {
        return xljkConsultantService.get(id);
    }

    /**
     * 添加或修改咨询师信息（权限标识：mentalHealth:xljkConsultant:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:operation')")
    @OperationLog(module = "咨询师信息", comments = "保存咨询师信息")
    @PostMapping("/operation")
    public void save(MultipartHttpServletRequest request) {
        xljkConsultantService.saveConsultant(request);
    }

    /**
     * 批量删除咨询师信息（权限标识：mentalHealth:xljkConsultant:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:remove')")
    @OperationLog(module = "咨询师信息", comments = "批量删除咨询师信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            xljkConsultantService.removeConsultant(id);
        }
    }

    /**
     * 根据学工号获取咨询师信息
     *
     * @param xgh
     * @returnhn
     */
    @GetMapping("/getConsultant")
    public XljkConsultant getConsultant(String xgh) {
        XljkConsultantParam param = new XljkConsultantParam();
        param.setXgh(xgh);
        List<XljkConsultant> list = xljkConsultantService.list(param);
        return CommonUtil.listGetOne(list);
    }

    /**
     * 导入模块下载（权限标识：mentalHealth:xljkConsultant:import）
     *
     * @param response
     */
//    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:import')")
//    @OperationLog(module = "咨询师信息", comments = "导入模板下载")
//    @GetMapping("/exportTemplate")
//    public void exportTemplate(HttpServletResponse response) {
//        try {
//            String[] titles = {/*"测评年份（必填）",*/ "学号（必填）", "姓名", "成绩（必填）"};
//            EasyExcelHelper.exportExcel(null, titles, null, response, null);
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//    }

    /**
     * 咨询师数据导入（权限标识：mentalHealth:xljkConsultant:import）
     *
     * @param file
     */
//    @PreAuthorize("hasAuthority('mentalHealth:xljkConsultant:import')")
//    @OperationLog(module = "咨询师信息", comments = "咨询师信息导入")
//    @PostMapping("/importData")
//    public void importData(@RequestParam(name = "file") MultipartFile file) {
//
//    }
}
