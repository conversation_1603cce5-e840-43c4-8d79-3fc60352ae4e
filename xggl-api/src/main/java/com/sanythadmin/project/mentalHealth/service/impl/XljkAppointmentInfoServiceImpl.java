package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfoLog;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoLogMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCounselorScheduleMapper;
import com.sanythadmin.project.mentalHealth.param.XljkAppointmentInfoParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkAppointmentInfoDTO;
import com.sanythadmin.project.mentalHealth.service.XljkAppointmentInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 正式咨询预约记录Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkAppointmentInfoServiceImpl extends ServiceImpl<XljkAppointmentInfoMapper, XljkAppointmentInfo> implements XljkAppointmentInfoService {
    @Resource
    private XljkAppointmentInfoMapper mapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private XljkAppointmentInfoLogMapper appointmentInfoLogMapper;
    @Resource
    private XljkCounselorScheduleMapper counselorScheduleMapper;

    @Override
    public PageResult<XljkAppointmentInfo> page(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam) {
        MyMPJLambdaWrapper<XljkAppointmentInfo, XljkAppointmentInfoParam> wrapper = getJoinWrapper(param, scheduleParam, null);
        Page<XljkAppointmentInfo> page = wrapper.getPage();
        page = mapper.selectJoinPage(page, XljkAppointmentInfo.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<XljkAppointmentInfoDTO> pagePerson(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam, UserInfoParam userInfo) {
        List<SFunction<XljkAppointmentInfo, ?>> sFunctions = new ArrayList<>();
        sFunctions.add(XljkAppointmentInfo::getXgh);
        sFunctions.add(XljkAppointmentInfo::getXm);
        sFunctions.add(XljkAppointmentInfo::getXb);
        sFunctions.add(XljkAppointmentInfo::getXymc);
        if (scheduleParam != null)
            scheduleParam.setSort(null);
        param.setSort("MAX(t.CREATE_TIME) DESC");
        Class<UserInfo> userInfoClass = UserInfo.class;
        MyMPJLambdaWrapper<XljkAppointmentInfo, XljkAppointmentInfoParam> wrapper = getJoinWrapper(param, scheduleParam, sFunctions);
        wrapper.leftJoin(userInfoClass,UserInfo::getXgh,  XljkAppointmentInfo::getXgh);
        wrapper.select("MAX(t.CREATE_TIME) AS CREATE_TIME", "COUNT(*) AS TOTAL").groupBy(sFunctions);
        wrapper.buildQueryCondition(userInfoClass, null, userInfo);
        Page<XljkAppointmentInfoDTO> page = new Page<>(param.getPage(), param.getLimit());
        page = mapper.selectJoinPage(page, XljkAppointmentInfoDTO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }


    private MyMPJLambdaWrapper<XljkAppointmentInfo, XljkAppointmentInfoParam> getJoinWrapper(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam,
                                                                                             List<SFunction<XljkAppointmentInfo, ?>> sFunctions) {
        MyMPJLambdaWrapper<XljkAppointmentInfo, XljkAppointmentInfoParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.leftJoin(XljkCounselorSchedule.class, XljkCounselorSchedule::getId, XljkAppointmentInfo::getCounselorScheduleId);
        if (sFunctions != null) {
            wrapper.select(sFunctions.toArray(new SFunction[]{}));
        } else {
            wrapper.selectAll(XljkAppointmentInfo.class);
        }
        if (scheduleParam != null)
            wrapper.buildQueryCondition(XljkCounselorSchedule.class, null, scheduleParam);
        return wrapper;
    }

    @Override
    public List<XljkAppointmentInfo> list(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam) {
        MyMPJLambdaWrapper<XljkAppointmentInfo, XljkAppointmentInfoParam> wrapper = getJoinWrapper(param, scheduleParam, null);
        return mapper.selectJoinList(XljkAppointmentInfo.class, wrapper);
    }

    @Transactional
    @Override
    public void operation(XljkAppointmentInfo appointmentInfo) {
        UserInfo userInfo = userInfoMapper.selectById(SecurityUtil.getUsername());
        UserInfoUtil.codeTextSet(Collections.singletonList(userInfo));
        appointmentInfo.setXgh(userInfo.getXgh());
        appointmentInfo.setXm(userInfo.getXm());
        appointmentInfo.setXb(userInfo.getXb());
        appointmentInfo.setXymc(userInfo.getXymc());
        appointmentInfo.setZymc(userInfo.getZymc());
        appointmentInfo.setSjh(userInfo.getSjh());
        appointmentInfo.setSftyyszc(JudgeMark.YES);
        appointmentInfo.setStatus(Constants.YUYUEZHONG);
        appointmentInfo.setCreateTime(LocalDateTime.now());
        if (StringUtils.hasText(appointmentInfo.getId())) {
            XljkAppointmentInfo xljkAppointmentInfo = mapper.selectById(appointmentInfo.getId());
            if (!Constants.YUYUEZHONG.equals(xljkAppointmentInfo.getStatus()))
                AssertUtil.throwMessage("操作失败, 当前状态预约记录不可编辑");
            mapper.updateById(appointmentInfo);
        } else {
            Object[] statusArray = {Constants.YUYUEZHONG, Constants.YIQUEREN, Constants.YIGAIQI};
            Long count = mapper.selectCount(new LambdaQueryWrapper<XljkAppointmentInfo>()
                    .eq(XljkAppointmentInfo::getXgh, appointmentInfo.getXgh())
                    .in(XljkAppointmentInfo::getStatus, statusArray));
            if (count > 0)
                AssertUtil.throwMessage("操作失败, 当前有未完成的预约记录");
            mapper.insert(appointmentInfo);
        }
    }

    @Transactional
    @Override
    public void updateStatus(XljkAppointmentInfoLog log) {
        XljkAppointmentInfo appointmentInfo = mapper.selectById(log.getAppointmentInfoId());
        if (appointmentInfo != null) {
            String username = SecurityUtil.getUsername();
            if (Constants.YIQUEREN.equals(appointmentInfo.getStatus()) ||
                    Constants.YISHUANGYUE.equals(appointmentInfo.getStatus())) {
                Long count = counselorScheduleMapper.selectCount(new LambdaQueryWrapper<XljkCounselorSchedule>()
                        .eq(XljkCounselorSchedule::getXgh, username)
                        .eq(XljkCounselorSchedule::getId, appointmentInfo.getCounselorScheduleId()));
                if (!(count > 0 || Constants.ROLE_SCOPE_QX.equals(SecurityUtil.getRoleScope())))
                    AssertUtil.throwMessage("无操作权限");
            }

            appointmentInfo.setStatus(log.getStatus());
            mapper.updateById(appointmentInfo);
            log.setCreateTime(LocalDateTime.now());
            log.setCzrxm(SecurityUtil.getRealName());
            log.setCzrzh(username);
            appointmentInfoLogMapper.insert(log);
        }
    }

    @Transactional
    @Override
    public void removeAppointmentInfo(String id) {
        mapper.deleteById(id);
        appointmentInfoLogMapper.delete(new LambdaQueryWrapper<XljkAppointmentInfoLog>()
                .eq(XljkAppointmentInfoLog::getAppointmentInfoId, id));
    }
}
