<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, XM, XB, CSRQ, PYCCMC, XYMC, NJMC, ZYMC, BJMC, PYCCID, XYID, ZYID, BJID, NJID, LXDH, WJLX, WJSJMS, WJFSSJ, WJFSDD, SJRY, SFSJSMWX, SFCZZSZCJH, SFCZZSZCCS, SFXYLJGY, SBRXM, YDSRGX, LXFS, SFYLXXXXLZX, SFJYQXQZYBZ, SFZJ, SFYXTRAQ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xb != null">
                        AND a.XB = #{param.xb}
                    </if>
                    <if test="param.csrq != null">
                        AND a.CSRQ LIKE concat(concat('%',#{param.csrq), '%')
                    </if>
                    <if test="param.pyccmc != null">
                        AND a.PYCCMC LIKE concat(concat('%',#{param.pyccmc), '%')
                    </if>
                    <if test="param.xymc != null">
                        AND a.XYMC LIKE concat(concat('%',#{param.xymc), '%')
                    </if>
                    <if test="param.njmc != null">
                        AND a.NJMC LIKE concat(concat('%',#{param.njmc), '%')
                    </if>
                    <if test="param.zymc != null">
                        AND a.ZYMC LIKE concat(concat('%',#{param.zymc), '%')
                    </if>
                    <if test="param.bjmc != null">
                        AND a.BJMC LIKE concat(concat('%',#{param.bjmc), '%')
                    </if>
                    <if test="param.pyccid != null">
                        AND a.PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
                    <if test="param.lxdh != null">
                        AND a.LXDH LIKE concat(concat('%',#{param.lxdh), '%')
                    </if>
                    <if test="param.wjlx != null">
                        AND a.WJLX LIKE concat(concat('%',#{param.wjlx), '%')
                    </if>
                    <if test="param.wjsjms != null">
                        AND a.WJSJMS LIKE concat(concat('%',#{param.wjsjms), '%')
                    </if>
                    <if test="param.wjfssj != null">
                        AND a.WJFSSJ LIKE concat(concat('%',#{param.wjfssj), '%')
                    </if>
                    <if test="param.wjfsdd != null">
                        AND a.WJFSDD LIKE concat(concat('%',#{param.wjfsdd), '%')
                    </if>
                    <if test="param.sjry != null">
                        AND a.SJRY LIKE concat(concat('%',#{param.sjry), '%')
                    </if>
                    <if test="param.sfsjsmwx != null">
                        AND a.SFSJSMWX = #{param.sfsjsmwx}
                    </if>
                    <if test="param.sfczzszcjh != null">
                        AND a.SFCZZSZCJH = #{param.sfczzszcjh}
                    </if>
                    <if test="param.sfczzszccs != null">
                        AND a.SFCZZSZCCS = #{param.sfczzszccs}
                    </if>
                    <if test="param.sfxyljgy != null">
                        AND a.SFXYLJGY = #{param.sfxyljgy}
                    </if>
                    <if test="param.sbrxm != null">
                        AND a.SBRXM LIKE concat(concat('%',#{param.sbrxm), '%')
                    </if>
                    <if test="param.ydsrgx != null">
                        AND a.YDSRGX LIKE concat(concat('%',#{param.ydsrgx), '%')
                    </if>
                    <if test="param.lxfs != null">
                        AND a.LXFS LIKE concat(concat('%',#{param.lxfs), '%')
                    </if>
                    <if test="param.sfylxxxxlzx != null">
                        AND a.SFYLXXXXLZX = #{param.sfylxxxxlzx}
                    </if>
                    <if test="param.sfjyqxqzybz != null">
                        AND a.SFJYQXQZYBZ = #{param.sfjyqxqzybz}
                    </if>
                    <if test="param.sfzj != null">
                        AND a.SFZJ = #{param.sfzj}
                    </if>
                    <if test="param.sfyxtraq != null">
                        AND a.SFYXTRAQ = #{param.sfyxtraq}
                    </if>
    </sql>
</mapper>
