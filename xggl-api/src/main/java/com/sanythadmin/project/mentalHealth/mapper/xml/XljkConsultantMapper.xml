<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkConsultantMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, SCLY, CYJY, ZZZS, XLJY
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.scly != null">
                        AND a.SCLY LIKE concat(concat('%',#{param.scly), '%')
                    </if>
                    <if test="param.cyjy != null">
                        AND a.CYJY LIKE concat(concat('%',#{param.cyjy), '%')
                    </if>
                    <if test="param.zzzs != null">
                        AND a.ZZZS LIKE concat(concat('%',#{param.zzzs), '%')
                    </if>
                    <if test="param.xljy != null">
                        AND a.XLJY LIKE concat(concat('%',#{param.xljy), '%')
                    </if>
    </sql>
</mapper>
