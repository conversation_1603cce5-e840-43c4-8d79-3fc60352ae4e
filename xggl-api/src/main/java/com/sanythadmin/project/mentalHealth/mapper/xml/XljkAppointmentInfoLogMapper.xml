<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoLogMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APPOINTMENT_INFO_ID, STATUS, REMARK, CREATE_TIME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.appointmentInfoId != null">
                        AND a.APPOINTMENT_INFO_ID LIKE concat(concat('%',#{param.appointmentInfoId), '%')
                    </if>
                    <if test="param.status != null">
                        AND a.STATUS LIKE concat(concat('%',#{param.status), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
    </sql>
</mapper>
