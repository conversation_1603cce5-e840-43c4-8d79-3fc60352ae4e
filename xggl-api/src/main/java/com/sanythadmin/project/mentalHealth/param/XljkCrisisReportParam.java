package com.sanythadmin.project.mentalHealth.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理危机上报查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCrisisReportParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Integer xb;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 培养层次名称
     */
    private String pyccmc;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 培养层次ID
     */
    private String pyccid;

    /**
     * 学院ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    private String zyid;

    /**
     * 班级ID
     */
    private String bjid;

    /**
     * 年级ID
     */
    private String njid;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 危机情况描述（危机类型（如抑郁、自残、焦虑、暴力倾向、自杀倾向等））
     */
    private String wjlx;

    /**
     * 危机情况描述（危机事件/问题描述）
     */
    private String wjsjms;

    /**
     * 危机情况描述（危机发生时间）
     */
    private String wjfssj;

    /**
     * 危机情况描述（危机发生地点）
     */
    private String wjfsdd;

    /**
     * 危机情况描述（涉及人员（仅当事人/家庭成员/同事/其他））
     */
    private String sjry;

    /**
     * 危机评估（是否涉及生命危险（是/否/不确定））
     */
    @QueryField(type = QueryType.EQ)
    private String sfsjsmwx;

    /**
     * 危机评估（是否存在自残/自杀计划（是/否/不确定））
     */
    @QueryField(type = QueryType.EQ)
    private String sfczzszcjh;

    /**
     * 危机评估（是否存在自残/自杀尝试（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfczzszccs;

    /**
     * 危机评估（是否需要立即干预（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxyljgy;

    /**
     * 上报人信息（上报人姓名（可匿名））
     */
    private String sbrxm;

    /**
     * 上报人信息（与当事人关系（同学/室友/老师/辅导员/亲属/其他））
     */
    private String ydsrgx;

    /**
     * 上报人信息（联系方式）
     */
    private String lxfs;

    /**
     * 处理建议（是否已联系学校心理中心））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfylxxxxlzx;

    /**
     * 处理建议（是否建议其寻求专业帮助（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfjyqxqzybz;

    /**
     * 处理建议（是/否转介）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzj;

    /**
     * 危机评估（是否影响他人安全（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfyxtraq;

    /**
     * 创建用户
     */
    @QueryField(type = QueryType.EQ)
    private String username;

    /**
     * 创建用户真实姓名
     */
    private String realName;
}
