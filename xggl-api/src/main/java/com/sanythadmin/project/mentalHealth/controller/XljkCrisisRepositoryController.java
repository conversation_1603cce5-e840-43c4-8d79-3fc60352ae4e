package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisRepositoryService;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisRepository;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisRepositoryParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 心理健康教育/心理危机档案信息库控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-crisis-repository")
public class XljkCrisisRepositoryController extends BaseController {
    @Resource
    private XljkCrisisRepositoryService xljkCrisisRepositoryService;

    /**
     * 分页查询心理危机档案信息库（权限标识：mentalHealth:xljkCrisisRepository:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisRepository:list')")
    @GetMapping("/page")
    public PageResult<XljkCrisisRepository> page(XljkCrisisRepositoryParam param) {
        PageParam<XljkCrisisRepository, XljkCrisisRepositoryParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkCrisisRepositoryService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理危机档案信息库（权限标识：mentalHealth:xljkCrisisRepository:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisRepository:list')")
    @GetMapping()
    public List<XljkCrisisRepository> list(XljkCrisisRepositoryParam param) {
        PageParam<XljkCrisisRepository, XljkCrisisRepositoryParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkCrisisRepositoryService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理危机档案信息库（权限标识：mentalHealth:xljkCrisisRepository:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisRepository:list')")
    @GetMapping("/{id}")
    public XljkCrisisRepository get(@PathVariable("id") String id) {
        return xljkCrisisRepositoryService.getById(id);
    }

    /**
     * 添加或修改心理危机档案信息库（权限标识：mentalHealth:xljkCrisisRepository:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisRepository:operation')")
    @OperationLog(module = "心理危机档案信息库", comments = "保存心理危机档案信息库")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCrisisRepository xljkCrisisRepository) {
        xljkCrisisRepositoryService.operation(xljkCrisisRepository);
    }

    /**
     * 批量删除心理危机档案信息库（权限标识：mentalHealth:xljkCrisisRepository:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisRepository:remove')")
    @OperationLog(module = "心理危机档案信息库", comments = "批量删除心理危机档案信息库")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkCrisisRepositoryService.removeByIds(ids);
    }
}
