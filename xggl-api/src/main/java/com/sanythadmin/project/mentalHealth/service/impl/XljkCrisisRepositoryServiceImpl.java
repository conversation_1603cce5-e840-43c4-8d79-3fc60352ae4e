package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisRepository;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisRepositoryMapper;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisRepositoryService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 心理危机档案信息库Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkCrisisRepositoryServiceImpl extends ServiceImpl<XljkCrisisRepositoryMapper, XljkCrisisRepository> implements XljkCrisisRepositoryService {

    @Resource
    private XljkCrisisRepositoryMapper mapper;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Transactional
    @Override
    public void operation(XljkCrisisRepository repository) {
        SysAccount account = SecurityUtil.getAccount();
        UserInfo userInfo = userInfoMapper.selectById(repository.getXgh());
        UserInfoUtil.codeTextSet(userInfo);
        BeanUtils.copyProperties(userInfo, repository);
        repository.setCzr(account.getRealName());
        repository.setCzrzh(account.getUsername());
        if (StringUtils.hasText(repository.getId())) {
            XljkCrisisRepository crisisRepository = mapper.selectById(repository.getId());
            repository.setCreateTime(crisisRepository.getCreateTime());
            mapper.updateById(repository);
        } else {
            mapper.insert(repository);
        }
    }
}
