package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyAnswerInfoService;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswerInfo;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerInfoParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 心理健康教育/测评量表答卷信息控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey-answer-info")
public class XljkSurveyAnswerInfoController extends BaseController {
    @Resource
    private XljkSurveyAnswerInfoService xljkSurveyAnswerInfoService;

    /**
     * 分页查询测评量表答卷信息（权限标识：mentalHealth:xljkSurveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswerInfo:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyAnswerInfo> page(XljkSurveyAnswerInfoParam param) {
        PageParam<XljkSurveyAnswerInfo, XljkSurveyAnswerInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkSurveyAnswerInfoService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部测评量表答卷信息（权限标识：mentalHealth:xljkSurveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswerInfo:list')")
    @GetMapping()
    public List<XljkSurveyAnswerInfo> list(XljkSurveyAnswerInfoParam param) {
        PageParam<XljkSurveyAnswerInfo, XljkSurveyAnswerInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkSurveyAnswerInfoService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询测评量表答卷信息（权限标识：mentalHealth:xljkSurveyAnswerInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswerInfo:list')")
    @GetMapping("/{id}")
    public XljkSurveyAnswerInfo get(@PathVariable("id") String id) {
        return xljkSurveyAnswerInfoService.getById(id);
    }

    /**
     * 添加或修改测评量表答卷信息（权限标识：mentalHealth:xljkSurveyAnswerInfo:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswerInfo:operation')")
    @OperationLog(module = "测评量表答卷信息", comments = "保存测评量表答卷信息")
    @PostMapping("/operation")
    public void save(@RequestBody XljkSurveyAnswerInfo xljkSurveyAnswerInfo) {
        if (StringUtils.hasLength(xljkSurveyAnswerInfo.getId())) {
            xljkSurveyAnswerInfoService.updateById(xljkSurveyAnswerInfo);
        } else {
            xljkSurveyAnswerInfoService.save(xljkSurveyAnswerInfo);
        }
    }

    /**
     * 批量删除测评量表答卷信息（权限标识：mentalHealth:xljkSurveyAnswerInfo:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswerInfo:remove')")
    @OperationLog(module = "测评量表答卷信息", comments = "批量删除测评量表答卷信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkSurveyAnswerInfoService.removeByIds(ids);
    }
}
