package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselingRecord;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCounselingRecordMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCounselorScheduleMapper;
import com.sanythadmin.project.mentalHealth.param.XljkCounselingRecordParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCounselingRecordQueryParam;
import com.sanythadmin.project.mentalHealth.service.XljkCounselingRecordService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理咨询记录Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkCounselingRecordServiceImpl extends ServiceImpl<XljkCounselingRecordMapper, XljkCounselingRecord> implements XljkCounselingRecordService {
    @Resource
    private XljkCounselingRecordMapper mapper;
    @Resource
    private XljkAppointmentInfoMapper appointmentInfoMapper;

    @Transactional
    @Override
    public void operation(XljkCounselingRecord record) {
        record.setCzr(SecurityUtil.getRealName());
        record.setCzrzh(SecurityUtil.getUsername());
        record.setUpdateTime(LocalDateTime.now());
        if (StringUtils.hasLength(record.getId())) {
            XljkCounselingRecord counselingRecord = mapper.selectById(record.getId());
            record.setCreateTime(counselingRecord.getCreateTime());
            mapper.updateById(record);
        } else {
            record.setCreateTime(LocalDateTime.now());
            mapper.insert(record);
        }

        String appointmentInfoId = record.getAppointmentInfoId();
        XljkAppointmentInfo appointmentInfo = appointmentInfoMapper.selectById(appointmentInfoId);
        if (appointmentInfo != null) {
            appointmentInfo.setStatus(Constants.YIWANCHENG);
            appointmentInfoMapper.updateById(appointmentInfo);
        }
    }

    @Override
    public PageResult<XljkCounselingRecord> page(XljkCounselingRecordParam param) {
        PageParam<XljkCounselingRecord, XljkCounselingRecordParam> page = new PageParam<>(param);
        page = mapper.selectPage(page, page.getOrderWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<XljkCounselingRecord> list(XljkCounselingRecordQueryParam param) {
        MyMPJLambdaWrapper<XljkCounselingRecord, XljkCounselingRecordParam> joinWrapper = getJoinWrapper(param);
        return mapper.selectJoinList(XljkCounselingRecord.class, joinWrapper);
    }

    private MyMPJLambdaWrapper<XljkCounselingRecord, XljkCounselingRecordParam> getJoinWrapper(XljkCounselingRecordQueryParam param) {
        Class<XljkAppointmentInfo> appointmentInfoClass = XljkAppointmentInfo.class;
        MyMPJLambdaWrapper<XljkCounselingRecord, XljkCounselingRecordParam> wrapper = new MyMPJLambdaWrapper<>(param.getCounselingRecord());
        wrapper.leftJoin(appointmentInfoClass, XljkAppointmentInfo::getId, XljkCounselingRecord::getAppointmentInfoId);
        wrapper.selectAll(XljkCounselingRecord.class);
        if (param.getAppointmentInfo() != null)
            wrapper.buildQueryCondition(appointmentInfoClass, null, param.getAppointmentInfo());
        return wrapper;
    }
}
