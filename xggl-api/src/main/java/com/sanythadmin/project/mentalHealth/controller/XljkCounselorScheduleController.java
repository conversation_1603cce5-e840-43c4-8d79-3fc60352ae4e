package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;
import com.sanythadmin.project.mentalHealth.service.XljkCounselorScheduleService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 心理健康教育/心理健康咨询师排班信息控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-counselor-schedule")
public class XljkCounselorScheduleController extends BaseController {
    @Resource
    private XljkCounselorScheduleService xljkCounselorScheduleService;

    /**
     * 咨询师列表
     *
     * @return
     */
    @GetMapping("/consultantList")
    public List<XljkConsultant> consultantList(XljkConsultantParam param) {
        return xljkCounselorScheduleService.consultantList(param);
    }

    /**
     * 分页查询心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselorSchedule:list')")
    @GetMapping("/page")
    public PageResult<XljkCounselorSchedule> page(XljkCounselorScheduleParam param) {
        return xljkCounselorScheduleService.page(param);
    }

    /**
     * 查询全部心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselorSchedule:list')")
    @GetMapping()
    public List<XljkCounselorSchedule> list(XljkCounselorScheduleParam param) {
        return xljkCounselorScheduleService.list(param, true);
    }

    /**
     * 根据id查询心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselorSchedule:list')")
    @GetMapping("/{id}")
    public XljkCounselorSchedule get(@PathVariable("id") String id) {
        return xljkCounselorScheduleService.getById(id);
    }

    /**
     * 添加或修改心理健康咨询师排班信息（权限标识：mentalHealth:schedule:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselorSchedule:operation')")
    @OperationLog(module = "心理健康咨询师排班信息", comments = "保存心理健康咨询师排班信息")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCounselorSchedule schedule) {
        xljkCounselorScheduleService.operation(schedule);
    }

    /**
     * 批量删除心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselorSchedule:remove')")
    @OperationLog(module = "心理健康咨询师排班信息", comments = "批量删除心理健康咨询师排班信息")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            xljkCounselorScheduleService.removeSchedule(id);
        }
    }
}
