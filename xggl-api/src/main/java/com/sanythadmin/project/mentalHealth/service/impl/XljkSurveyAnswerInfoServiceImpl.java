package com.sanythadmin.project.mentalHealth.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.mentalHealth.constant.ConstantsMentalHealth;
import com.sanythadmin.project.mentalHealth.entity.*;
import com.sanythadmin.project.mentalHealth.mapper.*;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerInfoParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyReportDTO;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyAnswerInfoService;
import com.sanythadmin.project.survey.entity.SurveyPquestionsOptions;
import com.sanythadmin.project.survey.mapper.SurveyPquestionsOptionsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 测评量表答卷信息Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkSurveyAnswerInfoServiceImpl extends ServiceImpl<XljkSurveyAnswerInfoMapper, XljkSurveyAnswerInfo> implements XljkSurveyAnswerInfoService {
    @Resource
    private XljkSurveyAnswerInfoMapper mapper;
    @Resource
    private XljkSurveyMapper xljkSurveyMapper;
    @Resource
    private XljkSurveyAnswerMapper answerMapper;
    @Resource
    private XljkSurveyReportMapper surveyReportMapper;
    @Resource
    private SurveyPquestionsOptionsMapper pquestionsOptionsMapper;
    @Resource
    private XljkSurveyInterpretationMapper surveyInterpretationMapper;
    @Resource
    private GridFsService gridFsService;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Override
    public PageResult<XljkSurveyAnswerInfo> page(XljkSurveyAnswerInfoParam param) {
        PageParam<XljkSurveyAnswerInfo, XljkSurveyAnswerInfoParam> page = new PageParam<>(param);
        page = mapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Transactional
    @Override
    public void answerOperation(XljkSurveyAnswerInfo answerInfo) {
        XljkSurvey survey = xljkSurveyMapper.selectById(answerInfo.getXsid());
        if (survey == null) AssertUtil.throwMessage("测评量表不存在");
        String username = SecurityUtil.getUsername();
        String realName = SecurityUtil.getRealName();
        if (!Constants.RICHANGCEPING.equals(survey.getLblb())) {
            Long testNum = mapper.selectCount(new LambdaQueryWrapper<XljkSurveyAnswerInfo>()
                    .eq(XljkSurveyAnswerInfo::getSid, answerInfo.getSid())
                    .eq(XljkSurveyAnswerInfo::getXsid, answerInfo.getXsid())
                    .eq(XljkSurveyAnswerInfo::getXgh, username));
            Integer total = survey.getKdtcs();
            if (total != null && testNum >= total)
                AssertUtil.throwMessage("测评量表已填写");
        }

        List<SurveyPquestionsOptions> options = pquestionsOptionsMapper.selectList(new LambdaQueryWrapper<SurveyPquestionsOptions>()
                .select(SurveyPquestionsOptions::getId, SurveyPquestionsOptions::getScore)
                .eq(SurveyPquestionsOptions::getSid, survey.getSid()));
        Map<String, Double> optionScoreMap = options.stream().collect(Collectors.toMap(SurveyPquestionsOptions::getId, SurveyPquestionsOptions::getScore));
        List<XljkSurveyAnswer> answers = answerInfo.getAnswers();
        answerInfo.setCreateDate(LocalDateTime.now());
        answerInfo.setXgh(username);
        answerInfo.setXm(realName);
        answerInfo.setCategory(survey.getCategory());
        mapper.insert(answerInfo);
        AtomicReference<Double> score = new AtomicReference<>(0d);
        for (XljkSurveyAnswer answer : answers) {
            if (!CollectionUtils.isEmpty(answer.getResultFiles())) {
                List<MultipartFile> attachment = new ArrayList<>();
                List<XljkSurveyAnswer.ResultItem> resultFiles = answer.getResultFiles();
                resultFiles.forEach(resultItem -> attachment.add(resultItem.getFile()));
                String fileInfoStr = gridFsService.save(attachment.toArray(new MultipartFile[]{}));
                List<FileInfo> fileInfos = new ArrayList<>(JSON.parseArray(fileInfoStr, FileInfo.class));
                if (!CollectionUtils.isEmpty(fileInfos))
                    answer.setResult(JSON.toJSONString(fileInfos));
            }

            Double val = optionScoreMap.get(answer.getResultId());
            if (val != null)
                score.updateAndGet(v -> v + val);
            answer.setPoint(val);
            answer.setXgh(username);
            answer.setInfoId(answerInfo.getId());
            answer.setCreateDate(LocalDateTime.now());
            answer.setXsid(survey.getId());
            answer.setSid(answerInfo.getSid());
            answerMapper.insert(answer);
        }

        List<XljkSurveyReport> surveyReports = generateSurveyReport(survey, answerInfo, score.get());
        if (!CollectionUtils.isEmpty(surveyReports)) {
            AtomicInteger index = new AtomicInteger(1);
            surveyReports.forEach(report -> {
                int sort = index.getAndIncrement();
                report.setAnswerInfoId(answerInfo.getId());
                report.setSort(sort);
                surveyReportMapper.insert(report);
            });
        }
        mapper.update(new LambdaUpdateWrapper<XljkSurveyAnswerInfo>()
                .set(XljkSurveyAnswerInfo::getScore, score.get())
                .set(XljkSurveyAnswerInfo::getTestResult, answerInfo.getTestResult())
                .eq(XljkSurveyAnswerInfo::getId, answerInfo.getId()));
    }

    @Override
    public Long getAnswerNum(XljkSurveyAnswerInfoParam param) {
        PageParam<XljkSurveyAnswerInfo, XljkSurveyAnswerInfoParam> page = new PageParam<>(param);
        return mapper.selectCount(page.getWrapper());
    }

    @Override
    public XljkSurveyReportDTO answerInfoReport(String answerInfoId) {
        XljkSurveyAnswerInfo answerInfo = mapper.selectById(answerInfoId);
        List<XljkSurveyReport> xljkSurveyReports = surveyReportMapper.selectList(new LambdaQueryWrapper<XljkSurveyReport>()
                .eq(XljkSurveyReport::getAnswerInfoId, answerInfoId)
                .orderByAsc(XljkSurveyReport::getSort));
        BaseUserInfo userInfo = userInfoMapper.selectById(answerInfo.getXgh());
        return new XljkSurveyReportDTO(userInfo, answerInfo, xljkSurveyReports);
    }

    private List<XljkSurveyReport> generateSurveyReport(XljkSurvey survey, XljkSurveyAnswerInfo answerInfo, Double score) {
        List<XljkSurveyReport> surveyReport = new ArrayList<>();
        if (!StringUtils.hasText(survey.getCategory()))
            survey.setCategory(ConstantsMentalHealth.QUESTION_CLASS_OTHER);
        List<XljkSurveyInterpretation> surveyInterpretations = surveyInterpretationMapper.selectList(new LambdaQueryWrapper<XljkSurveyInterpretation>()
                .eq(XljkSurveyInterpretation::getXsid, answerInfo.getXsid())
                .eq(XljkSurveyInterpretation::getSid, answerInfo.getSid()));
        Map<String, XljkSurveyInterpretation> interpretationMap = new HashMap<>();
        switch (survey.getCategory()) {
            case ConstantsMentalHealth.QUESTION_CLASS_SCL90:
                List<XljkSurveyReport> surveyItemScore = answerMapper.getSurveyItemScore(answerInfo.getId());
                List<XljkSurveyReport> surveyScoreAndAvg = answerMapper.getSurveyScoreAndAvg(answerInfo.getId());
                List<XljkSurveyReport> scl90PositiveInfo = answerMapper.scl90PositiveInfo(answerInfo.getId());
                surveyReport.addAll(surveyItemScore);
                surveyReport.addAll(surveyScoreAndAvg);
                surveyReport.addAll(scl90PositiveInfo);
                if (!CollectionUtils.isEmpty(surveyInterpretations))
                    interpretationMap = surveyInterpretations.stream().collect(Collectors.toMap(XljkSurveyInterpretation::getItemName, Function.identity()));
                answerInfo.setTestResult("心理健康");
                for (XljkSurveyReport report : surveyReport) {
                    XljkSurveyInterpretation interpretation = interpretationMap.get(report.getItemName());
                    if (interpretation != null) {
                        boolean withinRange = isWithinRange(Double.valueOf(report.getVal()), interpretation);
                        if (withinRange) {
                            answerInfo.setTestResult(interpretation.getRemark());
                            break;
                        }
                    }
                }
                break;
            case ConstantsMentalHealth.QUESTION_CLASS_ANXIETY:
                String val = String.valueOf(score * 1.25);
                surveyReport.add(new XljkSurveyReport(null, "标准分", val, answerInfo.getId(), ConstantsMentalHealth.QUESTION_CLASS_OTHER,
                        null, null, null));
                for (XljkSurveyInterpretation interpretation : surveyInterpretations) {
                    boolean withinRange = isWithinRange(Double.valueOf(val), interpretation);
                    if (withinRange) {
                        answerInfo.setTestResult(interpretation.getRemark());
                        break;
                    }
                }
                break;
            case ConstantsMentalHealth.QUESTION_CLASS_HAMD:
                break;
            default:
                break;
        }
        return surveyReport;
    }

    public boolean isWithinRange(Double val, XljkSurveyInterpretation object) {
        if (object == null || (object.getMinScore() == null && object.getMaxScore() == null))
            return false;
        Double minVal = object.getMinScore();
        Double maxVal = object.getMaxScore();
        String minOp = object.getMinScoreOperator();
        String maxOp = object.getMaxScoreOperator();

        boolean lowerBoundCheck = true;
        boolean upperBoundCheck = true;
        if (minVal != null) {
            lowerBoundCheck = switch (minOp) {
                case ConstantsMentalHealth.OPERATOR_GT -> val > minVal;
                case ConstantsMentalHealth.OPERATOR_GE -> val >= minVal;
                case ConstantsMentalHealth.OPERATOR_LT -> val < minVal;
                case ConstantsMentalHealth.OPERATOR_LE -> val <= minVal;
                default -> throw new RuntimeException("无效的最小值比较符号: " + minOp);
            };
        }

        if (maxVal != null) {
            upperBoundCheck = switch (maxOp) {
                case ConstantsMentalHealth.OPERATOR_GT -> val > maxVal;
                case ConstantsMentalHealth.OPERATOR_GE -> val >= maxVal;
                case ConstantsMentalHealth.OPERATOR_LT -> val < maxVal;
                case ConstantsMentalHealth.OPERATOR_LE -> val <= maxVal;
                default -> throw new RuntimeException("无效的最大值比较符号: " + maxOp);
            };
        }
        return lowerBoundCheck && upperBoundCheck;
    }
}
