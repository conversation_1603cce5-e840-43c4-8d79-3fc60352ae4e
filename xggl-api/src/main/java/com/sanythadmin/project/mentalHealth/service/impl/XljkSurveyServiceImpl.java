package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.*;
import com.sanythadmin.project.mentalHealth.mapper.*;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyDTO;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyService;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.selector.service.SelectorDataService;
import com.sanythadmin.project.survey.entity.*;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.enums.SurveyType;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsClassMapper;
import com.sanythadmin.project.survey.mapper.SurveyQuestionsMapper;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.param.SurveyPquestionsParam;
import com.sanythadmin.project.survey.service.SurveyPquestionsOptionsService;
import com.sanythadmin.project.survey.service.SurveyPquestionsService;
import com.sanythadmin.project.survey.service.SurveyService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理健康测评问卷Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Slf4j
@Service
public class XljkSurveyServiceImpl extends ServiceImpl<XljkSurveyMapper, XljkSurvey> implements XljkSurveyService {
    @Resource
    private XljkSurveyMapper mapper;
    @Resource
    private XljkSurveyAnswerInfoMapper answerInfoMapper;
    @Resource
    private XljkSurveyAnswerMapper answerMapper;
    @Resource
    private SelectorDataService selectorDataService;
    @Resource
    private SurveyService surveyService;
    @Resource
    private SurveyQuestionsClassMapper questionsClassMapper;
    @Resource
    private SurveyQuestionsMapper surveyQuestionsMapper;
    @Resource
    private SurveyPquestionsService surveyPquestionsService;
    @Resource
    private SurveyPquestionsOptionsService surveyPquestionsOptionsService;
    @Resource
    private XljkInterpretationMapper interpretationMapper;
    @Resource
    private XljkSurveyInterpretationMapper surveyInterpretationMapper;

    @Override
    public PageResult<XljkSurveyDTO> page(XljkSurveyParam param, SurveyParam surveyParam, boolean manage) {
        MyMPJLambdaWrapper<XljkSurvey, XljkSurveyParam> wrapper = getWrapper(param, surveyParam, manage);
        Page<XljkSurveyDTO> page = new Page<>(param.getPage(), param.getLimit());
        page = mapper.selectJoinPage(page, XljkSurveyDTO.class, wrapper);
        List<XljkSurveyDTO> records = page.getRecords();
        if (manage) {
            for (XljkSurveyDTO record : records) {
                List<SelectorData> list = selectorDataService.list(new LambdaQueryWrapper<SelectorData>()
                        .eq(SelectorData::getItemId, record.getSid()));
                record.setSelectorData(list);
            }
        }
        return new PageResult<>(records, page.getTotal());
    }

    private MyMPJLambdaWrapper<XljkSurvey, XljkSurveyParam> getWrapper(XljkSurveyParam param, SurveyParam surveyParam, boolean manage) {
        MyMPJLambdaWrapper<XljkSurvey, XljkSurveyParam> wrapper = new MyMPJLambdaWrapper<>(param);
        Class<Survey> surveyClass = Survey.class;
        SFunction<Survey, Object>[] sFunctions = CommonUtil.buildSFunctions(surveyClass, false);
        wrapper.innerJoin(surveyClass, Survey::getId, XljkSurvey::getSid)
                .selectAll(XljkSurvey.class)
                .select(sFunctions)
                .selectAssociation(surveyClass, XljkSurveyDTO::getSurvey);
        if (surveyParam != null)
            wrapper.buildQueryCondition(surveyClass, null, surveyParam);
        if (!manage) {
            wrapper.and(o -> o.notExists("SELECT 1 FROM SYT_SELECTOR_DATA SD WHERE SD.ITEM_ID = " + wrapper.getAlias() + ".SID")
                    .or(o1 -> o1.exists("SELECT 1 FROM SYT_SELECTOR_ACCOUNT SA WHERE SA.XGH = '" + SecurityUtil.getUsername() + "' AND SA.ITEM_ID = " + wrapper.getAlias() + ".SID")));
        }
        return wrapper;
    }

    @Override
    public List<XljkSurveyDTO> list(XljkSurveyParam param, SurveyParam surveyParam, boolean manage) {
        MyMPJLambdaWrapper<XljkSurvey, XljkSurveyParam> wrapper = getWrapper(param, surveyParam, manage);
        return mapper.selectJoinList(XljkSurveyDTO.class, wrapper);
    }

    @Transactional
    @Override
    public void operation(XljkSurveyDTO surveyDTO) {
        Survey survey = surveyDTO.getSurvey();
        List<SurveyQuestions> questions = null;
        String gid = surveyDTO.getGid();
        SurveyQuestionsClass questionsClass = questionsClassMapper.selectById(gid);
        surveyDTO.setCategory(questionsClass.getCategory());
        if (StringUtils.hasText(surveyDTO.getId())) {
            Long count = answerInfoMapper.selectCount(new LambdaQueryWrapper<XljkSurveyAnswerInfo>()
                    .eq(XljkSurveyAnswerInfo::getXsid, surveyDTO.getId()));
            if (count > 0)
                AssertUtil.throwMessage("存在已答卷记录不可编辑");
            questions = surveyQuestionsMapper.selectList(new LambdaQueryWrapper<SurveyQuestions>()
                    .eq(SurveyQuestions::getCid, gid));
            if (CollectionUtils.isEmpty(questions))
                AssertUtil.throwMessage("题目信息不能为空");
            XljkSurvey xljkSurvey = mapper.selectById(surveyDTO.getId());
            Survey surveyInDb = surveyService.getById(xljkSurvey.getSid());
            surveyInDb.setSubjectSum(questions.size());
            surveyInDb.setName(survey.getName());
            xljkSurvey.setLblb(surveyDTO.getLblb());
            xljkSurvey.setSfyxckcpjg(surveyDTO.getSfyxckcpjg());
            xljkSurvey.setPclx(surveyDTO.getPclx());
            xljkSurvey.setKdtcs(surveyDTO.getKdtcs());
            xljkSurvey.setGid(gid);
            xljkSurvey.setCategory(surveyDTO.getCategory());
            surveyService.updateById(surveyInDb);
            mapper.updateById(xljkSurvey);
            survey.setId(surveyInDb.getId());
            surveyPquestionsService.remove(new LambdaQueryWrapper<SurveyPquestions>()
                    .eq(SurveyPquestions::getSid, surveyInDb.getId()));
            surveyPquestionsOptionsService.remove(new LambdaQueryWrapper<SurveyPquestionsOptions>()
                    .eq(SurveyPquestionsOptions::getSid, surveyInDb.getId()));
            surveyInterpretationMapper.delete(new LambdaQueryWrapper<XljkSurveyInterpretation>()
                    .eq(XljkSurveyInterpretation::getXsid, xljkSurvey.getId()));
        } else {
            questions = surveyQuestionsMapper.selectList(new LambdaQueryWrapper<SurveyQuestions>()
                    .eq(SurveyQuestions::getCid, gid));
            if (CollectionUtils.isEmpty(questions))
                AssertUtil.throwMessage("题目信息不能为空");
            String username = SecurityUtil.getUsername();
            String realName = SecurityUtil.getRealName();
            survey.setStatus(SurveyState.ENABLED);
            survey.setWjms(SurveyType.GDXT);
            survey.setCreateTime(LocalDateTime.now());
            survey.setXgh(username);
            survey.setXm(realName);
            survey.setSubjectSum(questions.size());
            surveyService.save(survey);
            surveyDTO.setSid(survey.getId());
            mapper.insert(surveyDTO);
        }

        SurveyPquestionsParam param = new SurveyPquestionsParam();
        param.setGid(gid);
        param.setSid(survey.getId());
        param.setQid(CommonUtil.strJoin(questions.stream().map(SurveyQuestions::getId).toList()));
        surveyPquestionsService.saveSurveyPquestionsByQuestions(param);
        try {
            if (!CollectionUtils.isEmpty(surveyDTO.getSelectorData()))
                selectorDataService.saveSelectotData(survey.getId(), surveyDTO.getSfbl(), surveyDTO.getSelectorData());
        } catch (Exception e) {
            log.error("保存选择器数据失败", e);
        }
        List<XljkInterpretation> interpretations = interpretationMapper.selectList(new LambdaQueryWrapper<XljkInterpretation>()
                .eq(XljkInterpretation::getGid, gid));
        for (XljkInterpretation obj : interpretations) {
            XljkSurveyInterpretation interpretation = new XljkSurveyInterpretation();
            BeanUtils.copyProperties(obj, interpretation);
            interpretation.setId(null);
            interpretation.setSid(survey.getId());
            interpretation.setXsid(surveyDTO.getId());
            surveyInterpretationMapper.insert(interpretation);
        }
    }

    @Transactional
    @Override
    public void removeSurvey(List<String> id) {
        // TODO 问卷删除是否判断？
        answerMapper.delete(new LambdaQueryWrapper<XljkSurveyAnswer>()
                .in(XljkSurveyAnswer::getXsid, id));
        answerInfoMapper.delete(new LambdaQueryWrapper<XljkSurveyAnswerInfo>()
                .in(XljkSurveyAnswerInfo::getXsid, id));
        List<String> objects = mapper.selectObjs(new LambdaQueryWrapper<XljkSurvey>()
                .select(XljkSurvey::getSid).in(XljkSurvey::getId, id));
        surveyService.removeSurvey(objects);
        mapper.delete(new LambdaQueryWrapper<XljkSurvey>().in(XljkSurvey::getId, id));
    }
}
