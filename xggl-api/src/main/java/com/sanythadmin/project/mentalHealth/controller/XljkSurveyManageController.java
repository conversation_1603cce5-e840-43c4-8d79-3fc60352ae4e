package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.constant.ConstantsMentalHealth;
import com.sanythadmin.project.mentalHealth.entity.XljkSurvey;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswer;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswerInfo;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerInfoParam;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyDTO;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyQueryParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyReportDTO;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyAnswerInfoService;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyReportService;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyService;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 心理健康教育/（管理端）心理健康测评问卷控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey-manage")
public class XljkSurveyManageController extends BaseController {
    @Resource
    private XljkSurveyService xljkSurveyService;
    @Resource
    private XljkSurveyAnswerInfoService answerInfoService;
    @Resource
    private SurveyService surveyService;
    @Resource
    private XljkSurveyReportService surveyReportService;

    /**
     * 分页查询心理健康测评问卷（权限标识：mentalHealth:xljkSurveyManage:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyManage:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyDTO> page(XljkSurveyQueryParam param) {
        return xljkSurveyService.page(param.getParam(), param.getSurvey(), true);
    }

    /**
     * 分页查询测评答卷记录
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyManage:list')")
    @GetMapping("/pageAnswerInfo")
    public PageResult<XljkSurveyAnswerInfo> pageAnswerInfo(XljkSurveyAnswerInfoParam param) {
        return answerInfoService.page(param);
    }

    /**
     * 查询全部心理健康测评问卷（权限标识：mentalHealth:xljkSurveyManage:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyManage:list')")
    @GetMapping()
    public List<XljkSurvey> list(XljkSurveyParam param) {
        PageParam<XljkSurvey, XljkSurveyParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkSurveyService.list(page.getOrderWrapper());
    }


    /**
     * 添加或修改心理健康测评问卷（权限标识：mentalHealth:xljkSurveyManage:operation）
     *
     * @param xljkSurvey 心理测评问卷信息
     * @return 问卷ID
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyManage:operation')")
    @OperationLog(module = "心理健康测评问卷", comments = "保存心理健康测评问卷")
    @PostMapping("/operation")
    public void save(@RequestBody XljkSurveyDTO xljkSurvey) {
        xljkSurveyService.operation(xljkSurvey);
    }

    /**
     * 批量删除心理健康测评问卷（权限标识：mentalHealth:xljkSurveyManage:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyManage:remove')")
    @OperationLog(module = "心理健康测评问卷", comments = "批量删除心理健康测评问卷")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkSurveyService.removeSurvey(ids);
    }

    /**
     * 根据id查询问卷管理
     */
    @GetMapping("/{id}")
    public SurveyVO get(@PathVariable("id") String id) {
        return surveyService.getSurveyVO(id, null, null);
    }

    /**
     * 根据答卷及测评记录查询答题结果
     */
    @GetMapping("/surveyAnswer")
    public SurveyVO surveyAnswer(XljkSurveyAnswerInfo answerInfo) {
        return surveyService.getSurveyVO(answerInfo.getSid(), answerInfo, XljkSurveyAnswer.class);
    }

    /**
     * 测评卷报告
     *
     * @param answerInfoId
     * @return
     */
    @GetMapping("/surveyReport/{answerInfoId}")
    public XljkSurveyReportDTO getReport(@PathVariable("answerInfoId") String answerInfoId) {
        return answerInfoService.answerInfoReport(answerInfoId);
    }

    @GetMapping("/questionClassList")
    public List<HashMap<String, String>> questionClassList() {
        List<HashMap<String, String>> list = new ArrayList<>();
        List<String> classList = ConstantsMentalHealth.getQuestionClassList();
        for (String s : classList) {
            list.add(new HashMap<>() {{
                put("name", s);
            }});
        }
        return list;
    }
}
