package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 心理危机上报审核流程
 *
 * <AUTHOR>
 * @since 2025-05-07 17:39:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_CRISIS_REPORT_NODE")
@Entity
@Table(name = "SYT_XLJK_CRISIS_REPORT_NODE")
public class XljkCrisisReportNode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 审核节点角色
     */
    @Column(name = "ROLE_NAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ROLE_NAME")
    private String roleName;

    /**
     * 审核节点顺序
     */
    @Column(name = "SORT_NUMBER", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT_NUMBER")
    private Integer sortNumber;

}
