package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkFollowupRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Gender xb;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 培养层次名称
     */
    private String pyccmc;

    /**
     * 培养层次ID
     */
    private String pyccid;

    /**
     * 学院ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    private String zyid;

    /**
     * 班级ID
     */
    private String bjid;

    /**
     * 年级ID
     */
    private String njid;

    /**
     * 操作人
     */
    private String czr;

    /**
     * 操作人账号
     */
    private String czrzh;

    /**
     * 回访信息（回访时间）
     */
    private String hfsj;

    /**
     * 回访信息（回访方式（电话/面对面/线上交流/其他））
     */
    private String hffs;

    /**
     * 回访信息（回访人（心理老师/辅导员/社工/其他））
     */
    private String hfr;

    /**
     * 回访信息（与当事人沟通情况（愿意沟通/回避/情绪稳定/情绪波动））
     */
    private String ydsrgtqk;

}
