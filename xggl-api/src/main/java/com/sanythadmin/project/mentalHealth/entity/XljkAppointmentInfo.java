package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 正式咨询预约记录
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_APPOINTMENT_INFO")
@Entity
@Table(name = "SYT_XLJK_APPOINTMENT_INFO")
public class XljkAppointmentInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学工号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 联系电话
     */
    @Column(name = "SJH")
    @TableField("SJH")
    private String sjh;

    /**
     * 咨询问题分类（公共码表）
     */
    @Column(name = "ZXWTFL")
    @TableField("ZXWTFL")
    private String zxwtfl;

    /**
     * 主要问题或困扰
     */
    @Column(name = "WTJS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("WTJS")
    private String wtjs;

    /**
     * 是/否紧急情况
     */
    @Column(name = "SFJJQK", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFJJQK")
    private JudgeMark sfjjqk;

    /**
     * 紧急联系人
     */
    @Column(name = "JJLXR")
    @TableField("JJLXR")
    private String jjlxr;

    /**
     * 紧急联系人联系方式
     */
    @Column(name = "JJLXRLXFS")
    @TableField("JJLXRLXFS")
    private String jjlxrlxfs;

    /**
     * 咨询师排班信息ID
     */
    @Column(name = "COUNSELOR_SCHEDULE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("COUNSELOR_SCHEDULE_ID")
    private String counselorScheduleId;

    /**
     * 是/否同意隐私政策
     */
    @Column(name = "SFTYYSZC", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFTYYSZC")
    private JudgeMark sftyyszc;

    /**
     * 咨询目的
     */
    @Column(name = "ZXMD", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ZXMD")
    private String zxmd;

    /**
     * 咨询历史
     */
    @Column(name = "ZXLS")
    @TableField("ZXLS")
    private String zxls;

    /**
     * 家庭详细住址
     */
    @Column(name = "JTXXZZ")
    @TableField("JTXXZZ")
    private String jtxxzz;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 预约记录状态（预约中/已确认/已取消/已爽约/已改期/已完成）
     */
    @Column(name = "STATUS")
    @TableField("STATUS")
    private String status;

//    /**
//     * 审核操作人
//     */
//    @Column(name = "SHCZR")
//    @TableField("SHCZR")
//    private String shczr;
//
//    /**
//     * 审核操作人账号
//     */
//    @Column(name = "SHCZRZH")
//    @TableField("SHCZRZH")
//    private String shczrzh;
//
//    /**
//     * 审核时间
//     */
//    @Column(name = "SHSJ")
//    @TableField("SHSJ")
//    private LocalDateTime shsj;
//
//    /**
//     * 审核备注
//     */
//    @Column(name = "SHBZ")
//    @TableField("SHBZ")
//    private String shbz;
}
