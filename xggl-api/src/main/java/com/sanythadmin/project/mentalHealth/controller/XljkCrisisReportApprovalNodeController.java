package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportApprovalNodeService;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportApprovalNode;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportApprovalNodeParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理健康教育/心理危机上报审核记录控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-crisis-report-approval-node")
public class XljkCrisisReportApprovalNodeController extends BaseController {
    @Resource
    private XljkCrisisReportApprovalNodeService approvalNodeService;

    /**
     * 分页查询心理危机上报审核记录（权限标识：mentalHealth:xljkCrisisReportApprovalNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportApprovalNode:list')")
    @GetMapping("/page")
    public PageResult<XljkCrisisReportApprovalNode> page(XljkCrisisReportApprovalNodeParam param) {
        PageParam<XljkCrisisReportApprovalNode, XljkCrisisReportApprovalNodeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = approvalNodeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理危机上报审核记录（权限标识：mentalHealth:xljkCrisisReportApprovalNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportApprovalNode:list')")
    @GetMapping()
    public List<XljkCrisisReportApprovalNode> list(XljkCrisisReportApprovalNodeParam param) {
        PageParam<XljkCrisisReportApprovalNode, XljkCrisisReportApprovalNodeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return approvalNodeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理危机上报审核记录（权限标识：mentalHealth:xljkCrisisReportApprovalNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportApprovalNode:list')")
    @GetMapping("/{id}")
    public XljkCrisisReportApprovalNode get(@PathVariable("id") String id) {
        return approvalNodeService.getById(id);
    }

    /**
     * 添加或修改心理危机上报审核记录（权限标识：mentalHealth:xljkCrisisReportApprovalNode:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportApprovalNode:operation')")
    @OperationLog(module = "心理危机上报审核记录", comments = "保存心理危机上报审核记录")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCrisisReportApprovalNode xljkCrisisReportApprovalNode) {
        if (StringUtils.hasLength(xljkCrisisReportApprovalNode.getId())) {
            XljkCrisisReportApprovalNode approvalNode = approvalNodeService.getById(xljkCrisisReportApprovalNode.getId());
            xljkCrisisReportApprovalNode.setCreateTime(approvalNode.getCreateTime());
            approvalNodeService.updateById(xljkCrisisReportApprovalNode);
        } else {
            xljkCrisisReportApprovalNode.setCreateTime(LocalDateTime.now());
            approvalNodeService.save(xljkCrisisReportApprovalNode);
        }
    }

    /**
     * 批量删除心理危机上报审核记录（权限标识：mentalHealth:xljkCrisisReportApprovalNode:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportApprovalNode:remove')")
    @OperationLog(module = "心理危机上报审核记录", comments = "批量删除心理危机上报审核记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        approvalNodeService.removeByIds(ids);
    }
}
