package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 心理测评卷测评报告
 *
 * <AUTHOR>
 * @since 2025-04-27 14:37:31
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_SURVEY_REPORT")
@Entity
@Table(name = "SYT_XLJK_SURVEY_REPORT")
public class XljkSurveyReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 指标/因子名称
     */
    @Column(name = "ITEM_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("ITEM_NAME")
    private String itemName;

    /**
     * 分数或其他值
     */
    @Column(name = "val", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("val")
    private String val;

    /**
     * 测评卷答题ID
     */
    @Column(name = "ANSWER_INFO_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ANSWER_INFO_ID")
    private String answerInfoId;

    /**
     * 类别（指标/因子,总分,其他等）
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    /**
     * 问题数
     */
    @Column(name = "QUESTION_NUM", columnDefinition = ColumnType.NUMBER_10)
    @TableField("QUESTION_NUM")
    private String questionNum;

    /**
     * 平均分数
     */
    @Column(name = "AVG_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("AVG_SCORE")
    private Double avgScore;

    /**
     * 排序
     */
    @Column(name = "SORT", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT")
    private Integer sort;
}
