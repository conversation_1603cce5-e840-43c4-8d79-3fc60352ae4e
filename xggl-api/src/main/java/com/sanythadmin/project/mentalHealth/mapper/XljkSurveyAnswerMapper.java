package com.sanythadmin.project.mentalHealth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswer;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyReport;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评卷答案Mapper
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkSurveyAnswerMapper extends BaseMapper<XljkSurveyAnswer> {

    public List<XljkSurveyReport> getSurveyItemScore(@Param("answerInfoId") String answerInfoId);
    public List<XljkSurveyReport> getSurveyScoreAndAvg(@Param("answerInfoId") String answerInfoId);
    public List<XljkSurveyReport> scl90PositiveInfo(@Param("answerInfoId") String answerInfoId);
}
