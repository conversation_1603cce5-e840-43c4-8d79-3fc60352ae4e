package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportNodeMapper;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportNodeParam;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportNodeService;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportNode;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 心理危机上报审核流程Service实现
 *
 * <AUTHOR>
 * @since 2025-05-07 17:39:16
 */
@Service
public class XljkCrisisReportNodeServiceImpl extends ServiceImpl<XljkCrisisReportNodeMapper, XljkCrisisReportNode> implements XljkCrisisReportNodeService {

    @Resource
    private XljkCrisisReportNodeMapper mapper;

    @Override
    public List<XljkCrisisReportNode> list(XljkCrisisReportNodeParam param) {
        PageParam<XljkCrisisReportNode, XljkCrisisReportNodeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return mapper.selectList(page.getOrderWrapper());
    }
}
