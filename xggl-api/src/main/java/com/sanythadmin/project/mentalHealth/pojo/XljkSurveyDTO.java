package com.sanythadmin.project.mentalHealth.pojo;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.mentalHealth.entity.XljkSurvey;
import com.sanythadmin.project.selector.entity.SelectorData;
import com.sanythadmin.project.survey.entity.Survey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by JIANGPING on 2025/4/10.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class XljkSurveyDTO extends XljkSurvey {
    /**
     * 问卷信息
     */
    private Survey survey;
    /**
     * 人员选择器数据
     */
    private List<SelectorData> selectorData;
    /**
     * 人员选择器是否并联
     */
    private JudgeMark sfbl;
    /**
     * 测试次数
     */
    private Long testNum;
}
