package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkSurvey;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyDTO;
import com.sanythadmin.project.survey.param.SurveyParam;

import java.util.List;

/**
 * 心理健康测评问卷Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkSurveyService extends IService<XljkSurvey> {
    public PageResult<XljkSurveyDTO> page(XljkSurveyParam param, SurveyParam surveyParam, boolean manage);

    public List<XljkSurveyDTO> list(XljkSurveyParam param, SurveyParam surveyParam, boolean manage);

    public void operation(XljkSurveyDTO surveyDTO);

    public void removeSurvey(List<String> id);

}
