package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReport;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportApprovalNode;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportNode;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisRepository;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportApprovalNodeMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportNodeMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisRepositoryMapper;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportApprovalNodeParam;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCrisisReportQueryParam;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportService;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理危机上报Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkCrisisReportServiceImpl extends ServiceImpl<XljkCrisisReportMapper, XljkCrisisReport> implements XljkCrisisReportService {
    @Resource
    private XljkCrisisReportMapper mapper;
    @Resource
    private XljkCrisisReportApprovalNodeMapper approvalNodeMapper;
    @Resource
    private XljkCrisisReportNodeMapper xljkCrisisReportNodeMapper;
    @Resource
    private XljkCrisisRepositoryMapper xljkCrisisRepositoryMapper;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Override
    public PageResult<XljkCrisisReport> page(XljkCrisisReportParam param) {
        PageParam<XljkCrisisReport, XljkCrisisReportParam> page = new PageParam<>(param);
        QueryWrapper<XljkCrisisReport> wrapper = page.getOrderWrapper();
        page = mapper.selectPageWithPermission(page, wrapper, new MyMPQueryParams(XljkCrisisReport.class));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<XljkCrisisReport> pageApprovalList(XljkCrisisReportQueryParam param) {
        XljkCrisisReportApprovalNodeParam approvalNode = param.getApprovalNode();
        approvalNode.setRoleName(SecurityUtil.getRoleId());
        Class<XljkCrisisReportApprovalNode> approvalNodeClass = XljkCrisisReportApprovalNode.class;
        MyMPJLambdaWrapper<XljkCrisisReport, XljkCrisisReportParam> wrapper = new MyMPJLambdaWrapper<>(param.getReport());
        wrapper.innerJoin(approvalNodeClass, XljkCrisisReportApprovalNode::getCrisisReportId, XljkCrisisReport::getId);
        wrapper.buildQueryCondition(approvalNodeClass, null, approvalNode);
        Page<XljkCrisisReport> page = wrapper.getPage();
        page = mapper.selectJoinPage(page, XljkCrisisReport.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Transactional
    @Override
    public void operation(XljkCrisisReport xljkCrisisReport) {
        UserInfo userInfo = userInfoMapper.selectById(xljkCrisisReport.getXgh());
        UserInfoUtil.codeTextSet(userInfo);
        BeanUtils.copyProperties(userInfo, xljkCrisisReport);
        if (StringUtils.hasText(xljkCrisisReport.getId())) {
            XljkCrisisReport crisisReport = mapper.selectById(xljkCrisisReport.getId());
            xljkCrisisReport.setCreateTime(crisisReport.getCreateTime());
            xljkCrisisReport.setStatus(crisisReport.getStatus());
            mapper.updateById(xljkCrisisReport);
        } else {
            xljkCrisisReport.setStatus(ConstantsWorkflow.STATE_PENDING);
            xljkCrisisReport.setCreateTime(LocalDateTime.now());
            mapper.insert(xljkCrisisReport);
        }
        List<XljkCrisisReportNode> nodeList = xljkCrisisReportNodeMapper.selectList(new LambdaQueryWrapper<XljkCrisisReportNode>()
                .orderByAsc(XljkCrisisReportNode::getSortNumber));
        if (CollectionUtils.isEmpty(nodeList))
            AssertUtil.throwMessage("未设置危机上报审核流程");
        approvalNodeMapper.delete(new LambdaQueryWrapper<XljkCrisisReportApprovalNode>()
                .eq(XljkCrisisReportApprovalNode::getCrisisReportId, xljkCrisisReport.getId()));
        int size = nodeList.size();
        for (int i = 0; i < nodeList.size(); i++) {
            XljkCrisisReportNode node = nodeList.get(i);
            XljkCrisisReportApprovalNode approvalNode = new XljkCrisisReportApprovalNode();
            approvalNode.setCrisisReportId(xljkCrisisReport.getId());
            approvalNode.setRoleName(node.getRoleName());
            approvalNode.setPreNodeResult(i == 0 ? null : ConstantsWorkflow.STATE_PENDING);
            approvalNode.setResult(ConstantsWorkflow.STATE_PENDING);
            approvalNode.setNextNodeResult(i == size - 1 ? null : ConstantsWorkflow.STATE_PENDING);
            approvalNode.setSort(node.getSortNumber());
            approvalNode.setCreateTime(LocalDateTime.now());
            approvalNodeMapper.insert(approvalNode);
        }
    }

    @Transactional
    @Override
    public void removeCrisisReport(String id) {
        XljkCrisisReport crisisReport = mapper.selectById(id);
        if (!ConstantsWorkflow.STATE_PENDING.equals(crisisReport.getStatus()))
            AssertUtil.throwMessage("删除失败，当前状态不允许删除");
        approvalNodeMapper.delete(new LambdaQueryWrapper<XljkCrisisReportApprovalNode>()
                .eq(XljkCrisisReportApprovalNode::getCrisisReportId, id));
        mapper.deleteById(id);
    }

    @Transactional
    @Override
    public void approve(XljkCrisisReportApprovalNodeParam approvalNode) {
        XljkCrisisReportApprovalNode currentNode = approvalNodeMapper.selectOne(new LambdaQueryWrapper<XljkCrisisReportApprovalNode>()
                .eq(XljkCrisisReportApprovalNode::getCrisisReportId, approvalNode.getCrisisReportId())
                .eq(XljkCrisisReportApprovalNode::getRoleName, SecurityUtil.getRoleId()));
        if (null == currentNode)
            AssertUtil.throwMessage("操作失败,当前用户没有审批权限");
        if (ConstantsWorkflow.STATE_PASS.equals(currentNode.getNextNodeResult()))
            AssertUtil.throwMessage("操作失败,下级节点已经审批通过");
        currentNode.setResult(approvalNode.getResult());
        currentNode.setRemark(approvalNode.getRemark());
        currentNode.setUpdateTime(LocalDateTime.now());
        currentNode.setUsername(SecurityUtil.getUsername());
        currentNode.setRealName(SecurityUtil.getRealName());
        approvalNodeMapper.updateById(currentNode);
        List<XljkCrisisReportApprovalNode> nextNodes = approvalNodeMapper.selectList(new LambdaQueryWrapper<XljkCrisisReportApprovalNode>()
                .eq(XljkCrisisReportApprovalNode::getCrisisReportId, approvalNode.getCrisisReportId())
                .gt(XljkCrisisReportApprovalNode::getSort, currentNode.getSort())
                .orderByAsc(XljkCrisisReportApprovalNode::getSort));
        List<XljkCrisisReportApprovalNode> preNodes = approvalNodeMapper.selectList(new LambdaQueryWrapper<XljkCrisisReportApprovalNode>()
                .eq(XljkCrisisReportApprovalNode::getCrisisReportId, approvalNode.getCrisisReportId())
                .lt(XljkCrisisReportApprovalNode::getSort, currentNode.getSort())
                .orderByDesc(XljkCrisisReportApprovalNode::getSort));
        if (!CollectionUtils.isEmpty(preNodes)) {
            XljkCrisisReportApprovalNode node = preNodes.get(0);
            node.setNextNodeResult(approvalNode.getResult());
            approvalNodeMapper.updateById(node);
        }

        if (CollectionUtils.isEmpty(nextNodes)) {
            XljkCrisisReport crisisReport = mapper.selectById(approvalNode.getCrisisReportId());
            // 审核流程结束, 进入心理危机库
            crisisReport.setStatus(approvalNode.getResult());
            mapper.updateById(crisisReport);
            XljkCrisisRepository repository = new XljkCrisisRepository();
            BeanUtils.copyProperties(crisisReport, repository);
            xljkCrisisRepositoryMapper.insert(repository);
        } else {
            XljkCrisisReportApprovalNode node = nextNodes.get(0);
            node.setPreNodeResult(approvalNode.getResult());
            approvalNodeMapper.updateById(node);
        }
    }
}
