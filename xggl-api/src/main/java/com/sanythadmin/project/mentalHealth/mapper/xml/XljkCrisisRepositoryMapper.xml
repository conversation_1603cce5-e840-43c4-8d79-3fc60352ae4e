<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCrisisRepositoryMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, XM, XB, CSRQ, XYMC, NJMC, ZYMC, BJMC, LXDH, FDY, FDYLXFS, SSXX, CREATE_TIME, CZR, CZRZH, PYCCMC, PYCCID, XYID, ZYID, BJID, NJID, CRISIS_REPORT_ID, WJLX, WJSJMS, WJPGDJ, SFCZGWXLZXZLQK, SFYJWXLWJJL, GYCS, SFAPXLZX, SFYJZGT, SFXJYBGZ, HXHFJH
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xb != null">
                        AND a.XB = #{param.xb}
                    </if>
                    <if test="param.csrq != null">
                        AND a.CSRQ LIKE concat(concat('%',#{param.csrq), '%')
                    </if>
                    <if test="param.xymc != null">
                        AND a.XYMC LIKE concat(concat('%',#{param.xymc), '%')
                    </if>
                    <if test="param.njmc != null">
                        AND a.NJMC LIKE concat(concat('%',#{param.njmc), '%')
                    </if>
                    <if test="param.zymc != null">
                        AND a.ZYMC LIKE concat(concat('%',#{param.zymc), '%')
                    </if>
                    <if test="param.bjmc != null">
                        AND a.BJMC LIKE concat(concat('%',#{param.bjmc), '%')
                    </if>
                    <if test="param.lxdh != null">
                        AND a.LXDH LIKE concat(concat('%',#{param.lxdh), '%')
                    </if>
                    <if test="param.fdy != null">
                        AND a.FDY LIKE concat(concat('%',#{param.fdy), '%')
                    </if>
                    <if test="param.fdylxfs != null">
                        AND a.FDYLXFS LIKE concat(concat('%',#{param.fdylxfs), '%')
                    </if>
                    <if test="param.ssxx != null">
                        AND a.SSXX LIKE concat(concat('%',#{param.ssxx), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.czrzh != null">
                        AND a.CZRZH LIKE concat(concat('%',#{param.czrzh), '%')
                    </if>
                    <if test="param.pyccmc != null">
                        AND a.PYCCMC LIKE concat(concat('%',#{param.pyccmc), '%')
                    </if>
                    <if test="param.pyccid != null">
                        AND a.PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
                    <if test="param.crisisReportId != null">
                        AND a.CRISIS_REPORT_ID LIKE concat(concat('%',#{param.crisisReportId), '%')
                    </if>
                    <if test="param.wjlx != null">
                        AND a.WJLX LIKE concat(concat('%',#{param.wjlx), '%')
                    </if>
                    <if test="param.wjsjms != null">
                        AND a.WJSJMS LIKE concat(concat('%',#{param.wjsjms), '%')
                    </if>
                    <if test="param.wjpgdj != null">
                        AND a.WJPGDJ LIKE concat(concat('%',#{param.wjpgdj), '%')
                    </if>
                    <if test="param.sfczgwxlzxzlqk != null">
                        AND a.SFCZGWXLZXZLQK = #{param.sfczgwxlzxzlqk}
                    </if>
                    <if test="param.sfyjwxlwjjl != null">
                        AND a.SFYJWXLWJJL = #{param.sfyjwxlwjjl}
                    </if>
                    <if test="param.gycs != null">
                        AND a.GYCS LIKE concat(concat('%',#{param.gycs), '%')
                    </if>
                    <if test="param.sfapxlzx != null">
                        AND a.SFAPXLZX = #{param.sfapxlzx}
                    </if>
                    <if test="param.sfyjzgt != null">
                        AND a.SFYJZGT = #{param.sfyjzgt}
                    </if>
                    <if test="param.sfxjybgz != null">
                        AND a.SFXJYBGZ = #{param.sfxjybgz}
                    </if>
                    <if test="param.hxhfjh != null">
                        AND a.HXHFJH LIKE concat(concat('%',#{param.hxhfjh), '%')
                    </if>
    </sql>
</mapper>
