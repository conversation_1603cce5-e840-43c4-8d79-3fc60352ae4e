package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 心理咨询预约记录操作日志查询参数
 *
 * <AUTHOR>
 * @since 2025-04-09 17:45:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkAppointmentInfoLogParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 预约记录ID
     */
    @QueryField(type = QueryType.EQ)
    private String appointmentInfoId;

    /**
     * 预约记录状态
     */
    @QueryField(type = QueryType.EQ)
    private String status;

    /**
     * 备注
     */
    private String remark;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "CREATE_TIME desc";
    }
}
