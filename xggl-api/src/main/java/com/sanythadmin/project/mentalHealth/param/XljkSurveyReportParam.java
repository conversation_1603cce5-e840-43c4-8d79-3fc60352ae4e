package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理测评卷测评报告查询参数
 *
 * <AUTHOR>
 * @since 2025-04-27 14:37:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkSurveyReportParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 指标/因子名称
     */
    private String itemName;

    /**
     * 测评卷答题ID
     */
    @QueryField(type = QueryType.EQ)
    private String answerInfoId;

    /**
     * 类别（指标/因子，总分等）
     */
    @QueryField(type = QueryType.EQ)
    private String type;

}
