package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 心理危机档案信息库
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_CRISIS_REPOSITORY")
@Entity
@Table(name = "SYT_XLJK_CRISIS_REPOSITORY")
public class XljkCrisisRepository implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XB")
    private Gender xb;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ")
    @TableField("CSRQ")
    private String csrq;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC")
    @TableField("NJMC")
    private String njmc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC")
    @TableField("BJMC")
    private String bjmc;

    /**
     * 联系电话
     */
    @Column(name = "LXDH")
    @TableField("LXDH")
    private String lxdh;

    /**
     * 辅导员
     */
    @Column(name = "FDY")
    @TableField("FDY")
    private String fdy;

    /**
     * 辅导员联系方式
     */
    @Column(name = "FDYLXFS")
    @TableField("FDYLXFS")
    private String fdylxfs;

    /**
     * 宿舍信息
     */
    @Column(name = "SSXX")
    @TableField("SSXX")
    private String ssxx;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH")
    @TableField("CZRZH")
    private String czrzh;

    /**
     * 培养层次名称
     */
    @Column(name = "PYCCMC")
    @TableField("PYCCMC")
    private String pyccmc;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 关联危机上报ID
     */
//    @Column(name = "CRISIS_REPORT_ID")
//    @TableField("CRISIS_REPORT_ID")
//    private String crisisReportId;

    /**
     * 危机详情（危机类型（如抑郁、自残、焦虑、暴力倾向、自杀倾向等））
     */
    @Column(name = "WJLX", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("WJLX")
    private String wjlx;

    /**
     * 危机详情（危机情况描述（危机事件/问题描述）)
     */
    @Column(name = "WJSJMS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("WJSJMS")
    private String wjsjms;

    /**
     * 危机详情（危机评估等级)
     */
    @Column(name = "WJPGDJ", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("WJPGDJ")
    private String wjpgdj;

    /**
     * 危机详情（是否有既往心理危机记录）
     */
    @Column(name = "SFYJWXLWJJL")
    @TableField("SFYJWXLWJJL")
    private Integer sfyjwxlwjjl;

    /**
     * 处理过程（干预措施（心理咨询/家长沟通/医学评估/危机转介等））
     */
    @Column(name = "GYCS", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("GYCS")
    private String gycs;

    /**
     * 处理过程（是否安排心理咨询（是/否，若是，则记录咨询师信息））
     */
    @Column(name = "SFAPXLZX")
    @TableField("SFAPXLZX")
    private Integer sfapxlzx;

    /**
     * 处理过程（是否与家长沟通（是/否））
     */
    @Column(name = "SFYJZGT", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFYJZGT")
    private JudgeMark sfyjzgt;

    /**
     * 处理过程（是否需进一步关注（是/否））
     */
    @Column(name = "SFXJYBGZ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXJYBGZ")
    private JudgeMark sfxjybgz;

    /**
     * 处理过程（后续回访计划（定期访谈/心理健康测评等））
     */
    @Column(name = "HXHFJH")
    @TableField("HXHFJH")
    private String hxhfjh;

}
