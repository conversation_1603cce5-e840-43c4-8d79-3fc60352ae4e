package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;

import java.util.List;

/**
 * 心理健康咨询师排班信息Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkCounselorScheduleService extends IService<XljkCounselorSchedule> {
    public void operation(XljkCounselorSchedule schedule);

    public void removeSchedule(String id);

    public PageResult<XljkCounselorSchedule> page(XljkCounselorScheduleParam param);

    public List<XljkCounselorSchedule> list(XljkCounselorScheduleParam param, boolean addUserScope);
    public List<XljkConsultant> consultantList(XljkConsultantParam param);
}
