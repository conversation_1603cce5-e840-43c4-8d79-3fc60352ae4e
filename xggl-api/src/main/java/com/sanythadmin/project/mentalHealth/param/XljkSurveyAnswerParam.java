package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理测评卷答案查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkSurveyAnswerParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 答卷信息ID
     */
    private String infoId;

    /**
     * 心理和问卷关系ID
     */
    private String xsid;

    /**
     * 问卷ID
     */
    private String sid;

    /**
     * 问卷试题ID
     */
    private String contentId;

    /**
     * 分组ID
     */
    private String gid;

    /**
     * 问题类型
     */
    private String qtype;

    /**
     * 答卷结果
     */
    private String result;

    /**
     * 答题者账号
     */
    private String xgh;

    /**
     * 答题者姓名
     */
    private String xm;

    /**
     * 单选多选答题选项ID
     */
    private String resultId;

    /**
     * 答卷人IP
     */
    private String ip;

}
