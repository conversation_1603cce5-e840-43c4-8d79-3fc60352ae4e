<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkInterpretationMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, GID, MIN_SCORE, MIN_SCORE_OPERATOR, MAX_SCORE, MAX_SCORE_OPERATOR, REMARK, CREATE_TIME, UPDATE_TIME, USERNAME, ITEM_NAME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.gid != null">
                        AND a.GID LIKE concat(concat('%',#{param.gid), '%')
                    </if>
                    <if test="param.minScore != null">
                        AND a.MIN_SCORE = #{param.minScore}
                    </if>
                    <if test="param.minScoreOperator != null">
                        AND a.MIN_SCORE_OPERATOR LIKE concat(concat('%',#{param.minScoreOperator), '%')
                    </if>
                    <if test="param.maxScore != null">
                        AND a.MAX_SCORE = #{param.maxScore}
                    </if>
                    <if test="param.maxScoreOperator != null">
                        AND a.MAX_SCORE_OPERATOR LIKE concat(concat('%',#{param.maxScoreOperator), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.updateTime != null">
                        AND a.UPDATE_TIME LIKE concat(concat('%',#{param.updateTime), '%')
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
                    </if>
                    <if test="param.itemName != null">
                        AND a.ITEM_NAME LIKE concat(concat('%',#{param.itemName), '%')
                    </if>
    </sql>
</mapper>
