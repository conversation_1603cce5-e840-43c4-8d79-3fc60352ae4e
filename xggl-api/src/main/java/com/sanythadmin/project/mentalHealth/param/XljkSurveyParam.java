package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理健康测评问卷查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkSurveyParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 量表类别（日常测评/普查量表）
     */
    private String lblb;

    /**
     * 普查类型
     */
    private String pclx;

    /**
     * 答题次数
     */
    @QueryField(type = QueryType.EQ)
    private Integer kdtcs;

    /**
     * 创建人
     */
    private String cjr;

    /**
     * 创建人账号
     */
    private String cjrzh;

    /**
     * 关联问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 是/否允许查看测评结果
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfyxckcpjg;

}
