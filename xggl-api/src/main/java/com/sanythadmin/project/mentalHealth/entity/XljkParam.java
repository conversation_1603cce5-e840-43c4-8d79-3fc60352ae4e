package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 心理健康咨询预约参数设置
 *
 * <AUTHOR>
 * @since 2025-04-07 11:43:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_PARAM")
@Entity
@Table(name = "SYT_XLJK_PARAM")
public class XljkParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 预约窗口天数
     */
    @Column(name = "YYCKTS")
    @TableField("YYCKTS")
    private Integer yyckts;

    /**
     * 爽约次数
     */
    @Column(name = "SYCS")
    @TableField("SYCS")
    private Integer sycs;

    /**
     * 最多预约次数（自然年）
     */
    @Column(name = "NZDYYCS")
    @TableField("NZDYYCS")
    private Integer nzdyycs;

    /**
     * 预约知情同意书
     */
    @Column(name = "ZQTYS", columnDefinition = ColumnType.CLOB)
    @TableField("ZQTYS")
    private String zqtys;

}
