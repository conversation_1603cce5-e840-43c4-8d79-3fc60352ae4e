package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 咨询师信息查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 16:44:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkConsultantParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学工号
     */
    private String xgh;

    /**
     * 擅长领域
     */
    private String scly;

    /**
     * 从业经验
     */
    private String cyjy;

    /**
     * 资质证书
     */
    private String zzzs;

    /**
     * 心灵寄语
     */
    private String xljy;

}
