package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.mentalHealth.entity.XljkFollowupRecord;
import com.sanythadmin.project.mentalHealth.mapper.XljkFollowupRecordMapper;
import com.sanythadmin.project.mentalHealth.service.XljkFollowupRecordService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkFollowupRecordServiceImpl extends ServiceImpl<XljkFollowupRecordMapper, XljkFollowupRecord> implements XljkFollowupRecordService {

    @Resource
    private XljkFollowupRecordMapper mapper;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Transactional
    @Override
    public void operation(XljkFollowupRecord record) {
        UserInfo userInfo = userInfoMapper.selectById(record.getXgh());
        UserInfoUtil.codeTextSet(userInfo);
        BeanUtils.copyProperties(userInfo, record);
        if (StringUtils.hasText(record.getId())) {
            XljkFollowupRecord followupRecord = mapper.selectById(record.getId());
            record.setCreateTime(followupRecord.getCreateTime());
            mapper.updateById(record);
        } else {
            record.setCreateTime(LocalDateTime.now());
            mapper.insert(record);
        }
    }
}
