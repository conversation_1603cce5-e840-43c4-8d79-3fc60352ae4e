package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 心理危机上报审核流程查询参数
 *
 * <AUTHOR>
 * @since 2025-05-07 17:39:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCrisisReportNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 审核节点角色
     */
    @QueryField(type = QueryType.EQ)
    private String roleName;

    /**
     * 审核节点顺序
     */
    @QueryField(type = QueryType.EQ)
    private Integer sortNumber;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "SORT_NUMBER ASC";
    }
}
