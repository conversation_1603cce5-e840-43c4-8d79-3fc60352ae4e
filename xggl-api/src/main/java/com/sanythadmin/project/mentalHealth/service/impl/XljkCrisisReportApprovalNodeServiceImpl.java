package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportApprovalNodeMapper;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportApprovalNodeService;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportApprovalNode;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 心理危机上报审核记录Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkCrisisReportApprovalNodeServiceImpl extends ServiceImpl<XljkCrisisReportApprovalNodeMapper, XljkCrisisReportApprovalNode> implements XljkCrisisReportApprovalNodeService {

    @Resource
    private XljkCrisisReportApprovalNodeMapper mapper;

}
