package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 测评量表答卷信息查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkSurveyAnswerInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 心理问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String xsid;

    /**
     * 实际问卷ID
     */
    @QueryField(type = QueryType.EQ)
    private String sid;

    /**
     * 答卷得分
     */
    private Long score;

    /**
     * 答卷人
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

}
