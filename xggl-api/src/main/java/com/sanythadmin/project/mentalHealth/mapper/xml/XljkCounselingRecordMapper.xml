<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCounselingRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APPOINTMENT_INFO_ID, ZXDD, QDJJDWT, LFCS, XLCSFFHJS, DFZFKYFY, HXGJJH, BZXX, CREATE_TIME, CZR, CZRZH, SFCZZSZSQX, TLDZYNR, ZXSGCYPG, CBZDHWTFX, GYDJYHZD, ZXSZJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.appointmentInfoId != null">
                        AND a.APPOINTMENT_INFO_ID LIKE concat(concat('%',#{param.appointmentInfoId), '%')
                    </if>
                    <if test="param.zxdd != null">
                        AND a.ZXDD LIKE concat(concat('%',#{param.zxdd), '%')
                    </if>
                    <if test="param.qdjjdwt != null">
                        AND a.QDJJDWT LIKE concat(concat('%',#{param.qdjjdwt), '%')
                    </if>
                    <if test="param.lfcs != null">
                        AND a.LFCS = #{param.lfcs}
                    </if>
                    <if test="param.xlcsffhjs != null">
                        AND a.XLCSFFHJS LIKE concat(concat('%',#{param.xlcsffhjs), '%')
                    </if>
                    <if test="param.dfzfkyfy != null">
                        AND a.DFZFKYFY LIKE concat(concat('%',#{param.dfzfkyfy), '%')
                    </if>
                    <if test="param.hxgjjh != null">
                        AND a.HXGJJH LIKE concat(concat('%',#{param.hxgjjh), '%')
                    </if>
                    <if test="param.bzxx != null">
                        AND a.BZXX LIKE concat(concat('%',#{param.bzxx), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.czrzh != null">
                        AND a.CZRZH LIKE concat(concat('%',#{param.czrzh), '%')
                    </if>
                    <if test="param.sfczzszsqx != null">
                        AND a.SFCZZSZSQX = #{param.sfczzszsqx}
                    </if>
                    <if test="param.tldzynr != null">
                        AND a.TLDZYNR LIKE concat(concat('%',#{param.tldzynr), '%')
                    </if>
                    <if test="param.zxsgcypg != null">
                        AND a.ZXSGCYPG LIKE concat(concat('%',#{param.zxsgcypg), '%')
                    </if>
                    <if test="param.cbzdhwtfx != null">
                        AND a.CBZDHWTFX LIKE concat(concat('%',#{param.cbzdhwtfx), '%')
                    </if>
                    <if test="param.gydjyhzd != null">
                        AND a.GYDJYHZD LIKE concat(concat('%',#{param.gydjyhzd), '%')
                    </if>
                    <if test="param.zxszj != null">
                        AND a.ZXSZJ LIKE concat(concat('%',#{param.zxszj), '%')
                    </if>
    </sql>
</mapper>
