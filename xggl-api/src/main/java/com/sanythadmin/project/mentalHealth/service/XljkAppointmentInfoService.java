package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfoLog;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.param.XljkAppointmentInfoParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkAppointmentInfoDTO;

import java.util.List;

/**
 * 正式咨询预约记录Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkAppointmentInfoService extends IService<XljkAppointmentInfo> {

    public PageResult<XljkAppointmentInfo> page(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam);
    public PageResult<XljkAppointmentInfoDTO> pagePerson(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam,
                                                         UserInfoParam userInfoParam);

    public List<XljkAppointmentInfo> list(XljkAppointmentInfoParam param, XljkCounselorScheduleParam scheduleParam);

    public void operation(XljkAppointmentInfo appointmentInfo);

    public void updateStatus(XljkAppointmentInfoLog log);

    public void removeAppointmentInfo(String id);

}
