package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 咨询师信息Service
 *
 * <AUTHOR>
 * @since 2025-03-31 16:44:47
 */
public interface XljkConsultantService extends IService<XljkConsultant> {

    public void saveConsultant(MultipartHttpServletRequest request);
    public void removeConsultant(String id);
    public XljkConsultant get(String id);
    public PageResult<XljkConsultant> page(XljkConsultantParam param);
    public List<XljkConsultant> list(XljkConsultantParam param);
}
