<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportApprovalNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ROLE_NAME, RESULT, CRISIS_REPORT_ID, CREATE_TIME, UPDATE_TIME, SORT, REMARK
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.roleName != null">
                        AND a.ROLE_NAME LIKE concat(concat('%',#{param.roleName), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.crisisReportId != null">
                        AND a.CRISIS_REPORT_ID LIKE concat(concat('%',#{param.crisisReportId), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.updateTime != null">
                        AND a.UPDATE_TIME LIKE concat(concat('%',#{param.updateTime), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
    </sql>
</mapper>
