package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyAnswerService;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswer;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 心理健康教育/心理测评卷答案控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey-answer")
public class XljkSurveyAnswerController extends BaseController {
    @Resource
    private XljkSurveyAnswerService xljkSurveyAnswerService;

    /**
     * 分页查询心理测评卷答案（权限标识：mentalHealth:xljkSurveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswer:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyAnswer> page(XljkSurveyAnswerParam param) {
        PageParam<XljkSurveyAnswer, XljkSurveyAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkSurveyAnswerService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理测评卷答案（权限标识：mentalHealth:xljkSurveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswer:list')")
    @GetMapping()
    public List<XljkSurveyAnswer> list(XljkSurveyAnswerParam param) {
        PageParam<XljkSurveyAnswer, XljkSurveyAnswerParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkSurveyAnswerService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理测评卷答案（权限标识：mentalHealth:xljkSurveyAnswer:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswer:list')")
    @GetMapping("/{id}")
    public XljkSurveyAnswer get(@PathVariable("id") String id) {
        return xljkSurveyAnswerService.getById(id);
    }

    /**
     * 添加或修改心理测评卷答案（权限标识：mentalHealth:xljkSurveyAnswer:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswer:operation')")
    @OperationLog(module = "心理测评卷答案", comments = "保存心理测评卷答案")
    @PostMapping("/operation")
    public void save(@RequestBody XljkSurveyAnswer xljkSurveyAnswer) {
        if (StringUtils.hasLength(xljkSurveyAnswer.getId())) {
            xljkSurveyAnswerService.updateById(xljkSurveyAnswer);
        } else {
            xljkSurveyAnswerService.save(xljkSurveyAnswer);
        }
    }

    /**
     * 批量删除心理测评卷答案（权限标识：mentalHealth:xljkSurveyAnswer:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyAnswer:remove')")
    @OperationLog(module = "心理测评卷答案", comments = "批量删除心理测评卷答案")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkSurveyAnswerService.removeByIds(ids);
    }
}
