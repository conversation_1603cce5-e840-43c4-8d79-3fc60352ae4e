package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReport;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportApprovalNode;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportApprovalNodeParam;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCrisisReportQueryParam;

import java.util.List;

/**
 * 心理危机上报Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkCrisisReportService extends IService<XljkCrisisReport> {
    public PageResult<XljkCrisisReport> page(XljkCrisisReportParam param);
    public PageResult<XljkCrisisReport> pageApprovalList(XljkCrisisReportQueryParam param);
    public void operation(XljkCrisisReport xljkCrisisReport);
    public void removeCrisisReport(String id);
    public void approve(XljkCrisisReportApprovalNodeParam approvalNode);
}
