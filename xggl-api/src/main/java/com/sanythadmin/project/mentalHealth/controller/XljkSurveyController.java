package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkSurvey;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswer;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswerInfo;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerInfoParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyDTO;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyQueryParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyReportDTO;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyAnswerInfoService;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyService;
import com.sanythadmin.project.survey.enums.SurveyState;
import com.sanythadmin.project.survey.param.SurveyParam;
import com.sanythadmin.project.survey.service.SurveyService;
import com.sanythadmin.project.survey.vo.SurveyVO;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 心理健康教育/（测评端）心理健康测评问卷控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey")
public class XljkSurveyController extends BaseController {
    @Resource
    private XljkSurveyService xljkSurveyService;
    @Resource
    private XljkSurveyAnswerInfoService answerInfoService;
    @Resource
    private SurveyService surveyService;

    /**
     * 分页查询心理健康测评问卷（权限标识：mentalHealth:xljkSurvey:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurvey:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyDTO> page(XljkSurveyQueryParam param) {
        SurveyParam surveyParam = getSurveyParam(param);
        PageResult<XljkSurveyDTO> pageResult = xljkSurveyService.page(param.getParam(), surveyParam, false);
        List<XljkSurveyDTO> list = pageResult.getList();
        XljkSurveyAnswerInfoParam answerInfoParam = new XljkSurveyAnswerInfoParam();
        answerInfoParam.setXgh(SecurityUtil.getUsername());
        for (XljkSurveyDTO dto : list) {
            answerInfoParam.setXsid(dto.getId());
            answerInfoParam.setSid(dto.getSid());
            Long answerNum = answerInfoService.getAnswerNum(answerInfoParam);
            dto.setTestNum(answerNum);
        }
        return pageResult;
    }


    /**
     * 分页查询测评答卷记录
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurvey:list')")
    @GetMapping("/pageAnswerInfo")
    public PageResult<XljkSurveyAnswerInfo> pageAnswerInfo(XljkSurveyAnswerInfoParam param) {
        param.setXgh(SecurityUtil.getUsername());
        return answerInfoService.page(param);
    }

    /**
     * 查询全部心理健康测评问卷（权限标识：mentalHealth:xljkSurvey:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurvey:list')")
    @GetMapping()
    public List<XljkSurveyDTO> list(XljkSurveyQueryParam param) {
        SurveyParam surveyParam = getSurveyParam(param);
        return xljkSurveyService.list(param.getParam(), surveyParam, false);
    }

    private SurveyParam getSurveyParam(XljkSurveyQueryParam param) {
        SurveyParam surveyParam = param.getSurvey();
        if (surveyParam == null)
            surveyParam = new SurveyParam();
        surveyParam.setStatus(SurveyState.ENABLED);
        return surveyParam;
    }

    /**
     * 根据id查询心理健康测评问卷（权限标识：mentalHealth:xljkSurvey:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurvey:list')")
    @GetMapping("/{id}")
    public XljkSurvey get(@PathVariable("id") String id) {
        return xljkSurveyService.getById(id);
    }

    /**
     * 测评卷答案信息保存（权限标识：mentalHealth:xljkSurvey:answerOperation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurvey:answerOperation')")
    @OperationLog(module = "心理健康测评问卷", comments = "测评卷答案信息保存")
    @PostMapping("/answerOperation")
    public void answerOperation(MultipartHttpServletRequest request) {
        Map<String, Object> parsedData = CommonUtil.parseFormDataRequest(request);
        XljkSurveyAnswerInfo answerInfo = CommonUtil.toBean(parsedData, XljkSurveyAnswerInfo.class);
        answerInfo.setIp(CommonUtil.getIpAddr(request));
        answerInfoService.answerOperation(answerInfo);
    }


    /**
     * 根据答卷及测评记录查询答题结果
     */
    @GetMapping("/surveyAnswer")
    public SurveyVO surveyAnswer(XljkSurveyAnswerInfo answerInfo) {
        return surveyService.getSurveyVO(answerInfo.getSid(), answerInfo, XljkSurveyAnswer.class);
    }

    /**
     * 测评卷报告
     *
     * @param answerInfoId
     * @return
     */
    @GetMapping("/surveyReport/{answerInfoId}")
    public XljkSurveyReportDTO getReport(@PathVariable("answerInfoId") String answerInfoId) {
        return answerInfoService.answerInfoReport(answerInfoId);
    }
}
