package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 心理咨询记访谈记录
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_COUNSELING_RECORD")
@Entity
@Table(name = "SYT_XLJK_COUNSELING_RECORD")
public class XljkCounselingRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 预约记录ID
     */
    @Column(name = "APPOINTMENT_INFO_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("APPOINTMENT_INFO_ID")
    private String appointmentInfoId;

    /**
     * 咨询地点
     */
    @Column(name = "ZXDD", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("ZXDD")
    private String zxdd;


    /**
     * 期待解决的问题
     */
    @Column(name = "QDJJDWT", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("QDJJDWT")
    private String qdjjdwt;

    /**
     * 来访次数
     */
    @Column(name = "LFCS")
    @TableField("LFCS")
    private Integer lfcs;


    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;


    /**
     * 操作人
     */
    @Column(name = "CZR", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CZR")
    private String czr;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CZRZH")
    private String czrzh;

    /**
     * 主诉问题
     */
    @Column(name = "ZSWT", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ZSWT")
    private String zswt;

    /**
     * 观察与评估
     */
    @Column(name = "GCYPG", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("GCYPG")
    private String gcypg;

    /**
     * 采用的咨询方法和技术
     */
    @Column(name = "CYDZXFFHJS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CYDZXFFHJS")
    private String cydzxffhjs;

    /**
     * 咨询过程
     */
    @Column(name = "ZXGC", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ZXGC")
    private String zxgc;

    /**
     * 总结与备注
     */
    @Column(name = "ZJYBZ", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ZJYBZ")
    private String zjybz;

    /**
     * 是否存在自杀自伤风险
     */
    @Column(name = "SFCZZSZSFX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SFCZZSZSFX")
    private String sfczzszsfx;

    /**
     * 是否存在伤害他人风险
     */
    @Column(name = "SFCZSHTRFX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SFCZSHTRFX")
    private String sfczshtrfx;


    /**
     * 必要措施
     */
    @Column(name = "BYCS", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("BYCS")
    private String bycs;

//
//    /**
//     * （咨询过程记录）采用的咨询方法和技术
//     */
//    @Column(name = "XLCSFFHJS", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("XLCSFFHJS")
//    private String xlcsffhjs;
//
//    /**
//     * （咨询过程记录）到访者反馈与反应
//     */
//    @Column(name = "DFZFKYFY", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("DFZFKYFY")
//    private String dfzfkyfy;
//
//    /**
//     * （建议与计划）后续跟进计划（如是否需要进一步咨询、转介等）
//     */
//    @Column(name = "HXGJJH", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("HXGJJH")
//    private String hxgjjh;
//
//    /**
//     * （总结与备注）备注信息
//     */
//    @Column(name = "BZXX", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("BZXX")
//    private String bzxx;
//
//
//    /**
//     * 是/否存在自杀自伤倾向
//     */
//    @Column(name = "SFCZZSZSQX", columnDefinition = ColumnType.NUMBER_1)
//    @TableField("SFCZZSZSQX")
//    private JudgeMark sfczzszsqx;
//
//    /**
//     * （咨询过程记录）讨论的主要内容
//     */
//    @Column(name = "TLDZYNR", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("TLDZYNR")
//    private String tldzynr;
//
//    /**
//     * （评估与分析）咨询师的观察与评估（包括学生的情绪状态、行为表现等）
//     */
//    @Column(name = "ZXSGCYPG", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("ZXSGCYPG")
//    private String zxsgcypg;
//
//    /**
//     * （评估与分析）初步诊断或问题分析
//     */
//    @Column(name = "CBZDHWTFX", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("CBZDHWTFX")
//    private String cbzdhwtfx;
//
//    /**
//     * （建议与计划）给予的建议或指导
//     */
//    @Column(name = "GYDJYHZD", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("GYDJYHZD")
//    private String gydjyhzd;
//
//    /**
//     * （总结与备注）咨询师总结
//     */
//    @Column(name = "ZXSZJ", columnDefinition = ColumnType.VARCHAR2_512)
//    @TableField("ZXSZJ")
//    private String zxszj;

}
