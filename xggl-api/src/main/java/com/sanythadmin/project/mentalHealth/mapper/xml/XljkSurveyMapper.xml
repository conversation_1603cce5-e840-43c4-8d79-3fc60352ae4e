<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkSurveyMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, LBLB, PCLX, KDTCS, CJR, CJRZH, CREATE_TIME, SID, SFYXCKCPJG
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.lblb != null">
                        AND a.LBLB LIKE concat(concat('%',#{param.lblb), '%')
                    </if>
                    <if test="param.pclx != null">
                        AND a.PCLX LIKE concat(concat('%',#{param.pclx), '%')
                    </if>
                    <if test="param.kdtcs != null">
                        AND a.KDTCS = #{param.kdtcs}
                    </if>
                    <if test="param.cjr != null">
                        AND a.CJR LIKE concat(concat('%',#{param.cjr), '%')
                    </if>
                    <if test="param.cjrzh != null">
                        AND a.CJRZH LIKE concat(concat('%',#{param.cjrzh), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.sfyxckcpjg != null">
                        AND a.SFYXCKCPJG = #{param.sfyxckcpjg}
                    </if>
    </sql>
</mapper>
