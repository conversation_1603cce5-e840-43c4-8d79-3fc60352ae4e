package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 心理咨询预约记录操作日志（记录取消,爽约等操作）
 *
 * <AUTHOR>
 * @since 2025-04-09 17:45:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_APPOINTMENT_INFO_LOG")
@Entity
@Table(name = "SYT_XLJK_APPOINTMENT_INFO_LOG")
public class XljkAppointmentInfoLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 预约记录ID
     */
    @NotEmpty(message = "预约记录ID不能为空")
    @Column(name = "APPOINTMENT_INFO_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("APPOINTMENT_INFO_ID")
    private String appointmentInfoId;

    /**
     * 预约记录状态
     */
    @NotEmpty(message = "预约记录状态不能为空")
    @Column(name = "STATUS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("STATUS")
    private String status;

    /**
     * 备注
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 操作人姓名
     */
    @Column(name = "CZRXM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("CZRXM")
    private String czrxm;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CZRZH")
    private String czrzh;
}
