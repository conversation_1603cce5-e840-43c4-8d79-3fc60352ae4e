package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.entity.BaseSurveyAnswerInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 测评量表答卷信息
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_SURVEY_ANSWER_INFO")
@Entity
@Table(name = "SYT_XLJK_SURVEY_ANSWER_INFO")
public class XljkSurveyAnswerInfo extends BaseSurveyAnswerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 心理问卷ID
     */
    @Column(name = "XSID", columnDefinition = ColumnType.CHAR_32)
    @TableField("XSID")
    private String xsid;

    /**
     * 答题者姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 测试结果
     */
    @Column(name = "TEST_RESULT")
    @TableField(value = "TEST_RESULT", updateStrategy = FieldStrategy.ALWAYS)
    private String testResult;

    /**
     * 测评题目分类
     */
    @Column(name = "CATEGORY", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CATEGORY")
    private String category;

    /**
     * 测评卷答案
     */
    @Transient
    @TableField(exist = false)
    private List<XljkSurveyAnswer> answers;
}
