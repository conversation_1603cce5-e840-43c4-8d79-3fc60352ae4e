package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoMapper;
import com.sanythadmin.project.mentalHealth.mapper.XljkCounselorScheduleMapper;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;
import com.sanythadmin.project.mentalHealth.service.XljkConsultantService;
import com.sanythadmin.project.mentalHealth.service.XljkCounselorScheduleService;
import jakarta.annotation.Resource;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 心理健康咨询师排班信息Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Service
public class XljkCounselorScheduleServiceImpl extends ServiceImpl<XljkCounselorScheduleMapper, XljkCounselorSchedule> implements XljkCounselorScheduleService {
    @Resource
    private XljkCounselorScheduleMapper mapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private XljkConsultantService xljkConsultantService;
    @Resource
    private XljkAppointmentInfoMapper xljkAppointmentInfoMapper;

    private void extracted(Object param) {
        try {
            if (!Objects.equals(Constants.ROLE_SCOPE_QX, SecurityUtil.getRoleScope()))
                PropertyUtils.setProperty(param, "xgh", SecurityUtil.getUsername());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void operation(XljkCounselorSchedule schedule) {
        extracted(schedule);
        if (StringUtils.hasLength(schedule.getId())) {
            XljkCounselorSchedule selected = baseMapper.selectById(schedule.getId());
            schedule.setXgh(selected.getXgh());
            schedule.setXm(selected.getXm());
            buildTime(schedule);
            List<XljkCounselorSchedule> schedules = queryExists(schedule);
            if (!CollectionUtils.isEmpty(schedules) && !schedules.get(0).getId().equals(schedule.getId()))
                AssertUtil.throwMessage("该时间段已存在");
            baseMapper.updateById(schedule);
        } else {
            List<XljkCounselorSchedule> schedules = new ArrayList<>();
            if (Constants.SCHEDULE_TYPE_WEEK.equals(schedule.getPblx())) {
                LocalDate currentDate = LocalDate.now();
                LocalDate endDate = schedule.getYyckq() == null ? currentDate.plusDays(30) : schedule.getYyckq();
                HashMap<String, List<LocalDate>> map = getDateByWeek(currentDate, endDate, schedule.getXqmc());
                for (Map.Entry<String, List<LocalDate>> entry : map.entrySet()) {
                    String key = entry.getKey();
                    List<LocalDate> dates = entry.getValue();
                    for (LocalDate localDate : dates) {
                        if (currentDate.equals(localDate)) {
                            String[] strings = CommonUtil.split(schedule.getKssjStr(), ":");
                            LocalDateTime startDateTime = LocalDateTime.of(localDate, LocalTime.of(Integer.parseInt(strings[0]), Integer.parseInt(strings[1])));
                            if (!LocalDateTime.now().isBefore(startDateTime))
                                continue;
                        }

                        XljkCounselorSchedule newSchedule = new XljkCounselorSchedule();
                        BeanUtils.copyProperties(schedule, newSchedule);
                        LocalDateTime localDateTime = localDate.atStartOfDay();
                        newSchedule.setXqmc(key);
                        newSchedule.setGzrq(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
                        schedules.add(newSchedule);
                    }
                }
            } else {
                String[] dateArrays = CommonUtil.split(schedule.getGzrqStr());
                for (String text : dateArrays) {
                    XljkCounselorSchedule newSchedule = new XljkCounselorSchedule();
                    BeanUtils.copyProperties(schedule, newSchedule);
                    newSchedule.setGzrq(DateUtil.parse(text, DateUtil.DEFAULT_DATE_FORMAT));
                    newSchedule.setXqmc(DateUtil.getWeekOfDate(newSchedule.getGzrq()));
                    schedules.add(newSchedule);
                }
            }

            if (!StringUtils.hasText(schedule.getXgh()) && !StringUtils.hasText(schedule.getZxsxx()))
                AssertUtil.throwMessage("咨询师信息不能为空");

            List<String> userList = new ArrayList<>();
            String separator = "|";
            if (StringUtils.hasText(schedule.getXgh())) {
                UserInfo userInfo = userInfoMapper.selectById(schedule.getXgh());
                userList.add(schedule.getXgh().concat(separator).concat(userInfo.getXm()));
            } else {
                userList = Arrays.stream(CommonUtil.split(schedule.getZxsxx())).toList();
            }

            for (String text : userList) {
                String[] strings = text.split("\\" + separator);
                for (XljkCounselorSchedule counselorSchedule : schedules) {
                    counselorSchedule.setId(null);
                    counselorSchedule.setXgh(strings[0]);
                    counselorSchedule.setXm(strings[1]);
                    buildTime(counselorSchedule);
                    List<XljkCounselorSchedule> exists = queryExists(counselorSchedule);
                    if (!CollectionUtils.isEmpty(exists))
                        continue;
                    baseMapper.insert(counselorSchedule);
                }
            }
        }
    }

    @Override
    public void removeSchedule(String id) {
        Long count = xljkAppointmentInfoMapper.selectCount(new LambdaQueryWrapper<XljkAppointmentInfo>()
                .eq(XljkAppointmentInfo::getCounselorScheduleId, id));
        if (count > 0)
            AssertUtil.throwMessage("删除失败, 当前排班计划存在预约记录");
        baseMapper.deleteById(id);
    }

    private static void buildTime(XljkCounselorSchedule counselorSchedule) {
        String date = DateUtil.getDateTime(counselorSchedule.getGzrq(), DateUtil.DEFAULT_DATE_FORMAT);
        counselorSchedule.setKssj(DateUtil.getDate(date + " " + counselorSchedule.getKssjStr(), DateUtil.DEFAULT_DATE_FORMAT_HOURS_MINUTES));
        counselorSchedule.setJssj(DateUtil.getDate(date + " " + counselorSchedule.getJssjStr(), DateUtil.DEFAULT_DATE_FORMAT_HOURS_MINUTES));
    }


    private List<XljkCounselorSchedule> queryExists(XljkCounselorSchedule counselorSchedule) {
        List<XljkCounselorSchedule> schedules = baseMapper.selectList(new LambdaQueryWrapper<XljkCounselorSchedule>()
//                .eq(XljkCounselorSchedule::getGzrq, counselorSchedule.getGzrq())
                .eq(XljkCounselorSchedule::getXgh, counselorSchedule.getXgh())
                .eq(XljkCounselorSchedule::getKssj, counselorSchedule.getKssj()));
        setRealName(schedules);
        return schedules;
    }

    private void appendQueryCondition(QueryWrapper<XljkCounselorSchedule> wrapper, XljkCounselorScheduleParam param) {
        LambdaQueryWrapper<XljkCounselorSchedule> lambdaQueryWrapper = wrapper.lambda();
        if (param.getStartDate() != null)
            lambdaQueryWrapper.ge(XljkCounselorSchedule::getGzrq, param.getStartDate());
        if (param.getEndDate() != null)
            lambdaQueryWrapper.le(XljkCounselorSchedule::getGzrq, param.getEndDate());
        if (param.getGeStartdate() != null)
            lambdaQueryWrapper.ge(XljkCounselorSchedule::getKssj, param.getGeStartdate());
        if (param.getTime() != null)
            lambdaQueryWrapper.le(XljkCounselorSchedule::getKssj, param.getTime())
                    .ge(XljkCounselorSchedule::getJssj, param.getTime());
    }

    @Override
    public PageResult<XljkCounselorSchedule> page(XljkCounselorScheduleParam param) {
        extracted(param);
        PageParam<XljkCounselorSchedule, XljkCounselorScheduleParam> page = new PageParam<>(param);
        QueryWrapper<XljkCounselorSchedule> orderWrapper = page.getOrderWrapper();
        appendQueryCondition(orderWrapper, param);
        page = mapper.selectPage(page, orderWrapper);
        List<XljkCounselorSchedule> schedules = page.getRecords();
        setRealName(schedules);
        return new PageResult<>(schedules, page.getTotal());
    }

    @Override
    public List<XljkCounselorSchedule> list(XljkCounselorScheduleParam param, boolean addUserScope) {
        if (addUserScope)
            extracted(param);
        PageParam<XljkCounselorSchedule, XljkCounselorScheduleParam> page = new PageParam<>(param);
        QueryWrapper<XljkCounselorSchedule> orderWrapper = page.getOrderWrapper();
        appendQueryCondition(orderWrapper, param);
        List<XljkCounselorSchedule> schedules = mapper.selectList(orderWrapper);
        setRealName(schedules);
        return schedules;
    }

    @Override
    public List<XljkConsultant> consultantList(XljkConsultantParam param) {
        extracted(param);
        return xljkConsultantService.list(param);
    }

    private void setRealName(List<XljkCounselorSchedule> schedules) {
        if (!CollectionUtils.isEmpty(schedules)) {
            Set<String> hashSet = schedules.stream().map(XljkCounselorSchedule::getXgh).collect(Collectors.toSet());
            UserInfoParam param = new UserInfoParam();
            param.setXgh(CommonUtil.strJoin(hashSet));
            List<UserInfo> list = userInfoService.list(param, UserInfo::getXgh, UserInfo::getXm);
            Map<String, String> stringMap = list.stream().collect(Collectors.toMap(UserInfo::getXgh, UserInfo::getXm));
            schedules.forEach(schedule -> {
                schedule.setXm(stringMap.get(schedule.getXgh()));
            });
        }
    }

    public static HashMap<String, DayOfWeek> dayOfWeekMap() {
        HashMap<String, DayOfWeek> dayOfWeekMap = new HashMap<>();
        dayOfWeekMap.put("星期一", DayOfWeek.MONDAY);
        dayOfWeekMap.put("星期二", DayOfWeek.TUESDAY);
        dayOfWeekMap.put("星期三", DayOfWeek.WEDNESDAY);
        dayOfWeekMap.put("星期四", DayOfWeek.THURSDAY);
        dayOfWeekMap.put("星期五", DayOfWeek.FRIDAY);
        dayOfWeekMap.put("星期六", DayOfWeek.SATURDAY);
        dayOfWeekMap.put("星期日", DayOfWeek.SUNDAY);
        return dayOfWeekMap;
    }

    public static HashMap<String, List<LocalDate>> getDateByWeek(LocalDate startDate, LocalDate endDate, String weekText) {
        HashMap<String, List<LocalDate>> dateMap = new HashMap<>();
        HashMap<String, DayOfWeek> dayOfWeekMap = dayOfWeekMap();
        Arrays.stream(CommonUtil.split(weekText)).forEach(text -> {
            DayOfWeek dayOfWeek = dayOfWeekMap.get(text);
            List<LocalDate> list = getDateByWeek(startDate, endDate, dayOfWeek);
            List<LocalDate> dates = dateMap.get(text);
            if (dates != null)
                list.addAll(dates);
            dateMap.put(text, list);
        });
        return dateMap;
    }

    public static List<LocalDate> getDateByWeek(LocalDate startDate, LocalDate endDate, DayOfWeek dayOfWeek) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = startDate.with(TemporalAdjusters.nextOrSame(dayOfWeek));
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusWeeks(1);
        }
        return dates;
    }
}
