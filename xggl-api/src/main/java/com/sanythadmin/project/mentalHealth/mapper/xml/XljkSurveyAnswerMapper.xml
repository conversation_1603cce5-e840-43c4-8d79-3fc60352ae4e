<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkSurveyAnswerMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , INFO_ID, XSID, SID, CONTENT_ID, GID, QTYPE, POINT, RESULT, XGH, XM, CREATE_TIME, RESULT_ID, IP
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.infoId != null">
            AND a.INFO_ID LIKE concat(concat('%',#{param.infoId), '%')
        </if>
        <if test="param.xsid != null">
            AND a.XSID LIKE concat(concat('%',#{param.xsid), '%')
        </if>
        <if test="param.sid != null">
            AND a.SID LIKE concat(concat('%',#{param.sid), '%')
        </if>
        <if test="param.contentId != null">
            AND a.CONTENT_ID LIKE concat(concat('%',#{param.contentId), '%')
        </if>
        <if test="param.gid != null">
            AND a.GID LIKE concat(concat('%',#{param.gid), '%')
        </if>
        <if test="param.qtype != null">
            AND a.QTYPE LIKE concat(concat('%',#{param.qtype), '%')
        </if>
        <if test="param.point != null">
            AND a.POINT = #{param.point}
        </if>
        <if test="param.result != null">
            AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
        </if>
        <if test="param.xgh != null">
            AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
        </if>
        <if test="param.xm != null">
            AND a.XM LIKE concat(concat('%',#{param.xm), '%')
        </if>
        <if test="param.createTime != null">
            AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
        </if>
        <if test="param.resultId != null">
            AND a.RESULT_ID LIKE concat(concat('%',#{param.resultId), '%')
        </if>
        <if test="param.ip != null">
            AND a.IP LIKE concat(concat('%',#{param.ip), '%')
        </if>
    </sql>

    <select id="getSurveyItemScore" resultType="com.sanythadmin.project.mentalHealth.entity.XljkSurveyReport">
        select c.bz item_name, sum(c.point) val, count(*) question_num, '因子' type, round(avg(c.point), 2) avg_score
        from (select a.*, b.bz
              from syt_xljk_survey_answer a
                       left join syt_survey_pquestions b on a.content_id = b.id
              where a.info_id = #{answerInfoId}) c
        group by c.bz
        order by sum(c.point) desc
    </select>

    <select id="getSurveyScoreAndAvg" resultType="com.sanythadmin.project.mentalHealth.entity.XljkSurveyReport">
        select '总分' as item_name, sum(point) val, '其他' type
        from syt_xljk_survey_answer
        where info_id = #{answerInfoId}
        union all
        select '总均分' as item_name, round(avg(point), 2) val, '其他' type
        from syt_xljk_survey_answer
        where info_id = #{answerInfoId}
    </select>

    <select id="scl90PositiveInfo" resultType="com.sanythadmin.project.mentalHealth.entity.XljkSurveyReport">
        select '阳性项目数' item_name, count(*) val, count(*) question_num, '其他' type
        from syt_xljk_survey_answer a
        where a.info_id = #{answerInfoId}
          and result
        <![CDATA[ <> ]]>'A'
        union all
        select '阳性症状均分' item_name, round(avg(point), 2) val, count(*) question_num, '其他' type
        from syt_xljk_survey_answer a
        where a.info_id = #{answerInfoId}
          and result
        <![CDATA[ <> ]]>'A'
    </select>
</mapper>
