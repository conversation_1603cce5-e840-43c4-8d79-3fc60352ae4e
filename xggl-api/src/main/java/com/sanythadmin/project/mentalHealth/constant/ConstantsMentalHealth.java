package com.sanythadmin.project.mentalHealth.constant;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.utils.CommonUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by JIANGPING on 2025/4/29.
 */
public class ConstantsMentalHealth extends Constants {
    public static final String QUESTION_CLASS_SCL90 = "SCL-90自测量表";
    public static final String QUESTION_CLASS_ANXIETY = "焦虑自测量表";
    public static final String QUESTION_CLASS_HAMD = "汉密顿抑郁量表(HAMD)";
    public static final String QUESTION_CLASS_OTHER = "其他";
    public static final String OPERATOR_GE = "大于等于";
    public static final String OPERATOR_GT = "大于";
    public static final String OPERATOR_LE = "小于等于";
    public static final String OPERATOR_LT = "小于";

    public static List<String> getQuestionClassList() {
        List<String> list = new ArrayList<>();
        Field[] fields = CommonUtil.getAllFields(ConstantsMentalHealth.class, true);
        for (Field field : fields) {
            String fieldName = field.getName();
            if (!fieldName.startsWith("QUESTION_CLASS_"))
                continue;
            Object value = CommonUtil.getValue(fieldName, ConstantsMentalHealth.class);
            list.add(String.valueOf(value));
        }
        return list;
    }
}
