package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselingRecord;
import com.sanythadmin.project.mentalHealth.param.XljkCounselingRecordParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCounselingRecordQueryParam;

import java.util.List;

/**
 * 心理咨询记录Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkCounselingRecordService extends IService<XljkCounselingRecord> {
    public void operation(XljkCounselingRecord xljkCounselingRecord);
    public PageResult<XljkCounselingRecord> page(XljkCounselingRecordParam param);
    public List<XljkCounselingRecord> list(XljkCounselingRecordQueryParam param);

}
