package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 正式咨询预约记录查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkAppointmentInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 咨询问题分类
     */
    private String zxwtfl;

    /**
     * 主要问题或困扰
     */
    private String wtjs;

    /**
     * 是否紧急情况
     */
    private String sfjjqk;

    /**
     * 紧急联系人
     */
    private String jjlxr;

    /**
     * 紧急联系人联系方式
     */
    private String jjlxrlxfs;

    /**
     * 咨询师排班信息ID
     */
    private String counselorScheduleId;

    /**
     * 是否同意隐私政策
     */
    private String sftyyszc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 审核状态
     */
    @QueryField(type = QueryType.IN_STR)
    private String status;

    /**
     * 审核操作人
     */
    private String shczr;

    /**
     * 审核操作人账号
     */
    private String shczrzh;

    /**
     * 审核时间
     */
    private String shsj;

    /**
     * 审核备注
     */
    private String shbz;

    /**
     * 咨询目的
     */
    private String zxmd;

    /**
     * 咨询历史
     */
    private String zxls;

    /**
     * 家庭详细住址
     */
    private String jtxxzz;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "CREATE_TIME DESC";
    }
}
