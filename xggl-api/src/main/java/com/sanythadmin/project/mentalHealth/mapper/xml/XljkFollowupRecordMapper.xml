<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkFollowupRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, XM, XB, CSRQ, XYMC, NJMC, ZYMC, BJMC, LXDH, PYCCMC, PYCCID, XYID, ZYID, BJID, NJID, CREATE_TIME, CZR, CZRZH, HFSJ, HFFS, HFR, YDSRGTQK
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xb != null">
                        AND a.XB = #{param.xb}
                    </if>
                    <if test="param.csrq != null">
                        AND a.CSRQ LIKE concat(concat('%',#{param.csrq), '%')
                    </if>
                    <if test="param.xymc != null">
                        AND a.XYMC LIKE concat(concat('%',#{param.xymc), '%')
                    </if>
                    <if test="param.njmc != null">
                        AND a.NJMC LIKE concat(concat('%',#{param.njmc), '%')
                    </if>
                    <if test="param.zymc != null">
                        AND a.ZYMC LIKE concat(concat('%',#{param.zymc), '%')
                    </if>
                    <if test="param.bjmc != null">
                        AND a.BJMC LIKE concat(concat('%',#{param.bjmc), '%')
                    </if>
                    <if test="param.lxdh != null">
                        AND a.LXDH LIKE concat(concat('%',#{param.lxdh), '%')
                    </if>
                    <if test="param.pyccmc != null">
                        AND a.PYCCMC LIKE concat(concat('%',#{param.pyccmc), '%')
                    </if>
                    <if test="param.pyccid != null">
                        AND a.PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.czrzh != null">
                        AND a.CZRZH LIKE concat(concat('%',#{param.czrzh), '%')
                    </if>
                    <if test="param.hfsj != null">
                        AND a.HFSJ LIKE concat(concat('%',#{param.hfsj), '%')
                    </if>
                    <if test="param.hffs != null">
                        AND a.HFFS LIKE concat(concat('%',#{param.hffs), '%')
                    </if>
                    <if test="param.hfr != null">
                        AND a.HFR LIKE concat(concat('%',#{param.hfr), '%')
                    </if>
                    <if test="param.ydsrgtqk != null">
                        AND a.YDSRGTQK LIKE concat(concat('%',#{param.ydsrgtqk), '%')
                    </if>
    </sql>
</mapper>
