package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.apache.ibatis.type.JdbcType;

/**
 * 心理危机上报审核记录
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_CRISIS_REPORT_APPROVAL_NODE")
@Entity
@Table(name = "SYT_XLJK_CRISIS_REPORT_APPROVAL_NODE")
public class XljkCrisisReportApprovalNode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色名称
     */
    @Column(name = "ROLE_NAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ROLE_NAME")
    private String roleName;

    /**
     * 审核结果
     */
    @Column(name = "RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("RESULT")
    private String result;

    /**
     * 下级节点审批结果
     */
    @Column(name = "NEXT_NODE_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "NEXT_NODE_RESULT", jdbcType = JdbcType.VARCHAR, updateStrategy = FieldStrategy.ALWAYS)
    private String nextNodeResult;

    /**
     * 上级节点审批结果
     */
    @Column(name = "PRE_NODE_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "PRE_NODE_RESULT", jdbcType = JdbcType.VARCHAR, updateStrategy = FieldStrategy.ALWAYS)
    private String preNodeResult;

    /**
     * 危机上报ID
     */
    @Column(name = "CRISIS_REPORT_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CRISIS_REPORT_ID")
    private String crisisReportId;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 审核意见
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 审核人
     */
    @Column(name = "USERNAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("USERNAME")
    private String username;

    /**
     * 审核人姓名
     */
    @Column(name = "REAL_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("REAL_NAME")
    private String realName;

}
