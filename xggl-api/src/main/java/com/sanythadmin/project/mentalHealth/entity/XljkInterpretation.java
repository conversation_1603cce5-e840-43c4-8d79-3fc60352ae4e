package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测评卷测评结果解释
 *
 * <AUTHOR>
 * @since 2025-04-25 11:33:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_INTERPRETATION")
@Entity
@Table(name = "SYT_XLJK_INTERPRETATION")
public class XljkInterpretation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 试题类型ID
     */
    @Column(name = "GID", columnDefinition = ColumnType.CHAR_32)
    @TableField("GID")
    private String gid;

    /**
     * 最小分数
     */
    @Column(name = "MIN_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MIN_SCORE")
    private Double minScore;

    /**
     * 最小分数运算符号（大于/大于等于）
     */
    @Column(name = "MIN_SCORE_OPERATOR", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("MIN_SCORE_OPERATOR")
    private String minScoreOperator;

    /**
     * 最大分数
     */
    @Column(name = "MAX_SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("MAX_SCORE")
    private Double maxScore;

    /**
     * 最小分数运算符号（小于/小于等于）
     */
    @Column(name = "MAX_SCORE_OPERATOR", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("MAX_SCORE_OPERATOR")
    private String maxScoreOperator;

    /**
     * 分数解释
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @Column(name = "USERNAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("USERNAME")
    private String username;

    /**
     * 指标名称
     */
    @Column(name = "ITEM_NAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ITEM_NAME")
    private String itemName;

}
