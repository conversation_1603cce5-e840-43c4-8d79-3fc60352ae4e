package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkFollowupRecordService;
import com.sanythadmin.project.mentalHealth.entity.XljkFollowupRecord;
import com.sanythadmin.project.mentalHealth.param.XljkFollowupRecordParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 心理健康教育/心理回访记录控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-followup-record")
public class XljkFollowupRecordController extends BaseController {
    @Resource
    private XljkFollowupRecordService xljkFollowupRecordService;

    /**
     * 分页查询（权限标识：mentalHealth:xljkFollowupRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkFollowupRecord:list')")
    @GetMapping("/page")
    public PageResult<XljkFollowupRecord> page(XljkFollowupRecordParam param) {
        PageParam<XljkFollowupRecord, XljkFollowupRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkFollowupRecordService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部（权限标识：mentalHealth:xljkFollowupRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkFollowupRecord:list')")
    @GetMapping()
    public List<XljkFollowupRecord> list(XljkFollowupRecordParam param) {
        PageParam<XljkFollowupRecord, XljkFollowupRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkFollowupRecordService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询（权限标识：mentalHealth:xljkFollowupRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkFollowupRecord:list')")
    @GetMapping("/{id}")
    public XljkFollowupRecord get(@PathVariable("id") String id) {
        return xljkFollowupRecordService.getById(id);
    }

    /**
     * 添加或修改（权限标识：mentalHealth:xljkFollowupRecord:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkFollowupRecord:operation')")
    @OperationLog(module = "", comments = "保存")
    @PostMapping("/operation")
    public void save(@RequestBody XljkFollowupRecord xljkFollowupRecord) {
        xljkFollowupRecordService.operation(xljkFollowupRecord);
    }

    /**
     * 批量删除（权限标识：mentalHealth:xljkFollowupRecord:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkFollowupRecord:remove')")
    @OperationLog(module = "", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkFollowupRecordService.removeByIds(ids);
    }
}
