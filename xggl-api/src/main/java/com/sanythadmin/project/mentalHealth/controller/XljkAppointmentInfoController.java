package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfo;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfoLog;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselorSchedule;
import com.sanythadmin.project.mentalHealth.param.XljkAppointmentInfoLogParam;
import com.sanythadmin.project.mentalHealth.param.XljkAppointmentInfoParam;
import com.sanythadmin.project.mentalHealth.param.XljkCounselorScheduleParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkAppointmentInfoDTO;
import com.sanythadmin.project.mentalHealth.pojo.XljkAppointmentInfoQueryParam;
import com.sanythadmin.project.mentalHealth.service.XljkAppointmentInfoLogService;
import com.sanythadmin.project.mentalHealth.service.XljkAppointmentInfoService;
import com.sanythadmin.project.mentalHealth.service.XljkCounselorScheduleService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 心理健康教育/正式咨询预约记录控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-appointment-info")
public class XljkAppointmentInfoController extends BaseController {
    @Resource
    private XljkAppointmentInfoService xljkAppointmentInfoService;
    @Resource
    private XljkAppointmentInfoLogService xljkAppointmentInfoLogService;
    @Resource
    private XljkCounselorScheduleService xljkCounselorScheduleService;
    @Resource
    private UserInfoService userInfoService;


    /**
     * 分页查询正式咨询预约记录（权限标识：mentalHealth:xljkAppointmentInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:list')")
    @GetMapping("/page")
    public PageResult<XljkAppointmentInfoDTO> page(XljkAppointmentInfoParam param) {
        PageResult<XljkAppointmentInfo> page = xljkAppointmentInfoService.page(param, getScheduleParam());
        List<XljkAppointmentInfoDTO> list = getXljkAppointmentInfoDTOS(page.getList());
        setPhoto(list);
        return new PageResult<>(list, page.getCount());
    }

    /**
     * 分页查询正式咨询预约者记录（权限标识：mentalHealth:xljkAppointmentInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:list')")
    @GetMapping("/pagePerson")
    public PageResult<XljkAppointmentInfoDTO> pagePerson(XljkAppointmentInfoQueryParam queryParam) {
        PageResult<XljkAppointmentInfoDTO> pageResult = xljkAppointmentInfoService.pagePerson(queryParam.getAppointmentInfo(),
                getScheduleParam(), queryParam.getUserInfo());
        setPhoto(pageResult.getList());
        return pageResult;
    }


    private XljkCounselorScheduleParam getScheduleParam() {
        XljkCounselorScheduleParam scheduleParam = null;
        if (!Constants.ROLE_SCOPE_QX.equals(SecurityUtil.getRoleScope())) {
            scheduleParam = new XljkCounselorScheduleParam();
            scheduleParam.setXgh(SecurityUtil.getUsername());
        }
        return scheduleParam;
    }


    /**
     * 查询全部正式咨询预约记录（权限标识：mentalHealth:xljkAppointmentInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:list')")
    @GetMapping()
    public List<XljkAppointmentInfoDTO> list(XljkAppointmentInfoParam param) {
        List<XljkAppointmentInfo> list = xljkAppointmentInfoService.list(param, getScheduleParam());
        return getXljkAppointmentInfoDTOS(list);
    }

    /**
     * 根据id查询正式咨询预约记录（权限标识：mentalHealth:xljkAppointmentInfo:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:list')")
    @GetMapping("/{id}")
    public XljkAppointmentInfo get(@PathVariable("id") String id) {
        return xljkAppointmentInfoService.getById(id);
    }

    /**
     * 添加或修改正式咨询预约记录（权限标识：mentalHealth:xljkAppointmentInfo:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:operation')")
    @OperationLog(module = "正式咨询预约记录", comments = "保存正式咨询预约记录")
    @PostMapping("/operation")
    public void save(@RequestBody XljkAppointmentInfo xljkAppointmentInfo) {
        xljkAppointmentInfoService.operation(xljkAppointmentInfo);
    }


    /**
     * 批量删除正式咨询预约记录（权限标识：mentalHealth:xljkAppointmentInfo:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:remove')")
    @OperationLog(module = "正式咨询预约记录", comments = "批量删除正式咨询预约记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            xljkAppointmentInfoService.removeAppointmentInfo(id);
        }
    }


    /**
     * 预约记录状态变更
     *
     * @param log
     */
    @OperationLog(module = "正式咨询预约记录", comments = "预约记录状态变更")
    @PostMapping("/updateStatus")
    public void updateStatus(@RequestBody XljkAppointmentInfoLog log) {
        xljkAppointmentInfoService.updateStatus(log);
    }


    /**
     * 查询全部心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppointmentInfo:list')")
    @GetMapping("/counselorScheduleList")
    public List<XljkCounselorSchedule> counselorScheduleList(XljkCounselorScheduleParam param) {
        param.setGeStartdate(new Date());   // 查询开始时间大于当前时间排班信息
        return xljkCounselorScheduleService.list(param, false);
    }

    /**
     * 我的最近一条预约记录
     *
     * @return
     */
    @GetMapping("/myLastAppointmentInfo")
    public XljkAppointmentInfoDTO myLastAppointmentInfo() {
        XljkAppointmentInfoParam param = new XljkAppointmentInfoParam();
        param.setXgh(SecurityUtil.getUsername());
        param.setStatus(CommonUtil.strJoin(Constants.YUYUEZHONG, Constants.YIQUEREN, Constants.YIWANCHENG));
        List<XljkAppointmentInfo> list = xljkAppointmentInfoService.list(param, null);
        XljkAppointmentInfo appointmentInfo = CommonUtil.listGetOne(list);
        XljkAppointmentInfoDTO dto = null;
        if (appointmentInfo != null) {
            dto = new XljkAppointmentInfoDTO();
            BeanUtils.copyProperties(appointmentInfo, dto);
            XljkCounselorSchedule schedule = xljkCounselorScheduleService.getById(appointmentInfo.getCounselorScheduleId());
            dto.setSchedule(schedule);
        }
        return dto;
    }

    /**
     * 我的正式咨询预约记录
     */
    @GetMapping("/myAppointmentInfo")
    public List<XljkAppointmentInfoDTO> myAppointmentInfo(XljkAppointmentInfoParam param) {
        param.setXgh(SecurityUtil.getUsername());
        List<XljkAppointmentInfo> list = xljkAppointmentInfoService.list(param, null);
        return getXljkAppointmentInfoDTOS(list);
    }

    private void setPhoto(List<XljkAppointmentInfoDTO> list) {
        if (!CollectionUtils.isEmpty(list)) {
            List<String> usernameList = list.stream().map(XljkAppointmentInfoDTO::getXgh).toList();
            UserInfoParam param = new UserInfoParam();
            param.setXgh(CommonUtil.strJoin(usernameList));
            List<UserInfo> userInfos = userInfoService.list(param, UserInfo::getXgh, UserInfo::getPhoto);
            Map<String, String> stringMap = userInfos.stream().filter(o -> StringUtils.hasText(o.getPhoto()))
                    .collect(Collectors.toMap(UserInfo::getXgh, UserInfo::getPhoto));
            list.forEach(obj -> obj.setPhoto(stringMap.get(obj.getXgh())));
        }
    }

    private List<XljkAppointmentInfoDTO> getXljkAppointmentInfoDTOS(List<XljkAppointmentInfo> list) {
        List<XljkAppointmentInfoDTO> dtoList = new ArrayList<>();
        for (XljkAppointmentInfo appointmentInfo : list) {
            XljkAppointmentInfoDTO dto = new XljkAppointmentInfoDTO();
            BeanUtils.copyProperties(appointmentInfo, dto);
            XljkCounselorSchedule schedule = xljkCounselorScheduleService.getById(appointmentInfo.getCounselorScheduleId());
            dto.setSchedule(schedule);
            dtoList.add(dto);
        }
        return dtoList;
    }

    /**
     * 预约记录状态日志
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkAppageParampointmentInfo:list')")
    @GetMapping("/log")
    public PageResult<XljkAppointmentInfoLog> page(XljkAppointmentInfoLogParam param) {
        PageParam<XljkAppointmentInfoLog, XljkAppointmentInfoLogParam> pageParam = new PageParam<>(param);
        pageParam = xljkAppointmentInfoLogService.page(pageParam, pageParam.getOrderWrapper());
        return new PageResult<>(pageParam.getRecords(), pageParam.getTotal());
    }
}
