package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReport;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportApprovalNodeParam;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCrisisReportQueryParam;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 心理健康教育/心理危机上报控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-crisis-report")
public class XljkCrisisReportController extends BaseController {
    @Resource
    private XljkCrisisReportService xljkCrisisReportService;

    /**
     * 分页查询心理危机上报（权限标识：mentalHealth:xljkCrisisReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:list')")
    @GetMapping("/page")
    public PageResult<XljkCrisisReport> page(XljkCrisisReportParam param) {
        return xljkCrisisReportService.page(param);
    }

    /**
     * 分页查询心理危机上报审批列表（权限标识：mentalHealth:xljkCrisisReport:approvalList）
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:approvalList')")
    @GetMapping("/pageApprovalList")
    public PageResult<XljkCrisisReport> pageApprovalList(XljkCrisisReportQueryParam param) {
        return xljkCrisisReportService.pageApprovalList(param);
    }


    /**
     * 查询全部心理危机上报（权限标识：mentalHealth:xljkCrisisReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:list')")
    @GetMapping()
    public List<XljkCrisisReport> list(XljkCrisisReportParam param) {
        PageParam<XljkCrisisReport, XljkCrisisReportParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkCrisisReportService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理危机上报（权限标识：mentalHealth:xljkCrisisReport:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:list')")
    @GetMapping("/{id}")
    public XljkCrisisReport get(@PathVariable("id") String id) {
        return xljkCrisisReportService.getById(id);
    }

    /**
     * 添加或修改心理危机上报（权限标识：mentalHealth:xljkCrisisReport:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:operation')")
    @OperationLog(module = "心理危机上报", comments = "保存心理危机上报")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCrisisReport xljkCrisisReport) {
        xljkCrisisReportService.operation(xljkCrisisReport);
    }

    /**
     * 添加或修改心理危机上报审核（权限标识：mentalHealth:xljkCrisisReport:approve）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:approve')")
    @OperationLog(module = "心理危机上报", comments = "保存心理危机上报审核")
    @PostMapping("/approve")
    public void approve(@RequestBody XljkCrisisReportApprovalNodeParam param) {
        xljkCrisisReportService.approve(param);
    }

    /**
     * 批量删除心理危机上报（权限标识：mentalHealth:xljkCrisisReport:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReport:remove')")
    @OperationLog(module = "心理危机上报", comments = "批量删除心理危机上报")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            xljkCrisisReportService.removeCrisisReport(id);
        }
    }
}
