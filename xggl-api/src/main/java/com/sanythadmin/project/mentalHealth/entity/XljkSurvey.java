package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 心理健康测评问卷
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_SURVEY")
@Entity
@Table(name = "SYT_XLJK_SURVEY")
public class XljkSurvey implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 量表类别（日常测评/普查量表）
     */
    @Column(name = "LBLB", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("LBLB")
    private String lblb;

    /**
     * 普查类型
     */
    @Column(name = "PCLX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("PCLX")
    private String pclx;

    /**
     * 答题次数
     */
    @Column(name = "KDTCS")
    @TableField("KDTCS")
    private Integer kdtcs;

    /**
     * 关联问卷ID
     */
    @Column(name = "SID", columnDefinition = ColumnType.CHAR_32)
    @TableField("SID")
    private String sid;

    /**
     * 是/否允许查看测评结果
     */
    @Column(name = "SFYXCKCPJG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFYXCKCPJG")
    private JudgeMark sfyxckcpjg;

    /**
     * 题库题目分类ID
     */
    @Column(name = "GID", columnDefinition = ColumnType.CHAR_32)
    @TableField("GID")
    private String gid;

    /**
     * 测评题目分类
     */
    @Column(name = "CATEGORY", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CATEGORY")
    private String category;
}
