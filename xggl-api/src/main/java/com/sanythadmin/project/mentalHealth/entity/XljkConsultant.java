package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 咨询师信息
 *
 * <AUTHOR>
 * @since 2025-03-31 16:44:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_CONSULTANT")
@Entity
@Table(name = "SYT_XLJK_CONSULTANT")
public class XljkConsultant implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学工号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 咨询师类别（专职/校内兼职/外聘）
     */
    @Column(name = "ZXSLB", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZXSLB")
    private String zxslb;

    /**
     * 咨询师来源
     */
    @Column(name = "ZXSLY", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZXSLY")
    private String zxsly;

    /**
     * 擅长领域
     */
    @Column(name = "SCLY", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("SCLY")
    private String scly;

    /**
     * 从业经验
     */
    @Column(name = "CYJY", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("CYJY")
    private String cyjy;

    /**
     * 资质证书
     */
    @Column(name = "ZZZS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("ZZZS")
    private String zzzs;

    /**
     * 心灵寄语
     */
    @Column(name = "XLJY", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("XLJY")
    private String xljy;

    /**
     * 姓名
     */
    @Transient
    @TableField(exist = false)
    private String xm;

    @Transient
    @TableField(exist = false)
    private BaseUserInfo userInfo;

    public String getXm() {
        return userInfo != null ? userInfo.getXm() : null;
    }
}
