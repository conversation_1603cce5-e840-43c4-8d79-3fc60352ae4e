package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkInterpretationService;
import com.sanythadmin.project.mentalHealth.entity.XljkInterpretation;
import com.sanythadmin.project.mentalHealth.param.XljkInterpretationParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 心理健康教育/测评卷测评结果解释控制器
 *
 * <AUTHOR>
 * @since 2025-04-25 11:33:23
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-interpretation")
public class XljkInterpretationController extends BaseController {
    @Resource
    private XljkInterpretationService xljkInterpretationService;

    /**
     * 分页查询测评卷测评结果解释（权限标识：mentalHealth:xljkInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkInterpretation:list')")
    @GetMapping("/page")
    public PageResult<XljkInterpretation> page(XljkInterpretationParam param) {
        PageParam<XljkInterpretation, XljkInterpretationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkInterpretationService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部测评卷测评结果解释（权限标识：mentalHealth:xljkInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkInterpretation:list')")
    @GetMapping()
    public List<XljkInterpretation> list(XljkInterpretationParam param) {
        PageParam<XljkInterpretation, XljkInterpretationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkInterpretationService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询测评卷测评结果解释（权限标识：mentalHealth:xljkInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkInterpretation:list')")
    @GetMapping("/{id}")
    public XljkInterpretation get(@PathVariable("id") String id) {
        return xljkInterpretationService.getById(id);
    }

    /**
     * 添加或修改测评卷测评结果解释（权限标识：mentalHealth:xljkInterpretation:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkInterpretation:operation')")
    @OperationLog(module = "测评卷测评结果解释", comments = "保存测评卷测评结果解释")
    @PostMapping("/operation")
    public void save(@RequestBody XljkInterpretation xljkInterpretation) {
        xljkInterpretation.setUsername(SecurityUtil.getUsername());
        xljkInterpretation.setUpdateTime(LocalDateTime.now());
        if (StringUtils.hasLength(xljkInterpretation.getId())) {
            XljkInterpretation interpretation = xljkInterpretationService.getById(xljkInterpretation.getId());
            xljkInterpretation.setCreateTime(interpretation.getCreateTime());
            xljkInterpretationService.updateById(xljkInterpretation);
        } else {
            xljkInterpretation.setCreateTime(LocalDateTime.now());
            xljkInterpretationService.save(xljkInterpretation);
        }
    }

    /**
     * 批量删除测评卷测评结果解释（权限标识：mentalHealth:xljkInterpretation:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkInterpretation:remove')")
    @OperationLog(module = "测评卷测评结果解释", comments = "批量删除测评卷测评结果解释")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkInterpretationService.removeByIds(ids);
    }
}
