package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkCrisisReportNode;
import com.sanythadmin.project.mentalHealth.param.XljkCrisisReportNodeParam;
import com.sanythadmin.project.mentalHealth.service.XljkCrisisReportNodeService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 心理健康教育/心理危机上报审核流程控制器
 *
 * <AUTHOR>
 * @since 2025-05-07 17:39:16
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-crisis-report-node")
public class XljkCrisisReportNodeController extends BaseController {
    @Resource
    private XljkCrisisReportNodeService xljkCrisisReportNodeService;

    /**
     * 分页查询心理危机上报审核流程（权限标识：mentalHealth:xljkCrisisReportNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportNode:list')")
    @GetMapping("/page")
    public PageResult<XljkCrisisReportNode> page(XljkCrisisReportNodeParam param) {
        PageParam<XljkCrisisReportNode, XljkCrisisReportNodeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkCrisisReportNodeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理危机上报审核流程（权限标识：mentalHealth:xljkCrisisReportNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportNode:list')")
    @GetMapping()
    public List<XljkCrisisReportNode> list(XljkCrisisReportNodeParam param) {
        return xljkCrisisReportNodeService.list(param);
    }

    /**
     * 根据id查询心理危机上报审核流程（权限标识：mentalHealth:xljkCrisisReportNode:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportNode:list')")
    @GetMapping("/{id}")
    public XljkCrisisReportNode get(@PathVariable("id") String id) {
        return xljkCrisisReportNodeService.getById(id);
    }

    /**
     * 添加或修改心理危机上报审核流程（权限标识：mentalHealth:node:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportNode:operation')")
    @OperationLog(module = "心理危机上报审核流程", comments = "保存心理危机上报审核流程")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCrisisReportNode node) {
        XljkCrisisReportNodeParam param = new XljkCrisisReportNodeParam();
        param.setRoleName(node.getRoleName());
        List<XljkCrisisReportNode> list = xljkCrisisReportNodeService.list(param);
        if (node.getSortNumber() == null) {
            SortHelper<XljkCrisisReportNode> sortHelper = new SortHelper<>(xljkCrisisReportNodeService, "sort_number");
            node.setSortNumber(sortHelper.next());
        }
        if (StringUtils.hasLength(node.getId())) {
            if (!CollectionUtils.isEmpty(list) && !node.getId().equals(list.get(0).getId()))
                AssertUtil.throwMessage("审核角色已存在");
            xljkCrisisReportNodeService.updateById(node);
        } else {
            if (!CollectionUtils.isEmpty(list))
                AssertUtil.throwMessage("审核角色已存在");
            xljkCrisisReportNodeService.save(node);
        }
    }

    /**
     * 批量删除心理危机上报审核流程（权限标识：mentalHealth:xljkCrisisReportNode:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCrisisReportNode:remove')")
    @OperationLog(module = "心理危机上报审核流程", comments = "批量删除心理危机上报审核流程")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkCrisisReportNodeService.removeByIds(ids);
    }
}
