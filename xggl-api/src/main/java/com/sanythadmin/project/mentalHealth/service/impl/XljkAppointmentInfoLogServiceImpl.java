package com.sanythadmin.project.mentalHealth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoLogMapper;
import com.sanythadmin.project.mentalHealth.service.XljkAppointmentInfoLogService;
import com.sanythadmin.project.mentalHealth.entity.XljkAppointmentInfoLog;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 心理咨询预约记录操作日志Service实现
 *
 * <AUTHOR>
 * @since 2025-04-09 17:45:06
 */
@Service
public class XljkAppointmentInfoLogServiceImpl extends ServiceImpl<XljkAppointmentInfoLogMapper, XljkAppointmentInfoLog> implements XljkAppointmentInfoLogService {

    @Resource
    private XljkAppointmentInfoLogMapper mapper;

}
