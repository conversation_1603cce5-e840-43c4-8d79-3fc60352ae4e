<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkAppointmentInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, XM, XB, XYMC, ZYMC, LXDH, ZXWTFL, WTJS, SFJJQK, JJLXR, JJLXRLXFS, COUNSELOR_SCHEDULE_ID, SFTYYSZC, CREATE_TIME, STATUS, SHCZR, SHCZRZH, SHSJ, SHBZ, ZXMD, ZXLS, JTXXZZ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xb != null">
                        AND a.XB LIKE concat(concat('%',#{param.xb), '%')
                    </if>
                    <if test="param.xymc != null">
                        AND a.XYMC LIKE concat(concat('%',#{param.xymc), '%')
                    </if>
                    <if test="param.zymc != null">
                        AND a.ZYMC LIKE concat(concat('%',#{param.zymc), '%')
                    </if>
                    <if test="param.lxdh != null">
                        AND a.LXDH LIKE concat(concat('%',#{param.lxdh), '%')
                    </if>
                    <if test="param.zxwtfl != null">
                        AND a.ZXWTFL LIKE concat(concat('%',#{param.zxwtfl), '%')
                    </if>
                    <if test="param.wtjs != null">
                        AND a.WTJS LIKE concat(concat('%',#{param.wtjs), '%')
                    </if>
                    <if test="param.sfjjqk != null">
                        AND a.SFJJQK LIKE concat(concat('%',#{param.sfjjqk), '%')
                    </if>
                    <if test="param.jjlxr != null">
                        AND a.JJLXR LIKE concat(concat('%',#{param.jjlxr), '%')
                    </if>
                    <if test="param.jjlxrlxfs != null">
                        AND a.JJLXRLXFS LIKE concat(concat('%',#{param.jjlxrlxfs), '%')
                    </if>
                    <if test="param.counselorScheduleId != null">
                        AND a.COUNSELOR_SCHEDULE_ID LIKE concat(concat('%',#{param.counselorScheduleId), '%')
                    </if>
                    <if test="param.sftyyszc != null">
                        AND a.SFTYYSZC LIKE concat(concat('%',#{param.sftyyszc), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.status != null">
                        AND a.STATUS LIKE concat(concat('%',#{param.status), '%')
                    </if>
                    <if test="param.shczr != null">
                        AND a.SHCZR LIKE concat(concat('%',#{param.shczr), '%')
                    </if>
                    <if test="param.shczrzh != null">
                        AND a.SHCZRZH LIKE concat(concat('%',#{param.shczrzh), '%')
                    </if>
                    <if test="param.shsj != null">
                        AND a.SHSJ LIKE concat(concat('%',#{param.shsj), '%')
                    </if>
                    <if test="param.shbz != null">
                        AND a.SHBZ LIKE concat(concat('%',#{param.shbz), '%')
                    </if>
                    <if test="param.zxmd != null">
                        AND a.ZXMD LIKE concat(concat('%',#{param.zxmd), '%')
                    </if>
                    <if test="param.zxls != null">
                        AND a.ZXLS LIKE concat(concat('%',#{param.zxls), '%')
                    </if>
                    <if test="param.jtxxzz != null">
                        AND a.JTXXZZ LIKE concat(concat('%',#{param.jtxxzz), '%')
                    </if>
    </sql>
</mapper>
