package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkSurveyInterpretationService;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyInterpretation;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyInterpretationParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 测评卷测评结果解释控制器
 *
 * <AUTHOR>
 * @since 2025-04-25 10:55:06
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-survey-interpretation")
public class XljkSurveyInterpretationController extends BaseController {
    @Resource
    private XljkSurveyInterpretationService xljkSurveyInterpretationService;

    /**
     * 分页查询测评卷测评结果解释（权限标识：mentalHealth:xljkSurveyInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyInterpretation:list')")
    @GetMapping("/page")
    public PageResult<XljkSurveyInterpretation> page(XljkSurveyInterpretationParam param) {
        PageParam<XljkSurveyInterpretation, XljkSurveyInterpretationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkSurveyInterpretationService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部测评卷测评结果解释（权限标识：mentalHealth:xljkSurveyInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyInterpretation:list')")
    @GetMapping()
    public List<XljkSurveyInterpretation> list(XljkSurveyInterpretationParam param) {
        PageParam<XljkSurveyInterpretation, XljkSurveyInterpretationParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkSurveyInterpretationService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询测评卷测评结果解释（权限标识：mentalHealth:xljkSurveyInterpretation:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyInterpretation:list')")
    @GetMapping("/{id}")
    public XljkSurveyInterpretation get(@PathVariable("id") String id) {
        return xljkSurveyInterpretationService.getById(id);
    }

    /**
     * 添加或修改测评卷测评结果解释（权限标识：mentalHealth:xljkSurveyInterpretation:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyInterpretation:operation')")
    @OperationLog(module = "测评卷测评结果解释", comments = "保存测评卷测评结果解释")
    @PostMapping("/operation")
    public void save(@RequestBody XljkSurveyInterpretation xljkSurveyInterpretation) {
        if (StringUtils.hasLength(xljkSurveyInterpretation.getId())) {
            xljkSurveyInterpretationService.updateById(xljkSurveyInterpretation);
        } else {
            xljkSurveyInterpretationService.save(xljkSurveyInterpretation);
        }
    }

    /**
     * 批量删除测评卷测评结果解释（权限标识：mentalHealth:xljkSurveyInterpretation:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkSurveyInterpretation:remove')")
    @OperationLog(module = "测评卷测评结果解释", comments = "批量删除测评卷测评结果解释")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkSurveyInterpretationService.removeByIds(ids);
    }
}
