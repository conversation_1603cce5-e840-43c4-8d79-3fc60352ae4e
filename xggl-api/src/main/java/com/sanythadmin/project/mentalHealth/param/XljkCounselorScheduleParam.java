package com.sanythadmin.project.mentalHealth.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 心理健康咨询师排班信息查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCounselorScheduleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 星期名称
     */
    private String xqmc;

    /**
     * 工作日期
     */
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @QueryField(type = QueryType.EQ)
    private Date gzrq;


    /**
     * 查询条件（大于等于开始时间小于等于结束时间）
     */
    @QueryField(ignore = true)
    @DateTimeFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT_HOURS_MINUTES)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT_HOURS_MINUTES)
    private Date time;

    /**
     * 工作日期查询
     */
    @QueryField(ignore = true)
    @DateTimeFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date startDate;
    @QueryField(ignore = true)
    @DateTimeFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    private Date endDate;

    /**
     * 大于等于预约开始时间查询
     */
    @QueryField(ignore = true)
    private Date geStartdate;


    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "gzrq desc";
    }
}
