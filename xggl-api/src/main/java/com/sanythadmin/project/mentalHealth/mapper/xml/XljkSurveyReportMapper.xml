<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkSurveyReportMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ITEM_NAME, SCORE, ANSWER_INFO_ID, TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.itemName != null">
                        AND a.ITEM_NAME LIKE concat(concat('%',#{param.itemName), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE = #{param.score}
                    </if>
                    <if test="param.answerInfoId != null">
                        AND a.ANSWER_INFO_ID LIKE concat(concat('%',#{param.answerInfoId), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
    </sql>
</mapper>
