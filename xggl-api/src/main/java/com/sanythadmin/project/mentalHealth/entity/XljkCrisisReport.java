package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 心理危机上报
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_CRISIS_REPORT")
@Entity
@Table(name = "SYT_XLJK_CRISIS_REPORT")
public class XljkCrisisReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XB")
    private Gender xb;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CSRQ")
    private String csrq;

    /**
     * 培养层次名称
     */
    @Column(name = "PYCCMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("PYCCMC")
    private String pyccmc;

    /**
     * 学院名称
     */
    @Column(name = "XYMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XYMC")
    private String xymc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("NJMC")
    private String njmc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZYMC")
    private String zymc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("BJMC")
    private String bjmc;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 联系电话
     */
    @Column(name = "LXDH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("LXDH")
    private String lxdh;

    /**
     * 危机情况描述（危机类型（如抑郁、自残、焦虑、暴力倾向、自杀倾向等））
     */
    @Column(name = "WJLX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("WJLX")
    private String wjlx;

    /**
     * 危机情况描述（危机事件/问题描述）
     */
    @Column(name = "WJSJMS", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("WJSJMS")
    private String wjsjms;

    /**
     * 危机情况描述（危机发生时间）
     */
    @Column(name = "WJFSSJ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("WJFSSJ")
    private String wjfssj;

    /**
     * 危机情况描述（危机发生地点）
     */
    @Column(name = "WJFSDD", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("WJFSDD")
    private String wjfsdd;

    /**
     * 危机情况描述（涉及人员（仅当事人/家庭成员/同事/其他））
     */
    @Column(name = "SJRY", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SJRY")
    private String sjry;

    /**
     * 危机评估（是否涉及生命危险（是/否/不确定））
     */
    @Column(name = "SFSJSMWX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SFSJSMWX")
    private String sfsjsmwx;

    /**
     * 危机评估（是否存在自残/自杀计划（是/否/不确定））
     */
    @Column(name = "SFCZZSZCJH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SFCZZSZCJH")
    private String sfczzszcjh;

    /**
     * 危机评估（是否存在自残/自杀尝试（是/否））
     */
    @Column(name = "SFCZZSZCCS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFCZZSZCCS")
    private JudgeMark sfczzszccs;

    /**
     * 危机评估（是否需要立即干预（是/否））
     */
    @Column(name = "SFXYLJGY", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXYLJGY")
    private JudgeMark sfxyljgy;

    /**
     * 上报人信息（上报人姓名（可匿名））
     */
//    @Column(name = "SBRXM", columnDefinition = ColumnType.VARCHAR2_50)
//    @TableField("SBRXM")
//    private String sbrxm;

    /**
     * 上报人信息（与当事人关系（同学/室友/老师/辅导员/亲属/其他））
     */
//    @Column(name = "YDSRGX", columnDefinition = ColumnType.VARCHAR2_50)
//    @TableField("YDSRGX")
//    private String ydsrgx;

    /**
     * 上报人信息（联系方式）
     */
//    @Column(name = "LXFS", columnDefinition = ColumnType.VARCHAR2_50)
//    @TableField("LXFS")
//    private String lxfs;
    /**
     * 创建用户
     */
    @Column(name = "USERNAME", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("USERNAME")
    private String username;

    /**
     * 创建用户真实姓名
     */
    @Column(name = "REAL_NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("REAL_NAME")
    private String realName;

    /**
     * 处理建议（是否已联系学校心理中心））
     */
    @Column(name = "SFYLXXXXLZX", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFYLXXXXLZX")
    private JudgeMark sfylxxxxlzx;

    /**
     * 处理建议（是否建议其寻求专业帮助（是/否））
     */
    @Column(name = "SFJYQXQZYBZ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFJYQXQZYBZ")
    private JudgeMark sfjyqxqzybz;

    /**
     * 处理建议（是/否转介）
     */
    @Column(name = "SFZJ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFZJ")
    private JudgeMark sfzj;

    /**
     * 危机评估（是否影响他人安全（是/否））
     */
    @Column(name = "SFYXTRAQ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFYXTRAQ")
    private JudgeMark sfyxtraq;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 审核状态
     */
    @Column(name = "status")
    @TableField("status")
    private String status;
}
