<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkSurveyAnswerInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XSID, SID, USED_TIME, SCORE, CREATE_TIME, XGH
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xsid != null">
                        AND a.XSID LIKE concat(concat('%',#{param.xsid), '%')
                    </if>
                    <if test="param.sid != null">
                        AND a.SID LIKE concat(concat('%',#{param.sid), '%')
                    </if>
                    <if test="param.usedTime != null">
                        AND a.USED_TIME LIKE concat(concat('%',#{param.usedTime), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE LIKE concat(concat('%',#{param.score), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
    </sql>
</mapper>
