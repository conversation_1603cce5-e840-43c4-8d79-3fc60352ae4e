package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.mentalHealth.service.XljkParamService;
import com.sanythadmin.project.mentalHealth.entity.XljkParam;
import com.sanythadmin.project.mentalHealth.param.XljkParamParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 心理健康教育/心理健康咨询预约参数设置控制器
 *
 * <AUTHOR>
 * @since 2025-04-07 11:43:14
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-param")
public class XljkParamController extends BaseController {
    @Resource
    private XljkParamService xljkParamService;

    /**
     * 分页查询心理健康咨询预约参数设置（权限标识：mentalHealth:xljkParam:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkParam:list')")
    @GetMapping("/page")
    public PageResult<XljkParam> page(XljkParamParam param) {
        PageParam<XljkParam, XljkParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = xljkParamService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部心理健康咨询预约参数设置（权限标识：mentalHealth:xljkParam:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkParam:list')")
    @GetMapping()
    public List<XljkParam> list(XljkParamParam param) {
        PageParam<XljkParam, XljkParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return xljkParamService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询心理健康咨询预约参数设置（权限标识：mentalHealth:xljkParam:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkParam:list')")
    @GetMapping("/{id}")
    public XljkParam get(@PathVariable("id") String id) {
        return xljkParamService.getById(id);
    }

    /**
     * 添加或修改心理健康咨询预约参数设置（权限标识：mentalHealth:xljkParam:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkParam:operation')")
    @OperationLog(module = "心理健康咨询预约参数设置", comments = "保存心理健康咨询预约参数设置")
    @PostMapping("/operation")
    public void save(@RequestBody XljkParam xljkParam) {
        if (StringUtils.hasLength(xljkParam.getId())) {
            xljkParamService.updateById(xljkParam);
        } else {
            xljkParamService.save(xljkParam);
        }
    }

    /**
     * 批量删除心理健康咨询预约参数设置（权限标识：mentalHealth:xljkParam:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkParam:remove')")
    @OperationLog(module = "心理健康咨询预约参数设置", comments = "批量删除心理健康咨询预约参数设置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkParamService.removeByIds(ids);
    }


    /**
     * 心理咨询预约知情同意书
     *
     * @return
     */
    @GetMapping("/informedConsent")
    public String informedConsent() {
        List<XljkParam> list = xljkParamService.list();
        XljkParam xljkParam = CommonUtil.listGetOne(list);
        return xljkParam == null ? null : xljkParam.getZqtys();
    }
}
