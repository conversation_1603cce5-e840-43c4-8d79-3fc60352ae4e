package com.sanythadmin.project.mentalHealth.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理危机档案信息库查询参数
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XljkCrisisRepositoryParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Integer xb;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 辅导员
     */
    private String fdy;

    /**
     * 辅导员联系方式
     */
    private String fdylxfs;

    /**
     * 宿舍信息
     */
    private String ssxx;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 操作人
     */
    private String czr;

    /**
     * 操作人账号
     */
    private String czrzh;

    /**
     * 培养层次名称
     */
    private String pyccmc;

    /**
     * 培养层次ID
     */
    private String pyccid;

    /**
     * 学院ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    private String zyid;

    /**
     * 班级ID
     */
    private String bjid;

    /**
     * 年级ID
     */
    private String njid;

    /**
     * 关联危机上报ID
     */
    private String crisisReportId;

    /**
     * 危机详情（危机类型（如抑郁、自残、焦虑、暴力倾向、自杀倾向等））
     */
    private String wjlx;

    /**
     * 危机详情（危机情况描述（危机事件/问题描述）)
     */
    private String wjsjms;

    /**
     * 危机详情（危机评估等级)
     */
    private String wjpgdj;

    /**
     * 危机详情（过往是否存在心理咨询或治疗情况）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfczgwxlzxzlqk;

    /**
     * 危机详情（是否有既往心理危机记录）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfyjwxlwjjl;

    /**
     * 处理过程（干预措施（心理咨询/家长沟通/医学评估/危机转介等））
     */
    private String gycs;

    /**
     * 处理过程（是否安排心理咨询（是/否，若是，则记录咨询师信息））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfapxlzx;

    /**
     * 处理过程（是否与家长沟通（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfyjzgt;

    /**
     * 处理过程（是否需进一步关注（是/否））
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxjybgz;

    /**
     * 处理过程（后续回访计划（定期访谈/心理健康测评等））
     */
    private String hxhfjh;

}
