package com.sanythadmin.project.mentalHealth.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.FormDataWrapper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.*;
import com.sanythadmin.common.system.mapper.SysAccountMapper;
import com.sanythadmin.common.system.mapper.SysAccountRoleMapper;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.mentalHealth.entity.XljkConsultant;
import com.sanythadmin.project.mentalHealth.mapper.XljkConsultantMapper;
import com.sanythadmin.project.mentalHealth.param.XljkConsultantParam;
import com.sanythadmin.project.mentalHealth.service.XljkConsultantService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 咨询师信息Service实现
 *
 * <AUTHOR>
 * @since 2025-03-31 16:44:47
 */
@Service
public class XljkConsultantServiceImpl extends ServiceImpl<XljkConsultantMapper, XljkConsultant> implements XljkConsultantService {
    @Resource
    private XljkConsultantMapper consultantMapper;
    @Resource
    private SysAccountMapper accountMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysAccountRoleMapper accountRoleMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private GridFsService fsService;


    @Transactional
    @Override
    public void saveConsultant(MultipartHttpServletRequest request) {
        FormDataWrapper<XljkConsultant> wrapper = new FormDataWrapper<>(request, XljkConsultant.class);
//        Map<String, Object> parsedData = CommonUtil.parseFormDataRequest(request);
        XljkConsultant consultant = wrapper.getObject();//CommonUtil.toBean(parsedData, XljkConsultant.class);
        BaseUserInfo baseUserInfo = consultant.getUserInfo();
        baseUserInfo.setPhoto(null);
        UserInfo userInfo = null;
        if (StringUtils.hasText(consultant.getId())) {
            XljkConsultant xljkConsultant = consultantMapper.selectById(consultant.getId());
            consultant.setZzzs(xljkConsultant.getZzzs());
            consultantMapper.updateById(consultant);
            userInfo = userInfoService.get(xljkConsultant.getXgh());
            if (Objects.equals(UserType.CONSULTANT, userInfo.getUserType())) {
                BeanUtils.copyProperties(baseUserInfo, userInfo);
                userInfo.setXgh(xljkConsultant.getXgh());
                userInfo.setUserType(UserType.CONSULTANT);
                userInfoService.updateById(userInfo);
            }
            addRole(baseUserInfo);
        } else {
            userInfo = userInfoService.get(baseUserInfo.getXgh());
            if (userInfo == null) {
                userInfo = new UserInfo();
                BeanUtils.copyProperties(baseUserInfo, userInfo);
                userInfo.setUserType(UserType.CONSULTANT);
                userInfoService.saveUserInfo(userInfo);
            } else {
                userInfo.setRoleId(baseUserInfo.getRoleId());
                addRole(userInfo);
            }

            consultant.setZzzs(StringUtils.hasText(consultant.getZzzs()) ?
                    (JSON.isValidArray(consultant.getZzzs()) ? consultant.getZzzs() : null) : null);
            consultant.setXgh(baseUserInfo.getXgh());
            XljkConsultant selectedOne = consultantMapper.selectOne(new LambdaQueryWrapper<XljkConsultant>()
                    .eq(XljkConsultant::getXgh, baseUserInfo.getXgh()));
            if (selectedOne == null) {
                consultantMapper.insert(consultant);
            } else {
                consultant.setId(selectedOne.getId());
                consultantMapper.updateById(consultant);
            }
        }


        HashMap<String, List<FileInfo>> fileMap = CommonUtil.multipartFileSave(consultant.getXgh(), consultant, wrapper.getFileFields(), wrapper.getDeleteFileIds());
        if (!CollectionUtils.isEmpty(fileMap)) {
            String regex = "\\w+(\\.|\\[)\\w+(\\]|\\.)";
            List<String> consultantField = fileMap.keySet().stream().filter(key -> !key.matches(regex)).toList();
            List<String> userInfoField = fileMap.keySet().stream().filter(key -> key.matches(regex)).toList();
            if (!CollectionUtils.isEmpty(consultantField)) {
                UpdateWrapper<XljkConsultant> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(XljkConsultant::getId, consultant.getId());
                consultantField.forEach(key -> updateWrapper.set(key, JSON.toJSONString(fileMap.get(key))));
                consultantMapper.update(null, updateWrapper);
            }

            if (!CollectionUtils.isEmpty(userInfoField)) {
                String regex1 = "\\[(.*?)\\]";
                UpdateWrapper<UserInfo> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(UserInfo::getXgh, consultant.getXgh());
                int i = 0;
                List<String> oldFiles = new ArrayList<>();
                for (String key : userInfoField) {
                    Matcher matcher = Pattern.compile(regex1).matcher(key);
                    if (matcher.find()) {
                        String group = matcher.group(1);
                        updateWrapper.set(group, JSON.toJSONString(fileMap.get(key)));
                        Object value = CommonUtil.getValue(group, userInfo);
                        if (value != null)
                            oldFiles.add(String.valueOf(value));
                        i++;
                    }
                }
                userInfoService.update(null, updateWrapper);
                oldFiles.forEach(file -> {
                    fsService.removeByFileInfoStr(file);
                });
            }
        }
    }

    private void addRole(BaseUserInfo userInfo) {
        List<SysRole> roles = sysRoleMapper.selectList(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_ZXS));
        accountRoleMapper.delete(new LambdaQueryWrapper<SysAccountRole>()
                .eq(SysAccountRole::getUsername, userInfo.getXgh())
                .in(SysAccountRole::getRoleId, roles.stream().map(SysRole::getId).toList()));
        SysAccount account = accountMapper.selectOne(new LambdaQueryWrapper<SysAccount>()
                .eq(SysAccount::getUsername, userInfo.getXgh()));
        String[] roleArray = CommonUtil.split(userInfo.getRoleId());
        for (String id : roleArray) {
            SysAccountRole accountRole = new SysAccountRole();
            accountRole.setUsername(userInfo.getXgh());
            accountRole.setRoleId(id);
            accountRole.setAccountId(account.getId());
            accountRoleMapper.insert(accountRole);
        }
    }

    @Override
    public void removeConsultant(String id) {
        XljkConsultant consultant = consultantMapper.selectById(id);
        UserInfo userInfo = userInfoService.get(consultant.getXgh());
        if (userInfo.getUserType().equals(UserType.CONSULTANT))
            userInfoService.removeUserInfo(consultant.getXgh());
        consultantMapper.deleteById(id);
    }

    @Override
    public XljkConsultant get(String id) {
        XljkConsultantParam param = new XljkConsultantParam();
        param.setId(id);
        List<XljkConsultant> list = list(param);
        return CommonUtil.listGetOne(list);
    }

    @Override
    public PageResult<XljkConsultant> page(XljkConsultantParam param) {
        List<SysRole> roles = sysRoleMapper.selectList(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_ZXS));
        MyMPJLambdaWrapper<XljkConsultant, XljkConsultantParam> wrapper = UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param,
                XljkConsultant::getXgh, null);
        Class<UserInfo> userInfoClass = UserInfo.class;
        SFunction[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(BaseUserInfo.class), false);
        wrapper.selectAll(XljkConsultant.class).select(sFunctions);
        wrapper.selectAssociation(userInfoClass, XljkConsultant::getUserInfo);
        Page<XljkConsultant> page = wrapper.getPage();
        page = consultantMapper.selectJoinPage(page, XljkConsultant.class, wrapper);
        List<XljkConsultant> records = page.getRecords();
        extracted(records, roles);
        return new PageResult<>(records, page.getTotal());
    }

    private static void extracted(List<XljkConsultant> records, List<SysRole> roles) {
        UserInfoUtil.codeTextSet(records, true);
        for (XljkConsultant record : records) {
            BaseUserInfo userInfo = record.getUserInfo();
            String roleId = userInfo.getRoleId();
            List<String> list = new ArrayList<>(Arrays.asList(CommonUtil.split(roleId)));
            list.removeIf(id -> !roles.stream().map(SysRole::getId).toList().contains(id));
            userInfo.setRoleId(CommonUtil.strJoin(list));
        }
    }

    @Override
    public List<XljkConsultant> list(XljkConsultantParam param) {
        List<SysRole> roles = sysRoleMapper.selectList(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_ZXS));
        MyMPJLambdaWrapper<XljkConsultant, XljkConsultantParam> wrapper = UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param,
                XljkConsultant::getXgh, null);
        Class<UserInfo> userInfoClass = UserInfo.class;
        SFunction[] sFunctions = CommonUtil.buildSFunctions(userInfoClass, CommonUtil.getAllFields(BaseUserInfo.class), false);
        wrapper.selectAll(XljkConsultant.class).select(sFunctions);
        wrapper.selectAssociation(userInfoClass, XljkConsultant::getUserInfo);
        List<XljkConsultant> records = consultantMapper.selectJoinList(XljkConsultant.class, wrapper);
        extracted(records, roles);
        return records;
    }
}
