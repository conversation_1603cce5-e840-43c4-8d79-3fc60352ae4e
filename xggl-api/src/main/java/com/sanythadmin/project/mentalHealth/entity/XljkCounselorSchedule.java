package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 心理健康咨询师排班信息
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@JsonIgnoreProperties({"kssj", "jssj"})
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_COUNSELOR_SCHEDULE")
@Entity
@Table(name = "SYT_XLJK_COUNSELOR_SCHEDULE")
public class XljkCounselorSchedule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 咨询师学工号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 咨询师姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 星期名称
     */
    @Column(name = "XQMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XQMC")
    private String xqmc;

    /**
     * 工作日期
     */
    @JsonFormat(pattern = DateUtil.DEFAULT_DATE_FORMAT)
    @JsonDeserialize(using = DateDeserializers.DateDeserializer.class)
    @Column(name = "GZRQ", columnDefinition = ColumnType.DATE)
    @TableField("GZRQ")
    private Date gzrq;

    /**
     * 开始时间
     */
    @Column(name = "KSSJ", columnDefinition = ColumnType.DATE)
    @TableField("KSSJ")
    private Date kssj;

    /**
     * 结束时间
     */
    @Column(name = "JSSJ", columnDefinition = ColumnType.DATE)
    @TableField("JSSJ")
    private Date jssj;

    /**
     * 可预约人数
     */
    @Column(name = "KYYRS")
    @TableField("KYYRS")
    private Integer kyyrs;

    /**
     * 工作地点
     */
    @Column(name = "GZDD", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("GZDD")
    private String gzdd;

    /**
     * 排班类型（固定周期/固定日期）
     */
    @Transient
    @TableField(exist = false)
    private String pblx;

    /**
     * 预约窗口期（周期性班次有效截止期）
     */
    @Transient
    @TableField(exist = false)
    private LocalDate yyckq;

    /**
     * 工作日期字符串
     */
    @Transient
    @TableField(exist = false)
    private String gzrqStr;

    /**
     * 咨询师
     */
    @Transient
    @TableField(exist = false)
    private String zxsxx;

    /**
     * 开始/结束时间
     */
    @Transient
    @TableField(exist = false)
    private String kssjStr;
    @Transient
    @TableField(exist = false)
    private String jssjStr;

    public Date getGzrq() {
        if (StringUtils.hasText(gzrqStr)) {
            String[] arrays = CommonUtil.split(gzrqStr);
            if (arrays.length == 1) return DateUtil.parse(arrays[0], DateUtil.DEFAULT_DATE_FORMAT);
        }
        return gzrq;
    }

    public String getKssjStr() {
        return kssj != null ? DateUtil.getDateTime(kssj, DateUtil.DEFAULT_HOURS_MINUTES_FORMAT) : kssjStr;
    }

    public String getJssjStr() {
        return jssj != null ? DateUtil.getDateTime(jssj, DateUtil.DEFAULT_HOURS_MINUTES_FORMAT) : jssjStr;
    }
}
