<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCounselorScheduleMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, XQMC, GZRQ, KSSJ, JSSJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xqmc != null">
                        AND a.XQMC LIKE concat(concat('%',#{param.xqmc), '%')
                    </if>
                    <if test="param.gzrq != null">
                        AND a.GZRQ LIKE concat(concat('%',#{param.gzrq), '%')
                    </if>
                    <if test="param.kssj != null">
                        AND a.KSSJ LIKE concat(concat('%',#{param.kssj), '%')
                    </if>
                    <if test="param.jssj != null">
                        AND a.JSSJ LIKE concat(concat('%',#{param.jssj), '%')
                    </if>
    </sql>
</mapper>
