package com.sanythadmin.project.mentalHealth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkSurveyAnswerInfo;
import com.sanythadmin.project.mentalHealth.param.XljkSurveyAnswerInfoParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkSurveyReportDTO;

/**
 * 测评量表答卷信息Service
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
public interface XljkSurveyAnswerInfoService extends IService<XljkSurveyAnswerInfo> {
    public PageResult<XljkSurveyAnswerInfo> page(XljkSurveyAnswerInfoParam param);

    public void answerOperation(XljkSurveyAnswerInfo xljkSurveyAnswerInfo);

    public Long getAnswerNum(XljkSurveyAnswerInfoParam param);
    public XljkSurveyReportDTO answerInfoReport(String answerInfoId);
}
