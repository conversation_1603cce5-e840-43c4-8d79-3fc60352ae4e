package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.survey.entity.BaseSurveyAnswer;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 心理测评卷答案
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_SURVEY_ANSWER")
@Entity
@Table(name = "SYT_XLJK_SURVEY_ANSWER")
public class XljkSurveyAnswer extends BaseSurveyAnswer implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 心理和问卷关系ID
     */
    @Column(name = "XSID", columnDefinition = ColumnType.CHAR_32)
    @TableField("XSID")
    private String xsid;
}
