<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.mentalHealth.mapper.XljkCrisisReportNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ROLE_NAME, SORT_NUMBER
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.roleName != null">
                        AND a.ROLE_NAME LIKE concat(concat('%',#{param.roleName), '%')
                    </if>
                    <if test="param.sortNumber != null">
                        AND a.SORT_NUMBER = #{param.sortNumber}
                    </if>
    </sql>
</mapper>
