package com.sanythadmin.project.mentalHealth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.sanythadmin.common.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 心理回访
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_XLJK_FOLLOWUP_RECORD")
@Entity
@Table(name = "SYT_XLJK_FOLLOWUP_RECORD")
public class XljkFollowupRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField("XB")
    private Gender xb;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ")
    @TableField("CSRQ")
    private String csrq;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC")
    @TableField("NJMC")
    private String njmc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC")
    @TableField("BJMC")
    private String bjmc;

    /**
     * 联系电话
     */
    @Column(name = "LXDH")
    @TableField("LXDH")
    private String lxdh;

    /**
     * 培养层次名称
     */
    @Column(name = "PYCCMC")
    @TableField("PYCCMC")
    private String pyccmc;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @Column(name = "CZR", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("CZR")
    private String czr;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CZRZH")
    private String czrzh;

    /**
     * 回访信息（回访时间）
     */
    @Column(name = "HFSJ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("HFSJ")
    private String hfsj;

    /**
     * 回访信息（回访方式（电话/面对面/线上交流/其他））
     */
    @Column(name = "HFFS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("HFFS")
    private String hffs;

    /**
     * 回访信息（回访人（心理老师/辅导员/社工/其他））
     */
    @Column(name = "HFR", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("HFR")
    private String hfr;

    /**
     * 回访信息（与当事人沟通情况（愿意沟通/回避/情绪稳定/情绪波动））
     */
    @Column(name = "YDSRGTQK", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("YDSRGTQK")
    private String ydsrgtqk;

}
