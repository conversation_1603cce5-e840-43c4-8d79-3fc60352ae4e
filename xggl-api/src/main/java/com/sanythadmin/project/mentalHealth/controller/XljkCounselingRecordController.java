package com.sanythadmin.project.mentalHealth.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.mentalHealth.entity.XljkCounselingRecord;
import com.sanythadmin.project.mentalHealth.param.XljkCounselingRecordParam;
import com.sanythadmin.project.mentalHealth.pojo.XljkCounselingRecordQueryParam;
import com.sanythadmin.project.mentalHealth.service.XljkCounselingRecordService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 心理健康教育/心理咨询访谈记录控制器
 *
 * <AUTHOR>
 * @since 2025-03-31 09:15:56
 */
@RestController
@RequestMapping("/api/mentalHealth/xljk-counseling-record")
public class XljkCounselingRecordController extends BaseController {
    @Resource
    private XljkCounselingRecordService xljkCounselingRecordService;

    /**
     * 分页查询心理咨询记录（权限标识：mentalHealth:xljkCounselingRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselingRecord:list')")
    @GetMapping("/page")
    public PageResult<XljkCounselingRecord> page(XljkCounselingRecordParam param) {
        return xljkCounselingRecordService.page(param);
    }

    /**
     * 查询全部心理咨询记录（权限标识：mentalHealth:xljkCounselingRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselingRecord:list')")
    @GetMapping()
    public List<XljkCounselingRecord> list(XljkCounselingRecordQueryParam param) {
        return xljkCounselingRecordService.list(param);
    }

    /**
     * 根据id查询心理咨询记录（权限标识：mentalHealth:xljkCounselingRecord:list）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselingRecord:list')")
    @GetMapping("/{id}")
    public XljkCounselingRecord get(@PathVariable("id") String id) {
        return xljkCounselingRecordService.getById(id);
    }

    /**
     * 添加或修改心理咨询记录（权限标识：mentalHealth:xljkCounselingRecord:operation）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselingRecord:operation')")
    @OperationLog(module = "心理咨询记录", comments = "保存心理咨询记录")
    @PostMapping("/operation")
    public void save(@RequestBody XljkCounselingRecord xljkCounselingRecord) {
        xljkCounselingRecordService.operation(xljkCounselingRecord);
    }

    /**
     * 批量删除心理咨询记录（权限标识：mentalHealth:xljkCounselingRecord:remove）
     */
    @PreAuthorize("hasAuthority('mentalHealth:xljkCounselingRecord:remove')")
    @OperationLog(module = "心理咨询记录", comments = "批量删除心理咨询记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        xljkCounselingRecordService.removeByIds(ids);
    }
}
