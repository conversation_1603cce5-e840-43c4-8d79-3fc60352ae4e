package com.sanythadmin.project.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.param.WorkflowParam;

import java.util.List;

/**
 * 工作流节点Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowNodeService extends IService<WorkflowNode> {
    public List<WorkflowNode> nodeListByFormInfoAndApprover(String projectId, String year, String approverId);

    public List<WorkflowNode> nodeListByApprover(WorkflowParam param, String approverId);

    public List<WorkflowNode> listByWorkflow(String workflowId);

    /**
     * 根据工作流信息（工作流ID/项目ID,年份/业务模块,年份）查询所有审核节点
     *
     * @param param
     * @return
     */
    public List<WorkflowNode> getAllNode(WorkflowParam param);

    public void updateNodeApprovalTime(List<WorkflowNode> nodes);
}
