package com.sanythadmin.project.workflow.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单审批记录查询参数
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowApprovalNodeRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 工作流程ID
     */
    private String workflowId;

    /**
     * 审批节点ID
     */
    private String nodeId;

    /**
     * 审批时间
     */
    private String createTime;

    /**
     * 关联申请表记录ID
     */
    private String applicationId;

    /**
     * 审批结果
     */
    private String result;

    private String bz1;

    private String bz2;

    private String bz3;

    private String bz4;

    private String bz5;

    /**
     * 审批人角色ID
     */
    private String roleId;

    /**
     * 审批对象ID（username或roleId）
     */
    private String approverId;

    /**
     * 审批人姓名
     */
    private String realName;

    /**
     * 审批人username
     */
    private String username;

    /**
     * 退回方式
     */
    @QueryField(type = QueryType.EQ)
    private Integer returnWay;

    /**
     * 关联syt_workflow_node_approver表ID
     */
    private String nodeApproverId;

    @QueryField(type = QueryType.EQ)
    private Integer stage;

    @QueryField(type = QueryType.EQ)
    private Integer rebackWay;

}
