package com.sanythadmin.project.workflow.observer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.mapper.WorkflowEventMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeFormMapper;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2025/4/5.
 */
@Slf4j
@RequiredArgsConstructor
@Component("workflowNodeFormEventSubscription")
public class WorkflowNodeFormEventSubscription {

    private final WorkflowNodeFormMapper workflowNodeFormMapper;
    private final WorkflowEventMapper workflowEventMapper;

    public <T, U extends BaseApprovalNodeRecord> void notify(@NotNull ApprovalData<T, U> approvalData) {
        List<WorkflowNodeForm> nodeForms = workflowNodeFormMapper.selectList(new LambdaQueryWrapper<WorkflowNodeForm>()
                .eq(WorkflowNodeForm::getNodeId, approvalData.getRecord().getNodeId()));
        if (CollectionUtils.isEmpty(nodeForms)) return;
        List<WorkflowNodeForm> hasEvent = nodeForms.stream().filter(nodeForm -> StringUtils.hasText(nodeForm.getProcessEvent())).toList();
        List<WorkflowNodeForm> hasDefaultValExpression = nodeForms.stream().filter(nodeForm -> StringUtils.hasText(nodeForm.getDefaultVal())).toList();
        if (!CollectionUtils.isEmpty(hasEvent)) {
            Map<String, WorkflowEvent> eventMap = workflowEventMapper.selectList(new LambdaQueryWrapper<WorkflowEvent>()
                            .in(WorkflowEvent::getId, hasEvent.stream().map(WorkflowNodeForm::getId).toList()))
                    .stream().collect(Collectors.toMap(WorkflowEvent::getId, Function.identity()));
            for (WorkflowNodeForm form : hasEvent) {
                WorkflowEvent event = eventMap.get(form.getProcessEvent());
                WorkflowNodeFormEventObserver observer = SpringContextUtil.getBean(event.getEventClassName(), WorkflowNodeFormEventObserver.class);
                observer.process(approvalData, form);
            }
        }

        if (!CollectionUtils.isEmpty(hasDefaultValExpression)) {
            for (WorkflowNodeForm form : hasDefaultValExpression) {
                Object value = CommonUtil.getValue(form.getFieldEn(), approvalData.getRecord());
                if (value == null || !StringUtils.hasText(String.valueOf(value))) {
                    Object property = getValueByExpression(approvalData, form.getDefaultVal());
                    CommonUtil.setValue(approvalData.getRecord(), form.getFieldEn(), property);
                }
            }
        }
    }

    public static <T, U> Object getValueByExpression(ApprovalData<T, U> approvalData, String defaultValExpression) {
        String regex = "\\$\\{([^}]+)\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(defaultValExpression);
        if (matcher.find())
            try {
                return PropertyUtils.getProperty(approvalData, matcher.group(1));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        return defaultValExpression;
    }
}
