<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowApprovalNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, WORKFLOW_ID, NODE_ID, CREATE_TIME, UPDATE_TIME, APPLICATION_ID, NEXT_NODE_RESULT, PRE_NODE_RESULT, RESULT, SORT, END_NODE, START_NODE, STAGE, REVIEW_TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.workflowId != null">
                        AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
                    </if>
                    <if test="param.nodeId != null">
                        AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.updateTime != null">
                        AND a.UPDATE_TIME LIKE concat(concat('%',#{param.updateTime), '%')
                    </if>
                    <if test="param.applicationId != null">
                        AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
                    </if>
                    <if test="param.nextNodeResult != null">
                        AND a.NEXT_NODE_RESULT LIKE concat(concat('%',#{param.nextNodeResult), '%')
                    </if>
                    <if test="param.preNodeResult != null">
                        AND a.PRE_NODE_RESULT LIKE concat(concat('%',#{param.preNodeResult), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT = #{param.sort}
                    </if>
                    <if test="param.endNode != null">
                        AND a.END_NODE LIKE concat(concat('%',#{param.endNode), '%')
                    </if>
                    <if test="param.startNode != null">
                        AND a.START_NODE LIKE concat(concat('%',#{param.startNode), '%')
                    </if>
                    <if test="param.stage != null">
                        AND a.STAGE = #{param.stage}
                    </if>
                    <if test="param.reviewType != null">
                        AND a.REVIEW_TYPE = #{param.reviewType}
                    </if>
    </sql>
</mapper>
