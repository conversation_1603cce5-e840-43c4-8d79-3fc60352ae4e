package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作流节点流转条件
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_CONDITION")
@Entity
@Table(name = "SYT_WORKFLOW_CONDITION")
public class WorkflowCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 条件名称
     */
    @Column(name = "CONDITION_NAME")
    @TableField("CONDITION_NAME")
    private String conditionName;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 条件顺序
     */
    @Column(name = "SORT", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SORT")
    private Integer sort;

    @Column(name = "settype", columnDefinition = ColumnType.NUMBER_1)
    @TableField("settype")
    private Integer settype;
}
