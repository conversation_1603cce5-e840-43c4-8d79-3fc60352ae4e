<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowNodeStateMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,FIELD_ZH, FIELD_EN, STATE_PASS, STATE_TERMINATE, STATE_REJECT, WORKFLOW_ID, NODE_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.statePass != null">
            AND a.STATE_PASS LIKE concat(concat('%',#{param.statePass), '%')
        </if>
        <if test="param.stateTerminate != null">
            AND a.STATE_TERMINATE LIKE concat(concat('%',#{param.stateTerminate), '%')
        </if>
        <if test="param.stateReject != null">
            AND a.STATE_REJECT LIKE concat(concat('%',#{param.stateReject), '%')
        </if>
        <if test="param.workflowId != null">
            AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
        </if>
        <if test="param.nodeId != null">
            AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
        </if>
    </sql>
</mapper>
