package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workflow.enums.ReturnWay;
import com.sanythadmin.project.workflow.enums.ReviewStage;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by JIANGPING on 2024/11/27.
 */
@Data
@MappedSuperclass
public class BaseApprovalNodeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 工作流程ID
     */
    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 审批节点ID
     */
    @Column(name = "NODE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NODE_ID")
    private String nodeId;

    /**
     * 审批时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 关联申请表记录ID
     */
    @Column(name = "APPLICATION_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("APPLICATION_ID")
    private String applicationId;

    /**
     * 审批结果
     */
    @Column(name = "RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("RESULT")
    private String result;

    /**
     * 审批角色
     */
    @Column(name = "ROLE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ROLE_ID")
    private String roleId;

    /**
     * 审批人
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 审批人
     */
    @Column(name = "REAL_NAME")
    @TableField("REAL_NAME")
    private String realName;

    /**
     * 关联 WorkflowNodeApprover 实体 id
     */
    @Column(name = "NODE_APPROVER_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NODE_APPROVER_ID")
    private String nodeApproverId;

    /**
     * 关联 WorkflowNodeApprover 实体 approverId（审批人username或roleId）
     */
    @Column(name = "APPROVER_ID")
    @TableField("APPROVER_ID")
    private String approverId;

    /**
     * 退回方式（上级节点, 申请人）
     */
    @Column(name = "RETURN_WAY", columnDefinition = ColumnType.NUMBER_1)
    @TableField("RETURN_WAY")
    private ReturnWay returnWay;

    /**
     * 审批阶段(0: 初审, 1: 复审)
     */
    @Column(name = "STAGE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("STAGE")
    private ReviewStage stage;

    @Column(name = "BZ1")
    @TableField("BZ1")
    private String bz1;

    @Column(name = "BZ2")
    @TableField("BZ2")
    private String bz2;

    @Column(name = "BZ3")
    @TableField("BZ3")
    private String bz3;

    @Column(name = "BZ4")
    @TableField("BZ4")
    private String bz4;

    @Column(name = "BZ5")
    @TableField("BZ5")
    private String bz5;

    /**
     * 审核表单附件
     */
    @TableField(exist = false)
    @Transient
    private Map<String, List<MultipartFile>> fileMap;

    public static List<String> getExtendsField() {
        Field[] fields = CommonUtil.getAllFields(BaseApprovalNodeRecord.class);
        List<String> list = new ArrayList<>();
        for (Field field : fields) {
            if (field.getName().startsWith("bz"))
                list.add(field.getName());
        }
        return list;
    }
}
