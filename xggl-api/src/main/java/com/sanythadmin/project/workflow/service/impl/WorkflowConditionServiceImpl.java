package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.entity.WorkflowCondition;
import com.sanythadmin.project.workflow.mapper.WorkflowConditionMapper;
import com.sanythadmin.project.workflow.service.WorkflowConditionService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 节点条件关系Service实现
 *
 * <AUTHOR>
 * @since 2024-08-07 13:58:26
 */
@Service
public class WorkflowConditionServiceImpl extends ServiceImpl<WorkflowConditionMapper, WorkflowCondition> implements WorkflowConditionService {

    @Resource
    private WorkflowConditionMapper mapper;

}
