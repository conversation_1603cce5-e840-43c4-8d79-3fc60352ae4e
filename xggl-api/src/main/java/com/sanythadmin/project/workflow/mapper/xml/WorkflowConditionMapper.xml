<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowConditionMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NODE_ID, TARGET_NODE_ID, CONDITION, CONDITION_VALUE, CONTITION_ORIGIN
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.nodeId != null">
                        AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
                    </if>
                    <if test="param.targetNodeId != null">
                        AND a.TARGET_NODE_ID LIKE concat(concat('%',#{param.targetNodeId), '%')
                    </if>
                    <if test="param.condition != null">
                        AND a.CONDITION LIKE concat(concat('%',#{param.condition), '%')
                    </if>
                    <if test="param.conditionValue != null">
                        AND a.CONDITION_VALUE LIKE concat(concat('%',#{param.conditionValue), '%')
                    </if>
                    <if test="param.conditionOrigin != null">
                        AND a.CONDITION_ORIGIN = #{param.conditionOrigin}
                    </if>
    </sql>
</mapper>
