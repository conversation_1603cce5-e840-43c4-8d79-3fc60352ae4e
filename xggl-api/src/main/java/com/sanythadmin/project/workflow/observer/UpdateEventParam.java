package com.sanythadmin.project.workflow.observer;

import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by JIANGPING on 2024/12/25.
 */
@Data
@AllArgsConstructor
public class UpdateEventParam<T, U extends BaseApprovalNodeRecord> {
    private String setTable;
    private String setExpression;
    private String whereExpression;
    private UserInfo userInfo;
    private T applicationInfo;
    private U record;
}
