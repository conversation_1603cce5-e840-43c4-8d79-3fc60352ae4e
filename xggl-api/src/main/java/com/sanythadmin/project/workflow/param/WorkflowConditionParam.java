package com.sanythadmin.project.workflow.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批节点流转关系查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowConditionParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 分支节点ID
     */
    private String nodeId;

    /**
     * 流转目标节点ID
     */
    private String targetNodeId;

    /**
     * 流转条件（学籍表字段名）
     */
    private String condition;

    /**
     * 流转条件值
     */
    private String conditionValue;

    /**
     * 条件字段来源
     */
    @QueryField(type = QueryType.EQ)
    private Integer conditionOrigin;

}
