package com.sanythadmin.project.workflow.dto;

import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowCondition;
import com.sanythadmin.project.workflow.enums.NodeType;
import lombok.Data;

import java.util.List;

/**
 * Created by JIANGPING on 2024/7/23.
 */
@Data
public class ConditionDTO extends WorkflowCondition {
    private Integer priorityLevel;              // 前端回显
    private String nodeName;
    private NodeType type = NodeType.TiaoJian;  // 节点类型（2：条件节点）
    private NodeDTO childNode;
    private String tempKey;
    private List<WorkflowConditionDetail> conditionDetail;
}
