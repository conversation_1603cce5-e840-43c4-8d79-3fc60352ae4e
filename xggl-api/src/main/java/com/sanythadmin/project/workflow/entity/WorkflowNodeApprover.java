package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workflow.enums.ApproverType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 审批节点与审批对象关系
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_NODE_APPROVER")
@Entity
@Table(name = "SYT_WORKFLOW_NODE_APPROVER")
public class WorkflowNodeApprover implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 审批节点ID
     */
    @Column(name = "NODE_ID")
    @TableField("NODE_ID")
    private String nodeId;

    /**
     * 审批对象类型
     */
    @Column(name = "APPROVER_TYPE")
    @TableField("APPROVER_TYPE")
    private ApproverType approverType;

    /**
     * 审批对象字段
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 审批对象ID（关联用户或角色ID）
     */
    @Column(name = "APPROVER_ID")
    @TableField("APPROVER_ID")
    private String approverId;

    /**
     * 选择器数据（用于前端回显）
     */
    @Column(name = "SELECTOR_DATA", columnDefinition = ColumnType.VARCHAR2_1024)
    @TableField("SELECTOR_DATA")
    private String selectorData;

    /**
     * 节点审核表单
     */
    @Transient
    @TableField(exist = false)
    private List<WorkflowNodeForm> workflowNodeForm;
}
