package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeFormMapper;
import com.sanythadmin.project.workflow.observer.WorkflowNodeFormEventSubscription;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeFormService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工作流节点审批表单Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Service
@RequiredArgsConstructor
public class WorkflowNodeFormServiceImpl extends ServiceImpl<WorkflowNodeFormMapper, WorkflowNodeForm> implements WorkflowNodeFormService {
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final WorkflowNodeFormMapper nodeFormMapper;
    private final UserInfoMapper userInfoMapper;

    @Override
    public <T extends BaseApprovalNode> List<WorkflowNodeForm> getNodeFormByUser(Class<T> tClass, String applicationId, WorkflowParam param, String nodeId) {
        SqlSession sqlSession = null;
        try {
            String usernameAndRoleStr = CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId());
            List<WorkflowNodeApprover> list = nodeApproverMapper.nodeApproverList(param, usernameAndRoleStr);//.nodeListByFormInfoAndApprover(param.getProjectId(), param.getYear(), usernameAndRoleStr);
            if (CollectionUtils.isEmpty(list)) return null;
            if (StringUtils.hasText(nodeId)) {
                String finalNodeId1 = nodeId;
                list.removeIf(node -> !Objects.equals(node.getNodeId(), finalNodeId1));
            }

            String nodeApproverId = null;
            int size = list.size();
            if (size == 1) {
                nodeApproverId = list.get(0).getId();
            } else {
                Set<String> nodeIds = list.stream().map(WorkflowNodeApprover::getNodeId).collect(Collectors.toSet());
                SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
                sqlSession = sqlSessionFactory.openSession();
                BaseMapper<T> approvalNodeMapper = SqlHelper.getMapper(tClass, sqlSession);
                QueryWrapper<T> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("APPLICATION_ID", applicationId);
                queryWrapper.in("NODE_ID", nodeIds);
                queryWrapper.orderByAsc("SORT");
                List<T> approvalNodeList = approvalNodeMapper.selectList(queryWrapper);
                size = approvalNodeList == null ? 0 : approvalNodeList.size();
                if (size == 0) {
                    nodeId = nodeIds.stream().findFirst().orElse(null);
                } else if (size == 1) {
                    nodeId = approvalNodeList.get(0).getNodeId();
                } else {
                    nodeId = approvalNodeList.get(size - 1).getNodeId();
                    for (int i = size - 1; i >= 0; i--) {
                        T node = approvalNodeList.get(i);
                        if (Objects.equals(node.getResult(), ConstantsWorkflow.STATE_TERMINATE) ||
                                Objects.equals(node.getResult(), ConstantsWorkflow.STATE_REJECT) ||
                                Objects.equals(node.getResult(), ConstantsWorkflow.STATE_RETURN)) {
                            nodeId = node.getNodeId();
                            break;
                        } else if (Objects.equals(node.getResult(), ConstantsWorkflow.STATE_PENDING)) {
                            nodeId = node.getNodeId();
                            break;
                        }
                    }
                }
                String finalNodeId = nodeId;
                List<WorkflowNodeApprover> approvers = list.stream().filter(e -> e.getNodeId().equals(finalNodeId)).toList();
                nodeApproverId = approvers.get(0).getId();
            }

            if (StringUtils.hasText(nodeApproverId))
                return nodeFormMapper.selectList(new LambdaQueryWrapper<WorkflowNodeForm>()
                        .eq(WorkflowNodeForm::getNodeApproverId, nodeApproverId)
                        .eq(WorkflowNodeForm::getWorkflowId, list.get(0).getWorkflowId())
                        .orderByAsc(WorkflowNodeForm::getSort));
            return null;
        } finally {
            if (sqlSession != null)
                sqlSession.close();
        }
    }

    @Override
    public <T extends BaseApprovalNode, U extends BaseApplicationInfo> List<WorkflowNodeForm> getNodeFormByUser(Class<T> tClass, Class<U> uClass, String applicationId, WorkflowParam param, String nodeId) {
        List<WorkflowNodeForm> nodeForms = getNodeFormByUser(tClass, applicationId, param, nodeId);
        if (!CollectionUtils.isEmpty(nodeForms)) {
            List<WorkflowNodeForm> forms = nodeForms.stream().filter(nodeForm -> StringUtils.hasText(nodeForm.getDefaultVal())).toList();
            if (!CollectionUtils.isEmpty(forms)) {
                SqlSessionFactory sqlSessionFactory = super.getSqlSessionFactory();
                SqlSession sqlSession = sqlSessionFactory.openSession();
                BaseMapper<U> applicationInfoMapper = SqlHelper.getMapper(uClass, sqlSession);
                U u = applicationInfoMapper.selectById(applicationId);
                UserInfo userInfo = userInfoMapper.get(u.getXgh());
                ApprovalData<U, ?> approvalData = new ApprovalData<>(u, null, userInfo);
                for (WorkflowNodeForm form : forms) {
                    Object value = WorkflowNodeFormEventSubscription.getValueByExpression(approvalData, form.getDefaultVal());
                    CommonUtil.setValue(form, "defaultVal", value);
                }
            }
        }
        return nodeForms;
    }
}
