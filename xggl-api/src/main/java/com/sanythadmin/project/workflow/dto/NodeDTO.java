package com.sanythadmin.project.workflow.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanythadmin.project.workflow.entity.*;
import lombok.Data;

import java.util.List;

/**
 * Created by JIANGPING on 2024/7/23.
 */
@JsonIgnoreProperties({"tempKey"})
@Data
public class NodeDTO extends WorkflowNode {

    private List<WorkflowNodeApprover> workflowNodeApprovers;
    private WorkflowNodeState workflowNodeState;
    private List<ConditionDTO> conditionNodes;
    private NodeDTO childNode;
    private String tempKey;
}
