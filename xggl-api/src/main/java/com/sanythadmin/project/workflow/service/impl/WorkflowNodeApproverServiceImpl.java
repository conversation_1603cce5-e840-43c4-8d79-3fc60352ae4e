package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.service.WorkflowNodeApproverService;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 审批节点与审批对象关系Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Service
public class WorkflowNodeApproverServiceImpl extends ServiceImpl<WorkflowNodeApproverMapper, WorkflowNodeApprover> implements WorkflowNodeApproverService {

    @Resource
    private WorkflowNodeApproverMapper mapper;

    @Override
    public List<WorkflowNodeApprover> nodeListByFormInfoAndApprover(String projectId, String year, String approverId) {
        return mapper.nodeListByFormInfoAndApprover(projectId, year, approverId);
    }

    @Override
    public List<WorkflowNodeApprover> listByWorkflow(String workflowId) {
        return mapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>()
                .eq(WorkflowNodeApprover::getWorkflowId, workflowId));
    }
}
