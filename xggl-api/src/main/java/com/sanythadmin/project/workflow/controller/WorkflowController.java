package com.sanythadmin.project.workflow.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigDTO;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.form.entity.FormApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 工作流管理模块/工作流控制器
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@RestController
@RequestMapping("/api/workflow")
public class WorkflowController extends BaseController {
    @Resource
    private WorkflowService workflowService;
    @Resource
    private WorkflowNodeService workflowNodeService;

    /**
     * 分页查询工作流（权限标识：workflow:workflow:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflow:list')")
    @GetMapping("/page")
    public PageResult<Workflow> page(WorkflowParam param) {
        if (!StringUtils.hasText(param.getProjectId())) param.setProjectIdIsNull(true);
        PageParam<Workflow, WorkflowParam> page = new PageParam<>(param);
        QueryWrapper<Workflow> wrapper = page.getWrapper();
        page = workflowService.page(page, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部工作流（权限标识：workflow:workflow:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflow:list')")
    @GetMapping()
    public List<Workflow> list(WorkflowParam param) {
        if (!StringUtils.hasText(param.getProjectId())) param.setProjectIdIsNull(true);
        PageParam<Workflow, WorkflowParam> page = new PageParam<>(param);
        QueryWrapper<Workflow> orderWrapper = page.getOrderWrapper();
        //page.setDefaultOrder("create_time desc");
        return workflowService.list(orderWrapper);
    }

    /**
     * 根据id查询工作流（权限标识：workflow:workflow:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflow:list')")
    @GetMapping("/{id}")
    public WorkflowDTO get(@PathVariable("id") String id) {
        return workflowService.get(id);
    }

    /**
     * 根据项目ID和年份查询工作流
     */
    @GetMapping("/getByProject")
    public WorkflowDTO getByProject(@RequestParam String projectId, @RequestParam String year) {
        return workflowService.get(projectId, year);
    }

    /**
     * 添加或修改工作流（权限标识：workflow:workflow:operation）
     */
    @PreAuthorize("hasAuthority('workflow:workflow:operation')")
    @OperationLog(module = "工作流", comments = "保存工作流")
    @PostMapping("/operation")
    public void save(@RequestBody WorkflowDTO workflow) {
        workflowService.editWorkflow(workflow);
    }

    /**
     * 批量删除工作流（权限标识：workflow:workflow:remove）
     */
    @PreAuthorize("hasAuthority('workflow:workflow:remove')")
    @OperationLog(module = "工作流", comments = "批量删除工作流")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        workflowService.removeWorkflow(ids);
    }

    /**
     * 表单扩展字段
     *
     * @return
     */
    @GetMapping("/formExtendsField")
    public List<String> getExtendsField() {
        return FormApprovalNodeRecord.getExtendsField();
    }

    /**
     * 查询所有审核节点
     *
     * @param param
     * @return
     */
    @GetMapping("/getAllNode")
    public List<WorkflowNode> getAllNode(WorkflowParam param) {
        return workflowNodeService.getAllNode(param);
    }

    /**
     * 更新节点审核时间
     *
     * @param nodes
     */
    @PreAuthorize("hasAuthority('workflow:workflow:updateNodeApprovalTime')")
    @OperationLog(module = "工作流", comments = "更新节点审核时间")
    @PostMapping("/updateNodeApprovalTime")
    public void updateNodeApprovalTime(@RequestBody List<WorkflowNode> nodes) {
        workflowNodeService.updateNodeApprovalTime(nodes);
    }
}
