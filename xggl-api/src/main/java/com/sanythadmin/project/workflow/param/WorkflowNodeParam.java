package com.sanythadmin.project.workflow.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 工作流节点查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(ignore = true)
    private String id;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 关联流程ID
     */
    private String workflowId;

    /**
     * 审批开始时间
     */
    private String startTime;

    /**
     * 审批截至时间
     */
    private String endTime;

    /**
     * 是否条件流转节点（0：否，1: 是）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark flowNode;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

}
