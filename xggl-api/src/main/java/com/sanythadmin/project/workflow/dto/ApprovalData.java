package com.sanythadmin.project.workflow.dto;

import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import lombok.Data;

import java.util.List;

@Data
public class ApprovalData<T, U> {
    private T applicationInfo;
    private U record;
    private UserInfo userInfo;
    private List<WorkflowNodeApprover> nextNodeApprovers;

    public ApprovalData(T applicationInfo, U record, UserInfo userInfo) {
        this.applicationInfo = applicationInfo;
        this.record = record;
        this.userInfo = userInfo;
    }
}
