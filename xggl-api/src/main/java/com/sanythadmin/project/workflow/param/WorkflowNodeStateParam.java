package com.sanythadmin.project.workflow.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流节点状态查询参数
 *
 * <AUTHOR>
 * @since 2024-07-11 10:35:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowNodeStateParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 通过
     */
    private String statePass;

    /**
     * 不通过
     */
    private String stateTerminate;

    /**
     * 驳回
     */
    private String stateReject;

    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 工作流节点ID
     */
    private String nodeId;

}
