package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作流节点状态
 *
 * <AUTHOR>
 * @since 2024-07-11 10:35:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_NODE_STATE")
@Entity
@Table(name = "SYT_WORKFLOW_NODE_STATE")
public class WorkflowNodeState implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段中文名（默认：审核状态）
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 通过
     */
    @Column(name = "STATE_PASS")
    @TableField("STATE_PASS")
    private String statePass;

    /**
     * 通过触发事件
     */
    @Column(name = "STATE_PASS_EVENT_CLASS")
    @TableField("STATE_PASS_EVENT_CLASS")
    private String statePassEventClass;

    /**
     * 通过触发事件ID
     */
    @Column(name = "STATE_PASS_EVENT_ID")
    @TableField("STATE_PASS_EVENT_ID")
    private String statePassEventId;

    /**
     * 不通过
     */
    @Column(name = "STATE_TERMINATE")
    @TableField("STATE_TERMINATE")
    private String stateTerminate;

    /**
     * 不通过触发事件
     */
    @Column(name = "STATE_TERMINATE_EVENT_CLASS")
    @TableField("STATE_TERMINATE_EVENT_CLASS")
    private String stateTerminateEventClass;

    /**
     * 不通过触发事件ID
     */
    @Column(name = "STATE_TERMINATE_EVENT_ID")
    @TableField("STATE_TERMINATE_EVENT_ID")
    private String stateTerminateEventId;

    /**
     * 退回
     */
    @Column(name = "STATE_RETURN")
    @TableField("STATE_RETURN")
    private String stateReturn;

    /**
     * 退回触发事件
     */
    @Column(name = "STATE_RETURN_EVENT_CLASS")
    @TableField("STATE_RETURN_EVENT_CLASS")
    private String stateReturnEventClass;

    /**
     * 退回触发事件ID
     */
    @Column(name = "STATE_RETURN_EVENT_ID")
    @TableField("STATE_RETURN_EVENT_ID")
    private String stateReturnEventId;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 工作流节点ID
     */
    @Column(name = "NODE_ID")
    @TableField("NODE_ID")
    private String nodeId;

    // 系统内置审批状态名称
    @Transient
    @TableField(exist = false)
    private String presetStatePass = ConstantsWorkflow.STATE_PASS;
    @Transient
    @TableField(exist = false)
    private String presetStateTerminate = ConstantsWorkflow.STATE_TERMINATE;
    @Transient
    @TableField(exist = false)
    private String presetStateReturn = ConstantsWorkflow.STATE_RETURN;
}
