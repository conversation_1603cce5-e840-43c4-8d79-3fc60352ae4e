package com.sanythadmin.project.workflow.dto;

import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 审核结果信息
 * Created by JIANGPING on 2025/1/20.
 */
@Data
@AllArgsConstructor
public class ApprovalInfo<T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> {
    /**
     * 当前审核节点
     */
    private T currentApprovalNode;

    /**
     * 当前审核节点审核记录（包含审核表单信息）
     */
    private U currentApprovalNodeRecord;

    /**
     * 审核阶段（可用于更新申请表）
     */
    private ReviewResult reviewResult;

    /**
     * 可审核的下一级节点ID
     */
    private String nextApprovableNodeId;
}
