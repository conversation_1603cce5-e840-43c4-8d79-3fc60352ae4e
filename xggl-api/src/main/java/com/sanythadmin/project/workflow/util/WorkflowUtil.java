package com.sanythadmin.project.workflow.util;

import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.entity.*;
import com.sanythadmin.project.workflow.enums.ReviewStage;
import com.sanythadmin.project.workflow.param.WorkflowConditionDetailParam;
import com.sanythadmin.project.workflow.service.WorkflowConditionDetailService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;

/**
 * Created by JIANGPING on 2024/9/18.
 */
public class WorkflowUtil {

    /**
     * 获取当前所在节点
     *
     * @param approverNodes    审批人所在节点
     * @param approvalNodeList 全部审批节点
     * @return
     */
    public static <T, U extends BaseApprovalNode> U getApprovalNode(@NotNull List<T> approverNodes, @NotNull List<U> approvalNodeList) {
        HashSet<String> nodeIds = new HashSet<>();
        for (T object : approverNodes) {
            if (object instanceof WorkflowNodeApprover) {
                nodeIds.add(((WorkflowNodeApprover) object).getNodeId());
            } else if (object instanceof WorkflowNode) {
                nodeIds.add(((WorkflowNode) object).getId());
            }
        }

        List<U> approvalNodes = approvalNodeList.stream().filter(node -> nodeIds.contains(node.getNodeId())).toList();
        U currentApprovalNode = null;
        if (!CollectionUtils.isEmpty(approvalNodes)) {
            int size = approvalNodes.size();
            if (size == 1) {
                currentApprovalNode = approvalNodes.get(0);
            } else {
                approvalNodes = new ArrayList<>(approvalNodes);
                approvalNodes.sort(Comparator.comparing(U::getSort));
                currentApprovalNode = approvalNodes.get(size - 1);
                for (int i = size - 1; i >= 0; i--) {
                    U node = approvalNodes.get(i);
                    String result = node.getResult();
                    if (Objects.equals(ConstantsWorkflow.STATE_TERMINATE, result) || Objects.equals(ConstantsWorkflow.STATE_REJECT, result) || Objects.equals(ConstantsWorkflow.STATE_RETURN, result)) {
                        currentApprovalNode = node;
                        break;
                    } else if (Objects.equals(result, ConstantsWorkflow.STATE_PENDING)) {
                        currentApprovalNode = node;
                        break;
                    }
                }
            }
        }
        return currentApprovalNode;
    }

    /**
     * 获取当前审批节点的下标
     *
     * @param node 当前审批节点
     * @param list 所有审批节点
     * @return
     */
    public static <T extends BaseApprovalNode> Integer currentApprovalNodeIndex(@NotNull T node, @NotNull List<T> list) {
        list.sort(Comparator.comparing(T::getSort));
        OptionalInt optional = IntStream.range(0, list.size()).filter(i -> Objects.equals(list.get(i).getId(), node.getId())).findFirst();
        return optional.isPresent() ? optional.getAsInt() : null;
    }

    /**
     * 获取上一个审批节点
     *
     * @param node 当前审批节点
     * @param list 所有审批节点
     * @return
     */
    public static <T extends BaseApprovalNode> T preApprovalNode(@NotNull T node, @NotNull List<T> list) {
        Integer index = currentApprovalNodeIndex(node, list);
        if (index == null || index == 0) return null;
        return list.get(index - 1);
    }

    /**
     * 获取下一个审批节点
     *
     * @param node 当前审批节点
     * @param list 所有审批节点
     * @return
     */
    public static <T extends BaseApprovalNode> T nextApprovalNode(@NotNull T node, @NotNull List<T> list) {
        Integer index = currentApprovalNodeIndex(node, list);
        if (index == null || index == list.size() - 1) return null;
        return list.get(index + 1);
    }

    public static WorkflowNodeApprover getNodeApprover(@NotNull List<WorkflowNodeApprover> nodeApprovers, @NotNull BaseApprovalNode currentApprovalNode) {
        return nodeApprovers.stream().filter(node -> Objects.equals(node.getNodeId(), currentApprovalNode.getNodeId())).findFirst().orElse(null);
    }

    /**
     * 根据节点Id查找node节点
     *
     * @param nodeId   节点ID
     * @param nodeList 节点集合
     * @return
     */
    public static WorkflowNode node(@NotEmpty String nodeId, @NotNull List<WorkflowNode> nodeList) {
        return nodeList.stream().filter(node -> Objects.equals(node.getId(), nodeId)).findFirst().orElse(null);
    }


    /**
     * 根据流转条件获取下级审核节点
     *
     * @param username        用户名
     * @param currentNodeSort 当前审核节点排序
     * @param nodeList        节点列表
     * @param applicationInfo 申请信息
     * @param userInfo        用户信息
     * @param <T>
     * @return
     */
    public static <T> WorkflowNode nextNode(String username, Integer currentNodeSort, @NotNull List<WorkflowNode> nodeList, T applicationInfo, UserInfo userInfo) {
        return nextNode(username, currentNodeSort, nodeList, applicationInfo, userInfo, null);
    }

    /**
     * 根据流转条件获取下级审核节点
     *
     * @param username        用户名
     * @param currentNodeSort 当前审核节点排序
     * @param nodeList        节点列表
     * @param applicationInfo 申请信息
     * @param userInfo        用户信息
     * @param conditionMap    流转条件
     * @param <T>
     * @return
     */
    public static <T> WorkflowNode nextNode(String username, Integer currentNodeSort, @NotNull List<WorkflowNode> nodeList, T applicationInfo, UserInfo userInfo, Map<String, List<WorkflowConditionDetail>> conditionMap) {
        nodeList.sort(Comparator.comparing(WorkflowNode::getSort));
        if (!Objects.isNull(currentNodeSort))
            nodeList = nodeList.stream().filter(node -> node.getSort() > currentNodeSort).toList();
        if (CollectionUtils.isEmpty(nodeList)) return null;
        if (Objects.isNull(userInfo)) {
            UserInfoService bean = SpringContextUtil.getBean(UserInfoService.class);
            userInfo = bean.get(username);
        }
        for (WorkflowNode node : nodeList) {
            if (checkConditionIsMatch(node, applicationInfo, userInfo, conditionMap))
                return node;
        }
        return null;
    }

    public static <T> boolean checkConditionIsMatch(WorkflowNode node, T applicationInfo, UserInfo userInfo, Map<String, List<WorkflowConditionDetail>> conditionMap) {
        if (StringUtils.hasText(node.getConditionId())) {
            String[] strings = CommonUtil.split(node.getConditionId());
            String conditionId = strings[strings.length - 1];
            List<WorkflowConditionDetail> conditionDetails = null;
            if (!Objects.isNull(conditionMap)) {
                conditionDetails = conditionMap.get(conditionId);
            } else {
                WorkflowConditionDetailService bean = SpringContextUtil.getBean(WorkflowConditionDetailService.class);
                WorkflowConditionDetailParam param = new WorkflowConditionDetailParam();
                param.setConditionId(conditionId);
                PageParam<WorkflowConditionDetail, WorkflowConditionDetailParam> pageParam = new PageParam<>(param);
                conditionDetails = bean.list(pageParam.getWrapper());
            }
            return checkConditionIsMatch(conditionDetails, applicationInfo, userInfo);
        }
        return true;
    }

    private static <T> boolean checkConditionIsMatch(List<WorkflowConditionDetail> conditionDetails, T applicationInfo, UserInfo userInfo) {
        if (CollectionUtils.isEmpty(conditionDetails)) return true;
        for (WorkflowConditionDetail detail : conditionDetails) {
            Object value = CommonUtil.getValue(detail.getFieldEn(), userInfo);
            if (Objects.equals(detail.getCondition(), QueryType.EQ.name())) {
                if (!Objects.equals(detail.getValue(), String.valueOf(value))) return false;
            } else if (Objects.equals(detail.getCondition(), QueryType.NE.name())) {
                if (Objects.equals(detail.getValue(), String.valueOf(value))) return false;
            } else {
                if (!checkNumber(value, detail)) return false;
            }
        }
        return true;
    }

    private static boolean checkNumber(Object value, WorkflowConditionDetail detail) {
        BigDecimal value1 = new BigDecimal(String.valueOf(value));
        BigDecimal value2 = new BigDecimal(detail.getValue());
        int result = value1.compareTo(value2);
        if (Objects.equals(detail.getCondition(), QueryType.GT.name())) {
            return result > 0;// value1 大于 value2
        } else if (Objects.equals(detail.getCondition(), QueryType.LT.name())) {
            return result < 0;// value1 小于 value2
        }
        return false;
    }

    public static <T extends BaseApprovalNode> T getApprovalNode(@NotNull Class<T> tClass, WorkflowNode node, String applicationId, boolean startNode) {
        T approvalNode = null;
        try {
            approvalNode = tClass.getConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        approvalNode.setCreateTime(LocalDateTime.now());
        approvalNode.setWorkflowId(node.getWorkflowId());
        approvalNode.setNodeId(node.getId());
        approvalNode.setApplicationId(applicationId);
        approvalNode.setSort(node.getSort());
        approvalNode.setResult(ConstantsWorkflow.STATE_PENDING);
        approvalNode.setPreNodeResult(null);
        approvalNode.setNextNodeResult(ConstantsWorkflow.STATE_PENDING);
        approvalNode.setStartNode(startNode ? JudgeMark.YES : JudgeMark.NO);
        approvalNode.setEndNode(JudgeMark.NO);
        approvalNode.setStage(ReviewStage.ChuShen);
        approvalNode.setReviewType(node.getReviewType());
        approvalNode.setNodeName(node.getNodeName());
        return approvalNode;
    }

    public static <T extends BaseApprovalNodeRecord> void uploadApprovalFile(T record) {
        if (!CollectionUtils.isEmpty(record.getFileMap())) {
            Set<Map.Entry<String, List<MultipartFile>>> entries = record.getFileMap().entrySet();
            GridFsService gridFsService = SpringContextUtil.getBean(GridFsService.class);
            for (Map.Entry<String, List<MultipartFile>> entry : entries) {
                String fileInfoStr = gridFsService.save(entry.getValue().toArray(new MultipartFile[]{}));
                CommonUtil.setValue(record, entry.getKey(), fileInfoStr);
            }
        }
    }

    /**
     * 根据流转条件匹配所有审核节点
     *
     * @param tClass          审核节点class
     * @param userInfo        申请人记录对应用户信息
     * @param applicationInfo 申请信息
     * @param nodes           工作流节点
     * @param conditionMap    流转条件
     * @param <T>
     * @return
     */
    public static <T extends BaseApprovalNode, U extends BaseApplicationInfo> List<T> getApprovalNode(@NotNull Class<T> tClass, UserInfo userInfo, U applicationInfo, List<WorkflowNode> nodes, Map<String, List<WorkflowConditionDetail>> conditionMap) {
        List<T> approvalNodes = new ArrayList<>();
        Integer sort = null;
        while (true) {
            WorkflowNode nextNode = nextNode(null, sort, nodes, applicationInfo, userInfo, conditionMap);
            if (nextNode == null) break;
            sort = nextNode.getSort();
            T approvalNode = getApprovalNode(tClass, nextNode, applicationInfo.getId(), CollectionUtils.isEmpty(approvalNodes));
            approvalNodes.add(approvalNode);
        }
        return approvalNodes;
    }
}
