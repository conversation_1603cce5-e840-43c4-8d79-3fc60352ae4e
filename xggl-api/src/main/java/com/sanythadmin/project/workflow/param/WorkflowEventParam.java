package com.sanythadmin.project.workflow.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流管理模块事件配置查询参数
 *
 * <AUTHOR>
 * @since 2024-12-25 10:57:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowEventParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 事件类型（消息通知, 字段更新, 其他）
     */
    private String eventType;

}
