package com.sanythadmin.project.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * Created by JIANGPING on 2024/10/21.
 */
public enum ReviewStage implements ValueTextBaseEnum {
    ChuShen(0, "初审"),
    FuShen(1, "复审");

    @EnumValue
    private Integer value;
    @JsonValue
    private String text;

    ReviewStage(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
