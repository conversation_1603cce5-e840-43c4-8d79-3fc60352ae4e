package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowEventMapper;
import com.sanythadmin.project.workflow.service.WorkflowEventService;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 工作流管理模块事件配置Service实现
 *
 * <AUTHOR>
 * @since 2024-12-25 10:57:15
 */
@Service
public class WorkflowEventServiceImpl extends ServiceImpl<WorkflowEventMapper, WorkflowEvent> implements WorkflowEventService {

    @Resource
    private WorkflowEventMapper mapper;

}
