package com.sanythadmin.project.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;

import java.util.List;

/**
 * 审批节点与审批对象关系Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowNodeApproverService extends IService<WorkflowNodeApprover> {

    public List<WorkflowNodeApprover> nodeListByFormInfoAndApprover(String projectId, String year, String approverId);
    public List<WorkflowNodeApprover> listByWorkflow(String workflowId);
}
