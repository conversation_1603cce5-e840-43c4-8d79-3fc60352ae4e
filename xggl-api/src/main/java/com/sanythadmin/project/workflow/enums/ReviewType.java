package com.sanythadmin.project.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
public enum ReviewType implements ValueTextBaseEnum {
    huiQian(0, "会签"),
    huoQian(1, "或签");
    @EnumValue
    private Integer value;
    @JsonValue
    private String text;

    ReviewType(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
