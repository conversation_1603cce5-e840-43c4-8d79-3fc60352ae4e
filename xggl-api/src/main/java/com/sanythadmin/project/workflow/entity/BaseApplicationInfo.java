package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.core.web.ResultItem;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by JIANGPING on 2025/1/20.
 */
@Data
@MappedSuperclass
public class BaseApplicationInfo implements Serializable {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 申请人学/工号
     */
    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    /**
     * 申请时的角色
     */
    @Column(name = "ROLE_ID")
    @TableField("ROLE_ID")
    private String roleId;

    /**
     * 项目名称
     */
    @Column(name = "XXMC")
    @TableField("XXMC")
    private String xxmc;

    /**
     * 是否推送
     */
    @Column(name = "SFTS")
    @TableField("SFTS")
    private JudgeMark sfts;
    /**
     * 附件集合
     */
    @Transient
    @TableField(exist = false)
    private List<ResultItem> resultFiles;
}
