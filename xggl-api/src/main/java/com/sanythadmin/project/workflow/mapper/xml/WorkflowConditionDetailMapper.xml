<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowConditionDetailMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONDITION_ID, FIELD_ZH, FIELD_EN, VALUE, ORIGIN, USER_TYPE, LOAD_DATA_TYPE, CONDITION, FIELD_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.conditionId != null">
                        AND a.CONDITION_ID LIKE concat(concat('%',#{param.conditionId), '%')
                    </if>
                    <if test="param.fieldZh != null">
                        AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh), '%')
                    </if>
                    <if test="param.fieldEn != null">
                        AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
                    </if>
                    <if test="param.value != null">
                        AND a.VALUE LIKE concat(concat('%',#{param.value), '%')
                    </if>
                    <if test="param.origin != null">
                        AND a.ORIGIN = #{param.origin}
                    </if>
                    <if test="param.userType != null">
                        AND a.USER_TYPE = #{param.userType}
                    </if>
                    <if test="param.loadDataType != null">
                        AND a.LOAD_DATA_TYPE LIKE concat(concat('%',#{param.loadDataType), '%')
                    </if>
                    <if test="param.condition != null">
                        AND a.CONDITION LIKE concat(concat('%',#{param.condition), '%')
                    </if>
                    <if test="param.fieldId != null">
                        AND a.FIELD_ID LIKE concat(concat('%',#{param.fieldId), '%')
                    </if>
    </sql>
</mapper>
