package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作流节点审批表单
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_NODE_FORM")
@Entity
@Table(name = "SYT_WORKFLOW_NODE_FORM")
public class WorkflowNodeForm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @Column(name = "REQUIRED")
    @TableField("REQUIRED")
    private JudgeMark required;

    /**
     * 正则验证表达式（短文本输入内容）
     */
    @Column(name = "REG_EXPRESSION")
    @TableField("REG_EXPRESSION")
    private String regExpression;
    /**
     * 加载数据类型（来自公共码表code）
     */
    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;
    /**
     * 下拉控件加载数据URL ID
     */
    @Column(name = "LOAD_DATA_URL_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;
    /**
     * 加载数据URL
     */
    @Column(name = "LOAD_DATA_URL")
    @TableField("LOAD_DATA_URL")
    private String loadDataUrl;

    @Column(name = "TEXT_FIELD")
    @TableField("TEXT_FIELD")
    private String textField;

    @Column(name = "VALUE_FIELD")
    @TableField("VALUE_FIELD")
    private String valueField;

    /**
     * 控件类型
     */
    @Column(name = "CONTROL_TYPE")
    @TableField("CONTROL_TYPE")
    private String controlType;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 审批节点ID
     */
    @Column(name = "NODE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NODE_ID")
    private String nodeId;

    /**
     * 关联 WorkflowNodeApprover 实体 id
     */
    @Column(name = "NODE_APPROVER_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NODE_APPROVER_ID")
    private String nodeApproverId;

    /**
     * 关联处理事件
     */
    @Column(name = "PROCESS_EVENT", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("PROCESS_EVENT")
    private String processEvent;

    /**
     * 默认值或默认值表达式
     */
    @Column(name = "DEFAULT_VAL", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("DEFAULT_VAL")
    private String defaultVal;

    /**
     * 用户信息表更新字段

     @Column(name = "UPDATE_FIELD_EN")
     @TableField("UPDATE_FIELD_EN") private String updateFieldEn;
     */
}
