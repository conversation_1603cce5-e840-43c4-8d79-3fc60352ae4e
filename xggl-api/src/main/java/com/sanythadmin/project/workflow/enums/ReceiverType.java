package com.sanythadmin.project.workflow.enums;

/**
 * 消息接收者类型枚举
 *
 */
public enum ReceiverType {
    /**
     * 申请人
     */
    APPLICANT("APPLICANT", "申请人"),
    
    /**
     * 审批人
     */
    APPROVER("APPROVER", "审批人"),
    
    /**
     * 申请人和审批人
     */
    BOTH("BOTH", "申请人和审批人"),
    
    /**
     * 自定义接收人
     */
    CUSTOM("CUSTOM", "自定义接收人");

    private final String code;
    private final String desc;

    ReceiverType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ReceiverType fromCode(String code) {
        for (ReceiverType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return BOTH; // 默认返回BOTH
    }
} 