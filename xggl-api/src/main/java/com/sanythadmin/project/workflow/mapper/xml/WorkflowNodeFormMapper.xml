<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowNodeFormMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, LOAD_DATA_TYPE
        , FIELD_ZH, FIELD_EN, REQUIRED, REG_EXPRESSION, LOAD_DATA_URL, TEXT_FIELD, VALUE_FIELD, CONTROL_TYPE, TIP_TEXT, SORT, WORKFLOW_ID, NODE_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.fieldZh != null">
            AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh), '%')
        </if>
        <if test="param.fieldEn != null">
            AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
        </if>
        <if test="param.required != null">
            AND a.REQUIRED = #{param.required}
        </if>
        <if test="param.textField != null">
            AND a.TEXT_FIELD LIKE concat(concat('%',#{param.textField), '%')
        </if>
        <if test="param.valueField != null">
            AND a.VALUE_FIELD LIKE concat(concat('%',#{param.valueField), '%')
        </if>
        <if test="param.controlType != null">
            AND a.CONTROL_TYPE LIKE concat(concat('%',#{param.controlType), '%')
        </if>
        <if test="param.tipText != null">
            AND a.TIP_TEXT LIKE concat(concat('%',#{param.tipText), '%')
        </if>
        <if test="param.workflowId != null">
            AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
        </if>
        <if test="param.nodeId != null">
            AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
        </if>
    </sql>
</mapper>
