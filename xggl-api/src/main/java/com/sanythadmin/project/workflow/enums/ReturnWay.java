package com.sanythadmin.project.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * 退回方式
 * Created by JIANGPING on 2024/9/23.
 */
public enum ReturnWay implements ValueTextBaseEnum {
    /**
     * 申请人
     */
    APPLICANT(0, "APPLICANT"),
    /**
     * 上一节点
     */
    PRE_NODE(1, "PRE_NODE");


    @EnumValue
    private Integer value;
    @JsonValue
    private String text;

    ReturnWay(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
