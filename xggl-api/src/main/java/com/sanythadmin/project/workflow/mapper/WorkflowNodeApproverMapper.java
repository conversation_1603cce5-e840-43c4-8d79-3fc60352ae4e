package com.sanythadmin.project.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批节点与审批对象关系Mapper
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowNodeApproverMapper extends BaseMapper<WorkflowNodeApprover> {

    public List<WorkflowNodeApprover> nodeListByFormInfoAndApprover(@Param("projectId") String projectId, @Param("year") String year, @Param("approverId") String approverId);

    public List<WorkflowNodeApprover> nodeApproverList(@Param("param") WorkflowParam param, @Param("approverId") String approverId);
}
