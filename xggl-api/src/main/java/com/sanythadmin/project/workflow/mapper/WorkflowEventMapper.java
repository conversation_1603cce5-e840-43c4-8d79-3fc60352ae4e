package com.sanythadmin.project.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.observer.UpdateEventParam;
import org.apache.ibatis.annotations.Param;

/**
 * 工作流管理模块事件配置Mapper
 *
 * <AUTHOR>
 * @since 2024-12-25 10:57:15
 */
public interface WorkflowEventMapper extends BaseMapper<WorkflowEvent> {
    public <T, U extends BaseApprovalNodeRecord> void updateByExpression(@Param("param") UpdateEventParam<T, U> param);
}
