package com.sanythadmin.project.workflow.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流条件详情查询参数
 *
 * <AUTHOR>
 * @since 2024-07-25 16:04:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowConditionDetailParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(type = QueryType.EQ)
    private String conditionId;

    private String fieldZh;

    private String fieldEn;

    private String value;

    @QueryField(type = QueryType.EQ)
    private Integer origin;

    @QueryField(type = QueryType.EQ)
    private Integer userType;

    private String loadDataType;

    private String condition;

    private String fieldId;

}
