package com.sanythadmin.project.workflow.observer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.messsage.service.CommonMessageService;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.service.SysAccountRoleService;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.ApproverType;
import com.sanythadmin.project.workflow.enums.ReceiverType;
import com.sanythadmin.project.workflow.util.MessageTemplateResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowEventMessageObserver implements WorkflowNodeEventObserver {

    private final CommonMessageService commonMessageService;
    private final SysAccountRoleService sysAccountRoleService;
    private final MessageTemplateResolver messageTemplateResolver;
    
    @Override
    public <T, U extends BaseApprovalNodeRecord> void process(ApprovalData<T, U> approvalData, WorkflowEvent event) {
        try {
            if (event.getEnabled() != null && !event.getEnabled()) {
                log.debug("工作流事件未启用，跳过消息推送: {}", event.getEventName());
                return;
            }
            //业务模块自定义的二次推送开关
            Object sfts = CommonUtil.getValue("sfts", approvalData.getApplicationInfo());
            if (sfts != null && "0".equals(sfts.toString())) {
                log.debug("业务模块自定义的二次推送开关关闭，跳过消息推送: {}", event.getEventName());
                return;
            }

            ReceiverType receiverType = ReceiverType.fromCode(event.getReceiverType());
            
            // 处理申请人消息
            if (receiverType == ReceiverType.APPLICANT || receiverType == ReceiverType.BOTH) {
                sendMessageToApplicants(approvalData, event, receiverType);
            }
            
            // 处理审批人消息
            if (receiverType == ReceiverType.APPROVER || receiverType == ReceiverType.BOTH) {
                sendMessageToApprovers(approvalData, event, receiverType);
            }
            
            // 处理自定义接收者
            if (receiverType == ReceiverType.CUSTOM) {
                sendMessageToCustomReceivers(approvalData, event);
            }

        } catch (Exception e) {
            log.error("工作流事件消息推送失败: {}", event.getEventName(), e);
        }
    }

    /**
     * 发送消息给申请人
     */
    private <T, U extends BaseApprovalNodeRecord> void sendMessageToApplicants(ApprovalData<T, U> approvalData, WorkflowEvent event, ReceiverType receiverType) {
        // 获取申请人消息内容
        String message = getApplicantMessage(event, receiverType);
        if (StringUtils.isBlank(message)) {
            log.warn("申请人消息内容为空，跳过推送: {}", event.getEventName());
            return;
        }

        // 获取申请人
        Set<String> applicants = new HashSet<>();
        addApplicant(applicants, approvalData);
        
        if (applicants.isEmpty()) {
            log.warn("未找到申请人，跳过推送: {}", event.getEventName());
            return;
        }

        // 构建消息内容
        String messageContent = buildMessageContent(approvalData, event, message);
        String title = getApplicantMessageTitle(event, receiverType);
        title = StringUtils.isNotBlank(title) ? 
                buildMessageContent(approvalData, event, title) : "工作流通知";

        // 获取申请人的角色ID
        String applicantRoleId = getApplicantRoleId(approvalData);

        // 发送消息
        if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(event.getMobilePath()) || StringUtils.isNotBlank(event.getPcPath())) {
            commonMessageService.pushCard(applicants, title, messageContent, event.getMobilePath(), event.getPcPath(), applicantRoleId);
        } else {
            commonMessageService.pushText(applicants, messageContent);
        }

        log.info("工作流事件申请人消息推送成功: 事件={}, 接收者数量={}", event.getEventName(), applicants.size());
    }

    /**
     * 发送消息给审批人
     */
    private <T, U extends BaseApprovalNodeRecord> void sendMessageToApprovers(ApprovalData<T, U> approvalData, WorkflowEvent event, ReceiverType receiverType) {
        // 获取审批人消息内容
        String message = getApproverMessage(event, receiverType);
        if (StringUtils.isBlank(message)) {
            log.warn("审批人消息内容为空，跳过推送: {}", event.getEventName());
            return;
        }

        // 构建消息内容
        String messageContent = buildMessageContent(approvalData, event, message);
        String title = getApproverMessageTitle(event, receiverType);
        title = StringUtils.isNotBlank(title) ?
                buildMessageContent(approvalData, event, title) : "工作流通知";

        // 按角色分组推送审批人消息
        sendMessageToApproversByRole(approvalData, event, title, messageContent);
    }

    /**
     * 发送消息给自定义接收者
     */
    private <T, U extends BaseApprovalNodeRecord> void sendMessageToCustomReceivers(ApprovalData<T, U> approvalData, WorkflowEvent event) {
        if (StringUtils.isBlank(event.getMessage())) {
            log.warn("自定义接收者消息内容为空，跳过推送: {}", event.getEventName());
            return;
        }

        Set<String> receivers = new HashSet<>();
        addCustomReceivers(receivers, event.getCustomReceivers());
        
        if (receivers.isEmpty()) {
            log.warn("未找到自定义接收者，跳过推送: {}", event.getEventName());
            return;
        }

        String messageContent = buildMessageContent(approvalData, event);
        String title = StringUtils.isNotBlank(event.getMessageTitle()) ?
                buildMessageContent(approvalData, event, event.getMessageTitle()) : "工作流通知";

        if (StringUtils.isNotBlank(event.getMessageTitle()) || StringUtils.isNotBlank(event.getMobilePath()) || StringUtils.isNotBlank(event.getPcPath())) {
            commonMessageService.pushCard(receivers, title, messageContent, event.getMobilePath(), event.getPcPath(), null);
        } else {
            commonMessageService.pushText(receivers, messageContent);
        }

        log.info("工作流事件自定义接收者消息推送成功: 事件={}, 接收者数量={}", event.getEventName(), receivers.size());
    }

    /**
     * 获取申请人消息内容
     */
    private String getApplicantMessage(WorkflowEvent event, ReceiverType receiverType) {
        if (receiverType == ReceiverType.BOTH && StringUtils.isNotBlank(event.getApplicantMessage())) {
            return event.getApplicantMessage();
        }
        return event.getMessage();
    }

    /**
     * 获取审批人消息内容
     */
    private String getApproverMessage(WorkflowEvent event, ReceiverType receiverType) {
        if (receiverType == ReceiverType.BOTH && StringUtils.isNotBlank(event.getApproverMessage())) {
            return event.getApproverMessage();
        }
        return event.getMessage();
    }

    /**
     * 获取申请人消息标题
     */
    private String getApplicantMessageTitle(WorkflowEvent event, ReceiverType receiverType) {
        if (receiverType == ReceiverType.BOTH && StringUtils.isNotBlank(event.getApplicantMessageTitle())) {
            return event.getApplicantMessageTitle();
        }
        return event.getMessageTitle();
    }

    /**
     * 获取审批人消息标题
     */
    private String getApproverMessageTitle(WorkflowEvent event, ReceiverType receiverType) {
        if (receiverType == ReceiverType.BOTH && StringUtils.isNotBlank(event.getApproverMessageTitle())) {
            return event.getApproverMessageTitle();
        }
        return event.getMessageTitle();
    }



    /**
     * 获取申请人角色ID
     */
    private <T, U extends BaseApprovalNodeRecord> String getApplicantRoleId(ApprovalData<T, U> approvalData) {
        Object roleId = CommonUtil.getValue("roleId", approvalData.getApplicationInfo());
        return roleId != null ? roleId.toString() : null;
    }

    /**
     * 添加申请人
     */
    private <T, U extends BaseApprovalNodeRecord> void addApplicant(Set<String> receivers, ApprovalData<T, U> approvalData) {
        Object xgh = CommonUtil.getValue("xgh", approvalData.getApplicationInfo());
        if (xgh != null) {
            receivers.add(xgh.toString());
        }
    }

    /**
     * 按角色分组推送审批人消息
     */
    private <T, U extends BaseApprovalNodeRecord> void sendMessageToApproversByRole(ApprovalData<T, U> approvalData, WorkflowEvent event, String title, String messageContent) {
        List<WorkflowNodeApprover> nextNodeApprovers = approvalData.getNextNodeApprovers();
        if (nextNodeApprovers == null || nextNodeApprovers.isEmpty()) {
            log.warn("未找到审批人，跳过推送: {}", event.getEventName());
            return;
        }

        for (WorkflowNodeApprover approver : nextNodeApprovers) {
            if (approver.getApproverType() == ApproverType.role) {
                // 下级审批是角色：按角色分组推送
                sendMessageToRoleApprovers(approver.getApproverId(), event, title, messageContent);
            } else if (approver.getApproverType() == ApproverType.user) {
                // 下级审批是用户：查出用户的所有角色，分别推送
                sendMessageToUserApprovers(approver.getApproverId(), event, title, messageContent);
            }
        }
    }

    /**
     * 推送消息给角色审批人
     */
    private void sendMessageToRoleApprovers(String roleId, WorkflowEvent event, String title, String messageContent) {
        try {
            List<SysAccountRole> accountRoles = sysAccountRoleService.list(
                new LambdaQueryWrapper<SysAccountRole>()
                    .eq(SysAccountRole::getRoleId, roleId)
            );
            if (accountRoles != null && !accountRoles.isEmpty()) {
                Set<String> roleUserXghs = accountRoles.stream()
                    .map(SysAccountRole::getUsername)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

                if (!roleUserXghs.isEmpty()) {
                    if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(event.getMobilePath()) || StringUtils.isNotBlank(event.getPcPath())) {
                        commonMessageService.pushCard(roleUserXghs, title, messageContent, event.getMobilePath(), event.getPcPath(), roleId);
                    } else {
                        commonMessageService.pushText(roleUserXghs, messageContent);
                    }
                    log.info("工作流事件角色审批人消息推送成功: 事件={}, 角色={}, 接收者数量={}", event.getEventName(), roleId, roleUserXghs.size());
                }
            }
        } catch (Exception e) {
            log.warn("推送角色审批人消息失败: roleId={}", roleId, e);
        }
    }

    /**
     * 推送消息给用户审批人
     */
    private void sendMessageToUserApprovers(String username, WorkflowEvent event, String title, String messageContent) {
        try {
            // 查出用户的所有角色
            List<SysAccountRole> userRoles = sysAccountRoleService.list(
                new LambdaQueryWrapper<SysAccountRole>()
                    .eq(SysAccountRole::getUsername, username)
            );

            Set<String> singleUser = Set.of(username);

            if (userRoles == null || userRoles.isEmpty()) {
                // 用户没有角色，使用null角色推送
                if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(event.getMobilePath()) || StringUtils.isNotBlank(event.getPcPath())) {
                    commonMessageService.pushCard(singleUser, title, messageContent, event.getMobilePath(), event.getPcPath(), null);
                } else {
                    commonMessageService.pushText(singleUser, messageContent);
                }
                log.info("工作流事件用户审批人消息推送成功: 事件={}, 用户={}, 角色=null", event.getEventName(), username);
            } else if (userRoles.size() == 1) {
                // 单角色用户：直接跳转对应角色的审核页面
                String roleId = userRoles.get(0).getRoleId();
                if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(event.getMobilePath()) || StringUtils.isNotBlank(event.getPcPath())) {
                    commonMessageService.pushCard(singleUser, title, messageContent, event.getMobilePath(), event.getPcPath(), roleId);
                } else {
                    commonMessageService.pushText(singleUser, messageContent);
                }
                log.info("工作流事件用户审批人消息推送成功: 事件={}, 用户={}, 单角色={}", event.getEventName(), username, roleId);
            } else {
                // 多角色用户：跳转角色选择页面，在路径中增加多角色标识参数
                String mobilePath = buildMultiRoleApprovalPath(event.getMobilePath(), userRoles);
                String pcPath = buildMultiRoleApprovalPath(event.getPcPath(), userRoles);

                if (StringUtils.isNotBlank(title) || StringUtils.isNotBlank(mobilePath) || StringUtils.isNotBlank(pcPath)) {
                    // 对于多角色用户，不传递特定roleId，让前端处理角色选择
                    commonMessageService.pushCard(singleUser, title, messageContent, mobilePath, pcPath, null);
                } else {
                    commonMessageService.pushText(singleUser, messageContent);
                }
                log.info("工作流事件用户审批人消息推送成功: 事件={}, 用户={}, 多角色数量={}", event.getEventName(), username, userRoles.size());
            }
        } catch (Exception e) {
            log.warn("推送用户审批人消息失败: username={}", username, e);
        }
    }

    /**
     * 构建多角色审批路径
     * 为多角色用户在路径中增加参数，让前端知道需要进行角色选择
     */
    private String buildMultiRoleApprovalPath(String originalPath, List<SysAccountRole> userRoles) {
        if (StringUtils.isBlank(originalPath)) {
            return originalPath;
        }
        String roleIds = userRoles.stream()
            .map(SysAccountRole::getRoleId)
            .collect(Collectors.joining(","));
        String separator = originalPath.contains("?") ? "&" : "?";
        return originalPath + separator + "multiRole=true&roleIds=" + roleIds;
    }

    /**
     * 添加自定义接收人
     */
    private void addCustomReceivers(Set<String> receivers, String customReceivers) {
        if (StringUtils.isBlank(customReceivers)) {
            return;
        }
        
        String[] receiverArray = customReceivers.split(",");
        for (String receiver : receiverArray) {
            if (StringUtils.isNotBlank(receiver.trim())) {
                receivers.add(receiver.trim());
            }
        }
    }

    /**
     * 构建消息内容
     */
    private <T, U extends BaseApprovalNodeRecord> String buildMessageContent(ApprovalData<T, U> approvalData, WorkflowEvent event) {
        return buildMessageContent(approvalData, event, event.getMessage());
    }

    /**
     * 添加审批人（保留原方法以兼容其他地方的调用）
     */
    private <T, U extends BaseApprovalNodeRecord> void addApprovers(Set<String> receivers, ApprovalData<T, U> approvalData) {
        List<WorkflowNodeApprover> nextNodeApprovers = approvalData.getNextNodeApprovers();
        if (nextNodeApprovers == null || nextNodeApprovers.isEmpty()) {
            return;
        }
        for (WorkflowNodeApprover approver : nextNodeApprovers) {
            if (approver.getApproverType() == ApproverType.user) {
                receivers.add(approver.getApproverId());
            } else if (approver.getApproverType() == ApproverType.role) {
                try {
                    List<SysAccountRole> accountRoles = sysAccountRoleService.list(
                        new LambdaQueryWrapper<SysAccountRole>()
                            .eq(SysAccountRole::getRoleId, approver.getApproverId())
                    );
                    if (accountRoles != null) {
                        Set<String> roleUserXghs = accountRoles.stream()
                            .map(SysAccountRole::getUsername)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());
                        receivers.addAll(roleUserXghs);
                    }
                } catch (Exception e) {
                    log.warn("获取角色用户失败: roleId={}", approver.getApproverId(), e);
                }
            }
        }
    }

    /**
     * 构建消息内容（支持模板变量替换）
     */
    private <T, U extends BaseApprovalNodeRecord> String buildMessageContent(ApprovalData<T, U> approvalData, WorkflowEvent event, String template) {
        if (StringUtils.isBlank(template)) {
            return "";
        }

        try {
            // 使用新的模板解析器
            return messageTemplateResolver.resolveTemplate(template, approvalData);
        } catch (Exception e) {
            log.warn("消息模板变量替换失败: {}", event.getEventName(), e);
            return template; // 失败时返回原始模板
        }
    }
}
