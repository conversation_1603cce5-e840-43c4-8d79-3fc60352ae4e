package com.sanythadmin.project.workflow.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.enums.NodeType;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2024/7/23.
 */
@JsonIgnoreProperties({"nodes", "conditions"})
@Data
public class WorkflowDTO extends Workflow {

    private Boolean format = false;
    private NodeDTO workflowNodes;

    List<NodeDTO> nodes;
    List<ConditionDTO> conditions;

    public void format() {
        if (format) return;
        this.format = true;
        this.nodes = new ArrayList<>();
        this.conditions = new ArrayList<>();
        NodeDTO nodeDTO = this.workflowNodes;
        String parentNode = null;
        while (!Objects.isNull(nodeDTO) && !Objects.isNull(nodeDTO.getType())) {
            if (Objects.equals(nodeDTO.getType().getValue(), NodeType.LuYou.getValue())) {
                traversalConditionNode(nodeDTO, nodeDTO.getConditionName(), parentNode);
            } else {
                this.nodes.add(nodeDTO);
            }
            parentNode = nodeDTO.getNodeName();
            nodeDTO = nodeDTO.getChildNode();
        }

        // 排序处理
        int sort = 0;
        Map<String, Integer> sortMap = new HashMap<>();
        for (NodeDTO node : this.nodes) {
//            sort++;
//            node.setSort(sort);
//            if (Objects.equals(node.getType().getValue(), NodeType.jieDian.getValue())) {
            String tempKey = node.getTempKey();
            if (ObjectUtils.isEmpty(tempKey)) {
                node.setSort(++sort);
            } else {
                if (!sortMap.containsKey(tempKey))
                    sortMap.put(tempKey, ++sort);
                node.setSort(sortMap.get(tempKey));
            }
//            }
        }

        sort = 0;
        sortMap.clear();
        for (ConditionDTO condition : this.conditions) {
            String tempKey = condition.getTempKey();
            if (ObjectUtils.isEmpty(tempKey)) {
                condition.setSort(++sort);
            } else {
                if (!sortMap.containsKey(tempKey))
                    sortMap.put(tempKey, ++sort);
                condition.setSort(sortMap.get(tempKey));
            }
        }
    }

    private void traversalNode(NodeDTO node) {
        String parent = node.getParentNode();
        String conditionName = node.getConditionName();
        String parentNode = node.getNodeName();
        node = node.getChildNode();
        while (!Objects.isNull(node) && !Objects.isNull(node.getType())) {
            if (Objects.equals(node.getType().getValue(), NodeType.LuYou.getValue())) {
                traversalConditionNode(node, conditionName, parentNode);
            } else {
                node.setConditionName(conditionName);
                node.setParentNode(parent);
                this.nodes.add(node);
            }
            node = node.getChildNode();
        }
    }

    private void traversalConditionNode(NodeDTO node, String parentCondition, String parentNode) {
        List<ConditionDTO> conditionList = node.getConditionNodes();
        if (!CollectionUtils.isEmpty(conditionList)) {
            String tempKey = conditionList.stream().map(ConditionDTO::getNodeName).
                    collect(Collectors.joining(","));
            if (!StringUtils.hasText(node.getId()))
                node.setNodeName(CommonUtil.getUUID());
            node.setTempKey(tempKey);
            node.setConditionName(tempKey);
            node.setParentNode(parentNode);
            this.nodes.add(node);
            for (ConditionDTO condition : conditionList) {
                NodeDTO childNode = condition.getChildNode();
                boolean flag = Objects.isNull(childNode) || Objects.isNull(childNode.getType());
                condition.setTempKey(tempKey);
                if (!StringUtils.hasText(condition.getId())) {
                    condition.setConditionName(condition.getNodeName());
                    if (!flag) {
                        String conditionName = StringUtils.hasText(parentCondition) ? parentCondition + "," + condition.getConditionName()
                                : condition.getConditionName();
                        condition.setNodeName(childNode.getNodeName());
                        childNode.setParentNode(node.getNodeName());
                        childNode.setConditionName(conditionName);
                    }
                }
                this.conditions.add(condition);
                if (!flag) {
                    childNode.setTempKey(tempKey);
                    this.nodes.add(childNode);
                    traversalNode(childNode);
                }
            }
        }
    }
}
