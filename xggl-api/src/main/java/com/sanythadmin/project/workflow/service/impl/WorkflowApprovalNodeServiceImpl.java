package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowApprovalNodeMapper;
import com.sanythadmin.project.workflow.service.WorkflowApprovalNodeService;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNode;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 表单申请审批节点Service实现
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Service
public class WorkflowApprovalNodeServiceImpl extends ServiceImpl<WorkflowApprovalNodeMapper, WorkflowApprovalNode> implements WorkflowApprovalNodeService {

    @Resource
    private WorkflowApprovalNodeMapper mapper;

}
