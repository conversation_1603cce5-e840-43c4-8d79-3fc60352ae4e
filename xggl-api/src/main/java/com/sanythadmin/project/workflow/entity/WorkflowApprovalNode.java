package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单申请审批节点
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_APPROVAL_NODE")
@Entity
@Table(name = "SYT_WORKFLOW_APPROVAL_NODE")
public class WorkflowApprovalNode extends BaseApprovalNode {
    private static final long serialVersionUID = 1L;

}
