package com.sanythadmin.project.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * Created by JIANGPING on 2024/7/22.
 */
public enum NodeType implements ValueTextBaseEnum {
    <PERSON><PERSON><PERSON><PERSON>(0, "节点"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(1, "抄送人"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(2, "条件"),
    <PERSON><PERSON><PERSON>(3, "路由");
    @JsonValue
    @EnumValue
    private Integer value;
    private String text;

    NodeType(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @JsonCreator
    public static NodeType fromValue(Integer value) {
        for (NodeType nodeType : NodeType.values()) {
            if (nodeType.getValue().equals(value)) {
                return nodeType;
            }
        }
        return null;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
