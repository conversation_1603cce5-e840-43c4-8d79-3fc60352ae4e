package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.project.form.enums.ConditionOrigin;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 工作流条件详情
 *
 * <AUTHOR>
 * @since 2024-07-25 16:04:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_CONDITION_DETAIL")
@Entity
@Table(name = "SYT_WORKFLOW_CONDITION_DETAIL")
public class WorkflowConditionDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Column(name = "CONDITION_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CONDITION_ID")
    private String conditionId;

    @Column(name = "FIELD_ZH")
    @TableField("FIELD_ZH")
    private String fieldZh;

    @Column(name = "FIELD_EN")
    @TableField("FIELD_EN")
    private String fieldEn;

    @Column(name = "VALUE")
    @TableField("VALUE")
    private String value;

    @Column(name = "ORIGIN")
    @TableField("ORIGIN")
    private ConditionOrigin origin;

    @Column(name = "USER_TYPE")
    @TableField("USER_TYPE")
    private UserType userType;

    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;

    @Column(name = "CONDITION")
    @TableField("CONDITION")
    private String condition;

    @Column(name = "FIELD_ID")
    @TableField("FIELD_ID")
    private String fieldId;

    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;
}
