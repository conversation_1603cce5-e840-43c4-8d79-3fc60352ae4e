package com.sanythadmin.project.workflow.observer;

import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;

/**
 * Created by JIANGPING on 2025/4/5.
 */
public interface WorkflowNodeFormEventObserver {
    /**
     * 事件处理逻辑
     *
     * @param approvalData     申请信息/审核记录/申请用户信息
     * @param workflowNodeForm 审核表单字段
     * @param <T>
     * @param <U>
     */
    public <T, U extends BaseApprovalNodeRecord> void process(ApprovalData<T, U> approvalData, WorkflowNodeForm workflowNodeForm);
}
