package com.sanythadmin.project.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.dto.WorkflowNodeStateDTO;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 工作流Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowService extends IService<Workflow> {
    void editWorkflow(WorkflowDTO workflow);

    void removeWorkflow(List<String> ids);

    WorkflowDTO get(String id);

    WorkflowDTO get(String projectId, String year);

    List<Workflow> list(WorkflowParam param);

    Workflow getByNodeId(String nodeId);

    void copyWorkflow(WorkflowDTO workflowDTO, String projectId, String year, String name);

    void copyWorkflow(Workflow workflow, String projectId, String year);

    /**
     * 审核流程创建
     *
     * @param tClass        审核节点实体class
     * @param info          申请信息
     * @param workflowParam 工作流查询参数
     * @param userInfo      申请信息对应用户信息
     * @param <T>           审核节点对象
     * @param <R>           申请信息对象
     */
    <T extends BaseApprovalNode, R extends BaseApplicationInfo> String createApprovalNode(Class<T> tClass, R info, WorkflowParam workflowParam, UserInfo userInfo);

    /**
     * 审核逻辑处理
     *
     * @param tClass   审核节点实体class
     * @param record   审核记录
     * @param info     申请信息
     * @param param    工作流查询参数
     * @param userInfo 申请信息对应用户信息
     * @param <T>      审核节点对象
     * @param <U>      审核记录对象
     * @param <R>      申请信息对象
     * @return 审核信息
     */
    <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord, R extends BaseApplicationInfo> ApprovalInfo<T, U> approve(Class<T> tClass, U record, R info, WorkflowParam param, UserInfo userInfo);

    /**
     * 申请信息是否可编辑（检查申请信息是否被审核）
     *
     * @param tClass        审核节点实体class
     * @param recordClass   审核节点记录实体class
     * @param applicationId 申请ID
     * @param <T>
     * @param <U>
     * @return 工作流ID
     */
    <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> String checkApplicationInfoIsCanEdit(Class<T> tClass, Class<U> recordClass, String applicationId);


    /**
     * 根据当前用户查询申请信息审核记录
     *
     * @param tClass
     * @param recordClass
     * @param param         工作流查询参数
     * @param applicationId 申请ID
     * @param nodeId        节点ID
     * @param <T>
     * @param <U>
     * @return
     */
    <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> List<U> approvalNodeRecordList(Class<T> tClass, Class<U> recordClass, WorkflowParam param, String applicationId, String nodeId);


    /**
     * 根据申请ID,节点ID查询审核节点信息（审核节点，审核表单，审核记录）
     *
     * @param tClass
     * @param recordClass
     * @param applicationId 申请ID
     * @param nodeId        节点ID
     * @param <T>
     * @param <U>
     * @return
     */
    <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> List<ApprovalNodeInfo<T, U>> approvalNodeInfo(@NotNull Class<T> tClass, @NotNull Class<U> recordClass, @NotEmpty String applicationId, String nodeId);

    /**
     * 根据申请记录ID或工作流节点查询节点自定义状态
     *
     * @param tClass
     * @param param         工作流查询参数
     * @param nodeId        节点ID
     * @param applicationId 申请ID
     * @param <T>
     * @return
     */
    <T extends BaseApprovalNode> List<WorkflowNodeStateDTO> nodeCustomState(Class<T> tClass, WorkflowParam param, String nodeId, String applicationId);

    /**
     * 检查申请信息是否可以删除
     *
     * @param tClass        审批节点类
     * @param recordClass   审批记录类
     * @param applicationId 申请ID
     * @param approverId    审批人ID (格式: projectId/sysModelCode,year)
     * @return 工作流ID
     */
    <T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> String checkApplicationInfoIsCanDelete(Class<T> tClass,Class<U> recordClass,String projectIdOrSysModelCode,String year,String applicationId,String approverId);


    /**
     * 获取审批列表子查询包装器
     *
     * @param wrapper                 包装纸
     * @param nodeApproverMapper      节点审批人映射器
     * @param projectIdOrSysModelCode 项目id或系统模型代码
     * @param year                    年
     * @param nodeId                  节点id
     * @param result                  结果
     */
    <T extends BaseApplicationInfo,U extends BaseParam> void getApprovalListSubQueryWrapper(MyMPJLambdaWrapper<T,U> wrapper, WorkflowNodeApproverMapper nodeApproverMapper,
                                                                                                               String projectIdOrSysModelCode, String year, String nodeId, String result);
}
