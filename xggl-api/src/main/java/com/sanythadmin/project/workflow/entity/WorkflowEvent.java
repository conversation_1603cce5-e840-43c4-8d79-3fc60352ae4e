package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 工作流管理模块事件配置
 *
 * <AUTHOR>
 * @since 2024-12-25 10:57:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_EVENT")
@Entity
@Table(name = "SYT_WORKFLOW_EVENT")
public class WorkflowEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 触发事件service注入名称
     */
    @Column(name = "EVENT_CLASS_NAME")
    @TableField("EVENT_CLASS_NAME")
    private String eventClassName;

    /**
     * 事件名称
     */
    @Column(name = "EVENT_NAME")
    @TableField("EVENT_NAME")
    private String eventName;

    /**
     * 事件类型（消息通知, 字段更新, 审核表单校验,其他）
     */
    @Column(name = "EVENT_TYPE")
    @TableField("EVENT_TYPE")
    private String eventType;

    /**
     * 字段更新表达式
     */
    @Column(name = "SET_EXPRESSION")
    @TableField("SET_EXPRESSION")
    private String setExpression;

    /**
     * 条件表达式
     */
    @Column(name = "WHERE_EXPRESSION")
    @TableField("WHERE_EXPRESSION")
    private String whereExpression;

    /**
     * 消息通知模板
     */
    @Column(name = "MESSAGE")
    @TableField("MESSAGE")
    private String message;

    /**
     * 字段更新表名
     */
    @Column(name = "SET_TABLE")
    @TableField("SET_TABLE")
    private String setTable;

    /**
     * 消息接收者类型（APPLICANT:申请人, APPROVER:审批人, BOTH:申请人和审批人, CUSTOM:自定义接收人）
     */
    @Column(name = "RECEIVER_TYPE")
    @TableField("RECEIVER_TYPE")
    private String receiverType;

    /**
     * 自定义接收人（当receiverType为CUSTOM时使用，多个用逗号分隔）
     */
    @Column(name = "CUSTOM_RECEIVERS")
    @TableField("CUSTOM_RECEIVERS")
    private String customReceivers;

    /**
     * 消息标题模板
     */
    @Column(name = "MESSAGE_TITLE")
    @TableField("MESSAGE_TITLE")
    private String messageTitle;

    /**
     * 申请人消息模板（当receiverType为BOTH时使用）
     */
    @Column(name = "APPLICANT_MESSAGE")
    @TableField("APPLICANT_MESSAGE")
    private String applicantMessage;

    /**
     * 审批人消息模板（当receiverType为BOTH时使用）
     */
    @Column(name = "APPROVER_MESSAGE")
    @TableField("APPROVER_MESSAGE")
    private String approverMessage;

    /**
     * 申请人消息标题模板（当receiverType为BOTH时使用）
     */
    @Column(name = "APPLICANT_MESSAGE_TITLE")
    @TableField("APPLICANT_MESSAGE_TITLE")
    private String applicantMessageTitle;

    /**
     * 审批人消息标题模板（当receiverType为BOTH时使用）
     */
    @Column(name = "APPROVER_MESSAGE_TITLE")
    @TableField("APPROVER_MESSAGE_TITLE")
    private String approverMessageTitle;

    /**
     * 是否启用（0：禁用，1：启用）
     */
    @Column(name = "ENABLED")
    @TableField("ENABLED")
    private Boolean enabled;

    /**
     * 移动端跳转路径
     */
    @Column(name = "MOBILE_PATH")
    @TableField("MOBILE_PATH")
    private String mobilePath;
    /**
     * pc跳转路径
     */
    @Column(name = "PC_PATH")
    @TableField("PC_PATH")
    private String pcPath;

    /**
     * 备注说明
     */
    @Column(name = "REMARK")
    @TableField("REMARK")
    private String remark;

}
