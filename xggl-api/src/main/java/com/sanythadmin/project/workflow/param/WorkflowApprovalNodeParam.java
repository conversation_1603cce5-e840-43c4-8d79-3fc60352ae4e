package com.sanythadmin.project.workflow.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单申请审批节点查询参数
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowApprovalNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 工作流程ID
     */
    private String workflowId;

    /**
     * 审批节点ID
     */
    private String nodeId;

    /**
     * 审批时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 关联申请表记录ID
     */
    private String applicationId;

    /**
     * 下级节点审批结果
     */
    private String nextNodeResult;

    /**
     * 上级节点审批结果
     */
    private String preNodeResult;

    /**
     * 审批结果
     */
    private String result;

    /**
     * 是否结束节点
     */
    private Long endNode;

    /**
     * 是否开始节点
     */
    private Long startNode;

    /**
     * 审核阶段（初审, 复审）
     */
    @QueryField(type = QueryType.EQ)
    private Integer stage;

    /**
     * 会签，或签
     */
    @QueryField(type = QueryType.EQ)
    private Integer reviewType;

}
