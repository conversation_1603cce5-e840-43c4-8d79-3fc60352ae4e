<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.ID, a.NODE_NAME, a.WORKFLOW_ID, a.SORT, a.START_TIME, a.END_TIME, a.REVIEW_TYPE, a.TYPE,a.CONDITION_ID,a.CONDITION_NAME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.nodeName != null">
            AND a.NODE_NAME LIKE concat(concat('%',#{param.nodeName), '%')
        </if>
        <if test="param.workflowId != null">
            AND a.WOR<PERSON>FLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
        </if>
        <if test="param.startTime != null">
            AND a.START_TIME LIKE concat(concat('%',#{param.startTime), '%')
        </if>
        <if test="param.endTime != null">
            AND a.END_TIME LIKE concat(concat('%',#{param.endTime), '%')
        </if>
    </sql>

    <select id="nodeListByFormInfoAndApprover" resultType="com.sanythadmin.project.workflow.entity.WorkflowNode">
        select
        <include refid="Base_Column_List"/>
        from syt_workflow_node a where a.type = 0 and a.id in (
        select node_id from syt_workflow_node_approver where workflow_id in
        (select id from syt_workflow where (project_Id = #{projectId} or module_code = #{projectId})and year = #{year}) and approver_id in
        <if test="approverId != null">
            <foreach item="item" index="index" collection="approverId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) order by a.sort asc
    </select>

    <select id="nodeListByApprover" resultType="com.sanythadmin.project.workflow.entity.WorkflowNode">
        select <include refid="Base_Column_List"/> from syt_workflow_node a where a.type = 0 and a.id in (
        select node_id from syt_workflow_node_approver where workflow_id in
        (<choose>
            <when test="param.id != null">
                #{param.id}
            </when>
            <when test="param.projectId != null and param.year != null">
                SELECT ID FROM SYT_WORKFLOW WHERE PROJECT_ID = #{param.projectId} AND YEAR = #{param.year}
            </when>
            <when test="param.moduleCode != null and param.year != null">
                SELECT ID FROM SYT_WORKFLOW WHERE MODULE_CODE = #{param.moduleCode} AND YEAR = #{param.year}
            </when>
        </choose>) and approver_id in
        <if test="approverId != null">
            <foreach item="item" index="index" collection="approverId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) order by a.sort asc
    </select>
</mapper>
