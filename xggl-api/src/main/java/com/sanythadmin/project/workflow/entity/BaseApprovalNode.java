package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.workflow.enums.ReturnWay;
import com.sanythadmin.project.workflow.enums.ReviewStage;
import com.sanythadmin.project.workflow.enums.ReviewType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Created by JIANGPING on 2024/11/27.
 */
@Data
@MappedSuperclass
public class BaseApprovalNode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 节点名称
     */
    @Column(name = "NODE_NAME")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 工作流程ID
     */
    @Column(name = "WORKFLOW_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 审批节点ID
     */
    @Column(name = "NODE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("NODE_ID")
    private String nodeId;

    /**
     * 审批时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField(value = "UPDATE_TIME", jdbcType = JdbcType.DATE, updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime updateTime;

    /**
     * 关联申请表记录ID
     */
    @Column(name = "APPLICATION_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("APPLICATION_ID")
    private String applicationId;

    /**
     * 下级节点审批结果
     */
    @Column(name = "NEXT_NODE_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "NEXT_NODE_RESULT", jdbcType = JdbcType.VARCHAR, updateStrategy = FieldStrategy.ALWAYS)
    private String nextNodeResult;

    /**
     * 上级节点审批结果
     */
    @Column(name = "PRE_NODE_RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField(value = "PRE_NODE_RESULT", jdbcType = JdbcType.VARCHAR, updateStrategy = FieldStrategy.ALWAYS)
    private String preNodeResult;

    /**
     * 审批结果
     */
    @Column(name = "RESULT", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("RESULT")
    private String result;

    /**
     * 开始节点
     */
    @Column(name = "START_NODE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("START_NODE")
    private JudgeMark startNode;

    /**
     * 结束节点
     */
    @Column(name = "END_NODE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("END_NODE")
    private JudgeMark endNode;

    /**
     * 节点顺序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 审批阶段(0: 初审, 1: 复审)
     */
    @Column(name = "STAGE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("STAGE")
    private ReviewStage stage;

    /**
     * 审批类型（0：会签，1：或签）
     */
    @Column(name = "REVIEW_TYPE")
    @TableField("REVIEW_TYPE")
    private ReviewType reviewType;

    /**
     * 退回方式（上级节点, 申请人）
     */
    @Column(name = "RETURN_WAY", columnDefinition = ColumnType.NUMBER_1)
    @TableField("RETURN_WAY")
    private ReturnWay returnWay;
}
