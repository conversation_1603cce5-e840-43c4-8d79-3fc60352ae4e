package com.sanythadmin.project.workflow.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
public enum ApproverType implements ValueTextBaseEnum {

    role(0, "角色"),
    user(1, "用户");
    @JsonValue
    @EnumValue
    private Integer value;
    private String text;

    ApproverType(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
