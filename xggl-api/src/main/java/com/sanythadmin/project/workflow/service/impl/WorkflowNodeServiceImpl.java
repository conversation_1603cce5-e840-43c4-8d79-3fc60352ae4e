package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.NodeType;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeMapper;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 工作流节点Service实现
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Service
public class WorkflowNodeServiceImpl extends ServiceImpl<WorkflowNodeMapper, WorkflowNode> implements WorkflowNodeService {
    @Resource
    private WorkflowNodeMapper workflowNodeMapper;
    @Resource
    private WorkflowService workflowService;

    @Override
    public List<WorkflowNode> nodeListByFormInfoAndApprover(String projectId, String year, String approverId) {
        return workflowNodeMapper.nodeListByFormInfoAndApprover(projectId, year, approverId);
    }

    @Override
    public List<WorkflowNode> nodeListByApprover(WorkflowParam param, String approverId) {
        return workflowNodeMapper.nodeListByApprover(param, approverId);
    }

    @Override
    public List<WorkflowNode> listByWorkflow(String workflowId) {
        return workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>()
                .eq(WorkflowNode::getWorkflowId, workflowId)
                .eq(WorkflowNode::getType, NodeType.JieDian)
                .orderByAsc(WorkflowNode::getSort));
    }

    @Override
    public List<WorkflowNode> getAllNode(WorkflowParam param) {
        List<Workflow> list = workflowService.list(param);
        if (CollectionUtils.isEmpty(list))
            return null;
        return workflowNodeMapper.selectList(new LambdaQueryWrapper<WorkflowNode>()
                .in(WorkflowNode::getWorkflowId, list.stream().map(Workflow::getId).toList())
                .eq(WorkflowNode::getType, NodeType.JieDian)
                .orderByAsc(WorkflowNode::getSort));
    }

    @Transactional
    @Override
    public void updateNodeApprovalTime(List<WorkflowNode> nodes) {
        for (WorkflowNode node : nodes) {
            workflowNodeMapper.update(new LambdaUpdateWrapper<WorkflowNode>()
                    .set(WorkflowNode::getStartTime, node.getStartTime())
                    .set(WorkflowNode::getEndTime, node.getEndTime())
                    .eq(WorkflowNode::getId, node.getId()));
        }
    }
}
