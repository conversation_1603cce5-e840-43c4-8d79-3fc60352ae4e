package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowConditionDetailMapper;
import com.sanythadmin.project.workflow.service.WorkflowConditionDetailService;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 工作流条件详情Service实现
 *
 * <AUTHOR>
 * @since 2024-07-25 16:04:50
 */
@Service
public class WorkflowConditionDetailServiceImpl extends ServiceImpl<WorkflowConditionDetailMapper, WorkflowConditionDetail> implements WorkflowConditionDetailService {

    @Resource
    private WorkflowConditionDetailMapper mapper;

    @Override
    public List<WorkflowConditionDetail> listByWorkflow(String workflowId) {
        return mapper.selectList(new LambdaQueryWrapper<WorkflowConditionDetail>()
                .eq(WorkflowConditionDetail::getWorkflowId, workflowId));
    }
}
