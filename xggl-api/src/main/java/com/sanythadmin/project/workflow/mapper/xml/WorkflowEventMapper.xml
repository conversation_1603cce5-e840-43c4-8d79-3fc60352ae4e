<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowEventMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, EVENT_CLASS_NAME, EVENT_NAME, EVENT_TYPE, SET_EXPRESSION, WHERE_EXPRESSION, MESSAGE, SET_TABLE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.eventClassName != null">
                        AND a.EVENT_CLASS_NAME LIKE concat(concat('%',#{param.eventClassName), '%')
                    </if>
                    <if test="param.eventName != null">
                        AND a.EVENT_NAME LIKE concat(concat('%',#{param.eventName), '%')
                    </if>
                    <if test="param.eventType != null">
                        AND a.EVENT_TYPE LIKE concat(concat('%',#{param.eventType), '%')
                    </if>
                    <if test="param.setExpression != null">
                        AND a.SET_EXPRESSION LIKE concat(concat('%',#{param.setExpression), '%')
                    </if>
                    <if test="param.whereExpression != null">
                        AND a.WHERE_EXPRESSION LIKE concat(concat('%',#{param.whereExpression), '%')
                    </if>
                    <if test="param.message != null">
                        AND a.MESSAGE LIKE concat(concat('%',#{param.message), '%')
                    </if>
                    <if test="param.setTable != null">
                        AND a.SET_TABLE LIKE concat(concat('%',#{param.setTable), '%')
                    </if>
    </sql>

    <update id="updateByExpression" parameterType="com.sanythadmin.project.workflow.observer.UpdateEventParam">
        UPDATE ${param.setTable} set ${param.setExpression} where ${param.whereExpression}
    </update>
</mapper>
