package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeStateMapper;
import com.sanythadmin.project.workflow.service.WorkflowNodeStateService;
import com.sanythadmin.project.workflow.entity.WorkflowNodeState;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 工作流节点状态Service实现
 *
 * <AUTHOR>
 * @since 2024-07-11 10:35:32
 */
@Service
public class WorkflowNodeStateServiceImpl extends ServiceImpl<WorkflowNodeStateMapper, WorkflowNodeState> implements WorkflowNodeStateService {

    @Resource
    private WorkflowNodeStateMapper mapper;

}
