package com.sanythadmin.project.workflow.observer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.entity.WorkflowNodeState;
import com.sanythadmin.project.workflow.mapper.WorkflowEventMapper;
import com.sanythadmin.project.workflow.service.WorkflowNodeStateService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * Created by JIANGPING on 2024/10/10.
 */
@Slf4j
@RequiredArgsConstructor
@Component("workflowNodeEventSubscription")
public class WorkflowNodeEventSubscription {

    private final WorkflowNodeStateService workflowNodeStateService;
    private final WorkflowEventMapper workflowEventMapper;

    // 正则表达式模式，用于匹配可能的SQL注入攻击
    private static final String SQL_INJECTION_PATTERN = "(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\\b(select|update|or|delete|insert|replace|truncate|char|substr|ascii|declare|exec|count|master|into|drop|execute)\\b)";

    // 预编译正则表达式
    private static final Pattern SQL_INJECTION_REGEX = Pattern.compile(SQL_INJECTION_PATTERN, Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    // 验证字符串是否可能含有SQL注入攻击
    public static boolean isSqlInjectionPossible(String input) {
        return SQL_INJECTION_REGEX.matcher(input).find();
    }

    public <T, U extends BaseApprovalNodeRecord> void notify(@NotNull ApprovalData<T, U> approvalData) {
        U record = approvalData.getRecord();
        WorkflowNodeState workflowNodeState = workflowNodeStateService.getOne(new LambdaQueryWrapper<WorkflowNodeState>()
                .eq(WorkflowNodeState::getNodeId, record.getNodeId())
                .eq(WorkflowNodeState::getWorkflowId, record.getWorkflowId()));
        if (Objects.isNull(workflowNodeState)) return;
        notify(workflowNodeState, approvalData);
    }

    public <T, U extends BaseApprovalNodeRecord> void notify(@NotNull WorkflowNodeState nodeState, @NotNull ApprovalData<T, U> approvalData) {
        String eventId = null;
        U record = approvalData.getRecord();
        T applicationInfo = approvalData.getApplicationInfo();
        UserInfo userInfo = approvalData.getUserInfo();
        if (Objects.equals(nodeState.getPresetStatePass(), record.getResult())) {
            eventId = nodeState.getStatePassEventId();
        } else if (Objects.equals(nodeState.getPresetStateReturn(), record.getResult())) {
            eventId = nodeState.getStateReturnEventId();
        } else if (Objects.equals(nodeState.getPresetStateTerminate(), record.getResult())) {
            eventId = nodeState.getStateTerminateEventId();
        }

        if (Objects.isNull(eventId)) return;
        List<WorkflowEvent> list = workflowEventMapper.selectList(new LambdaQueryWrapper<WorkflowEvent>()
                .in(WorkflowEvent::getId, (Object[]) CommonUtil.split(eventId)));
        for (WorkflowEvent event : list) {
            if (Objects.equals(event.getEventType(), ConstantsWorkflow.WORKFLOW_EVENT_UPDATE)) {
                if (!StringUtils.hasText(event.getSetTable()) ||
                        !StringUtils.hasText(event.getSetExpression()) ||
                        !StringUtils.hasText(event.getWhereExpression())) {
                    log.error("WorkflowEvent Parameter is empty: {}", event.getEventName());
                    continue;
                }

                if (isSqlInjectionPossible(event.getSetTable()) ||
                        isSqlInjectionPossible(event.getSetExpression()) ||
                        isSqlInjectionPossible(event.getWhereExpression())) {
                    log.error("WorkflowEvent config error: {}", event.getEventName());
                    continue;
                }

                UpdateEventParam<T, U> param = new UpdateEventParam<>(event.getSetTable(), event.getSetExpression(),
                        event.getWhereExpression(), userInfo, applicationInfo, record);
                workflowEventMapper.updateByExpression(param);
            } else if (Objects.equals(event.getEventType(), ConstantsWorkflow.WORKFLOW_EVENT_MESSAGE)) {
                WorkflowNodeEventObserver observer = SpringContextUtil.getBean(event.getEventClassName(), WorkflowNodeEventObserver.class);
                observer.process(approvalData, event);
            } else if (Objects.equals(event.getEventType(), ConstantsWorkflow.WORKFLOW_EVENT_OTHER)) {
                WorkflowNodeEventObserver observer = SpringContextUtil.getBean(event.getEventClassName(), WorkflowNodeEventObserver.class);
                observer.process(approvalData, event);
            }
        }
    }
}
