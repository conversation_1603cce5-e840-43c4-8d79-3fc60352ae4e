package com.sanythadmin.project.workflow.dto;

import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import lombok.Data;

import java.util.List;

/**
 * Created by JIANGPING on 2024/8/30.
 */
@Data
public class ApprovalNodeInfo<T extends BaseApprovalNode, U extends BaseApprovalNodeRecord> {
    /**
     * 审核节点
     */
    private T approvalNode;
    /**
     * 审核节点表单
     */
    private List<WorkflowNodeForm> nodeForms;
    /**
     * 审核节点审核记录
     */
    private List<U> approvalNodeRecords;
}
