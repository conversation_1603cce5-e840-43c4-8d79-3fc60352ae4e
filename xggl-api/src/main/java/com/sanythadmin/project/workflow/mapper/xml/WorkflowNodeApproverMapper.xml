<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NODE_ID, APPROVER_ID, APPROVER_TYPE, WORKFLOW_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.nodeId != null">
            AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
        </if>
        <if test="param.approverId != null">
            AND a.APPROVER_ID LIKE concat(concat('%',#{param.approverId), '%')
        </if>
    </sql>

    <select id="nodeListByFormInfoAndApprover" resultType="com.sanythadmin.project.workflow.entity.WorkflowNodeApprover">
        select <include refid="Base_Column_List"/>
        from syt_workflow_node_approver
        where workflow_id in (select id from syt_workflow where (project_Id = #{projectId} or MODULE_CODE = #{projectId}) and year = #{year}) and approver_id in
        <if test="approverId != null">
            <foreach item="item" index="index" collection="approverId.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="nodeApproverList" resultType="com.sanythadmin.project.workflow.entity.WorkflowNodeApprover">
        select
        <include refid="Base_Column_List"/>
        from SYT_WORKFLOW_NODE_APPROVER
        where WORKFLOW_ID in (
        <choose>
            <when test="param.id != null">
                #{param.id}
            </when>
            <when test="param.projectId != null and param.year != null">
                SELECT ID FROM SYT_WORKFLOW WHERE PROJECT_ID = #{param.projectId} AND YEAR = #{param.year}
            </when>
            <when test="param.moduleCode != null and param.year != null">
                SELECT ID FROM SYT_WORKFLOW WHERE MODULE_CODE = #{param.moduleCode} AND YEAR = #{param.year}
            </when>
        </choose>
        ) and APPROVER_ID in
        <if test="approverId != null">
            <foreach item="item" index="index" collection="approverId.split(',')" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
