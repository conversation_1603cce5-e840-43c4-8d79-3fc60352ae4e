package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW")
@Entity
@Table(name = "SYT_WORKFLOW")
public class Workflow implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 流程名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 项目ID
     */
    @Column(name = "PROJECT_ID")
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 年份
     */
    @Column(name = "YEAR")
    @TableField("YEAR")
    private String year;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 模块编码
     */
    @Column(name = "MODULE_CODE",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("MODULE_CODE")
    private String moduleCode;
}
