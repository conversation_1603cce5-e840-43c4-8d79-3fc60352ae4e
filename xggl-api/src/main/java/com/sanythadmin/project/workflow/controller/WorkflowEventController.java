package com.sanythadmin.project.workflow.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.workflow.service.WorkflowEventService;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;
import com.sanythadmin.project.workflow.param.WorkflowEventParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 工作流管理模块/事件配置控制器
 *
 * <AUTHOR>
 * @since 2024-12-25 10:57:15
 */
@RestController
@RequestMapping("/api/workflow/workflow-event")
public class WorkflowEventController extends BaseController {
    @Resource
    private WorkflowEventService workflowEventService;

    /**
     * 分页查询工作流管理模块事件配置（权限标识：workflow:workflowEvent:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflowEvent:list')")
    @GetMapping("/page")
    public PageResult<WorkflowEvent> page(WorkflowEventParam param) {
        PageParam<WorkflowEvent, WorkflowEventParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = workflowEventService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部工作流管理模块事件配置（权限标识：workflow:workflowEvent:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflowEvent:list')")
    @GetMapping()
    public List<WorkflowEvent> list(WorkflowEventParam param) {
        PageParam<WorkflowEvent, WorkflowEventParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return workflowEventService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询工作流管理模块事件配置（权限标识：workflow:workflowEvent:list）
     */
    @PreAuthorize("hasAuthority('workflow:workflowEvent:list')")
    @GetMapping("/{id}")
    public WorkflowEvent get(@PathVariable("id") String id) {
        return workflowEventService.getById(id);
    }

    /**
     * 添加或修改工作流管理模块事件配置（权限标识：workflow:workflowEvent:operation）
     */
    @PreAuthorize("hasAuthority('workflow:workflowEvent:operation')")
    @OperationLog(module = "工作流管理模块事件配置", comments = "保存工作流管理模块事件配置")
    @PostMapping("/operation")
    public void save(@RequestBody WorkflowEvent workflowEvent) {
        if (StringUtils.hasLength(workflowEvent.getId())) {
            workflowEventService.updateById(workflowEvent);
        } else {
            workflowEventService.save(workflowEvent);
        }
    }

    /**
     * 批量删除工作流管理模块事件配置（权限标识：workflow:workflowEvent:remove）
     */
    @PreAuthorize("hasAuthority('workflow:workflowEvent:remove')")
    @OperationLog(module = "工作流管理模块事件配置", comments = "批量删除工作流管理模块事件配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        workflowEventService.removeByIds(ids);
    }
}
