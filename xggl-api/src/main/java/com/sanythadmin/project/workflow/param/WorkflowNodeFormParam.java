package com.sanythadmin.project.workflow.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 工作流节点审批表单查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowNodeFormParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark required;

    /**
     * 控件类型
     */
    private String controlType;

    /**
     * 工作流ID
     */
    @QueryField(type = QueryType.EQ)
    private String workflowId;

    /**
     * 审批节点ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String nodeId;

    /**
     * 关联 WorkflowNodeApprover 实体 id
     */
    @QueryField(type = QueryType.EQ)
    private String nodeApproverId;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }
}
