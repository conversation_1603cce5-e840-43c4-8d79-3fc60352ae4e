package com.sanythadmin.project.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import com.sanythadmin.project.workflow.entity.BaseApprovalNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.param.WorkflowParam;

import java.util.List;

/**
 * 工作流节点审批表单Service
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowNodeFormService extends IService<WorkflowNodeForm> {
    /**
     * 查询节点审核表单信息根据当前用户
     *
     * @param tClass
     * @param applicationId
     * @param param
     * @param nodeId
     * @param <T>
     * @return
     */
    public <T extends BaseApprovalNode> List<WorkflowNodeForm> getNodeFormByUser(Class<T> tClass, String applicationId, WorkflowParam param, String nodeId);

    public <T extends BaseApprovalNode, U extends BaseApplicationInfo> List<WorkflowNodeForm> getNodeFormByUser(Class<T> tClass, Class<U> uClass, String applicationId, WorkflowParam param, String nodeId);
}
