package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.workflow.enums.NodeType;
import com.sanythadmin.project.workflow.enums.ReviewType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流节点
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_NODE")
@Entity
@Table(name = "SYT_WORKFLOW_NODE")
public class WorkflowNode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 节点名称
     */
    @Column(name = "NODE_NAME")
    @TableField("NODE_NAME")
    private String nodeName;

    /**
     * 关联流程ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 审批节点序号
     */
    @Column(name = "SORT", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SORT")
    private Integer sort;

    /**
     * 审批开始时间
     */
    @Column(name = "START_TIME")
    @TableField("START_TIME")
    private LocalDateTime startTime;

    /**
     * 审批截至时间
     */
    @Column(name = "END_TIME")
    @TableField("END_TIME")
    private LocalDateTime endTime;

    /**
     * 审批类型（0：会签，1：或签）
     */
    @Column(name = "REVIEW_TYPE")
    @TableField("REVIEW_TYPE")
    private ReviewType reviewType;

    /**
     * 条件名称
     */
    @Column(name = "CONDITION_NAME")
    @TableField("CONDITION_NAME")
    private String conditionName;

    /**
     * 条件ID
     */
    @Column(name = "CONDITION_ID")
    @TableField("CONDITION_ID")
    private String conditionId;

    /**
     * 前端回显类型（0：节点, 1：抄送人, 2：条件, 3：路由）
     */
    @Column(name = "type", columnDefinition = ColumnType.NUMBER_1)
    @TableField("type")
    private NodeType type;
    @Column(name = "settype", columnDefinition = ColumnType.NUMBER_1)
    @TableField("settype")
    private Integer settype;

    /**
     * 父节点
     */
    @Column(name = "PARENT_NODE")
    @TableField("PARENT_NODE")
    private String parentNode;

    /**
     * 节点审批时间是否正确
     *
     * @return
     */
    public boolean approvalTimeIsCorrect() {
        return DateUtil.localDateTimeIsCorrect(startTime, endTime);
    }
}
