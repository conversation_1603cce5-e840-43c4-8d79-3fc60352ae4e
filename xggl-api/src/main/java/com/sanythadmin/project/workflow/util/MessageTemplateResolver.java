package com.sanythadmin.project.workflow.util;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息模板变量解析器
 * 支持将模板变量（如${申请人姓名}）替换为实际值
 */
@Slf4j
@Component
@AllArgsConstructor
public class MessageTemplateResolver {


    /**
     * 解析消息模板，替换变量为实际值
     *
     * @param template 消息模板
     * @param approvalData 审批数据
     * @return 解析后的消息内容
     */
    public <T, U extends BaseApprovalNodeRecord> String resolveTemplate(String template, ApprovalData<T, U> approvalData) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }
        Map<String, String> variables = buildVariableMap(approvalData);
        return CommonUtil.replaceVariables(template, variables);
    }

    private <T, U extends BaseApprovalNodeRecord> Map<String, String> buildVariableMap(ApprovalData<T, U> approvalData) {
        Map<String, String> variables = new HashMap<>();
        try {
            buildApplicationVariables(variables, approvalData);
            buildApprovalVariables(variables, approvalData);
        } catch (Exception e) {
            log.error("构建变量映射表时发生错误", e);
        }
        return variables;
    }

    /**
     * 构建申请相关变量
     */
    private <T, U extends BaseApprovalNodeRecord> void buildApplicationVariables(Map<String, String> variables, ApprovalData<T, U> approvalData) {
        try {
            // 申请人信息
            if (approvalData.getUserInfo() != null) {
                variables.put("申请人姓名", CommonUtil.getStringValue(approvalData.getUserInfo().getXm()));
                variables.put("申请人工号", CommonUtil.getStringValue(approvalData.getUserInfo().getXgh()));
                variables.put("申请人学院", CommonUtil.getStringValue(approvalData.getUserInfo().getXymc()));
                variables.put("申请人专业", CommonUtil.getStringValue(approvalData.getUserInfo().getZymc()));
                variables.put("申请人班级", CommonUtil.getStringValue(approvalData.getUserInfo().getBjmc()));
            }
            // 申请基本信息
            if (approvalData.getApplicationInfo() != null) {
                Object appData = approvalData.getApplicationInfo();
                variables.put("项目名称", CommonUtil.getFieldValue(appData, "xmmc", "项目名称"));
            }
            
        } catch (Exception e) {
            log.warn("构建申请相关变量时发生错误", e);
        }
    }

    /**
     * 构建审批相关变量
     */
    private <T, U extends BaseApprovalNodeRecord> void buildApprovalVariables(Map<String, String> variables, ApprovalData<T, U> approvalData) {
        try {
            // 当前审批节点记录信息
            if (approvalData.getRecord() != null) {
                U currentRecord = approvalData.getRecord();
                variables.put("当前审批人", CommonUtil.getStringValue(currentRecord.getRealName()));
                variables.put("审批状态", CommonUtil.getStringValue(currentRecord.getResult()));
            }
            
            // 下一审批人信息
            if (approvalData.getNextNodeApprovers() != null && !approvalData.getNextNodeApprovers().isEmpty()) {
                variables.put("下一审批人", CommonUtil.getStringValue(approvalData.getNextNodeApprovers().get(0).getApproverId()));
            } else {
                variables.put("下一审批人", "无");
            }
            if (approvalData.getUserInfo() != null) {
                variables.put("当前审批人学院", CommonUtil.getStringValue(approvalData.getUserInfo().getXymc()));
            }
            
        } catch (Exception e) {
            log.warn("构建审批相关变量时发生错误", e);
        }
    }

}