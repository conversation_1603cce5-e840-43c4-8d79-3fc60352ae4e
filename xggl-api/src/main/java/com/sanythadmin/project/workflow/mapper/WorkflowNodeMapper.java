package com.sanythadmin.project.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作流节点Mapper
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
public interface WorkflowNodeMapper extends BaseMapper<WorkflowNode> {

    public List<WorkflowNode> nodeListByFormInfoAndApprover(@Param("projectId") String projectId, @Param("year") String year, @Param("approverId") String approverId);

    public List<WorkflowNode> nodeListByApprover(@Param("param") WorkflowParam param, @Param("approverId") String approverId);
}
