package com.sanythadmin.project.workflow.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工作流查询参数
 *
 * <AUTHOR>
 * @since 2024-06-26 09:26:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkflowParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 流程ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 流程名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String projectId;

    @QueryField(type = QueryType.IS_NULL, value = "projectId")
    private Boolean projectIdIsNull;

    /**
     * 年份
     */
    @QueryField(type = QueryType.EQ)
    private String year;

    /**
     * 模块编码
     */
    @QueryField(type = QueryType.EQ)
    private String moduleCode;

    @QueryField(type = QueryType.IS_NULL, value = "moduleCode")
    private Boolean moduleCodeIsNull;

    @Override
    public String getSort() {
        return "create_time asc";
    }

    public WorkflowParam() {
    }

    public WorkflowParam(String id) {
        this.id = id;
    }

    public WorkflowParam(String projectId, String year, String moduleCode) {
        this.projectId = projectId;
        this.year = year;
        this.moduleCode = moduleCode;
    }
}
