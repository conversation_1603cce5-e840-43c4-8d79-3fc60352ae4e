package com.sanythadmin.project.workflow.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.workflow.mapper.WorkflowApprovalNodeRecordMapper;
import com.sanythadmin.project.workflow.service.WorkflowApprovalNodeRecordService;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 表单审批记录Service实现
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Service
public class WorkflowApprovalNodeRecordServiceImpl extends ServiceImpl<WorkflowApprovalNodeRecordMapper, WorkflowApprovalNodeRecord> implements WorkflowApprovalNodeRecordService {

    @Resource
    private WorkflowApprovalNodeRecordMapper mapper;

}
