package com.sanythadmin.project.workflow.observer;

import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowEvent;

/**
 * 工作流节点事件处理
 * Created by JIANGPING on 2024/10/10.
 */
public interface WorkflowNodeEventObserver {
    /**
     * 事件处理逻辑
     *
     * @param approvalData 申请信息/审核记录/申请用户信息
     * @param event        事件
     * @param <T>
     * @param <U>
     */
    public <T, U extends BaseApprovalNodeRecord> void process(ApprovalData<T, U> approvalData, WorkflowEvent event);
}
