package com.sanythadmin.project.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 表单审批记录
 *
 * <AUTHOR>
 * @since 2025-01-16 17:18:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_WORKFLOW_APPROVAL_NODE_RECORD")
@Entity
@Table(name = "SYT_WORKFLOW_APPROVAL_NODE_RECORD")
public class WorkflowApprovalNodeRecord extends BaseApprovalNodeRecord {
    private static final long serialVersionUID = 1L;


}
