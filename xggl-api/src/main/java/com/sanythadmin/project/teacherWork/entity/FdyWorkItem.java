package com.sanythadmin.project.teacherWork.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 辅导员工作手册项
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDY_WORK_ITEM")
@Entity
@Table(name = "SYT_FDY_WORK_ITEM")
public class FdyWorkItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 项目名称
     */
    @Column(name = "NAME", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("NAME")
    private String name;

    /**
     * 业务类型
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    /**
     * 排序
     */
    @Column(name = "SORT_NUMBER")
    @TableField("SORT_NUMBER")
    private Long sortNumber;

    /**
     * 月工作量/完成记录
     */
    @Column(name = "MONTH_WORKLOAD")
    @TableField("MONTH_WORKLOAD")
    private Long monthWorkload;

    /**
     * 未完成消息提醒(否：0,是：1)
     */
    @Column(name = "UNFINISHED_REMIND", columnDefinition = ColumnType.NUMBER_1)
    @TableField("UNFINISHED_REMIND")
    private JudgeMark unfinishedRemind;

    /**
     * 简介
     */
    @Column(name = "REMARK", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("REMARK")
    private String remark;

    /**
     * 单次完成记分
     */
    @Column(name = "POINT", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("POINT")
    private Double point;

    public String getType() {
        return StringUtils.hasText(type)? type.toUpperCase() : type;
    }
}
