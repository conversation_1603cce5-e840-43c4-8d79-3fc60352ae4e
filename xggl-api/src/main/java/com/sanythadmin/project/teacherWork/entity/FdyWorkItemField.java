package com.sanythadmin.project.teacherWork.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 辅导员工作手册项表单设置
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDY_WORK_ITEM_FIELD")
@Entity
@Table(name = "SYT_FDY_WORK_ITEM_FIELD")
public class FdyWorkItemField implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 字段中文名
     */
    @Column(name = "FIELD_ZH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("FIELD_ZH")
    private String fieldZh;

    /**
     * 字段英文名
     */
    @Column(name = "FIELD_EN", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("FIELD_EN")
    private String fieldEn;

    /**
     * 正则验证表达式
     */
    @Column(name = "REG_EXPRESSION", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("REG_EXPRESSION")
    private String regExpression;

    /**
     * 排序值
     */
    @Column(name = "SORT_NUMBER")
    @TableField("SORT_NUMBER")
    private Long sortNumber;

    /**
     * 下拉控件加载数据URL ID
     */
    @Column(name = "LOAD_DATA_URL_ID")
    @TableField("LOAD_DATA_URL_ID")
    private String loadDataUrlId;

    /**
     * 加载数据URL
     */
    @Column(name = "LOAD_DATA_URL")
    @TableField("LOAD_DATA_URL")
    private String loadDataUrl;

    /**
     * value字段名
     */
    @Column(name = "VALUE_FIELD")
    @TableField("VALUE_FIELD")
    private String valueField;

    /**
     * text字段名
     */
    @Column(name = "TEXT_FIELD")
    @TableField("TEXT_FIELD")
    private String textField;

    /**
     * 提示文本
     */
    @Column(name = "TIP_TEXT")
    @TableField("TIP_TEXT")
    private String tipText;

    /**
     * 加载数据类型（来自公共码表code）
     */
    @Column(name = "LOAD_DATA_TYPE")
    @TableField("LOAD_DATA_TYPE")
    private String loadDataType;

    /**
     * 控件类型（短文本, 单选/多选，文本域, 日期, 小数，整数，附件等）
     */
    @Column(name = "CONTROL_TYPE")
    @TableField("CONTROL_TYPE")
    private String controlType;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @Column(name = "REQUIRED", columnDefinition = ColumnType.NUMBER_1)
    @TableField("REQUIRED")
    private JudgeMark required;

    /**
     * 工作手册项目ID
     */
    @Column(name = "ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 列表头显示
     */
    @Column(name = "LIST_SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("LIST_SHOW_FLAG")
    private JudgeMark listShowFlag;

    /**
     * 查询条件显示
     */
    @Column(name = "QUERY_SHOW_FLAG", columnDefinition = ColumnType.NUMBER_1)
    @TableField("QUERY_SHOW_FLAG")
    private JudgeMark queryShowFlag;

    /**
     * 业务类型
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    public String getType() {
        return StringUtils.hasText(type)? type.toUpperCase() : type;
    }
}
