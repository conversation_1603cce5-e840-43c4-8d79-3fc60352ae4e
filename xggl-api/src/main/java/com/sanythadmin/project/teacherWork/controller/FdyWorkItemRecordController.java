package com.sanythadmin.project.teacherWork.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordDTO;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordQueryParam;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemRecordService;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemRecord;
import com.sanythadmin.project.teacherWork.param.FdyWorkItemRecordParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 辅导员工作手册/辅导员工作记录控制器
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@RestController
@RequestMapping("/api/teacherWork/fdy-work-item-record")
public class FdyWorkItemRecordController extends BaseController {
    @Resource
    private FdyWorkItemRecordService fdyWorkItemRecordService;

    /**
     * 分页查询全部辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:list')")
    @GetMapping("/page")
    public PageResult<FdyWorkItemRecordDTO> page(FdyWorkItemRecordQueryParam param) {
        return fdyWorkItemRecordService.page(param);
    }

    /**
     * 分页查询辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:list')")
    @GetMapping("/myPage")
    public PageResult<FdyWorkItemRecordDTO> myPage(FdyWorkItemRecordQueryParam param) {
        UserInfoParam teacher = param.getTeacher() == null ? new UserInfoParam() : param.getTeacher();
        teacher.setXgh(getAccount().getUsername());
        param.setTeacher(teacher);
        return fdyWorkItemRecordService.page(param);
    }

    /**
     * 查询全部辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:list')")
    @GetMapping()
    public List<FdyWorkItemRecord> list(FdyWorkItemRecordParam param) {
        PageParam<FdyWorkItemRecord, FdyWorkItemRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdyWorkItemRecordService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:list')")
    @GetMapping("/{id}")
    public FdyWorkItemRecord get(@PathVariable("id") String id) {
        return fdyWorkItemRecordService.getById(id);
    }

    /**
     * 添加或修改辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:operation）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:operation')")
    @OperationLog(module = "辅导员工作记录", comments = "保存辅导员工作记录")
    @PostMapping("/operation")
    public void save(HttpServletRequest request) {
        fdyWorkItemRecordService.operation(request);
    }

    /**
     * 批量删除辅导员工作记录（权限标识：teacherWork:fdyWorkItemRecord:remove）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemRecord:remove')")
    @OperationLog(module = "辅导员工作记录", comments = "批量删除辅导员工作记录")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        fdyWorkItemRecordService.removeByIds(ids);
    }
}
