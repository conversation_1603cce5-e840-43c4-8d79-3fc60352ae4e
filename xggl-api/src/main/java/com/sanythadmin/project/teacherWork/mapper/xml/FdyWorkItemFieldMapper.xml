<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherWork.mapper.FdyWorkItemFieldMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , FIELD_ZH, FIELD_EN, REG_EXPRESSION, SORT_NUMBER, LOAD_DATA_URL_ID, LOAD_DATA_URL, VALUE_FIELD, TEXT_FIELD, TIP_TEXT, LOAD_DATA_TYPE, CONTROL_TYPE, REQUIRED, ITEM_ID, LIST_SHOW_FLAG, QUERY_SHOW_FLAG, TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.fieldZh != null">
            AND a.FIELD_ZH LIKE concat(concat('%',#{param.fieldZh), '%')
        </if>
        <if test="param.fieldEn != null">
            AND a.FIELD_EN LIKE concat(concat('%',#{param.fieldEn), '%')
        </if>
        <if test="param.regExpression != null">
            AND a.REG_EXPRESSION LIKE concat(concat('%',#{param.regExpression), '%')
        </if>
        <if test="param.sortNumber != null">
            AND a.SORT_NUMBER LIKE concat(concat('%',#{param.sortNumber), '%')
        </if>
        <if test="param.loadDataUrlId != null">
            AND a.LOAD_DATA_URL_ID LIKE concat(concat('%',#{param.loadDataUrlId), '%')
        </if>
        <if test="param.loadDataUrl != null">
            AND a.LOAD_DATA_URL LIKE concat(concat('%',#{param.loadDataUrl), '%')
        </if>
        <if test="param.valueField != null">
            AND a.VALUE_FIELD LIKE concat(concat('%',#{param.valueField), '%')
        </if>
        <if test="param.textField != null">
            AND a.TEXT_FIELD LIKE concat(concat('%',#{param.textField), '%')
        </if>
        <if test="param.tipText != null">
            AND a.TIP_TEXT LIKE concat(concat('%',#{param.tipText), '%')
        </if>
        <if test="param.loadDataType != null">
            AND a.LOAD_DATA_TYPE LIKE concat(concat('%',#{param.loadDataType), '%')
        </if>
        <if test="param.controlType != null">
            AND a.CONTROL_TYPE LIKE concat(concat('%',#{param.controlType), '%')
        </if>
        <if test="param.required != null">
            AND a.REQUIRED LIKE concat(concat('%',#{param.required), '%')
        </if>
        <if test="param.itemId != null">
            AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
        </if>
        <if test="param.listShowFlag != null">
            AND a.LIST_SHOW_FLAG = #{param.listShowFlag}
        </if>
        <if test="param.queryShowFlag != null">
            AND a.QUERY_SHOW_FLAG = #{param.queryShowFlag}
        </if>
        <if test="param.type != null">
            AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
        </if>
    </sql>
</mapper>
