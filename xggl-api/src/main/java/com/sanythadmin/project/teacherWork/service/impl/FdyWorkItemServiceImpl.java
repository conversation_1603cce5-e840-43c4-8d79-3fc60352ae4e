package com.sanythadmin.project.teacherWork.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemField;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemRecord;
import com.sanythadmin.project.teacherWork.mapper.FdyWorkItemFieldMapper;
import com.sanythadmin.project.teacherWork.mapper.FdyWorkItemMapper;
import com.sanythadmin.project.teacherWork.mapper.FdyWorkItemRecordMapper;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemService;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItem;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 辅导员工作手册项Service实现
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Service
public class FdyWorkItemServiceImpl extends ServiceImpl<FdyWorkItemMapper, FdyWorkItem> implements FdyWorkItemService {
    @Resource
    private FdyWorkItemMapper mapper;
    @Resource
    private FdyWorkItemFieldMapper fieldMapper;
    @Resource
    private FdyWorkItemRecordMapper recordMapper;

    @Override
    public void removeItem(String id) {
        Long count = recordMapper.selectCount(new LambdaQueryWrapper<FdyWorkItemRecord>()
                .eq(FdyWorkItemRecord::getItemId, id).eq(FdyWorkItemRecord::getSfzc, JudgeMark.NO));
        if (count > 0) AssertUtil.throwMessage("删除失败, 当前项目下已有记录");
        recordMapper.delete(new LambdaQueryWrapper<FdyWorkItemRecord>().eq(FdyWorkItemRecord::getItemId, id));
        fieldMapper.delete(new LambdaQueryWrapper<FdyWorkItemField>().eq(FdyWorkItemField::getItemId, id));
        mapper.deleteById(id);
    }
}
