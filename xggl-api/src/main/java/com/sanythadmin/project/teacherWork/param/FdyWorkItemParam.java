package com.sanythadmin.project.teacherWork.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 辅导员工作手册项查询参数
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdyWorkItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 未完成消息提醒
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark unfinishedRemind;

    public String getType() {
        return StringUtils.hasText(type)? type.toUpperCase() : type;
    }
}
