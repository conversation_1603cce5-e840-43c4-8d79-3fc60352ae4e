package com.sanythadmin.project.teacherWork.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 辅导员工作记录
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_FDY_WORK_ITEM_RECORD")
@Entity
@Table(name = "SYT_FDY_WORK_ITEM_RECORD")
public class FdyWorkItemRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 院系ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 性别（男：1， 女：2）
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 工作项ID
     */
    @Column(name = "ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 业务类型
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 是否暂存（是：1， 否：0）
     */
    @Column(name = "SFZC")
    @TableField("SFZC")
    private JudgeMark sfzc;

    @Column(name = "C1")
    @TableField("C1")
    private String c1;

    @Column(name = "C2")
    @TableField("C2")
    private String c2;

    @Column(name = "C3")
    @TableField("C3")
    private String c3;

    @Column(name = "C4")
    @TableField("C4")
    private String c4;

    @Column(name = "C5")
    @TableField("C5")
    private String c5;

    @Column(name = "C6")
    @TableField("C6")
    private String c6;

    @Column(name = "C7")
    @TableField("C7")
    private String c7;

    @Column(name = "C8")
    @TableField("C8")
    private String c8;

    @Column(name = "C9")
    @TableField("C9")
    private String c9;

    @Column(name = "C10")
    @TableField("C10")
    private String c10;

    @Column(name = "C11")
    @TableField("C11")
    private String c11;

    @Column(name = "C12")
    @TableField("C12")
    private String c12;

    @Column(name = "C13")
    @TableField("C13")
    private String c13;

    @Column(name = "C14")
    @TableField("C14")
    private String c14;

    @Column(name = "C15")
    @TableField("C15")
    private String c15;

    @Column(name = "L1", columnDefinition = ColumnType.CLOB)
    @TableField("L1")
    private String l1;
    @Column(name = "L2", columnDefinition = ColumnType.CLOB)
    @TableField("L2")
    private String l2;
    @Column(name = "L3", columnDefinition = ColumnType.CLOB)
    @TableField("L3")
    private String l3;
    @Column(name = "L4", columnDefinition = ColumnType.CLOB)
    @TableField("L4")
    private String l4;
    @Column(name = "L5", columnDefinition = ColumnType.CLOB)
    @TableField("L5")
    private String l5;

    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : type;
    }
}
