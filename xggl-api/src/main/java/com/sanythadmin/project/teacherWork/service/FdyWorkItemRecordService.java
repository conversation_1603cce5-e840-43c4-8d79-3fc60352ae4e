package com.sanythadmin.project.teacherWork.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemRecord;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordDTO;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordQueryParam;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 辅导员工作记录Service
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
public interface FdyWorkItemRecordService extends IService<FdyWorkItemRecord> {

    public void operation(HttpServletRequest request);

    public PageResult<FdyWorkItemRecordDTO> page(FdyWorkItemRecordQueryParam param);

}
