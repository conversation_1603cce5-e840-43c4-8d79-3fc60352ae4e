package com.sanythadmin.project.teacherWork.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemService;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItem;
import com.sanythadmin.project.teacherWork.param.FdyWorkItemParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 辅导员工作手册/辅导员工作手册项控制器
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@RestController
@RequestMapping("/api/teacherWork/fdy-work-item")
public class FdyWorkItemController extends BaseController {
    @Resource
    private FdyWorkItemService fdyWorkItemService;

    /**
     * 分页查询辅导员工作手册项（权限标识：teacherWork:fdyWorkItem:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItem:list')")
    @GetMapping("/page")
    public PageResult<FdyWorkItem> page(FdyWorkItemParam param) {
        PageParam<FdyWorkItem, FdyWorkItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = fdyWorkItemService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部辅导员工作手册项（权限标识：teacherWork:fdyWorkItem:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItem:list')")
    @GetMapping()
    public List<FdyWorkItem> list(FdyWorkItemParam param) {
        PageParam<FdyWorkItem, FdyWorkItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return fdyWorkItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询辅导员工作手册项（权限标识：teacherWork:fdyWorkItem:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItem:list')")
    @GetMapping("/{id}")
    public FdyWorkItem get(@PathVariable("id") String id) {
        return fdyWorkItemService.getById(id);
    }

    /**
     * 添加或修改辅导员工作手册项（权限标识：teacherWork:fdyWorkItem:operation）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItem:operation')")
    @OperationLog(module = "辅导员工作手册项", comments = "保存辅导员工作手册项")
    @PostMapping("/operation")
    public void save(@RequestBody FdyWorkItem fdyWorkItem) {
        if (StringUtils.hasLength(fdyWorkItem.getId())) {
            fdyWorkItemService.updateById(fdyWorkItem);
        } else {
            fdyWorkItemService.save(fdyWorkItem);
        }
    }

    /**
     * 批量删除辅导员工作手册项（权限标识：teacherWork:fdyWorkItem:remove）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItem:remove')")
    @OperationLog(module = "辅导员工作手册项", comments = "批量删除辅导员工作手册项")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            fdyWorkItemService.removeItem(id);
        }
    }
}
