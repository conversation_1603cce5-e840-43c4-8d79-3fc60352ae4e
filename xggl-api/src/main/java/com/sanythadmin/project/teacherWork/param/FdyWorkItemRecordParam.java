package com.sanythadmin.project.teacherWork.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 辅导员工作记录查询参数
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdyWorkItemRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 工作项ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 创建人
     */
    private String username;

    /**
     * 是否暂存
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzc;

    private String c1;
    private String c2;
    private String c3;
    private String c4;
    private String c5;
    private String c6;
    private String c7;
    private String c8;
    private String c9;
    private String c10;
    private String c11;
    private String c12;
    private String c13;
    private String c14;
    private String c15;

    public String getType() {
        return StringUtils.hasText(type)? type.toUpperCase() : type;
    }
}
