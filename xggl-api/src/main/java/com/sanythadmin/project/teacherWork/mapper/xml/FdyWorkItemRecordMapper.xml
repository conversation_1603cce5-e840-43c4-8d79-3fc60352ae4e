<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherWork.mapper.FdyWorkItemRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , XGH, ITEM_ID, TYPE, CREATE_TIME, USERNAME, TJZT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.xgh != null">
            AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
        </if>
        <if test="param.itemId != null">
            AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
        </if>
        <if test="param.type != null">
            AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
        </if>
        <if test="param.createTime != null">
            AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
        </if>
        <if test="param.username != null">
            AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
        </if>
        <if test="param.tjzt != null">
            AND a.TJZT = #{param.tjzt}
        </if>
    </sql>
</mapper>
