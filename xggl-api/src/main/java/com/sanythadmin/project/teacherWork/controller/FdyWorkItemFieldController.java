package com.sanythadmin.project.teacherWork.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemField;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemRecord;
import com.sanythadmin.project.teacherWork.param.FdyWorkItemFieldParam;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemFieldService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 辅导员工作手册/辅导员工作手册项表单设置控制器
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@RestController
@RequestMapping("/api/teacherWork/fdy-work-item-field")
public class FdyWorkItemFieldController extends BaseController {
    @Resource
    private FdyWorkItemFieldService fdyWorkItemFieldService;


    /**
     * 分页查询辅导员工作手册项表单设置（权限标识：teacherWork:fdyWorkItemField:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemField:list')")
    @GetMapping("/page")
    public PageResult<FdyWorkItemField> page(FdyWorkItemFieldParam param) {
        PageParam<FdyWorkItemField, FdyWorkItemFieldParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = fdyWorkItemFieldService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部辅导员工作手册项表单设置（权限标识：teacherWork:fdyWorkItemField:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemField:list')")
    @GetMapping()
    public List<FdyWorkItemField> list(FdyWorkItemFieldParam param) {
        return fdyWorkItemFieldService.list(param);
    }

    /**
     * 根据id查询辅导员工作手册项表单设置（权限标识：teacherWork:fdyWorkItemField:list）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemField:list')")
    @GetMapping("/{id}")
    public FdyWorkItemField get(@PathVariable("id") String id) {
        return fdyWorkItemFieldService.getById(id);
    }

    /**
     * 添加或修改辅导员工作手册项表单设置（权限标识：teacherWork:fdyWorkItemField:operation）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemField:operation')")
    @OperationLog(module = "辅导员工作手册项表单设置", comments = "保存辅导员工作手册项表单设置")
    @PostMapping("/operation")
    public void save(@RequestBody FdyWorkItemField fdyWorkItemField) {
        if (StringUtils.hasLength(fdyWorkItemField.getId())) {
            fdyWorkItemFieldService.updateById(fdyWorkItemField);
        } else {
            fdyWorkItemFieldService.save(fdyWorkItemField);
        }
    }

    /**
     * 批量删除辅导员工作手册项表单设置（权限标识：teacherWork:fdyWorkItemField:remove）
     */
    @PreAuthorize("hasAuthority('teacherWork:fdyWorkItemField:remove')")
    @OperationLog(module = "辅导员工作手册项表单设置", comments = "批量删除辅导员工作手册项表单设置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        fdyWorkItemFieldService.removeByIds(ids);
    }

    /**
     * 可配置字段列表
     *
     * @return
     */
    @GetMapping("/fieldList")
    public List<String> fieldList(@RequestParam(name = "notUsed", defaultValue = "false", required = false) Boolean notUsed,
                                  FdyWorkItemFieldParam param) {
        List<String> list = new ArrayList<>();
        Class<?> aClass = FdyWorkItemRecord.class;
        String reg = "[cl]\\d";
        Field[] fields = CommonUtil.getAllFields(aClass);
        Pattern pattern = Pattern.compile(reg);
        for (Field field : fields) {
            Matcher matcher = pattern.matcher(field.getName());
            if (matcher.find()) list.add(matcher.group());
        }

        if (notUsed) {
            List<FdyWorkItemField> usedList = fdyWorkItemFieldService.list(param);
            list.removeAll(usedList.stream().map(FdyWorkItemField::getFieldEn).toList());
        }
        return list;
    }
}
