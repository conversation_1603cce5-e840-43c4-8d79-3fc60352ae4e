package com.sanythadmin.project.teacherWork.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemRecord;
import com.sanythadmin.project.teacherWork.mapper.FdyWorkItemRecordMapper;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordDTO;
import com.sanythadmin.project.teacherWork.pojo.FdyWorkItemRecordQueryParam;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemRecordService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 辅导员工作记录Service实现
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Service
public class FdyWorkItemRecordServiceImpl extends ServiceImpl<FdyWorkItemRecordMapper, FdyWorkItemRecord> implements FdyWorkItemRecordService {
    @Resource
    private FdyWorkItemRecordMapper mapper;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Transactional
    @Override
    public void operation(HttpServletRequest request) {
        FormDataWrapper<FdyWorkItemRecord> wrapper = new FormDataWrapper<>(request, FdyWorkItemRecord.class);
        Map<String, Object> paramMap = wrapper.getTextFields();
        FdyWorkItemRecord info = wrapper.getObject();
        List<String> fields = wrapper.getFields();
        if (StringUtils.hasText(info.getId())) {
            FdyWorkItemRecord fdyWorkItemRecord = mapper.selectById(info.getId());
            for (String key : paramMap.keySet()) {
                if (!fields.contains(key)) continue;
                CommonUtil.setValue(fdyWorkItemRecord, key, CommonUtil.getValue(key, info));
            }
            mapper.updateById(fdyWorkItemRecord);
            info = fdyWorkItemRecord;
        } else {
            if (StringUtils.hasText(info.getXgh())) {
                UserInfo userInfo = userInfoMapper.get(info.getXgh());
                if (userInfo == null)
                    AssertUtil.throwMessage("未找到学生信息");
                info.setXb(userInfo.getXb());
                info.setXm(userInfo.getXm());
                info.setXyid(userInfo.getXyid());
                info.setZyid(userInfo.getZyid());
                info.setBjid(userInfo.getBjid());
                info.setNjid(userInfo.getNjid());
            }

            info.setUsername(SecurityUtil.getUsername());
            info.setCreateTime(LocalDateTime.now());
            mapper.insert(info);
        }
        HashMap<String, List<FileInfo>> fileMap = CommonUtil.multipartFileSave(info.getXgh(), info, wrapper.getFileFields(), wrapper.getDeleteFileIds());
        if (!CollectionUtils.isEmpty(fileMap)) {
            UpdateWrapper<FdyWorkItemRecord> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(FdyWorkItemRecord::getId, info.getId());
            for (Map.Entry<String, List<FileInfo>> entry : fileMap.entrySet()) {
                updateWrapper.set(entry.getKey(), JSON.toJSONString(entry.getValue()));
            }
            mapper.update(null, updateWrapper);
        }
    }

    @Override
    public PageResult<FdyWorkItemRecordDTO> page(FdyWorkItemRecordQueryParam param) {
        MyMPJLambdaWrapper<FdyWorkItemRecord, FdyWorkItemRecordQueryParam> wrapper = UserInfoUtil.buildInnerJoinUserInfoWrapper(null, param,
                FdyWorkItemRecord::getUsername, param.getTeacher());
        wrapper.selectAll(FdyWorkItemRecord.class);
        wrapper.selectAssociation(UserInfo.class, FdyWorkItemRecordDTO::getTeacher);
        Page<FdyWorkItemRecordDTO> page = wrapper.pageParam();
        page = mapper.selectJoinPage(page, FdyWorkItemRecordDTO.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
