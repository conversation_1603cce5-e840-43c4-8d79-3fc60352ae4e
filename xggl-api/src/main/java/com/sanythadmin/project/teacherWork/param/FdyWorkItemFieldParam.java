package com.sanythadmin.project.teacherWork.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 辅导员工作手册项表单设置查询参数
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FdyWorkItemFieldParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 字段中文名
     */
    private String fieldZh;

    /**
     * 字段英文名
     */
    private String fieldEn;

    /**
     * 加载数据类型（来自公共码表code）
     */
    private String loadDataType;

    /**
     * 控件类型（短文本, 单选/多选，文本域, 日期, 小数，整数，附件等）
     */
    private String controlType;

    /**
     * 是否必填（1：是 , 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark required;

    /**
     * 工作手册项目ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 列表头显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark listShowFlag;

    /**
     * 查询条件显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark queryShowFlag;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "sortNumber asc";
    }

    public String getType() {
        return StringUtils.hasText(type)? type.toUpperCase() : type;
    }
}
