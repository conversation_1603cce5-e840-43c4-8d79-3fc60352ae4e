package com.sanythadmin.project.teacherWork.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.teacherWork.mapper.FdyWorkItemFieldMapper;
import com.sanythadmin.project.teacherWork.param.FdyWorkItemFieldParam;
import com.sanythadmin.project.teacherWork.service.FdyWorkItemFieldService;
import com.sanythadmin.project.teacherWork.entity.FdyWorkItemField;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 辅导员工作手册项表单设置Service实现
 *
 * <AUTHOR>
 * @since 2025-05-14 17:49:38
 */
@Service
public class FdyWorkItemFieldServiceImpl extends ServiceImpl<FdyWorkItemFieldMapper, FdyWorkItemField> implements FdyWorkItemFieldService {

    @Resource
    private FdyWorkItemFieldMapper mapper;

    @Override
    public List<FdyWorkItemField> list(FdyWorkItemFieldParam param) {
        PageParam<FdyWorkItemField, FdyWorkItemFieldParam> page = new PageParam<>(param);
        return mapper.selectList(page.getOrderWrapper());
    }
}
