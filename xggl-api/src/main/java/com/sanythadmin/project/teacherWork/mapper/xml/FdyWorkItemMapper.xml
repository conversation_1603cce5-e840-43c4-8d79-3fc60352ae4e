<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.teacherWork.mapper.FdyWorkItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , NAME, TYPE, SORT_NUMBER, MONTH_WORKLOAD, UNFINISHED_REMIND, REMARK, POINT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.name != null">
            AND a.NAME LIKE concat(concat('%',#{param.name), '%')
        </if>
        <if test="param.type != null">
            AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
        </if>
        <if test="param.sortNumber != null">
            AND a.SORT_NUMBER LIKE concat(concat('%',#{param.sortNumber), '%')
        </if>
        <if test="param.monthWorkload != null">
            AND a.MONTH_WORKLOAD LIKE concat(concat('%',#{param.monthWorkload), '%')
        </if>
        <if test="param.unfinishedRemind != null">
            AND a.UNFINISHED_REMIND LIKE concat(concat('%',#{param.unfinishedRemind), '%')
        </if>
        <if test="param.remark != null">
            AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
        </if>
        <if test="param.point != null">
            AND a.POINT = #{param.point}
        </if>
    </sql>
</mapper>
