package com.sanythadmin.project.restrict.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.code.entity.CodeXszt;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.code.service.CodeXsztService;
import com.sanythadmin.project.form.entity.FormRestrict;
import com.sanythadmin.project.restrict.service.RestrictCheckService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 限制学生状态属性
 * Created by JIANGPING on 2025/5/28.
 */
@RequiredArgsConstructor
@Service("restrictStudentCategoryService")
public class RestrictStudentCategoryService extends RestrictCheckService {

    private final CodeXsztService codeXsztService;

    @Override
    public String check(UserInfo userInfo, FormRestrict formRestrict) {
        String stateId = userInfo.getRyztid();
        if (StringUtils.hasText(stateId)) {
            CodeXsztParam param = new CodeXsztParam();
            param.setId(stateId);
            List<CodeXszt> list = codeXsztService.list(param);
            CodeXszt codeState = CommonUtil.listGetOne(list);
            if (codeState != null) {
                JSONObject object = JSON.parseObject(formRestrict.getConditionValue());
                boolean flag = false;
                for (String key : object.keySet()) {
                    String value = object.getString(key);
                    if (value != null) {
                        Object value1 = CommonUtil.getValue(key, codeState);
                        if (value1 instanceof ValueTextBaseEnum baseEnum)
                            value1 = baseEnum.getText();
                        if (Objects.equals(value, String.valueOf(value1))) {
                            flag = true;
                            break;
                        }
                    }
                }
                if (flag)
                    return formRestrict.getConditionName();
            }
        }
        return null;
    }
}
