package com.sanythadmin.project.restrict.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.form.entity.FormRestrict;
import com.sanythadmin.project.restrict.service.RestrictCheckService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 限制用户相关信息
 * Created by JIANGPING on 2024/9/9.
 */
@Service("restrictUserInfoService")
public class RestrictUserInfoService extends RestrictCheckService {
    @Override
    public String check(UserInfo userInfo, FormRestrict formRestrict) {
        if (formRestrict != null && StringUtils.hasText(formRestrict.getConditionValue())) {
            Set<String> fieldSet = Arrays.stream(CommonUtil.getAllFields(UserInfo.class))
                    .map(Field::getName).collect(Collectors.toSet());
            JSONObject object = JSON.parseObject(formRestrict.getConditionValue());
            for (String key : object.keySet()) {
                if (!fieldSet.contains(key))
                    continue;
                String text = object.getString(key);
                List<String> list = JSON.isValidArray(text) ? JSON.parseArray(text, String.class)
                        : Collections.singletonList(text);
                Object value = CommonUtil.getValue(key, userInfo);
                if (value == null)
                    return formRestrict.getConditionName();
                if (Objects.equals(QueryType.LIKE.name(), formRestrict.getCondition())
                        || Objects.equals(QueryType.EQ.name(), formRestrict.getCondition())) {
                    if (list.contains((String) value))
                        return formRestrict.getConditionName();
                }
            }
        }
        return null;
    }
}
