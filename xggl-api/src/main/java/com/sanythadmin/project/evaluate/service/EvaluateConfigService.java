package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;

import java.util.List;

/**
 * 综合测评参数配置Service
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
public interface EvaluateConfigService extends IService<EvaluateConfig> {
    public void edit(EvaluateConfigDTO evaluateConfig);
    public void copyOperation(EvaluateConfigDTO evaluateConfig);
    public Long count(EvaluateConfigParam evaluateConfig);
    public List<EvaluateConfig> list(EvaluateConfigParam evaluateConfig);
    public List<EvaluateConfig> getEnabledConfig(UserInfo userInfo, String type);
    public List<EvaluateConfig> getEnabledConfig(String pyccid, String njid, String xyid, String type);
    public void removeConfig(String... id);
}
