package com.sanythadmin.project.evaluate.service;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.workflow.dto.ApprovalData;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import com.sanythadmin.project.workflow.entity.WorkflowNodeForm;
import com.sanythadmin.project.workflow.observer.WorkflowNodeFormEventObserver;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Created by JIANGPING on 2025/4/5.
 */
@Component("applicationItemDetailScoreCheck")
@AllArgsConstructor
public class ApplicationItemDetailScoreCheck implements WorkflowNodeFormEventObserver {
    private final EvaluateApplicationInfoService applicationInfoService;

    @Override
    public <T, U extends BaseApprovalNodeRecord> void process(ApprovalData<T, U> approvalData, WorkflowNodeForm workflowNodeForm) {
        Object value = CommonUtil.getValue(workflowNodeForm.getFieldEn(), approvalData.getRecord());
        EvaluateApplicationInfo applicationInfo = (EvaluateApplicationInfo) approvalData.getApplicationInfo();
        if (value != null && StringUtils.hasText(value.toString())) {
            double fs = Double.parseDouble(value.toString());
            applicationInfoService.checkItemDetailFloatValue(fs, applicationInfo.getItemDetailId());
        }
    }
}
