<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateApprovalNodeRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APPLICATION_ID, APPROVER_ID, BZ1, BZ2, BZ3, BZ4, BZ5, CREATE_TIME, NODE_APPROVER_ID, NODE_ID, REAL_NAME, RESULT, RETURN_WAY, ROLE_ID, STAGE, USERNAME, WORKFLOW_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.applicationId != null">
                        AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
                    </if>
                    <if test="param.approverId != null">
                        AND a.APPROVER_ID LIKE concat(concat('%',#{param.approverId), '%')
                    </if>
                    <if test="param.bz1 != null">
                        AND a.BZ1 LIKE concat(concat('%',#{param.bz1), '%')
                    </if>
                    <if test="param.bz2 != null">
                        AND a.BZ2 LIKE concat(concat('%',#{param.bz2), '%')
                    </if>
                    <if test="param.bz3 != null">
                        AND a.BZ3 LIKE concat(concat('%',#{param.bz3), '%')
                    </if>
                    <if test="param.bz4 != null">
                        AND a.BZ4 LIKE concat(concat('%',#{param.bz4), '%')
                    </if>
                    <if test="param.bz5 != null">
                        AND a.BZ5 LIKE concat(concat('%',#{param.bz5), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.nodeApproverId != null">
                        AND a.NODE_APPROVER_ID LIKE concat(concat('%',#{param.nodeApproverId), '%')
                    </if>
                    <if test="param.nodeId != null">
                        AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
                    </if>
                    <if test="param.realName != null">
                        AND a.REAL_NAME LIKE concat(concat('%',#{param.realName), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.returnWay != null">
                        AND a.RETURN_WAY = #{param.returnWay}
                    </if>
                    <if test="param.roleId != null">
                        AND a.ROLE_ID LIKE concat(concat('%',#{param.roleId), '%')
                    </if>
                    <if test="param.stage != null">
                        AND a.STAGE = #{param.stage}
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
                    </if>
                    <if test="param.workflowId != null">
                        AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
                    </if>
    </sql>
</mapper>
