package com.sanythadmin.project.evaluate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationReferenceInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationReferenceInfoParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationReferenceInfoService;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.NodeType;
import com.sanythadmin.project.workflow.service.WorkflowConditionDetailService;
import com.sanythadmin.project.workflow.service.WorkflowNodeApproverService;
import com.sanythadmin.project.workflow.service.WorkflowNodeService;
import com.sanythadmin.project.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 综合测评/指标加减分参考项控制器
 *
 * <AUTHOR>
 * @since 2025-02-12 14:06:10
 */
@RestController
@RequestMapping("/api/evaluate/v-evaluate-application-reference-info")
public class EvaluateApplicationReferenceInfoController extends BaseController {
    @Resource
    private EvaluateApplicationReferenceInfoService referenceInfoService;
    @Resource
    private EvaluateConfigService configService;
    @Resource
    private WorkflowService workflowService;
    @Resource
    private WorkflowConditionDetailService conditionDetailService;
    @Resource
    private WorkflowNodeApproverService nodeApproverService;
    @Resource
    private WorkflowNodeService workflowNodeService;

    /**
     * 分页查询指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
     */
    @PreAuthorize("hasAuthority('evaluate:vEvaluateApplicationReferenceInfo:list')")
    @GetMapping("/page")
    public PageResult<EvaluateApplicationReferenceInfo> page(EvaluateApplicationReferenceInfoParam param) {
        return referenceInfoService.pageList(param);
    }

    /**
     * 查询全部指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
     */
    @PreAuthorize("hasAuthority('evaluate:vEvaluateApplicationReferenceInfo:list')")
    @GetMapping()
    public List<EvaluateApplicationReferenceInfo> list(EvaluateApplicationReferenceInfoParam param) {
        PageParam<EvaluateApplicationReferenceInfo, EvaluateApplicationReferenceInfoParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return referenceInfoService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
     */
    @PreAuthorize("hasAuthority('evaluate:vEvaluateApplicationReferenceInfo:list')")
    @GetMapping("/{id}")
    public EvaluateApplicationReferenceInfo get(@PathVariable("id") String id) {
        return referenceInfoService.getById(id);
    }

    /**
     * 同步数据至加减分项申请表
     *
     * @param param
     */
    @OperationLog(module = "综合测评管理", comments = "加减分申请参考数据同步")
    @PreAuthorize("hasAuthority('evaluate:vEvaluateApplicationReferenceInfo:operation')")
    @PostMapping("/operation")
    public void operation(EvaluateApplicationReferenceInfoParam param) {
        List<EvaluateConfig> list = configService.list();
        if (CollectionUtils.isEmpty(list) || !StringUtils.hasText(list.get(0).getCpnf()))
            AssertUtil.throwMessage("缺失综测配置信息");
        EvaluateConfig config = list.get(0);
        Workflow workflow = workflowService.getOne(new LambdaQueryWrapper<Workflow>()
                .eq(Workflow::getYear, config.getCpnf())
                .eq(Workflow::getProjectId, config.getId()));
        if (workflow == null) AssertUtil.throwMessage("缺失审核流程配置");
        List<WorkflowNodeApprover> approverList = nodeApproverService.list(new LambdaQueryWrapper<WorkflowNodeApprover>()
                .eq(WorkflowNodeApprover::getWorkflowId, workflow.getId()));
        Map<String, List<WorkflowNodeApprover>> nodeApproverMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(approverList))
            nodeApproverMap = approverList.stream().collect(Collectors.groupingBy(WorkflowNodeApprover::getNodeId));
        List<WorkflowNode> nodes = workflowNodeService.list(new LambdaQueryWrapper<WorkflowNode>()
                .eq(WorkflowNode::getWorkflowId, workflow.getId()).eq(WorkflowNode::getType, NodeType.JieDian)
                .orderByAsc(WorkflowNode::getSort));
        List<WorkflowConditionDetail> conditionDetails = conditionDetailService.list(new LambdaQueryWrapper<WorkflowConditionDetail>()
                .eq(WorkflowConditionDetail::getWorkflowId, workflow.getId()));
        Map<String, List<WorkflowConditionDetail>> conditionMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(conditionDetails))
            conditionMap = conditionDetails.stream().collect(Collectors.groupingBy(WorkflowConditionDetail::getConditionId));
        PageResult<EvaluateApplicationReferenceInfo> pageResult = page(param);
        List<EvaluateApplicationReferenceInfo> referenceInfos = pageResult.getList();
        SysAccount account = SecurityUtil.getAccount();
        for (EvaluateApplicationReferenceInfo referenceInfo : referenceInfos) {
            referenceInfoService.sync(referenceInfo, config, workflow, nodeApproverMap, nodes, conditionMap, account);
        }
    }
}
