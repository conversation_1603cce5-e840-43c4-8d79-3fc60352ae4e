package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.dto.ApplicationInfoImportDTO;
import com.sanythadmin.project.evaluate.dto.BatchApprovalMessage;
import com.sanythadmin.project.evaluate.dto.EvaluateQueryDTO;
import com.sanythadmin.project.evaluate.entity.*;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemParam;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationInfoService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailService;
import com.sanythadmin.project.evaluate.service.EvaluateItemService;
import com.sanythadmin.project.evaluate.vo.EvaluateApplicationInfoVO;
import com.sanythadmin.project.evaluate.vo.UserItemScoreVO;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.dto.WorkflowNodeStateDTO;
import com.sanythadmin.project.workflow.entity.*;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 综合测评/加减分项审核管理
 * Created by JIANGPING on 2024/12/16.
 */
@Slf4j
@RestController
@RequestMapping("/api/evaluate/approval")
@RequiredArgsConstructor
public class EvaluateApprovalController extends BaseController {
    private final EvaluateItemService itemService;
    private final EvaluateItemDetailService itemDetailService;
    private final EvaluateConfigService configService;
    private final EvaluateApplicationInfoService applicationInfoService;
    private final WorkflowService workflowService;
    private final WorkflowNodeService workflowNodeService;
    private final PersonInfoFactory personInfoFactory;
    private final WorkflowConditionDetailService conditionDetailService;
    private final WorkflowNodeApproverService nodeApproverService;
    private final WorkflowNodeFormService workflowNodeFormService;

    /**
     * 综测年份
     *
     * @return
     */
    @GetMapping("/yearList")
    public List<Map<String, Object>> yearList() {
        return applicationInfoService.distinct(EvaluateApplicationInfo::getCpnf);
    }


    /**
     * 分页查询综合测评参数配置（权限标识：evaluate:approval:list）
     */
    @PreAuthorize("hasAuthority('evaluate:approval:list')")
    @GetMapping("/pageConfig")
    public PageResult<EvaluateConfig> pageConfig(EvaluateConfigParam param) {
        param.setState(State.ENABLED);
        PageParam<EvaluateConfig, EvaluateConfigParam> page = new PageParam<>(param);
        page = configService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 当前用户审批节点
     *
     * @param configId 参数配置ID
     * @param cpnf     测评年份
     * @return
     */
    @GetMapping("/approvalNodeList")
    public List<WorkflowNode> approvalNodeList(String configId, String cpnf) {
        return workflowNodeService.nodeListByFormInfoAndApprover(configId
                , cpnf, CommonUtil.appendComma(SecurityUtil.getUsername(), SecurityUtil.getRoleId()));
    }

    /**
     * 工作流审核流程
     *
     * @return
     */
    @GetMapping("/workflow")
    public WorkflowDTO workflow(String workflowId) {
        return workflowService.get(workflowId);
    }

    /**
     * 加减分项审批列表（权限标识：evaluate:approval:list）
     *
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:approval:list')")
    @GetMapping("/pageApprovalList")
    public PageResult<EvaluateApplicationInfoVO> pageApprovalList(EvaluateQueryDTO queryDTO) {
        return applicationInfoService.pageApprovalList(queryDTO.getApplicationInfo(), queryDTO.getItemDetail()
                , queryDTO.getApprovalNode());
    }

    /**
     * 根据ID查询加减分项申请信息（权限标识：evaluate:approval:list）
     *
     * @param id
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:approval:list')")
    @GetMapping("/{id}/applicationInfo")
    public EvaluateApplicationInfoVO applicationInfo(@PathVariable("id") String id) {
        EvaluateApplicationInfoParam param = new EvaluateApplicationInfoParam();
        param.setId(id);
        PageResult<EvaluateApplicationInfoVO> pageResult = applicationInfoService.pageList(param);
        return CommonUtil.listGetOne(pageResult.getList());
    }

    /**
     * 根据申请信息查询当前用户审核表单字段
     *
     * @param workflowId    工作流ID
     * @param applicationId 申请记录ID
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeFormFieldList")
    public List<WorkflowNodeForm> nodeFormFieldList(String workflowId, String applicationId, String nodeId) {
        return workflowNodeFormService.getNodeFormByUser(EvaluateApprovalNode.class, EvaluateApplicationInfo.class,
                applicationId, new WorkflowParam(workflowId), nodeId);
    }

    /**
     * 根据申请信息查询当前用户审核节点记录
     *
     * @param applicationId 申请记录ID
     * @param nodeId        审核节点ID
     * @return
     */
    @GetMapping("/nodeApprovalRecord")
    public List<EvaluateApprovalNodeRecord> nodeApprovalRecord(String applicationId, String nodeId) {
        return workflowService.approvalNodeRecordList(EvaluateApprovalNode.class, EvaluateApprovalNodeRecord.class,
                null, applicationId, nodeId);
    }

    /**
     * 根据申请信息ID查询所有审核节点，审核记录
     *
     * @param id 申请信息ID
     * @return
     */
    @GetMapping("/{id}/approvalNodes")
    public List<ApprovalNodeInfo<EvaluateApprovalNode, EvaluateApprovalNodeRecord>> approvalNodes(@PathVariable("id") String id) {
        return workflowService.approvalNodeInfo(EvaluateApprovalNode.class, EvaluateApprovalNodeRecord.class, id, null);
    }

    /**
     * 根据申请记录ID或项目ID,年份查询节点自定义状态
     *
     * @param applicationId 申请记录ID
     * @param param         工作流查询参数
     * @param nodeId        节点ID
     * @return
     */
    @GetMapping("/nodeState")
    public List<WorkflowNodeStateDTO> nodeState(String applicationId, WorkflowParam param, String nodeId) {
        return workflowService.nodeCustomState(EvaluateApprovalNode.class, param, nodeId, applicationId);
    }

    /**
     * 单个审核操作（权限标识：evaluate:approval:operation）
     *
     * @param request 请求体
     */
    @OperationLog(module = "综合测评管理", comments = "加减分项审核")
    @PreAuthorize("hasAuthority('evaluate:approval:operation')")
    @PostMapping("/operation")
    public String operation(HttpServletRequest request) throws ExecutionException, InterruptedException {
        FormDataWrapper<EvaluateApprovalNodeRecord> wrapper = new FormDataWrapper<>(request, EvaluateApprovalNodeRecord.class);
        Map<String, List<MultipartFile>> multipartFileMap = wrapper.getFileFields();
        EvaluateApprovalNodeRecord record = wrapper.getObject();
        record.setId(null);
        record.setFileMap(multipartFileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        CompletableFuture<String> future = applicationInfoService.approve(record, null, executor);
        CompletableFuture.allOf(future).join();
        return future.get();
    }

    /**
     * 批量审核操作（权限标识：evaluate:approval:operation）
     */
    @OperationLog(module = "综合测评管理", comments = "加减分项批量审核")
    @PreAuthorize("hasAuthority('evaluate:approval:operation')")
    @PostMapping("/batchOperation")
    public void operation(EvaluateQueryDTO queryDTO, HttpServletRequest request) throws Exception {
        List<CompletableFuture<String>> futures = new ArrayList<>();
        List<BatchApprovalMessage> errors = new ArrayList<>();
        EvaluateApplicationInfoParam param = queryDTO.getApplicationInfo();
        EvaluateApprovalNodeRecord record = queryDTO.getRecord();
        Map<String, List<MultipartFile>> multipartFileMap = CommonUtil.getFileFromRequest(request);
        param.setPage(1L);
        param.setLimit((long) (Integer.MAX_VALUE - 1));
        param.setInternalCall(true);
        PageResult<EvaluateApplicationInfoVO> pageResult = applicationInfoService.pageApprovalList(param, queryDTO.getItemDetail()
                , queryDTO.getApprovalNode());
        List<EvaluateApplicationInfoVO> list = pageResult.getList();
        record.setId(null);
        record.setFileMap(multipartFileMap);
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        for (EvaluateApplicationInfoVO vo : list) {
            EvaluateApprovalNodeRecord temp = new EvaluateApprovalNodeRecord();
            BeanUtils.copyProperties(record, temp);
            temp.setApplicationId(vo.getId());
            CompletableFuture<String> future = applicationInfoService.approve(temp, vo, executor)
                    .handle((result, ex) -> {
                        if (ex != null)
                            errors.add(new BatchApprovalMessage(vo.getXgh(), vo.getXm(), vo.getJjfmx(), ex.getCause().getMessage()));
                        return null;
                    });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[]{})).join();
        if (!CollectionUtils.isEmpty(errors))
            EasyExcelHelper.writeApproveErrorInfo(errors, new String[]{"学号", "姓名", "加减分项明细", "失败原因"},
                    new String[]{"xgh", "xm", "jjfmx", "message"});
    }

    /**
     * 删除加减分项申请信息（权限标识：evaluate:approval:remove）
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('evaluate:approval:remove')")
    @OperationLog(module = "综合测评管理", comments = "加减分项批量删除")
    @PostMapping("/{nodeId}/removeApplicationInfo")
    public void removeApplicationInfo(@PathVariable("nodeId") String nodeId, @RequestBody List<String> ids) {
        for (String id : ids) {
            applicationInfoService.delete(id, nodeId);
        }
    }

    /**
     * 加减分项数据导出
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('evaluate:approval:list')")
    @GetMapping("/export")
    public void export(EvaluateQueryDTO queryDTO, HttpServletResponse response) {
        try {
            String[] title = {"学号", "姓名", "学院", "专业", "班级", "年级", "加分明细", "指标", "测评年份", "加分值", "审批状态"};
            String[] property = {"xgh", "xm", "xymc", "zymc", "bjmc", "njmc", "jjfmx", "zbmc", "cpnf", "fs", "spjg"};
            List<EvaluateApplicationInfoVO> list = applicationInfoService.approvalList(queryDTO.getApplicationInfo(), queryDTO.getItemDetail(), queryDTO.getApprovalNode());
            EasyExcelHelper.exportExcel(list, title, property, response, null, "加减分项申请数据");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 加减分项申请数据导入（权限标识：evaluate:approval:dataImport）
     */
    @PreAuthorize("hasAuthority('evaluate:approval:dataImport')")
    @OperationLog(module = "综合测评管理", comments = "加减分项申请数据")
    @PostMapping("/dataImport")
    public void dataImport(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "nodeId") String nodeId) throws Exception {
        List<EvaluateApplicationInfo> infos = ExcelUtils.readExcelToEntity(EvaluateApplicationInfo.class, file.getInputStream(), file.getOriginalFilename(), getHeads());
        List<UserInfo> userInfoList = personInfoFactory.apply(UserType.STUDENT, service -> service.queryList(null, CodeXsztParam.currentStudent()));
        if (CollectionUtils.isEmpty(userInfoList))
            AssertUtil.throwMessage("学生信息不存在");
        Workflow workflow = workflowService.getByNodeId(nodeId);
        EvaluateConfig config = configService.getById(workflow.getProjectId());
        EvaluateItemParam itemParam = new EvaluateItemParam();
        itemParam.setConfigId(workflow.getProjectId());
        List<EvaluateItem> itemList = itemService.list(itemParam);
        EvaluateItemDetailParam itemDetailParam = new EvaluateItemDetailParam();
        itemDetailParam.setConfigId(workflow.getProjectId());
        List<EvaluateItemDetail> itemDetails = itemDetailService.list(itemDetailParam);
        if (CollectionUtils.isEmpty(itemList) || CollectionUtils.isEmpty(itemDetails))
            AssertUtil.throwMessage("请维护指标及加减分规则");
        Map<String, EvaluateItem> itemMap = itemList.stream().collect(Collectors.toMap(EvaluateItem::getId, Function.identity()));
        Map<String, EvaluateItemDetail> itemDetailMap = itemDetails.stream().collect(Collectors.toMap(EvaluateItemDetail::getJjfdm, Function.identity()));
        itemDetailMap.putAll(itemDetails.stream().collect(Collectors.toMap(EvaluateItemDetail::getJjfmx, Function.identity())));
        Map<String, UserInfo> userInfoMap = userInfoList.stream().collect(Collectors.toMap(UserInfo::getXgh, Function.identity()));
        List<UserItemScoreVO> userItemScoreVOS = applicationInfoService.groupByUserAndItemSumScore(config.getId(), null, null);
        List<WorkflowNodeApprover> approverList = nodeApproverService.listByWorkflow(workflow.getId());
        List<WorkflowNode> nodes = workflowNodeService.listByWorkflow(workflow.getId());
        List<WorkflowConditionDetail> conditionDetails = conditionDetailService.listByWorkflow(workflow.getId());
        List<String> arrays = Arrays.asList(CommonUtil.strArray(SecurityUtil.getUsername(), SecurityUtil.getRoleId()));
        List<String> list = approverList.stream().filter(node -> arrays.contains(node.getApproverId()))
                .map(WorkflowNodeApprover::getNodeId).toList();
        List<WorkflowNode> currentUserNodes = new ArrayList<>(nodes.stream().filter(node -> list.contains(node.getId())).toList());
        currentUserNodes.sort(Comparator.comparing(WorkflowNode::getSort));

        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < infos.size(); i++) {
            int line = i + 1;
            EvaluateApplicationInfo applicationInfo = infos.get(i);
            ApplicationInfoImportDTO dto = new ApplicationInfoImportDTO(null, userInfoMap.get(applicationInfo.getXgh()), applicationInfo,
                    nodes, currentUserNodes, conditionDetails, approverList, itemMap, itemDetailMap, config,
                    userItemScoreVOS, workflow);
            CompletableFuture<Void> future = applicationInfoService.dataImportCheck(dto, executor)
                    .handle((result, ex) -> {
                        if (ex != null) {
                            String message = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();
                            errors.add(new ExcelImportError(line, applicationInfo.getXgh(), message));
                        }
                        return null;
                    });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }
        SysAccount account = SecurityUtil.getAccount();
        for (EvaluateApplicationInfo applicationInfo : infos) {
            applicationInfoService.dataImport(new ApplicationInfoImportDTO(account, userInfoMap.get(applicationInfo.getXgh()), applicationInfo,
                    nodes, currentUserNodes, conditionDetails, approverList,
                    null, null, null, null, null));
        }
    }

    private List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
        heads.add(new ExcelUtils.Head("加减分规则或代码名称", "itemDetailId"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm"));
        heads.add(new ExcelUtils.Head("加减分数", "fs", "(-)?\\d+(\\.\\d+)?"));
        heads.add(new ExcelUtils.Head("加减分原因", "jfyy", 512));
        return heads;
    }

    /**
     * 加分项数据导入模板
     */
    @PreAuthorize("hasAuthority('evaluate:approval:dataImport')")
    @GetMapping("/dataImportTemplate")
    public void dataImportTemplate(HttpServletResponse response) {
        ExcelUtils.dataImportTemplate(getHeads(), response);
    }
}
