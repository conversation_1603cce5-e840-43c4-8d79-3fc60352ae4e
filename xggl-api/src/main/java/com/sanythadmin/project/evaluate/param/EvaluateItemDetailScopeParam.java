package com.sanythadmin.project.evaluate.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标加减分规则查询参数
 *
 * <AUTHOR>
 * @since 2025-01-06 11:07:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateItemDetailScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 指标加减分规则ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String itemDetailId;
    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String njid;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String pyccid;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;
    /**
     * 专业ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String zyid;

}
