package com.sanythadmin.project.evaluate.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SqlUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.*;
import com.sanythadmin.project.evaluate.mapper.*;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailScopeParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.service.WorkflowService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 综合测评参数配置Service实现
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@AllArgsConstructor
@Service
public class EvaluateConfigServiceImpl extends ServiceImpl<EvaluateConfigMapper, EvaluateConfig> implements EvaluateConfigService {
    private final EvaluateConfigMapper mapper;
    private final EvaluateConfigScopeMapper configScopeMapper;
    private final EvaluateConfigScopeService configScopeService;
    private final WorkflowService workflowService;
    private final EvaluateItemMapper itemMapper;
    private final EvaluateItemDetailMapper itemDetailMapper;
    private final EvaluateItemDetailScopeMapper itemDetailScopeMapper;
    private final EvaluateApplicationInfoMapper applicationInfoMapper;


    @Transactional
    @Override
    public void edit(EvaluateConfigDTO evaluateConfig) {
        List<EvaluateConfigScopeDTO> scopes = getEvaluateConfigScopes(evaluateConfig);
        if (StringUtils.hasLength(evaluateConfig.getId())) {
            if (!CollectionUtils.isEmpty(scopes)) {
                Set<String> collect = scopes.stream().map(EvaluateConfigScope::getConfigId).collect(Collectors.toSet());
                if (!collect.contains(evaluateConfig.getId()) || collect.size() > 1) {
                    UserInfoUtil.codeTextSet(scopes);
                    AssertUtil.throwMessage("保存失败, 存在相同范围【测评年份、测评年级、培养层次,、测评院系】测评方案, 请调整上述内容后发布", JSON.toJSONString(scopes));
                }
            }

            Long count = applicationInfoMapper.selectCount(new LambdaQueryWrapper<EvaluateApplicationInfo>()
                    .eq(EvaluateApplicationInfo::getConfigId, evaluateConfig.getId()));
            if (count > 0)
                AssertUtil.throwMessage("保存失败, 当前测评方案下已有申请记录");
            EvaluateConfig config = mapper.selectById(evaluateConfig.getId());
            evaluateConfig.setCreateTime(config.getCreateTime());
            mapper.updateById(evaluateConfig);
            configScopeMapper.delete(new LambdaQueryWrapper<EvaluateConfigScope>()
                    .eq(EvaluateConfigScope::getConfigId, evaluateConfig.getId()));
        } else {
            if (!CollectionUtils.isEmpty(scopes)) {
                UserInfoUtil.codeTextSet(scopes);
                AssertUtil.throwMessage("保存失败, 存在相同范围【测评年份、测评年级、培养层次,、测评院系】测评方案, 请调整上述内容后发布", JSON.toJSONString(scopes));
            }
            evaluateConfig.setState(State.ENABLED);
            evaluateConfig.setCreateTime(LocalDateTime.now());
            mapper.insert(evaluateConfig);
        }
        saveConfigScope(evaluateConfig);
    }

    private List<EvaluateConfigScopeDTO> getEvaluateConfigScopes(EvaluateConfigDTO evaluateConfig) {
        EvaluateConfigScopeParam configScope = new EvaluateConfigScopeParam();
        configScope.setPyccid(evaluateConfig.getPyccid());
        configScope.setNjid(evaluateConfig.getNjid());
        configScope.setXyid(evaluateConfig.getXyid());
        EvaluateConfigParam configParam = new EvaluateConfigParam(evaluateConfig.getCpnf(), State.ENABLED, evaluateConfig.getType());
        return configScopeService.list(configScope, configParam);
    }

    @Transactional
    @Override
    public void copyOperation(EvaluateConfigDTO evaluateConfig) {
        String oldConfigId = evaluateConfig.getId();
        evaluateConfig.setId(null);
        List<EvaluateConfigScopeDTO> scopes = getEvaluateConfigScopes(evaluateConfig);
        if (!CollectionUtils.isEmpty(scopes)) {
            UserInfoUtil.codeTextSet(scopes);
            AssertUtil.throwMessage("保存失败, 存在相同范围【测评年份、测评年级、培养层次,、测评院系】测评方案, 请调整上述内容后发布", JSON.toJSONString(scopes));
        }
        evaluateConfig.setCreateTime(LocalDateTime.now());
        evaluateConfig.setState(State.ENABLED);
        mapper.insert(evaluateConfig);
        saveConfigScope(evaluateConfig);

        EvaluateConfig oldConfig = mapper.selectById(oldConfigId);
        WorkflowDTO oldWorkflow = workflowService.get(oldConfigId, oldConfig.getCpnf());
        if (!Objects.isNull(oldWorkflow)) {
            workflowService.copyWorkflow(oldWorkflow, evaluateConfig.getId(), evaluateConfig.getCpnf(), evaluateConfig.getTitle());
            workflowService.editWorkflow(oldWorkflow);
        }
        copyItem(oldConfigId, evaluateConfig);
    }

    @Override
    public Long count(EvaluateConfigParam evaluateConfig) {
        PageParam<EvaluateConfig, EvaluateConfigParam> pageParam = new PageParam<>(evaluateConfig);
        return mapper.selectCount(pageParam.getWrapper());
    }

    @Override
    public List<EvaluateConfig> list(EvaluateConfigParam evaluateConfig) {
        PageParam<EvaluateConfig, EvaluateConfigParam> pageParam = new PageParam<>(evaluateConfig);
        QueryWrapper<EvaluateConfig> wrapper = pageParam.getWrapper();
        return mapper.selectList(wrapper.lambda().orderByDesc(EvaluateConfig::getCpnf)
                .orderByDesc(EvaluateConfig::getCreateTime));
    }

    @Override
    public List<EvaluateConfig> getEnabledConfig(UserInfo userInfo, String type) {
        return getEnabledConfig(userInfo.getPyccid(), userInfo.getNjid(), userInfo.getXyid(), type);
    }

    @Override
    public List<EvaluateConfig> getEnabledConfig(String pyccid, String njid, String xyid, String type) {
        EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
        scopeParam.setPyccid(pyccid);
        scopeParam.setNjid(njid);
        scopeParam.setXyid(xyid);
        PageParam<EvaluateConfigScope, EvaluateConfigScopeParam> pageParam = new PageParam<>(scopeParam);
        String condition = SqlUtil.getRealSql(pageParam.getWrapper());

        MyMPJLambdaWrapper<EvaluateConfig, EvaluateConfigParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.eq(EvaluateConfig::getState, State.ENABLED);
        wrapper.eq(EvaluateConfig::getType, type);
        wrapper.exists("select 1 from SYT_EVALUATE_CONFIG_SCOPE t1 where t1.CONFIG_ID="
                + wrapper.getAlias() + ".id" + (StringUtils.hasText(condition) ? " and " + condition : ""));
        wrapper.orderByDesc(EvaluateConfig::getCpnf);
        return mapper.selectJoinList(EvaluateConfig.class, wrapper);
    }

    @Override
    public void removeConfig(String... ids) {
        for (String id : ids) {
            Long count = applicationInfoMapper.selectCount(new LambdaQueryWrapper<EvaluateApplicationInfo>()
                    .eq(EvaluateApplicationInfo::getConfigId, id));
            if (count > 0)
                AssertUtil.throwMessage("删除失败, 当前测评方案下已有申请记录");
            List<EvaluateItemDetail> itemDetails = itemDetailMapper.selectList(new LambdaQueryWrapper<EvaluateItemDetail>()
                    .eq(EvaluateItemDetail::getConfigId, id));
            if (!CollectionUtils.isEmpty(itemDetails)) {
                List<String> itemDetailIds = itemDetails.stream().map(EvaluateItemDetail::getId).toList();
                itemDetailScopeMapper.delete(new LambdaQueryWrapper<EvaluateItemDetailScope>()
                        .in(EvaluateItemDetailScope::getItemDetailId, itemDetailIds));
                itemDetailMapper.deleteBatchIds(itemDetailIds);
            }
            itemMapper.delete(new LambdaQueryWrapper<EvaluateItem>().eq(EvaluateItem::getConfigId, id));
            configScopeMapper.delete(new LambdaQueryWrapper<EvaluateConfigScope>()
                    .eq(EvaluateConfigScope::getConfigId, id));
            mapper.deleteById(id);
            EvaluateConfig config = mapper.selectById(id);
            WorkflowDTO workflowDTO = workflowService.get(config.getId(), config.getCpnf());
            if (!Objects.isNull(workflowDTO))
                workflowService.removeWorkflow(Collections.singletonList(workflowDTO.getId()));
        }
    }

    public void saveConfigScope(EvaluateConfigDTO evaluateConfig) {
        List<String> list1 = StringUtils.hasText(evaluateConfig.getPyccid()) ? Arrays.asList(CommonUtil.split(evaluateConfig.getPyccid())) : new ArrayList<>();
        List<String> list2 = StringUtils.hasText(evaluateConfig.getNjid()) ? Arrays.asList(CommonUtil.split(evaluateConfig.getNjid())) : new ArrayList<>();
        List<String> list3 = StringUtils.hasText(evaluateConfig.getXyid()) ? Arrays.asList(CommonUtil.split(evaluateConfig.getXyid())) : new ArrayList<>();
        List<List<String>> result = new ArrayList<>();
        for (String item1 : list1) {
            for (String item2 : list2) {
                for (String item3 : list3) {
                    if (item1 == null && item2 == null && item3 == null)
                        continue;
                    result.add(Arrays.asList(item1, item2, item3));
                }
            }
        }

        result.forEach(combination -> saveConfigScope(evaluateConfig, combination.get(0), combination.get(1), combination.get(2)));
    }

    private void saveConfigScope(EvaluateConfig config, String pyccid, String njid, String xyid) {
        EvaluateConfigScope configScope = new EvaluateConfigScope();
        configScope.setConfigId(config.getId());
        configScope.setSqkssj(config.getSqkssj());
        configScope.setSqjzsj(config.getSqjzsj());
//        configScope.setSfyjscj(JudgeMark.NO);
        configScope.setPyccid(pyccid);
        configScope.setNjid(njid);
        configScope.setXyid(xyid);
        configScopeMapper.insert(configScope);
    }


    /**
     * 复制指标及指标加减分项
     *
     * @param oldConfigId
     * @param config
     */
    private void copyItem(String oldConfigId, EvaluateConfigDTO config) {
        Long count = itemMapper.selectCount(new LambdaQueryWrapper<EvaluateItem>().eq(EvaluateItem::getConfigId, oldConfigId));
        if (!Objects.isNull(count)) {
            List<EvaluateItem> evaluateItems = itemMapper.selectList(new LambdaQueryWrapper<EvaluateItem>()
                    .eq(EvaluateItem::getConfigId, oldConfigId));
            if (Objects.isNull(evaluateItems)) return;
            HashMap<String, String> hashMap = new HashMap<>();
            copyItem(config, null, evaluateItems, hashMap);

            List<EvaluateItemDetail> itemDetails = itemDetailMapper.selectList(new LambdaQueryWrapper<EvaluateItemDetail>()
                    .eq(EvaluateItemDetail::getConfigId, oldConfigId));
            if (CollectionUtils.isEmpty(itemDetails)) return;
            itemDetails.forEach(itemDetail -> {
                String oldId = itemDetail.getId();
                itemDetail.setId(null);
                itemDetail.setConfigId(config.getId());
                itemDetail.setCpnf(config.getCpnf());
                itemDetail.setItemId(hashMap.get(itemDetail.getItemId()));
                itemDetailMapper.insert(itemDetail);
                hashMap.put(oldId, itemDetail.getId());
            });

            MyMPJLambdaWrapper<EvaluateItemDetailScope, EvaluateItemDetailScopeParam> wrapper1 = new MyMPJLambdaWrapper<>();
            wrapper1.exists("select 1 from SYT_EVALUATE_ITEM_DETAIL t1 where t1.id = " + wrapper1.getAlias() + ".ITEM_DETAIL_ID and t1.CONFIG_ID = '" + oldConfigId + "'");
            List<EvaluateItemDetailScope> itemDetailScopes = itemDetailScopeMapper.selectJoinList(EvaluateItemDetailScope.class, wrapper1);
            for (EvaluateItemDetailScope detailScope : itemDetailScopes) {
                detailScope.setId(null);
                detailScope.setItemDetailId(hashMap.get(detailScope.getItemDetailId()));
                itemDetailScopeMapper.insert(detailScope);
            }
        }
    }

    private void copyItem(EvaluateConfigDTO config, String itemId, List<EvaluateItem> evaluateItems, HashMap<String, String> hashMap) {
        List<EvaluateItem> items = StringUtils.hasText(itemId) ? evaluateItems.stream().filter(item -> Objects.equals(item.getParentId(), itemId)).toList() : evaluateItems.stream().filter(item -> !StringUtils.hasText(item.getParentId())).toList();
        if (CollectionUtils.isEmpty(items)) return;
        for (EvaluateItem item : items) {
            String oldId = item.getId();
            item.setId(null);
            item.setConfigId(config.getId());
            item.setCpnf(config.getCpnf());
            item.setType(config.getType());
            item.setParentId(StringUtils.hasText(itemId) ? hashMap.get(itemId) : null);
            itemMapper.insert(item);
            hashMap.put(oldId, item.getId());
            copyItem(config, oldId, evaluateItems, hashMap);
        }
    }
}
