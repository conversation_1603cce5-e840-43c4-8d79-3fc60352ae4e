package com.sanythadmin.project.evaluate.dto;

import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.vo.UserItemScoreVO;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2024/12/31.
 */
@AllArgsConstructor
@Data
public class ApplicationInfoImportDTO {
    private SysAccount account;
    private UserInfo userInfo;
    private EvaluateApplicationInfo applicationInfo;
    private List<WorkflowNode> nodes;
    private List<WorkflowNode> currentUserNodes;
    private List<WorkflowConditionDetail> conditionDetails;
    private List<WorkflowNodeApprover> nodeApprovers;
    private Map<String, EvaluateItem> itemMap;
    private Map<String, EvaluateItemDetail> itemDetailMap;
    private EvaluateConfig config;
    private List<UserItemScoreVO> userItemScoreVOS;
    private Workflow workflow;


    public WorkflowNode currentUserLastNode() {
        return currentUserNodes.get(currentUserNodes.size() - 1);
    }

    public List<WorkflowNode> preNodes() {
        WorkflowNode currentUserLastNode = currentUserLastNode();
        List<WorkflowNode> list = new java.util.ArrayList<>(nodes.stream().filter(node -> node.getSort() <= currentUserLastNode.getSort()).toList());
        list.sort(Comparator.comparing(WorkflowNode::getSort));
        return list;
    }

    public Map<String, List<WorkflowConditionDetail>> conditionMap() {
        Map<String, List<WorkflowConditionDetail>> conditionMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(conditionDetails))
            conditionMap = conditionDetails.stream().collect(Collectors.groupingBy(WorkflowConditionDetail::getConditionId));
        return conditionMap;
    }

    public Map<String, WorkflowNode> nodeMap() {
        Map<String, WorkflowNode> nodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(nodes))
            nodeMap = nodes.stream().collect(Collectors.toMap(WorkflowNode::getId, Function.identity()));
        return nodeMap;
    }

    public Map<String, List<WorkflowNodeApprover>> nodeApproverMap() {
        Map<String, List<WorkflowNodeApprover>> nodeApproverMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(nodes))
            nodeApproverMap = nodeApprovers.stream().collect(Collectors.groupingBy(WorkflowNodeApprover::getNodeId));
        return nodeApproverMap;
    }
}
