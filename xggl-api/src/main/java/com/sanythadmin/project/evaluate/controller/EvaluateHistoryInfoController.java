package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNode;
import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNodeRecord;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationInfoService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.vo.EvaluateApplicationInfoVO;
import com.sanythadmin.project.workflow.dto.ApprovalNodeInfo;
import com.sanythadmin.project.workflow.service.WorkflowService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 综合测评/历史记录信息管理
 * Created by JIANGPING on 2025/3/4.
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/evaluate/evaluate-history-info")
public class EvaluateHistoryInfoController extends BaseController {
    private final EvaluateConfigService configService;
    private final EvaluateApplicationInfoService applicationInfoService;
    private final WorkflowService workflowService;

    /**
     * 分页查询综合测评参数配置（权限标识：evaluate:history:list）
     */
    @PreAuthorize("hasAuthority('evaluate:history:list')")
    @GetMapping("/pageConfig")
    public PageResult<EvaluateConfig> pageConfig(EvaluateConfigParam param) {
        param.setState(State.DISABLED);
        PageParam<EvaluateConfig, EvaluateConfigParam> page = new PageParam<>(param);
        page = configService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 加减分申请列表（权限标识：evaluate:history:list）
     *
     * @param param 加减分项查询参数
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:history:list')")
    @GetMapping("/pageApplicationInfo")
    public PageResult<EvaluateApplicationInfoVO> pageApplicationInfo(EvaluateApplicationInfoParam param) {
        return applicationInfoService.pageList(param);
    }

    /**
     * 根据ID查询加减分项申请信息（权限标识：evaluate:history:list）
     *
     * @param id 申请信息ID
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:history:list')")
    @GetMapping("/{id}/applicationInfo")
    public EvaluateApplicationInfoVO applicationInfo(@PathVariable("id") String id) {
        EvaluateApplicationInfoParam param = new EvaluateApplicationInfoParam();
        param.setId(id);
        PageResult<EvaluateApplicationInfoVO> pageResult = pageApplicationInfo(param);
        return CommonUtil.listGetOne(pageResult.getList());
    }

    /**
     * 根据申请信息ID查询所有审核节点，审核记录
     *
     * @param id 申请信息ID
     * @return
     */
    @GetMapping("/{id}/approvalNodes")
    public List<ApprovalNodeInfo<EvaluateApprovalNode, EvaluateApprovalNodeRecord>> approvalNodes(@PathVariable("id") String id) {
        return workflowService.approvalNodeInfo(EvaluateApprovalNode.class, EvaluateApprovalNodeRecord.class, id, null);
    }
}
