package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.entity.EvaluateReviewScore;
import com.sanythadmin.project.evaluate.param.EvaluateReviewScoreParam;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 学生评议分成绩Service
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
public interface EvaluateReviewScoreService extends IService<EvaluateReviewScore> {
    public void operation(EvaluatePeerReviewRecord record);
    public void updateDeptScore(EvaluateReviewScore scores);
    public CompletableFuture<Void> importUpdateDeptScore(EvaluateReviewScore score, Executor executor);
    public PageResult<EvaluateReviewScore> page(EvaluateReviewScoreParam param);
}
