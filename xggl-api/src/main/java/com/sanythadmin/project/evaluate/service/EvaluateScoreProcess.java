package com.sanythadmin.project.evaluate.service;

import com.alibaba.fastjson2.JSON;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.evaluate.dto.CalQueryDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateScoreProcessDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by JIANGPING on 2025/3/15.
 */
@Slf4j
@AllArgsConstructor
@Component
public class EvaluateScoreProcess {
    public static final String QUEUE_KEY = "EVALUATE_SCORE_CALCULATOR_QUEUE";
    private static final AtomicBoolean isRunning = new AtomicBoolean(false);
    public final UserInfoService userInfoService;
    public final RedisTemplate<String, Object> redisTemplate;
    public final EvaluateScoreService evaluateScoreService;

    /**
     * 向 Redis 队列中添加计算任务
     *
     * @param param
     */
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void addTask(CalQueryDTO param) {
        SysAccount account = SecurityUtil.getAccount();
        account.setAuthorities(null);
        EvaluateScoreProcessDTO dto = new EvaluateScoreProcessDTO(param, account);
        redisTemplate.opsForList().leftPush(QUEUE_KEY, JSON.toJSONString(dto));
        startConsumerIfNeeded();
    }


    /**
     * 启动消费者线程（如果未启动）
     */
    private void startConsumerIfNeeded() {
        if (isRunning.compareAndSet(false, true))
            this.consumeTask();
    }

    /**
     * 消费 Redis 队列
     */
    private void consumeTask() {
        try {
            while (true) {
                Object value = redisTemplate.opsForList().rightPop(QUEUE_KEY, 10, TimeUnit.SECONDS);
                if (Objects.isNull(value)) {
                    break;
                }
                process(value);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            isRunning.set(false);
        }
    }


    private void process(Object value) {
        try {
            EvaluateScoreProcessDTO obj = JSON.parseObject(String.valueOf(value), EvaluateScoreProcessDTO.class);
            SysAccount account = obj.getAccount();
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                    account, null, null);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            CalQueryDTO param = obj.getParam();
            evaluateScoreService.scoreCalculator(param);
            evaluateScoreService.scoreRankCalculator(param);
            evaluateScoreService.updateEvaluatePeopleNumber(param.getConfigId());
            evaluateScoreService.updateRankPercent(param.getConfigId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
