package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.param.EvaluatePeerReviewRecordParam;

import java.util.List;
import java.util.Map;

/**
 * 互评分记录Service
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
public interface EvaluatePeerReviewRecordService extends IService<EvaluatePeerReviewRecord> {
    public void operation(EvaluatePeerReviewRecord record);
    public PageResult<EvaluatePeerReviewRecord> page(EvaluatePeerReviewRecordParam param);
    public PageResult<EvaluatePeerReviewRecord> myPage(EvaluatePeerReviewRecordParam param);
    public List<EvaluatePeerReviewRecord> myPeerReviewUsers(UserInfoParam param);
    public List<Map<String, Object>> years();
    public List<EvaluatePeerReviewRecord> peerReviewStudents(EvaluatePeerReviewRecordParam param);
}
