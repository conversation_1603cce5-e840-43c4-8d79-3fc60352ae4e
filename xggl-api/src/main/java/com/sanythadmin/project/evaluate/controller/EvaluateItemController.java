package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.param.EvaluateItemParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.service.EvaluateItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 综合测评/指标管理控制器
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@RestController
@RequestMapping("/api/evaluate/evaluate-item")
public class EvaluateItemController extends BaseController {
    @Resource
    private EvaluateItemService evaluateItemService;
    @Resource
    private EvaluateConfigService evaluateConfigService;

    /**
     * 分页查询综合测评指标（权限标识：evaluate:evaluateItem:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItem:list')")
    @GetMapping("/page")
    public PageResult<EvaluateItem> page(EvaluateItemParam param) {
        PageParam<EvaluateItem, EvaluateItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = evaluateItemService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部综合测评指标（权限标识：evaluate:evaluateItem:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItem:list')")
    @GetMapping()
    public List<EvaluateItem> list(EvaluateItemParam param) {
        PageParam<EvaluateItem, EvaluateItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return evaluateItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询综合测评指标
     */
    @GetMapping("/{id}")
    public EvaluateItem get(@PathVariable("id") String id) {
        return evaluateItemService.getById(id);
    }


    /**
     * 添加或修改综合测评指标（权限标识：evaluate:evaluateItem:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItem:operation')")
    @OperationLog(module = "综合测评指标", comments = "保存综合测评指标")
    @PostMapping("/operation")
    public void operation(@RequestBody EvaluateItemDTO evaluateItem) {
        evaluateItemService.editItem(evaluateItem);
    }

    /**
     * 批量删除综合测评指标（权限标识：evaluate:evaluateItem:remove）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItem:remove')")
    @OperationLog(module = "综合测评指标", comments = "批量删除综合测评指标")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        for (String id : ids) {
            evaluateItemService.remove(id);
        }
    }
}
