package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateApprovalNodeParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    @QueryField(type = QueryType.EQ)
    private String applicationId;

    @QueryField(type = QueryType.EQ)
    private Integer endNode;

    private String nextNodeResult;
    @QueryField(type = QueryType.EQ)
    private String nodeId;

    private String preNodeResult;

    private String result;

    @QueryField(type = QueryType.EQ)
    private Integer stage;

    @QueryField(type = QueryType.EQ)
    private Integer startNode;

    @QueryField(type = QueryType.EQ)
    private String workflowId;

}
