package com.sanythadmin.project.evaluate.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.evaluate.dto.EvaluateTotalScore;
import com.sanythadmin.project.evaluate.dto.ScoreSourceDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 加减分项申请信息Mapper
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
public interface EvaluateApplicationInfoMapper extends MyMPJBaseMapper<EvaluateApplicationInfo> {
    public List<EvaluateTotalScore> getScoreBySource(@Param("param") ScoreSourceDTO param);
}
