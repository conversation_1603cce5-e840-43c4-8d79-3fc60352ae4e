package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.Gender;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合测评成绩
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_SCORE")
@Entity
@Table(name = "SYT_EVALUATE_SCORE")
public class EvaluateScore implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("XM")
    private String xm;

    /**
     * 性别
     */
    @Column(name = "XB", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 校区名称
     */
    @Column(name = "XQMC", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("XQMC")
    private String xqmc;

    /**
     * 年级人数
     */
    @Column(name = "NJRS")
    @TableField("NJRS")
    private Integer njrs;

    /**
     * 学院人数
     */
    @Column(name = "XYRS")
    @TableField("XYRS")
    private Integer xyrs;

    /**
     * 专业人数
     */
    @Column(name = "ZYRS")
    @TableField("ZYRS")
    private Integer zyrs;

    /**
     * 班级人数
     */
    @Column(name = "BJRS")
    @TableField("BJRS")
    private Integer bjrs;

    /**
     * 综测成绩
     */
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 参数配置ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 业务类型
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

    /**
     * 测评人数
     */
    @Column(name = "CPRS")
    @TableField("CPRS")
    private Integer cprs;

    /**
     * 测评排名
     */
    @Column(name = "CPPM")
    @TableField("CPPM")
    private Integer cppm;

    /**
     * 测评排名百分比
     */
    @Column(name = "CPPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("CPPMBFB")
    private Double cppmbfb;

    /**
     * 年级排名
     */
    @Column(name = "NJPM")
    @TableField("NJPM")
    private Integer njpm;

    /**
     * 年级排名百分比
     */
    @Column(name = "NJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("NJPMBFB")
    private Double njpmbfb;

    /**
     * 学院排名
     */
    @Column(name = "XYPM")
    @TableField("XYPM")
    private Integer xypm;

    /**
     * 学院排名百分比
     */
    @Column(name = "XYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("XYPMBFB")
    private Double xypmbfb;

    /**
     * 专业排名
     */
    @Column(name = "ZYPM")
    @TableField("ZYPM")
    private Integer zypm;

    /**
     * 专业排名百分比
     */
    @Column(name = "ZYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("ZYPMBFB")
    private Double zypmbfb;

    /**
     * 班级排名
     */
    @Column(name = "BJPM")
    @TableField("BJPM")
    private Integer bjpm;

    /**
     * 班级排名百分比
     */
    @Column(name = "BJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("BJPMBFB")
    private Double bjpmbfb;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 成绩等级
     */
    @Column(name = "CJDJ")
    @TableField("CJDJ")
    private String cjdj;

    /**
     * 操作人
     */
    @Column(name = "CZR",columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("CZR")
    private String czr;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH",columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CZRZH")
    private String czrzh;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
