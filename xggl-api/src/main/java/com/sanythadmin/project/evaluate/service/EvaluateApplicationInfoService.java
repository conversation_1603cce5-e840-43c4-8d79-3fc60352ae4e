package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.ApplicationInfoImportDTO;
import com.sanythadmin.project.evaluate.dto.CalQueryDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.*;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateApprovalNodeParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;
import com.sanythadmin.project.evaluate.vo.EvaluateApplicationInfoVO;
import com.sanythadmin.project.evaluate.vo.UserItemScoreVO;
import com.sanythadmin.project.workflow.entity.Workflow;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 加减分项申请信息Service
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
public interface EvaluateApplicationInfoService extends IService<EvaluateApplicationInfo> {
    public void operation(HttpServletRequest request);

    public void delete(String id, String nodeId);

    public long count(EvaluateApplicationInfoParam param);

    public void checkItemDetailFloatValue(Double value, String itemDetailId);

    public boolean checkItemScoreIsLimit(String username, String cpnf, EvaluateApplicationInfo info);

    public List<Map<String, Object>> distinct(@NotNull SFunction<EvaluateApplicationInfo, String> field);

    public PageResult<EvaluateApplicationInfoVO> pageList(EvaluateApplicationInfoParam param);

    public PageResult<EvaluateApplicationInfoVO> pageApprovalList(EvaluateApplicationInfoParam param, EvaluateItemDetailParam itemDetailParam,
                                                                  EvaluateApprovalNodeParam approvalNodeParam);

    public List<EvaluateApplicationInfoVO> approvalList(EvaluateApplicationInfoParam param, EvaluateItemDetailParam itemDetailParam,
                                                        EvaluateApprovalNodeParam approvalNodeParam);

    public CompletableFuture<String> approve(EvaluateApprovalNodeRecord record, EvaluateApplicationInfo applicationInfo
            , Executor executor);

    public CompletableFuture<Void> dataImportCheck(ApplicationInfoImportDTO dto, Executor executor);

    public void dataImport(ApplicationInfoImportDTO object);

    public List<UserItemScoreVO> groupByUserAndItemSumScore(@NotEmpty String configId, String ItemId, String username);

    public void setApplicationBaseInfo(EvaluateApplicationInfo info, UserInfo userInfo, JudgeMark sfsyxjydqxx);
}
