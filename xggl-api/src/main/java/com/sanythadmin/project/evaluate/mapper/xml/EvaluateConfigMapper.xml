<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateConfigMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CPNF, SFSYXJYDQXX
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
                    <if test="param.sfsyxjydqxx != null">
                        AND a.SFSYXJYDQXX = #{param.sfsyxjydqxx}
                    </if>
    </sql>
</mapper>
