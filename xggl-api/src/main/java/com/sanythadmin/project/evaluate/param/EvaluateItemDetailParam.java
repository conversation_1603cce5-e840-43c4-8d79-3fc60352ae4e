package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 指标加减分规则查询参数
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateItemDetailParam extends BaseParam {
    private static final long serialVersionUID = 1L;


    /**
     * 加分项明细ID查询条件
     */
    @QueryField(type = QueryType.IN_LIST)
    private List<String> id;

    /**
     * 加减分明细
     */
    private String jjfmx;

    /**
     * 加减分代码
     */
    private String jjfdm;

    /**
     * 是否浮动值
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sffdz;

    /**
     * 是否显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxs;

    /**
     * 指标ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;
    /**
     * 参数配置ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;

    /**
     * 业务类型
     */
    @QueryField(ignore = true)
    private String type;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
