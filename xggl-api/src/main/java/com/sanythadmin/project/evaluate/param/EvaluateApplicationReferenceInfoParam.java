package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标加减分参考项查询参数
 *
 * <AUTHOR>
 * @since 2025-02-12 14:06:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateApplicationReferenceInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 业务模块来源
     */
    private String source;

    /**
     * 备注
     */
    private String remark;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 姓名
     */
    @QueryField(ignore = true)
    private String xm;
    /**
     * 指标加减分规则
     */
    @QueryField(ignore = true)
    private String itemDetailName;
    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;
}
