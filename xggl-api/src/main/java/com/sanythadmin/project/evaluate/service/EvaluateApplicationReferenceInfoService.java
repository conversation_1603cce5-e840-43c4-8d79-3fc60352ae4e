package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationReferenceInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationReferenceInfoParam;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;

import java.util.List;
import java.util.Map;

/**
 * 指标加减分参考项Service
 *
 * <AUTHOR>
 * @since 2025-02-12 14:06:10
 */
public interface EvaluateApplicationReferenceInfoService extends IService<EvaluateApplicationReferenceInfo> {
    public PageResult<EvaluateApplicationReferenceInfo> pageList(EvaluateApplicationReferenceInfoParam param);

    public void sync(EvaluateApplicationReferenceInfo referenceInfo, EvaluateConfig config, Workflow workflow,
                     Map<String, List<WorkflowNodeApprover>> nodeApproverMap, List<WorkflowNode> nodes, Map<String, List<WorkflowConditionDetail>> conditionMap,
                     SysAccount account);
}
