package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 互评分记录
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_PEER_REVIEW_RECORD")
@Entity
@Table(name = "SYT_EVALUATE_PEER_REVIEW_RECORD")
public class EvaluatePeerReviewRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @NotEmpty(message = "学号不能为空")
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("XM")
    private String xm;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空")
    @Column(name = "SCORE", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("SCORE")
    private Double score;

    /**
     * 打分者
     */
    @Column(name = "RATER")
    @TableField("RATER")
    private String rater;

    /**
     * 打分者账号
     */
    @Column(name = "RATER_ACCOUNT")
    @TableField("RATER_ACCOUNT")
    private String raterAccount;

    /**
     * 方案ID
     */
    @NotEmpty(message = "测评方案不能为空")
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONFIG_ID")
    private String configId;

    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;
}
