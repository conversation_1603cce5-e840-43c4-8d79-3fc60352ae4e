package com.sanythadmin.project.evaluate.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.State;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 综合测评参数配置查询参数
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateConfigParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 是否使用学籍异动前信息
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfsyxjydqxx;
    /**
     * 参数有效标识
     */
    @QueryField(type = QueryType.EQ)
    private State state;
    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "cpnf desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }

    public EvaluateConfigParam() {

    }

    public EvaluateConfigParam(String cpnf, State state, String type) {
        this.cpnf = cpnf;
        this.state = state;
        this.type = type;
    }
}
