package com.sanythadmin.project.evaluate.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.ValueTextBaseEnum;

public enum ProcessStage implements ValueTextBaseEnum {
//    ShenQingZhong(0, "申请中"),
//    <PERSON><PERSON><PERSON><PERSON><PERSON>(1, "审核中"),
//    <PERSON><PERSON><PERSON><PERSON>(2, "计算中"),
//    <PERSON><PERSON><PERSON><PERSON><PERSON>(3, "已结束");
    <PERSON><PERSON><PERSON><PERSON><PERSON>(0, "待审核"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(1, "待计算"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(2, "已结束");
    @EnumValue
    private final Integer value;
    @JsonValue
    private final String text;

    ProcessStage(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
