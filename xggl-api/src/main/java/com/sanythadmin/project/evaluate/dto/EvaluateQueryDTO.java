package com.sanythadmin.project.evaluate.dto;

import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNodeRecord;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateApprovalNodeParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;
import lombok.Data;

/**
 * Created by JIANGPING on 2025/4/11.
 */
@Data
public class EvaluateQueryDTO {

    /**
     * 申请项查询参数
     */
    private EvaluateApplicationInfoParam applicationInfo;
    /**
     * 指标加减分项查询参数
     */
    private EvaluateItemDetailParam itemDetail;
    /**
     * 审核节点查询参数
     */
    private EvaluateApprovalNodeParam approvalNode;

    /**
     * 审核表单/记录
     */
    private EvaluateApprovalNodeRecord record;

}
