package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 综测参数配置范围查询参数
 *
 * <AUTHOR>
 * @since 2025-02-19 13:55:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateConfigScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 综测配置ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String configId;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String pyccid;
    @QueryField(type = QueryType.IS_NULL, value = "pyccid")
    private Boolean pyccidIsNull;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;
    @QueryField(type = QueryType.IS_NULL, value = "xyid")
    private Boolean xyidIsNull;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String njid;
    @QueryField(type = QueryType.IS_NULL, value = "njid")
    private Boolean njidIsNull;
}
