<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateScoreMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , XGH, XM, XB, XYID, ZYID, BJID, NJID, PYCCID, XQMC, NJRS, XYRS, ZYRS, BJRS, SCORE, CPNF, CONFIG_ID, TYPE, CPRS, CPPM, CPPMBFB, NJPM, NJPMBFB, XYPM, XYPMBFB, ZYPM, ZYPMBFB, BJPM, BJPMBFB, CREATE_TIME, CZR, CZRZH, CJDJ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND ID = #{param.id}
        </if>
        <if test="param.xgh != null">
            AND XGH LIKE concat(concat('%',#{param.xgh), '%')
        </if>
        <if test="param.xm != null">
            AND XM LIKE concat(concat('%',#{param.xm), '%')
        </if>
        <if test="param.xb != null">
            AND XB = #{param.xb}
        </if>
        <if test="param.xyid != null">
            AND XYID LIKE concat(concat('%',#{param.xyid), '%')
        </if>
        <if test="param.zyid != null">
            AND ZYID LIKE concat(concat('%',#{param.zyid), '%')
        </if>
        <if test="param.bjid != null">
            AND BJID LIKE concat(concat('%',#{param.bjid), '%')
        </if>
        <if test="param.njid != null">
            AND NJID LIKE concat(concat('%',#{param.njid), '%')
        </if>
        <if test="param.pyccid != null">
            AND PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
        </if>
        <if test="param.xqmc != null">
            AND XQMC LIKE concat(concat('%',#{param.xqmc), '%')
        </if>
        <if test="param.njrs != null">
            AND NJRS LIKE concat(concat('%',#{param.njrs), '%')
        </if>
        <if test="param.xyrs != null">
            AND XYRS LIKE concat(concat('%',#{param.xyrs), '%')
        </if>
        <if test="param.zyrs != null">
            AND ZYRS LIKE concat(concat('%',#{param.zyrs), '%')
        </if>
        <if test="param.bjrs != null">
            AND BJRS LIKE concat(concat('%',#{param.bjrs), '%')
        </if>
        <if test="param.score != null">
            AND SCORE = #{param.score}
        </if>
        <if test="param.cpnf != null">
            AND CPNF LIKE concat(concat('%',#{param.cpnf), '%')
        </if>
        <if test="param.configId != null">
            AND CONFIG_ID = #{param.configId}
        </if>
        <if test="param.type != null">
            AND TYPE LIKE concat(concat('%',#{param.type), '%')
        </if>
        <if test="param.cprs != null">
            AND CPRS LIKE concat(concat('%',#{param.cprs), '%')
        </if>
        <if test="param.cppm != null">
            AND CPPM LIKE concat(concat('%',#{param.cppm), '%')
        </if>
        <if test="param.cppmbfb != null">
            AND CPPMBFB LIKE concat(concat('%',#{param.cppmbfb), '%')
        </if>
        <if test="param.njpm != null">
            AND NJPM LIKE concat(concat('%',#{param.njpm), '%')
        </if>
        <if test="param.njpmbfb != null">
            AND NJPMBFB LIKE concat(concat('%',#{param.njpmbfb), '%')
        </if>
        <if test="param.xypm != null">
            AND XYPM LIKE concat(concat('%',#{param.xypm), '%')
        </if>
        <if test="param.xypmbfb != null">
            AND XYPMBFB LIKE concat(concat('%',#{param.xypmbfb), '%')
        </if>
        <if test="param.zypm != null">
            AND ZYPM LIKE concat(concat('%',#{param.zypm), '%')
        </if>
        <if test="param.zypmbfb != null">
            AND ZYPMBFB LIKE concat(concat('%',#{param.zypmbfb), '%')
        </if>
        <if test="param.bjpm != null">
            AND BJPM LIKE concat(concat('%',#{param.bjpm), '%')
        </if>
        <if test="param.bjpmbfb != null">
            AND BJPMBFB LIKE concat(concat('%',#{param.bjpmbfb), '%')
        </if>
        <if test="param.createTime != null">
            AND CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
        </if>
        <if test="param.czr != null">
            AND CZR LIKE concat(concat('%',#{param.czr), '%')
        </if>
        <if test="param.czrzh != null">
            AND CZRZH LIKE concat(concat('%',#{param.czrzh), '%')
        </if>
        <if test="param.cjdj != null">
            AND CJDJ LIKE concat(concat('%',#{param.cjdj), '%')
        </if>
    </sql>

    <!--测评排名-->
    <sql id="totalRank">
        select id, rank() over(order by score desc) cppm from syt_evaluate_score
        <include refid="Base_Query_Condition"/>
    </sql>

    <!--年级排名-->
    <sql id="gradeRank">
        select id, rank() over(partition by pyccid,njid order by score desc) njpm from syt_evaluate_score
        <include refid="Base_Query_Condition"/>
    </sql>

    <!--学院排名-->
    <sql id="collegeRank">
        select id, rank() over(partition by pyccid,njid,xyid order by score desc) xypm from syt_evaluate_score
        <include refid="Base_Query_Condition"/>
    </sql>

    <!--专业排名-->
    <sql id="majorRank">
        select id, rank() over(partition by pyccid,njid,xyid,zyid order by score desc) zypm from syt_evaluate_score
        <include refid="Base_Query_Condition"/>
    </sql>

    <!--班级排名-->
    <sql id="classRank">
        select id, rank() over(partition by pyccid,njid,xyid,zyid,bjid order by score desc) bjpm from syt_evaluate_score
        <include refid="Base_Query_Condition"/>
    </sql>

    <select id="getRank" parameterType="com.sanythadmin.project.evaluate.param.EvaluateScoreParam">
        select a.id, a.cppm, b.njpm, c.xypm, d.zypm, e.bjpm from (<include refid="totalRank"/>) a, (<include
            refid="gradeRank"/>) b, (<include
            refid="collegeRank"/>) c, (<include refid="majorRank"/>) d, (<include refid="classRank"/>) e
        where a.id = b.id and b.id = c.id and c.id = d.id and d.id = e.id
    </select>

    <update id="updateEvaluatePeopleNumber" parameterType="java.lang.String">
        update syt_evaluate_score a
        set a.cprs = (select count(*) from syt_evaluate_score where config_id = #{configId}),
            a.njrs = (select total
                      from (select u.pyccid, u.njid, count(*) total
                            from Syt_User_Info u
                            where u.user_type = 1
                              and exists (select 1
                                          from syt_code_xszt c
                                          where c.id = u.ryztid
                                            and c.sfzx = 1
                                            and c.sfby = 0)
                            group by u.pyccid, u.njid) b
                      where a.pyccid = b.pyccid
                        and a.njid = b.njid),
            a.xyrs = (select total
                      from (select u.pyccid, u.njid, u.xyid, count(*) total
                            from Syt_User_Info u
                            where u.user_type = 1
                              and exists (select 1
                                          from syt_code_xszt c
                                          where c.id = u.ryztid
                                            and c.sfzx = 1
                                            and c.sfby = 0)
                            group by u.pyccid, u.njid, u.xyid) b
                      where a.pyccid = b.pyccid
                        and a.njid = b.njid
                        and a.xyid = b.xyid),
            a.zyrs = (select total
                      from (select u.pyccid, u.njid, u.xyid, u.zyid, count(*) total
                            from Syt_User_Info u
                            where u.user_type = 1
                              and exists (select 1
                                          from syt_code_xszt c
                                          where c.id = u.ryztid
                                            and c.sfzx = 1
                                            and c.sfby = 0)
                            group by u.pyccid, u.njid, u.xyid, u.zyid) b
                      where a.pyccid = b.pyccid
                        and a.njid = b.njid
                        and a.xyid = b.xyid
                        and a.zyid = b.zyid),
            a.bjrs = (select total
                      from (select u.pyccid, u.njid, u.xyid, u.zyid, u.bjid, count(*) total
                            from Syt_User_Info u
                            where u.user_type = 1
                              and exists (select 1
                                          from syt_code_xszt c
                                          where c.id = u.ryztid
                                            and c.sfzx = 1
                                            and c.sfby = 0)
                            group by u.pyccid, u.njid, u.xyid, u.zyid, u.bjid) b
                      where a.pyccid = b.pyccid
                        and a.njid = b.njid
                        and a.xyid = b.xyid
                        and a.zyid = b.zyid
                        and a.bjid = b.bjid)
        where config_id = #{configId}
    </update>

    <update id="updateRankPercent" parameterType="java.lang.String">
        update syt_evaluate_score z
        set z.cppmbfb = round(z.cppm / decode(z.cprs, 0, 1, z.cprs) * 100, 2),
            z.xypmbfb = round(z.xypm / decode(z.xyrs, 0, 1, z.xyrs) * 100, 2),
            z.zypmbfb = round(z.zypm / decode(z.zyrs, 0, 1, z.zyrs) * 100, 2),
            z.bjpmbfb = round(z.bjpm / decode(z.bjrs, 0, 1, z.bjrs) * 100, 2),
            z.njpmbfb = round(z.njpm / decode(z.njrs, 0, 1, z.njrs) * 100, 2)
        where z.cpnf = #{configId}
    </update>
</mapper>
