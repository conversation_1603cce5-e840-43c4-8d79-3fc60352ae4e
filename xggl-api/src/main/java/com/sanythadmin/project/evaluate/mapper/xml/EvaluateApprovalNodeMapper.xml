<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateApprovalNodeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APPLICATION_ID, CREATE_TIME, END_NODE, NEXT_NODE_RESULT, NODE_ID, PRE_NODE_RESULT, RESULT, SORT, STAGE, START_NODE, UPDATE_TIME, WORKFLOW_ID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.applicationId != null">
                        AND a.APPLICATION_ID LIKE concat(concat('%',#{param.applicationId), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.endNode != null">
                        AND a.END_NODE = #{param.endNode}
                    </if>
                    <if test="param.nextNodeResult != null">
                        AND a.NEXT_NODE_RESULT LIKE concat(concat('%',#{param.nextNodeResult), '%')
                    </if>
                    <if test="param.nodeId != null">
                        AND a.NODE_ID LIKE concat(concat('%',#{param.nodeId), '%')
                    </if>
                    <if test="param.preNodeResult != null">
                        AND a.PRE_NODE_RESULT LIKE concat(concat('%',#{param.preNodeResult), '%')
                    </if>
                    <if test="param.result != null">
                        AND a.RESULT LIKE concat(concat('%',#{param.result), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.stage != null">
                        AND a.STAGE = #{param.stage}
                    </if>
                    <if test="param.startNode != null">
                        AND a.START_NODE = #{param.startNode}
                    </if>
                    <if test="param.updateTime != null">
                        AND a.UPDATE_TIME LIKE concat(concat('%',#{param.updateTime), '%')
                    </if>
                    <if test="param.workflowId != null">
                        AND a.WORKFLOW_ID LIKE concat(concat('%',#{param.workflowId), '%')
                    </if>
    </sql>
</mapper>
