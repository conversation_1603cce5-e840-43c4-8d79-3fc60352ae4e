package com.sanythadmin.project.evaluate.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.FileInfo;
import com.sanythadmin.common.core.web.GridFsService;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.evaluate.constants.ConstantsEvaluate;
import com.sanythadmin.project.evaluate.dto.ApplicationInfoImportDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.*;
import com.sanythadmin.project.evaluate.mapper.*;
import com.sanythadmin.project.evaluate.param.*;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationInfoService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.vo.EvaluateApplicationInfoVO;
import com.sanythadmin.project.evaluate.vo.UserItemScoreVO;
import com.sanythadmin.project.form.param.FormApplicationInfoParam;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.dto.ApprovalInfo;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.enums.ApproverType;
import com.sanythadmin.project.workflow.enums.ReviewType;
import com.sanythadmin.project.workflow.mapper.WorkflowMapper;
import com.sanythadmin.project.workflow.mapper.WorkflowNodeApproverMapper;
import com.sanythadmin.project.workflow.param.WorkflowParam;
import com.sanythadmin.project.workflow.service.WorkflowService;
import com.sanythadmin.project.workflow.util.WorkflowUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 加减分项申请信息Service实现
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@RequiredArgsConstructor
@Service
public class EvaluateApplicationInfoServiceImpl extends ServiceImpl<EvaluateApplicationInfoMapper, EvaluateApplicationInfo> implements EvaluateApplicationInfoService {
    private final EvaluateItemMapper evaluateItemMapper;
    private final EvaluateItemDetailMapper evaluateItemDetailMapper;
    private final EvaluateApplicationInfoMapper mapper;
    private final WorkflowMapper workflowMapper;
    private final WorkflowNodeApproverMapper nodeApproverMapper;
    private final EvaluateApprovalNodeMapper approvalNodeMapper;
    private final EvaluateApprovalNodeRecordMapper approvalNodeRecordMapper;
    private final UserInfoMapper userInfoMapper;
    private final GridFsService gridFsService;
    private final WorkflowService workflowService;
    private final EvaluateConfigScopeService configScopeService;


    @Transactional
    @Override
    public void operation(HttpServletRequest request) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        FormDataWrapper<EvaluateApplicationInfo> wrapper = new FormDataWrapper<>(request, EvaluateApplicationInfo.class);
        Map<String, Object> paramMap = wrapper.getTextFields();
        EvaluateApplicationInfo info = wrapper.getObject();
        List<String> fields = wrapper.getFields();
        EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
        scopeParam.setPyccid(userInfo.getPyccid());
        scopeParam.setNjid(userInfo.getNjid());
        scopeParam.setXyid(userInfo.getXyid());
        EvaluateConfigParam configParam = new EvaluateConfigParam();
        configParam.setState(State.ENABLED);
        configParam.setType(info.getType());
        List<EvaluateConfigScopeDTO> configs = configScopeService.list(scopeParam, configParam);
        EvaluateConfigScopeDTO config = CommonUtil.listGetOne(configs);
        AssertUtil.isTrue(!Objects.isNull(config), "申请失败, 缺失参数配置信息");
        AssertUtil.isTrue(DateUtil.localDateTimeIsCorrect(config.getSqkssj(), config.getSqjzsj())
                , "未在申请时间范围内");
        Workflow workflow = workflowMapper.selectOne(new LambdaQueryWrapper<Workflow>()
                .eq(Workflow::getYear, config.getCpnf())
                .eq(Workflow::getProjectId, config.getConfigId()));
        AssertUtil.isTrue(!Objects.isNull(workflow), "申请失败, 请先配置审核流程");
        checkItemDetailFloatValue(info.getFs(), info.getItemDetailId());
        boolean limit = checkItemScoreIsLimit(userInfo.getXgh(), config.getCpnf(), info);
        AssertUtil.isTrue(!limit, "申请失败, 申请分数之和已超出指标最大值");
        info.setFs_old(info.getFs());
        if (StringUtils.hasText(info.getId())) {
            workflowService.checkApplicationInfoIsCanEdit(EvaluateApprovalNode.class, EvaluateApprovalNodeRecord.class, info.getId());
            EvaluateApplicationInfo applicationInfo = mapper.selectById(info.getId());
            for (String key : paramMap.keySet()) {
                if (!fields.contains(key)) continue;
                CommonUtil.setValue(applicationInfo, key, CommonUtil.getValue(key, info));
            }
            mapper.updateById(applicationInfo);
            info = applicationInfo;
        } else {
            info.setConfigId(config.getConfigId());
            info.setXgh(userInfo.getXgh());
            // 设置申请时的角色ID
            info.setRoleId(SecurityUtil.getRoleId());
            info.setXm(userInfo.getXm());
            info.setCpnf(config.getCpnf());
            info.setWorkflowId(workflow.getId());
            info.setSftj(JudgeMark.YES);
            info.setCreateTime(LocalDateTime.now());
            info.setSjly(ConstantsEvaluate.ShenQing);
            info.setSpjg(ReviewResult.DaiShenPi);
            // 设置项目名称为指标名称
            if (StringUtils.hasText(info.getItemId())) {
                EvaluateItem item = evaluateItemMapper.selectById(info.getItemId());
                if (item != null) {
                    info.setXxmc(item.getZbmc());
                }
            }
            setApplicationBaseInfo(info, userInfo, config.getSfsyxjydqxx());
            mapper.insert(info);
        }

        HashMap<String, List<FileInfo>> fileMap = CommonUtil.multipartFileSave(userInfo.getXgh(), info, wrapper.getFileFields(), wrapper.getDeleteFileIds());
        if (!CollectionUtils.isEmpty(fileMap)) {
            UpdateWrapper<EvaluateApplicationInfo> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(EvaluateApplicationInfo::getId, info.getId());
            for (Map.Entry<String, List<FileInfo>> entry : fileMap.entrySet()) {
                updateWrapper.set(entry.getKey(), JSON.toJSONString(entry.getValue()));
            }
            mapper.update(null, updateWrapper);
        }

        workflowService.createApprovalNode(EvaluateApprovalNode.class, info, new WorkflowParam(info.getWorkflowId()), userInfo);
    }

    @Transactional
    @Override
    public void delete(String id, String nodeId) {
        String username = SecurityUtil.getUsername();
        String roleName = SecurityUtil.getRoleName();
        EvaluateApplicationInfo applicationInfo = mapper.selectById(id);
        if (Objects.equals(username, applicationInfo.getXgh()) && Objects.isNull(nodeId)) {
            if (Objects.equals(JudgeMark.YES, applicationInfo.getSftj())) {
                if (Objects.equals(ConstantsEvaluate.TongBu, applicationInfo.getSjly()))
                    AssertUtil.throwMessage("该类型数据不允许删除");
                Object[] arrays = {ConstantsWorkflow.STATE_PASS, ConstantsWorkflow.STATE_RETURN, ConstantsWorkflow.STATE_TERMINATE};
                Long count = approvalNodeMapper.selectCount(new LambdaQueryWrapper<EvaluateApprovalNode>().eq(EvaluateApprovalNode::getApplicationId, id).in(EvaluateApprovalNode::getResult, arrays));
                if (count > 0) AssertUtil.throwMessage("删除失败, 申请信息已审核");
                count = approvalNodeRecordMapper.selectCount(new LambdaQueryWrapper<EvaluateApprovalNodeRecord>().eq(EvaluateApprovalNodeRecord::getApplicationId, id).in(EvaluateApprovalNodeRecord::getResult, arrays));
                if (count > 0) AssertUtil.throwMessage("删除失败, 申请信息已审核");
            }
        } else {
            List<WorkflowNodeApprover> nodeApprovers = nodeApproverMapper.selectList(new LambdaQueryWrapper<WorkflowNodeApprover>().eq(WorkflowNodeApprover::getWorkflowId, applicationInfo.getWorkflowId()).eq(WorkflowNodeApprover::getNodeId, nodeId).in(WorkflowNodeApprover::getApproverId, (Object) CommonUtil.strArray(username, roleName)));
            if (CollectionUtils.isEmpty(nodeApprovers)) AssertUtil.throwMessage("没有删除权限");

            EvaluateApprovalNode approvalNode = approvalNodeMapper.selectOne(new LambdaQueryWrapper<EvaluateApprovalNode>().eq(EvaluateApprovalNode::getApplicationId, id).eq(EvaluateApprovalNode::getNodeId, nodeId));
            if (!Objects.equals(ConstantsEvaluate.STATE_PENDING, approvalNode.getNextNodeResult()))
                AssertUtil.throwMessage("删除失败, 下一级已审批当前申请信息");
            Long count = approvalNodeRecordMapper.selectCount(new LambdaQueryWrapper<EvaluateApprovalNodeRecord>().eq(EvaluateApprovalNodeRecord::getApplicationId, id).eq(EvaluateApprovalNodeRecord::getNodeId, nodeId));
            if (count > 0) AssertUtil.throwMessage("删除失败, 下一级已审批当前申请信息");
        }

        gridFsService.removeByFileInfoStr(applicationInfo.getFjxx());
        approvalNodeMapper.delete(new LambdaQueryWrapper<EvaluateApprovalNode>().eq(EvaluateApprovalNode::getApplicationId, id));
        approvalNodeRecordMapper.delete(new LambdaQueryWrapper<EvaluateApprovalNodeRecord>().eq(EvaluateApprovalNodeRecord::getApplicationId, id));
        mapper.deleteById(id);
    }

    @Override
    public long count(EvaluateApplicationInfoParam param) {
        PageParam<EvaluateApplicationInfo, EvaluateApplicationInfoParam> pageParam = new PageParam<>(param);
        return mapper.selectCount(pageParam.getWrapper());
    }

    @Override
    public void checkItemDetailFloatValue(Double fs, String itemDetailId) {
        EvaluateItemDetail itemDetail = evaluateItemDetailMapper.selectById(itemDetailId);
        if (Objects.equals(JudgeMark.YES, itemDetail.getSffdz())) {
            if (fs < itemDetail.getZxz() || fs > itemDetail.getZdz())
                AssertUtil.throwMessage("分数必须在" + itemDetail.getZxz() + "到" + itemDetail.getZdz() + "之间");
        }
    }

    /**
     * 设置学籍信息
     *
     * @param info
     * @param userInfo
     * @param sfsyxjydqxx 是否使用学籍异动前信息
     */
    @Override
    public void setApplicationBaseInfo(EvaluateApplicationInfo info, UserInfo userInfo, JudgeMark sfsyxjydqxx) {
        info.setPyccid(userInfo.getPyccid());
        info.setXyid(userInfo.getXyid());
        info.setZyid(userInfo.getZyid());
        info.setBjid(userInfo.getBjid());
        info.setNjid(userInfo.getNjid());
        if (Objects.equals(JudgeMark.YES, sfsyxjydqxx)) {
            // 查询最新学籍异动记录设置学院, 专业, 班级信息

        }
    }

    @Override
    public boolean checkItemScoreIsLimit(String username, String s, EvaluateApplicationInfo info) {
        EvaluateItem item = evaluateItemMapper.selectById(info.getItemId());
        if (Objects.equals(ConstantsEvaluate.CALC_METHOD_VALUE, item.getZblx())) {
            List<EvaluateApplicationInfo> infos = mapper.selectList(new LambdaQueryWrapper<EvaluateApplicationInfo>()
                    .eq(EvaluateApplicationInfo::getXgh, username)
                    .eq(EvaluateApplicationInfo::getCpnf, s)
                    .eq(EvaluateApplicationInfo::getItemId, info.getItemId())
                    .eq(EvaluateApplicationInfo::getSftj, JudgeMark.YES));
            BigDecimal score = !Objects.isNull(info.getFs()) ? new BigDecimal(String.valueOf(info.getFs().doubleValue())) : BigDecimal.ZERO;
            for (EvaluateApplicationInfo applicationInfo : infos) {
                if (StringUtils.hasText(info.getId()) && Objects.equals(info.getId(), applicationInfo.getId()))
                    continue;
                score = score.add(new BigDecimal(String.valueOf(applicationInfo.getFs())));
            }
            return score.compareTo(new BigDecimal(String.valueOf(item.getZdz().doubleValue()))) > 0;
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> distinct(SFunction<EvaluateApplicationInfo, String> field) {
        return mapper.selectMaps(new LambdaQueryWrapper<EvaluateApplicationInfo>().select(field).groupBy(field).orderByDesc(field));
    }

    @Override
    public PageResult<EvaluateApplicationInfoVO> pageList(EvaluateApplicationInfoParam param) {
        Page<EvaluateApplicationInfoVO> page = new Page<>(param.getPage(), param.getLimit());
        MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> wrapper = listQueryWrapper(param);
        page = mapper.selectJoinPage(page, EvaluateApplicationInfoVO.class, wrapper);
        UserInfoUtil.codeTextSet(page.getRecords());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public PageResult<EvaluateApplicationInfoVO> pageApprovalList(EvaluateApplicationInfoParam param, EvaluateItemDetailParam itemDetailParam, EvaluateApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> wrapper = approvalListQueryWrapper(param, itemDetailParam, approvalNodeParam);
        Page<EvaluateApplicationInfoVO> page = new Page<>(param.getPage(), param.getLimit());
        Long count = nodeApproverMapper.selectCount(new LambdaQueryWrapper<WorkflowNodeApprover>()
                .eq(WorkflowNodeApprover::getNodeId, approvalNodeParam.getNodeId())
                .eq(WorkflowNodeApprover::getApproverType, ApproverType.user)
                .in(WorkflowNodeApprover::getApproverId, (Object[]) CommonUtil.strArray(SecurityUtil.getUsername(), SecurityUtil.getRoleId()))
        );
        if (count > 0) {
            page = mapper.selectJoinPage(page, EvaluateApplicationInfoVO.class, wrapper);
        } else {
            page = mapper.selectJoinPageWithPermission(page, EvaluateApplicationInfoVO.class, wrapper, new MyMPQueryParams(EvaluateApplicationInfo.class));
        }
        UserInfoUtil.codeTextSet(page.getRecords());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<EvaluateApplicationInfoVO> approvalList(EvaluateApplicationInfoParam param, EvaluateItemDetailParam itemDetailParam, EvaluateApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> wrapper = approvalListQueryWrapper(param, itemDetailParam, approvalNodeParam);
        List<EvaluateApplicationInfoVO> list = mapper.selectJoinListWithPermission(EvaluateApplicationInfoVO.class, wrapper, new MyMPQueryParams(EvaluateApplicationInfo.class));
        UserInfoUtil.codeTextSet(list);
        return list;
    }

    public MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> listQueryWrapper(EvaluateApplicationInfoParam param) {
        MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.leftJoin(EvaluateItem.class, EvaluateItem::getId, EvaluateApplicationInfo::getItemId);
        wrapper.leftJoin(EvaluateItemDetail.class, EvaluateItemDetail::getId, EvaluateApplicationInfo::getItemDetailId);
        wrapper.selectAll(EvaluateApplicationInfo.class).select(EvaluateItem::getZbmc).selectAs(EvaluateItemDetail::getRemark, "itemDetailRemark")
                .select(CommonUtil.buildSFunctions(EvaluateItemDetail.class, CommonUtil.getAllFields(EvaluateItemDetail.class)
                        , "id", "sfxs", "itemId", "sort", "cpnf", "username", "remark", "configId"));
        return wrapper;
    }

    public MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> approvalListQueryWrapper(EvaluateApplicationInfoParam param, EvaluateItemDetailParam itemDetailParam, EvaluateApprovalNodeParam approvalNodeParam) {
        MyMPJLambdaWrapper<EvaluateApplicationInfo, EvaluateApplicationInfoParam> wrapper = listQueryWrapper(param);
        StringBuilder existsSql = new StringBuilder("SELECT 1 FROM SYT_EVALUATE_APPROVAL_NODE WHERE NODE_ID IN (" + CommonUtil.str2SqlIn(approvalNodeParam.getNodeId()) + ") AND " + wrapper.getAlias() + ".ID = APPLICATION_ID");
        if (StringUtils.hasText(approvalNodeParam.getResult()))
            existsSql.append(" AND RESULT = ").append(CommonUtil.str2SqlIn(approvalNodeParam.getResult()));
        wrapper.exists(existsSql.toString());
        return wrapper;
    }


    @Transactional
    @Override
    public CompletableFuture<String> approve(EvaluateApprovalNodeRecord record, EvaluateApplicationInfo applicationInfo
            , Executor executor) {
        final EvaluateApplicationInfo info = applicationInfo == null ? mapper.selectById(record.getApplicationId()) : applicationInfo;
        return CompletableFuture.supplyAsync(() -> {
            UserInfo userInfo = userInfoMapper.get(info.getXgh());
            ApprovalInfo<EvaluateApprovalNode, EvaluateApprovalNodeRecord> approvalInfo = workflowService.approve(EvaluateApprovalNode.class,
                    record, info, new WorkflowParam(info.getWorkflowId()), userInfo);
            mapper.update(new LambdaUpdateWrapper<EvaluateApplicationInfo>()
                    .set(EvaluateApplicationInfo::getSpjg, approvalInfo.getReviewResult())
                    .eq(EvaluateApplicationInfo::getId, info.getId()));
            return approvalInfo.getNextApprovableNodeId();
        }, executor);
    }

    @Override
    public CompletableFuture<Void> dataImportCheck(ApplicationInfoImportDTO dto, Executor executor) {
        EvaluateApplicationInfo info = dto.getApplicationInfo();
        Workflow workflow = dto.getWorkflow();
        UserInfo userInfo = dto.getUserInfo();
        EvaluateConfig config = dto.getConfig();
        return CompletableFuture.runAsync(() -> {
            info.setWorkflowId(workflow.getId());
            info.setSjly(ConstantsEvaluate.DaoRu);
            info.setCreateTime(LocalDateTime.now());
            info.setCpnf(workflow.getYear());
            info.setConfigId(workflow.getProjectId());
            info.setSpjg(ReviewResult.DaiShenPi);
            info.setFs_old(info.getFs());
            if (userInfo == null) {
                AssertUtil.throwMessage("学生信息不存在");
            } else {
                info.setXm(userInfo.getXm());
                setApplicationBaseInfo(info, userInfo, config.getSfsyxjydqxx());
            }

            EvaluateItemDetail itemDetail = dto.getItemDetailMap().get(info.getItemDetailId());
            if (itemDetail == null) {
                AssertUtil.throwMessage("加减分规则不存在");
            } else {
                info.setItemDetailId(itemDetail.getId());
                info.setItemId(itemDetail.getItemId());
                Double fs = info.getFs();
                if (Objects.equals(JudgeMark.YES, itemDetail.getSffdz()) &&
                        (fs < itemDetail.getZxz() || fs > itemDetail.getZdz()))
                    AssertUtil.throwMessage("加减分值不在范围内（范围值：" + itemDetail.getZxz() + "~" + itemDetail.getZdz() + "）");

                EvaluateItem item = dto.getItemMap().get(itemDetail.getItemId());
                if (item != null && item.getZdz() != null) {
                    // 设置项目名称为指标名称
                    info.setXxmc(item.getZbmc());
                    for (UserItemScoreVO score : dto.getUserItemScoreVOS()) {
                        if (Objects.equals(score.getItemId(), item.getId()) &&
                                Objects.equals(score.getXgh(), info.getXgh()) &&
                                (score.getScore() + fs) > item.getZdz()) {
                            AssertUtil.throwMessage("当前分值若加入则超过指标项最大分值");
                        }
                    }
                }
            }

            List<WorkflowNode> preNodes = dto.preNodes();
            int total = 0;
            for (WorkflowNode node : preNodes) {
                if (WorkflowUtil.checkConditionIsMatch(node, info, userInfo, dto.conditionMap()))
                    total++;
            }

            if (total == 0) AssertUtil.throwMessage("没有符合条件的审批节点");

        }, executor);
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void dataImport(ApplicationInfoImportDTO object) {
        SysAccount account = object.getAccount();
        WorkflowNode currentUserLastNode = object.currentUserLastNode();
        EvaluateApplicationInfo info = object.getApplicationInfo();
        mapper.insert(info);
        List<WorkflowNode> preNodes = object.preNodes();
        List<EvaluateApprovalNode> approvalNodes = new ArrayList<>();
        for (WorkflowNode node : preNodes) {
            if (WorkflowUtil.checkConditionIsMatch(node, info, object.getUserInfo(), object.conditionMap())) {
                EvaluateApprovalNode approvalNode = WorkflowUtil.getApprovalNode(EvaluateApprovalNode.class, node, info.getId(), CollectionUtils.isEmpty(approvalNodes));
                approvalNodes.add(approvalNode);
            }
        }

        WorkflowNode nextNode = WorkflowUtil.nextNode(info.getXgh(), currentUserLastNode.getSort(), object.getNodes(), info, object.getUserInfo());
        boolean currentUserLastNodeIsPass = true;
        int size = approvalNodes.size();
        for (int index = 0; index < size; index++) {
            EvaluateApprovalNode approvalNode = approvalNodes.get(index);
            approvalNode.setUpdateTime(LocalDateTime.now());
            if (index > 0) approvalNode.setPreNodeResult(ConstantsWorkflow.STATE_PASS);
            if (index == size - 1 && Objects.isNull(nextNode)) {
                approvalNode.setNextNodeResult(null);
                approvalNode.setEndNode(JudgeMark.YES);
            } else {
                approvalNode.setNextNodeResult(ConstantsWorkflow.STATE_PASS);
            }

            ReviewType reviewType = object.nodeMap().get(approvalNode.getNodeId()).getReviewType();
            approvalNode.setResult(ConstantsWorkflow.STATE_PASS);
            if (!Objects.isNull(reviewType) && Objects.equals(ReviewType.huiQian, reviewType) && approvalNode.getNodeId().equals(currentUserLastNode.getId())) {
                List<WorkflowNodeApprover> nodeApprovers = object.nodeApproverMap().get(approvalNode.getNodeId());
                for (WorkflowNodeApprover approver : nodeApprovers) {
                    if (!Objects.equals(approver.getApproverId(), account.getRole().getId()) && !Objects.equals(approver.getApproverId(), account.getUsername())) {
                        approvalNode.setResult(ConstantsWorkflow.STATE_PENDING);
                        currentUserLastNodeIsPass = false;
                        break;
                    }
                }
            }

            WorkflowNodeApprover nodeApprover = WorkflowUtil.getNodeApprover(object.getNodeApprovers(), approvalNode);
            EvaluateApprovalNodeRecord record = new EvaluateApprovalNodeRecord();
            record.setCreateTime(LocalDateTime.now());
            record.setResult(ConstantsWorkflow.STATE_PASS);
            record.setApplicationId(info.getId());
            record.setNodeId(approvalNode.getNodeId());
            record.setWorkflowId(approvalNode.getWorkflowId());
            record.setUsername(account.getUsername());
            record.setRealName(account.getRealName());
            record.setRoleId(account.getRole().getId());
            record.setNodeApproverId(nodeApprover.getId());
            record.setApproverId(nodeApprover.getApproverId());
            record.setStage(approvalNode.getStage());
            approvalNodeRecordMapper.insert(record);
            approvalNodeMapper.insert(approvalNode);
        }

        try {
            if (Objects.isNull(nextNode) || !currentUserLastNodeIsPass) return;
            EvaluateApprovalNode nextApprovalNode = WorkflowUtil.getApprovalNode(EvaluateApprovalNode.class, nextNode, info.getId(), false);
            nextApprovalNode.setPreNodeResult(ConstantsWorkflow.STATE_PASS);
            approvalNodeMapper.insert(nextApprovalNode);
        } finally {
            ReviewResult result = Objects.isNull(nextNode) && currentUserLastNodeIsPass ? ReviewResult.TongGuo : ReviewResult.ShenPiZhong;
            baseMapper.update(new LambdaUpdateWrapper<EvaluateApplicationInfo>()
                    .set(EvaluateApplicationInfo::getSpjg, result)
                    .eq(EvaluateApplicationInfo::getId, info.getId()));
        }
    }

    @Override
    public List<UserItemScoreVO> groupByUserAndItemSumScore(@NotEmpty String configId, String ItemId, String username) {
        MyMPJLambdaWrapper<EvaluateApplicationInfo, FormApplicationInfoParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.select(EvaluateApplicationInfo::getXgh, EvaluateApplicationInfo::getItemId)
                .selectSum(EvaluateApplicationInfo::getFs, "score");
        wrapper.eq(EvaluateApplicationInfo::getConfigId, configId);
        wrapper.eq(EvaluateApplicationInfo::getSftj, JudgeMark.YES);
        if (StringUtils.hasText(username)) wrapper.eq(EvaluateApplicationInfo::getXgh, username);
        if (StringUtils.hasText(ItemId)) wrapper.eq(EvaluateApplicationInfo::getItemId, ItemId);
        wrapper.exists("select 1 from syt_evaluate_item t1 where item_id = t1.id and t1.config_id = '" + configId + "' and t1.zblx = '" + ConstantsEvaluate.CALC_METHOD_VALUE + "'");
        wrapper.groupBy(EvaluateApplicationInfo::getXgh, EvaluateApplicationInfo::getItemId);
        return mapper.selectJoinList(UserItemScoreVO.class, wrapper);
    }
}
