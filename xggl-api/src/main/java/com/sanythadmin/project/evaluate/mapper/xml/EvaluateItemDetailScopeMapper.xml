<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailScopeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ITEM_DETAIL_ID, PYCCID, XYID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.itemDetailId != null">
                        AND a.ITEM_DETAIL_ID LIKE concat(concat('%',#{param.itemDetailId), '%')
                    </if>
                    <if test="param.pyccid != null">
                        AND a.PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
    </sql>
</mapper>
