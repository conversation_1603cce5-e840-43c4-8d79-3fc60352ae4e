<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, JJFMX, JJFDM, SFFDZ, ZXZ, ZDZ, GDFZ, SFXS, ZBID, SORT, REMARK
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.jjfmx != null">
                        AND a.JJFMX LIKE concat(concat('%',#{param.jjfmx), '%')
                    </if>
                    <if test="param.jjfdm != null">
                        AND a.JJFDM LIKE concat(concat('%',#{param.jjfdm), '%')
                    </if>
                    <if test="param.sffdz != null">
                        AND a.SFFDZ = #{param.sffdz}
                    </if>
                    <if test="param.zxz != null">
                        AND a.ZXZ LIKE concat(concat('%',#{param.zxz), '%')
                    </if>
                    <if test="param.zdz != null">
                        AND a.ZDZ LIKE concat(concat('%',#{param.zdz), '%')
                    </if>
                    <if test="param.gdfz != null">
                        AND a.GDFZ LIKE concat(concat('%',#{param.gdfz), '%')
                    </if>
                    <if test="param.sfxs != null">
                        AND a.SFXS = #{param.sfxs}
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
    </sql>
</mapper>
