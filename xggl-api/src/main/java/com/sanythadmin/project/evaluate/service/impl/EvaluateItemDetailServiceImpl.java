package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDetailDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetailScope;
import com.sanythadmin.project.evaluate.mapper.EvaluateApplicationInfoMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailScopeMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemMapper;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 指标加减分规则Service实现
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@AllArgsConstructor
@Service
public class EvaluateItemDetailServiceImpl extends ServiceImpl<EvaluateItemDetailMapper, EvaluateItemDetail> implements EvaluateItemDetailService {
    private final EvaluateItemDetailMapper mapper;
    private final EvaluateItemMapper evaluateItemMapper;
    private final EvaluateItemDetailScopeMapper detailScopeMapper;
    private final EvaluateApplicationInfoMapper applicationInfoMapper;

    @Transactional
    @Override
    public void editItemDetail(EvaluateItemDetailDTO itemDetail) {
        if (itemDetail.getSort() == null) {
            SortHelper<EvaluateItemDetail> sortHelper = new SortHelper<>(this, "sort");
            itemDetail.setSort((long) sortHelper.next());
        }

        EvaluateItem item = evaluateItemMapper.selectOne(new LambdaQueryWrapper<EvaluateItem>().eq(EvaluateItem::getId, itemDetail.getItemId()));
        itemDetail.setCpnf(item.getCpnf());
        itemDetail.setUsername(item.getUsername());
        itemDetail.setConfigId(item.getConfigId());
        if (StringUtils.hasText(itemDetail.getId())) {
            mapper.updateById(itemDetail);
            detailScopeMapper.delete(new LambdaQueryWrapper<EvaluateItemDetailScope>().eq(EvaluateItemDetailScope::getItemDetailId, itemDetail.getId()));
        } else {
            itemDetail.setJjfdm(String.valueOf(System.currentTimeMillis()));
            mapper.insert(itemDetail);
        }
        saveItemDetailScope(itemDetail);
    }

    private void saveItemDetailScope(EvaluateItemDetailDTO itemDetail) {
        Collection<String> list = getProcessedCollection(itemDetail.getZyid());
        Collection<String> list1 = getProcessedCollection(itemDetail.getXyid());
        Collection<String> list2 = getProcessedCollection(itemDetail.getPyccid());
        Collection<String> list3 = getProcessedCollection(itemDetail.getNjid());
        if (Stream.of(list, list1, list2, list3).allMatch(CollectionUtils::isEmpty))
            return;

        List<EvaluateItemDetailScope> itemDetailScopes = getCombinationList(list, list1, list2, list3).stream()
                .filter(combined -> combined.stream().anyMatch(Objects::nonNull))
                .map(combined -> createDetailScope(combined, itemDetail.getId()))
                .toList();
        itemDetailScopes.forEach(detailScopeMapper::insert);
    }

    private EvaluateItemDetailScope createDetailScope(List<String> combined, String itemDetailId) {
        EvaluateItemDetailScope scope = new EvaluateItemDetailScope();
        scope.setItemDetailId(itemDetailId);
        scope.setZyid(combined.get(0));
        scope.setXyid(combined.get(1));
        scope.setPyccid(combined.get(2));
        scope.setNjid(combined.get(3));
        return scope;
    }

    private Collection<String> getProcessedCollection(String input) {
        Collection<String> collection = StringUtils.hasText(input)
                ? new ArrayList<>(Arrays.asList(CommonUtil.split(input)))
                : new ArrayList<>();
        if (collection.isEmpty())
            collection.add(null); // 确保至少有一个元素（可能为null）
        return collection;
    }

    @SafeVarargs
    private List<List<String>> getCombinationList(Collection<String>... collections) {
        return Arrays.stream(collections)
                .reduce(Collections.singletonList(new ArrayList<>()),
                        (acc, coll) -> acc.stream()
                                .flatMap(list -> coll.stream()
                                        .map(item -> {
                                            List<String> newList = new ArrayList<>(list);
                                            newList.add(item);
                                            return newList;
                                        })
                                ).collect(Collectors.toList()),
                        (list1, list2) -> {
                            List<List<String>> merged = new ArrayList<>(list1);
                            merged.addAll(list2);
                            return merged;
                        }
                );
    }

    @Transactional
    @Override
    public void remove(String id) {
        Long count = applicationInfoMapper.selectCount(new LambdaQueryWrapper<EvaluateApplicationInfo>().eq(EvaluateApplicationInfo::getItemDetailId, id));
        if (count > 0) AssertUtil.throwMessage("删除失败, 当前加分规则已有申请信息");
        detailScopeMapper.delete(new LambdaQueryWrapper<EvaluateItemDetailScope>()
                .eq(EvaluateItemDetailScope::getItemDetailId, id));
        mapper.deleteById(id);
    }

    @Override
    public List<EvaluateItemDetail> list(EvaluateItemDetailParam param) {
        MyMPJLambdaWrapper<EvaluateItemDetail, EvaluateItemDetailParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.innerJoin(EvaluateItem.class, EvaluateItem::getId, EvaluateItemDetail::getItemId);
        return mapper.selectJoinList(EvaluateItemDetail.class, wrapper);
    }

    @Override
    public PageResult<EvaluateItemDetail> listByApplicant(EvaluateItemDetailParam param, UserInfo userInfo) {
        MyMPJLambdaWrapper<EvaluateItemDetail, EvaluateItemDetailParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.and(q -> appendChildCondition(wrapper.getAlias(), userInfo, q));
        Page<EvaluateItemDetail> page = wrapper.getPage();
        page = mapper.selectJoinPage(page, EvaluateItemDetail.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    public void appendChildCondition(String alias, UserInfo userInfo, MPJLambdaWrapper<EvaluateItemDetail> wrapper) {
        String conditionSql = buildQueryCondition(userInfo);
        wrapper.notExists("SELECT 1 FROM SYT_EVALUATE_ITEM_DETAIL_SCOPE ITEM_SCOPE WHERE ITEM_SCOPE.ITEM_DETAIL_ID = " + alias + ".ID")
                .or(true).exists("SELECT 1 FROM SYT_EVALUATE_ITEM_DETAIL_SCOPE ITEM_SCOPE1"
                        + " WHERE ITEM_SCOPE1.ITEM_DETAIL_ID = " + alias + ".ID AND (" + conditionSql + ")");
    }

    public String buildQueryCondition(UserInfo userInfo) {
        HashMap<String, String> columnMap = new HashMap<>() {{
            put("ITEM_SCOPE1.PYCCID", userInfo.getPyccid());
            put("ITEM_SCOPE1.XYID", userInfo.getXyid());
            put("ITEM_SCOPE1.ZYID", userInfo.getZyid());
            put("ITEM_SCOPE1.NJID", userInfo.getNjid());
        }};
        String[] fields = columnMap.keySet().toArray(new String[]{});
        int length = fields.length;
        StringBuilder text = new StringBuilder();
        // 生成所有的组合，但排除三个字段都为空的组合
        for (int i = 1; i < (1 << length); i++) {
            StringBuilder combination = new StringBuilder();
            // 根据二进制值生成每个字段的组合
            for (int j = 0; j < length; j++) {
                // 检查该字段是否为空（0）或不为空（1）
                String field = fields[j];
                if ((i & (1 << j)) != 0) {
                    combination.append(field).append(" = '").append(columnMap.get(field)).append("' ");
                } else {
                    combination.append(field).append(" IS NULL ");
                }
                combination.append("AND ");
            }
            combination.setLength(combination.length() - 5);
            text.append("(").append(combination).append(")").append(" OR ");
        }
        text.setLength(text.length() - 4);
        return text.toString();
    }
}
