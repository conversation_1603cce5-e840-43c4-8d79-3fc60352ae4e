package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 学生评议分成绩
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_REVIEW_SCORE")
@Entity
@Table(name = "SYT_EVALUATE_REVIEW_SCORE")
public class EvaluateReviewScore implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学生互评分
     */
    @Column(name = "XSHPF", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("XSHPF")
    private Double xshpf;

    /**
     * 学院评分
     */
    @Column(name = "XYPF", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("XYPF")
    private Double xypf;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("XM")
    private String xm;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 测评方案ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;
}
