package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.project.workflow.entity.BaseApplicationInfo;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 加减分项申请信息
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_APPLICATION_INFO")
@Entity
@Table(name = "SYT_EVALUATE_APPLICATION_INFO")
public class EvaluateApplicationInfo extends BaseApplicationInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 测评年份
     */
    @Column(name = "CPNF")
    @TableField("CPNF")
    private String cpnf;

    /**
     * 加减分原因
     */
    @Column(name = "JFYY", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("JFYY")
    private String jfyy;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 指标ID
     */
    @Column(name = "ITEM_ID")
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 指标加减分项ID
     */
    @Column(name = "ITEM_DETAIL_ID")
    @TableField("ITEM_DETAIL_ID")
    private String itemDetailId;

    /**
     * 附件信息
     */
    @Column(name = "FJXX", columnDefinition = ColumnType.VARCHAR2_512)
    @TableField("FJXX")
    private String fjxx;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 备注信息
     */
    @Column(name = "REMARK")
    @TableField("REMARK")
    private String remark;

    /**
     * 原申请分数
     */
    @Column(name = "FS_OLD")
    @TableField("FS_OLD")
    private Double fs_old;

    /**
     * 分数（最终分数）
     */
    @Column(name = "FS")
    @TableField("FS")
    private Double fs;

    /**
     * 审批结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "SPJG", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("SPJG")
    private ReviewResult spjg;

    /**
     * 数据来源（申请, 同步, 导入）
     */
    @Column(name = "SJLY", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("SJLY")
    private String sjly;

    /**
     * 数据来源详情URL
     */
    @Column(name = "SJLYXQ")
    @TableField("SJLYXQ")
    private String sjlyxq;

    /**
     * 关联加减分参考数据ID
     */
    @Column(name = "REFERENCEINFO_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("REFERENCEINFO_ID")
    private String referenceInfoId;

    /**
     * 来源模块
     */
    @Column(name = "SJLYMK")
    @TableField("SJLYMK")
    private String sjlymk;

    /**
     * 是否提交
     */
    @Column(name = "SFTJ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFTJ")
    private JudgeMark sftj;

    /**
     * 工作流ID
     */
    @Column(name = "WORKFLOW_ID")
    @TableField("WORKFLOW_ID")
    private String workflowId;

    /**
     * 综测参数配置ID
     */
    @Column(name = "CONFIG_ID")
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 业务类型
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
