package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.param.EvaluateItemParam;

import java.util.List;

/**
 * 综合测评指标Service
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
public interface EvaluateItemService extends IService<EvaluateItem> {

    public void remove(String id);

    public void editItem(EvaluateItemDTO evaluateItem);

    public List<EvaluateItem> list(EvaluateItemParam param);
}
