package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.entity.ScoreSourceConfig;
import com.sanythadmin.project.code.mapper.ScoreSourceConfigMapper;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.constants.ConstantsEvaluate;
import com.sanythadmin.project.evaluate.dto.*;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.entity.EvaluateScore;
import com.sanythadmin.project.evaluate.mapper.EvaluateApplicationInfoMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateScoreMapper;
import com.sanythadmin.project.evaluate.param.EvaluateScoreParam;
import com.sanythadmin.project.evaluate.service.EvaluateScoreService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 综合测评成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
@AllArgsConstructor
@Service
public class EvaluateScoreServiceImpl extends ServiceImpl<EvaluateScoreMapper, EvaluateScore> implements EvaluateScoreService {
    private final EvaluateItemMapper evaluateItemMapper;
    private final EvaluateScoreMapper evaluateScoreMapper;
    private final ScoreSourceConfigMapper scoreSourceConfigMapper;
    private final EvaluateScoreMapper scoreMapper;
    private final EvaluateApplicationInfoMapper applicationInfoMapper;
    private final UserInfoMapper userInfoMapper;
    private final UserInfoService userInfoService;
    private static final DecimalFormat decimalFormat = new DecimalFormat("#.00");

    @Override
    public PageResult<EvaluateScore> page(EvaluateScoreParam param) {
        PageParam<EvaluateScore, EvaluateScoreParam> page = new PageParam<>(param);
        QueryWrapper<EvaluateScore> wrapper = page.getWrapper();
        wrapper.lambda().orderByDesc(EvaluateScore::getCpnf).orderByAsc(EvaluateScore::getCppm);
        page = evaluateScoreMapper.selectPageWithPermission(page, wrapper, new MyMPQueryParams(EvaluateScore.class));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<EvaluateScore> list(EvaluateScoreParam param) {
        PageParam<EvaluateScore, EvaluateScoreParam> page = new PageParam<>(param);
        return evaluateScoreMapper.selectList(page.getWrapper());
    }

    @Override
    public void scoreCalculator(CalQueryDTO calQP) {
        List<EvaluateItem> rootItems = evaluateItemMapper.selectList(new LambdaQueryWrapper<EvaluateItem>()
                .eq(EvaluateItem::getConfigId, calQP.getConfigId())
                .isNull(EvaluateItem::getParentId));
        for (EvaluateItem evaluateItem : rootItems) {
            CalLeafData calLeafData = scoreCalculator(evaluateItem, calQP);
            Map<String, Double> leafDataMap = calLeafData.getMap();
            double maxValue = 0;
            boolean hasMaxValue = false;
            if (evaluateItem.getZdz() != null) {
                hasMaxValue = true;
                maxValue = evaluateItem.getZdz();
            }
            // 通用分数
            double value = leafDataMap.get("0") != null ? leafDataMap.get("0") : 0;
            // 根节点固定项加分
            value += (evaluateItem.getGdjf() != null ? evaluateItem.getGdjf() : 0);
            removeScoreByConfig(calQP);
            List<UserInfo> userInfos = getStudentByConfig(calQP);
            for (UserInfo userInfo : userInfos) {
                EvaluateScore evaluateScore = new EvaluateScore();
                BeanUtils.copyProperties(userInfo, evaluateScore);
                evaluateScore.setCreateTime(LocalDateTime.now());
                evaluateScore.setConfigId(calQP.getConfigId());
                evaluateScore.setCpnf(calQP.getCpnf());
                double score = leafDataMap.get(userInfo.getXgh()) == null ? value : leafDataMap.get(userInfo.getXgh()) + value;
                score = Double.parseDouble(decimalFormat.format(score));
                if (hasMaxValue && score > maxValue)
                    score = maxValue;
                evaluateScore.setScore(score);
                evaluateScore.setType(evaluateItem.getType());
                scoreMapper.insert(evaluateScore);
            }
        }
    }

    private CalLeafData scoreCalculator(EvaluateItem evaluateItem, CalQueryDTO calQP) {
        String xh = calQP.getXgh();
        CalLeafData leaf = new CalLeafData();
        List<EvaluateItem> childItems = evaluateItemMapper.selectList(new LambdaQueryWrapper<EvaluateItem>()
                .eq(EvaluateItem::getConfigId, calQP.getConfigId())
                .eq(EvaluateItem::getParentId, evaluateItem.getId()));
        if (!CollectionUtils.isEmpty(childItems)) {
            List<CalTree> trees = new ArrayList<>();
            List<CalItem> items = new ArrayList<>();
            Map<String, Double> map = new HashMap<>();
            for (EvaluateItem childItem : childItems) {
                CalLeafData leafData = scoreCalculator(childItem, calQP);
                CalTree treeData = leafData.getTree();
                if (treeData != null)
                    trees.add(treeData);
                List<CalItem> calItems = leafData.getItems();
                if (!CollectionUtils.isEmpty(calItems))
                    items.addAll(calItems);
                Map<String, Double> leafDataMap = leafData.getMap();
                // 根据学号将分数相加
                for (String key : leafDataMap.keySet()) {
                    Double value = map.get(key) != null ? map.get(key) + leafDataMap.get(key) : leafDataMap.get(key);
                    map.put(key, value);
                }
            }

            double maxValue = 0;
            boolean hasMaxValue = false;
            if (evaluateItem.getZdz() != null) {
                hasMaxValue = true;
                maxValue = evaluateItem.getZdz();   // 当前指标最大值
            }
            double jichufen = evaluateItem.getGdjf();
            if (evaluateItem.getSfcyjs().getMark()) {
                if (evaluateItem.getZblx().equals(ConstantsEvaluate.CALC_METHOD_PERCENT)) {
                    jichufen = Double.parseDouble(decimalFormat.format(jichufen * (evaluateItem.getBfb() / 100.0)));
                }
                maxValue -= jichufen;
                for (String key : map.keySet()) {
                    double val = map.get(key);
                    if (evaluateItem.getZblx().equals(ConstantsEvaluate.CALC_METHOD_PERCENT)) {
                        val = Double.parseDouble(decimalFormat.format(val * (evaluateItem.getBfb() / 100.0)));
                    }
                    // 成绩上限优化
                    if (hasMaxValue && val > maxValue) {
                        val = maxValue;
                    }
                    map.put(key, val);
                }
            }

            map.put("0", map.get("0") + jichufen);
            leaf.setMap(map);
            if (StringUtils.hasText(xh)) {
                CalTree tree = new CalTree();
                tree.setId(evaluateItem.getId());
                tree.setName(evaluateItem.getZbmc());
                tree.setTrees(trees);
                double value = map.get(xh) != null ? map.get(xh) : 0;
                double att = map.get("0") != null ? map.get("0") : 0;
                value += att;
                value = Double.parseDouble(decimalFormat.format(value));
                tree.setScore(value);
                leaf.setTree(tree);
                CalItem item = new CalItem(evaluateItem.getId(), evaluateItem.getZbmc(), value);
                items.add(item);
                leaf.setItems(items);
            }
            return leaf;
        } else {
            return leafExecute(evaluateItem, calQP);
        }
    }

    private CalLeafData leafExecute(EvaluateItem item, CalQueryDTO calQP) {
        CalLeafData leaf = new CalLeafData();
        String xh = calQP.getXgh();
        Map<String, Double> map = new HashMap<>();
        if (StringUtils.hasText(item.getCjly())) {
            ScoreSourceConfig sourceConfig = scoreSourceConfigMapper.selectById(item.getCjly());
            ScoreSourceDTO scoreSourceParam = new ScoreSourceDTO();
            BeanUtils.copyProperties(sourceConfig, scoreSourceParam);
            scoreSourceParam.setXgh(xh);
            scoreSourceParam.setCpnf(calQP.getCpnf());
            List<EvaluateTotalScore> list = applicationInfoMapper.getScoreBySource(scoreSourceParam);
            if (!CollectionUtils.isEmpty(list))
                map = list.stream().collect(Collectors.toMap(EvaluateTotalScore::getXgh, EvaluateTotalScore::getScore));
        }

        List<EvaluateTotalScore> totalScores = getItemTotalScore(xh, item.getId(), calQP);
        if (!CollectionUtils.isEmpty(totalScores)) {
            for (EvaluateTotalScore obj : totalScores) {
                if (Objects.equals(obj.getItemId(), item.getId())) {
                    Double score = Objects.isNull(map.get(obj.getXgh())) ? obj.getScore() : map.get(obj.getXgh()) + obj.getScore();
                    map.put(obj.getXgh(), score);
                }
            }
        }

        double maxValue = 0;
        boolean hasMaxValue = false;
        double jichufen = item.getGdjf();   // 固定加分
        if (item.getZdz() != null) {
            hasMaxValue = true;
            maxValue = item.getZdz();       // 当前指标最大值
        }

        if (JudgeMark.YES == item.getSfcyjs()) {
            if (item.getZblx().equals(ConstantsEvaluate.CALC_METHOD_PERCENT)) {
                jichufen = Double.parseDouble(decimalFormat.format(jichufen * (item.getBfb() / 100.0)));
            }
            maxValue -= jichufen;
            for (String key : map.keySet()) {
                double val = map.get(key);
                if (item.getZblx().equals(ConstantsEvaluate.CALC_METHOD_PERCENT)) {
                    val = Double.parseDouble(decimalFormat.format(val * (item.getBfb() / 100.0)));
                }
                // 成绩上限优化
                if (hasMaxValue && val > maxValue)
                    val = maxValue;
                map.put(key, val);
            }
        }
        map.put("0", jichufen);

        if (StringUtils.hasText(xh)) {
            CalTree tree = new CalTree();
            tree.setId(item.getId());
            tree.setName(item.getZbmc());
            double value = map.get(xh) != null ? map.get(xh) + jichufen : jichufen;
            value = Double.parseDouble(decimalFormat.format(value));
            tree.setScore(value);
            leaf.setTree(tree);
            CalItem calItem = new CalItem(item.getId(), item.getZbmc(), value);
            leaf.addItem(calItem);
        }
        leaf.setMap(map);
        return leaf;
    }

    public List<EvaluateTotalScore> getItemTotalScore(String username, String itemId, CalQueryDTO config) {
        QueryWrapper<EvaluateApplicationInfo> wrapper = new QueryWrapper<>();
        wrapper.select("xgh", "item_id", "sum(fs) as score");
        LambdaQueryWrapper<EvaluateApplicationInfo> lambdaQueryWrapper = wrapper.lambda();
        lambdaQueryWrapper.eq(EvaluateApplicationInfo::getConfigId, config.getConfigId())
                .in(EvaluateApplicationInfo::getSpjg, ReviewResult.TongGuo, ReviewResult.ShenPiZhong)
                .groupBy(EvaluateApplicationInfo::getXgh, EvaluateApplicationInfo::getItemId);
        if (StringUtils.hasText(username))
            lambdaQueryWrapper.eq(EvaluateApplicationInfo::getXgh, username);
        if (StringUtils.hasText(itemId))
            lambdaQueryWrapper.in(EvaluateApplicationInfo::getItemId, (Object[]) CommonUtil.split(itemId));
        return applicationInfoMapper.selectObjs(lambdaQueryWrapper);
    }

    public List<UserInfo> getStudentByConfig(CalQueryDTO calQuery) {
        // 查出在汇总范围的学生
        UserInfoParam userInfoParam = new UserInfoParam();
        userInfoParam.setPyccid(calQuery.getPyccid());
        userInfoParam.setXyid(calQuery.getXyid());
        userInfoParam.setNjid(calQuery.getNjid());
        userInfoParam.setXgh(calQuery.getXgh());
        userInfoParam.setUserType(UserType.STUDENT);
        MPJLambdaWrapper<UserInfo> queryWrapper = userInfoService.getQueryWrapper(userInfoParam, CodeXsztParam.currentStudent());
        return userInfoMapper.selectJoinListWithPermission(UserInfo.class, queryWrapper);
    }

    private void removeScoreByConfig(CalQueryDTO param) {
        LambdaQueryWrapper<EvaluateScore> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EvaluateScore::getConfigId, param.getConfigId());
        if (StringUtils.hasText(param.getPyccid()))
            wrapper.eq(EvaluateScore::getPyccid, param.getPyccid());
        if (StringUtils.hasText(param.getNjid()))
            wrapper.eq(EvaluateScore::getNjid, param.getNjid());
        if (StringUtils.hasText(param.getXyid()))
            wrapper.eq(EvaluateScore::getXyid, param.getXyid());
        if (StringUtils.hasText(param.getXgh()))
            wrapper.eq(EvaluateScore::getXgh, param.getXgh());
        scoreMapper.delete(wrapper);
    }


    @Transactional
    @Override
    public void scoreRankCalculator(CalQueryDTO param) {
        EvaluateScoreParam scoreParam = new EvaluateScoreParam();
        scoreParam.setConfigId(param.getConfigId());
        List<EvaluateScore> rankList = evaluateScoreMapper.getRank(scoreParam);
        for (EvaluateScore score : rankList) {
            scoreMapper.update(new LambdaUpdateWrapper<EvaluateScore>()
                    .eq(EvaluateScore::getId, score.getId())
                    .set(EvaluateScore::getCppm, score.getCppm())
                    .set(EvaluateScore::getNjpm, score.getNjpm())
                    .set(EvaluateScore::getXypm, score.getXypm())
                    .set(EvaluateScore::getZypm, score.getZypm())
                    .set(EvaluateScore::getBjpm, score.getBjpm()));
        }
    }

    @Override
    public void updateEvaluatePeopleNumber(String configId) {
        scoreMapper.updateEvaluatePeopleNumber(configId);
    }

    @Override
    public void updateRankPercent(String configId) {
        scoreMapper.updateRankPercent(configId);
    }
}
