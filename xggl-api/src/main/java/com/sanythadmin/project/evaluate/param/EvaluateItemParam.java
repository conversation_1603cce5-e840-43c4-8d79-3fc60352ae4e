package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 综合测评指标查询参数
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_LIST)
    private List<String> id;

    /**
     * 指标名称
     */
    private String zbmc;

    /**
     * 记分方式（比例，分值）
     */
    @QueryField(type = QueryType.EQ)
    private Integer zblx;

    /**
     * 是否参与计算
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfcyjs;

    /**
     * 成绩来源（平均成绩, 加权成绩, 体育成绩等）
     */
    private String cjly;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 是否显示
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfxs;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 父指标ID, 父指标ID为空（为true时查询父指标ID为null）
     */
    @QueryField(type = QueryType.EQ)
    private String parentId;
    @QueryField(type = QueryType.IS_NULL, value = "parentId")
    private Boolean parentIdIsNull;

    /**
     * 用户名
     */
    @QueryField(type = QueryType.EQ)
    private String username;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 参数配置ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort asc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
