package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.param.*;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationInfoService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailService;
import com.sanythadmin.project.evaluate.service.EvaluateItemService;
import com.sanythadmin.project.evaluate.vo.EvaluateApplicationInfoVO;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 综合测评/学生申请端管理
 * Created by JIANGPING on 2024/12/4.
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/evaluate/apply")
public class EvaluateApplyController extends BaseController {
    private final EvaluateApplicationInfoService applicationInfoService;
    private final EvaluateItemService itemService;
    private final EvaluateItemDetailService itemDetailService;
    private final EvaluateConfigScopeService configScopeService;

    /**
     * 综合测评参数配置（权限标识：evaluate:evaluateApply:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateApply:list')")
    @GetMapping("/configList")
    public List<EvaluateConfigScopeDTO> configList(EvaluateConfigParam configParam) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
        scopeParam.setPyccid(userInfo.getPyccid());
        scopeParam.setNjid(userInfo.getNjid());
        scopeParam.setXyid(userInfo.getXyid());
        configParam.setState(State.ENABLED);
        return configScopeService.list(scopeParam, configParam);
    }

    /**
     * 分页查询加减分项申请信息（权限标识：evaluate:evaluateApply:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateApply:list')")
    @GetMapping("/pageApplicationInfo")
    public PageResult<EvaluateApplicationInfoVO> page(EvaluateApplicationInfoParam param) {
        param.setXgh(getAccount().getUsername());
        return applicationInfoService.pageList(param);
    }


    /**
     * 根据ID查询加减分项申请信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}/applicationInfo")
    public EvaluateApplicationInfoVO applicationInfo(@PathVariable("id") String id) {
        EvaluateApplicationInfoParam param = new EvaluateApplicationInfoParam();
        param.setXgh(SecurityUtil.getUsername());
        param.setId(id);
        PageResult<EvaluateApplicationInfoVO> pageResult = applicationInfoService.pageList(param);
        return CommonUtil.listGetOne(pageResult.getList());
    }


    /**
     * 删除加减分项申请信息
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateApply:remove')")
    @OperationLog(module = "综合测评申请端", comments = "加减分项批量删除")
    @PostMapping("/removeApplicationInfo")
    public void removeApplicationInfo(@RequestBody List<String> ids) {
        for (String id : ids) {
            applicationInfoService.delete(id, null);
        }
    }

    /**
     * 指标项申请操作（权限标识：evaluate:evaluateApply:operation）
     *
     * @param request
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateApply:operation')")
    @OperationLog(module = "综合测评申请端", comments = "加减分项申请")
    @PostMapping("/operation")
    public void operation(HttpServletRequest request) {
        applicationInfoService.operation(request);
    }


    /**
     * 综测年份
     *
     * @return
     */
    @GetMapping("/yearList")
    public List<Map<String, Object>> yearList() {
        return applicationInfoService.distinct(EvaluateApplicationInfo::getCpnf);
    }

    /**
     * 根据当前用户查询指标
     *
     * @return
     */
    @GetMapping("/itemList")
    public List<EvaluateItem> itemList(EvaluateItemParam param) {
        Object[] objects = getEvaluateConfigScopeDTO(param);
        if (objects[0] == null) return null;
        param.setConfigId(((EvaluateConfigScopeDTO) objects[0]).getConfigId());
        return itemService.list(param);
    }

    private Object[] getEvaluateConfigScopeDTO(EvaluateItemParam param) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
        scopeParam.setPyccid(userInfo.getPyccid());
        scopeParam.setNjid(userInfo.getNjid());
        scopeParam.setXyid(userInfo.getXyid());
        EvaluateConfigParam configParam = new EvaluateConfigParam();
        configParam.setState(State.ENABLED);
        configParam.setType(param.getType());
        List<EvaluateConfigScopeDTO> configScopeDTOS = configScopeService.list(scopeParam, configParam);
        return new Object[]{CommonUtil.listGetOne(configScopeDTOS), userInfo};
    }

    /**
     * 根据当前用户查询指标加减分规则
     *
     * @return
     */
    @GetMapping("/itemDetailList")
    public PageResult<EvaluateItemDetail> itemDetailList(EvaluateItemDetailParam param) {
        return itemDetailService.listByApplicant(param, SecurityUtil.getUserInfo());
    }

    @GetMapping("/myItemDetailList")
    public PageResult<EvaluateItemDetail> myItemDetailList(EvaluateItemDetailParam param) {
        EvaluateItemParam itemParam = new EvaluateItemParam();
        itemParam.setType(param.getType());
        Object[] objects = getEvaluateConfigScopeDTO(itemParam);
        if (objects[0] == null) return null;
        param.setConfigId(((EvaluateConfigScopeDTO) objects[0]).getConfigId());
        return itemDetailService.listByApplicant(param, (UserInfo) objects[1]);
    }
}
