package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.mapper.EvaluatePeerReviewRecordMapper;
import com.sanythadmin.project.evaluate.param.EvaluatePeerReviewRecordParam;
import com.sanythadmin.project.evaluate.service.EvaluatePeerReviewRecordService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 互评分记录Service实现
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Service
public class EvaluatePeerReviewRecordServiceImpl extends ServiceImpl<EvaluatePeerReviewRecordMapper, EvaluatePeerReviewRecord> implements EvaluatePeerReviewRecordService {

    @Resource
    private UserInfoService userInfoService;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void operation(EvaluatePeerReviewRecord record) {
        baseMapper.delete(new LambdaQueryWrapper<EvaluatePeerReviewRecord>()
                .eq(EvaluatePeerReviewRecord::getXgh, record.getXgh())
                .eq(EvaluatePeerReviewRecord::getCpnf, record.getCpnf())
                .eq(EvaluatePeerReviewRecord::getRaterAccount, record.getRaterAccount()));
        UserInfo userInfo = userInfoMapper.selectById(record.getXgh());
        BeanUtils.copyProperties(userInfo, record);
        baseMapper.insert(record);
    }

    @Override
    public PageResult<EvaluatePeerReviewRecord> page(EvaluatePeerReviewRecordParam param) {
        PageParam<EvaluatePeerReviewRecord, EvaluatePeerReviewRecordParam> page = new PageParam<>(param);
        page = baseMapper.selectPageWithPermission(page, page.getWrapper(), new MyMPQueryParams(EvaluatePeerReviewRecord.class));
        List<EvaluatePeerReviewRecord> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public PageResult<EvaluatePeerReviewRecord> myPage(EvaluatePeerReviewRecordParam param) {
        param.setRaterAccount(SecurityUtil.getUsername());
        PageParam<EvaluatePeerReviewRecord, EvaluatePeerReviewRecordParam> page = new PageParam<>(param);
        page = baseMapper.selectPage(page, page.getWrapper());
        List<EvaluatePeerReviewRecord> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public List<EvaluatePeerReviewRecord> myPeerReviewUsers(UserInfoParam param) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        param.setBjid(userInfo.getBjid());
        SFunction<UserInfo, Object>[] sFunctions = CommonUtil.buildSFunctions(UserInfo.class, CommonUtil.getAllFields(BaseUserInfo.class), true);
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = userInfoService.getQueryWrapper(param, CodeXsztParam.currentStudent(), sFunctions);
        wrapper.ne(UserInfo::getXgh, userInfo.getXgh());
        List<UserInfo> userInfos = userInfoMapper.selectJoinList(wrapper);
        return userInfos != null ? userInfos.stream().map(result -> CommonUtil.copyProperties(EvaluatePeerReviewRecord.class, result)).toList() : null;
    }

    @Override
    public List<Map<String, Object>> years() {
        List<Map<String, Object>> maps = baseMapper.selectMaps(new LambdaQueryWrapper<EvaluatePeerReviewRecord>()
                .select(EvaluatePeerReviewRecord::getCpnf)
                .groupBy(EvaluatePeerReviewRecord::getCpnf)
                .orderByDesc(EvaluatePeerReviewRecord::getCpnf));
        List<Map<String, Object>> newMaps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    newMap.put(entry.getKey().toLowerCase(), entry.getValue());
                }
                newMaps.add(newMap);
            }
        }
        return newMaps;
    }

    @Override
    public List<EvaluatePeerReviewRecord> peerReviewStudents(EvaluatePeerReviewRecordParam param) {
        PageParam<EvaluatePeerReviewRecord, EvaluatePeerReviewRecordParam> page = new PageParam<>(param);
        QueryWrapper<EvaluatePeerReviewRecord> wrapper = page.getWrapper(true);
        wrapper.lambda().select(EvaluatePeerReviewRecord::getConfigId, EvaluatePeerReviewRecord::getXgh, EvaluatePeerReviewRecord::getXm, EvaluatePeerReviewRecord::getCpnf, EvaluatePeerReviewRecord::getPyccid,
                        EvaluatePeerReviewRecord::getNjid, EvaluatePeerReviewRecord::getBjid, EvaluatePeerReviewRecord::getZyid, EvaluatePeerReviewRecord::getXyid)
                .groupBy(EvaluatePeerReviewRecord::getConfigId, EvaluatePeerReviewRecord::getXgh, EvaluatePeerReviewRecord::getXm, EvaluatePeerReviewRecord::getCpnf, EvaluatePeerReviewRecord::getPyccid, EvaluatePeerReviewRecord::getNjid,
                        EvaluatePeerReviewRecord::getBjid, EvaluatePeerReviewRecord::getZyid, EvaluatePeerReviewRecord::getXyid);
        return baseMapper.selectListWithPermission(wrapper, new MyMPQueryParams(EvaluatePeerReviewRecord.class));
    }
}
