package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 指标加减分参考项视图（来源其他业务模块数据）
 *
 * <AUTHOR>
 * @since 2025-02-12 14:06:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("V_EVALUATE_APPLICATION_REFERENCE_INFO")
public class EvaluateApplicationReferenceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 业务模块来源
     */
    @TableField("SOURCE")
    private String source;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 学号
     */
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @TableField(exist = false)
    private String xm;

    /**
     * 测评年份
     */
    @TableField("CPNF")
    private String cpnf;

    /**
     * 业务数据详情页URL
     */
    @TableField("DETAIL_URL")
    private String detailUrl;

    /**
     * 所属指标加减分规则ID
     */
    @TableField("ITEM_DETAIL_ID")
    private String itemDetailId;

    /**
     * 所属指标加减分规则名称
     */
    @TableField(exist = false)
    private String itemDetailName;

    /**
     * 加减分数（减分为负数）
     */
    @TableField("FS")
    private Double fs;
    /**
     * 业务类型
     */
    @TableField("type")
    private String type;
}
