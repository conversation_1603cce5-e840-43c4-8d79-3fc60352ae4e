package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.Gender;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 综合测评成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateScoreParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Gender xb;

    /**
     * 学院ID
     */
    private String xyid;

    /**
     * 专业ID
     */
    private String zyid;

    /**
     * 班级ID
     */
    private String bjid;

    /**
     * 年级ID
     */
    private String njid;

    /**
     * 培养层次ID
     */
    private String pyccid;

    /**
     * 校区名称
     */
    private String xqmc;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 参数配置ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 成绩等级
     */
    private String cjdj;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
