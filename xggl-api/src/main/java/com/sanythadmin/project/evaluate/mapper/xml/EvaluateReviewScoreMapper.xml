<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateReviewScoreMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XSHPF, XYPF, CPNF, XGH
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xshpf != null">
                        AND a.XSHPF = #{param.xshpf}
                    </if>
                    <if test="param.xypf != null">
                        AND a.XYPF = #{param.xypf}
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
    </sql>
</mapper>
