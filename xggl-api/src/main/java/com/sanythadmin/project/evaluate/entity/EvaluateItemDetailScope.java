package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 指标加减分规则
 *
 * <AUTHOR>
 * @since 2025-01-06 11:07:26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_ITEM_DETAIL_SCOPE")
@Entity
@Table(name = "SYT_EVALUATE_ITEM_DETAIL_SCOPE")
public class EvaluateItemDetailScope implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 指标加减分规则ID
     */
    @Column(name = "ITEM_DETAIL_ID")
    @TableField("ITEM_DETAIL_ID")
    private String itemDetailId;
    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;
}
