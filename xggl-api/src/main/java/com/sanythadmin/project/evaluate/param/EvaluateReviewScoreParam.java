package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学生评议分成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateReviewScoreParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.EQ)
    private String pyccid;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.EQ)
    private String njid;

    /**
     * 方案ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;
}
