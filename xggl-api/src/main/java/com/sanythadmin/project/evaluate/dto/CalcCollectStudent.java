package com.sanythadmin.project.evaluate.dto;

import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateScore;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by JIANGPING on 2025/3/14.
 */
@Data
public class CalcCollectStudent {
    private List<UserInfo> userInfos;
    private Map<String, EvaluateScore> scoreMap;
}
