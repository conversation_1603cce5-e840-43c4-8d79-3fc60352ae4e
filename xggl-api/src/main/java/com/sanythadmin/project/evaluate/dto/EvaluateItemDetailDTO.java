package com.sanythadmin.project.evaluate.dto;

import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by JIANGPING on 2025/1/6.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EvaluateItemDetailDTO extends EvaluateItemDetail {
    private String pyccid;
    private String xyid;
    private String zyid;
    private String njid;
    private String pyccmc;
    private String xymc;
    private String zymc;
    private String njmc;
}
