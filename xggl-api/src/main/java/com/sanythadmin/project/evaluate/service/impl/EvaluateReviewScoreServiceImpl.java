package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.entity.UserOrgMap;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.param.UserOrgMapParam;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluateConfigScope;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.entity.EvaluateReviewScore;
import com.sanythadmin.project.evaluate.mapper.EvaluateConfigMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateConfigScopeMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluatePeerReviewRecordMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateReviewScoreMapper;
import com.sanythadmin.project.evaluate.param.EvaluatePeerReviewRecordParam;
import com.sanythadmin.project.evaluate.param.EvaluateReviewScoreParam;
import com.sanythadmin.project.evaluate.service.EvaluateReviewScoreService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学生评议分成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Service
public class EvaluateReviewScoreServiceImpl extends ServiceImpl<EvaluateReviewScoreMapper, EvaluateReviewScore> implements EvaluateReviewScoreService {
    @Resource
    private EvaluatePeerReviewRecordMapper evaluatePeerReviewRecordMapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private EvaluateConfigMapper configMapper;
    @Resource
    private EvaluateConfigScopeMapper configScopeMapper;

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void operation(EvaluatePeerReviewRecord record) {
        EvaluateReviewScore reviewScore = baseMapper.selectOne(new LambdaQueryWrapper<EvaluateReviewScore>()
                .eq(EvaluateReviewScore::getXgh, record.getXgh())
                .eq(EvaluateReviewScore::getConfigId, record.getConfigId())
                .eq(EvaluateReviewScore::getCpnf, record.getCpnf()));
        if (reviewScore == null) {
            reviewScore = new EvaluateReviewScore();
            BeanUtils.copyProperties(record, reviewScore);
            reviewScore.setId(null);
        }
        EvaluatePeerReviewRecordParam recordParam = new EvaluatePeerReviewRecordParam();
        BeanUtils.copyProperties(record, recordParam);
        PageParam<EvaluatePeerReviewRecord, EvaluatePeerReviewRecordParam> page = new PageParam<>(recordParam);
        List<EvaluatePeerReviewRecord> records = evaluatePeerReviewRecordMapper.selectList(page.getWrapper().lambda()
                .orderByDesc(EvaluatePeerReviewRecord::getScore));
        int total = records.size();
        double score = 0;
        int excludeNum = 2;
        if (total > excludeNum) {
            for (int i = 1; i < total - 1; i++) {
                score += records.get(i).getScore();
            }

            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            score = Double.parseDouble(decimalFormat.format(score / (total - excludeNum)));
        }
        reviewScore.setXshpf(score);
        if (!StringUtils.hasText(reviewScore.getId())) {
            baseMapper.insert(reviewScore);
        } else {
            baseMapper.update(new LambdaUpdateWrapper<EvaluateReviewScore>()
                    .set(EvaluateReviewScore::getXshpf, reviewScore.getXshpf())
                    .eq(EvaluateReviewScore::getId, reviewScore.getId()));
        }
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void updateDeptScore(EvaluateReviewScore scores) {
        if (StringUtils.hasText(scores.getId())) {
            baseMapper.update(new LambdaUpdateWrapper<EvaluateReviewScore>()
                    .set(EvaluateReviewScore::getXypf, scores.getXypf())
                    .eq(EvaluateReviewScore::getId, scores.getId()));
        } else {
            EvaluateConfig config = configMapper.selectById(scores.getConfigId());
            scores.setCpnf(config.getCpnf());
            scores.setId(null);
            baseMapper.insert(scores);
        }
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public CompletableFuture<Void> importUpdateDeptScore(EvaluateReviewScore score, Executor executor) {
        return CompletableFuture.runAsync(() -> {
            EvaluateReviewScore reviewScore = baseMapper.selectOne(new LambdaQueryWrapper<EvaluateReviewScore>()
                    .eq(EvaluateReviewScore::getXgh, score.getXgh())
                    .eq(EvaluateReviewScore::getConfigId, score.getConfigId()));
            if (reviewScore != null) {
                baseMapper.update(new LambdaUpdateWrapper<EvaluateReviewScore>()
                        .set(EvaluateReviewScore::getXypf, score.getXypf())
                        .eq(EvaluateReviewScore::getId, reviewScore.getId()));
            } else {
                baseMapper.insert(score);
            }
        }, executor);
    }

    @Override
    public PageResult<EvaluateReviewScore> page(EvaluateReviewScoreParam param) {
        List<EvaluateConfigScope> configScopes = configScopeMapper.selectList(new LambdaQueryWrapper<EvaluateConfigScope>()
                .eq(EvaluateConfigScope::getConfigId, param.getConfigId()));
        Set<String> eduIds = configScopes.stream().map(EvaluateConfigScope::getPyccid).collect(Collectors.toSet());
        Set<String> deptIds = configScopes.stream().map(EvaluateConfigScope::getXyid).collect(Collectors.toSet());
        Set<String> gradeIds = configScopes.stream().map(EvaluateConfigScope::getNjid).collect(Collectors.toSet());
        Class<UserOrgMap> orgMapClass = UserOrgMap.class;
        UserInfoParam userInfoParam = new UserInfoParam();
        userInfoParam.setXgh(param.getXgh());
        userInfoParam.setXm(param.getXm());
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>(userInfoParam);
        wrapper.innerJoin(orgMapClass, UserOrgMap::getXgh, UserInfo::getXgh)
                .select(UserInfo::getXgh, UserInfo::getXm)
                .select(UserOrgMap::getPyccid, UserOrgMap::getNjid, UserOrgMap::getXyid, UserOrgMap::getZyid, UserOrgMap::getBjid)
                .in(UserOrgMap::getPyccid, eduIds).in(UserOrgMap::getXyid, deptIds).in(UserOrgMap::getNjid, gradeIds);
        UserOrgMapParam userOrgMapParam = new UserOrgMapParam();
        BeanUtils.copyProperties(param, userOrgMapParam);
        wrapper.buildQueryCondition(orgMapClass, null, userOrgMapParam);

        PageParam<EvaluateReviewScore, EvaluateReviewScoreParam> page = new PageParam<>(param);
        page = userInfoMapper.selectJoinPageWithPermission(page, EvaluateReviewScore.class, wrapper, new MyMPQueryParams(orgMapClass));
        List<EvaluateReviewScore> records = page.getRecords();
        QueryWrapper<EvaluateReviewScore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(EvaluateReviewScore::getXgh, EvaluateReviewScore::getXypf, EvaluateReviewScore::getXshpf, EvaluateReviewScore::getId)
                .eq(EvaluateReviewScore::getConfigId, param.getConfigId());
        if (!param.getInternalCall() && !CollectionUtils.isEmpty(records))
            queryWrapper.lambda().in(EvaluateReviewScore::getXgh, records.stream().map(EvaluateReviewScore::getXgh).toList());
        List<EvaluateReviewScore> scores = baseMapper.selectList(queryWrapper);
        Map<String, EvaluateReviewScore> scoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(scores))
            scoreMap = scores.stream().collect(Collectors.toMap(EvaluateReviewScore::getXgh, Function.identity()));
        EvaluateConfig config = configMapper.selectById(param.getConfigId());
        for (EvaluateReviewScore record : records) {
            record.setConfigId(param.getConfigId());
            record.setCpnf(config.getCpnf());
            record.setId(scoreMap.getOrDefault(record.getXgh(), new EvaluateReviewScore()).getId());
            record.setXypf(scoreMap.getOrDefault(record.getXgh(), new EvaluateReviewScore()).getXypf());
            record.setXshpf(scoreMap.getOrDefault(record.getXgh(), new EvaluateReviewScore()).getXshpf());
        }
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }
}
