<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluatePeerReviewRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, SCORE, RATER, RATER_ACCOUNT, CONFIG_ID, CREATE_TIME, CPNF
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE = #{param.score}
                    </if>
                    <if test="param.rater != null">
                        AND a.RATER LIKE concat(concat('%',#{param.rater), '%')
                    </if>
                    <if test="param.raterAccount != null">
                        AND a.RATER_ACCOUNT LIKE concat(concat('%',#{param.raterAccount), '%')
                    </if>
                    <if test="param.configId != null">
                        AND a.CONFIG_ID LIKE concat(concat('%',#{param.configId), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
    </sql>
</mapper>
