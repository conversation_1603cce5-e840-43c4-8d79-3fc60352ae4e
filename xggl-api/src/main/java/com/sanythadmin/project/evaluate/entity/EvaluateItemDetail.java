package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 指标加减分规则
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_ITEM_DETAIL")
@Entity
@Table(name = "SYT_EVALUATE_ITEM_DETAIL")
public class EvaluateItemDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 加减分明细
     */
    @Column(name = "JJFMX")
    @TableField("JJFMX")
    private String jjfmx;

    /**
     * 加减分代码
     */
    @Column(name = "JJFDM")
    @TableField("JJFDM")
    private String jjfdm;

    /**
     * 是否浮动值
     */
    @Column(name = "SFFDZ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFFDZ")
    private JudgeMark sffdz;

    /**
     * 最小值
     */
    @Column(name = "ZXZ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("ZXZ")
    private Double zxz;

    /**
     * 最大值
     */
    @Column(name = "ZDZ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("ZDZ")
    private Double zdz;

    /**
     * 固定分值
     */
    @Column(name = "GDFZ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("GDFZ")
    private Double gdfz;

    /**
     * 是否显示
     */
    @Column(name = "SFXS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXS")
    private JudgeMark sfxs;

    /**
     * 指标ID
     */
    @Column(name = "ITEM_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ITEM_ID")
    private String itemId;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Long sort;

    /**
     * 备注信息
     */
    @Column(name = "REMARK")
    @TableField("REMARK")
    private String remark;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 综测配置ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONFIG_ID")
    private String configId;
}
