package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.evaluate.enums.ProcessStage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 综测参数配置范围
 *
 * <AUTHOR>
 * @since 2025-02-19 13:55:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_CONFIG_SCOPE")
@Entity
@Table(name = "SYT_EVALUATE_CONFIG_SCOPE")
public class EvaluateConfigScope implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 综测配置ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 申请开始时间
     */
    @Column(name = "SQKSSJ")
    @TableField("SQKSSJ")
    private LocalDateTime sqkssj;

    /**
     * 申请截至时间
     */
    @Column(name = "SQJZSJ")
    @TableField("SQJZSJ")
    private LocalDateTime sqjzsj;

    /**
     * 是否已计算成绩
     */
//    @Column(name = "SFYJSCJ", columnDefinition = ColumnType.NUMBER_1)
//    @TableField("SFYJSCJ")
//    private JudgeMark sfyjscj;

    /**
     * 状态
     */
    @Column(name = "STAGE_STATE", columnDefinition = ColumnType.NUMBER_1)
    @TableField("STAGE_STATE")
    private ProcessStage stageState;
}
