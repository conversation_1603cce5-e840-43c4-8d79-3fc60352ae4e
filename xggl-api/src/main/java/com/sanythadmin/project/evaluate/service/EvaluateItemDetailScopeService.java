package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetailScope;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailScopeParam;

import java.util.List;

/**
 * 指标加减分规则Service
 *
 * <AUTHOR>
 * @since 2025-01-06 11:07:26
 */
public interface EvaluateItemDetailScopeService extends IService<EvaluateItemDetailScope> {

    public List<EvaluateItemDetailScope> list(EvaluateItemDetailScopeParam param);

}
