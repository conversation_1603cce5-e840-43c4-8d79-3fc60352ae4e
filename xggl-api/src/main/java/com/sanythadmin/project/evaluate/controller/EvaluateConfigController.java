package com.sanythadmin.project.evaluate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluateConfigScope;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationInfoService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.workflow.dto.WorkflowDTO;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.service.WorkflowService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 综合测评/参数配置信息管理
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/evaluate/evaluate-config")
public class EvaluateConfigController extends BaseController {
    private final EvaluateConfigService evaluateConfigService;
    private final EvaluateConfigScopeService evaluateConfigScopeService;
    private final EvaluateApplicationInfoService applicationInfoService;
    private final WorkflowService workflowService;


    /**
     * 分页查询综合测评参数配置（权限标识：evaluate:evaluateConfig:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:list')")
    @GetMapping("/page")
    public PageResult<EvaluateConfigDTO> page(EvaluateConfigParam param) {
        PageParam<EvaluateConfig, EvaluateConfigParam> page = new PageParam<>(param);
        QueryWrapper<EvaluateConfig> wrapper = page.getWrapper();
        wrapper.lambda().orderByDesc(EvaluateConfig::getCpnf).orderByDesc(EvaluateConfig::getCreateTime);
        page = evaluateConfigService.page(page, wrapper);
        List<EvaluateConfigDTO> configDTOS = getConfigDTOS(page.getRecords());
        UserInfoUtil.codeTextSet(configDTOS);
        EvaluateApplicationInfoParam applicationInfoParam = new EvaluateApplicationInfoParam();
        for (EvaluateConfigDTO configDTO : configDTOS) {
            applicationInfoParam.setConfigId(configDTO.getId());
            long count = applicationInfoService.count(applicationInfoParam);
            configDTO.setSfkbj(count == 0 && Objects.equals(configDTO.getState(), State.ENABLED));
        }
        return new PageResult<>(configDTOS, page.getTotal());
    }

    /**
     * 分页查询综合测评参数范围（权限标识：evaluate:evaluateConfig:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:list')")
    @GetMapping("/pageScopeList")
    public PageResult<EvaluateConfigScopeDTO> pageScopeList(EvaluateConfigScopeParam param) {
        return evaluateConfigScopeService.page(param, null);
    }

    private List<EvaluateConfigDTO> getConfigDTOS(List<EvaluateConfig> configs) {
        List<EvaluateConfigDTO> itemDTOs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(configs)) {
            List<String> list = configs.stream().map(EvaluateConfig::getId).toList();
            Map<String, List<EvaluateConfigScope>> map = new HashMap<>();
            EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
            scopeParam.setConfigId(CommonUtil.appendComma(list.toArray(new String[]{})));
            List<EvaluateConfigScope> scopes = evaluateConfigScopeService.list(scopeParam);
            if (!CollectionUtils.isEmpty(scopes))
                map = scopes.stream().collect(Collectors.groupingBy(EvaluateConfigScope::getConfigId));
            Map<String, List<EvaluateConfigScope>> finalMap = map;
            configs.forEach(item -> {
                EvaluateConfigDTO dto = convert2ConfigDTO(item, !CollectionUtils.isEmpty(finalMap) ? finalMap.get(item.getId()) : null);
                itemDTOs.add(dto);
            });
        }
        return itemDTOs;
    }

    private EvaluateConfigDTO convert2ConfigDTO(EvaluateConfig config, List<EvaluateConfigScope> scopes) {
        EvaluateConfigDTO itemDTO = new EvaluateConfigDTO();
        BeanUtils.copyProperties(config, itemDTO);
        if (!CollectionUtils.isEmpty(scopes)) {
            Set<String> list = scopes.stream().map(EvaluateConfigScope::getPyccid).filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list)) itemDTO.setPyccid(CommonUtil.strJoin(list));
            list = scopes.stream().map(EvaluateConfigScope::getXyid).filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list)) itemDTO.setXyid(CommonUtil.strJoin(list));
            list = scopes.stream().map(EvaluateConfigScope::getNjid).filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list)) itemDTO.setNjid(CommonUtil.strJoin(list));
        }
        return itemDTO;
    }

    /**
     * 查询全部综合测评参数配置
     */
    @GetMapping()
    public List<EvaluateConfig> list(EvaluateConfigParam param) {
        return evaluateConfigService.list(param);
    }

    /**
     * 根据id查询综合测评参数配置
     */
    @GetMapping("/{id}")
    public EvaluateConfigDTO get(@PathVariable("id") String id) {
        EvaluateConfig config = evaluateConfigService.getById(id);
        EvaluateConfigScopeParam param = new EvaluateConfigScopeParam();
        param.setConfigId(id);
        List<EvaluateConfigScope> itemScopes = evaluateConfigScopeService.list(param);
        return convert2ConfigDTO(config, itemScopes);
    }

    /**
     * 添加或修改综合测评参数配置（权限标识：evaluate:evaluateConfig:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:operation')")
    @OperationLog(module = "综合测评参数配置", comments = "保存综合测评参数配置")
    @PostMapping("/operation")
    public void operation(@RequestBody EvaluateConfigDTO evaluateConfig) {
        evaluateConfigService.edit(evaluateConfig);
    }

    /**
     * 综测参数范围维护（权限标识：evaluate:evaluateConfig:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:operation')")
    @OperationLog(module = "综合测评参数配置", comments = "保存综合测评参数配置")
    @PostMapping("/operationScope")
    public void operationScope(@RequestBody EvaluateConfigScope scope) {
        evaluateConfigScopeService.edit(scope);
    }


    /**
     * 批量删除综合测评参数配置（权限标识：evaluate:evaluateConfig:remove）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:remove')")
    @OperationLog(module = "综合测评参数配置", comments = "批量删除综合测评参数配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        for (String configId : ids) {
            evaluateConfigScopeService.remove(new LambdaQueryWrapper<EvaluateConfigScope>()
                    .eq(EvaluateConfigScope::getConfigId, configId));
        }
        evaluateConfigService.removeByIds(ids);
    }

    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:remove')")
    @OperationLog(module = "综合测评参数配置", comments = "批量删除综合测评参数配置")
    @PostMapping("/{id}/removeScope")
    public void removeScope(@PathVariable("id") String id) {
        EvaluateConfigScope configScope = evaluateConfigScopeService.getById(id);
        EvaluateConfig config = evaluateConfigService.getById(configScope.getConfigId());
        EvaluateApplicationInfoParam param = new EvaluateApplicationInfoParam();
        param.setCpnf(config.getCpnf());
        param.setPyccid(configScope.getPyccid());
        param.setXyid(configScope.getXyid());
        param.setNjid(configScope.getNjid());
        param.setSftj(JudgeMark.YES);
        long count = applicationInfoService.count(param);
        if (count > 0)
            AssertUtil.throwMessage("删除失败，综测参数范围下存在加减分申请信息");
        evaluateConfigScopeService.removeById(id);
    }

    /**
     * 综测加减分项审核工作流配置
     *
     * @param id          参数配置ID
     * @param workflowDTO 工作流信息
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:operation')")
    @OperationLog(module = "综合测评参数配置", comments = "工作流配置")
    @PostMapping("/{id}/workflow")
    public void workflow(@PathVariable("id") String id, @RequestBody WorkflowDTO workflowDTO) {
        EvaluateConfig config = evaluateConfigService.getById(id);
        workflowService.copyWorkflow(workflowDTO, config.getId(), config.getCpnf(), "综合测评");
        workflowService.editWorkflow(workflowDTO);
    }


    /**
     * 查询工作流
     *
     * @param id 参数配置ID
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:list')")
    @GetMapping("/{id}/workflow")
    public Workflow workflow(@PathVariable("id") String id) {
        EvaluateConfig config = evaluateConfigService.getById(id);
        return workflowService.getOne(new LambdaQueryWrapper<Workflow>()
                .eq(Workflow::getProjectId, id)
                .eq(Workflow::getYear, config.getCpnf()));
    }

    /**
     * 综测配置信息复制
     *
     * @param config
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateConfig:operation')")
    @OperationLog(module = "综合测评参数配置", comments = "综测配置信息复制")
    @PostMapping("/copyOperation")
    public void copyOperation(@RequestBody EvaluateConfigDTO config) {
        evaluateConfigService.copyOperation(config);
    }
}
