package com.sanythadmin.project.evaluate.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 计算每个指标的返回数据
 */
@Data
public class CalLeafData {
    /**
     * 树形明细数据
     */
    private CalTree tree;
    /**
     * 全体数据
     */
    private Map<String, Double> map;
    /**
     * Field 列表名字数据
     */
    private List<CalItem> items;

    public void addItem(CalItem item){
        if(this.items==null)
            this.items = new ArrayList<>();
        items.add(item);
    }
}
