package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetailScope;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailScopeMapper;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailScopeParam;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailScopeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标加减分规则Service实现
 *
 * <AUTHOR>
 * @since 2025-01-06 11:07:26
 */
@Service
public class EvaluateItemDetailScopeServiceImpl extends ServiceImpl<EvaluateItemDetailScopeMapper, EvaluateItemDetailScope> implements EvaluateItemDetailScopeService {

    @Resource
    private EvaluateItemDetailScopeMapper mapper;

    @Override
    public List<EvaluateItemDetailScope> list(EvaluateItemDetailScopeParam param) {
        PageParam<EvaluateItemDetailScope, EvaluateItemDetailScopeParam> page = new PageParam<>(param);
        return mapper.selectList(page.getWrapper());
    }
}
