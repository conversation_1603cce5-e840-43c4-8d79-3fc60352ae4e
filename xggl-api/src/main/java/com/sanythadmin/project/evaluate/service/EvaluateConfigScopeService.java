package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfigScope;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;

import java.util.List;

/**
 * 综测参数配置范围Service
 *
 * <AUTHOR>
 * @since 2025-02-19 13:55:05
 */
public interface EvaluateConfigScopeService extends IService<EvaluateConfigScope> {

    public void edit(EvaluateConfigScope scope);

    public PageResult<EvaluateConfigScopeDTO> page(EvaluateConfigScopeParam scopeParam, EvaluateConfigParam param);

    public List<EvaluateConfigScopeDTO> list(EvaluateConfigScopeParam scopeParam, EvaluateConfigParam param);

    public List<EvaluateConfigScope> list(EvaluateConfigScopeParam param);
}
