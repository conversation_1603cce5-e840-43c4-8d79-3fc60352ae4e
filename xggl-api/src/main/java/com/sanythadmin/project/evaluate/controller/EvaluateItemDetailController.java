package com.sanythadmin.project.evaluate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDetailDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetailScope;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailScopeParam;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateItemDetailService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 综合测评/指标加减分规则控制器
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@RestController
@RequestMapping("/api/evaluate/evaluate-item-detail")
public class EvaluateItemDetailController extends BaseController {
    @Resource
    private EvaluateItemDetailService evaluateItemDetailService;
    @Resource
    private EvaluateItemDetailScopeService evaluateItemDetailScopeService;

    /**
     * 分页查询指标加减分规则（权限标识：evaluate:evaluateItemDetail:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItemDetail:list')")
    @GetMapping("/page")
    public PageResult<EvaluateItemDetailDTO> page(EvaluateItemDetailParam param) {
        PageParam<EvaluateItemDetail, EvaluateItemDetailParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = evaluateItemDetailService.page(page, page.getWrapper());
        List<EvaluateItemDetailDTO> itemDetailDTOS = getEvaluateItemDetailDTOS(page.getRecords());
        UserInfoUtil.codeTextSet(itemDetailDTOS);
        return new PageResult<>(itemDetailDTOS, page.getTotal());
    }

    /**
     * 查询全部指标加减分规则（权限标识：evaluate:evaluateItemDetail:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItemDetail:list')")
    @GetMapping()
    public List<EvaluateItemDetailDTO> list(EvaluateItemDetailParam param) {
        PageParam<EvaluateItemDetail, EvaluateItemDetailParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<EvaluateItemDetail> itemDetails = evaluateItemDetailService.list(page.getOrderWrapper());
        return getEvaluateItemDetailDTOS(itemDetails);
    }

    /**
     * 根据id查询指标加减分规则
     */
    @GetMapping("/{id}")
    public EvaluateItemDetailDTO get(@PathVariable("id") String id) {
        EvaluateItemDetail itemDetail = evaluateItemDetailService.getById(id);
        List<EvaluateItemDetailScope> itemScopes = evaluateItemDetailScopeService.list(new LambdaQueryWrapper<EvaluateItemDetailScope>()
                .eq(EvaluateItemDetailScope::getItemDetailId, id));
        return convert2EvaluateItemDetailDTO(itemDetail, itemScopes);
    }

    private List<EvaluateItemDetailDTO> getEvaluateItemDetailDTOS(List<EvaluateItemDetail> itemList) {
        List<EvaluateItemDetailDTO> itemDTOs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemList)) {
            List<String> list = itemList.stream().map(EvaluateItemDetail::getId).toList();
            Map<String, List<EvaluateItemDetailScope>> map = new HashMap<>();
            EvaluateItemDetailScopeParam scopeParam = new EvaluateItemDetailScopeParam();
            scopeParam.setItemDetailId(CommonUtil.appendComma(list.toArray(new String[]{})));
            List<EvaluateItemDetailScope> itemScopes = evaluateItemDetailScopeService.list(scopeParam);
            if (!CollectionUtils.isEmpty(itemScopes))
                map = itemScopes.stream().collect(Collectors.groupingBy(EvaluateItemDetailScope::getItemDetailId));
            Map<String, List<EvaluateItemDetailScope>> finalMap = map;
            itemList.forEach(item -> {
                EvaluateItemDetailDTO dto = convert2EvaluateItemDetailDTO(item, !CollectionUtils.isEmpty(finalMap) ? finalMap.get(item.getId()) : null);
                itemDTOs.add(dto);
            });
        }
        return itemDTOs;
    }

    private EvaluateItemDetailDTO convert2EvaluateItemDetailDTO(EvaluateItemDetail item, List<EvaluateItemDetailScope> itemScopes) {
        EvaluateItemDetailDTO itemDTO = new EvaluateItemDetailDTO();
        BeanUtils.copyProperties(item, itemDTO);
        if (!CollectionUtils.isEmpty(itemScopes)) {
            Collection<String> list = itemScopes.stream().map(EvaluateItemDetailScope::getPyccid)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list))
                itemDTO.setPyccid(CommonUtil.appendComma(list.toArray(new String[]{})));
            list = itemScopes.stream().map(EvaluateItemDetailScope::getXyid)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list))
                itemDTO.setXyid(CommonUtil.appendComma(list.toArray(new String[]{})));
            list = itemScopes.stream().map(EvaluateItemDetailScope::getZyid)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list))
                itemDTO.setZyid(CommonUtil.appendComma(list.toArray(new String[]{})));
            list = itemScopes.stream().map(EvaluateItemDetailScope::getNjid)
                    .filter(StringUtils::hasText).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(list))
                itemDTO.setNjid(CommonUtil.appendComma(list.toArray(new String[]{})));
        }
        return itemDTO;
    }

    /**
     * 添加或修改指标加减分规则（权限标识：evaluate:evaluateItemDetail:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItemDetail:operation')")
    @OperationLog(module = "指标加减分规则", comments = "保存指标加减分规则")
    @PostMapping("/operation")
    public void operation(@RequestBody EvaluateItemDetailDTO evaluateItemDetail) {
        evaluateItemDetailService.editItemDetail(evaluateItemDetail);
    }

    /**
     * 批量删除指标加减分规则（权限标识：evaluate:evaluateItemDetail:remove）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateItemDetail:remove')")
    @OperationLog(module = "指标加减分规则", comments = "批量删除指标加减分规则")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        evaluateItemDetailService.removeByIds(ids);
    }
}
