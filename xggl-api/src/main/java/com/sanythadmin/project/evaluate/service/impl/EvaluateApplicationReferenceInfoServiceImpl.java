package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.ReviewResult;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.project.evaluate.constants.ConstantsEvaluate;
import com.sanythadmin.project.evaluate.entity.*;
import com.sanythadmin.project.evaluate.mapper.*;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationReferenceInfoParam;
import com.sanythadmin.project.evaluate.service.EvaluateApplicationReferenceInfoService;
import com.sanythadmin.project.workflow.constant.ConstantsWorkflow;
import com.sanythadmin.project.workflow.entity.Workflow;
import com.sanythadmin.project.workflow.entity.WorkflowConditionDetail;
import com.sanythadmin.project.workflow.entity.WorkflowNode;
import com.sanythadmin.project.workflow.entity.WorkflowNodeApprover;
import com.sanythadmin.project.workflow.util.WorkflowUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 指标加减分参考项Service实现
 *
 * <AUTHOR>
 * @since 2025-02-12 14:06:10
 */
@Service
public class EvaluateApplicationReferenceInfoServiceImpl extends ServiceImpl<EvaluateApplicationReferenceInfoMapper, EvaluateApplicationReferenceInfo> implements EvaluateApplicationReferenceInfoService {
    @Resource
    private EvaluateApplicationReferenceInfoMapper mapper;
    @Resource
    private EvaluateApplicationInfoMapper applicationInfoMapper;
    @Resource
    private EvaluateItemDetailMapper itemDetailMapper;
    @Resource
    private EvaluateApprovalNodeMapper approvalNodeMapper;
    @Resource
    private EvaluateApprovalNodeRecordMapper approvalNodeRecordMapper;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Override
    public PageResult<EvaluateApplicationReferenceInfo> pageList(EvaluateApplicationReferenceInfoParam param) {
        Page<EvaluateApplicationReferenceInfo> page = new Page<>(param.getPage(), param.getLimit());
        MyMPJLambdaWrapper<EvaluateApplicationReferenceInfo, EvaluateApplicationReferenceInfoParam> wrapper = new MyMPJLambdaWrapper<>(param);
        wrapper.leftJoin(EvaluateItemDetail.class, EvaluateItemDetail::getId, EvaluateApplicationReferenceInfo::getItemDetailId);
        wrapper.leftJoin(UserInfo.class, UserInfo::getXgh, EvaluateApplicationReferenceInfo::getXgh);
        wrapper.selectAll(EvaluateApplicationReferenceInfo.class).select(UserInfo::getXm).selectAs(EvaluateItemDetail::getJjfmx, "itemDetailName");
        if (StringUtils.hasText(param.getXm()))
            wrapper.like(UserInfo::getXm, param.getXm());
        if (StringUtils.hasText(param.getItemDetailName()))
            wrapper.like(EvaluateItemDetail::getJjfmx, param.getItemDetailName());
        page = mapper.selectJoinPage(page, EvaluateApplicationReferenceInfo.class, wrapper);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Transactional
    @Override
    public void sync(EvaluateApplicationReferenceInfo referenceInfo, EvaluateConfig config, Workflow workflow,
                     Map<String, List<WorkflowNodeApprover>> nodeApproverMap, List<WorkflowNode> nodes, Map<String, List<WorkflowConditionDetail>> conditionMap,
                     SysAccount account) {
        Long count = applicationInfoMapper.selectCount(new LambdaQueryWrapper<EvaluateApplicationInfo>()
                .eq(EvaluateApplicationInfo::getReferenceInfoId, referenceInfo.getId()));
        if (count == null || count == 0) {
            EvaluateItemDetail itemDetail = itemDetailMapper.selectById(referenceInfo.getItemDetailId());
            if (itemDetail == null) AssertUtil.throwMessage("加分规则不存在");
            UserInfo userInfo = userInfoMapper.get(referenceInfo.getXgh());
            EvaluateApplicationInfo applicationInfo = new EvaluateApplicationInfo();
            BeanUtils.copyProperties(userInfo, applicationInfo);
            applicationInfo.setReferenceInfoId(referenceInfo.getId());
            applicationInfo.setCreateTime(LocalDateTime.now());
            applicationInfo.setItemDetailId(itemDetail.getId());
            applicationInfo.setItemId(itemDetail.getItemId());
            applicationInfo.setFs(referenceInfo.getFs());
            applicationInfo.setJfyy(referenceInfo.getRemark());
            applicationInfo.setWorkflowId(workflow.getId());
            applicationInfo.setCpnf(config.getCpnf());
            applicationInfo.setSftj(JudgeMark.YES);
            applicationInfo.setSjly(ConstantsEvaluate.TongBu);
            applicationInfo.setSjlymk(referenceInfo.getSource());
            applicationInfo.setSjlyxq(referenceInfo.getDetailUrl());
            applicationInfo.setSpjg(ReviewResult.TongGuo);
            applicationInfoMapper.insert(applicationInfo);
            List<EvaluateApprovalNode> approvalNodes = WorkflowUtil.getApprovalNode(EvaluateApprovalNode.class, userInfo,
                    applicationInfo, nodes, conditionMap);
            approvalNodes.forEach(approvalNode -> {
                approvalNode.setResult(ConstantsWorkflow.STATE_PASS);
                approvalNode.setPreNodeResult(ConstantsWorkflow.STATE_PASS);
                approvalNode.setNextNodeResult(ConstantsWorkflow.STATE_PASS);
                approvalNode.setUpdateTime(LocalDateTime.now());
            });
            approvalNodes.get(0).setPreNodeResult(null);
            approvalNodes.get(approvalNodes.size() - 1).setNextNodeResult(null);
            approvalNodes.get(approvalNodes.size() - 1).setEndNode(JudgeMark.YES);
            for (EvaluateApprovalNode approvalNode : approvalNodes) {
                WorkflowNodeApprover approver = nodeApproverMap.get(approvalNode.getNodeId()).get(0);
                EvaluateApprovalNodeRecord record = new EvaluateApprovalNodeRecord();
                record.setCreateTime(LocalDateTime.now());
                record.setResult(ConstantsWorkflow.STATE_PASS);
                record.setApplicationId(applicationInfo.getId());
                record.setNodeId(approvalNode.getNodeId());
                record.setWorkflowId(approvalNode.getWorkflowId());
                record.setUsername(account.getUsername());
                record.setRealName(account.getRealName());
                record.setRoleId(account.getRole().getId());
                record.setNodeApproverId(approver.getId());
                record.setApproverId(approver.getApproverId());
                record.setStage(approvalNode.getStage());
                approvalNodeMapper.insert(approvalNode);
                approvalNodeRecordMapper.insert(record);
            }
        }
    }
}
