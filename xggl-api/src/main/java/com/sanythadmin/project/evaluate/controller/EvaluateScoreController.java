package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.CalQueryDTO;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateScore;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.param.EvaluateScoreParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateScoreProcess;
import com.sanythadmin.project.evaluate.service.EvaluateScoreService;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 综合测评/成绩汇总控制器
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/evaluate/evaluate-score")
public class EvaluateScoreController extends BaseController {
    private final EvaluateConfigScopeService configScopeService;
    private final EvaluateScoreService evaluateScoreService;
    private final EvaluateScoreProcess evaluateScoreProcess;

    /**
     * 分页查询参数配置信息（权限标识：evaluate:evaluateScore:list）
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:list')")
    @GetMapping("/pageConfig")
    public PageResult<EvaluateConfigScopeDTO> pageConfig(EvaluateConfigScopeParam configScopeParam, EvaluateConfigParam param) {
        return configScopeService.page(configScopeParam, param);
    }

    /**
     * 分页查询综合测评成绩（权限标识：evaluate:evaluateScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:list')")
    @GetMapping("/page")
    public PageResult<EvaluateScore> page(EvaluateScoreParam param) {
        return evaluateScoreService.page(param);
    }

    /**
     * 查询全部综合测评成绩（权限标识：evaluate:evaluateScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:list')")
    @GetMapping()
    public List<EvaluateScore> list(EvaluateScoreParam param) {
        PageParam<EvaluateScore, EvaluateScoreParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return evaluateScoreService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询综合测评成绩（权限标识：evaluate:evaluateScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:list')")
    @GetMapping("/{id}")
    public EvaluateScore get(@PathVariable("id") String id) {
        return evaluateScoreService.getById(id);
    }

    /**
     * 综合测评成绩计算（权限标识：evaluate:evaluateScore:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:operation')")
    @OperationLog(module = "综合测评成绩", comments = "计算并保存综合测评成绩")
    @PostMapping("/operation")
    public void save(@RequestBody CalQueryDTO param) {
        evaluateScoreProcess.addTask(param);
    }

    /**
     * 批量删除综合测评成绩（权限标识：evaluate:evaluateScore:remove）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateScore:remove')")
    @OperationLog(module = "综合测评成绩", comments = "批量删除综合测评成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        evaluateScoreService.removeByIds(ids);
    }
}
