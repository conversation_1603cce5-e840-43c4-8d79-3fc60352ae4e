package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.enums.State;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 综合测评参数配置
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_CONFIG")
@Entity
@Table(name = "SYT_EVALUATE_CONFIG")
public class EvaluateConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 标题名称
     */
    @Column(name = "TITLE", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("TITLE")
    private String title;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 申请开始时间
     */
    @Column(name = "SQKSSJ")
    @TableField("SQKSSJ")
    private LocalDateTime sqkssj;

    /**
     * 申请截至时间
     */
    @Column(name = "SQJZSJ")
    @TableField("SQJZSJ")
    private LocalDateTime sqjzsj;

    /**
     * 是否可计算成绩
     */
    @Column(name = "SFKJSCJ", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFKJSCJ")
    private JudgeMark sfkjscj;

    /**
     * 是否使用学籍异动前信息
     */
    @Column(name = "SFSYXJYDQXX", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFSYXJYDQXX")
    private JudgeMark sfsyxjydqxx;

    /**
     * 状态（成绩计算完成更新为停用状态）
     */
    @Column(name = "state", columnDefinition = ColumnType.NUMBER_1)
    @TableField("state")
    private State state;

    /**
     * 业务类型
     */
    @Column(name = "type", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("type")
    private String type;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;
    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
