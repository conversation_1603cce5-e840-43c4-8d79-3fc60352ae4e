package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.evaluate.mapper.EvaluateApprovalNodeRecordMapper;
import com.sanythadmin.project.evaluate.service.EvaluateApprovalNodeRecordService;
import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNodeRecord;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Service
public class EvaluateApprovalNodeRecordServiceImpl extends ServiceImpl<EvaluateApprovalNodeRecordMapper, EvaluateApprovalNodeRecord> implements EvaluateApprovalNodeRecordService {

    @Resource
    private EvaluateApprovalNodeRecordMapper mapper;

}
