<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateApplicationInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CPNF, JFYY, XGH, XM, XYID, ZYID, BJID, NJID, ITEM_ID, ITEM_DETAIL_ID, FJXX, CREATE_TIME, REMARK, FS, SFYSP
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
                    <if test="param.jfyy != null">
                        AND a.JFYY LIKE concat(concat('%',#{param.jfyy), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
                    <if test="param.itemId != null">
                        AND a.ITEM_ID LIKE concat(concat('%',#{param.itemId), '%')
                    </if>
                    <if test="param.itemDetailId != null">
                        AND a.ITEM_DETAIL_ID LIKE concat(concat('%',#{param.itemDetailId), '%')
                    </if>
                    <if test="param.fjxx != null">
                        AND a.FJXX LIKE concat(concat('%',#{param.fjxx), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
                    <if test="param.fs != null">
                        AND a.FS = #{param.fs}
                    </if>
                    <if test="param.sfysp != null">
                        AND a.SFYSP = #{param.sfysp}
                    </if>
    </sql>

    <select id="getScoreBySource" resultType="com.sanythadmin.project.evaluate.dto.ScoreSourceDTO"   >
        select ${param.xhColumn} as xgh, ${param.cpnfColumn} as cpnf, ${param.scoreColumn} as score
        from ${param.tableName}
        <where>
            <if test="param.xgh != null">
                AND ${param.xhColumn} = #{param.xgh}
            </if>
            <if test="param.cpnf != null">
                AND ${param.cpnfColumn} = #{param.cpnf}
            </if>
        </where>
    </select>
</mapper>
