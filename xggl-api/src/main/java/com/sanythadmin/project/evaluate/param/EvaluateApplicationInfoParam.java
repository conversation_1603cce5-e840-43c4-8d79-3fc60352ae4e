package com.sanythadmin.project.evaluate.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.enums.JudgeMark;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 加减分项申请信息查询参数
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateApplicationInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 测评年份
     */
    @QueryField(type = QueryType.EQ)
    private String cpnf;

    /**
     * 学工号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 培养层次ID
     */
    @QueryField(type = QueryType.EQ)
    private String pyccid;
    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.EQ)
    private String njid;

    /**
     * 指标ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 指标加减分项ID
     */
    @QueryField(type = QueryType.EQ)
    private String itemDetailId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否已审批
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfysp;

    /**
     * 是否提交
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sftj;
    @QueryField(type = QueryType.EQ)
    private String configId;

    /**
     * 业务类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "createTime desc";
    }

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
