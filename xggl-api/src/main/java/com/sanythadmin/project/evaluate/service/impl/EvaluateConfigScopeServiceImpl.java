package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluateConfigScope;
import com.sanythadmin.project.evaluate.mapper.EvaluateConfigScopeMapper;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 综测参数配置范围Service实现
 *
 * <AUTHOR>
 * @since 2025-02-19 13:55:05
 */
@Service
public class EvaluateConfigScopeServiceImpl extends ServiceImpl<EvaluateConfigScopeMapper, EvaluateConfigScope> implements EvaluateConfigScopeService {
    @Resource
    private EvaluateConfigScopeMapper mapper;
    @Resource
    private UserDataScopeService userDataScopeService;

    @Transactional
    @Override
    public void edit(EvaluateConfigScope scope) {
        mapper.updateById(scope);
    }

    @Override
    public PageResult<EvaluateConfigScopeDTO> page(EvaluateConfigScopeParam scopeParam, EvaluateConfigParam param) {
        UserDataScopeParam dataScopeParam = new UserDataScopeParam();
        dataScopeParam.setRoleId(SecurityUtil.getRoleId());
        dataScopeParam.setGlzXgh(SecurityUtil.getUsername());
        List<UserDataScope> scopeList = userDataScopeService.queryList(dataScopeParam);
        UserDataScope dataScope = userDataScopeService.list2Single(scopeList, true);
        if (!Objects.isNull(dataScope)) {
            if (scopeParam == null)
                scopeParam = new EvaluateConfigScopeParam();
            scopeParam.setPyccid(dataScope.getPyccid());
            scopeParam.setXyid(dataScope.getXyid());
            scopeParam.setNjid(dataScope.getNjid());
        }
        Page<EvaluateConfigScopeDTO> page = new Page<>(scopeParam.getPage(), scopeParam.getLimit());
        MyMPJLambdaWrapper<EvaluateConfigScope, EvaluateConfigScopeParam> wrapper = getEvaluateConfigScopeQueryWrapper(scopeParam, param);
        page = mapper.selectJoinPage(page, EvaluateConfigScopeDTO.class, wrapper);
        List<EvaluateConfigScopeDTO> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public List<EvaluateConfigScopeDTO> list(EvaluateConfigScopeParam scopeParam, EvaluateConfigParam param) {
        MyMPJLambdaWrapper<EvaluateConfigScope, EvaluateConfigScopeParam> wrapper = getEvaluateConfigScopeQueryWrapper(scopeParam, param);
        return mapper.selectJoinList(EvaluateConfigScopeDTO.class, wrapper);
    }

    private MyMPJLambdaWrapper<EvaluateConfigScope, EvaluateConfigScopeParam> getEvaluateConfigScopeQueryWrapper(EvaluateConfigScopeParam scopeParam, EvaluateConfigParam param) {
        MyMPJLambdaWrapper<EvaluateConfigScope, EvaluateConfigScopeParam> wrapper = new MyMPJLambdaWrapper<>(scopeParam);
        Class<EvaluateConfig> configClass = EvaluateConfig.class;
        wrapper.leftJoin(configClass, EvaluateConfig::getId, EvaluateConfigScope::getConfigId);
        wrapper.buildQueryCondition(configClass, null, param);
        Field[] fields = CommonUtil.getAllFields(configClass, false);
        SFunction<EvaluateConfig, Object>[] sFunctions = CommonUtil.buildSFunctions(configClass, fields, "id", "sqkssj", "sqjzsj");
        wrapper.selectAll(EvaluateConfigScope.class).select(sFunctions);
        wrapper.orderByDesc(EvaluateConfig::getCpnf);
        return wrapper;
    }

    @Override
    public List<EvaluateConfigScope> list(EvaluateConfigScopeParam param) {
        PageParam<EvaluateConfigScope, EvaluateConfigScopeParam> page = new PageParam<>(param);
        return mapper.selectList(page.getWrapper());
    }
}
