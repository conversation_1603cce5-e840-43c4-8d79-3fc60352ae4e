<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateApplicationReferenceInfoMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SOURCE, REMARK, XGH, CPNF, DETAIL_URL
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.source != null">
                        AND a.SOURCE LIKE concat(concat('%',#{param.source), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
                    <if test="param.detailUrl != null">
                        AND a.DETAIL_URL LIKE concat(concat('%',#{param.detailUrl), '%')
                    </if>
    </sql>
</mapper>
