package com.sanythadmin.project.evaluate.vo;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import lombok.Data;

@Data
public class EvaluateApplicationInfoVO extends EvaluateApplicationInfo {
    private String zbmc;
    private String jjfmx;
    private String jjfdm;
    private JudgeMark sffdz;
    private Double zxz;
    private Double zdz;
    private Double gdfz;
    private String itemDetailRemark;

    /**
     * 学院名称
     */
    private String xymc;
    /**
     * 专业名称
     */
    private String zymc;
    /**
     * 班级名称
     */
    private String bjmc;
    /**
     * 年级名称
     */
    private String njmc;
    /**
     * 培养层次名称
     */
    private String pyccmc;
}
