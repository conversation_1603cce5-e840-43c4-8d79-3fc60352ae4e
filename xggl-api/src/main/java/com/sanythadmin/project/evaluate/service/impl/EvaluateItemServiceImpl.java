package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.SortHelper;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateItem;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetailScope;
import com.sanythadmin.project.evaluate.mapper.EvaluateApplicationInfoMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemDetailScopeMapper;
import com.sanythadmin.project.evaluate.mapper.EvaluateItemMapper;
import com.sanythadmin.project.evaluate.param.EvaluateItemParam;
import com.sanythadmin.project.evaluate.service.EvaluateItemService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 综合测评指标Service实现
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@AllArgsConstructor
@Service
public class EvaluateItemServiceImpl extends ServiceImpl<EvaluateItemMapper, EvaluateItem> implements EvaluateItemService {
    private final EvaluateItemMapper mapper;
    private final EvaluateItemDetailMapper itemDetailMapper;
    private final EvaluateItemDetailScopeMapper itemDetailScopeMapper;
    private final EvaluateApplicationInfoMapper applicationInfoMapper;

    @Transactional
    @Override
    public void remove(String id) {
        Long count = applicationInfoMapper.selectCount(new LambdaQueryWrapper<EvaluateApplicationInfo>()
                .eq(EvaluateApplicationInfo::getItemId, id));
        if (count > 0) AssertUtil.throwMessage("删除失败, 当前指标加分规则已有申请信息");
        List<EvaluateItemDetail> itemDetails = itemDetailMapper.selectList(new LambdaQueryWrapper<EvaluateItemDetail>()
                .eq(EvaluateItemDetail::getItemId, id));
        if (!CollectionUtils.isEmpty(itemDetails)) {
            List<String> itemDetailIds = itemDetails.stream().map(EvaluateItemDetail::getId).toList();
            itemDetailScopeMapper.delete(new LambdaQueryWrapper<EvaluateItemDetailScope>().in(EvaluateItemDetailScope::getItemDetailId, itemDetailIds));
            itemDetailMapper.deleteBatchIds(itemDetailIds);
        }

        mapper.deleteById(id);
    }

    @Override
    public void editItem(EvaluateItemDTO evaluateItem) {
        if (evaluateItem.getSort() == null) {
            SortHelper<EvaluateItem> sortHelper = new SortHelper<>(this, "sort");
            evaluateItem.setSort((long) sortHelper.next());
        }
        evaluateItem.setUsername(SecurityUtil.getUsername());
        if (StringUtils.hasText(evaluateItem.getId())) {
            EvaluateItem item = mapper.selectById(evaluateItem.getId());
            evaluateItem.setCpnf(item.getCpnf());
            mapper.updateById(evaluateItem);
        } else {
            mapper.insert(evaluateItem);
        }
    }

    @Override
    public List<EvaluateItem> list(EvaluateItemParam param) {
        PageParam<EvaluateItem, EvaluateItemParam> page = new PageParam<>(param);
        return mapper.selectList(page.getWrapper());
    }
}
