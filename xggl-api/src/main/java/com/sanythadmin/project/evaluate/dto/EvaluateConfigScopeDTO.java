package com.sanythadmin.project.evaluate.dto;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.project.evaluate.entity.EvaluateConfigScope;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Created by JIANGPING on 2025/2/21.
 */
@Data
public class EvaluateConfigScopeDTO extends EvaluateConfigScope {
    /**
     * 参数名称/备注
     */
    private String title;
    /**
     * 测评年份
     */
    private String cpnf;
    /**
     * 是否使用学籍异动前信息
     */
    private JudgeMark sfsyxjydqxx;
    /**
     * 是否可计算成绩
     */
    private JudgeMark sfkjscj;
    /**
     * 业务类型
     */
    private String type;
    private String pyccmc;
    private String xymc;
    private String njmc;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
