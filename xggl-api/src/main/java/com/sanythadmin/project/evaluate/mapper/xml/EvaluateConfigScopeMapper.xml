<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateConfigScopeMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CONFIG_ID, PYCCID, XYID, NJID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.configId != null">
                        AND a.CONFIG_ID LIKE concat(concat('%',#{param.configId), '%')
                    </if>
                    <if test="param.pyccid != null">
                        AND a.PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.njid != null">
                        AND a.NJID LIKE concat(concat('%',#{param.njid), '%')
                    </if>
    </sql>
</mapper>
