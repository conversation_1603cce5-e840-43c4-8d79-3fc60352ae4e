package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.evaluate.dto.EvaluateItemDetailDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateApplicationInfo;
import com.sanythadmin.project.evaluate.entity.EvaluateItemDetail;
import com.sanythadmin.project.evaluate.param.EvaluateItemDetailParam;

import java.util.List;

/**
 * 指标加减分规则Service
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
public interface EvaluateItemDetailService extends IService<EvaluateItemDetail> {
    public void editItemDetail(EvaluateItemDetailDTO evaluateItemDetail);

    public void remove(String id);

    public List<EvaluateItemDetail> list(EvaluateItemDetailParam param);

    public PageResult<EvaluateItemDetail> listByApplicant(EvaluateItemDetailParam param, UserInfo userInfo);
}
