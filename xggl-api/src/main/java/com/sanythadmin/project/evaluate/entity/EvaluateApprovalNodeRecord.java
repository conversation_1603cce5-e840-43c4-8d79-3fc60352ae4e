package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.project.workflow.entity.BaseApprovalNodeRecord;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_APPROVAL_NODE_RECORD")
@Entity
@Table(name = "SYT_EVALUATE_APPROVAL_NODE_RECORD")
public class EvaluateApprovalNodeRecord extends BaseApprovalNodeRecord {

}
