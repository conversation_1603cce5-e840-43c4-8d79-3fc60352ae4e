<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.evaluate.mapper.EvaluateItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ZBMC, ZBLX, SFCYJS, CJLY, BFB, GDJF, TYPE, SFXS, ZDZ, XYID, REMARK, PARENT_ID, SORT, USERNAME
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.zbmc != null">
                        AND a.ZBMC LIKE concat(concat('%',#{param.zbmc), '%')
                    </if>
                    <if test="param.zblx != null">
                        AND a.ZBLX = #{param.zblx}
                    </if>
                    <if test="param.sfcyjs != null">
                        AND a.SFCYJS = #{param.sfcyjs}
                    </if>
                    <if test="param.cjly != null">
                        AND a.CJLY LIKE concat(concat('%',#{param.cjly), '%')
                    </if>
                    <if test="param.bfb != null">
                        AND a.BFB LIKE concat(concat('%',#{param.bfb), '%')
                    </if>
                    <if test="param.gdjf != null">
                        AND a.GDJF LIKE concat(concat('%',#{param.gdjf), '%')
                    </if>
                    <if test="param.type != null">
                        AND a.TYPE LIKE concat(concat('%',#{param.type), '%')
                    </if>
                    <if test="param.sfxs != null">
                        AND a.SFXS = #{param.sfxs}
                    </if>
                    <if test="param.zdz != null">
                        AND a.ZDZ LIKE concat(concat('%',#{param.zdz), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.remark != null">
                        AND a.REMARK LIKE concat(concat('%',#{param.remark), '%')
                    </if>
                    <if test="param.parentId != null">
                        AND a.PARENT_ID LIKE concat(concat('%',#{param.parentId), '%')
                    </if>
                    <if test="param.sort != null">
                        AND a.SORT LIKE concat(concat('%',#{param.sort), '%')
                    </if>
                    <if test="param.username != null">
                        AND a.USERNAME LIKE concat(concat('%',#{param.username), '%')
                    </if>
    </sql>
</mapper>
