package com.sanythadmin.project.evaluate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.evaluate.mapper.EvaluateApprovalNodeMapper;
import com.sanythadmin.project.evaluate.service.EvaluateApprovalNodeService;
import com.sanythadmin.project.evaluate.entity.EvaluateApprovalNode;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * Service实现
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Service
public class EvaluateApprovalNodeServiceImpl extends ServiceImpl<EvaluateApprovalNodeMapper, EvaluateApprovalNode> implements EvaluateApprovalNodeService {

    @Resource
    private EvaluateApprovalNodeMapper mapper;

}
