package com.sanythadmin.project.evaluate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.dto.CalQueryDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateScore;
import com.sanythadmin.project.evaluate.param.EvaluateApplicationInfoParam;
import com.sanythadmin.project.evaluate.param.EvaluateScoreParam;

import java.util.List;

/**
 * 综合测评成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
public interface EvaluateScoreService extends IService<EvaluateScore> {
    public PageResult<EvaluateScore> page(EvaluateScoreParam param);
    public List<EvaluateScore> list(EvaluateScoreParam param);
    public void scoreCalculator(CalQueryDTO calQP);
    public void scoreRankCalculator(CalQueryDTO calQP);
    public void updateEvaluatePeopleNumber(String configId);
    public void updateRankPercent(String configId);
}
