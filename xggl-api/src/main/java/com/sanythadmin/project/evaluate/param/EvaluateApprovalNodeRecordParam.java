package com.sanythadmin.project.evaluate.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询参数
 *
 * <AUTHOR>
 * @since 2024-11-28 13:55:49
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EvaluateApprovalNodeRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(type = QueryType.EQ)
    private String applicationId;

    private String approverId;

    private String bz1;

    private String bz2;

    private String bz3;

    private String bz4;

    private String bz5;

    private String nodeApproverId;
    @QueryField(type = QueryType.EQ)
    private String nodeId;

    private String realName;

    private String result;

    @QueryField(type = QueryType.EQ)
    private Integer returnWay;

    private String roleId;

    @QueryField(type = QueryType.EQ)
    private Integer stage;

    private String username;

    private String workflowId;

}
