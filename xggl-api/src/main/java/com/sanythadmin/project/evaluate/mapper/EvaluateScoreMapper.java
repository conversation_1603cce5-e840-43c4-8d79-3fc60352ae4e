package com.sanythadmin.project.evaluate.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.evaluate.entity.EvaluateScore;
import com.sanythadmin.project.evaluate.param.EvaluateScoreParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 综合测评成绩Mapper
 *
 * <AUTHOR>
 * @since 2025-03-14 15:58:36
 */
public interface EvaluateScoreMapper extends MyMPJBaseMapper<EvaluateScore> {
    public List<EvaluateScore> getRank(@Param("param") EvaluateScoreParam param);

    public void updateEvaluatePeopleNumber(@Param("configId") String configId);

    public void updateRankPercent(@Param("configId") String configId);
}
