package com.sanythadmin.project.evaluate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.common.enums.JudgeMark;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 综合测评指标
 *
 * <AUTHOR>
 * @since 2024-10-31 09:41:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_EVALUATE_ITEM")
@Entity
@Table(name = "SYT_EVALUATE_ITEM")
public class EvaluateItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 指标名称
     */
    @Column(name = "ZBMC")
    @TableField("ZBMC")
    private String zbmc;

    /**
     * 记分方式（比例，分值）
     */
    @Column(name = "ZBLX", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("ZBLX")
    private String zblx;

    /**
     * 是否参与计算
     */
    @Column(name = "SFCYJS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFCYJS")
    private JudgeMark sfcyjs;

    /**
     * 成绩来源（平均成绩, 加权成绩, 体育成绩等）
     */
    @Column(name = "CJLY")
    @TableField("CJLY")
    private String cjly;

    /**
     * 指标百分比
     */
    @Column(name = "BFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("BFB")
    private Double bfb;

    /**
     * 固定加分
     */
    @Column(name = "GDJF", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("GDJF")
    private Double gdjf;

    /**
     * 是否显示
     */
    @Column(name = "SFXS", columnDefinition = ColumnType.NUMBER_1)
    @TableField("SFXS")
    private JudgeMark sfxs;

    /**
     * 最大值
     */
    @Column(name = "ZDZ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("ZDZ")
    private Double zdz;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @TableField("REMARK")
    private String remark;

    /**
     * 父指标ID
     */
    @Column(name = "PARENT_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PARENT_ID")
    private String parentId;

    /**
     * 排序
     */
    @Column(name = "SORT", columnDefinition = ColumnType.NUMBER_10)
    @TableField("SORT")
    private Long sort;

    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 综测配置ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONFIG_ID")
    private String configId;

    /**
     * 业务类型
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;

    public String getType() {
        return StringUtils.hasText(type) ? type.toUpperCase() : null;
    }
}
