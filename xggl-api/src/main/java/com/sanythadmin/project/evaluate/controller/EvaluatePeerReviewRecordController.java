package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.AssertUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.DateUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.State;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.evaluate.dto.EvaluateConfigScopeDTO;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.param.EvaluateConfigParam;
import com.sanythadmin.project.evaluate.param.EvaluateConfigScopeParam;
import com.sanythadmin.project.evaluate.param.EvaluatePeerReviewRecordParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigScopeService;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.service.EvaluatePeerReviewRecordService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 综合测评/互评分记录控制器
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@RestController
@RequestMapping("/api/evaluate/evaluate-peer-review-record")
public class EvaluatePeerReviewRecordController extends BaseController {
    @Resource
    private EvaluatePeerReviewRecordService evaluatePeerReviewRecordService;
    @Resource
    private EvaluateConfigService evaluateConfigService;
    @Resource
    private EvaluateConfigScopeService configScopeService;


    /**
     * 管理端分页查询互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:list')")
    @GetMapping("/page")
    public PageResult<EvaluatePeerReviewRecord> page(EvaluatePeerReviewRecordParam param) {
        return evaluatePeerReviewRecordService.page(param);
    }

    /**
     * 分页查询我的互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:mylist）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:mylist')")
    @GetMapping("/myPage")
    public PageResult<EvaluatePeerReviewRecord> myPage(EvaluatePeerReviewRecordParam param) {
        return evaluatePeerReviewRecordService.myPage(param);
    }

    /**
     * 我的互评用户列表
     *
     * @param param
     * @return
     */
    @GetMapping("/myPeerReviewUsers")
    public List<EvaluatePeerReviewRecord> myPeerReviewUsers(UserInfoParam param) {
        return evaluatePeerReviewRecordService.myPeerReviewUsers(param);
    }

    /**
     * 查询全部互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:list')")
    @GetMapping()
    public List<EvaluatePeerReviewRecord> list(EvaluatePeerReviewRecordParam param) {
        PageParam<EvaluatePeerReviewRecord, EvaluatePeerReviewRecordParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return evaluatePeerReviewRecordService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:list')")
    @GetMapping("/{id}")
    public EvaluatePeerReviewRecord get(@PathVariable("id") String id) {
        return evaluatePeerReviewRecordService.getById(id);
    }

    /**
     * 添加或修改互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:operation')")
    @OperationLog(module = "互评分记录", comments = "保存互评分记录")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody List<EvaluatePeerReviewRecord> records) {
        UserInfo userInfo = SecurityUtil.getUserInfo();
        EvaluateConfigScopeParam scopeParam = new EvaluateConfigScopeParam();
        scopeParam.setPyccid(userInfo.getPyccid());
        scopeParam.setNjid(userInfo.getNjid());
        scopeParam.setXyid(userInfo.getXyid());
        EvaluateConfigParam configParam = new EvaluateConfigParam();
        configParam.setState(State.ENABLED);
        configParam.setId(records.get(0).getConfigId());
        List<EvaluateConfigScopeDTO> configs = configScopeService.list(scopeParam, configParam);
        EvaluateConfigScopeDTO config = CommonUtil.listGetOne(configs);
        AssertUtil.isTrue(DateUtil.localDateTimeIsCorrect(config.getSqkssj(), config.getSqjzsj())
                , "未在评分时间范围内");
        for (EvaluatePeerReviewRecord record : records) {
            record.setRaterAccount(userInfo.getXgh());
            record.setRater(userInfo.getXm());
            record.setCreateTime(LocalDateTime.now());
            record.setCpnf(config.getCpnf());
            evaluatePeerReviewRecordService.operation(record);
        }
    }

    /**
     * 批量删除互评分记录（权限标识：evaluate:evaluatePeerReviewRecord:remove）
     */
//    @PreAuthorize("hasAuthority('evaluate:evaluatePeerReviewRecord:remove')")
//    @OperationLog(module = "互评分记录", comments = "批量删除互评分记录")
//    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        evaluatePeerReviewRecordService.removeByIds(ids);
    }

    /**
     * 测评年份
     *
     * @return
     */
    @GetMapping("/years")
    public List<Map<String, Object>> years() {
        return evaluatePeerReviewRecordService.years();
    }
}
