package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreLycj;
import com.sanythadmin.project.score.param.ScoreLycjParam;

import java.util.List;
import java.util.Map;

/**
 * 劳育成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
public interface ScoreLycjService extends IService<ScoreLycj> {

    public PageResult<ScoreLycj> page(ScoreLycjParam param);

    public void saveScore(ScoreLycj object);

    public void removeByParam(ScoreLycjParam param);

    public List<ScoreLycj> list(ScoreLycjParam param);
    public List<Map<String, Object>> distinctData(SFunction<ScoreLycj, ?> column);
}
