package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 美育成绩
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SCORE_MYCJ")
@Entity
@Table(name = "SYT_SCORE_MYCJ")
public class ScoreMycj extends BaseScoreEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 成绩类别（第一课堂/第二课堂）
     */
    @Column(name = "TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("TYPE")
    private String type;
}
