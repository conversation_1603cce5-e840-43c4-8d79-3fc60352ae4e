package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.common.ScoreImportData;
import com.sanythadmin.project.score.common.ScoreUtil;
import com.sanythadmin.project.score.entity.ScoreTzjkcs;
import com.sanythadmin.project.score.param.ScoreTzjkcsParam;
import com.sanythadmin.project.score.service.ScoreTzjkcsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成绩管理/体质健康测试成绩控制器
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Slf4j
@RestController
@RequestMapping("/api/score/score-tzjkcs")
public class ScoreTzjkcsController extends BaseController {
    @Resource
    private ScoreTzjkcsService scoreTzjkcsService;

    /**
     * 分页查询体质健康测试成绩（权限标识：score:scoreTzjkcs:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:list')")
    @GetMapping("/page")
    public PageResult<ScoreTzjkcs> page(ScoreTzjkcsParam param) {
        return scoreTzjkcsService.page(param);
    }

    /**
     * 查询全部体质健康测试成绩（权限标识：score:scoreTzjkcs:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:list')")
    @GetMapping()
    public List<ScoreTzjkcs> list(ScoreTzjkcsParam param) {
        return scoreTzjkcsService.list(param);
    }

    /**
     * 根据id查询体质健康测试成绩（权限标识：score:scoreTzjkcs:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:list')")
    @GetMapping("/{id}")
    public ScoreTzjkcs get(@PathVariable("id") String id) {
        return scoreTzjkcsService.getById(id);
    }

    /**
     * 添加或修改体质健康测试成绩（权限标识：score:scoreTzjkcs:operation）
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:operation')")
    @OperationLog(module = "体质健康测试成绩", comments = "保存体质健康测试成绩")
    @PostMapping("/operation")
    public void save(@RequestBody ScoreTzjkcs scoreTzjkcs) {
        if (StringUtils.hasLength(scoreTzjkcs.getId())) {
            scoreTzjkcsService.updateById(scoreTzjkcs);
        } else {
            scoreTzjkcsService.save(scoreTzjkcs);
        }
    }

    /**
     * 批量删除体质健康测试成绩（权限标识：score:scoreTzjkcs:remove）
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:remove')")
    @OperationLog(module = "体质健康测试成绩", comments = "批量删除体质健康测试成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        scoreTzjkcsService.removeByIds(ids);
    }

    /**
     * 导入模块下载（权限标识：score:scoreTzjkcs:import）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:import')")
    @OperationLog(module = "体质健康测试成绩", comments = "体质健康测试成绩导出")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        try {
            ExcelUtils.dataImportTemplate(getHeads(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 成绩数据导出（权限标识：score:scoreTzjkcs:exportData）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:exportData')")
    @OperationLog(module = "体质健康测试成绩", comments = "体质健康测试成绩导出")
    @GetMapping("/exportData")
    public void exportData(HttpServletResponse response, ScoreTzjkcsParam param) {
        try {
            List<ScoreTzjkcs> list = scoreTzjkcsService.list(param);
            String[] titles = {"测评年份", "学号", "姓名", "成绩"};
            String[] property = {"cpnf", "xgh", "xm", "score"};
            EasyExcelHelper.exportExcel(list, titles, property, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 成绩数据导入（权限标识：score:scoreTzjkcs:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('score:scoreTzjkcs:import')")
    @OperationLog(module = "体质健康测试成绩", comments = "体质健康测试成绩导入")
    @PostMapping("/importData")
    public void importData(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "configId", required = false) String configId) throws Exception {
        List<ExcelUtils.Head> heads = getHeads();
        ScoreImportData<ScoreTzjkcs> importData = ScoreUtil.importData(ScoreTzjkcs.class, file, heads, configId);
        List<ScoreTzjkcs> list = importData.getList();
        list.forEach(object -> scoreTzjkcsService.saveScore(object));
    }

    private static List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
//        heads.add(new ExcelUtils.Head("测评年份", "cpnf"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("成绩", "score", "(-)?\\d+(\\.\\d+)?"));
        heads.add(new ExcelUtils.Head("等级", "grade", false));
        return heads;
    }

    /**
     * 测评年份
     * @return
     */
    @GetMapping("/years")
    public List<Map<String, Object>> years() {
        return scoreTzjkcsService.years();
    }
}
