package com.sanythadmin.project.score.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;

import java.util.Objects;

public enum CourseMarkType implements TextBaseEnum {
    CanYuJiSuan("参与计算"),
    BuCanYuJiSuan("不参与计算");

    @JsonValue
    private final String text;

    CourseMarkType(String text) {
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @JsonCreator
    public static CourseMarkType fromValue(String value) {
        for (CourseMarkType markType : CourseMarkType.values()) {
            String string = markType.toString();
            String text = markType.getText();
            if (Objects.equals(string, value) || Objects.equals(text, value))
                return markType;
        }
        return null;
    }
}
