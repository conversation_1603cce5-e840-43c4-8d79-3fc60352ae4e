package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.score.entity.ScoreLycj;
import com.sanythadmin.project.score.mapper.ScoreLycjMapper;
import com.sanythadmin.project.score.param.ScoreLycjParam;
import com.sanythadmin.project.score.service.ScoreLycjService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 劳育成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Service
public class ScoreLycjServiceImpl extends ServiceImpl<ScoreLycjMapper, ScoreLycj> implements ScoreLycjService {

    @Resource
    private ScoreLycjMapper mapper;

    @Override
    public PageResult<ScoreLycj> page(ScoreLycjParam param) {
        PageParam<ScoreLycj, ScoreLycjParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = mapper.selectPageWithPermission(page, page.getOrderWrapper(), new MyMPQueryParams(ScoreLycj.class));
        List<ScoreLycj> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, page.getTotal());
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void saveScore(ScoreLycj object) {
        mapper.delete(new LambdaQueryWrapper<ScoreLycj>()
                .eq(ScoreLycj::getXgh, object.getXgh())
                .eq(ScoreLycj::getType, object.getType())
                .eq(ScoreLycj::getCpnf, object.getCpnf()));
        mapper.insert(object);
    }

    @Transactional
    @Override
    public void removeByParam(ScoreLycjParam param) {
        PageParam<ScoreLycj, ScoreLycjParam> page = new PageParam<>(param);
        mapper.delete(page.getWrapper());
    }

    @Override
    public List<ScoreLycj> list(ScoreLycjParam param) {
        PageParam<ScoreLycj, ScoreLycjParam> page = new PageParam<>(param);
        return mapper.selectListWithPermission(page.getWrapper(), new MyMPQueryParams(ScoreLycj.class));
    }

    @Override
    public List<Map<String, Object>> distinctData(SFunction<ScoreLycj, ?> column) {
        List<Map<String, Object>> maps = baseMapper.selectMaps(new LambdaQueryWrapper<ScoreLycj>()
                .select(column).groupBy(column).orderByDesc(column));
        List<Map<String, Object>> newMaps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    newMap.put(entry.getKey().toLowerCase(), entry.getValue());
                }
                newMaps.add(newMap);
            }
        }
        return newMaps;
    }
}
