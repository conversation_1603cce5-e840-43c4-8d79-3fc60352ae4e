package com.sanythadmin.project.score.common;

import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.constants.ConstantsEvaluate;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ScoreUtil {
    public static <T> ScoreImportData<T> importData(Class<T> tClass, MultipartFile file, List<ExcelUtils.Head> heads, String configId) throws Exception {
        SysParamService paramService = SpringContextUtil.getBean(SysParamService.class);
        SysParam sysParam = paramService.getByParamName(ConstantsEvaluate.ZhongHeCePingNianFen);
        if (sysParam == null)
            AssertUtil.throwMessage("请先设置测评年份");
        List<T> infos = ExcelUtils.readExcelToEntity(tClass, file.getInputStream(), file.getOriginalFilename(), heads);
        UserInfoService userInfoService = SpringContextUtil.getBean(UserInfoService.class);
        List<UserInfo> userInfoList = userInfoService.listWithPermission(null, CodeXsztParam.currentStudent(), UserInfo::getXm, UserInfo::getXgh);
        if (CollectionUtils.isEmpty(userInfoList))
            AssertUtil.throwMessage("学生信息不存在");
        Map<String, UserInfo> userInfoMap = userInfoList.stream().collect(Collectors.toMap(UserInfo::getXgh, Function.identity()));
        SysAccount account = SecurityUtil.getAccount();
        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        Field[] fields = CommonUtil.getAllFields(tClass);
        List<String> list = Arrays.stream(fields).map(Field::getName).toList();
        boolean hasCreateTime = list.contains("createTime");
        boolean hasConfigId = list.contains("configId");
        for (int i = 0; i < infos.size(); i++) {
            T info = infos.get(i);
            String xgh = String.valueOf(CommonUtil.getValue("xgh", info)).trim();
            UserInfo userInfo = userInfoMap.get(xgh);
            if (Objects.isNull(userInfo)) {
                errors.add(ExcelImportError.data(i + 1, xgh, "学生信息不存在"));
                continue;
            }
            BeanUtils.copyProperties(userInfo, info);
            if (hasCreateTime)
                CommonUtil.setValue(info, "createTime", LocalDateTime.now());
            if (hasConfigId)
                CommonUtil.setValue(info, "configId", configId);
            Object value = CommonUtil.getValue("cpnf", info);
            if (value == null || !StringUtils.hasText(String.valueOf(value)))
                CommonUtil.setValue(info, "cpnf", sysParam.getParamValue());
            CommonUtil.setValue(info, "czr", account.getRealName());
            CommonUtil.setValue(info, "czrzh", account.getUsername());
        }

        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }
        return new ScoreImportData<>(infos, userInfoMap);
    }
}
