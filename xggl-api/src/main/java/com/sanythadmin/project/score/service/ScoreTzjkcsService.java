package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreTzjkcs;
import com.sanythadmin.project.score.param.ScoreTzjkcsParam;

import java.util.List;
import java.util.Map;

/**
 * 体质健康测试成绩Service
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
public interface ScoreTzjkcsService extends IService<ScoreTzjkcs> {
    public PageResult<ScoreTzjkcs> page(ScoreTzjkcsParam param);

    public void saveScore(ScoreTzjkcs object);

    public void removeByParam(ScoreTzjkcsParam param);

    public List<ScoreTzjkcs> list(ScoreTzjkcsParam param);
    public List<Map<String, Object>> years();
}
