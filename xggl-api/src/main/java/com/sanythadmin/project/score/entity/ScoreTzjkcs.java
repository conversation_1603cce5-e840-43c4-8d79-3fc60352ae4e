package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 体质健康测试成绩
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SCORE_TZJKCS")
@Entity
@Table(name = "SYT_SCORE_TZJKCS")
public class ScoreTzjkcs extends BaseScoreEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 健康测试等级
     */
    @Column(name = "grade", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("grade")
    private String grade;
}
