package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.CourseScoreItem;
import com.sanythadmin.project.score.param.CourseScoreItemParam;
import com.sanythadmin.project.score.service.CourseScoreItemService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 成绩管理/学生各科课程成绩控制器
 *
 * <AUTHOR>
 * @since 2025-03-17 17:28:59
 */
@RestController
@RequestMapping("/api/score/course-score-item")
public class CourseScoreItemController extends BaseController {
    @Resource
    private CourseScoreItemService courseScoreItemService;

    /**
     * 分页查询学生各科课程成绩（权限标识：score:courseScoreItem:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreItem:list')")
    @GetMapping("/page")
    public PageResult<CourseScoreItem> page(CourseScoreItemParam param) {
        PageParam<CourseScoreItem, CourseScoreItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = courseScoreItemService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部学生各科课程成绩（权限标识：score:courseScoreItem:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreItem:list')")
    @GetMapping()
    public List<CourseScoreItem> list(CourseScoreItemParam param) {
        PageParam<CourseScoreItem, CourseScoreItemParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return courseScoreItemService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询学生各科课程成绩（权限标识：score:courseScoreItem:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreItem:list')")
    @GetMapping("/{id}")
    public CourseScoreItem get(@PathVariable("id") String id) {
        return courseScoreItemService.getById(id);
    }

    /**
     * 添加或修改学生各科课程成绩（权限标识：score:courseScoreItem:operation）
     */
    @PreAuthorize("hasAuthority('score:courseScoreItem:operation')")
    @OperationLog(module = "学生各科课程成绩", comments = "保存学生各科课程成绩")
    @PostMapping("/operation")
    public void save(@RequestBody CourseScoreItem courseScoreItem) {
        if (StringUtils.hasLength(courseScoreItem.getId())) {
            courseScoreItemService.updateById(courseScoreItem);
        } else {
            courseScoreItemService.save(courseScoreItem);
        }
    }

    /**
     * 批量删除学生各科课程成绩（权限标识：score:courseScoreItem:remove）
     */
    @PreAuthorize("hasAuthority('score:courseScoreItem:remove')")
    @OperationLog(module = "学生各科课程成绩", comments = "批量删除学生各科课程成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        courseScoreItemService.removeByIds(ids);
    }

    /**
     * 根据字段名distinct获取数据
     *
     * @param fieldName
     * @return
     */
    @GetMapping("/distinctData")
    public List<Map<String, Object>> distinctData(CourseScoreItemParam param, @RequestParam(value = "fieldName") String fieldName) {
        return courseScoreItemService.distinctByField(param, CommonUtil.split(fieldName));
    }

    /**
     * 根据字段名distinct分页获取数据
     *
     * @param fieldName
     * @return
     */
    @GetMapping("/pageDistinctData")
    public PageResult<Map<String, Object>> pageDistinctData(CourseScoreItemParam param, @RequestParam(value = "fieldName") String fieldName) {
        return courseScoreItemService.pageDistinctByField(param, CommonUtil.split(fieldName));
    }
}
