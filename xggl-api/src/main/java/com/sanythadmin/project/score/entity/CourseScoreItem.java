package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 学生各科课程成绩
 *
 * <AUTHOR>
 * @since 2025-03-17 17:28:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_COURSE_SCORE_ITEM")
@Entity
@Table(name = "SYT_COURSE_SCORE_ITEM")
public class CourseScoreItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XM")
    private String xm;

    /**
     * 学院名称
     */
    @Column(name = "XYMC", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XYMC")
    private String xymc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("ZYMC")
    private String zymc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("BJMC")
    private String bjmc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("NJMC")
    private String njmc;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 学年
     */
    @Column(name = "XN", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XN")
    private String xn;

    /**
     * 课程号
     */
    @Column(name = "KCH", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("KCH")
    private String kch;

    /**
     * 课程名
     */
    @Column(name = "KCM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("KCM")
    private String kcm;

    /**
     * 课序号
     */
    @Column(name = "KXH", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("KXH")
    private String kxh;

    /**
     * 课程成绩
     */
    @Column(name = "KCCJ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("KCCJ")
    private Double kccj;

    /**
     * 课程属性代码
     */
    @Column(name = "KCSXDM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("KCSXDM")
    private String kcsxdm;

    /**
     * 课程属性名称
     */
    @Column(name = "KCSXMC", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("KCSXMC")
    private String kcsxmc;

    /**
     * 绩点成绩
     */
    @Column(name = "JDCJ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("JDCJ")
    private Double jdcj;

    /**
     * 学分
     */
    @Column(name = "XF", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("XF")
    private Double xf;

    /**
     * 学时
     */
    @Column(name = "XS", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("XS")
    private Double xs;

    /**
     * 考试类型代码
     */
    @Column(name = "KSLXDM", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("KSLXDM")
    private String kslxdm;

    /**
     * 考试类型名称
     */
    @Column(name = "KSLXMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("KSLXMC")
    private String kslxmc;

    /**
     * 修读方式名称
     */
    @Column(name = "XDFSMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XDFSMC")
    private String xdfsmc;

    /**
     * 修读方式代码
     */
    @Column(name = "XDFSDM", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XDFSDM")
    private String xdfsdm;

    /**
     * 补考方式代码
     */
    @Column(name = "BKFSDM", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("BKFSDM")
    private String bkfsdm;

    /**
     * 补考方式名称
     */
    @Column(name = "BKFSMC", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("BKFSMC")
    private String bkfsmc;

    /**
     * 成绩标识
     */
    @Column(name = "CJBS", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CJBS")
    private String cjbs;

    /**
     * 未通过原因说明
     */
    @Column(name = "WTGYYSM", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("WTGYYSM")
    private String wtgyysm;

    /**
     * 未通过原因代码
     */
    @Column(name = "WTGYYDM", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("WTGYYDM")
    private String wtgyydm;

    /**
     * 考试时间
     */
    @Column(name = "KSSJ", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("KSSJ")
    private String kssj;

    /**
     * 分类
     */
    @Column(name = "FL", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("FL")
    private String fl;

    /**
     * 备注
     */
    @Column(name = "BZ", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField("BZ")
    private String bz;
}
