package com.sanythadmin.project.score.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
 * 成绩管理/课程属性占比配置
 *
 * <AUTHOR>
 * @since 2025-03-18 15:15:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_COURSE_PROPERTIES_PERCENT")
@Entity
@Table(name = "SYT_COURSE_PROPERTIES_PERCENT")
public class CoursePropertiesPercent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学年
     */
    @Column(name = "XN")
    @TableField("XN")
    private String xn;

    /**
     * 课程属性代码
     */
    @Column(name = "KCSXDM")
    @TableField("KCSXDM")
    private String kcsxdm;

    /**
     * 课程属性名称
     */
    @Column(name = "KCSXMC")
    @TableField("KCSXMC")
    private String kcsxmc;

    /**
     * 百分比
     */
    @Column(name = "PERCENT", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField("PERCENT")
    private Double percent;

}
