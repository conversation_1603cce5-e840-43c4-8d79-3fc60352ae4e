package com.sanythadmin.project.score.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程属性占比配置查询参数
 *
 * <AUTHOR>
 * @since 2025-03-18 15:15:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoursePropertiesPercentParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学年
     */
    private String xn;

    /**
     * 课程属性代码
     */
    private String kcsxdm;

    /**
     * 课程属性名称
     */
    private String kcsxmc;

}
