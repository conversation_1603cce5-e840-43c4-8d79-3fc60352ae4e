package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 体育活动参与加分
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SCORE_TYHDCYJF")
@Entity
@Table(name = "SYT_SCORE_TYHDCYJF")
public class ScoreTyhdcyjf extends BaseScoreEntity {
    private static final long serialVersionUID = 1L;
}
