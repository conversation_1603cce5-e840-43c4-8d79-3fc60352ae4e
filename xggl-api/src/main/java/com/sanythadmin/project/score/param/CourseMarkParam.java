package com.sanythadmin.project.score.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.project.score.enums.CourseMarkType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 标记课程查询参数
 *
 * <AUTHOR>
 * @since 2025-03-18 15:52:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CourseMarkParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学年学期
     */
    @QueryField(type = QueryType.EQ)
    private String xnxq;

    /**
     * 课程号
     */
    private String kch;

    /**
     * 课程名
     */
    private String kcm;

    /**
     * 课程属性名称
     */
    @QueryField(type = QueryType.EQ)
    private String kcsxmc;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String xyid;
    @QueryField(type = QueryType.IS_NULL, value = "xyid")
    private Boolean xyidIsNull;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String bjid;
    @QueryField(type = QueryType.IS_NULL, value = "bjid")
    private Boolean bjidIsNull;

    /**
     * 操作人
     */
    @QueryField(type = QueryType.LIKE)
    private String czr;

    /**
     * 操作人账号
     */
    @QueryField(type = QueryType.EQ)
    private String czrzh;

    /**
     * 标记类型
     */
    @QueryField(type = QueryType.EQ)
    private CourseMarkType markType;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "ID desc,CREATE_TIME desc";
    }
}
