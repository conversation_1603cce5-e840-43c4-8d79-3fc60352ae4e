package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.param.CodeBjbParam;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.score.entity.CourseMark;
import com.sanythadmin.project.score.enums.CourseMarkType;
import com.sanythadmin.project.score.param.CourseMarkParam;
import com.sanythadmin.project.score.pojo.CourseMarkDTO;
import com.sanythadmin.project.score.pojo.CourseMarkVO;
import com.sanythadmin.project.score.service.CourseMarkService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 成绩管理/课程标记控制器
 *
 * <AUTHOR>
 * @since 2025-03-18 15:52:35
 */
@RestController
@RequestMapping("/api/score/course-mark")
public class CourseMarkController extends BaseController {
    @Resource
    private CourseMarkService courseMarkService;
    @Resource
    private CodeBjbService codeBjbService;

    /**
     * 分页查询标记课程（权限标识：score:courseMark:list）
     */
    @PreAuthorize("hasAuthority('score:courseMark:list')")
    @GetMapping("/page")
    public PageResult<CourseMarkVO> page(CourseMarkParam param) {
        return courseMarkService.page(param);
    }

    /**
     * 查询全部标记课程（权限标识：score:courseMark:list）
     */
    @PreAuthorize("hasAuthority('score:courseMark:list')")
    @GetMapping()
    public List<CourseMark> list(CourseMarkParam param) {
        PageParam<CourseMark, CourseMarkParam> page = new PageParam<>(param);
        return courseMarkService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询标记课程（权限标识：score:courseMark:list）
     */
    @PreAuthorize("hasAuthority('score:courseMark:list')")
    @GetMapping("/{id}")
    public CourseMark get(@PathVariable("id") String id) {
        return courseMarkService.getById(id);
    }

    /**
     * 添加标记课程（权限标识：score:courseMark:operation）
     */
    @PreAuthorize("hasAuthority('score:courseMark:operation')")
    @OperationLog(module = "标记课程", comments = "保存标记课程")
    @PostMapping("/operation")
    public void save(@RequestBody CourseMarkDTO param) {
        List<CourseMark> selectData = param.getSelectData();
        if (!CollectionUtils.isEmpty(param.getBjid())) {
            CodeBjbParam classParam = new CodeBjbParam();
            classParam.setId(CommonUtil.strJoin(param.getBjid()));
            List<CodeBjb> classList = codeBjbService.list(classParam);
            classList.forEach(obj -> save(obj.getXyid(), obj.getId(), param.getMarkType(), selectData));
        } else {
            List<String> deptIds = param.getXyid();
            if (CollectionUtils.isEmpty(deptIds)) {
                deptIds = new ArrayList<>();
                deptIds.add(null);
            }
            deptIds.forEach(str -> save(str, null, param.getMarkType(), selectData));
        }
    }

    private void save(String deptId, String classId, CourseMarkType markType, List<CourseMark> selectData) {
        for (CourseMark selectDatum : selectData) {
            CourseMark courseMark = new CourseMark();
            courseMark.setXnxq(selectDatum.getXnxq());
            courseMark.setKcm(selectDatum.getKcm());
            courseMark.setKch(selectDatum.getKch());
            courseMark.setKcsxmc(selectDatum.getKcsxmc());
            courseMark.setXyid(deptId);
            courseMark.setBjid(classId);
            courseMark.setMarkType(markType);
            CourseMarkParam queryParam = new CourseMarkParam();
            BeanUtils.copyProperties(courseMark, queryParam);
            if (!StringUtils.hasText(queryParam.getXyid()))
                queryParam.setXyidIsNull(true);
            if (!StringUtils.hasText(queryParam.getBjid()))
                queryParam.setBjidIsNull(true);
            List<CourseMark> temps = list(queryParam);
            if (!CollectionUtils.isEmpty(temps))
                continue;
            courseMark.setCreateTime(LocalDateTime.now());
            courseMark.setCzr(SecurityUtil.getRealName());
            courseMark.setCzrzh(SecurityUtil.getUsername());
            courseMarkService.save(courseMark);
        }
    }

    /**
     * 批量删除标记课程（权限标识：score:courseMark:remove）
     */
    @PreAuthorize("hasAuthority('score:courseMark:remove')")
    @OperationLog(module = "标记课程", comments = "批量删除标记课程")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        courseMarkService.removeByIds(ids);
    }
}
