package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.utils.JxlsUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.entity.TemplateFile;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.project.evaluate.constants.ConstantsEvaluate;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.score.common.ScoreImportData;
import com.sanythadmin.project.score.common.ScoreUtil;
import com.sanythadmin.project.score.entity.CourseScoreResult;
import com.sanythadmin.project.score.param.CourseScoreItemParam;
import com.sanythadmin.project.score.param.CourseScoreResultParam;
import com.sanythadmin.project.score.service.CourseScoreResultService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 成绩管理/课程成绩结果控制器
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
@Slf4j
@RestController
@RequestMapping("/api/score/course-score-result")
public class CourseScoreResultController extends BaseController {
    @Resource
    private CourseScoreResultService courseScoreResultService;
    @Resource
    private SysParamService sysParamService;

    /**
     * 分页查询课程成绩（权限标识：score:courseScoreResult:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:list')")
    @GetMapping("/page")
    public PageResult<CourseScoreResult> page(CourseScoreResultParam param) {
        return courseScoreResultService.page(param);
    }

    /**
     * 查询全部课程成绩（权限标识：score:courseScoreResult:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:list')")
    @GetMapping()
    public List<CourseScoreResult> list(CourseScoreResultParam param) {
        PageParam<CourseScoreResult, CourseScoreResultParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return courseScoreResultService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询课程成绩（权限标识：score:courseScoreResult:list）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:list')")
    @GetMapping("/{id}")
    public CourseScoreResult get(@PathVariable("id") String id) {
        return courseScoreResultService.getById(id);
    }

    /**
     * 课程成绩计算（权限标识：score:courseScoreResult:operation）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:operation')")
    @OperationLog(module = "课程成绩", comments = "课程成绩计算")
    @PostMapping("/scoreResultCalculator")
    public void save(@RequestBody CourseScoreItemParam itemParam, EvaluateConfig config) {
        if (!StringUtils.hasText(config.getCpnf())) {
            SysParam sysParam = sysParamService.getByParamName(ConstantsEvaluate.ZhongHeCePingNianFen);
            config.setCpnf(sysParam.getParamValue());
        }
        HashMap<String, CourseScoreResult> scoreResultMap = courseScoreResultService.scoreResultCalc(itemParam, config);
        List<CompletableFuture<String>> completableFutures = new ArrayList<>();
        long timeMillis = System.currentTimeMillis();
        for (Map.Entry<String, CourseScoreResult> entry : scoreResultMap.entrySet()) {
            CompletableFuture<String> future = courseScoreResultService.saveOrUpdateCourseResult(entry.getValue(), true);
            completableFutures.add(future);
        }
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[]{})).join();
        CourseScoreResultParam scoreResultParam = new CourseScoreResultParam();
        scoreResultParam.setCpnf(config.getCpnf());
        courseScoreResultService.updateRangePeopleNumber(scoreResultParam);
        List<CourseScoreResult> rankAndPercent = courseScoreResultService.getRankAndPercent(scoreResultParam);
        for (CourseScoreResult result : rankAndPercent) {
            courseScoreResultService.updateRankAndPercent(result);
        }
        System.out.println(("耗时：" + (System.currentTimeMillis() - timeMillis) / 1000) + "秒");
    }

    /**
     * 批量删除课程成绩（权限标识：score:courseScoreResult:remove）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:remove')")
    @OperationLog(module = "课程成绩", comments = "批量删除课程成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        courseScoreResultService.removeByIds(ids);
    }

    /**
     * 成绩数据导出（权限标识：score:courseScoreResult:exportData）
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:exportData')")
    @GetMapping("/exportData")
    public void exportData(TemplateFile templateFile, CourseScoreResultParam param, HttpServletResponse response) {
        List<CourseScoreResult> results = courseScoreResultService.listWithPermission(param);
        UserInfoUtil.codeTextSet(results);
        JxlsUtil.exportExcel(templateFile, results, response);
    }

    /**
     * 导入模板下载（权限标识：score:courseScoreResult:import）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:import')")
    @OperationLog(module = "课程成绩", comments = "课程成绩导入模板下载")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        try {
            String[] titles = {"测评年份（必填）", "学年学期（必填）", "学号（必填）", "姓名", "智育成绩（必填）", "平均成绩（必填）"};
            EasyExcelHelper.exportExcel(null, titles, null, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 成绩数据导入（权限标识：score:courseScoreResult:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('score:courseScoreResult:import')")
    @OperationLog(module = "课程成绩", comments = "课程成绩导入")
    @PostMapping("/importData")
    public void importData(@RequestParam(name = "file") MultipartFile file) throws Exception {
        List<ExcelUtils.Head> heads = new ArrayList<>();
        heads.add(new ExcelUtils.Head("测评年份", "cpnf"));
        heads.add(new ExcelUtils.Head("学年学期", "xnxq"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("智育成绩", "jqcj", "(-)?\\d+(\\.\\d+)?"));
        heads.add(new ExcelUtils.Head("平均成绩", "pjcj", "(-)?\\d+(\\.\\d+)?"));
        ScoreImportData<CourseScoreResult> importData = ScoreUtil.importData(CourseScoreResult.class, file, heads, null);
        List<CourseScoreResult> list = importData.getList();
        UserInfoUtil.codeTextSet(list);
        List<CompletableFuture<String>> completableFutures = new ArrayList<>();
        long timeMillis = System.currentTimeMillis();
        for (CourseScoreResult result : list) {
            CompletableFuture<String> future = courseScoreResultService.saveOrUpdateCourseResult(result, false);
            completableFutures.add(future);
        }
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[]{})).join();
        System.out.println(("耗时：" + (System.currentTimeMillis() - timeMillis) / 1000) + "秒");
    }
}
