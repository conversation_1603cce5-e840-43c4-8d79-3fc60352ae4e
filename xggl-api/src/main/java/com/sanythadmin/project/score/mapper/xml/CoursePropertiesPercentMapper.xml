<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.score.mapper.CoursePropertiesPercentMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XN, KCSXDM, KCSXMC, PERCENT
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xn != null">
                        AND a.XN LIKE concat(concat('%',#{param.xn), '%')
                    </if>
                    <if test="param.kcsxdm != null">
                        AND a.<PERSON><PERSON>XDM LIKE concat(concat('%',#{param.kcsxdm), '%')
                    </if>
                    <if test="param.kcsxmc != null">
                        AND a.KCSXMC LIKE concat(concat('%',#{param.kcsxmc), '%')
                    </if>
                    <if test="param.percent != null">
                        AND a.PERCENT = #{param.percent}
                    </if>
    </sql>
</mapper>
