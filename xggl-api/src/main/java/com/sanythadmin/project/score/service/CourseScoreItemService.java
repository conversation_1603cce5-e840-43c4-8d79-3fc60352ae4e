package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.CourseScoreItem;
import com.sanythadmin.project.score.param.CourseScoreItemParam;

import java.util.List;
import java.util.Map;

/**
 * 学生各科课程成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-17 17:28:59
 */
public interface CourseScoreItemService extends IService<CourseScoreItem> {

    public void saveScore(CourseScoreItem scoreItem);

    public List<Map<String, Object>> distinctByField(CourseScoreItemParam param, String... fieldName);
    public PageResult<Map<String, Object>> pageDistinctByField(CourseScoreItemParam param, String... fieldName);

    public List<Map<String, Object>> distinctByField(CourseScoreItemParam param, List<SFunction<CourseScoreItem, ?>> field);

}
