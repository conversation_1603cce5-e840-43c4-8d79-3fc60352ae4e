package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreTyhdcyjf;
import com.sanythadmin.project.score.entity.ScoreTzjkcs;
import com.sanythadmin.project.score.mapper.ScoreTzjkcsMapper;
import com.sanythadmin.project.score.param.ScoreTzjkcsParam;
import com.sanythadmin.project.score.service.ScoreTzjkcsService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 体质健康测试成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Service
public class ScoreTzjkcsServiceImpl extends ServiceImpl<ScoreTzjkcsMapper, ScoreTzjkcs> implements ScoreTzjkcsService {

    @Resource
    private ScoreTzjkcsMapper mapper;

    @Override
    public PageResult<ScoreTzjkcs> page(ScoreTzjkcsParam param) {
        PageParam<ScoreTzjkcs, ScoreTzjkcsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = mapper.selectPageWithPermission(page, page.getOrderWrapper(), new MyMPQueryParams(ScoreTzjkcs.class));
        List<ScoreTzjkcs> records = page.getRecords();
        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void saveScore(ScoreTzjkcs object) {
        mapper.delete(new LambdaQueryWrapper<ScoreTzjkcs>()
                .eq(ScoreTzjkcs::getXgh, object.getXgh())
                .eq(ScoreTzjkcs::getCpnf, object.getCpnf()));
        mapper.insert(object);
    }

    @Override
    public void removeByParam(ScoreTzjkcsParam param) {
        PageParam<ScoreTzjkcs, ScoreTzjkcsParam> page = new PageParam<>(param);
        mapper.delete(page.getWrapper());
    }

    @Override
    public List<ScoreTzjkcs> list(ScoreTzjkcsParam param) {
        PageParam<ScoreTzjkcs, ScoreTzjkcsParam> page = new PageParam<>(param);
        return mapper.selectListWithPermission(page.getWrapper(), new MyMPQueryParams(ScoreTzjkcs.class));
    }

    @Override
    public List<Map<String, Object>> years() {
        List<Map<String, Object>> maps = baseMapper.selectMaps(new LambdaQueryWrapper<ScoreTzjkcs>()
                .select(ScoreTzjkcs::getCpnf)
                .groupBy(ScoreTzjkcs::getCpnf)
                .orderByDesc(ScoreTzjkcs::getCpnf));
        List<Map<String, Object>> newMaps = new ArrayList<>();
        if (!CollectionUtils.isEmpty(maps)) {
            for (Map<String, Object> map : maps) {
                Map<String, Object> newMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    newMap.put(entry.getKey().toLowerCase(), entry.getValue());
                }
                newMaps.add(newMap);
            }
        }
        return newMaps;
    }
}
