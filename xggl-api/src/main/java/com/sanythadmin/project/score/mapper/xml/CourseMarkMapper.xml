<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.score.mapper.CourseMarkMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XNXQ, KCH, KCM, KCSXMC, XYID, CZR, CZRZH, CREATE_TIME, MARK_TYPE
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xnxq != null">
                        AND a.XNXQ LIKE concat(concat('%',#{param.xnxq), '%')
                    </if>
                    <if test="param.kch != null">
                        AND a.KCH LIKE concat(concat('%',#{param.kch), '%')
                    </if>
                    <if test="param.kcm != null">
                        AND a.KCM LIKE concat(concat('%',#{param.kcm), '%')
                    </if>
                    <if test="param.kcsxmc != null">
                        AND a.KCSXMC LIKE concat(concat('%',#{param.kcsxmc), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.czr != null">
                        AND a.CZR LIKE concat(concat('%',#{param.czr), '%')
                    </if>
                    <if test="param.czrzh != null">
                        AND a.CZRZH LIKE concat(concat('%',#{param.czrzh), '%')
                    </if>
                    <if test="param.createTime != null">
                        AND a.CREATE_TIME LIKE concat(concat('%',#{param.createTime), '%')
                    </if>
                    <if test="param.markType != null">
                        AND a.MARK_TYPE LIKE concat(concat('%',#{param.markType), '%')
                    </if>
    </sql>
</mapper>
