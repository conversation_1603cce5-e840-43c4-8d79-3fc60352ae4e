package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.CourseMark;
import com.sanythadmin.project.score.param.CourseMarkParam;
import com.sanythadmin.project.score.pojo.CourseMarkVO;

/**
 * 标记课程Service
 *
 * <AUTHOR>
 * @since 2025-03-18 15:52:35
 */
public interface CourseMarkService extends IService<CourseMark> {
    public PageResult<CourseMarkVO> page(CourseMarkParam param);

}
