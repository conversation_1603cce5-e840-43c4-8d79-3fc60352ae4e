package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanythadmin.common.core.web.ColumnType;
import com.sanythadmin.project.score.enums.CourseMarkType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 成绩管理/标记课程（北京外国语大学除必修课程外个别学院须标记选修课程加入计算智育成绩）
 *
 * <AUTHOR>
 * @since 2025-03-18 15:52:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_COURSE_MARK")
@Entity
@Table(name = "SYT_COURSE_MARK")
public class CourseMark implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ")
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 课程号
     */
    @Column(name = "KCH")
    @TableField("KCH")
    private String kch;

    /**
     * 课程名
     */
    @Column(name = "KCM")
    @TableField("KCM")
    private String kcm;

    /**
     * 课程属性名称
     */
    @Column(name = "KCSXMC")
    @TableField("KCSXMC")
    private String kcsxmc;

    /**
     * 学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 操作人
     */
    @Column(name = "CZR")
    @TableField("CZR")
    private String czr;

    /**
     * 操作人账号
     */
    @Column(name = "CZRZH")
    @TableField("CZRZH")
    private String czrzh;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 标记类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "MARK_TYPE", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("MARK_TYPE")
    private CourseMarkType markType;

}
