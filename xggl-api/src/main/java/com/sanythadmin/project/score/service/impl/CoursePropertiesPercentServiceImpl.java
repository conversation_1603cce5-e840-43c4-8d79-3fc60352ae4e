package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.project.score.mapper.CoursePropertiesPercentMapper;
import com.sanythadmin.project.score.service.CoursePropertiesPercentService;
import com.sanythadmin.project.score.entity.CoursePropertiesPercent;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 课程属性占比配置Service实现
 *
 * <AUTHOR>
 * @since 2025-03-18 15:15:39
 */
@Service
public class CoursePropertiesPercentServiceImpl extends ServiceImpl<CoursePropertiesPercentMapper, CoursePropertiesPercent> implements CoursePropertiesPercentService {

    @Resource
    private CoursePropertiesPercentMapper mapper;

}
