package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.CourseScoreItem;
import com.sanythadmin.project.score.mapper.CourseScoreItemMapper;
import com.sanythadmin.project.score.param.CourseScoreItemParam;
import com.sanythadmin.project.score.service.CourseScoreItemService;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 学生各科课程成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-03-17 17:28:59
 */
@Service
public class CourseScoreItemServiceImpl extends ServiceImpl<CourseScoreItemMapper, CourseScoreItem> implements CourseScoreItemService {

    @Resource
    private CourseScoreItemMapper mapper;

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Override
    public void saveScore(CourseScoreItem scoreItem) {
        mapper.insert(scoreItem);
    }

    @Override
    public List<Map<String, Object>> distinctByField(CourseScoreItemParam param, String... fieldName) {
        LambdaQueryWrapper<CourseScoreItem> wrapper = buildDistinctWrapper(param, fieldName);
        return convert(Objects.isNull(wrapper) ? null : mapper.selectMaps(wrapper), fieldName);
    }

    @Override
    public PageResult<Map<String, Object>> pageDistinctByField(CourseScoreItemParam param, String... fieldName) {
        LambdaQueryWrapper<CourseScoreItem> wrapper = buildDistinctWrapper(param, fieldName);
        if (wrapper == null)
            return null;
        IPage<Map<String, Object>> mapsPage = new PageParam<>();
        mapsPage.setCurrent(param.getPage());
        mapsPage.setSize(param.getLimit());
        mapsPage = mapper.selectMapsPage(mapsPage, wrapper);
        List<Map<String, Object>> list = convert(mapsPage.getRecords(), fieldName);
        return new PageResult<>(list, mapsPage.getTotal());
    }

    @Override
    public List<Map<String, Object>> distinctByField(CourseScoreItemParam param, List<SFunction<CourseScoreItem, ?>> sFunctions) {
        LambdaQueryWrapper<CourseScoreItem> wrapper = buildDistinctWrapper(param, sFunctions);
        return convert(Objects.isNull(wrapper) ? null : mapper.selectMaps(wrapper), sFunctions);
    }

    public LambdaQueryWrapper<CourseScoreItem> buildDistinctWrapper(CourseScoreItemParam param, String... fieldName) {
        if (fieldName != null && fieldName.length > 0) {
            List<SFunction<CourseScoreItem, ?>> sFunctions = getsFunctions(fieldName);
            return buildDistinctWrapper(param, sFunctions);
        }
        return null;
    }

    public LambdaQueryWrapper<CourseScoreItem> buildDistinctWrapper(CourseScoreItemParam param, List<SFunction<CourseScoreItem, ?>> sFunctions) {
        if (sFunctions != null && !sFunctions.isEmpty()) {
            PageParam<CourseScoreItem, CourseScoreItemParam> page = new PageParam<>(param);
            LambdaQueryWrapper<CourseScoreItem> wrapper = page.getWrapper().lambda();
            sFunctions.forEach(wrapper::isNotNull);
            return wrapper.select(sFunctions)
                    .groupBy(sFunctions).orderByDesc(sFunctions);
        }
        return null;
    }

    public List<Map<String, Object>> convert(List<Map<String, Object>> maps, String... fieldName) {
        if (CollectionUtils.isEmpty(maps))
            return null;
        if (fieldName != null && fieldName.length > 0) {
            List<SFunction<CourseScoreItem, ?>> sFunctions = getsFunctions(fieldName);
            return convert(maps, sFunctions);
        }
        return null;

    }

    public List<Map<String, Object>> convert(List<Map<String, Object>> list, List<SFunction<CourseScoreItem, ?>> sFunctions) {
        HashMap<String, String> fieldMap = new HashMap<>();
        for (SFunction<CourseScoreItem, ?> sFunction : sFunctions) {
            String fieldName = CommonUtil.getFieldName(sFunction);
            fieldMap.put(fieldName.toUpperCase(), fieldName);
        }
        List<Map<String, Object>> newMaps = new ArrayList<>();
        for (Map<String, Object> map : list) {
            Map<String, Object> newMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = fieldMap.get(entry.getKey().toUpperCase());
                if (!StringUtils.hasText(key))
                    continue;
                newMap.put(key, entry.getValue());
            }
            newMaps.add(newMap);
        }
        return newMaps;
    }

    public List<SFunction<CourseScoreItem, ?>> getsFunctions(String... fieldName) {
        Field[] fields = CommonUtil.getAllFields(CourseScoreItem.class);
        List<String> list = Arrays.stream(fields).map(Field::getName).toList();
        List<String> strings = Arrays.asList(fieldName);
        strings.removeIf(str -> !list.contains(str));
        List<SFunction<CourseScoreItem, ?>> sFunctions = new ArrayList<>();
        strings.forEach(str -> {
            sFunctions.add(CommonUtil.getSFunction(CourseScoreItem.class, str));
        });
        return sFunctions;
    }
}
