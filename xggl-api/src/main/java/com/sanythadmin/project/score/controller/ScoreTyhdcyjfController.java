package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.common.ScoreImportData;
import com.sanythadmin.project.score.common.ScoreUtil;
import com.sanythadmin.project.score.entity.ScoreTyhdcyjf;
import com.sanythadmin.project.score.param.ScoreTyhdcyjfParam;
import com.sanythadmin.project.score.service.ScoreTyhdcyjfService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成绩管理/体育活动参与加分控制器
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
@Slf4j
@RestController
@RequestMapping("/api/score/score-tyhdcyjf")
public class ScoreTyhdcyjfController extends BaseController {
    @Resource
    private ScoreTyhdcyjfService scoreTyhdcyjfService;

    /**
     * 分页查询体育活动参与加分（权限标识：score:scoreTyhdcyjf:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:list')")
    @GetMapping("/page")
    public PageResult<ScoreTyhdcyjf> page(ScoreTyhdcyjfParam param) {
        return scoreTyhdcyjfService.page(param);
    }

    /**
     * 查询全部体育活动参与加分（权限标识：score:scoreTyhdcyjf:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:list')")
    @GetMapping()
    public List<ScoreTyhdcyjf> list(ScoreTyhdcyjfParam param) {
        return scoreTyhdcyjfService.list(param);
    }

    /**
     * 根据id查询体育活动参与加分（权限标识：score:scoreTyhdcyjf:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:list')")
    @GetMapping("/{id}")
    public ScoreTyhdcyjf get(@PathVariable("id") String id) {
        return scoreTyhdcyjfService.getById(id);
    }

    /**
     * 添加或修改体育活动参与加分（权限标识：score:scoreTyhdcyjf:operation）
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:operation')")
    @OperationLog(module = "体育活动参与加分", comments = "保存体育活动参与加分")
    @PostMapping("/operation")
    public void save(@RequestBody ScoreTyhdcyjf scoreTyhdcyjf) {
        if (StringUtils.hasLength(scoreTyhdcyjf.getId())) {
            scoreTyhdcyjfService.updateById(scoreTyhdcyjf);
        } else {
            scoreTyhdcyjfService.save(scoreTyhdcyjf);
        }
    }

    /**
     * 批量删除体育活动参与加分（权限标识：score:scoreTyhdcyjf:remove）
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:remove')")
    @OperationLog(module = "体育活动参与加分", comments = "批量删除体育活动参与加分")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        scoreTyhdcyjfService.removeByIds(ids);
    }

    /**
     * 导入模块下载（权限标识：score:scoreTyhdcyjf:import）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:import')")
    @OperationLog(module = "体育活动参与加分", comments = "体育活动参与加分导入")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        try {
            ExcelUtils.dataImportTemplate(getHeads(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 成绩数据导出（权限标识：score:scoreTyhdcyjf:exportData）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:exportData')")
    @OperationLog(module = "体育活动参与加分", comments = "体育活动参与加分导出")
    @GetMapping("/exportData")
    public void exportData(HttpServletResponse response, ScoreTyhdcyjfParam param) {
        try {
            List<ScoreTyhdcyjf> list = scoreTyhdcyjfService.list(param);
            String[] titles = {"测评年份", "学号", "姓名", "成绩"};
            String[] property = {"cpnf", "xgh", "xm", "score"};
            EasyExcelHelper.exportExcel(list, titles, property, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 成绩数据导入（权限标识：score:scoreTyhdcyjf:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('score:scoreTyhdcyjf:import')")
    @OperationLog(module = "体育活动参与加分", comments = "体育活动参与加分导入")
    @PostMapping("/importData")
    public void importData(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "configId", required = false) String configId) throws Exception {
        List<ExcelUtils.Head> heads = getHeads();
        ScoreImportData<ScoreTyhdcyjf> importData = ScoreUtil.importData(ScoreTyhdcyjf.class, file, heads, configId);
        List<ScoreTyhdcyjf> list = importData.getList();
        list.forEach(object -> scoreTyhdcyjfService.saveScore(object));
    }

    private static List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
//        heads.add(new ExcelUtils.Head("测评年份", "cpnf"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("成绩", "score", "(-)?\\d+(\\.\\d+)?"));
        return heads;
    }

    /**
     * 测评年份
     *
     * @return
     */
    @GetMapping("/years")
    public List<Map<String, Object>> years() {
        return scoreTyhdcyjfService.years();
    }
}
