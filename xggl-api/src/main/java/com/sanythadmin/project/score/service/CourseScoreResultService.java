package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.score.entity.CourseScoreResult;
import com.sanythadmin.project.score.param.CourseScoreItemParam;
import com.sanythadmin.project.score.param.CourseScoreResultParam;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 课程成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
public interface CourseScoreResultService extends IService<CourseScoreResult> {
    public List<CourseScoreResult> listWithPermission(CourseScoreResultParam param);
    public HashMap<String, CourseScoreResult> scoreResultCalc(CourseScoreItemParam param, EvaluateConfig config);
    public CompletableFuture<String> saveOrUpdateCourseResult(CourseScoreResult result, boolean updateDetailScore);
    public void updateRangePeopleNumber(CourseScoreResultParam param);
    public List<CourseScoreResult> getRankAndPercent(CourseScoreResultParam param);
    public void updateRankAndPercent(CourseScoreResult param);
    public PageResult<CourseScoreResult> page(CourseScoreResultParam param);
}
