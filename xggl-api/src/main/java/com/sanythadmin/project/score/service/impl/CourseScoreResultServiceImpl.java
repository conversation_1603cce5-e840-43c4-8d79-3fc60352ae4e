package com.sanythadmin.project.score.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SqlUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.score.entity.CourseMark;
import com.sanythadmin.project.score.entity.CoursePropertiesPercent;
import com.sanythadmin.project.score.entity.CourseScoreItem;
import com.sanythadmin.project.score.entity.CourseScoreResult;
import com.sanythadmin.project.score.enums.CourseMarkType;
import com.sanythadmin.project.score.mapper.CourseMarkMapper;
import com.sanythadmin.project.score.mapper.CoursePropertiesPercentMapper;
import com.sanythadmin.project.score.mapper.CourseScoreItemMapper;
import com.sanythadmin.project.score.mapper.CourseScoreResultMapper;
import com.sanythadmin.project.score.param.CourseScoreItemParam;
import com.sanythadmin.project.score.param.CourseScoreResultParam;
import com.sanythadmin.project.score.pojo.CourseScoreTemp;
import com.sanythadmin.project.score.service.CourseScoreResultService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 课程成绩Service实现
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
@Service
public class CourseScoreResultServiceImpl extends ServiceImpl<CourseScoreResultMapper, CourseScoreResult> implements CourseScoreResultService {
    @Resource
    private CourseScoreResultMapper mapper;
    @Resource
    private CourseScoreItemMapper courseScoreItemMapper;
    @Resource
    private CoursePropertiesPercentMapper coursePropertiesPercentMapper;
    @Resource
    private CourseMarkMapper courseMarkMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Override
    public List<CourseScoreResult> listWithPermission(CourseScoreResultParam param) {
        PageParam<CourseScoreResult, CourseScoreResultParam> page = new PageParam<>(param);
        return mapper.selectListWithPermission(page.getOrderWrapper(), new MyMPQueryParams(CourseScoreResult.class));
    }

    private List<CourseScoreTemp> getUsersCourseScore(CourseScoreItemParam param) {
        PageParam<CourseScoreItem, CourseScoreItemParam> page = new PageParam<>(param);
        QueryWrapper<CourseScoreItem> wrapper = page.getWrapper();
        wrapper.lambda().select(CourseScoreItem::getXgh).groupBy(CourseScoreItem::getXgh);
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> lambdaWrapper = userInfoService.getQueryWrapper(null, CodeXsztParam.currentStudent(), "xgh", "xm");
        String sql = "SELECT 1 FROM SYT_COURSE_SCORE_ITEM WHERE " + lambdaWrapper.getAlias() + ".XGH = XGH AND " + SqlUtil.getRealSql(wrapper);
        List<UserInfo> userInfos = userInfoMapper.selectJoinList(UserInfo.class, lambdaWrapper.exists(sql));
        List<CourseMark> courseMarks = courseMarkMapper.selectList(new LambdaQueryWrapper<CourseMark>()
                .in(CourseMark::getXnxq, (Object[]) CommonUtil.split(param.getXnxq())));
        List<String> courseCategory = courseScoreItemMapper.selectObjs(page.getWrapper().lambda()
                .select(CourseScoreItem::getKcsxmc).isNotNull(CourseScoreItem::getKcsxmc).groupBy(CourseScoreItem::getKcsxmc));
        List<CourseScoreTemp> courseScoreTemps = new ArrayList<>();
        for (UserInfo userInfo : userInfos) {
            List<CourseScoreItem> courseScoreItems = courseScoreItemMapper.selectList(page.getWrapper().lambda()
                    .eq(CourseScoreItem::getXgh, userInfo.getXgh()));
            courseCategory.forEach(category -> {
                AtomicReference<Double> score = new AtomicReference<>(0d);
                AtomicReference<Double> credit = new AtomicReference<>(0d);
                List<CourseScoreItem> list = courseScoreItems.stream().filter(item -> Objects.equals(item.getKcsxmc(), category)).toList();
                if (!CollectionUtils.isEmpty(list)) {
                    list.forEach(item -> {
                        if (!isCourseExcluded(courseMarks, item, userInfo)) {
                            score.updateAndGet(v -> (v + (item.getKccj() == null ? 0 : item.getKccj()) * (item.getXf() == null ? 0 : item.getXf())));
                            credit.updateAndGet(v -> (v + (item.getXf() == null ? 0 : item.getXf())));
                        }
                    });
                }
                CourseScoreTemp scoreTemp = new CourseScoreTemp();
                BeanUtils.copyProperties(courseScoreItems.get(0), scoreTemp);
                scoreTemp.setPyccid(userInfo.getPyccid());
                scoreTemp.setNjid(userInfo.getNjid());
                scoreTemp.setXyid(userInfo.getXyid());
                scoreTemp.setZyid(userInfo.getZyid());
                scoreTemp.setBjid(userInfo.getBjid());
                scoreTemp.setKcsxmc(category);
                scoreTemp.setKccj(score.get());
                scoreTemp.setXf(credit.get());
                courseScoreTemps.add(scoreTemp);
            });
        }
        return courseScoreTemps;
    }

    public boolean isCourseExcluded(List<CourseMark> courseMarks, CourseScoreItem item, UserInfo userInfo) {
        return courseMarks.stream().anyMatch(mark ->
                Objects.equals(item.getKch(), mark.getKch()) &&
                        Objects.equals(item.getXnxq(), mark.getXnxq()) &&
                        Objects.equals(mark.getMarkType(), CourseMarkType.BuCanYuJiSuan) &&
                        (
                                (!StringUtils.hasText(mark.getXyid()) && !StringUtils.hasText(mark.getBjid())) ||
                                        (userInfo.getXyid().equals(mark.getXyid()) && !StringUtils.hasText(mark.getBjid())) ||
                                        userInfo.getBjid().equals(mark.getBjid())
                        )
        );
    }


    @Override
    public HashMap<String, CourseScoreResult> scoreResultCalc(CourseScoreItemParam param, EvaluateConfig config) {
        List<CourseScoreTemp> courseScoreTemps = getUsersCourseScore(param);
        List<CoursePropertiesPercent> coursePropertiesPercents = coursePropertiesPercentMapper.selectList(new LambdaQueryWrapper<CoursePropertiesPercent>()
                .eq(CoursePropertiesPercent::getXn, config.getCpnf()));
        Map<String, CoursePropertiesPercent> coursePropertiesPercentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(coursePropertiesPercents)) {
            coursePropertiesPercentMap = coursePropertiesPercents.stream().collect(Collectors.toMap(
                    CoursePropertiesPercent::getKcsxmc, Function.identity()
            ));
        }

        PageParam<CourseScoreItem, CourseScoreItemParam> page = new PageParam<>(param);
        DecimalFormat decimalFormat = new DecimalFormat("###.00");
        String studentNo = null;
        double sumScore = 0d;
        double sumCredit = 0d;
        HashMap<String, CourseScoreResult> scoreResultMap = new HashMap<>();
        HashMap<String, Map<String, Double>> scoreDetailMap = new HashMap<>();
        for (CourseScoreTemp item : courseScoreTemps) {
            CoursePropertiesPercent propertiesPercent = coursePropertiesPercentMap.get(item.getKcsxmc());
            double percent = propertiesPercent != null ? propertiesPercent.getPercent() / 100 : 1;
            if (Objects.equals(item.getXgh(), studentNo)) {
                sumScore += item.getKccj() * percent;
                sumCredit += item.getXf();
            } else {
                sumScore = (item.getKccj() == null ? 0 : item.getKccj()) * percent;
                sumCredit = item.getXf() == null ? 0 : item.getXf();
            }

            studentNo = item.getXgh();
            Map<String, Double> valueMap = scoreDetailMap.get(studentNo) == null ? new HashMap<>() : scoreDetailMap.get(studentNo);
            valueMap.put(item.getKcsxmc(), item.getKccj());
            scoreDetailMap.put(studentNo, valueMap);
            CourseScoreResult result = scoreResultMap.get(studentNo);
            if (result == null) {
                result = new CourseScoreResult();
                BeanUtils.copyProperties(item, result);
            }
            result.setXnxq(param.getXnxq());
            result.setJqcj(Double.parseDouble(decimalFormat.format(sumScore / (sumCredit == 0 ? 1 : sumCredit))));
            scoreResultMap.put(studentNo, result);
        }

        if (!CollectionUtils.isEmpty(scoreResultMap)) {
            List<CourseScoreItem> avgScoreList = courseScoreItemMapper.selectList(page.getWrapper().select("xgh", "avg(kccj) as kccj")
                    .lambda().isNotNull(CourseScoreItem::getKccj).groupBy(CourseScoreItem::getXgh));
            Map<String, Double> avgMap = avgScoreList.stream().collect(Collectors.toMap(CourseScoreItem::getXgh, CourseScoreItem::getKccj));
            for (Map.Entry<String, CourseScoreResult> entry : scoreResultMap.entrySet()) {
                String key = entry.getKey();
                CourseScoreResult result = entry.getValue();
                String scoreDetail = scoreDetailMap.get(key) != null ? JSON.toJSONString(scoreDetailMap.get(key)) : null;
                result.setGxcj(scoreDetail);
                result.setCpnf(config.getCpnf());
                result.setPjcj(avgMap.get(key) != null ? Double.parseDouble(decimalFormat.format(avgMap.get(key))) : null);
                scoreResultMap.put(key, result);
            }
        }
        return scoreResultMap;
    }

    @Async(value = AsyncConfig.ASYNC_EXECUTOR)
    @Override
    public CompletableFuture<String> saveOrUpdateCourseResult(CourseScoreResult result, boolean updateDetailScore) {
        CourseScoreResult scoreResult = mapper.selectOne(new LambdaQueryWrapper<CourseScoreResult>()
                .eq(CourseScoreResult::getXgh, result.getXgh())
                .eq(CourseScoreResult::getXnxq, result.getXnxq())
                .eq(CourseScoreResult::getCpnf, result.getCpnf()));
        if (scoreResult == null) {
            mapper.insert(result);
        } else {
            LambdaUpdateWrapper<CourseScoreResult> updateWrapper = new LambdaUpdateWrapper<CourseScoreResult>()
                    .eq(CourseScoreResult::getId, scoreResult.getId())
                    .set(CourseScoreResult::getJqcj, result.getJqcj() == null ? 0 : result.getJqcj())
                    .set(CourseScoreResult::getPjcj, result.getPjcj() == null ? 0 : result.getPjcj());
            if (updateDetailScore)
                updateWrapper.set(CourseScoreResult::getGxcj, result.getGxcj());
            mapper.update(updateWrapper);
        }
        return CompletableFuture.completedFuture(result.getXgh());
    }

    @Override
    public void updateRangePeopleNumber(CourseScoreResultParam param) {
        mapper.updateRangePeopleNumber(param);
    }

    @Override
    public List<CourseScoreResult> getRankAndPercent(CourseScoreResultParam param) {
        return mapper.getRankAndPercent(param);
    }

    @Async(value = AsyncConfig.ASYNC_EXECUTOR)
    @Override
    public void updateRankAndPercent(CourseScoreResult param) {
        mapper.update(new LambdaUpdateWrapper<CourseScoreResult>()
                .eq(CourseScoreResult::getId, param.getId())
                .set(CourseScoreResult::getJqcjpm, param.getJqcjpm())
                .set(CourseScoreResult::getJqcjpmbfb, param.getJqcjpmbfb())
                .set(CourseScoreResult::getJqcjxypm, param.getJqcjxypm())
                .set(CourseScoreResult::getJqcjxypmbfb, param.getJqcjxypmbfb())
                .set(CourseScoreResult::getJqcjzypm, param.getJqcjzypm())
                .set(CourseScoreResult::getJqcjzypmbfb, param.getJqcjzypmbfb())
                .set(CourseScoreResult::getJqcjbjpm, param.getJqcjbjpm())
                .set(CourseScoreResult::getJqcjbjpmbfb, param.getJqcjbjpmbfb())
                .set(CourseScoreResult::getPjcjpm, param.getPjcjpm())
                .set(CourseScoreResult::getPjcjpmbfb, param.getPjcjpmbfb())
                .set(CourseScoreResult::getPjcjxypm, param.getPjcjxypm())
                .set(CourseScoreResult::getPjcjxypmbfb, param.getPjcjxypmbfb())
                .set(CourseScoreResult::getPjcjzypm, param.getPjcjzypm())
                .set(CourseScoreResult::getPjcjzypmbfb, param.getPjcjzypmbfb())
                .set(CourseScoreResult::getPjcjbjpm, param.getPjcjbjpm())
                .set(CourseScoreResult::getPjcjbjpmbfb, param.getPjcjbjpmbfb())
        );
    }

    @Override
    public PageResult<CourseScoreResult> page(CourseScoreResultParam param) {
        PageParam<CourseScoreResult, CourseScoreResultParam> page = new PageParam<>(param);
        page = baseMapper.selectPageWithPermission(page, page.getWrapper(), new MyMPQueryParams(CourseScoreResult.class));
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
