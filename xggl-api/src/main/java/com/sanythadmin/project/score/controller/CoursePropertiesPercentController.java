package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.project.score.service.CoursePropertiesPercentService;
import com.sanythadmin.project.score.entity.CoursePropertiesPercent;
import com.sanythadmin.project.score.param.CoursePropertiesPercentParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 成绩管理/课程属性占比配置控制器
 *
 * <AUTHOR>
 * @since 2025-03-18 15:15:39
 */
@RestController
@RequestMapping("/api/score/course-properties-percent")
public class CoursePropertiesPercentController extends BaseController {
    @Resource
    private CoursePropertiesPercentService coursePropertiesPercentService;

    /**
     * 分页查询课程属性占比配置（权限标识：score:coursePropertiesPercent:list）
     */
    @PreAuthorize("hasAuthority('score:coursePropertiesPercent:list')")
    @GetMapping("/page")
    public PageResult<CoursePropertiesPercent> page(CoursePropertiesPercentParam param) {
        PageParam<CoursePropertiesPercent, CoursePropertiesPercentParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = coursePropertiesPercentService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部课程属性占比配置（权限标识：score:coursePropertiesPercent:list）
     */
    @PreAuthorize("hasAuthority('score:coursePropertiesPercent:list')")
    @GetMapping()
    public List<CoursePropertiesPercent> list(CoursePropertiesPercentParam param) {
        PageParam<CoursePropertiesPercent, CoursePropertiesPercentParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return coursePropertiesPercentService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询课程属性占比配置（权限标识：score:coursePropertiesPercent:list）
     */
    @PreAuthorize("hasAuthority('score:coursePropertiesPercent:list')")
    @GetMapping("/{id}")
    public CoursePropertiesPercent get(@PathVariable("id") String id) {
        return coursePropertiesPercentService.getById(id);
    }

    /**
     * 添加或修改课程属性占比配置（权限标识：score:coursePropertiesPercent:operation）
     */
    @PreAuthorize("hasAuthority('score:coursePropertiesPercent:operation')")
    @OperationLog(module = "课程属性占比配置", comments = "保存课程属性占比配置")
    @PostMapping("/operation")
    public void save(@RequestBody CoursePropertiesPercent coursePropertiesPercent) {
        if (StringUtils.hasLength(coursePropertiesPercent.getId())) {
            coursePropertiesPercentService.updateById(coursePropertiesPercent);
        } else {
            coursePropertiesPercentService.save(coursePropertiesPercent);
        }
    }

    /**
     * 批量删除课程属性占比配置（权限标识：score:coursePropertiesPercent:remove）
     */
    @PreAuthorize("hasAuthority('score:coursePropertiesPercent:remove')")
    @OperationLog(module = "课程属性占比配置", comments = "批量删除课程属性占比配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        removeParamCheck(ids);
        coursePropertiesPercentService.removeByIds(ids);
    }
}
