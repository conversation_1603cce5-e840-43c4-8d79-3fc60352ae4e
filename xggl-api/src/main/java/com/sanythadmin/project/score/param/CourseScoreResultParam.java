package com.sanythadmin.project.score.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CourseScoreResultParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 测评年份
     */
    private String cpnf;

    /**
     * 学年学期
     */
    private String xnxq;

    /**
     * 学号
     */
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 年级名称
     */
    private String njmc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 培养出层次ID
     */
    @QueryField(type = QueryType.EQ)
    private String pyccid;

    /**
     * 年级ID
     */
    @QueryField(type = QueryType.EQ)
    private String njid;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.EQ)
    private String xyid;

    /**
     * 专业ID
     */
    @QueryField(type = QueryType.EQ)
    private String zyid;

    /**
     * 班级ID
     */
    @QueryField(type = QueryType.EQ)
    private String bjid;

    /**
     * 综测参数配置ID
     */
    @QueryField(type = QueryType.EQ)
    private String configId;

    @QueryField(ignore = true)
    private String rankColumn;
}
