package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 体育成绩
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_SCORE_TYCJ")
@Entity
@Table(name = "SYT_SCORE_TYCJ")
public class ScoreTycj extends BaseScoreEntity {
    private static final long serialVersionUID = 1L;

}
