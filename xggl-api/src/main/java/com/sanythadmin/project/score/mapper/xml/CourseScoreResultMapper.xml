<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.score.mapper.CourseScoreResultMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , CPNF, XNXQ, XGH, XM, XYMC, ZYMC, NJMC, BJMC, GXCJ, JQCJ, PJCJ, ZRS, XYRS, ZYRS, BJRS, JQCJPM, JQCJPMBFB, JQCJXYPM, JQCJXYPMBFB, JQCJZYPM, JQCJZYPMBFB, JQCJBJPM, JQCJBJPMBFB, PJCJPM, PJCJPMBFB, PJCJXYPM, PJCJXYPMBFB, PJCJZYPM, PJCJZYPMBFB, PJCJBJPM, PJCJBJPMBFB, PYCCID, NJID, XY<PERSON>, ZY<PERSON>, BJID
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.id != null">
            AND ID = #{param.id}
        </if>
        <if test="param.cpnf != null">
            AND CPNF =#{param.cpnf}
        </if>
        <if test="param.xnxq != null">
            AND XNXQ LIKE concat(concat('%',#{param.xnxq), '%')
        </if>
        <if test="param.xgh != null">
            AND XGH LIKE concat(concat('%',#{param.xgh), '%')
        </if>
        <if test="param.xm != null">
            AND XM LIKE concat(concat('%',#{param.xm), '%')
        </if>
        <if test="param.xymc != null">
            AND XYMC LIKE concat(concat('%',#{param.xymc), '%')
        </if>
        <if test="param.zymc != null">
            AND ZYMC LIKE concat(concat('%',#{param.zymc), '%')
        </if>
        <if test="param.njmc != null">
            AND NJMC = #{param.njmc}
        </if>
        <if test="param.bjmc != null">
            AND BJMC LIKE concat(concat('%',#{param.bjmc), '%')
        </if>
        <if test="param.pyccid != null">
            AND PYCCID LIKE concat(concat('%',#{param.pyccid), '%')
        </if>
        <if test="param.njid != null">
            AND NJID LIKE concat(concat('%',#{param.njid), '%')
        </if>
        <if test="param.xyid != null">
            AND XYID LIKE concat(concat('%',#{param.xyid), '%')
        </if>
        <if test="param.zyid != null">
            AND ZYID LIKE concat(concat('%',#{param.zyid), '%')
        </if>
        <if test="param.bjid != null">
            AND BJID LIKE concat(concat('%',#{param.bjid), '%')
        </if>
    </sql>

    <update id="updateRangePeopleNumber" parameterType="com.sanythadmin.project.score.param.CourseScoreResultParam">
        update syt_course_score_result r
        set r.zrs  = (select total
                 from (select cpnf, count(*) total
                       from syt_course_score_result group by cpnf) where r.cpnf = cpnf),
            r.xyrs = (select total
                 from (select cpnf, njmc, xymc, count(*) total
                       from syt_course_score_result group by cpnf, njmc, xymc)
                 where r.cpnf = cpnf and r.njmc = njmc and r.xymc = xymc),
            r.zyrs = (select total
                 from (select cpnf, njmc, xymc, zymc, count(*) total
                       from syt_course_score_result group by cpnf, njmc, xymc, zymc)
                 where r.cpnf = cpnf and r.njmc = njmc and r.xymc = xymc and r.zymc = zymc),
            r.bjrs = (select total
                 from (select cpnf, njmc, xymc, zymc, bjmc, count(*) total
                       from syt_course_score_result group by cpnf, njmc, xymc, zymc, bjmc)
                 where r.cpnf = cpnf and r.njmc = njmc and r.xymc = xymc and r.zymc = zymc and r.bjmc = bjmc)
        <where>
            <include refid="Base_Query_Condition"/>
        </where>
    </update>

    <select id="getRankAndPercent" parameterType="com.sanythadmin.project.score.param.CourseScoreResultParam">
        select a.id, a.jqcjpm, b.jqcjxypm, c.jqcjzypm, d.jqcjbjpm, e.pjcjpm, f.pjcjxypm, g.pjcjzypm, h.pjcjbjpm,
        round(a.jqcjpm / decode(a.zrs, 0, 1, a.zrs) * 100, 2) jqcjpmbfb,
        round(b.jqcjxypm / decode(a.xyrs, 0, 1, a.xyrs) * 100, 2) jqcjxypmbfb,
        round(c.jqcjzypm / decode(a.zyrs, 0, 1, a.zyrs) * 100, 2) jqcjzypmbfb,
        round(d.jqcjbjpm / decode(a.bjrs, 0, 1, a.bjrs) * 100, 2) jqcjbjpmbfb,
        round(e.pjcjpm / decode(a.zrs, 0, 1, a.zrs) * 100, 2) pjcjpmbfb,
        round(f.pjcjxypm / decode(a.xyrs, 0, 1, a.xyrs) * 100, 2) pjcjxypmbfb,
        round(g.pjcjzypm / decode(a.zyrs, 0, 1, a.zyrs) * 100, 2) pjcjzypmbfb,
        round(h.pjcjbjpm / decode(a.bjrs, 0, 1, a.bjrs) * 100, 2) pjcjbjpmbfb from (
        select id, zrs, xyrs, zyrs, bjrs, rank() over(partition by cpnf order by jqcj desc) jqcjpm from syt_course_score_result) a,(
        select id, rank() over(partition by cpnf, njmc, xymc order by jqcj desc) jqcjxypm from syt_course_score_result) b,(
        select id, rank() over(partition by cpnf, njmc, xymc, zymc order by jqcj desc) jqcjzypm from syt_course_score_result) c,(
        select id, rank() over(partition by cpnf, njmc, xymc, zymc, bjmc order by jqcj desc) jqcjbjpm from syt_course_score_result) d,(
        select id, rank() over(partition by cpnf order by pjcj desc) pjcjpm from syt_course_score_result) e,(
        select id, rank() over(partition by cpnf, njmc, xymc order by pjcj desc) pjcjxypm from syt_course_score_result) f,(
        select id, rank() over(partition by cpnf, njmc, xymc, zymc order by pjcj desc) pjcjzypm from syt_course_score_result) g,(
        select id, rank() over(partition by cpnf, njmc, xymc, zymc, bjmc order by pjcj desc) pjcjbjpm from syt_course_score_result) h
        where a.id = b.id and b.id = c.id and c.id = d.id and d.id = e.id and e.id = f.id and f.id = g.id and g.id = h.id
        and exists (select 1 from syt_course_score_result where a.id = id <include refid="Base_Query_Condition"/>)
    </select>
</mapper>
