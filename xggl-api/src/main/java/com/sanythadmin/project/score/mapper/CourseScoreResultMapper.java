package com.sanythadmin.project.score.mapper;

import com.sanythadmin.common.core.mybatisplus.base.MyMPJBaseMapper;
import com.sanythadmin.project.score.entity.CourseScoreResult;
import com.sanythadmin.project.score.param.CourseScoreResultParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程成绩Mapper
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
public interface CourseScoreResultMapper extends MyMPJBaseMapper<CourseScoreResult> {

    public void updateRangePeopleNumber(@Param("param") CourseScoreResultParam param);
    public List<CourseScoreResult> getRankAndPercent(@Param("param") CourseScoreResultParam param);
}
