<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.score.mapper.ScoreTycjMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XGH, CPNF, SCORE, XM, XYID, ZYID, BJID, SCORE1, SCORE2, SCORE3
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xgh != null">
                        AND a.XGH LIKE concat(concat('%',#{param.xgh), '%')
                    </if>
                    <if test="param.cpnf != null">
                        AND a.CPNF LIKE concat(concat('%',#{param.cpnf), '%')
                    </if>
                    <if test="param.score != null">
                        AND a.SCORE = #{param.score}
                    </if>
                    <if test="param.xm != null">
                        AND a.XM LIKE concat(concat('%',#{param.xm), '%')
                    </if>
                    <if test="param.xyid != null">
                        AND a.XYID LIKE concat(concat('%',#{param.xyid), '%')
                    </if>
                    <if test="param.zyid != null">
                        AND a.ZYID LIKE concat(concat('%',#{param.zyid), '%')
                    </if>
                    <if test="param.bjid != null">
                        AND a.BJID LIKE concat(concat('%',#{param.bjid), '%')
                    </if>
                    <if test="param.score1 != null">
                        AND a.SCORE1 = #{param.score1}
                    </if>
                    <if test="param.score2 != null">
                        AND a.SCORE2 = #{param.score2}
                    </if>
                    <if test="param.score3 != null">
                        AND a.SCORE3 = #{param.score3}
                    </if>
    </sql>
</mapper>
