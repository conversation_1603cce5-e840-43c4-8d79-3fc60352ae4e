<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanythadmin.project.score.mapper.CourseScoreItemMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, XH, XYMC, ZYMC, BJMC, NJMC, XNXQ, XN, KCH, KCM, KXH, KCCJ, KCSXDM, KCSXMC, JDCJ, XF, XS, KSLXDM, KSLXMC, XDFSMC, XDFSDM, BKFSDM, BKFSMC, CJBS, WTGYYSM, WTGYYDM, BZ
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
                    <if test="param.id != null">
                        AND a.ID = #{param.id}
                    </if>
                    <if test="param.xh != null">
                        AND a.XH LIKE concat(concat('%',#{param.xh), '%')
                    </if>
                    <if test="param.xymc != null">
                        AND a.XYMC LIKE concat(concat('%',#{param.xymc), '%')
                    </if>
                    <if test="param.zymc != null">
                        AND a.ZYMC LIKE concat(concat('%',#{param.zymc), '%')
                    </if>
                    <if test="param.bjmc != null">
                        AND a.BJMC LIKE concat(concat('%',#{param.bjmc), '%')
                    </if>
                    <if test="param.njmc != null">
                        AND a.NJMC LIKE concat(concat('%',#{param.njmc), '%')
                    </if>
                    <if test="param.xnxq != null">
                        AND a.XNXQ LIKE concat(concat('%',#{param.xnxq), '%')
                    </if>
                    <if test="param.xn != null">
                        AND a.XN LIKE concat(concat('%',#{param.xn), '%')
                    </if>
                    <if test="param.kch != null">
                        AND a.KCH LIKE concat(concat('%',#{param.kch), '%')
                    </if>
                    <if test="param.kcm != null">
                        AND a.KCM LIKE concat(concat('%',#{param.kcm), '%')
                    </if>
                    <if test="param.kxh != null">
                        AND a.KXH LIKE concat(concat('%',#{param.kxh), '%')
                    </if>
                    <if test="param.kccj != null">
                        AND a.KCCJ = #{param.kccj}
                    </if>
                    <if test="param.kcsxdm != null">
                        AND a.KCSXDM LIKE concat(concat('%',#{param.kcsxdm), '%')
                    </if>
                    <if test="param.kcsxmc != null">
                        AND a.KCSXMC LIKE concat(concat('%',#{param.kcsxmc), '%')
                    </if>
                    <if test="param.jdcj != null">
                        AND a.JDCJ = #{param.jdcj}
                    </if>
                    <if test="param.xf != null">
                        AND a.XF = #{param.xf}
                    </if>
                    <if test="param.xs != null">
                        AND a.XS = #{param.xs}
                    </if>
                    <if test="param.kslxdm != null">
                        AND a.KSLXDM LIKE concat(concat('%',#{param.kslxdm), '%')
                    </if>
                    <if test="param.kslxmc != null">
                        AND a.KSLXMC LIKE concat(concat('%',#{param.kslxmc), '%')
                    </if>
                    <if test="param.xdfsmc != null">
                        AND a.XDFSMC LIKE concat(concat('%',#{param.xdfsmc), '%')
                    </if>
                    <if test="param.xdfsdm != null">
                        AND a.XDFSDM LIKE concat(concat('%',#{param.xdfsdm), '%')
                    </if>
                    <if test="param.bkfsdm != null">
                        AND a.BKFSDM LIKE concat(concat('%',#{param.bkfsdm), '%')
                    </if>
                    <if test="param.bkfsmc != null">
                        AND a.BKFSMC LIKE concat(concat('%',#{param.bkfsmc), '%')
                    </if>
                    <if test="param.cjbs != null">
                        AND a.CJBS LIKE concat(concat('%',#{param.cjbs), '%')
                    </if>
                    <if test="param.wtgyysm != null">
                        AND a.WTGYYSM LIKE concat(concat('%',#{param.wtgyysm), '%')
                    </if>
                    <if test="param.wtgyydm != null">
                        AND a.WTGYYDM LIKE concat(concat('%',#{param.wtgyydm), '%')
                    </if>
                    <if test="param.bz != null">
                        AND a.BZ LIKE concat(concat('%',#{param.bz), '%')
                    </if>
    </sql>
</mapper>
