package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.common.ScoreImportData;
import com.sanythadmin.project.score.common.ScoreUtil;
import com.sanythadmin.project.score.entity.ScoreTycj;
import com.sanythadmin.project.score.param.ScoreTycjParam;
import com.sanythadmin.project.score.service.ScoreTycjService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成绩管理/体育成绩控制器
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Slf4j
@RestController
@RequestMapping("/api/score/score-tycj")
public class ScoreTycjController extends BaseController {
    @Resource
    private ScoreTycjService scoreTycjService;

    /**
     * 分页查询体育成绩（权限标识：score:scoreTycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:list')")
    @GetMapping("/page")
    public PageResult<ScoreTycj> page(ScoreTycjParam param) {
        return scoreTycjService.page(param);
    }

    /**
     * 查询全部体育成绩（权限标识：score:scoreTycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:list')")
    @GetMapping()
    public List<ScoreTycj> list(ScoreTycjParam param) {
        return scoreTycjService.list(param);
    }

    /**
     * 根据id查询体育成绩（权限标识：score:scoreTycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:list')")
    @GetMapping("/{id}")
    public ScoreTycj get(@PathVariable("id") String id) {
        return scoreTycjService.getById(id);
    }

    /**
     * 按查询条件删除体育成绩（权限标识：score:scoreTycj:remove）
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:remove')")
    @OperationLog(module = "体育成绩", comments = "批量删除体育成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody ScoreTycjParam param) {
        scoreTycjService.removeByParam(param);
    }


    /**
     * 导入模块下载（权限标识：score:scoreTycj:import）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:import')")
    @OperationLog(module = "体育成绩", comments = "体育成绩导入")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        try {
            ExcelUtils.dataImportTemplate(getHeads(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 成绩数据导出（权限标识：score:scoreTycj:exportData）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:exportData')")
    @OperationLog(module = "体育成绩", comments = "体育成绩导出")
    @GetMapping("/exportData")
    public void exportData(HttpServletResponse response, ScoreTycjParam param) {
        try {
            List<ScoreTycj> list = scoreTycjService.list(param);
            String[] titles = {"测评年份", "学号", "姓名", "成绩"};
            String[] property = {"cpnf", "xgh", "xm", "score"};
            EasyExcelHelper.exportExcel(list, titles, property, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 成绩数据导入（权限标识：score:scoreTycj:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('score:scoreTycj:import')")
    @OperationLog(module = "体育成绩", comments = "体育成绩导入")
    @PostMapping("/importData")
    public void importData(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "configId", required = false) String configId) throws Exception {
        List<ExcelUtils.Head> heads = getHeads();
        ScoreImportData<ScoreTycj> importData = ScoreUtil.importData(ScoreTycj.class, file, heads, configId);
        List<ScoreTycj> list = importData.getList();
        list.forEach(object -> scoreTycjService.saveScore(object));
    }

    private static List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
//        heads.add(new ExcelUtils.Head("测评年份", "cpnf"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("成绩", "score", "(-)?\\d+(\\.\\d+)?"));
        return heads;
    }

    /**
     * 测评年份
     * @return
     */
    @GetMapping("/years")
    public List<Map<String, Object>> years() {
        return scoreTycjService.years();
    }
}
