package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreTycj;
import com.sanythadmin.project.score.param.ScoreTycjParam;

import java.util.List;
import java.util.Map;

/**
 * 体育成绩Service
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
public interface ScoreTycjService extends IService<ScoreTycj> {
    public PageResult<ScoreTycj> page(ScoreTycjParam param);

    public void saveScore(ScoreTycj object);

    public void removeByParam(ScoreTycjParam param);

    public List<ScoreTycj> list(ScoreTycjParam param);
    public List<Map<String, Object>> years();
}
