package com.sanythadmin.project.score.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.utils.EasyExcelHelper;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.common.ScoreImportData;
import com.sanythadmin.project.score.common.ScoreUtil;
import com.sanythadmin.project.score.entity.ScoreLycj;
import com.sanythadmin.project.score.param.ScoreLycjParam;
import com.sanythadmin.project.score.service.ScoreLycjService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成绩管理/劳育成绩控制器
 *
 * <AUTHOR>
 * @since 2025-03-13 18:01:54
 */
@Slf4j
@RestController
@RequestMapping("/api/score/score-lycj")
public class ScoreLycjController extends BaseController {
    @Resource
    private ScoreLycjService scoreLycjService;

    /**
     * 分页查询劳育成绩（权限标识：score:scoreLycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:list')")
    @GetMapping("/page")
    public PageResult<ScoreLycj> page(ScoreLycjParam param) {
        return scoreLycjService.page(param);
    }

    /**
     * 查询全部劳育成绩（权限标识：score:scoreLycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:list')")
    @GetMapping()
    public List<ScoreLycj> list(ScoreLycjParam param) {
        return scoreLycjService.list(param);
    }

    /**
     * 根据id查询劳育成绩（权限标识：score:scoreLycj:list）
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:list')")
    @GetMapping("/{id}")
    public ScoreLycj get(@PathVariable("id") String id) {
        return scoreLycjService.getById(id);
    }

    /**
     * 按查询条件删除劳育成绩（权限标识：score:scoreLycj:remove）
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:remove')")
    @OperationLog(module = "劳育成绩", comments = "批量删除劳育成绩")
    @PostMapping("/remove")
    public void remove(@RequestBody ScoreLycjParam param) {
        scoreLycjService.removeByParam(param);
    }


    /**
     * 导入模块下载（权限标识：score:scoreLycj:import）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:import')")
    @OperationLog(module = "劳育成绩", comments = "劳育成绩导入")
    @GetMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        try {
            ExcelUtils.dataImportTemplate(getHeads(), response);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 成绩数据导出（权限标识：score:scoreLycj:exportData）
     *
     * @param response
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:exportData')")
    @OperationLog(module = "劳育成绩", comments = "劳育成绩导出")
    @GetMapping("/exportData")
    public void exportData(HttpServletResponse response, ScoreLycjParam param) {
        try {
            List<ScoreLycj> list = scoreLycjService.list(param);
            String[] titles = {"测评年份", "学号", "姓名", "成绩类别", "成绩"};
            String[] property = {"cpnf", "xgh", "xm", "type", "score"};
            EasyExcelHelper.exportExcel(list, titles, property, response, null);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 成绩数据导入（权限标识：score:scoreLycj:import）
     *
     * @param file
     */
    @PreAuthorize("hasAuthority('score:scoreLycj:import')")
    @OperationLog(module = "劳育成绩", comments = "劳育成绩导入")
    @PostMapping("/importData")
    public void importData(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "configId", required = false) String configId) throws Exception {
        List<ExcelUtils.Head> heads = getHeads();
        ScoreImportData<ScoreLycj> importData = ScoreUtil.importData(ScoreLycj.class, file, heads, configId);
        List<ScoreLycj> list = importData.getList();
        list.forEach(object -> scoreLycjService.saveScore(object));
    }

    private static List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
//        heads.add(new ExcelUtils.Head("测评年份", "cpnf"));
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("成绩类别", "type"));
        heads.add(new ExcelUtils.Head("成绩", "score", "(-)?\\d+(\\.\\d+)?"));
        return heads;
    }

    /**
     * 测评年份
     *
     * @return
     */
    @GetMapping("/years")
    public List<Map<String, Object>> years() {
        return scoreLycjService.distinctData(ScoreLycj::getCpnf);
    }

    /**
     * 成绩类别
     *
     * @return
     */
    @GetMapping("/typelist")
    public List<Map<String, Object>> typelist() {
        return scoreLycjService.distinctData(ScoreLycj::getType);
    }
}
