package com.sanythadmin.project.score.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.UserInfoUtil;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.score.entity.CourseMark;
import com.sanythadmin.project.score.mapper.CourseMarkMapper;
import com.sanythadmin.project.score.param.CourseMarkParam;
import com.sanythadmin.project.score.pojo.CourseMarkVO;
import com.sanythadmin.project.score.service.CourseMarkService;
import com.sanythadmin.project.userInfo.dto.DataScopeCache;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 标记课程Service实现
 *
 * <AUTHOR>
 * @since 2025-03-18 15:52:35
 */
@Service
public class CourseMarkServiceImpl extends ServiceImpl<CourseMarkMapper, CourseMark> implements CourseMarkService {

    @Resource
    private CourseMarkMapper mapper;
    @Resource
    private UserDataScopeService userDataScopeService;

    @Override
    public PageResult<CourseMarkVO> page(CourseMarkParam param) {
        SysRole role = SecurityUtil.getRole();
        String username = SecurityUtil.getUsername();
        PageParam<CourseMark, CourseMarkParam> page = new PageParam<>(param);
        QueryWrapper<CourseMark> wrapper = new QueryWrapper<>();
        if (!Constants.ROLE_SCOPE_QX.equals(role.getRoleScope())) {
            DataScopeCache scopeCache = userDataScopeService.get(username, role.getId());
            UserDataScope userDataScope = scopeCache.getDataScope();
            if (Constants.ROLE_SCOPE_BY.equals(role.getRoleScope())) {
                String str = userDataScope != null ? userDataScope.getXyid() : scopeCache.getDeptId();
                wrapper.lambda().or(q -> q.in(CourseMark::getXyid, str)).or(q1 -> q1.isNull(CourseMark::getXyid));
            } else if (Constants.ROLE_SCOPE_BJ.equals(role.getRoleScope())) {
                if (userDataScope != null) {
                    wrapper.lambda().or(q -> q.in(CourseMark::getBjid, (Object[]) CommonUtil.split(userDataScope.getBjid())))
                            .or(q1 -> q1.isNull(CourseMark::getXyid));
                } else {
                    wrapper.lambda().or(q -> q.in(CourseMark::getXyid, (Object[]) CommonUtil.split(scopeCache.getDeptId()))
                            .isNull(CourseMark::getBjid)).or(q1 -> q1.isNull(CourseMark::getXyid));
                }
            }
        }
        page = mapper.selectPage(page, wrapper);
        List<CourseMark> records = page.getRecords();
        List<CourseMarkVO> courseMarkVOS = new ArrayList<>();
        for (CourseMark record : records) {
            CourseMarkVO courseMarkVO = new CourseMarkVO();
            BeanUtils.copyProperties(record, courseMarkVO);
            courseMarkVOS.add(courseMarkVO);
        }
        UserInfoUtil.codeTextSet(courseMarkVOS);
        return new PageResult<>(courseMarkVOS, page.getTotal());
    }
}
