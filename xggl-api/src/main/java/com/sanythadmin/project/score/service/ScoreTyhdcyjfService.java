package com.sanythadmin.project.score.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.score.entity.ScoreTycj;
import com.sanythadmin.project.score.entity.ScoreTyhdcyjf;
import com.sanythadmin.project.score.param.ScoreTycjParam;
import com.sanythadmin.project.score.param.ScoreTyhdcyjfParam;

import java.util.List;
import java.util.Map;

/**
 * 体育活动参与加分Service
 *
 * <AUTHOR>
 * @since 2025-07-27 09:47:33
 */
public interface ScoreTyhdcyjfService extends IService<ScoreTyhdcyjf> {
    public PageResult<ScoreTyhdcyjf> page(ScoreTyhdcyjfParam param);
    public void saveScore(ScoreTyhdcyjf object);

    public void removeByParam(ScoreTyhdcyjfParam param);

    public List<ScoreTyhdcyjf> list(ScoreTyhdcyjfParam param);
    public List<Map<String, Object>> years();
}
