package com.sanythadmin.project.score.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.sanythadmin.common.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 课程成绩结果
 *
 * <AUTHOR>
 * @since 2025-03-19 16:59:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_COURSE_SCORE_RESULT")
@Entity
@Table(name = "SYT_COURSE_SCORE_RESULT")
public class CourseScoreResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 测评年份
     */
    @Column(name = "CPNF", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("CPNF")
    private String cpnf;

    /**
     * 学年学期
     */
    @Column(name = "XNXQ", columnDefinition = ColumnType.VARCHAR2_100)
    @TableField("XNXQ")
    private String xnxq;

    /**
     * 学号
     */
    @Column(name = "XGH", columnDefinition = ColumnType.VARCHAR2_50)
    @TableField("XGH")
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 学院名称
     */
    @Column(name = "XYMC")
    @TableField("XYMC")
    private String xymc;

    /**
     * 专业名称
     */
    @Column(name = "ZYMC")
    @TableField("ZYMC")
    private String zymc;

    /**
     * 年级名称
     */
    @Column(name = "NJMC")
    @TableField("NJMC")
    private String njmc;

    /**
     * 班级名称
     */
    @Column(name = "BJMC")
    @TableField("BJMC")
    private String bjmc;

    /**
     * 各项成绩
     */
    @Column(name = "GXCJ", columnDefinition = ColumnType.VARCHAR2_256)
    @TableField(value = "GXCJ", updateStrategy = FieldStrategy.ALWAYS)
    private String gxcj;

    /**
     * 加权成绩
     */
    @Column(name = "JQCJ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "JQCJ", updateStrategy = FieldStrategy.ALWAYS)
    private Double jqcj;

    /**
     * 平均成绩
     */
    @Column(name = "PJCJ", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "PJCJ", updateStrategy = FieldStrategy.ALWAYS)
    private Double pjcj;

    /**
     * 总人数
     */
    @Column(name = "ZRS")
    @TableField(value = "ZRS", updateStrategy = FieldStrategy.ALWAYS)
    private Integer zrs;

    /**
     * 学院人数
     */
    @Column(name = "XYRS")
    @TableField(value = "XYRS", updateStrategy = FieldStrategy.ALWAYS)
    private Integer xyrs;

    /**
     * 专业人数
     */
    @Column(name = "ZYRS")
    @TableField(value = "ZYRS", updateStrategy = FieldStrategy.ALWAYS)
    private Integer zyrs;

    /**
     * 班级人数
     */
    @Column(name = "BJRS")
    @TableField(value = "BJRS", updateStrategy = FieldStrategy.ALWAYS)
    private Integer bjrs;

    /**
     * 加权成绩排名
     */
    @Column(name = "JQCJPM")
    @TableField(value = "JQCJPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer jqcjpm;

    /**
     * 加权成绩排名百分比
     */
    @Column(name = "JQCJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "JQCJPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double jqcjpmbfb;

    /**
     * 加权成绩学院排名
     */
    @Column(name = "JQCJXYPM")
    @TableField(value = "JQCJXYPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer jqcjxypm;

    /**
     * 加权成绩学院排名百分比
     */
    @Column(name = "JQCJXYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "JQCJXYPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double jqcjxypmbfb;

    /**
     * 加权成绩专业排名
     */
    @Column(name = "JQCJZYPM")
    @TableField(value = "JQCJZYPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer jqcjzypm;

    /**
     * 加权成绩专业排名百分比
     */
    @Column(name = "JQCJZYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "JQCJZYPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double jqcjzypmbfb;

    /**
     * 加权成绩班级排名
     */
    @Column(name = "JQCJBJPM")
    @TableField(value = "JQCJBJPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer jqcjbjpm;

    /**
     * 加权成绩班级排名百分比
     */
    @Column(name = "JQCJBJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "JQCJBJPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double jqcjbjpmbfb;

    /**
     * 平均成绩排名
     */
    @Column(name = "PJCJPM")
    @TableField(value = "PJCJPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer pjcjpm;

    /**
     * 平均成绩排名百分比
     */
    @Column(name = "PJCJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "PJCJPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double pjcjpmbfb;

    /**
     * 平均成绩学院排名
     */
    @Column(name = "PJCJXYPM")
    @TableField(value = "PJCJXYPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer pjcjxypm;

    /**
     * 平均成绩学院排名百分比
     */
    @Column(name = "PJCJXYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "PJCJXYPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double pjcjxypmbfb;

    /**
     * 平均成绩专业排名
     */
    @Column(name = "PJCJZYPM")
    @TableField(value = "PJCJZYPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer pjcjzypm;

    /**
     * 平均成绩专业排名百分比
     */
    @Column(name = "PJCJZYPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "PJCJZYPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double pjcjzypmbfb;

    /**
     * 平均成绩班级排名
     */
    @Column(name = "PJCJBJPM")
    @TableField(value = "PJCJBJPM", updateStrategy = FieldStrategy.ALWAYS)
    private Integer pjcjbjpm;

    /**
     * 平均成绩班级排名百分比
     */
    @Column(name = "PJCJBJPMBFB", columnDefinition = ColumnType.NUMBER_5_2)
    @TableField(value = "PJCJBJPMBFB", updateStrategy = FieldStrategy.ALWAYS)
    private Double pjcjbjpmbfb;

    /**
     * 培养出层次ID
     */
    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 年级ID
     */
    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    /**
     * 学院ID
     */
    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    /**
     * 综测参数配置ID
     */
    @Column(name = "CONFIG_ID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("CONFIG_ID")
    private String configId;

}
