package com.sanythadmin.project.score.param;

import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 学生各科课程成绩查询参数
 *
 * <AUTHOR>
 * @since 2025-03-17 17:28:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CourseScoreItemParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @QueryField(type = QueryType.EQ)
    private String xgh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 学院名称
     */
    private String xymc;

    /**
     * 专业名称
     */
    private String zymc;

    /**
     * 班级名称
     */
    private String bjmc;

    /**
     * 年级名称
     */
    @QueryField(type = QueryType.IN_STR)
    private String njmc;

    /**
     * 学年学期
     */
    @QueryField(type = QueryType.IN_STR)
    private String xnxq;

    /**
     * 学年
     */
    @QueryField(type = QueryType.IN_STR)
    private String xn;

    /**
     * 课程号
     */
    private String kch;

    /**
     * 课程名
     */
    private String kcm;

    /**
     * 课序号
     */
    private String kxh;

    /**
     * 课程属性代码
     */
    private String kcsxdm;

    /**
     * 课程属性名称
     */
    @QueryField(type = QueryType.IN_STR)
    private String kcsxmc;

    /**
     * 考试类型代码
     */
    private String kslxdm;

    /**
     * 考试类型名称
     */
    private String kslxmc;

    /**
     * 修读方式名称
     */
    private String xdfsmc;

    /**
     * 修读方式代码
     */
    private String xdfsdm;

    /**
     * 补考方式代码
     */
    private String bkfsdm;

    /**
     * 补考方式名称
     */
    private String bkfsmc;

    /**
     * 成绩标识
     */
    private String cjbs;

    /**
     * 未通过原因说明
     */
    private String wtgyysm;

    /**
     * 未通过原因代码
     */
    private String wtgyydm;

    @Override
    public String getSort() {
        return StringUtils.hasText(super.getSort()) ? super.getSort() : "xnxq desc";
    }
}
