package com.sanythadmin.platform.dingtalk.entity;

import com.sanythadmin.platform.entity.PlatformBaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

/**
 * 钉钉应用表
 *
 * @since 2025/4/6 17:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "SYT_DINGTALK_APP")
public class DingtalkApp extends PlatformBaseEntity {


    private String name;
    private String agentId;
    private String clientId;
    private String clientSecret;
//    /**
//     * PC 重定向URL
//     */
//    private String pcRedirectUrl;
    /**
     * Mobile 重定向路由
     */
    private String mobileRedirectPath;
    /**
     * Mobile 重定向URL
     */
    private String mobileRedirectUrl;
    private String description;
    @Column(name = "is_debug", nullable = false)
    @ColumnDefault("0")
    @NotNull
    private boolean debug;

}
