package com.sanythadmin.platform.dingtalk.message;

import com.alibaba.fastjson2.JSON;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.entity.DingtalkUser;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanythadmin.platform.dingtalk.util.DingtalkUtil;
import com.sanythadmin.platform.util.PlatformUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @since 2025/4/10 13:36
 */
public class DtDealMessageRunner implements Runnable {
    private final Logger logger = LoggerFactory.getLogger(DtDealMessageRunner.class);
    private final DingtalkUserRepository userRepository;
    private final DtMessageParamCache paramCache;
    private final Map<String, List<String>> usernameMap = new HashMap<>();
    private DingtalkApp app;
    private Boolean output;
    private String urlRoot;
    private String actionCardTitle;
    private String mobileUrlRoot;
    private static final String ACTION_CARD_TITLE = "学工系统业务通知";
    private static final String DEFAULT_URL_TEXT = "查看详情";

    public DtDealMessageRunner(DtMessageParamCache paramCache) {
        this.userRepository = paramCache.getUserRepository();
        this.paramCache = paramCache;
    }

    private DingtalkApp getApp() {
        if (app == null) {
            app = paramCache.getApp();
        }
        return app;
    }

    private boolean isOutput() {
        if (output == null) {
            output = paramCache.isSendOutput();
        }
        return output;
    }

    public String getActionCardTitle() {
        if (actionCardTitle == null) {
            DingtalkParam paramTitle = paramCache.getParamRepository().findFirstByKey(DingtalkParamKey.MessageActionCardTitle);
            if (paramTitle != null && StringUtils.isNotEmpty(paramTitle.getValue())) {
                actionCardTitle = paramTitle.getValue();
            } else {
                actionCardTitle = ACTION_CARD_TITLE;
            }
        }
        return actionCardTitle;
    }

    private String getUrlRoot() {
        if (urlRoot == null) {
            DingtalkParam param = paramCache.getParamRepository().findFirstByKey(DingtalkParamKey.ServerUrlRoot);
            PlatformUtil.checkTrue(param != null && StringUtils.isNotEmpty(param.getValue()), DingtalkParamKey.ServerUrlRoot.getText()+"为空");
            urlRoot = param.getValue();
        }
        return urlRoot;
    }

    private String getMobileUrlRoot() {
        if (mobileUrlRoot == null) {
            DingtalkParam param = paramCache.getParamRepository().findFirstByKey(DingtalkParamKey.MobileUrlRoot);
            PlatformUtil.checkTrue(param != null && StringUtils.isNotEmpty(param.getValue()), DingtalkParamKey.MobileUrlRoot.getText()+"为空");
            mobileUrlRoot = param.getValue();
        }
        return mobileUrlRoot;
    }

    @Override
    public void run() {
        try {
            RedisTemplate<String, Object> redisTemplate = paramCache.getRedisTemplate();
            while (true) {
                DtMessageData take = (DtMessageData) redisTemplate.opsForList().rightPop(DingtalkUtil.MESSAGE_QUEUE_KEY, 5, TimeUnit.SECONDS);
                if (take == null) {
                    break;
                }
                process(take);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void process(DtMessageData messageData) {
        Set<String> userIds2 = new HashSet<>(messageData.getUsernames());
        for (String username : messageData.getUsernames()) {
            if (usernameMap.containsKey(username)) {
                userIds2.addAll(usernameMap.get(username));
                continue;
            }

            ArrayList<String> usernames = new ArrayList<>();
            usernameMap.put(username, usernames);
            List<DingtalkUser> users = userRepository.findBySystemUsername(username);
            users.stream().map(DingtalkUser::getDingUserId).forEach(usernames::add);
            userIds2.addAll(usernames);
        }
        if (userIds2.isEmpty()) {
            return;
        }

        DtOapiV2MessageSendReq sendPack = new DtOapiV2MessageSendReq();
        DingtalkApp app = getApp();
        sendPack.setAgent_id(Long.valueOf(app.getAgentId()));
        sendPack.setUserIds(userIds2);

        switch (messageData.getType()) {
            case text:
                sendPack.setMsg(DtOapiV2MessageSendReqMsg.ofText(messageData.getContent()));
                break;
            case action_card:
                // 卡片消息标题
                String title = messageData.getTitle();
                if (StringUtils.isEmpty(title)) {
                    title = getActionCardTitle();
                }

                // 卡片消息链接，移动端内部路由
                String mobilePath = messageData.getMobilePath();
                String destUrl = null;
                String urlText = StringUtils.isNotEmpty(messageData.getUrlTitle()) ? messageData.getUrlTitle() : DEFAULT_URL_TEXT;
                if (StringUtils.isNotEmpty(mobilePath)) {
//                    String encodePath = URLEncoder.encode(mobilePath, StandardCharsets.UTF_8);

                    String mobileUrlRoot = getMobileUrlRoot();
                    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(mobileUrlRoot);
                    uriComponentsBuilder.path(DingtalkUtil.MOBILE_ENTRANCE_PATH);
                    uriComponentsBuilder.queryParam("u", mobilePath);
                    String originUrl = uriComponentsBuilder.build().toUri().toString();

//                    String encode = URLEncoder.encode(originUrl, StandardCharsets.UTF_8);
                    String b64Url = Base64.encodeBase64URLSafeString(originUrl.getBytes(StandardCharsets.UTF_8));

                    String urlRoot = getUrlRoot();
                    UriComponentsBuilder uriComponentsBuilder1 = UriComponentsBuilder.fromUriString(urlRoot);
                    uriComponentsBuilder1.path("/nonlogin/platform/dingtalk/webAuth/preIndex");
//                    uriComponentsBuilder1.queryParam("id", app.getId());
                    uriComponentsBuilder1.queryParam("b64Url", b64Url);
                    if (messageData.getRoleId() != null) {
                        uriComponentsBuilder1.queryParam("b64RoleId", Base64.encodeBase64URLSafeString(messageData.getRoleId().getBytes(StandardCharsets.UTF_8)));
                    }
                    destUrl = uriComponentsBuilder1.build().toUri().toString();
                    PlatformUtil.checkTrue(destUrl.length() <= 500, "卡片消息链接长度已超500字：" + JSON.toJSONString(messageData));
                }

                String markdown = messageData.getMarkdown();
                sendPack.setMsg(DtOapiV2MessageSendReqMsg.ofActionCard(title, markdown, destUrl, urlText));
                break;
        }

        String accessToken = paramCache.getClientService().getAccessTokenInner(app);
        RestTemplate restTemplate = new RestTemplate();
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        uriComponentsBuilder.queryParam("access_token", accessToken);
        URI uri1 = uriComponentsBuilder.build().encode().toUri();
        ResponseEntity<DtOapiV2MessageSendRes> resEntity = restTemplate.postForEntity(uri1, sendPack, DtOapiV2MessageSendRes.class);

        if (!resEntity.getStatusCode().is2xxSuccessful() || resEntity.getBody() == null) {
            logger.error("dingtalk message task error, http status code: {}, result: {}", resEntity.getStatusCode(), resEntity.getBody());
            return;
        }

        DtOapiV2MessageSendRes res = resEntity.getBody();
        if (res.getErrCode() != 0) {
            logger.error("dingtalk message task error: {}, send: {}", res, sendPack);
        } else {
            if (isOutput()) {
                logger.info("dingtalk message task success, send: {}, result: {}", sendPack, res);
            }
        }
    }
}
