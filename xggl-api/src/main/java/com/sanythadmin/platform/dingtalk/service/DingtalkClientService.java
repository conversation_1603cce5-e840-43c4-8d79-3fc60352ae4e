package com.sanythadmin.platform.dingtalk.service;

import com.sanythadmin.platform.dingtalk.dto.DingtalkUserGetByCodeResult;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;

public interface DingtalkClientService {
    String getAccessTokenInner(DingtalkApp app);

    DingtalkUserGetByCodeResult getUserByCode(String accessToken, String authCode);

    String getJsapiTicket(DingtalkApp app);
}
