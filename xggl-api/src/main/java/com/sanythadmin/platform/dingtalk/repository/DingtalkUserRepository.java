package com.sanythadmin.platform.dingtalk.repository;

import com.sanythadmin.platform.dingtalk.entity.DingtalkUser;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface DingtalkUserRepository extends PagingAndSortingRepository<DingtalkUser, String>, CrudRepository<DingtalkUser, String>, JpaSpecificationExecutor<DingtalkUser> {
    DingtalkUser findFirstByDingUserId(String userid);

    DingtalkUser findFirstByDingUsernameAndDingUserIdNull(String name);

    List<DingtalkUser> findBySystemUsername(String systemUsername);
}
