package com.sanythadmin.platform.dingtalk.entity;

import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.entity.PlatformBaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

/**
 * 钉钉系统参数
 *
 * @since 2025/4/6 17:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "SYT_DINGTALK_PARAM")
public class DingtalkParam extends PlatformBaseEntity {

    @Column(unique = true)
    @Enumerated(EnumType.STRING)
    private DingtalkParamKey key;
    private String value;
    @Column(name = "enabled", nullable = false)
    @ColumnDefault("0")
    @NotNull
    private boolean enabled;
    private String description;

}
