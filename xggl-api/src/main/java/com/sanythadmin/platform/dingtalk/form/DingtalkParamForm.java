package com.sanythadmin.platform.dingtalk.form;

import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.form.PlatformBaseForm;
import com.sanythadmin.platform.vo.PlatformOption;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/4/6 19:02
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class DingtalkParamForm extends PlatformBaseForm {

    public DingtalkParamForm(DingtalkParam app) {
        super(app);
        this.key = PlatformOption.of(app.getKey().name(), app.getKey().getText());
        this.value = app.getValue();
        this.enabled = app.isEnabled() ? "是" : "否";
        this.description = app.getDescription();
    }

    /**
     * 参数类型 #不可修改
     */
    private PlatformOption key;
    private String value;
    private String enabled;
    private String description;
}
