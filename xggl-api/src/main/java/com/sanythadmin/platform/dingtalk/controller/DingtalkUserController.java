package com.sanythadmin.platform.dingtalk.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.platform.dingtalk.entity.DingtalkUser;
import com.sanythadmin.platform.dingtalk.form.DingtalkUserForm;
import com.sanythadmin.platform.dingtalk.query.DingtalkUserQuery;
import com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanythadmin.platform.util.PlatformUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 平台/钉钉/用户管理
 *
 * @since 2025/4/6 19:09
 */
@RestController
@RequestMapping("/api/platform/dingtalk/user")
public class DingtalkUserController {

    private final DingtalkUserRepository userRepository;

    public DingtalkUserController(DingtalkUserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * 分页查询
     * 权限标识：platform:dingtalk:user:list
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:user:list')")
    @GetMapping("/page")
    public PageResult<DingtalkUserForm> page(DingtalkUserQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<DingtalkUser> page = userRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getDingUsernameLike() != null)
                        return criteriaBuilder.like(root.get("dingUsername"), "%" + query.getDingUsernameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(DingtalkUserForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('platform:dingtalk:user:list')")
    @GetMapping("/{id}")
    public DingtalkUserForm get(@PathVariable("id") String id) {
        Optional<DingtalkUser> item = userRepository.findById(id);
        return new DingtalkUserForm(item.orElseThrow());
    }

    /**
     * 添加或修改
     * 权限标识：platform:dingtalk:user:operation
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:user:operation')")
    @OperationLog(module = "平台-钉钉-用户", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody DingtalkUserForm form) {
        DingtalkUser entity;
        if (form.getId() != null) {
            entity = userRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new DingtalkUser();
            entity.setCreatedAt(LocalDateTime.now());
        }

        PlatformUtil.checkTrue(form.getSystemUsername() != null, "系统用户名不能为空。");
        entity.setSystemUsername(form.getSystemUsername());
        entity.setDingUserId(form.getDingUserId());
        entity.setDingUsername(form.getDingUsername());
//        entity.setDingMobile(form.getDingMobile());

        userRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：platform:dingtalk:user:remove
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:user:remove')")
    @OperationLog(module = "平台-钉钉-用户", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        userRepository.deleteAllById(ids);
    }
}
