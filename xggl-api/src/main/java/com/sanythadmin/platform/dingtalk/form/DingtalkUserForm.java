package com.sanythadmin.platform.dingtalk.form;

import com.sanythadmin.platform.dingtalk.entity.DingtalkUser;
import com.sanythadmin.platform.form.PlatformBaseForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/4/6 19:08
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class DingtalkUserForm extends PlatformBaseForm {

    public DingtalkUserForm(DingtalkUser app) {
        super(app);
        this.dingUserId = app.getDingUserId();
        this.dingUsername = app.getDingUsername();
//        this.dingMobile = app.getDingMobile();
        this.systemUsername = app.getSystemUsername();
    }

    /**
     * 钉钉用户Id
     */
    private String dingUserId;
    /**
     * 钉钉用户名
     */
    private String dingUsername;
    /**
     * 系统用户名
     */
    private String systemUsername;
}
