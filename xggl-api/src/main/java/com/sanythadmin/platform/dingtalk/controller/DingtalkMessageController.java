package com.sanythadmin.platform.dingtalk.controller;

import com.sanythadmin.platform.dingtalk.message.DingtalkMessageService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 平台/钉钉/消息测试
 *
 * @since 2025/4/10 10:28
 */
@PreAuthorize("hasAuthority('platform:dingtalk:message')")
@RestController
@RequestMapping("/api/platform/dingtalk/message")
public class DingtalkMessageController {

    private final DingtalkMessageService messageService;

    public DingtalkMessageController(DingtalkMessageService messageService) {
        this.messageService = messageService;
    }

    @RequestMapping("send")
    public void send(String username, String content) {
        messageService.pushText(username, content);
    }

    @RequestMapping("sendCard")
    public void sendCard(String username, String title, String markdown, String path, String urlTitle, String roleId) {
        messageService.pushActionCard(username, title, markdown, path, urlTitle, roleId);
    }
}
