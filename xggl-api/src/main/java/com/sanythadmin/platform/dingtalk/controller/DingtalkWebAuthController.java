package com.sanythadmin.platform.dingtalk.controller;

import com.sanythadmin.common.core.config.ConfigProperties;
import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.security.JwtSubject;
import com.sanythadmin.common.core.security.JwtUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysJwt;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.service.SysAccountRoleService;
import com.sanythadmin.common.system.service.SysAccountService;
import com.sanythadmin.common.system.service.SysJwtService;
import com.sanythadmin.common.system.service.SysSessionVarService;
import com.sanythadmin.platform.dingtalk.dto.DingtalkUserGetByCodeResult;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.entity.DingtalkUser;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.dingtalk.enums.DingtalkVarKey;
import com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanythadmin.platform.dingtalk.service.DingtalkClientService;
import com.sanythadmin.platform.dingtalk.util.DingtalkUtil;
import com.sanythadmin.platform.dingtalk.vo.DingtalkWebAuthResultVo;
import com.sanythadmin.platform.util.PlatformUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 平台/钉钉/网页认证
 *
 * @since 2025/4/7 10:23
 */
@AllArgsConstructor
@Controller
@RequestMapping("/nonlogin/platform/dingtalk/webAuth")
public class DingtalkWebAuthController {

    protected final Log logger = LogFactory.getLog(getClass());
    private final DingtalkAppRepository appRepository;
    private final DingtalkClientService clientService;
    private final DingtalkUserRepository userRepository;
    private final DingtalkSysAccountRepository sysAccountRepository;
    private final SysJwtService sysJwtService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SysAccountRoleService sysAccountRoleService;
    private final SysAccountService sysAccountService;
    private final DingtalkParamRepository paramRepository;
    private final SysSessionVarService sysSessionVarService;

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception ex) {
//        return ResponseEntity
//                .status(HttpStatus.INTERNAL_SERVER_ERROR)
//                .body(Map.of("message", ex.getMessage()));
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("系统异常：" + ex.getMessage());
    }

    private String getMobileUrl(DingtalkApp app) {
        if (StringUtils.isNotEmpty(app.getMobileRedirectUrl())) {
            return app.getMobileRedirectUrl();
        }
        if (StringUtils.isEmpty(app.getMobileRedirectPath())) {
            return null;
        }

        DingtalkParam param = paramRepository.findFirstByKey(DingtalkParamKey.MobileUrlRoot);
        if (param == null || StringUtils.isEmpty(param.getValue())) {
            return null;
        }

        String mobileUrlRoot = param.getValue();

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(mobileUrlRoot);
        uriComponentsBuilder.path(DingtalkUtil.MOBILE_ENTRANCE_PATH);
        uriComponentsBuilder.queryParam("u", app.getMobileRedirectPath());

        return uriComponentsBuilder.build().toUri().toString();
    }

    @RequestMapping("preIndex")
    public String preIndex(String id, ModelMap modelMap) {
        modelMap.put("corpId", paramRepository.findFirstByKey(DingtalkParamKey.CorpId).getValue());
        if (StringUtils.isEmpty(id)) {
            id = paramRepository.findFirstByKey(DingtalkParamKey.MainAppId).getValue();
        }
        DingtalkApp app = appRepository.findById(id).orElseThrow(() -> new BusinessException("应用不存在"));
        modelMap.put("appId", app.getId());
        modelMap.put("clientId", app.getClientId());
//        modelMap.put("pcUrl", app.getPcRedirectUrl());
        modelMap.put("mobileUrl", getMobileUrl(app));
        modelMap.put("debug", app.isDebug());

        return "/platform/dingtalk/web_auth/index";
    }

    /**
     * 集中身份认证
     *
     * @since 2025/4/7 16:04
     */
    @ResponseBody
    @RequestMapping("authenticate")
    public DingtalkWebAuthResultVo authenticate(String appId, String authCode, String b64RoleId, HttpServletRequest request) {
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(authCode), "免登授权码为空");
        DingtalkApp app = appRepository.findById(appId).orElseThrow();

        String accessToken = clientService.getAccessTokenInner(app);

        DingtalkUserGetByCodeResult userResult = clientService.getUserByCode(accessToken, authCode);

        boolean exists = sysAccountRepository.existsById(userResult.getUserid());
        if (exists) {
            return signToken(app, userResult.getUserid(), b64RoleId, request);
        }

        // 根据 钉钉UserId 查找对照用户
        DingtalkUser userTransform = userRepository.findFirstByDingUserId(userResult.getUserid());
        if (userTransform != null) {
            PlatformUtil.checkTrue(StringUtils.isNotEmpty(userTransform.getSystemUsername()), "未获取到系统用户名");
            return signToken(app, userTransform.getSystemUsername(), b64RoleId, request);
        }

        // 根据 钉钉name 查找对照用户
        userTransform = userRepository.findFirstByDingUsernameAndDingUserIdNull(userResult.getName());
        if (userTransform != null) {
            PlatformUtil.checkTrue(StringUtils.isNotEmpty(userTransform.getSystemUsername()), "未获取到系统用户名");
            return signToken(app, userTransform.getSystemUsername(), b64RoleId, request);
        }

        throw new BusinessException("钉钉用户【" + userResult.getUserid() + "-" + userResult.getName() + "】认证失败，无对应系统用户");
    }

    private DingtalkWebAuthResultVo signToken(DingtalkApp app, String username, String b64RoleId, HttpServletRequest request) {
        SysJwt config = CommonUtil.listGetOne(sysJwtService.list());
        // 签发token
        SysAccount account = sysAccountService.getByUsername(username);
        Long expireTime = config.getTokenExpireTime();
        JwtSubject subject = JwtSubject.genCacheKey(account.getUsername(), account.getRole().getId());
        String access_token = JwtUtil.buildToken(subject,
                expireTime, config.getTokenKey());
        redisTemplate.opsForValue().set(subject.getCacheKey(), account, expireTime, TimeUnit.SECONDS);

        String rid = null;
        if (b64RoleId != null) {
            // 设置角色
            String roleId = new String(Base64.decodeBase64(b64RoleId));
            List<SysRole> roleList = account.getRoleList();
            boolean exists = roleList.stream().map(SysRole::getId).anyMatch(roleId::equals);
            if (exists) {
                account = sysAccountService.getByUsernameAndRoleId(account.getUsername(), roleId);
                redisTemplate.opsForValue().set(account.getUsername(), account, expireTime, TimeUnit.SECONDS);
                rid = roleId;
            }
        }

//        return "redirect:" + app.getMobileRedirectUrl() + "?token=" + access_token;
        if (app.isDebug()) {
            System.out.println("dingtalk sign system token: " + access_token);
        }
        sysSessionVarService.setValue(access_token, DingtalkVarKey.APP_ID.getKey(), app.getId());

        DingtalkWebAuthResultVo resultVo = new DingtalkWebAuthResultVo();
        resultVo.setToken(access_token);
        resultVo.setRoleId(rid);
        return resultVo;
    }

    @ResponseBody
    @RequestMapping("debug-info")
    public void debugInfo(String info) {
        logger.info("debug-info: " + info);
    }
}
