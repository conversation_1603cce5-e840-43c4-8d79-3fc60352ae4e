package com.sanythadmin.platform.dingtalk.enums;

import lombok.Getter;

@Getter
public enum DingtalkParamKey {

    CorpId("CorpId"),
    MainAppId("主应用Id"),
    EnableMessage("启用消息推送"),
    EnableMessageOutput("是否启用消息推送输出"),
    MessageActionCardTitle("卡片消息标题"),
    ServerUrlRoot("网址根"),
    MobileUrlRoot("移动端网址根"),
//    PcUrlRoot("PC端网址根"),
    ;

    private final String text;

    DingtalkParamKey(String text) {
        this.text = text;
    }

}
