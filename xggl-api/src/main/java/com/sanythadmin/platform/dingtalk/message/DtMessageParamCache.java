package com.sanythadmin.platform.dingtalk.message;

import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanythadmin.platform.dingtalk.service.DingtalkClientService;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @since 2025/4/9 15:08
 */
@Getter
public class DtMessageParamCache {

    private static final int expiration_s = 60;
    private final DingtalkParamRepository paramRepository;
    private final DingtalkAppRepository appRepository;
    private final DingtalkUserRepository userRepository;
    private final DingtalkClientService clientService;
    private final RedisTemplate<String, Object> redisTemplate;

    private DtMessageParamEntity<DingtalkApp> appEntity;
    private DtMessageParamEntity<Boolean> isSendEntity;
    private DtMessageParamEntity<Boolean> sendOutputEntity;
    private DtMessageParamEntity<DingtalkParam> messageAppParamEntity;

    public DtMessageParamCache(DingtalkParamRepository paramRepository, DingtalkAppRepository appRepository, DingtalkUserRepository userRepository, DingtalkClientService clientService, RedisTemplate<String, Object> redisTemplate) {
        this.paramRepository = paramRepository;
        this.appRepository = appRepository;
        this.userRepository = userRepository;
        this.clientService = clientService;
        this.redisTemplate = redisTemplate;
    }

    private boolean checkDate(DtMessageParamEntity<?> entity) {
        if (entity == null) {
            return true;
        }
        return checkDate(entity.getDate());
    }

    private synchronized DingtalkParam getMessageAppParam() {
        boolean getNew = checkDate(messageAppParamEntity);
        if (getNew) {
            DingtalkParam param = paramRepository.findFirstByKey(DingtalkParamKey.MainAppId);
            messageAppParamEntity = new DtMessageParamEntity<>(param);
        }
        return messageAppParamEntity.getValue();
    }

    public synchronized DingtalkApp getApp() {
        boolean getNew = checkDate(appEntity);
        if (getNew) {
            DingtalkParam messageAppParam = getMessageAppParam();
            String appId = messageAppParam.getValue();
            if (StringUtils.isNotEmpty(appId)) {
                appEntity = new DtMessageParamEntity<>(appRepository.findById(appId).orElseThrow());
            } else {
                appEntity = new DtMessageParamEntity<>(null);
            }
        }

        return appEntity.getValue();
    }

    public synchronized boolean canSend() {
        boolean getNew = checkDate(isSendEntity);
        if (getNew) {
            boolean b = false;
            DingtalkParam param = paramRepository.findFirstByKey(DingtalkParamKey.EnableMessage);
            if (param != null && param.isEnabled()) {
                DingtalkApp app = getApp();
                b = app != null;
            }
            isSendEntity = new DtMessageParamEntity<>(b);
        }

        return isSendEntity.getValue();
    }

    private boolean checkDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return true;
        }
        boolean getNew = false;
        Duration between = Duration.between(localDateTime, LocalDateTime.now());
        if (between.getSeconds() > expiration_s) {
            getNew = true;
        }
        return getNew;
    }

    public synchronized boolean isSendOutput() {
        boolean getNew = checkDate(sendOutputEntity);
        if (getNew) {
            DingtalkParam param = paramRepository.findFirstByKey(DingtalkParamKey.EnableMessageOutput);
            sendOutputEntity = new DtMessageParamEntity<>(param != null && param.isEnabled());
        }
        return sendOutputEntity.getValue();
    }

}
