package com.sanythadmin.platform.dingtalk.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.form.DingtalkAppForm;
import com.sanythadmin.platform.dingtalk.query.DingtalkAppQuery;
import com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanythadmin.platform.util.PlatformUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 平台/钉钉/应用管理
 *
 * @since 2025/4/6 18:46
 */
@RestController
@RequestMapping("/api/platform/dingtalk/app")
public class DingtalkAppController {

    private final DingtalkAppRepository appRepository;

    public DingtalkAppController(DingtalkAppRepository appRepository) {
        this.appRepository = appRepository;
    }

    /**
     * 分页查询
     * 权限标识：platform:dingtalk:app:list
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:app:list')")
    @GetMapping("/page")
    public PageResult<DingtalkAppForm> page(DingtalkAppQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<DingtalkApp> page = appRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(DingtalkAppForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('platform:dingtalk:app:list')")
    @GetMapping("/{id}")
    public DingtalkAppForm get(@PathVariable("id") String id) {
        Optional<DingtalkApp> item = appRepository.findById(id);
        return new DingtalkAppForm(item.orElseThrow());
    }

    /**
     * 添加或修改
     * 权限标识：platform:dingtalk:app:operation
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:app:operation')")
    @OperationLog(module = "平台-钉钉-应用", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody DingtalkAppForm form) {
        DingtalkApp entity;
        if (form.getId() != null) {
            entity = appRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new DingtalkApp();
            entity.setCreatedAt(LocalDateTime.now());
        }

        PlatformUtil.checkTrue(StringUtils.isNotEmpty(form.getName()), "名称不能为空");
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(form.getAgentId()), "agentId不能为空");
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(form.getClientId()), "clientId不能为空");
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(form.getClientSecret()), "clientSecret不能为空");
        entity.setName(form.getName());
        entity.setAgentId(form.getAgentId());
        entity.setDescription(form.getDescription());
        entity.setClientId(form.getClientId());
        entity.setClientSecret(form.getClientSecret());
//        entity.setPcRedirectUrl(form.getPcRedirectUrl());
        entity.setMobileRedirectPath(form.getMobileRedirectPath());
        entity.setMobileRedirectUrl(form.getMobileRedirectUrl());
        entity.setDebug("是".equals(form.getDebug()));

        appRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：platform:dingtalk:app:remove
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:app:remove')")
    @OperationLog(module = "平台-钉钉-应用", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        appRepository.deleteAllById(ids);
    }
}
