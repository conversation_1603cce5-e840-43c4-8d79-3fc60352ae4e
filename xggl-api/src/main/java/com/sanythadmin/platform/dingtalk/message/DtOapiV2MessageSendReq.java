package com.sanythadmin.platform.dingtalk.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;

/**
 * @since 2025/4/10 9:26
 */
@Data
public class DtOapiV2MessageSendReq {

    @JsonIgnore
    private Collection<String> userIds;
    private Long agent_id;
    private DtOapiV2MessageSendReqMsg msg;

    public String getUserid_list() {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        return String.join(",", userIds);
    }

}
