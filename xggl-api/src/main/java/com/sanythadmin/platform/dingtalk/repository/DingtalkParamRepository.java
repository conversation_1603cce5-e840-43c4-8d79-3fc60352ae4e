package com.sanythadmin.platform.dingtalk.repository;

import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface DingtalkParamRepository extends PagingAndSortingRepository<DingtalkParam, String>, CrudRepository<DingtalkParam, String>, JpaSpecificationExecutor<DingtalkParam> {
    DingtalkParam findFirstByKey(DingtalkParamKey key);

    @Query("select key from DingtalkParam")
    List<DingtalkParamKey> allKey();
}
