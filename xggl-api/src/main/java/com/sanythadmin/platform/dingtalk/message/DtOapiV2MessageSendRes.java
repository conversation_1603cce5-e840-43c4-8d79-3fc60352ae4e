package com.sanythadmin.platform.dingtalk.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @since 2025/4/10 9:40
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DtOapiV2MessageSendRes {

    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("errmsg")
    private String errMsg;
    @JsonProperty("errcode")
    private Integer errCode;
    @JsonProperty("task_id")
    private Long taskId;

}
