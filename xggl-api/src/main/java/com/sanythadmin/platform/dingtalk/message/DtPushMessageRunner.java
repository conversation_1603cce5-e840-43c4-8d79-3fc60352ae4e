package com.sanythadmin.platform.dingtalk.message;

import com.sanythadmin.platform.dingtalk.util.DingtalkUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2025/4/10 14:20
 */
@AllArgsConstructor
public class DtPushMessageRunner implements Runnable {

    private final AtomicBoolean isDeal;
    private final DtMessageParamCache dtMessageParamCache;
    private final DtMessageData message;

    @Override
    public void run() {
        RedisTemplate<String, Object> redisTemplate = dtMessageParamCache.getRedisTemplate();
        redisTemplate.opsForList().leftPush(DingtalkUtil.MESSAGE_QUEUE_KEY, message);
        if (isDeal.compareAndSet(false, true)) {

            DtDealMessageRunner dealMessageRunner = new DtDealMessageRunner(dtMessageParamCache);
            dealMessageRunner.run();

            isDeal.set(false);
        }
    }

}
