package com.sanythadmin.platform.dingtalk.entity;

import com.sanythadmin.platform.entity.PlatformBaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钉钉用户对照表
 *
 * @since 2025/4/6 17:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "SYT_DINGTALK_USER")
public class DingtalkUser extends PlatformBaseEntity {

    /**
     * 钉钉用户ID
     */
    private String dingUserId;
    /**
     * 钉钉用户名
     */
    private String dingUsername;
//    /**
//     * 钉钉手机号
//     */
//    private String dingMobile;
    /**
     * 系统用户名
     */
    private String systemUsername;

}
