package com.sanythadmin.platform.dingtalk.message;

import com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanythadmin.platform.dingtalk.service.DingtalkClientService;
import com.sanythadmin.platform.util.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @since 2023/8/8 16:39
 */
@Service
public class DingtalkMessageServiceImpl implements DingtalkMessageService {

    private final DtMessageParamCache dtMessageParamCache;
    private final ExecutorService messageThreadPool = new ThreadPoolExecutor(0, 10, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>());
    private final AtomicBoolean isDeal = new AtomicBoolean(false);

    public DingtalkMessageServiceImpl(DingtalkAppRepository appRepository, DingtalkParamRepository paramRepository, DingtalkUserRepository userRepository, DingtalkClientService clientService, RedisTemplate<String, Object> redisTemplate) {
        dtMessageParamCache = new DtMessageParamCache(paramRepository, appRepository, userRepository, clientService, redisTemplate);
    }

    @Override
    public void pushText(Collection<String> usernames, String content) {
        DtMessageData data = new DtMessageData();
        data.setType(DtMsgType.text);
        data.setUsernames(usernames);
        data.setContent(content);
        pushMessage(data);
    }

    @Override
    public void pushText(String username, String content) {
        pushText(Collections.singletonList(username), content);
    }

    @Override
    public void pushActionCard(Collection<String> usernames, String title, String markdown, String mobilePath, String urlTitle, String roleId) {
        DtMessageData data = new DtMessageData();
        data.setType(DtMsgType.action_card);
        data.setUsernames(usernames);
        data.setTitle(title);
        data.setMarkdown(markdown);
        data.setMobilePath(mobilePath);
        data.setUrlTitle(urlTitle);
        data.setRoleId(roleId);
        pushMessage(data);
    }

    @Override
    public void pushActionCard(String username, String title, String markdown, String mobilePath, String urlTitle, String roleId) {
        pushActionCard(Collections.singletonList(username), title, markdown, mobilePath, urlTitle, roleId);
    }

    private void pushMessage(DtMessageData message) {
        PlatformUtil.checkTrue(CollectionUtils.isNotEmpty(message.getUsernames()), "usernames为空");
        if (message.getType() == DtMsgType.text) {
            PlatformUtil.checkTrue(StringUtils.isNotEmpty(message.getContent()), "content为空");
        } else if (message.getType() == DtMsgType.action_card) {
            PlatformUtil.checkTrue(StringUtils.isNotEmpty(message.getMarkdown()), "markdown为空");
        }

        if (!dtMessageParamCache.canSend()) {
            return;
        }

        DtPushMessageRunner pushMessageRunner = new DtPushMessageRunner(isDeal, dtMessageParamCache, message);
        messageThreadPool.execute(pushMessageRunner);
    }
}
