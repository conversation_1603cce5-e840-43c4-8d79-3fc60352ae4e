package com.sanythadmin.platform.dingtalk.service.impl;

import com.sanythadmin.platform.dingtalk.dto.*;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.service.DingtalkClientService;
import com.sanythadmin.platform.util.PlatformUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.concurrent.TimeUnit;

/**
 * @since 2025/4/7 10:35
 */
@AllArgsConstructor
@Service
public class DingtalkClientServiceImpl implements DingtalkClientService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RestTemplate restTemplate = new RestTemplate();

//    public DingtalkClientServiceImpl(RedisTemplate<String, Object> redisTemplate) {
//        this.redisTemplate = redisTemplate;
//    }

    /**
     * 企业内部应用获取 access_token
     *
     * @since 2025/4/7 10:35
     */
    @Override
    public String getAccessTokenInner(DingtalkApp app) {
        PlatformUtil.checkTrue(app != null, "app为空");
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(app.getClientId()), "clientId为空");
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(app.getClientSecret()), "clientSecret为空");

        String cacheKey = "dingtalk:clientAccessToken:" + app.getId();

        String accessToken = (String) redisTemplate.opsForValue().get(cacheKey);
        if (accessToken != null) {
            return accessToken;
        }

        String url = "https://api.dingtalk.com/v1.0/oauth2/accessToken";
        DingtalkAccessTokenReq req = new DingtalkAccessTokenReq();
        req.setAppKey(app.getClientId());
        req.setAppSecret(app.getClientSecret());
        ResponseEntity<DingtalkAccessTokenRes> tokenResResponseEntity = restTemplate.postForEntity(url, req, DingtalkAccessTokenRes.class);
        if (!tokenResResponseEntity.getStatusCode().is2xxSuccessful()) {
            DingtalkAccessTokenRes body = tokenResResponseEntity.getBody();
            String message = "";
            if (body != null) {
                message = "code:" + body.getCode() + ", message:" + body.getMessage() + ", requestId:" + body.getRequestId();
            }
            PlatformUtil.throwException("获取AccessToken失败。" + message);
        }
        DingtalkAccessTokenRes body = tokenResResponseEntity.getBody();
        PlatformUtil.checkTrue(body != null, "获取AccessToken失败。body为空。");
        String clientAccessToken = body.getAccessToken();

        redisTemplate.opsForValue().set(cacheKey, clientAccessToken, body.getExpireIn(), TimeUnit.SECONDS);

        return clientAccessToken;
    }

    /**
     * 通过免登授权码和access_token获取用户的userId
     *
     * @since 2025/4/7 11:45
     */
    @Override
    public DingtalkUserGetByCodeResult getUserByCode(String accessToken, String authCode) {
        RestTemplate restTemplate = new RestTemplate();

        String url = "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo";
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url);
        uriComponentsBuilder.queryParam("access_token", accessToken);

        URI uri1 = uriComponentsBuilder.build().encode().toUri();
        DingtalkUserGetByCodeReq req = new DingtalkUserGetByCodeReq();
        req.setCode(authCode);
        DingtalkUserGetByCodeRes res = restTemplate.postForObject(uri1, req, DingtalkUserGetByCodeRes.class);
        PlatformUtil.checkTrue(res.getErrCode() == 0, "request_id:" + res.getRequestId() + ", errcode:" + res.getErrCode() + ", errmsg:" + res.getErrMsg());
        return res.getResult();
    }

    /**
     * 获取jsapiTicket
     *
     * @since 2025/4/8 14:13
     */
    @Override
    public String getJsapiTicket(DingtalkApp app) {
        String cacheKey = "dingtalk:clientJsapiTicket:" + app.getId();
        String jsapiTicket = (String) redisTemplate.opsForValue().get(cacheKey);
        if (jsapiTicket != null) {
            return jsapiTicket;
        }

        String accessToken = getAccessTokenInner(app);

        String url = "https://api.dingtalk.com/v1.0/oauth2/jsapiTickets";
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-acs-dingtalk-access-token", accessToken);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<DingtalkJsapiTicketRes> resEntity = restTemplate.postForEntity(url, httpEntity, DingtalkJsapiTicketRes.class);
        if (!resEntity.getStatusCode().is2xxSuccessful()) {
            DingtalkJsapiTicketRes body = resEntity.getBody();
            String message = "";
            if (body != null) {
                message = "code:" + body.getCode() + ", message:" + body.getMessage() + ", requestId:" + body.getRequestId();
            }
            PlatformUtil.throwException("获取jsapiTicket失败。" + message);
        }

        DingtalkJsapiTicketRes body = resEntity.getBody();
        PlatformUtil.checkTrue(body != null, "获取jsapiTicket失败。body为空。");
        String ticket = body.getJsapiTicket();
        redisTemplate.opsForValue().set(cacheKey, ticket, body.getExpireIn(), TimeUnit.SECONDS);

        return ticket;
    }
}
