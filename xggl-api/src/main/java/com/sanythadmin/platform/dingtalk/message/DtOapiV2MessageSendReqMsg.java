package com.sanythadmin.platform.dingtalk.message;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @since 2025/4/10 9:27
 */
@Data
public class DtOapiV2MessageSendReqMsg {

    @JsonProperty("msgtype")
    private DtMsgType dtMsgType;
    private DtMessageSendPackMsgText text;
    @JsonProperty("action_card")
    private DtOapiV2MessageMsgActionCard actionCard;

    public static DtOapiV2MessageSendReqMsg ofText(String text) {
        // text大于500时截取500以内文本
        text= StringUtils.abbreviate(text, 500);
        DtOapiV2MessageSendReqMsg msg = new DtOapiV2MessageSendReqMsg();
        msg.setDtMsgType(DtMsgType.text);
        msg.setText(DtMessageSendPackMsgText.of(text));
        return msg;
    }

    public static DtOapiV2MessageSendReqMsg ofActionCard(String title, String markdown, String url) {
        return DtOapiV2MessageSendReqMsg.ofActionCard(title, markdown, url, "查看详情");
    }

    public static DtOapiV2MessageSendReqMsg ofActionCard(String title, String markdown, String url, String urlText) {
        markdown = StringUtils.abbreviate(markdown, 1000);
        urlText = StringUtils.abbreviate(urlText, 20);

        DtOapiV2MessageMsgActionCard actionCard = new DtOapiV2MessageMsgActionCard();
        actionCard.setTitle(title);
        actionCard.setMarkdown(markdown);
        actionCard.setSingleTitle(urlText);
        actionCard.setSingleUrl(url);

        DtOapiV2MessageSendReqMsg msg = new DtOapiV2MessageSendReqMsg();
        msg.setDtMsgType(DtMsgType.action_card);
        msg.setActionCard(actionCard);
        return msg;
    }

}
