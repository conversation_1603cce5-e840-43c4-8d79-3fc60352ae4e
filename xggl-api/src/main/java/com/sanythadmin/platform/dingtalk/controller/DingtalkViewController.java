package com.sanythadmin.platform.dingtalk.controller;

import com.sanythadmin.common.system.service.SysSessionVarService;
import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.dingtalk.enums.DingtalkVarKey;
import com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository;
import com.sanythadmin.platform.dingtalk.service.DingtalkClientService;
import com.sanythadmin.platform.dingtalk.util.DingtalkConfigUtil;
import com.sanythadmin.platform.dingtalk.vo.DtJsConfigVo;
import com.sanythadmin.platform.util.PlatformUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 平台/钉钉/通用数据获取
 *
 * @since 2025/4/8 15:08
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/platform/dingtalk/view")
public class DingtalkViewController {

    private final SysSessionVarService sysSessionVarService;
    private final DingtalkAppRepository appRepository;
    private final DingtalkClientService clientService;
    private final DingtalkParamRepository paramRepository;

    /**
     * 获取 JSAPI 鉴权
     *
     * @since 2025/4/8 15:09
     */
    @ResponseBody
    @RequestMapping("getJsConfig")
    public DtJsConfigVo getJsConfig(String url, HttpServletRequest request) {
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(url), "鉴权url为空");
        if (url.contains("#")) {
            url = url.substring(0, url.indexOf("#"));
        }
//        if (url.contains("?")) {
//            url = url.substring(0, url.indexOf("?"));
//        }

        DingtalkParam param = paramRepository.findFirstByKey(DingtalkParamKey.CorpId);
        PlatformUtil.checkTrue(param != null && StringUtils.isNotEmpty(param.getValue()), "corpId为空");
        String corpId = param.getValue();

        String appId = sysSessionVarService.getValue(request, DingtalkVarKey.APP_ID.getKey(), String.class);
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(appId), "应用ID为空");
        DingtalkApp app = appRepository.findById(appId).orElseThrow();
        String agentId = app.getAgentId();
        PlatformUtil.checkTrue(StringUtils.isNotEmpty(agentId), "agentId为空");

        String jsapiTicket = clientService.getJsapiTicket(app);

        String nonceStr = RandomStringUtils.random(10, true, true);
        long timeStamp = System.currentTimeMillis();
        String signature = DingtalkConfigUtil.sign(jsapiTicket, nonceStr, timeStamp, url);

        return new DtJsConfigVo(agentId, corpId, timeStamp, nonceStr, signature, app.isDebug());
    }

    @RequestMapping("setValue")
    public void setValue(String v, HttpServletRequest request, String key) {
        sysSessionVarService.setValue(request, StringUtils.isNotEmpty(key) ? key : "DingtalkController", v);
    }

    @RequestMapping("getValue")
    public String getValue(HttpServletRequest request, String key) {
        return (String) sysSessionVarService.getValue(request, StringUtils.isNotEmpty(key) ? key : "DingtalkController");
    }
}
