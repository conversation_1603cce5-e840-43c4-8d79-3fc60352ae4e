package com.sanythadmin.platform.dingtalk.message;

import java.util.Collection;

public interface DingtalkMessageService {
    void pushText(Collection<String> usernames, String content);

    void pushText(String humanCode, String content);

    void pushActionCard(Collection<String> usernames, String title, String markdown, String mobilePath, String urlTitle, String roleId);

    void pushActionCard(String username, String title, String markdown, String mobilePath, String urlTitle, String roleId);
}
