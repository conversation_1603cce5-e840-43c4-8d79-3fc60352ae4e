package com.sanythadmin.platform.dingtalk.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.platform.dingtalk.entity.DingtalkParam;
import com.sanythadmin.platform.dingtalk.enums.DingtalkParamKey;
import com.sanythadmin.platform.dingtalk.form.DingtalkParamForm;
import com.sanythadmin.platform.dingtalk.query.DingtalkParamQuery;
import com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository;
import com.sanythadmin.platform.util.PlatformUtil;
import com.sanythadmin.platform.vo.PlatformOption;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 平台/钉钉/参数管理
 *
 * @since 2025/4/6 19:03
 */
@RestController
@RequestMapping("/api/platform/dingtalk/param")
public class DingtalkParamController {

    private final DingtalkParamRepository paramRepository;

    public DingtalkParamController(DingtalkParamRepository paramRepository) {
        this.paramRepository = paramRepository;
    }

    /**
     * 分页查询
     * 权限标识：platform:dingtalk:param:list
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:param:list')")
    @GetMapping("/page")
    public PageResult<DingtalkParamForm> page(DingtalkParamQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<DingtalkParam> page = paramRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getKey() != null)
                        return criteriaBuilder.equal(root.get("key"), query.getKey());
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(DingtalkParamForm::new).toList(), page.getTotalElements());
    }

    @PreAuthorize("hasAuthority('platform:dingtalk:param:list')")
    @GetMapping("/{id}")
    public DingtalkParamForm get(@PathVariable("id") String id) {
        Optional<DingtalkParam> item = paramRepository.findById(id);
        return new DingtalkParamForm(item.orElseThrow());
    }

    /**
     * 可选参数Key
     * 权限标识：platform:dingtalk:param:list
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:param:list')")
    @GetMapping("/optionalKey")
    public List<PlatformOption> optionalKey() {
        List<PlatformOption> list = new ArrayList<>();
        DingtalkParamKey[] paramKeys = DingtalkParamKey.values();
        List<DingtalkParamKey> keys = paramRepository.allKey();
        for (DingtalkParamKey paramKey : paramKeys) {
            if (keys.contains(paramKey)) {
                continue;
            }
            list.add(new PlatformOption(paramKey.name(), paramKey.getText()));
        }
        return list;
    }

    /**
     * 添加或修改
     * 权限标识：platform:dingtalk:param:operation
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:param:operation')")
    @OperationLog(module = "平台-钉钉-参数", comments = "保存")
    @PostMapping("/operation")
    public void operation(@RequestBody DingtalkParamForm form) {
        DingtalkParam entity;
        if (form.getId() != null) {
            entity = paramRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new DingtalkParam();
            entity.setCreatedAt(LocalDateTime.now());
            PlatformUtil.checkTrue(StringUtils.isNotEmpty(form.getKey().getId()), "key不能为空");
            DingtalkParamKey key = DingtalkParamKey.valueOf(form.getKey().getId());
            DingtalkParam param1 = paramRepository.findFirstByKey(key);
            PlatformUtil.checkTrue(param1 == null, "不可添加重复KEY");
            entity.setKey(key);
        }

        entity.setValue(form.getValue());
        entity.setEnabled("是".equals(form.getEnabled()));
        entity.setDescription(form.getDescription());

        paramRepository.save(entity);
    }

    /**
     * 批量删除
     * 权限标识：platform:dingtalk:param:remove
     */
    @PreAuthorize("hasAuthority('platform:dingtalk:param:remove')")
    @OperationLog(module = "平台-钉钉-参数", comments = "批量删除")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        paramRepository.deleteAllById(ids);
    }
}
