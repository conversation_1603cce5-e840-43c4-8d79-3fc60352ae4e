package com.sanythadmin.platform.dingtalk.form;

import com.sanythadmin.platform.dingtalk.entity.DingtalkApp;
import com.sanythadmin.platform.form.PlatformBaseForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/4/6 18:49
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class DingtalkAppForm extends PlatformBaseForm {

    public DingtalkAppForm(DingtalkApp app) {
        super(app);
        this.name = app.getName();
        this.agentId = app.getAgentId();
        this.clientId = app.getClientId();
        this.clientSecret = app.getClientSecret();
//        this.pcRedirectUrl = app.getPcRedirectUrl();
        this.mobileRedirectPath = app.getMobileRedirectPath();
        this.mobileRedirectUrl = app.getMobileRedirectUrl();
        this.description = app.getDescription();
        this.debug = app.isDebug() ? "是" : "否";
    }

    /**
     * 名称
     */
    private String name;
    private String agentId;
    private String clientId;
    private String clientSecret;
    /**
     * 移动端重定向路由
     */
    private String mobileRedirectPath;
    /**
     * 移动端重定向URL
     */
    private String mobileRedirectUrl;
    /**
     * 说明
     */
    private String description;
    /**
     * 是否启用调试
     */
    private String debug;
}
