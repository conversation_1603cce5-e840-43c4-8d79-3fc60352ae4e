package com.sanythadmin.platform.controller;

import com.sanythadmin.common.integration.authcenter.client.AcApiClient;
import com.sanythadmin.common.integration.authcenter.util.AuthCenterUtil;
import com.sanythadmin.common.system.service.SysSessionVarService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @since 2025/6/16 16:54
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/platform")
public class PlatformController {

    protected final Log logger = LogFactory.getLog(getClass());
    private final SysSessionVarService sysSessionVarService;
    private final AcApiClient acApiClient;

    @RequestMapping("/getJsapiConfig")
    public Object getJsapiConfig(String url, HttpServletRequest request) {
        String appId = (String) sysSessionVarService.getValue(request, AuthCenterUtil.AUTH_CENTER_APP_ID);
        return acApiClient.getJsapiConfig(appId, url).block();
    }

    @RequestMapping("debug-info")
    public void debugInfo(String info) {
        logger.info("debug-info: " + info);
    }
}
