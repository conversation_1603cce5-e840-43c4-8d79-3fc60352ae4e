package com.sanythadmin.platform.util;

import com.sanythadmin.common.core.exception.BusinessException;
import jakarta.servlet.http.HttpServletRequest;

/**
 * @since 2025/4/7 10:28
 */
public class PlatformUtil {

    public static void throwException(String message) throws BusinessException {
        throw new BusinessException(message);
    }

    public static void checkTrue(boolean expression, String content) throws BusinessException {
        if (!expression) {
            throw new BusinessException(content);
        }
    }

    public static String getUrlRoot(HttpServletRequest request) {
        String scheme = request.getScheme();
        int serverPort = request.getServerPort();
        String url_root = scheme + "://" + request.getServerName();
        if (serverPort != 80 && serverPort != 443) {
            url_root += ":" + serverPort;
        }

        return url_root;
    }

}
