package com.sanythadmin.platform.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @since 2025/4/6 17:28
 */
@Data
@EqualsAndHashCode(of = "id")
@MappedSuperclass
public abstract class PlatformBaseEntity {

    @Id
    @Column(length=36)
    @GeneratedValue(strategy= GenerationType.UUID)
    private String id;

    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
}
