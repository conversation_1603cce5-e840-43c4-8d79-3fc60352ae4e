package com.sanythadmin.platform.form;

import com.sanythadmin.platform.entity.PlatformBaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @since 2025/4/6 18:50
 */
@Data
@NoArgsConstructor
public abstract class PlatformBaseForm {

    public PlatformBaseForm(PlatformBaseEntity baseEntity) {
        this.id = baseEntity.getId();
        this.createdAtText = baseEntity.getCreatedAt();
        this.updatedAtText = baseEntity.getUpdatedAt();
    }

    private String id;
    private LocalDateTime createdAtText;
    private LocalDateTime updatedAtText;
}
