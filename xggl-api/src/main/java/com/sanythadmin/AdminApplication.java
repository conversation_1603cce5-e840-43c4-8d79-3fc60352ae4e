package com.sanythadmin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 启动类
 * Created by EleAdmin on 2018-02-22 11:29:03
 */
@EnableScheduling
@EnableAsync
@EnableTransactionManagement
@MapperScan("com.sanythadmin.**.mapper")
@SpringBootApplication
public class AdminApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(AdminApplication.class, args);
//        SpringContextUtil.setApplicationContext(context);
    }
}
