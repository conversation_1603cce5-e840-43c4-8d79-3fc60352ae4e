<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.1 https://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>${timestamp}</id>
    <!-- 打包类型，这里设置zip，则最终输出一个zip包 -->
    <formats>
        <format>zip</format>
    </formats>
    <!-- 设置项目外目录的处理方式，一条规则一个fileSet -->
    <fileSets>
        <fileSet>
            <directory>target/classes</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>*.yml</include>
                <include>*.xml</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
        <fileSet>
            <directory>target/lib</directory>
            <outputDirectory>/lib</outputDirectory>
            <excludes>
                <exclude>${project.groupId}:${project.artifactId}</exclude>
            </excludes>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/bin</directory>
            <includes>
                <include>*.sh</include>
                <include>*.bat</include>
            </includes>
            <fileMode>0755</fileMode>
            <outputDirectory>/bin</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>target</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>xggl-${version}-api.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>