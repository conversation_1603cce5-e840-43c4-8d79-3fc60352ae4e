package com.sanythadmin;

import com.sanythadmin.common.core.security.JwtSubject;
import com.sanythadmin.common.core.security.JwtUtil;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

/*
ab8f2dde6fba7c507755b8cb7d96738a,超级管理员
30a640ded7ebc5beb8fce3fb04ec6443,用人单位
36d5ca2b3906a97f4274ffb8e8a36210,系统管理员
3459a5b39afafe3877c8db87f1426740,心理咨询师
7bf21f3894826e65cde1f778d398639d,学生处
703773d305a7e2700ff29e51e72c4d82,学院副书记
2960f42fb51558ef38823a3f4e8c7dcd,辅导员
bc5afe28536dddbb9ae7ef7da50076b5,资助中心
a107826ec6cea94e6a3f231dc2254f1b,学生
30a640ded7ebc5beb8fce3fb04ec6442,班长
* */
public class GenAccessToken {
    // 角色映射表
    private static final Map<String, String> ROLE_MAP = new HashMap<>();
    
    // 数据库连接信息
    private static final String DB_URL = "********************************************";
    private static final String DB_USERNAME = "syt_xg_2023";
    private static final String DB_PASSWORD = "123456";
    
    static {
        ROLE_MAP.put("1", "ab8f2dde6fba7c507755b8cb7d96738a"); // 超级管理员
        ROLE_MAP.put("2", "30a640ded7ebc5beb8fce3fb04ec6443"); // 用人单位
        ROLE_MAP.put("3", "36d5ca2b3906a97f4274ffb8e8a36210"); // 系统管理员
        ROLE_MAP.put("4", "3459a5b39afafe3877c8db87f1426740"); // 心理咨询师
        ROLE_MAP.put("5", "7bf21f3894826e65cde1f778d398639d"); // 学生处
        ROLE_MAP.put("6", "703773d305a7e2700ff29e51e72c4d82"); // 学院副书记
        ROLE_MAP.put("7", "2960f42fb51558ef38823a3f4e8c7dcd"); // 辅导员
        ROLE_MAP.put("8", "bc5afe28536dddbb9ae7ef7da50076b5"); // 资助中心
        ROLE_MAP.put("9", "a107826ec6cea94e6a3f231dc2254f1b"); // 学生
        ROLE_MAP.put("10", "30a640ded7ebc5beb8fce3fb04ec6442"); // 班长
    }
    
    public static void main(String[] args) {
        String tokenKey = null;
        
        try {
            // 加载Oracle驱动
            Class.forName("oracle.jdbc.OracleDriver");
            
            // 连接数据库
            Connection conn = DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT TOKEN_KEY FROM SYT_SYS_JWT WHERE ROWNUM = 1");
            
            if (rs.next()) {
                tokenKey = rs.getString("TOKEN_KEY");
            }
            
            rs.close();
            stmt.close();
            conn.close();
            
            if (tokenKey == null) {
                System.err.println("未找到JWT配置，请确保SYT_SYS_JWT表中有数据");
                return;
            }
        } catch (Exception e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
            return;
        }
        
        // 显示角色选项
        System.out.println("请选择角色：");
        System.out.println("1. 超级管理员");
        System.out.println("2. 用人单位");
        System.out.println("3. 系统管理员");
        System.out.println("4. 心理咨询师");
        System.out.println("5. 学生处");
        System.out.println("6. 学院副书记");
        System.out.println("7. 辅导员");
        System.out.println("8. 资助中心");
        System.out.println("9. 学生");
        System.out.println("10. 班长");
        
        // 获取用户输入
        Scanner scanner = new Scanner(System.in);
        System.out.print("请输入角色编号: ");
        String choice = scanner.nextLine();
        String roleId = ROLE_MAP.getOrDefault(choice, ROLE_MAP.get("1")); // 默认超级管理员
        
        System.out.print("请输入用户名(默认admin): ");
        String userName = scanner.nextLine();
        if (userName.trim().isEmpty()) {
            userName = "admin";
        }
        
        System.out.print("请输入token有效期(天数，默认365): ");
        String daysStr = scanner.nextLine();
        long days = 365;
        if (!daysStr.trim().isEmpty()) {
            try {
                days = Long.parseLong(daysStr);
            } catch (NumberFormatException e) {
                System.out.println("输入格式不正确，使用默认值365天");
            }
        }
        
        Long expireTime = 60 * 60 * 24L * days;
        
        String access_token = JwtUtil.buildToken(JwtSubject.genCacheKey(userName, roleId),
                expireTime, tokenKey);
        System.out.println("\n生成的Token:");
        System.out.println("Bearer " + access_token);
    }
}