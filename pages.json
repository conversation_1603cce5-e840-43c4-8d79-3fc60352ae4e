{
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				// "enablePullDownRefresh": true,
				// "navigationStyle": "custom"
			}
		},
		{
			"path": "pages/checkin/m/index",
			"style": {
				"navigationBarTitleText": "签到项目",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/checkin/s/detail",
			"style": {
				"navigationBarTitleText": "详情"
			}
		},
		{
			"path": "pages/checkin/m/fbqdxm",
			"style": {
				"navigationBarTitleText": "发布签到项目"
			}
		},
		{
			"path": "pages/checkin/s/detail",
			"style": {
				"navigationBarTitleText": "发布签到项目"
			}
		},
		{
			"path": "pages/loading/loading"
		},
		{
			"path": "pages/index-fdy/index",
			"style": {
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index-xlzxs/index",
			"style": {
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/about/about",
			"style": {
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/comments/comments",
			"style": {
				"enablePullDownRefresh": true,
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/detail/detail",
			"style": {
				"enablePullDownRefresh": false,
				"backgroundTextStyle": "dark",

				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/hot/hot",
			"style": {
				"navigationBarTitleText": "文章排行"
			}
		},
		{
			"path": "pages/logs/logs",
			"style": {
				"navigationBarTitleText": "查看启动日志"
			}
		},
		{
			"path": "pages/list/list",
			"style": {
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/page/page",
			"style": {}
		},
		{
			"path": "pages/pay/pay",
			"style": {}
		},
		{
			"path": "pages/poster/poster",
			"style": {}
		},
		{
			"path": "pages/readlog/readlog",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/search/search",
			"style": {
				"enablePullDownRefresh": true,
				"backgroundTextStyle": "dark"
			}
		},
		{
			"path": "pages/topic/topic",
			"style": {}
		},
		{
			"path": "pages/theme/theme",
			"style": {
				"navigationStyle": "default"
			}
		},
		{
			"path": "pages/webpage/webpage",
			"style": {}
		},
		{
			"path": "pages/tags/tags",
			"style": {}
		},
		{
			"path": "pages/my/my",
			"style": {}
		},
		{
			"path": "pages/my-vant/my",
			"style": {}
		},
		{
			"path": "pages/service/service",
			"style": {}
		},
		{
			"path": "pages/service1/service1",
			"style": {}
		},
		{
			"path": "pages/service-static/service",
			"style": {}
		},
		{
			"path": "pages/ucharts-demo/ucharts",
			"style": {}
		},
		{
			"path": "pages/demo/list-page",
			"style": {
				"navigationBarTitleText": "demo页",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/person/list",
			"style": {
				"navigationBarTitleText": "学生查询",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/person/list1",
			"style": {
				"navigationBarTitleText": "学生查询",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/demo/add-page",
			"style": {
				"navigationBarTitleText": "add demo页"
			}
		},
		{
			"path": "pages/demo/detail-page",
			"style": {
				"navigationBarTitleText": "详情"
				// "enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/demo/qd-page",
			"style": {
				"navigationBarTitleText": "签到"
				// "enablePullDownRefresh": true
			}
		}
		
	],
	"easycom": {
	  "ui-(.*)": "@/ui/components/ui-$1/ui-$1.vue",
	  "ucs-icon-(.*)": "@/uni_modules/ucs-iconPark/components/ucs-icon-$1/ucs-icon-$1.vue",
	  "app-(.*)": "@/app/components/app-$1/app-$1.vue",
	  "mp-html": "@/components/mp-html/mp-html"
	  // "^van-(.*)": "vant/lib/$1/index"
	},
	// "tabBar": {
	// 	"color": "#959595",
	// 	"selectedColor": "#118fff",
	// 	"borderStyle": "white",
	// 	"backgroundColor": "#ffffff",
	// 	"list": [
	// 		{
	// 			"pagePath": "pages/index/index",
	// 			"text": " "
	// 		},
	// 		{
	// 			"pagePath": "pages/topic/topic",
	// 			"text": " "
	// 		},
	// 		{
	// 			"pagePath": "pages/hot/hot",
	// 			"text": " "
	// 		},
	// 		{
	// 			"pagePath": "pages/my/my",
	// 			"text": " "
	// 		}
	// 	]
	// },
	"requiredBackgroundModes": [
		"audio",
		"location"
	],
	"networkTimeout": {
		"request": 10000,
		"connectSocket": 10000,
		"uploadFile": 10000,
		"downloadFile": 10000
	},
	"sitemapLocation": "sitemap.json",
	"resizable": false,
	"darkmode": true,
	"globalStyle": {
		"backgroundTextStyle": "light",
		"navigationBarBackgroundColor": "#fff",
		"navigationBarTextStyle": "black",
		"pageOrientation": "portrait",
		"navigationStyle": "custom"
	},
	"subPackages": []
}