{"id": "lime-circle", "displayName": "lime-circle 进度环-圆形进度条", "version": "0.2.8", "description": "lime-circle 进度环 提供css和canvas渲染的圆形进度环, 弧形进度条, 环形进度条, 有渐变色, 仪表盘等配置。兼容uniapp/uniappx", "keywords": ["进度环", "弧形进度条", "圆形进度条", "渐变进度条", "仪表盘进度条"], "repository": "", "engines": {"HBuilderX": "^3.6.4"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["lime-shared"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-uvue": "y", "app-nvue": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}