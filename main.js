import App from './App'
import ui from './ui'
import { install } from '@icon-park/vue/es/all';
import '@icon-park/vue/styles/index.css'; // 引入图标样式

import Vant from 'vant';
import { Uploader } from 'vant';

Vue.use(Vant);
Vue.use(Uploader);

// import 'vant-green/lib/index.css';
// Api函数polyfill（目前为实验版本，如不需要，可删除！）';
// import Polyfill from './polyfill/polyfill';
// Polyfill.init();

// 动态加载并执行立即执行脚本
import('./security.js').then((result) => {
  console.log("Init script result:", result.default);
});
// 全局mixins，用于实现setData等功能，请勿删除！';
import Mixin from './polyfill/mixins';
Vue.mixin(Mixin);

import SignCanvas from "sign-canvas";
Vue.use(SignCanvas);

// #ifndef VUE3
import Vue from 'vue';
import HoverBall from '@/components/hover-ball.vue' //引入组件
import IconPark from '@/components/Icon-Park.vue' //引入iconpark组件

install(Vue); // 使用默认前缀 'icon'
let u = navigator.userAgent;

let browser = {
    versions: function () {
        // app = navigator.appVersion;
        return {
            mobile: !!u.match(/AppleWebKit.*Mobile.*/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf('Android') > -1 || u.indexOf('Adr') > -1, //android终端
            iPhone: u.indexOf('iPhone') > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf('iPad') > -1, //是否iPad
            isWeChat: u.includes('MicroMessenger'), //是否为微信
            isYiban: u.includes('yiban'),  //是否为易班
            isDingTalk: u.includes('DingTalk')  //是否为钉钉
        };
    }(),
    language: (navigator.browserLanguage || navigator.language).toLowerCase()
};
const webViewType = {WeChat: 'WeChat', Yiban: 'Yiban', DingTalk: 'DingTalk'};
let currentWebView = "";
if (browser.versions.isWeChat) {
    currentWebView = webViewType.WeChat;
} else if (browser.versions.isYiban) {
    currentWebView = webViewType.Yiban;
} else if (browser.versions.isDingTalk) {
    currentWebView = webViewType.DingTalk;
}

let chromeVersion = null;
if (u.includes("Chrome/")) {
    chromeVersion = Number(u.substr(u.indexOf("Chrome/") + 7, 2));
}
// alert('currentWebView:' + currentWebView)
console.log('main', currentWebView)

Vue.config.productionTip = false;
App.mpType = 'app';
const app = new Vue({
	...App,
	data: {
		isIos: browser.versions.ios,
		isAndroid: browser.versions.android,
		chromeVersion: chromeVersion,
		isWeChat: browser.versions.isWeChat,
		isYiban: browser.versions.isYiban,
		isDingTalk: browser.versions.isDingTalk
	},
	created() {
		// console.log('main created')
		// if (store.state.isProduction) {
		// 	if (this.isDingTalk) {
		// 		const appId = sessionStorage.getItem("appId");
		// 		dingtalkInit(appId);
		// 	}
		// }
	}
});
console.log('app', app)
Vue.component("HoverBall", HoverBall)
Vue.component("IconPark", IconPark)
app.$mount();
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue';
export function createApp() {
	const app = createSSRApp(App);
	app.mixin(Mixin);
	return {
		app,
	};
}
// #endif