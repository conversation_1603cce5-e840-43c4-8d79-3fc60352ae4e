export default {
	state: {
		userInfo: null, //用户信息
		roleKey: null,
		roleName: null,
		realName: null,
		userAccount: null, // 用户账号
		// userId: null,  
		token: null,
		isTheme: true, // 是否开启多主题（true:会读缓存，能设置对应的主题)
		themeAuto: false, // 跟随系统
		theme: 'light', // 设置默认主题
		main: 'blue', // 设置默认强调色
		text: 1, // 设置默认字号等级(0-4)
		domain: ' ',
		apiPath: '/api/',
		homePath: '/pages/index/index', // 设置首页路径(一些组件会用到跳回主页，请每个项目设置好！)
		tabbar: [{
		            title: '首页',
		            icon: 'cicon-home-line',
		            curIcon: 'cicon-home-line',
		            url: '/pages/index/index',
		            type: 'tab'
		        },
		        {
		            title: '分类',
		            icon: 'cicon-discover-o',
		            curIcon: 'cicon-discover',
		            url: '/pages/topic/topic',
		            type: 'tab'
		        },
		        {
		            title: '排行',
		            icon: 'cicon-upstage-o',
		            curIcon: 'cicon-upstage',
		            url: '/pages/hot/hot',
		            type: 'tab'
		        }, 
		        {
		            title: '我的',
		            icon: 'cicon-my-o',
		            curIcon: 'cicon-my',
		            url: '/pages/my/my',
		            type: 'tab'
		        }],
		nav: [],
		menuList: [
			{
				// iconPath: require('@/static/home-icon/home-gray.svg'),
				// selectedIconPath: require('@/static/home-icon/home-active.svg'),
				icon: 'home',
				text: '首页',
				active: false
			},
			{
				// iconPath: require('@/static/home-icon/news-gray.svg'),
				// selectedIconPath: require('@/static/home-icon/news-active.svg'),
				icon: 'message-one',
				text: '消息',
				active: false
			},
			{
				// iconPath: require('@/static/home-icon/service-gray.svg'),
				// selectedIconPath: require('@/static/home-icon/service-active.svg'),
				icon: 'all-application',
				text: '服务',
				active: false
			},
			{
				// iconPath: require('@/static/home-icon/mine-gray.svg'),
				// selectedIconPath: require('@/static/home-icon/mine-active.svg'),
				icon: 'user',
				text: '我的',
				active: false
			}
		]
	},
	getters: {
		getRoleName: state => {
			return state.roleName
		},
		getRoleKey: state => {
			return state.roleKey
		},
		getUserAccount: state => {
			return state.userAccount
		},
		getUserInfo: state => {
			return state.userInfo
		},
		getToken: state => {
			return state.token
		},
		getCustomTheme: state => {
			return state.theme
		},
		getCustomMain: state => {
			return state.main
		},
		getCustomText: state => {
			return state.text
		},
		getTabbar: state => {
			return state.tabbar
		},
		getMenuList: state => {
			return state.menuList
		},
		getNav: state => {
			return state.nav
		},
		getDomain: state => {
			return state.domain
		},
		getApiPath: state => {
			return state.apiPath
		},
	},
	mutations: {
		changeMenuActive(state, activeIndex) {
			console.log('changeMenuActive', activeIndex)
			state.menuList.forEach((item, index) => {
				item.active = activeIndex == index ? true : false
			});
			console.log('state.menuList', state.menuList)
		},
		setUser(state, data) {
			console.log('setUser', data);
			if(data) {
				// state.userInfo = data;
				// state.userId = data.userId;
				if(data.realName) {
					state.realName = data.realName;
				}
				if(data.userAccount) {
					state.userAccount = data.userAccount;
				}
				state.roleKey = data.roleKey;
				state.roleName = data.roleName;
			} else {
				// state.userInfo = null;
				// state.userId = null;
				state.userAccount = null;
				state.realName = null;
				state.roleKey = null;
				state.roleName = null;
			}
			console.log('setUser 后', state.roleKey, state.roleName, state.userAccount);
		},
		setToken(state, data) {
			console.log('setToken', data);
			if(data) {
				state.token = data;
			}  else {
				state.token = null;
			}
			console.log('setToken 后', state.token);
		}
	},
	actions: {}
}
