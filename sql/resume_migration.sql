-- 简历管理模块数据迁移脚本
-- 创建时间：2025-01-22
-- 说明：将枚举类型迁移为数据库表管理

-- ========================================
-- 第一步：创建模板类型表（如果不存在）
-- ========================================

-- 检查表是否存在，如果不存在则创建
DECLARE
    table_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO table_count 
    FROM user_tables 
    WHERE table_name = 'SYT_RESUME_TEMPLATE_TYPE';
    
    IF table_count = 0 THEN
        EXECUTE IMMEDIATE '
        CREATE TABLE SYT_RESUME_TEMPLATE_TYPE (
            ID VARCHAR2(32 CHAR) PRIMARY KEY,
            TYPE_CODE VARCHAR2(50 CHAR) UNIQUE NOT NULL,
            TYPE_NAME VARCHAR2(100 CHAR) NOT NULL,
            DESCRIPTION VARCHAR2(512 CHAR),
            ICON VARCHAR2(100 CHAR),
            IS_ACTIVE NUMBER(1) DEFAULT 1,
            SORT_ORDER NUMBER(10) DEFAULT 0,
            IS_SYSTEM NUMBER(1) DEFAULT 0,
            CREATE_TIME TIMESTAMP,
            UPDATE_TIME TIMESTAMP,
            CREATE_BY VARCHAR2(32 CHAR),
            UPDATE_BY VARCHAR2(32 CHAR)
        )';
        
        -- 添加注释
        EXECUTE IMMEDIATE 'COMMENT ON TABLE SYT_RESUME_TEMPLATE_TYPE IS ''简历模板类型表''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYT_RESUME_TEMPLATE_TYPE.ID IS ''主键''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYT_RESUME_TEMPLATE_TYPE.TYPE_CODE IS ''类型代码（唯一标识）''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYT_RESUME_TEMPLATE_TYPE.TYPE_NAME IS ''类型名称''';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYT_RESUME_TEMPLATE_TYPE.IS_SYSTEM IS ''是否系统内置（1：是，0：否）''';
        
        -- 创建索引
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_TEMPLATE_TYPE_CODE ON SYT_RESUME_TEMPLATE_TYPE(TYPE_CODE)';
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_TEMPLATE_TYPE_ACTIVE ON SYT_RESUME_TEMPLATE_TYPE(IS_ACTIVE)';
        
        DBMS_OUTPUT.PUT_LINE('模板类型表创建成功');
    ELSE
        DBMS_OUTPUT.PUT_LINE('模板类型表已存在，跳过创建');
    END IF;
END;
/

-- ========================================
-- 第二步：插入默认模板类型数据
-- ========================================

-- 插入系统内置的模板类型
INSERT INTO SYT_RESUME_TEMPLATE_TYPE (ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY) 
SELECT 'TYPE_TECHNICAL_001', 'TECHNICAL', '技术类', '适用于技术岗位的简历模板', 1, 1, 1, SYSDATE, SYSDATE, 'SYSTEM', 'SYSTEM' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'TECHNICAL');

INSERT INTO SYT_RESUME_TEMPLATE_TYPE (ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY) 
SELECT 'TYPE_MANAGEMENT_001', 'MANAGEMENT', '管理类', '适用于管理岗位的简历模板', 1, 2, 1, SYSDATE, SYSDATE, 'SYSTEM', 'SYSTEM' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'MANAGEMENT');

INSERT INTO SYT_RESUME_TEMPLATE_TYPE (ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY) 
SELECT 'TYPE_SALES_001', 'SALES', '销售类', '适用于销售岗位的简历模板', 1, 3, 1, SYSDATE, SYSDATE, 'SYSTEM', 'SYSTEM' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'SALES');

INSERT INTO SYT_RESUME_TEMPLATE_TYPE (ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY) 
SELECT 'TYPE_DESIGN_001', 'DESIGN', '设计类', '适用于设计岗位的简历模板', 1, 4, 1, SYSDATE, SYSDATE, 'SYSTEM', 'SYSTEM' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'DESIGN');

INSERT INTO SYT_RESUME_TEMPLATE_TYPE (ID, TYPE_CODE, TYPE_NAME, DESCRIPTION, IS_ACTIVE, SORT_ORDER, IS_SYSTEM, CREATE_TIME, UPDATE_TIME, CREATE_BY, UPDATE_BY) 
SELECT 'TYPE_GENERAL_001', 'GENERAL', '通用类', '适用于大多数求职场景的通用简历模板', 1, 5, 1, SYSDATE, SYSDATE, 'SYSTEM', 'SYSTEM' FROM DUAL
WHERE NOT EXISTS (SELECT 1 FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'GENERAL');

-- ========================================
-- 第三步：检查并添加新字段
-- ========================================

-- 检查 SYT_RESUME_TEMPLATE 表是否有 TEMPLATE_TYPE_ID 字段
DECLARE
    column_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO column_count 
    FROM user_tab_columns 
    WHERE table_name = 'SYT_RESUME_TEMPLATE' AND column_name = 'TEMPLATE_TYPE_ID';
    
    IF column_count = 0 THEN
        -- 添加新字段
        EXECUTE IMMEDIATE 'ALTER TABLE SYT_RESUME_TEMPLATE ADD TEMPLATE_TYPE_ID VARCHAR2(32 CHAR)';
        EXECUTE IMMEDIATE 'COMMENT ON COLUMN SYT_RESUME_TEMPLATE.TEMPLATE_TYPE_ID IS ''模板类型ID''';
        
        -- 创建索引
        EXECUTE IMMEDIATE 'CREATE INDEX IDX_RESUME_TEMPLATE_TYPE_ID ON SYT_RESUME_TEMPLATE(TEMPLATE_TYPE_ID)';
        
        DBMS_OUTPUT.PUT_LINE('TEMPLATE_TYPE_ID 字段添加成功');
    ELSE
        DBMS_OUTPUT.PUT_LINE('TEMPLATE_TYPE_ID 字段已存在，跳过添加');
    END IF;
END;
/

-- ========================================
-- 第四步：数据迁移（如果存在旧的 TEMPLATE_TYPE 字段）
-- ========================================

-- 迁移现有数据：将 TEMPLATE_TYPE 枚举值转换为 TEMPLATE_TYPE_ID
DECLARE
    old_column_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO old_column_count 
    FROM user_tab_columns 
    WHERE table_name = 'SYT_RESUME_TEMPLATE' AND column_name = 'TEMPLATE_TYPE';
    
    IF old_column_count > 0 THEN
        -- 更新现有记录
        UPDATE SYT_RESUME_TEMPLATE SET TEMPLATE_TYPE_ID = (
            SELECT ID FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = SYT_RESUME_TEMPLATE.TEMPLATE_TYPE
        ) WHERE TEMPLATE_TYPE IS NOT NULL AND TEMPLATE_TYPE_ID IS NULL;
        
        DBMS_OUTPUT.PUT_LINE('数据迁移完成，影响行数：' || SQL%ROWCOUNT);
        
        -- 可选：删除旧字段（谨慎操作，建议先备份）
        -- EXECUTE IMMEDIATE 'ALTER TABLE SYT_RESUME_TEMPLATE DROP COLUMN TEMPLATE_TYPE';
        -- DBMS_OUTPUT.PUT_LINE('旧字段 TEMPLATE_TYPE 已删除');
    ELSE
        DBMS_OUTPUT.PUT_LINE('未发现旧的 TEMPLATE_TYPE 字段，跳过数据迁移');
    END IF;
END;
/

-- ========================================
-- 第五步：添加外键约束（可选）
-- ========================================

-- 添加外键约束
DECLARE
    constraint_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO constraint_count 
    FROM user_constraints 
    WHERE table_name = 'SYT_RESUME_TEMPLATE' AND constraint_name = 'FK_TEMPLATE_TYPE';
    
    IF constraint_count = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE SYT_RESUME_TEMPLATE ADD CONSTRAINT FK_TEMPLATE_TYPE FOREIGN KEY (TEMPLATE_TYPE_ID) REFERENCES SYT_RESUME_TEMPLATE_TYPE(ID)';
        DBMS_OUTPUT.PUT_LINE('外键约束添加成功');
    ELSE
        DBMS_OUTPUT.PUT_LINE('外键约束已存在，跳过添加');
    END IF;
END;
/

-- ========================================
-- 第六步：更新默认模板的类型关联
-- ========================================

-- 更新默认模板的类型关联
UPDATE SYT_RESUME_TEMPLATE 
SET TEMPLATE_TYPE_ID = (SELECT ID FROM SYT_RESUME_TEMPLATE_TYPE WHERE TYPE_CODE = 'GENERAL')
WHERE ID = 'DEFAULT_TEMPLATE_001' AND TEMPLATE_TYPE_ID IS NULL;

COMMIT;

-- 输出迁移完成信息
SELECT 'Resume template type migration completed successfully!' AS MIGRATION_STATUS FROM DUAL;
