const ak = 'b1af91a4ef715141f6e89e3e55afa6f0'; // 高德的地图key
const securityJsCode = '8b419c91198bdf079b2ebb28ca6dbc8c'
/**
 * 异步加载高德地图 1.4.15版本
 * @returns {Promise}
 * @constructor
 */
function MapLoader1() {
    return new Promise((resolve, reject) => {
        if (document.getElementById('amap_plus_js')) {
            document.head.removeChild(document.getElementById('amap_plus_js'));
        }
        if (document.getElementById('gaodeMapIdScript')) {
            document.head.removeChild(document.getElementById('gaodeMapIdScript'));
            createScript();
        } else {
            createScript();
        }
        function createScript() {
            var script = document.createElement('script')
            script.type = 'text/javascript'
            script.async = true
            script.id = "gaodeMapIdScript"
            script.src = 'https://webapi.amap.com/maps?v=1.4.15&callback=initAMap&key=' + ak + '&plugin=AMap.Autocomplete,AMap.PlaceSearch,AMap.Geocoder,AMap.Scale,AMap.HawkEye,AMap.ToolBar,AMap.ControlBar,AMap.Driving,AMap.MoveAnimation,AMap.GraspRoad,AMap.MarkerClusterer'
            script.onerror = reject
            document.head.appendChild(script)
        }
        window.initAMap = () => {
            resolve(window.AMap)
        }
    })
}
export { MapLoader1 }



/**
 * 异步加载高德地图 2.0版本
 * @returns {Promise}
 * @constructor
 */
function MapLoader2() {
	window._AMapSecurityConfig = {
	    securityJsCode: securityJsCode,
	}
    return new Promise((resolve, reject) => {
        if (document.getElementById('amap_plus_js')) {
            document.head.removeChild(document.getElementById('amap_plus_js'));
        }
        if (document.getElementById('gaodeMapIdScript')) {
            document.head.removeChild(document.getElementById('gaodeMapIdScript'));
            createScript();
        } else {
            createScript();
        }
        function createScript() {
            var script = document.createElement('script')
            script.type = 'text/javascript'
            script.async = true
            script.id = "gaodeMapIdScript"
            script.src = 'https://webapi.amap.com/maps?v=2.0&callback=initAMap&key=' + ak + '&plugin=AMap.Scale,AMap.PolygonEditor'
            script.onerror = reject
            document.head.appendChild(script)
        }
        window.initAMap = () => {
            resolve(window.AMap)
        }
    })
}
export { MapLoader2 }
