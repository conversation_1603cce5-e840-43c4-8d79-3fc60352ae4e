import config from "../config.js";
var domain = config.getDomain;
var pageCount = config.getPageCount;
var categoriesID = config.getCategoriesID;
// var HOST_URI = 'https://' + domain + '/wp-json/wp/v2/';
var HOST_URI_WATCH_LIFE_JSON = 'http://' + domain + '/wp-json/watch-life-net/v1/';
// var HOST_URI = 'http://' + domain + '/api/';
module.exports = {
	/**
	 * 获取学生列表分页
	 * page=1&limit=10
	 * /api/personInfo/{userType}/queryPage
	*/
	personQueryPage: function () {
	  return  '/api/personInfo/';
	}
}