function testAlert(str) {
    // alert(str);
}

/**
 * 函数名称：download
 * 函数作用：下载
 * 参数说明：地址
 * @param vurl
 */
export function download_fun(vurl) {
    let defaultFun = function () {
        window.open(vurl);
        // window.open(vurl, '_parent', 'download');
    }
    try {
        if (window.hasOwnProperty('download')) {
            testAlert('download_fun window.download');
            window.download(vurl);
        } else if (window.hasOwnProperty('local_obj') && window.local_obj.hasOwnProperty('download')) {
            testAlert('download_fun window.local_obj.download');
            window.local_obj.download(vurl);
        } else {
            testAlert('download_fun default')
            defaultFun();
        }
    } catch (e) {
        alert("系统或设备不支持")
    }
}

let commonCallback;
/**
 * (旧)
 * 函数作用：扫一扫结果返回
 * 参数说明：二维码中必须包含“yiban_scan_result”标识否则跳转新的页面
 * @param info
 */
window.getScanResult = function (info) {
    if (commonCallback) {
        commonCallback(info);
    }
};

/**
 * 扫描二维码
 * @param callBack
 */
export function scan_fun(callBack) {
    if (window.hasOwnProperty('yiban') && window.yiban.hasOwnProperty('scan')) {
        testAlert('scan_fun window.yiban.scan');
        if (callBack) {
            window.yiban.scan({
                success: function (result) {
                    callBack(result);
                }, fail: function (error) {
                    alert(error);
                }
            });
        } else {
            window.yiban.scan();
        }
    } else if (window.hasOwnProperty('encode')) {
        testAlert('scan_fun window.encode');
        commonCallback = callBack;
        window.encode();
    } else if (window.hasOwnProperty('local_obj') && window.local_obj.hasOwnProperty('encode')) {
        testAlert('scan_fun window.local_obj.encode');
        commonCallback = callBack;
        window.local_obj.encode();
    } else {
        alert("系统或设备不支持")
    }
}

/**
 * (旧)
 * 函数名称：yibanhtml5location
 * 函数作用：客户端获取地理位置，异步返回位置信息，html根据返回信息做界面内容处理
 * 参数说明：易班app回调设定，无需用户调用
 * @param position Json  {"longitude":"经度坐标", "latitude":"纬度坐标", "address":"位置名称"}
 */
window.yibanhtml5location = function (position) {
    testAlert("yibanhtml5location" + position + " commonCallback:" + !!commonCallback);
    if (!commonCallback) {
        return
    }

    let positionObj = JSON.parse(position);
    if (positionObj.address) {
        commonCallback(position);
        return;
    }

    if (!(positionObj.longitude && positionObj.latitude
        && window.hasOwnProperty('AMap') && window.AMap.hasOwnProperty('LngLat') && window.AMap.hasOwnProperty('plugin'))) {
        commonCallback(position);
        return;
    }

    // 使用高德地图API从经纬度获取地理信息
    let lngLat = new window.AMap.LngLat(positionObj.longitude, positionObj.latitude);
    window.AMap.plugin('AMap.Geocoder', function () {
        let geocoder = new window.AMap.Geocoder();
        geocoder.getAddress(lngLat, function (status, result) {
            if (status === 'complete' && result.info === 'OK') {
                positionObj.address = result.regeocode.formattedAddress;
            }
            commonCallback(JSON.stringify(positionObj));
        })
    });
}

/**
 * 获取当前用户的位置定位信息。（需用户设备开启定位服务且许可使用权限）
 * 基于高德地图坐标系GCJ-02。
 * @param callback
 */
export function location_fun(callback) {
    if (!callback) {
        return;
    }
    commonCallback = callback;

    if (window.hasOwnProperty('yiban') && window.yiban.hasOwnProperty('getLocation')) {
        testAlert('location_fun window.yiban.getLocation');
        window.yiban.getLocation({
            success: function (result) {
                callback(result);
            }, fail: function (error) {
                alert(error);
            }
        });
    } else if (window.hasOwnProperty('ios_yibanhtml5location')) {
        testAlert('location_fun window.ios_yibanhtml5location');
        window.ios_yibanhtml5location();
    } else if (window.hasOwnProperty('local_obj') && window.local_obj.hasOwnProperty('yibanhtml5location')) {
        testAlert('location_fun window.local_obj.yibanhtml5location');
        window.local_obj.yibanhtml5location();
    } else {
        alert("系统或设备不支持")
    }
}
