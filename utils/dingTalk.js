import * as dd from 'dingtalk-jsapi';
var wxRequest = require('@/utils/wxRequest.js');

function init(appId, url) {
    if (!appId) {
        return false;
    }
    wxRequest.postRequest("syt/dingtalk/api/getJSConfig.htm", {appId, url: url ? url : null, rType: 'mobile'}).then(r => {
        let rData = r.data;
        if (rData.status) {
            let data1 = rData.data;
            console.log("data1", data1);
            dd.error(function (error) {
                /**
                 {
                   errorMessage:"错误信息",// errorMessage 信息会展示出钉钉服务端生成签名使用的参数，请和您生成签名的参数作对比，找出错误的参数
                   errorCode: "错误码"
                }
                 **/
                console.error(error)
                // alert('dd error: ' + JSON.stringify(error));
            });
            dd.config({
                agentId: data1.agentId, // 必填，微应用ID
                corpId: data1.corpId,//必填，企业ID
                timeStamp: data1.timeStamp, // 必填，生成签名的时间戳
                nonceStr: data1.nonceStr, // 必填，生成签名的随机串
                signature: data1.signature, // 必填，签名
                // type:0/1,   //选填。0表示微应用的jsapi,1表示服务窗的jsapi；不填默认为0。该参数从dingtalk.js的0.8.3版本开始支持
                jsApiList: [
                    'runtime.info',
                    // 'biz.contact.choose',
                    // 'device.notification.confirm',
                    'device.notification.alert',
                    // 'device.notification.prompt',
                    // 'biz.ding.post',
                    // 'biz.util.openLink',
                    'device.geolocation.get',
                ] // 必填，需要使用的jsapi列表，注意：不要带dd。
            });
        }
    })
}

function getLocation(callBack) {
    dd.ready(function () {
        dd.device.geolocation.get({
            targetAccuracy: 200,
            coordinate: 1,
            withReGeocode: false,
            // useCache: true, //默认是true，如果需要频繁获取地理位置，请设置false
            onSuccess: function (result) {
                /* 高德坐标 result 结构
                {
                    longitude : Number,
                    latitude : Number,
                    accuracy : Number,
                    address : String,
                    province : String,
                    city : String,
                    district : String,
                    road : String,
                    netType : String,
                    operatorType : String,
                    errorMessage : String,
                    errorCode : Number,
                    isWifiEnabled : Boolean,
                    isGpsEnabled : Boolean,
                    isFromMock : Boolean,
                    provider : wifi|lbs|gps,
                    isMobileEnabled : Boolean
                }
                */
                if (callBack) {
                    let o = {
                        longitude: result.longitude,
                        latitude: result.latitude,
                        address: result.address
                    }
                    if (!o.address) {
                        let lngLat = new window.AMap.LngLat(o.longitude, o.latitude);
                        window.AMap.plugin('AMap.Geocoder', function () {
                            let geocoder = new window.AMap.Geocoder();
                            geocoder.getAddress(lngLat, function (status, result) {
                                if (status === 'complete' && result.info === 'OK') {
                                    o.address = result.regeocode.formattedAddress;
                                }
                                callBack(JSON.stringify(o));
                            })
                        });
                    } else {
                        callBack(JSON.stringify(o));
                    }
                }
            },
            onFail: function (err) {
                console.log(err)
            }
        });
    })

}

function scanQRCode(func) {
    dd.biz.util.scan({
        type: 'qrCode', // type 为 all、qrCode、barCode，默认是all。
        onSuccess: function (data) {
            //onSuccess将在扫码成功之后回调
            /* data结构
              { 'text': String}
            */
            // console.log(data.text)
            if (func) {
                func(data.text);
            }
        },
        onFail: function (err) {
            console.log(err)
        }
    })
}

export {init, getLocation, scanQRCode}
