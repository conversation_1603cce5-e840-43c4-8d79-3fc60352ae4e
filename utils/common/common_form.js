export function fieldCheck(field, value, hasFile) {
    const fieldType = field.type;
	 console.log('校验 field, value', fieldType, value, hasFile);
	  // && Array.isArray(value)
    if (value) {
		console.log('有值', value, field)
        if (['inputNumber'].includes(fieldType)) {
            const vNum = Number(value);
			console.log('是数字', value, isNaN(vNum))
            if (isNaN(vNum)) {
                return '必须是数字';
            }
            // if (value.includes('.') && fieldType === 'integer') {
            //     return '必须是整数';
            // }
            // if (!value.includes('.') && fieldType === 'float') {
            //     return '必须是小数';
            // }
            if (field.el === 'yhkh') {
                var bankno = value;
                // if (bankno == "") return '请输入银行卡号';
                if (bankno.length < 16 || bankno.length > 19) {
                    return '请输入正确的银行卡号,银行卡长度不正确';
                }
                //开头6位
                var strBin = "10,18,30,35,37,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,58,60,62,65,68,69,84,87,88,94,95,98,99";
                if (strBin.indexOf(bankno.substring(0, 2)) == -1) {
                    return '请输入正确的银行卡号';
                }
                //Luhn校验
                if (!luhnCheck(bankno)) {
                    return '请输入正确的银行卡号';
                }
            }
        } else if (['timerange'].includes(fieldType)) {
			console.log('是时间范围', value, value.split(','));
			let timeList = value.split(',');
            if (timeList[0] == "" || timeList[1] == "") {
                return '必填';
            }
        } else if (["imageUpload", "videoUpload", "fileUpload","smoothSignature"].includes(fieldType)) {
			console.log('无文件', value, field)
			if (field.required != null ? field.required : field.defaultRequired) {
			    console.info('必填', field.prop);
			    // if (fieldType === 'file' || fieldType === 'smoothSignature') {
			        if (!hasFile) {
			            return '必填';
			        }
			    // } else {
			    //     return '必填';
			    // }
			}
		} else if ("string_s" === fieldType || "string_l" === fieldType) {
            if (field.el === 'sfzh') {//请输入正确的身份证号格式
                var v = value;
                console.log(v)
                // //Luhn校验
                // if (!IdentityCodeValid(sfzh)) {
                //     return '请输入正确的身份证号';
                // }
                if (!IdentityCodeValid(v) && !HMCardValid(v) && !HongkongIdCardValid(v) && !MacaoIdCardValid(v) && !TWCardValid(v)) {
                    return "请输入正确的证件号码(身份证/港澳内地通行证/港澳居民身份证/台湾内地通行证)"
                }
            } else if (field.el === 'yhkh') {
                let bankno = value;
                // if (bankno == "") return '请输入银行卡号';
                if (bankno.length < 16 || bankno.length > 19) {
                    return '请输入正确的银行卡号,银行卡长度不正确';
                }
                //开头6位
                let strBin = "10,18,30,35,37,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,58,60,62,65,68,69,84,87,88,94,95,98,99";
                if (strBin.indexOf(bankno.substring(0, 2)) == -1) {
                    return '请输入正确的银行卡号';
                }
                //Luhn校验
                if (!luhnCheck(bankno)) {
                    return '请输入正确的银行卡号';
                }
            } else if (field.el === 'yzbm') {//请输入正确的邮编格式
                let pattern = new RegExp("^[0-9]{6}$");
                if (!pattern.test(value)) {
                    return '请输入正确的邮编格式';
                }
            } else if (field.el === 'phone') {//请输入正确的手机号码格式
                let pattern = new RegExp("^1[3|4|5|6|7|8|9][0-9]{9}$");
                if (!pattern.test(value)) {
                    return '请输入正确的手机号码格式';
                }
            } else if (field.el === 'num') {//必须是数字
                let pattern = new RegExp("^[0-9]*$");
                if (!pattern.test(value)) {
                    return '必须是数字';
                }
            } else if (field.el === 'containnum') {//必须包含数字
                let pattern = new RegExp(".*\\d+.*");
                if (!pattern.test(value)) {
                    return '必须包含数字';
                }
            } else if (field.el === 'qq') {//请输入正确的qq格式
                let pattern = new RegExp("[1-9][0-9]{4,}");
                if (!pattern.test(value)) {
                    return '请输入正确的qq格式';
                }
            } else if (field.el === 'qqEmail') {//请输入正确的qq邮箱
                let pattern = new RegExp("[1-9]\\d{7,10}@qq\\.com");
                if (!pattern.test(value)) {
                    return '请输入正确的qq邮箱';
                }
            } else if (field.el === 'email') {//请输入正确的邮箱
                let pattern = new RegExp("[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");
                if (!pattern.test(value)) {
                    return '请输入正确的邮箱';
                }
            } else if (field.el === 'english') {//请输入英文
                let pattern = new RegExp("^[a-zA-Z\\_]+$");
                if (!pattern.test(value)) {
                    return '请输入英文';
                }
            } else if (field.el === 'chinese') {//请输入汉字
                let pattern = new RegExp("^[u4e00-u9fa5],{0,}$");
                if (!pattern.test(value)) {
                    return '请输入汉字';
                }
            } else if (field.el && field.el !== 'minLength:2') {
                let pattern = new RegExp(field.el);
                if (!pattern.test(value)) {
                    return '不满足正则';
                }
            }
        }
        if (field.minLength && value.length < field.minLength) {
            return '字符长度必须大于' + field.minLength;
        }
        if (field.maxLength && value.length > field.maxLength) {
            return '字符长度必须小于' + field.maxLength;
        }
    } else {
		console.log('无值', value, field)
        if (field.required != null ? field.required : field.defaultRequired) {
            console.info('必填', field.prop);
            if (["imageUpload", "videoUpload", "fileUpload","smoothSignature"].includes(fieldType)) {
                if (!hasFile) {
                    return '必填';
                }
            } else {
                return '必填';
            }
        }
    }
    return null;
}

//验证身份证号
function IdentityCodeValid(code) {
    var city = {
        11: "北京",
        12: "天津",
        13: "河北",
        14: "山西",
        15: "内蒙古",
        21: "辽宁",
        22: "吉林",
        23: "黑龙江 ",
        31: "上海",
        32: "江苏",
        33: "浙江",
        34: "安徽",
        35: "福建",
        36: "江西",
        37: "山东",
        41: "河南",
        42: "湖北 ",
        43: "湖南",
        44: "广东",
        45: "广西",
        46: "海南",
        50: "重庆",
        51: "四川",
        52: "贵州",
        53: "云南",
        54: "西藏 ",
        61: "陕西",
        62: "甘肃",
        63: "青海",
        64: "宁夏",
        65: "新疆",
        71: "台湾",
        81: "香港",
        82: "澳门",
        91: "国外 "
    };
    var tip = "";
    var pass = true;
    //验证身份证格式（6个地区编码，8位出生日期，3位顺序号，1位校验位）
    if (!code || !/^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(code)) {
        tip = "身份证号格式错误";
        pass = false;
    } else if (!city[code.substr(0, 2)]) {
        tip = "地址编码错误";
        pass = false;
    } else {
        //18位身份证需要验证最后一位校验位
        if (code.length == 18) {
            code = code.split('');
            //∑(ai×Wi)(mod 11)
            //加权因子
            var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
            //校验位
            var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
            var sum = 0;
            var ai = 0;
            var wi = 0;
            for (var i = 0; i < 17; i++) {
                ai = code[i];
                wi = factor[i];
                sum += ai * wi;
            }
            var last = parity[sum % 11];
            if (parity[sum % 11] != code[17]) {
                tip = "校验位错误";
                pass = false;
            }
        }
    }
    return pass;
}

// 港澳居民来往内地通行证
// 规则：通行证证件号码共11位。第1位为字母，“H”字头签发给香港居民，“M”字头签发给澳门居民；
// 第2位至第11位为数字，前8位数字为通行证持有人的终身号，后2位数字表示换证次数，首次发证为00，此后依次递增。
// H/M + 8位或10位数字
// 样本： H12345678 或 H1234567801
function HMCardValid(code) {
    var reg = /^[H|h|M|m](\d{8}|\d{10})$/;
    var tip = '', pass = true;
    if (!code) {
        tip = '港澳居民来往内地通行证号码不能为空';
        pass = false;
    } else if (!reg.test(code.trim())) {
        tip = '港澳居民来往内地通行证号码输入错误';
        pass = false;
    }

    //if(!pass) layer.open({content: tip, skin: 'msg',time:2});
    return pass;
}

// 香港
// 规则： XYabcdef(z)表示。「X」可能是「空格」或是一个英文字母，「Y」则必定是英文字母。「abcdef」代表一个六位数字，而「z」是作为检码之用，它的可能选择是0, 1, 2, ... , 9, A(代表10)
// 样本： C668668(a)    X354670(0)    b354670(a)    g354670a
// reg = /^((\s?[A-Za-z])|([A-Za-z]{2}))\d{6}\(?[\daA]\)?$/;
function HongkongIdCardValid(data) {
    var reg = /^((\s?[A-Za-z])|([A-Za-z]{2}))\d{6}\(?[\daA]\)?$/;
    var tip = '', pass = true;
    if (!data) {
        tip = '香港居民来身份证号码不能为空';
        pass = false;
    } else if (!reg.test(data.trim())) {
        tip = '香港居民身份证号码输入错误';
        pass = false;
    }

    //if(!pass) layer.open({content: tip, skin: 'msg',time:2});
    return pass;
}

// 澳门：
// 规则： 居民身份证号码由8个拉丁数字组成格式为XNNNNNN(Y)
// 样本： 5215299(8)
// reg = /^[1|5|7][0-9]{6}\(?[0-9Aa]\)?/;
function MacaoIdCardValid(data) {
    var reg = /^[1|5|7][0-9]{6}\(?[0-9Aa]\)?/;
    var tip = '', pass = true;
    if (!data) {
        tip = '澳门居民来身份证号码不能为空';
        pass = false;
    } else if (!reg.test(data.trim())) {
        tip = '澳门居民身份证号码输入错误';
        pass = false;
    }

    //if(!pass) layer.open({content: tip, skin: 'msg',time:2});
    return pass;
}

// 台湾居民来往大陆通行证
// 规则： 新版8位或18位数字， 旧版10位数字 + 英文字母
// 样本： 12345678 或 1234567890B  R122159007
function TWCardValid(data) {
    var reg = /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/;
    var tip = '', pass = true;
    if (!data) {
        tip = '台湾居民来往大陆通行证号码不能为空';
        pass = false;
    } else if (!reg.test(data.trim())) {
        tip = '台湾居民来往大陆通行证号码输入错误';
        pass = false;
    }

    //if(!pass) layer.open({content: tip, skin: 'msg',time:2});
    return pass;
}

//验证银行卡
export function luhnCheck(bankno) {
    var lastNum = bankno.substr(bankno.length - 1, 1);//取出最后一位（与luhn进行比较）

    var first15Num = bankno.substr(0, bankno.length - 1);//前15或18位
    var newArr = new Array();
    for (var i = first15Num.length - 1; i > -1; i--) {    //前15或18位倒序存进数组
        newArr.push(first15Num.substr(i, 1));
    }
    var arrJiShu = new Array();  //奇数位*2的积 <9
    var arrJiShu2 = new Array(); //奇数位*2的积 >9

    var arrOuShu = new Array();  //偶数位数组
    for (var j = 0; j < newArr.length; j++) {
        if ((j + 1) % 2 == 1) {//奇数位
            if (parseInt(newArr[j]) * 2 < 9)
                arrJiShu.push(parseInt(newArr[j]) * 2);
            else
                arrJiShu2.push(parseInt(newArr[j]) * 2);
        } else //偶数位
            arrOuShu.push(newArr[j]);
    }

    var jishu_child1 = new Array();//奇数位*2 >9 的分割之后的数组个位数
    var jishu_child2 = new Array();//奇数位*2 >9 的分割之后的数组十位数
    for (var h = 0; h < arrJiShu2.length; h++) {
        jishu_child1.push(parseInt(arrJiShu2[h]) % 10);
        jishu_child2.push(parseInt(arrJiShu2[h]) / 10);
    }

    var sumJiShu = 0; //奇数位*2 < 9 的数组之和
    var sumOuShu = 0; //偶数位数组之和
    var sumJiShuChild1 = 0; //奇数位*2 >9 的分割之后的数组个位数之和
    var sumJiShuChild2 = 0; //奇数位*2 >9 的分割之后的数组十位数之和
    var sumTotal = 0;
    for (var m = 0; m < arrJiShu.length; m++) {
        sumJiShu = sumJiShu + parseInt(arrJiShu[m]);
    }

    for (var n = 0; n < arrOuShu.length; n++) {
        sumOuShu = sumOuShu + parseInt(arrOuShu[n]);
    }

    for (var p = 0; p < jishu_child1.length; p++) {
        sumJiShuChild1 = sumJiShuChild1 + parseInt(jishu_child1[p]);
        sumJiShuChild2 = sumJiShuChild2 + parseInt(jishu_child2[p]);
    }
    //计算总和
    sumTotal = parseInt(sumJiShu) + parseInt(sumOuShu) + parseInt(sumJiShuChild1) + parseInt(sumJiShuChild2);

    //计算luhn值
    var k = parseInt(sumTotal) % 10 == 0 ? 10 : parseInt(sumTotal) % 10;
    var luhn = 10 - k;

    if (lastNum == luhn) {
        console.log("验证通过");
        return true;
    } else {
        return false;
    }
}
