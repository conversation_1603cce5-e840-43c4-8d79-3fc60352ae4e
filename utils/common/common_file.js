const images = ['jpg', 'JPG', 'JPEG', 'jpeg', 'png', 'PNG', 'BMP', 'bmp'];
const fileMediaType = new Map([
	['imageUpload', 'image'], //image/video/all
	['videoUpload', 'video'],
	['fileUpload', 'all']
])
const videos = ['mp4', 'webm', 'ogg', 'mov', 'wmv'];

export const imagesType = images;
export const videosType = videos;

export function getFileMediaType(key) {
	return fileMediaType.get(key) || 'image'
}

export function isImage(file) {
    if (file.type) {
        return file.type.includes('image')
    }
    if (file.id) {
        return images.includes(file.id.substr(file.id.lastIndexOf('.')+1))
    }
    if (file.name) {
        return images.includes(file.name.substr(file.name.lastIndexOf('.')+1))
    }
    // if (file.fileType) {
    //     // console.log(file.fileType,"====file.fileType.includes('image')===",file.fileType.includes('image'))
    //     return file.fileType.includes('image')
    // }
    // if (file.pid) {
    //     return images.includes(file.pid.substr(file.pid.lastIndexOf('.')+1))
    // }
    if (file.fileName) {
        return images.includes(file.fileName.substr(file.fileName.lastIndexOf('.')+1))
    }
    return false
}
