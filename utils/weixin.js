import wx from 'weixin-js-sdk'
// import axios from "axios";
var wxRequest = require('@/utils/wxRequest.js');
import qs from "qs";

export function init() {
    const href = window.location.href;
    const url = href.substring(0, href.indexOf("#"));
    let isDebug = false;
    wxRequest.postRequest("/syt/platform/weixin/js/getJsConfig.htm", qs.stringify({originalUrl: url}), {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        },
        withCredentials: true
    }).then(res => {
        let resData = res.data;
        if (resData) {
            if (resData.debug) {
                isDebug = true;
            }
            wx.config({
                beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                debug: isDebug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: resData.appId, // 必填，企业微信的corpID
                timestamp: resData.timestamp, // 必填，生成签名的时间戳
                nonceStr: resData.nonceStr, // 必填，生成签名的随机串
                signature: resData.signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
                jsApiList: [
                    "scanQRCode",
                    "getLocation",
                    "chooseImage",
                    "getLocalImgData",
                ], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
            });
        }
    })
}

export function location_fun(callBack) {
    wx.getLocation({
        type: 'gcj02', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        // type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: function (res) {
            // var latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
            // var longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
            // var speed = res.speed; // 速度，以米/每秒计
            // var accuracy = res.accuracy; // 位置精度
            let o = {
                longitude: res.longitude,
                latitude: res.latitude,
                address: ''
            };
            let lngLat = new window.AMap.LngLat(o.longitude, o.latitude);
            window.AMap.plugin('AMap.Geocoder', function () {
                let geocoder = new window.AMap.Geocoder();
                geocoder.getAddress(lngLat, function (status, result) {
                    if (status === 'complete' && result.info === 'OK') {
                        o.address = result.regeocode.formattedAddress;
                    }
                    if (callBack) {
                        callBack(JSON.stringify(o));
                    }
                })
            });
        },
        fail: res => {
            console.log(res.errMsg);
        }
    });
}

export function scan_fun(callBack) {
    wx.scanQRCode({
        desc: 'scanQRCode desc',
        needResult: 1, // 默认为0，扫描结果由企业微信处理，1则直接返回扫描结果，
        success: function (res) {
            if (callBack) {
                callBack(res.resultStr);
            }
        },
        error: function (res) {
            if (res.errMsg.includes('function_not_exist')) {
                alert('版本过低请升级')
            }
        }
    });
}
