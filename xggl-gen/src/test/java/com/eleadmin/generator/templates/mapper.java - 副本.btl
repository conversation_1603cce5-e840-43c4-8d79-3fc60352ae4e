package ${package.Mapper};

import ${superMapperClassPackage};
import com.baomidou.mybatisplus.core.metadata.IPage;
import ${package.Entity}.${entity};
import ${cfg.packageName!}.${package.ModuleName}.param.${entity}Param;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ${table.comment!}Mapper
 *
 * <AUTHOR>
 * @since ${date(), 'yyyy-MM-dd HH:mm:ss'}
 */
<% if(kotlin){ %>
interface ${table.mapperName} : ${superMapperClass}<${entity}>
<% }else{ %>
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<${entity}>
     */
    List<${entity}> selectPageRel(@Param("page") IPage<${entity}> page,
                             @Param("param") ${entity}Param param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<${entity}> selectListRel(@Param("param") ${entity}Param param);

}
<% } %>
