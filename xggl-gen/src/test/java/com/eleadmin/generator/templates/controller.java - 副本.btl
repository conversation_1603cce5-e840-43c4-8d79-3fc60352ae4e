<%
var serviceIns = strutil.toLowerCase(strutil.subStringTo(table.serviceName, 0, 1)) + strutil.subString(table.serviceName, 1);
var authPre = package.ModuleName + ':' + table.entityPath;
var idFieldName, idPropertyName;
for(field in table.fields) {
    if(field.keyFlag) {
        idFieldName = field.name;
        idPropertyName = field.propertyName;
    }
}
%>
package ${package.Controller};

<% if(isNotEmpty(superControllerClassPackage)) { %>
import ${superControllerClassPackage};
<% } %>
import ${cfg.packageName!}.${package.ModuleName}.service.${entity}Service;
import ${cfg.packageName!}.${package.ModuleName}.entity.${entity};
import ${cfg.packageName!}.${package.ModuleName}.param.${entity}Param;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.annotation.OperationLog;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;
<% if(!restControllerStyle) { %>
import org.springframework.stereotype.Controller;
<% } %>

import jakarta.annotation.Resource;
import java.util.List;

/**
 * ${table.comment!}控制器
 *
 * <AUTHOR>
 * @since ${date(), 'yyyy-MM-dd HH:mm:ss'}
 */
<% if(swagger2) { %>
@Api(tags = "${table.comment!}管理")
<% } %>
<% if(restControllerStyle) { %>
@RestController
<% } else { %>
@Controller
<% } %>
@RequestMapping("${cfg.controllerMappingPrefix!}<% if(isNotEmpty(package.ModuleName)){ %>/${package.ModuleName}<% } %>/<% if(isNotEmpty(controllerMappingHyphenStyle)){ %>${controllerMappingHyphen}<% }else{ %>${table.entityPath}<% } %>")
<% if(kotlin) { %>
class ${table.controllerName}<% if(isNotEmpty(superControllerClass)) { %> : ${superControllerClass}()<% } %>
<% } else if(isNotEmpty(superControllerClass)) { %>
public class ${table.controllerName} extends ${superControllerClass} {
<% } else { %>
public class ${table.controllerName} {
<% } %>
    @Resource
    private ${table.serviceName} ${serviceIns};

    <% if(!swagger2) { %>
    /**
     * 分页查询${table.comment!}（权限标识：${authPre}:list）
     */
    <% } %>
    <% if(cfg.authAnnotation) { %>
    @PreAuthorize("hasAuthority('${authPre}:list')")
    <% } %>
    <% if(swagger2) { %>
    @ApiOperation("分页查询${table.comment!}")
    <% } %>
    <% if(!restControllerStyle) { %>
    @ResponseBody
    <% } %>
    @GetMapping("/page")
    public PageResult<${entity}> page(${entity}Param param) {
        PageParam<${entity}, ${entity}Param> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = ${serviceIns}.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    <% if(!swagger2) { %>
    /**
     * 查询全部${table.comment!}（权限标识：${authPre}:list）
     */
    <% } %>
    <% if(cfg.authAnnotation) { %>
    @PreAuthorize("hasAuthority('${authPre}:list')")
    <% } %>
    <% if(swagger2) { %>
    @ApiOperation("查询全部${table.comment!}")
    <% } %>
    <% if(!restControllerStyle) { %>
    @ResponseBody
    <% } %>
    @GetMapping()
    public List<${entity}> list(${entity}Param param) {
        PageParam<${entity}, ${entity}Param> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return ${serviceIns}.list(page.getOrderWrapper());
    }

    <% if(!swagger2) { %>
    /**
     * 根据id查询${table.comment!}（权限标识：${authPre}:list）
     */
    <% } %>
    @PreAuthorize("hasAuthority('${authPre}:list')")
    @GetMapping("/{id}")
    public ${entity} get(@PathVariable("id") String id) {
        return ${serviceIns}.getById(id);
    }

    <% if(!swagger2) { %>
    /**
     * 添加或修改${table.comment!}（权限标识：${authPre}:operation）
     */
    <% } %>
    <% if(cfg.authAnnotation) { %>
    @PreAuthorize("hasAuthority('${authPre}:operation')")
    <% } %>
    <% if(cfg.logAnnotation) { %>
    @OperationLog(module = "${table.comment!}", comments = "保存${table.comment!}")
    <% } %>
    <% if(swagger2) { %>
    @ApiOperation("添加或修改${table.comment!}")
    <% } %>
    <% if(!restControllerStyle) { %>
    @ResponseBody
    <% } %>
    @PostMapping("/operation")
    public void save(@RequestBody ${entity} ${table.entityPath}) {
        if (StringUtils.hasLength(${table.entityPath}.getId())) {
            ${serviceIns}.updateById(${table.entityPath});
        } else {
            ${serviceIns}.save(${table.entityPath});
        }
    }

    <% if(!swagger2) { %>
    /**
     * 批量删除${table.comment!}（权限标识：${authPre}:remove）
     */
    <% } %>
    <% if(cfg.authAnnotation) { %>
    @PreAuthorize("hasAuthority('${authPre}:remove')")
    <% } %>
    <% if(cfg.logAnnotation) { %>
    @OperationLog(module = "${table.comment!}", comments = "批量删除${table.comment!}")
    <% } %>
    <% if(swagger2) { %>
    @ApiOperation("批量删除${table.comment!}")
    <% } %>
    <% if(!restControllerStyle) { %>
    @ResponseBody
    <% } %>
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        ${serviceIns}.removeByIds(ids);
    }
}
