<%
var idPropertyName, idCapitalName;
for(field in table.fields) {
    if(field.keyFlag) {
        idPropertyName = field.propertyName;
        idCapitalName = field.capitalName;
    }
}
%>
package ${package.ServiceImpl};

import ${superServiceImplClassPackage};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${package.Entity}.${entity};
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * ${table.comment!}Service实现
 *
 * <AUTHOR>
 * @since ${date(), 'yyyy-MM-dd HH:mm:ss'}
 */
@Service
<% if(kotlin){ %>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
<% }else{ %>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Resource
    private ${table.mapperName} mapper;

}
<% } %>
