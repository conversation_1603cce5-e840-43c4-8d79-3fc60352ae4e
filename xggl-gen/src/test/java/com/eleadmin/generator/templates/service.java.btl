<%
var idPropertyName, idComment;
for(field in table.fields) {
    if(field.keyFlag) {
        idPropertyName = field.propertyName;
        idComment = field.comment;
    }
}
%>
package ${package.Service};

import ${superServiceClassPackage};
import ${package.Entity}.${entity};

/**
 * ${table.comment!}Service
 *
 * <AUTHOR>
 * @since ${date(), 'yyyy-MM-dd HH:mm:ss'}
 */
<% if(kotlin){ %>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<% }else{ %>
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {

}
<% } %>
