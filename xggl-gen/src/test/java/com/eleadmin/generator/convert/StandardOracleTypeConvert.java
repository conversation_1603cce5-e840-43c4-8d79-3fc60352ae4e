package com.eleadmin.generator.convert;

import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.IColumnType;
import com.baomidou.mybatisplus.generator.config.converts.OracleTypeConvert;

public class StandardOracleTypeConvert extends OracleTypeConvert {

    @Override
    public IColumnType processTypeConvert(GlobalConfig globalConfig, String fieldType) {
        String lowerFieldType = fieldType.toLowerCase();

        if (lowerFieldType.startsWith("number")) {
            // 检查是否包含精度信息
            int leftParenIndex = lowerFieldType.indexOf("(");
            int rightParenIndex = lowerFieldType.indexOf(")");

            if (leftParenIndex > 0 && rightParenIndex > leftParenIndex) {
                // 提取精度 (p,s)
                String[] precision = lowerFieldType.substring(leftParenIndex + 1, rightParenIndex).split(",");
                try {
                    int precisionP = Integer.parseInt(precision[0].trim());
                    int scaleS = precision.length > 1 ? Integer.parseInt(precision[1].trim()) : 0;

                    if (scaleS == 0) {
                        // 整数类型映射
                        if (precisionP <= 9) {
                            return DbColumnType.INTEGER; // 小范围整数
                        } else {
                            return DbColumnType.LONG; // 大范围整数
                        }
                    } else {
                        // 小数类型映射
                        return DbColumnType.DOUBLE;
                    }
                } catch (NumberFormatException e) {
                    // 如果解析失败，回退到默认 BigDecimal
                    return DbColumnType.BIG_DECIMAL;
                }
            }

            // 如果没有括号 (p, s)，默认将其视为整数类型
            return DbColumnType.BIG_DECIMAL; // 或 DbColumnType.INTEGER，根据业务需求决定
        }

        // 使用默认类型转换逻辑
        return super.processTypeConvert(globalConfig, fieldType);
    }
}
