<!-- 日常服务 -->
<template>
	<view>
	<view class="index-sticky ui-BG-card ui-BG" v-if="listServices.length > 0" style="padding: 16px; position: inherit;">
		<!-- <ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding-bottom: 8px !important;padding-top: 0 !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title> -->
		<!-- <view class="index-sticky ui-BG-card ui-BG" style="margin: 0;padding: 0;"> -->
			<!-- <image class="service-image" src="@/static/home-icon/rcsw.png"></image> -->
		<!-- </view> -->
		<!-- <view class="class="index-sticky ui-BG-card ui-BG""> -->
		<!-- style="margin-top: -240px;" -->
		<view style="display: flex;align-items: center;">
			<view class="content-left">
				<view class="service-title text-shadow ">{{title}}</view>
				<block v-if="serviceType =='type1'">
					<view class="service-desc  ">日常事务工作量大？</view>
					<view class="service-desc  ">手续繁琐？人手不足？</view>
					<view class="service-desc  ">本模块功能帮助辅导员高效处理</view>
					<view class="service-desc  ">学生日常事务性工作</view>
				</block>
				<block v-else-if="serviceType =='type2'">
					<view class="service-desc  ">线下评定周期长，繁琐易错？</view>
					<view class="service-desc  ">评定过程不透明？数据无留存？</view>
					<view class="service-desc  ">本服务能帮助资助评定工作有序开展</view>
					<view class="service-desc  ">过程可管可控，提高工作效率</view>
				</block>
				<block v-else-if="serviceType =='type3' || serviceType =='type5'">
					<view class="service-desc  ">宿管服务覆盖学生从入住</view>
					<view class="service-desc  ">到退宿的全业务周期，</view>
					<view class="service-desc  ">实现对宿管数据、流程决策全过程</view>
					<view class="service-desc  ">的信息化、移动化、智能化。</view>
				</block>
				<block v-else-if="serviceType =='type4'">
					<view class="service-desc  ">日常事务工作量大？</view>
					<view class="service-desc  ">手续繁琐？人手不足？</view>
					<view class="service-desc  ">本模块功能帮助辅导员高效处理学</view>
					<view class="service-desc  ">生日常事务性工作</view>
				</block>
			</view>
			<view class="content-right">
				<image class="service-image" src="@/static/home-icon/service-page.png"></image>
			</view>
		</view>
		<!-- style="padding: 11px;" -->
	</view>
	<view class="index-sticky ui-BG-card ui-BG menu-container" style="margin: 16px 0 4px; padding: 0;">
		<view class="ui-grid ui-cols-4 menu-box">
			<!-- .slice(0, 4) -->
			<block v-for="(item,index) in listServices" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" :data-type="item.title"
					:data-url="item.name" :data-path="item.name">
					<!-- <image v-if="item.type == 'image'" class="round" mode="aspectFill" :src="item.name"></image> -->
					<Icon-Park v-if="item.type == 'image'" :iconName="item.iconName" :fill="item.iconColor" :theme="item.theme" :size="item.size" />
					<view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view>
					<text class="ui-TC text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}</text>
				</view>
			</block>
		</view>
	</view>
	</view>
</template>

<script>
	import StatisticData from "./statisticData";
	export default {
		props: {
			serviceType:  {
				type: String,
				default: ''
			}
		},
		data(){
			return {
				listServices: [],
				title: null,
			}
		},
		created(){
			console.log('this.serviceType', this.serviceType);
			this.getData();
		},
		watch: {
			serviceType(newVal){
				if(newVal) {
					this.getData();
				}
			}
		},
		methods: {
			getData() {
				let serviceType = this.serviceType;
				console.log('serviceType', serviceType)
				this.listServices = [];
				if(serviceType.indexOf('service') > -1) {
					this.listServices = StatisticData[serviceType];
				} else {
					let listServices = StatisticData.allServiceList.find(item => item.id == serviceType);
					if(listServices) {
						this.listServices = listServices.dataList;
						this.title = listServices.title;
					}
				}
				console.log('this.listServices 22', this.listServices)
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
			
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	/* 图标导航 */
	.ui-BG-card {
		margin: 0 16px;
		border-radius: 12px;
		background: linear-gradient(89.99999deg, #EFF6FF 0%, #DBEAFE 100%);
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
	}
	.service-image {
		width: 80px;
		height: 80px;
		margin: 0 auto;
	}
	.service-title {
		color: var(--ui-TC-7);
		font-family: "Inter";
		font-size: 18px;
		font-style: normal;
		font-weight: 500;
		line-height: 30px;
		width: 100%;
	}
	.text-shadow {
		word-wrap: break-word;
	    text-shadow: 4rpx 4rpx 0 rgba(255, 255, 255, 1);
	    text-transform: none;
	}
	.service-desc {
		width: 100%;
		padding-top: 2rpx !important;
		padding-bottom: 4rpx !important;
		// color: var(--ui-TC-3) !important;
		color: #6A6A6A;
	}
	.content {
		margin-top:-244px !important; 
		padding: 11px 11px 23px !important;
		position: inherit;
	}
	.ui-BG-card.menu-container {
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
		border-radius: 40px 0 var(--radius) 0;
		// background: linear-gradient(167.1deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 50%, transparent 100%);
		background: transparent !important;
		box-shadow: none;
	}
	.menu-box {
		// padding: 36rpx 22rpx 22rpx !important;
		margin: 0 !important;
		position: inherit;
		// background-color: gray;
		// border-radius: 40px 0 var(--radius) 0;
		// background: linear-gradient(167.1deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 50%, transparent 100%);
	}
	.ui-item {
		display: block;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
	}
	.ui-item.ui-padding-12:last-child(1) {
		padding-bottom: 4px !important;
	}
	.ui-padding-12 {
		padding-bottom: 24rpx !important;
	}
	// .ui-item image, .ui-item svg {
	.ui-item .i-icon {
		width: 24px;
		height: 24px;
		margin: auto;
		margin-bottom: 2px;
		display: block;
		padding: 12px;
		background-color: #ffffff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
		border-radius: 12px;
	}
	uni-image {
		position: inherit;
	}
	.ui-item text {
		color: var(--ui-TC-5) !important;
		font-family: "Inter";
		font-size: 14px !important;
		font-style: normal;
		font-weight: 400;
		line-height: 30px;
	}
	.content {
		display: flex;
		width: 100%;
		align-items: center;
	}
	.content-left {
		flex: 1;
	}
	.content-right {
		width: 80px;
	}
</style>