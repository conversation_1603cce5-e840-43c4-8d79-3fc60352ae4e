<!-- 日常服务 -->
<template>
	<block>
	<!-- <view v-if="marginTop != 20" class="index-sticky ui-BG-card" style="margin-top: 84rpx;">
		<ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding-bottom: 8px !important;padding-top: 0 !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in listServices" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" :data-type="item.title"
					:data-url="item.name" :data-path="item.name">
					<image v-if="item.type == 'image'" class="round" mode="aspectFill" :src="item.name"></image>
					<view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view>
					<text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}11</text>
				</view>
			</block>
		</view>
	</view> -->
	<view class="index-sticky ui-BG-card ui-BG">
		<ui-title v-if="title" :title="title" :desc="desc" :moreIcon="moreIcon" depth="3" style="padding-bottom: 12px !important;padding-top: 0 !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main"/>
			<!-- <text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" /> -->
			<text slot="more">
			{{more}}
			</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in listServices" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" :data-type="item.title">
					<view class="text-df"  :style="{color: item.color}">
						{{ item.number }}
					</view>
					<text class="ui-TC-6 text-df">{{item.title}}</text>
				</view>
			</block>
		</view>
		<view class="charts-box" v-if="chartType" style="margin-top: 10px;">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view>
	</view>
	</block>
</template>

<script>
	import StatisticData from "./statisticData";
	//下面是演示数据，您的项目不需要引用，数据需要您从服务器自行获取
	import demodata from '@/mockdata/demodata.json'
	export default {
		props: {
			// 当前选中的主题对应的系统设置的16进制颜色代码
			fillColor: {
				type: String,
				default: ''
			},
			serviceType:  {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: ''
			},
			titleIcon: {
				type: String,
				default: 'cicon-titles'
			},
			desc: {
				type: String,
				default: ''
			},
			more: {
				type: String,
				default: ''
			},
			moreIcon: {
				type: Boolean,
				default: false
			},
			marginTop: {
				type: Number,
				default: 20
			},
			chartType: {
				type: String,
				default: null
			},
			opts: {
				type: Object,
				default() {
					return {}
				}
			}
		},
		data(){
			return {
				listServices: [],
				chartsDataColumn: {},
			}
		},
		created(){
			console.log('this.serviceType', this.serviceType, this.marginTop, this.chartType);
			this.getData();
		},
		watch: {
			serviceType(newVal){
				console.log('111111111111111', newVal)
				if(newVal) {
					this.getData();
				}
			},
		},
		methods: {
			getData() {
				let serviceType = this.serviceType;
				if(this.title == '重点工作提醒') {
					serviceType = 'service2';
				} else if(this.title == '奖学金审批') {
					serviceType = 'service7';
				} else if(this.title == '宿舍检查') {
					serviceType = 'service9';
				}
				console.log('aaaa', this.listServices, serviceType)
				if(serviceType) {
					if(serviceType.indexOf('service') > -1) {
						this.listServices = StatisticData[serviceType];
						console.log('aaaa', this.listServices)
					} else {
						let listServices = StatisticData.allServiceList.find(item => item.id == serviceType);
						if(listServices) {
							this.listServices = listServices.dataList;
						}
					}
				}
				if(this.chartType) {
					setTimeout(() => {
						//因部分数据格式一样，这里不同图表引用同一数据源的话，需要深拷贝一下构造不同的对象
						//开发者需要自行处理服务器返回的数据，应与标准数据格式一致，注意series的data数值应为数字格式
					 if(serviceType == 'service6') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.Column))
					 } else if(serviceType == 'service7') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 } else if(serviceType == 'service8') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.Line))
					 } else if(serviceType == 'service11') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 } else if(serviceType == 'service12') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 }
					  console.log('this.chartsDataColumn', this.chartsDataColumn);
					}, 1000)
				}
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: #ffffff !important;
	}
	/* 图标导航 */
	.ui-BG-card {
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
		padding: 16px;
		box-shadow: none;
		margin: 16px;
		border-radius: 12px;
	}
	
	.ui-item {
		display: block;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
		position: relative;
	}
	.ui-padding-12 {
		padding-bottom: 0rpx;
	}
	
	.ui-item image, .ui-item svg {
		width: 24px;
		height: 24px;
		margin: auto;
		margin-bottom: 2px;
		display: block;
		padding: 12px;
		background-color: #ffffff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
		border-radius: 12px;
	}
	.ui-item image, .ui-item svg path {
	  // fill: red; /* 改变SVG路径的颜色 */
	}
	
	.ui-item text {
		// color: #333;
		// font-weight: 300;
		color:var(--ui-TC-6);
		font-family: "Inter";
		font-size: 12px !important;
		font-style: normal;
		font-weight: 400;
		// line-height: 30px;
	}
	.ui-item uni-view {
		color: var(--ui-TC-7);
		font-family: "Inter";
		font-size: 18px !important;
		font-style: normal;
		font-weight: 600;
		line-height: 28px;
	}
	.ui-item::after {
		content: ""; /* 必须有content属性，即使为空 */
		position: absolute; /* 绝对定位 */
		top: 5%; /* 伪元素位于元素顶部 */
		right: 0; /* 从右侧开始 */
		width: 1px; /* 宽度 */
		height: 90%; /* 边框的高度 */
		background-color: var(--ui-TC-8); /* 边框颜色 */
	}
	.ui-item:last-child::after {
		content: ""; /* 必须有content属性，即使为空 */
		position: absolute; /* 绝对定位 */
		top: 5%; /* 伪元素位于元素顶部 */
		right: 0; /* 从右侧开始 */
		width: 0; /* 宽度 */
		height: 80%; /* 边框的高度 */
		background-color: none; /* 边框颜色 */
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
</style>