<template>
	<!-- 新闻动态 -->
	<!-- v-if="StickyList.length > 0" -->
	<view class="ui-BG-card main ui-BG" style="padding: 16px;margin-top: 20rpx;">
		<ui-title v-if="title" :title="title" :desc="desc" :moreIcon="moreIcon" depth="3"  style="padding-bottom: 0px !important;padding-top: 0 !important;">
			<!-- <text slot="icon" class="ui-TC-Main" /> -->
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main"/>
			<text slot="more">{{more}}</text>
		</ui-title>
		<!-- 列表template模板 -->
		<view class="post-list" style="padding-top: 0;">
			<block>
				<view class="post-item ui-BG ui-BG-card" @tap="redictDetail">
					<view class="post-desc">
						<view class="post-title ui-TC">
							<text>智慧开放大学党委召开学习贯彻习近平新时代中国特色社会主义思想主题教育总结会议</text>
						</view>
						<view class="post-data ">
							<text class="cicon-time-o"> 2022-12-03 14:25
							</text>
							<!-- <text class="cicon-popover-o  "> 100</text> -->
							<text class="cicon-favorite-o"> 4</text>
							<text class="cicon-eye-o "> 420</text>
						</view>
					</view>
					<!-- <image :src="item.post_medium_image" mode="aspectFill" class="post-img"
						v-if="item.post_medium_image"></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card" @tap="redictDetail">
					<view class="post-desc">
						<view class="post-title ui-TC">
							<text>党委书记杨成飞出席华新学院2023级新生开学典礼暨军训汇报表演</text>
						</view>
						<view class="post-data ">
							<text class="cicon-time-o"> 2022-12-03 14:25
							</text>
							<!-- <text class="cicon-popover-o  "> 100</text> -->
							<text class="cicon-favorite-o"> 4</text>
							<text class="cicon-eye-o"> 420</text>
						</view>
					</view>
					<!-- <image :src="item.post_medium_image" mode="aspectFill" class="post-img"
						v-if="item.post_medium_image"></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card" @tap="redictDetail">
					<view class="post-desc">
						<view class="post-title ui-TC">
							<text>追寻先烈足迹 传承红色基因——纪委办监察审计党支部开展“强党建 促发展”主题党日活动</text>
						</view>
						<view class="post-data ">
							<text class="cicon-time-o"> 2022-12-03 14:25</text>
							<!-- <text class="cicon-popover-o  "> 100</text> -->
							<text class="cicon-favorite-o "> 4</text>
							<text class="cicon-eye-o "> 420</text>
						</view>
					</view>
					<!-- <image :src="item.post_medium_image" mode="aspectFill" class="post-img"
						v-if="item.post_medium_image"></image> -->
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default{
		props: {
			title: {
				type: String,
				default: ''
			},
			titleIcon: {
				type: String,
				default: 'cicon-titles'
			},
			desc: {
				type: String,
				default: ''
			},
			more: {
				type: String,
				default: ''
			},
			moreIcon: {
				type: Boolean,
				default: false
			}
		},
		data(){
			return {
				StickyList: []
			}
		}
	}
</script>

<style scoped>
	::v-deep .ui-BG {
		background-color: #ffffff !important;
	}
	.ui-BG-card.main {
		box-shadow: none;
		margin: 16px;
		border-radius: 12px;
		/* background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx); */
	}
	.sticky-list {
		height: 130rpx;
		overflow: hidden;
	}
	.sticky-list-item-title {
		display: block;
	
		overflow: hidden;
	}
	.post-list .ui-BG-card:nth-child(1) {
		padding: 16px 0;
		margin: 0;
		border-bottom: 1px solid var(--ui-TC-8);
		border-radius: 0;
	}
	.post-list:first-child .ui-BG-card:nth-child(1) {
		padding: 0 0 16px;
		margin: 0;
		border-bottom: 1px solid var(--ui-TC-8);
		border-radius: 0;
	}
	.post-list:last-child .ui-BG-card {
		border-bottom: none;
	}
	.cicon-time-o::before,.cicon-favorite-o::before,.cicon-eye-o::before {
		margin-right: 6rpx;
	}
	.post-title {
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		/* line-height: 20px; */
	}
	.post-data {
		padding-top: 8px;
		color: var(--ui-TC-6) !important;
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
</style>