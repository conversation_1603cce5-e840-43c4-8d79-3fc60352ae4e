<template>
	<!-- 通知公告 -->
	<!-- v-if="StickyList.length > 0" -->
	<view class="ui-BG-card ui-BG"  style="padding: 0; margin:0 0 4px; border-radius: 0">
		<!-- <ui-title title="推荐文章" desc="汇聚精彩文章" depth="3">
			<text slot="icon" class="ui-TC-Main cicon-whatshot" />
		</ui-title> -->
		<!-- display-multiple-items="2" -->
		<swiper class="sticky-list" :autoplay="true" interval="5000" duration="400" :circular="true"
			:vertical="true">
			<swiper-item class="sticky-list-item" @tap="redictDetail">
				<view class="content">
					<view class="sticky-list-item-title text-df text-ellipsis">
						<!-- <text class="dot ui-BG-Main"></text> -->
						<Icon-Park :iconName="'speaker-one'" :size="18" style="float: left;margin: 4px 3px;"  />
						<!-- <icon-speaker-one :fill="[fillColor]" :size="18" style="float: left;margin: 5px 3px;" /> -->
						党委书记杨成飞出席华新学院2023级新生开学典礼暨军训汇报表演
					</view>
					<text class="time text-sm">2022-12-03 14:25</text>
				</view>
			</swiper-item>
			<swiper-item class="sticky-list-item" @tap="redictDetail">
				<view class="content">
				<view class="sticky-list-item-title text-df text-ellipsis">
					<!-- <text class="dot ui-BG-Main"></text> -->
					<Icon-Park :iconName="'speaker-one'" :size="18" style="float: left;margin: 4px 3px;"  />
					<!-- <icon-speaker-one :fill="[fillColor]" :size="18" style="float: left;margin: 5px 3px;" /> -->
					智慧开放大学召开高等学历继续教育改革工作推进会
				</view>
				<text class="time text-sm">2022-12-04 14:25</text>
				</view>
			</swiper-item>
			<swiper-item class="sticky-list-item" @tap="redictDetail">
				<view class="content">
				<view class="sticky-list-item-title text-df text-ellipsis">
					<!-- <text class="dot ui-BG-Main"></text> -->
					<Icon-Park :iconName="'speaker-one'" :size="18" style="float: left;margin: 4px 3px;"  />
					<!-- <icon-speaker-one :fill="[fillColor]" :size="18" style="float: left;margin: 5px 3px;" /> -->
					追寻先烈足迹 传承红色基因——纪委办监察审计党支部开展“强党建 促发展”主题党日活动
				</view>
				<text class="time text-sm">2022-12-05 14:25</text>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default{
		props: {
			fillColor:{
				type: String,
				default: ''
			}
		},
		data(){
			return {
				StickyList: []
			}
		}
	}
</script>

<style scoped>
	.ui-BG-card {
		/* background-color: transparent !important;
		background-image: url("@/static/home-icon/tzgg-bg.png");
		background-size: cover; */
		/* margin: 10rpx 20rpx; */
		/* background-position: inherit; */
	}
	.sticky-list {
		height: 64rpx;
		overflow: hidden;
	}
	.sticky-list-item {
		height: 60rpx !important;
		line-height: 64rpx;
		max-height: 60rpx;
	}
	.content {
		display: flex;
		padding: 0 10px;
	}
	.sticky-list-item-title {
		flex: 1;
		display: block;
		overflow: hidden;
		font-size: 14px;
		color: var(--ui-TC-7) !important;
	}
	.text-ellipsis {
		/* height: 50rpx; */
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}
	.time {
		/* padding-left: 130rpx; */
		width: 94px;
		color: var(--ui-TC-6);
	}
</style>