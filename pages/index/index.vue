<template>
	<!-- tabbar -->
	<ui-sys :loading="isLoading">
		<!-- 背景图 -->
		<ui-navbar :back="false" bg="ui-BG-Main" op isSlot :index="-1000">
			<view class="flex-bar p-4">
				<image src="@/static/home-icon/school-logo.png" mode="aspectFill" style="width: 54rpx;height: 54rpx;margin-right: 14rpx;" />
				<span class="font-3 color-white" style="font-weight: bold;">北京智慧大学</span>
			</view>
			<view class="icons-list">
				<icon-me @click.native="openSelectRole" :strokeWidth="4" :fill="['#ffffff']" :size="24" />
			</view>
		</ui-navbar>
		<!-- 首次进入引导添加到“我的小程序” -->
		<view class="addMyMiniapp" v-if="isFirst" @tap.stop.prevent="shutAddMyMiniapp">
			<view>点击加入我的小程序 ↑</view>
			<text>了解"{{webSiteName}}"最新文章</text>
		</view>
		<!-- 背景渐变色 -->
		<!-- <view class="fixed-bg">
		</view> -->
		<!-- 遍历组件数组 -->
		<block v-for="item in componentsList">
			<Swiper v-if="item.displayType == '轮播图'" :dataList="item.items"></Swiper>
			<Notice v-else-if="item.displayType == '滚动公告'" :fillColor="'red'"></Notice>
			<CommonApps v-else-if="item.displayType == '菜单组件'" style="margin-top: 16px;" :dataList="getChildList(item.items, 'app')" :theme="'filled'" :fillColor="currentThemeColor"></CommonApps>
			<MainServiceImgLeft v-else-if="item.displayType == '服务卡片组件'" :dataList="getChildList(item.items, 'groupVo')"></MainServiceImgLeft>
			<Common-Number v-else-if="item.displayType == '数据概览-数据量'" :title="item.functionType" :more="'更多'" :moreIcon="true" :serviceType="'service2'"></Common-Number>
			<Common-Table v-else-if="item.displayType == '数据概览-表格'" :title="item.functionType" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
			<News v-else-if="item.displayType == '数据概览-列表'" :title="item.functionType" :more="'更多'" :moreIcon="true"></News>
		</block>
		<!-- 轮播图 -->
		<!-- <Swiper></Swiper> -->
		<!-- 通知公告 -->
		<!-- <Notice :fillColor="'red'"></Notice> -->
		<!-- 日常服务 -->
		<!-- <Common-Service style="margin-top: 16px;" :serviceType="'service1'" :fillColor="currentThemeColor"></Common-Service> -->
		<!-- 主要功能 -->
		<!-- <MainService style="margin-bottom: 16px;"></MainService>
		<MainServiceNoImg style="margin-bottom: 16px;"></MainServiceNoImg>
		<MainServiceImgLeft ></MainServiceImgLeft> -->
		<!-- 我的数据 -->
		<!-- <Common-Number :title="'我的数据'" :more="'更多'" :moreIcon="true" :serviceType="'service2'"></Common-Number> -->
		<!-- 待办任务 -->
		<!-- <Common-Table :title="'待办任务'" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table> -->
		<!-- 文章列表 -->
		<view class="container ">
			<!-- 新闻动态 -->
			<!-- <News :title="'新闻动态'" :more="'更多'" :moreIcon="true"></News> -->
			<view :style="'display:'+(isLoading?'block':'none')+';margin-top: 48rpx;'">
				<!-- parse <template is="tempLoading"/> -->
				<block name="tempLoading">
					<view class="sk-three-bounce">
						<view class="sk-child sk-bounce1 ui-BG-Main-2"></view>
						<view class="sk-child sk-bounce2 ui-BG-Main-2"></view>
						<view class="sk-child sk-bounce3 ui-BG-Main-2"></view>
					</view>
				</block>
			</view>
			<!-- 无更多文章提示 -->
			<view class="loadingmore" :style="'display:'+(floatDisplay)">
				<view class="no-more" v-if="(isLastPage)">- 无更多内容 -</view>
			</view>
			<!-- 版权信息template模板 -->
			<!-- <view class="copyright"> -->
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<!-- <block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view> -->
		</view>
		<!-- 无法访问网络 -->
		<view class="showerror" :style="'display:'+(showerror)">
			<text class="cicon-wifi-off text-xsl"></text>
			<view class="errortext">
				暂时无法访问网络，下拉重试...
			</view>
		</view>
		<HoverBall></HoverBall>
		<uni-popup ref="selectRoleDialog" type="dialog" :is-mask-click="true" @maskClick="onMaskClick">
			<uni-popup-dialog type="info" ref="inputClose" title="请选择角色"
			@confirm="dialogConfirm" :showClose="false" confirmText="关闭">
				<scroll-view scroll-y="true" show-scrollbar="true" class="scroll-view-style">
			      <!-- 内容 -->
			      <view v-for="(item, index) in dataList" :key="index" @click="onSelectRole(item)">
			        {{ item.name }}
			      </view>
			    </scroll-view>
			</uni-popup-dialog>
		</uni-popup>
	</ui-sys>

</template>

<script>
	// import Button from 'vant-green/lib/button';
	// import 'vant-green/lib/button/style';
	import DynamicIcon from "@/components/Icon-Park.vue"
	import Swiper from "@/components/swiper.vue";
	import CommonApps from "@/components/common-apps.vue";
	// import CommonService from "./components/common-service.vue";
	import CommonNumber from "@/components/common-number.vue";
	import MainService from "./components/main-service.vue";
	import MainServiceNoImg from "./components/main-service-no-img.vue";
	import MainServiceImgLeft from "@/components/main-service-img-left.vue";
	import Notice from "./components/notice.vue";
	import News from "./components/news.vue";
	import CommonTable from "./components/common-table.vue";
	import store from '@/ui/store'
	var Api = require("@/utils/api.js");
	var util = require("@/utils/util.js");
	var wxApi = require("@/utils/wxApi.js");
	var wxRequest = require("@/utils/wxRequest.js");
	import config from "@/utils/config";
	const Adapter = require("@/utils/adapter.js");
	var pageCount = config.getPageCount;
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;

	const floatBall = uni.requireNativePlugin('Ba-FloatBall')


	export default {
		components: {
			DynamicIcon,
			Swiper,
			CommonApps,
			// CommonService,
			CommonNumber,
			MainService,
			MainServiceNoImg,
			MainServiceImgLeft,
			Notice,
			News,
			CommonTable
		},
		data() {
			return {
				currentDate: new Date(),
				dataList: [], 
				currentThemeColor: '',
				ballSize: 0.12,
				menuSize: 0.5,
				menuItemSize: 0.1,
				ballIcon: "floatball_icon",

				postsList: [],
				StickyList: [],
				isLastPage: false,
				page: 1,
				search: '',
				categories: 0,
				showerror: "none",
				showCategoryName: "",
				categoryName: "",
				showallDisplay: "block",
				displayHeader: "none",
				displaySwiper: "none",
				floatDisplay: "none",
				displayfirstSwiper: "none",
				webSiteName: webSiteName,
				domain: domain,
				isFirst: false, // 是否第一次打开,
				isLoading: false,
				componentsList: [], // 首页所有模块的list
				outLinkUrl: null
			}
		},
		props: {},
		onShareAppMessage: function() {
			return {
				title: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',
				path: 'pages/index/index',
				success: function(res) { // 转发成功
				},
				fail: function(res) { // 转发失败
				}
			};
		},
		onPullDownRefresh: function() {
			console.log("下拉加载")
			var self = this;
			self.setData({
				showerror: "none",
				showallDisplay: "block",
				displaySwiper: "none",
				floatDisplay: "none",
				isLastPage: false,
				page: 1,
				postsShowSwiperList: []
			});
			this.getHomeDetail();
		},
		onReachBottom: function() {
			var self = this;
			if (!self.isLastPage) {
				self.setData({
					page: self.page + 1
				});
				console.log('当前页' + self.page);
			} else {
				console.log('最后一页');
			}
		},
		onLoad: function(options) {
			console.log('index onLoad', options);
			console.log('token', uni.getStorageSync('token'));
			var isFirstStorage = uni.getStorageSync('isFirst');
			console.log('isFirstStorage', isFirstStorage)
			let params = util.getUrlParams();
			console.log('getUrlParams', params);
			let self = this;
			// 只要传递了token过来，无论本地是否登录，都是用传递的token
			if(params.t) { // 登录验证通过返回token
				uni.setStorageSync('token', params.t)
				if(params.rid) { // 根据传递的rid查询roleName
					self.getUserRoleList(params.rid);
					self.getHomeDetail();
					if(params.u) { // 如果传递了url，跳转至该url
						self.setData({
							outLinkUrl: params.u
						})
						console.log('self.outLinkUrl', self.outLinkUrl);
					}
				} else { // 如果没有传递rid，弹出选择角色框
					// uni.setStorageSync('isFirst', false); // console.log(wx.getStorageSync('isFirst'));
					console.log('传递了token', store.getters.getRoleKey)
					if(!store.getters.getRoleKey) { // 如果没选过角色，需要弹出角色选择弹窗(点击选中角色后，如过传递了url，选中后直接跳转至改url)
						setTimeout(function() {
							self.openSelectRole(); 
						}, 100);
					}
					if(params.u) { // 如果传递了url，跳转至该url
						self.setData({
							outLinkUrl: params.u
						})
					}
				}
				
			} else { 
				if(uni.getStorageSync('token')) {
					console.log('登录页过来的', uni.getStorageSync('isFirst'))
					console.log('isFirstStorage', isFirstStorage)
					if (isFirstStorage) { //第一次打开，弹出选择角色，并且设置为false
						uni.setStorageSync('isFirst', false); // console.log(wx.getStorageSync('isFirst'));
						setTimeout(function() {
							self.openSelectRole();
						}, 100);
					}
					self.getHomeDetail();
				} else { // 登录验证失败，跳转至登录页，并提示
					uni.showToast({
					  title: '登录失败',
					  mask: false,
					  icon: 'error',
					  duration: 1000
					});
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}
			}
			
			uni.setNavigationBarTitle({
				title: webSiteName
			});

		},
		onShow: function(options) {
			console.log('onShow outLinkUrl', this.outLinkUrl);
			this.currentThemeColor = util.getCurrentThemeColor();
			uni.setStorageSync('openLinkCount', 0);
			var nowDate = new Date();
			nowDate = nowDate.getFullYear() + "-" + (nowDate.getMonth() + 1) + '-' + nowDate.getDate();
			nowDate = new Date(nowDate).getTime();
		},
		methods: {
			onMaskClick() {
				console.log('点击了遮罩', store.getters.getRoleKey);
				// if(!store.getters.getRoleKey) {
				// 	uni.showToast({
				// 		title: '尚未选择角色！',
				// 		icon: 'error',
				// 		duration: 1000
				// 	});
				// } else {
				// 	this.$refs.selectRoleDialog.close();
				// }
				this.$refs.selectRoleDialog.close();
			},
			/**返回的应用详情/服务详情被包在了对象的app和groupVo里，需要重组一下*/
			getChildList(list, keyword) {
				let res = [];
				list.map(item => {
					res.push(item[keyword])
				})
				console.log('getChildList', res)
				return res
			},
			//获取首页组件
			getHomeDetail() {
			  var self = this;
			  self.setData({
			  	isLoading: true
			  });
			  // uni.showLoading({
			  //   title: '加载中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.getHomeCompoments();
			  var getCompoments = wxRequest.getRequest(url, data);
			  getCompoments.then(response => {
				console.log('getCompoments', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let resDataList = resData.data;
						self.setData({
							componentsList: resDataList
						}, ()=> {
							console.log('componentsList', this.componentsList)
						})
						if(self.outLinkUrl) {
							console.log('self.outLinkUrl', self.outLinkUrl)
							uni.navigateTo({
								url: self.outLinkUrl,
								success: (res) => {
									// 页面跳转成功，清掉outLinkUrl，避免返回该页面仍跳转至传递过来的页面
									self.setData({
										outLinkUrl: null
									}); 
								},
								fail: (err) => {
									console.error('页面跳转失败', err);
								}
							});
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
				uni.hideLoading();
				self.setData({
				  showerror: "block",
				  floatDisplay: "block"
				});
			  }).finally(function () {
				  uni.hideLoading();
				  uni.stopPullDownRefresh();
				  self.setData({
				  	isLoading: false
				  });
				// setTimeout(function () {
				//   uni.hideLoading();
				// }, 1000);
			  });
			},
			onSelectRole(item) {
				this.switchRole(item);
			},
			openSelectRole(){
				this.getUserRoleList();
				this.$refs.selectRoleDialog.open();
			},
			getUserRoleList(roleKey) {
				var self = this;
				// uni.showLoading({
				//   title: '查询中',
				//   mask: true
				// });
				let url = Api.getUserRoleList();
				var getUserRoleListRequest = wxRequest.postRequest(url);
				getUserRoleListRequest.then(response => {
				  if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						self.setData({
							dataList: resData.data
						})
						if(roleKey) {
							let roleObj = resData.data.find(i => i.id == roleKey);
							console.log('roleObj', roleObj)
							if(roleObj) {
								let data = {
									roleKey: roleObj.id,
									roleName: roleObj.name
								}
								this.$store.commit('setUser', data);
							}
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
				  } else if (response.statusCode === 404) {
				    // console.log('登录失败');
				  }
				}).catch(function () {
				  uni.hideLoading();
				  self.setData({
				    showerror: "block",
				    floatDisplay: "block"
				  });
				}).finally(function () {
					uni.hideLoading();
				});
			},
			switchRole(item) {
				var self = this;
				uni.showLoading({
				  title: '切换中',
				  mask: true
				});
				let url = Api.switchRole();
				var postSwitchRole = wxRequest.postRequest(url +'/'+item.id);
				postSwitchRole.then(response => {
				  if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let data = {
							roleKey: item.id,
							roleName: item.name
						}
						this.$store.commit('setUser', data);
						this.dialogConfirm();
						this.getHomeDetail();
						if(self.outLinkUrl) { // 如果传递了url，跳转至该url
							uni.navigateTo({
								url: self.outLinkUrl,
								success: (res) => {
									// 页面跳转成功，清掉outLinkUrl，避免返回该页面切换角色后仍跳转外传递过来的页面
									self.setData({
										outLinkUrl: null
									}); 
								},
								fail: (err) => {
									console.error('页面跳转失败', err);
								}
							});
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}	
				  } else if (response.statusCode === 404) {
				    // console.log('登录失败');
				  }
				}).catch(function () {
				  uni.hideLoading();
				  self.setData({
				    showerror: "block",
				    floatDisplay: "block"
				  });
				}).finally(function () {
				  setTimeout(function () {
				    uni.hideLoading();
				  }, 1000);
				});
			},
			dialogConfirm() {
				this.$refs.selectRoleDialog.close();
			},
			initFW() { //初始化
				floatBall.init({
						ballSize: this.ballSize, //悬浮球大小
						menuSize: this.menuSize, //悬浮球菜单范围大小
						menuItemSize: this.menuItemSize, //菜单项大小
						ballIcon: this.ballIcon, //悬浮球图片
						ballMenus: [{
							icon: 'ic_weibo', //菜单项图片
							tag: 'weibo' //菜单项标识
						}, {
							icon: 'ic_weixin',
							tag: 'weixin'
						}, {
							icon: 'ic_email',
							tag: 'email'
						}, {
							icon: 'ic_qq',
							tag: 'qq'
						}, {
							icon: 'ic_weixin',
							tag: 'weixin'
						}]
					},
					(res) => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 3000
						})
					});
			},
			showFW() { //展示
				floatBall.show((res) => {
					console.log(res);
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					})
				});
			},
			hideFW() { //隐藏
				floatBall.hide(
					(res) => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 3000
						})
					});
			},
			formSubmit: function(e) {
				var url = '../list/list';
				var key = '';

				if (e.currentTarget.id == "search-input") {
					key = e.detail.value;
				} else {
					key = e.detail.value.input;
				}

				if (key != '') {
					url = url + '?search=' + key;
					uni.navigateTo({
						url: url
					});
				} else {
					uni.showModal({
						title: '提示',
						content: '请输入内容',
						showCancel: false
					});
				}
			},
			// 自定义分享朋友圈
			onShareTimeline: function() {
				return {
					title: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',
					path: 'pages/index/index'
				};
			},

			//加载分页
			loadMore: function(e) {
				console.log('loadMore');
				var self = this;

				if (!self.isLastPage) {
					self.setData({
						page: self.page + 1
					});
					// this.fetchPostsData(self);
				} else {
					uni.showToast({
						title: '没有更多内容',
						mask: false,
						duration: 1000
					});
				}
			}
		}
	};
</script>
<style lang="scss">
	.color-white {
		color: #ffffff;
	}
	.index-top {
		position: relative;
	}

	.indexbg {
		position: absolute;
		top: -30rpx;
		width: 750rpx;
		height: 500rpx;
		z-index: -1;

	}

	/* 搜索 */
	.ui-search-bar {
		position: relative;
		display: flex;
		align-items: center;
		height: 100rpx;
		margin-bottom: 15rpx;
	}

	.ui-search-bar .ui-search-action+.ui-search-form {
		margin-left: 20rpx;
	}

	.ui-search-bar .ui-search-form {
		position: relative;
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		color: #333;
		height: 68rpx;
		font-size: 24rpx;


	}

	.ui-search-icon,
	.ui-search-close {
		height: 68rpx;
		line-height: 68rpx;
	}

	.ui-search-icon {
		margin-left: 25rpx;
		margin-right: 20rpx;
	}

	.ui-search-input {
		position: relative;
		flex: 1;
		height: 68rpx;


	}

	.ui-search-text-left {
		text-align: left;
	}

	.ui-search-text-center {
		text-align: center;
	}

	.ui-search-text-right {
		text-align: right;
	}

	.input-placeholder {
		color: inherit;
		opacity: 0.5;
	}

	.ui-search-close {
		margin-left: 20rpx;
		margin-right: 25rpx;
	}

	.ui-search-bar .ui-search-form+.ui-search-action {
		margin-left: 20rpx;
	}




	/*模块标题样式 */
	.common-subtitle {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 130rpx;
		padding: 0 24rpx;
		background: #fff;
	}

	.common-subtitle-left {
		font-size: 34rpx;
		color: #333;
		font-weight: 500;
	}

	.common-subtitle-right {
		font-size: 26rpx;
		font-weight: 400;
		color: #959595;
		margin-right: 6rpx;
	}

	/* 精选栏目菜单导航 */


	.selected-nav-list {
		display: flex;
		padding: 0 24rpx 40rpx;

		justify-content: space-between;
	}

	.selected-nav-item {
		margin-right: 16rpx;
		text-align: center;
	}

	.selected-nav-item image {
		width: 156rpx;
		height: 156rpx;
		background: #f5f7f7;
	}

	.selected-nav-item text {
		line-height: 26rpx;
		font-size: 26rpx;
		color: #333;
	}

	.list-item-ad {
		margin: 24rpx;

		overflow: hidden;
		border-radius: var(--radius);
	}

	/* 引导添加到我的小程序 */
	.addMyMiniapp {
		color: #333;
		line-height: 1.2;
		background: #fff;
		padding: 24rpx 24rpx;
		position: fixed;
		top: 20rpx;
		right: 24rpx;
		z-index: 999;
		box-shadow: 0 16rpx 30rpx -12rpx rgba(88, 88, 88, 0.2);
	}

	.addMyMiniapp>view {
		font-size: 24rpx;
		font-weight: 500;
	}

	.addMyMiniapp>text {
		font-size: 20rpx;
		font-weight: 200;
	}

	.addMyMiniapp::before {
		content: '';
		width: 20rpx;
		height: 20rpx;
		background: #fff;
		transform: rotate(45deg);
		position: absolute;
		right: 118rpx;
		top: -10rpx;
	}
</style>


<style lang="scss">
	/* .ui-grid {
		margin: 12rpx;
	} */
	.ui-item .ui-BG-card {
		margin: 12rpx;
		padding: 0;
		overflow: hidden;
	}
	.icons-list {
		display: flex;
		align-items: center;
	}
	.icons-list .uni-icons,.icons-list text, .icons-list .i-icon {
		padding-right: 40rpx;
	}
	.icons-list text:last-child {
		padding-right: 28rpx;
	}

	/* 
	.ui-item {

		text-align: center;
		position: relative;
	}


	.ui-card {
		margin: 12rpx !important;
	} */

	.ui-card-bg {
		/* height: 160rpx; */
		position: relative;
		background-size: cover;
		background-position: center;
		overflow: hidden;
		filter: saturate(160%) blur(22px);

	}

	/* .ui-card-image {
		
		position: absolute;
		width: 120rpx;
		height: 120rpx;
		display: inline-block;
		top: 100rpx;
		left: 121.5rpx;
		background-color: #fff;
		border-radius: 100%;
		border: 1px solid #fff;
	} */

	.ui-card-sub {
		position: absolute;
		right: 10px;
		top: 10px;
		border: solid 1px rgba(255, 255, 255, .5);
		z-index: 1;
		color: #fff;
		padding: 2px 6px;
		background-color: rgba(0, 0, 0, 0.1);

	}

	.ui-card-sub:hover {
		background-color: rgba(0, 0, 0, 0.4);
	}

	.ui-card-content {
		padding: 24rpx 12rpx 24rpx;
		overflow: hidden;
		background-color: #fff;
		z-index: 9;
		height: 236rpx;
	}

	.ui-card-content .dec {
		margin-top: 10rpx;

	}
	::v-deep .uni-dialog-content {
		flex-direction: column;
		// max-height: 200px;
		overflow: hidden;
		overflow-y: scroll;
		padding: 0 4px;
	}
	.uni-dialog-content uni-view {
		line-height: 32px;
		color: var(--ui-TC-6);
	}
	.uni-popup {
		z-index: 3000;
	}
	::v-deep .uni-dialog-title {
		padding-top: 16px;
		padding-bottom: 10px;
	}
	.scroll-view-style {
	  max-height: calc(100vh - 123px); /* 设置滚动区域的高度 */
	  background-color: #fff;
	  padding: 0;
	  border-radius: 12px;
	  text-align: center;
	  margin-bottom: 10px;
	}
	.uni-popup-dialog {
		width: 54vw;
		border-radius: 8px;
	}
	
</style>