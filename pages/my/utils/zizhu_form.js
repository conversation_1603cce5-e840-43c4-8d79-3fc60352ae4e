import {Notify} from 'vant';
import {fieldCheck} from "@/utils/common/common_form";

function onNotifyError(message) {
    Notify({type: 'danger', message: message});
}

export function onFieldCheck3(field, value, files) {
    return onFieldCheck(field, value, files && files.length > 0)
}

export function onFieldCheck(field, value, hasFile) {
    console.log("onFieldCheck", field, value, hasFile);
    return fieldCheck(field, value, hasFile);
}

export function formCheck(fields, obj, fileMap) {
    if (fields) {
        for (const field of fields) {
            let en = field.prop;
			console.log('formCheck', fileMap.get('bz57'));
            const t = onFieldCheck3(field, obj[en], fileMap.get(en));
            if (t) {
                onNotifyError('[' + field.label + '] '+t);
                return false;
            }
        }
        return true;
    }
    return false;
}

