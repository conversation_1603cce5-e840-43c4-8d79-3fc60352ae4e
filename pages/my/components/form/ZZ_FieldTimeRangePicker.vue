<template>
    <div style="border-bottom: 1px solid #ebedf0 !important;">
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="valueShow"
                   :required="required"
                   :placeholder="placeholder">
				<template #input>
					<view class="uni-date">
						<view class="uni-date-editor">
							<slot>
								<view class="uni-date-editor--x"
									:class="{'uni-date-editor--x__disabled': disabled,'uni-date-x--border': border}">
									<view class="uni-date-x uni-date-range">
										<uni-icons class="icon-calendar" type="calendar" color="#c0c4cc" size="22"></uni-icons>
										<template v-if="field.type == 'timerange'">
											<view class="uni-date__x-input text-center" @click="onclick('startTime', '开始时间')">
												{{ startTime || '开始时间' }}
											</view>
											<view class="range-separator">-</view>
																
											<view class="uni-date__x-input text-center"  @click="onclick('endTime', '结束时间')">
												{{ endTime || '结束时间' }}
											</view>
										</template>
										<template v-else>
											<view class="uni-date__x-input text-center" @click="onclick('startTime', '选择时间')">
												{{ startTime || '选择时间' }}
											</view>
										</template>
									</view>
									<view v-if="showClearIcon" class="uni-date__icon-clear" @click.stop="clear">
										<uni-icons type="clear" color="#c0c4cc" size="22"></uni-icons>
									</view>
								</view>
							</slot>
						</view>
					</view>
				</template>
		</van-field>
        <ZZ_TimerangePicker :value="valueShow" :title="title" :timeKey="timeKey" v-on:update:value="onInput($event)" :show.sync="selectorShow"
                       :type="type"></ZZ_TimerangePicker>
		
    </div>
</template>

<script>
    import ZZ_TimerangePicker from "./ZZ_TimerangePicker.vue";
    import {dateFormat_YMD, diyDateFormat} from "@/pages/my/utils/my_moment";

    export default {
        components: {
            ZZ_TimerangePicker
        },
        name: "ZZ_FieldSelector",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            label: String,
            selfeltype: String,
            type: {
                type: String,
                default() {
                    return 'time'
                }
            },
			border: {
				type: [Boolean],
				default: false
			},
			disabled: {
				type: [Boolean],
				default: false
			},
			clearIcon: {
				type: [Boolean],
				default: true
			},
        },
        data() {
            return {
                selectorShow: false,
				title: "",
				startTime: "",
				endTime: "",
				valueShow: "",
				timeKey: "startTime",
				timeRange: [], // 用于存储选择的时间范围
            }
        },
		watch: {
			value(newValue) {
				console.log('11111111', newValue)
			    if (newValue) {
					if(this.field.type == 'timerange') {
						let list = newValue.split(',');
						if(list.length > 0) {
							this.startTime = list[0];
							this.endTime = list[1];
						}
					} else {
						this.startTime = newValue;
					}
					console.log('22222222', this.startTime, this.endTime)
			    } else {
					this.startTime = "";
					this.endTime = "";
				}
			},
		},
        computed: {
			showClearIcon() {
				return this.clearIcon && !this.disabled && (this.startTime || this.endTime)
			},
            selfEltype() {
                let eltype = ""
                switch (this.selfeltype) {
                    case 'yyyy-MM-dd HH:mm:ss':
                        eltype = 'YYYY-MM-DD HH:mm:ss';
                        break;
                    case 'yyyy-MM-dd HH:mm':
                        eltype = 'YYYY-MM-DD HH:mm';
                        break;
                    case 'yyyy-MM-dd':
                        eltype = 'YYYY-MM-DD';
                        break;
                    case 'yyyy-MM':
                        eltype = 'YYYY-MM'
                        break;
                    case 'HH:mm:ss':
                        eltype = 'HH:mm:ss';
                        break;
                    case 'HH:mm':
                        eltype = 'HH:mm';
                        break;
                    default:
                        eltype = 'YYYY-MM-DD'
                }
                return eltype
            }
        },
        methods: {
            onInput(obj) {
				if(this.field.type == 'time' || this.field.type == 'timerange'){
					obj.value = obj.value + ":00";
				}
				console.log('fieldTimeRange' , obj)
				let value = obj.value;
				if(this.field.type == 'timerange') {
					value = this.getValue(obj);
				}
				this.$emit("input", value);
            },
			clear() {
				this.setData({
					startTime: "",
					endTime: ""
				});
				this.$emit("input", "");
			},
			onclick(timeKey, title) {
				console.log('点击了', timeKey)
				if(this.disabled) {
					return;
				}
				this.selectorShow = true
				this.valueShow = this[timeKey];
				this.timeKey = timeKey;
				this.title = title;
				console.log('点击后', this.valueShow, this.title, this.startTime, this.timeKey);
			},
			// 获取修改开始时间/结束时间后的vaule整个字符串
			getValue(obj) {
				if(obj.timeKey == 'startTime') {
				  return this.getTimeRangeStr(obj.value, this.endTime)
				} else {
				  return this.getTimeRangeStr(this.startTime, obj.value)
				}
			},
			// 比较time1和time2的大小，将小的拼放在前面，大的放在后面，中间拼上,
			getTimeRangeStr(time1, time2) {
				let timeRangeStr =  `${time1},${time2}`;
				if(time1 && time2) {
					if(Number(time1.replace(/:/g, "")) > Number(time2.replace(/:/g, ""))) {
						timeRangeStr = `${time2},${time1}`;
					}
				}
				console.log('timeRangeStr', timeRangeStr)
				return timeRangeStr;
			}
        }
    }
</script>

<style scoped>
::v-deep .uni-date__x-input, ::v-deep .uni-date-x .range-separator {
	height: 24px !important;
	line-height: 24px !important;
}
::v-deep .uni-date-x--border {
	border: none;
}
.uni-date {
		width: 100%;
		flex: 1;
	}

	.uni-date-x {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-radius: 4px;
		background-color: #fff;
		color: #666;
		font-size: 14px;
		flex: 1;

		.icon-calendar {
			padding-left: 3px;
		}

		.range-separator {
			height: 35px;
			/* #ifndef MP */
			padding: 0 2px;
			/* #endif */
			line-height: 35px;
		}
	}

	.uni-date-x--border {
		box-sizing: border-box;
		border-radius: 4px;
		border: 1px solid #e5e5e5;
	}

	.uni-date-editor--x {
		display: flex;
		align-items: center;
		position: relative;
	}

	.uni-date-editor--x .uni-date__icon-clear {
		padding-right: 3px;
		display: flex;
		align-items: center;
		/* #ifdef H5 */
		cursor: pointer;
		/* #endif */
	}

	.uni-date__x-input {
		width: auto;
		height: 35px;
		/* #ifndef MP */
		padding-left: 5px;
		/* #endif */
		position: relative;
		flex: 1;
		line-height: 35px;
		font-size: 14px;
		overflow: hidden;
	}

	.text-center {
		text-align: center;
	}

	.uni-date__input {
		height: 40px;
		width: 100%;
		line-height: 40px;
		font-size: 14px;
	}

	.uni-date-range__input {
		text-align: center;
		max-width: 142px;
	}

	.uni-date-picker__container {
		position: relative;
	}

	.uni-date-mask--pc {
		position: fixed;
		bottom: 0px;
		top: 0px;
		left: 0px;
		right: 0px;
		background-color: rgba(0, 0, 0, 0);
		transition-duration: 0.3s;
		z-index: 996;
	}

	.uni-date-single--x {
		background-color: #fff;
		position: absolute;
		top: 0;
		z-index: 999;
		border: 1px solid #EBEEF5;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		border-radius: 4px;
	}

	.uni-date-range--x {
		background-color: #fff;
		position: absolute;
		top: 0;
		z-index: 999;
		border: 1px solid #EBEEF5;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		border-radius: 4px;
	}

	.uni-date-editor--x__disabled {
		opacity: 0.4;
		cursor: default;
	}

	.uni-date-editor--logo {
		width: 16px;
		height: 16px;
		vertical-align: middle;
	}

	/* 添加时间 */
	.popup-x-header {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	.popup-x-header--datetime {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex: 1;
	}

	.popup-x-body {
		display: flex;
	}

	.popup-x-footer {
		padding: 0 15px;
		border-top-color: #F1F1F1;
		border-top-style: solid;
		border-top-width: 1px;
		line-height: 40px;
		text-align: right;
		color: #666;
	}

	.popup-x-footer text:hover {
		color: $uni-primary;
		cursor: pointer;
		opacity: 0.8;
	}

	.popup-x-footer .confirm-text {
		margin-left: 20px;
		color: $uni-primary;
	}

	.uni-date-changed {
		text-align: center;
		color: #333;
		border-bottom-color: #F1F1F1;
		border-bottom-style: solid;
		border-bottom-width: 1px;
	}

	.uni-date-changed--time text {
		height: 50px;
		line-height: 50px;
	}

	.uni-date-changed .uni-date-changed--time {
		flex: 1;
	}

	.uni-date-changed--time-date {
		color: #333;
		opacity: 0.6;
	}

	.mr-50 {
		margin-right: 50px;
	}

	/* picker 弹出层通用的指示小三角, todo：扩展至上下左右方向定位 */
	.uni-popper__arrow,
	.uni-popper__arrow::after {
		position: absolute;
		display: block;
		width: 0;
		height: 0;
		border: 6px solid transparent;
		border-top-width: 0;
	}

	.uni-popper__arrow {
		filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
		top: -6px;
		left: 10%;
		margin-right: 3px;
		border-bottom-color: #EBEEF5;
	}

	.uni-popper__arrow::after {
		content: " ";
		top: 1px;
		margin-left: -6px;
		border-bottom-color: #fff;
	}
</style>
