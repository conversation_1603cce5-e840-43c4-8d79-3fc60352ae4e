<template>
	<van-slider style="padding: 12px 14px;background-color: #fff; width: 88%;"
		bar-height="2px"
		range
		v-model="sliderValue"
		inactive-color="gray"
		:disabled="readonly"
		@change="onSliderChange"
	>
		<template #left-button>
			<div class="custom-button">{{ sliderValue[0] }}</div>
		</template>
		<template #right-button>
			<div class="custom-button">{{ sliderValue[1] }}</div>
		</template>
	</van-slider>
</template>

<script>
	export default {
		props: {
			value: String,
			readonly: Boolean
		},
		data() {
			return {
				sliderValue: [0, 100]
			}
		},
		watch: {
			value(newVal) {
			    if (newVal) {
					this.sliderValue = newVal.split(',');
			    }
			}
		},
		methods: {
			onSliderChange(value) {
				this.$emit("input", value.join(','))
			},
		}
	}
</script>

<style>
	.custom-button {
	    width: 22px;
		height: 22px;
	    color: #fff;
	    font-size: 10px;
	    line-height: 23px;
	    text-align: center;
	    background-color: #1989fa;
	    border-radius: 100px;
	}
</style>