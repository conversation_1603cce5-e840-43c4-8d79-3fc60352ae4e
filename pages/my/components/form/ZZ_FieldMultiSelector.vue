<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="value"
                   :required="required"
                   @click="readonly ? selectorShow = false : selectorShow = true"
                   readonly
                   :type="isShowSelect?'textarea':'text'"
                   :placeholder="placeholder"
                   :is-link="isShowSelect?true:false">
            <template v-if="field && field.typeKey && (field.typeKey == 'checkbox' || field.typeKey == 'checkboxButton')" slot="input">
                <!--                direction="horizontal"-->
                <van-checkbox-group :value="valueShow" v-on:input="onInput"
                                    :direction="directionMark==='none'?'':'horizontal'">
                    <van-checkbox :disabled="readonly" :key="option + index" v-for="(option,index) in columnsName" :name="option" shape="square">
                        {{option}}
                    </van-checkbox>
                </van-checkbox-group>
            </template>
			<template v-else-if="field && field.typeKey && field.typeKey == 'multipleSelect'" slot="input">
				{{ initModel[field.prop]  ? initModel[field.prop] : placeholder}}
			</template>
        </van-field>
		<ZZ_MultiSelector
		  v-if="field && field.typeKey && field.typeKey == 'multipleSelect'"
		  :value="valueShow"
		  v-on:input="onInput($event)"
		  :columns="columnsName" 
		  :show.sync="selectorShow">
		</ZZ_MultiSelector>
        
    </div>
</template>

<script>
	var Api = require('../../api/index.js');
	var wxRequest = require('@/utils/wxRequest.js');
    import ZZ_MultiSelector from "./ZZ_MultiSelector";

    export default {
        components: {
            ZZ_MultiSelector
        },
        name: "ZZ_FieldMultiSelector",
        props: {
            field: Object,
            directionMark: String,
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
			props: Object,
			initModel: Object,
			readonly: Boolean,
        },
        data() {
            return {
                selectorShow: false,
				options: []
            }
        },
		watch: {
			value(newValue) {
				console.log('多选 value', newValue)
				if(newValue) {
					// this.
				}
			}
		},
        computed: {
            isShowSelect() {
                return this.options && this.options.length > 5;
            },
            valueShow() {
				console.log('computed 11', this.value)
                if (this.value) {
                    return this.value.split(',');
                }
                return [];
            },
			// 下拉name
			columnsName() {
				let res = [];
				this.options.map(item => {
					res.push(item.text)
				})
				return res;
			},
			// 下拉code
			columnsId() {
				return this.options.map(item => item.value)
			}
        },
        methods: {
			// 获取options
			getOptions() {
			  var self = this;
			  // self.setData({
			  // 	isLoading: true
			  // });
			  // uni.showLoading({
			  //   title: '加载中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.codeCommonQuery();
				 url = url + '?codeType=' + self.props.code;
				 if(self.props.dicQueryParams && self.props.dicQueryParams.dictFieldUrl) {
					 url = url.split('/api')[0] + '/api' + self.props.dicQueryParams.dictFieldUrl;
					 if(self.props.dicQueryParams.params) {
						let queryStr = this.getObjQueryStr(self.props.dicQueryParams.params);
						url = url + "?" + queryStr;
					 }
				 }
			  var getcodeCommonQuery = wxRequest.getRequest(url, data);
			  getcodeCommonQuery.then(response => {
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let resDataList = resData.data;
						let options = [];
						let props = self.props;
						resDataList.forEach(item => {
							let value, text;
							// value 和 text 都是getValType
							if(self.props.dicQueryParams && self.props.dicQueryParams.getValType) {
							  // console.log('有', self.props.dicQueryParams.getValType)
							  value = item[self.props.dicQueryParams.getValType];
							  text = item[self.props.dicQueryParams.getValType];
							} else if(self.props.dicQueryParams && self.props.dicQueryParams.textField && self.props.dicQueryParams.valueField) {
								// value 和 text取设置的字段
								// console.log('1111')
								value = item[self.props.dicQueryParams.valueField];
								text = item[self.props.dicQueryParams.textField];
							} else {
								value = item.name;
								text = item.name;
							}
							options.push({
								value,
								text
							})
							// options.push(text)
						})
						self.setData({
							options: options
						})
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
				uni.hideLoading();
				self.setData({
				  showerror: "block",
				  floatDisplay: "block"
				});
			  }).finally(function () {
				  uni.hideLoading();
			  });
			},
            onInput(value) {
				console.log('value', value)
                this.$emit("input", value.join(','));
            },
			getObjQueryStr(obj) {
				let initModel = this.initModel;
				let str = "";
				Object.keys(obj).forEach(key => {
					str = str + `${key}=${initModel[key]}`; 
				})
				return str;
			}
		},
		created() {
			// this.queryCode();
			this.getOptions();
		}
    }
</script>

<style>
    ::v-deep .van-checkbox__label {
        margin-left: 10px;
        color: #323233;
        line-height: 25px !important;
        width: 100% !important;
    }
</style>
