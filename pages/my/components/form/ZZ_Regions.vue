<template>
    <!-- <div> -->
        <!-- <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom">
            <van-cell-group style="margin-bottom: 50px;">
                <van-cell v-for="item in results"
                          :key="item.name"
                          @click="onItemClick(item)">
                    <van-checkbox slot="title" :value="item.checked">{{item.name}}</van-checkbox>
                </van-cell>
            </van-cell-group>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
            </div>
        </van-popup> -->
		<van-popup :value="show" v-on:input="onChangeShow($event)" position="bottom" :style="{ height: '100%', width: '100%'}">
		            <van-field label="省/市/区"
		                       placeholder="请选择省/市/区"
		                       @click="clickShowVanArea"
		                       :value="setDefaultAddress"
		                       :required="setAreaRequest"
		                       readonly
		                       is-link/>
		            <van-field label="详细地址"
		                       placeholder="请输入详细地址"
		                       :required="setAreaRequest"
		                       :value="setDefaultAddressDetail"
		                       type="textarea"
		                       maxlength="255"
		                       rows="4"
		                       autosize
		                       show-word-limit
		                       v-on:input="onInputAddressDetail($event)"/>
		            <van-popup v-model="showDetailAreaPopup" position="bottom" :style="{ height: '50%' }"
		                       :close-on-click-overlay="false">
		                <van-area :area-list="columns"
		                          :columns-placeholder="['请选择', '请选择', '请选择']"
		                          :value="setDefaultCode"
		                          ref="myArea"
		                          @change="onAreaChange"
		                          @confirm="onAreaConfirm"
		                          @cancel="onAreaCancel"/>
		            </van-popup>
		            <!-- <div class="van-submit-bar">
		                <van-button class="van-submit-bar-button"
		                            type="info"
		                            size="large"
		                            @click="handleAreaSubmit">确定
		                </van-button>
		                <van-button class="van-submit-bar-button vanButton"
		                            type="info"
		                            size="large"
		                            @click="showAreaPopup = false">取消
		                </van-button>
		            </div> -->
					<div class="multi-s">
					        <van-button class="multi-s-button" type="info" @click="handleAreaSubmit">确定</van-button>
					        <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
					    </div>
					</van-popup>
		        </van-popup>
    <!-- </div> -->
</template>

<script>
    export default {
        name: "ZZ_MultiSelector",
        props: {
            value: String,
            show: Boolean,
			defaultCode: String,
            columns: Object,
			field: Object,
        },
        data() {
            return {
                results: [],
				showDetailAreaPopup: false,
				setDefaultAddress: null,
				setDefaultAddressDetail: null,
				setAreaRequest: false,
				setDefaultCode: null,
            }
        },
        watch: {
            show(newVal) {
				console.log('watch shwo', newVal, this.columns)
                if (newVal && this.value) {
                    this.initResults();
                }
            }
        },
        computed: {
            columnsShow() {
                if (this.columns) {
                    return this.columns;
                }
                return [];
            }
        },
        methods: {
			onInputAddressDetail(value) {
                this.setDefaultAddressDetail = value;
            },
			clickShowVanArea() {
				this.showDetailAreaPopup = true;
			},
			handleAreaSubmit() {
				if (!this.setDefaultAddress) {
					this.$toast("请选择省/市/区");
					return false;
				}
				if (!this.setDefaultAddressDetail) {
					this.$toast("请输入详细地址");
					return false;
				}
				
				let tvalue = null;
				if (this.setDefaultAddress && this.setDefaultAddress.split(",").length >= 2) {
					tvalue = this.setDefaultAddress + (this.setDefaultAddressDetail ? "," + this.setDefaultAddressDetail : '');
				} else {
					tvalue = this.setDefaultAddressStr + (this.setDefaultAddressDetail ? "," + this.setDefaultAddressDetail : '');
				}
				// this.onInput(this.currentField, tvalue);
				console.log('确定', tvalue)
				this.$emit('input', tvalue);
				this.onCancel();

				this.setDefaultAddress = null;
				this.setDefaultAddressDetail = null;

			},
			//value=0改变省，1改变市，2改变区
			onAreaChange(picker) {
				let val = picker.getValues();
				let areaName = "";
				if (val[0] && val[1] && val[2]) {
					for (var i = 0; i < val.length; i++) {
						areaName = areaName + (i == 0 ? "" : "/") + val[i].name;
					}
					this.carmodel = areaName;
					this.setDefaultAddress = areaName;
				}
			},
			//确定选择城市
			onAreaConfirm(val) {
				console.log('onAreaConfirm', val, this.setDefaultCode)
				if (val[0].name === '海外') {
					if (val[1] && val[1].name) {
						this.showDetailAreaPopup = false; //关闭弹框
						this.province = val[0].name;
						this.areaCode = val[0].code;
						this.city = val[1].name;
						let areaStr = val[0].name + "," + val[1].name;
						this.carmodel = areaStr;
						this.setDefaultAddress = areaStr;
					} else {
						this.$toast("请选择地区");
					}
				} else {
					if (val[2] && val[2].name) {
						this.showDetailAreaPopup = false; //关闭弹框
						this.province = val[0].name;
						this.areaCode = val[0].code;
						this.city = val[1].name;
						this.county = val[2].name;
						let areaStr = val[0].name + "," + val[1].name + "," + val[2].name;
						this.carmodel = areaStr;
						this.setDefaultAddress = areaStr;
					} else {
						this.$toast("请选择地区");
					}
				}
			},
			//取消选中城市
			onAreaCancel() {
				// this.onInput(this.currentField, '');
				this.$emit('input', '');
				this.setDefaultAddressDetail = '';
				this.setDefaultAddress = '';
				this.setDefaultAddressStr = '';
				this.showDetailAreaPopup = false;
				// this.onChangeShow(false);
				this.$refs.myArea.reset(); // 重置城市列表
			},
            initResults() {
                let value = this.value;
				if(this.field.type == 'regions' || this.field.type == 'citys') { // 只取前三个
					let list = value.split(',')
					if(list.length > 3) {
						value = list.slice(0, 3).join(',');
					}
				}
				console.log('initResults value', value, this.field)
				this.setDefaultAddress = value;
				this.setDefaultCode = this.defaultCode;
				this.setDefaultAddressDetail =  '';
				// console.log('this.results', arr)
            },
            onItemClick(model) {
                model.checked = !model.checked;
            },
            onConfirm() {
                let arr = [];
                this.results.forEach(item => {
                    if (item.checked) {
                        arr.push(item.name);
                    }
                });
                this.$emit('input', arr);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
				console.log('onCancel')
            },
            onChangeShow(value) {
				console.log('onChangeShow')
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;

        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 100%;
        background-color: #fff;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        /*background: #00a5ec;*/
        font-size: 16px;
    }
</style>
