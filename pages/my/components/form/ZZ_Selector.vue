<template>
    <div>
        <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom"
                   get-container="body">
            <van-picker show-toolbar :columns="columns" :value-key="valueKey" :loading="!columns"
                        @cancel="onCancel()" @confirm="onConfirm"/>
        </van-popup>
    </div>
</template>

<script>
    export default {
        name: "ZZ_Selector",
        props: {
            show: Boolean,
            columns: {
                type: Array,
                default() {
                    return [];
                }
            },
            valueKey: {
                type: String,
                default() {
                    return 'text'
                }
            }
        },
        methods: {
            onConfirm(value) {
				console.log('onConfirm', value)
                this.$emit('update:value', value);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>

</style>
