<template>
	<van-slider style="padding: 11px 14px;background-color: #fff; width: 88%;"
		bar-height="2px" 
		v-model="sliderValue"
		inactive-color="gray"
		:disabled="readonly"
		@change="onSliderChange"
	>
		<template #button>
			<div class="custom-button">{{ sliderValue }}</div>
		</template>
	</van-slider>
</template>

<script>
	export default {
		props: {
			value: {
				type: [Number, String],
				default() {
					return 0
				}
			},
			readonly: Boolean
		},
		data() {
			return {
				sliderValue: 0
			}
		},
		watch: {
			value(newVal) {
			    if (newVal) {
					this.sliderValue = newVal;
			    } else {
					this.sliderValue = 0;
				}
			}
		},
		methods: {
			onSliderChange(value) {
				this.$emit("input", value)
			},
		}
	}
</script>

<style>
	.custom-button {
	    width: 22px;
		height: 22px;
	    color: #fff;
	    font-size: 10px;
	    line-height: 23px;
	    text-align: center;
	    background-color: #1989fa;
	    border-radius: 100px;
	}
</style>