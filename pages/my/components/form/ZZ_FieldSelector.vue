<template>
	<van-field :error="field.style?true:false"
			   :error-message="errorMessage"
			   :label="label"
			   :value="value"
			   :required="required"
			   @click="selectorShow = true"
			   readonly
			   :placeholder="placeholder">
		<template v-if="readonly">
			<van-radio-group :value="value"></van-radio-group>
		</template>
		<template v-else>
			<!-- <template v-if="!isShowSelect" slot="input"> -->
			<template v-if="field && field.typeKey && (field.typeKey == 'radio' || field.typeKey == 'radioButton')" slot="input">
				<!-- direction="horizontal" 一行排列  单选 女子学院-->
				<van-radio-group :value="value" v-on:input="onInput">
					<van-radio 
							   :key="index"
							   v-for="(item, index) in columnsName"
							   :name="item"
					>{{item}}
					</van-radio>
				</van-radio-group>
			</template>
			<template v-else-if="field && field.typeKey && field.typeKey == 'select'" slot="input">
				{{value?value:placeholder}}
				<ZZ_Selector 
							 v-on:update:value="onInput($event)"
							 :columns="columnsName"
							 :show.sync="selectorShow"></ZZ_Selector>
			</template>
		</template>
	</van-field>
</template>

<script>
	var Api = require('../../api/index.js');
	var wxRequest = require('@/utils/wxRequest.js');
	
    import ZZ_Selector from "./ZZ_Selector";
    // import {post} from "@/zizhu/utils/zizhu_http";
    // import {queryCodeNoLimit as qcnlAPI} from "@/zizhu/utils/zizhu_api";
    // import {queryCode as qcAPI} from "@/common/utils/common_api";

    export default {
        components: {ZZ_Selector},
        name: "ZZ_FieldSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            // options: Array,
			props: Object,
            field: Object,
			initModel: Object,
			readonly: Boolean,
        },
        data() {
            return {
				selfValue: "",
                selectorShow: false,
                radioShow: false,
                nowVal: "",
                oldVal: "",
                mark: 1,
                same: false,
				options: []
                // setOptions: [],
            };
        },
        computed: {
            isShowSelect() {
                return this.setOptions && this.setOptions.length > 0;
            },
            setOptions() {
                return this.options ? this.options : [];
            },
			// 下拉name
			columnsName() {
				let res = [];
				this.options.map(item => {
					res.push(item.text)
				})
				return res;
			},
			// 下拉code
			columnsId() {
				return this.options.map(item => item.value)
			}
        },
        methods: {
			// 获取options
			getOptions() {
			  var self = this;
			  // self.setData({
			  // 	isLoading: true
			  // });
			  // uni.showLoading({
			  //   title: '加载中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.codeCommonQuery();
				 url = url + '?codeType=' + self.props.code;
				 if(self.props.dicQueryParams && self.props.dicQueryParams.dictFieldUrl) {
					 url = url.split('/api')[0] + '/api' + self.props.dicQueryParams.dictFieldUrl;
					 if(self.props.dicQueryParams.params) {
						let queryStr = this.getObjQueryStr(self.props.dicQueryParams.params);
						url = url + "?" + queryStr;
						console.log('queryStr >>', queryStr)
					 }
				 }
			  var getcodeCommonQuery = wxRequest.getRequest(url, data);
			  getcodeCommonQuery.then(response => {
				// console.log('getcodeCommonQuery', response, self.props.dicQueryParams)
				// console.log('self.props', self.props, self.props.dicQueryParams.getValType)
				// console.log('getValType', self.props.dicQueryParams.getValType)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let resDataList = resData.data;
						let options = [];
						let props = self.props;
						resDataList.forEach(item => {
							// console.log('item', item)
							let value, text;
							// value 和 text 都是getValType
							if(self.props.dicQueryParams && self.props.dicQueryParams.getValType) {
							  // console.log('有', self.props.dicQueryParams.getValType)
							  value = item[self.props.dicQueryParams.getValType];
							  text = item[self.props.dicQueryParams.getValType];
							} else if(self.props.dicQueryParams && self.props.dicQueryParams.textField && self.props.dicQueryParams.valueField) {
								// value 和 text取设置的字段
								// console.log('1111')
								value = item[self.props.dicQueryParams.valueField];
								text = item[self.props.dicQueryParams.textField];
							} else {
								value = item.name;
								text = item.name;
							}
							options.push({
								value,
								text
							})
							// options.push(text)
						})
						console.log('options', options)
						self.setData({
							options: options
						})
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
				uni.hideLoading();
				self.setData({
				  showerror: "block",
				  floatDisplay: "block"
				});
			  }).finally(function () {
				  uni.hideLoading();
			  });
			},
            // queryCode() {
            //     let field = this.field;
            //     if (field && field.type === 'init') {
            //         if (field.el) {
            //             if (!field.yzgz) {
            //                 post(qcnlAPI, {code: field.en}).then(response => {
            //                     let rData = response.data;
            //                     if (rData) {
            //                         for (const x of rData) {
            //                             this.setOptions.push(x.name);
            //                         }
            //                     }
            //                 });
            //             }
            //         } else {
            //             let json = {}, code = '';
            //             let setUrl = null;
            //             //籍贯，校区,类型
            //             if (field.en === "jg" || field.en === "xqmc" || field.en === "zxslx") setUrl = '/syt/qywx/teacherinfo/queryjg.htm';
            //             //教师类型
            //             else if (field.en === "jslx") setUrl = "/syt/qywx/teacherinfo/queryjslx.htm";
            //             //期刊类别
            //             else if (field.en === "qklb") setUrl = "/syt/qywx/teacherinfo/queryqklbb.htm";
            //             //学生培养层次
            //             else if (field.en === "pyccb") setUrl = "/syt/qywx/teacherinfo/querypyccb.htm";
            //             else if (field.en === "dwmc") setUrl = "/syt/hdsb/querydwb.htm";
            //             else {
            //                 setUrl = qcAPI;
            //             }
            //             if (field.en === 'mzmc') code = "CodeMzb";
            //             // if (field.en === 'dwmc') code = "CodeDwb";
            //             if (field.en === 'zymc') code = "CodeZyb";
            //             if (field.en === 'pyccb' || field.en === "pyccmc") code = "CodePyccb";//培养层次
            //             if (field.en === 'xl') code = "CodeXlb";
            //             if (field.en === 'zzfdylb') code = "CodeZzfdylb";
            //             if (field.en === 'zyjszw') code = "CodeZyjs";
            //             if (field.en === 'xzzw') code = "CodeXzzw";
            //             if (field.en === 'zzmmmc') code = "CodeZzmmb";//政治面貌

            //             if (setUrl) {
            //                 json.code = code;
            //                 post(setUrl, json).then(response => {
            //                     let resData = "";
            //                     let rData = response.data;
            //                     resData = rData;
            //                     if (field.en === "jg") resData = rData.jgList;
            //                     if (field.en === "xqmc") resData = rData.xqList;
            //                     if (field.en === "zxslx") resData = rData.zxslxList;
            //                     if (resData) {
            //                         for (const x of resData) {
            //                             this.setOptions.push(x.name);
            //                         }
            //                     }
            //                 });
            //             }
            //         }
            //     }
            // },
            handle(index) {
				console.log('radioclick', index)
                // if (this.oldVal == this.nowVal) {
                //     this.nowVal = "";
                //     this.oldVal = "";
                //     this.same = true;
                //     this.onInput(this.nowVal);
                //     this.mark = 1;
                // } else {
                //     this.same = false;
                //     this.nowVal = this.setOptions[index];
                //     this.onInput(this.nowVal);
                //     this.oldVal = this.nowVal;
                // }
                // if (this.mark) {
                //     this.mark = 0;
                //     if (this.oldVal == "") {
                //         this.oldVal = this.setOptions[index];
                //         this.nowVal = this.setOptions[index];
                //     }
                // }
            },
            onInput(value) {
				console.log('radio', value)
                this.nowVal = value;

                if (this.same) {
                    this.nowVal = "";
                }
                this.$emit("input", this.nowVal);
            },
			getObjQueryStr(obj) {
				let initModel = this.initModel;
				let str = "";
				Object.keys(obj).forEach(key => {
					str = str + `${key}=${initModel[key]}`; 
				})
				return str;
			}
        },
        created() {
            // this.queryCode();
			// console.log('222', this.props)
			this.getOptions();
        }
    };
</script>

<style scoped>
	.van-radio{
		margin-bottom: 8px;
	}
	.van-radio:last-child{
		margin-bottom: 0px;
	}
</style>
