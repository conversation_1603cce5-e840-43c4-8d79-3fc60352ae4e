<template>
    <div>
        <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom">
			<van-cell-group style="margin-bottom: 50px;">
				<van-field v-model="selfValue" label="标签名称" required placeholder="请输入标签名称" />
			</van-cell-group>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    export default {
        name: "ZZ_MultiSelector",
        props: {
            value: String,
			index: Number,
            show: Boolean,
            columns: Array
        },
        data() {
            return {
				selfValue: ''
            }
        },
        watch: {
            show(newVal) {
				console.log('show1', this.index)
				if(this.value) { //修改的下标的值
					console.log('show2', this.index)
					this.setData({
						selfValue: this.value
					})
				}
            }
        },
        computed: {},
        methods: {
            onConfirm() {
				if(!this.selfValue) {
					uni.showToast({
						title: '请输入标签名称！',
						icon: 'none'
					});
					return;
				}
                this.$emit('input', {value: this.selfValue, index: this.index});
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
				this.setData({
					selfValue: ''
				})
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;

        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 100%;
        background-color: #fff;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        /*background: #00a5ec;*/
        font-size: 16px;
    }
</style>
