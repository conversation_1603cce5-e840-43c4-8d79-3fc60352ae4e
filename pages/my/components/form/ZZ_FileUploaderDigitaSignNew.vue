<template>
	<div class="wrap">
	    <van-popup :style="{ height: '100%' }"
	               :value="show"
	               v-on:input="onChangeShow($event)"
	               position="bottom">
	        <div v-if="wxts" style="text-align: left;margin: 10px 3px 0 10px;color: red;">*{{wxts}}</div>
	        <!-- <canvas class="app-sign-canvas" :id="domId" ref="myCanvas">
	            您的浏览器不支持canvas技术,请升级浏览器!
	        </canvas> -->
			<sign-canvas
			  class="sign-canvas"
			  ref="SignCanvas"
			  :options="options"
			  v-model="value"
			/>
	        <div style="text-align: center; line-height: 30px; margin-bottom: 50px;">请在红框内签名!</div>
	        <div class="multi-s">
	            <van-button class="multi-s-button multi-s-button-right" type="info" @click="onCancel">取消</van-button>
	            <van-button class="multi-s-button multi-s-button-right" type="info" @click="canvasClear">清空
	            </van-button>
	            <van-button class="multi-s-button multi-s-button-right" type="info" @click="saveAsImgOK">确认</van-button>
	        </div>
	    </van-popup>
	</div>
</template>

<script>
export default {
  name: "signDialog",
  props: {
      wxts: String,
      show: Boolean,
      image: {
          required: false,
          type: [String],
          default: null
      },
  },
  data() {
    return {
      value: null,
      options: {
        lastWriteSpeed: 1,
        lastWriteWidth: 2,
        lineCap: "round",
        lineJoin: "round",
        canvasWidth: 360, // 初始宽度，稍后会调整
        canvasHeight: 660,
        isShowBorder: false,
        bgColor: "#ffffff",
        borderWidth: 1,
        borderColor: "#ff787f",
        writeWidth: 5,
        maxWriteWidth: 30,
        minWriteWidth: 5,
        writeColor: "#101010",
        isSign: true,
        imgType: "png"
      }
    };
  },
  watch: {
      show(newVal) {
  		console.log('watch show', this.show);
  		this.$nextTick(() => {
          if (newVal) this.adjustCanvasWidth();
  		})
      }
  },
  methods: {
	onCancel() {
	  this.onChangeShow(false);
	},
	onChangeShow(value) {
	  this.$emit('update:show', value);
	},
    canvasClear() {
      this.$refs.SignCanvas.canvasClear();
    },
	adjustCanvasWidth() {
		const popupWidth = this.$el.querySelector(".van-popup").offsetWidth;
		const popupHeight = this.$el.querySelector(".van-popup").offsetHeight;
		console.log('adjustCanvasWidth', popupWidth)
		this.$refs.SignCanvas.$el.style.width = `${popupWidth - 16}px`;
		this.$refs.SignCanvas.$el.style.height = "auto";
		this.$refs.SignCanvas.$el.style.marginTop = "8px";
		// this.$refs.SignCanvas.$el.style.maxHeight = "100%";
      // const dialogWidth = this.$el.querySelector(".el-dialog").offsetWidth;
      // this.$refs.SignCanvas.$el.style.width = `${dialogWidth}px`;
      // this.$refs.SignCanvas.$el.style.height = "auto";
      // this.$refs.SignCanvas.$el.style.maxHeight = "100%";
    },
	/**
	 *  保存图片，格式为base64
	 */
	saveAsImg() {		
		const img = this.$refs.SignCanvas.saveAsImg(); 
		console.log('img', img);
		return img;
	},
	/**
	 * 保存图片
	 */
	saveAsImgOK() {
	    const img = this.saveAsImg(); // base64编码的字符串
		let file = this.dataURLtoFile(img, "signature" + Math.random().toString(36).substr(2) + '.png');
		let filesObj = {
		    content: img,
			url: img,
		    file: file,
		    status: "",
		};
		let files = [];
		files.push(filesObj);
		// alert(`image 的base64：${img}`);
		console.log("saveAsImgOK====", files);
		this.$emit('input', files);
		this.onCancel();
	},
	/**
	 * 将base64转换为文件对象
	 */
	dataURLtoFile(dataurl, filename) {
	    var arr = dataurl.split(',');
	    var mime = arr[0].match(/:(.*?);/)[1];
	    var bstr = atob(arr[1]);
	    var n = bstr.length;
	    var u8arr = new Uint8Array(n);
	    while (n--) {
	        u8arr[n] = bstr.charCodeAt(n);
	    }
	    //转换成file对象
	    return new File([u8arr], filename, {type: mime});
	},
	/**
	   * base64转二进制文件流
	   * @param base64
	   * @param mimeType
	   * @returns {Blob}
	   */
	// base64ToBlob(base64, mimeType) {
	// 	const byteCharacters = atob(base64);
	// 	const byteArrays = [];
	// 	for (let offset = 0; offset < byteCharacters.length; offset += 512) {
	// 	  const slice = byteCharacters.slice(offset, offset + 512);
	// 	  const byteNumbers = new Array(slice.length);
	// 	  for (let i = 0; i < slice.length; i++) {
	// 		byteNumbers[i] = slice.charCodeAt(i);
	// 	  }
	// 	  const byteArray = new Uint8Array(byteNumbers);
	// 	  byteArrays.push(byteArray);
	// 	}
	// 	return new Blob(byteArrays, { type: mimeType });
	// }
  }
};
</script>

<style scoped>
.multi-s {
        display: flex;
        width: 100%;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
		background-color: #ffffff;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        font-size: 16px;
    }

    /*.multi-s-button-ceter {*/
    /*    !*border-color: #00A5EC;*!*/
    /*    !*color: #00A5EC;*!*/
    /*}*/

    .multi-s-button-right {
        background-color: #00A5EC;
        border-color: #00A5EC;
    }

    .app-sign-canvas {
        /*display: block;*/
        margin: 12px;
        border: 1px dashed #f00;
        /*position: fixed;*/
        z-index: -1;
        top: 0;
        background: #fff;

    }


    .fixed-water-mark {
        position: fixed;
        pointer-events: none;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1600;
        background: red;
    }

    .fixed-water-mark #watermark {
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        opacity: 0.4;
        margin: 0 auto;
        background: #ffa200;
    }

/* .sign >>> .el-dialog {
  background: #b4a078;
}
.sign >>> .el-dialog__header {
  padding: 2px 0px;
}
.sign >>> .el-dialog__body {
  padding: 0;
}
.sign >>> .el-button {
  font-size: 12px;
  font-weight: 400;
}
.btnList {
  padding: 5px;
}
.sign >>> .el-dialog__title {
  line-height: 32px;
  font-size: 24px;
  color: hsl(40, 28.57%, 30.82%);
  text-shadow: 0 .03em .03em black;
}
*/
.sign-canvas {
  display: block;
  margin: 0 auto;
  background: #ffffff;
  /* border-radius: 8px; */
} 
</style>