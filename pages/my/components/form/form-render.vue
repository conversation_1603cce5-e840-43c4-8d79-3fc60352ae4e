<!-- form表单展示各种类型的字段 -->
<template>
	<view class="container" style="padding: 6px 12px;">
		<!--  {{currentGroup}} -->
		<van-cell-group 
			:key="currentGroup.id"
			:title="currentGroup.groupName ? currentGroup.groupName : ''"
			>
			<template v-if="initItems&&initItems.length <= 0">
                <div style="text-align: center;margin: 10px;color: #999;">暂无数据</div>
            </template>
			<template v-else v-for="(field, index) in initItems">
				<!-- <template v-if="field.showFlag == '是' || currentGroup.enType === 'preview'"> -->
				<van-field 
					v-if="field.type === 'input'"
				   :error="field.style?true:false"
				   :error-message="getErrorMessage(field.prop)"
				   :key="field.key"
				   :readonly="currentGroup.enType === 'preview'"
				   :maxlength="field.maxLength ? field.maxLength : null"
				   :type="returnType(field)"
				   :label="field.label"
				   :value="value[field.prop]"
				   v-on:input="onInput(field, $event)"
				   :required="currentGroup.enType === 'preview' ? false : field.required"
				   :placeholder="returnPlaceholder(field)"/>
				<van-field 
				   v-else-if="field.type === 'inputNumber'"
				   type="number"
				   :readonly="currentGroup.enType === 'preview'"
				   :error="field.style?true:false"
				   :error-message="getErrorMessage(field.prop)"
				   :key="field.key"
				   :label="field.label"
				   :value="value[field.prop]"
				   v-on:input="onInput(field, $event)"
				   :placeholder="returnPlaceholder(field)"
				   :required="currentGroup.enType === 'preview' ? false : field.required" />
				<van-field
					v-if="field.type === 'textarea'"
					:error="field.style ? true:false"
					:error-message="getErrorMessage(field.prop)"
					:key="field.key"
					:readonly="currentGroup.enType === 'preview'"
					:maxlength="field.maxLength ? field.maxLength : null"
					:type="'textarea'"
					:label="field.label"
					:value="value[field.prop]"
					v-on:input="onInput(field, $event)"
					:rows="3"
					maxlength="255"
					show-word-limit
					class="textarea-box"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"/>
				<van-field
					v-if="field.type === 'textareaBlobtext'"
				   :error="field.style ? true:false"
				   :error-message="getErrorMessage(field.prop)"
				   :key="field.key"
				   :readonly="currentGroup.enType === 'preview'"
				   :type="'textarea'"
				   :label="field.label"
				   :value="value[field.prop]"
				   v-on:input="onInput(field, $event)"
				   :rows="3"
				   :required="currentGroup.enType === 'preview' ? false : field.required"
				   :placeholder="returnPlaceholder(field)"/>
				<van-field 
					v-else-if="field.type === 'rate'" 
					:readonly="currentGroup.enType === 'preview'"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:label="field.label">
					<template slot="input">
						<uni-rate
							v-model="value[field.prop]" 
							:allow-half="false" 
							:touchable="true" 
							color="#bbb" 
							:size="22" 
							margin="8"
							:max="5"
							@change="onInput(field, $event.value)"
							
							:readonly="currentGroup.enType === 'preview'"
						/>
					</template>
				</van-field>
				<van-cell
					v-else-if="field.type === 'slider'"
					:error-message="getErrorMessage(field.prop)"
					:key="field.key"
					:title="field.label"
					:placeholder="returnPlaceholder(field)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					center
					title-style="min-width: 86px;color: #646566; max-width: 86px;"
					:readonly="currentGroup.enType === 'preview'">
						<Slider :value="value[field.prop]" v-on:input="onInput(field, $event)" :readonly="currentGroup.enType === 'preview'" />
				</van-cell>
				<van-cell
					v-else-if="field.type === 'sliderRange'"
					:error-message="getErrorMessage(field.prop)"
					:key="field.key"
					:title="field.label"
					:placeholder="returnPlaceholder(field)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					center
					title-style="min-width: 86px;color: #646566; max-width: 86px;"
					:readonly="currentGroup.enType === 'preview'">
						<SliderRange :value="value[field.prop]" :readonly="currentGroup.enType === 'preview'" v-on:input="onInput(field, $event)" />
				</van-cell>
				<van-cell 
					v-else-if="field.type === 'stepper'"
					:error-message="getErrorMessage(field.prop)"
					:key="field.key"
					:title="field.label"
					:placeholder="returnPlaceholder(field)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					center
					title-style="min-width: 86px;color: #646566; max-width: 86px;"
					:readonly="currentGroup.enType === 'preview'">
						<van-stepper :value="value[field.prop]" input-width="80px" button-size="32px" min="-1000"
							v-on:input="onInput(field, $event)"
							step="0.5"
							:decimal-length="2"/>
				</van-cell>
				<van-cell
					v-else-if="field.type === 'switch'"
					:error-message="getErrorMessage(field.prop)"
					:key="field.key"
					:title="field.label"
					:placeholder="returnPlaceholder(field)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					center
					title-style="min-width: 86px;color: #646566; max-width: 86px;"
					:readonly="currentGroup.enType === 'preview'">
						<template #right-icon>
							<SwitchIsNot :readonly="currentGroup.enType === 'preview'" :value="value[field.prop]" v-on:input="onInput(field, $event)" />
						</template>
				</van-cell>
				<ZZ_FieldFileUploader 
					v-else-if="field.type === 'smoothSignature' || field.type === 'imageUpload' || field.type === 'fileUpload'"
					:errorMessage="getErrorMessage(field.prop)"
					:readOnlyMark="currentGroup.enType === 'preview'"
					:field="field"
					:key="field.key"
					:value="value[field.prop]"
					v-on:addFiles="onAddFiles"
					:oldFilesObj="onGetOldFiles(field.prop)"
					v-on:deleteFile="onDeleteFile"
					:label="field.label"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"></ZZ_FieldFileUploader>
				<!-- 下拉和单选 -->
				<ZZ_FieldSelector
					v-else-if="(field.type === 'select' || field.type === 'dictSelect') && (field.typeKey == 'select' || field.typeKey == 'radio' || field.typeKey == 'radioButton'||field.typeKey == 'dictRadio')"
					:errorMessage="getErrorMessage(field.prop)"
					:key="field.key"
					:value="value[field.prop]"
					:label="field.label"
					:field="field"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:props="field.props"
					:initModel="value"
					:readonly="currentGroup.enType === 'preview'"
					>
					<!-- :options="field.options?field.options:(field.el && field.el.split('$'))"
					:disabled="field.showFlag === 'readonly'" -->
				</ZZ_FieldSelector>
				<ZZ_FieldMultiSelector
					v-else-if="(field.type === 'select' || field.type === 'dictSelect') && (field.typeKey == 'multipleSelect' ||field.typeKey == 'checkbox' || field.typeKey == 'dictCheckbox')"
					:errorMessage="getErrorMessage(field.prop)"
					:key="field.key"
					:value="value[field.prop]"
					:label="field.label"
					:field="field"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:props="field.props"
					:initModel="value"
					:readonly="currentGroup.enType === 'preview'"
					>
				</ZZ_FieldMultiSelector>
				<ZZ_FieldBadge
					v-else-if="field.type === 'editTag'"
					:errorMessage="getErrorMessage(field.prop)"
					:key="field.key"
					:value="value[field.prop]"
					:label="field.label"
					:field="field"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:props="field.props"
					:initModel="value"
					:readonly="currentGroup.enType === 'preview'"
					>
				</ZZ_FieldBadge>
				<ZZ_FieldRegions
					v-else-if="['regions','citys', 'provinceCity', 'province'].includes(field.type)"
					:errorMessage="getErrorMessage(field.prop)"
					:key="field.key"
					:value="value[field.prop]"
					:label="field.label"
					:field="field"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:props="field.props"
					:initModel="value"
					:readonly="currentGroup.enType === 'preview'"
					>
				</ZZ_FieldRegions>
				<ZZ_FieldDateRangePicker 
					v-else-if="[ 'date', 'datetime', 'daterange', 'datetimerange'].indexOf(field.type) > -1"
					:errorMessage="getErrorMessage(field.prop)"
					:field="field"
					:key="field.key+'datePicker'"
					:type="field.type"
					:selfeltype="field.selfeltype"
					:value="value[field.prop]"
					:label="field.label"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:readonly="currentGroup.enType === 'preview'"
				/>
				<ZZ_FieldTimeRangePicker
					v-else-if="field.type == 'timerange' || field.type == 'time' || field.type == 'timeSelect'"
					:errorMessage="getErrorMessage(field.prop)"
					:field="field"
					:key="field.key+'datePicker'"
					:type="field.type"
					:selfeltype="field.selfeltype"
					:value="value[field.prop]"
					:label="field.label"
					v-on:input="onInput(field, $event)"
					:required="currentGroup.enType === 'preview' ? false : field.required"
					:placeholder="returnPlaceholder(field)"
					:disabled="currentGroup.enType === 'preview'"
				/>
				<!-- </template> -->
			</template>
		</van-cell-group>
	</view>
</template>

<script>
	var Api = require('@/utils/api.js');
	var wxRequest = require('@/utils/wxRequest.js');
	// var util = require('@/components/ProForm/util.js')
	import {
	    stringTypes,
	    arrayTypes,
	    selectTypes,
	    uploadTypes,
	    regionsArrayTypes,
	    baseCodeTypes
	  } from '@/components/ProForm/util';
	import {onFieldCheck3, formCheck} from "../../utils/zizhu_form";
	import DateTimePicker from "@/components/dengrq-datetime-picker/dateTimePicker/index.vue"
	import ZZ_FieldDatePicker from './ZZ_FieldDatePicker.vue';
	import SliderRange from './SliderRange.vue';
	import Slider from './Slider.vue';
	import SwitchIsNot from './SwitchIsNot.vue';
	import ZZ_FieldDateRangePicker from './ZZ_FieldDateRangePicker.vue';
	import ZZ_FieldTimeRangePicker from './ZZ_FieldTimeRangePicker.vue';
	import ZZ_FieldFileUploader from './ZZ_FieldFileUploader.vue';
	import ZZ_FieldSelector from './ZZ_FieldSelector.vue';
	import ZZ_FieldMultiSelector from './ZZ_FieldMultiSelector.vue';
	import ZZ_FieldBadge from './ZZ_FieldBadge.vue';
	import ZZ_FieldRegions from './ZZ_FieldRegions.vue';
	
	// import MyFieldDatePicker from './MyFieldDatePicker.vue';
	// import FileUploaderSignature from './FileUploaderSignature.vue';
	export default {
		props: {
			/** 表单左侧label宽度 */
			labelWidth: {
			  type: String,
			  default: '75'
			},
			/** 表单label对齐方式: top/left */
			labelPosition: {
			  type: String,
			  default: 'top'
			},
			/** 当前组的信息*/
			currentGroup: {
				type: Object,
				default() {
					return {}
				}
			},
			/** 用户的信息 */
			value: Object,
			// userInfo: {
			// 	type: Object,
			// 	default() {
			// 		return {}
			// 	}
			// },
			/**角色名称 student/teacher*/
			userType: {
				type: String,
				default: ''
			},
			// isTableItem: {
			// 	type: Boolean,
			// 	default() {
			// 		return false
			// 	}
			// },
			// itemIndex: {
			// 	type: Number
			// }
		},
		components: {
			DateTimePicker,
			ZZ_FieldDatePicker,
			ZZ_FieldDateRangePicker,
			ZZ_FieldTimeRangePicker,
			SliderRange,
			Slider,
			SwitchIsNot,
			ZZ_FieldFileUploader,
			ZZ_FieldSelector,
			ZZ_FieldMultiSelector,
			ZZ_FieldBadge,
			ZZ_FieldRegions,
			// FileUploaderSignature,
			// MyFieldDatePicker
		},
		data(){
			return {
				// formData: {},
				// rangeList: [{"value": 0,"text": "篮球"	},{"value": 1,"text": "足球"},{"value": 2,"text": "游泳"}],
				fieldData: [], /**所有字段list*/
				initItems: [], /** 表单项 */
				// value: {}, /**form表单*/
				currentUserData: null, // 当前人的信息
				changes: [],
				isChanging: false,
				errorMap: new Map(),
				grade: 0,
				fileMap: new Map(),
				fileMapChangeTracker: 0,
				currentField: {}
			}
		},
		computed: {
		},
		created(){
			this.currentUserData = this.value ? this.value : null;
			this.getData();
		},
		watch: {
			currentGroup(newVal){
				console.log('*************', newVal)
				if(newVal) {
					this.getData();
				}
			},
		},
		methods: {
			showDateRange(item) {
				this.currentField = item;
				this.show = true;
			},
			formatDate(date) {
			  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			/** 监控input事件 */
			onInput(field, value) {
				const key = field.prop;
				// this.value = Object.assign(this.value, {[key]: value});
				console.log('onInput 1', field, value, this.value);
				let newObj = Object.assign({}, this.value);
				newObj[key] = value;
				if (key === 'location_address') {
					newObj['location_longitude'] = this.longitude;
					newObj['location_latitude'] = this.latitude;
				}
				this.$emit("input", newObj);
				// if(!this.isTableItem) {
				// 	this.$emit("input", newObj);
				// } else {
				// 	this.$emit("input", newObj);
				// }
				this.onChangePush(field, value);
				setTimeout(() => {
					this.onStartChange();
				}, this.setLoadingTime ? this.setLoadingTime : 300);
				this.updateFormValue(field, field.prop, value);
				this.fieldCheck(field, value);
			},
			/**?*/
			onStartChange() {
				console.log('onStartChange', this.changes, this.isChanging)
                if (this.changes.length > 0) {
                    if (!this.isChanging) {
                        this.isChanging = true;
                        let b = true;
                        let hasChange = false;
                        const fields = this.initItems;
                        while (b) {
                            let cObj = this.changes.shift();
                            if (cObj) {
                                if (!hasChange) hasChange = true;
                                const cData = cObj.data;
                                switch (cObj.attachType) {
                                    case "change other": {
                                        let field = fields.find(item => {
                                            return item.prop === cData.name;
                                        });
                                        if (field) {
                                            const en = field.prop;
                                            let isRequired = null;
                                            let isDisplay = null;
                                            if (cData.value === "是") {
                                                isRequired = field.readonly ? null : true;
                                                isDisplay = 'black';
                                                this.onInput(field, null);
                                            } else if (cData.value === "否") {
                                                isRequired = false;
                                                isDisplay = field.defaultDisplay;
                                            }
                                            field.isRequired = isRequired;
                                            field.isDisplay = isDisplay;
                                            this.fieldCheck(field, this.value[en]);
                                            if (cData.hasOwnProperty("linked") && cData.linked) {
                                                let linked = [];
                                                if (cData.linked) {
                                                  linked = JSON.parse(cData.linked);
                                                }
                                                if (linked && linked.length > 0) {
                                                    let options = [];
                                                    linked.forEach(op => {
                                                        options.push(op.text);
                                                    });
                                                    field.options = options;
                                                    field.el = '';
                                                    this.onInput(field, null);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    case "math other": {
                                        setTimeout(() => {
                                            const mathObj = cData.mathObj;
                                            const mathFun = mathObj.mathFun;
                                            let num = null;
                                            const datas = mathFun.datas;
                                            for (let i = 0; i < datas.length; i++) {
                                                const datum = datas[i];
                                                const childEn = datum.fielden;
                                                let val = null;
                                                if (this.value.hasOwnProperty(childEn)) {
                                                    val = this.value[childEn];
                                                }
                                                if (!val) {
                                                    val = 0;
                                                }
                                                const v = Number(val);
                                                if (num === null) {
                                                    num = v;
                                                    continue;
                                                }
                                                switch (mathFun.type) {
                                                    case "jia":
                                                        num += v;
                                                        break;
                                                    case "jian":
                                                        num -= v;
                                                        break;
                                                    case "cheng":
                                                        num *= v;
                                                        break;
                                                    case "chu":
                                                        if (v === 0) {
                                                            num = 0;
                                                        } else {
                                                            num /= v;
                                                        }
                                                        break;
                                                }
                                            }
                                            let newObj = Object.assign({}, this.value);
                                            newObj[mathObj.result] = num;
                                            this.$emit("input", newObj);
                                        }, 500);
                                        break;
                                    }
                                }
                            }
                            if (this.changes.length === 0) {
                                this.isChanging = false;
                                b = false;
                            }
                        }
                    }
                }
            },
			/**?*/
			onChangePush(field, value) {
                const type = field.type;
                if (field.fjtj) {
                    if (["single", "init"].includes(type)) {
                        let cParams = this.additionalConditionsParse(field.fjtj);
                        const cParam = cParams.find(item => {
                            return item.text === value;
                        });
                        if (cParam) {
                            if (cParam.data) {
                                cParam.data.forEach(item => {
                                    this.changes.push({data: item, attachType: "change other"});
                                });
                            }
                        }
                    }
                }
                if (field.mathObj) {
                    if (["integer", "float"].includes(type)) {
                        this.changes.push({
                            data: {cEn: field.prop, mathObj: field.mathObj},
                            attachType: "math other"
                        });
                    }
                }
            },
			additionalConditionsParse(originStr) {
                let parsed = null;
                if (originStr.includes("*")) {
                    parsed = JSON.parse(originStr.replace(/\(\*\)/g, '"'));
                } else {
                    parsed = JSON.parse(unzipStr(originStr).replace(/\(\*\)/g, '"'));
                }
                return parsed;
            },
			fieldCheck(field, value) {
				this.errorMap.set(
					field.prop,
					onFieldCheck3(field, value, this.onGetOldFiles(field.prop).files)
				);
				console.log('fieldCheck1111', this.errorMap, this.onGetOldFiles(field.prop))
			},
			getErrorMessage(key) {
				console.log('getErrorMessage', key, this.errorMap.get(key))
				if(this.currentGroup.enType == 'preview') {
					return ""
				}
				return this.errorMap.get(key);
			},
			returnType(field) {
                const fieldType = field.type;
                if (["clob", "string_l"].includes(fieldType)) {
                    return "textarea";
                }
                if (field.id === 'location_address') {
                    return "textarea";
                }
                if (field.prop === "bjs" || field.prop === "njs") {
                    return "textarea";
                }
                if (["integer", "float"].includes(fieldType)) {
                    return "number";
                }
            },
            returnPlaceholder(field) {
                if (!field.readonly) {
                    let fieldType = field.type;
                    let wxts = field.wxts;
                    if (fieldType === "readonly" || fieldType === "smoothSignature") return "";
                    if (["single", "more", "init", "date", "datetime", "time", "select", "dictSelect", "daterange"].includes(fieldType)) return wxts ? wxts : "请选择";
                    else if (["imageUpload", "videoUpload", "fileUpload"].includes(fieldType)) return wxts ? wxts : "请上传";
                    else if (["inputNumber"].includes(fieldType)) return wxts ? wxts : "请输入数字(支持小数)";
                    else return wxts ? wxts : "请输入";
                } else {
                    return null;
                }
            },
			//获取个人信息子菜单
			getData() {
			  var self = this;
			  // uni.showLoading({
			  //   title: '查询中',
			  //   mask: true
			  // });
			  let data = {
				  groupId: this.currentGroup.id, groupName: this.currentGroup.groupName, tempField: '否'
			  }
			  console.log('self.userType', self.userType)
			  let url = Api.getTFieldListByGroup();
			  if(self.userType == 'student') {
				  url = Api.getSFieldListByGroup();
			  }
			  var getFieldGroupListRequest = wxRequest.getRequest(url, data);
			  getFieldGroupListRequest.then(response => {
				console.log('bygroup >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let list = resData.data;
						self.setData({
							fieldData: list
						})
						if (list) {
						  this.formatFormData(list)
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  // self.fetchCodeImgData();
			    }, 1000);
			  });
			},
			formatFormData(fieldData) {
			    let fieldResult = [];
			    if (fieldData && fieldData.length > 0) {
				  fieldData.map(i=> {
					  // 学院专业班级特殊处理
					  if (i.fieldEn === 'xyid') {
					    i.fieldEn = 'xymc';
					  } else if (i.fieldEn === 'zyid') {
					    i.fieldEn = 'zymc';
					  } else if (i.fieldEn === 'bjid') {
					    i.fieldEn = 'bjmc';
					  }	else if (i.fieldEn === 'njid') {
					    i.fieldEn = 'njmc';
					  } else if (i.fieldEn === 'ryztid') {
					    i.fieldEn = 'ryzt';
					  } else if (i.fieldEn === 'pyccid') {
					    i.fieldEn = 'pyccmc';
					  }
				  })
				  console.log('改', fieldData)
			      fieldData.forEach((e, index) => {
			        let props = {};
			        let selfFieldLink = '';
			        let nextField = '';
			        let typeKey = e.controlType;
			        let type = e.controlType;
					
			        if (baseCodeTypes.includes(e.controlType)) {
			          if (e.loadDataType && e.loadDataType !== 'other') {
			            type = 'dictSelect';
			            props = {
			              code: e.loadDataType,
			              dicQueryParams: {
			                getValType: '',
			                valueField: e.valueField,
			                textField: e.textField
			              },
			              filterable: true
			            };
			            if (!e.valueField && !e.textField) {
			              props.dicQueryParams.getValType =
			                e.loadDataType !== 'pycc' ? 'name' : '';
			            }
			          } else {
			            //院系专业班级页面逻辑处理
			            type = 'dictSelect';
			            let diyCode = '';
			            if (e.loadDataUrl === '/code/codeDwb') {
			              diyCode = 'xymc';
			              selfFieldLink = 'xymc';
			              nextField = 'zymc';
			            } else if (e.loadDataUrl === '/code/codeZyb') {
			              diyCode = 'zymc';
			              selfFieldLink = 'zymc';
			              nextField = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeBjb') {
			              diyCode = 'bjmc';
			              selfFieldLink = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeCommon/regionList') {
			              diyCode = 'regions';
			            } else {
			              diyCode = e.loadDataType;
			            }
			            props = {
			              code: diyCode,
			              filterable: true,
			              dicQueryParams: {
			                dictFieldUrl: e.loadDataUrl,
			                valueField: e.valueField,
			                textField: e.textField
			              }
			            };
			          }
			        } else if (
			          e.controlType === 'fileUpload' ||
			          e.controlType === 'imageUpload' || 
					  e.controlType === 'smoothSignature'
			        ) {
			          //附件上传数量
			          props = {
			            limit: e.regExpression ? Number(e.regExpression) : 1
			          };
			        }
			        let newObj = Object.assign(
			          {
			            key: 'fieldKey_' + index,
			            label: e.fieldZh,
			            prop: e.fieldEn,
			            type: type,
			            typeKey: typeKey,
			            required: e.required === '是',
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            fieldLinks: e.fieldLinks,
			            selfFieldLink: selfFieldLink,
			            nextField: nextField,
			            // colProps: [
			            //   'textareaBlobtext',
			            //   'textarea',
			            //   'smoothSignature',
			            //   'radioButton',
			            //   'checkboxButton'
			            // ].includes(typeKey)
			            //   ? 24
			            //   : (e?.span ?? 8)
			          },
			          {
			            props: props
			          }
			        );
			        fieldResult.push(newObj);
			
			        if (e.controlType === 'regions') {
			          //省市区+详细地址信息
			          fieldResult.push({
			            key: 'fieldKey_99' + index,
			            label: '详细地址',
			            prop: e.fieldEn + '_regionsDetail',
			            type: 'input',
			            typeKey: 'input',
			            required: false,
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            colProps: 8
			          });
			        }
			      });
			      this.initItems = fieldResult;
				  // fieldResult.map(i => {
					 //  this.initItems.push(i);
				  // })
				  console.log('this.initItems', this.initItems)
			    }
			    let fieldFormResult = {};
			    if (fieldResult && fieldResult.length > 0) {
			      fieldResult.forEach((item) => {
					console.log('fieldResult item', item)
					// this.fieldCheck(item, this.value[item.prop]);
			        if (item.typeKey) {
			          // if (
			          //   uploadTypes.includes(item.typeKey) ||
			          //   regionsArrayTypes.includes(item.typeKey) ||
			          //   arrayTypes.includes(item.typeKey)
			          // ) {
			          //   fieldFormResult[item.prop] = [];
			          // }
			          if (
			            stringTypes.includes(item.typeKey) ||
			            selectTypes.includes(item.typeKey)
			          ) {
			            fieldFormResult[item.prop] = '';
						if(item.prop == "xymc") {
							fieldFormResult['xyid'] = '';
						} else if(item.prop == "zymc") {
							fieldFormResult['zyid'] = '';
						} else if(item.prop == "bjmc") {
							fieldFormResult['bjid'] = '';
						} else if(item.prop == "njmc") {
							fieldFormResult['njid'] = '';
						} else if(item.prop == "ryzt") {
							fieldFormResult['ryztid'] = '';
						} else if(item.prop == "pyccmc") {
							fieldFormResult['pyccid'] = '';
						}
			          }
			        }
			      });
				  console.log('form', fieldFormResult)
			      this.value = fieldFormResult;
			    }
				if (this.currentUserData) {
				  console.log('fieldResult', fieldResult, this.currentUserData)
			      if (fieldResult && fieldResult.length > 0) {
			        fieldResult.forEach((item, index) => {
					  console.log('>>>>>', index)
			          let setValue = null;
			          let dataValue = this.currentUserData
			            ? this.currentUserData[item.prop]
			            : null;
					  console.log('dataValue', dataValue)
			          if (dataValue) {
			            if (regionsArrayTypes.includes(item.typeKey)) {
			              let regionsArr = dataValue.split(',');
			              if (item.typeKey === 'regions') {
							console.log('1')
			                if (regionsArr.length > 0) {
								console.log('2')
			                  if (regionsArr[3]) {
								  console.log('3')
			                    this.initItems.filter((f) => {
			                      if (f.prop === item.prop + '_regionsDetail') {
			                        this.value[f.prop] = regionsArr[3];
			                      }
			                    });
			                    regionsArr.splice(regionsArr.length - 1, 1);
			                    setValue = regionsArr.join(',');
			                  } else {
								  console.log('4')
			                    setValue = regionsArr;//原来
			                  }
			                }
			              } else {
							console.log('5')
			                setValue = regionsArr.join(',');
			              }
			            } else if (
			              item.typeKey === 'imageUpload' ||
			              item.typeKey === 'fileUpload' ||
			              item.typeKey === 'smoothSignature'
			            ) {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    return {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      url: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                  })
			                : [];
			            } else if (item.typeKey === 'fileUpload') {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    let newObj = {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      fileUrl: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                    let mark = isImageFile(newObj);
			                    newObj.isImageFile = mark;
			                    if (mark) newObj.url = newObj.fileUrl;
			                    return newObj;
			                  })
			                : [];
			            } 
						// else if (
			   //            item.typeKey === 'multipleSelect' &&
			   //            this.currentGroup.enType !== 'conditionSet'
			   //          ) {
			   //            //多选值处理
			   //            setValue = dataValue.split(',');
			   //          } 
						else {
			              setValue = dataValue;
			            }
			          } else {
						console.log('没有值', dataValue)
					  }
					  console.log('setValue',item.prop, setValue);
			          if (setValue){
						  this.updateFormValue(item, item.prop, setValue);
					  }
					  this.fieldCheck(item, setValue);
			        });
			      }
			    }
				this.onStartChange();
			  },
			  /** 更新表单数据 */
			updateFormValue (item, prop, newVal, mark) {
			  let value = newVal;
			  // this.value[item.prop] = value;
			  this.value = Object.assign(this.value, {[item.prop]: value});
			  console.log('处理后1', this.value)
			  if(item.prop == "xymc") {
			  	this.value['xyid'] = this.currentUserData['xyid'];
			  } else if(item.prop == "zymc") {
				this.value['zyid'] = this.currentUserData['zyid'];
			  } else if(item.prop == "bjmc") {
				this.value['bjid'] = this.currentUserData['bjid'];
			  } else if(item.prop == "njmc") {
				this.value['njid'] = this.currentUserData['njid'];
			  } else if(item.prop == "ryzt") {
				this.value['ryztid'] = this.currentUserData['ryztid'];
			  } else if(item.prop == "pyccmc") {
				this.value['pyccid'] = this.currentUserData['pyccid'];
			  }
			  console.log('1111', this.value)
			  if (item.selfFieldLink) {
				//院系专业班级三个字段页面逻辑特殊处理
				// nextTick(() => {
				  this.initItems.filter((f) => {
					if (f.selfFieldLink === item.nextField) {
					  f.props['refresh'] = value; //watch每次刷新，重新请求
					  if (item.nextField === 'zymc') {
						f.props.dicQueryParams['params'] = { xyid: value };
					  }
					  if (item.nextField === 'bjmc') {
						f.props.dicQueryParams['params'] = { zyid: value };
					  }
					}
				  });
				// });
			  }
			  if (item.fieldLinks) {
				//获取选择值下面所有的显示字段信息
				let showfields = item.fieldLinks.filter((f) => {
				  return f.fieldVal === value;
				});
				console.log('联动fieldLinks', showfields);
				if (showfields.length > 0) {
				  // 联动表达式
				  showfields.forEach((sf) => {
					// nextTick(() => {
					  //切换选择值，清空之前选项所赋值,判断是否是手动切换
					  // if (!["preview", "sqpreview"].includes(props.currentData.enType) && mark === 'change') value.value[sf.linkField] = '';
					  if (
						!['preview', 'sqpreview'].includes(this.currentGroup.enType) &&
						mark === 'change'
					  )
						this.value[sf.linkField] = '';
					  //动态切换选择值，关联具体字段的显示隐藏
					  this.initItems.filter((init) => {
						if (sf.linkField === init.prop) {
						  if (sf.linkFieldDataType) {
							// init.showFlag = ["preview", "sqpreview"].includes(props.currentData.enType) ? "readonly" : sf.showFlag
							init.showFlag = ['preview', 'sqpreview'].includes(
							  this.currentGroup.enType
							)
							  ? 'readonly'
							  : sf.showFlag;
							init.props = {
							  code: sf.linkFieldDataType,
							  filterable: true
							};
						  } else {
							init.showFlag = sf.showFlag;
						  }
						}
						init.selfModifyFlag = init.showFlag;
					  });
					// });
				  });
				}
			  }
			  //统一入口处理只读
			  this.initItems.filter((init) => {
				// if (["preview", "sqpreview"].includes(props.currentData.enType)) {
				if (['preview', 'sqpreview'].includes(this.currentGroup.enType)) {
				  init.showFlag = 'readonly';
				}
				init.selfModifyFlag = init.showFlag;
			  });
			  console.log('统一入口处理', this.initItems)
			  console.log('处理后', this.value)
			  // if (this.currentGroup && this.currentGroup.listFlag !== '是') {
			  //   emit('onDoneGroup', {
			  //     groupId: this.currentGroup.id,
			  //     groupName: this.currentGroup.title,
			  //     values: this.value
			  //   });
			  // }
			},
			// onDeleteFile(en, index) {
			onDeleteFile(field, index) {	
                let arr1 = this.fileMap.get(field.prop);
                if (arr1 && arr1.length > index) {
                    arr1.splice(index, 1);
                    this.fileMap.set(field.prop, arr1);
                    this.fileMapChangeTracker--;
					this.fieldCheck(field, arr1);
                }
            },
            // onAddFiles(en, arr) {
            onAddFiles(field, arr) {
				console.log('onAddFiles', field, arr)
                // let arr1 = this.fileMap.get(en);
                let arr1 = this.fileMap.get(field.prop);
                if (!arr1) {
                    arr1 = [];
                }
                for (const arrE of arr) {
                    arr1.push(arrE);
                }
                this.fileMap.set(field.prop, arr1);
                this.fileMapChangeTracker++;
				this.fieldCheck(field, arr1);
				console.log('onAddFiles', this.fileMap, this.fileMapChangeTracker)
            },
            onGetOldFiles(en) {
                return {
                    tracker: this.fileMapChangeTracker,
                    files: this.fileMap.get(en)
                };
            },
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
			mapToObj(strMap) {
                let obj = {};
                for (let [k, v] of strMap) {
                    obj[k] = v;
                }
                return obj;
            },
			onEmitFileStr() {
                this.$emit("receiveFileStr", this.getFileStr());
            },
            getFileStr() {
                return JSON.stringify(this.mapToObj(this.fileMap));
            },
            checkForm() {
                if (formCheck(this.initItems, this.value, this.fileMap)) {
                    this.$emit("formSubmit", this.formFields, this.getFileStr());
                }
            },
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	.uni-forms-item {
		// margin-bottom: 0;
	}
	.vertical-apply .van-cell__right-icon {
	    margin-top: -90px;
	    float: right;
	}
	
	.divDisplayBlack {
	    display: block;
	}
	
	.divDisplayNone {
	    display: none;
	}
	
	.van-submit-bar {
	    display: flex;
	    height: 50px;
	    border-top: 1px solid #eee;
	    box-shadow: 0 -2px 3px -1px #eee;
	}
	
	.van-submit-bar-button {
	    height: 35px;
	    width: 45.5% !important;
	    line-height: 35px;
	    margin-left: 3% !important;
	    background: #00a5ec;
	    font-size: 16px;
	    border-color: #00a5ec;
	}
	
	.vanButton {
	    background: #fff;
	    color: #00a5ec;
	}
	
	.vertical-apply >>> .van-cell {
	    font-size: 15px;
	}
	
	.vertical-apply >>> .van-field {
	    display: block;
	    font-size: 15px;
	}
	
	.vertical-apply >>> .van-field .van-field__label {
	    width: 100%;
	    margin-bottom: 5px;
	    color: #646566 !important;
	}
	
	.vertical-apply >>> .van-radio {
	    margin-bottom: 5px;
	}
	::v-deep .van-field__control {
		font-family: "ui-num", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
		font-size: 14px;
		color: #646566;
	}
	::v-deep .van-field__control::placeholder {
		font-family: "ui-num", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
		font-size: 14px;
	}
	::v-deep .van-cell__title {
		width: 86px;
	}
	// ::v-deep .van-cell__title {
	// 	width: 86px !important;
	// }
	// ::v-deep .van-cell__value {
	// 	flex: 1;
	// }
	.custom-button {
	    width: 22px;
		height: 22px;
	    color: #fff;
	    font-size: 10px;
	    line-height: 22px;
	    text-align: center;
	    background-color: #1989fa;
	    border-radius: 100px;
	}
	// ::v-deep .van-slider {
	// 	background-color: red !important;
	// }
	.van-cell-group__title {
		color: #000;
		padding: 16px 16px 8px;
		background-color: #fff;
		border-radius: 12px 12px 0 0;
	}
	.van-cell-group {
		overflow: hidden;
		border-radius: 0 0 12px 12px;
		box-shadow: rgba(12, 12, 12, 0.08) 0px 2px 15px -5px;
	}
</style>