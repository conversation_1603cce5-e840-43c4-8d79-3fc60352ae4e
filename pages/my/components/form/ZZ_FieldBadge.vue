<template>
	<div>
		<van-field :error="field.style?true:false"
				   :error-message="errorMessage"
				   :label="label"
				   :value="value"
				   :required="required"
				   readonly
				   :placeholder="placeholder"
				   right-icon="plus"
				   @click-right-icon="onAdd"
				   >
				<template slot="input">
					<div class="badge-container">
						<van-badge color="#C8C8C8" v-for="(badge, index) in badgeList" :key="badge+index">
						  <div class="badge-child" @click="onEdit(badge, index)">{{badge}}</div>
						  <template #content>
							<van-icon @click="onDelete(index)" name="cross" class="badge-icon" />
						  </template>
						</van-badge>
					</div>
				</template>
		</van-field>
		<!-- 弹窗输入 -->
		<ZZ_Badge
		  :value="curValue"
		  :index="curIndex"
		  v-on:input="onInput($event)"
		  :show.sync="selectorShow">
		</ZZ_Badge>
	</div>
</template>

<script>
	var Api = require('../../api/index.js');
	var wxRequest = require('@/utils/wxRequest.js');
	
    import ZZ_Badge from "./ZZ_Badge.vue";

    export default {
        components: {ZZ_Badge},
        name: "ZZ_FieldSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            // options: Array,
			props: Object,
            field: Object,
			initModel: Object,
			readonly: Boolean,
        },
        data() {
            return {
                selectorShow: false,
                radioShow: false,
                mark: 1,
                same: false,
				options: [],
				curValue: '',
				curIndex: null,
				badgeList: []
            };
        },
        computed: {},
		watch: {
			value(newValue) {
				if (newValue) {
				    this.badgeList = newValue.split(',');
				} else {
					this.badgeList = [];
				}
			},
			selectorShow(newVal) {
				if(!newVal) {
					this.setData({
						curValue: '',
						curIndex: null
					})
				}
			}
		},
        methods: {
			onAdd(){
				if(this.readonly) {
					return false
				}
				this.selectorShow = true;
			},
			onEdit(value, index){
				if(this.readonly) {
					return false
				}
				this.setData({
					curValue: value,
					curIndex: index,
					selectorShow: true
				})
			},
			onDelete(index){
				if(this.readonly) {
					return false
				}
				this.badgeList.splice(index, 1);
				this.$emit("input", this.badgeList.join(','));
			},
            onInput(obj) {
				let existIndex = this.badgeList.findIndex(item => item == obj.value);
				if(obj.hasOwnProperty('index') && obj.index != null) {// 修改某个标签
					if(existIndex != -1) {
						if(existIndex !== obj.index) {
							uni.showToast({
								title: '该标签已存在，修改失败！',
								icon: 'none'
							});
							return;
						}
					}
					// 修改
					this.badgeList.splice(obj.index, 1, obj.value);
				} else {
					console.log('新增 *********');
					if(existIndex != -1) {
						uni.showToast({
							title: '该标签已存在，不会重复添加！',
							icon: 'none'
						});
						return;
					}
					this.badgeList.push(obj.value);
				}
                this.$emit("input", this.badgeList.join(','));
            }
        }
    };
</script>

<style scoped>
	.van-radio{
		margin-bottom: 8px;
	}
	.van-radio:last-child{
		margin-bottom: 0px;
	}
	/* 撑起field的高度 */
	.badge-container {
	  display: flex;
	  flex-wrap: wrap;
	  gap: 10px 0px;
	}
	.van-badge__wrapper{
		margin-right: 14px;
	}
	.badge-child {
		padding:0 6px;
		background-color: rgb(244,244,244);
		border: 1px solid rgb(233,233,234);
		border-radius: 6px;
		height: 22px;
	}
	.badge-icon {
	  display: block;
	  font-size: 10px;
	  line-height: 16px;
	}
</style>
