<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="value"
                   :required="required"
                   @click="showClick"
                   readonly
                   :placeholder="placeholder"
                   is-link>
			<!-- <template slot="input">
				{{ value ? value: '请选择'}}
			</template> -->
        </van-field>
		<van-popup v-model="selectorShow" position="bottom" :style="{ height: '50%' }"
		           :close-on-click-overlay="false">
		    <van-area :area-list="options"
		              :columns-placeholder="['请选择', '请选择', '请选择']"
		              :value="setDefaultCode"
		              ref="myArea"
		              @change="onAreaChange"
		              @confirm="onAreaConfirm"
		              @cancel="onAreaCancel"/>
		</van-popup>
		<!-- <ZZ_Regions
		  :value="value"
		  :defaultCode="setDefaultCode"
		  v-on:input="onInput($event)"
		  :columns="options" 
		  :show.sync="selectorShow"
		  :field="field">
		</ZZ_Regions> -->
        
    </div>
</template>

<script>
	var Api = require('../../api/index.js');
	var wxRequest = require('@/utils/wxRequest.js');
    import ZZ_Regions from "./ZZ_Regions.vue";

    export default {
        components: {
            ZZ_Regions
        },
        name: "ZZ_FieldMultiSelector",
        props: {
            field: Object,
            directionMark: String,
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
			props: Object,
			initModel: Object,
			readonly: Boolean,
        },
        data() {
            return {
                selectorShow: false,
				currentField: null,
				setDefaultAddressStr: null,//用于编辑地址，没有改变原本值，预存入的值
				setDefaultCode: null,
				options: {},
				regionList: [], // 原始地址list
            }
        },
        computed: {
            isShowSelect() {
                return this.options && this.options.length > 5;
            },
            valueShow() {
                if (this.value) {
                    return this.value.split(',');
                }
                return [];
            },
			// 下拉name
			columnsName() {
				let res = [];
				this.options.map(item => {
					res.push(item.text)
				})
				return res;
			},
			// 下拉code
			columnsId() {
				return this.options.map(item => item.value)
			}
        },
        methods: {
			onInputAddressDetail(value) {
				// if (!this.setDefaultAddress) {
				//     this.setDefaultAddressDetail = null;
				//     this.$toast("请选择省/市/区");
				//     return false;
				// }
				this.setDefaultAddressDetail = value;
			},
			// showClick(item) {
			showClick() {
                // this.currentField = item;
				if(this.readonly) {
					return false;
				}
                this.currentField = this.field;
				console.log('showClick field initModel', this.field, this.initModel)
                // if (item.en === "jtdz" || item.en === "txdz" || item.type === 'address') {
                    let areaCodeArray = this.value ? this.value.split(",") : [];
					console.log('areaCodeArray', areaCodeArray);
                    let county = null;
                    if (areaCodeArray.length > 0) {
                        if (areaCodeArray[0] === '海外') {
                            this.setDefaultAddress = areaCodeArray[0] + "," + areaCodeArray[1]
                            this.setDefaultAddressDetail = areaCodeArray[2];
                            let countyList = this.options["city_list"];
							console.log('11111', countyList)
                            for (let i in countyList) {
                                if (areaCodeArray[1] && countyList[i] === areaCodeArray[1]) {
                                    county = i;
                                }
                            }
                            this.setDefaultAddressStr = areaCodeArray.splice(0, 2);
                        } else {
                            this.setDefaultAddress = areaCodeArray[0] + "," + areaCodeArray[1] + "," + areaCodeArray[2];
                            this.setDefaultAddressDetail = areaCodeArray[3];
							let cityObj = this.regionList.find(r => r.label == areaCodeArray[0]);
							if(cityObj) {
								let cityList = cityObj.children;
								let countryObj = cityList.find(c => c.label == areaCodeArray[1]);
								if(countryObj) {
									let countryList = countryObj.children;
									let areaObj = countryList.find(c => c.label == areaCodeArray[2]);
									console.log('areaObj', areaObj);
									if(areaObj) {
										county = areaObj.value;
									}
								}
							}
							console.log('111111111111', county);
                            // let countyList = this.options["county_list"];
                            // for (let i in countyList) {
                            //     if (areaCodeArray[2] && countyList[i] === areaCodeArray[2]) {
                            //         county = i;
                            //     }
                            // }
                            this.setDefaultAddressStr = areaCodeArray.splice(0, 3);
                        }
                    }
                    this.setDefaultCode = county; // 回显选中的省市县，只需要最后一级的code
					console.log('this.setDefaultCode', this.setDefaultCode)
                // }
                if (this.field.required) this.setAreaRequest = true;
                this.selectorShow = true;
            },
			// 获取options
			getOptions() {
			  var self = this;
			  // self.setData({
			  // 	isLoading: true
			  // });
			  // uni.showLoading({
			  //   title: '加载中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.regionListQuery();
			  var getRegionListQuery = wxRequest.getRequest(url, data);
			  getRegionListQuery.then(response => {
				console.log('regionList', response,response.data)
			    if (response.statusCode === 200) {
					let resData = response.data;
					console.log('regionList 200', resData, resData.code)
					if(resData.code === 0) {
						console.log('resData.code 0',resData)
						let areaList = self.extractLevels(resData.data);
						self.setData({
							options: areaList,
							regionList: resData.data
						})
						console.log('regionList ******', self.options)
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
				uni.hideLoading();
				self.setData({
				  showerror: "block",
				  floatDisplay: "block"
				});
			  }).finally(function () {
				  uni.hideLoading();
			  });
			},
            onInput(value) {
                this.$emit("input", value);
            },
			// 递归省市区list获取一个对象，结构如下：
			// areaList = {
			// 	province_list: {110000: '北京市',120000: '天津市'},
			// 	city_list: {110100: '北京市',120100: '天津市'},
			// 	county_list: {110101: '东城区',110102: '西城区'},
			// }
			extractLevels(data) {
			  const result = new Map(); // 用于存储结果
			  const levels = { province_list: new Map(), city_list: new Map(), county_list: new Map() }; // 分别存储第一级、第二级和第三级label和value
			  // 递归函数
			  function traverse(arr, level) {
			    arr.forEach(item => {
			      // 根据层级将对象存储到对应的数组中
			      if (level === 1) {
			        levels.province_list.set([item.value], item.label);
			      } else if (level === 2) {
			        levels.city_list.set([item.value], item.label);
			      } else if (level === 3) {
			        levels.county_list.set([item.value], item.label);
			      }
			      // 如果存在 children，继续递归
			      if (item.children) {
			        traverse(item.children, level + 1);
			      }
			    });
			  }
			
			  // 从第一级开始遍历
			  traverse(data, 1);
			  // 将结果存储到 Map 对象中
			  result.set('province_list', Object.fromEntries(levels.province_list));
			  result.set('city_list', Object.fromEntries(levels.city_list));
			  result.set('county_list', Object.fromEntries(levels.county_list));
			  return Object.fromEntries(result);
			},
			//value=0改变省，1改变市，2改变区
			onAreaChange(picker) {
				let val = picker.getValues();
				let areaName = "";
				if (val[0] && val[1] && val[2]) {
					for (var i = 0; i < val.length; i++) {
						areaName = areaName + (i == 0 ? "" : "/") + val[i].name;
					}
					this.carmodel = areaName;
					this.setDefaultAddress = areaName;
				}
			},
			//确定选择城市
			onAreaConfirm(val) {
				console.log('onAreaConfirm', val, this.setDefaultCode)
				if (val[0].name === '海外') {
					if (val[1] && val[1].name) {
						this.selectorShow = false; //关闭弹框
						this.province = val[0].name;
						this.areaCode = val[0].code;
						this.city = val[1].name;
						let areaStr = val[0].name + "," + val[1].name;
						this.carmodel = areaStr;
						this.setDefaultAddress = areaStr;
						this.$emit('input', areaStr);
					} else {
						this.$toast("请选择地区");
					}
				} else {
					if (val[2] && val[2].name) {
						this.selectorShow = false; //关闭弹框
						this.province = val[0].name;
						this.areaCode = val[0].code;
						this.city = val[1].name;
						this.county = val[2].name;
						let areaStr = val[0].name + "," + val[1].name + "," + val[2].name;
						this.carmodel = areaStr;
						this.setDefaultAddress = areaStr;
						this.$emit('input', areaStr);
					} else {
						this.$toast("请选择地区");
					}
				}
			},
			//取消选中城市
			onAreaCancel() {
				// this.onInput(this.currentField, '');
				this.$emit('input', '');
				this.setDefaultAddressDetail = '';
				this.setDefaultAddress = '';
				this.setDefaultAddressStr = '';
				this.selectorShow = false;
				// this.onChangeShow(false);
				this.$refs.myArea.reset(); // 重置城市列表
			},
		},
		created() {
			this.getOptions();
		}
    }
</script>

<style>
    ::v-deep .van-checkbox__label {
        margin-left: 10px;
        color: #323233;
        line-height: 25px !important;
        width: 100% !important;
    }
</style>
