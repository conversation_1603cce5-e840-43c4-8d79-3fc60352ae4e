<template>
    <div>
        <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom"
                   get-container="body">
            <!-- <van-datetime-picker v-model="result"
                                 :type="type"
                                 :min-date="minDate"
                                 @cancel="onCancel()" @confirm="onConfirm"/> -->
					<van-datetime-picker
						v-model="selectorShow"
						v-model:values="dateTimeValues"
						type="datetime"
						:min-date="minDate"
						:max-date="maxDate"
						:formatter="formatter"
						@confirm="onConfirm"
						@cancel="onCancel"
					  />
        </van-popup>
    </div>

</template>

<script>
    export default {
        name: "ZZ_DateRangePicker",
        props: {
            value: Date,
            show: Boolean,
            type: {
                type: String,
                default() {
                    return 'datetime'
                }
            }
        },
        data() {
            return {
                result: this.type === 'time' ? '' : new Date(),
				dateTimeValues: ['', ''],
				minDate: new Date(2023, 0, 1),
				maxDate: new Date(2025, 11, 31)
            }
        },
        watch: {
            // show(newVal) {
            //     if (newVal) {
            //         this.initResults();
            //     }
            // }
        },
        computed: {},
        methods: {
			formatter (type, val) {
				console.log('type, val', type, val)
			  if (type === 'year') {
			    return `${val}年`;
			  } else if (type === 'month') {
			    return `${val}月`;
			  } else if (type === 'day') {
			    return `${val}日`;
			  } else if (type === 'hour') {
			    return `${val}时`;
			  } else if (type === 'minute') {
			    return `${val}分`;
			  } else if (type === 'second') {
			    return `${val}秒`;
			  }
			  return val;
			},
            // initResults() {
            //     console.log("ZZ_DatePicker initResults", this.value);
            //     if (this.value) {
            //         this.result = this.value;
            //     }
            // },
            onConfirm(value) {
                // console.log("ZZ_DatePicker onConfirm", value);
                this.$emit('update:value', value);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>

</style>
