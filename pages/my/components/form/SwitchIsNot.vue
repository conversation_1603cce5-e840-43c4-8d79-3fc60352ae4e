<template>
	<van-switch v-model="switchValue" :disabled="readonly" @change="onChange" size="20" />
</template>

<script>
	export default {
		props: {
			value: String,
			readonly: Boolean
		},
		data() {
			return {
				switchValue: false
			}
		},
		watch: {
			value(newVal) {
				console.log('newVal', newVal)
			    if (newVal) {
					this.switchValue = newVal == '是' ? true : false;
			    }
			}
		},
		methods: {
			onChange(value) {
				console.log('onChange', value)
				this.$emit("input", value ? "是" : "否")
			},
		}
	}
</script>

<style>

</style>