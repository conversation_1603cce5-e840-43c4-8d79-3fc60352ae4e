<template>
    <img @click="onImageView" :src="fileData" alt="" :width="width" :height="height">
</template>

<script>
    import {ImagePreview} from 'vant';
    // import {baseUrl} from "@/zizhu/utils/zizhu_http";
    // import {downloadFile} from "@/zizhu/utils/zizhu_api";

    export default {
        name: "ZZ_Image",
        props: {
            src: String,
            width: String,
            height: String,
            id: String,
        },
        computed: {
            fileData() {
                if (this.src) {
                    return this.src;
                }
                if (this.id) {
                    // return baseUrl + downloadFile + "?id=" + this.id;
					return this.id;
                }
                return null;
            }
        },
        methods: {
            onImageView() {
                ImagePreview({
                    images: [this.fileData],
                    closeOnPopstate: true
                });
            }
        }
    }
</script>

<style scoped>

</style>
