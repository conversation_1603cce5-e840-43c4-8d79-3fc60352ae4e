<template>
  <div>
      <van-popup :value="show"
                 v-on:input="onChangeShow($event)"
                 position="bottom"
                 get-container="body">
				<van-datetime-picker
				    v-model="result"
				    :type="'time'"
				    :title="title"
				    :min-date="minDate"
					@confirm="onConfirm"
					@cancel="onCancel"
					:filter="filter"
				  />
      </van-popup>
  </div>
</template>

<script>
    export default {
        name: "TimeRangePickerVue",
        props: {
            value: String,
            show: Boolean,
			title: {
				type: String,
				default(){
					return '选择时间'
				}
			},
			/**区分开始时间startTime/结束时间endtime*/
			timeKey: { 
				type: String,
				default(){
					return 'startTime'
				}
			},
            type: {
                type: String,
                default() {
                    return 'time'
                }
            },
			border: {
				type: [Boolean],
				default: true
			},
			disabled: {
				type: [Boolean],
				default: false
			},
        },
        data() {
            return {
                result: "",
                minDate: new Date(1900, 0, 1)
            }
        },
        watch: {
            show(newVal) {
                if (newVal) {
                    this.initResults();
                }
            }
        },
        computed: {},
        methods: {
            initResults() {
                console.log("ZZ_DatePicker initResults", this.value);
                if (this.value) {
                    this.result = this.value;
                }
            },
            onConfirm(value) {
				let obj = {
					timeKey: this.timeKey,
					value: value
				}
                console.log("ZZ_TimePicker onConfirm", obj);
                this.$emit('update:value', obj);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            },
			filter(type, options) {
			  console.log('this.type', this.type);
			  if(this.type == 'timeSelect') {
				  if (type === 'hour') {
				  	return options.filter((option) => option >= 9 && option <= 18);
				  }
				  if (type === 'minute') {
				  				return options.filter((option) => option % 30 === 0);
				  }
			  }
			  return options;
			},
        }
    }
</script>

<style scoped>

</style>
