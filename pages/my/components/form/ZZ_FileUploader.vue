<template>
    <div class="wrap">
        <van-popup 
			:value="show" :closeable="false"
            v-on:input="onChangeShow($event)"
            position="bottom">
		   <!-- #ifdef H5 || MP-WEIXIN -->
				<view class="example-body" style="margin: 8px" >
					<uni-file-picker
						:value="fileList"
						limit="9" 
						mode="grid"
						@select="onSelect"
						@delete="onDelete"
						@progress="progress" 
						@success="success" 
						@fail="fail" 
						:file-mediatype="fileType"
						title="最多选择9个文件"
					>
					</uni-file-picker>
				</view>
			<!-- #endif -->
            <div v-if="['image', 'video'].includes(fileType)" class="text-type">文件格式限制：{{ limitStr() }}</div>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
	import { Toast } from 'vant';
	import { Uploader, Image } from 'vant';
	import { isImage } from "../../utils/zizhu_file.js";
	import { imagesType, videosType } from "@/utils/common/common_file.js";
    export default {
        name: "ZZ_FileUploader",
		components: {
			[Uploader.name]: Uploader,
			[Image.name]: Image
		},
        props: {
            show: Boolean,
            fileType: {
				type: String,
				default() {
					// image/video/all
					return 'image'
				}
			},
        },
        data() {
            return {
                fileList: []
            }
        },
        watch: {
            show(newVal) {
                if (newVal) {
                    this.initResults();
                }
            }
        },
        computed: {
            fileTypeTrans() {
                if (this.fileType) {
                    return "*"
                }
                return "image/*"
            },
            types() {
                if (this.fileType) {
                    return this.fileType.split(',');
                }
                return null;
            },
        },
        methods: {
			limitStr() {
				return this.fileType == 'video' ? videosType.join('，') : imagesType.join('，')
			},
			onSelect(e) {
				console.log('选择文件', e);
				// this.fileList = e.tempFiles.map(file => ({
				// 	url: file.path, // 文件的临时路径
				// 	name: file.name, // 文件名
				// }));
				this.fileList.push(e.tempFiles[0]);
				console.log('this.fileList', this.fileList);
			},
			onDelete(e) {
				console.log('删除文件', e);
				this.fileList = this.fileList.filter(file => file.url !== e.tempFile.path);
				console.log('this.fileList', this.fileList);
			},
			// 获取上传进度
			progress(e){
				console.log('上传进度：',e)
			},
			
			// 上传成功
			success(e){
				console.log('上传成功')
			},
			
			// 上传失败
			fail(e){
				console.log('上传失败：',e)
			},
            onOversize(file) {
                console.log(file);
                this.$dialog.alert({
                    message: '文件大小不能超过 15M'
                })
            },
            initResults() {
                this.fileList = [];
            },
			afterRead(file) {
			  // 此时可以自行将文件上传至服务器
			  console.log('afterRead', file);
			  // file.status = 'done';
			  setTimeout(() => {
				file.status = 'done';  
			  }, 500)
			},
			// 返回布尔值
			beforeRead(file) {
			  if (file.type !== 'image/jpeg') {
				Toast('请上传 jpg 格式图片');
				return false;
			  }
			  return true;
			},
            onConfirm() {
				console.log('确定', this.fileList)
                if (this.fileList.length > 0) {
                    let files = [];
                    for (const f of this.fileList) {
                        if (this.types) {
                            // let type = f.file.name.split('.')[1];
                            // if (this.types.includes(type)) {
                                files.push(f);
                            // }
                        } else {
                            files.push(f);
                        }
                    }
					console.log('files', files)
                    this.$emit('input', files);
                }

                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
		}
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: space-between;
        align-items: center;
		gap: 10px;
		padding: 0 10px;
    }

    .multi-s-button {
        /* flex-grow: 1; */
		flex: 1; /* 每个按钮占据相同的宽度 */
        height: 35px;
        /* width: 45%; */
        /* margin-right: 2%; */
        line-height: 35px;
        font-size: 16px;
    }

    .text-type {
        margin-bottom: 4px;
        margin-left: 2px;
		padding-left: 8px;
        font-size: 1em;
        color: darkgrey;
    }
</style>
