<template>
    <div style="border-bottom: 1px solid #ebedf0 !important;">
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :required="required"
                   @click="field.readonly?'':selectorShow = true"
                   readonly
                   :placeholder="placeholder">
                   <!-- :value="valueShow" -->
				<template #input>
					<uni-datetime-picker
					  v-if="type == 'daterange' || type == 'datetimerange'"
					  v-model="timeRange"
					  :type="type"
					  start-placeholder="开始时间"
					  end-placeholder="结束时间"
					  :border="false"
					  :disabled="readonly"
					  @change="onTimeRangeChange"
					/>
					<uni-datetime-picker
					  v-else-if="type == 'date' || type == 'datetime'"
					  v-model="timeSingle"
					  :type="type"
					  :border="false"
					  :disabled="readonly"
					  @change="onSingleTimeChange"
					/>
				</template>
		</van-field>
    </div>
</template>

<script>
    import {dateFormat_YMD, diyDateFormat} from "@/pages/my/utils/my_moment";

    export default {
        name: "ZZ_FieldSelector",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            readonly: Boolean,
            label: String,
            selfeltype: String,
            type: {
                type: String,
                default() {
                    return 'date'
                }
            }
        },
        data() {
            return {
                selectorShow: false,
				timeRange: [], // 用于存储选择的时间范围
				timeSingle: ""
            }
        },
		watch: {
			value(newValue) {
				console.log('watch', newValue)
				if(newValue) {
					console.log('newValue', newValue);
					if(this.type == 'daterange' || this.type == 'datetimerange') {
						let list = newValue.split(',');
						if(list.length == 2) {
							list.map(i => {
								this.timeRange.push(i);	
							})
						}
					} else {
						this.timeSingle = newValue;
					}
				}
			}
		},
        computed: {
            selfEltype() {
                let eltype = ""
                switch (this.selfeltype) {
                    case 'yyyy-MM-dd HH:mm:ss':
                        eltype = 'YYYY-MM-DD HH:mm:ss';
                        break;
                    case 'yyyy-MM-dd HH:mm':
                        eltype = 'YYYY-MM-DD HH:mm';
                        break;
                    case 'yyyy-MM-dd':
                        eltype = 'YYYY-MM-DD';
                        break;
                    case 'yyyy-MM':
                        eltype = 'YYYY-MM'
                        break;
                    case 'HH:mm:ss':
                        eltype = 'HH:mm:ss';
                        break;
                    case 'HH:mm':
                        eltype = 'HH:mm';
                        break;
                    default:
                        eltype = 'YYYY-MM-DD'
                }
                return eltype
            },
            // valueShow() {
            //     if (this.value) {
            //         if (this.selfeltype === 'HH:mm:ss' || this.selfeltype === 'HH:mm') {
            //             return this.value;
            //         } else {
            //             return diyDateFormat(this.value, this.selfEltype);
            //         }
            //     }
            //     return '';
            // },
            valueBind() {
                if (this.value) {
                    return new Date(this.value);
                }
                return new Date();
            },
        },
		created(){
			console.log('ssssssss', this.value, this.type);
			// this.value = "2025-01-08 12:00:59,2025-01-08 13:10:09";
			if(this.value) {
				console.log('this.value', this.value);
				if(this.type == 'daterange' || this.type == 'datetimerange') {
					let list = this.value.split(',');
					if(list.length == 2) {
						list.map(i => {
							this.timeRange.push(i);	
						})
					}
				} else {
					this.timeSingle = this.value;
				}
			}
		},
        methods: {
			onTimeRangeChange(value) {
			  console.log("选择的时间范围：", value);
			  this.$emit("input", value.join(','));
			},
			onSingleTimeChange(value) {
			  console.log("选择的单个时间：", value);
			  this.$emit("input", value);
			},
            // onInput(value) {
            //     console.log(this.selfEltype)
            //     if (this.selfeltype === 'HH:mm:ss' || this.selfeltype === 'HH:mm') {
            //         this.$emit("input", this.selfeltype === 'HH:mm:ss' ? value + ':00' : value)
            //     } else {
            //         this.$emit("input", diyDateFormat(value, this.selfEltype))
            //     }
            // }
        }
    }
</script>

<style scoped>
::v-deep .uni-date__x-input, ::v-deep .uni-date-x .range-separator {
	height: 24px !important;
	line-height: 24px !important;
}
/* ::v-deep .uni-date-x--border {
	border: none;
} */
</style>
