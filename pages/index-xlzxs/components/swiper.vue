<template>
	<view class="index-top">
		<!-- <view class="indexbg ui-BG-Main ui-BG-transparent" :style="'margin-top: -'+(sys_navBar+sys_statusBar+sys_capsule.top)+'rpx;'">
		</view> -->
	
		<view class="ui-BG-card ui-BG" style="padding: 0; border-radius: none;">
			<!-- 搜索 -->
			<!-- <form @submit.stop.prevent="formSubmit" @reset.stop.prevent="formReset" id="search-form">
				<view class="ui-search-bar  "> -->
					<!-- <view class="ui-search-action">
						<ui-avatar slot="left" ui="round" src="/images/gravatar.png" />
					</view> -->
					<!-- <view class="ui-search-form   round">
						<text class="ui-search-icon _icon-search" />
						<input class="ui-search-input ui-search-text-left" value="" id="search-input" name="input"
							confirm-type="search" placeholder="搜索你感兴趣的内容..." @confirm="formSubmit" />
	
					</view>
					<view class="ui-search-action">
						<button slot="right" class="ui-btn ui-BG-Main round sm" form-type="submit">搜索</button>
					</view>
				</view>
			</form> -->
	
	
			<!-- 轮播图 -->
			<view class=" ">
				<ui-swiper :info="swipe_nav" v-if="swipe_nav.length > 0" dotStyle="long" dotCur="ui-BG-Main"
					height="352rpx" imgHeight="352rpx" imgName="image" swiperCss="radius"
					@uiTap="redictAppDetail" :isImage="false" :autoplay="autoplay" />
			</view>
		</view>
	</view>
</template>

<script>
	var Api = require("@/utils/api.js");
	var util = require("@/utils/util.js");

	var wxApi = require("@/utils/wxApi.js");
	var wxRequest = require("@/utils/wxRequest.js");
	import config from "@/utils/config";
	const Adapter = require("@/utils/adapter.js");
	export default {
		data(){
			return {
				swipe_nav: [],
				indicatorDots: true,
				autoplay: false,
				interval: 2000,
				duration: 500
			}
		},
		created(){
			this.getHomeconfig();
		},
		methods: {
			getHomeconfig() {
				//获取扩展设置
				var self = this;
				// var getHomeconfig = wxRequest.getRequest(Api.get_homeconfig());
				// getHomeconfig.then(res => {
				// 	console.log('get_homeconfig', res.data);
					// let expand = res.data.expand;
					
					let swipe_nav = [
						{
							image: require('@/static/home-icon/frame.png'),
							title: "banner图1",
							id: 1
						},
						{
							// image: 'https://cdn.uviewui.com/uview/swiper/swiper1.png',
							image: require('@/static/home-icon/frame.png'),
							title: "banner图2",
							id: 2
						},
						{
							// image: 'https://cdn.uviewui.com/uview/swiper/swiper2.png',
							image: require('@/static/home-icon/frame.png'),
							title: "banner图3",
							id: 3
						}
					];
					let swipe_list = [];
					swipe_nav.forEach((item)=>{
						let obj = {
							appid: "",
							enable: "yes",
							image: item.image,
							jumptype: "redirect",
							path: "/pages/detail/detail?id" + item.id,
							title: item.title,
							type: "apppage",
							unassociated: "yes",
							url: ""
						}
						swipe_list.push(obj);
					})
					// let selected_nav = expand.selected_nav;
					// let _d = res.data.downloadfileDomain;
					// let _b = res.data.businessDomain;
					// let zanImageurl = res.data.zanImageurl;
					// let logoImageurl = res.data.logoImageurl;
					// let downloadfileDomain = _d.length ? _d.split(',') : [];
					// let businessDomain = _b.length ? _b.split(',') : [];
					console.log('swipe', swipe_nav)
					
					self.setData({
						swipe_nav: swipe_list,
						// selected_nav,
						// selected_nav
					});
					// uni.setStorageSync('downloadfileDomain', downloadfileDomain);
					// uni.setStorageSync('businessDomain', businessDomain);
					// uni.setStorageSync('zanImageurl', zanImageurl);
					// uni.setStorageSync('logoImageurl', logoImageurl);
				// });
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
			
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	.index-top {
		position: relative;
	}
	
	.indexbg {
		position: absolute;
		top: -30rpx;
		width: 750rpx;
		height: 520rpx;
		z-index: -1;
	}
	.ui-BG-Main{
		background: linear-gradient(to bottom, --ui-BG-Main, --ui-BG-Main, white);
	}
	.ui-BG-card {
		margin: 0;
		border-radius: 0;
		box-shadow: none;
	}
</style>