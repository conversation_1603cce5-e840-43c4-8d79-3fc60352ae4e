export  default {
	colorList: ['#3B82F6','#F97316','#22C55E','#EF4444','#A855F7'],
	iconNameList: ['address-book','chart-histogram-two','remind','people','id-card'],
	service1: [{
		iconName: 'address-book',
		iconColor: '#3B82F6',
		theme: 'filled',
		// name: require('@/static/home-icon/notice.png'),
		type: 'image',
		title: '咨询协议'
	},
	{
		iconName: 'chart-histogram-two',
		iconColor: '#F97316',
		theme: 'filled',
		// name: require('@/static/home-icon/notice.png'),
		type: 'image',
		title: '咨询时间管理'
	},
	{
		iconName: 'remind',
		iconColor: '#EF4444',
		theme: 'filled',
		// name: require('@/static/home-icon/notice.png'),
		type: 'image',
		title: '咨询预约'
	},
	{
		iconName: 'people',
		iconColor: '#22C55E',
		theme: 'filled',
		// name: require('@/static/home-icon/学生查询.png'),
		type: 'image',
		title: '心理测试'
	},
	// {
	// 	iconName: 'id-card',
	// 	iconColor: '#A855F7',
	// 	theme: 'filled',
	// 	// name: require('@/static/home-icon/证件证明.png'),
	// 	type: 'image',
	// 	title: '证件证明'
	// },
	// {
	// 	iconName: 'calendar',
	// 	iconColor: '#3B82F6',
	// 	theme: 'filled',
	// 	// name: require('@/static/home-icon/请销假.png'),
	// 	type: 'image',
	// 	title: '请销假'
	// },
	// {
	// 	iconName: 'layers',
	// 	iconColor: '#F97316',
	// 	theme: 'filled',
	// 	// name: require('@/static/home-icon/活动管理.png'),
	// 	type: 'image',
	// 	title: '活动管理'
	// },
	{
		iconName: 'all-application',
		iconColor: '#6B7280',
		theme: 'filled',
		// name: require('@/static/home-icon/更多.png'),
		type: 'image',
		title: '更多'
	}
	],
	service2: [
		{
			number: 6,
			title: '待回复消息'
		},
		// {
		// 	number: 16,
		// 	title: '我的消息'
		// },
		// {
		// 	number: 35,
		// 	title: '待办消息'
		// },
		// {
		// 	number: 3,
		// 	title: '未读消息'
		// },
	],
	service3: [
		{
			name: '心理咨询项目',
			type: '2025-01-02',
			hours: 2.5
		},
		{
			name: '心理辅导项目',
			type: '2025-01-03',
			hours: 8
		},
		{
			name: '心理测评项目',
			type: '2025-01-04',
			hours: 4
		},
		// {
		// 	name: '赵星星',
		// 	type: '2025-01-05',
		// 	hours: 4
		// }
	],
	service4: [
		{
			name: require('@/static/home-icon/通讯录.png'),
			type: 'image',
			title: '通讯录'
		},
		{
			name: require('@/static/home-icon/学生统计.png'),
			type: 'image',
			title: '学生统计'
		},
		{
			name: require('@/static/home-icon/通知公告.png'),
			type: 'image',
			title: '通知公告'
		},
		{
			name: require('@/static/home-icon/学生查询.png'),
			type: 'image',
			title: '学生查询'
		},
		{
			name: require('@/static/home-icon/证件证明.png'),
			type: 'image',
			title: '证件证明'
		},
		{
			name: require('@/static/home-icon/请销假.png'),
			type: 'image',
			title: '请销假'
		},
		{
			name: require('@/static/home-icon/活动管理.png'),
			type: 'image',
			title: '活动管理'
		}
	],
	service5: [
		{
			number: 667,
			title: '待审批'
		},
		{
			number: 1560,
			title: '已审批'
		},
		{
			number: 350,
			title: '在假人数'
		},
		{
			number: 20,
			title: '销假逾期'
		},
	],
	service6: [
		{
			number: 501,
			title: '离校人数'
		},
		{
			number: 106,
			title: '留校人数'
		},
		{
			number: 320,
			title: '申请总人数'
		},
		{
			number: 280,
			title: '未申请人数'
		},
	],
	service7: [
		{
			number: 301,
			title: '进行中'
		},
		{
			number: 126,
			title: '未开始'
		},
		{
			number: 20,
			title: '已结束'
		},
		{
			number: '25%',
			title: '签到率'
		},
	],
	service8: [
		{
			number: 301,
			title: '评定总人数'
		},
		{
			number: 126,
			title: '评定金额数'
		},
		{
			number: 20,
			title: '评定项目数'
		},
		{
			number: '51%',
			title: '评定人数占比'
		},
	],
	service9: [
		{
			number: 211,
			title: '晚归人次'
		},
		{
			number: 105,
			title: '未归人次'
		},
		{
			number: 502,
			title: '卫生检查数'
		},
		{
			number: 21,
			title: '违纪次数'
		},
	],
	service10: [
		{
			number: 301,
			title: '入住总人数'
		},
		{
			number: 126,
			title: '床位总数'
		},
		{
			number: 20,
			title: '空床位数'
		},
		{
			number: '71%',
			title: '入住率'
		},
	],
	service11: [
		{
			number: 301,
			title: '进行中'
		},
		{
			number: 126,
			title: '未开始'
		},
		{
			number: 20,
			title: '已结束'
		},
		{
			number: '25%',
			title: '签到率'
		},
	],
	service12: [
		{
			number: 667,
			title: '待审批'
		},
		{
			number: 1560,
			title: '已审批'
		},
		{
			number: 350,
			title: '在假人数'
		},
		{
			number: 20,
			title: '销假逾期'
		},
	],
	service13: [
		{
			number: 61,
			title: '本月咨询'
		},
		{
			number: 161,
			title: '成功干预'
		},
		{
			number: 53,
			title: '平均满意度'
		},
		// {
		// 	number: 31,
		// 	title: '销假逾期数'
		// },
	],
	service14: [
		{
			number: 87,
			title: '宿舍卫生记录数'
		},
		{
			number: 94,
			title: '宿舍违纪记录数'
		},
		{
			number: 35,
			title: '晚归/未归记录数'
		},
		{
			number: 29,
			title: '宿舍报修记录数'
		},
	],
	service15: [
		{
			name: '张三',
			type: '213131405021',
			hours: '2025-01-02'
		},
		{
			name: '李文',
			type: '213131405022',
			hours: '2025-02-02'
		},
		{
			name: '王明',
			type: '213131405023',
			hours: '2025-01-03'
		},
		{
			name: '赵星星',
			type: '213131405024',
			hours: '2025-01-04'
		}
	],
	allServiceList: [
		{
			title: '常用服务',
			id: 'commonuse',
			dataList: [
				{
					iconName: 'address-book',
					iconColor: '#3B82F6',
					// name: require('@/static/home-icon/notice.png'),
					type: 'image',
					title: '通讯录'
				},
				{
					iconName: 'chart-histogram-two',
					iconColor: '#F97316',
					// name: require('@/static/home-icon/notice.png'),
					type: 'image',
					title: '学生统计'
				},
				{
					iconName: 'remind',
					iconColor: '#EF4444',
					// name: require('@/static/home-icon/notice.png'),
					type: 'image',
					title: '通知公告'
				},
				{
					iconName: 'people',
					iconColor: '#22C55E',
					// name: require('@/static/home-icon/学生查询.png'),
					type: 'image',
					title: '学生查询'
				},
				{
					iconName: 'id-card',
					iconColor: '#A855F7',
					// name: require('@/static/home-icon/证件证明.png'),
					type: 'image',
					title: '证件证明'
				},
				{
					iconName: 'calendar',
					iconColor: '#3B82F6',
					// name: require('@/static/home-icon/请销假.png'),
					type: 'image',
					title: '请销假'
				},
				{
					iconName: 'layers',
					iconColor: '#F97316',
					// name: require('@/static/home-icon/活动管理.png'),
					type: 'image',
					title: '活动管理'
				}
			]
		},
		{
			title: '日常服务',
			id: 'type1',
			dataList: [
			{
				iconName: 'wallet',
				iconColor: '#3B82F6',
				type: 'image',
				title: '奖学金'
			},
			{
				iconName: 'chart-histogram-two',
				iconColor: '#F97316',
				type: 'image',
				title: '优秀评选'
			},
			{
				iconName: 'calendar',
				iconColor: '#EF4444',
				type: 'image',
				title: '假期事务'
			},
			{
				iconName: 'degree-hat',
				iconColor: '#22C55E',
				type: 'image',
				title: '勤工助学'
			},
			{
				iconName: 'find',
				iconColor: '#A855F7',
				type: 'image',
				title: '带班查询'
			},
			{
				iconName: 'file-staff',
				iconColor: '#3B82F6',
				type: 'image',
				title: '请销假'
			}],
		},
		{
			title: "资助服务",
			id: 'type2',
			dataList: [
				{
					iconName: 'wallet',
					iconColor: '#3B82F6',
					type: 'image',
					title: '奖学金'
				},
				{
					iconName: 'wallet-two',
					iconColor: '#F97316',
					type: 'image',
					title: '助学金'
				},
				{
					iconName: 'paper-money',
					iconColor: '#EF4444',
					type: 'image',
					title: '贷学金'
				},
				{
					iconName: 'imbalance',
					iconColor: '#22C55E',
					type: 'image',
					title: '困难认定'
				},
				{
					iconName: 'set-off',
					iconColor: '#A855F7',
					type: 'image',
					title: '勤工俭学'
				},
				{
					iconName: 'bank',
					iconColor: '#3B82F6',
					type: 'image',
					title: '助学贷款'
				},
				{
					iconName: 'chart-histogram-two',
					type: 'image',
					iconColor: '#F97316',
					title: '资助统计'
				},
			]
		},
		{
			title: '宿管服务',
			id: 'type3',
			dataList: [
			{
				name: require('@/static/home-icon/通知公告.png'),
				iconColor: '#3B82F6',
				type: 'image',
				title: '住宿管理'
			},
			{
				name: require('@/static/home-icon/通讯录.png'),
				iconColor: '#F97316',
				type: 'image',
				title: '卫生检查'
			},
			{
				name: require('@/static/home-icon/学生统计.png'),
				iconColor: '#EF4444',
				type: 'image',
				title: '晚归登记'
			},
			{
				name: require('@/static/home-icon/请销假.png'),
				iconColor: '#22C55E',
				type: 'image',
				title: '资产报修'
			},
			{
				name: require('@/static/home-icon/活动管理.png'),
				iconColor: '#A855F7',
				type: 'image',
				title: '宿舍违纪'
			},
			{
				name: require('@/static/home-icon/请销假.png'),
				iconColor: '#3B82F6',
				type: 'image',
				title: '假期留校'
			},
			{
				name: require('@/static/home-icon/学生查询.png'),
				iconColor: '#F97316',
				type: 'image',
				title: '宿舍调整'
			}]
		},
		{
			title: '第二课堂',
			id: 'type4',
			dataList: [
			{
				name: require('@/static/home-icon/请销假.png'),
				iconColor: '#3B82F6',
				type: 'image',
				title: '第二课堂'
			},
			{
				name: require('@/static/home-icon/学生统计.png'),
				iconColor: '#F97316',
				type: 'image',
				title: '活动签到'
			},
			{
				name: require('@/static/home-icon/通知公告.png'),
				iconColor: '#EF4444',
				type: 'image',
				title: '活动管理'
			},
			{
				name: require('@/static/home-icon/学生统计.png'),
				iconColor: '#22C55E',
				type: 'image',
				title: '活动统计'
			},
			{
				name: require('@/static/home-icon/学生查询.png'),
				iconColor: '#A855F7',
				type: 'image',
				title: '活动审批'
			}]
		},
		{
			title: '综合测评',
			id: 'type5',
			dataList: [{
				name: require('@/static/home-icon/通讯录.png'),
				iconColor: '#3B82F6',
				type: 'image',
				title: '加分审核'
			},
			{
				name: require('@/static/home-icon/通知公告.png'),
				iconColor: '#F97316',
				type: 'image',
				title: '综测成绩'
			},
			{
				name: require('@/static/home-icon/证件证明.png'),
				iconColor: '#EF4444',
				type: 'image',
				title: '数据画像'
			}]
		}
	]
}
