<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="valueShow"
                   :required="required"
                   @click="field.readonly?'':selectorShow = true"
                   readonly
                   :placeholder="placeholder"/>
        <!--        <ZZ_Selector v-on:update:value="onInput($event)"-->
        <!--                     :columns="field.el && field.el.split('$')" :show.sync="selectorShow"></ZZ_Selector>-->
        <ZZ_DatePicker :value="valueBind" v-on:update:value="onInput($event)" :show.sync="selectorShow"
                       :type="type"></ZZ_DatePicker>
    </div>
</template>

<script>
    import ZZ_DatePicker from "./ZZ_DatePicker";
    import {dateFormat_YMD, diyDateFormat} from "@/pages/my/utils/my_moment";

    export default {
        components: {
            ZZ_DatePicker
        },
        name: "ZZ_FieldSelector",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: Boolean,
            label: String,
            selfeltype: String,
            type: {
                type: String,
                default() {
                    return 'date'
                }
            }
        },
        data() {
            return {
                selectorShow: false
            }
        },
        computed: {
            selfEltype() {
                let eltype = ""
                switch (this.selfeltype) {
                    case 'yyyy-MM-dd HH:mm:ss':
                        eltype = 'YYYY-MM-DD HH:mm:ss';
                        break;
                    case 'yyyy-MM-dd HH:mm':
                        eltype = 'YYYY-MM-DD HH:mm';
                        break;
                    case 'yyyy-MM-dd':
                        eltype = 'YYYY-MM-DD';
                        break;
                    case 'yyyy-MM':
                        eltype = 'YYYY-MM'
                        break;
                    case 'HH:mm:ss':
                        eltype = 'HH:mm:ss';
                        break;
                    case 'HH:mm':
                        eltype = 'HH:mm';
                        break;
                    default:
                        eltype = 'YYYY-MM-DD'
                }
                return eltype
            },
            valueShow() {
                if (this.value) {
                    if (this.selfeltype === 'HH:mm:ss' || this.selfeltype === 'HH:mm') {
                        return this.value;
                    } else {
                        return diyDateFormat(this.value, this.selfEltype);
                    }
                }
                return '';
            },
            valueBind() {
                if (this.value) {
                    return new Date(this.value);
                }
                return new Date();
            },
        },
        methods: {
            onInput(value) {
                console.log('onInput', this.selfEltype, diyDateFormat(value, this.selfEltype))
                if (this.selfeltype === 'HH:mm:ss' || this.selfeltype === 'HH:mm') {
                    this.$emit("input", this.selfeltype === 'HH:mm:ss' ? value + ':00' : value)
                } else {
                    this.$emit("input", diyDateFormat(value, this.selfEltype))
                }
            }
        }
    }
</script>

<style scoped>

</style>
