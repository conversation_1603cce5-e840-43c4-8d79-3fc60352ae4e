<template>
    <div>
        <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom">
            <van-cell-group style="margin-bottom: 55px;">
                <van-cell v-for="item in results"
                          :key="item.name"
                          @click="onItemClick(item)">
                    <van-checkbox slot="title" :value="item.checked">{{item.name}}</van-checkbox>
                </van-cell>
            </van-cell-group>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    export default {
        name: "ZZ_MultiSelector",
        props: {
            value: Array,
            show: Boolean,
            columns: Array
        },
        data() {
            return {
                results: []
            }
        },
        watch: {
            show(newVal) {
                if (newVal) {
                    this.initResults();
                }
            }
        },
        computed: {
            columnsShow() {
                if (this.columns) {
                    return this.columns;
                }
                return [];
            }
        },
        methods: {
            initResults() {
                let value = this.value;
                if (!value) {
                    value = [];
                }
                let arr = [];
                this.columnsShow.forEach(item => {
                    arr.push({name: item, checked: value.includes(item)})
                });
                this.results = arr;
            },
            onItemClick(model) {
                model.checked = !model.checked;
            },
            onConfirm() {
                let arr = [];
                this.results.forEach(item => {
                    if (item.checked) {
                        arr.push(item.name);
                    }
                });
                this.$emit('input', arr);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;

        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 100%;
        background-color: #fff;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        /*background: #00a5ec;*/
        font-size: 16px;
    }
</style>
