<template>
    <div>
        <van-popup :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom"
                   get-container="body">
            <van-datetime-picker v-model="result"
                                 :type="type"
                                 :min-date="minDate"
                                 @cancel="onCancel()" @confirm="onConfirm"/>
        </van-popup>
    </div>

</template>

<script>
    export default {
        name: "ZZ_DatePicker",
        props: {
            value: Date,
            show: Boolean,
            type: {
                type: String,
                default() {
                    return 'datetime'
                }
            }
        },
        data() {
            return {
                result: this.type === 'time' ? '' : new Date(),
                minDate: new Date(1900, 0, 1)
            }
        },
        watch: {
            // show(newVal) {
            //     if (newVal) {
            //         this.initResults();
            //     }
            // }
        },
        computed: {},
        methods: {
            // initResults() {
            //     console.log("ZZ_DatePicker initResults", this.value);
            //     if (this.value) {
            //         this.result = this.value;
            //     }
            // },
            onConfirm(value) {
                // console.log("ZZ_DatePicker onConfirm", value);
                this.$emit('update:value', value);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>

</style>
