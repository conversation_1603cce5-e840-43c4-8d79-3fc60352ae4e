<template>
    <div class="wrap">
        <van-popup :style="{ height: '100%' }"
                   :value="show"
                   v-on:input="onChangeShow($event)"
                   position="bottom">
            <div v-if="wxts" style="text-align: left;margin: 10px 3px 0 10px;color: red;">*{{wxts}}</div>
            <canvas class="app-sign-canvas" :id="domId" ref="myCanvas">
                您的浏览器不支持canvas技术,请升级浏览器!
            </canvas>
            <div style="text-align: center;">请在红框内签名!</div>
            <div class="multi-s">
                <van-button class="multi-s-button multi-s-button-right" type="info" @click="onCancel">取消</van-button>
                <van-button class="multi-s-button multi-s-button-right" type="info" @click="canvasClear">清空
                </van-button>
                <van-button class="multi-s-button multi-s-button-right" type="info" @click="saveAsImgOK">确认</van-button>
            </div>
        </van-popup>
    </div>
</template>


<script>
    export default {
        name: 'SignCanvas',
        model: {
            value: 'image',
            event: 'confirm'
        },
        props: {
            wxts: String,
            show: Boolean,
            image: {
                required: false,
                type: [String],
                default: null
            },
            options: {  //配置项
                required: false,
                type: [Object],
                default: () => null
            },
        },
        created() {
        },
        watch: {
            show(newVal) {
                if (newVal) this.init();
            },
            // context() {
            //     //温馨提示，做图片水印使用 wt20210913
            //     if (this.wxts) this.paintFixedWaterMark(this.wxts)
            // }
        },
        data() {
            return {
                value: null, //base64
                domId: `sign-canvas-${Math.random().toString(36).substr(2)}`,  //生成唯一dom标识
                canvas: null,    //canvas dom对象
                context: null,   //canvas 画布对象
                config: {
                    lastWriteSpeed: 1,  //书写速度 [Number] 可选
                    lastWriteWidth: 2,  //下笔的宽度 [Number] 可选
                    lineCap: 'round',   //线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square]	正方形线帽
                    lineJoin: 'round',  //线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
                    canvasWidth: 360, //canvas宽高 [Number] 可选
                    canvasHeight: 660,  //高度  [Number] 可选
                    isShowBorder: false, //是否显示边框 [可选]
                    bgColor: '#060505', //背景色 [String] 可选
                    borderWidth: 1, // 网格线宽度  [Number] 可选
                    borderColor: "#00a5ec", //网格颜色  [String] 可选
                    writeWidth: 5, //基础轨迹宽度  [Number] 可选
                    maxWriteWidth: 30, // 写字模式最大线宽  [Number] 可选
                    minWriteWidth: 5, // 写字模式最小线宽  [Number] 可选
                    writeColor: '#101010', // 轨迹颜色  [String] 可选
                    isSign: true, //签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
                    imgType: 'png'   //下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
                },
            };
        },
        methods: {
            paintFixedWaterMark(workWxts) {//在Vue中可改成ES6写法
                var twLength = this.context.measureText(workWxts).width;
                var wrap = document.createElement("div");//建立一个div
                wrap.className = "fixed-water-mark";//给div添加类名
                var wm = document.createElement("canvas");//单个水印画布
                wm.id = "watermark";//给canvas标签添加id
                wm.width = twLength + 60;//设置canvas宽
                wm.height = 130;//设置canvas高
                wm.style.display = "none";//设置画布隐藏属性
                wrap.appendChild(wm);//在div中添加画布
                var rwm = document.createElement("canvas");//重复绘制水印画布，用于整个页面
                rwm.id = "repeat-watermark";
                wrap.appendChild(rwm);
                document.body.appendChild(wrap);
                //绘制单个水印
                var cw = document.getElementById('watermark');
                var ctx = cw.getContext("2d");
                // var twLength = ctx.measureText(workWxts).width;
                ctx.clearRect(0, 0, wm.width, wm.height);//清空矩形
                ctx.font = "14px 黑体";//设置字体
                ctx.textBaseline = 'middle';//更改字号后，必须重置对齐方式，否则居中麻烦。设置文本的垂直对齐方式
                ctx.textAlign = 'left';
                ctx.rotate(-12 * Math.PI / 180);//逆时针旋转20度
                // ctx.fillStyle = "rgba(100,100,100,0.3)";//填充透明度为0.3的灰色
                ctx.fillStyle = "rgba(156,153,153,0.45)";//填充透明度为0.3的灰色
                // ctx.fillText(workWxts, 80, 85);//填充内容为 温馨提示信息
                // let xNum = wm.width / 2 - 23;
                // let yNum = wm.height - 5;
                // console.log(xNum,yNum)
                // ctx.fillText(workWxts, xNum, yNum);
                // 填充内容为 温馨提示信息
                this.printAt(ctx, workWxts, -10, 78, 24, wm.width-28);

                //在另外一个画布上重复绘制单个水印
                this.context.clearRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                this.context.fillStyle = "#fff";//填充透明度为0.3的灰色
                this.context.fillRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                var pat = this.context.createPattern(cw, "repeat");//在水平和垂直方向重复绘制单个水印
                this.context.fillStyle = pat;
                this.context.fillRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                setTimeout(function () {
                    document.body.removeChild(wrap);
                }, 500);
            },
            printAt(context, text, x, y, lineHeight, fitWidth) {
                fitWidth = fitWidth || 0;
                if (fitWidth <= 0) {
                    context.fillText(text, x, y);
                    return;
                }
                for (var idx = 1; idx <= text.length; idx++) {
                    var str = text.substr(0, idx);
                    if (context.measureText(str).width > fitWidth) {
                        context.fillText(text.substr(0, idx - 1), x, y);
                        this.printAt(context, text.substr(idx - 1), x, y + lineHeight, lineHeight, fitWidth);
                        return;
                    }
                }
                context.fillText(text, x, y);
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            },
            init() {
                this.$nextTick(() => {
                    this.canvas = this.$refs.myCanvas;
                    this.context = this.canvas.getContext("2d");
                    const options = this.options;
                    if (options) {
                        for (const key in options) {
                            this.config[key] = options[key];
                        }
                    }
                    this.canvasInit();
                    this.canvasClear();
                })
            },
            /**
             * 轨迹宽度
             */
            setLineWidth() {
                const nowTime = new Date().getTime();
                const diffTime = nowTime - this.config.lastWriteTime;
                this.config.lastWriteTime = nowTime;
                let returnNum = this.config.minWriteWidth + (this.config.maxWriteWidth - this.config.minWriteWidth) * diffTime / 30;
                if (returnNum < this.config.minWriteWidth) {
                    returnNum = this.config.minWriteWidth;
                } else if (returnNum > this.config.maxWriteWidth) {
                    returnNum = this.config.maxWriteWidth;
                }
                returnNum = returnNum.toFixed(2);
                //写字模式和签名模式
                if (this.config.isSign) {
                    this.context.lineWidth = this.config.writeWidth;
                } else {
                    this.context.lineWidth = this.config.lastWriteWidth = this.config.lastWriteWidth / 4 * 3 + returnNum / 4;
                }

            },
            /**
             * 绘制轨迹
             */
            writing(point) {
                this.context.beginPath();
                this.context.moveTo(this.config.lastPoint.x, this.config.lastPoint.y);
                this.context.lineTo(point.x, point.y);
                this.setLineWidth();
                this.context.stroke();
                this.config.lastPoint = point;
                this.context.closePath();
            },
            /**
             * 轨迹样式
             */
            writeContextStyle() {
                this.context.beginPath();
                this.context.strokeStyle = this.config.writeColor;
                this.context.lineCap = this.config.lineCap;
                this.context.lineJoin = this.config.lineJoin;
            },
            /**
             * 写开始
             */
            writeBegin(point) {
                this.config.isWrite = true;
                this.config.lastWriteTime = new Date().getTime();
                this.config.lastPoint = point;
                this.writeContextStyle();
            },
            /**
             * 写结束
             */
            writeEnd(point) {
                this.config.isWrite = false;
                this.config.lastPoint = point;
                this.saveAsImg();
            },
            /**
             * 清空画板
             */
            canvasClear() {
                this.context.save();
                this.context.strokeStyle = this.config.writeColor;
                this.context.clearRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                this.context.beginPath();
                let size = this.config.borderWidth / 2;
                if (this.config.isShowBorder) {
                    //画外面的框
                    this.context.moveTo(size, size);
                    this.context.lineTo(this.config.canvasWidth - size, size);
                    this.context.lineTo(this.config.canvasWidth - size, this.config.canvasHeight - size);
                    this.context.lineTo(size, this.config.canvasHeight - size);
                    this.context.closePath();
                    this.context.lineWidth = this.config.borderWidth;
                    this.context.strokeStyle = this.config.borderColor;
                    this.context.stroke();
                }
                if (this.config.isShowBorder && !this.config.isSign) {
                    //画里面的框
                    this.context.moveTo(0, 0);
                    this.context.lineTo(this.config.canvasWidth, this.config.canvasHeight);
                    this.context.lineTo(this.config.canvasWidth, this.config.canvasHeight / 2);
                    this.context.lineTo(this.config.canvasWidth, this.config.canvasHeight / 2);
                    this.context.lineTo(0, this.config.canvasHeight / 2);
                    this.context.lineTo(0, this.config.canvasHeight);
                    this.context.lineTo(this.config.canvasWidth, 0);
                    this.context.lineTo(this.config.canvasWidth / 2, 0);
                    this.context.lineTo(this.config.canvasWidth / 2, this.config.canvasHeight);
                    this.context.stroke();
                }
                this.$emit('confirm', null);
                this.context.restore();
                //温馨提示，做图片水印使用 wt20210913
                if (this.wxts) {
                    this.paintFixedWaterMark(this.wxts)
                } else {
                    this.context.clearRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                    this.context.fillStyle = "#fff";//填充透明度为0.3的灰色
                    this.context.fillRect(0, 0, this.config.canvasWidth, this.config.canvasHeight);
                }
            },
            /**
             *  保存图片 格式base64
             */
            saveAsImg() {
                const image = new Image();
                image.src = this.canvas.toDataURL("image/png");
                this.$emit('confirm', image.src);
                return image.src;
            },
            /**
             * 保存图片
             */
            saveAsImgOK() {
                const img = this.saveAsImg();
                let file = this.dataURLtoFile(img, "signature" + Math.random().toString(36).substr(2) + '.png');
                let filesObj = {
                    content: img,
                    file: file,
                    status: "",
                };
                let files = [];
                files.push(filesObj);
                // alert(`image 的base64：${img}`);
                console.log("saveAsImgOK====", files);
                this.$emit('input', files);
                this.onCancel();
            },
            /**
             * 将base64转换为文件对象
             */
            dataURLtoFile(dataurl, filename) {
                var arr = dataurl.split(',');
                var mime = arr[0].match(/:(.*?);/)[1];
                var bstr = atob(arr[1]);
                var n = bstr.length;
                var u8arr = new Uint8Array(n);
                while (n--) {
                    u8arr[n] = bstr.charCodeAt(n);
                }
                //转换成file对象
                return new File([u8arr], filename, {type: mime});
                //转换成成blob对象
                //return new Blob([u8arr],{type:mime});
            },
            /**
             * 初始化画板
             */
            canvasInit() {
                let that = this;
                that.canvas.width = that.config.canvasWidth;
                that.canvas.height = that.config.canvasHeight;
                that.config.emptyCanvas = that.canvas.toDataURL("image/png");
                that.bindEvent();
            },
            /**
             * 绑定事件
             */
            bindEvent() {
                let that = this;
                //鼠标按下 => 下笔
                that.canvas.addEventListener('mousedown', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    that.writeBegin({x: e.offsetX || e.clientX, y: e.offsetY || e.clientY});
                });
                //书写过程 => 下笔书写
                that.canvas.addEventListener('mousemove', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    that.config.isWrite && that.writing({x: e.offsetX, y: e.offsetY});
                });
                //鼠标松开 => 提笔
                that.canvas.addEventListener('mouseup', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    that.writeEnd({x: e.offsetX, y: e.offsetY});
                });
                //离开书写区域 => 提笔离开
                that.canvas.addEventListener('mouseleave', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    that.writeEnd({x: e.offsetX, y: e.offsetY});
                });
                /* ==========================移动端兼容=Start================================ */
                //手指按下 => 下笔
                that.canvas.addEventListener('touchstart', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const touch = e.targetTouches[0];
                    // const getBCR = touch.target.getBoundingClientRect();
                    const offsetLeft = that.offset(touch.target, 'left');
                    const offsetTop = that.offset(touch.target, 'top');
                    // const offsetLeft =  touch.target.offsetLeft;
                    // const offsetTop = touch.target.offsetTop;
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    that.writeBegin({x, y});
                });
                //手指移动 => 下笔书写
                that.canvas.addEventListener('touchmove', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const touch = e.targetTouches[0];
                    const offsetLeft = that.offset(touch.target, 'left');
                    const offsetTop = that.offset(touch.target, 'top');
                    // const offsetLeft = touch.target.offsetLeft;
                    // const offsetTop = touch.target.offsetTop;
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    // console.log(touch)
                    that.config.isWrite && that.writing({x, y});
                });
                //手指移动结束 => 提笔离开
                that.canvas.addEventListener('touchend', (e) => {
                    e && e.preventDefault() && e.stopPropagation();
                    const tcs = e.targetTouches;
                    const ccs = e.changedTouches;
                    const touch = tcs && tcs.length && tcs[0] || ccs && ccs.length && ccs[0];
                    const offsetLeft = that.offset(touch.target, 'left');
                    const offsetTop = that.offset(touch.target, 'top');
                    // const offsetLeft = touch.target.offsetLeft;
                    // const offsetTop = touch.target.offsetTop;
                    let x = touch.clientX ? touch.clientX - offsetLeft : touch.clientX;
                    let y = touch.clientY ? touch.clientY - offsetTop : touch.clientY;
                    that.writeEnd({x, y});
                });
                /* ==========================移动端兼容=End================================ */
            },
            /**
             * 下载二维码到本地
             */
            downloadSignImg(name) {
                // const c = document.getElementById(this.domId);
                const c = this.$refs.myCanvas;
                const dataURL = c.toDataURL('image/png');
                this.saveFile(dataURL, name ? `${name}.${this.config.imgType}` : `${Date.parse(new Date())}.${this.config.imgType}`);
            },
            /**
             * 保存文件
             */
            saveFile(data, filename) {
                const saveLink = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
                saveLink.href = data;
                saveLink.download = filename;
                const event = document.createEvent('MouseEvents');
                event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
                saveLink.dispatchEvent(event);
            },
            /**
             * 获取dom对象的偏移量 可以获取解决position定位的问题
             */
            offset(obj, direction) {
                //将top,left首字母大写,并拼接成offsetTop,offsetLeft
                const offsetDir = 'offset' + direction[0].toUpperCase() + direction.substring(1);
                let realNum = obj[offsetDir];
                let positionParent = obj.offsetParent;  //获取上一级定位元素对象
                while (positionParent != null) {
                    realNum += positionParent[offsetDir];
                    positionParent = positionParent.offsetParent;
                }
                return realNum;
            }
        }
    }
    ;
</script>

<style scoped>
    .multi-s {
        display: flex;
        width: 100%;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        font-size: 16px;
    }

    /*.multi-s-button-ceter {*/
    /*    !*border-color: #00A5EC;*!*/
    /*    !*color: #00A5EC;*!*/
    /*}*/

    .multi-s-button-right {
        background-color: #00A5EC;
        border-color: #00A5EC;
    }

    .app-sign-canvas {
        /*display: block;*/
        margin: 12px;
        border: 1px dashed #f00;
        /*position: fixed;*/
        z-index: -1;
        top: 0;
        background: #fff;

    }


    .fixed-water-mark {
        position: fixed;
        pointer-events: none;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1600;
        background: red;
    }

    .fixed-water-mark #watermark {
        text-align: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        opacity: 0.4;
        margin: 0 auto;
        background: #ffa200;
    }
</style>
