<!-- form表单展示各种类型的字段 -->
<template>
	<view class="container" style="padding: 12px;">
		<!-- :key="index"
		:title="fieldGroup.type?fieldGroup.type:title"
		:class="setClassMark" -->
		<van-cell-group >
			<template v-for="(field, index) in initItems">
				<!--  -->
				<van-field 
					v-if="field.type === 'input'"
				   :error="field.style?true:false"
				   :error-message="getErrorMessage(field.prop)"
				   :key="field.id"
				   :readonly="field.readonly"
				   :maxlength="field.maxLength ? field.maxLength : null"
				   :type="returnType(field)"
				   :label="field.label"
				   :value="initModel[field.prop]"
				   v-on:input="onInput(field, $event)"
				   :required="field.required"
				   :placeholder="returnPlaceholder(field)"/>
				<van-field 
				   v-else-if="field.type === 'inputNumber'"
				   type="number"
				   :readonly="field.readonly"
				   :error-message="getErrorMessage(field.prop)"
				   :key="field.id"
				   :label="field.label"
				   :value="initModel[field.prop]"
				   :placeholder="returnPlaceholder(field)"
				   v-on:input="onInput(field, $event)"
				   :required="field.required" />
				<van-field 
					v-else-if="field.type === 'rate'" 
					:required="field.required" 
					:label="field.label">
					<template slot="input">
						<uni-rate
							:value="initModel[field.prop]" 
							:allow-half="false" 
							:touchable="true" 
							color="#bbb" 
							:size="22" 
							margin="8"
							:max="5"
							:disabled="field.showFlag === 'readonly'"
							:readonly="field.showFlag === 'readonly'"
						/>
					</template>
				</van-field>
				<van-cell 
					v-else-if="field.type === 'stepper'"
					:error-message="getErrorMessage(field.prop)"
					:key="field.id"
					:title="field.label"
					:placeholder="returnPlaceholder(field)"
					:required="field.required"
					:readonly="field.type==='readonly'">
						<van-stepper :value="initModel[field.prop]" input-width="80px" button-size="32px" min="-1000"
							v-on:input="onInput(field, $event)"
							step="0.5"
							:decimal-length="2"/>
				</van-cell>
				<ZZ_FieldDatePicker 
					v-else-if="field.type === 'date' || field.type === 'sdate' || field.type === 'edate'"
					:errorMessage="getErrorMessage(field.prop)"
					:field="field"
					:key="field.id+'datePicker'"
					:type="field.type"
					:selfeltype="field.selfeltype"
					:value="initModel[field.prop]"
					:label="field.label"
					v-on:input="onInput(field, $event)"
					:required="field.required"
					:placeholder="returnPlaceholder(field)">
				</ZZ_FieldDatePicker>
										
										
			</template>
		</van-cell-group>
	</view>
</template>

<script>
	var Api = require('@/utils/api.js');
	var wxRequest = require('@/utils/wxRequest.js');
	// var util = require('@/components/ProForm/util.js')
	import {
	    stringTypes,
	    arrayTypes,
	    selectTypes,
	    uploadTypes,
	    regionsArrayTypes,
	    baseCodeTypes
	  } from '@/components/ProForm/util';
	import ZZ_FieldDatePicker from './ZZ_FieldDatePicker.vue';
	// import MyFieldDatePicker from './MyFieldDatePicker.vue';
	// import FileUploaderSignature from './FileUploaderSignature.vue';
	export default {
		props: {
			/** 表单左侧label宽度 */
			labelWidth: {
			  type: String,
			  default: '75'
			},
			/** 表单label对齐方式: top/left */
			labelPosition: {
			  type: String,
			  default: 'top'
			},
			/** 当前组的信息*/
			currentGroup: {
				type: Object,
				default() {
					return {}
				}
			},
			/** 用户的信息 */
			userInfo: {
				type: Object,
				default() {
					return {}
				}
			},
			/**角色名称 student/teacher*/
			userType: {
				type: String,
				default: ''
			},
		},
		components: {
			ZZ_FieldDatePicker,
			// FileUploaderSignature,
			// MyFieldDatePicker
		},
		data(){
			return {
				// formData: {},
				rangeList: [{"value": 0,"text": "篮球"	},{"value": 1,"text": "足球"},{"value": 2,"text": "游泳"}],
				fieldData: [], /**所有字段list*/
				initItems: [], /** 表单项 */
				initModel: {}, /**form表单*/
				currentUserData: null, // 当前人的信息
				errorMap: new Map(),
				grade: 0,
			}
		},
		computed: {
		},
		watch: {
			// selectedCities(newVal) {
			// 	console.log('selectedCities', newVal);
			// }
		},
		created(){
			// this.formData = this.userInfo;
			this.currentUserData = this.userInfo ? this.userInfo : null;
			this.getData();
			console.log('this.currentGroup', this.currentGroup, baseCodeTypes);
			// console.log('this.formData', this.formData, this.userInfo);
		},
		// watch: {
		// 	currentGroup(newVal){
		// 		console.log('currentGroup', newVal)
		// 		if(newVal) {
		// 			this.getData();
		// 		}
		// 	},
		// },
		methods: {
			/** 监控input事件 */
			onInput(field, value) {
				const key = field.prop;
				this.initModel[key] = value;
				console.log('onInput', field, value, this.initModel);
				// let newObj = Object.assign({}, this.value);
				// newObj[key] = value;
				// if (key === 'location_address') {
				// 	newObj['location_longitude'] = this.longitude;
				// 	newObj['location_latitude'] = this.latitude;
				// }
				// this.$emit("input", newObj);
				// this.onChangePush(field, value);
				// setTimeout(() => {
				// 	this.onStartChange();
				// }, this.setLoadingTime ? this.setLoadingTime : 3000);
				// this.fieldCheck(field, value);
			},
			handleChange(value) {
			  console.log('Selected cities:', value, this.initModel);
			},
			getErrorMessage(key) {
				return this.errorMap.get(key);
			},
			returnType(field) {
                const fieldType = field.type;
                if (["clob", "string_l"].includes(fieldType)) {
                    return "textarea";
                }
                if (field.id === 'location_address') {
                    return "textarea";
                }
                if (field.en === "bjs" || field.en === "njs") {
                    return "textarea";
                }
                if (["integer", "float"].includes(fieldType)) {
                    return "number";
                }
            },
            returnPlaceholder(field) {
                if (!field.readonly) {
                    let fieldType = field.type;
                    let wxts = field.wxts;
                    if (fieldType === "readonly" || fieldType === "signature") return "";
                    if (["single", "more", "init", "date"].includes(fieldType)) return wxts ? wxts : "请选择";
                    else if (["file"].includes(fieldType)) return wxts ? wxts : "请上传";
                    else if (["float"].includes(fieldType)) return wxts ? wxts : "请输入数字(支持小数)";
                    else if (["integer"].includes(fieldType)) return wxts ? wxts : "请输入数字";
                    else return wxts ? wxts : "请输入";
                } else {
                    return null;
                }
            },
			returnPlaceholder(field) {
                if (!field.readonly) {
                    let fieldType = field.type;
                    let wxts = field.wxts;
                    if (fieldType === "readonly" || fieldType === "signature") return "";
                    if (["single", "more", "init", "date"].includes(fieldType)) return wxts ? wxts : "请选择";
                    else if (["file"].includes(fieldType)) return wxts ? wxts : "请上传";
                    else if (["float"].includes(fieldType)) return wxts ? wxts : "请输入数字(支持小数)";
                    else if (["integer"].includes(fieldType)) return wxts ? wxts : "请输入数字";
                    else return wxts ? wxts : "请输入";
                } else {
                    return null;
                }
            },
			//获取个人信息子菜单
			getData() {
			  var self = this;
			  // uni.showLoading({
			  //   title: '查询中',
			  //   mask: true
			  // });
			  let data = {
				  groupId: this.currentGroup.id, groupName: this.currentGroup.groupName, tempField: '否'
			  }
			  console.log('self.userType', self.userType)
			  let url = Api.getTFieldListByGroup();
			  if(self.userType == 'student') {
				  url = Api.getSFieldListByGroup();
			  }
			  var getFieldGroupListRequest = wxRequest.getRequest(url, data);
			  getFieldGroupListRequest.then(response => {
				console.log('bygroup >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let list = resData.data;
						self.setData({
							fieldData: list
						})
						if (list) {
						  this.formatFormData(list)
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  // self.fetchCodeImgData();
			    }, 1000);
			  });
			},
			formatFormData(fieldData) {
			    let fieldResult = [];
			    if (fieldData && fieldData.length > 0) {
			      fieldData.forEach((e, index) => {
			        let props = {};
			        let selfFieldLink = '';
			        let nextField = '';
			        let typeKey = e.controlType;
			        let type = e.controlType;
			        if (baseCodeTypes.includes(e.controlType)) {
			          if (e.loadDataType && e.loadDataType !== 'other') {
			            type = 'dictSelect';
			            props = {
			              code: e.loadDataType,
			              dicQueryParams: {
			                getValType: '',
			                valueField: e.valueField,
			                textField: e.textField
			              },
			              filterable: true
			            };
			            if (!e.valueField && !e.textField) {
			              props.dicQueryParams.getValType =
			                e.loadDataType !== 'pycc' ? 'name' : '';
			            }
			          } else {
			            //院系专业班级页面逻辑处理
			            type = 'dictSelect';
			            let diyCode = '';
			            if (e.loadDataUrl === '/code/codeDwb') {
			              diyCode = 'xymc';
			              selfFieldLink = 'xymc';
			              nextField = 'zymc';
			            } else if (e.loadDataUrl === '/code/codeZyb') {
			              diyCode = 'zymc';
			              selfFieldLink = 'zymc';
			              nextField = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeBjb') {
			              diyCode = 'bjmc';
			              selfFieldLink = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeCommon/regionList') {
			              diyCode = 'regions';
			            } else {
			              diyCode = e.loadDataType;
			            }
			            props = {
			              code: diyCode,
			              filterable: true,
			              dicQueryParams: {
			                dictFieldUrl: e.loadDataUrl,
			                valueField: e.valueField,
			                textField: e.textField
			              }
			            };
			          }
			        } else if (
			          e.controlType === 'fileUpload' ||
			          e.controlType === 'imageUpload'
			        ) {
			          //附件上传数量
			          props = {
			            limit: e.regExpression ? Number(e.regExpression) : 1
			          };
			        }
			        let newObj = Object.assign(
			          {
			            key: 'fieldKey_' + index,
			            label: e.fieldZh,
			            prop: e.fieldEn,
			            type: type,
			            typeKey: typeKey,
			            required: e.required === '是',
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            fieldLinks: e.fieldLinks,
			            selfFieldLink: selfFieldLink,
			            nextField: nextField,
			          },
			          {
			            props: props
			          }
			        );
			        fieldResult.push(newObj);
			
			        if (e.controlType === 'regions') {
			          //省市区+详细地址信息
			          fieldResult.push({
			            key: 'fieldKey_99' + index,
			            label: '详细地址',
			            prop: e.fieldEn + '_regionsDetail',
			            type: 'input',
			            typeKey: 'input',
			            required: false,
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            colProps: 8
			          });
			        }
			      });
			      this.initItems = fieldResult;
				  console.log('this.initItems', this.initItems)
			    }
			    let fieldFormResult = {};
			    if (fieldResult && fieldResult.length > 0) {
			      fieldResult.forEach((item) => {
			        if (item.typeKey) {
			          if (
			            uploadTypes.includes(item.typeKey) ||
			            regionsArrayTypes.includes(item.typeKey) ||
			            arrayTypes.includes(item.typeKey)
			          ) {
			            fieldFormResult[item.prop] = [];
			          }
			          if (
			            stringTypes.includes(item.typeKey) ||
			            selectTypes.includes(item.typeKey)
			          ) {
			            fieldFormResult[item.prop] = '';
			          }
			        }
			      });
				  console.log('form', fieldFormResult)
			      this.initModel = fieldFormResult;
			    }
				if (this.currentUserData) {
				  console.log('fieldResult', fieldResult, this.currentUserData)
			      if (fieldResult && fieldResult.length > 0) {
			        fieldResult.forEach((item) => {
			          let setValue = null;
			          let dataValue = this.currentUserData
			            ? this.currentUserData[item.prop]
			            : null;
					  console.log('dataValue', dataValue)
			          if (dataValue) {
			            if (regionsArrayTypes.includes(item.typeKey)) {
			              let regionsArr = dataValue.split(',');
			              if (item.typeKey === 'regions') {
			                if (regionsArr.length > 0) {
			                  if (regionsArr[3]) {
			                    this.initItems.filter((f) => {
			                      if (f.prop === item.prop + '_regionsDetail') {
			                        this.initModel[f.prop] = regionsArr[3];
			                      }
			                    });
			                    regionsArr.splice(regionsArr.length - 1, 1);
			                    setValue = regionsArr;
			                  } else {
			                    setValue = regionsArr;
			                  }
			                }
			              } else {
			                setValue = regionsArr;
			              }
			            } else if (
			              item.typeKey === 'imageUpload' ||
			              item.typeKey === 'smoothSignature'
			            ) {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    return {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      url: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                  })
			                : [];
			            } else if (item.typeKey === 'fileUpload') {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    let newObj = {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      fileUrl: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                    let mark = isImageFile(newObj);
			                    newObj.isImageFile = mark;
			                    if (mark) newObj.url = newObj.fileUrl;
			                    return newObj;
			                  })
			                : [];
			            } else if (
			              item.typeKey === 'multipleSelect' &&
			              this.currentGroup.enType !== 'conditionSet'
			            ) {
			              //多选值处理
			              setValue = dataValue.split(',');
			            } else {
			              setValue = dataValue;
			            }
			          }
					  console.log('setValue', setValue);
			          if (setValue) this.updateFormValue(item, item.prop, setValue);
			        });
			      }
			    }
			  },
			  /** 更新表单数据 */
				updateFormValue (item, prop, newVal, mark) {
			      let value = newVal;
			      this.initModel[item.prop] = value;
				  console.log('更新后', this.initModel)
			      if (item.selfFieldLink) {
			        //院系专业班级三个字段页面逻辑特殊处理
			        nextTick(() => {
			          this.initModel.filter((f) => {
			            if (f.selfFieldLink === item.nextField) {
			              f.props['refresh'] = value; //watch每次刷新，重新请求
			              if (item.nextField === 'zymc') {
			                f.props.dicQueryParams['params'] = { xyid: value };
			              }
			              if (item.nextField === 'bjmc') {
			                f.props.dicQueryParams['params'] = { zyid: value };
			              }
			            }
			          });
			        });
			      }
			      if (item.fieldLinks) {
			        //获取选择值下面所有的显示字段信息
			        let showfields = item.fieldLinks.filter((f) => {
			          return f.fieldVal === value;
			        });
			        if (showfields.length > 0) {
			          // 联动表达式
			          showfields.forEach((sf) => {
			            nextTick(() => {
			              //切换选择值，清空之前选项所赋值,判断是否是手动切换
			              // if (!["preview", "sqpreview"].includes(props.currentData.enType) && mark === 'change') initModel.value[sf.linkField] = '';
			              if (
			                !['preview', 'sqpreview'].includes(this.currentGroup.enType) &&
			                mark === 'change'
			              )
			                this.initModel[sf.linkField] = '';
			              //动态切换选择值，关联具体字段的显示隐藏
			              this.initModel.filter((init) => {
			                if (sf.linkField === init.prop) {
			                  if (sf.linkFieldDataType) {
			                    // init.showFlag = ["preview", "sqpreview"].includes(props.currentData.enType) ? "readonly" : sf.showFlag
			                    init.showFlag = ['preview', 'sqpreview'].includes(
			                      this.currentGroup.enType
			                    )
			                      ? 'readonly'
			                      : sf.showFlag;
			                    init.props = {
			                      code: sf.linkFieldDataType,
			                      filterable: true
			                    };
			                  } else {
			                    init.showFlag = sf.showFlag;
			                  }
			                }
			                init.selfModifyFlag = init.showFlag;
			              });
			            });
			          });
			        }
			      }
			      //统一入口处理只读
			      this.initItems.filter((init) => {
			        // if (["preview", "sqpreview"].includes(props.currentData.enType)) {
			        if (['preview', 'sqpreview'].includes(this.currentGroup.enType)) {
			          init.showFlag = 'readonly';
			        }
			        init.selfModifyFlag = init.showFlag;
			      });
				  console.log('统一入口处理', this.initItems)
			      // if (this.currentGroup && this.currentGroup.listFlag !== '是') {
			      //   emit('onDoneGroup', {
			      //     groupId: this.currentGroup.id,
			      //     groupName: this.currentGroup.title,
			      //     values: this.initModel
			      //   });
			      // }
			    },
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	.uni-forms-item {
		// margin-bottom: 0;
	}
	.vertical-apply .van-cell__right-icon {
	    margin-top: -90px;
	    float: right;
	}
	
	.divDisplayBlack {
	    display: block;
	}
	
	.divDisplayNone {
	    display: none;
	}
	
	.van-submit-bar {
	    display: flex;
	    height: 50px;
	    border-top: 1px solid #eee;
	    box-shadow: 0 -2px 3px -1px #eee;
	}
	
	.van-submit-bar-button {
	    height: 35px;
	    width: 45.5% !important;
	    line-height: 35px;
	    margin-left: 3% !important;
	    background: #00a5ec;
	    font-size: 16px;
	    border-color: #00a5ec;
	}
	
	.vanButton {
	    background: #fff;
	    color: #00a5ec;
	}
	
	.vertical-apply >>> .van-cell {
	    font-size: 15px;
	}
	
	.vertical-apply >>> .van-field {
	    display: block;
	    font-size: 15px;
	}
	
	.vertical-apply >>> .van-field .van-field__label {
	    width: 100%;
	    margin-bottom: 5px;
	    color: #646566 !important;
	}
	
	.vertical-apply >>> .van-radio {
	    margin-bottom: 5px;
	}
</style>