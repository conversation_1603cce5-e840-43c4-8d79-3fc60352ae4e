<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="value"
                   :required="required"
                   @click="selectorShow = true"
                   :readonly="true"
                   :placeholder="placeholder">
            <template v-if="field.readonly">
                <van-radio-group :value="value"></van-radio-group>
            </template>
            <template v-else>
                <template v-if="!isShowSelect" slot="input">
                    <!-- direction="horizontal" 一行排列  单选 女子学院-->
                    <van-radio-group :value="value" v-on:input="onInput">
                        <van-radio @click="handle(index)"
                                   :key="index"
                                   v-for="(item, index) in setOptions"
                                   :name="item">{{item}}
                        </van-radio>
                    </van-radio-group>
                </template>
                <template v-else slot="input">
                    {{value?value:placeholder}}
                    <ZZ_Selector v-if="isShowSelect"
                                 v-on:update:value="onInput($event)"
                                 :columns="setOptions"
                                 :show.sync="selectorShow"></ZZ_Selector>
                </template>
            </template>
        </van-field>
    </div>
</template>

<script>
    import ZZ_Selector from "@/zizhu/components/form/ZZ_Selector";
    import {post} from "@/zizhu/utils/zizhu_http";
    import {queryCodeNoLimit as qcnlAPI} from "@/zizhu/utils/zizhu_api";
    import {queryCode as qcAPI} from "@/common/utils/common_api";

    export default {
        components: {ZZ_Selector},
        name: "ZZ_FieldSelector",
        props: {
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: Array,
            field: Object,
        },
        data() {
            return {
                selectorShow: false,
                radioShow: false,
                nowVal: "",
                oldVal: "",
                mark: 1,
                same: false,
                // setOptions: [],
            };
        },
        computed: {
            isShowSelect() {
                return this.setOptions && this.setOptions.length > 5;
            },
            setOptions() {
                return this.options ? this.options : [];
            }
        },
        methods: {
            queryCode() {
                let field = this.field;
                if (field && field.type === 'init') {
                    if (field.el) {
                        if (!field.yzgz) {
                            post(qcnlAPI, {code: field.en}).then(response => {
                                let rData = response.data;
                                if (rData) {
                                    for (const x of rData) {
                                        this.setOptions.push(x.name);
                                    }
                                }
                            });
                        }
                    } else {
                        let json = {}, code = '';
                        let setUrl = null;
                        //籍贯，校区,类型
                        if (field.en === "jg" || field.en === "xqmc" || field.en === "zxslx") setUrl = '/syt/qywx/teacherinfo/queryjg.htm';
                        //教师类型
                        else if (field.en === "jslx") setUrl = "/syt/qywx/teacherinfo/queryjslx.htm";
                        //期刊类别
                        else if (field.en === "qklb") setUrl = "/syt/qywx/teacherinfo/queryqklbb.htm";
                        //学生培养层次
                        else if (field.en === "pyccb") setUrl = "/syt/qywx/teacherinfo/querypyccb.htm";
                        else if (field.en === "dwmc") setUrl = "/syt/hdsb/querydwb.htm";
                        else {
                            setUrl = qcAPI;
                        }
                        if (field.en === 'mzmc') code = "CodeMzb";
                        // if (field.en === 'dwmc') code = "CodeDwb";
                        if (field.en === 'zymc') code = "CodeZyb";
                        if (field.en === 'pyccb' || field.en === "pyccmc") code = "CodePyccb";//培养层次
                        if (field.en === 'xl') code = "CodeXlb";
                        if (field.en === 'zzfdylb') code = "CodeZzfdylb";
                        if (field.en === 'zyjszw') code = "CodeZyjs";
                        if (field.en === 'xzzw') code = "CodeXzzw";
                        if (field.en === 'zzmmmc') code = "CodeZzmmb";//政治面貌

                        if (setUrl) {
                            json.code = code;
                            post(setUrl, json).then(response => {
                                let resData = "";
                                let rData = response.data;
                                resData = rData;
                                if (field.en === "jg") resData = rData.jgList;
                                if (field.en === "xqmc") resData = rData.xqList;
                                if (field.en === "zxslx") resData = rData.zxslxList;
                                if (resData) {
                                    for (const x of resData) {
                                        this.setOptions.push(x.name);
                                    }
                                }
                            });
                        }
                    }
                }
            },
            handle(index) {
                if (this.oldVal == this.nowVal) {
                    this.nowVal = "";
                    this.oldVal = "";
                    this.same = true;
                    this.onInput(this.nowVal);
                    this.mark = 1;
                } else {
                    this.same = false;
                    this.nowVal = this.setOptions[index];
                    this.onInput(this.nowVal);
                    this.oldVal = this.nowVal;
                }
                if (this.mark) {
                    this.mark = 0;
                    if (this.oldVal == "") {
                        this.oldVal = this.setOptions[index];
                        this.nowVal = this.setOptions[index];
                    }
                }
            },
            onInput(value) {
                this.nowVal = value;

                if (this.same) {
                    this.nowVal = "";
                }
                this.$emit("input", this.nowVal);
            }
        },
        created() {
            this.queryCode();
        }
    };
</script>

<style scoped>
</style>
