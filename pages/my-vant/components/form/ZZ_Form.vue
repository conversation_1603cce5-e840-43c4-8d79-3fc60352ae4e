<template>
    <section v-if="fieldGroupFields">
        <!--  class="vertical-apply" 表单上下两行排列   -->
        <template v-for="(fieldGroup,index) in fieldGroupFields">
            <!--                :title="fieldGroup.type?fieldGroup.type:title"-->
            <van-cell-group :key="index"
                            :title="fieldGroup.type?fieldGroup.type:title"
                            :class="setClassMark">
                <template v-for="field in fieldGroup.list">
                    <div :class="(field.isDisplay!=null?field.isDisplay:field.defaultDisplay)==='none'?'divDisplayNone':'divDisplayBlack'"
                         v-if="field.en==='jtdz' || field.en==='txdz' || field.type === 'address'"
                         :key="field.id">
                        <van-field :label="field.zh"
                                   :value="value[field.en]"
                                   :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                   placeholder="选择省/市/区"
                                   type="textarea"
                                   is-link
                                   readonly
                                   @click="field.readonly?'':showClick(field)"/>
                    </div>
                    <div v-else :key="field.id"
                         :class="(field.isDisplay!=null?field.isDisplay:field.defaultDisplay)==='none'?'divDisplayNone':'divDisplayBlack'">
                        <ZZ_FieldFileUploader v-if="field.type === 'file'"
                                              :readOnlyMark="field.readonly"
                                              :errorMessage="getErrorMessage(field.en)"
                                              :field="field"
                                              :key="field.id+'fileUploader'"
                                              :value="value[field.en]"
                                              v-on:addFiles="onAddFiles"
                                              :oldFilesObj="onGetOldFiles(field.en)"
                                              v-on:deleteFile="onDeleteFile"
                                              :label="field.zh"
                                              :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                              :placeholder="returnPlaceholder(field)"></ZZ_FieldFileUploader>
                        <ZZ_FieldFileUploader v-else-if="field.type === 'signature'"
                                              :errorMessage="getErrorMessage(field.en)"
                                              :readOnlyMark="field.readonly"
                                              :field="field"
                                              :key="field.id+'fileUploader'"
                                              :value="value[field.en]"
                                              v-on:addFiles="onAddFiles"
                                              :oldFilesObj="onGetOldFiles(field.en)"
                                              v-on:deleteFile="onDeleteFile"
                                              :label="field.zh"
                                              :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                              :placeholder="returnPlaceholder(field)"></ZZ_FieldFileUploader>
                        <ZZ_FieldDatePicker :errorMessage="getErrorMessage(field.en)"
                                            :field="field"
                                            :key="field.id+'datePicker'"
                                            :type="field.eltype"
                                            :selfeltype="field.selfeltype"
                                            v-else-if="field.type === 'date' || field.type === 'sdate' || field.type === 'edate'"
                                            :value="value[field.en]"
                                            :label="field.zh"
                                            v-on:input="onInput(field, $event)"
                                            :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                            :placeholder="returnPlaceholder(field)"></ZZ_FieldDatePicker>
                        <ZZ_FieldMultiSelector
                                v-else-if="field.type === 'more' || (field.en==='bjs'&&field.type !== 'readonly') || (field.en==='njs'&&field.type !== 'readonly')"
                                :errorMessage="getErrorMessage(field.en)"
                                :key="field.id+'multiSelector'"
                                :value="value[field.en]"
                                :label="field.zh"
                                :field="field"
                                v-on:input="onInput(field, $event)"
                                :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                :placeholder="returnPlaceholder(field)"
                                :options="field.options?field.options:(field.el?field.el.split('$'):[])"></ZZ_FieldMultiSelector>
                        <ZZ_FieldSelector
                                v-else-if="field.type === 'single'"
                                :errorMessage="getErrorMessage(field.en)"
                                :key="field.id+'selector'"
                                :value="value[field.en]"
                                :label="field.zh"
                                :field="field"
                                v-on:input="onInput(field, $event)"
                                :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                :placeholder="returnPlaceholder(field)"
                                :options="field.options?field.options:(field.el && field.el.split('$'))"></ZZ_FieldSelector>
                        <ZZ_FieldSelector v-else-if="field.type === 'init'"
                                          :errorMessage="getErrorMessage(field.en)"
                                          :key="field.id+'init'"
                                          :value="value[field.en]"
                                          :field="field"
                                          :label="field.zh"
                                          v-on:input="onInput(field, $event)"
                                          :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                          :placeholder="returnPlaceholder(field)"
                                          :options="field.options?field.options:(field.el && field.el.split('$'))"></ZZ_FieldSelector>
                        <van-cell v-else-if="field.type === 'bllwxs'"
                                  :error-message="getErrorMessage(field.en)"
                                  :key="field.id"
                                  :title="field.zh"
                                  :placeholder="returnPlaceholder(field)"
                                  :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                  :readonly="field.type==='readonly'">
                            <van-stepper :value="value[field.en]" input-width="80px" button-size="32px" min="-1000"
                                         v-on:input="onInput(field, $event)"
                                         step="0.5"
                                         :decimal-length="2"/>
                        </van-cell>
                        <van-field v-else-if="field.type === 'float'"
                                   type="number"
                                   :readonly="field.readonly"
                                   :error-message="getErrorMessage(field.en)"
                                   :key="field.id"
                                   :label="field.zh"
                                   :value="value[field.en]"
                                   :placeholder="returnPlaceholder(field)"
                                   v-on:input="onInput(field, $event)"
                                   :required="field.isRequired!==null?field.isRequired:field.defaultRequired">
                            <!--                            <van-field :value="value[field.en]"  type="number" label="数字" />-->
                        </van-field>
                        <van-field v-else-if="field.type==='readonly'"
                                   readonly
                                   :error="field.style?true:false"
                                   :error-message="getErrorMessage(field.en)"
                                   :key="field.id"
                                   :maxlength="field.maxLength ? field.maxLength : null"
                                   :type="returnType(field)"
                                   :label="field.zh"
                                   :value="value[field.en]"
                                   v-on:input="onInput(field, $event)"
                                   :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                   :placeholder="returnPlaceholder(field)"/>
                        <van-field v-else
                                   :error="field.style?true:false"
                                   :error-message="getErrorMessage(field.en)"
                                   :key="field.id"
                                   :readonly="field.readonly"
                                   :maxlength="field.maxLength ? field.maxLength : null"
                                   :type="returnType(field)"
                                   :label="field.zh"
                                   :value="value[field.en]"
                                   v-on:input="onInput(field, $event)"
                                   :required="field.isRequired!==null?field.isRequired:field.defaultRequired"
                                   :placeholder="returnPlaceholder(field)"/>
                    </div>
                </template>
            </van-cell-group>
        </template>
        <van-popup v-model="showAreaPopup" position="bottom" :style="{ height: '100%' }">
            <van-field label="省/市/区"
                       placeholder="请选择省/市/区"
                       @click="clickShowVanArea"
                       :value="setDefaultAddress"
                       :required="setAreaRequest"
                       readonly
                       is-link/>
            <van-field label="详细地址"
                       placeholder="请输入详细地址"
                       :required="setAreaRequest"
                       :value="setDefaultAddressDetail"
                       type="textarea"
                       maxlength="200"
                       rows="4"
                       autosize
                       show-word-limit
                       v-on:input="onInputAddressDetail($event)"/>
            <van-popup v-model="showDetailAreaPopup" position="bottom" :style="{ height: '50%' }"
                       :close-on-click-overlay="false">
                <van-area :area-list="areaList"
                          :columns-placeholder="['请选择', '请选择', '请选择']"
                          :value="setDefaultCode"
                          ref="myArea"
                          @change="onAreaChange"
                          @confirm="onAreaConfirm"
                          @cancel="onAreaCancel"/>
            </van-popup>
            <div class="van-submit-bar">
                <van-button class="van-submit-bar-button"
                            type="info"
                            size="large"
                            @click="handleAreaSubmit">确定
                </van-button>
                <van-button class="van-submit-bar-button vanButton"
                            type="info"
                            size="large"
                            @click="showAreaPopup = false">取消
                </van-button>
            </div>
        </van-popup>
    </section>
</template>

<script>
    import ZZ_FieldSelector from "@/zizhu/components/form/ZZ_FieldSelector";
    import ZZ_FieldMultiSelector from "@/zizhu/components/form/ZZ_FieldMultiSelector";
    import ZZ_FieldDatePicker from "@/zizhu/components/form/ZZ_FieldDatePicker";
    import ZZ_FieldFileUploader from "@/zizhu/components/form/ZZ_FieldFileUploader";
    import {onFieldCheck3, formCheck} from "@/zizhu/utils/zizhu_form";
    import {unzipStr} from "@/zizhu/utils/zizhu_pako";

    import AreaList from "@/service/area";
    import {groupArr} from '@/utils/common'

    import {queryCode as qcAPI} from "@/common/utils/common_api";
    import {post, postNoType} from "@/zizhu/utils/zizhu_http";
    import {queryCodeNoLimit as qcnlAPI} from "@/zizhu/utils/zizhu_api";
    import Pako from "pako";
    import Utils from "@/utils/common/momentWrap";

    export default {
        components: {
            ZZ_FieldSelector,
            ZZ_FieldMultiSelector,
            ZZ_FieldDatePicker,
            ZZ_FieldFileUploader
        },
        name: "ZZ_Form",
        props: {
            setLoadingTime: Number,
            setClass: String,
            fields: Array,
            value: Object,
            setvalue: Object,
            title: String,
            applyZzfz: Array,
        },
        computed: {
            setClassMark() { //是否换行
                if (this.setClass === 'info') {
                    return ''
                } else {
                    return 'vertical-apply'
                }
            },
        },
        data() {
            return {
                activeColor: 'red',
                fontSize: 30,
                changes: [],
                isChanging: false,
                requiredMap: new Map(),
                optionsMap: new Map(),
                attachMathMap: new Map(),
                errorMap: new Map(),
                formFields: [],
                fieldGroupFields: [],
                fileMap: new Map(),
                fileMapChangeTracker: 0,

                areaList: AreaList,
                showAreaPopup: false,
                setDefaultCode: null,
                setDefaultAddress: null,
                setDefaultAddressDetail: null,
                showDetailAreaPopup: false,
                setAreaRequest: false,
                carmodel: null,
                currentField: null,
                setDefaultAddressStr: null,//用于编辑地址，没有改变原本值，预存入的值

                longitude: null,
                latitude: null,
                address: null,
                canLLA: true,
            };
        },
        watch: {
            value(newVal, oldVal) {
                if (!oldVal && newVal) {
                    this.onInit();
                }
            },
            fields(newVal, oldVal) {
                if (newVal && (!oldVal || oldVal.length !== newVal.length)) {
                    this.onInit();
                }
            },
        },
        methods: {
            styleObject(val) {
                let style = {}
                if (val) style = "{" + val + "}"
                console.log(val, style)
                return style
            },
            onInputAddressDetail(value) {
                // if (!this.setDefaultAddress) {
                //     this.setDefaultAddressDetail = null;
                //     this.$toast("请选择省/市/区");
                //     return false;
                // }
                this.setDefaultAddressDetail = value;
            },
            clickShowVanArea() {
                this.showDetailAreaPopup = true;
            },
            handleAreaSubmit() {
                if (!this.setDefaultAddress) {
                    this.$toast("请选择省/市/区");
                    return false;
                }
                if (!this.setDefaultAddressDetail) {
                    this.$toast("请输入详细地址");
                    return false;
                }

                let tvalue = null;
                if (this.setDefaultAddress && this.setDefaultAddress.split(",").length >= 2) {
                    tvalue = this.setDefaultAddress + (this.setDefaultAddressDetail ? "," + this.setDefaultAddressDetail : '');
                } else {
                    tvalue = this.setDefaultAddressStr + (this.setDefaultAddressDetail ? "," + this.setDefaultAddressDetail : '');
                }
                this.onInput(this.currentField, tvalue);
                this.showAreaPopup = false;

                this.setDefaultAddress = null;
                this.setDefaultAddressDetail = null;

            },
            //value=0改变省，1改变市，2改变区
            onAreaChange(picker) {
                let val = picker.getValues();
                let areaName = "";
                if (val[0] && val[1] && val[2]) {
                    for (var i = 0; i < val.length; i++) {
                        areaName = areaName + (i == 0 ? "" : "/") + val[i].name;
                    }
                    this.carmodel = areaName;
                    this.setDefaultAddress = areaName;
                }
            },
            //确定选择城市
            onAreaConfirm(val) {
                if (val[0].name === '海外') {
                    if (val[1] && val[1].name) {
                        this.showDetailAreaPopup = false; //关闭弹框
                        this.province = val[0].name;
                        this.areaCode = val[0].code;
                        this.city = val[1].name;
                        let areaStr = val[0].name + "," + val[1].name;
                        this.carmodel = areaStr;
                        this.setDefaultAddress = areaStr;
                    } else {
                        this.$toast("请选择地区");
                    }
                } else {
                    if (val[2] && val[2].name) {
                        this.showDetailAreaPopup = false; //关闭弹框
                        this.province = val[0].name;
                        this.areaCode = val[0].code;
                        this.city = val[1].name;
                        this.county = val[2].name;
                        let areaStr = val[0].name + "," + val[1].name + "," + val[2].name;
                        this.carmodel = areaStr;
                        this.setDefaultAddress = areaStr;
                    } else {
                        this.$toast("请选择地区");
                    }
                }
            },
            //取消选中城市
            onAreaCancel() {
                this.onInput(this.currentField, '');
                this.setDefaultAddressDetail = '';
                this.setDefaultAddress = '';
                this.setDefaultAddressStr = '';
                this.showDetailAreaPopup = false;
                this.$refs.myArea.reset(); // 重置城市列表
            },
            mapToObj(strMap) {
                let obj = {};
                for (let [k, v] of strMap) {
                    obj[k] = v;
                }
                return obj;
            },
            onEmitFileStr() {
                this.$emit("receiveFileStr", this.getFileStr());
            },
            getFileStr() {
                return JSON.stringify(this.mapToObj(this.fileMap));
            },
            checkForm() {
                if (formCheck(this.formFields, this.value, this.fileMap)) {
                    this.$emit("formSubmit", this.formFields, this.getFileStr());
                }
            },
            checkFormZC() {
                this.$emit("formSubmit", this.formFields, JSON.stringify(this.mapToObj(this.fileMap)))
            },
            onDeleteFile(en, index) {
                let arr1 = this.fileMap.get(en);
                if (arr1 && arr1.length > index) {
                    arr1.splice(index, 1);
                    this.fileMap.set(en, arr1);
                    this.fileMapChangeTracker--;
                }
            },
            onAddFiles(en, arr) {
                let arr1 = this.fileMap.get(en);
                if (!arr1) {
                    arr1 = [];
                }
                for (const arrE of arr) {
                    arr1.push(arrE);
                }
                this.fileMap.set(en, arr1);
                this.fileMapChangeTracker++;
            },
            onGetOldFiles(en) {
                return {
                    tracker: this.fileMapChangeTracker,
                    files: this.fileMap.get(en)
                };
            },
            fieldCheck(field, value) {
                this.errorMap.set(
                    field.en,
                    onFieldCheck3(field, value, this.onGetOldFiles(field.en).files)
                );
            },
            getErrorMessage(key) {
                return this.errorMap.get(key);
            },
            showClick(item) {
                this.currentField = item;
                if (item.en === "jtdz" || item.en === "txdz" || item.type === 'address') {
                    let areaCodeArray = this.value[item.en] ? this.value[item.en].split(",") : [];
                    let county = null;
                    if (areaCodeArray.length > 0) {
                        if (areaCodeArray[0] === '海外') {
                            this.setDefaultAddress = areaCodeArray[0] + "," + areaCodeArray[1]
                            this.setDefaultAddressDetail = areaCodeArray[2];
                            let countyList = this.areaList["city_list"];
                            for (let i in countyList) {
                                if (areaCodeArray[1] && countyList[i] === areaCodeArray[1]) {
                                    county = i;
                                }
                            }
                            this.setDefaultAddressStr = areaCodeArray.splice(0, 2);
                        } else {
                            this.setDefaultAddress = areaCodeArray[0] + "," + areaCodeArray[1] + "," + areaCodeArray[2];
                            this.setDefaultAddressDetail = areaCodeArray[3];
                            let countyList = this.areaList["county_list"];
                            for (let i in countyList) {
                                if (areaCodeArray[2] && countyList[i] === areaCodeArray[2]) {
                                    county = i;
                                }
                            }
                            this.setDefaultAddressStr = areaCodeArray.splice(0, 3);
                        }
                    }
                    this.setDefaultCode = county;
                }
                if (item.sfbt === "是") this.setAreaRequest = true;
                this.showAreaPopup = true;
            },
            onInput(field, value) {
                let s = this.formFields.filter(item => item.type === 'sdate');
                let e = this.formFields.filter(item => item.type === 'edate');
                if (s.length > 0 && e.length > 0) {
                    let sDate = field.type === 'sdate' ? value : this.value[s[0].en] ? this.value[s[0].en] : null
                    let eDate = field.type === 'edate' ? value : this.value[e[0].en] ? this.value[e[0].en] : null
                    if (sDate && eDate) {
                        let sjc = Utils.dateSJCCurrtentDate(sDate, eDate)
                        if (!sjc) {
                            if (field.type === 'sdate') {
                                this.$toast(s[0].zh + "必须在" + e[0].zh + '之前');
                            }
                            if (field.type === 'edate') {
                                this.$toast(e[0].zh + "必须在" + s[0].zh + '之后');
                            }
                            value = null;
                        }
                        let days = Utils.dateSJDays(sDate, eDate)
                        if (days && days > 6) {
                            this.$toast(e[0].zh + "必须在" + s[0].zh + "开始7天内日期 ");
                            value = null;
                        }
                    }
                }
                const key = field.en;
                let newObj = Object.assign({}, this.value);
                newObj[key] = value;
                if (key === 'location_address') {
                    newObj['location_longitude'] = this.longitude;
                    newObj['location_latitude'] = this.latitude;
                }
                this.$emit("input", newObj);
                this.onChangePush(field, value);
                setTimeout(() => {
                    this.onStartChange();
                }, 300);
                this.fieldCheck(field, value);
            },
            returnType(field) {
                const fieldType = field.type;
                if (["clob", "string_l"].includes(fieldType)) {
                    return "textarea";
                }
                if (field.id === 'location_address') {
                    return "textarea";
                }
                if (field.en === "bjs" || field.en === "njs") {
                    return "textarea";
                }
                if (["integer", "float"].includes(fieldType)) {
                    return "number";
                }
            },
            returnPlaceholder(field) {
                if (!field.readonly) {
                    let fieldType = field.type;
                    let wxts = field.wxts;
                    if (fieldType === "readonly" || fieldType === "signature") return "";
                    if (["single", "more", "init", "date"].includes(fieldType)) return wxts ? wxts : "请选择";
                    else if (["file"].includes(fieldType)) return wxts ? wxts : "请上传";
                    else if (["float"].includes(fieldType)) return wxts ? wxts : "请输入数字(支持小数)";
                    else if (["integer"].includes(fieldType)) return wxts ? wxts : "请输入数字";
                    else return wxts ? wxts : "请输入";
                } else {
                    return null;
                }
            },
            onStartChange() {
                if (this.changes.length > 0) {
                    if (!this.isChanging) {
                        this.isChanging = true;
                        let b = true;
                        let hasChange = false;
                        const fields = this.formFields;
                        while (b) {
                            let cObj = this.changes.shift();
                            if (cObj) {
                                if (!hasChange) hasChange = true;
                                const cData = cObj.data;
                                switch (cObj.attachType) {
                                    case "change other": {
                                        let field = fields.find(item => {
                                            return item.en === cData.name;
                                        });
                                        if (field) {
                                            const en = field.en;
                                            let isRequired = null;
                                            let isDisplay = null;
                                            if (cData.value === "是") {
                                                isRequired = field.readonly ? null : true;
                                                isDisplay = 'black';
                                                this.onInput(field, null);
                                            } else if (cData.value === "否") {
                                                isRequired = false;
                                                isDisplay = field.defaultDisplay;
                                            }
                                            field.isRequired = isRequired;
                                            field.isDisplay = isDisplay;
                                            this.fieldCheck(field, this.value[en]);
                                            if (cData.hasOwnProperty("linked") && cData.linked) {
                                                const linked = JSON.parse(cData.linked);
                                                if (linked && linked.length > 0) {
                                                    let options = [];
                                                    linked.forEach(op => {
                                                        options.push(op.text);
                                                    });
                                                    field.options = options;
                                                    field.el = '';
                                                    this.onInput(field, null);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    case "math other": {
                                        setTimeout(() => {
                                            const mathObj = cData.mathObj;
                                            const mathFun = mathObj.mathFun;
                                            let num = null;
                                            const datas = mathFun.datas;
                                            for (let i = 0; i < datas.length; i++) {
                                                const datum = datas[i];
                                                const childEn = datum.fielden;
                                                let val = null;
                                                if (this.value.hasOwnProperty(childEn)) {
                                                    val = this.value[childEn];
                                                }
                                                if (!val) {
                                                    val = 0;
                                                }
                                                const v = Number(val);
                                                if (num === null) {
                                                    num = v;
                                                    continue;
                                                }
                                                switch (mathFun.type) {
                                                    case "jia":
                                                        num += v;
                                                        break;
                                                    case "jian":
                                                        num -= v;
                                                        break;
                                                    case "cheng":
                                                        num *= v;
                                                        break;
                                                    case "chu":
                                                        if (v === 0) {
                                                            num = 0;
                                                        } else {
                                                            num /= v;
                                                        }
                                                        break;
                                                }
                                            }
                                            let newObj = Object.assign({}, this.value);
                                            newObj[mathObj.result] = num;
                                            this.$emit("input", newObj);
                                        }, 500);
                                        break;
                                    }
                                }
                            }
                            if (this.changes.length === 0) {
                                this.isChanging = false;
                                b = false;
                            }
                        }
                    }
                }
            },
            onChangePush(field, value) {
                const type = field.type;
                if (field.fjtj) {
                    if (["single", "init"].includes(type)) {
                        let cParams = this.additionalConditionsParse(field.fjtj);
                        const cParam = cParams.find(item => {
                            return item.text === value;
                        });
                        if (cParam) {
                            if (cParam.data) {
                                cParam.data.forEach(item => {
                                    this.changes.push({data: item, attachType: "change other"});
                                });
                            }
                        }
                    }
                }
                if (field.mathObj) {
                    if (["integer", "float"].includes(type)) {
                        this.changes.push({
                            data: {cEn: field.en, mathObj: field.mathObj},
                            attachType: "math other"
                        });
                    }
                }
            },
            additionalConditionsParse(originStr) {
                let parsed = null;
                if (originStr.includes("*")) {
                    parsed = JSON.parse(originStr.replace(/\(\*\)/g, '"'));
                } else {
                    parsed = JSON.parse(unzipStr(originStr).replace(/\(\*\)/g, '"'));
                }
                return parsed;
            },
            queryCode(field) {
                if (field.el) {
                    let url = qcnlAPI
                    if (field.el === 'CodeBjb') url = qcAPI
                    postNoType(url, {code: field.el}).then(response => {
                        let rData = response.data;
                        if (rData) {
                            let field2 = this.formFields.find(item => {
                                return item.en === field.en;
                            });
                            for (const x of rData) {
                                field2.options.push(x.name);
                            }
                        }
                    });
                } else {
                    let json = {}, code = '';
                    let setUrl = null;
                    //籍贯，校区,类型
                    // || field.en === "xqmc"
                    if (field.en === "jg" || field.en === "xqmc" || field.en === "zxslx") {
                        setUrl = '/syt/qywx/teacherinfo/queryjg.htm';
                    } else if (field.en === "sydmc") {
                        setUrl = '/syt/mobile/code/querySyd.htm';
                    }else if (field.en === "dwmc") {
                        setUrl = "/syt/hdsb/querydwb.htm"
                    }
                        //教师类型
                        // else if (field.en === "jslx") setUrl = "/syt/qywx/teacherinfo/queryjslx.htm";
                        //期刊类别
                        // else if (field.en === "qklb") setUrl = "/syt/qywx/teacherinfo/queryqklbb.htm";
                        //所带班级-获取所有班级
                        // else if (field.en === "bjs") setUrl = "/syt/zzresult/querybjb.htm";
                        //学生培养层次
                    // else if (field.en === "pyccb" || field.en === "pyccmc") setUrl = "/syt/qywx/teacherinfo/querypyccb.htm";
                    else {
                        setUrl = qcAPI;
                    }
                    if (field.en === 'zxslx') code = "CodeZxslxb";//类型
                    if (field.en === 'xqmc') code = "CodeXqb";//校区名称
                    if (field.en === 'jslx') code = "CodeJslx";//教师类型
                    if (field.en === 'pyccb' || field.en === "pyccmc") code = "CodePyccb";//培养层次
                    // if (field.en === 'sydmc') code = "CodeSydmb";//生源地
                    if (field.en === 'qklb') code = "CodeQklbb";//期刊类别
                    if (field.en === 'mzmc') code = "CodeMzb";//民族
                    if (field.en === 'dwmc') {
                        setUrl = "/syt/hdsb/querydwb.htm"
                        // code = "CodeDwb";
                    }
                    if (field.en === 'zymc') code = "CodeZyb";
                    if (field.en === 'bjmc' || field.en === 'bjs') code = "CodeBjb";
                    if (field.en === 'njmc' || field.en === 'njs') code = "CodeNjb";
                    if (field.en === 'xl') code = "CodeXlb";
                    if (field.en === 'xzzw') code = "CodeXzzw";
                    if (field.en === 'xzlxmc') code = "CodeXzlxb";//学制
                    if (field.en === 'xslbmc') code = "CodeXslbmc";
                    if (field.en === 'zjlbmc') code = "CodeZjlbmc";
                    if (field.en === 'zyjszw') code = "CodeZyjs";
                    if (field.en === 'zzmmmc') code = "CodeZzmmb";//政治面貌
                    if (field.en === 'hjxz') code = "CodeHjxzb";//户籍性质
                    if (field.en === 'zzfdylb') code = "CodeZzfdylb";//专职辅导员类别
                    let resData = "";
                    if (setUrl) {
                        if (code) json.code = code;
                        postNoType(setUrl, json).then(response => {
                                let rData = response.data;
                                if (field.en === "jg") {
                                    resData = rData.jgList;
                                } else if (field.en === "zxslx") {
                                    resData = rData.zxslxList;
                                } else if (field.en === "sydmc") {
                                    let parse = JSON.parse(Pako.ungzip(window.atob(rData.sydStr), {to: 'string'}));
                                    resData = parse;
                                } else {
                                    resData = rData;
                                }
                                if (resData) {
                                    let field2 = this.formFields.find(item => {
                                        return item.en === field.en;
                                    });
                                    for (const x of resData) {
                                        field2.options.push(x.name);
                                    }
                                }
                            }
                        );
                    }
                }
            },
            onInit() {
                this.$store.commit("showLoading");
                this.formFields = JSON.parse(JSON.stringify(this.fields));
                if (this.formFields) {
                    let newObj = Object.assign({}, this.value);
                    let attachCMap = new Map();
                    this.formFields.forEach(field => {
                        const en = field.en;
                        // const mathObj = attachCMap.get(en);
                        // if (mathObj) field.mathObj = mathObj;
                        const dValue = field.defaultValue;
                        if (!this.value[en] && dValue) {
                            newObj[en] = dValue;
                        }
                        if (newObj[en]) {
                            this.onChangePush(field, newObj[en]);
                        }
                        if (field.type === 'init') {
                            field.options = [];
                            this.queryCode(field);
                        }
                        // 初始化计算联动
                        if (field.fjtj && ["integer", "float"].includes(field.type)) {
                            const cParam = this.additionalConditionsParse(field.fjtj);
                            let mathObj = {result: en, mathFun: cParam};
                            cParam.datas.forEach(item => {
                                const childEn = item.fielden;
                                attachCMap.set(childEn, mathObj);
                            });
                        }
                        if (("file" === field.type || "signature" === field.type) && this.value) {
                            let value = this.value[en];
                            if (value) {
                                let arr = [];
                                if (value.includes(":")) {
                                    let files = value.split("|");
                                    for (const file of files) {
                                        let fileId = file.split(":");
                                        arr.push({
                                            id: fileId[0],
                                            name: fileId[1],
                                            old: true
                                        });
                                    }
                                } else {
                                    arr.push({
                                        id: value,
                                        name: value,
                                        old: true
                                    });
                                }
                                this.fileMap.set(en, arr);
                                this.fileMapChangeTracker++;
                            }
                        }
                    });
                    this.formFields.forEach(field => {
                        const en = field.en;
                        const mathObj = attachCMap.get(en);
                        if (mathObj) {
                            field.mathObj = mathObj;
                        }
                    });
                    setTimeout(() => {
                        //根据某个字段分组 groupArr
                        let formatSttype = groupArr(this.formFields, 'fztype');
                        formatSttype.forEach(e => {
                            let arr = this.applyZzfz ? this.applyZzfz.filter(zzfz => zzfz.NAME === e.type) : [];
                            e.SORT = arr.length > 0 ? arr[0].SORT : '99999';
                        });
                        this.fieldGroupFields = formatSttype.sort(this.compare('SORT'));
                        this.$emit("input", newObj);
                        this.onStartChange();
                        this.$store.commit("hideLoading");
                        //form表单初始化完才现在操作按钮
                        this.$emit("showSubmitBtn", true);
                    }, this.setLoadingTime ? this.setLoadingTime : 1000);
                }
            },
            //sort方法根据数组中对象的某一个属性值进行排序
            compare(property) {
                return function (a, b) {
                    var value1 = a[property];
                    var value2 = b[property];
                    return value1 - value2;
                }
            },
        },
        created() {
            if (this.value) this.onInit();
        }
    };
</script>

<style scoped>
    .vertical-apply .van-cell__right-icon {
        margin-top: -90px;
        float: right;
    }

    .divDisplayBlack {
        display: block;
    }

    .divDisplayNone {
        display: none;
    }

    .van-submit-bar {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
    }

    .van-submit-bar-button {
        height: 35px;
        width: 45.5% !important;
        line-height: 35px;
        margin-left: 3% !important;
        background: #00a5ec;
        font-size: 16px;
        border-color: #00a5ec;
    }

    .vanButton {
        background: #fff;
        color: #00a5ec;
    }

    .vertical-apply >>> .van-cell {
        font-size: 15px;
    }

    .vertical-apply >>> .van-field {
        display: block;
        font-size: 15px;
    }

    .vertical-apply >>> .van-field .van-field__label {
        width: 100%;
        margin-bottom: 5px;
        color: #646566 !important;
    }

    .vertical-apply >>> .van-radio {
        margin-bottom: 5px;
    }
</style>
