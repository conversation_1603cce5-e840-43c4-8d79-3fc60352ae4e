<template>
    <div class="wrap">
        <van-popup :value="show" closeable
                   v-on:input="onChangeShow($event)"
                   position="bottom">
            <van-uploader style="margin: 8px" v-model="fileList" :accept="fileTypeTrans" :multiple="true"
                          :max-size="15000 * 1024" @oversize="onOversize" :max-count="3"/>
            <div v-if="fileType" class="text-type">文件格式限制：{{ fileType }}</div>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="info" @click="onCancel">取消</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    export default {
        name: "ZZ_FileUploader",
        props: {
            show: Boolean,
            fileType: String,
        },
        data() {
            return {
                fileList: []
            }
        },
        watch: {
            show(newVal) {
                if (newVal) {
                    this.initResults();
                }
            }
        },
        computed: {
            fileTypeTrans() {
                if (this.fileType) {
                    return "*"
                }
                return "image/*"
            },
            types() {
                if (this.fileType) {
                    return this.fileType.split(',');
                }
                return null;
            },
        },
        methods: {
            onOversize(file) {
                console.log(file);
                this.$dialog.alert({
                    message: '文件大小不能超过 15M'
                })
            },
            initResults() {
                this.fileList = [];
            },
            onConfirm() {
                if (this.fileList.length > 0) {
                    let files = [];
                    for (const f of this.fileList) {
                        if (this.types) {
                            let type = f.file.name.split('.')[1];
                            if (this.types.includes(type)) {
                                files.push(f);
                            }
                        } else {
                            files.push(f);
                        }
                    }
                    this.$emit('input', files);
                }

                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-right: 2%;
        line-height: 35px;
        font-size: 16px;
    }

    .text-type {
        margin-bottom: 4px;
        margin-left: 2px;
        font-size: 1em;
        color: darkgrey;
    }
</style>
