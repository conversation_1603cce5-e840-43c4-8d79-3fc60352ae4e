<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :value="value"
                   :required="required"
                   @click="selectorShow = true"
                   readonly
                   :type="isShowSelect?'textarea':'text'"
                   :placeholder="placeholder"
                   :is-link="isShowSelect?true:false">
            <template v-if="!isShowSelect" slot="input">
                <!--                direction="horizontal"-->
                <van-checkbox-group :value="valueShow" v-on:input="onInput"
                                    :direction="directionMark==='none'?'':'horizontal'">
                    <van-checkbox :key="option + index" v-for="(option,index) in options" :name="option" shape="square">
                        {{option}}
                    </van-checkbox>
                </van-checkbox-group>
            </template>
        </van-field>
        <ZZ_MultiSelector v-if="isShowSelect"
                          :value="valueShow"
                          v-on:input="onInput($event)"
                          :columns="options" :show.sync="selectorShow"></ZZ_MultiSelector>
    </div>
</template>

<script>
    import ZZ_MultiSelector from "@/zizhu/components/form/ZZ_MultiSelector";

    export default {
        components: {
            ZZ_MultiSelector
        },
        name: "ZZ_FieldMultiSelector",
        props: {
            field: Object,
            directionMark: String,
            placeholder: String,
            value: String,
            errorMessage: String,
            label: String,
            required: Boolean,
            options: {
                type: Array,
                default() {
                    return []
                }
            }
        },
        data() {
            return {
                selectorShow: false
            }
        },
        computed: {
            isShowSelect() {
                return this.options && this.options.length > 5;
            },
            valueShow() {
                if (this.value) {
                    return this.value.split(',');
                }
                return [];
            }
        },
        methods: {
            onInput(value) {
                this.$emit("input", value.join());
            }
        }
    }
</script>

<style>
    .van-checkbox__label {
        margin-left: 10px;
        color: #323233;
        line-height: 25px !important;
        width: 100% !important;
    }
</style>
