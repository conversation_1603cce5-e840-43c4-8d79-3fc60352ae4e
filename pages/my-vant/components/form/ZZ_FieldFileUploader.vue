<template>
    <div>
        <van-field :error="field.style?true:false"
                   :error-message="errorMessage"
                   :label="label"
                   :required="required"
                   :placeholder="placeholder?placeholder:''"
                   readonly>
            <template #button>
                <van-button v-if="!readOnlyMark && (field.type =='fileUpload' || field.type =='imageUpload')" slot="button" size="small" type="info"
                            @click="isShow = true">上传文件
                </van-button>
                <van-button v-else-if="!readOnlyMark&& field.type ==='signature'" slot="button" size="small"
                            type="info"
                            style="background: #00A5EC;border: none;"
                            @click="onClickSignature">点我签字
                </van-button>
            </template>
        </van-field>
        <div v-if="oldFiles">
            <div class="file-item" :key="fIndex" v-for="(file,fIndex) in oldFiles">
                <van-icon v-if="!readOnlyMark" name="delete" size="1.5em" @click="onDeleteFile(fIndex)"/>
                <div v-if="isImage(file)" class="file-item-other">
                    <ZZ_Image v-if="file.data" :src="file.data" width="100%"></ZZ_Image>
                    <ZZ_Image :id="file.id" width="100%"></ZZ_Image>
                </div>
                <div v-else class="file-item-other">
                    {{file.name}}
                </div>
            </div>
        </div>
        <ZZ_FileUploader v-on:input="onInput($event)" :show.sync="isShow" :file-type="field.el"></ZZ_FileUploader>
        <ZZ_FileUploader_DigitaSign v-on:input="onInput($event)" :show.sync="isShowDigitaSign" :wxts="field.wxts"
                                    :file-type="field.el"></ZZ_FileUploader_DigitaSign>
    </div>
</template>

<script>
    import ZZ_FileUploader from "@/zizhu/components/form/ZZ_FileUploader";
    import ZZ_FileUploader_DigitaSign from "@/zizhu/components/form/ZZ_FileUploaderDigitaSign";
    import ZZ_Image from "@/zizhu/components/ZZ_Image";
    import {isImage, imagesType} from "@/zizhu/utils/zizhu_file";

    export default {
        components: {
            ZZ_FileUploader, ZZ_Image, ZZ_FileUploader_DigitaSign
        },
        name: "ZZ_FieldFileUploader",
        props: {
            field: Object,
            value: String,
            wxts: String,
            errorMessage: String,
            placeholder: String,
            label: String,
            required: Boolean,
            oldFilesObj: Object,
            readOnlyMark: Boolean,
        },
        data() {
            return {
                isShow: false,
                isShowDigitaSign: false,
            }
        },
        computed: {
            oldFiles() {
                return this.oldFilesObj.files
            }
        },
        methods: {
            onClickSignature() {
                //签名只保留一张
                if (this.field.type === 'signature') {
                    this.$emit("deleteFile", this.field.en, 0);
                    this.isShowDigitaSign = true;
                }
            },
            isImage(file) {
                return isImage(file);
            },
            onDeleteFile(index) {
                this.$emit("deleteFile", this.field.en, index)
            },
            onInput(value) {
                if (value && value.length > 0) {
                    let arr = [];
                    value.forEach((x) => {
                        arr.push({
                            name: x.file.name,
                            data: x.content,
                            type: x.file.type
                        })
                    });
                    this.$emit("addFiles", this.field.en, arr);
                }
            }
        }
    }
</script>

<style scoped>
    .file-item {
        margin-top: 3px;
        position: relative;
        padding: 0 5px;
        display: inline-block;
        width: 30%;
        height: 140px;
        overflow: hidden;
    }

    .file-item-other {
        display: block;
    }

    .file-item .van-icon {
        vertical-align: sub;
        position: absolute;
        right: 5px;
        top: 0;
    }

    .van-doc-demo-block[data-v-9986b786] .file-item .van-icon-delete {
        position: absolute;
        left: 105px;
        bottom: 5px;
        top: 0;
    }

    .van-hairline--top-bottom {
        position: static;
    }
</style>
