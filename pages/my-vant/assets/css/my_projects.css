.div-root {
    /*border-top: 8px solid #f8f8f8;*/
    padding-top: 8px;
}

.head {
    background-color: #fff;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    color: #323233;
    font-size: 14px;
    line-height: 24px;
    padding: 2px 16px;
    margin-bottom: 8px;
}

.project-list-item {
    background-color: #fff;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    color: #323233;
    font-size: 14px;
    line-height: 24px;
    padding-top: 10px;
    padding-left: 10px;
    padding-right: 16px;
    margin-bottom: 8px;
}

.project-list-item-name {
    font-size: 16px;
    font-weight: bold;
}

.project-list-item-info {
    color: #7d7e80;
    font-size: 14px;
    line-height: 22px;
}

.item-bottom {
    display: flex;
    justify-content: space-between;
    color: #00A5EC;
    border-top: 1px solid #eee;
    margin-top: 5px;
}

.item-bottom-button {
    text-align: center;
    flex: 1;
    /*flex-grow: 1;*/
    /*width: 50%;*/
    line-height: 35px;
    height: 35px;
    border-left: 1px solid #eee;
}

.item-bottom .item-bottom-button:nth-of-type(1) {
    border-left: none;
}
