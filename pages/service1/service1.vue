<template>
	<!-- <block> -->

		<!-- tabbar -->
		<ui-sys >
			<ui-navbar :back="false" bg="ui-BG-Main" op isSlot :borderBottom="false">
				<form @submit.stop.prevent="formSubmit" @reset.stop.prevent="formReset" id="search-form" style="width: 100%">
					<view class="ui-search-bar  ">
						<!-- <view class="ui-search-action">
						  <ui-avatar slot="left" ui="round" src="/images/gravatar.png" />
						</view> -->
						<view class="ui-search-form round">
							<text class="ui-search-icon _icon-search" />
							<input  class="ui-search-input ui-search-text-left" value="" id="search-input" name="input"
								confirm-type="search" placeholder="搜索服务..." @confirm="formSubmit" />
						</view>
						<view class="ui-search-action">
							<button slot="right" class="ui-btn ui-BG-Main round sm" form-type="submit">搜索</button>
						</view>
					</view>
				</form>
				<!-- <view class="flex-bar p-4">
					<image src="@/static/home-icon/school-logo.png" mode="aspectFill" style="width: 54rpx;height: 54rpx;margin-right: 14rpx;" />
					<span class="font-3 color-white" style="font-weight: bold;">北京智慧大学</span>
				</view> -->
				<view class="icons-list">
					<text class="cicon-settings-o" style="font-size: 15px;"></text>
					<text style="font-size: 15px;">设置</text>
				</view>
			</ui-navbar>
			<!-- 背景渐变色 -->
			<!-- <view class="fixed-bg">
			</view> -->
			<view class="fixed-nav index-sticky ui-BG-card ui-BG">
				<view class="flex-left">
					<scroll-view scroll-x class="nav" :show-scrollbar="false">
						<view class="flex text-center">
							<!-- .slice(0, 4) -->
							<view class="cu-item flex-sub" :class="index==TabCur?'text-bold cur':''" v-for="(item,index) in tabNav" :key="index" @tap="navSelect($event, item)" :data-id="index">
								{{item.title}}
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="flex-right" @click="handleClickMenu(['fade', 'slide-top'])">
					<uni-icons type="bars" size="20"></uni-icons>
				</view>
			</view>
			<uni-transition ref="ani" custom-class="transition" :mode-class="modeClass" :styles="styles"
				:show="showMenu">
				<view class="ui-grid ui-cols-3 menuBox">
					<block v-for="(item, index) in tabNav">
						<view class="ui-item bg-shallowblue">
							<button @click="changeTabNav({navIndex: index, isDropdown: true, typeId: item.id})" size="mini" type="default" :class="item.active ? 'active' : ''"
								hover-class="is-hover">{{item.title}}
							</button>
						</view>
					</block>
				</view>
			</uni-transition>
			<block v-if="TabCur >= 0">
				<Service-With-Image  :serviceType="typeId" style="margin-top: 108rpx; margin-bottom: -16px;"></Service-With-Image>
				<block v-if="typeId =='type1'">
					<Common-Number :title="'我的数据'" :more="'更多'" :moreIcon="true" :serviceType="'service5'"></Common-Number>
					<Common-Number :title="'假期去向'" :more="'五一劳动节小长假'"  :moreIcon="true"  :serviceType="'service6'" :chartType="'column'"></Common-Number>
					<Common-Number :title="'签到任务'" :more="'更多'" :moreIcon="true" :serviceType="'service7'"></Common-Number>
					<Common-Table :title="'待办任务'" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
				</block>
				<block v-else-if="typeId =='type2'">
					<Common-Table :title="'审核数据'" :more="'更多 >'" :serviceType="'service3'"></Common-Table>
					<Common-Number :title="'评定数据'" :more="'2023~2024'" :moreIcon="true" :serviceType="'service8'"></Common-Number>
					<Common-Number :title="'公示信息'" :serviceType="'service7'" :chartType="'pie'"></Common-Number>
				</block>
				<block v-else-if="typeId =='type3'">
					<Common-Number :title="'查宿数据'" :more="'近30天'" :moreIcon="true" :serviceType="'service9'"></Common-Number>
					<Common-Number :title="'住宿数据'" :serviceType="'service10'"></Common-Number>
					<Common-Table :title="'待办任务'" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
					<Common-Number :title="'假期去向'" :more="'五一劳动节小长假'" :moreIcon="true"  :serviceType="'service8'" :chartType="'line'"></Common-Number>
				</block>
				<block v-else-if="typeId =='type4'">
					<Common-Number :title="'签到任务'" :more="'更多'" :moreIcon="true" :opts="{legend:{position: 'bottom'},extra:{ring:{ringWidth: 45,linearType:'custom',centerColor:'#FF0'}}}"  :serviceType="'service11'" :chartType="'ring'"></Common-Number>
					<Common-Table :title="'审核数据'" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
				</block>
				<block v-else-if="typeId =='type5'">
					<Common-Number :title="'假期去向'" :more="'五一劳动节小长假'" :moreIcon="true"  :serviceType="'service6'"></Common-Number>
					<Common-Number :title="'请假数据'" :serviceType="'service12'"  :chartType="'funnel'" :opts="{extra:{funnel:{type:'pyramid',labelAlign:'left'}}}"></Common-Number>
					<Common-Number :title="'签到任务'" :more="'更多 >'" :moreIcon="true"  :serviceType="'service7'"></Common-Number>
					<Common-Table :title="'待办任务'" :more="'更多 >'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
				</block>
			</block>
			<!-- 常用服务 -->
			<block v-else>
				<Common-Service  v-for="(item, index) in serviceList" :key="index.id" :title="item.title" :serviceType="item.id" :marginTop="index == 0 ? 84 : 20"></Common-Service>
				<!-- <Common-Service v-else :title="item.title" :serviceType="index"></Common-Service> -->
			</block>
			<!-- <Common-Service v-for="(item, index) in serviceList" :title="item.title" :serviceType="index"></Common-Service> -->
			<!-- 日常服务 -->
			<!-- <Common-Service title="日常服务" :serviceType="'service5'"></Common-Service> -->
			<!-- 第二课堂 -->
			<!-- <Common-Service title="第二课堂" :serviceType="'service6'"></Common-Service> -->
			<!-- 资助服务 -->
			<!-- <Common-Service title="资助服务" :serviceType="'service7'"></Common-Service> -->
			<!-- 宿管服务 -->
			<!-- <Common-Service title="宿管服务" :serviceType="'service8'"></Common-Service> -->
			<!-- 综合测评 -->
			<!-- <Common-Service title="综合测评" :serviceType="'service9'"></Common-Service> -->
			
			<!-- 用户信息 -->
			<!-- <view class="userinfo ui-BG-Main" style="background-color: red;">
			</view>

			<view class="info_box ui-BG-card ui-BG" style="background-color: red;">

				<view class="info_avatar">
					<image mode="aspectFill" src="../../static/images/gravatar.png" />
				</view>
				<view class="info_data">
					<view class="info_user">
						<view class="info_name ui-TC">{{webSiteName}} </view>
						<text class="info_lv ui-BG-Main radius">{{domain}}</text>


					</view>


				</view>
			</view> -->
			<!-- <div style="height: 1000px; background-color: #ffffff; margin-top: 160px;">
				aaaaa
			</div> -->
			<!-- 浏览记录 -->
			<!-- <view class="  ui-BG-card ui-BG">
				<ui-title title="我的记录" depth="3">
					<text slot="icon" class="ui-TC-Main cicon-slack-square" />
				</ui-title>

				<view class="ui-grid ui-cols-4  ">
					<block v-for="(item,index) in (list)" :key="item.id">



						<navigator class="ui-item " :url="item.path">
							<view><text :class="(item.icon)+' text-xxl m-2'" :style="'color:'+(item.color)"></text>
							</view>
							<text class="ui-TC text-df">{{item.name}}</text>
						</navigator>



					</block>
				</view>
			</view> -->
			<!-- 服务列表 -->
			<!-- <view class="ui-BG-card ui-BG">

				<ui-menu ui=" radius">

					<ui-menu-item arrow title="关于我们" icon="cicon-service-fill"
						@click.native="_to('/pages/about/about')">
					</ui-menu-item> -->
					<!-- #ifndef MP-BAIDU -->




					<!-- <ui-menu-item arrow title="联系方式" icon="cicon-weixin" isAction :data-value="wechat"
						@click.native="tapCopy">

						<text slot="action" class="text-sm ui-TC-3 m-1">{{wechat}}</text>
					</ui-menu-item><!-- #endif -->
					<!-- #ifndef MP-BAIDU -->
					<!-- <view style="position: relative;">
						<ui-menu-item arrow title="意见反馈" icon="cicon-creative" /> <button class="ui-btn kefubtn  "
							open-type="feedback"></button>
					</view> -->
					<!-- #endif -->

					<!-- <ui-menu-item arrow title="定制主题" icon="cicon-clothes" @click.native="_to('/pages/theme/theme')" />
				</ui-menu> -->
			<!-- </view> -->

			<!-- 版权信息 -->
			<!-- <view class="copyright"> -->
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<!-- <block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view> -->
			<HoverBall></HoverBall>
		</ui-sys>
	<!-- </block> -->
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import CommonService from "@/pages/index/components/common-service.vue";
	import CommonNumber from "@/pages/index/components/common-number.vue";
	import CommonTable from "@/pages/index/components/common-table.vue";
	import ServiceWithImage from "@/pages/index/components/service-with-image.vue";
	import config from '../../utils/config.js';
	import StatisticData from "../index/components/statisticData";
	var Api = require('../../utils/api.js');
	var util = require('../../utils/util.js');
	var Auth = require('../../utils/auth.js');
	var wxApi = require('../../utils/wxApi.js');
	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		components: {
			CommonService,
			CommonNumber,
			CommonTable,
			ServiceWithImage
		},
		data() {
			return {
				showMenu: false,
				modeClass: 'fade',
				styles: {
					justifyContent: 'center',
					alignItems: 'center',
					position: 'fixed',
					top: '170rpx',
					width: '100%',
					minHeight: '30px',
					borderRadius: '0px 0px var(--radius) var(--radius)',
					textAlign: 'center',
					backgroundColor: '#ffffff',
					boxShadow: '0 0 5px 1px rgba(0,0,0,0.2)',
					zIndex: '1'
				},
				tabNav: [
					// {name: '全部服务', active: true},
					{name: '日常事务', active: true},
					{name: '资助服务', active: false},
					{name: '宿管服务', active: false},
					{name: '辅导员工作', active: false},
					{name: '第二课堂', active: false},
					{name: '综合测评', active: false},
					{name: '团委服务', active: false},
					{name: '社区服务', active: false},
				],
				typeId: 'type1', // 选中tab的服务id
				TabCur: 0,
				scrollLeft: 0,
				serviceList: [],
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				chartsDataColumn1: {},
				// list: [{
				// 	name: "浏览",
				// 	icon: "cicon-eye",
				// 	color: "#9DCA06",
				// 	path: "/pages/readlog/readlog?key=1"
				// }, {
				// 	name: "评论",
				// 	icon: "cicon-popover",
				// 	color: "#FFB300",
				// 	path: "/pages/readlog/readlog?key=2"
				// }, {
				// 	name: "点赞",
				// 	icon: "cicon-favorite",
				// 	color: "#53bcf5",
				// 	path: "/pages/readlog/readlog?key=3"
				// }, {
				// 	name: "订阅",
				// 	color: "#F37D7D",
				// 	icon: "cicon-notice-active",
				// 	path: "/pages/readlog/readlog?key=5"
				// }],

				selected: 0,

				dialog: {
					hidden: false,
					title: "",
					content: ""
				}
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			console.log(this.wechat)
		},
		onReady: function() {
			var that = this;
			// this.getChartData();
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			// this.getChartData();
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			console.log('typeof this.getTabBar',typeof this.getTabBar)
			if (typeof this.getTabBar === 'function' && this.getTabBar()) {
				this.getTabBar().setData({
					selected: 3
				});
			}
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		created(){
			let tabNav = [];
			StatisticData.allServiceList.forEach(item => {
				if(item.title != "常用服务") {
					tabNav.push({
						title: item.title,
						id: item.id,
						active: false
					});
				}
			})
			// tabNav.unshift({title: "全部服务", id: "all", active: true });
			tabNav.map((item,index) => {
				if(index == 0 ) {
					item.active = true;
				}
			})
			this.tabNav = tabNav;
			this.serviceList = StatisticData.allServiceList.filter(item => item.title !='常用服务');
			console.log('serviceList', this.serviceList);
		},
		methods: {
			// getChartData(){
			// 	setTimeout(() => {
			// 		//因部分数据格式一样，这里不同图表引用同一数据源的话，需要深拷贝一下构造不同的对象
			// 		//开发者需要自行处理服务器返回的数据，应与标准数据格式一致，注意series的data数值应为数字格式
			// 	  this.chartsDataColumn1 = JSON.parse(JSON.stringify(demodata.Column))
			// 	}, 1000)
			// },
			handleClickMenu(type){
				this.showMenu =!this.showMenu;
				this.modeClass = type;
			},
			navSelect(e, item) {
				console.log('点击第 ', e, item)
				console.log('点击第 ', e.currentTarget.dataset.id, ' 个')
				let navIndex = e.currentTarget.dataset.id;
				// this.TabCur = navIndex;
				// this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60;
				this.changeTabNav({navIndex, typeId: item.id});
			},
			changeTabNav({navIndex, isDropdown, typeId}){
				this.TabCur = navIndex;
				this.typeId = typeId;
				this.tabNav.forEach((item, index) => {
					item.active = navIndex == index ? true : false
				});
				if(isDropdown) {
					this.handleClickMenu(['fade', 'slide-top']);	
				}
			},
			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},

			tapCopy(e) {

				uni.setClipboardData({
					data: this.wechat,

					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},



				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '',
					"dialog.content": ''
				});
			}
		}
	};
</script>
<style>
	/* 背景渐变色 */
	.fixed-bg {
		position: fixed;
		top: 0rpx;
		width: 100%;
		height: 300rpx;
		z-index: -1000;
		background: linear-gradient(to bottom, var(--ui-BG-Main) 0%, var(--ui-BG-Main) 180rpx, var(--ui-BG-2) 100%);
	}
	/* 固定的横向导航 */
	.fixed-nav {
		display: flex;
		align-items: center;
		position: fixed;
		/* top: 100rpx; */
		width: 100%;
		/* height: 120rpx; */
		z-index: 2;
		background-color: #ffffff !important;
		border-radius: 0;
		/* background: linear-gradient(to bottom, var(--ui-BG-Main), rgba(var(--ui-BG-Main), 0.5)); */
		padding: 0 20rpx;
		border-bottom: 1px solid var(--ui-TC-8);
		margin: 0;
	}
	.flex-left {
	  flex-grow: 1; /* 左侧元素占据剩余空间 */
	  overflow: hidden;
	}
	::v-deep .uni-scroll-view {
		scrollbar-width: none;
	}
	::v-deep .uni-scroll-view::-webkit-scrollbar {
	  display: none; /* Chrome, Safari 和 Opera */
	}
	.flex-right {
	  padding-left: 10rpx;
	  padding-right: 10rpx;
	  width: 60rpx; /* 右侧元素固定宽度，根据需要设置 */
	  box-shadow: 0px 0  0px rgba(0, 0, 0, 0.3);
	}
	.flex-right uni-icons {
		color: var(--ui-TC-5);
		float: right;
		margin-top: 1px;
	}
	.margin-40 {
		margin-top: 84rpx !important;
	}
	.menuList {
		/* position: fixed; */
		/* top: 85px;
		width: 100%;
		background-color: red; */
	}
	.menuList {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		height: 0px;
		background-color: #fff;
	}
	.menuBox {
		padding: 20rpx 20rpx 6rpx;
	}
	.menuBox uni-button {
		/* margin: 4px 0 0; */
		padding: 0 1.5em;
		color: #788086;
		border-radius: 15px;
		border: none;
		background-color: #fff;
		font-size: 12px;
	}
	.menuBox uni-button.active {
		background-color: rgba(245, 246, 255, 1);
		color: #1B2125;
		font-weight: bold;
	}
	.menuBox uni-button::after {
		border: none;
	}


	/* #ifndef APP-NVUE */
	.menuList ::v-deep .transition {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100px;
		height: 100px;
		border-radius: 5px;
		text-align: center;
		background-color: #4cd964;
		box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.2);
	}

	/* #endif */

	.text {
		font-size: 14px;
		color: #fff;
	}
	/* ==================
	         导航栏
	 ==================== */
	.nav {
		white-space: nowrap;
		z-index: 2001;
	}
	::-webkit-scrollbar {
		display: none;
	}
	.nav .cu-item {
		width: 25%;
		height: 76rpx;
		display: inline-block;
		line-height: 76rpx;
		/* margin: 0 10rpx; */
		padding: 0 20rpx;
		font-size: 14px;
		text-align: center;
		color: var(--ui-TC-5);
		font-family: "Inter";
		font-style: normal;
		font-weight: 500;
		/* color: black; */
	}
	.nav .cu-item.cur {
		border-bottom: 2px solid var(--ui-BG-Main);
		color: var(--ui-BG-Main);
		font-weight: 500;
	}
	.flex-sub {
	  flex: 1;
	}
	.text-bold {
		/* color: var(--ui-BG-Main) */
		font-weight: 400 !important;
	}
	/* pages/my/my.wxss */
	/* 用户信息 */

	.userinfo {
		height: 200rpx;
		position: relative;
		z-index: -1;
	}
	::v-deep .ui-fixed-box .ui-BG-Main {
		background-color: #ffffff !important;
	}
	.ui-fixed-bg.ui-BG-Main {
		background-color: red !important;
	}

	.scan-free {
		color: var(--ui-TC) !important;
	}

	.scan-free::after {
		border: solid var(--ui-TC) !important;
	}

	.kefubtn {
		position: absolute;
		top: 0;
		left: 0;
		min-height: 4em;
		width: 100%;
		opacity: 0;
	}

	/* 用户 */
	.info_box {
		margin-top: -120rpx;
	}

	.info_scan {
		float: right;
	}

	.info_right {
		float: right;
		min-width: 80rpx;
		text-align: center;
		display: block;

	}

	.info_right .exit {
		margin-left: 10rpx;

	}

	.info_avatar {
		margin-top: -80rpx;
		height: 100rpx;
		width: 100rpx;
		overflow: hidden;
		position: relative;
		border-radius: 50%;

	}

	.info_avatar image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background-color: #f7f7f7;
	}


	.info_user {
		display: block;
		margin: 24rpx 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;

	}

	.info_name {

		display: inline-block;
		height: 45rpx;
		line-height: 45rpx;
		margin-right: 20rpx;
		color: #333333;
		font-size: 18px;
	}

	.info_lv {

		margin-top: 8rpx;

		font-size: 12px;
		height: 12px;
		line-height: 12px;
		padding: 5rpx 8rpx;
		overflow: hidden;
	}

	.info_uid {
		display: block;
		height: 22px;
		line-height: 22px;
		font-size: 12px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;

		color: #999999 !important;


		font-weight: 300;
	}

	.ui-item {
		/* display: block;
		padding: 20rpx 0 20rpx;
		text-align: center; */
	}

	.ui-menu-item {

		padding: 0;
	}

	.ui-title {

		padding-left: 10rpx;
	}

	.cicon-service-fill {
		color: #F37D7D;
	}

	.cicon-weixin {
		font-size: 15px;
		color: #FFB300;
	}

	.cicon-service {
		color: #9DCA08;
	}

	.cicon-creative {
		color: #91B9EB;
	}

	.cicon-clothes {
		font-size: 13px;
		color: #FFB300;
	}
	/* 搜索 */
	.ui-search-bar {
		position: relative;
		display: flex;
		align-items: center;
		height: 68rpx;
		margin-top: 16rpx;
		margin-bottom: 16rpx;
		padding: 0 0 0 20rpx;
		border-radius: 8px;
	}
	._icon-search::before {
		color: inherit;
	}
	
	.ui-search-bar .ui-search-action+.ui-search-form {
		margin-left: 20rpx;
	}
	.color-white {
		color: #ffffff;
	}
	.ui-search-bar .ui-search-form {
		position: relative;
		flex: 1;
		display: flex;
		align-items: center;
		background-color: var(--ui-BG-1);
		color: #333;
		height: 68rpx;
		font-size: 24rpx;
	
	
	}
	
	.ui-search-icon,
	.ui-search-close {
		height: 68rpx;
		line-height: 68rpx;
	}
	
	.ui-search-icon {
		margin-left: 25rpx;
		margin-right: 20rpx;
	}
	
	.ui-search-input {
		position: relative;
		flex: 1;
		height: 68rpx;
	
	
	}
	
	.ui-search-text-left {
		text-align: left;
	}
	
	.ui-search-text-center {
		text-align: center;
	}
	
	.ui-search-text-right {
		text-align: right;
	}
	
	.input-placeholder {
		color: inherit;
		opacity: 0.5;
	}
	
	.ui-search-close {
		margin-left: 20rpx;
		margin-right: 25rpx;
	}
	
	.ui-search-bar .ui-search-form+.ui-search-action {
		margin-left: 20rpx;
	}
	.uni-input-placeholder {
		font-size: 14px;
	}
	.icons-list text {
		color: var(--ui-TC-5);
		padding-right: 12rpx;
	}
	.icons-list text:last-child {
		padding-right: 28rpx;
	}
</style>