<!-- 辅导员角色的静态页面 -->
<template>
	<!-- tabbar -->
	<ui-sys :loading="isloading">
		<!-- 背景图 -->
		<ui-navbar :back="false" bg="ui-BG-Main" op isSlot>
			<view class="flex-bar p-4">
				<image src="@/static/home-icon/school-logo.png" mode="aspectFill" style="width: 54rpx;height: 54rpx;margin-right: 14rpx;" />
				<span class="font-3 color-white" style="font-weight: bold;">北京智慧大学</span>
			</view>
			<view class="icons-list">
				<icon-me @click.native="openSelectRole" :strokeWidth="4" :fill="['#ffffff']" :size="24" />
			</view>
		</ui-navbar>
		<!-- 首次进入引导添加到“我的小程序” -->
		<view class="addMyMiniapp" v-if="isFirst" @tap.stop.prevent="shutAddMyMiniapp">
			<view>点击加入我的小程序 ↑</view>
			<text>了解"{{webSiteName}}"最新文章</text>
		</view>
		<!-- 背景渐变色 -->
		<!-- <view class="fixed-bg">
		</view> -->
		<!-- 轮播图 -->
		<!-- <Swiper></Swiper> -->
		<!-- 通知公告 -->
		<!-- <Notice :fillColor="'red'"></Notice> -->
		<!-- 主要功能 -->
		<!-- <MainService style="margin-bottom: 16px;"></MainService>
		<MainServiceNoImg style="margin-bottom: 16px;"></MainServiceNoImg> -->
		<!-- <MainServiceImgLeft ></MainServiceImgLeft> -->
		<!-- 我的数据 -->
		<Common-Number :title="'重点工作提醒'" :more="'更多'" :moreIcon="true" :serviceType="'service2'"></Common-Number>
		<Common-Number :title="'学生请销假'" :more="'更多'" :moreIcon="true" :serviceType="'service13'"></Common-Number>
		<Common-Number :title="'宿舍检查'" :more="'更多'" :moreIcon="true" :serviceType="'service14'"></Common-Number>
		<!-- 待办任务 -->
		<Common-Table :title="'请销假审批'" :more="'更多'" :moreIcon="true" :serviceType="'service3'"></Common-Table>
		<Common-Table1 :title="'奖学金审批'" :more="'更多'" :moreIcon="true" :serviceType="'service15'"></Common-Table1>
		<!-- 日常服务 -->
		<Common-Service style="margin-top: 16px;" :serviceType="'service1'" :fillColor="currentThemeColor"></Common-Service>
		<!-- 文章列表 -->
		<view class="container ">
			<!-- 新闻动态 -->
			<!-- <News :title="'新闻动态'" :more="'更多'" :moreIcon="true"></News>
			<News :title="'社区动态'" :more="'更多'" :moreIcon="true"></News> -->
			<view :style="'display:'+(isLoading?'block':'none')+';margin-top: 48rpx;'">
				<!-- parse <template is="tempLoading"/> -->
				<block name="tempLoading">
					<view class="sk-three-bounce">
						<view class="sk-child sk-bounce1 ui-BG-Main-2"></view>
						<view class="sk-child sk-bounce2 ui-BG-Main-2"></view>
						<view class="sk-child sk-bounce3 ui-BG-Main-2"></view>
					</view>
				</block>
			</view>
			<!-- 无更多文章提示 -->
			<view class="loadingmore" :style="'display:'+(floatDisplay)">
				<view class="no-more" v-if="(isLastPage)">- 无更多文章 -</view>
			</view>
			<!-- 版权信息template模板 -->
			<!-- <view class="copyright"> -->
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<!-- <block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view> -->
		</view>
		<!-- 无法访问网络 -->
		<view class="showerror" :style="'display:'+(showerror)">
			<text class="cicon-wifi-off text-xsl"></text>
			<view class="errortext">
				暂时无法访问网络，下拉重试...
			</view>
		</view>
		<HoverBall></HoverBall>
		<uni-popup ref="selectRoleDialog" type="dialog" :mask-click="true">
			<uni-popup-dialog type="info" ref="inputClose" title="请选择角色"
			@confirm="dialogConfirm" :showClose="false" confirmText="关闭">
				<scroll-view scroll-y="true" show-scrollbar="true" class="scroll-view-style">
			      <!-- 内容 -->
			      <view v-for="(item, index) in dataList" :key="index" @click="onSelectRole(item)">
			        {{ item.name }}
			      </view>
			    </scroll-view>
				<!-- <view @click="onselectRole">1. </view>
				<view @click="onselectRole">2. </view>
				<view>3. </view>
				<view>4. </view>
				<view>5. </view>
				<view>6. </view>
				<view>7. </view>
				<view>8. </view>
				<view>9. </view>
				<view>10. </view>
				<view>11. </view>
				<view>12. </view>
				<view>13. </view>
				<view>14. </view> -->
			</uni-popup-dialog>
			<!-- <scroll-view scroll-y="true" show-scrollbar="true" class="scroll-view-style">
			      <view v-for="(item, index) in dataList" :key="index">
			        {{ item }}
			      </view>
			    </scroll-view> -->
		</uni-popup>
	</ui-sys>

</template>

<script>
	import DynamicIcon from "@/components/Icon-Park.vue"
	import Swiper from "./components/swiper.vue";
	import CommonService from "./components/common-service.vue";
	import CommonNumber from "./components/common-number.vue";
	import MainService from "./components/main-service.vue";
	import MainServiceNoImg from "./components/main-service-no-img.vue";
	import MainServiceImgLeft from "./components/main-service-img-left.vue";
	import Notice from "./components/notice.vue";
	import News from "./components/news.vue";
	import CommonTable from "./components/common-table.vue";
	import CommonTable1 from "./components/common-table1.vue";
	var Api = require("@/utils/api.js");
	var util = require("@/utils/util.js");
	var wxApi = require("@/utils/wxApi.js");
	var wxRequest = require("@/utils/wxRequest.js");
	import config from "@/utils/config";
	const Adapter = require("@/utils/adapter.js");
	var pageCount = config.getPageCount;
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;

	const floatBall = uni.requireNativePlugin('Ba-FloatBall')


	export default {
		components: {
			DynamicIcon,
			Swiper,
			CommonService,
			CommonNumber,
			MainService,
			MainServiceNoImg,
			MainServiceImgLeft,
			Notice,
			News,
			CommonTable,
			CommonTable1
		},
		data() {
			return {
				isloading1: true,
				dataList: Array.from({ length: 20 }, (_, i) => `内容 ${i + 1}`), // 示例数据
				currentThemeColor: '',
				ballSize: 0.12,
				menuSize: 0.5,
				menuItemSize: 0.1,
				ballIcon: "floatball_icon",

				postsList: [],
				StickyList: [],
				isLastPage: false,
				page: 1,
				search: '',
				categories: 0,
				showerror: "none",
				showCategoryName: "",
				categoryName: "",
				showallDisplay: "block",
				displayHeader: "none",
				displaySwiper: "none",
				floatDisplay: "none",
				displayfirstSwiper: "none",
				listAdsuccess: true,
				webSiteName: webSiteName,
				domain: domain,
				isFirst: false,
				// 是否第一次打开,
				isLoading: false,
				// swipe_nav: [
				// 	require('../../static/home-icon/banner.png'),
				// 	'https://cdn.uviewui.com/uview/swiper/swiper3.png',
				// 	'https://cdn.uviewui.com/uview/swiper/swiper2.png',
				// 	'https://cdn.uviewui.com/uview/swiper/swiper1.png',
				// ],
				selected_nav: [{
					name: require('../../static/home-icon/通讯录.png'),
					title: '首页'
				}, {
					name: require('../../static/home-icon/通讯录.png'),
					title: '分类'
				}, {
					name: require('../../static/home-icon/通讯录.png'),
					title: '排行'
				}, {
					name: require('../../static/home-icon/通讯录.png'),
					title: '我的'
				}, {
					name: require('../../static/home-icon/更多.png'),
					title: '更多'
				}],
			}
		},
		props: {},
		onShareAppMessage: function() {
			return {
				title: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',
				path: 'pages/index/index',
				success: function(res) { // 转发成功
				},
				fail: function(res) { // 转发失败
				}
			};
		},
		onPullDownRefresh: function() {
			console.log("下拉加载")
			var self = this;
			self.setData({
				showerror: "none",
				showallDisplay: "block",
				displaySwiper: "none",
				floatDisplay: "none",
				isLastPage: false,
				page: 1,
				postsShowSwiperList: []
			});
			// this.getHomeconfig();
			// this.fetchPostsData(self);
		},
		onReachBottom: function() {
			var self = this;
			if (!self.isLastPage) {
				self.setData({
					page: self.page + 1
				});
				console.log('当前页' + self.page);
				this.fetchPostsData(self);
			} else {
				console.log('最后一页');
			}
		},
		onLoad: function(options) {
			console.log('onLoad', this.$store.getters.getUserInfo);
			var self = this; // 设置页面标题：文章分类

			uni.setNavigationBarTitle({
				title: webSiteName
			});

			// self.fetchPostsData(self); // 判断用户是不是第一次打开，弹出添加到我的小程序提示
			// self.fetchstickyPostsData(self);
			var isFirstStorage = uni.getStorageSync('isFirst');
			console.log('isFirstStorage', isFirstStorage)
			if (isFirstStorage) { //第一次打开，弹出选择角色，并且设置为false
				// self.setData({
				// 	isFirst: true
				// });
				uni.setStorageSync('isFirst', false); // console.log(wx.getStorageSync('isFirst'));

				setTimeout(function() {
					// self.setData({
					// 	isFirst: false
					// });
					if(uni.getStorageSync('token')) {
						self.openSelectRole();
					}
				}, 100);
			} else {
				
			}
			// this.getHomeconfig();
		},
		onShow: function(options) {
			console.log('onShow', util.getCurrentThemeColor());
			this.currentThemeColor = util.getCurrentThemeColor();
			uni.setStorageSync('openLinkCount', 0);
			var nowDate = new Date();
			nowDate = nowDate.getFullYear() + "-" + (nowDate.getMonth() + 1) + '-' + nowDate.getDate();
			nowDate = new Date(nowDate).getTime();
		},
		mounted() {
			// if(uni.getStorageSync('token')) {
			// 	this.openSelectRole();	
			// }
		},
		methods: {
			onSelectRole(item) {
				console.log('选择角色', item);
				let data = {
					roleKey: item.id,
					roleName: item.name
				}
				this.$store.commit('setUser', data);
				this.onselectRole();
			},
			openSelectRole(){
				console.log('点击了')
				this.$refs.selectRoleDialog.open();
				this.getUserRoleList();
			},
			getUserRoleList() {
				var self = this;
				// uni.showLoading({
				//   title: '查询中',
				//   mask: true
				// });
				let url = Api.getUserRoleList();
				var getUserRoleListRequest = wxRequest.postRequest(url);
				getUserRoleListRequest.then(response => {
				  console.log('getUserRoleList >>>>>>', response)
				  if (response.statusCode === 200) {
									let resData = response.data;
									if(resData.code === 0) {
										self.setData({
											dataList: resData.data
										})
										// self.$store.commit('setUser', resData.data.userInfo);
										// uni.showToast({
										// 	title: `登录成功`,
										// 	duration: 1000
										// })
										// uni.navigateTo({
										// 	url: '/pages/index/index'
										// });
										// self.setData({
										// 	codeImgData: resData.data.base64,
										// 	codeImgId: resData.data.cid
										// })
									} else {
										uni.showToast({
											title: resData.message,
											icon: 'error',
											duration: 1500
										});
									}
									
				  } else if (response.statusCode === 404) {
				    // console.log('登录失败');
				  }
				}).catch(function () {
				  uni.hideLoading();
				  self.setData({
				    showerror: "block",
				    floatDisplay: "block"
				  });
				}).finally(function () {
				  setTimeout(function () {
				    uni.hideLoading();
				  }, 1000);
				});
			},
			onselectRole() {
				this.$refs.selectRoleDialog.close();
			},
			dialogConfirm(){
				this.$refs.selectRoleDialog.close();
			},
			initFW() { //初始化
				floatBall.init({
						ballSize: this.ballSize, //悬浮球大小
						menuSize: this.menuSize, //悬浮球菜单范围大小
						menuItemSize: this.menuItemSize, //菜单项大小
						ballIcon: this.ballIcon, //悬浮球图片
						ballMenus: [{
							icon: 'ic_weibo', //菜单项图片
							tag: 'weibo' //菜单项标识
						}, {
							icon: 'ic_weixin',
							tag: 'weixin'
						}, {
							icon: 'ic_email',
							tag: 'email'
						}, {
							icon: 'ic_qq',
							tag: 'qq'
						}, {
							icon: 'ic_weixin',
							tag: 'weixin'
						}]
					},
					(res) => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 3000
						})
					});
			},
			showFW() { //展示
				floatBall.show((res) => {
					console.log(res);
					uni.showToast({
						title: res.msg,
						icon: "none",
						duration: 3000
					})
				});
			},
			hideFW() { //隐藏
				floatBall.hide(
					(res) => {
						console.log(res);
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 3000
						})
					});
			},
			formSubmit: function(e) {
				var url = '../list/list';
				var key = '';

				if (e.currentTarget.id == "search-input") {
					key = e.detail.value;
				} else {
					key = e.detail.value.input;
				}

				if (key != '') {
					url = url + '?search=' + key;
					uni.navigateTo({
						url: url
					});
				} else {
					uni.showModal({
						title: '提示',
						content: '请输入内容',
						showCancel: false
					});
				}
			},
			// 自定义分享朋友圈
			onShareTimeline: function() {
				return {
					title: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',
					path: 'pages/index/index'
				};
			},

			getHomeconfig() {
				//获取扩展设置
				var self = this;
				var getHomeconfig = wxRequest.getRequest(Api.get_homeconfig());
				getHomeconfig.then(res => {
					// console.log(res.data);
					let expand = res.data.expand;
					let swipe_nav = expand.swipe_nav;
					let selected_nav = expand.selected_nav;
					let _d = res.data.downloadfileDomain;
					let _b = res.data.businessDomain;
					let zanImageurl = res.data.zanImageurl;
					let logoImageurl = res.data.logoImageurl;
					let downloadfileDomain = _d.length ? _d.split(',') : [];
					let businessDomain = _b.length ? _b.split(',') : [];
					self.setData({
						swipe_nav: swipe_nav,
						selected_nav,
						selected_nav
					});
					uni.setStorageSync('downloadfileDomain', downloadfileDomain);
					uni.setStorageSync('businessDomain', businessDomain);
					uni.setStorageSync('zanImageurl', zanImageurl);
					uni.setStorageSync('logoImageurl', logoImageurl);
				});
			},

			//获取文章列表数据
			fetchPostsData: function(data) {
				var self = this;
				if (!data) data = {};
				if (!data.page) data.page = 1;
				if (!data.categories) data.categories = 0;
				if (!data.search) data.search = '';

				if (data.page === 1) {
					self.setData({
						postsList: []
					});
				}

				;
				self.setData({
					isLoading: true
				});
				var getCategoriesRequest = wxRequest.getRequest(Api.getCategoriesIds());
				getCategoriesRequest.then(res => {
					if (!res.data.Ids == "") {
						data.categories = res.data.Ids;
						self.setData({
							categories: res.data.Ids
						});
					}

					var getPostsRequest = wxRequest.getRequest(Api.getPosts(data));
					getPostsRequest.then(response => {
						if (response.statusCode === 200) {
							if (response.data.length) {
								if (response.data.length < pageCount) {
									self.setData({
										isLastPage: true,
										isLoading: false
									});
								}

								self.setData({
									floatDisplay: "block",
									postsList: self.postsList.concat(response
										.data.map(function(item) {
											var strdate = item.date;

											if (item.category_name !=
												null) {
												item.categoryImage =
													"/static/images/category.png";
											} else {
												item.categoryImage = "";
											}

											if (item
												.post_medium_image ==
												null || item
												.post_medium_image == ''
											) {
												item.post_medium_image =
													"/static/images/logo700.png";
											}

											item.date = util.cutstr(
												strdate, 10, 1);
											return item;
										}))
								});
							} else {
								if (response.data.code ==
									"rest_post_invalid_page_number") {
									self.setData({
										isLastPage: true,
										isLoading: false
									});
									uni.showToast({
										title: '没有更多内容',
										mask: false,
										duration: 1500
									});
								} else {
									uni.showToast({
										title: response.data.message,
										duration: 1500
									});
								}
							}
						}
					}).catch(function(response) {
						if (data.page == 1) {
							self.setData({
								showerror: "block",
								floatDisplay: "none"
							});
						} else {
							uni.showModal({
								title: '加载失败',
								content: '加载数据失败,请重试.',
								showCancel: false
							});
							self.setData({
								page: data.page - 1
							});
						}
					}).finally(function(response) {
						uni.hideLoading();
						self.setData({
							isLoading: false
						});
						uni.stopPullDownRefresh();
					});
				});
			},
			//获取置顶文章列表数据
			fetchstickyPostsData: function(data) {
				var self = this;
				if (!data) data = {};
				if (!data.categories) data.categories = 0;
				if (!data.search) data.search = '';
				self.setData({
					isLoading: true
				});
				var getCategoriesRequest = wxRequest.getRequest(Api.getCategoriesIds());
				getCategoriesRequest.then(res => {
					if (!res.data.Ids == "") {
						data.categories = res.data.Ids;
						self.setData({
							categories: res.data.Ids
						});
					}

					var getStickyPostsRequest = wxRequest.getRequest(Api.getStickyPosts(data));
					getStickyPostsRequest.then(response => {
						if (response.statusCode === 200) {
							if (response.data.length) {
								console.log('notice', response.data)
								self.setData({
									floatDisplay: "block",
									StickyList: response.data,
								});
							} else {

							}
						}
					}).catch(function(response) {

					}).finally(function(response) {
						uni.hideLoading();
						self.setData({
							isLoading: false
						});
						uni.stopPullDownRefresh();
					});
				});
			},
			//加载分页
			loadMore: function(e) {
				console.log('loadMore');
				var self = this;

				if (!self.isLastPage) {
					self.setData({
						page: self.page + 1
					}); //console.log('当前页' + self.data.page);

					this.fetchPostsData(self);
				} else {
					uni.showToast({
						title: '没有更多内容',
						mask: false,
						duration: 1000
					});
				}
			},
			// 跳转至查看文章详情
			redictDetail: function(e) {
				// console.log('查看文章');
				var id = e.currentTarget.id,
					url = '../detail/detail?id=' + id;
				uni.navigateTo({
					url: url
				});
			},
			//首页图标跳转
			onNavRedirect: function(e) {
				var redicttype = e.currentTarget.dataset.redicttype;
				var url = e.currentTarget.dataset.url == null ? '' : e.currentTarget.dataset.url;
				var appid = e.currentTarget.dataset.appid == null ? '' : e.currentTarget.dataset.appid;
				var extraData = e.currentTarget.dataset.extraData == null ? '' : e.currentTarget.dataset
					.extraData;

				if (redicttype == 'apppage') {
					//跳转到小程序内部页面         
					uni.navigateTo({
						url: url
					});
				} else if (redicttype == 'webpage') //跳转到web-view内嵌的页面
				{
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				} else if (redicttype == 'miniapp') //跳转到其他app
				{
					var n = this;
					//#ifdef APP-PLUS  
					console.log(n.sweixin);
					n.sweixin ? n.sweixin.launchMiniProgram({
						id: 'gh_17dae6107b00'
					}) : plus.nativeUI.alert('当前环境不支持微信操作!');

					//#endif  

				}
			},
			// // 跳转至查看小程序列表页面或文章详情页
			// redictAppDetail: function(e) {
			// 	let {
			// 		index,
			// 		type,
			// 		appid,
			// 		url,
			// 		path
			// 	} = e.currentTarget.dataset;

			// 	if (index === 0) {
			// 		console.log(index)
			// 		uni.navigateTo({
			// 			url: '/pages/index/index'
			// 		});
			// 	} else if (index === 1) {
			// 		uni.navigateTo({
			// 			url: '/pages/topic/topic'
			// 		});
			// 	} else if (index === 2) {
			// 		uni.navigateTo({
			// 			url: '/pages/hot/hot'
			// 		});
			// 	} else if (index === 3) {
			// 		uni.navigateTo({
			// 			url: '/pages/my/my'
			// 		});
			// 	}

			// 	if (type === 'apppage') {
			// 		// 小程序页面         
			// 		uni.navigateTo({
			// 			url: path
			// 		});
			// 	}

			// 	if (type === 'webpage') {
			// 		// web-view页面
			// 		url = '../webpage/webpage?url=' + url;
			// 		uni.navigateTo({
			// 			url: url
			// 		});
			// 	}

			// 	if (type === 'miniapp') {
			// 		// 其他小程序
			// 		url = '../webpage/webpage?url=' + url;
			// 		uni.navigateTo({
			// 			url: url
			// 		});
			// 	}
			// },
			//返回首页
			redictHome: function(e) {
				//console.log('查看某类别下的文章');  
				var id = e.currentTarget.dataset.id,
					url = '/pages/index/index';
				uni.switchTab({
					url: url
				});
			},

		}
	};
</script>
<style lang="scss">
	.color-white {
		color: #ffffff;
	}
	.index-top {
		position: relative;
	}

	.indexbg {
		position: absolute;
		top: -30rpx;
		width: 750rpx;
		height: 500rpx;
		z-index: -1;

	}
	.fixed-bg {
		position: fixed;
		top: 0;
		width: 100%;
		height: 540rpx;
		z-index: -1000;
		background: linear-gradient(to bottom, var(--ui-BG-Main) 65%, var(--ui-BG-2) 100%);
	}

	/* 轮播图 */



	/* 图标导航 */


	// .ui-item {
	// 	display: block;
	// 	/* padding: 24rpx 0 20rpx; */
	// 	text-align: center;
	// }

	// .ui-item image {

	// 	width: 80rpx;
	// 	height: 80rpx;
	// 	margin: auto;
	// 	margin-bottom: 20rpx;
	// 	display: block;
	// }

	// .ui-item text {

	// 	color: #333;
	// 	font-weight: 300;
	// }

	/* 搜索 */
	.ui-search-bar {
		position: relative;
		display: flex;
		align-items: center;
		height: 100rpx;
		margin-bottom: 15rpx;
	}

	.ui-search-bar .ui-search-action+.ui-search-form {
		margin-left: 20rpx;
	}

	.ui-search-bar .ui-search-form {
		position: relative;
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		color: #333;
		height: 68rpx;
		font-size: 24rpx;


	}

	.ui-search-icon,
	.ui-search-close {
		height: 68rpx;
		line-height: 68rpx;
	}

	.ui-search-icon {
		margin-left: 25rpx;
		margin-right: 20rpx;
	}

	.ui-search-input {
		position: relative;
		flex: 1;
		height: 68rpx;


	}

	.ui-search-text-left {
		text-align: left;
	}

	.ui-search-text-center {
		text-align: center;
	}

	.ui-search-text-right {
		text-align: right;
	}

	.input-placeholder {
		color: inherit;
		opacity: 0.5;
	}

	.ui-search-close {
		margin-left: 20rpx;
		margin-right: 25rpx;
	}

	.ui-search-bar .ui-search-form+.ui-search-action {
		margin-left: 20rpx;
	}




	/*模块标题样式 */
	.common-subtitle {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 130rpx;
		padding: 0 24rpx;
		background: #fff;
	}

	.common-subtitle-left {
		font-size: 34rpx;
		color: #333;
		font-weight: 500;
	}

	.common-subtitle-right {
		font-size: 26rpx;
		font-weight: 400;
		color: #959595;
		margin-right: 6rpx;
	}

	/* 精选栏目菜单导航 */


	.selected-nav-list {
		display: flex;
		padding: 0 24rpx 40rpx;

		justify-content: space-between;
	}

	.selected-nav-item {
		margin-right: 16rpx;
		text-align: center;
	}

	.selected-nav-item image {
		width: 156rpx;
		height: 156rpx;
		background: #f5f7f7;
	}

	.selected-nav-item text {
		line-height: 26rpx;
		font-size: 26rpx;
		color: #333;
	}

	.list-item-ad {
		margin: 24rpx;

		overflow: hidden;
		border-radius: var(--radius);
	}

	/* 引导添加到我的小程序 */
	.addMyMiniapp {
		color: #333;
		line-height: 1.2;
		background: #fff;
		padding: 24rpx 24rpx;
		position: fixed;
		top: 20rpx;
		right: 24rpx;
		z-index: 999;
		box-shadow: 0 16rpx 30rpx -12rpx rgba(88, 88, 88, 0.2);
	}

	.addMyMiniapp>view {
		font-size: 24rpx;
		font-weight: 500;
	}

	.addMyMiniapp>text {
		font-size: 20rpx;
		font-weight: 200;
	}

	.addMyMiniapp::before {
		content: '';
		width: 20rpx;
		height: 20rpx;
		background: #fff;
		transform: rotate(45deg);
		position: absolute;
		right: 118rpx;
		top: -10rpx;
	}
</style>


<style lang="scss">
	/* .ui-grid {
		margin: 12rpx;
	} */
	.ui-item .ui-BG-card {
		margin: 12rpx;
		padding: 0;
		overflow: hidden;
	}
	.icons-list {
		display: flex;
		align-items: center;
	}
	.icons-list .uni-icons,.icons-list text, .icons-list .i-icon {
		padding-right: 40rpx;
	}
	.icons-list text:last-child {
		padding-right: 28rpx;
	}

	/* 
	.ui-item {

		text-align: center;
		position: relative;
	}


	.ui-card {
		margin: 12rpx !important;
	} */

	.ui-card-bg {
		/* height: 160rpx; */
		position: relative;
		background-size: cover;
		background-position: center;
		overflow: hidden;
		filter: saturate(160%) blur(22px);

	}

	/* .ui-card-image {
		
		position: absolute;
		width: 120rpx;
		height: 120rpx;
		display: inline-block;
		top: 100rpx;
		left: 121.5rpx;
		background-color: #fff;
		border-radius: 100%;
		border: 1px solid #fff;
	} */

	.ui-card-sub {
		position: absolute;
		right: 10px;
		top: 10px;
		border: solid 1px rgba(255, 255, 255, .5);
		z-index: 1;
		color: #fff;
		padding: 2px 6px;
		background-color: rgba(0, 0, 0, 0.1);

	}

	.ui-card-sub:hover {
		background-color: rgba(0, 0, 0, 0.4);
	}

	.ui-card-content {
		padding: 24rpx 12rpx 24rpx;
		overflow: hidden;
		background-color: #fff;
		z-index: 9;
		height: 236rpx;
	}

	.ui-card-content .dec {
		margin-top: 10rpx;

	}
	::v-deep .uni-dialog-content {
		flex-direction: column;
		// max-height: 200px;
		overflow: hidden;
		overflow-y: scroll;
		padding: 0 4px;
	}
	.uni-dialog-content uni-view {
		line-height: 32px;
		color: var(--ui-TC-6);
	}
	.uni-popup {
		z-index: 3000;
	}
	::v-deep .uni-dialog-title {
		padding-top: 16px;
		padding-bottom: 10px;
	}
	.scroll-view-style {
	  max-height: calc(100vh - 123px); /* 设置滚动区域的高度 */
	  background-color: #fff;
	  padding: 0;
	  border-radius: 12px;
	  text-align: center;
	  margin-bottom: 10px;
	}
	.uni-popup-dialog {
		width: 54vw;
		border-radius: 8px;
	}
	
</style>