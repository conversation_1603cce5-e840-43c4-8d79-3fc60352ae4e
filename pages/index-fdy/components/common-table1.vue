<!-- 待办事务 -->
<template>
	<view class="index-sticky ui-BG-card ui-BG">
		<!-- v-if="listServices.length > 0" -->
		<ui-title v-if="title" :title="title" :desc="desc" :moreIcon="moreIcon" depth="3" style="padding-bottom: 12px !important;padding-top: 0 !important;">
			<!-- :class="titleIcon" -->
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<!-- <scroll-view scroll-x class="bg-white nav">
			<view class="flex text-center">
				<view class="cu-item flex-sub" :class="index==TabCur?'text-bold cur':''" v-for="(item,index) in tabNav" :key="index" @tap="tabSelect" :data-id="index">
					{{tabNav[index]}}
				</view>
			</view>
		</scroll-view> -->
		<view v-if="TabCur==0" :class="'ui-grid '+ 'ui-cols-3'">
			<block>
				<view class="ui-item">
					<text class="color-black" style="margin-left: 6px;"> 姓名</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-center">
					<text class="color-shallowgray">学号</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right">
					<text class="color-shallowgray" style="margin-right: 6px;">申请时间 </text>
				</view>
			</block>
			<block v-for="(item,index) in (listServices)" :key="index">
				<block>
					 <!-- :class="index%2==0?'':'bg-shallowgray'" -->
					<view class="ui-item">
						<text class="ui-TC-Main color-black" style="margin-left: 6px;">{{item.name}}</text>
					</view>
				</block>
				<block>
					 <!-- :class="index%2==0?'':'bg-shallowgray'" -->
					<view class="ui-item text-center">
						<text class="color-shallowgray">{{item.type}}</text>
					</view>
				</block>
				<block>
					<!-- :class="index%2==0?'':'bg-shallowgray'" -->
					<view class="ui-item text-right" >
						<text class="color-shallowgray" style="margin-right: 6px;">{{item.hours}}</text>
					</view>
				</block>
			</block>
		</view>
		<!-- <view v-if="TabCur==1" :class="'ui-grid '+ 'ui-cols-3'">
			<block>
				<view class="ui-item bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-left: 6px;">请假人</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm">请假类型</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-right: 6px;">请假时长</text>
				</view>
			</block>
			<block v-for="(item,index) in (listServices)" :key="index">
				<block>
					<view class="ui-item">
						<text class="ui-TC text-sm ui-TC-Main color-black" style="margin-left: 6px;">{{item.name}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC">{{item.type}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC" style="margin-right: 6px;">{{item.hours}}</text>
					</view>
				</block>
			</block>
		</view>
		<view v-if="TabCur==2" :class="'ui-grid '+ 'ui-cols-3'">
			<block>
				<view class="ui-item bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-left: 6px;">请假人</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm">请假类型</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-right: 6px;">请假时长</text>
				</view>
			</block>
			<block v-for="(item,index) in (listServices)" :key="index">
				<block>
					<view class="ui-item">
						<text class="ui-TC text-sm ui-TC-Main color-black" style="margin-left: 6px;">{{item.name}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC">{{item.type}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC" style="margin-right: 6px;">{{item.hours}}</text>
					</view>
				</block>
			</block>
		</view>
		<view v-if="TabCur==3" :class="'ui-grid '+ 'ui-cols-3'">
			<block>
				<view class="ui-item bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-left: 6px;">请假人</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm">请假类型</text>
				</view>
			</block>
			<block>
				<view class="ui-item text-right bg-shallowblue">
					<text class="ui-TC text-sm" style="margin-right: 6px;">请假时长</text>
				</view>
			</block>
			<block v-for="(item,index) in (listServices)" :key="index">
				<block>
					<view class="ui-item">
						<text class="ui-TC text-sm ui-TC-Main color-black" style="margin-left: 6px;">{{item.name}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC">{{item.type}}</text>
					</view>
				</block>
				<block>
					<view class="ui-item text-right">
						<text class="ui-TC text-sm ui-TC" style="margin-right: 6px;">{{item.hours}}</text>
					</view>
				</block>
			</block>
		</view> -->
	</view>
</template>

<script>
	import StatisticData from "./statisticData";
	export default {
		props: {
			serviceType:  {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: ''
			},
			// 标题前的图标
			titleIcon: {
				type: String,
				default: 'cicon-titles'
			},
			desc: {
				type: String,
				default: ''
			},
			more: {
				type: String,
				default: ''
			},
			moreIcon: {
				type: Boolean,
				default: false
			}
		},
		data(){
			return {
				listServices: [],
				tabNav: ['请假审批', '签到任务', '假期审批','活动审批'],
				TabCur: 0,
				scrollLeft: 0
			}
		},
		created(){
			console.log('this.serviceType', this.serviceType);
			this.getData();
		},
		methods: {
			tabSelect(e) {
				this.TabCur = e.currentTarget.dataset.id;
				this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60
			},
			getData() {
				let serviceType = this.serviceType;
				if(serviceType) {
					this.listServices = StatisticData[serviceType];
				}
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
			
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	/* 图标导航 */
	.ui-BG-card {
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
		padding: 16px;
		margin: 16px;
		border-radius: 12px;
	}
	
	.ui-item {
		display: block;
		/* padding: 24rpx 0 20rpx; */
		height: 36px;
		line-height: 36px;
		font-size: 14px;
		font-family: "Inter";
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid var(--ui-TC-8); 
	}
	.ui-item:nth-last-child(1), .ui-item:nth-last-child(2), .ui-item:nth-last-child(3) {
		border-bottom: none;
	}
	.text-right {
		text-align: right;
	}
	.ui-item image {
		width: 80rpx;
		height: 80rpx;
		margin: auto;
		margin-bottom: 0rpx;
		display: block;
	}
	
	.ui-item text {
		color: #333;
		font-weight: 300;
	}
	/* ==================
	         导航栏
	 ==================== */
	.nav {
		white-space: nowrap;
	}
	::-webkit-scrollbar {
		display: none;
	}
	.nav .cu-item {
		width: 25%;
		height: 56rpx;
		display: inline-block;
		line-height: 56rpx;
		margin: 0 10rpx;
		padding: 0 20rpx;
		font-size: 12px;
		text-align: center;
	}
	.nav .cu-item.cur {
		border-bottom: 3px solid var(--ui-BG-Main);
	}
	.flex-sub {
	  flex: 1;
	}
	.text-bold {
		// color: var(--ui-BG-Main)
		font-weight: bold;
	}
	.bg-shallowblue{
		background-color: var(--ui-BG-Main-5);
	}
	.color-black {
		color: var(--ui-TC-7) !important;
	}
	.color-shallowgray {
		color: var(--ui-TC-6) !important;
	}
	.bg-shallowgray {
		background-color: rgb(250,250,250);
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
</style>