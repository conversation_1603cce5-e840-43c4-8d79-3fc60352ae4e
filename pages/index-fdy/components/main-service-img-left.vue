<!-- 主要功能 -->
<template>
	<view class="ui-grid ui-cols-2"  style="padding: 0 8px; margin-top: 0;">
		<!-- <view class="ui-item " v-for="(item,index) in (categoriesList)" :key="item.id">
			<view class="ui-BG ui-BG-card"> -->
				<!-- <view class="ui-card-bg " :data-item="item.name" :data-id="item.id" @tap="redictIndex"
					:style="'background-image:url('+(item.category_thumbnail_image)+') ;'">
				</view> -->
				<!-- <image mode="aspectFill" class="ui-card-image" :src="item.category_thumbnail_image"
					:data-item="item.name" :data-id="item.id" @tap="redictIndex"></image> -->
				<!-- <view class="ui-card-content  radius" :data-item="item.name" :data-id="item.id" @tap="redictIndex"
					:style="'background-image:url('+(item.category_thumbnail_image)+') ;'">
					<view> <text class=" ">{{item.name}} </text>
					</view>
					<text class="ui-TC-3 text-xs ">{{item.count}}篇文章 </text>
					<text class="ui-TC-3 text-df text-linecut-2 dec">{{item.description}} </text>
				</view>
			</view>
		</view> -->
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-blue" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-calendar theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">日常事务</text>
							<text class="post-data">学生日常事务性工作</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
					<!-- <image src="/static/images/logo.png" mode="aspectFill" class="post-img"
					></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-pink" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-degree-hat theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<!-- <icon-view-list theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" /> -->
						<view>
							<text class="post-title ">资助服务</text>
							<text class="post-data">奖/助/贷/勤/补/困</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
					<!-- <image src="https://www.watch-life.net/images/2024/11/1-300x240.jpg" mode="aspectFill" class="post-img"></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-purple" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-city theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">宿管服务</text>
							<text class="post-data">查宿/住宿/调退宿</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-green" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-outdoor theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">第二课堂</text>
							<text class="post-data">活动管理/报名/成绩</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	var util = require("@/utils/util.js");
	export default {
		// props: {
		// 	// 当前选中的主题对应的系统设置的16进制颜色代码
		// 	fillColor: {
		// 		type: String,
		// 		default: ''
		// 	}
		// },
		data(){
			return {
				fillColor: util.getCurrentThemeColor(), // 当前选中的主题对应的系统设置的16进制颜色代码
				categoriesList: [{
					"id": 1,
					"count": 184,
					"description": "记录观察生活、思考人生的思想火花。",
					"link": "https://www.watch-life.net/category/life-thinking",
					"name": "随思偶得",
					"slug": "life-thinking",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/life-thinking.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/1",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=1"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 255,
					"count": 94,
					"description": "记录我的可爱小天使和她老爸的故事",
					"link": "https://www.watch-life.net/category/lovely-daughter",
					"name": "家有女儿",
					"slug": "lovely-daughter",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/happy-fathers-day-wishes.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/255",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=255"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 1059,
					"count": 57,
					"description": "微信的相关文章。",
					"link": "https://www.watch-life.net/category/wechat",
					"name": "微信",
					"slug": "wechat",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/weixin.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/1059",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=1059"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 374,
					"count": 9,
					"description": "有关网站应用的文章",
					"link": "https://www.watch-life.net/category/website-app",
					"name": "网站应用",
					"slug": "website-app",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/website.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/374",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=374"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}],
			}
		}
	}
</script>

<style>
	.ui-BG-card {
		margin: 0 8px;
	}
	.ui-item.post-list:nth-child(1),.ui-item.post-list:nth-child(2) {
		margin-bottom: 16px;
	}
	/* .post-item.ui-BG-card:nth-child(3),.post-item.ui-BG-card:nth-child(4) {
		margin-top: 20rpx;
	} */
	.ui-item:nth-child(1) .ui-BG-card {
		/* background-image: url("@/static/home-icon/ywbj1.png"); */
		background-size: cover;
		background-position: center;
	}
	.ui-item:nth-child(2) .ui-BG-card {
		/* background: linear-gradient(to right, rgba(251,230,219, 0.6),rgb(251,230,219), rgba(251,230,219, 0.6),rgb(251,230,219)); */
		/* background-image: url("@/static/home-icon/ywbj2.png"); */
		background-size: cover;
		background-position: center;
	}
	.ui-item:nth-child(3) .ui-BG-card {
		/* background: linear-gradient(to right, rgba(223,234,255, 0.6),rgb(223,234,255), rgba(223,234,255, 0.6),rgb(223,234,255)); */
		/* background-image: url("@/static/home-icon/ywbj3.png"); */
		background-size: cover;
		background-position: center;
	}
	.ui-item:nth-child(4) .ui-BG-card{
		/* background: linear-gradient(to right, rgba(216,245,238, 0.6),rgb(216,245,238), rgba(216,245,238, 0.6),rgb(216,245,238)); */
		/* background-image: url("@/static/home-icon/ywbj4.png"); */
		background-size: cover;
		background-position: center;
	}
	.post-item {
		align-items: center;
	}
	/* .post-img{
		width: 36px;
		height: 36px;
	} */
	.post-title {
		height: auto;
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px;
	}
	.font-bold {
		font-weight: bold;
	}
	.post-desc {
		margin-right: 0;
		display: flex;
		width: 100%;
		align-items: center;
	}
	.post-left {
		width: 40px;
	}
	.post-left uni-image{
		width: 36px;
		margin-top: 6px;
	}
	.post-middle {
		flex: 1;
	}
	.post-right {
		width: 16px;
	}
	.post-desc uni-image {
		padding-bottom: 4px;
	}
	.post-data {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
	/* .post-data > uni-text {
		margin-right: 0;
	} */
</style>