<!-- 日常服务 -->
<template>
	<block>
	<view v-if="marginTop != 20" class="index-sticky ui-BG-card" style="margin-top: 84rpx; padding: 12px 0 0;">
		<ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding: 0 12px 8px !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in listServices" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" :data-type="item.title"
					:data-url="item.name" :data-path="item.name">
					<view v-if="item.type == 'image'" class="image-box">
						<!-- <image mode="aspectFill" :src="item.name"></image>		 -->
						<Icon-Park :theme="item.filled" :iconName="item.iconName" :fill="item.iconColor" />
						<!-- <icon-chart-histogram-two v-else-if="item.iconName == 'chart-histogram-two'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-more v-else-if="item.iconName == 'more'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="4" />
						<icon-remind v-else-if="item.iconName == 'remind'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-peoples-two v-else-if="item.iconName == 'peoples-two'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-id-card v-else-if="item.iconName == 'id-card'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-calendar v-else-if="item.iconName == 'calendar'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-all-application v-else-if="item.iconName == 'all-application'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-activity-source v-else-if="item.iconName == 'activity-source'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-more-app v-else-if="item.iconName == 'more-app'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-layers v-else-if="item.iconName == 'layers'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" /> -->
						<!-- <icon-remind theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" /> -->
						<!-- <component :is="`icon-${item.iconName}`" :size="24" :fill="[item.iconColor]" theme="filled" :strokeWidth="3" /> -->
					</view>
					<view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view>
					<text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}</text>
				</view>
			</block>
		</view>
		<view class="charts-box" v-if="chartType">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view>
	</view>
	<view v-else class="index-sticky ui-BG-card" style="padding: 0px 0  0;">
		<ui-title v-if="title" :title="title" :desc="desc" depth="3" style="padding: 0 12px 8px !important;">
			<text v-if="titleIcon"  slot="icon" class="ui-TC-Main" :class="titleIcon" />
			<text slot="more">{{more}}</text>
		</ui-title>
		<view :class="'ui-grid '+ 'ui-cols-4'">
			<block v-for="(item,index) in listServices" :key="index">
				<view class="ui-item  ui-padding-12" @tap.stop.prevent="redictAppDetail" :data-type="item.title"
					:data-url="item.name" :data-path="item.name">
					<view v-if="item.type == 'image'" class="image-box">
						<!-- <image mode="aspectFill" :src="item.name"></image>		 -->
						<Icon-Park :iconName="item.iconName" :fill="item.iconColor" :theme="item.theme" :size="item.size" />
						<!-- <icon-address-book v-if="item.iconName == 'address-book'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-chart-histogram-two v-else-if="item.iconName == 'chart-histogram-two'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-more v-else-if="item.iconName == 'more'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="4" />
						<icon-remind v-else-if="item.iconName == 'remind'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-peoples-two v-else-if="item.iconName == 'peoples-two'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-id-card v-else-if="item.iconName == 'id-card'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-calendar v-else-if="item.iconName == 'calendar'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-all-application v-else-if="item.iconName == 'all-application'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-activity-source v-else-if="item.iconName == 'activity-source'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-more-app v-else-if="item.iconName == 'more-app'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" />
						<icon-layers v-else-if="item.iconName == 'layers'" theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" /> -->
						<!-- <icon-remind theme="filled" :size="24" :fill="[item.iconColor]" :strokeWidth="3" /> -->
						<!-- <component :is="`icon-${item.iconName}`" :size="24" :fill="[item.iconColor]" theme="filled" :strokeWidth="3" /> -->
					</view>
					<view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view>
					<text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.title}}</text>
				</view>
			</block>
		</view>
		<view class="charts-box" v-if="chartType">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view>
	</view>
	</block>
</template>

<script>
	import StatisticData from "./statisticData";
	//下面是演示数据，您的项目不需要引用，数据需要您从服务器自行获取
	import demodata from '@/mockdata/demodata.json'
	export default {
		props: {
			// 当前选中的主题对应的系统设置的16进制颜色代码
			fillColor: {
				type: String,
				default: ''
			},
			serviceType:  {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: ''
			},
			titleIcon: {
				type: String,
				default: '',
				// cicon-titles
			},
			desc: {
				type: String,
				default: ''
			},
			more: {
				type: String,
				default: ''
			},
			marginTop: {
				type: Number,
				default: 20
			},
			chartType: {
				type: String,
				default: null
			},
			opts: {
				type: Object,
				default() {
					return {}
				}
			}
		},
		data(){
			return {
				listServices: [],
				chartsDataColumn: {},
			}
		},
		created(){
			console.log('this.serviceType', this.serviceType, this.marginTop, this.chartType);
			this.getData();
		},
		watch: {
			serviceType(newVal){
				if(newVal) {
					this.getData();
				}
			},
		},
		methods: {
			getData() {
				let serviceType = this.serviceType;
				if(serviceType) {
					if(serviceType.indexOf('service') > -1) {
						this.listServices = StatisticData[serviceType];
					} else {
						let listServices = StatisticData.allServiceList.find(item => item.id == serviceType);
						if(listServices) {
							this.listServices = listServices.dataList;
						}
					}
				}
				if(this.chartType) {
					setTimeout(() => {
						//因部分数据格式一样，这里不同图表引用同一数据源的话，需要深拷贝一下构造不同的对象
						//开发者需要自行处理服务器返回的数据，应与标准数据格式一致，注意series的data数值应为数字格式
					 if(serviceType == 'service6') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.Column))
					 } else if(serviceType == 'service7') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 } else if(serviceType == 'service8') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.Line))
					 } else if(serviceType == 'service11') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 } else if(serviceType == 'service12') {
						this.chartsDataColumn = JSON.parse(JSON.stringify(demodata.PieA))
					 }
					  console.log('this.chartsDataColumn', this.chartsDataColumn);
					}, 1000)
				}
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	/* 图标导航 */
	.ui-BG-card {
		// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
		// box-shadow: none;
		// margin: 0;
		padding: 16px;
		box-shadow: none;
		margin: 0;
		border-radius: 0;
	}
	
	.ui-item {
		display: block;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
	}
	.ui-padding-12 {
		padding-bottom: 12px;
	}
	.ui-item .i-icon {
		width: 24px;
		height: 24px;
		margin: auto;
		margin-bottom: 2px;
		display: block;
		padding: 12px;
		background-color: #ffffff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
		border-radius: 12px;
	}
	.ui-item image, .ui-item svg path {
	  // fill: red; /* 改变SVG路径的颜色 */
	}
	
	.ui-item text {
		// color: #333;
		// font-weight: 300;
		color: var(--ui-TC-5) !important;
		font-family: "Inter";
		font-size: 14px !important;
		font-style: normal;
		font-weight: 400;
		line-height: 30px; /* 1.429 */
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
	// .ui-item .i-icon {
	//   width: 24px;
	//   height: 24px;
	//   margin: auto;
	//     margin-bottom: auto;
	//   margin-bottom: 2px;
	//   display: block;
	//   padding: 12px;
	//   background-color: #ffffff;
	//   box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0), 0px 0px 0px 0px rgba(0, 0, 0, 0);
	//   border-radius: 12px;
	// }
</style>