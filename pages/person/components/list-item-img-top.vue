<!-- 主要功能 -->
<template>
	<view class="ui-grid ui-cols-2"  style="padding: 0 8px; margin-top: 0;">
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-blue" @tap="redictDetail" style="padding: 16px 8px">
					<view class="post-desc">
						<image v-if="true" mode="aspectFill" src="@/static/images/icon-female.svg" />
						<image v-if="true" mode="aspectFill" src="@/static/images/icon-female.svg" />
						<view class="post-title  ">
							<text>日常事务</text>
						</view>
						<view class="post-data">
							<text>学生日常事务性工作</text>
						</view>
						<view class="post-data">
							<text>学生日常事务性工作</text>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 当前选中的主题对应的系统设置的16进制颜色代码
			fillColor: {
				type: String,
				default: ''
			}
		},
		data(){
			return {
				categoriesList: [{
					"id": 1,
					"count": 184,
					"description": "记录观察生活、思考人生的思想火花。",
					"link": "https://www.watch-life.net/category/life-thinking",
					"name": "随思偶得",
					"slug": "life-thinking",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/life-thinking.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/1",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=1"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 255,
					"count": 94,
					"description": "记录我的可爱小天使和她老爸的故事",
					"link": "https://www.watch-life.net/category/lovely-daughter",
					"name": "家有女儿",
					"slug": "lovely-daughter",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/happy-fathers-day-wishes.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/255",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=255"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 1059,
					"count": 57,
					"description": "微信的相关文章。",
					"link": "https://www.watch-life.net/category/wechat",
					"name": "微信",
					"slug": "wechat",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/weixin.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/1059",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=1059"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}, {
					"id": 374,
					"count": 9,
					"description": "有关网站应用的文章",
					"link": "https://www.watch-life.net/category/website-app",
					"name": "网站应用",
					"slug": "website-app",
					"taxonomy": "category",
					"parent": 0,
					"meta": [],
					"subimg": "subscription.png",
					"subflag": "0",
					"category_thumbnail_image": "https://www.watch-life.net/images/website.jpg",
					"_links": {
						"self": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories/374",
							"targetHints": {
								"allow": ["GET"]
							}
						}],
						"collection": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/categories"
						}],
						"about": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/taxonomies/category"
						}],
						"wp:post_type": [{
							"href": "https://www.watch-life.net/wp-json/wp/v2/posts?categories=374"
						}],
						"curies": [{
							"name": "wp",
							"href": "https://api.w.org/{rel}",
							"templated": true
						}]
					}
				}],
			}
		}
	}
</script>

<style>
	.ui-BG-card {
		margin: 0 8px 16px;
	}

	.post-img{
		width: 36px;
		height: 36px;
	}
	.post-title {
		height: auto;
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		/* height: 24px; */
		text-wrap: wrap;
	}

	.post-desc {
		text-align: center;
		margin-right: 0;
	}
	.post-desc image {
		width: 48px;
		height: 48px;
		border-radius: 50%;
		margin: 0 auto;
		margin-bottom: 8px;
	}
	.post-data {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
	.post-data > uni-text {
		margin-right: 0;
	}
</style>