<template>
		<view>
			<slot v-bind:list="list"></slot>
			<uni-load-more :status="moreStatus"></uni-load-more>
			<!-- 无法访问网络 -->
			<view class="showerror" :style="'display:'+(showerror)">
				<text class="cicon-wifi-off text-xsl"></text>
				<view class="errortext" @click="searchMore">
					暂时无法访问网络，下拉重试...
				</view>
			</view>
		</view>
</template>

<script>
	import config from '@/utils/config.js';
	var util = require('@/utils/util.js');

	var Auth = require('@/utils/auth.js');

	var wxApi = require('@/utils/wxApi.js');

	var wxRequest = require('@/utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		props: {
			url: {
				type: String,
				default: ''
			},
			queryObj: {
				type: Object,
				default() {
					return {}
				}
			}
		},
		data() {
			return {
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: "",
				page: 1,
				pageSize: 20,
				list: [],

				selected: 0,

				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				moreStatus: "more",
			};
		},
		watch: {
			url(newUrl) {
				console.log('url >>', newUrl)
			},
			queryObj(newVal) {
				console.log('queryObj >>', newVal)
				if(newVal) {
					this.reload();
				}
			}
		},
		methods: {
			// 刷新页面/重新加载
			reload(){
				let self = this;
				self.setData({
					page: 1,
				})
				let data = Object.assign({page: self.page, limit: self.pageSize},self.queryObj);
				
				console.log('reload', self.queryObj, data)
				self.getPersonData(data);
				// setTimeout(()=>{
				// 	console.log("停止当前页面的下拉刷新", self.page)
				// 	uni.stopPullDownRefresh(); // 请求数据，成功获取数据则停止当前页面的下拉刷新
				// }, 2000)
			},
			// 分页加载更多
			loadMore(){
				let self = this;
				if(this.moreStatus === "noMore" || this.moreStatus === "loading") {
					console.log('已经是最后一页或者加载中, 不需要发送请求');
				} else {
					self.setData({
						moreStatus: "loading",
						page: self.page + 1,
					})
					console.log('self.page', self.page);
					// setTimeout(()=> {
					// 	self.setData({
					// 		moreStatus: "noMore"
					// 		// moreStatus: "more"
					// 	})
					// }, 2000)
					this.getPersonData({page: self.page})
					console.log('loadMore');
				}
			},
			//获取学生列表数据
			getPersonData(data) {
			  var self = this;
			  if (!data) data = {};
			  if (!data.page) data.page = 1;
			  // if (!data.search) data.search = '';
			
			  if (data.page === 1) {
			    self.setData({
			      list: []
			    });
			  }
			  self.setData({
			    isLoading: true
			  });
			  let url = self.url;
			  // + '&xgh=' + self.queryObj.xgh
			  url = url + '?page=' + self.page + '&limit=' + self.pageSize ;
			  if(self.queryObj && self.queryObj.xgh) {
				  url = url + '&xgh=' + self.queryObj.xgh;
			  }
			  console.log('参数url', url)
			  var getPostsRequest = wxRequest.getRequest(url);
			  getPostsRequest.then(res => {
				console.log('getPersonData', res)
			    if (res.statusCode === 200) {
					let resData = res.data;
					if(resData.code === 0) {
						console.log('resData.data', resData.data)// list:[],count: 总数
						let curPageList = resData.data.list;
						console.log('curPageList', curPageList)
						if (curPageList.length < self.pageSize) { // 说明是最后一页
						  self.setData({
						    moreStatus: 'noMore',
						    isLoading: false
						  });
						} else {
							self.setData({
								moreStatus: 'more'
							})
						}
						self.setData({
						  list: self.list.concat(curPageList)
						});
					}
			       // setTimeout(function () {
			      //     wx.hideLoading();
			      // }, 1500);
			    } 
			  }).catch(function () {
			    if (data.page == 1) {
			      self.setData({
			        showerror: "block",
			        floatDisplay: "none"
			      });
			    } else {
			      uni.showModal({
			        title: '加载失败',
			        content: '加载数据失败,请重试.',
			        showCancel: false
			      });
			      self.setData({
			        page: data.page - 1
			      });
			    }
			  }).finally(function () {
			    uni.hideLoading();
				uni.stopPullDownRefresh();
			    self.setData({
			      isLoading: false
			    });
			  });
			},
		}
	};
</script>
<style lang="scss">
	::v-deep .uni-progress-info {
		font-size: 14px;
	}
	.card-line-title {
		font-weight: bold;
	}
	.card-line {
		display: flex;
		flex-direction: row;
	}
	.card-line-label {
		width: 70px;
	}
	.card-line-content {
		flex-grow: 1;
	}
	.card-actions {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 36px;
		border-top: 1px #eee solid;
	}
	.card-actions-item {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.card-actions-item-text {
		font-size: 12px;
		color: #666;
		margin-left: 5px;
	}
	.cover-image {
		flex: 1;
		height: 150px;
	}
	.no-border {
		border-width: 0;
	}
</style>