<!-- 主要功能 -->
<template>
	<view class="ui-grid ui-cols-1"  style="padding: 0; margin-top: 0;">
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-blue" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<view class="info_avatar">
							<image class="avatar" v-if="true" mode="aspectFill" src="@/static/images/icon-female.svg" />
							<!-- <image class="avatar" v-if="true" mode="aspectFill" src="@/static/images/logo.png" /> -->
							<image class="avatar" v-else mode="aspectFill" src="@/static/images/icon-male.svg" />
						</view>
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">姓名 - 学号</text>
							<text class="post-data">学院</text>
							<text class="post-data">专业</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
					<!-- <image src="/static/images/logo.png" mode="aspectFill" class="post-img"
					></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-pink" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-degree-hat theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<!-- <icon-view-list theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" /> -->
						<view>
							<text class="post-title ">资助服务</text>
							<text class="post-data">奖/助/贷/勤/补/困</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
					<!-- <image src="https://www.watch-life.net/images/2024/11/1-300x240.jpg" mode="aspectFill" class="post-img"></image> -->
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-purple" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-city theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">宿管服务</text>
							<text class="post-data">查宿/住宿/调退宿</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
				</view>
			</block>
		</view>
		<!-- 列表template模板 -->
		<view class="ui-item post-list">
			<block>
				<view class="post-item ui-BG ui-BG-card ui-BG-green" @tap="redictDetail" style="padding: 16px">
					<view class="post-left">
						<icon-outdoor theme="outline" :size="32" :fill="[fillColor]" :strokeWidth="3" />	
					</view>
					<view class="post-desc">
						<view>
							<text class="post-title">第二课堂</text>
							<text class="post-data">活动管理/报名/成绩</text>
						</view>
					</view>
					<view class="post-right">
						<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			/**
			 * 是否通屏
			 */
			isFull: {
				type: Boolean,
				default: false,
			},
			/**
			 * 右上角状态(每种状态显示不同颜色)
			 */
			status: {
				type: String,
				default: '',
			},
			/**
			 * 图片url
			 */
			banner: {
				type: String,
				default: '',
			},
		}
	}
</script>

<style>
	.ui-BG-card {
		margin: 0 0px;
		border-radius: 0;
		border-top: 1px solid var(--ui-TC-8);
	}
	.post-item {
		align-items: center;
	}
	/* .post-img{
		width: 36px;
		height: 36px;
	} */
	.post-title {
		height: auto;
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px;
	}
	.font-bold {
		font-weight: bold;
	}
	.post-desc {
		margin-right: 0;
		display: flex;
		width: 100%;
		padding-left: 16px;
		/* align-items: center;
		text-align: left; */
	}
	.post-left {
		width: 56px;
	}
	/* .post-left uni-image{
		width: 36px;
		margin-top: 6px;
	} */
	.info_avatar image {
		width: 58px;
		height: 58px;
		border-radius: 50%;
	}
	.post-middle {
		flex: 1;
	}
	.post-right {
		width: 16px;
	}
	.post-desc uni-image {
		padding-bottom: 4px;
	}
	.post-data {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		display: block !important;
	}
	/* .post-data > uni-text {
		margin-right: 0;
	} */
</style>