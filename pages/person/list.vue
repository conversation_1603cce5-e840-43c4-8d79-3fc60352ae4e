<template>
		<!-- tabbar -->
		<ui-sys>
			<uni-search-bar radius="100" @confirm="search" :focus="false" v-model="searchValue" @blur="blur" @focus="focus" @input="input"
				placeholder="请输入学号"	@cancel="cancel" @clear="clear" cancelButton="none" clearButton="none" >
			</uni-search-bar>
			<BaseList ref="list" style="margin-top: -10px;margin-left: 8px;margin-right: 8px;" :url="url" :queryObj="queryObj">
				<template v-slot:default="slotProps">
						<!-- 列表template模板 -->
						<view class="ui-grid ui-cols-3">
							<block v-for="(item, index) in slotProps.list">
								<view class="ui-item" @click="handleDetail(item)" :key="item.id">
									<view class="image-box">
										<image v-if="item.photo" @click.stop="imagePreview(item)" class="avatar" mode="aspectFill" :src="'/api/file/inline/' + JSON.parse(item.photo)[0].id" />
										<image @click.stop="imagePreview(item)" class="avatar" v-else-if="item.xb && item.xb == '女'" mode="aspectFill" src="@/static/images/icon-female.svg" />
										<image @click.stop="imagePreview(item)" class="avatar" v-else mode="aspectFill" src="@/static/images/icon-male.svg" />
									</view>
									<view class="title-text">
										{{item.xm}}
									</view>
									<!-- <view class="title-text" v-if="userType == 'student' && roleName == '学生处'">
										{{item.xgh}}
									</view> -->
									<view class="normal-text" v-if="userType == 'student' && roleName == '学生处'">
										{{item.xymc}}
									</view>
									<view class="normal-text" v-if="userType == 'student' && roleName == '辅导员'">
										{{item.bjmc}}
									</view>
									<!-- <view class="normal-text" v-if="userType == 'student' && roleName == '学生处'">
										{{item.zymc}}
									</view> -->
									<view class="normal-text">
										<a @click.stop="" style="color: rgb(25, 137, 250)" :href="'tel:'+item.sjh">{{item.sjh}}</a>
									</view>
								</view>
							</block>
						</view>
					<!-- <view class="ui-grid ui-cols-2"  style="padding: 0 8px; margin-top: 0;"> -->
						<!-- <view class="ui-item post-list" v-for="(item, index) in slotProps.list" :key="item.id">
							<block>
								<view class="post-item ui-BG ui-BG-card ui-BG-blue" @click="handleDetail(item)" style="padding: 16px 8px">
									<view class="post-desc">
										<image v-if="item.photo" @click.stop="imagePreview(item)" class="avatar" mode="aspectFill" :src="'/api/file/inline/' + JSON.parse(item.photo)[0].id" />
										<image @click.stop="imagePreview(item)" class="avatar" v-else-if="item.xb && item.xb == '女'" mode="aspectFill" src="@/static/images/icon-female.svg" />
										<image @click.stop="imagePreview(item)" class="avatar" v-else mode="aspectFill" src="@/static/images/icon-male.svg" />
										<view class="post-title">
											<text>{{item.xm}}</text>
										</view>
										<view class="post-data" v-if="userType == 'student' && roleName == '学生处'">
											<text>{{item.xgh}}</text>
										</view>
										<view class="post-data">
											<text>{{item.xymc}}</text>
										</view>
										<view class="post-data" v-if="userType == 'student' && roleName == '学生处'">
											<text>{{item.zymc}}</text>
										</view>
										<view class="post-data">
											<a @click.stop="" :href="'tel:'+item.sjh">{{item.sjh}}</a>
										</view>
									</view>
								</view>
							</block>
						</view> -->
					<!-- </view> -->
				</template>
			</BaseList>
			<!-- 普通弹窗 -->
			<uni-popup ref="popup" background-color="#fff" @change="change">
				<view class="popup-content">
					<image v-if="currentPerson&&currentPerson.photo" class="avatar" mode="aspectFill" :src="'/api/file/inline/' + JSON.parse(currentPerson.photo)[0].id" />
					<image v-else-if="currentPerson.xb&&currentPerson.xb =='女'" class="avatar" mode="aspectFill" src="@/static/images/icon-female.svg" />
					<image v-else-if="currentPerson.xb&&currentPerson.xb =='男'" class="avatar" mode="aspectFill" src="@/static/images/icon-male.svg" />
				</view>
			</uni-popup>
			<HoverBall></HoverBall>
		</ui-sys>
</template>

<script>
	import config from '../../utils/config.js';
	import ListItem from "@/components/ListItem.vue";
	import store from '@/ui/store'

	var personApi = require('@/utils/person/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	import BaseList from "./components/BaseList.vue"
	export default {
		components: {
			BaseList,
			ListItem,
		},
		data() {
			return {
				roleName: store.getters.getRoleName,
				queryObj: {}, // 检索条件
				items: ['选项卡1', '选项卡2'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: require('@/static/home-icon/banner.png'),
				searchValue: null,
				url: null,
				selected: 0,
				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				userType: '',
				currentPerson: {}
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			console.log('options',options, this.roleName)
			this.setData({
				userType: options.type,
				url: personApi.personQueryPage() + options.type + '/queryPage'
			})
			console.log('options',options, options.type, this.url)
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			// baseList组件请求数据
			this.$refs.list.reload();
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// if (typeof this.getTabBar === 'function' && this.getTabBar()) {
			// 	this.getTabBar().setData({
			// 		selected: 3
			// 	});
			// }
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
			// baseList组件请求数据
			this.$refs.list.reload(); // 注：请求数据，成功获取数据则停止下拉刷新
			// uni.startPullDownRefresh();//主动触发下拉刷新
			// setTimeout(()=>{
			// 	uni.stopPullDownRefresh(); 
			// }, 2000)
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
			// baseList分页组件加载更多
			this.$refs.list.loadMore();
		},
		methods: {
			imagePreview(item){
				console.log('点击了图片', item);
				this.setData({
					currentPerson: item
				})
				this.$refs.popup.open();
			},
			change(e) {
				console.log('弹出层状态改变了', e);
				if(!e.show) { // 关闭清空
					this.setData({
						currentPerson: {}
					})
				}
			},
			search() {
				console.log('search', this.searchValue);
				this.queryObj = Object.assign({}, {xgh: this.searchValue});
				// this.$refs.list.reload();
			},
			input(res) {
				console.log('----input:', res)
			},
			clear(res) {
				// uni.showToast({
				// 	title: 'clear事件，清除值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			blur(res) {
				// uni.showToast({
				// 	title: 'blur事件，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			focus(e) {
				// uni.showToast({
				// 	title: 'focus事件，输出值为：' + e.value,
				// 	icon: 'none'
				// })
			},
			cancel(res) {
				// uni.showToast({
				// 	title: '点击取消，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			actionsClick(text, item){
				// uni.showToast({
				// 	title:text,
				// 	icon:'none'
				// })
				console.log('点击了签到', item)
				if(text =='签到') {
					uni.navigateTo({
						url: '/pages/demo/qd-page?id=' + item.id
					});
				} else if(text =='新增') {
					uni.navigateTo({
						url: '/pages/demo/add-page'
					});
				} else if(text =='审核') {
					uni.navigateTo({
						url: '/pages/demo/detail-page'
					});
				}
			},
			handleDetail(item){
				console.log('点击了详情', item)
				uni.navigateTo({
					url: '/pages/my/my?userId=' + item.xgh 
					+ '&userType=' + this.userType 
					+ '&enterType=edit' // preview
				});
				// uni.navigateTo({
				// 	url: '/pages/demo/detail-page?id=' + item.id
				// });
			},
			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},
			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '',
					"dialog.content": ''
				});
			}
		}
	};
</script>
<style lang="scss">
	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}
	/**列表子项样式*/
	.ui-BG-card {
		margin: 0 8px 16px;
	}
	.ui-item.post-list:nth-child(1),.ui-item.post-list:nth-child(2) {
		margin-top: 10px;
	}
	.post-title {
		height: auto;
		margin-bottom: 4px;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		// line-height: 20px;
		font-style: normal;
		font-weight: 500;
		/* height: 24px; */
		text-wrap: wrap;
	}
	
	.post-desc {
		text-align: center !important;
		margin-right: 0;
	}
	.post-desc image {
		width: 48px;
		height: 48px;
		border-radius: 50%;
		margin: 0 auto;
		margin-bottom: 8px;
	}
	.post-data {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		width: 100%;
		justify-content: center;
	}
	.post-data > uni-text{
		margin-right: 0;
	}
	.post-data a {
		text-decoration: none;
		color: var(--ui-TC-6) !important;
	}
	// 图片预览
	.popup-content {
		@include flex;
		align-items: center;
		justify-content: center;
		// padding: 15px;
		width: 100vw;
		height: 100vw;
		background-color: #fff;
	}
	.popup-content image {
		width: 100vw;
		height: 100vw;
	}

	.popup-height {
		@include height;
		width: 200px;
	}
	// 横向3个学生样式
	.ui-cols-3 .ui-item {
		width: calc((100% - 48px) / 3);
	}
	.ui-item {
		display: block;
		position: relative;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
		background-color: #ffffff;
		margin: 8px;
		border-radius: 12px;
		padding: 16px 4px 16px;
		line-break: anywhere;
		box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.10), 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
	}
	::v-deep .ui-item .title-text, .ui-item .normal-text {
		line-height: 18px !important;
	}
	.image-box {
		// margin-bottom: 10px;
	}
	.image-box image {
		width: 48px;
		height: 48px;
		border-radius: 50%;
		margin: 0 auto;
		margin-bottom: 2px;
	}
	.title-text {
		height: auto;
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
	}
	.ui-item .normal-text {
		// color: #333;
		// font-weight: 300;
		margin-bottom: 0;
		color: var(--ui-TC-5) !important;
		font-family: "Inter";
		font-size: 14px !important;
		font-style: normal;
		font-weight: 400;
		// line-height: 30px;
		 /* 1.429 */
	}
	// .normal-text {
	// 	color: var(--ui-TC-6) !important;
	// 	font-family: "Inter";
	// 	font-size: 14px;
	// 	font-style: normal;
	// 	font-weight: 400;
	// 	width: 100%;
	// 	justify-content: center;
	// }
</style>