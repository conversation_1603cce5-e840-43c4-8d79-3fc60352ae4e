<template>
		<!-- tabbar -->
		<ui-sys>
			<uni-search-bar radius="100" @confirm="search" :focus="false" v-model="searchValue" @blur="blur" @focus="focus" @input="input"
				placeholder="请输入学号"	@cancel="cancel" @clear="clear" cancelButton="none" clearButton="none" >
			</uni-search-bar>
			<BaseList ref="list" style="margin-top: 0px;" :url="url" :queryObj="queryObj">
				<template v-slot:default="slotProps">
					<view class="ui-grid ui-cols-1"  style="padding: 0; margin-top: 0;">
						<view class="ui-item post-list" v-for="(item, index) in slotProps.list" :key="item.id">
							<view class="post-item ui-BG ui-BG-card ui-BG-blue"  @click="handleDetail(item)" style="padding: 16px;">
								<view class="post-left">
									<view class="info_avatar">
										<image class="avatar" v-if="item.xb && item.xb == '女'" mode="aspectFill" src="@/static/images/icon-female.svg" />
										<!-- <image class="avatar" v-if="true" mode="aspectFill" src="@/static/images/logo.png" /> -->
										<image class="avatar" v-else mode="aspectFill" src="@/static/images/icon-male.svg" />
									</view>
								</view>
								<view class="post-desc">
									<view>
										<text class="post-title">{{item.xm}}
										<span style="color: var(--ui-TC-6) !important;margin-left: 8px;">{{item.xgh}}</span>
										</text>
										<text class="post-data">{{item.xymc}}</text>
										<text class="post-data">{{item.zymc}}</text>
										<!-- <text class="post-data">{{item.sjh}}</text> -->
										<a class="post-data" @click.stop="" :href="'tel:'+item.sjh">{{item.sjh}}</a>
									</view>
								</view>
								<view class="post-right">
									<icon-right :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" />
								</view>
							</view>
						</view>
					</view>
				</template>
			</BaseList>
			<HoverBall></HoverBall>
		</ui-sys>
</template>

<script>
	import config from '../../utils/config.js';
	import ListItem from "@/components/ListItem.vue";

	var personApi = require('@/utils/person/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	import BaseList from "./components/BaseList.vue"
	export default {
		components: {
			BaseList,
			ListItem,
		},
		data() {
			return {
				queryObj: {}, // 检索条件
				items: ['选项卡1', '选项卡2'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: require('@/static/home-icon/banner.png'),
				searchValue: null,
				url: null,
				selected: 0,
				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				userType: ''
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			console.log('options',options, options.type)
			this.setData({
				userType: options.type,
				url: personApi.personQueryPage() + options.type + '/queryPage'
			})
			console.log('options',options, options.type, this.url)
			
			
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			// baseList组件请求数据
			this.$refs.list.reload();
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// if (typeof this.getTabBar === 'function' && this.getTabBar()) {
			// 	this.getTabBar().setData({
			// 		selected: 3
			// 	});
			// }
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
			// baseList组件请求数据
			this.$refs.list.reload(); // 注：请求数据，成功获取数据则停止下拉刷新
			// uni.startPullDownRefresh();//主动触发下拉刷新
			// setTimeout(()=>{
			// 	uni.stopPullDownRefresh(); 
			// }, 2000)
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
			// baseList分页组件加载更多
			this.$refs.list.loadMore();
		},
		methods: {
			search() {
				console.log('search', this.searchValue);
				this.queryObj = Object.assign({}, {xgh: this.searchValue});
				// this.$refs.list.reload();
			},
			input(res) {
				console.log('----input:', res)
			},
			clear(res) {
				// uni.showToast({
				// 	title: 'clear事件，清除值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			blur(res) {
				// uni.showToast({
				// 	title: 'blur事件，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			focus(e) {
				// uni.showToast({
				// 	title: 'focus事件，输出值为：' + e.value,
				// 	icon: 'none'
				// })
			},
			cancel(res) {
				// uni.showToast({
				// 	title: '点击取消，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			actionsClick(text, item){
				// uni.showToast({
				// 	title:text,
				// 	icon:'none'
				// })
				console.log('点击了签到', item)
				if(text =='签到') {
					uni.navigateTo({
						url: '/pages/demo/qd-page?id=' + item.id
					});
				} else if(text =='新增') {
					uni.navigateTo({
						url: '/pages/demo/add-page'
					});
				} else if(text =='审核') {
					uni.navigateTo({
						url: '/pages/demo/detail-page'
					});
				}
			},
			handleDetail(item){
				console.log('点击了详情', item)
				uni.navigateTo({
					url: '/pages/my/my?userId=' + item.xgh 
					+ '&userType=' + this.userType 
					+ '&enterType=edit' // preview
				});
				// uni.navigateTo({
				// 	url: '/pages/demo/detail-page?id=' + item.id
				// });
			},
			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},
			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '',
					"dialog.content": ''
				});
			}
		}
	};
</script>
<style lang="scss">
	/**列表子项样式*/
	.ui-BG-card {
		margin: 0 !important;
		border-radius: 0;
		border-top: 1px solid var(--ui-TC-8);
	}
	.post-item {
		align-items: center;
	}
	/* .post-img{
		width: 36px;
		height: 36px;
	} */
	.post-title {
		height: auto;
		margin-bottom: 0;
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px;
		text-wrap: wrap;
	}
	.font-bold {
		font-weight: bold;
	}
	.post-desc {
		margin-right: 0;
		display: flex;
		width: 100%;
		padding-left: 16px;
		/* align-items: center;
		text-align: left; */
	}
	.post-left {
		width: 56px;
	}
	/* .post-left uni-image{
		width: 36px;
		margin-top: 6px;
	} */
	.post-left image {
		width: 58px;
		height: 58px;
		border-radius: 50%;
		margin-top: 10px;
	}
	.post-middle {
		flex: 1;
	}
	.post-right {
		width: 16px;
	}
	.post-desc uni-image {
		padding-bottom: 4px;
	}
	.post-data {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		text-decoration: none;
	}
</style>