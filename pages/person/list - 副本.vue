<template>
		<!-- tabbar -->
		<ui-sys>
			<uni-search-bar radius="100" @confirm="search" :focus="false" v-model="searchValue" @blur="blur" @focus="focus" @input="input"
				placeholder="请输入学号"	@cancel="cancel" @clear="clear" cancelButton="none" clearButton="none" >
			</uni-search-bar>
			<BaseList ref="list" style="margin-top: -10px;" :url="url" :queryObj="queryObj">
				<template v-slot:default="slotProps">
					<List-Item v-for="(item, index) in slotProps.list" :isFull="item.isFull" :status="item.status" :banner="item.banner" :key="item.id">
						<view @click="handleDetail(item)" slot="content" class="card-content">
							<view class="card-line-title">
								{{item.xm}} - {{item.xgh}}
							</view>
							<view class="card-line">
								<view class="card-line-label">性别: </view>
								<view class="card-line-content">
									{{item.xb}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">学院: </view>
								<view class="card-line-content">
									{{item.xymc}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">专业: </view>
								<view class="card-line-content">
									{{item.zymc}}
								</view>
							</view>
							<!-- <view class="card-line">
								<view class="card-line-label">签到时间: </view>
								<view class="card-line-content">
									{{item.startDate}} 至 {{item.endDate}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">签到进度:</view>
								<view class="card-line-content">
									<progress :percent="item.progress" show-info stroke-width="6" />
								</view>
							</view> -->
						</view>
						<!-- <view slot="actions" class="card-actions">
							<view class="card-actions-item" @click="actionsClick('签到', item)">
								<text class="card-actions-item-text">签到</text>
							</view>
							<view class="card-actions-item" @click="actionsClick('新增', item)">
								<text class="card-actions-item-text">编辑</text>
							</view>
							<view class="card-actions-item" @click="actionsClick('审核', item)">
								<text class="card-actions-item-text">审核</text>
							</view>
						</view> -->
					</List-Item>
				</template>
			</BaseList>
			<HoverBall></HoverBall>
		</ui-sys>
</template>

<script>
	import config from '../../utils/config.js';
	import ListItem from "@/components/ListItem.vue";

	var personApi = require('@/utils/person/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	import BaseList from "./components/BaseList.vue"
	export default {
		components: {
			BaseList,
			ListItem,
			ListItemImgLeft
		},
		data() {
			return {
				queryObj: {}, // 检索条件
				items: ['选项卡1', '选项卡2'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: require('@/static/home-icon/banner.png'),
				searchValue: null,
				url: null,
				selected: 0,
				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				userType: ''
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			console.log('options',options, options.type)
			this.setData({
				userType: options.type,
				url: personApi.personQueryPage() + options.type + '/queryPage'
			})
			console.log('options',options, options.type, this.url)
			
			
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {
			// baseList组件请求数据
			this.$refs.list.reload();
		},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			// if (typeof this.getTabBar === 'function' && this.getTabBar()) {
			// 	this.getTabBar().setData({
			// 		selected: 3
			// 	});
			// }
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
			// baseList组件请求数据
			this.$refs.list.reload(); // 注：请求数据，成功获取数据则停止下拉刷新
			// uni.startPullDownRefresh();//主动触发下拉刷新
			// setTimeout(()=>{
			// 	uni.stopPullDownRefresh(); 
			// }, 2000)
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
			// baseList分页组件加载更多
			this.$refs.list.loadMore();
		},
		methods: {
			search() {
				console.log('search', this.searchValue);
				this.queryObj = Object.assign({}, {xgh: this.searchValue});
				// this.$refs.list.reload();
			},
			input(res) {
				console.log('----input:', res)
			},
			clear(res) {
				// uni.showToast({
				// 	title: 'clear事件，清除值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			blur(res) {
				// uni.showToast({
				// 	title: 'blur事件，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			focus(e) {
				// uni.showToast({
				// 	title: 'focus事件，输出值为：' + e.value,
				// 	icon: 'none'
				// })
			},
			cancel(res) {
				// uni.showToast({
				// 	title: '点击取消，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			actionsClick(text, item){
				// uni.showToast({
				// 	title:text,
				// 	icon:'none'
				// })
				console.log('点击了签到', item)
				if(text =='签到') {
					uni.navigateTo({
						url: '/pages/demo/qd-page?id=' + item.id
					});
				} else if(text =='新增') {
					uni.navigateTo({
						url: '/pages/demo/add-page'
					});
				} else if(text =='审核') {
					uni.navigateTo({
						url: '/pages/demo/detail-page'
					});
				}
			},
			handleDetail(item){
				console.log('点击了详情', item)
				uni.navigateTo({
					url: '/pages/my/my?userId=' + item.xgh 
					+ '&userType=' + this.userType 
					+ '&enterType=edit' // preview
				});
				// uni.navigateTo({
				// 	url: '/pages/demo/detail-page?id=' + item.id
				// });
			},
			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},
			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '',
					"dialog.content": ''
				});
			}
		}
	};
</script>
<style lang="scss">
	/**列表子项样式*/
	::v-deep .uni-progress-info {
		font-size: 14px;
	}
	.card-content {
		padding-bottom: 8px;
	}
	.card-line-title {
		font-weight: bold;
	}
	.card-line {
		display: flex;
		flex-direction: row;
	}
	.card-line-label {
		width: 40px;
		font-size: 13px;
	}
	.card-line-content {
		font-size: 13px;
		flex-grow: 1;
	}
	.card-actions {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 36px;
		border-top: 1px #eee solid;
	}
	.card-actions-item {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.card-actions-item-text {
		font-size: 12px;
		color: #00a5ec;
		margin-left: 5px;
	}
	.cover-image {
		flex: 1;
		height: 150px;
	}
	.no-border {
		border-width: 0;
	}
</style>