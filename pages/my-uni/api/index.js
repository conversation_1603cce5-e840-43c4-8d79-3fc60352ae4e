import config from "@/utils/config.js";
var domain = config.getDomain;
var pageCount = config.getPageCount;
var categoriesID = config.getCategoriesID;
// var HOST_URI = 'https://' + domain + '/wp-json/wp/v2/';
var HOST_URI_WATCH_LIFE_JSON = 'http://' + domain + '/wp-json/watch-life-net/v1/';
// var HOST_URI = 'http://' + domain + '/api/';
module.exports = {
	/**
	 * 通用字典列表
	 * @param { codeType }  codeType - 字典类型
	*/
	codeCommonQuery: function () {
	  return '/api/code/codeCommon';
	},
	/**
	 * 学院表
	*/
	codeDwbQuery: function () {
	  return '/api/code/codeDwb';
	},
	/**
	 * 专业表
	 * @param { xyid }  xyid - 学院id
	*/
	codeZybQuery: function () {
	  return '/api/code/codeZyb';
	},
	/**
	 * 班级表
	 * @param { zyid }  zyid - 专业id
	*/
	codeZybQuery: function () {
	  return '/api/code/codeBjb';
	}
}