<template>
	<view class="my-uni-data-select">
	    <uni-data-select
	      v-model="innerValue"
	      :localdata="options"
	      :placeholder="placeholder"
	      @change="handleChange"
		  :disabled="disabled"
	    />
	</view>
</template>

<script>
var Api = require('../../api/index.js');
var wxRequest = require('@/utils/wxRequest.js');

export default {
  props: {
	/** 值 */
    value: {
      type: [Array, String, Number],
      default: () => []
    },
	/** 下拉选项相关的属性 */
	props: {
		type: Object,
		default: () => {}
	},
    placeholder: {
      type: String,
      default: '请选择'
    },
	/** 是否禁用 */
	disabled: {
		type: Boolean,
		default: false
	}
  },
  data() {
    return {
	  /**子组件接收父组件传递的值，用于在当前组件中修改*/
      innerValue: this.value,
	  options: []
    };
  },
  watch: {
	props: {
		immediate: true,
		handler(newVal) {
			if(newVal) {
				this.getOptions();
			}
			console.log('props newVal', newVal)
		  // this.innerValue = newVal;
		}
	},
    value: {
      immediate: true,
      handler(newVal) {
        this.innerValue = newVal;
      }
    },
    innerValue(newVal) {
      this.$emit('input', newVal);
      // this.$emit('update:value', newVal);
    }
  },
  methods: {
    handleChange(value) {
	  console.log('handleChange', value);
      this.$emit('change', value);
    },
   //获取首页组件
   getOptions() {
     var self = this;
     // self.setData({
     // 	isLoading: true
     // });
     // uni.showLoading({
     //   title: '加载中',
     //   mask: true
     // });
     let data = {}
     let url = Api.codeCommonQuery();
	 url = url + '?codeType=' + self.props.code;
	 if(self.props.dicQueryParams && self.props.dicQueryParams.dictFieldUrl) {
		 url = url.split('/api')[0] + '/api' + self.props.dicQueryParams.dictFieldUrl
	 }
     var getcodeCommonQuery = wxRequest.getRequest(url, data);
     getcodeCommonQuery.then(response => {
   	console.log('getcodeCommonQuery', response, self.props.dicQueryParams)
   	console.log('self.props', self.props, self.props.dicQueryParams.getValType)
   	console.log('getValType', self.props.dicQueryParams.getValType)
       if (response.statusCode === 200) {
   		let resData = response.data;
   		if(resData.code === 0) {
   			let resDataList = resData.data;
			let options = [];
			let props = self.props;
			resDataList.forEach(item => {
				// console.log('item', item)
				let value, text;
				if(self.props.dicQueryParams && self.props.dicQueryParams.getValType) {
				  // console.log('有', self.props.dicQueryParams.getValType)
				  value = item[self.props.dicQueryParams.getValType];
				  text = item[self.props.dicQueryParams.getValType];
				} else if(self.props.dicQueryParams && self.props.dicQueryParams.textField && self.props.dicQueryParams.valueField) {
					// console.log('1111')
					value = item[self.props.dicQueryParams.valueField];
					text = item[self.props.dicQueryParams.textField];
				}
				options.push({
					value,
					text
				})
			})
			console.log('options', options)
			self.setData({
				options: options
			})
   		} else {
   			uni.showToast({
   				title: resData.message,
   				icon: 'error',
   				duration: 1500
   			});
   		}
   		
       } else if (response.statusCode === 404) {
         console.log('查询失败');
       }
     }).catch(function () {
   	uni.hideLoading();
   	self.setData({
   	  showerror: "block",
   	  floatDisplay: "block"
   	});
     }).finally(function () {
   	  uni.hideLoading();
     });
   },
	
  }
};
</script>

<style scoped>
.my-uni-data-select {
  width: 100%;
  margin-bottom: 15px;
}
</style>