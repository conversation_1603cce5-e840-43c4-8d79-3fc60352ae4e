<template>
    <div>
        <van-popup
                :value="show"
                v-on:input="onChangeShow($event)"
                position="bottom"
        >
            <van-cell-group>
                <van-cell
                        v-for="item in columns"
                        :key="item"
                        @click="onItemClick(item)"
                >
                    <van-checkbox
                            slot="title"
                            :value="item === result"
                    >{{item}}
                    </van-checkbox>
                </van-cell>
            </van-cell-group>
            <div class="multi-s">
                <van-button class="multi-s-button" type="info" @click="onConfirm">确认</van-button>
                <van-button class="multi-s-button" type="default" @click="onCancel">取消</van-button>
            </div>
        </van-popup>
    </div>
</template>

<script>
    export default {
        name: "ZZ_RadioSelector",
        props: {
            value: String,
            show: Boolean,
            columns: Array
        },
        data() {
            return {
                result: null
            }
        },
        watch: {
            show(newVal) {
                if (newVal) {
                    this.initResults();
                }
            }
        },
        methods: {
            initResults() {
                // let value = this.value;
                // if (!value) {
                //     value = [];
                // }
                // let arr = [];
                // this.columnsShow.forEach(item => {
                //     arr.push({name: item, checked: value.includes(item)})
                // });
                // this.results = arr;
                this.result = this.value;
            },
            onItemClick(model) {
                // model.checked = !model.checked;
                this.result = model;
            },
            onConfirm() {
                // let arr = [];
                // this.results.forEach(item => {
                //     if (item.checked) {
                //         arr.push(item.name);
                //     }
                // });
                // this.$emit('input', arr);
                this.$emit('input', this.result);
                this.onCancel();
            },
            onCancel() {
                this.onChangeShow(false);
            },
            onChangeShow(value) {
                this.$emit('update:show', value);
            }
        }
    }
</script>

<style scoped>
    .multi-s {
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        justify-content: center;
        align-items: center;
    }

    .multi-s-button {
        flex-grow: 1;
        height: 35px;
        width: 45%;
        margin-left: 2%;
        margin-right: 2%;
        line-height: 35px;
        /*background: #00a5ec;*/
        font-size: 16px;
    }
</style>
