<template>
    <div>
        <van-field
                :error-message="errorMessage"
                :label="label"
                :value="valueShow"
                :required="required"
                @click="selectorShow = true"
                readonly
                :placeholder="placeholder"/>
        <!--        <ZZ_Selector v-on:update:value="onInput($event)"-->
        <!--                     :columns="field.el && field.el.split('$')" :show.sync="selectorShow"></ZZ_Selector>-->
        <MyDatePicker :value="valueBind" v-on:update:value="onInput($event)" :show.sync="selectorShow" type="date"></MyDatePicker>
    </div>
</template>

<script>
    import MyDatePicker from "./MyDatePicker";
    import {dateFormat_YMD} from "@/pages/my/utils/my_moment";

    export default {
        components: {
            MyDatePicker
        },
        name: "MyFieldSelector",
        props: {
            field: Object,
            placeholder: String,
            value: String,
            errorMessage: String,
            required: <PERSON><PERSON>an,
            label: String,
        },
        data() {
            return {
                selectorShow: false
            }
        },
        computed: {
            valueShow() {
                if (this.value) {
                    return dateFormat_YMD(this.value);
                }
                return '';
            },
            valueBind() {
                if (this.value) {
                    return new Date(this.value);
                }
                return new Date();
            }
        },
        methods: {
            onInput(value) {
                this.$emit("input", dateFormat_YMD(value))
            }
        }
    }
</script>

<style scoped>

</style>
