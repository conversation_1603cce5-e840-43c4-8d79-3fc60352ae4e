<!-- form表单展示各种类型的字段 -->
<template>
	<view class="container" style="padding: 12px;">
		table形式展示
		<!-- <uni-forms ref="form" :model="formData" validate-trigger="bind">
		  <uni-forms-item
			v-for="(field, index) in initItems"
			:key="field.key"
			:label="field.label"
			:label-width="100"
		  >
			<view v-if="field.type === 'input'">
			  <uni-easyinput
				v-model="formData[field.prop]"
				placeholder="请输入"
				:required="field.required"
				:rules="field.rules"
			  />
			</view>
			<view v-if="field.type === 'select' || field.type === 'dictSelect'">
			  <uni-easyinput
				v-model="formData[field.prop]"
				placeholder="请输入"
			  />
			</view>
			<view v-else-if="field.type === 'number'">
			  <uni-number-box
				v-model="formData[field.prop]"
				:min="0"
			  />
			</view>
			<view v-else-if="field.type === 'textarea'">
			  <uni-easyinput
				type="textarea"
				v-model="formData[field.prop]"
				placeholder="请输入"
			  />
			</view>
			<view v-else-if="field.type === 'radio'">
			  <uni-data-checkbox
				v-model="formData[field.prop]"
				:localdata="field.datas"
			  />
			</view>
			<view v-else-if="field.type === 'checkbox'">
			  <uni-data-checkbox
				multiple
				v-model="formData[field.prop]"
				:localdata="field.datas"
			  />
			</view>
			<view v-else-if="field.type === 'date'">
			  <uni-datetime-picker
				type="date"
				v-model="formData[field.prop]"
				placeholder="请选择日期"
			  />
			</view>
			<view v-else-if="field.type === 'img'">
			  <uni-file-picker
				v-model="formData[field.prop]"
				file-mediatype="['image']"
				mode="grid"
				:limit="1"
			  />
			</view>
		  </uni-forms-item>
		</uni-forms> -->
	</view>
</template>

<script>
	var Api = require('@/utils/api.js');
	var wxRequest = require('@/utils/wxRequest.js');
	var util = require('../util.js')
	
	export default {
		props: {
			currentGroup: {
				type: Object,
				default() {
					return {}
				}
			},
			userInfo: {
				type: Object,
				default() {
					return {}
				}
			},
			roleName: {
				type: String,
				default: ''
			},
		},
		data(){
			return {
				formData: {},
				fieldData: [], /**所有字段list*/
				initItems: [], /** 表单项 */
			}
		},
		computed: {
		},
		created(){
			this.formData = this.userInfo;
			this.getData();
			console.log('this.currentGroup', this.currentGroup, util.baseCodeTypes);
			console.log('this.formData', this.formData);
		},
		// watch: {
		// 	currentGroup(newVal){
		// 		console.log('currentGroup', newVal)
		// 		if(newVal) {
		// 			this.getData();
		// 		}
		// 	},
		// },
		methods: {
			//获取个人信息子菜单
			getData() {
			  var self = this;
			  // uni.showLoading({
			  //   title: '查询中',
			  //   mask: true
			  // });
			  let data = {
				  groupId: this.currentGroup.id, groupName: this.currentGroup.groupName, tempField: '否'
			  }
			  console.log('self.roleName', self.roleName)
			  let url = Api.getTFieldListByGroup();
			  if(self.roleName == '学生' || self.roleName == 'student') {
				  url = Api.getSFieldListByGroup();
			  }
			  var getFieldGroupListRequest = wxRequest.getRequest(url, data);
			  getFieldGroupListRequest.then(response => {
				console.log('bygroup >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						let list = resData.data;
						self.setData({
							fieldData: list
						})
						if (list) {
						  this.formatFormData(list)
						}
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  // self.fetchCodeImgData();
			    }, 1000);
			  });
			},
			formatFormData(fieldData) {
			    let fieldResult = [];
			    if (fieldData && fieldData.length > 0) {
			      fieldData.forEach((e, index) => {
			        let props = {};
			        let selfFieldLink = '';
			        let nextField = '';
			        let typeKey = e.controlType;
			        let type = e.controlType;
			        if (util.baseCodeTypes.includes(e.controlType)) {
			          if (e.loadDataType && e.loadDataType !== 'other') {
			            type = 'dictSelect';
			            props = {
			              code: e.loadDataType,
			              dicQueryParams: {
			                getValType: '',
			                valueField: e.valueField,
			                textField: e.textField
			              },
			              filterable: true
			            };
			            if (!e.valueField && !e.textField) {
			              props.dicQueryParams.getValType =
			                e.loadDataType !== 'pycc' ? 'name' : '';
			            }
			          } else {
			            //院系专业班级页面逻辑处理
			            type = 'dictSelect';
			            let diyCode = '';
			            if (e.loadDataUrl === '/code/codeDwb') {
			              diyCode = 'xymc';
			              selfFieldLink = 'xymc';
			              nextField = 'zymc';
			            } else if (e.loadDataUrl === '/code/codeZyb') {
			              diyCode = 'zymc';
			              selfFieldLink = 'zymc';
			              nextField = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeBjb') {
			              diyCode = 'bjmc';
			              selfFieldLink = 'bjmc';
			            } else if (e.loadDataUrl === '/code/codeCommon/regionList') {
			              diyCode = 'regions';
			            } else {
			              diyCode = e.loadDataType;
			            }
			            props = {
			              code: diyCode,
			              filterable: true,
			              dicQueryParams: {
			                dictFieldUrl: e.loadDataUrl,
			                valueField: e.valueField,
			                textField: e.textField
			              }
			            };
			          }
			        } else if (
			          e.controlType === 'fileUpload' ||
			          e.controlType === 'imageUpload'
			        ) {
			          //附件上传数量
			          props = {
			            limit: e.regExpression ? Number(e.regExpression) : 1
			          };
			        }
			        let newObj = Object.assign(
			          {
			            key: 'fieldKey_' + index,
			            label: e.fieldZh,
			            prop: e.fieldEn,
			            type: type,
			            typeKey: typeKey,
			            required: e.required === '是',
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            fieldLinks: e.fieldLinks,
			            selfFieldLink: selfFieldLink,
			            nextField: nextField,
			            // colProps: [
			            //   'textareaBlobtext',
			            //   'textarea',
			            //   'smoothSignature',
			            //   'radioButton',
			            //   'checkboxButton'
			            // ].includes(typeKey)
			            //   ? 24
			            //   : (e?.span ?? 8)
			          },
			          {
			            props: props
			          }
			        );
			        fieldResult.push(newObj);
			
			        if (e.controlType === 'regions') {
			          //省市区+详细地址信息
			          fieldResult.push({
			            key: 'fieldKey_99' + index,
			            label: '详细地址',
			            prop: e.fieldEn + '_regionsDetail',
			            type: 'input',
			            typeKey: 'input',
			            required: false,
			            showFlag: e.showFlag,
			            selfModifyFlag: e.showFlag,
			            colProps: 8
			          });
			        }
			      });
			      this.initItems = fieldResult;
				  console.log('this.initItems', this.initItems)
			    }
			    let fieldFormResult = {};
			    if (fieldResult && fieldResult.length > 0) {
			      fieldResult.forEach((item) => {
			        if (item.typeKey) {
			          if (
			            uploadTypes.includes(item.typeKey) ||
			            regionsArrayTypes.includes(item.typeKey) ||
			            arrayTypes.includes(item.typeKey)
			          ) {
			            fieldFormResult[item.prop] = [];
			          }
			          if (
			            stringTypes.includes(item.typeKey) ||
			            selectTypes.includes(item.typeKey)
			          ) {
			            fieldFormResult[item.prop] = '';
			          }
			        }
			      });
			      initModel.value = fieldFormResult;
			    }
			    if (currentData.value) {
			      if (fieldResult && fieldResult.length > 0) {
			        fieldResult.forEach((item) => {
			          let setValue = null;
			          let dataValue = currentData.value
			            ? currentData.value[item.prop]
			            : null;
			          if (dataValue) {
			            if (regionsArrayTypes.includes(item.typeKey)) {
			              let regionsArr = dataValue.split(',');
			              if (item.typeKey === 'regions') {
			                if (regionsArr.length > 0) {
			                  if (regionsArr[3]) {
			                    initItems.value.filter((f) => {
			                      if (f.prop === item.prop + '_regionsDetail') {
			                        initModel.value[f.prop] = regionsArr[3];
			                      }
			                    });
			                    regionsArr.splice(regionsArr.length - 1, 1);
			                    setValue = regionsArr;
			                  } else {
			                    setValue = regionsArr;
			                  }
			                }
			              } else {
			                setValue = regionsArr;
			              }
			            } else if (
			              item.typeKey === 'imageUpload' ||
			              item.typeKey === 'smoothSignature'
			            ) {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    return {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      url: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                  })
			                : [];
			            } else if (item.typeKey === 'fileUpload') {
			              setValue = dataValue
			                ? JSON.parse(dataValue).map((d, i) => {
			                    let newObj = {
			                      key: d.id,
			                      name: d.originalFilename,
			                      contentType: d.contentType,
			                      fileUrl: BASE_URL + 'api/file/inline/' + d.id,
			                      status: 'done'
			                    };
			                    let mark = isImageFile(newObj);
			                    newObj.isImageFile = mark;
			                    if (mark) newObj.url = newObj.fileUrl;
			                    return newObj;
			                  })
			                : [];
			            } else if (
			              item.typeKey === 'multipleSelect' &&
			              props.currentGroup.enType !== 'conditionSet'
			            ) {
			              //多选值处理
			              setValue = dataValue.split(',');
			            } else {
			              setValue = dataValue;
			            }
			          }
			          if (setValue) updateFormValue(item, item.prop, setValue);
			        });
			      }
			    }
			  },
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	.uni-forms-item {
		margin-bottom: 0;
	}
</style>