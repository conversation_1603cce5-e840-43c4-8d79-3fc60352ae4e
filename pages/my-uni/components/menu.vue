<!-- 学生信息group -->
<template>
	<!-- <block> -->
	<view class="ui-BG-card" style="margin: 16px 8px;padding: 0;">
		<!-- style="margin-top: 84rpx; padding: 12px 0 0;" -->
		<!-- currentGroup:
		{{JSON.stringify(currentGroup)}} -->
		<view :class="'ui-grid '+ 'ui-cols-3'">
			<!-- <block v-for="(item,index) in listServices" :key="index"> -->
			<block v-for="(item,index) in dataList" :key="index">
				<!-- @tap.stop.prevent="redictAppDetail" -->
				<view class="ui-item" @tap.stop.prevent="showDrawer(item)">
					<view  class="image-box">
						<Icon-Park :theme="item.filled" :iconName="iconNameList[index % 5]" :fill="colorList[index % 5]" />
					</view>
					<!-- <view v-else class=" text-df" style="font-weight: bold;line-height: 32px;">
						{{ item.name }}
					</view> -->
					<text class="ui-TC-5 text-df" :class="item.type !== 'image' ? 'ui-TC':''">{{item.groupName}}</text>
					<view class="percent" v-if="item.percent">
						{{ item.percent }}80%
					</view>
				</view>
			</block>
		</view>
		<!-- <view class="charts-box" v-if="chartType">
		  <qiun-data-charts :type="chartType" :chartData="chartsDataColumn" :opts="opts" />
		</view> -->
		<view class="example-body">
			<uni-drawer ref="showRight" mode="right" :mask-click="false" @change="change($event,'showRight')">
				<view class="scroll-view">
					<scroll-view class="scroll-view-box" scroll-y="true" style="margin-bottom: 50px;">
						<!-- <view class="info">
							<text class="info-text">右侧遮罩只能通过按钮关闭，不能通过点击遮罩关闭</text>
						</view>
						<view class="close">
							<button @click="closeDrawer"><text class="word-btn-white">关闭Drawer</text></button>
						</view> -->
						<block v-if="currentGroup">
							<view class="text-center" style="padding: 12px 0;font-size: 18px;">
								{{currentGroup.groupName}}
							</view>
							<!-- 是列表 -->
							<TablePreview v-if="currentGroup.listFlag === '是'" 
							:currentGroup="currentGroup" :userType="userType" :userInfo="userInfo">
							</TablePreview>
							<!-- 是表单 -->
							<FormPreview v-else :currentGroup="currentGroup" :userType="userType" :userInfo="userInfo" />
							<!-- <FormRender v-else :currentGroup="currentGroup" :userType="userType" :userInfo="userInfo" /> -->
						</block>
						
						<!-- <view class="info-content" v-for="item in 30" :key="item">
							<text>可滚动内容 {{item}}</text>
						</view> -->
						<!-- <view class="close">
							<button  @click="closeDrawer"><text class="word-btn-white">关闭Drawer</text></button>
						</view> -->
						<view class="button-body box fixed">
							<button class="button" type="default" @click="closeDrawer">返回</button>
							<!-- <button class="button" type="primary" >退回</button> -->
							<!-- <button class="button" type="warn" @click="dialogToggle">不通过</button>
							<button class="button" type="primary" >通过</button> -->
						</view>
					</scroll-view>
				</view>
			</uni-drawer>
		</view>
	</view>
	<!-- </block> -->
</template>

<script>
	import FormPreview from "./form/form-preview.vue"
	import TablePreview from "./table-preview.vue"
	export default {
		components: {
			FormPreview,
			TablePreview,
		},
		props: {
			dataList: {
				type: Array,
				default: () => {
					return []
				}
			},
			userInfo: {
				type: Object,
				default() {
					return {}
				}
			},
			userType: {
				type: String,
				default: ''
			},
			// 当前选中的主题对应的系统设置的16进制颜色代码
			fillColor: {
				type: String,
				default: ''
			},
			serviceType:  {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: ''
			},
			titleIcon: {
				type: String,
				default: '',
				// cicon-titles
			},
			desc: {
				type: String,
				default: ''
			},
			more: {
				type: String,
				default: ''
			},
			marginTop: {
				type: Number,
				default: 20
			},
			chartType: {
				type: String,
				default: null
			},
			opts: {
				type: Object,
				default() {
					return {}
				}
			}
		},
		data(){
			return {
				listServices: [],
				chartsDataColumn: {},
				colorList: ['#3B82F6','#F97316','#22C55E','#EF4444','#A855F7'],
				iconNameList: ['address-book','chart-histogram-two','remind','people','id-card'],
				showRight: false,
				currentGroup: {}
				// @click.native="_to('/pages/theme/theme')"
			}
		},
		created(){
			console.log('this.serviceType', this.serviceType, this.marginTop, this.chartType);
		},
		computed: {
		},
		watch: {
		},
		methods: {
			// 打开窗口
			showDrawer(item) {
				console.log('打开', item)
				this.currentGroup = item;
				this.$refs.showRight.open();
			},
			// 关闭窗口
			closeDrawer(e) {
				this.$refs.showRight.close()
			},
			// 抽屉状态发生变化触发
			change(e, type) {
				console.log((type === 'showLeft' ? '左窗口' : '右窗口') + (e ? '打开' : '关闭'));
				this[type] = e
			},
			// 跳转至查看小程序列表页面或文章详情页
			redictAppDetail: function(e) {
				let {
					index,
					type,
					appid,
					url,
					path
				} = e.currentTarget.dataset;
				uni.navigateTo({
					url: '/pages/demo/list-page'
				});
				if (index === 0) {
					console.log(index)
					uni.navigateTo({
						url: '/pages/index/index'
					});
				} else if (index === 1) {
					uni.navigateTo({
						url: '/pages/topic/topic'
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: '/pages/hot/hot'
					});
				} else if (index === 3) {
					uni.navigateTo({
						url: '/pages/my/my'
					});
				}
			
				if (type === 'apppage') {
					// 小程序页面         
					uni.navigateTo({
						url: path
					});
				}
			
				if (type === 'webpage') {
					// web-view页面
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			
				if (type === 'miniapp') {
					// 其他小程序
					url = '../webpage/webpage?url=' + url;
					uni.navigateTo({
						url: url
					});
				}
			},
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG {
		background-color: none !important;
	}
	// /* 图标导航 */
	// ::v-deep .ui-BG-card {
	// 	// background: linear-gradient(to bottom, var(--ui-BG-Main-6) 0%, var(--ui-BG-Main-TC) 84rpx);
	// 	// box-shadow: none;
	// 	margin: 16px 8px;
	// 	padding: 16px;
	// 	box-shadow: none;
	// 	margin: 0;
	// 	border-radius: 0;
	// }
	.ui-cols-3 .ui-item {
		width: calc((100% - 48px) / 3);
	}
	.ui-item {
		display: block;
		position: relative;
		/* padding: 24rpx 0 20rpx; */
		text-align: center;
		background-color: #ffffff;
		margin: 8px;
		border-radius: 12px;
		padding: 28px 4px 16px;
		line-break: anywhere;
	}
	::v-deep .ui-item uni-text {
		line-height: 14px !important;
	}
	.image-box {
		margin-bottom: 10px;
	}
	.ui-item .i-icon {
		width: 24px;
		height: 24px;
		margin: auto;
		display: block;
		// padding: 16px;
		// background-color: #ffffff;
		// box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
		// border-radius: 12px;
	}
	.ui-item image, .ui-item svg path {
	  // fill: red; /* 改变SVG路径的颜色 */
	}
	
	.ui-item text {
		// color: #333;
		// font-weight: 300;
		color: var(--ui-TC-5) !important;
		font-family: "Inter";
		font-size: 14px !important;
		font-style: normal;
		font-weight: 400;
		line-height: 30px; /* 1.429 */
	}
	::v-deep .ui-title .action .action-title {
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
	.percent {
		// margin-top: 6px;
		color: #007AFF;
		font-family: "Inter";
		font-size: 12px;
		font-style: normal;
		font-weight: 500;
		position: absolute;
		top: 8px;
		right: 8px;
		// line-height: 20px; /* 1.429 */
	}
	// 抽屉相关样式
	.example-body {
		padding: 10px;
	}
	.scroll-view {
		/* #ifndef APP-NVUE */
		width: 100%;
		height: 100%;
		/* #endif */
		flex:1
	}
	// 处理抽屉内容滚动
	.scroll-view-box {
		flex: 1;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}
	.info {
		padding: 15px;
		color: #666;
	}
	
	.info-text {
		font-size: 14px;
		color: #666;
	}
	.info-content {
		padding: 5px 15px;
	}
	.close {
		padding: 10px;
	}
	::v-deep .uni-drawer__content {
	  width: 100vw !important;
	}
	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}
	
	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}
	.fixed {
		position: fixed;
		left: 0;
		bottom: 0;
	}
	.box {
		@include flex;
	}
	
	.button {
		@include flex;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 35px;
		margin: 0 5px;
		border-radius: 5px;
		font-size: 14px;
	}
	uni-button::after{
		border: none;
	}
	.button-body {
		width: 100%;
		background-color: #fff;
		padding: 6px 0;
	}

</style>