import momentWrap from "@/utils/common/momentWrap";

/**
 * moment
 */
export const moment = momentWrap.moment;

/**
 * 【2018-09-17|不限制  至  2018-09-18|不限制】/【不限制】
 * @param start
 * @param end
 * @returns {string}
 */
export function applicationApprovalDate(start, end) {
    let sFormat = momentWrap.dateFormat_YMD(start);
    let eFormat = momentWrap.dateFormat_YMD(end);
    if (!sFormat && !eFormat)
        return '不限制';
    let arr = [sFormat, '至', eFormat];
    arr.forEach((item, index, theArray) => {
        if (!item)
            theArray[index] = '不限制'
    });
    return arr.join(" ");
}

/**
 * 2018-09-06
 * @param date
 * @returns {*}
 */
export function dateFormat_YMD(date, orGetNow) {
    return momentWrap.dateFormat_YMD(date, orGetNow);
}

export function dateFormat_YMD_now() {
    return momentWrap.dateFormat_YMD(null, true);
}

/**
 * 2019-07-18 11:11:11
 * @param o
 * @returns {*|string}
 */
export function dateFormat_date(o) {
    return momentWrap.dateFormat_date(o);
}

/**
 * 2019-07-18 11:00
 * @param o
 * @returns {*}
 */
export function dateFormat_YHMHM(o) {
    return momentWrap.dateFormat_YHMHM(o);
}
