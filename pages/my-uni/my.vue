<template>
	<block>
		<ui-sys>
			<view class="info_box ui-BG-card ui-BG" style="margin: 0;border-radius: 0;">
				<view class="charts-box" style="width: 102px; margin: 0 auto;height: 120px;position: relative;">
				  <qiun-data-charts type="arcbar" :opts="{title:{name:'80%', color:'#0081ff',fontSize:16,position: 'bottom',offsetX: 0,offsetY: 50,},subtitle:{name:'',color:'#666666',fontSize:16},extra: {arcbar: {width: 4}}}" :chartData="totalPercentData"/>
				  <view class="info_avatar">
				  	<image class="avatar" @click.stop="imagePreview" v-if="userInfo && userInfo.photo" mode="aspectFill" :src="'/api/file/inline/' + JSON.parse(userInfo.photo)[0].id" />
				  	<image class="avatar" @click.stop="imagePreview" v-else-if="userInfo && userInfo.xb && userInfo.xb === '女'" mode="aspectFill" src="../../static/images/icon-female.svg" />
				  	<image class="avatar" @click.stop="imagePreview" v-else mode="aspectFill" src="../../static/images/icon-male.svg" />
				  </view>
				</view>
				<view class="info_user">
					<view class="info_name">{{userInfo.xm}} </view>
					<view class="info_lv">{{userInfo.xgh}}</view>
					<view class="info_lv">{{userInfo.xymc}}</view>
				</view>
			</view>
			<!-- <view class="info_fdy_box  ui-BG-card ui-BG">
				<view class="">
					<span class="complete">
						总体完成度
					</span>
					<span class="complete-percent">
						80%
					</span>
				</view>
				<view class="progress-box" style="margin-top: 8px;">
					<progress :percent="20" stroke-width="6" />
				</view>
			</view> -->
			<!-- 辅导员查询学生无需显示辅导员信息 -->
			<view class="info_fdy_box  ui-BG-card ui-BG" v-if="userType == 'student' && roleName !== '辅导员'">
				<view class="info_fdy">
					<view class="info_title">辅导员信息</view>
					<view class="info_item">辅导员姓名：{{userInfo.fdyxm}} </view>
					<text class="info_item">辅导员电话：{{userInfo.fdygh}}</text>
				</view>
			</view>
			<!-- 服务列表 -->
			<Menu style="margin-top: 16px;" :userType="userType" :dataList="dataList" :userInfo="userInfo"></Menu>
			<!-- <view class="ui-BG-card ui-BG">
				<ui-menu ui="radius">
					<ui-menu-item-new v-for="(item, index) in dataList" arrow :title="item.groupName"> -->
						<!-- @click.native="_to('/pages/about/about')" -->
						<!-- <icon-info slot="leftIcon" :size="20" :strokeWidth="3" :fill="sysColor" />
						<icon-right slot="rightIcon" :size="16" :strokeWidth="3" :fill="['#6B7280']" />
					</ui-menu-item-new> -->
					<!-- <ui-menu-item-new arrow title="关于我们"> -->
						<!-- @click.native="_to('/pages/about/about')" -->
						<!-- <icon-info slot="leftIcon" :size="20" :strokeWidth="3" :fill="sysColor" />
						<icon-right slot="rightIcon" :size="16" :strokeWidth="3" :fill="['#6B7280']" />
					</ui-menu-item-new> -->
					<!-- #ifndef MP-BAIDU -->
					<!-- <ui-menu-item-new arrow title="帮助中心"> -->
						<!-- @click.native="tapCopy" -->
						<!-- <text slot="action" class="text-sm ui-TC-3 m-1">{{wechat}}</text> -->
						<!-- <icon-help slot="leftIcon" :size="20" :strokeWidth="3" :fill="sysColor" />
						<icon-right slot="rightIcon" :size="16" :strokeWidth="3" :fill="['#6B7280']" />
					</ui-menu-item-new> -->
					<!-- #endif -->

					<!-- <view style="position: relative;">
						<ui-menu-item-new arrow title="在线客服" icon="cicon-service" data-url="/pages/about/about"
							@tap.native="tapToUrl" /> <button class="ui-btn kefubtn  " open-type="contact"></button>
					</view> -->
					<!-- #ifndef MP-BAIDU -->
					<!-- <view style="position: relative;">
						<ui-menu-item-new arrow title="意见反馈" icon="cicon-creative" /> <button class="ui-btn kefubtn  "
							open-type="feedback"></button>
					</view> -->
					<!-- #endif -->

					<!-- <ui-menu-item-new arrow title="定制主题" icon="cicon-clothes" @click.native="_to('/pages/theme/theme')">
						<icon-platte slot="leftIcon" :size="20" :strokeWidth="3" :fill="sysColor" />
						<icon-right slot="rightIcon" :size="16" :strokeWidth="3" :fill="['#6B7280']" />
					</ui-menu-item-new>
				</ui-menu>
			</view> -->

			<!-- 版权信息 -->
			<!-- <view class="copyright"> -->
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<!-- <block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view> -->
			<!-- 普通弹窗 -->
			<uni-popup ref="popup" background-color="#fff" @change="change">
				<view class="popup-content">
					<image v-if="userInfo&&userInfo.photo" class="avatar" mode="aspectFill" :src="'/api/file/inline/' + JSON.parse(userInfo.photo)[0].id" />
					<image v-else-if="userInfo.xb&&userInfo.xb =='女'" class="avatar" mode="aspectFill" src="@/static/images/icon-female.svg" />
					<image v-else-if="userInfo.xb&&userInfo.xb =='男'" class="avatar" mode="aspectFill" src="@/static/images/icon-male.svg" />
				</view>
			</uni-popup>
			<HoverBall></HoverBall>
		</ui-sys>
	</block>
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import Menu from "./components/menu.vue";
	import config from '../../utils/config.js';
	import store from '@/ui/store'

	var Api = require('../../utils/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		components: {
			Menu,
		},
		data() {
			return {
				roleName: store.getters.getRoleName,
				colorList: ['#3B82F6','#F97316','#22C55E','#EF4444','#A855F7'],
				iconNameList: ['address-book','chart-histogram-two','remind','people','id-card'],
				setting: [{groupName: '定制主题'}],
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				userInfo: {
					xm: '',
					xgh: ''
				},
				sysColor: [],
				dataList: [],
				
				totalPercentData: {},
				userType: null,
				userAccount: null,
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 * options是uni.navigateTo中url传递的参数
		 * options.userId 用户学号
		 * options.userType 用户类型： student/teacher/self
		 */
		onLoad: function(options) {
			var self = this;
			console.log('options', options);
			console.log('store.state', this.roleName);
			console.log(this.wechat)
			// 从学生/教师查询跳转过来的
			if(options.userId && options.userType) {
				self.setData({
					userType: options.userType,
					enterType: options.enterType,
					userAccount: options.userId
				})
			} else { //点击悬浮框中我的，使用当前登录账号和角色
				self.setData({
					userType: 'self'
				})
			}
			this.getUserInfo();
		},
		onReady: function() {
			var self = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			this.sysColor = [util.getCurrentThemeColor()];
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {},
		methods: {
			change(e) {
				console.log('弹出层状态改变了', e);
			},
			imagePreview(item){
				this.$refs.popup.open();
			},
			//获取个人信息
			getUserInfo(obj) {
			  var self = this;
			  // uni.showLoading({
			  //   title: '查询中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.getPersonInfo(); // (this.userAccount ? this.userAccount : '' )
			  if(this.userType == 'student' || this.userType == 'teacher') {
			  	url = url + '/' + this.userType  + '/' + this.userAccount;
			  }
			  var getPersonInfo = wxRequest.getRequest(url, data);
			  getPersonInfo.then(response => {
				console.log('getPersonInfo >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						self.setData({
							userInfo: resData.data || {},
							userType: resData.data && resData.data.userType
						}, ()=> {
							console.log('111',self.userInfo);
							self.getFieldGroupList();
							setTimeout(() => {
								self.totalPercentData = {
									"series": [{
										"name": "完成度",
										"data": 0.8,
										"color": "#2fc25b"
									}]
								}
							})
						})
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  // self.fetchCodeImgData();
			    }, 1000);
			  });
			},
			//获取个人信息子菜单
			getFieldGroupList(obj) {
			  var self = this;
			  // uni.showLoading({
			  //   title: '查询中',
			  //   mask: true
			  // });
			  let data = {}
			  let url = Api.getTFieldGroupList();
			  if(this.userType == 'student') {
				  url = Api.getSFieldGroupList();
			  }
			  var getFieldGroupListRequest = wxRequest.getRequest(url, data);
			  getFieldGroupListRequest.then(response => {
				console.log('getFieldGroupListRequest >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						// let list = resData.data.concat(this.setting); // 目前未配置主题设置，这里整合进去主题配置
						let list = resData.data; // 目前未配置主题设置，这里整合进去主题配置
						list.forEach(i => {
							i.enType = self.enterType
						})
						self.setData({
							dataList: list
						})
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('查询失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  // self.fetchCodeImgData();
			    }, 1000);
			  });
			},

			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},

			tapCopy(e) {

				uni.setClipboardData({
					data: this.wechat,

					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},



				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '111',
					"dialog.content": '2222'
				});
			}
		}
	};
</script>
<style lang="scss">
	/* pages/my/my.wxss */
	/* 用户信息 */
	::v-deep .ui-BG-card {
		margin: 16px;
		padding: 16px;
		overflow: hidden;
	}
	/* ::v-deep .ui-BG-card:nth-child(3) {
		margin: 0px;
		border-radius: 0;
		padding: 0;
	} */
	.ui-BG-Main {
		border-radius: 0 0 var(--radius) var(--radius);
	}
	.userinfo {
		height: 200rpx;
		position: relative;
		z-index: -1;
	}

	.scan-free {
		color: var(--ui-TC) !important;
	}

	.scan-free::after {
		border: solid var(--ui-TC) !important;
	}

	.kefubtn {
		position: absolute;
		top: 0;
		left: 0;
		min-height: 4em;
		width: 100%;
		opacity: 0;
	}

	/* 用户 */
	.info_box {
		/* margin-top: -120rpx; */
		display: flex;
		align-items: center;
		flex-direction: column;
	}
	/* 用户 */
	.info_fdy_box {
		/* margin-top: -120rpx; */
		display: flex;
		/* align-items: center; */
		flex-direction: column;
	}
	.info_scan {
		float: right;
	}

	.info_right {
		float: right;
		min-width: 80rpx;
		text-align: center;
		display: block;

	}

	.info_right .exit {
		margin-left: 10rpx;

	}

	.info_avatar {
		/* margin-top: -80rpx; */
		/* height: 100rpx;
		width: 100rpx;
		overflow: hidden;
		position: relative;
		border-radius: 50%; */
		/* width: 64px; */
		margin-bottom: 4px;
		
	}

	.info_avatar image {
		width: 72px;
		height: 72px;
		border-radius: 50%;
		position: absolute;
		top: 23px;
		left: 15px;
		// margin: 0 auto;
		/* background-color: #f7f7f7; */
	}
	

	.info_user {
		text-align: center;
		/* flex: 1; */
		/* display: block;
		margin: 24rpx 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap; */
		/* overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap; */
	}

	.info_name {
		/* display: inline-block;
		height: 45rpx;
		line-height: 45rpx;
		margin-right: 20rpx;
		color: #333333;
		font-size: 18px; */
		color: var(--ui-TC-7) !important;
		font-family: "Inter";
		font-size: 18px;
		font-style: normal;
		font-weight: 400;
		line-height: 24px; /* 1.556 */
		/* height: 33px; */
		width: 100%;
		/* padding-left: 16px;
		overflow: hidden;
		  text-overflow: ellipsis;
		  white-space: nowrap; */
	}

	.info_lv {
		color: var(--ui-TC-6) !important;
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 20px; /* 1.429 */
		// height: 29px;
		/* padding-left: 16px; */
		/* margin-top: 8rpx;

		font-size: 12px;
		height: 12px;
		line-height: 12px;
		padding: 5rpx 8rpx;
		overflow: hidden; */
	}

	.info_uid {
		display: block;
		height: 22px;
		line-height: 22px;
		font-size: 12px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;

		color: #999999 !important;


		font-weight: 300;
	}
	
	.info_fdy .info_title {
		color: var(--ui-TC-7);
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 1.5 */
	}
	.info_fdy .info_item {
		color: var(--ui-TC-6);
		font-family: "Inter";
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 24px; /* 1.5 */
	}
	
	.ui-item {
		display: block;
		padding: 24rpx 0 20rpx;
		text-align: center;
	}

	.ui-menu-item-new {
		padding: 0;
	}
	.ui-menu-item-new:last-child {
		border-bottom: none;
	}

	.ui-title {

		padding-left: 10rpx;
	}

	.cicon-service-fill {
		color: #F37D7D;
	}

	.cicon-weixin {
		font-size: 15px;
		color: #FFB300;
	}

	.cicon-service {
		color: #9DCA08;
	}

	.cicon-creative {
		color: #91B9EB;
	}

	.cicon-clothes {
		font-size: 13px;
		color: #FFB300;
	}
	.complete {
	  color: var(--ui-TC-7);
	  font-family: "Inter";
	  font-size: 16px;
	  font-style: normal;
	  font-weight: 500;
	  line-height: 24px;
	}
	.complete-percent {
	  float: right;
	  color: var(--ui-BG-Main);
	  font-family: "Inter";
	  font-size: 16px;
	  font-style: normal;
	  font-weight: 500;
	  line-height: 24px;
	}
	// 图片预览
	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}
	
	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}
	.popup-content {
		@include flex;
		align-items: center;
		justify-content: center;
		// padding: 15px;
		width: 100vw;
		height: 100vw;
		background-color: #fff;
	}
	.popup-content image {
		width: 100vw;
		height: 100vw;
	}
	
	.popup-height {
		@include height;
		width: 200px;
	}
</style>
