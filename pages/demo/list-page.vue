<template>
		<!-- tabbar -->
		<ui-sys>
			<uni-search-bar radius="100" @confirm="search" :focus="false" v-model="searchValue" @blur="blur" @focus="focus" @input="input"
				placeholder="请输入项目名称"	@cancel="cancel" @clear="clear" cancelButton="none" clearButton="none" >
			</uni-search-bar>
			<BaseList ref="list" style="margin-top: -10px;">
				<template v-slot:default="slotProps">
					<!-- title="基础卡片" sub-title="副标题" extra="额外信息" :thumbnail="avatar"-->
					<uni-card class="detailbox" :isFull="item.isFull" :padding="'10px 2px'" :status="item.status" v-for="(item, index) in slotProps.list" :key="item.id">
						<!-- <template v-slot:title>
							<uni-list>
								<uni-list-item :show-switch="true" title="自定义标题"/>
							</uni-list>
						</template> -->
						<!-- <image v-if="index == 3" style="width: 100%; height: 140px;margin-top: 12px;" :src="require('@/static/home-icon/banner.png')"></image> -->
						<image v-if="item.banner" style="width: 100%; height: 140px;margin-top: 12px;" :src="item.banner"></image>
						<!-- <text class="uni-body uni-mt-5">卡片组件通用来显示完整独立的一段信息，同时让用户理解他的作用。例如一篇文章的预览图、作者信息、时间等，卡片通常是更复杂和更详细信息的入口点。</text> -->
						<view @click="handleDetail(item)">
							<view class="card-line-title">
								{{item.name}}
							</view>
							<view class="card-line">
								<view class="card-line-label">创建人: </view>
								<view class="card-line-content">
									{{item.creator}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">创建时间: </view>
								<view class="card-line-content">
									{{item.createDate}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">签到时间: </view>
								<view class="card-line-content">
									{{item.startDate}} 至 {{item.endDate}}
								</view>
							</view>
							<view class="card-line">
								<view class="card-line-label">签到进度:</view>
								<view class="card-line-content">
									<progress :percent="item.progress" show-info stroke-width="6" />
								</view>
							</view>
						</view>
						<view slot="actions" class="card-actions">
							<view class="card-actions-item" @click="actionsClick('签到', item)">
								<!-- <uni-icons type="pengyouquan" size="18" color="#999"></uni-icons> -->
								<text class="card-actions-item-text">签到</text>
							</view>
							<view class="card-actions-item" @click="actionsClick('新增', item)">
								<!-- <uni-icons type="heart" size="18" color="#999"></uni-icons> -->
								<text class="card-actions-item-text">编辑</text>
							</view>
							<view class="card-actions-item" @click="actionsClick('审核', item)">
								<!-- <uni-icons type="heart" size="18" color="#999"></uni-icons> -->
								<text class="card-actions-item-text">审核</text>
							</view>
						</view>
					</uni-card>
				</template>
			</BaseList>
			<!-- 无法访问网络 -->
			<view class="showerror" :style="'display:'+(showerror)">
				<text class="cicon-wifi-off text-xsl"></text>
				<view class="errortext">
					暂时无法访问网络，下拉重试...
				</view>
			</view>
			<!-- 版权信息 -->
			<view class="copyright">
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view>
			<HoverBall></HoverBall>
		</ui-sys>
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import config from '../../utils/config.js';

	var Api = require('../../utils/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	import BaseList from "./components/BaseList.vue"
	export default {
		components: {
			BaseList
		},
		data() {
			return {
				items: ['选项卡1', '选项卡2'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: require('@/static/home-icon/banner.png'),
				searchValue: null,
				list: [{
					name: "浏览",
					icon: "cicon-eye",
					color: "#9DCA06",
					path: "/pages/readlog/readlog?key=1"
				}, {
					name: "评论",
					icon: "cicon-popover",
					color: "#FFB300",
					path: "/pages/readlog/readlog?key=2"
				}, {
					name: "点赞",
					icon: "cicon-favorite",
					color: "#53bcf5",
					path: "/pages/readlog/readlog?key=3"
				}, {
					name: "订阅",
					color: "#F37D7D",
					icon: "cicon-notice-active",
					path: "/pages/readlog/readlog?key=5"
				}],

				selected: 0,

				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				isLoading: false,
				moreStatus: "more"
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			console.log(this.wechat)
		},
		onReady: function() {
			var that = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			if (typeof this.getTabBar === 'function' && this.getTabBar()) {
				this.getTabBar().setData({
					selected: 3
				});
			}
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
			this.$refs.list.reload();
			// uni.startPullDownRefresh();//主动触发下拉刷新
			// setTimeout(()=>{
			// 	uni.stopPullDownRefresh(); // 请求数据，成功获取数据则停止下拉刷新
			// }, 2000)
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
			this.$refs.list.loadMore();
		},
		methods: {
			input(res) {
				console.log('----input:', res)
			},
			clear(res) {
				// uni.showToast({
				// 	title: 'clear事件，清除值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			blur(res) {
				// uni.showToast({
				// 	title: 'blur事件，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			focus(e) {
				// uni.showToast({
				// 	title: 'focus事件，输出值为：' + e.value,
				// 	icon: 'none'
				// })
			},
			cancel(res) {
				// uni.showToast({
				// 	title: '点击取消，输入值为：' + res.value,
				// 	icon: 'none'
				// })
			},
			actionsClick(text, item){
				// uni.showToast({
				// 	title:text,
				// 	icon:'none'
				// })
				console.log('点击了签到', item)
				if(text =='签到') {
					uni.navigateTo({
						url: '/pages/demo/qd-page?id=' + item.id
					});
				} else if(text =='新增') {
					uni.navigateTo({
						url: '/pages/demo/add-page'
					});
				} else if(text =='审核') {
					uni.navigateTo({
						url: '/pages/demo/detail-page'
					});
				}
			},
			handleDetail(item){
				console.log('点击了详情', item)
				uni.navigateTo({
					url: '/pages/demo/detail-page?id=' + item.id
				});
			},
			tapToUrl(e) {
				uni.navigateTo({
					url: e.currentTarget.dataset.url
				});
			},
			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			},

			confirm: function() {
				this.setData({
					"dialog.hidden": true,
					"dialog.title": '',
					"dialog.content": ''
				});
			}
		}
	};
</script>
<style lang="scss">
	// .container {
	// 	overflow: hidden;
	// }

	// .custom-cover {
	// 	flex: 1;
	// 	flex-direction: row;
	// 	position: relative;
	// }

	// .cover-content {
	// 	position: absolute;
	// 	bottom: 0;
	// 	left: 0;
	// 	right: 0;
	// 	height: 40px;
	// 	background-color: rgba($color: #000000, $alpha: 0.4);
	// 	display: flex;
	// 	flex-direction: row;
	// 	align-items: center;
	// 	padding-left: 15px;
	// 	font-size: 14px;
	// 	color: #fff;
	// }
	.uni-padding-wrap {
		// width: 750rpx;
		// padding: 0px 30px;
	}
	::v-deep .uni-progress-info {
		font-size: 14px;
	}
	.card-line-title {
		font-weight: bold;
	}
	.card-line {
		display: flex;
		flex-direction: row;
	}
	.card-line-label {
		width: 70px;
		font-size: 13px;
	}
	.card-line-content {
		font-size: 13px;
		flex-grow: 1;
	}
	.card-actions {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 36px;
		border-top: 1px #eee solid;
	}
	.card-actions-item {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.card-actions-item-text {
		font-size: 12px;
		color: #00a5ec;
		margin-left: 5px;
	}
	.cover-image {
		flex: 1;
		height: 150px;
	}
	.no-border {
		border-width: 0;
	}
</style>