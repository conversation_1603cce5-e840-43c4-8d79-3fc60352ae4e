<template>
	<ui-sys :loading="isloading">
		<uni-section title="表单常用组件" type="line" style="margin-bottom: 55px;">
			<view class="example">
				<uni-title type="h1" title="h1 一级标题"></uni-title>
				<uni-title type="h2" title="h2 二级标题" align="center" color="green"></uni-title>
				<uni-title type="h3" title="h3 三级标题" align="right"></uni-title>
				<view class="segmented-control">
					<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="button">
					</uni-segmented-control>
				</view>
				<!-- 基础表单校验 -->
				<uni-forms ref="valiForm" :rules="rules" :modelValue="valiFormData" :label-position="alignment">
					<uni-forms-item label="姓名" required name="name">
						<uni-easyinput v-model="valiFormData.name" ref="name" :focus="focus" placeholder="请输入姓名" />
					</uni-forms-item>
					<uni-forms-item label="年龄" required name="age">
						<uni-easyinput v-model="valiFormData.age" ref="age" type="number" placeholder="请输入年龄" />
					</uni-forms-item>
					<uni-forms-item label="密码" name="password">
						<uni-easyinput v-model="valiFormData.password" placeholder-style="color:#F76260" type="password" placeholder="请输入密码" />
					</uni-forms-item>
					<uni-forms-item label="性别" name="sex">
						<uni-data-select
							v-model="valiFormData.sex"
							:localdata="range"
							@change="onSexChange"
							label="性别"
						  ></uni-data-select>
					</uni-forms-item>
					<uni-forms-item label="数字输入">
						<uni-number-box :value="valiFormData.numberValue" :step="0.1" :min="0" :max="2" />
					</uni-forms-item>
					<uni-forms-item label="滑动选择">
						<slider :value="valiFormData.slideValue" @change="onSliderChange" show-value />
					</uni-forms-item>
					<uni-forms-item label="班级级联" name="classes">
						<uni-data-picker placeholder="请选择班级" popup-title="请选择班级" :localdata="dataTree" v-model="valiFormData.classes"
										@change="onClassesChange" @nodeclick="onnodeclick" @popupopened="onclassespopupopened" @popupclosed="onclassespopupclosed">
									</uni-data-picker>
					</uni-forms-item>
					<uni-forms-item label="下拉支持搜索" name="city" label-width="120">
						<uni-combox :candidates="cityList" emptyTips="没有搜到的提示语" placeholder="请选择所在城市" v-model="valiFormData.city"></uni-combox>
					</uni-forms-item>
					<uni-forms-item label="单选" name="radio1">
						<uni-data-checkbox v-model="valiFormData.radio1" :localdata="sexList" @change="onSingleChange"></uni-data-checkbox>
					</uni-forms-item>
					<uni-forms-item label="多选" name="checkbox1">
						<uni-data-checkbox multiple v-model="valiFormData.checkbox1" :localdata="hobby"></uni-data-checkbox>
					</uni-forms-item>
					<uni-forms-item label="时间" name="datetimesingle">
						<uni-datetime-picker type="datetime" v-model="valiFormData.datetimesingle" @change="ontimechange" />
					</uni-forms-item>
					<uni-forms-item label="日期" name="time2">
						<uni-datetime-picker type="date" :clear-icon="false" v-model="valiFormData.time2" @maskClick="maskClick" />
					</uni-forms-item>
					<uni-forms-item label="自我介绍" name="introduction">
						<uv-textarea v-model="valiFormData.introduction" placeholder="请输入内容" count :maxlength="200"></uv-textarea>
						<!-- <uni-easyinput type="textarea" maxlength="10" v-model="valiFormData.introduction" placeholder="请输入自我介绍" /> -->
					</uni-forms-item>
					<uni-forms-item label="自动变高" name="introduction">
						<uv-textarea v-model="valiFormData.introduction" placeholder="请输入内容" autoHeight></uv-textarea>
						<!-- <uni-easyinput type="textarea" autoHeight v-model="valiFormData.introduction" placeholder="请输入自动变高" /> -->
					</uni-forms-item>
					<uni-forms-item label="滑动评分半星" name="stars" label-width="120">
						<uni-rate :value="valiFormData.stars" allow-half color="#bbb" active-color="red" :is-fill="false" @change="onRateChange"/>
					</uni-forms-item>
					<uni-forms-item label="点击评分整星" name="stars" label-width="120">
						<uni-rate :value="valiFormData.stars" :allow-half="false" :touchable="false" color="#bbb" :size="16" :max="10" />
					</uni-forms-item>
					<uni-forms-item label="倒计时">
						<uni-countdown :day="1" :hour="1" :minute="12" :second="40" />
					</uni-forms-item>
					<uni-forms-item label="不显示天小时" :label-width="100">
						<uni-countdown :show-day="false" :show-hour="false" :show-colon="true" :hour="12" :minute="12" :second="12" />
					</uni-forms-item>
					<uni-forms-item label="附件" name="imageValue">
						<uni-file-picker 
							v-model="imageValue" 
							fileMediatype="image"
							mode="grid" 
							@select="select" 
							@progress="progress" 
							@success="success" 
							@fail="fail" 
						/>
					</uni-forms-item>
				</uni-forms>
				<view class="button-body box fixed">
					<button class="button" type="plain" @click="handleBack">返回</button>
					<button class="button" type="primary" @click="submit('valiForm')">提交</button>
				</view>
			</view>
		</uni-section>
	</ui-sys>
</template>

<script>
	export default {
		data(){
			return {
				focus: false,
				isloading: false,
				current: 0,
				imageValue: [],
				// 对齐方式
				items: ['顶部对齐','左对齐'],
				// 校验表单数据
				valiFormData: {
					name: '',
					age: '',
					password: '',
					introduction: '',
					numberValue: 1,
					sex: 0,
					classes: '1-2',
					radio1: 0,
					checkbox1: [],
					datetimesingle: '',
					city: '',
					stars: 3,
					slideValue: '',
				},
				imageValue: [],
				range: [
				  { value: 0, text: "男" },
				  { value: 1, text: "女" },
				],
				// 校验规则
				rules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
							required: true,
							errorMessage: '年龄不能为空'
						}, {
							format: 'number',
							errorMessage: '年龄只能输入数字'
						}]
					}
				},
				dataTree: [{
					text: "一年级",
					value: "1-0",
					children: [{
						text: "1.1班",
						value: "1-1"
					},
					{
						text: "1.2班",
						value: "1-2"
					},
					{
						text: "1.3班",
						value: "1-3"
					},
					{
						text: "1.4班",
						value: "1-4"
					},
					{
						text: "1.5班",
						value: "1-5"
					},
					{
						text: "1.6班",
						value: "1-6"
					},
					{
						text: "1.7班",
						value: "1-7"
					},
					{
						text: "1.8班",
						value: "1-8"
					},
					{
						text: "1.9班",
						value: "1-9"
					},
					{
						text: "1.10班",
						value: "1-10"
					}]
				},
				{
					text: "二年级",
					value: "2-0",
					children: [{
						text: "2.1班",
						value: "2-1"
					},
					{
						text: "2.2班",
						value: "2-2"
					}]
				},
				{
					text: "三年级",
					value: "3-0",
					disable: true
				},
				{
					text: "四年级",
					value: "4-0",
				}],
				sexList: [{
					text: '男',
					value: 0
				}, {
					text: '女',
					value: 1
				}, {
					text: '未知',
					value: 2
				}],
				hobby: [{
					text: '足球',
					value: 0
				}, {
					text: '篮球',
					value: 1
				}, {
					text: '游泳',
					value: 2
				}],
				cityList: ['北京', '南京', '东京', '武汉', '天津', '上海', '海口'],
				listStyles:{
					// 是否显示边框
					border: true,
					// 是否显示分隔线
					dividline: true,
					// 线条样式
					borderStyle: {
						width:1,
						color:'blue',
						radius:2
					}
				}
			}
		},
		computed: {
			// 处理表单排列切换
			alignment() {
				if (this.current === 0) return 'top'
				if (this.current === 1) return 'left'
				return 'left'
			}
		},
		onLoad() {},
		onReady() {
			// 设置自定义表单校验规则，必须在节点渲染完毕后执行
			// this.$refs.valiForm.setRules(this.rules)
		},
		methods: {
			// 获取上传状态
			select(e){
				console.log('选择文件：',e)
			},
			// 获取上传进度
			progress(e){
				console.log('上传进度：',e)
			},
			
			// 上传成功
			success(e){
				console.log('上传成功')
			},
			
			// 上传失败
			fail(e){
				console.log('上传失败：',e)
			},
			onClickItem(e) {
				console.log(e);
				this.current = e.currentIndex
			},
			change(value) {
				this.valiFormData.numberValue = value
			},
			onSexChange(e) {
				console.log("e:", e);
			},
			onSliderChange(e) {
				console.log('value 发生变化：' + e.detail.value)
			},
			onnodeclick(e) {
				console.log('onnodeclick', e);
			},
			onclassespopupopened(e) {
				console.log('classespopupopened');
			},
			onclassespopupclosed(e) {
				console.log('popupclosed', this.valiFormData);
			},
			onClassesChange(e) {
				console.log('onClassesChange:', e);
			},
			onSingleChange(e) {
				console.log("e:", e);
			},
			ontimechange(e) {
				console.log('ontimechange事件:', e);
			},
			maskClick(e){
				console.log('maskClick事件:', e);
			},
			onRateChange(e) {
				console.log('rate发生改变:',e, e.value)
			},
			submit(ref) {
				console.log('this.valiFormData', this.valiFormData)
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					uni.showToast({
						title: `校验通过`
					})
				}).catch(err => {
					console.log('err', err);
					this.focus = true;
					let message = err.map(item => item.errorMessage).join(',');
					uni.showToast({
						title: `${message}`
					})
				})
			},
			handleBack(){
				uni.navigateBack({
				  delta: 1
				});
			}
		}
	}
</script>

<style lang="scss">
	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}
	.fixed {
		position: fixed;
		left: 0;
		bottom: 0;
	}
	.box {
		@include flex;
	}

	.button {
		@include flex;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 35px;
		margin: 0 5px;
		border-radius: 5px;
		font-size: 14px;
	}
	uni-button::after{
		border: none;
	}
	.button-body {
		width: 100%;
		background-color: #fff;
		padding: 6px 0;
	}
	.example {
		padding: 0 24rpx;
	}
	.segmented-control {
		margin-bottom: 30rpx;
	}
</style>