<template>
		<!-- tabbar -->
		<ui-sys>
			<view class="uni-padding-wrap uni-common-mt">
				<uni-segmented-control :current="current" :values="items" :style-type="styleType" style="height: 40px;"
					:active-color="activeColor" @clickItem="onClickItem" />
			</view>
			<view class="page-section page-section-gap" v-if="current == 0">
				<map style="width: 100%; height: 220px;" :latitude="latitude" :longitude="longitude" :markers="covers">
				</map>
				<uni-card class="detailbox" :isFull="false" :padding="'10px 2px'"  :status="'进行中'">
					<!-- <template v-slot:title>
						<uni-list>
							<uni-list-item :show-switch="true" title="自定义标题"/>
						</uni-list>
					</template> -->
					<!-- <image style="width: 100%;" :src="cover"></image> -->
					<!-- <text class="uni-body uni-mt-5">卡片组件通用来显示完整独立的一段信息，同时让用户理解他的作用。例如一篇文章的预览图、作者信息、时间等，卡片通常是更复杂和更详细信息的入口点。</text> -->
					<view>
						<view class="card-line-title">
							活动签到
						</view>
						<view class="card-line">
							<view class="card-line-label">签到时段: </view>
							<view class="card-line-content">
								2024-05-11 10:00 - 17:00
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到地点: </view>
							<view class="card-line-content">
								科教楼报告厅
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到次数: </view>
							<view class="card-line-content">
								1/1次  （已签到数/应签到数）
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到说明: </view>
							<view class="card-line-content">
								该活动需准时签到
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-link" @click="dialogToggle">定位不准如何解决？</view>
						</view>
						<view class="card-line">
							<view class="card-line-link">点我重新定位</view>
						</view>
						<view class="sign-box">
							<view class="sign-button">
								<view class="">
									定位签到
								</view>
								<view class="">
									10:08
								</view>
							</view>
						</view>
					</view>
				</uni-card>
				
			</view>
			<view class="page-section page-section-gap" v-else-if="current == 1" style="margin-top: -10px;">
				<uni-card class="detailbox" :isFull="false" :padding="'10px 2px'">
					<!-- <template v-slot:title>
						<uni-list>
							<uni-list-item :show-switch="true" title="自定义标题"/>
						</uni-list>
					</template> -->
					<!-- <image style="width: 100%;" :src="cover"></image> -->
					<!-- <text class="uni-body uni-mt-5">卡片组件通用来显示完整独立的一段信息，同时让用户理解他的作用。例如一篇文章的预览图、作者信息、时间等，卡片通常是更复杂和更详细信息的入口点。</text> -->
					<view>
						<view class="card-line-title">
							活动签到
						</view>
						<view class="card-line">
							<view class="card-line-label">签到时段: </view>
							<view class="card-line-content">
								2024-05-11 10:00 - 17:00
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到地点: </view>
							<view class="card-line-content">
								科教楼报告厅
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到次数: </view>
							<view class="card-line-content">
								1/1次  （已签到数/应签到数）
							</view>
						</view>
						<view class="card-line">
							<view class="card-line-label">签到说明: </view>
							<view class="card-line-content">
								该活动需准时签到
							</view>
						</view>
						<uni-steps style="padding: 10px 0 20px;" :options="list2" active-color="#007AFF" :active="list2.length - 1" direction="column" :showWords="['time', 'address','remark']" />
					</view>
				</uni-card>
			</view>	
			<!-- 版权信息 -->
			<view class="copyright">
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view>
			<uni-popup ref="dialog" type="dialog">
				<uni-popup-dialog ref="inputClose" title="如何提高定位精准度"
				@confirm="dialogInputConfirm">
					<text>1.定位签到依赖于手机系统的定位模块，签到前请检查确保您手机的GPS服务已经启用 
					<br />
					2.必须打开WIFI增强手机定位准确度，可以不连接WIFI网络 </text>
				<!-- <view class="" style="width: 100%;">
					
				</view>	
				<view class="">
					2.必须打开WIFI增强手机定位准确度，可以不连接WIFI网络 
				</view>	
				<view class="text-box" scroll-y="true">
					
				</view> -->
				</uni-popup-dialog>
			</uni-popup>
			<HoverBall></HoverBall>
		</ui-sys>
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import config from '../../utils/config.js';

	var Api = require('../../utils/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		components: {
		},
		data() {
			return {
				items: ['签到详情', '签到记录'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				selected: 0,
				showerror: "none",
				moreStatus: "more",
				id:0, // 使用 marker点击事件 需要填写id
				title: 'map',
				latitude: 39.909,
				longitude: 116.39742,
				covers: [{
					latitude: 39.909,
					longitude: 116.39742,
					iconPath: '../../../static/location.png'
				}, {
					latitude: 39.90,
					longitude: 116.39,
					iconPath: '../../../static/location.png'
				}],
				list2: [{
					time: '签到时间：2024-05-11 10：30',
					address: '教学楼A',
					remark: '来晚了'
				}, {
					time: '签到时间：2024-05-11 11：30',
					address: '教学楼A',
					// remark: '来晚了'
				}, {
					time: '签到时间：2024-05-11 12：30',
					address: '教学楼A',
					// remark: '来晚了'
				}, {
					time: '签到时间：2024-05-11 13：30',
					address: '教学楼A',
					remark: '下课了'
				}, {
					time: '签到时间：2024-05-11 13：30',
					address: '教学楼A',
					remark: '下课了'
				}, {
					time: '签到时间：2024-05-11 13：30',
					address: '教学楼A',
					remark: '下课了'
				}, {
					time: '签到时间：2024-05-11 13：30',
					address: '教学楼A',
					remark: '下课了'
				}]
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			console.log(this.wechat)
			console.log("options", options)
		},
		onReady: function() {
			var that = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
			if (typeof this.getTabBar === 'function' && this.getTabBar()) {
				// this.getTabBar().setData({
				// 	selected: 3
				// });
			}
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
		},
		methods: {
			dialogToggle() {
				this.$refs.dialog.open()
			},
			onClickItem(e) {
				if (this.current !== e.currentIndex) {
					this.current = e.currentIndex;
					console.log('e', e, this.current)
				}
			},

			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			}
		}
	};
</script>
<style lang="scss">
	.uni-padding-wrap {
		// width: 750rpx;
		// padding: 0px 30px;
	}
	.card-line-title {
		font-weight: bold;
	}
	.card-line {
		display: flex;
		flex-direction: row;
	}
	.card-line-label {
		width: 70px;
		font-size: 13px;
	}
	.card-line-content {
		font-size: 13px;
		flex-grow: 1;
	}
	.card-line-link {
		font-size: 13px;
		color: var(--ui-BG-Main);
	}
	.card-actions {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 36px;
		border-top: 1px #eee solid;
	}
	.card-actions-item {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.card-actions-item-text {
		font-size: 12px;
		color: #00a5ec;
		margin-left: 5px;
	}
	.cover-image {
		flex: 1;
		height: 150px;
	}
	.no-border {
		border-width: 0;
	}
	::v-deep .amap-logo, .amap-copyright {
		display: none !important;
	}
	::v-deep .amap-copyright {
		display: none !important;
	}
	.sign-box {
		padding-top: 40px;
		padding-bottom: 40px;
		display: flex;
		justify-content: center;
	}
	.sign-button {
		width: 90px;
		height: 90px;
		border-radius: 45px;
		background: linear-gradient(135deg, white 0%, gray 100%);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
	}
</style>