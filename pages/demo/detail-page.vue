<template>
		<ui-sys>
			<view class="page-section page-section-gap" style="margin: 0 10px;">
				<uni-section title="详情" type="line">
					<uni-collapse ref="collapse" v-model="activeIndex" @change="collapseChange">
						<uni-collapse-item title="学生信息" name="0">
							<view style="margin-bottom: 12px;">
							<view class="content">姓名：李彤</view>
							<view class="content">学号：16115118</view>
							<view class="content">学院：传媒学院</view>
							<view class="content">专业：影视传媒</view>
							<view class="content">班级: 张三</view>
							<view class="content">状态：进行中</view>
							</view>
						</uni-collapse-item>
						<uni-collapse-item title="活动信息" name="1">
							<view style="margin-bottom: 12px;">
							<view class="content">活动名称：活动签到</view>
							<view class="content">活动级别：院级</view>
							<view class="content">活动时间：2024-10-12 10:00 - 2024-10-12 12:00</view>
							<view class="content">活动地点：教学楼A</view>
							<view class="content">创建人: 张三</view>
							<view class="content">状态：进行中</view>
							<view class="content">备注：活动仅用于测试</view>
							</view>
						</uni-collapse-item>
						<uni-collapse-item title="签到信息" name="2">
							<view style="margin-bottom: 12px;">
							<view class="content">签到时间：2024-10-12 10:00</view>
							<view class="content">签退时间：2024-10-12 12:00</view>
							</view>
						</uni-collapse-item>
						<uni-collapse-item title="审核流程" name="3">
							<uni-steps style="padding: 10px 0 20px;" :options="list2" active-color="#007AFF" :active="list2.length - 1" direction="column" :showWords="['name','time', 'address','remark']" />
						</uni-collapse-item>
					</uni-collapse>
				</uni-section>
			</view>
			<!-- 版权信息 -->
			<view class="copyright">
				<!-- parse <template is="tempCopyright" :data="webSiteName:webSiteName,domain:domain"/> -->
				<block name="tempCopyright">
					<view style="margin-top: 24rpx;"> © {{webSiteName}} {{domain}} </view>
				</block>
			</view>
			<view class="button-body box fixed">
				<button class="button" type="default" >返回</button>
				<!-- <button class="button" type="primary" >退回</button> -->
				<button class="button" type="warn" @click="dialogToggle">不通过</button>
				<button class="button" type="primary" >通过</button>
			</view>
			<uni-popup ref="dialog" type="dialog">
				<uni-popup-dialog ref="inputClose" title="提示"
				@confirm="dialogInputConfirm">
					<text>确定退回？</text>
				<!-- <view class="" style="width: 100%;">
					
				</view>	
				<view class="">
					2.必须打开WIFI增强手机定位准确度，可以不连接WIFI网络 
				</view>	
				<view class="text-box" scroll-y="true">
					
				</view> -->
				</uni-popup-dialog>
			</uni-popup>
			<!-- <HoverBall></HoverBall> -->
		</ui-sys>
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import config from '../../utils/config.js';

	var Api = require('../../utils/api.js');

	var util = require('../../utils/util.js');

	var Auth = require('../../utils/auth.js');

	var wxApi = require('../../utils/wxApi.js');

	var wxRequest = require('../../utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		components: {
		},
		data() {
			return {
				activeIndex: ['1','2','3'],
				items: ['签到详情', '签到记录'],
				current: 0,
				activeColor: '#007aff',
				styleType: 'text', //button 实心按钮
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				selected: 0,
				showerror: "none",
				moreStatus: "more",
				id:0, // 使用 marker点击事件 需要填写id
				title: 'map',
				latitude: 39.909,
				longitude: 116.39742,
				covers: [{
					latitude: 39.909,
					longitude: 116.39742,
					iconPath: '../../../static/location.png'
				}, {
					latitude: 39.90,
					longitude: 116.39,
					iconPath: '../../../static/location.png'
				}],
				list2: [{
					name: '辅导员',
					time: '2024-05-11 10：30',
					address: '通过',
				}, {
					name: '学生处',
					time: '2024-05-11 11：30',
					address: '通过',
					// remark: '来晚了'
				}, {
					name: '指导教师',
					time: '2024-05-11 12：30',
					address: '通过',
					// remark: '来晚了'
				}]
			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			var that = this;
			console.log(this.wechat)
			console.log("options", options)
		},
		onReady: function() {
			var that = this;
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
		},
		methods: {
			dialogToggle() {
				this.$refs.dialog.open()
			},
			collapseChange(e){
				// 如果是手风琴模式，返回类型为string，否则为array
				console.log('e', e)
			}
		}
	};
</script>
<style lang="scss">
	@mixin flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	@mixin height {
		/* #ifndef APP-NVUE */
		height: 100%;
		/* #endif */
		/* #ifdef APP-NVUE */
		flex: 1;
		/* #endif */
	}
	.fixed {
		position: fixed;
		left: 0;
		bottom: 0;
	}
	.box {
		@include flex;
	}

	.button {
		@include flex;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 35px;
		margin: 0 5px;
		border-radius: 5px;
		font-size: 14px;
	}
	uni-button::after{
		border: none;
	}
	.button-body {
		width: 100%;
		background-color: #fff;
		padding: 6px 0;
	}
	.uni-padding-wrap {
		// width: 750rpx;
		// padding: 0px 30px;
	}
	.content {
		padding: 0 15px;
	}
</style>