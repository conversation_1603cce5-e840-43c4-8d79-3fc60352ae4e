<template>
		<view>
			<slot v-bind:list="list"></slot>
			<uni-load-more :status="moreStatus"></uni-load-more>
			<!-- 无法访问网络 -->
			<view class="showerror" :style="'display:'+(showerror)">
				<text class="cicon-wifi-off text-xsl"></text>
				<view class="errortext" @click="searchMore">
					暂时无法访问网络，下拉重试...
				</view>
			</view>
		</view>
</template>

<script>
	/*
	 * 
	 * 微慕小程序开源版
	 * author: jianbo
	 * organization: 微慕  www.minapper.com
	 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
	 * 技术支持微信号：iamxjb
	 * 开源协议：MIT
	 * 
	 *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.
	 */
	import config from '@/utils/config.js';

	var Api = require('@/utils/api.js');

	var util = require('@/utils/util.js');

	var Auth = require('@/utils/auth.js');

	var wxApi = require('@/utils/wxApi.js');

	var wxRequest = require('@/utils/wxRequest.js');

	var app = getApp();
	var webSiteName = config.getWebsiteName;
	var domain = config.getDomain;
	var wechat = config.getWecat;
	export default {
		data() {
			return {
				webSiteName: webSiteName,
				domain: domain,
				wechat: wechat,
				avatar: "",
				page: 1,
				pageSize: 10,
				list: [
					{
					id: 1,
					name: "活动签到",
					creator: "张三",
					status: '进行中',
					createDate: "2024-04-12 10:00",
					startDate: "2024-04-13 10:00",
					endDate: "2024-04-14 00:00",
					progress: "65"
				}, {
					id: 2,
					name: "课堂签到",
					creator: "李四",
					status: '未开始',
					createDate: "2024-04-12 10:00",
					startDate: "2024-04-13 10:00",
					endDate: "2024-04-14 00:00",
					progress: "51"
				}, {
					id: 3,
					name: "会议签到",
					creator: "王五",
					status: '已暂停',
					createDate: "2024-04-12 10:00",
					startDate: "2024-04-13 10:00",
					endDate: "2024-04-14 00:00",
					progress: "79",
					banner: require('@/static/home-icon/banner.png')
				}, {
					id: 4,
					name: "通屏",
					creator: "赵六",
					status: '已结束',
					createDate: "2024-04-12 10:00",
					startDate: "2024-04-13 10:00",
					endDate: "2024-04-14 00:00",
					progress: "26",
					isFull: true
				}
				],

				selected: 0,

				dialog: {
					hidden: false,
					title: "",
					content: ""
				},
				showerror: "none",
				moreStatus: "more",
			};
		},
		methods: {
			// 刷新页面/重新加载
			reload(){
				let self = this;
				self.setData({
					page: 1,
				})
				setTimeout(()=>{
					console.log("停止当前页面的下拉刷新", self.page)
					uni.stopPullDownRefresh(); // 请求数据，成功获取数据则停止当前页面的下拉刷新
				}, 2000)
			},
			// 分页加载更多
			loadMore(){
				let self = this;
				if(this.moreStatus === "noMore" || this.moreStatus === "loading") {
					console.log('已经是最后一页或者加载中, 不需要发送请求');
				} else {
					self.setData({
						moreStatus: "loading",
						page: self.page + 1,
					})
					console.log('self.page', self.page);
					setTimeout(()=> {
						self.setData({
							moreStatus: "noMore"
							// moreStatus: "more"
						})
					}, 2000)
					console.log('loadMore');
				}
			}
		}
	};
</script>
<style lang="scss">
	::v-deep .uni-progress-info {
		font-size: 14px;
	}
	.card-line-title {
		font-weight: bold;
	}
	.card-line {
		display: flex;
		flex-direction: row;
	}
	.card-line-label {
		width: 70px;
	}
	.card-line-content {
		flex-grow: 1;
	}
	.card-actions {
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 36px;
		border-top: 1px #eee solid;
	}
	.card-actions-item {
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.card-actions-item-text {
		font-size: 12px;
		color: #666;
		margin-left: 5px;
	}
	.cover-image {
		flex: 1;
		height: 150px;
	}
	.no-border {
		border-width: 0;
	}
</style>