<template>
	<ui-sys :loading="isloading">
		<view class="login-body">
			<view class="login-header">
				<view class="">
					<img src="@/static/home-icon/login-logo.png" alt="" />
				</view>
				<view class="school-name">智慧学工系统</view>
				<!-- <view class="" @click.native="tapCopy">传递的token:</view>
				<view class="">{{paramToken}}</view> -->
			</view>
			<uni-forms ref="customForm" :rules="rules" :modelValue="valiFormData" :label-position="'top'">
				<uni-forms-item label="账号" required name="username">
					<uni-easyinput v-model="valiFormData.username" ref="username" placeholder="请输入账号" />
				</uni-forms-item>
				<uni-forms-item label="密码" required name="password">
					<uni-easyinput v-model="valiFormData.password" type="password" placeholder="请输入密码" />
				</uni-forms-item>
				<uni-forms-item label="验证码" required name="code">
					<view class="verification-box">
						<uni-easyinput class="verification-input" v-model="valiFormData.code" @confirm="onConfirm" type="code" placeholder="请输入验证码" />
						<view class="verification-image" @click="fetchCodeImgData">
							<img :src="codeImgData" alt="" />
						</view>
					</view>
				</uni-forms-item>
			</uni-forms>
			<!-- <button type="primary" @click="submit('customForm')">登录</button> -->
			<van-button type="info" @click="submit('customForm')" block>登录</van-button>
		</view>
	</ui-sys>
</template>

<script>
	import config from "@/utils/config.js";
	var Api = require("@/utils/api.js");
	var util = require("@/utils/util.js");
	
	var wxApi = require("@/utils/wxApi.js");
	var wxRequest = require("@/utils/wxRequest.js");
	var domain = config.getDomain;
	var HOST_URI = 'http://' + domain + '/api/';
	export default {
		data(){
			return {
				isloading: false,
				focus: true,
				// 校验表单数据
				valiFormData: {
					username: 'admin',
					password: '123456',
					code: ''
				},
				paramToken: '',
				codeImgData: "",
				codeImgCid: "",
				// 校验规则
				rules: {
					username: {
						rules: [{
							required: true,
							errorMessage: '账号不能为空'
						}]
					},
					password: {
						rules: [{
							required: true,
							errorMessage: '密码不能为空'
						}]
					},
					code: {
						rules: [{
							required: true,
							errorMessage: '验证码不能为空'
						}]
					}
				}
			}
		},
		onLoad(options) {
			console.log('login 参数', options);
			console.log('当前环境', process.env.NODE_ENV);
			this.paramToken = options.token;
			this.insertScript();
			this.fetchCodeImgData();
		},
		onReady() {
			// 设置自定义表单校验规则，必须在节点渲染完毕后执行
			// this.$refs.valiForm.setRules(this.rules)
		},
		methods: {
			
			tapCopy(e) {
				console.log('tapCopy', e)
				uni.setClipboardData({
					data: this.paramToken,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					}
				});
			},
			onConfirm(e){
				this.submit('customForm');
			},
			//获取验证码图片
			fetchCodeImgData: function () {
			  var self = this;
			  self.setData({
			    codeImgData: ""
			  });
			  // uni.showLoading({
			  //   title: '正在加载',
			  //   mask: true
			  // });
			  var getVertificateCodeImg = wxRequest.getRequest(Api.getVertificateCodeImg());
			  getVertificateCodeImg.then(response => {
				  console.log('res >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0 && resData.data) {
						self.setData({
							codeImgData: resData.data.base64,
							codeImgCid: resData.data.cid
						})
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('获取验证码失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
			    }, 1500);
			  });
			},
			submit(ref) {
				console.log('encrypt', encrypt('admin'));
				console.log('encrypt', encrypt);
				// console.log('window.encrypt', window.encrypt('admin'));
				console.log('this.valiFormData', this.valiFormData)
				this.$refs[ref].validate().then(res => {
					console.log('success', res);
					console.log('encrypt', encrypt(this.valiFormData.useruame));
					this.login(res);
				}).catch(err => {
					console.log('err', err);
					this.focus = true;
					let message = err.map(item => item.errorMessage).join(',');
					uni.showToast({
						icon: 'error',
						title: `${message}`
					})
				})
			},
			//获取验证码图片
			login (obj) {
			  var self = this;
			  uni.showLoading({
			    title: '登录中',
			    mask: true
			  });
			  let data = {
				  username: encrypt(obj.username),
				  password: encrypt(obj.password),
				  code: encrypt(obj.code),
				  cid: encrypt(this.codeImgCid),
				  remember: true
			  }
			  let url = Api.login();
			  var postLogin = wxRequest.postRequest(url, data);
			  postLogin.then(response => {
				console.log('login >>>>>>', response)
			    if (response.statusCode === 200) {
					let resData = response.data;
					if(resData.code === 0) {
						self.$store.commit('setUser', {...resData.data.userInfo, userAccount: obj.username});
						uni.setStorageSync("token", resData.data.access_token);
						uni.setStorageSync('isFirst', true);
						console.log('set isFirst', uni.getStorageSync('isFirst'))
						// self.$store.commit('setToken', resData.data.access_token);
						uni.showToast({
							title: `登录成功`,
							duration: 1000
						})
						uni.navigateTo({
							url: '/pages/index/index'
						});
						// self.setData({
						// 	codeImgData: resData.data.base64,
						// 	codeImgId: resData.data.cid
						// })
					} else {
						uni.showToast({
							title: resData.message,
							icon: 'error',
							duration: 1500
						});
					}
					
			    } else if (response.statusCode === 404) {
			      console.log('登录失败');
			    }
			  }).catch(function () {
			    uni.hideLoading();
			    self.setData({
			      showerror: "block",
			      floatDisplay: "block"
			    });
			  }).finally(function () {
			    setTimeout(function () {
			      uni.hideLoading();
				  self.fetchCodeImgData();
				  self.insertScript();
			    }, 1000);
			  });
			},
			handleBack(){
				uni.navigateBack({
				  delta: 1
				});
			},
			insertScript() {
			  var scriptElement = document.createElement('script');
			  scriptElement.type = 'text/javascript';
			  scriptElement.async = true; // 确保脚本异步加载
			  scriptElement.src = '/api/sec_js';
			  var targetElement = document.getElementsByTagName('head')[0] || document.body;
			  targetElement.appendChild(scriptElement);
			}
		}
	}
</script>

<style lang="scss">
	::v-deep .ui-BG-2 {
		background-color: var(--ui-BG-1) !important;
	}
	.login-body {
		padding: 16px;
	}
	.login-header {
		padding: 50px 0 32px;
		text-align: center;
	}
	.login-header img {
		width: 64px;
		height: 64px;
		margin-bottom: 10px;
	}
	.school-name {
		color: #111827;
		font-family: "Inter";
		font-size: 28px;
		font-style: normal;
		font-weight: 700;
		line-height: 42px; /* 1.5 */
		text-align: center;
	}
	.verification-box {
		width: 100%;
		display: flex;
	}
	.verification-input {
		flex: 1;
		padding-right: 2px;
	}
	.verification-image {
		width: 106px;
		border: 1px solid rgb(229, 229, 229);
		background-color: #fff;
		height: 36px;
	}
	.verification-image img {
		width: 104px;
		height: 33px;
	}
</style>