<template>
	<ui-sys :loading="isLoading">
		
	</ui-sys>
</template>

<script>
	export default {
		data(){
			return {
				isLoading: false,
				options: {}
			}
		},
		onLoad(options) {
			// options.t: token  options.u:跳转的
			// console.log('base64解码', atob('L3BhZ2VzL2luZGV4L2luZGV4'), atob(options.u))
			console.log('options', options)
			this.options = options;
			// alert('u:' + JSON.stringify(options.u))
			if(options.t) { // 登录验证通过返回token
				uni.setStorageSync('token', options.t)
				uni.setStorageSync('isFirst', true);
				if(options.u) { // 如果传递了base64的url，base64解码并跳转至该url
				    let url = atob(options.u);
					console.log('url', url)
					// 获取用户基本信息
					uni.navigateTo({
						url: atob(options.u)
					});
				} else { 
					/**没有url就跳转至首页*/
					uni.navigateTo({
						url: '/pages/index/index'
					});
				}
			} else { // 登录验证失败，跳转至登录页，并提示
				uni.showToast({
				  title: '登录失败',
				  mask: false,
				  icon: 'error',
				  duration: 1000
				});
				uni.navigateTo({
					url: '/pages/login/login'
				});
			}
		}
	}
</script>

<style>
</style>