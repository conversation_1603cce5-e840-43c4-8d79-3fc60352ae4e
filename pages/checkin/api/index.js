import config from "@/utils/config.js";
var domain = config.getDomain;
var pageCount = config.getPageCount;
// var categoriesID = config.getCategoriesID;
// var HOST_URI = 'https://' + domain + '/wp-json/wp/v2/';
// var HOST_URI_WATCH_LIFE_JSON = 'http://' + domain + '/wp-json/watch-life-net/v1/';
// var HOST_URI = 'http://' + domain + '/api/';
module.exports = {
	/**
	 * 获取签到项目列表分页
	 * page=1&limit=10
	 * /api/personInfo/{userType}/queryPage
	*/
	checkinItemPage: function () {
	  return  '/api/checkin/checkin-item/page';
	},
	/**
	 * 添加/修改签到项目
	*/
	checkinItemOperation: function () {
	  return  '/api/checkin/checkin-item/operation';
	},
	/**
	 * 删除签到项目
	 * array: ['']
	*/
	checkinItemDelete: function () {
	  return  '/api/checkin/checkin-item/remove';
	},
	/**
	 * 添加预设位置
	 * {"name":"北城国际A区", "bz":"丰收路11号", "type":"poi", "fwbj":200, "jwdzb":"38.073891#114.517896"}
	*/
	checkinAddressOperation: function () {
	  return  '/api/checkin/checkin-address/operation';
	},
	/**
	 * 获取签到项目详情
	 * id
	*/
	checkinItemDetail: function () {
	  return  '/api/checkin/checkin-item/';
	},
	/**
	 * 获取发布签到项目需要的字典
	 * @param { codeType }  字典code  hdQdfs：签到方式  	hdQdsjlx：时间类型  weeks：循环频次
	*/
	codeCommon: function () {
	  return  '/api/code/codeCommon';
	},
	/**
	 * 获取预设签到地址
	*/
	checkinAddress: function () {
	  return  '/api/checkin/checkin-address';
	},
	
	/**
	 * 获取签到对象的角色列表
	 * paramMode：base
	*/
	selectorConfig: function () {
	  return  '/api/selector/selector-config';
	},
	
	/**
	 * 获取签到对象角色下的成员分页
	 * @param { page } 页码
	 * @param { limit } 每页条数
	 * /api/personInfo/${type}/queryPage
	*/
	personPage: function () {
	  return  '/api/personInfo';
	}
	
}