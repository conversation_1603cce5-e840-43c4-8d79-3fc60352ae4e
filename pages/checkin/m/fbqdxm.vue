<template>
	<section class="div-root" style="padding-bottom: 50px; background-color: #ffffff;">
	    <view class="title">发布签到项目</view>
		<van-cell-group>
			<van-field 
				v-model="formData.name"
				label="签到名称"
				placeholder="请输入签到名称"
				required
				:error-message="nameemsg"
			/>
            <van-field 
				readonly
                clickable
                label="签到方式"
                :value="formData.qdfsName"
                placeholder="请选择签到方式"
                @click="showqdfsPicker = true"
                required/>
            <van-popup v-model="showqdfsPicker" position="bottom">
                <van-radio-group v-model="formData.qdfsName">
                    <van-cell v-for="(item, index) in qdfsNameColumns"
                              clickable
                              :key="item"
                              :title="`${item}`"
                              :label="`${qdfsLabelColumns[index]}`"
                              @click="toggle(item, index)">
                    </van-cell>
                </van-radio-group>
                <van-radio-group>
                    <van-cell style="text-align: center;"
                              title="取消"
                              @click="showqdfsPicker = false"></van-cell>
                </van-radio-group>
            </van-popup>
			<template v-if="formData.qdfs == 'smqd' || formData.qdfs == 'dwsmqd'">
				<van-field
					readonly
					clickable
					label="二维码类型"
					:value="formData.qrtypeName"
					placeholder="请选择二维码类型"
					@click="showQrtypePicker = true"
				/>
				<van-popup v-model="showQrtypePicker" position="bottom">
					<van-picker show-toolbar :columns="qrtypeColumns" :loading="!qrtypeColumns"
								@cancel="showQrtypePicker = false" @confirm="onQrtypeConfirm"/>
				</van-popup>
				<van-field v-model="formData.qrmc"
						   label="二维码名称"
						   placeholder="请输入二维码名称"
				/>
				<van-field v-if="formData.qrlx === 1" :value="formData.qryxsj"
						   label="二维码有效时间(秒)"
						   placeholder="请输入二维码有效时间"
						   readonly
						   clickable
						   @touchstart.native.stop="showQryxsjC = true"
				/>
				<van-number-keyboard v-model="formData.qryxsj"
									 safe-area-inset-bottom
									 :show="showQryxsjC"
									 :z-index="1000"
									 close-button-text="完成"
									 @blur="showQryxsjC = false"/>
			</template>
			<template v-if="formData.qdfs == 'dwqd' || formData.qdfs == 'dwsmqd' || formData.qdfs == 'dwpzqd' ">
				<!-- 签到条件： "2"：范围外可签到  "1"：必须在范围内签到 
					qdtj: false '1'   true: '2'
				-->
				<van-cell title="范围外可签到" title-style="color: #646566;" required>
					<template #right-icon>
						<van-switch v-model="formData.qdtj" @change="qdtjChange"
								size="20"
								active-color="#1989fa"/>
					</template>
				</van-cell>
				<!-- <van-field v-model="radius"
						   placeholder="请输入签到范围(整数)" type="digit" label="签到范围(米)" :required="!fwwkqd"/> -->
				<van-field label="签到位置(可多选)"
                           label-width="120px"
                           readonly
                           clickable
                           placeholder="请在地图上选择"
                           @click="showMap"
                           required/>
				<van-cell title="已选择位置">
					<template #label>
						<div v-for="(address,index) in addressList" :key="address.id">
							<van-tag closeable size="medium" type="primary" style="margin: 5px;"
									 @close="closeAddressTag(index)">
								<span> {{address.name}}</span>
							</van-tag>
						</div>
					</template>
				</van-cell>
				<van-field readonly
						   clickable
						   label="预设签到位置"
						   placeholder="请选择预设签到位置"
						   @click="showyswzPicker = true"
						   type="textarea"
						   rows="4"
						   autosize
						   :value="codesYswzNames"
						   required/>
				<van-popup v-model="showyswzPicker" position="bottom" :style="{ height: '60%' }">
					<view class="" style="margin-bottom: 50px;">
						<van-cell v-for="item in yswzColumns" :key='item.id' @click="checkYswz(item)">
							<van-checkbox :key="item.id"
										  :name="item.name"
										  :value="codesYswzIds.includes(item.id)"
										  shape="square"> {{item.name}}
							</van-checkbox>
						</van-cell>
					</view>
					<div class="van-submit-bar">
						<view class="van-submit-bar-box">
							<van-button type="info" plain @click="showyswzPicker = false">取消</van-button>
							<van-button type="info" @click="showyswzSubmit">确定</van-button>
						</view>
					</div>
				</van-popup>
			</template>
			<van-field readonly
                       clickable
                       label="签到时间"
                       placeholder="请选择签到时间"
                       :value="showQdsjName"
                       @click="showqdsjPicker = true"
                       required/>
			<van-popup v-model="showqdsjPicker"
					position="bottom"
                   :style="{ height: '100%',width:'100%' }">
            <!-- <set-qdsj :detailId='detailId' :canUseYibanMessage="canUseYibanMessage" :sync="sync"></set-qdsj> -->
				<set-qdsj :detailId='detailId' :hdQdsjlxList="hdQdsjlxList" :weeksList="weeksList"></set-qdsj>
			</van-popup>
			<van-cell v-if="formData.qdcs" center title="限签次数" title-style="color: #646566;">
			    <van-stepper v-model="formData.qdcs" integer min="1" step="1" />
			</van-cell>
			<van-field readonly
                       clickable
                       label="签到对象"
                       :value="names"
                       placeholder="请选择签到对象"
                       @click="showrangPicker = true"/>
			<van-popup v-model="showrangPicker" position="bottom" :style="{ height: '100%',width:'100%' }">
				<set-rang :code="code"
						  :rang="rang"
						  v-on:checkChange="onCheckChange"
						  v-on:handleRangCancle="showrangPicker=false">
				</set-rang>
			</van-popup>
			<van-field v-model="formData.bz"
			   label="签到说明"
			   type="textarea"
			   placeholder="请输入签到说明"
			   rows="4"
			   maxlength="255"
			   show-word-limit
			   autosize/>
			<van-cell title="是否提醒" title-style="color: #646566;">
				<template #right-icon>
					<van-switch v-model="formData.sftx" @change="sftjChange"
							size="20"
							active-color="#1989fa"/>
				</template>
			</van-cell>
			<van-cell v-if="formData.sftx" center title="提前提醒时间" title-style="color: #646566;">
			    <van-stepper v-model="formData.tqtxsj" integer min="0" step="1" />
			</van-cell>
		</van-cell-group>
		<van-popup position="bottom"
                   v-model="showaddressPicker"
                   :style="{ height: '80%',width:'100%' }">
            <set-map v-on:initMap="handleInitMap"></set-map>
        </van-popup>
		<view class="van-submit-bar">
			<view class="van-submit-bar-box">
		      <!-- <van-button type="info" plain @click="$router.go(-1)">返回</van-button> -->
		      <van-button type="info" @click="handleSubmit" :loading="loading" loading-text="提交中...">提交</van-button>
			</view>
		</view>
	</section>
</template>

<script>
	import Bus from '@/utils/event-bus'
	import setQdsj from '../components/qdsj.vue'
	import setRang from '../components/rang.vue'
	import setMap from '../components/map.vue'
	var util = require("@/utils/util.js");
	import Utils from '@/utils/common/momentWrap.js';
	var wxRequest = require('@/utils/wxRequest.js');
	var Api = require('../api/index.js');
	export default {
		components:{
			setQdsj,
			setRang,
			setMap
		},
		onLoad: function(options) {
			console.log('onLoad', options)
			if(options.type) {
				this.type = options.type;
			}
			if(options.id) {
				this.detailId = options.id;
				// this.getDetail();
				// setTimeout(()=>{
				// 	this.getCodeCommn('hdQdsjlx');
				// 	this.getCodeCommn('hdQdfs');
				// 	this.getCodeCommn('weeks');
				// 	this.getAddress();
				// }, 2000)
				this.getAllData();
			} else {
				this.getCodeCommn('hdQdsjlx');
				this.getCodeCommn('hdQdfs');
				this.getCodeCommn('weeks');
				this.getAddress();
			}
		},
		data() {
			return {
				type: null, // 签到项目类型
				loading: false,
				detailId: null,//签到活动ID，修改时查询数据
				formData: {
					name: null, // 活动名称
					qdfsName: null, // 签到方式name
					qdfs: null, // 签到方式code
					qrtypeName: '', // 二维码类型名称
					qrlx: '', // 二维码类型code
					qrmc: null, // 二维码名称
					qryxsj: '',// 二维码有效时间
					qdtj: false, // 签到条件： true - "2"：范围外可签到   false - "1"：必须在范围内签到 ;回显需要转换为true/false，提交时需要转换为对应的数字
					qdcs: 1, //限签次数
					bz: null, //签到说明
					sftx: false, // 是否提醒   true: 是  false： 否
					tqtxsj: 0,
				},
				nameemsg: null,
				showqdfsPicker: false,
				hdQdfsList: [],
				hdQdsjlxList: [],
				weeksList: [],
				showQrtypePicker: false,
				qrtypeColumns: ['动态二维码（自动刷新，防止作弊）', '静态二维码（适合打印张贴）'],
                showQryxsj: false,//二维码有效时间(单位：分钟，默认5分钟)
				showQryxsjC: false,
				fwwkqd: false,
				showQdsjName: null,
				showqdsjPicker: false,
				showQryxsj: false,//二维码有效时间(单位：分钟，默认5分钟)
				// radius: '',
				showyswzPicker: false,
                yswzColumns: [],
                checkedYswzArray: [], //预设位置选中的数据
                checkedYswzNames: [],
				addressList: [], // 地图选中的数据
				code: [],
                rang: [],
                showRang: null,
				showrangPicker: false,
                rangcolumns: [],
				setObjects: [
                    {model: 'name', placeholder: '请输入签到名称'},
                    {model: 'qdfs', placeholder: '请选择签到方式'},
                    {model: 'showQdsjName', placeholder: '请选择签到时间'},
                ],
				showaddressPicker: false,
			}
		},
		computed: {
			codesYswzIds() {
                let arr = [];
                if (this.checkedYswzArray) {
                    this.checkedYswzArray.forEach(item => {
                        arr.push(item.id)
                    })
                }
                return arr
            },
            codesYswzNames() {
                let arr = [];
                if (this.checkedYswzArray) {
                    this.checkedYswzArray.forEach(item => {
                        arr.push(item.name)
                    })
                }
                return arr.toString()
            },
            codesYswzpolygonLngLat() {
                let arr = [];
                if (this.checkedYswzArray) {
                    this.checkedYswzArray.forEach(item => {
                        arr.push(item.jwdzb)
                    })
                }
                return arr.join('#')
            },
			qdfsNameColumns() {
				let arr = [];
				if (this.hdQdfsList) {
				    this.hdQdfsList.forEach(item => {
				        arr.push(item.name)
				    })
				}
				return arr;
			},
			qdfsCodeColumns() {
				let arr = [];
				if (this.hdQdfsList) {
				    this.hdQdfsList.forEach(item => {
				        arr.push(item.code)
				    })
				}
				return arr;
			},
			qdfsLabelColumns() {
				let arr = [];
				if (this.hdQdfsList) {
				    this.hdQdfsList.forEach(item => {
				        arr.push(item.description)
				    })
				}
				return arr;
			},
			codes() {
                let arr = [];
                if (this.rang) {
                    this.rang.forEach(item => {
                        arr.push(item.value)
                    })
                }
                return arr
            },
            names() {
                let arr = [];
                if (this.rang) {
                    this.rang.forEach(item => {
                        arr.push(item.name)
                    })
                }
                return arr.join()
            },
		},
		methods: {
			showMap() {
				console.log('window._AMapSecurityConfig', window._AMapSecurityConfig);
				this.showaddressPicker = true;
                // if (!this.radius) {
                //     this.$toast('请输入签到范围');
                // } else {
                //     this.showaddressPicker = true
                // }
            },
			handleInitMap(data) {
				console.log('data', data);
				let self = this;
				let obj = {
					bz: data.address,
					fwbj: data.radius,
					jwdzb: data.location.lng + "#" + data.location.lat,
					name: data.name,
					type: "poi"
				}
				this.$dialog
				.confirm({
				  title: "提示",
				  message: "是否同步地址？",
				  cancelButtonText: '不同步',
				  confirmButtonText: '同步'
				})
				.then(() => {
					console.log('用户点击了“确定”按钮');
					// 调用保存预设地址接口
					obj.isFromMap = "是";
					self.handleAddressSubmit(obj);
				}).catch(() => {
					obj.isFromMap = "否";
					self.addressList.push(obj);
					console.log('用户点击了“取消”按钮');
					console.log('取消后addressList', self.addressList)
					// 将其直接添加到已选地址中
				})
                this.showaddressPicker = false;
            },
			closeAddressTag(index) {
				this.addressList.splice(index, 1);
				console.log('删除后addressList', this.addressList)
			},
			handleAddressSubmit(data) {
				console.log('提交地址', data);
			    let isSubmit = true;
			    let self = this;
			    let onError = () => {
			        this.$toast.fail('提交失败');
			    };
			    if (isSubmit) {
					let url = Api.checkinAddressOperation();
					wxRequest.postRequest(url, data).then(res => {
						console.log('提交地址返回结果', res)
						if(res.statusCode === 200) {
							if(res.data.code === 0) {
								self.checkedYswzArray.push(res.data.data);
								this.$toast.success({
									duration: 2000,
									message: '添加成功',
									onClose: function () {
										// 重新查询预设地址列表
										self.getAddress();
									}
								});
							} else {
								this.$toast.fail({
									duration: 2000,
									message: res.data.message,
								});
							}
							
						} else {
							this.$toast.fail({
								duration: 2000,
								message: res.data.message,
							});
						}
					}).finally(()=>{
						// this.loading = false;
					});
			    }
			},
			handleSubmit() {
				console.log('提交', this.formData);
                let newObj = {
					...this.formData,
                    showQdsjName: this.showQdsjName,
					type: this.type ? this.type : this.formData.type,
                    // name: this.formData.name,
                    // qdfs: this.formData.qdfs,
                };
				newObj.sftx = newObj.sftx ? '是' : '否';
				if (this.detailId) {
					newObj.id = this.detailId;
				}
                let isSubmit = true;
                this.setObjects.forEach((el, index) => {
                    if (index > 0) {
                        if (newObj[this.setObjects[index - 1].model] && !newObj[el.model]) {
                            this.$toast.clear();
                            this.$toast(el.placeholder);
                            isSubmit = false;
                            return false;
                        }
                    } else {
                        if (!newObj[el.model]) {
                            this.$toast.clear();
                            this.$toast(el.placeholder);
                            isSubmit = false;
                            return false;
                        }
                    }
                });
				console.log('newObj', newObj)
				console.log('newObj.qdtj', newObj.qdtj)
				if(newObj.qdtj) {
					newObj.qdtj = '2';
				} else {
					newObj.qdtj = '1';
				}
				if(newObj.ksrq && newObj.jsrq) {
					newObj.ksjsrqfw	= [newObj.ksrq, newObj.jsrq]
				}
				// newObj.addressesIds	= this.codesYswzIds;
				newObj.selectorDatas = this.rang;
				console.log('newObj', newObj)
                if (this.formData.qdfs && (['dwqd', 'dwsmqd', 'dwpzqd'].includes(this.formData.qdfs))) {
                    // if (!this.radius) {
                    //     this.$toast.clear();
                    //     this.$toast('请输入签到范围');
                    //     return false;
                    // }
                    // if (this.checkedYswzArray.length == 0 && this.addressList.length == 0) {
                    //     this.$toast.clear();
                    //     this.$toast('请选择签到地址');
                    //     return false;
                    // }
                }
				newObj.checkinItemAddresses	= this.checkedYswzArray.concat(this.addressList);
                // if (this.formData.qdfs && (this.formData.qdfs === 'smqd' || this.formData.qdfs === 'dwsmqd')) {
                //     if (!this.formData.qryxsj) {
                //         this.$toast.clear();
                //         this.$toast('请输入二维码有效时间');
                //         return false;
                //     }
                // }
                let self = this;
                let onError = () => {
                    this.$toast.fail('提交失败');
                    // self.$store.commit('hideLoading');
                    // self.$router.back()
					uni.navigateBack({
					    delta: 1 // 返回的页面数，1 表示返回上一页
					});
                };
                if (isSubmit) {
                    this.loading = true;
					let url = Api.checkinItemOperation();
					wxRequest.postRequest(url, newObj).then(res => {
						if(res.statusCode === 200) {
							if(res.data.code == 0) {
								this.$toast.success({
									duration: 2000,
									message: '发布成功',
									onClose: function () {
										// self.$store.commit('hideLoading');
										// 点击回退的时候当做地址回退
										// self.$router.back()
										uni.navigateBack({
										    delta: 1 // 返回的页面数，1 表示返回上一页
										});
									}
								});
							} else {
								this.$toast.fail({
									duration: 2000,
									message: res.data.message,
								});
							}
							
						} else {
							this.$toast.fail({
								duration: 2000,
								message: res.data.message,
							});
						}
						console.log('提交返回值', res);
						
					}).finally(()=>{
						this.loading = false;
					});
                }
            },
			onCheckChange(e) {
                let userCode = e.value;
                if (this.codes.includes(userCode)) {
                    this.rang.splice(this.codes.indexOf(userCode), 1)
                } else {
                    this.rang.push(e)
                }
				console.log('点击了', this.rang)
            },
			sftjChange(item) {
				console.log('item', item, this.formData);
			},
			qdtjChange(item) {
				// this.formData.qdtj = item
				console.log('item', item, this.formData);
			},
			onQrtypeConfirm(value, index) {
				this.formData.qrtypeName = value;
				this.formData.qrlx = index + 1;
				console.log('value, index', value, index, this.formData);
				this.showQrtypePicker = false
			},
			toggle(title, index) {
				console.log('title, index', title, index);
				this.selectdQdType = title === '扫码签到' ? false : true;
				//二维码有效时间(单位：分钟，默认5分钟)
				// if (title === '扫码签到' || title === '定位&扫码签到') this.showQryxsj = true;
				this.showQryxsj = (title === '扫码签到' || title === '定位&扫码签到') ? true : false;
				this.formData.qdfsName = title;
				this.formData.qdfs = this.qdfsCodeColumns[index];
				console.log('this.qdfs', this.formData);
				this.showqdfsPicker = false
			},
			checkYswz(item) {
                //获取当前group已选择的信息
                let currentRang = this.checkedYswzNames ? this.checkedYswzNames : [];
                let userCode = item.id;
                if (this.codesYswzIds.includes(userCode)) {
                    this.checkedYswzArray.splice(this.codesYswzIds.indexOf(userCode), 1)
                    currentRang.splice(this.codesYswzIds.indexOf(userCode), 1)
                } else {
                    this.checkedYswzArray.push(item)
                    currentRang.push(item)
                }
				console.log('this.checkedYswzArray', this.checkedYswzArray)
            },
            showyswzSubmit() {
                this.showyswzPicker = false;
				console.log('选中', this.codesYswzNames, this.codesYswzIds)
            },
			async getCodeCommn(codeType) {
				let self = this;
				let url = Api.codeCommon();
				let data = {
					codeType
				}
				await wxRequest.getRequest(url, data).then(res => {
					if (res.statusCode === 200) {
						let resData = res.data;
						if(resData.code === 0) {
							self.setData({
								[codeType + 'List']: resData.data
							})
						} 
					}
				}).catch(error => {
				    console.log(error);
				}).finally(() => {
				    // this.loading = false
				})
			},
			// 预设签到地址
			async getAddress() {
				let self = this;
				let url = Api.checkinAddress();
				let data = {}
				await wxRequest.getRequest(url, data).then(res => {
					if (res.statusCode === 200) {
						let resData = res.data;
						if(resData.code === 0) {
							resData.data.forEach(i => {
								i.isFromMap = '是';
							})
							self.setData({
								yswzColumns: resData.data
							})
							console.log('预设地址',this.yswzColumns)
						} 
					}
				}).catch(error => {
				    console.log(error);
				    // this.error = true
				}).finally(() => {
				    // this.loading = false
				})
			},
			async getDetail() {
				console.log('this.detailId', this.detailId)
				let self = this;
				let url = Api.checkinItemDetail() + this.detailId;
				let data = {}
				await wxRequest.getRequest(url, data).then(res => {
					console.log('详情', res)
					if (res.statusCode === 200) {
						let resData = res.data;
						if(resData.code === 0) {
							let data = resData.data;
							data.sftx = resData.data.sftx == '是' ? true: false;
							data.qdtj = resData.data.qdtj == '2' ? true: false;
							let checkedYswzArray = []; // 预设位置
							let addressList = []; // 地图选中位置
							resData.data.checkinItemAddresses.forEach(item => {
								if(item.isFromMap == '是') {
									let obj = {
										bz: item.bz,
										fwbj: item.fwbj,
										jwdzb: item.jwdzb,
										name: item.name,
										type: item.type,
										isFromMap: item.isFromMap,
										id: item.addressId,
										sort: item.sort,
										status: item.status
									};
									checkedYswzArray.push(obj)
								} else {
									// delete item.id;
									item.id = item.addressId;
									item.addressId = null;
									addressList.push(item)
								}
								
							})
							self.setData({
								formData: data,
								rang: resData.data.selectorDatas,
								checkedYswzArray,
								addressList
							})
						} 
					}
				}).catch(error => {
				    console.log(error);
				    // this.error = true
				}).finally(() => {
				    // this.loading = false
				})
			},
			getAllData() {
				Promise.allSettled([this.getCodeCommn('hdQdsjlx'), this.getCodeCommn('hdQdfs'),this.getCodeCommn('weeks'),this.getAddress(), this.getDetail()])
				.then(res => {
					console.log('allSettled res', res, this.hdQdsjlxList, this.hdQdfsList, this.formData);
					this.updateForm();
				})
			},
			updateForm() {
				let formData = this.formData;
				let hdQdfsObj = this.hdQdfsList.find(i => i.code == formData.qdfs);
				this.formData.qdfsName = hdQdfsObj ? hdQdfsObj.name : "";
				let qdsjObj = this.hdQdsjlxList.find(i => i.code == formData.qdsjlx);
				this.showQdsjName = qdsjObj ? qdsjObj.name : "";
				console.log('更新后', this.formData);
			}
			
		},
		created() {
			// this.detailId = 路由里的id
            // this.ajaxCheckFB();
            // let query = this.$route.query; //传递过来的参数
            // this.mark = query.mark;//编辑
            // this.detailId = query.id;//活动ID
            // this.getDetailInfo();
            // this.getCanUseYibanMessage();
            //签到时间页-点击取消
            Bus.$off('handleCancle');
            Bus.$on('handleCancle', () => {
                this.showqdsjPicker = false
            });
            Bus.$off('handleQdshSubmit');
            Bus.$on('handleQdshSubmit', data => {
                this.showqdsjPicker = false;
                this.formData = Object.assign(this.formData, data);
                this.showQdsjName = data.qdsjName;
				console.log('this.formData', this.formData)
            });
            //签到对象页面-点击取消
            Bus.$off('handleRangSubmit');
            Bus.$on('handleRangSubmit', data => {
                this.showrangPicker = false;
                this.rang = data.rang;
                this.showRang = data.showRang
            });
        }
	}
	
</script>

<style scoped>
	.div-root {
		padding-top: 8px;
		/* padding: 8px; */
	}
	.title {
		padding-left: 12px;
		padding-right: 12px;
		text-align: center;
		color: #646566;
	}
	::v-deep .van-field__control {
		font-family: "ui-num", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
		font-size: 14px;
		color: #646566;
	}
	::v-deep .van-field__control::placeholder {
		font-family: "ui-num", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
		font-size: 14px;
	}
	.van-cell {
		color: #646566;
	}
</style>