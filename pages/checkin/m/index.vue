<!--签到管理列表-->
<template>
	<set-list :mark="mark"></set-list>
</template>

<script>
	import setList from '../components/list'
	export default {
		props: {
            mark: String,
        },
		components: {
			setList
		},
		data() {
			return {

			};
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
		},
		onReady: function() {
		},
		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady: function() {},
		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow: function() {
		},
		/**
		 * 生命周期函数--监听页面隐藏
		 */
		onHide: function() {},
		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload: function() {},
		/**
		 * 页面相关事件处理函数--监听用户下拉动作
		 */
		onPullDownRefresh: function() {
			console.log('下拉刷新');
			// this.$refs.list.reload();
			// uni.startPullDownRefresh();//主动触发下拉刷新
			// setTimeout(()=>{
			// 	uni.stopPullDownRefresh(); // 请求数据，成功获取数据则停止下拉刷新
			// }, 2000)
		},
		/**
		 * 页面上拉触底事件的处理函数
		 */
		onReachBottom: function() {
			console.log('到底了加载更多');
		},
		methods: {
			tapCopy(e) {
				uni.setClipboardData({
					data: this.wechat,
					success() {
						uni.showToast({
							title: '复制成功！',
							icon: 'none'
						});
					},
				});
			}
		}
	};
</script>
<style lang="scss">

</style>