<template>
    <section>
        <van-tabs v-model="activeIndex"
                  @click="onClick"
                  color="#00a5ec"
                  title-active-color="#00a5ec"
                  sticky>
            <van-tab v-on:setRangBoxList="onRangBoxList"
                     v-for="(tab,index) in option"
                     :title="tab.title"
                     :name="tab.id"
                     :key="tab.id">
                <rang-list :markText="tab.title"
                           :mark="tab.id"
						   :tabObj="tab"
                           :rang="rang"
                           :result="code"
                           v-on:checkRangBox="chkRbox"
                           v-on:handleRangCancle="$emit('handleRangCancle')"></rang-list>
            </van-tab>
        </van-tabs>
    </section>
</template>
<script>
    // import store from '@/store
    import Bus from '@/utils/event-bus'
    import rangList from './ranglist'
	var wxRequest = require('@/utils/wxRequest.js');
	var AjaxApi = require('../api/index.js');

    export default {
        components: { rangList },
        props: {
            setMark: String,
            rang: Array,
            showRang: String,
            code: Array
        },
        data() {
            return {
                // roleKey: store.state.roleKey,
                roleKey: null,
                activeIndex: 0,
                mark: null,
                alloption: [
                    {text: '机构', value: 'dw'},
                    {text: '学生', value: 'lsxs'},
                    {text: '专业', value: 'zy'},
                    {text: '班级', value: 'bj'},
                    {text: '年级', value: 'nj'}
                ],
                fdyoption: [
                    {text: '学生', value: 'lsxs'},
                    {text: '班级', value: 'bj'},
                ],
                jsqdoption: [
                    {text: '教师', value: 'jiaoshi'},
                ],
                zyzdoption: [
                    {text: '在校生', value: 'zyzd'},
                ],
                jsxsoption: [
                    {text: '教师', value: 'jiaoshi'},
                    {text: '学生', value: 'lsxs'},
                ],
                xsoption: [
                    {text: '学生', value: 'lsxs'},
                ],
                dwoption: [
                    {text: '机构', value: 'dw'},
                ],
                result: [],
                option: [],
                total: [],
                checked: false,
            }
        },
        created() {
			this.getRoleList();
            // if (this.setMark === 'jiaoshi') {
            //     this.option = this.jsqdoption
            // } else if (this.setMark === 'zyzd') {
            //     this.option = this.zyzdoption
            // } else if (this.setMark === 'jsxs') {
            //     this.option = this.jsxsoption
            // } else if (this.setMark === 'xs') {
            //     this.option = this.xsoption
            // } else if (this.setMark === 'dw') {
            //     this.option = this.dwoption
            // } else {
            //     if (this.roleKey === '班级') {
            //         this.option = this.fdyoption
            //     } else {
            //         this.option = this.alloption;
            //         if (this.setMark && this.roleKey === '全校') {
            //             if (this.setMark === 'tzgg') {
            //                 this.option.push({text: '角色', value: 'role'})
            //             } else {
            //                 this.option.push({text: '角色', value: 'js'})
            //             }
            //         }
            //     }
            // }
        },
        computed: {},
        methods: {
            onRangBoxList(e) {
                this.result = e.list;
                this.total = e.total;
            },
            chkRbox(e) {
                this.$emit('checkChange', e)
            },
            onClick(name) {
				console.log('onClick', name, this.activeIndex)
                let json = {
                    pageIndex: 0,
                    loading: name == 0 ? false : true,
                    finished: name == 0 ? true : false,
                    items: [],
                    mark: name,
                    checked: false,
                };
				
                if (this.searchValue) json.searchValue = this.searchValue;
                // Bus.$emit('handleSelectedRangTabs', json)
            },
			//获取角色列表
			getRoleList() {
			    let data = {
					paramMode: 'base'
			    };
			    let url, type, newArray = [];
				url = AjaxApi.selectorConfig();
			    wxRequest.getRequest(url, data).then(res => {
					console.log('角色', res)
					if(res.statusCode == 200) {
						let resData = res.data;
						if(resData.code == 0) {
							this.setData({
								option: resData.data
							})
						}
						console.log('option', this.option)
					}
			    }).catch(error => {
			        console.log(error);
			    }).finally(() => {
			    })
			}
        }
    }
</script>
