<template>
	<view class="" style="display: inline;">
		<section class="content">
		    <view class="sec-title">与频次</view>
		    <van-radio-group v-model="qdsjlx">
		        <van-cell v-for="(item, index) in hdQdsjlxList" :key='index'>
		            <van-radio slot="right-icon" :name="item.code">
		                <div class="list-item-name">{{item.name}}</div>
		                <div class="list-item-info">{{item.description}}</div>
		            </van-radio>
		        </van-cell>
		    </van-radio-group>
			<template v-if="qdsjlx ==='zqxhms'">
				<van-field 
				   label="循环频次"
				   required
				   readonly
				>
				    <template slot="input">
				        <van-checkbox-group v-model="xhpc" :direction="'horizontal'">
				            <van-checkbox v-for="(option, index) in xhpcColumnsName"
								:key="option + index" 
								:name="option.code" 
								shape="square" 
								style="margin-bottom: 8px;"
							>
				                {{option.name}}
				            </van-checkbox>
				        </van-checkbox-group>
				    </template>
				</van-field>
			</template>
		    <template v-if="qdsjlx==='jdms' || qdsjlx==='zqxhms'">
		        <van-field
		                readonly
		                clickable
						required
		                label="开始日期"
		                :value="ksrq"
		                placeholder="请选择开始日期"
		                @click="showksrqPicker = true"/>
		        <van-popup v-model="showksrqPicker" position="bottom">
		            <van-datetime-picker
		                    type="date"
		                    :min-date="minDate"
		                    @cancel="showksrqPicker = false"
		                    @confirm="onksrqConfirm"/>
		        </van-popup>
		        <van-field
		                readonly
		                clickable
						required
		                label="结束日期"
		                :value="jsrq"
		                placeholder="请选择结束日期"
		                @click="showjsrqPicker = true"/>
		        <van-popup v-model="showjsrqPicker" position="bottom">
		            <van-datetime-picker
		                    type="date"
		                    :min-date="minDate"
		                    @cancel="showjsrqPicker = false"
		                    @confirm="onjsrqConfirm"/>
		        </van-popup>
		    </template>
		    <template v-if="qdsjlx==='gjms'">
		        <van-field
		                readonly
		                clickable
						required
		                label="选择日期"
		                placeholder="请选择日期"
		                @click="showxzrqPicker = true"/>
		        <van-popup v-model="showxzrqPicker" position="bottom">
		            <van-datetime-picker
		                    type="date"
		                    :min-date="minDate"
		                    @cancel="showxzrqPicker = false"
		                    @confirm="onxzrqConfirm"/>
		        </van-popup>
		        <van-field
		                readonly
		                label="已选择日期"
		                type="textarea"
		                :value="qdrq"
		                rows="4"
		                autosize/>
				<ZZ_FieldTimeRangePicker
					:field="field"
					:type="field.type"
					:value="qdsjd"
					label="签到时间"
					v-on:input="onTimeSelect(field, $event)"
					:required="true"
					placeholder="请选择时间"
				/>
				<van-field
				        readonly
				        label="已选择时间"
				        type="textarea"
				        :value="qdsj"
				        rows="4"
				        autosize/>
		    </template>
			<template v-if="qdsjlx==='jdms' || qdsjlx==='zqxhms'">
				<van-field
				        readonly
				        clickable
						required
				        label="开始时间"
				        :value="kssj"
				        placeholder="请选择开始时间"
				        @click="showkssjPicker = true"/>
				<van-popup v-model="showkssjPicker" position="bottom">
				    <van-datetime-picker
				            type="time"
				            :min-hour="1"
				            :max-hour="23"
				            @cancel="showkssjPicker = false"
				            @confirm="onkssjConfirm"/>
				</van-popup>
				<van-field
				        readonly
				        clickable
						required
				        label="结束时间"
				        :value="jssj"
				        placeholder="请选择结束时间"
				        @click="showjssjPicker = true"/>
				<van-popup v-model="showjssjPicker" position="bottom">
				    <van-datetime-picker
				            type="time"
				            :min-hour="1"
				            :max-hour="23"
				            @cancel="showjssjPicker = false"
				            @confirm="onjssjConfirm"/>
				</van-popup>
			</template>
			
		    <!-- <van-cell center title="签到记录限定">
		        <van-stepper v-model="qdxzcs" integer/>
		    </van-cell> -->
		    <!--        //canUseYibanMessage这个值为true 且sync值为true时，才显示wqdcs的设置-->
		    <!-- <van-cell center title="当天未签到次数" v-if="sync && canUseYibanMessage">
		        <van-stepper v-model="wqdcs" integer/>
		    </van-cell> -->
		
		    <!-- <van-cell title="签到数据是否可更新">
		        <van-switch
		                v-model="qdsjkgx"
						size="20"
		                active-color="#00a5ec"
		        />
		    </van-cell> -->
		    <!--        <van-switch-cell active-color="#00a5ec" v-model="qdsjkgx" title="签到数据是否可更新"/>-->
		    <!-- 更新时间间隔(分钟) -->
		    <!-- <van-cell v-if="qdsjkgx===true" center title="更新时间间隔(分钟)">
		        <van-stepper v-model="gxsjjg" min="1" max="15"/>
		    </van-cell> -->
		</section>
		<div class="van-submit-bar">
			<view class="van-submit-bar-box">
				<van-button
						plain
				        type="info"
				        @click="handleCancle">取消
				</van-button>
				<van-button
				        type="info"
				        @click="handleQdshSubmit">确定
				</van-button>
			</view>
		</div>
	</view>
    
</template>
<script>
    import Bus from '@/utils/event-bus'
    import Utils from '@/utils/common/momentWrap.js'
	import ZZ_FieldTimeRangePicker from '../../my/components/form/ZZ_FieldTimeRangePicker.vue';
	var wxRequest = require('@/utils/wxRequest.js');
	var Api = require('../api/index.js');
	

    export default {
        props: {
            // viewInfo: Object,
            detailId: String,//活动ID
            mark: String,
            sync: Boolean,
            canUseYibanMessage: Boolean,
			hdQdsjlxList: {
				type: Array,
				default() {
					return []
				}
			},
			weeksList: {
				type: Array,
				default() {
					return []
				}
			},
        },
		components: {
			ZZ_FieldTimeRangePicker
		},
        data() {
            return {
				field: {
					type: 'timerange',
				},
                showkssjPicker: false,
                showjssjPicker: false,
                minDate: new Date(),
                kssj: null,//开始时间
                jssj: null,//结束时间
                qdsjlx: null,//时间模式--简单模式：jdms  高级模式：gjms 周期循环模式：zqxhms
                ksrq: null,//简单模式-开始日期
                jsrq: null,//简单模式-结束日期
                qdrq: null,//签到日期多选：2019-07-29,2019-07-30,2019-07-31
				qdsjd: "",
				qdsj: '',//高级签到时间段：01:01:00,02:00:00
                // qdsjkgx: false,//签到数据可更新
                // gxsjjg: null,//更新时间间隔(分钟)
                // qdxzcs: null,//当天需签到次数，
                showksrqPicker: false,
                showjsrqPicker: false,
                showkgjqdrqPicker: false,
                showxzrqPicker: false,
                qdrqxs	: [],
                // wqdcs: null,
				xhpc: [],
            }
        },
        created() {
            if(this.detailId) {
				this.getDetailInfo();
			} else {
				this.qdsjlx = this.hdQdsjlxList.length > 0 ? this.hdQdsjlxList[0].code : null;
			}
        },
		computed: {
			xhpcColumnsName() {
				let arr = [];
				if(this.weeksList) {
					this.weeksList.forEach((item)=>{
						arr.push({
							code: item.code,
							name: item.name
						})
					})
				}
				console.log('xhpcColumnsName', arr)
				return arr;
			},
			xhpcColumnsCode() {
				let arr = [];
				if(this.weeksList) {
					this.weeksList.forEach((item) => {
						arr.push(item.code)
					})
				}
				return arr;
			},
			qdsjName() {
				let str = "";
				if(this.qdsjlx) {
					console.log('this.qdsjlx', this.qdsjlx);
					let hdqdsjObj = this.hdQdsjlxList.find(i => i.code == this.qdsjlx);
					str = hdqdsjObj.name
				}
				return str;
			}
		},
        methods: {
			onTimeSelect(field, value) {
				this.qdsjd = value;
				console.log('field,value', field, value, this.qdsjd);
				let list = value.split(',');
				let hasValue = list.filter(i => i);
				console.log('hasValue', hasValue);
				// 选了两个时间，添加到已选时间，并清空qdsjd
			    if(hasValue.length == 2) {
					this.qdsj = this.qdsj ? `${this.qdsj},${list.join('-')}` : list.join('-');
					this.qdsjd = "";
				}
				console.log('this.qdsj', this.qdsj, this.qdsjd);
			},
            getDetailInfo() {
				console.log('this.detailId', this.detailId)
				let self = this;
				let url = Api.checkinItemDetail() + this.detailId;
				let data = {}
				wxRequest.getRequest(url, data).then(res => {
					console.log('详情', res)
					if (res.statusCode === 200) {
						let resData = res.data;
						if(resData.code === 0) {
							let data = resData.data;
							this.qdsjlx	 = data.qdsjlx;
							this.ksrq = data.ksrq;
							this.jsrq = data.jsrq;
							this.kssj = data.kssj;
							this.jssj = data.jssj;
							this.qdrq = data.qdrq;
							this.qdrqxs	 = data.qdrqxs;
							this.qdsj = data.qdsj;
							this.xhpc = data.xhpc.split(',');
							// data.sftx = resData.data.sftx == '是' ? true: false;
							// data.qdtj = resData.data.qdtj == '2' ? true: false;
							// let hdQdfsObj = this.hdQdfsList.find(i => i.code == resData.data.qdfs);
							// data.qdfsName = hdQdfsObj ? hdQdfsObj.name : "";
							// console.log('签到方式', this.hdQdfsList);
							// self.setData({
							// 	formData: data,
							// 	rang: resData.data.selectorDatas,
							// 	checkedYswzArray: resData.data.checkinItemAddresses,
							// })
							// console.log('预设地址',this.yswzColumns)
						} 
					}
				}).catch(error => {
				    console.log(error);
				    // this.error = true
				}).finally(() => {
				    // this.loading = false
				})
                // let url = AjaxApi.getBasicData;
                // let sendHttp = http;
                // if(this.mark==='fbqdxmJsqd'){
                //     url = AjaxApiJsqd.QueryDetail;
                //     sendHttp = httpJsqd;
                // }
                // sendHttp.post(url, {id: this.detailId}).then(res => {
                //     if (res.status === 200) {
                //         let query = res.data.obj;
                //         if (query) {
                //             this.qdsjlx = query.qdsjlx;
                //             this.ksrq = query.ksrq;
                //             this.jsrq = query.jsrq;
                //             this.kssj = query.kssj;
                //             this.jssj = query.jssj;
                //             this.qdsjkgx = query.qdsjkgx === '是' ? true : false;
                //             this.gxsjjg = query.gxsjjg;
                //             this.qdxzcs = query.qdxzcs;
                //             this.gjqdrq = query.gjqdrq;
                //             this.wqdcs = query.wqdcs;
                //         }
                //     }
                // })
            },
            onkssjConfirm(value) {
                this.kssj = value;
                this.showkssjPicker = false
            },
            onjssjConfirm(value) {
                this.jssj = value;
                this.showjssjPicker = false
            },
            onksrqConfirm(value) {
                this.ksrq = Utils.dateFormat_YMD(value);
                this.showksrqPicker = false
            },
            onjsrqConfirm(value) {
                this.jsrq = Utils.dateFormat_YMD(value);
                this.showjsrqPicker = false
            },
            onxzrqConfirm(value) {
                let youWant = Utils.dateFormat_YMD(value);
				console.log('value', value, youWant);
				// 已选择的不再添加
				if(!this.qdrqxs	.includes(youWant)) {
					this.qdrqxs	.push(youWant);
					this.qdrq = this.qdrqxs	.join(",");
				}
				this.showxzrqPicker = false;
            },
            handleQdshSubmit() {
				console.log('确定', this);
                console.log(this.kssj < this.jssj);
                if (!this.qdsjlx) {
                    this.$toast.clear();
                    this.$toast('请选择签到模式');
                    return false;
                }else{
                    if (this.qdsjlx==='jdms' && !this.ksrq) {
                        this.$toast.clear();
                        this.$toast('请选择开始日期');
                        return false;
                    }
                    if (this.qdsjlx==='jdms' && !this.jsrq) {
                        this.$toast.clear();
                        this.$toast('请选择结束日期');
                        return false;
                    }
                    if (this.qdsjlx==='gjms' && !this.qdrq) {
                        this.$toast.clear();
                        this.$toast('请选择日期');
                        return false;
                    }
                }
                if (!this.kssj) {
                    this.$toast.clear();
                    this.$toast('请选择开始时间');
                    return false;
                }
                if (!this.jssj) {
                    this.$toast.clear();
                    this.$toast('请选择结束时间');
                    return false;
                }
                if (this.kssj > this.jssj) {
                    this.$toast.clear();
                    this.$toast('结束时间不能小于开始时间');
                    return false;
                }
                let data = {
                    ksrq: this.ksrq,
                    jsrq: this.jsrq,
                    qdsjlx: this.qdsjlx,
					qdsjName: this.qdsjName,
                    kssj: this.kssj,
                    jssj: this.jssj,
                    qdrq: this.qdrq,
					qdrqxs: this.qdrqxs,
					qdsj: this.qdsj,
					xhpc: this.xhpc.join(',')
                    // qdxzcs: this.qdxzcs,
                    // qdsjkgx: this.qdsjkgx === true ? '是' : '否',
                    // gxsjjg: this.gxsjjg,
                    // wqdcs: this.wqdcs,
                };
                Bus.$emit('handleQdshSubmit', data)
            },
            handleCancle() {
                Bus.$emit('handleCancle')
            },
			// getCodeCommn(codeType) {
			// 	let self = this;
			// 	let url = Api.codeCommon();
			// 	let data = {
			// 		codeType
			// 	}
			// 	wxRequest.getRequest(url, data).then(res => {
			// 		console.log('字典表', res)
			// 		if (res.statusCode === 200) {
			// 			let resData = res.data;
			// 			if(resData.code === 0) {
			// 				self.setData({
			// 					[codeType + 'List']: resData.data
			// 				})
			// 				console.log('11111',this[codeType + 'List'])
			// 			} 
			// 		}
			// 	}).catch(error => {
			// 	    console.log(error);
			// 	    // this.error = true
			// 	}).finally(() => {
			// 	    // this.loading = false
			// 	})
			// }
        }
    }
</script>
<style scoped>
	.sec-title {
		padding-left: 16px;
		padding-right: 16px;
		line-height: 40px;
		/* background-color: #f1f1f1; */
	}

    .van-radio__label {
        width: 100%;
    }

    .van-radio {
        width: 100%;
    }
	.list-item-info {
		color: #c8c9cc;
	}
	.content {
		box-sizing: border-box;
		overflow-y: auto; /* 允许内容区域垂直滚动 */
		height: calc(100% - 50px); /* 减去按钮的高度 */
    }
	.van-cell--borderless::after, .van-cell:last-child::after {
		display: block;
	}
</style>

