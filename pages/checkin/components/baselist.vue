<!--签到列表-->
<template>
    <van-list v-model="loading"
              :finished="finished"
              finished-text="暂无更多数据"
              :error.sync="error"
              error-text="请求失败，点击重新加载"
              @load="loadMoreItems"
              style="margin-bottom: 50px; padding: 12px">
        <van-cell-group>
            <van-cell v-for="item in items" :key='item.id' @click="handleQd(item)" class="card-box">
                <van-row>
                    <van-col span="24">
                        <div>
							<span class="list-item-name">{{item.name}}</span>
							<!-- <span style="float:right;font-size: 12px;color: #999;">{{dateFormatMD(item.createDate)}}</span> -->
							<span style="float: right"
								  class="weikaishi"
								  v-if="item.status == '未开始'">{{item.status}}</span>
							<span style="float: right"
								  class="yijieshu"
								  v-if="item.status == '已结束'">{{item.status}}</span>
							<span style="float: right"
								  class="jinxingzhong"
								  v-if="item.status == '进行中'">{{item.status}}</span>
						</div>
                        <!--                        <div class="list-item-info">签到时段：-->
                        <!--                            <template v-if="item.sjms!='jdms'">-->
                        <!--                                {{item.gjqdrq}} {{item.kssj}} — {{item.jssj}}-->
                        <!--                            </template>-->
                        <!--                            <template v-else>-->
                        <!--                                {{item.ksrq}}—{{item.jsrq}} {{item.kssj}}—{{item.jssj}}-->
                        <!--                            </template>-->
                        <!--                        </div>-->
                        <div class="list-item-info">发布人：{{item.cjrxm}}</div>
                        <div class="list-item-info">活动时间：{{item.ksrq + ' ' + item.kssj}}~{{item.jsrq + ' ' + item.jssj}}</div>
                        <div class="list-item-info">
                            <span class="jindu">本月签到：{{item.yqdrs?item.yqdrs:0}}/{{item.qdrs}}</span>
                            <span class="jindutiao">
                              <van-progress v-if="item.yqdrs===0 || item.qdrs===0"
                                            style="width:90%;"
                                            inactive
                                            :percentage='parseInt(0)'
                                            :show-pivot="false"/>
                              <van-progress v-else
                                            style="width:90%;"
                                            color="#07C160"
                                            inactive
                                            :show-pivot="false"
                                            :percentage="toPercent(item.yqdrs,item.qdrs)"/>
                          </span>
                        </div>
                    </van-col>
                    <!-- <van-col span="4">
                        <span style="float:right;font-size: 12px;color: #999;">{{dateFormatMD(item.createDate)}}</span>
                        <span style="float: right"
                              class="weikaishi"
                              v-if="item.rwzt == '未开始'">{{item.rwzt}}</span>
                        <span style="float: right"
                              class="yijieshu"
                              v-if="item.rwzt == '已结束'">{{item.rwzt}}</span>
                        <span style="float: right"
                              class="jinxingzhong"
                              v-if="item.rwzt == '进行中'">{{item.rwzt}}</span>
                    </van-col> -->
                </van-row>
            </van-cell>
        </van-cell-group>
    </van-list>
</template>
<script>
    // import store from '@/store'
    // import http from '@/components/wdmqd/utils/axiosWDM'
    // import AjaxApi from '@/components/wdmqd/service/ajaxWDM'
    // import Utils from '@/utils/momentWrap'
    // import AjaxApiJsqd from "@/jsqd/utils/api";
    // import httpJsqd from "@/jsqd/utils/http";
	var util = require("@/utils/util.js");
	import Utils from '@/utils/common/momentWrap.js';
	var wxRequest = require('@/utils/wxRequest.js');
	var Api = require('../api/index.js');

    export default {
        props: {
            mark: String,
            qdlb: String,
            rq: Date,
            qdrwzt: String,
        },
        data() {
            return {
                // typeZh: store.state.dTitle,
                typeZh: '',
                loading: false,
                finished: false,
                json: null,
                pageIndex: 0,
                pageSize: 10,
                items: [],
                result: null,
                checked: false,
                total: null,
                error: null,
                qdlbOptions: [{text: '全部项目', value: 'all'}],
            }
        },
        watch: {
            qdlb() {
                this.loading = true;
                this.init()
            },
            rq() {
                this.loading = true;
                this.init()
            },
            qdrwzt() {
                this.loading = true;
                this.init()
            },
        },
        created() {
            this.json = this.$route.query; //传递过来的参数
        },
        methods: {
            init() {
                this.finished = true;
                this.items = [];
                this.pageIndex = 0;
                this.finished = false;
                this.loadMoreItems();
            },
            toPercent(num, total) {
				if(num && total) {
					return Math.round((num / total) * 10000) / 100.0 // 小数点后两位百分比
				} else {
					return 0
				}
            },
            handleQd(item) {
				console.log('handleQd', item)
				uni.navigateTo({
					url: '/pages/checkin/s/detail?id=' + item.id
				})
                //签到统计
                // if (this.mark === 'tjlist') {
                //     this.$router.push({
                //         name: 'wdm_m_list2Pie',
                //         query: {detailId: item.id}
                //     })
                //     this.$emit('onShowEchartsWdm', item.id)
                // } else if (this.mark === 'jsqdtjlist') {
                //     this.$emit('onShowEchartsWdmJSQD', item.id)
                // } else if (this.mark === 'jsqd') {//教师签到jsqd
                //     // this.$router.push({
                //     //     path: '/jsqd/s_detail',
                //     //     query: {mark: this.mark, detailId: item.id, activeTab: String(0)}
                //     // })
                // } else {
                //     // this.$router.push({
                //     //     name: 'wdm_s_detail',
                //     //     query: {detailId: item.id, activeTab: String(0)}
                //     // })
                // }
            },
            dateFormatMD(o) {
                return Utils.dateFormat_md(o)
            },
            //获取列表数据
            loadMoreItems() {
				let params = util.getUrlParams();
				console.log('getUrlParams 12', params);
				console.log('loadMoreItems')
				let self = this;
                if (this.loading) {
                    let json = {
                        page: this.pageIndex,
                        limit: this.pageSize,
						type: params.type
                    };
                    // let setHttp = http;
                    // let url = AjaxApi.querylist;
					let url = Api.checkinItemPage();
                    if (this.qdlb) json.qdlb = this.qdlb;
                    if (this.qdrwzt) json.status = this.status;
                    if (this.rq) json.rq = Utils.dateFormat_YM(this.rq);
                    // if (this.mark === 'tjlist') {
                    //     url = AjaxApi.getRcLimitData;
                    // } else if (this.mark === 'jsqd') {//教师签到jsqd
                    //     setHttp = httpJsqd;
                    //     url = AjaxApiJsqd.Querylist;
                    // } else if (this.mark === 'jsqdtjlist') {//教师签到统计jsqd
                    //     setHttp = httpJsqd;
                    //     url = AjaxApiJsqd.RollcallStatisticsQueryList;
                    // }
					wxRequest.getRequest(url, json).then(res => {
						// setHttp.post(url, json).then(res => {
						console.log('签到项目', res)
						// this.finished = true;
						if (res.statusCode === 200) {
							let resData = res.data;
							if(resData.code === 0) {
								let curPageList = resData.data.list;
								this.total = resData.data.count;
								self.setData({
								  items: self.items.concat(curPageList)
								});
								if (resData.data.count <= self.pageSize * (self.pageIndex + 1)) { // 说明是最后一页
									self.setData({
									  finished: true
									});
								} else {
									self.setData({
									  finished: false,
									  pageIndex: this.pageIndex + 1
									});
								}
								console.log('pageIndex, finished', this.pageIndex, this.finished)
							} else {
								this.error = true;
							}
						}
                    }).catch(error => {
                        console.log(error);
                        this.error = true
                    }).finally(() => {
                        this.loading = false
                    })
                }
            }
        }
    }
</script>
<style scoped lang="scss">
    .list-item-name {
        font-weight: bold;
    }

    .jindu {
        /* width: 50%; */
        width: auto;
		margin-right: 10px;
        float: left;
    }

    .jindutiao {
        padding-top: 5px;
        float: left;
        width: 50%;
    }

    .van-progress {
        height: 8px;
        margin-top: 2px;
    }

    .jinxingzhong {
        color: #07c160;
        font-size: 12px;
    }

    .weikaishi {
        color: #fdae63;
        font-size: 12px;
    }

    .yijieshu {
        color: #999;
        font-size: 12px;
    }

    .vanRow {
        border-bottom: 1px solid #ebedf0;
        z-index: 2020 !important;
        background: #fff;
    }

    .riliImg {
        float: right;
        margin-right: 15px;
        margin-top: 10px;
    }

    .wrap >>> .van-dropdown-menu__item {
        padding-left: 5px;
        justify-content: left !important;
        -webkit-justify-content: left !important;
    }

    .van-col--20 div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .van-icon-points {
        margin-right: 5px;
        font-size: 1.3em;
        float: right;
        line-height: 48px;
    }

    .jindutiao >>> .van-progress__portion {
        background: #07c160 !important;
    }
	.van-cell-group {
		background-color: var(--ui-BG-1) !important;
	}
    .van-cell {
        padding: 13px 16px;
		/* background-color: gray; */
		// border: 1px solid gray;
		margin-bottom: 12px;
		// border-radius: 12px;
		// box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
		box-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.10), 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 0px 0px 0px rgba(0, 0, 0, 0.00), 0px 0px 0px 0px rgba(0, 0, 0, 0.00);
    }
</style>
