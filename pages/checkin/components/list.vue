<!--签到列表-->
<template>
    <section class="wrap">
        <van-row class="vanRow" style="position: fixed;top: 0;left: 0;z-index: 100;width:100%;">
            <van-col span="16">
                <van-dropdown-menu>
                    <van-dropdown-item
                            v-model="qdrwzt"
                            :options="option1"
                            @change="qdztChange"/>
                </van-dropdown-menu>
            </van-col>
            <van-col span="8">
                <div @click="chkTj">
                    <span>统计查看</span>
                    <van-icon name="points" color="#1989fa"/>
                </div>
            </van-col>
        </van-row>
        <set-base-list :qdrwzt="qdrwzt" :mark="mark" style="margin-top:50px;padding-bottom: 50px;"></set-base-list>
		<view class="van-submit-bar">
			<view class="van-submit-bar-box">
		      <van-button type="info" plain @click="$router.go(-1)">返回</van-button>
		      <van-button type="info" @click="toPublish">发布签到项目</van-button>
			</view>
		</view>
    </section>
</template>
<script>
    import setBaseList from './baselist'
	var util = require("@/utils/util.js");
	
    export default {
        props: {
            mark: String,//student-学生饼图统计
            setFlag: String,//student-学生饼图统计
        },
        components: {
			setBaseList
		},
        data() {
            return {
                loading: false,
                finished: false,
                json: null,
                pageIndex: 0,
                pageSize: 10,
                items: [],
                result: null,
                checked: false,
                total: null,
                error: null,
                qdrwzt: '全部',
                option1: [
                    {text: '全部', value: '全部'},
                    {text: '进行中', value: '进行中'},
                    {text: '未开始', value: '未开始'},
                    {text: '已结束', value: '已结束'}
                ],
            }
        },
        created() {
            // this.json = this.$route.query //传递过来的参数
        },
        methods: {
			toPublish() {
				let params = util.getUrlParams();
				console.log('跳转', params);
				uni.navigateTo({
					url: '/pages/checkin/m/fbqdxm?type=' + params.type
				})
			},
            qdztChange(value) {
                this.qdrwzt = value;
            },
            chkTj() {
                console.log("this.mark====", this.mark);
                if (this.mark === 'student') {
                    this.$router.push({name: 'wdm_s_list2'})
                } else if (this.mark === 'jsqd') {
                    if (this.setFlag === 'jsqd_gr') {
                        this.$router.push({path: '/jsqd/s_list2'})
                    } else {
                        this.$router.push({path: '/jsqd/m_list2'})
                    }
                } else {
                    this.$router.push({name: 'wdm_m_list2'})
                }
            },
        }
    }
</script>
<style scoped>
    .jindu {
        width: 40%;
        float: left;
    }

    .jindutiao {
        width: 40%;
        padding-top: 3px;
        float: left;
    }

    .van-progress {
        height: 8px;
        margin-top: 2px;
    }

    .jinxingzhong {
        color: #07c160;
    }

    .weikaishi {
        color: #fdae63;
    }

    .yijieshu {
        color: #999;
    }
    .van-dropdown-menu__bar{
        box-shadow: 0 0px 2px rgba(100,101,102,.12);
    }
    .vanRow {
        border-bottom: 1px solid #ebedf0;
        z-index: 2020 !important;
        background: #fff;
    }

    .riliImg {
        float: right;
        margin-right: 15px;
        margin-top: 10px;
    }

    .wrap >>> .van-dropdown-menu__item {
        padding-left: 5px;
        justify-content: left !important;
        -webkit-justify-content: left !important;
    }

    .van-col--8 {
        font-size: 14px;
        color: #1989fa;
        line-height: 50px;
        text-align: right;
        padding-right: 10px;
    }

    .van-col--8 span {
        float: right;
    }

    .van-icon-points {
        margin-right: 5px;
        font-size: 1.3em;
        float: right;
        line-height: 48px;
    }

    .wrap >>> .van-progress__portion {
        background: #07c160 !important;
    }
</style>
