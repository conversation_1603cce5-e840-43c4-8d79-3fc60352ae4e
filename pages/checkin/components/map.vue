<template>
    <section>
        <div id="iCenter" class="map amap-container"></div>
        <div id="pickerBox">
			<van-field v-model="radius"
					   placeholder="请输入范围半径(整数)"
					   type="digit"
					   label="范围半径(米)"
					   required />
            <van-search v-model="address"
                        placeholder="请输入搜索关键词"
                        show-action
                        shape="round"
                        @search="onSearch">
                <div slot="action" @click="onSearch">搜索</div>
            </van-search>
        </div>

        <div v-show="showRes" id="panel"></div>
    </section>
</template>
<script>
    import { MapLoader2 } from '@/utils/AMap.js'

    export default {
        props: {
            // radius: String
        },
        data() {
            return {
                show: true,
                showRes: false,
                map: '', //保存地址的经纬度
                address: '', //搜索的地区
                poiArr: [], //左边搜索出来的数组
                windowsArr: [], //信息窗口的数组
                marker: [],
                mapObj: '', //地图对象
                selectedIndex: -1,
				radius: 200
            }
        },
        created() {
            this.loadBMapScript();
        },
        mounted() {
            // this.mapInit();
            // this.placeSearch('')
        },
        methods: {//加载在线文件
            loadBMapScript() {
				// window._AMapSecurityConfig = {
				//     securityJsCode: '8b419c91198bdf079b2ebb28ca6dbc8c',
				// }
                MapLoader2().then(AMap => {
                    console.log('地图加载成功', window._AMapSecurityConfig)
                    // this.initMap(); //初始化地图
                    this.mapInit();
                    this.placeSearch('')
                }, e => {
                    console.log('地图加载失败', e)
                })
            },
            //创建一个map
            mapInit() {
                //初始化地图对象，加载地图
                this.mapObj = new window.AMap.Map('iCenter', {
                    resizeEnable: true, //是否监控地图容器尺寸变化
                    // zoom:19, //初始化地图层级  地图显示的缩放级别
                    // center: [116.397428, 39.90923] //初始化地图中心点
                })
            },
            //根据名字地址去搜索结果
            placeSearch(name) {
				console.log('window._AMapSecurityConfig', window._AMapSecurityConfig)
                let self = this
                window.AMap.plugin(["AMap.PlaceSearch"], function () {
                    //构造地点查询类
                    var placeSearch = new window.AMap.PlaceSearch({
                        pageSize: 4, // 单页显示结果条数
                        pageIndex: 1, // 页码
                        // city: "010", // 兴趣点城市
                        // citylimit: true,  //是否强制限制在设置的城市内搜索
                        map: self.mapObj, // 展现结果的地图实例
                        panel: "panel", // 结果列表将在此容器中进行展示。
                        autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
						user_scode: '8b419c91198bdf079b2ebb28ca6dbc8c',
                    });
                    console.log(name)
                    //关键字查询
                    placeSearch.search(name);
                    console.log(window.AMap)
                    //点击panel列表的点标记
                    window.AMap.Event.addListener(placeSearch, "listElementClick", function (e) {
                        console.log(e.data.location);//当前marker的经纬度信息
                        console.log(e.data.location.lng + ',' + e.data.location.lat);
                        console.log(e.data.address);//获取当前marker的具体地址信息
                        console.log(e.data);//则是包含所有的marker数据

                        self.showRes = false;
						let data = e.data;
						data.radius = self.radius;
                        self.$emit('initMap', data)
                    });
                });
            },
            onSearch() {
				if(!this.radius) {
					this.$toast('请输入签到范围');
					return;
				}
				if(!this.address) {
					this.$toast('请输入搜索关键词');
					return;
				}
                this.mapObj = '';
                this.windowsArr = [];
                this.mapInit();
                this.placeSearch(this.address);
                this.showRes = true
            }
        }
    }
</script>
<style scoped>

    #panel {
        position: absolute;
        background-color: white;
        /*max-height: 90%;*/
        overflow-y: auto;
        top: 100px;
        right: 10px;
        width: 280px;
    }

    #iCenter {
        /* width: 60vh; */
        height: 100vh;
        margin: 0;
        font-size: 13px;
        background: rgb(252, 249, 242);
    }

    #iCenter >>> .amap-copyright {
        opacity: 0;
    }

    #iCenter >>> .amap-logo {
        opacity: 0;
    }

    #pickerBox {
        position: absolute;
        z-index: 9999;
        top: 10px;
        right: 10px;
        width: 280px;
    }

    #result {
        position: relative;
        background: #fff;
        width: 300px;
        max-height: 500px;
        overflow-y: scroll;
        border-right: 1px solid #ccc;
    }

    .amap_lib_placeSearch_page {
        position: absolute;
        bottom: 0;
        width: 100%;
    }

    #me {
        border-top: 1px solid #ccc;
        margin-top: 6px;
        padding-top: 6px;
        width: 100%;
        display: block;
    }

    .amap_lib_placeSearch .poibox {
        border-bottom: 1px solid #eaeaea;
        cursor: pointer;
        padding: 5px 0 5px 10px;
        position: relative;
        min-height: 35px;
    }

    .amap_lib_placeSearch_poi {
        background: url(https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png) no-repeat;
        height: 31px;
        width: 19px;
        cursor: pointer;
        left: -1px;
        text-align: center;
        color: #fff;
        font: 12px arial, simsun, sans-serif;
        padding-top: 3px;
    }

    .amap_lib_placeSearch .poibox .poi-title {
        margin-left: 25px;
        font-size: 13px;
        overflow: hidden;
    }

    .amap_lib_placeSearch .amap_lib_placeSearch_poi {
        position: absolute;
    }

    .amap_lib_placeSearch .poibox .poi-info {
        word-break: break-all;
        margin: -10px 0 0 25px;
        overflow: hidden;
    }

    .amap_lib_placeSearch .poibox .poi-info p {
        color: #999;
        font-family: Tahoma;
        line-height: 20px;
        font-size: 12px;
    }

    .amap_lib_placeSearch .poibox .poibox-icon {
        margin-left: 10px;
        margin-top: 5px;
    }

    .amap-pl-pc .poi-img {
        float: right;
        margin: 3px 8px 0;
        width: 90px;
        height: 56px;
        overflow: hidden;
    }

    .poibox {
        cursor: pointer;
    }

    .poibox:hover {
        background: #f6f6f6;
    }

    .selected {
        background-image: url(https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png) !important;
    }

    .amap-info-content {
        width: 200px !important;
    }
</style>
