<template>
  <div>
    <div v-if="showMap" id="container" class="amap-wrapper" v-on:touchmove="onTouchMove"></div>
    <van-cell v-if="info" class="list-item">
      <van-row style="border-bottom: 1px solid #eee; padding-bottom: 5px;">
        <van-col span="24">
          <div class="list-item-name" style="font-size: 16px;">
            [{{ typeZh }}]
			{{ info.name }}
            <van-popup v-model="showImgPicker" :style="{ height: '100%', width: '100%' }" position="bottom"
              bind:close="onClose">
              <div style="text-align: center;margin: 30px;">
                <!--请登录易班APP，扫描下面二维码签到-->
                [{{ typeZh }}]
				{{ info.name }}
                <img style="margin-top:30px;" :src="baseUrl + 'syt/rollcalllimit/generatingQRCode.htm?id=' + info.id" />
              </div>
              <div class="van-submit-bar" style="text-align: center;">
                <van-button type="info" class="van-submit-bar-button vanButton1" size="large"
                  @click="showImgPicker = false">返回
                </van-button>
              </div>
            </van-popup>
          </div>
          <div class="list-item-info">发布人：{{ info.cjrxm }}</div>
          <!--                    <div class="list-item-info">签到时段：-->
          <!--                        <template v-if="info.sjms!='jdms'">-->
          <!--                            {{info.gjqdrq}} {{info.kssj}} — {{info.jssj}}-->
          <!--                        </template>-->
          <!--                        <template v-else>-->
          <!--                            {{info.ksrq}}—{{info.jsrq}} {{info.kssj}}—{{info.jssj}}-->
          <!--                        </template>-->
          <!--                    </div>-->
          <div class="list-item-info" v-if="canOpt">
            签到次数：{{ info.yqdrs === null ? 0 : info.yqdrs }} / {{ info.qdxzcs }} (已签到数/应签到数)
          </div>
          <div class="list-item-info" v-if="!canOpt">
            签到次数：{{ info.yqdrs === null ? 0 : info.yqdrs }} / {{ info.qdrs }} (已签到数/应签到数)
          </div>
          <div class="list-item-info">
            签到说明：{{ info.bz }}
          </div>
          <div v-if="info.qdfs !== '扫码签到'" class="list-item-info" style="color: #00A5EC;" @click="showTiShi">
            定位不准如何解决？
          </div>
          <van-dialog v-model="tishishow" :closeOnClickOverlay="true" :style="{ width: '80%' }">
            <div class="list-item-name" style="margin-top: 14px;text-align: center;">如何提高定位精准度</div>
            <div class="list-item-info" style="margin-top: 10px;padding: 0 10px;">
              1.定位签到依赖于手机系统的定位模块，签到前请检查确保您手机的GPS服务已经启用
            </div>
            <div class="list-item-info" style="margin-top: 10px;margin-bottom:10px;padding: 0 10px;">
              2.必须打开WIFI增强手机定位准确度，可以不连接WIFI网络
            </div>
          </van-dialog>
          <div v-if="showMap && (showLocation || !this.myAddress)" class="list-item-info"
            style="color: #00A5EC;margin-top: 6px;" @click="checkLocation">
            点我重新定位
          </div>
        </van-col>
      </van-row>
      <div v-if="canOpt && setCirclesDiv">
        <div class="circleWrap" @click="toQdSubmit(circleClick ? setCirclesDiv.type : 'checked')">
          <div :class="setCircleColor">
            <van-icon :name="setCirclesDiv.type" size="2em" />
            <p><span>{{ setRWZT }}</span></p>
            <p class="timeHeight" ref="mViewRoot">{{ realTime }}</p>
          </div>
          <p class="circleBottom">{{ info.qdfs }}</p>
        </div>
      </div>
      <div v-if="!canOpt">
        <div v-if="info.qdfs === '定位&扫码签到' || info.qdfs === '扫码签到'" style="margin-left: calc(50% - 33px);margin-top: 20px;"
          @click="showImgPicker = true">
          <img style="width:65px;" :src="baseUrl + 'syt/rollcalllimit/generatingQRCode.htm?id=' + info.id" />
          <div class="list-item-info" style="color: #00A5EC;">生成二维码</div>
        </div>
      </div>
    </van-cell>
	 <!-- v-if="info.czts === 'yes'" -->
    <div class="van-submit-bar">
		<view class="van-submit-bar-box">
		  <van-button type="info" plain @click="handleDelete">删除
		  </van-button>
		  <van-button type="info" @click="handleWdmEdit">编辑
		  </van-button>
		</view>
    </div>
    <van-popup v-model="showDialog" round position="bottom" :style="{ height: '75%' }">
      <h4 class="van-doc-demo-block__title" style="text-align: center;"></h4>
      <van-cell-group>
        <van-field readonly label="签到时间" :value="realTime" />
        <van-field readonly :value="myAddress" type="textarea" rows="2" label="签到地点" />
        <van-field v-model="description" label="备注" type="textarea" placeholder="请输入备注" rows="4" autosize />
        <template v-if="optType === 'photograph'">
          <!-- <ZZ_FieldFileUploader :field="field" v-on:addFiles="onAddFiles" :oldFilesObj="onGetOldFiles(field.en)"
            v-on:deleteFile="onDeleteFile" :label="field.zh"></ZZ_FieldFileUploader> -->
          <!--                    <set-upload v-on:initUploadInfo="getUploadInfo" :imageFileUrl="imageFileUrl" :fileList="fileList"-->
          <!--                                :mark="mark"></set-upload>-->
        </template>
      </van-cell-group>
      <div class="van-submit-bar" style="text-align: center;margin-bottom:10px;">
        <van-button type="info" class="van-submit-bar-button" size="large" @click="handleQdSubmit">确定
        </van-button>
        <van-button type="info" class="van-submit-bar-button vanButton" size="large" @click="handleCancle">取消
        </van-button>
      </div>
    </van-popup>

    <van-popup class="surePopup" v-model="showDetailPopup" position="center" :style="{ height: '55%', width: '80%' }">
      <div class="stuInfoWrap" v-if="stuItem">
        <van-icon style="float: right;color: #999;margin-top: 8px;" @click="showDetailPopup = false" name="cross" />
        <p class="stuInfoWrapTitle">信息确认</p>
        <div class="passWrap">
          <!--                    <img class="passImg" src="./assets/pass.png" alt="">-->
          <!--                    <p class="passP">可以通行</p>-->
        </div>
        <van-cell class="vanCell">
          <van-row>
            <van-col span="8">
              <div class="headImgWrap">
                <img class="headImg" :src="baseUrl + 'syt/showimage2/' + stuItem.xh + '.htm'" :onerror="defaultImg" />
              </div>
            </van-col>
            <van-col span="16">
              <div class="van-address-item_address"><span>姓名：</span>{{ stuItem.xm }}</div>
              <div class="van-address-item_address"><span>学号：</span>{{ stuItem.xh }}</div>
              <div class="van-address-item_address"><span>学院：</span>{{ stuItem.dwmc }}</div>
              <div class="van-address-item_address"><span>班级：</span>{{ stuItem.bjmc }}</div>
            </van-col>
          </van-row>
        </van-cell>
        <div class="van-submit-bar">
          <van-button type="info" class="van-submit-bar-button" style="width: 94% !important;" size="large"
            @click="showDetailPopup = false">确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
// import http from '@/components/wdmqd/utils/axiosWDM'
// import AjaxApi from '@/components/wdmqd/service/ajaxWDM'
// import Utils from '@/utils/momentWrap'
// import AjaxApiJsqd from "@/jsqd/utils/api";
// import httpJsqd from "@/jsqd/utils/http";
// import ZZ_FieldFileUploader from "@/zizhu/components/form/ZZ_FieldFileUploader";
// import store from '@/store'
	import { MapLoader2 } from '@/utils/AMap.js'
	import Utils from '@/utils/common/momentWrap.js';
	var wxRequest = require('@/utils/wxRequest.js');
	var AjaxApi = require('../api/index.js');

import {
  mapState
} from 'vuex'
const http = {}
export default {
  // setUpload,
  components: {
    // ZZ_FieldFileUploader
  },
  name: 'position',
  props: {
    detailId: String,
    mark: String,
  },
  data () {
    return {
      fileMap: new Map(),
      fileMapChangeTracker: 0,
      field: {
        en: 'img',
        zh: '上传附件',
      },

      showLocation: false,
      zoom: 16,
      defaultLngLat: [116.397428, 39.90923],
      limitLngLat: [116.328878, 40.052817],
      map: null,
      marker: null,
      project: null,
      now: new Date(),
      position: null,
      location: null,
      canPosition: false,
      myAddress: null,
      description: null,
      info: {},
      showMap: true,
      circleOpt: [
        {
          type: 'location',
          text: '定位签到'
        },
        {
          type: 'photograph',
          text: '拍照签到'
        },
        {
          type: 'qr',
          text: '扫码签到'
        },
        {
          type: 'qr',
          text: '定位&扫码签到'
        },
        {
          type: 'photograph',
          text: '定位&拍照签到'
        },
      ],
      showImgPicker: false,
      isScan: false,
      showDate: new Date(),
      showDialog: false,
      mobileId: null, //设备ID
      optType: null, //操作类型：定位，拍照，扫码
      fileList: [], //file附件上传相关
      fileString: null,
      fileName: null,
      fileType: null,
      showDefaultDiv: false, //详情页默认显示的圆
      qrCodeId: null, //扫描二维码返回的ID
      imageFileUrl: null, //android手机图片地址
      realTime: '',
      tishishow: false,
      xxdmMark: null,//学校代码标识
      showDetailPopup: false,
      stuItem: null,
      // defaultImg: 'this.src="' + require('./assets/headImg.png') + '"', //默认图地址
      defaultImg: '', //默认图地址
    }
  },
  computed: {
    ...mapState(['dTitle', 'roleKey', "isProduction"]),
    typeZh () {
      // czts 管理端有yes(可编辑，删除)、no（不可编辑，不可删除）
      //      学生端czts：kgx（可更新签到记录），kxz（可新增签到记录）
      return this.dTitle;
    },
    circles () {
      let arr = [];
      if (this.info) {
        //是否绘制签到范围的值 sfhzqdfw
        if (this.info.sfhzqdfw === '是') {
          let newPolygonLngLatArr = this.info.polygonLngLat.split('#');
          newPolygonLngLatArr.forEach(item => {
            let polygonLngLat = item ? JSON.parse(item.split(',')) : '';
            if (polygonLngLat) arr.push(polygonLngLat);
          })
        } else {
          if (this.info.centerLngLat) {
            let newCenterLngLat = this.info.centerLngLat.split(',');
            let index = 0;
            newCenterLngLat.forEach(item => {
              let lnglat = item.split('#');
              arr.push({
                index: index++,
                center: [lnglat[0], lnglat[1]],
                radius: this.info.radius,
              })
            })
          }
        }
      }
      return arr;
    },
    setCircleColor () { //签到圆的样式
      if (this.showLocation) {
        return {
          circle: true
        }
      } else {
        if (this.info.rwzt == '已结束') {
          return {
            circle: true
          }
        } else if (this.info.rwzt == '进行中') {
          if (this.info.yqdrs === this.info.qdxzcs) {
            return {
              circleBlue: true
            }
          }
          if (this.info.qdfs !== '扫码签到') {
            if (this.info.fwwkqd === '是' || this.canPosition) {
              if (this.xxdmMark === '11149') {
                return {
                  circleGreen: true
                }
              } else {
                if (this.myAddress) {
                  return {
                    circleGreen: true
                  }
                } else {
                  return {
                    circle: true
                  }
                }
              }
            } else {
              return {
                circle: true
              }
            }
          } else {
            return {
              circleGreen: true
            }
          }
        } else {
          return {
            circle: true
          }
        }
      }
    },
    /**标识已签到*/
    setRWZT () {
      if (this.showLocation) {
        return '定位失败';
      } else {
        if (this.info.yqdrs === this.info.qdxzcs) {
          return '已完成签到';
        }
        if (this.info.rwzt === '进行中' && this.info.qdfs !== '扫码签到') {
          if (this.xxdmMark === '11149') {
            if (this.info.fwwkqd === '是') {
              return '可签到'
            }
            if (this.canPosition) {
              return '已进入签到范围';
            } else {
              return '未在签到范围内';
            }
          } else {
            if (this.myAddress) {
              if (this.info.fwwkqd === '是') {
                return '可签到'
              }
              if (this.canPosition) {
                return '已进入签到范围';
              } else {
                return '未在签到范围内';
              }
            } else {
              return '定位失败';
            }
          }
        }
        return this.info.rwzt;
      }
    },
    /**
     * 圆是否可点击
     * @returns {boolean}
     */
    circleClick () {
      if (this.showLocation) {
        return false;
      } else {
        if (this.info.yqdrs === this.info.qdxzcs) return false;
        if (this.info.rwzt === '进行中') {
          if (this.info.qdfs !== '扫码签到' && (this.info.fwwkqd === '是' || this.canPosition)) {
            if (this.xxdmMark === '11149') {//11149
              return true;
            } else {
              if (this.myAddress) {
                return true;
              } else {
                return false;
              }
            }
          } else if (this.info.qdfs === '扫码签到') {
            return true;
          }
          return false;
        }
        return false;
      }
    },
    setCirclesDiv () {
      return this.circleOpt.find(item => {
        return item.text === this.info.qdfs;
      })
    },
    baseUrl () {
      return http.baseUrl
    },
    canOpt () {
      if (this.mark === 'jsqd') {
        return (this.roleKey === '班级' || this.roleKey === '宿管' || this.roleKey === '本院' || this.roleKey === '全校')
      } else {
        return this.roleKey === '个人'
      }
    },
    lngLat () {
      return window.AMap.LngLat
    }
  },
  methods: {
    mapToObj (strMap) {
      let obj = {};
      for (let [k, v] of strMap) {
        obj[k] = v;
      }
      return obj;
    },
    onDeleteFile (en, index) {
      let arr1 = this.fileMap.get(en);
      if (arr1 && arr1.length > index) {
        arr1.splice(index, 1);
        this.fileMap.set(en, arr1);
        this.fileMapChangeTracker--;
      }
    },
    onAddFiles (en, arr) {
      let arr1 = this.fileMap.get(en);
      if (!arr1) {
        arr1 = [];
      }
      for (const arrE of arr) {
        arr1.push(arrE);
      }
      this.fileMap.set(en, arr1);
      this.fileMapChangeTracker++;
    },
    onGetOldFiles (en) {
      return {
        tracker: this.fileMapChangeTracker,
        files: this.fileMap.get(en)
      };
    },
    handleWdmEdit () {
		uni.navigateTo({
			url: '/pages/checkin/m/fbqdxm?id=' + this.detailId
		})
      // if (this.mark === 'jsqd') {
      //   this.$router.push({
      //     path: '/jsqd/m_fbqdxm', query: {
      //       detailId: this.detailId,
      //       mark: 'edit'
      //     }
      //   })
      // } else {
      //   this.$router.push({
      //     name: 'wdm_m_fbqdxm',
      //     query: {
      //       id: this.detailId,
      //       mark: 'edit'
      //     }
      //   })
      // }
    },
    handleDelete () {
      this.$dialog.confirm({
        message: '确认要删除？'
      }).then(() => {
        let self = this;
		let url = AjaxApi.checkinItemDelete();
		let data = {
			array: [this.detailId]
		}
		data = [this.detailId];
		console.log('删除', data)
		wxRequest.postRequest(url, data).then(res => {
			console.log('删除', res)
			if (res.statusCode === 200) {
				let resData = res.data;
				if(resData.code === 0) {
					
				}
			}
		})
        // this.$store.commit('showLoading');
        // http.post(AjaxApi.remove, {
        //   id: this.info.id
        // }).then(res => {
        //   if (res.status === 200) {
        //     this.$toast.success({
        //       duration: 2000,
        //       message: '删除成功',
        //       onClose: function () {
        //         self.$store.commit('hideLoading');
        //         // 点击回退的时候当做地址回退
        //         self.$router.back()
        //       }
        //     });
        //   }
        // })
      });
    },
    returnQrCode (res) {
      if (res) {
        this.qrCodeId = res;
        //签到提交
        // this.handleQdSubmit()
        this.$store.commit('showLoading');
        let json = {
          description: this.description,
          deviceId: this.mobileId,
          rlid: this.info.id,
          projectId: this.info.id,
          type: this.info.type,
          qdfs: this.info.qdfs,
          scope: this.canPosition,
        };
        if (this.qrCodeId) json.qrValue = this.qrCodeId;
        if (this.location) {
          json.longitude = this.location.longitude;
          json.latitude = this.location.latitude;
          json.address = this.location.address;
        }
        this.showDialog = false;

        let url = AjaxApi.dwqdoperation;
        let sendHttp = http;
        if (this.mark === 'jsqd') {
          url = AjaxApiJsqd.dwqdoperation;
          sendHttp = httpJsqd;
        }
        sendHttp.post(url, json).then(res => {
          if (res.status === 200) {
            let message = '';
            if (res.data === 'no') {
              // message = '不是当前签到任务的二维码';
              message = '二维码无效';
            }
            if (res.data === '1') {
              message = '此手机已签到该任务';
            }
            if (res.data === 'success') {
              this.getDetailInfo();
              message = '签到成功';
            }
            if (this.info.sftcqrxx === '是') {
              this.getStudentInfo();
            } else {
              this.$dialog.alert({
                message: message
              });
              this.$store.commit('hideLoading');
            }
          } else {
            this.$dialog.alert({
              message: res
            });
            this.$store.commit('hideLoading');
          }
        });
        this.$store.commit('hideLoading');
      }
    },
    onQrcodeSubmit () {
      this.$root.scanQRCode(this.returnQrCode)
    },
    toQdSubmit (type) {
      if (type !== 'checked') {
        this.optType = type;
        if (type === 'qr') {
          this.onQrcodeSubmit();
        } else {
          this.showDialog = true
        }
      }
    },
    handleQdSubmit () {
      this.$store.commit('showLoading');
      let submitMark = true;
      let json = {
        longitude: this.location ? this.location.longitude : "",
        latitude: this.location ? this.location.latitude : "",
        address: this.location ? this.location.address : "",
        deviceId: this.mobileId,
        rlid: this.info.id,
        projectId: this.info.id,
        type: this.info.type,
        qdfs: this.info.qdfs,
        scope: this.canPosition,
      };
      if (this.xxdmMark === '11149' && !this.myAddress) {
        if (!this.description) {
          submitMark = false;
          this.$dialog.alert({
            message: '请输入签到备注'
          });
          this.$store.commit('hideLoading');
          return false;
        }
      }
      json.description = this.description;
      if (this.qrCodeId) json.qrValue = this.qrCodeId
      if (this.info.qdfs === '定位&拍照签到') {
        console.log()
        if (this.mapToObj(this.fileMap).img === undefined) {
          submitMark = false;
          this.$dialog.alert({
            message: this.info.qdfs + ',请上传材料'
          });
          this.$store.commit('hideLoading');
          return false;
        }
        submitMark = true;
        json.fileString = JSON.stringify(this.mapToObj(this.fileMap))
      }
      if (json && submitMark) {
        this.showDialog = false;
        let url = AjaxApi.dwqdoperation;
        let sendHttp = http;
        if (this.mark === 'jsqd') {
          url = AjaxApiJsqd.dwqdoperation;
          sendHttp = httpJsqd;
        }
        sendHttp.post(url, json).then(res => {
          if (res.status === 200) {
            let message = '';
            if (res.data === 'no') {
              message = '不是当前签到任务的二维码';
            }
            if (res.data === '1') {
              message = '此手机已签到该任务';
            }
            if (res.data === 'success') {
              this.getDetailInfo();
              message = '签到成功';
            }
            if (res.data === 'error') {
              message = '接口返回失败';
            }
            if (this.info.sftcqrxx === '是') {
              this.getStudentInfo();
            } else {
              this.$dialog.alert({
                message: message
              });
              this.$store.commit('hideLoading');
            }
          } else {
            this.$dialog.alert({
              message: res
            });
            this.$store.commit('hideLoading');
          }
        })
      }
    },
    getStudentInfo () {
      let self = this;
      http.post('/syt/student/getById.htm').then(res => {
        if (res.status === 200) {
          this.stuItem = res.data;
          self.$store.commit('hideLoading');
          self.showDetailPopup = true
        }
      });
    },
    getDetailInfo () {
		console.log('this.detailId', this.detailId)
		let self = this;
		let url = AjaxApi.checkinItemDetail() + this.detailId;
		let data = {}
		wxRequest.getRequest(url, data).then(res => {
			console.log('详情', res)
			if (res.statusCode === 200) {
				let resData = res.data;
				if(resData.code === 0) {
					// let data = resData.data;
					// data.sftx = resData.data.sftx == '是' ? true: false;
					// data.qdtj = resData.data.qdtj == '2' ? true: false;
					// self.setData({
					// 	formData: data,
					// 	rang: resData.data.selectorDatas,
					// 	checkedYswzArray: resData.data.checkinItemAddresses,
					// })
					// console.log('预设地址',this.yswzColumns)
					this.xxdmMark = res.data.data.xxdm;
					this.info = res.data.data;
					if (this.info.qdfs !== '扫码签到') {
					  //当前定位信息================================//
					  // this.checkLocation();
					} else {
					  this.showMap = false;
					}
					this.setRealTime(true);
					this.$store.commit('hideLoading');
				} 
			}
		}).catch(error => {
		    console.log(error);
		    // this.error = true
		}).finally(() => {
		    // this.loading = false
		})
      // let url = AjaxApi.getBasicData;
      // let sendHttp = http;
      // if (this.mark === 'jsqd') {
      //   url = AjaxApiJsqd.QueryDetail;
      //   sendHttp = httpJsqd;
      // }
      // sendHttp.post(url, {
      //   id: this.detailId
      // }).then(res => {
      //   if (res.status === 200) {
      //     this.xxdmMark = res.data.xxdm;
      //     this.info = res.data.obj;
      //     if (this.info.qdfs !== '扫码签到') {
      //       //当前定位信息================================//
      //       // this.checkLocation();
      //     } else {
      //       this.showMap = false;
      //     }
      //     this.setRealTime(true);
      //     this.$store.commit('hideLoading');
      //   }
      // });
    },
    handleCancle () {
      this.showDialog = false
    },
    getAddress (lngLat) {
      let self = this;
      window.AMap.plugin('AMap.Geocoder', function () {
        let geocoder = new window.AMap.Geocoder();
        geocoder.getAddress(lngLat, function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            self.location.address = result.regeocode.formattedAddress;
          }
        })
      })
    },
    onTouchMove (e) {
      e.preventDefault();
    },
    locationChanged (position) {
      try {
        this.position = position;
        this.location = JSON.parse(position);
        // 传入经纬度，设置地图中心点
        let lngLatPosition = new this.lngLat(this.location.longitude, this.location.latitude); // 标准写法
        this.map.setCenter(lngLatPosition);
        this.marker.setPosition(lngLatPosition);

        if (this.isScan) this.canPosition = true;
        if (this.circles && this.info.rwzt === '进行中') {
          if (this.info.sfhzqdfw === '是') {//是否绘制签到范围
            this.circles.forEach(item => {
              let a = window.AMap.GeometryUtil.isPointInRing(lngLatPosition, item);
              if (a) {
                this.canPosition = true;
                return false;
              }
            });
            // 判断 p0 是否在 p1-p2-p3 围成的封闭区域内
            // var inRing = AMap.GeometryUtil.isPointInRing(p0, [p1, p2, p3]);
          } else {
            for (let i = 0; i < this.circles.length; i++) {
              const circle = this.circles[i];
              let a = this.twoLocationDistance2(lngLatPosition, circle.center) < circle.radius;
              if (a) {
                this.canPosition = true;
                break;
              }
            }
          }
        }
        if (!this.location.address || this.location.address.indexOf('null') > -1) {
          this.getAddress(lngLatPosition);
        }
        this.myAddress = this.location.address;
        this.showLocation = false;
        this.$toast("定位成功");
      } catch (e) {
        //定位失败，显示重新定位按钮
        this.showLocation = true;
        console.log("定位失败=====", e);
        this.$toast("定位失败...");
      }
    },
    checkLocation () {
      this.$toast('定位中');
      this.$root.onGetLocation(this.locationChanged);
    },
    twoLocationDistance2 (center, center2) {
      return center.distance(new this.lngLat(center2[0], center2[1])); //计算lnglat1到lnglat2之间的实际距离(m)
    },
	loadBMapScript () {
		console.log('loadBMapScript')
		MapLoader2().then(AMap => {
		    console.log('地图加载成功', window._AMapSecurityConfig, window.AMap.Map)
		    // this.initMap(); //初始化地图
			this.onFetch();
		}, e => {
		    console.log('地图加载失败', e)
		})
	},
    initMap () {
		console.log('initMap', this.showMap)
      this.map = new window.AMap.Map('container', {
        // center: this.defaultLngLat,
        // zoom: this.zoom,
        resizeEnable: true
      });
      // this.marker = new window.AMap.Marker({
      //   position: this.defaultLngLat
      // });
      // this.map.add(this.marker);
      // let self = this;
      // let dbxdzMark = this.info.sfhzqdfw;
      // if (this.circles) {
      //   if (dbxdzMark === '是') {
      //     this.circles.forEach(item => {
      //       let circle = new window.AMap.Polygon({
      //         map: this.map,
      //         path: item, //设置多边形边界路径
      //         strokeColor: "#FF33FF", //线颜色
      //         strokeOpacity: 0.2, //线透明度
      //         strokeWeight: 3, //线宽
      //         fillColor: "#1791fc", //填充色
      //         fillOpacity: 0.35 //填充透明度
      //       });
      //       self.map.add(circle);
      //     });
      //   } else {
      //     this.circles.forEach(item => {
      //       let circle = new window.AMap.Circle({
      //         center: new this.lngLat(item.center[0], item.center[1]), // 圆心位置
      //         radius: item.radius, //半径
      //         strokeColor: "#3366FF", //线颜色
      //         strokeOpacity: 0.3, //线透明度
      //         strokeWeight: 3, //线粗细度
      //         fillColor: "#FFA500", //填充颜色
      //         fillOpacity: 0.35 //填充透明度
      //       });
      //       self.map.add(circle);
      //     });
      //   }
      // }
      // window.AMap.plugin(['AMap.ToolBar'], function () {
      //   let toolbar = new window.AMap.ToolBar();
      //   self.map.addControl(toolbar);
      // });
    },
    setRealTime (bool) {
      if (this.$refs.mViewRoot || bool) {
        this.realTime = Utils.moment().format('LTS');
        setTimeout(this.setRealTime, 1000)
      }
    },
    onFetch () {
	  console.log('onFetch', this.detailId)
	  let self = this;
	  let url = AjaxApi.checkinItemDetail() + this.detailId;
	  let data = {}
	  let init = () => {
	    this.initMap();
	    // this.loadBMapScript();
	  };
	  wxRequest.getRequest(url, data).then(res => {
	  	console.log('详情', res)
	  	if (res.statusCode === 200) {
	  		let resData = res.data;
	  		if(resData.code === 0) {
	  			// let data = resData.data;
	  			// data.sftx = resData.data.sftx == '是' ? true: false;
	  			// data.qdtj = resData.data.qdtj == '2' ? true: false;
	  			// self.setData({
	  			// 	formData: data,
	  			// 	rang: resData.data.selectorDatas,
	  			// 	checkedYswzArray: resData.data.checkinItemAddresses,
	  			// })
	  			// console.log('预设地址',this.yswzColumns)
	  			this.xxdmMark = res.data.data.xxdm;
	  			this.info = res.data.data;
				console.log('this.info', this.info)
	  			if (this.info.qdfs !== 'smqd') {
				  init();
	  			  //当前定位信息================================//
	  			  // this.checkLocation();
	  			} else {
	  			  this.showMap = false;
	  			}
	  			this.setRealTime(true);
	  			this.$store.commit('hideLoading');
	  		} 
	  	}
	  }).catch(error => {
	      console.log(error);
	      // this.error = true
	  }).finally(() => {
	      // this.loading = false
	  })
      // let setHttp = http;
      // let url = AjaxApi.getBasicData;
      // if (this.mark === 'jsqd') {//教师签到jsqd
      //   setHttp = httpJsqd;
      //   url = AjaxApiJsqd.QueryDetail;
      // }
      // setHttp.post(url, { id: this.detailId }).then(res => {
      //   if (res.status === 200) {
      //     this.xxdmMark = res.data.xxdm;
      //     this.info = res.data.obj;
      //     if (this.info.qdfs !== '扫码签到') {
      //       init();
      //       //当前定位信息================================//
      //       this.checkLocation();
      //     } else {
      //       this.showMap = false;
      //     }
      //     this.setRealTime(true);
      //     this.$store.commit('hideLoading');
      //   }
      // });
    },
    showTiShi () {
      this.tishishow = true;
    },
  },
  created () {
  },
  mounted () {
	  this.loadBMapScript();
	  // this.onFetch();
  }
}
</script>

<style scoped>
.amap-wrapper {
  width: 100%;
  height: 13rem;
}

.amap-wrapper>>>.amap-copyright {
  opacity: 0;
}

.amap-wrapper>>>.amap-logo {
  opacity: 0;
}

.circle {
  width: 140px;
  height: 140px;
  background: -moz-linear-gradient(top, #e3e2e2 0%, #b6b4b4 100%);
  background: -webkit-linear-gradient(top, #e3e2e2 0%, #b6b4b4 100%);
  background: -o-linear-gradient(top, #e3e2e2 0%, #b6b4b4 100%);
  background: -ms-linear-gradient(top, #e3e2e2 0%, #b6b4b4 100%);
  background: linear-gradient(to bottom, #e3e2e2 0%, #b6b4b4 100%);
  border-radius: 70px;
  box-shadow: 0px 10px 20px -10px rgba(0, 0, 0, 0.5);
  margin: 30px auto;
  padding-top: 0.1px;
}

.circleWrap p,
.circleBottom {
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  color: #fff;
}

.circleBottom {
  color: #999 !important;
}

.timeHeight {
  line-height: 14px !important;
  font-size: 12px !important;
  text-align: center;
}

.circleBottom:nth-of-type(2) {
  font-size: 12px;
}

.circleWrap .van-icon {
  text-align: center;
  display: block;
  margin-top: 20px;
  color: #fff;
}

.circleBlue {
  width: 110px;
  height: 110px;
  background: -moz-linear-gradient(top, #01da7e 0%, #01a761 100%);
  background: -webkit-linear-gradient(top, #01da7e 0%, #01a761 100%);
  background: -o-linear-gradient(top, #01da7e 0%, #01a761 100%);
  background: -ms-linear-gradient(top, #01da7e 0%, #01a761 100%);
  background: linear-gradient(to bottom, #01da7e 0%, #01a761 100%);
  border-radius: 55px;
  box-shadow: 0px 10px 20px -10px rgba(0, 0, 0, 0.5);
  margin: 30px auto;
  padding-top: 0.1px;
}

.circleGreen {
  width: 110px;
  height: 110px;
  background: -moz-linear-gradient(top, #24bdff 0%, #0087c2 100%);
  background: -webkit-linear-gradient(top, #24bdff 0%, #0087c2 100%);
  background: -o-linear-gradient(top, #24bdff 0%, #0087c2 100%);
  background: -ms-linear-gradient(top, #24bdff 0%, #0087c2 100%);
  background: linear-gradient(to bottom, #24bdff 0%, #0087c2 100%);
  border-radius: 55px;
  box-shadow: 0px 10px 20px -10px rgba(0, 0, 0, 0.5);
  margin: 30px auto;
  padding-top: 0.1px;
}

.van-submit-bar {
  display: flex;
  height: 50px;
  border-top: 1px solid #eee;
  box-shadow: 0 -2px 3px -1px #eee;
}

.van-submit-bar-button {
  height: 35px;
  width: 45.5% !important;
  line-height: 35px;
  margin-left: 3% !important;
  background: #00a5ec;
  font-size: 16px;
  border-color: #00a5ec;
}

.vanButton {
  background: #fff;
  color: #00a5ec;
}

.van-col--20 {
  padding-right: 5px;
}

.list-item:not(:last-child)::after {
  border: none;
}

.vanButton1 {
  width: 94% !important;
  background: #fff;
  color: #00a5ec;
  margin-left: 3% !important;
}

.surePopup {
  border-radius: 18px;
}

.stuInfoWrap {
  width: 90%;
  /*padding: 10px;*/
  margin: 0px auto;
  margin-top: 10px;
  border-radius: 8px;
  background: #fff;
}

.headImgWrap {
  width: 80px;
  height: 95px;
  overflow: hidden;
  margin-right: 10px;
  background: #e8e8e8;
  border-radius: 3px;
}

.headImg {
  width: 80px;
}

.stuInfoWrapTitle {
  width: 100%;
  margin: 0 auto;
  font-size: 18px;
  font-weight: bold;
  line-height: 40px;
  border-bottom: 1px solid #f8f8f8;
  text-align: center;
}

.van-address-item_address {
  color: #000;
}

.van-address-item_address span {
  display: inline-block;
  width: 100px;
  text-align: right;
  color: #999;
  margin-right: 8px;
}

.van-col--16 {
  padding-left: 15px;
}

.van-col--16 .van-address-item_address span {
  width: 50px;
  display: none;
}


.passWrap {
  text-align: center;
  padding-top: 20px;
  margin-bottom: 20px;
  /*margin-top: 10px;*/
}

.passImg {
  width: 50px;
  height: 50px;
}

.passWrap p {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
}

.passP {
  color: #50C4A2;
}

.noPassP {
  color: #d81e06;
}

.close {
  float: right;
  width: 30px;
  height: 30px;
  font-size: 20px;
  color: #999;
  margin-top: 8px;
}
</style>
