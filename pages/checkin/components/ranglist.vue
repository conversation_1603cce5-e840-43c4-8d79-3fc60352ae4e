<template>
    <div>
        <van-search v-model="searchValue"
                    :placeholder="markText==='年级'?'请输入要搜索的'+markText:'请输入要搜索的'+markText+'名称'"
                    show-action
                    shape="round"
                    @search="onSearch">
            <div slot="action" @click="onSearch">搜索</div>
        </van-search>
        <van-list v-model="loading"
                  :finished="finished"
                  finished-text="暂无更多数据"
                  @load="loadMoreItems"
                  :error.sync="error"
                  error-text="请求失败，点击重新加载"
                  style="height:80vh;overflow-y:scroll;margin-top: 5px;">
            <div>
                <van-grid :gutter="3" :column-num="mark==='nj'?4:2" :center="false">
                    <van-grid-item :key="index" v-for="(item,index) in items" @click="onCellClick(item)">
                        <van-checkbox shape="square"
                                      :key="item.value"
                                      :name="item.value"
                                      :value="codes.includes(item.value)"
                                      class="van-radio"
                                      style="font-size: 10px;">
                            <div style="font-size: 14px;line-height: 1;">
                                {{item.name}}
                                <!-- <template v-if="mark==='lsxs'"> {{item.value}}</template> -->
                            </div>
                        </van-checkbox>
                    </van-grid-item>
                </van-grid>
            </div>
        </van-list>
        <div class="van-submit-bar van-submit-bar1">
            <div class="van-submit-bar__bar">
				<!-- v-if="mark!=='zyzd'" -->
                              <!-- @click="onCheckAll" -->
                <van-checkbox  class="van-submit-bar-check" style="margin-left: 15px;"
                              :value="isSelectedAll">全选
                </van-checkbox>
                <div class="van-submit-bar__text van-submit-bar__text1">
                    <span style="float: left;margin-left: 15px;line-height: 50px;">共{{total}}记录</span>
                </div>
                <van-button
                        type="info"
                        class="van-button--square van-submit-bar__button"
                        size="large" @click="handleRangCancle">下一步
                </van-button>
            </div>
        </div>
    </div>
</template>
<script>
    import Bus from '@/utils/event-bus'
    // import Axios from '@/utils/axiosWrap'
    // import AjaxApi from '@/components/wdmqd/service/ajaxWDM'
	var wxRequest = require('@/utils/wxRequest.js');
	var AjaxApi = require('../api/index.js');
	var Api1 = require('@/pages/my/api/index.js'); 

    export default {
        props: {
            markText: String,
            mark: String,
            result: Array,
            rang: Array,
			tabObj: {
				type: Object,
				default() {
					return {}
				}
			}
        },
        data() {
            return {
                loading: false, //控制上拉加载的加载动画
                finished: false, //控在页面往下移动到底部时是否调用接口获取数据
                json: {},
                pageIndex: 1,
                pageSize: 10,
                items: [],
                checked: false,
                total: [],
                error: null,
                searchValue: null,
                // initRang: []
            }
        },
        watch: {
            mark(newV) {
				console.log('mark', newV)
                if (newV) this.onSearch();
            },
			"tabObj.id": function(newVal,oldVal) {
				console.log('tabObj.id', newVal)
			}
        },
        computed: {
            isSelectedAll() {
                const codes = this.codes;
				console.log('isSelectedAll', this.codes)
                for (let i = 0; i < this.items.length; i++) {
                    const item = this.items[i];
                    if (!codes.includes(item.value)) {
                        return false;
                    }
                }
                return true;
            },
            codes() {
                let arr = [];
                this.rang.forEach(item => {
                    arr.push(item.value);
                });
                return arr;
            },
            chktotal() {
                //当绑定值变化时触发的事件
                return this.rang.length
            },
        },
        methods: {
            onSearch() {
				console.log('onSearch')
                this.finished = true;
                this.pageIndex = 1;
                this.pageSize = 10;
                this.items = [];
                this.finished = false;
                this.loading = true;
                if (this.loading) this.loadMoreItems();
            },
            onCheckAll() {
				console.log('onCheckAll')
                const isSelectedAll = this.isSelectedAll;
                const codes = this.codes;
				console.log('this.codes', this.codes)
				console.log('this.items', this.items)
                this.items.forEach(item => {
                    if (isSelectedAll) {
                        this.onCellClick(item);
                    } else {
                        if (!codes.includes(item.value)) {
                            this.onCellClick(item);
                        }
                    }
                })
            },
            handleRangCancle() {
                this.$emit('handleRangCancle')
            },
            onCellClick(model) {
                this.$emit('checkRangBox', model);
            },
            //获取列表数据
            loadMoreItems() {
				console.log('loadMoreItems', this.tabObj, this.mark)
				let self = this;
                let json = {};
                let url, type, newArray = [];
				if(this.tabObj.name == 'person') {
					json = {
						page: this.pageIndex,
						limit: this.pageSize,
					}
					url = AjaxApi.personPage() + '/' + this.tabObj.userType + '/queryPage';
					this.getPageData(url, json);
				} else {
					if(this.tabObj.name === 'xymc') {
						url =  Api1.codeDwbQuery();
						json.userType = this.tabObj.userType;
					} else if(this.tabObj.name === 'zymc'){
						url = Api1.codeZybQuery()
					} else if(this.tabObj.name === 'bjmc') {
						url = Api1.codeBjbQuery()
					} else if(this.tabObj.name === 'nj') {
						url = Api1.codeCommonQuery();
						json.codeType = this.tabObj.name
					}
					this.getDictData(url, json)
				}
            },
			getDictData(url, json) {
				let self = this;
				wxRequest.getRequest(url, json).then(res => {
					console.log('res dict', res);
						if(res.statusCode === 200) {
							let resData = res.data;
							if(resData.code === 0) {
								let curPageList = resData.data;
								let resultList = [];
								let tabObj = self.tabObj;
								curPageList.forEach(e => {
									resultList.push({
										// dictDataCode: e.id,
										// dictDataName: e.name,
										userType: e.userType,
										...e,
										name: e.name,
										value: e.id,
										configId: tabObj.id,
										configKey: tabObj.key,
										configName: tabObj.name,
										configTitle: tabObj.title,
									})
								})
								self.setData({
								  items: resultList,
								});
								console.log('pageIndex, finished', this.pageIndex, this.finished);
								console.log('this.items', this.items);
							} else {
								this.error = true;
							}
						}
				}).catch(error => {
				    console.log(error);
				    this.error = true;
				}).finally(() => {
					this.finished = true;
				    this.loading = false;
				    let data = {
				        list: this.items,
				        total: this.total,
				        type: 'array',
				    };
				    this.$emit('setRangBoxList', data);
				})
			},
			getPageData(url, json) {
				let self = this;
				wxRequest.getRequest(url, json).then(res => {
					console.log('res', res);
						// this.finished = true;
						if(res.statusCode === 200) {
							let resData = res.data;
							if(resData.code === 0) {
								let curPageList = resData.data.list ? resData.data.list : resData.data;
								let resultList = [];
								let tabObj = self.tabObj;
								this.total = resData.data.count;
								curPageList.forEach(e => {
									resultList.push({
										// dictDataCode: e.xgh,
										// dictDataName: e.xm,
										userType: e.userType,
										...e,
										name: e.xm,
										value: e.xgh,
										configId: tabObj.id,
										configKey: tabObj.key,
										configName: tabObj.name,
										configTitle: tabObj.title,
									})
								})
								self.setData({
								  items: self.items.concat(resultList),
								  pageIndex: this.pageIndex + 1,
								  finished: resData.data.count <= self.pageSize * self.pageIndex
								});
								console.log('pageIndex, finished', this.pageIndex, this.finished);
								console.log('this.items', this.items);
							} else {
								this.error = true;
							}
						}
				}).catch(error => {
				    console.log(error);
				    this.error = true;
				}).finally(() => {
				    this.loading = false;
				    let data = {
				        list: this.items,
				        total: this.total,
				        type: 'array',
				    };
				    this.$emit('setRangBoxList', data);
				})
			}
        },
        created() {
        },
    }
</script>
<style scoped>
    .van-submit-bar {
        width: 100%;
        display: flex;
        height: 50px;
        border-top: 1px solid #eee;
        box-shadow: 0 -2px 3px -1px #eee;
        padding: unset;
    }

    .van-submit-bar__text1 {
        color: #999 !important;
        font-weight: normal !important;
        font-size: 13px !important;
        line-height: 50px;
        float: left;
        margin-left: 15px;
    }

    .van-submit-bar__bar {
        width: 100%;
    }

    .van-button--square {
        float: right;
        width: 80px;
    }

    .van-submit-bar-check {
        height: 50px;
        float: left;
    }
</style>
