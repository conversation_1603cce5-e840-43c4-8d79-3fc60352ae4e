<!--学生签到详情页-->
<template>
    <section style="background: #fff;height: 100vh;">
        <van-tabs v-model="activeTab" color="#00a5ec" line-height="2px" line-width="50%" title-active-color="#00a5ec">
            <van-tab title="签到">
<!--                <set-map-view v-if="info" :detailId="detailId" v-on:getActiveDetailInfo="onActiveDetailInfo"></set-map-view>-->
                <set-map-view :detailId="detailId"></set-map-view>
            </van-tab>
            <van-tab v-if="roleKey === '个人'" title="记录">
                <!-- <set-records-list :id="detailId"></set-records-list> -->
            </van-tab>
            <van-tab v-if="roleKey != '个人'" title="统计">
                <!-- <set-count-list :id="detailId"></set-count-list> -->
            </van-tab>
        </van-tabs>
    </section>
</template>
<script>
    // import store from '@/store'
    // import http from '@/components/wdmqd/utils/axiosWDM'
    // import AjaxApi from '@/components/wdmqd/service/ajaxWDM'
    // import setMapView from '../components/mapview'
    // import setRecordsList from '@/components/wdmqd/recordslist'
    // import setCountList from '@/components/wdmqd/countlist'
	import setMapView from '../components/mapview'
	var wxRequest = require('@/utils/wxRequest.js');
	var AjaxApi = require('../api/index.js');

    export default {
        components: {
			setMapView, 
			// setRecordsList, 
			// setCountList
		},
        props:{
            // detailId:String,
            mark:String,
            setActiveTab:String,
        },
        data() {
            return {
                // roleKey: store.state.roleKey,
                roleKey: '',
                activeTab: 0,
                info: null,
				detailId: null,
            }
        },
        created() {
            if(this.setActiveTab)this.activeTab = Number(this.setActiveTab);
            this.getDetailInfo();
        },
		onLoad: function(options) {
			var self = this;
			console.log('onLoad options', options);
			if(options.id) {
				this.detailId = options.id;
				this.getDetailInfo();
			}
		},
        methods: {
            // onActiveDetailInfo(){
            //     this.getDetailInfo();
            // },
            getDetailInfo() {
                // http.post(AjaxApi.getBasicData, {id: this.detailId}).then(res => {
                //     if (res.status === 200) {
                //         this.info = res.data.obj;
                //         this.info.canUseYibanMessage = res.data.canUseYibanMessage;
                //     }
                // })
				let url = AjaxApi.checkinItemDetail() + this.detailId;
				let data = {}
				wxRequest.getRequest(url, data).then(res => {
					console.log('详情', res)
					if (res.statusCode === 200) {
						let resData = res.data;
						if(resData.code === 0) {
							this.info = resData.data;
							// this.info.canUseYibanMessage = res.data.canUseYibanMessage;
						} 
					}
				}).catch(error => {
				    console.log(error);
				    // this.error = true
				}).finally(() => {
				    // this.loading = false
				})
            }
        }
    }
</script>
<style scoped>
	.van-tabs >>> .van-tab{
		font-size: 16px;
	}
</style>