<template>
  <div class="signaturePage" ref="signatureRef">
    <div style="margin-bottom: 5px;" v-if="filed.showFlag!=='readonly'">
      <el-button size="small" @click="handleClear">清屏</el-button>
      <el-button size="small" @click="handleUndo">撤销</el-button>
      <el-button size="small" @click="handlePreview">保存</el-button>
      <!--      <el-button @click="handleColor">Change Color</el-button>-->
    </div>
    <canvas id="canvas" v-show="!showSignatureData"/>
    <el-image v-if="showSignatureData" :style="{ height: '600px', width: canvasWidth+'px',}"
              :src="signatureData"
              fit="cover"/>
    <div class="tip" v-if="filed.showFlag!=='readonly'">使用手机端手写更方便</div>
  </div>
</template>

<script setup>
import SmoothSignature from "smooth-signature";
import {onMounted, ref, watch} from "vue";
import {ElNotification} from 'element-plus/es';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  /** 字典值 */
  modelValue: [String, Number, Boolean, Array],
  /** 每次刷新，重新请求 */
  refresh: String,
  /** 组件信息 */
  filed: Object,
  /** 是否禁用 */
  disabled: Boolean,
});

const signatureRef = ref(null)
const signature = ref(null)
const canvasWidth = ref(null)

const handleClear = () => {
  showSignatureData.value = false;
  signature.value?.clear();
}

const handleUndo = () => {
  showSignatureData.value = false;
  signature.value?.undo();
}

const handleColor = () => {
  signature.value.color = '#' + Math.random().toString(16).slice(-6);
}

const handlePreview = () => {
  const isEmpty = signature.value.isEmpty();
  if (isEmpty && !showSignatureData.value) {
    ElNotification({
      title: '温馨提示',
      message:
        '你还没有填写' + props.filed.label,
      type: 'warning',
      duration: 2500,
      position: 'top-right'
    });
    return false;
  }
  if (!showSignatureData.value) {
    const base64String = signature.value.getPNG();// Base64编码的字符串
    const contentType = 'image/png'; // MIME类型
    const blob = base64ToBlob(base64String.split(',')[1], contentType);
    emit('update:modelValue', blob);
    ElNotification({
      title: '温馨提示',
      message:
        props.filed.label + '已保存',
      type: 'success',
      duration: 2500,
      position: 'top-right'
    });
  } else {
    ElNotification({
      title: '温馨提示',
      message:
        props.filed.label + '你没有更新操作',
      type: 'warning',
      duration: 2500,
      position: 'top-right'
    });
  }
}

const init = () => {
  const canvas = document.getElementById("canvas");
  canvasWidth.value = window.innerWidth / 2
  const options = {
    width: canvasWidth.value,
    height: 600,
    minWidth: 4,
    maxWidth: 12,
    // color: '#1890ff',
    bgColor: '#f6f6f6'
  };
  signature.value = new SmoothSignature(canvas, options);
}

/**
 * base64转二进制文件流
 * @param base64
 * @param mimeType
 * @returns {Blob}
 */
const base64ToBlob = (base64, mimeType) => {
  const byteCharacters = atob(base64);
  const byteArrays = [];
  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);
    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }
  return new Blob(byteArrays, {type: mimeType});
}

const showSignatureData = ref(false)
const signatureData = ref(null)

/**
 * 签名回显，如果已有签名，没有重新签名，handlePreview特殊处理
 */
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue && modelValue.length > 0) {
      showSignatureData.value = true
      signatureData.value = modelValue[0].url
    }
  }, {immediate: true})

onMounted(() => {
  init()
});

</script>
<script>
export default {
  name: 'PCSIGNATURE'
};
</script>

<style lang="less">
.signaturePage {
  canvas {
    border: 2px dashed #ccc;
    cursor: crosshair;
  }

  .tip {
    color: var(--el-color-primary);
  }
}
</style>
