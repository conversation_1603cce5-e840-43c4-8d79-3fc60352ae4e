<template>
  <ele-drawer size="56%"
              :title="header?header:'人员选择'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="false"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '8px!important' ,background:'#f0f2f5',height:'auto'}"
              :title-style="{fontSize:'16px' }"
              @update:modelValue="updateModelValue">
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="70%"
                     :min-size="340"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{borderWidth: '0 ', width: '100%'}"
                     :style="{ height: '100%', overflow: 'visible' }">
      <div class="left-panel">
        <ele-card class="option-card"
                  :style="{margin:'0 0 1px 8px !important'}"
                  :body-style="{padding:'10px 0 5px 0 !important',height: 'auto',  overflow:'auto'}">
          <!--          (pageHeight+5) + 'px'-->
          <template #header>
            <ele-tabs type="plain"
                      size="small"
                      v-model="activeName"
                      :items="tableTabs">
              <template #label="{ item,label}">
                <ele-text :type="activeName===item.id?'primary':''">
                  {{ label }}
                </ele-text>
              </template>
            </ele-tabs>
          </template>
          <!-- 搜索 -->
          <div style="padding: 0 10px;" class="select-all-control">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-checkbox :indeterminate="isIndeterminate[activeName]"
                             v-model="checkAll[activeName]"
                             @change="handleCheckAllChange">全选
                  [{{ leftOperation[activeName]?.length ?? 0 }}/{{
                    (currentTabInfo?.newName === 'person' && currentTabInfo?.userType === 'student') ? total : rDataList[activeName]?.length ?? 0
                  }}]
                </el-checkbox>
              </el-col>
              <el-col :span="16">
                <el-input
                  v-model="leftSearchText" placeholder="请输入您要查询的关键字" clearable
                  :suffix-icon="Search"
                  size="small"
                  @clear="onSearchLeft"
                  @keyup.enter="onSearchLeft"
                  @change="onSearchLeft"/>
              </el-col>
            </el-row>
            <searchPreview v-if="currentTabInfo?.newName==='person'&&currentTabInfo?.userType === 'student'"
                           @onDone="onDoneSearch"/>
          </div>
          <div :style="{height:currentPageHeight+'px', overflow:'auto'}">
            <el-checkbox-group style="margin: 0 10px!important" class="checkbox-group"
                               v-model="leftOperation[activeName]" :max="realMaxNumber">
              <template v-if="rDataList[activeName]">
                <el-row :gutter="8">
                  <el-col :span="getSelectedColSpan()" v-for="(item, index) in rDataList[activeName]"
                          :key="index">
                    <el-checkbox class="option-item"
                                 @change="onCheckLeft(item)" :value="item.dictDataCode">
                    <span style="font-size: 12px;">{{
                        item.dictDataName ? item.dictDataName : item.name
                      }}</span>
                    </el-checkbox>
                  </el-col>
                </el-row>
              </template>
            </el-checkbox-group>
          </div>
          <el-pagination style="margin: 10px;"
                         v-if="currentTabInfo?.newName==='person'&&currentTabInfo?.userType==='student'"
                         @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="currentPage"
                         :page-sizes="[60, 80, 100, 200]"
                         :page-size="pageSize"
                         :total="total"
                         :page-count="total/pageSize"
                         size="small"
                         :background="true"
                         layout=" sizes, prev,  next, jumper">
          </el-pagination>
        </ele-card>
      </div>
      <template #body>
        <ele-card class="selected-panel"
                  :style="{margin:'0 2px 1px 3px !important'}"
                  :body-style="{height: (pageHeight+5)+'px', overflow:'auto'}">
          <template #header>
            <div class="panel-header" style="padding: 4px 0">
              <span>已所选范围</span>
            </div>
          </template>
          <template #extra>
            <el-checkbox v-model="isSync" v-if="paramMode!=='zzbdweh'&&paramMode!=='base'">
              是否并联
            </el-checkbox>
          </template>
          <div class="selected-items-container">
            <div v-if="paramMode==='zzbdweh'" class="selected-group">
              <ele-alert title="支持元素拖拽进行排序"
                         show-icon
                         style="margin-bottom: 12px"
                         :closable="false"/>
              <template v-for="lgroup in leftGroupData">
                <el-divider content-position="left">{{ lgroup.type }}</el-divider>
                <el-checkbox-group :model-value="leftOperationArray.map(item => item.dictDataCode)"
                                   class="checkbox-group">
                  <vue-draggable v-model="lgroup.list"
                                 item-key="value"
                                 :animation="300"
                                 :set-data="() => void 0"
                                 class="draggable-container">
                    <template #item="{ element }">
                      <el-checkbox :value="element?.dictDataCode"
                                   @change="onCheckRight(element)"
                                   class="selected-item">
                        <span class="item-label" style="font-size: 12px;">
                          {{ element?.dictDataName || element?.name }}
                        </span>
                        <el-icon class="drag-handle" style="width: 2em">
                          <DragOutlined/>
                        </el-icon>
                      </el-checkbox>
                    </template>
                  </vue-draggable>
                </el-checkbox-group>
              </template>
            </div>
            <div v-else class="selected-group">
              <el-checkbox-group style="margin: 0 10px!important" class="checkbox-group"
                                 :model-value="leftOperationArray.map(item => item.dictDataCode)">
                <el-row :gutter="8">
                  <el-col v-for="(item, index) in leftOperationArray"
                          :key="index">
                    <el-checkbox
                      @change="onCheckRight(item)" :value="item.dictDataCode">{{
                        item.dictDataName ? item.dictDataName : item.name
                      }}
                    </el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </div>
          </div>
        </ele-card>
      </template>
    </ele-split-panel>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" size="small" plain @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {Search} from '@element-plus/icons-vue'
import {ElMessage as EleMessage} from "element-plus"
import {useUserStore} from "@/store/modules/user"
import {storeToRefs} from "pinia"
import {useDictData} from "@/utils/use-dict-data"
import {mergeUnique, removeSomeData,groupArr} from '@/utils/common_bak2.js'
import {getSelectorConfig} from './api/index'
import VueDraggable from 'vuedraggable'
import SearchPreview from './search-preview.vue'
import {queryPage} from '@/views/personInfo/st/api/index'
import {DragOutlined} from "@/components/icons/index.js";

const userStore = useUserStore()
const {pageHeight, dicts} = storeToRefs(userStore)

const props = defineProps({
  modelValue: Boolean,
  paramMode: {
    type: String,
    default: 'base'
  },
  selectMaxNubmer: {
    type: [Number, String],
    default: undefined,
    validator: value => {
      return typeof value === 'number' || value === '—' || value === undefined || value === null;
    }
  },
  header: String,
  projectId: String,
  userType: String,
  perSelectedData: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['done', 'update:modelValue', 'change'])

// 响应式数据
const leftSearchText = ref('')
const leftOperation = ref({})
const leftOperationArray = ref([])
const isIndeterminate = ref({})
const checkAll = ref({})
const activeName = ref()
const tableTabs = ref([])
const isSync = ref(false)
const leftGroupData = ref([])
const total = ref(0)
const pageSize = ref(60)
const currentPage = ref(1)
const queryParams = ref({})
const rDataList = ref({})
const initialSelectedData = ref([]);
const isFirstLoad = ref(true);

// 计算属性
const realMaxNumber = computed(() => {
  return (props.selectMaxNubmer === '—' || !props.selectMaxNubmer)
    ? undefined
    : Number(props.selectMaxNubmer);
})

const currentTabInfo = computed(() => {
  return tableTabs.value.find(tab => tab.id === activeName.value)
})

/** 页面高度设置 */
const currentPageHeight = computed(() => {
  if (!currentTabInfo.value) return pageHeight.value - 42;
  const tabName = currentTabInfo.value.newName;
  const userType = currentTabInfo.value.userType;
  return tabName === 'person' && userType === 'student' ? pageHeight.value - 125 : pageHeight.value - 42;
});


// 根据当前tab类型获取已选区域的列跨度
const getSelectedColSpan = () => {
  if (!currentTabInfo.value) return 12;

  const tabName = currentTabInfo.value.newName;
  console.log(tabName)
  if (['zymc', 'bjmc'].includes(tabName)) {
    return 12; // 一行显示两个
  } else if (['nj'].includes(tabName)) {
    return 4; // 一行显示六个
  }
  return 6; // 一行显示四个
}

const updateModelValue = (value) => {
  if (!value) {
    leftOperationArray.value = JSON.parse(JSON.stringify(initialSelectedData.value));
    leftGroupData.value = groupArr(initialSelectedData.value, 'configId');

    leftOperation.value = {};
    tableTabs.value.forEach(tab => {
      leftOperation.value[tab.id] = [];
    });

    const groupedData = groupArr(initialSelectedData.value, 'configId');
    groupedData.forEach(group => {
      leftOperation.value[group.type] = group.list.map(item => item.value);
    });

    tableTabs.value.forEach(tab => {
      updateLeftCheckboxState(tab.id);
    });
  }
  emits('update:modelValue', value);
};

const queryStudentList = async () => {
  if (!currentTabInfo.value?.userType) return
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      ...queryParams.value
    }
    const resData = await queryPage(currentTabInfo.value.userType, params)
    total.value = resData.count

    const listData = resData.list.map(e => ({
      dictDataCode: e.xgh,
      dictDataName: e.xm,
      userType: e.userType,
      ...e
    }))

    rDataList.value[activeName.value] = listData.length ? listData : []
    updateLeftCheckboxState(activeName.value)
  } catch (e) {
    EleMessage.error(e.message)
  }
}

const fetchDictData = async () => {
  const tab = currentTabInfo.value
  if (!tab) return

  let queryParams = {}

  if (tab.columns) {
    queryParams.params = JSON.parse(tab.columns)
  }

  if (tab.url) {
    queryParams.dictFieldUrl = tab.url
    queryParams.params = {userType: tab?.userType}
  }

  queryParams.userType = tab?.userType

  if (tab.newName === 'zzjbxx') {
    queryParams.valueField = "fieldEn"
    queryParams.textField = "fieldZh"
  }

  if (tab.newName === 'zzdtxx') {
    queryParams.valueField = "id"
    queryParams.textField = "groupName"
  }

  await useDictData([tab.newName], queryParams)
  rDataList.value[tab.id] = dicts.value[tab.newName] || []
  updateLeftCheckboxState(activeName.value)
}

const handleCheckAllChange = (val) => {
  const tabId = activeName.value
  isIndeterminate.value[tabId] = false
  const leftData = rDataList.value[tabId] || []

  const allDataCode = leftData.map(e => e.dictDataCode)
  const allData = leftData.map(e => ({
    name: e.dictDataName,
    value: e.dictDataCode,
    configId: tabId,
    configKey: currentTabInfo.value?.key,
    configName: currentTabInfo.value?.name,
    configTitle: currentTabInfo.value?.title,
    ...e
  }))

  leftOperation.value[tabId] = val ? allDataCode : []

  leftOperationArray.value = val
    ? mergeUnique(allData, leftOperationArray.value, 'dictDataCode')
    : removeSomeData(allData, leftOperationArray.value, 'dictDataCode')

  if (props.paramMode === 'zzbdweh') {
    updateGroupDataOnCheckAll(val, allData)
  }
}

const updateGroupDataOnCheckAll = (isChecked, data) => {
  const tabTitle = currentTabInfo.value?.title
  if (!tabTitle) return

  const groupIndex = leftGroupData.value.findIndex(obj => obj.type === tabTitle)

  if (isChecked) {
    if (groupIndex === -1) {
      leftGroupData.value.push({
        type: tabTitle,
        list: data,
        size: data.length
      })
    } else {
      leftGroupData.value[groupIndex].list = data
    }
  } else if (groupIndex !== -1) {
    leftGroupData.value.splice(groupIndex, 1)
  }
}

const onCheckLeft = (item) => {
  const tabId = activeName.value
  const newData = {
    name: item.dictDataName,
    value: item.dictDataCode,
    configId: tabId,
    configKey: currentTabInfo.value?.key,
    configName: currentTabInfo.value?.name,
    configTitle: currentTabInfo.value?.title,
    ...item
  }

  const isSelected = leftOperationArray.value.some(obj => obj.dictDataCode === item.dictDataCode)
  if (isSelected) {
    leftOperationArray.value = leftOperationArray.value.filter(v => v.dictDataCode !== item.dictDataCode)
  } else {
    leftOperationArray.value.push(newData)
  }

  updateLeftCheckboxState(tabId)

  if (props.paramMode === 'zzbdweh') {
    updateGroupDataOnCheckItem(isSelected, newData)
  }
}

const updateLeftCheckboxState = (tabId) => {
  const checkedCount = leftOperation.value[tabId]?.length || 0;
  const dataCount = rDataList.value[tabId]?.length || 0;

  checkAll.value[tabId] = checkedCount === dataCount && dataCount > 0;
  isIndeterminate.value[tabId] = checkedCount > 0 && checkedCount < dataCount;
};

const updateGroupDataOnCheckItem = (isSelected, data) => {
  const tabTitle = data.configTitle
  if (!tabTitle) return

  const groupIndex = leftGroupData.value.findIndex(obj => obj.type === tabTitle)

  if (isSelected) {
    if (groupIndex !== -1) {
      const itemIndex = leftGroupData.value[groupIndex].list.findIndex(
        v => v.dictDataCode === data.dictDataCode
      )
      if (itemIndex !== -1) {
        leftGroupData.value[groupIndex].list.splice(itemIndex, 1)
        if (leftGroupData.value[groupIndex].list.length === 0) {
          leftGroupData.value.splice(groupIndex, 1)
        }
      }
    }
  } else {
    if (groupIndex === -1) {
      leftGroupData.value.push({
        type: tabTitle,
        list: [data]
      })
    } else {
      leftGroupData.value[groupIndex].list.push(data)
    }
  }
}

const onCheckRight = (item) => {
  leftOperationArray.value = leftOperationArray.value.filter(
    v => v.dictDataCode !== item.dictDataCode
  )

  Object.keys(leftOperation.value).forEach(tabId => {
    leftOperation.value[tabId] = leftOperation.value[tabId].filter(
      v => v !== item.dictDataCode
    )
  })

  if (props.paramMode === 'zzbdweh') {
    removeFromGroupData(item)
  }

  updateLeftCheckboxState(activeName.value)
}

const removeFromGroupData = (item) => {
  const tabTitle = item.configTitle
  if (!tabTitle) return

  const groupIndex = leftGroupData.value.findIndex(obj => obj.type === tabTitle)
  if (groupIndex === -1) return

  const itemIndex = leftGroupData.value[groupIndex].list.findIndex(
    v => v.dictDataCode === item.dictDataCode
  )
  if (itemIndex !== -1) {
    leftGroupData.value[groupIndex].list.splice(itemIndex, 1)
    if (leftGroupData.value[groupIndex].list.length === 0) {
      leftGroupData.value.splice(groupIndex, 1)
    }
  }
}

const save = () => {
  emits('done', leftOperationArray.value)
  updateModelValue(false)
  console.log(leftOperationArray.value)
}

const onSearchLeft = () => {
  if (!activeName.value) return

  if (leftSearchText.value) {
    rDataList.value[activeName.value] = (rDataList.value[activeName.value] || []).filter(
      obj => obj.dictDataName.includes(leftSearchText.value)
    )
  } else {
    if (currentTabInfo.value?.newName === 'person') {
      queryStudentList()
    } else {
      rDataList.value[activeName.value] = dicts.value[currentTabInfo.value?.name] || []
    }
  }
}

const onDoneSearch = (data) => {
  queryParams.value = data
  queryStudentList()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  queryStudentList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  queryStudentList()
}

const initSelectedData = () => {
  const resDataChk = initialSelectedData.value;

  tableTabs.value.forEach(tab => {
    if (!leftOperation.value[tab.id]) {
      leftOperation.value[tab.id] = [];
    }
  });

  if (!resDataChk || !resDataChk.length) return;

  const resData = groupArr(resDataChk, 'configId');
  resData.forEach(e => {
    leftOperation.value[e.type] = e.list.map(item => item.value);
  });

  leftOperationArray.value = resDataChk.map(e => ({
    ...e,
    dictDataCode: e.value,
    dictDataName: e.name
  }));

  tableTabs.value.forEach(tab => {
    updateLeftCheckboxState(tab.id);
  });

  if (props.paramMode === 'zzbdweh') {
    leftGroupData.value = resData.map(group => ({
      type: group.type,
      list: group.list.map(item => ({
        ...item,
        dictDataCode: item.value,
        dictDataName: item.name
      }))
    }));
  }
};

const loadTabData = async () => {
  if (!currentTabInfo.value) return
  try {
    if (currentTabInfo.value.newName === 'person') {
      await queryStudentList()
    } else {
      await fetchDictData()
    }
    updateLeftCheckboxState(activeName.value)
  } catch (error) {
    EleMessage.error(`加载数据失败: ${error.message}`)
  }
}

const querySelectorConfig = async () => {
  try {
    const resData = await getSelectorConfig({
      paramMode: props.paramMode,
      userType: props.userType
    })

    if (resData.length > 0) {
      resData.forEach(e => {
        e.label = e.title
        e.newName = e.name
        e.name = e.id
      })
      tableTabs.value = resData
      activeName.value = resData[0].id
    }
  } catch (error) {
    EleMessage.error(`获取配置失败: ${error.message}`)
  }
}

watch(() => props.modelValue, async (modelValue) => {
  if (modelValue) {
    if (isFirstLoad.value || props.perSelectedData !== initialSelectedData.value) {
      initialSelectedData.value = JSON.parse(JSON.stringify(props.perSelectedData));
      isFirstLoad.value = false;
    }
    await querySelectorConfig();
    initSelectedData();
  } else {
    leftOperationArray.value = JSON.parse(JSON.stringify(initialSelectedData.value));
  }
}, {immediate: true});

watch([currentPage, pageSize], () => {
  if (currentTabInfo.value?.newName === 'person') {
    queryStudentList()
  }
})

watch(() => activeName.value, async (value) => {
  if (value) {
    await loadTabData()
  }
}, {immediate: true, deep: true})

</script>

<style lang="scss" scoped>
:deep(.ele-split-panel) {
  .left-panel {
    padding-right: 8px;
    height: 100%;
    overflow-y: auto;

    .option-card {
      border-radius: 4px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-card__header) {
        padding: 10px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
      }

      .select-all-control {
        display: flex;
        align-items: center;

        .el-checkbox {
          height: 26px;
        }
      }


    }

    :deep(.checkbox-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 2px;

    }
  }

  .selected-panel {
    height: 100%;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

    :deep(.el-card__header) {
      padding: 10px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
    }

    .panel-header {
      padding: 0 10px;
      display: flex;
      align-items: center;
      gap: 8px;

      .help-icon {
        color: #909399;
        cursor: help;
        opacity: 0.6;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }

    .selected-items-container {
      height: 100%;
      overflow-y: auto;

      .selected-group {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .checkbox-group {
          width: 100%;
        }

        .draggable-container {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          padding: 8px;

          .selected-item {
            width: auto;
            margin: 0;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: #f5f7fa;
            transition: all 0.2s;

            &:hover {
              background-color: #e6e9ed;
            }

            :deep(.el-checkbox__label) {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .item-label {
                font-size: 12px;
                flex: 1;
                color: var(--el-checkbox-text-color);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .drag-handle {
                margin-left: 10px;
                color: #c0c4cc;
                cursor: move;

                &:hover {
                  color: #909399;
                }
              }
            }
          }
        }
      }
    }
  }

}
</style>
