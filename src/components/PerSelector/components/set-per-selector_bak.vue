<template>
  <ele-page :style="{padding:'0 5px 0 5px!important'}">
    <ele-split-panel ref="splitRef"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     :min-size="40"
                     :max-size="-40"
                     flex-table
                     border
                     space="3px"
                     :size="'80%'"
                     allow-collapse
                     :custom-style="{borderWidth: '0 1px 0 0',width: '100%' }"
                     :body-style="{height: (pageHeight+68)+'px',overflow:'auto'}"
                     :style="{ height: '100%', overflow: 'visible' }">
      <ele-card v-for="tab in tableTabs" :header="'可选'+tab.title" :name="tab.id"
                :header-style="{fontSize:'unset'}"
                :body-style="{height:(['zzjbxx','zzzdyjbxx'].includes(tab.name)?(pageHeight-pageHeight/3) : (pageHeight-pageHeight/1.3))+'px',
                 'overflow': 'auto',padding: '3px 0 6px 0 !important'}">
        <template #extra>
          <el-checkbox style="height: 26px;" :indeterminate="isIndeterminate[tab.id]"
                       v-model="checkAll[tab.id]"
                       @change="handleCheckAllChange($event,tab)">全选
            [{{ leftOperation[tab.id]?.length ?? 0 }}/{{
              rDataList[tab.id]?.length ?? 0
            }}]
          </el-checkbox>
        </template>
        <el-checkbox-group style="margin: 5px;" v-model="leftOperation[tab.id]">
          <template v-if="rDataList[tab.id]">
            <template v-for="(item, index) in rDataList[tab.id]"
                      :key="index">
              <el-checkbox style="min-width: 80px;" v-if="tab.title==='多条信息'"
                           @change="onCheckLeft(item,tab)" :value="item.dictDataCode">{{
                  item.dictDataName
                }}
              </el-checkbox>
              <el-checkbox style="min-width: 80px;" v-else-if="tab.title==='基本信息'&&item.display==='block'"
                           @change="onCheckLeft(item,tab)" :value="item.dictDataCode">{{
                  item.dictDataName
                }}
              </el-checkbox>
            </template>
          </template>
        </el-checkbox-group>
      </ele-card>
      <template #body>
        <ele-card flex-table
                  :header-style="{fontSize:'unset'}"
                  :body-style="{  height: (pageHeight-48)+'px', 'overflow': 'auto','flex': 1,padding: '3px 0 6px 0 !important'}">
          <template #header>
            已选信息111
          </template>
          <template #extra>
            <ele-tooltip effect="light"
                         placement="top-start"
                         :popperOptions="{
              modifiers: [{ name: 'offset', options: { offset: [-8, 10] } }]
            }">
              <el-icon :size="18" style="align-self: center;  cursor: help">
                <QuestionCircleOutlined style="opacity: 0.6"/>
              </el-icon>
              <template #content>
                <div>支持元素拖拽进行排序</div>
              </template>
            </ele-tooltip>
          </template>
          <template v-for="lgroup in leftGroupData">
            <template v-if="lgroup.type!=='多条信息'">
              <el-checkbox-group v-model="leftOperation[lgroup.fieldId]" style="margin: 5px;">
                <vue-draggable :list="lgroup.list"
                               tag="tbody"
                               item-key="value"
                               :animation="300"
                               :set-data="() => void 0"
                               :force-fallback="true"
                               @update="updateSort">
                  <template #item="{ element }">
                    <el-checkbox style="width: 140px;" @change="onCheckRight(element)"
                                 :value="element?.dictDataCode">
                      <span :style="{color: 'var(--ele-checkbox-color)'}">{{
                          element?.dictDataName ? element?.dictDataName : element?.name
                        }}
                      </span>
                    </el-checkbox>
                  </template>
                </vue-draggable>
              </el-checkbox-group>
            </template>
          </template>
        </ele-card>
      </template>
    </ele-split-panel>
  </ele-page>
</template>
<script setup>
import {ref, watch, nextTick, unref,} from 'vue'
import {useUserStore} from "@/store/modules/user";
import {storeToRefs} from "pinia";
import {mergeUnique, removeSomeData} from '@/utils/common_bak2.js';
import {getSelectorConfig} from '../api/index';
import {groupArr} from '@/utils/common_bak2.js';
import VueDraggable from 'vuedraggable';
import {getFormGroup} from "@/views/zizhu/api/form-group-index.js";
import {useRouter} from "vue-router";
import {getFormTemplateField} from "@/views/zizhu/api/form-template-field-index.js";
import {getDicFieldValueByUrl} from "@/views/system/sphfw/dictionary-field/api/index.js";
import {getApprovalCustomField} from "@/views/zizhu/approval/api/index.js";
import {DragOutlined, QuestionCircleOutlined} from "@/components/icons/index.js";
import ProForm from "@/components/ProForm/index.vue";

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /**功能业务类型*/
  paramMode: String,
  hearder: String,
  RandomString: String,
  currentXmId: String,
  userType: String,
  currentDiyType: String,//审核，自定义类型，list=自定义列，query=自定义查询字段
  pageHeight: Number,
  perSelectedData: Array,
});
// 左侧数据
const leftOperation = ref({})
// 右侧数据
const leftOperationArray = ref([])

// 定义emit
const emits = defineEmits(['done', 'update:modelValue', 'change', 'updateInfoBtn'])

const isIndeterminate = ref({});
const checkAll = ref({});

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

const userStore = useUserStore();
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let pathArray = path.split("/")
let routeType = props.paramMode === 'zzshzdylzdycx' ? pathArray[4] : pathArray[3];

const {dicts} = storeToRefs(userStore);
const tableTabs = ref([])
/** 请求状态 */
const loading = ref(true);

/**
 * 项目基础设置，人员选择器分组处理
 * paramMode==='zzbdweh'
 * */
const leftGroupData = ref([]);
const rDataList = ref({});

/** 是否全选 */
const handleCheckAllChange = (val, currentTab) => {
  isIndeterminate["value"][currentTab.id] = false;
  const leftData = rDataList["value"][currentTab.id]
  let allDataCode = []
  let allData = []
  if (leftData.length > 0) {
    leftData.forEach(e => {
      allDataCode.push(e.dictDataCode)
      // allData.push(e)
      allData.push({
        "name": e.dictDataName,
        "value": e.dictDataCode,
        "configId": currentTab.id,
        "configKey": currentTab.key,
        "configName": currentTab.id,
        "configTitle": currentTab.title,
        ...e
      })
    })
  }
  let newArray = []
  if (val) {
    leftOperation["value"][currentTab.id] = allDataCode;
    newArray = mergeUnique(allData, leftOperationArray.value, 'dictDataCode')
  } else {
    leftOperation["value"][currentTab.id] = [];
    newArray = removeSomeData(allData, leftOperationArray.value, 'dictDataCode')
  }
  leftOperationArray.value = newArray
  if (props.paramMode === 'zzbdweh' || props.paramMode === 'zzshzdylzdycx') {
    let isExistGroupData = leftGroupData.value.filter(obj => obj.type === currentTab.title)
    if (isExistGroupData.length > 0) {
      isExistGroupData.forEach((v) => {
        if (v.type === currentTab.title) {
          if (val) {
            //全选，添加右侧分组数组
            v.list = allData
          } else {
            //全不选，删除右侧分组数组
            leftGroupData.value.splice(leftGroupData.value.indexOf(v), 1)
          }
        }
      })
    } else {
      leftGroupData.value.push({type: currentTab.title, fieldId: currentTab.id, list: allData, size: allData.length})
    }
  }
  emits('done', leftOperationArray.value);
}

/**
 * 左侧选中
 * @param e
 * @param currentTab
 */
const onCheckLeft = (e, currentTab) => {
  console.log(e, currentTab)
  let tabName = currentTab?.id
  let newData = {
    "name": e.dictDataName,
    "value": e.dictDataCode,
    "configId": currentTab?.id,
    "configKey": currentTab?.key,
    "configName": currentTab?.id,
    "configTitle": currentTab?.title,
    ...e
  }
  let isExist = leftOperationArray.value.filter(obj => obj.dictDataCode === e.dictDataCode)
  if (isExist.length > 0) {
    leftOperationArray.value.forEach((v) => {
      if (v.dictDataCode === e.dictDataCode) {
        leftOperationArray.value.splice(leftOperationArray.value.indexOf(v), 1)
      }
    })
  } else {
    leftOperationArray.value.push(newData)
  }
  if (props.paramMode === 'zzbdweh' || props.paramMode === 'zzshzdylzdycx') {
    let isExistGroupData = leftGroupData.value.filter(obj => obj.type === newData.configTitle)
    if (isExistGroupData.length > 0) {
      isExistGroupData.forEach((v) => {
        // if (v.type === newData.configTitle) {
        if (isExist.length > 0) {//左侧单个点击取消选择
          console.log('a')
          v.list.forEach((vl) => {
            if (vl.dictDataCode === e.dictDataCode) {
              v.list.splice(v.list.indexOf(vl), 1)
              if (v.list.length <= 0) {
                let index = leftGroupData.value.findIndex(element => element.fieldId === newData.configName);
                leftGroupData.value.splice(index, 1)
                //list为空删除右侧分组数组
                // leftGroupData.value.splice(leftGroupData.value.indexOf(vl), 1)
              }
            }
          })
        } else {//左侧单个点击选择，给右侧组赋值
          if (rDataList["value"][tabName] && rDataList["value"][tabName].length > 0) {
            rDataList["value"][tabName].forEach(filed => {
              if (filed.dictDataCode === newData.dictDataCode) filed.display = 'none';
            })
            v.list.push(newData)
          }
        }
        // }
      })
    } else {//左侧单个点击选择，给右侧组赋值---首次
      leftGroupData.value.push({type: currentTab.title, fieldId: currentTab.id, list: [newData]})
      if (rDataList["value"][tabName] && rDataList["value"][tabName].length > 0) {
        rDataList["value"][tabName].forEach(filed => {
          if (filed.dictDataCode === newData.dictDataCode) filed.display = 'none';
        })
      }
    }
  }

  let checkedCount = leftOperation["value"][tabName] ? leftOperation["value"][tabName].length : 0;
  let dataCount = rDataList["value"][tabName] ? rDataList["value"][tabName].length : 0;
  checkAll["value"][tabName] = checkedCount === dataCount
  isIndeterminate["value"][tabName] = checkedCount > 0 && checkedCount < dataCount;

  emits('done', leftOperationArray.value);
}

/**
 * 更新次序
 * @param evt
 */
const updateSort = (evt,) => {
  evt.preventDefault()
  let sortedButtonList = []
  leftGroupData.value.map(function (leftData, index) {
    leftData.list.map(function (value, index) {
      value.sort = index + 1
      sortedButtonList.push(value)
    })
  })
  emits('done', sortedButtonList);
}

/**
 * 右侧选中
 * @param e
 */
const onCheckRight = (e) => {
  let tabName = e.configName
  console.log(e, rDataList["value"])
  let isExist = leftOperationArray.value.filter(obj => obj.dictDataCode === e.dictDataCode)
  if (isExist.length > 0) {
    leftOperationArray.value.forEach((v) => {
      if (v.dictDataCode === e.dictDataCode) {
        leftOperationArray.value.splice(leftOperationArray.value.indexOf(v), 1)
      }
    })
    tableTabs.value.forEach(tab => {
      const leftData = leftOperation["value"][tab.id]
      if (leftData && leftData.length > 0) {
        let isExistCheck = leftData.filter(obj => obj === e.dictDataCode)
        if (isExistCheck.length > 0) {
          leftData.forEach((v) => {
            if (v === e.dictDataCode) {
              leftData.splice(leftData.indexOf(v), 1)
            }
          })
        }
      }
    })
    /** 点击右侧，右侧清空，左侧数据回显*/
    if (rDataList["value"][tabName] && rDataList["value"][tabName].length > 0) {
      rDataList["value"][tabName].forEach(filed => {
        if (filed.dictDataCode === e.dictDataCode) filed.display = 'block';
      })
    }
  }
  if (props.paramMode === 'zzbdweh' || props.paramMode === 'zzshzdylzdycx') {
    let isExistGroupData = leftGroupData.value.filter(obj => obj.type === e.configTitle)
    if (isExistGroupData.length > 0) {
      isExistGroupData[0].list.forEach((v) => {
        if (v.dictDataCode === e.dictDataCode) {
          isExistGroupData[0].list.splice(isExistGroupData[0].list.indexOf(v), 1)
          if (isExistGroupData[0].list.length <= 0) {
            let index = leftGroupData.value.findIndex(element => element.fieldId === v.configName);
            //删除右侧分组数组
            leftGroupData.value.splice(index, 1)
          }
        }
      })
    }
  }
  let checkedCount = leftOperation["value"][tabName] ? leftOperation["value"][tabName].length : 0;
  let dataCount = rDataList["value"][tabName] ? rDataList["value"][tabName].length : 0;
  checkAll["value"][tabName] = checkedCount === dataCount
  isIndeterminate["value"][tabName] = checkedCount > 0 && checkedCount < dataCount;
  emits('done', leftOperationArray.value);
}

const querySelectorConfig = async () => {
  let obj = {}
  if (props.paramMode) obj.paramMode = props.paramMode
  await getSelectorConfig(obj).then((resData) => {
    tableTabs.value = resData
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

watch(
  () => tableTabs.value,
  (tableTabs) => {
    if (tableTabs.length > 0) {
      tableTabs.forEach(tab => {
        let queryParams = {}
        if (tab.columns) {
          queryParams = {
            'params': JSON.parse(tab.columns),
          }
        }
        if (tab.url) {
          queryParams = {
            'dictFieldUrl': tab.url,
            'params': {userType: props.userType ? props.userType : tab?.userType},
          }
        }
        queryParams.userType = props.userType ? props.userType : tab?.userType;
        if (tab.name === 'zzjbxx' || tab.name === 'zzzdyjbxx') {
          queryParams.valueField = "fieldEn";
          queryParams.textField = "fieldZh";
          // listShowFlag: '是',
          queryParams['params'] = {
            tempField: '否',
            listFlag: '否',
            userType: props.userType ? props.userType : tab?.userType
          };
        }
        if (tab.name === 'zzzdysqzd') {//自定义申请字段
          queryParams.valueField = "fieldEn";
          queryParams.textField = "fieldZh";
          queryParams['params'] = {listFlag: '否', projectId: props.currentXmId, type: routeType, infoType: 'apply'};
        }
        if (tab.name === 'zzdtxx') {
          queryParams.valueField = "id";
          queryParams.textField = "groupName";
          queryParams['params'] = {
            listFlag: '是',
            showFlag: '是',
            userType: props.userType ? props.userType : tab?.userType
          };
        }
        let rData = []
        getDicFieldValueByUrl(tab.url, queryParams.params).then((list) => {
          let valueField = queryParams?.valueField ?? 'id';
          let textField = queryParams?.textField ?? 'name';
          if (list) {
            list.forEach((resData => {
              let obj = {'dictDataCode': resData[valueField], 'dictDataName': resData[textField], ...resData}
              if (tab.name === 'zzzdyjbxx' || tab.name === 'zzzdysqzd') {
                obj.projectId = props.currentXmId;
                obj.customType = props.currentDiyType;
                obj.type = routeType;
                obj.infoType = tab.name === 'zzzdyjbxx' ? "base" : resData.infoType;
              }
              obj.display = 'block';
              rData.push(obj)
            }))
            rDataList["value"][tab.id] = rData
          }
        }).catch((e) => {
          EleMessage.error(e.message);
        });
      })
      // 使用setTimeout
      setTimeout(() => {
        if (props.paramMode === 'zzshzdylzdycx') {
          // currentDiyType
          queryApprovalCustomField()
        } else {
          queryBaseFields();
        }
      }, 100);

      setTimeout(() => {
        if (props.paramMode !== 'zzshzdylzdycx') queryGroupList();
      }, 150);
    }
  },
  {immediate: true}
)

const queryApprovalCustomField = () => {
  getApprovalCustomField(props.currentXmId, {customType: props.currentDiyType}).then((list) => {
    if (list) {
      if (list.length > 0) {
        list.forEach(e => {
          e.name = e.fieldZh
          e.value = e.fieldEn
          e.dictDataCode = e.fieldEn
          e.dictDataName = e.fieldZh
          e.configTitle = e.infoType === 'base' ? '基本信息' : '申请字段'
          e.configKey = e.infoType === 'base' ? 'zzzdyjbxx' : 'zzzdysqzd'
          let getGroupData = tableTabs.value.filter(obj => obj.key === e.configKey)
          e.configName = getGroupData[0].id
          e.configId = getGroupData[0].id
        })
        leftOperationArray.value = list;
        leftGroupData.value = groupArr(leftOperationArray.value, 'configTitle', 'configName');
        leftGroupData.value.forEach((lgv) => {
          leftOperation["value"][lgv.fieldId] = lgv.list.map(item => item.value);
        })

        //   let tabName = getGroupData[0].id
        //   let checkedCount = leftOperation.value[tabName] ? leftOperation.value[tabName].length : 0;
        //   let dataCount = rDataList.value[tabName] ? rDataList.value[tabName].length : 0;
        //   checkAll.value[tabName] = checkedCount === dataCount
        //   isIndeterminate.value[tabName] = checkedCount > 0 && checkedCount < dataCount;
        emits('done', leftOperationArray.value);
      }
    }
    loading.value = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
}

/** 查询基本信息已设置的字段信息 */
const queryBaseFields = () => {
  getFormTemplateField({
    projectId: props.currentXmId,
    type: routeType,
    infoType: ['base']
  }).then((list) => {
    if (list.length > 0) {
      let getGroupData = tableTabs.value.filter(obj => obj.key === 'zzjbxx')
      list.forEach(e => {
        e.name = e.fieldZh
        e.value = e.fieldEn
        e.configId = e.groupId
        e.configKey = 'zzjbxx'
        e.configName = getGroupData[0].id
        e.configTitle = '基本信息'
        e.dictDataCode = e.fieldEn
        e.dictDataName = e.fieldZh
        leftOperationArray.value.push(e)
      })
      if (getGroupData.length > 0) {
        let ids = list.map(item => item.value);
        leftOperation["value"][getGroupData[0].id] = ids;
        leftGroupData.value = groupArr(leftOperationArray.value, 'configTitle', 'configName');
        let tabName = getGroupData[0].id

        /**初始化左右侧数据回显*/
        if (rDataList["value"][tabName] && rDataList["value"][tabName].length > 0) {
          rDataList["value"][tabName].forEach(filed => {
            if (ids && ids.length > 0) {
              ids.forEach(fieldEn => {
                if (filed.fieldEn === fieldEn) filed.display = 'none';
              })
            }
          })
        }
        console.log(rDataList["value"][tabName])
        let checkedCount = leftOperation["value"][tabName] ? leftOperation["value"][tabName].length : 0;
        let dataCount = rDataList["value"][tabName] ? rDataList["value"][tabName].length : 0;
        checkAll["value"][tabName] = checkedCount === dataCount
        isIndeterminate["value"][tabName] = checkedCount > 0 && checkedCount < dataCount;
        emits('done', leftOperationArray.value);
        emits('updateInfoBtn');
      }
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

/** 查询多条信息已设置的组 */
const queryGroupList = () => {
  getFormGroup({
    projectId: props.currentXmId,
    type: routeType,
    infoType: ['list'],
    dicGroupIdIsNull: false//默认查询基础表组信息
  }).then((list) => {
    nextTick(() => {
      if (list.length > 0) {
        let getGroupData = tableTabs.value.filter(obj => obj.key === 'zzdtxx')
        list.forEach(e => {
          e.groupId = e.id
          e.name = e.groupName
          e.value = e.dicGroupId
          e.configId = e.dicGroupId
          e.configKey = 'zzdtxx'
          e.configName = getGroupData[0].id
          e.configTitle = "多条信息"
          e.dictDataCode = e.dicGroupId
          e.dictDataName = e.groupName
          leftOperationArray.value.push(e)
        })
        if (getGroupData.length > 0) {
          let ids = list.map(item => item.value);
          leftOperation["value"][getGroupData[0].id] = ids;
          leftGroupData.value.push({
            type: getGroupData[0].title,
            fieldId: getGroupData[0].id,
            list: list,
            size: list.length
          })
          let tabName = getGroupData[0].id
          let checkedCount = leftOperation["value"][tabName] ? leftOperation["value"][tabName].length : 0;
          let dataCount = rDataList["value"] && rDataList["value"][tabName] ? rDataList["value"][tabName].length : 0;
          checkAll["value"][tabName] = checkedCount === dataCount
          isIndeterminate["value"][tabName] = checkedCount > 0 && checkedCount < dataCount;
          emits('done', leftOperationArray.value);
        }
      }
    })
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

watch(
  () => props.RandomString,
  (RandomString) => {
    if (RandomString) {
      querySelectorConfig()
    }
  },
  {immediate: true}
)

</script>

<style lang="scss" scoped>
//:deep( .rightChecked .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner ) {
//  display: none !important;
//}
</style>
