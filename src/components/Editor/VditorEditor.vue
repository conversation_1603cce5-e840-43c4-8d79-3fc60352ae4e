<template>
  <div ref="vditorContainer" class="vditor-wrapper"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import Vditor from 'vditor';
import 'vditor/dist/index.css';

const props = defineProps({
  modelValue: String,       // 双向绑定内容
  mode: {                   // 模式：markdown / wysiwyg
    type: String,
    default: 'wysiwyg',
    validator: (v) => ['markdown', 'wysiwyg'].includes(v)
  }
});

const emit = defineEmits(['update:modelValue']);
const vditorContainer = ref(null);
const vditorInstance = ref(null);

// 初始化编辑器
onMounted(() => {
  vditorInstance.value = new Vditor(vditorContainer.value, {
    height: 500,
    mode: props.mode,
    value: props.modelValue || '',
    cache: {
      enable: false // 禁用本地缓存
    },
    input: (content) => {
      emit('update:modelValue', content);
    },
    // toolbar: [
    //   'emoji',
    //   'headings',
    //   'bold',
    //   'italic',
    //   'link',
    //   'upload',
    //   'code',
    //   'table',
    //   '|',
    //   'outline',
    //   'preview',
    //   'fullscreen'
    // ]
  });
});

// 内容变化时同步
watch(() => props.modelValue, (newVal) => {
  if (newVal !== vditorInstance.value?.getValue()) {
    vditorInstance.value?.setValue(newVal || '');
  }
});

// 销毁实例
onBeforeUnmount(() => {
  vditorInstance.value?.destroy();
});
</script>

<style>
.vditor-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
