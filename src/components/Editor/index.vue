<template>
  <div class="editor-container">
    <!-- 富文本编辑器 -->
    <div ref="editor" class="quill-editor" style="height: 400px;"></div>

    <!-- 上传图片进度条 -->
    <el-progress v-if="uploading" :percentage="uploadProgress" status="active"></el-progress>

    <!-- 预览图片 -->
    <div v-if="imagePreviews.length" class="image-previews">
      <div v-for="(image, index) in imagePreviews" :key="index" class="preview-item">
        <img :src="image.thumbUrl" alt="Uploaded Image" class="preview-img" />
        <el-button type="text" @click="removePreview(index)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Quill from 'quill'
import Compressor from 'compressorjs'
import Cropper from 'cropperjs'
import { ElButton, ElProgress, ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  components: { ElButton, ElProgress },
  data() {
    return {
      uploading: false,
      uploadProgress: 0,
      imagePreviews: [],
      quill: null, // Quill编辑器实例
    }
  },
  mounted() {
    this.initEditor()
  },
  methods: {
    // 初始化Quill编辑器
    initEditor() {
      const options = {
        theme: 'snow',
        modules: {
          toolbar: [
            [{ header: '1' }, { header: '2' }, { font: [] }],
            [{ list: 'ordered' }, { list: 'bullet' }],
            ['bold', 'italic', 'underline'],
            ['link', 'image'], // 添加图片按钮
            [{ align: [] }],
            ['clean'],
          ],
        },
      }

      this.quill = new Quill(this.$refs.editor, options)

      // 监听图片上传
      this.quill.getModule('toolbar').addHandler('image', this.handleImageUpload)
    },

    // 处理图片上传
    handleImageUpload() {
      const input = document.createElement('input')
      input.setAttribute('type', 'file')
      input.setAttribute('accept', 'image/*')
      input.click()

      input.onchange = async (e) => {
        const file = e.target.files[0]
        if (file) {
          try {
            const compressedFile = await this.compressImage(file)
            this.imagePreviews.push({
              thumbUrl: URL.createObjectURL(compressedFile),
              file: compressedFile,
            })
            this.uploadImage(compressedFile)
          } catch (error) {
            ElMessage.error('图片压缩失败')
          }
        }
      }
    },

    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        new Compressor(file, {
          quality: 0.6,
          success(result) {
            resolve(result)
          },
          error(err) {
            reject(err)
          },
        })
      })
    },

    // 图片上传
    async uploadImage(file) {
      this.uploading = true
      try {
        const formData = new FormData()
        formData.append('file', file)

        const res = await axios.post('/upload', formData, {
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          },
        })

        if (res.data.success) {
          const imageUrl = res.data.url
          this.insertImageToEditor(imageUrl)
        } else {
          ElMessage.error('上传失败')
        }
      } catch (err) {
        ElMessage.error('上传出错')
      } finally {
        this.uploading = false
      }
    },

    // 将上传的图片插入到Quill编辑器中
    insertImageToEditor(imageUrl) {
      const range = this.quill.getSelection()
      this.quill.insertEmbed(range.index, 'image', imageUrl)
    },

    // 删除图片预览
    removePreview(index) {
      this.imagePreviews.splice(index, 1)
    },

    // 处理图片裁剪
    handlePreview(file) {
      this.openCropper(file)
    },

    // 初始化裁剪功能
    openCropper(file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.src = e.target.result
        img.onload = () => {
          const cropperModal = document.createElement('div')
          cropperModal.innerHTML = `
            <div>
              <img id="cropper-image" src="${img.src}" />
              <button id="cropper-save">保存裁剪</button>
            </div>
          `
          document.body.appendChild(cropperModal)

          const cropper = new Cropper(document.getElementById('cropper-image'), {
            aspectRatio: 1,
            crop: (event) => {
              console.log(event.detail.width, event.detail.height)
            },
          })

          document.getElementById('cropper-save').addEventListener('click', () => {
            const croppedCanvas = cropper.getCroppedCanvas()
            croppedCanvas.toBlob((blob) => {
              const croppedFile = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' })
              this.uploadCroppedImage(croppedFile)
              document.body.removeChild(cropperModal)
            })
          })
        }
      }
      reader.readAsDataURL(file)
    },

    // 上传裁剪后的图片
    async uploadCroppedImage(file) {
      try {
        const formData = new FormData()
        formData.append('file', file)

        const res = await axios.post('/upload', formData)

        if (res.data.success) {
          ElMessage.success('裁剪并上传成功')
        } else {
          ElMessage.error('裁剪上传失败')
        }
      } catch (err) {
        ElMessage.error('裁剪上传失败')
      }
    },
  },
}
</script>

<style scoped>
.editor-container {
  width: 80%;
  margin: 0 auto;
}

.quill-editor {
  border: 1px solid #ccc;
  border-radius: 4px;
}

.image-previews {
  display: flex;
  flex-wrap: wrap;
}

.preview-item {
  margin: 10px;
  position: relative;
}

.preview-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.el-upload {
  margin-bottom: 20px;
}

.el-progress {
  margin-top: 20px;
}
</style>
