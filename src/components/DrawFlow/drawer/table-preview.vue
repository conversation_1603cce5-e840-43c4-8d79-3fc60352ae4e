<template>
  <ele-page
    flex-table
    :style="
      [
        'leaveConditions',
        'leaveProcessConditions',
        'questionSel_',
        'projectTemplateField',
        'PagerQuestionSel_',
        'surveyQuestionSel_'
      ].some((keyword) => approverType.includes(keyword))
        ? { padding: '8px 0px 0 0px !important' }
        : { padding: '0!important' }
    "
  >
    <ele-card
      flex-table
      :body-style="{ height: 'auto', padding: '5px 0!important' }"
    >
      <template #header>
        <ele-text v-if="dataName">{{ dataName }}</ele-text>
        <ele-tooltip
          v-if="approverType === 'zizhu'"
          style="cursor: pointer"
          effect="light"
          placement="left"
          :popperOptions="{
            modifiers: [{ name: 'offset', options: { offset: [-8, 10] } }]
          }"
        >
          <IconPark name="help" size="18" strokeWidth="3" />
          <template #content>
            <div>英文名下拉组件的选项数据：</div>
            <div
              >c1...c开始的是系统内置短文本选项，l1...l开始的是系统内置长文本选项</div
            >
            <div>表格行可以拖动拍序</div>
          </template>
        </ele-tooltip>

        <ele-alert
          v-if="approverType === 'coursePropertiesPercent'"
          :closable="false"
          effect="light"
          :style="{
            maxWidth: '89%',
            fontWeight: 'normal',
            padding: '6px',
            marginLeft: '-5px'
          }"
        >
          <span>
            智育成绩的计算通常综合多个维度，包括学科考试成绩、课堂表现、作业完成情况及实践创新能力等。不同部分的权重分配可以根据实际需求进行调整，以更全面地衡量学生的学习能力和综合素质。
          </span>
        </ele-alert>
      </template>
      <template #extra>
        <el-button
          v-if="isCanShowAddBtn(approverType)"
          type="primary"
          size="small"
          plain
          class="ele-btn-icon"
          @click="add"
        >
          新建
        </el-button>
      </template>
      <el-form ref="formRef" size="small" :model="form" @submit.prevent="">
        <div>
          <ele-table
            size="small"
            border
            style="table-layout: fixed; height: auto"
          >
            <thead>
              <tr>
                <th
                  v-for="header in initTableHeader"
                  :style="
                    header?.hide
                      ? 'display: none;'
                      : 'width:' + header.minWidth + 'px'
                  "
                >
                  {{ header.label }}
                </th>
                <th
                  v-if="
                    !['nodeStatus'].includes(approverType) &&
                    workFlowReadonly !== true
                  "
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    right: 0,
                    zIndex: 98,
                    width: approverType === 'zizhu' ? '110px' : '83px'
                  }"
                >
                  操作
                </th>
              </tr>
            </thead>
            <tbody
              v-if="form.users.length === 0"
              :class="{ 'empty-background': form.users.length === 0 }"
            >
              <tr>
                <td :colspan="initTableHeader.length + 1">
                  <el-result style="margin-top: -150px; overflow: hidden">
                    <template #icon>
                      <empty-list />
                    </template>
                    <template #sub-title>
                      <ele-text type="placeholder"
                        >暂无数据,请点击右上角新建</ele-text
                      >
                    </template>
                  </el-result>
                </td>
              </tr>
            </tbody>
            <template
              v-if="
                [
                  'nodeStatus',
                  'evaluateScopeList',
                  'questionSel_',
                  'PagerQuestionSel_',
                  'surveyQuestionSelWjst_',
                  'surveyQuestionSel_'
                ].some((keyword) => approverType.includes(keyword))
              "
            >
              <tr v-for="(row, index) in form.users" :key="row.key">
                <td
                  v-for="item in initTableHeader"
                  :style="
                    item?.hide
                      ? 'display: none;'
                      : 'width:' + item.minWidth + 'px'
                  "
                >
                  <el-form-item
                    v-if="
                      [
                        'nodeStatus',
                        'questionSelCode',
                        'extendsField',
                        'pyccmc',
                        'njmc',
                        'xymc'
                      ].includes(item.type)
                    "
                    label=""
                    class="form-error-popper"
                    style="margin-bottom: 0 !important"
                  >
                    <div class="editable-cell-text">{{ row[item.prop] }}</div>
                  </el-form-item>
                  <el-form-item
                    v-else-if="item.type === 'glsj'"
                    label=""
                    class="form-error-popper"
                    style="margin-bottom: 0 !important"
                  >
                    <div v-if="workFlowReadonly" class="editable-cell-text">{{
                      row[item.prop]
                    }}</div>
                    <template v-else>
                      <dict-data
                        v-if="row['fieldEn'] !== 'result'"
                        placeholder="请选择关联事件"
                        code="nodeStatusGlsj"
                        type="multipleSelect"
                        :dicQueryParams="{
                          dictFieldUrl: '/workflow/workflow-event',
                          valueField: 'id',
                          textField: 'eventName'
                        }"
                        :model-value="row[item.prop]"
                        @update:modelValue="
                          (value) => updateValue(index, item.prop, value)
                        "
                      />
                    </template>
                  </el-form-item>
                  <template v-else>
                    <TableFormItem
                      v-if="row.isEdit"
                      :item="item"
                      :index="index"
                      :model="row"
                      @updateValue="
                        (value) => updateValue(index, item.prop, value)
                      "
                    >
                      <template
                        v-for="name in Object.keys($slots).filter(
                          (k) =>
                            ![
                              'default',
                              'footer',
                              'topExtra',
                              'bottomExtra'
                            ].includes(k)
                        )"
                        #[name]="slotProps"
                      >
                        <slot :name="name" v-bind="slotProps || {}"></slot>
                      </template>
                    </TableFormItem>
                    <div v-else class="editable-cell-text">{{
                      row[item.prop]
                    }}</div>
                  </template>
                </td>
                <td
                  v-if="approverType !== 'nodeStatus'"
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    right: 0,
                    zIndex: 98
                  }"
                >
                  <div style="display: inline; align-items: center">
                    <el-link
                      type="danger"
                      underline="never"
                      @click="remove(element, index)"
                    >
                      删除
                    </el-link>
                  </div>
                </td>
              </tr>
            </template>
            <template v-else>
              <vue-draggable
                tag="tbody"
                item-key="key"
                v-model="form.users"
                handle=".sort-handle"
                :animation="300"
                :set-data="() => void 0"
                :force-fallback="true"
                @update="updateSort"
              >
                <template #item="{ element, index }">
                  <tr :key="element.key">
                    <td
                      v-for="item in initTableHeader"
                      :style="
                        item?.hide
                          ? 'display: none;'
                          : 'width:' + item.minWidth + 'px'
                      "
                    >
                      <el-form-item
                        v-if="item.type === 'conditionValue'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          type="select"
                          filterable
                          :code="element['loadDataType']"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'processEvent'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          v-if="element.isEdit"
                          type="select"
                          filterable
                          code="eventType"
                          :dicQueryParams="{
                            dictFieldUrl: '/workflow/workflow-event',
                            valueField: 'id',
                            textField: 'eventName',
                            params: { eventType: '审核表单' }
                          }"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'extendsField'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <template
                          v-if="approverType === 'zizhu' && element.isEdit"
                        >
                          <el-select
                            v-model="element[item.prop]"
                            :disabled="workFlowReadonly"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in zizhuFieldEn"
                              :key="item"
                              :label="item"
                              :value="item"
                              @click="selectChange()"
                            />
                          </el-select>
                        </template>
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="
                          [
                            'bankCount',
                            'surveyBankCount',
                            'nodeStatus',
                            'xn',
                            'kcsxdm'
                          ].includes(item.type)
                        "
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <div class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="
                          item.type === 'examQsum' && element['bankCount']
                        "
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                        :rules="rules"
                        :prop="'users.' + index + '.' + item.prop"
                      >
                        <!--                        <div class="editable-cell-text">{{ element[item.prop] }}</div>-->
                        <!--                        v-model="element[item.prop]"-->
                        <el-input-number
                          :max="element['bankCount']"
                          controls-position="right"
                          class="ele-fluid"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                      </el-form-item>
                      <el-form-item
                        v-else-if="
                          item.type === 'surveyQsum' &&
                          element['surveyBankCount']
                        "
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                        :rules="rules"
                        :prop="'users.' + index + '.' + item.prop"
                      >
                        <!--                        <div class="editable-cell-text">{{ element[item.prop] }}</div>-->
                        <!--                        v-model="element[item.prop]"-->
                        <el-input-number
                          :max="element['surveyBankCount']"
                          controls-position="right"
                          class="ele-fluid"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'regExpression'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          v-if="element.isEdit"
                          type="select"
                          filterable
                          :disabled="
                            !['input', 'inputNumber'].includes(
                              element['controlType']
                            )
                          "
                          code="RegExp"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <template v-else-if="item.type === 'loadDataType'">
                        <el-form-item
                          v-if="approverType === 'zizhu'"
                          label=""
                          class="form-error-popper"
                          style="margin-bottom: 0 !important"
                        >
                          <dict-data
                            v-if="element.isEdit"
                            type="select"
                            filterable
                            code="listDicCode"
                            :model-value="element[item.prop]"
                            @update:modelValue="
                              (value) => updateValue(index, item.prop, value)
                            "
                          />
                          <div v-else class="editable-cell-text">{{
                            element[item.prop]
                          }}</div>
                        </el-form-item>
                        <el-form-item
                          v-else
                          label=""
                          class="form-error-popper"
                          style="margin-bottom: 0 !important"
                        >
                          <dict-data
                            v-if="element.isEdit"
                            type="select"
                            filterable
                            :disabled="
                              !baseCodeTypes.includes(element['controlType'])
                            "
                            code="listDicCode"
                            :model-value="element[item.prop]"
                            @update:modelValue="
                              (value) => updateValue(index, item.prop, value)
                            "
                          />
                          <div v-else class="editable-cell-text">{{
                            element[item.prop]
                          }}</div>
                        </el-form-item>
                      </template>
                      <el-form-item
                        v-else-if="item.type === 'loadDataUrlId'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          v-if="element.isEdit"
                          type="select"
                          filterable
                          :disabled="
                            !['other', 'studentXsda'].includes(
                              element['loadDataType']
                            )
                          "
                          :code="
                            element['loadDataType'] === 'other'
                              ? 'listDicUrl'
                              : 'studentXsda'
                          "
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'defaultVal'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <el-input
                          :model-value="element[item.prop]"
                          placeholder="请输入"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                          :disabled="
                            ['studentXsda'].includes(element['loadDataType'])
                          "
                        />
                      </el-form-item>
                      <!--                      <el-form-item v-else-if="['nodeStatus'].includes(item.type)"-->
                      <!--                                    label=""-->
                      <!--                                    class="form-error-popper"-->
                      <!--                                    style="margin-bottom: 0 !important">-->
                      <!--                        <el-input readonly :model-value="element[item.prop]" placeholder="请输入"/>-->
                      <!--                      </el-form-item>-->
                      <TableFormItem
                        v-else-if="element.isEdit"
                        :item="item"
                        :index="index"
                        :model="element"
                        @updateValue="
                          (value) => updateValue(index, item.prop, value)
                        "
                      >
                        <template
                          v-for="name in Object.keys($slots).filter(
                            (k) =>
                              ![
                                'default',
                                'footer',
                                'topExtra',
                                'bottomExtra'
                              ].includes(k)
                          )"
                          #[name]="slotProps"
                        >
                          <slot :name="name" v-bind="slotProps || {}"></slot>
                        </template>
                      </TableFormItem>
                      <div v-else class="editable-cell-text">{{
                        element[item.prop]
                      }}</div>
                    </td>
                    <td
                      v-if="workFlowReadonly !== true"
                      :style="{
                        textAlign: 'center',
                        position: 'sticky',
                        right: 0,
                        zIndex: 98
                      }"
                    >
                      <div style="display: inline; align-items: center">
                        <!--                        <el-link-->
                        <!--                          v-if="(approverType.includes('randomTopicSelection_')-->
                        <!--                           || approverType.includes('randomSurveyTopicSelection_'))&&element.isEdit"-->
                        <!--                          type="success"-->
                        <!--                          underline="never"-->
                        <!--                          @click="done(element)">-->
                        <!--                          完成-->
                        <!--                        </el-link>-->
                        <el-link
                          v-if="
                            [
                              'randomTopicSelection_',
                              'randomSurveyTopicSelection_',
                              'coursePropertiesPercent'
                            ].some((keyword) =>
                              approverType.includes(keyword)
                            ) && element.isEdit
                          "
                          type="success"
                          underline="never"
                          @click="done(element)"
                        >
                          完成
                        </el-link>
                        <el-divider
                          v-if="
                            [
                              'randomTopicSelection_',
                              'randomSurveyTopicSelection_',
                              'coursePropertiesPercent'
                            ].some((keyword) => approverType.includes(keyword))
                          "
                          direction="vertical"
                          style="margin: 0 4px"
                        />
                        <el-link
                          type="danger"
                          underline="never"
                          @click="remove(element, index)"
                        >
                          删除
                        </el-link>
                        <el-divider
                          v-if="
                            approverType === 'zizhu' &&
                            ['select', 'radio', 'radioButton'].includes(
                              element['controlType']
                            )
                          "
                          direction="vertical"
                          style="margin: 0 4px"
                        />
                        <el-link
                          v-if="
                            approverType === 'zizhu' &&
                            ['select', 'radio', 'radioButton'].includes(
                              element['controlType']
                            )
                          "
                          type="primary"
                          underline="never"
                          @click="handleSetfield(element)"
                        >
                          附加条件
                        </el-link>
                      </div>
                    </td>
                  </tr>
                </template>
              </vue-draggable>
            </template>
            <!--            </template>-->
            <!--            <tr v-if="!form.users || !form.users.length" style="text-align: center">-->
            <!--              &lt;!&ndash;              <td :colspan="initTableHeader.length+1" >&ndash;&gt;-->

            <!--              &lt;!&ndash;              </td>&ndash;&gt;-->
            <!--            </tr>-->
          </ele-table>
        </div>
      </el-form>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { computed, ref, watch, nextTick, reactive, markRaw } from 'vue';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { PlusOutlined } from '@/components/icons';
  import {
    arrayTypes,
    regionsArrayTypes,
    selectTypes,
    stringTypes,
    uploadTypes,
    baseCodeTypes
  } from '@/components/ProForm/util';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getFormExtendsField,
    listStatusInit
  } from '@/views/dingding-flow/api/index.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import VueDraggable from 'vuedraggable';
  import { DragOutlined } from '@/components/icons';
  import {
    comQuestionSels,
    groupArr,
    insertAtIndex,
    removeDuplicates
  } from '@/utils/common_bak2.js';
  import {
    getFormTemplateField,
    getFormTemplateFieldList,
    operation,
    removes
  } from '@/views/zizhu/api/form-template-field-index.js';
  import { ElMessageBox } from 'element-plus';
  import {
    CompressOutlined,
    ExpandOutlined,
    QuestionCircleOutlined
  } from '@/components/icons/index.js';
  import {
    getConditionField,
    removes as removeCondition
  } from '@/views/zizhu/condition/api/field-index.js';
  import {
    getExamQuestionsById,
    getExamQuestionsSum
  } from '@/views/exam/exam-questions/api/index.js';
  import {
    getExamPaperQuestionsById,
    removesExamPaperQuestionsRules,
    saveExamPaperQuestionsRules
  } from '@/views/exam/test-paper/bank-data/api/index.js';

  import { getExamPaperQuestionRule } from '@/views/exam/test-paper/api/rule-index.js';
  import { removeFormProjectTemplateField } from '@/views/zizhu/form-project-template-field/api/index.js';
  import {
    getSurveyPQuestionsById,
    getSurveyQuestionsById
  } from '@/views/survey/test-paper/bank-data/api/index.js';
  import {
    getSurveyQuestionRule,
    getSurveyQuestionRuleById,
    operationSurveyQuestionsRule,
    removesSurveyQuestionsRule
  } from '@/views/survey/test-paper/api/rule-index.js';
  import { gettSurveyQuestionsSum } from '@/views/survey/survey-questions/api/index.js';
  import { getEvaluateConfigScopeList } from '@/views/evaluate/evaluate-config/api/index.js';
  import IconSvg from '@/views/exception/500/components/icon-svg.vue';
  import EmptyList from '@/components/illustrations/empty-list.vue';
  import EmptyPage from '@/components/illustrations/empty-page.vue';
  import {
    getScoreCoursePropertiesPercent,
    operationCoursePropertiesPercent,
    removesCoursePropertiesPercent
  } from '@/views/evaluate/score/course-properties-percent/api/index.js';
  import IconPark from '@/components/IconPark/index.vue';

  const props = defineProps({
    dataName: String,
    approverType: String,
    approverId: String,
    currentNode: Object, //资助申请表单，左侧组信息
    selectorData: Object,
    currentProject: Object, //项目信息
    approverData: Object,
    // approverData: {
    //   type: Array,
    //   required: true,
    // },
    RandomString: String,
    workFlowReadonly: {
      type: Boolean,
      default: false
    },
    /** 请假类型id(请假类型流程配置使用)*/
    projectId: String,
    /** 申请类型Id(请假类型流程配置使用) */
    requestType: String
  });

  // 定义emits
  const emits = defineEmits([
    'onDoneGroup',
    'child-event',
    'FieldLinkEvent',
    'doneRefreshGroup'
  ]);

  /** 请求状态 */
  const loading = ref(true);

  /** 表头 */
  const initTableHeader = ref([]);

  /** zizhu表数据 */
  const initTableData = ref([]);

  /** 资助申请字段英文下拉框数据 */
  const allApproverData = ref([]);
  const zizhuFieldEn = ref([]);
  const zizhuFieldEnList = ref([]);
  /** 资助审核字段英文 */
  const shExtendsFieldEn = ref([]);
  const shExtendsFieldEnList = ref([]);

  const userStore = useUserStore();
  const { pageHeight, dicts } = storeToRefs(userStore);

  /** 表单实例 */
  const formRef = ref(null);

  const isLoading = ref(true);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    users: []
  });

  /** 删除 */
  const remove = (_row, i) => {
    if (props.approverType === 'zizhu') {
      if (_row?.id) {
        removeOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      selectChange();
      // 找到删除的对象索引
      let index = initTableData.value.findIndex(
        (item) => item.fieldEn === _row.fieldEn
      );
      // 确保对象存在于数组中
      if (index !== -1) {
        // 删除对象
        initTableData.value.splice(index, 1);
      }
      emits('onDoneGroup', initTableData.value);
    } else if (props.approverType === 'condition') {
      if (_row?.id) {
        removeConditionOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData: props.selectorData,
        workflowNodeForm: form.users
      };
      emits('onDoneGroup', rData);
    } else if (
      props.approverType === 'leaveConditions' ||
      props.approverType === 'leaveProcessConditions'
    ) {
      if (_row?.id) {
        removeConditionOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      emits('onDoneGroup', form.users);
    } else if (
      ['questionSel_', 'PagerQuestionSel_', 'surveyQuestionSel_'].some(
        (keyword) => props.approverType.includes(keyword)
      )
    ) {
      //题库选项配置
      // props.approverType.includes('questionSel_')
      // || props.approverType.includes('PagerQuestionSel_')
      // || props.approverType.includes('surveyQuestionSel_')
      form.users.splice(i, 1);
      //多选题/单选题，删除选项后，code（A，B，C...需要重新排序）
      form.users.forEach((e, index) => {
        e.code = comQuestionSels()[index];
      });
      console.log(form.users);
      emits('onDoneGroup', form.users);
    } else if (props.approverType.includes('randomTopicSelection_')) {
      //抽题规则设置
      if (_row?.id) {
        removeQuestionsRuleOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      emits('onDoneGroup', form.users);
    } else if (props.approverType.includes('randomSurveyTopicSelection_')) {
      //抽题规则设置
      if (_row?.id) {
        removeSurveyQuestionsRuleOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      emits('onDoneGroup', form.users);
    } else if (props.approverType.includes('projectTemplateField')) {
      //扩展字段设置
      if (_row?.id) {
        removeFormProjectTemplateFieldOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      emits('onDoneGroup', form.users);
    } else if (props.approverType.includes('coursePropertiesPercent')) {
      if (_row?.id) {
        removeCoursePropertiesPercent([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      emits('onDoneGroup', form.users);
    } else {
      form.users.splice(i, 1);
      if (props.approverType.includes('workflow_')) {
        //流程节点
        selectChangeSh();
      }
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData: props.selectorData,
        workflowNodeForm: form.users
      };
      emits('onDoneGroup', rData);
    }
  };

  /**
   * 资助可配置字段列表
   */
  const queryFormTemplateFieldList = () => {
    let obj = {
      listFlag: props.currentNode.listFlag === '是',
      projectId: props.currentNode.projectId,
      year: props.currentNode.year,
      notUsed: true
    };
    if (props.currentNode.listFlag === '是') obj.groupId = props.currentNode.id;
    getFormTemplateFieldList(obj)
      .then((list) => {
        if (list) {
          zizhuFieldEn.value = list;
          zizhuFieldEnList.value = list;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /**
   * 工作流获取审核设置扩展字段列表
   */
  const queryFormExtendsField = () => {
    getFormExtendsField()
      .then((list) => {
        if (list) {
          shExtendsFieldEn.value = list;
          shExtendsFieldEnList.value = list;
          // if (props.approverType.includes('workflow_')) {
          //   add()
          // }
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 工作流审核英文下拉更新选中数据 */
  const selectChangeSh = () => {
    let newData = [...shExtendsFieldEnList.value, ...form.users];
    if (newData && newData.length > 0) {
      let arraySomeMap = newData.map((item) => {
        return item.fieldEn;
      });
      shExtendsFieldEn.value = removeDuplicates(
        shExtendsFieldEnList.value,
        arraySomeMap
      );
    }
  };

  const queryConditionField = () => {
    getConditionField({ conditionId: props.approverId })
      .then((list) => {
        if (list && list.length > 0) {
          list.forEach((item) => {
            item.isEdit = true;
          });
          form.users = list;
          let rData = {
            fieldEn: props.approverType,
            approverId: props.approverType,
            selectorData: props.approverData,
            workflowNodeForm: form.users
          };
          emits('onDoneGroup', rData);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 获取表单字段 */
  const initModel = computed(() => {
    const fieldFormResult = [];
    initTableHeader.value.forEach((item) => {
      if (item.type) {
        fieldFormResult[item.prop] = null;
        if (
          uploadTypes.includes(item.type) ||
          regionsArrayTypes.includes(item.type) ||
          arrayTypes.includes(item.type)
        ) {
          fieldFormResult[item.prop] = [];
        }
        if (
          stringTypes.includes(item.type) ||
          selectTypes.includes(item.type)
        ) {
          fieldFormResult[item.prop] = '';
        }
      }
    });
    return fieldFormResult;
  });

  /** 关联关系设置 */
  const handleSetfield = async (row) => {
    let newObj = {
      infoType: row.infoType,
      projectId: row.projectId,
      groupId: row.groupId,
      fieldId: row.id,
      fieldEn: row.fieldEn,
      controlType: row.controlType,
      loadDataType: row.loadDataType,
      loadDataUrl: row.loadDataUrl
    };
    // currentFieldLink.value = newObj
    // showFieldLink.value = true;
    emits('FieldLinkEvent', newObj);
  };

  // 更新次序
  const updateSort = (evt) => {
    evt.preventDefault();
    if (props.approverType === 'zizhu') {
      const tableDataGroup = groupArr(initTableData.value, 'groupId');
      let newTableData = [];
      tableDataGroup.forEach((item) => {
        if (item.type === props.approverId) {
          form.users.forEach((value, index) => {
            newTableData.push(value);
          });
        } else {
          item.list.forEach((value, index) => {
            newTableData.push(value);
          });
        }
      });
      const sortedButtonList = newTableData.map(function (value, index) {
        value.sort = index + 1;
        return value;
      });
      emits('onDoneGroup', sortedButtonList);
    } else {
      const sortedButtonList = form.users.map(function (value, index) {
        value.sort = index + 1;
        return value;
      });
      if (
        props.approverType.includes('randomTopicSelection_') ||
        props.approverType.includes('randomSurveyTopicSelection_')
      ) {
        emits('onDoneGroup', sortedButtonList);
      } else {
        let rData = {
          fieldEn: props.approverType,
          approverId: props.approverId,
          selectorData: props.selectorData,
          workflowNodeForm: sortedButtonList
        };
        emits('onDoneGroup', rData);
      }
    }
  };

  /** 添加 */
  const add = () => {
    console.log(initModel.value);
    let obj = {
      ...initModel.value,
      isEdit: true
    };
    let index = 1;
    let arrKeyIndex = [];
    if (props.approverType === 'zizhu') {
      // 使用map()获取每个keyIndex，然后使用 Math.max 求最大值
      arrKeyIndex = initTableData.value.map((e) => e.sort);
      obj.key = Date.now() + '-' + initTableData.value.length;
    } else {
      // 使用map()获取每个keyIndex，然后使用 Math.max 求最大值
      arrKeyIndex = form.users.map((e) => e.sort);
      obj.key = Date.now() + '-' + form.users.length;
    }
    if (arrKeyIndex.length > 0) {
      const maxValue = Math.max(...arrKeyIndex);
      index = maxValue + 1;
    }
    obj.sort = index;
    if (props.approverType === 'zizhu') {
      obj.projectId = props.currentNode.projectId;
      obj.infoType = props.currentNode.infoType;
      obj.type = props.currentNode.type;
      obj.year = props.currentNode.year;
      if (zizhuFieldEn.value.length > 0) {
        obj.fieldEn = zizhuFieldEn.value[0];
        obj.groupId = props.approverId;
        initTableData.value.push(obj);
        form.users.push(obj);
        selectChange();
      } else {
        EleMessage.error(`资助申请字段配置，英文名扩展字段已用完，请先添加`);
      }
      formRef['value']?.validate?.((valid, obj) => {
        if (!valid) {
          // const errors = obj ? Object.keys(obj).length : 0;
          // EleMessage.error(`有 ${errors} 项校验不通过1`);
          return false;
        }
      });
    } else if (props.approverType.includes('workflow_')) {
      selectChangeSh();
      if (shExtendsFieldEn.value.length > 0) {
        obj.fieldEn = shExtendsFieldEn.value[0];
        form.users.push(obj);
        // initTableData.value.push(obj);
      } else {
        EleMessage.error(`工作流审核配置，英文名扩展字段已用完，请先添加`);
      }
    } else if (
      ['questionSel_', 'PagerQuestionSel_', 'surveyQuestionSel_'].some(
        (keyword) => props.approverType.includes(keyword)
      )
    ) {
      //题库选项配置
      obj.code = comQuestionSels()[obj.sort - 1];
      form.users.push(obj);
    } else if (props.approverType.includes('coursePropertiesPercent')) {
      //课程属性占比配置
      obj.xn = props.currentProject.cpnf;
      form.users.push(obj);
    } else if (props.approverType.includes('randomTopicSelection_')) {
      //随机抽题,抽题规则设置
      obj.pid = props.currentNode.pid;
      obj.gid = props.currentNode.id;
      form.users.push(obj);
    } else if (props.approverType.includes('randomSurveyTopicSelection_')) {
      //随机抽题,抽题规则设置
      obj.sid = props.currentNode.sid;
      obj.gid = props.currentNode.id;
      form.users.push(obj);
    } else {
      if (props.approverType === 'condition') {
        //资助限制条件设置
        obj.conditionId = props.approverId;
      } else if (props.approverType === 'projectTemplateField') {
        //资助项目扩展字段维护
        obj.fieldEn = 'k' + index;
        obj.projectId = props.currentProject.id;
        obj.type = props.currentProject.type;
        obj.year = props.currentProject.year;
      } else {
        obj.fieldEn = 'bz' + index;
      }
      form.users.push(obj);
      if (props.approverType === 'projectTemplateField')
        emits('onDoneGroup', form.users);
    }
  };

  /** 更新值 */
  const updateValue = (index, prop, value) => {
    console.log('更新值=====', index, prop, value, form.users[index]);
    form.users[index][prop] = value;
    if (['sttype', 'cid'].includes(prop)) {
      if (form.users[index]['cid'] && form.users[index]['sttype']) {
        queryExamQuestionsSum(form.users[index]);
      }
    }
    if (['qtype', 'cid'].includes(prop)) {
      if (form.users[index]['cid'] && form.users[index]['qtype']) {
        querySurveyQuestionsSum(form.users[index]);
      }
    }
    if (prop === 'kcsxmc') {
      //课程属性名称
      /** 加载数据URL 更新listDicUrl*/
      let dicData = dicts.value['kcsxdmData'];
      if (dicData) {
        dicData.forEach((item) => {
          if (item['kcsxmc'] === value) {
            //课程属性代码
            form.users[index].kcsxdm = item.kcsxdm;
          }
        });
      }
    }
    if (prop === 'controlType') {
      form.users[index]['regExpression'] = null;
      form.users[index]['loadDataType'] = null;
      form.users[index]['loadDataUrlId'] = null;
    }
    if (prop === 'loadDataUrlId') {
      if (form.users[index]['loadDataType'] === 'studentXsda') {
        //学生档案-学生字段查询
        form.users[index].defaultValField = value;
      } else {
        /** 加载数据URL 更新listDicUrl*/
        let dicUrlData = dicts.value['listDicUrl'];
        if (dicUrlData) {
          dicUrlData.forEach((item) => {
            if (item.id === value) {
              form.users[index].valueField = item.valueField;
              form.users[index].textField = item.textField;
              form.users[index].loadDataUrl = item.url;
            }
          });
        }
      }
    }
    if (props.approverType === 'zizhu') {
      emits('onDoneGroup', initTableData.value);
    } else if (
      [
        'leaveConditions',
        'leaveProcessConditions',
        'randomSurveyTopicSelection_',
        'randomTopicSelection_',
        'projectTemplateField',
        'surveyQuestionSel_',
        'PagerQuestionSel_',
        'questionSel_'
      ].some((keyword) => props.approverType.includes(keyword))
    ) {
      emits('onDoneGroup', form.users);
    } else {
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData:
          props.approverType === 'nodeStatus'
            ? props.approverData
            : props?.selectorData,
        workflowNodeForm: form.users
      };
      emits('onDoneGroup', rData);
    }
  };

  /** 编辑 */
  const edit = (row) => {
    row.isEdit = true;
  };

  /** 完成 */
  const done = (row) => {
    for (let i in row) {
      if (row[i] === true || row[i] === false)
        row[i] = row[i] === true ? '是' : '否';
    }
    submit(row);
  };

  /** 删除 */
  const removeOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
            emits('child-event', props.currentNode);
            // reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 删除随机抽题规则 */
  const removeQuestionsRuleOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.sttype).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesExamPaperQuestionsRules({
          id: rows.map((d) => d.id).join(','),
          pid: props.currentNode.pid,
          gid: props.currentNode.id
        })
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
            emits('doneRefreshGroup', {
              pid: props.currentNode.pid,
              gid: props.currentNode.id
            });
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 删除随机抽题规则 */
  const removeSurveyQuestionsRuleOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.qtype).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesSurveyQuestionsRule({
          id: rows.map((d) => d.id).join(','),
          sid: props.currentNode.sid,
          gid: props.currentNode.id
        })
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
            emits('doneRefreshGroup', {
              pid: props.currentNode.sid,
              gid: props.currentNode.id
            });
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 删除限制条件 */
  const removeConditionOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removeCondition(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 删除扩展字段设置 */
  const removeFormProjectTemplateFieldOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removeFormProjectTemplateField(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const removeCoursePropertiesPercent = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.kcsxmc).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesCoursePropertiesPercent(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
  /** 随机抽题规则设置，抽题数验证 */
  const rules = computed(() => {
    if (
      props.approverType.includes('randomTopicSelection_') ||
      props.approverType.includes('randomSurveyTopicSelection_')
    ) {
      return {
        required: true,
        message: '请输入抽题数',
        trigger: 'blur'
      };
    } else {
      return {};
    }
  });

  /** 表单提交 */
  const submit = (row) => {
    formRef['value']?.validate?.((valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        EleMessage.error(`有 ${errors} 项校验不通过`);
        return false;
      } else {
        loading.value = true;
        let objArr = [];
        objArr.push(row);
        if (props.approverType.includes('randomTopicSelection_')) {
          saveEpqRules(row);
        } else if (props.approverType.includes('randomSurveyTopicSelection_')) {
          saveSurveyEpqRules(row);
        } else if (props.approverType.includes('coursePropertiesPercent')) {
          saveCoursePropertiesPercent(row);
        } else {
          operation(objArr)
            .then((msg) => {
              loading.value = false;
              EleMessage.success(msg);
              row.isEdit = true;
              emits('child-event', props.currentNode);
            })
            .catch((e) => {
              loading.value = false;
              EleMessage.error(e.message);
            });
        }
      }
    });
  };

  /**在线考试 随机抽题，试卷维护试题,保存随机抽题规则 */
  const saveEpqRules = (row) => {
    saveExamPaperQuestionsRules(row)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        row.isEdit = true;
        QueryExamPaperQuestionRule();
        emits('doneRefreshGroup', {
          pid: props.currentNode.pid,
          gid: props.currentNode.id
        });
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /**问卷 随机抽题，试卷维护试题,保存随机抽题规则 */
  const saveSurveyEpqRules = (row) => {
    console.log(row, props.currentNode);
    console.log(JSON.stringify(row));
    operationSurveyQuestionsRule(row)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        row.isEdit = true;
        QuerySurveyPaperQuestionRule();
        emits('doneRefreshGroup', {
          pid: props.currentNode.sid,
          gid: props.currentNode.id
        });
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  const saveCoursePropertiesPercent = (row) => {
    operationCoursePropertiesPercent(row)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        row.isEdit = true;
        queryScoreCoursePropertiesPercent();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** table组件是否显示新增按钮 */
  const isCanShowAddBtn = (approverType) => {
    let result = true;
    if (['nodeStatus', 'evaluateScopeList'].includes(approverType))
      result = false;
    if (
      approverType.includes('workflow_') &&
      shExtendsFieldEn.value.length === 0
    )
      result = false;
    if (approverType === 'zizhu' && zizhuFieldEn.value.length === 0)
      result = false;
    if (props.workFlowReadonly) result = false;
    return result;
  };

  /** 资助申请字段设置英文更新选中数据 */
  const selectChange = () => {
    let newData = [...allApproverData.value, ...form.users];
    if (newData && newData.length > 0) {
      let arraySomeMap = newData.map((item) => {
        return item.fieldEn;
      });
      zizhuFieldEn.value = removeDuplicates(
        zizhuFieldEnList.value,
        arraySomeMap
      );
    }
  };

  /** 查询组的申请字段信息 */
  const queryFormTemplateField = () => {
    console.log('查询组的申请字段信息');
    loading.value = true;
    getFormTemplateField({
      projectId: props.currentNode.projectId,
      groupId: props.currentNode.id,
      type: props.currentNode.type
      // infoType: ['apply', 'list']
    })
      .then((list) => {
        loading.value = false;
        console.log(list);
        allApproverData.value = list ?? [];
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 获取题数 */
  const queryExamQuestionsSum = async (formUsers) => {
    let result = 0;
    let obj = {
      cid: formUsers['cid'],
      sttype: formUsers['sttype']
    };
    await getExamQuestionsSum(obj).then((list) => {
      result = list ?? 0;
    });
    formUsers['bankCount'] = result;
  };

  /**问卷 获取题数 */
  const querySurveyQuestionsSum = async (formUsers) => {
    let result = 0;
    let obj = {
      cid: formUsers['cid'],
      qtype: formUsers['qtype']
    };
    await gettSurveyQuestionsSum(obj).then((list) => {
      result = list ?? 0;
    });
    formUsers['surveyBankCount'] = result;
  };

  const QueryEvaluateConfigScopeList = () => {
    getEvaluateConfigScopeList({
      configId: props.approverId,
      page: 1,
      limit: 10
    })
      .then((data) => {
        if (data.list && data.list.length > 0) {
          nextTick(() => {
            data.list.forEach((item) => {
              item.isEdit = true;
            });
            console.log(data.list);
            setTimeout(() => {
              form.users = data.list;
              emits('onDoneGroup', form.users);
            }, 1000);
          });
        } else {
          form.users = [];
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const queryScoreCoursePropertiesPercent = () => {
    getScoreCoursePropertiesPercent({ configId: props.approverId })
      .then((data) => {
        if (data && data.length > 0) {
          nextTick(() => {
            data.forEach((item) => {
              item.isEdit = true;
            });
            setTimeout(() => {
              form.users = data;
              emits('onDoneGroup', form.users);
            }, 1000);
          });
        } else {
          form.users = [];
          emits('onDoneGroup', form.users);
        }
        isLoading.value = false;
      })
      .catch((e) => {
        isLoading.value = false;
        EleMessage.error(e.message);
      });
  };

  const QueryExamPaperQuestionRule = () => {
    getExamPaperQuestionRule({
      pid: props.currentNode.pid,
      gid: props.currentNode.id
    })
      .then((list) => {
        if (list && list.length > 0) {
          nextTick(() => {
            list.forEach((item) => {
              item.isEdit = true;
              if (item['cid'] && item['sttype']) {
                queryExamQuestionsSum(item);
              }
            });
            console.log(list);
            setTimeout(() => {
              form.users = list;
              emits('onDoneGroup', form.users);
            }, 1000);
          });
        } else {
          form.users = [];
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const QuerySurveyPaperQuestionRule = () => {
    getSurveyQuestionRule({
      sid: props.currentNode.pid,
      gid: props.currentNode.id
    })
      .then((list) => {
        if (list && list.length > 0) {
          nextTick(() => {
            list.forEach((item) => {
              item.isEdit = true;
              if (item['cid'] && item['qtype']) {
                querySurveyQuestionsSum(item);
              }
            });
            setTimeout(() => {
              form.users = list;
              emits('onDoneGroup', form.users);
            }, 1000);
          });
        } else {
          form.users = [];
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const QueryExamQuestionsById = () => {
    getExamQuestionsById(props.approverId)
      .then((list) => {
        console.log(list);
        if (list && list.examQuestionsOptionsList.length > 0) {
          list.examQuestionsOptionsList.forEach((item) => {
            item.isEdit = true;
          });
          form.users = list.examQuestionsOptionsList;
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const QueryPaperExamQuestionsById = () => {
    getExamPaperQuestionsById(props.approverId)
      .then((list) => {
        if (list && list.examPaperQuestionsOptionsList.length > 0) {
          list.examPaperQuestionsOptionsList.forEach((item) => {
            item.isEdit = true;
          });
          form.users = list.examPaperQuestionsOptionsList;
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  /**
   * 问卷题库试题编辑
   * @constructor
   */
  const QuerySurveyQuestionsById = () => {
    getSurveyQuestionsById(props.approverId)
      .then((list) => {
        if (list && list.surveyQuestionsOptionsList.length > 0) {
          list.surveyQuestionsOptionsList.forEach((item) => {
            item.isEdit = true;
          });
          form.users = list.surveyQuestionsOptionsList;
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /**
   * 问卷试题编辑
   * @constructor
   */
  const QuerySurveyPQuestionsById = () => {
    getSurveyPQuestionsById(props.approverId)
      .then((list) => {
        if (list && list.surveyPquestionsOptionsList.length > 0) {
          list.surveyPquestionsOptionsList.forEach((item) => {
            item.isEdit = true;
          });
          form.users = list.surveyPquestionsOptionsList;
          emits('onDoneGroup', form.users);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  watch(
    () => props.approverType,
    (approverType) => {
      if (approverType) {
        if (approverType === 'nodeStatus') {
          initTableHeader.value = [
            {
              prop: 'fieldEn',
              label: '英文名(系统内置不支持编辑)',
              type: 'nodeStatus',
              hide: true
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              type: 'input'
            },
            {
              prop: 'glsj',
              label: '关联事件',
              type: 'glsj'
            }
          ];
          listStatusInit()
            .then((data) => {
              console.log(data);
              console.log(props.approverData);
              // if (JSON.stringify(props.approverData) !== '{}') {//页面值回显
              if (props.approverData && props.approverData.length > 0) {
                //页面值回显
                let getApproverData = props.approverData[0];
                data.forEach((item) => {
                  if (item.fieldEn === 'result') {
                    item.fieldZh = getApproverData['fieldZh'];
                  } else {
                    item.fieldZh = getApproverData[item.fieldEn];
                    item['glsj'] = getApproverData[item.fieldEn + 'EventId']
                      ? getApproverData[item.fieldEn + 'EventId'].split(',')
                      : [];
                  }
                  item.isEdit = !props.workFlowReadonly;
                });
              }
              form.users = data;
              let rData = {
                fieldEn: props.approverType,
                approverId: props.approverType,
                // selectorData: rData,
                workflowNodeForm: form.users
              };
              emits('onDoneGroup', rData);
            })
            .catch((e) => {
              EleMessage.error(e.message);
            });
        } else if (approverType === 'leaveConditions') {
          //请假类型条件设置
          initTableHeader.value = [
            {
              prop: 'operatorId',
              label: '匹配规则',
              type: 'dictSelect',
              props: {
                code: 'leaveOperator',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/leave/project/optionalConditionOperator',
                  valueField: 'id',
                  textField: 'text'
                }
              },
              required: true,
              editable: true
            },
            {
              prop: 'fieldId',
              label: '匹配字段',
              type: 'dictSelect',
              props: {
                code: 'leaveField',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/leave/project/optionalConditionField',
                  valueField: 'id',
                  textField: 'text'
                }
              },
              editable: true,
              required: true
            },
            {
              prop: 'expectedValue',
              label: '匹配预期值',
              type: 'input',
              editable: true,
              required: true
            },
            {
              prop: 'actionId',
              label: '生效后的操作',
              type: 'dictSelect',
              props: {
                code: 'leaveAction',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/leave/project/optionalConditionAction',
                  valueField: 'id',
                  textField: 'text'
                }
              },
              editable: true,
              required: true
            },
            {
              prop: 'matchAll',
              label: '是否匹配所有',
              type: 'switch',
              editable: true,
              required: true
            }
          ];
        } else if (approverType === 'leaveProcessConditions') {
          //请假类型条件设置
          initTableHeader.value = [
            {
              prop: 'operatorId',
              label: '匹配规则',
              type: 'dictSelect',
              props: {
                code: 'leaveOperator',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: `/leave/project/approval_rule_node/optionalConditionOperator?projectId=${props.projectId}&requestType=${props.requestType}`,
                  valueField: 'id',
                  textField: 'text'
                }
              },
              required: true,
              editable: true
            },
            {
              prop: 'fieldId',
              label: '匹配字段',
              type: 'dictSelect',
              props: {
                code: 'leaveField',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: `/leave/project/approval_rule_node/optionalConditionField?projectId=${props.projectId}&requestType=${props.requestType}`,
                  valueField: 'id',
                  textField: 'text'
                }
              },
              editable: true,
              required: true
            },
            {
              prop: 'expectedValue',
              label: '匹配预期值',
              type: 'input',
              editable: true,
              required: true
            },
            {
              prop: 'actionId',
              label: '生效后的操作',
              type: 'dictSelect',
              props: {
                code: 'leaveAction',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: `/leave/project/approval_rule_node/optionalConditionAction?projectId=${props.projectId}&requestType=${props.requestType}`,
                  valueField: 'id',
                  textField: 'text'
                }
              },
              editable: true,
              required: true
            }
            // {
            //   prop: 'matchAll',
            //   label: '是否匹配所有',
            //   type: 'switch',
            //   editable: true,
            //   required: true
            // }
          ];
        } else if (approverType === 'projectTemplateField') {
          //资助项目扩展字段维护
          initTableHeader.value = [
            {
              prop: 'fieldEn',
              label: '英文名',
              type: 'extendsField',
              minWidth: 70,
              required: true,
              editable: true
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              type: 'input',
              editable: true,
              required: true
            },
            {
              prop: 'defaultVal',
              label: '默认值',
              type: 'input',
              editable: true,
              required: true
            }
          ];
        } else if (approverType === 'evaluateScopeList') {
          //参数配置所选范围列表-表头
          initTableHeader.value = [
            {
              prop: 'njmc',
              label: '年级',
              type: 'njmc',
              minWidth: 80
            },
            {
              prop: 'pyccmc',
              label: '培养层次',
              type: 'pyccmc',
              minWidth: 100
            },
            {
              prop: 'xymc',
              label: '院系',
              type: 'xymc'
            },
            {
              prop: 'sqkssj',
              label: '申请开始时间',
              type: 'datetime',
              editable: true,
              required: true
            },
            {
              prop: 'sqjzsj',
              label: '申请截止时间',
              type: 'datetime',
              editable: true,
              required: true
            },
            {
              prop: 'status',
              label: '状态',
              type: 'input',
              editable: true,
              required: true
            }
          ];
        } else if (approverType === 'coursePropertiesPercent') {
          //课程属性占比配置-表头
          initTableHeader.value = [
            {
              prop: 'xn',
              label: '学年',
              type: 'xn',
              required: true,
              editable: true
            },
            {
              prop: 'kcsxmc',
              label: '课程属性名称',
              type: 'dictSelect',
              props: {
                code: 'kcsxdmData',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/score/course-score-item/distinctData',
                  valueField: 'kcsxmc',
                  textField: 'kcsxmc',
                  params: { fieldName: 'kcsxmc,kcsxdm' }
                }
              },
              required: true,
              editable: true
            },
            {
              prop: 'kcsxdm',
              label: '课程属性代码',
              type: 'kcsxdm',
              required: true,
              editable: true
            },
            {
              prop: 'percent',
              label: '百分比',
              type: 'inputNumber',
              required: true,
              editable: true
            }
          ];
          queryScoreCoursePropertiesPercent();
        } else if (approverType === 'nodeCondition') {
          //工作流条件设置
          initTableHeader.value = [
            {
              prop: 'fieldEn',
              label: '英文名',
              minWidth: 66,
              editable: false,
              type: 'extendsField',
              required: true
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              editable: true,
              type: 'extendsField',
              required: true
            },
            {
              prop: 'value',
              label: '流转条件值',
              editable: true,
              type: 'conditionValue',
              required: true
            },
            {
              prop: 'condition',
              label: '逻辑条件',
              editable: true,
              type: 'select',
              options: [
                { label: '等于', value: 'EQ' },
                { label: '不等于', value: 'NE' },
                { label: '大于', value: 'GT' },
                { label: '小于', value: 'LT' }
              ]
            }
          ];
        } else if (approverType.includes('randomTopicSelection_')) {
          //随机选题，抽题规则配置-在线考试
          initTableHeader.value = [
            {
              prop: 'sttype',
              label: '试题类型',
              minWidth: 106,
              editable: true,
              required: true,
              type: 'dictSelect',
              props: {
                code: 'sttype',
                filterable: true,
                dicQueryParams: {
                  getValType: 'name'
                }
              }
            },
            {
              prop: 'cid',
              label: '试题分类',
              editable: true,
              required: true,
              type: 'dictSelect',
              props: {
                code: 'bankData',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/exam/exam-questions-class',
                  getValType: 'id'
                }
              }
            },
            {
              prop: 'bankCount',
              label: '题库试题总数',
              editable: true,
              type: 'bankCount',
              required: true
            },
            {
              prop: 'qsum',
              type: 'examQsum',
              label: '抽题数'
            },
            {
              prop: 'qscore',
              label: '每题分数',
              editable: true,
              type: 'input',
              required: true
            }
          ];
          if (props.currentNode) {
            QueryExamPaperQuestionRule();
          }
        } else if (approverType.includes('randomSurveyTopicSelection_')) {
          //随机选题，抽题规则配置-问卷
          initTableHeader.value = [
            {
              prop: 'qtype',
              label: '试题类型',
              editable: true,
              required: true,
              type: 'dictSelect',
              props: {
                code: 'qtype',
                filterable: true,
                dicQueryParams: {
                  getValType: 'name'
                }
              }
            },
            {
              prop: 'cid',
              label: '试题分类',
              editable: true,
              required: true,
              type: 'dictSelect',
              props: {
                code: 'surveyBankData',
                filterable: true,
                dicQueryParams: {
                  dictFieldUrl: '/survey/survey-questions-calss',
                  getValType: 'id'
                }
              }
            },
            {
              prop: 'surveyBankCount',
              label: '题库试题总数',
              editable: true,
              type: 'surveyBankCount',
              required: true
            },
            {
              prop: 'qsum',
              type: 'surveyQsum',
              label: '抽题数'
            }
          ];
          if (props.currentNode) {
            QuerySurveyPaperQuestionRule();
          }
        } else if (
          [
            'questionSel_',
            'PagerQuestionSel_',
            'surveyQuestionSel_',
            'surveyQuestionSelWjst_'
          ].some((keyword) => approverType.includes(keyword))
        ) {
          //题库选项配置

          initTableHeader.value = [
            {
              prop: 'code',
              label: '选项',
              minWidth: 50,
              editable: false,
              type: 'questionSelCode'
              // required: true,
            },
            {
              prop: 'name',
              label: '选项值',
              editable: true,
              type: 'input',
              required: true
            }
          ];
          if (
            approverType.includes('surveyQuestionSel_') ||
            approverType.includes('surveyQuestionSelWjst_')
          ) {
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'score',
                label: '分数',
                editable: true,
                minWidth: 120,
                type: 'inputNumber',
                required: true
              },
              2
            );
          }
          console.log('props.approverId====', props.approverId);
          if (props.approverId) {
            if (approverType.includes('questionSel_')) {
              QueryExamQuestionsById();
            } else if (approverType.includes('PagerQuestionSel_')) {
              QueryPaperExamQuestionsById();
            } else if (approverType.includes('surveyQuestionSel_')) {
              QuerySurveyQuestionsById();
            } else if (approverType.includes('surveyQuestionSelWjst_')) {
              QuerySurveyPQuestionsById();
            }
          } else {
            console.log('props.approverId====', approverType);
            let aType = approverType.split('_')[1];
            let data = [
              { code: 'A', name: '', score: null, isEdit: true, sort: 1 },
              { code: 'B', name: '', score: null, isEdit: true, sort: 2 },
              { code: 'C', name: '', score: null, isEdit: true, sort: 3 },
              { code: 'D', name: '', score: null, isEdit: true, sort: 4 }
            ];
            if (aType === '判断题')
              data = [
                { code: '对', name: '', score: '', isEdit: true, sort: 1 },
                { code: '错', name: '', score: '', isEdit: true, sort: 2 }
              ];
            form.users = data;
            emits('onDoneGroup', form.users);
          }
        } else {
          let enFields = [
            {
              prop: 'fieldEn',
              label: '英文名',
              type: 'extendsField',
              minWidth: 70,
              required: true,
              editable: true
              // hide: true,
            }
          ];
          let baseFields = [
            {
              prop: 'fieldZh',
              label: '中文名',
              type: 'input',
              editable: true,
              required: true
            },
            {
              prop: 'required',
              label: '是否必填',
              required: true,
              type: 'switch'
            },
            {
              prop: 'controlType',
              label: '字段类型',
              required: true,
              editable: true,
              type: 'dictSelect',
              props: { code: 'controlType', filterable: true }
            },
            {
              prop: 'tipText',
              label: '提示文本',
              editable: true,
              type: 'input'
            },
            {
              prop: 'regExpression',
              label: '正则验证表达式',
              editable: true,
              type: 'regExpression'
            },
            {
              prop: 'loadDataType',
              label: '加载数据',
              type: 'loadDataType'
            },
            {
              prop: 'loadDataUrlId',
              label: '加载数据URL',
              type: 'loadDataUrlId'
            }
          ];
          let zizhuFields = [
            {
              prop: 'defaultVal',
              label: '默认值',
              editable: true,
              type: 'defaultVal'
            },
            {
              prop: 'processEvent',
              label: '处理事件',
              editable: true,
              type: 'processEvent'
            }
          ];
          if (approverType === 'condition') {
            initTableHeader.value = baseFields;
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'fieldEn',
                label: '英文名',
                type: 'input',
                editable: true,
                required: true
              },
              0
            );
            queryConditionField(); //限制条件，table数据回显
          } else if (approverType === 'zizhu') {
            initTableHeader.value = enFields.concat(baseFields, zizhuFields);
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'editFlag',
                label: '是否可编辑',
                editable: true,
                type: 'switch'
              },
              3
            );
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'showFlag',
                label: '是否显示',
                editable: true,
                type: 'switch'
              },
              4
            );
          } else if (approverType.includes('workflow_')) {
            queryFormExtendsField();
            initTableHeader.value = enFields.concat(baseFields, zizhuFields);
          } else {
            initTableHeader.value = enFields.concat(baseFields, zizhuFields);
          }
          nextTick(() => {
            if (props.approverData && props.approverData.length > 0) {
              //页面值回显
              props.approverData.forEach((item) => {
                item.isEdit = true;
                // item.isEdit = props.workFlowReadonly ? false : true;
              });
              form.users = props.approverData;
              if (approverType !== 'zizhu') {
                let rData = {
                  fieldEn: approverType,
                  approverId: props.approverId,
                  selectorData: props.selectorData,
                  workflowNodeForm: form.users
                };
                emits('onDoneGroup', rData);
              }
            } else {
              console.log('33333===', approverType);
              //资助申请表单不默认添加行数据
              if (
                approverType !== 'zizhu' &&
                !approverType.includes('workflow_')
              ) {
                add();
              }
            }
          });
        }
      }
    },
    {
      immediate: true
    }
  );

  watch(
    () => props.RandomString,
    (RandomString) => {
      if (RandomString) {
        //工作流条件参数页面回显
        if (props.approverType === 'nodeCondition') {
          let newData = [];
          if (props.approverData && props.approverData.length > 0) {
            //页面值回显
            props.approverData.forEach((item, index) => {
              let obj = {
                fieldId: item.id,
                fieldEn: item.fieldEn,
                fieldZh: item.fieldZh,
                loadDataType: item.loadDataType,
                value: item?.value,
                condition: item?.condition,
                sort: index + 1,
                isEdit: true
              };
              newData.push(obj);
            });
          }
          form.users = newData;
          let rData = {
            fieldEn: props.approverType,
            approverId: props.approverId,
            workflowNodeForm: form.users
          };
          emits('onDoneGroup', rData);
        } else if (
          props.approverType === 'leaveConditions' ||
          props.approverType === 'leaveProcessConditions'
        ) {
          let newData = [];
          if (props.approverData && props.approverData.length > 0) {
            //页面值回显
            props.approverData.forEach((item) => {
              let obj = {
                ...item,
                isEdit: true
              };
              newData.push(obj);
            });
          }
          form.users = newData;
          emits('onDoneGroup', form.users);
        } else if (props.approverType === 'coursePropertiesPercent') {
          //课程属性占比配置-表头
          queryScoreCoursePropertiesPercent();
        } else if (props.approverType === 'evaluateScopeList') {
          form.users = props.approverData;
          emits('onDoneGroup', form.users);
        } else if (props.approverType === 'projectTemplateField') {
          form.users = props.approverData;
          emits('onDoneGroup', form.users);
        } else {
          if (props.approverType === 'zizhu') {
            queryFormTemplateFieldList();
            queryFormTemplateField();
          }
          setTimeout(() => {
            if (props.approverData && props.approverData.length > 0) {
              //页面值回显
              props.approverData.forEach((item) => {
                item.isEdit = true;
              });
              if (props.approverType === 'zizhu') {
                selectChange();
                initTableData.value = props.approverData;
                form.users = initTableData.value.filter(
                  (obj) => obj.groupId === props.approverId
                );
                emits('onDoneGroup', initTableData.value);
              }
              if (props.approverType.includes('workflow_')) {
                form.users = props.approverData;
                selectChangeSh();
              }
            } else {
              form.users = [];
            }
          }, 100);
        }
      }
    },
    {
      immediate: true
    }
  );

  defineExpose({ formRef });
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }

  .empty-background {
    pointer-events: none;
  }

  ::v-deep .el-result__subtitle {
    margin-top: -110px !important;
  }
</style>
