<!-- 字典组件 -->
<template>
  <template v-if="type === 'text'">
    <span v-for="item in valueData" :key="item.dictDataCode">
      {{ item.dictDataName }}
    </span>
  </template>
  <template v-else-if="type === 'tag'">
    <el-tag v-for="item in valueData"
            :key="item.dictDataCode"
            :disable-transitions="true"
            size="small">
      {{ item.dictDataName }}
    </el-tag>
  </template>
  <el-radio-group v-else-if="type === 'radio'"
                  :disabled="disabled"
                  @update:modelValue="updateValue"
                  :model-value="modelValue">
    <el-radio v-for="item in data"
              :key="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
              :value="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode">
      {{ item.dictDataName }}
    </el-radio>
  </el-radio-group>
  <el-radio-group v-else-if="type === 'radioButton'"
                  :disabled="disabled"
                  @update:modelValue="updateValue"
                  :model-value="modelValue">
    <el-radio-button v-for="item in data"
                     :key="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
                     :value="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
    >
      {{ item.dictDataName }}
    </el-radio-button>
  </el-radio-group>
  <el-checkbox-group v-else-if="type === 'checkboxButton'"
                     :disabled="disabled"
                     @update:modelValue="updateValue"
                     :model-value="modelValue">
    <el-checkbox v-for="item in data" border style="margin-bottom: 8px;"
                 :key="item.dictDataCode"
                 :value="item.dictDataCode">
      {{ item.dictDataName }}
    </el-checkbox>
  </el-checkbox-group>
  <el-checkbox-group v-else-if="type === 'checkbox'"
                     :disabled="disabled"
                     v-model="checkedValues"
                     @change="updateValue">
    <template v-if="isColProps">
      <el-row>
        <el-col :span="code==='xymc'?3:3" v-for="(item,index) in data" :key="index">
          <el-checkbox :label="item.dictDataCode">
            {{ item.dictDataName }}
          </el-checkbox>
        </el-col>
      </el-row>
    </template>
    <template v-else>
      <el-checkbox v-for="item in data"
                   :key="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
                   :label="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode">
        <!--      :key="item.dictDataCode"-->
        <!--                   :label="item.dictDataCode">-->
        {{ item.dictDataName }}
      </el-checkbox>
    </template>
  </el-checkbox-group>
  <el-select v-else-if="type==='searchSet'"
             @update:modelValue="updateValue"
             :model-value="modelValue"
             :clearable="true"
             :disabled="disabled"
             :placeholder="placeholder"
             :teleported="teleported"
             :filterable="filterable"
             class="ele-fluid">
    <!--        码表获取数据，根据getValType设置类型取code还是name-->
    <el-option v-for="item in data"
               @click="selectChange(item)"
               :key="item.dictDataCode"
               :value="item.dictDataCode"
               :label="item.dictDataName"/>
  </el-select>
  <el-select v-else-if="type==='select_allowCreate'"
             @update:modelValue="updateValue"
             :model-value="modelValue"
             :clearable="true"
             :placeholder="placeholder+'或者创建新的选项'"
             :teleported="teleported"
             filterable
             allow-create
             default-first-option
             :reserve-keyword="false"
             class="ele-fluid">
    <!--        码表获取数据，根据getValType设置类型取code还是name-->
    <el-option v-for="item in data"
               :key="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
               :value="(dicQueryParams&&dicQueryParams.getValType==='name')?item.dictDataName:item.dictDataCode"
               :label="item.dictDataName"/>
  </el-select>
  <el-select v-else
             @update:modelValue="updateValue"
             :model-value="modelValue"
             :clearable="true"
             :placeholder="placeholder"
             :multiple="type === 'multipleSelect'"
             :disabled="disabled"
             :teleported="teleported"
             :filterable="filterable"
             :collapse-tags="collapseTags"
             :collapse-tags-tooltip="collapseTagsTooltip"
             :value-key="dicQueryParams?.valueField || 'id'"
             :max-collapse-tags="maxCollapseTags"
             class="ele-fluid">
    <el-option v-for="item in data"
               :key="getOptionValueKey(item)"
               :value="getOptionValue(item)"
               :label="getOptionLabel(item)">{{ getOptionLabel(item) }}
    </el-option>
  </el-select>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {useUserStore} from '@/store/modules/user';
import {ElMessage as EleMessage} from 'element-plus';
import {listRoles} from '@/views/system/role/api';
import {getCodeDwb} from '@/views/base-code/dwb/api';
import {getCodeZyb} from '@/views/base-code/zyb/api';
import {getCodeBjb} from '@/views/base-code/bjb/api';
import {getFieldList} from '@/views/personInfo/st/api';
import {getDicFieldValueByUrl, getDictionaryFieldByGroupId} from '@/views/system/sphfw/dictionary-field/api';
import {getControlTypeData} from '@/utils/control-type-util';
import {getCodeType} from '@/views/base-code/dictionary/api';
import {getSelectControlApi} from '@/views/base-code/selectDictionary/api';
import {getDictionaryGroup} from '@/views/system/sphfw/dictionary-group/api';
import {getListGroupConfig} from '@/views/system/sphfw/list-group/api';
import {getCodeData} from '@/views/base-code/dictionary/api/data-index';
import {getFormExtendsField} from '@/views/dingding-flow/api/index.js';
import {getFormType} from '@/views/zizhu/form-type/api/index.js';
import {storeToRefs} from 'pinia';
import {getNodeState} from '@/views/qgzx/qgzx-job-approval/api/index.js';
import {comApproveStatus} from '@/utils/common_bak2.js';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
    /** 字典值 */
    modelValue: [String, Number, Boolean, Array],
    /** 字典类型 */
    code: String,
    /** 每次刷新，重新请求 */
    refresh: String,
    // /** 动态获取接口参数配置 */
    dicQueryParams: Object,
    /** 组件类型 */
    type: String,
    /** 是否禁用 */
    disabled: Boolean,
    /** 提示文本 */
    placeholder: String,
    /**multiple 属性设置为 true 时，代表多选场景下用户最多可以选择的项目数， 为 0 则不限制*/
    maxCollapseTags: String,
    /**多选时是否将选中值按文字的形式展示*/
    collapseTags: {
      type: Boolean,
      default: false,
    },
    /**当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。*/
    collapseTagsTooltip: {
      type: Boolean,
      default: false,
    },
    /** select的下拉是否插入到body下 */
    teleported: {
      type: Boolean,
      default: true,
    },
    /** select 是否可搜索 */
    filterable: {
      type: Boolean,
      default: true,
    },
    /** 是否设置选项width*/
    isColProps: Boolean,
  })
;

/** 字典数据 */
const data = ref([]);

// 已缓存的字典
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
/** 绑定值对应的字典数据 */
const valueData = computed(() => {
  const result = [];
  const val = props.modelValue;
  if (val == null || val === '') {
    return result;
  }
  const values = Array.isArray(val) ? val : [val];
  values.forEach((v) => {
    const temp = dicData.value.find((d) => d.dictDataCode === v);
    if (temp != null) {
      result.push(temp);
    } else {
      result.push({dictDataCode: v, dictDataName: v});
    }
  });
  return result;
});
// 带"."的字段，动态取嵌套对象的值
const getDeepValue = (obj, path) => {
  if (!obj || !path) return undefined;
  return path.split('.').reduce((acc, key) => acc && acc[key], obj);
};

const getOptionLabel = (item) => {
  if (props.dicQueryParams?.valueType === 'Object') {
    return getDeepValue(item, props.dicQueryParams?.textField);
  }
  // if (props.dicQueryParams?.getValType) {
  //   return props.dicQueryParams?.getValType === 'name' ? item.dictDataName : item.dictDataCode;
  // }
  return item.dictDataName;
};

const getOptionValue = (item) => {
  if (props.dicQueryParams?.valueType === 'Object') {
    return item;
  } else if (props.dicQueryParams?.getValType === 'name') {
    return item.dictDataName;
  } else {
    return item.dictDataCode;
  }
};

const getOptionValueKey = (item) => {
  if (props.dicQueryParams?.valueField) {
    return item[props.dicQueryParams?.valueField];
  }
  if (props.dicQueryParams?.getValType === 'name') {
    return item.dictDataName;
  } else {
    return item.dictDataCode;
  }
};

/** 更新选中数据 searchSet特殊处理 获取el-select，value=object */
const selectChange = (value) => {
  emit('update:modelValue', value);
};

const checkedValues = computed({
  get() {
    return Array.isArray(props.modelValue) ? props.modelValue : [];
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

/** 更新选中数据 */
const updateValue = (value) => {
  emit('update:modelValue', value);
};

const getUseDictData = (code, dicQueryParams) => {
    let result = [];
    let codeData = computed(() => dicts.value[code] || []);
    // // 若还未缓存过则获取字典数据
    if (codeData.value.length > 0) {
      data.value = codeData.value;
      return false;
    }
    let groupId = dicQueryParams?.groupId;
    let dictionaryFieldUrl = dicQueryParams?.dictFieldUrl;
    let userType = dicQueryParams?.userType;
    let params = dicQueryParams?.params;
    if (code === 'listRoles') {//角色下拉
      /** 获取角色数据 */
      listRoles().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.name});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'studentXsda') {//学生档案-学生字段查询
      getFieldList('student', {listShowFlag: '是'}).then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.fieldEn, 'dictDataName': resData.fieldZh, ...resData});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'zizuywlx') {//资助业务类型
      getFormType().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.type, 'dictDataName': resData.name, ...resData});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'searchSet') {//学生高级查询设置
      getFieldList(userType, {tempField: '否'}).then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.fieldEn, 'dictDataName': resData.fieldZh, ...resData});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'xymc') {//院系
      getCodeDwb().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.name, ...list});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'zymc') {//专业
      getCodeZyb(params).then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.name});
          }));
          userStore.setDicts(result, code);
          data.value = result;
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'bjmc') {//bjmc
      getCodeBjb(params).then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.name});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'controlType') {//获取控件类型
      getControlTypeData().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.value, 'dictDataName': resData.label});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'listDicCode') {//加载数据
      getCodeType().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.code, 'dictDataName': resData.name, ...resData});
          }));
          result.push({'dictDataCode': 'other', 'dictDataName': '其他'});
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'extendsField') {//流程审核表单获取扩展字段列表
      getFormExtendsField().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData, 'dictDataName': resData, disabled: false});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'listDicUrl') {//加载数据URL
      getSelectControlApi().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.name, ...resData});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'groupType') {//信息字段，所属字段组
      let obj = {};
      if (userType) obj = {
        userType: userType,
      };
      getDictionaryGroup(obj).then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.groupName});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'listGroup') {//信息字段，所属字段组
      getListGroupConfig().then((list) => {
        if (list) {
          list.forEach((resData => {
            result.push({'dictDataCode': resData.id, 'dictDataName': resData.groupName});
          }));
          data.value = result;
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else if (code === 'nodeStateJob') {//勤工助学-岗位申报审核
      console.log(dicQueryParams?.moduleCode);
      getNodeState({moduleCode: dicQueryParams?.moduleCode}).then((list) => {
        if (list) {
          let baseItmes = comApproveStatus();
          baseItmes.forEach(item => {
            item.label = list[0][item.prename];
            item.name = list[0][item.diyname];
            item.dictDataCode = list[0][item.prename];
            item.dictDataName = list[0][item.diyname];
            item.codeType = code;
          });
          console.log(baseItmes);
          data.value = baseItmes;
          userStore.setDicts(baseItmes, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    } else {
      if (dictionaryFieldUrl) {//其他类型
        if (code.includes('_dictionaryField') && groupId && dictionaryFieldUrl) {//字段英文名
          getDictionaryFieldByGroupId(groupId, dictionaryFieldUrl).then((list) => {
            if (list) {
              list.forEach((resData => {
                result.push({'dictDataCode': resData, 'dictDataName': resData});
              }));
              userStore.setDicts(result, code);
              data.value = result;
            }
          }).catch((e) => {
            EleMessage.error(e.message);
          });
        } else {
          getDicFieldValueByUrl(dictionaryFieldUrl, params).then((list) => {
              let valueField = dicQueryParams?.valueField ?? 'id';
              let textField = dicQueryParams?.textField ?? 'name';
              if (list) {
                list.forEach((resData => {
                  result.push({'dictDataCode': resData[valueField], 'dictDataName': resData[textField], ...resData});
                }));
                userStore.setDicts(result, code);
                data.value = result;
              }
            },
          ).catch((e) => {
            EleMessage.error(e.message);
          });
        }
      } else {
        //公共码表获取
        getCodeData({codeType: code}).then((list) => {
          let valueField = dicQueryParams?.valueField ?? 'name';
          let textField = dicQueryParams?.textField ?? 'name';
          if (list) {
            list.forEach((resData => {
              result.push({'dictDataCode': resData[valueField], 'dictDataName': resData[textField], ...resData});
            }));
            userStore.setDicts(result, code);
            data.value = result;
          }
        }).catch((e) => {
          EleMessage.error(e.message);
        });
      }
    }
    ;
  }
;

watch(
  () => props.code,
  (code) => {
    if (code) {
      getUseDictData(code, props.dicQueryParams);
    }
  },
  {
    immediate: true,
  },
);
watch(
  () => props.refresh,
  (refresh) => {
    if (refresh) {
      userStore.setDicts([], props.code);
      getUseDictData(props.code, props.dicQueryParams);
    }
  },
  {
    immediate: true,
  },
);
</script>
