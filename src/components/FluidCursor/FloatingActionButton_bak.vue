<template>
  <!-- 可拖动悬浮图标 -->
  <div
    ref="dragContainer"
    class="floating-icon"
    :class="{ 'is-dragging': isDragging, 'icon-active': isActive }"
    :style="{ top: positionY + 'px' }"
    @mousedown="startDrag"
    @touchstart="startTouchDrag"
    @click="handleClick"
  >
    <div class="icon-content">
      <IconPark class="icon" name="thinking-problem" size="40"/>
      <!--      <svg class="icon" viewBox="0 0 24 24">-->
      <!--&lt;!&ndash;        &lt;!&ndash; 示例SVG图标（可替换为任意图标） &ndash;&gt;&ndash;&gt;-->
      <!--&lt;!&ndash;        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm0-4c-.55 0-1-.45-1-1V7c0-.55.45-1 1-1s1 .45 1 1v5c0 .55-.45 1-1 1z"/>&ndash;&gt;-->
      <!--      </svg>-->
      <div class="ripple"></div>
    </div>
  </div>

  <!-- 聊天窗口 -->
  <Teleport to="body">
    <ChatWindow v-if="showChat" @close="closeChat"/>
  </Teleport>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue'
import IconPark from "@/components/IconPark/index.vue";

// 组件状态
const positionY = ref(0)
const isDragging = ref(false)
const isActive = ref(false)
const showChat = ref(false)
const startDragY = ref(0)
const dragContainer = ref(null)
let maxY = 0

// 初始化位置
const initPosition = () => {
  const containerHeight = dragContainer.value?.offsetHeight || 0
  positionY.value = window.innerHeight / 2 - containerHeight / 2
  maxY = window.innerHeight - containerHeight
}

// 拖拽处理逻辑
const startDrag = (e) => {
  e.preventDefault()
  isDragging.value = true
  startDragY.value = e.clientY - positionY.value
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const startTouchDrag = (e) => {
  isDragging.value = true
  startDragY.value = e.touches[0].clientY - positionY.value
  document.addEventListener('touchmove', onTouchDrag)
  document.addEventListener('touchend', stopDrag)
}

const onDrag = (e) => {
  if (!isDragging.value) return
  updatePosition(e.clientY)
}

const onTouchDrag = (e) => {
  if (!isDragging.value) return
  updatePosition(e.touches[0].clientY)
}

const updatePosition = (clientY) => {
  let newY = clientY - startDragY.value
  newY = Math.max(0, Math.min(newY, maxY))
  positionY.value = newY
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onTouchDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
}

// 点击处理
const handleClick = (e) => {
  if (isDragging.value) return // 拖拽时不触发点击

  isActive.value = true
  showChat.value = true
  setTimeout(() => isActive.value = false, 300)
}

// 窗口大小变化处理
const handleResize = () => {
  initPosition()
  maxY = window.innerHeight - dragContainer.value.offsetHeight
}

// 生命周期
onMounted(() => {
  initPosition()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.floating-icon {
  position: fixed;
  right: 30px;
  width: 56px;
  height: 56px;
  cursor: grab;
  z-index: 9999;
  transition: top 0.3s cubic-bezier(0.4, 0, 0.2, 1),
  transform 0.3s ease,
  box-shadow 0.3s ease;
  will-change: transform;
}

.icon-content {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, #f0f2f5, var(--el-color-primary));
  box-shadow: 0 4px 20px var(--el-color-primary);
  transition: all 0.3s ease;
}

.icon {
  width: 60%;
  height: 60%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  fill: white;
}

/* 悬停效果 */
.floating-icon:hover:not(.is-dragging) .icon-content {
  transform: scale(1.1) rotate(15deg);
  box-shadow: 0 8px 30px var(--el-color-primary);
}

/* 点击效果 */
.floating-icon.icon-active .icon-content {
  transform: scale(0.9);
}

.ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* 拖拽状态 */
.floating-icon.is-dragging {
  cursor: grabbing;
  transition: none;
}

.floating-icon.is-dragging .icon-content {
  filter: brightness(1.2);
  box-shadow: 0 8px 35px var(--el-color-primary);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .floating-icon {
    right: 15px;
    width: 48px;
    height: 48px;
  }
}
</style>
