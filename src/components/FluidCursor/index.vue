<template>
  <div class="cursor" ref="cursorRef"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const cursorRef = ref(null)
let posX = 0, posY = 0
let mouseX = 0, mouseY = 0

const animate = () => {
  posX += (mouseX - posX) / 6
  posY += (mouseY - posY) / 6

  cursorRef.value.style.left = `${posX}px`
  cursorRef.value.style.top = `${posY}px`

  requestAnimationFrame(animate)
}

const handleMouseMove = (e) => {
  mouseX = e.clientX
  mouseY = e.clientY
}

const handleClick = () => {
  cursorRef.value.classList.add('expand')
  setTimeout(() => {
    cursorRef.value.classList.remove('expand')
  }, 500)
}

onMounted(() => {
  window.addEventListener('mousemove', handleMouseMove)
  window.addEventListener('click', handleClick)
  animate()
})

onUnmounted(() => {
  window.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('click', handleClick)
})
</script>

<style scoped>
.cursor {
  width: 30px;
  height: 30px;
  border: 2px solid #fff;
  border-radius: 50%;
  position: fixed;
  pointer-events: none;
  transition:
    width 0.3s ease,
    height 0.3s ease,
    background 0.3s ease;
  transform: translate(-50%, -50%);
  mix-blend-mode: difference;
  background: rgba(255, 255, 255, 0.1);
}

.cursor.expand {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.3);
}

@media (hover: hover) {
  .cursor {
    width: 40px;
    height: 40px;
  }
}
</style>
