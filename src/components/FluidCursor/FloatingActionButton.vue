<template>
  <div ref="dragContainer"
       class="floating-widget"
       :class="{ 'is-dragging': isDragging, 'is-active': isActive }"
       :style="{ top: positionY + 'px' }"
       @mousedown="startDrag"
       @touchstart.passive="startTouchDrag"
       @click="handleClick">
    <div class="widget-content">
      <!-- 图标部分 -->
      <div class="icon-wrapper">
        <IconPark class="main-icon" name="thinking-problem" size="40"/>
        <!--        <svg class="main-icon" viewBox="0 0 24 24">-->
        <!--          <path-->
        <!--            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm0-4c-.55 0-1-.45-1-1V7c0-.55.45-1 1-1s1 .45 1 1v5c0 .55-.45 1-1 1z"/>-->
        <!--        </svg>-->
        <!--        <div class="ripple-effect"></div>-->
      </div>
      <!-- 文字部分 -->
      <div class="text-container">
        <span class="action-text">操作指引</span>
        <span class="hover-text">点击查看</span>
      </div>
    </div>
  </div>

  <!--  <Teleport to="body">-->
  <!--    <ChatWindow v-if="showChat" @close="closeChat"/>-->
  <!--  </Teleport>-->
</template>
<script setup>
import {ref, onMounted, onUnmounted, nextTick} from 'vue'
import IconPark from "@/components/IconPark/index.vue";

const emit = defineEmits(['doneClick']);
// 组件状态（新增初始化标志位）
const isMounted = ref(false)
// 组件状态
const positionY = ref(0)
const isDragging = ref(false)
const isActive = ref(false)
const showChat = ref(false)
const startDragY = ref(0)
const dragContainer = ref(null)
let maxY = 0

// 初始化位置 修正后的初始化方法（新增防抖）
const initPosition = () => {
  if (!isMounted.value) return

  const containerHeight = dragContainer.value.offsetHeight
  console.log(window.innerHeight, window.innerHeight / 2)
  console.log(containerHeight, containerHeight / 2)
  positionY.value = Math.max(
    window.innerHeight / 2 - containerHeight / 2,
    window.innerHeight - containerHeight - 60 // 安全边距
  )
  updateMaxY()
}

// 拖拽处理逻辑
const startDrag = (e) => {
  e.preventDefault()
  isDragging.value = true
  startDragY.value = e.clientY - positionY.value
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const startTouchDrag = (e) => {
  isDragging.value = true
  startDragY.value = e.touches[0].clientY - positionY.value
  document.addEventListener('touchmove', onTouchDrag)
  document.addEventListener('touchend', stopDrag)
}

const onDrag = (e) => {
  if (!isDragging.value) return
  updatePosition(e.clientY)
}

const onTouchDrag = (e) => {
  if (!isDragging.value) return
  updatePosition(e.touches[0].clientY)
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('touchmove', onTouchDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
}

// 点击处理
const handleClick = (e) => {
  if (isDragging.value) return // 拖拽时不触发点击
  isActive.value = true
  showChat.value = true
  setTimeout(() => isActive.value = false, 300)
  emit('doneClick')
}

// 分离最大Y计算逻辑
const updateMaxY = () => {
  maxY = window.innerHeight - dragContainer.value.offsetHeight - 60 // 底部留空
}

// 优化后的拖拽方法（新增节流）
let lastUpdate = 0
const updatePosition = (clientY) => {
  const now = Date.now()
  if (now - lastUpdate < 16) return // 60fps节流
  lastUpdate = now

  let newY = clientY - startDragY.value
  newY = Math.max(20, Math.min(newY, maxY)) // 顶部/底部边距

  if (positionY.value !== newY) {
    positionY.value = newY
  }
}

// 增强的窗口大小处理（新增防抖）
let resizeTimer = null
const handleResize = () => {
  clearTimeout(resizeTimer)
  resizeTimer = setTimeout(() => {
    updateMaxY()
    console.log(positionY.value, maxY)
    positionY.value = Math.min(positionY.value, maxY)
  }, 100)
}

// 生命周期（新增挂载标志）
onMounted(() => {
  isMounted.value = true
  initPosition()
  window.addEventListener('resize', handleResize)

  // 确保容器存在
  nextTick(() => {
    if (!dragContainer.value) {
      console.error('拖拽容器未正确挂载')
    }
  })
})

onUnmounted(() => {
  isMounted.value = false
  window.removeEventListener('resize', handleResize)
})

</script>

<style scoped>

.floating-widget {
  position: fixed;
  right: 30px;
  cursor: grab;
  z-index: 9999;
  transition: top 0.3s cubic-bezier(0.4, 0, 0.2, 1),
  transform 0.3s ease,
  box-shadow 0.3s ease;
  will-change: transform;
}

.widget-content {
  display: flex;
  align-items: center;
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 20px var(--el-color-primary);
  padding: 12px 10px;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.icon-wrapper {
  position: relative;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.main-icon {
  width: 100%;
  height: 100%;
  fill: var(--el-color-primary);
  transition: transform 0.3s ease;
}

.text-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: nowrap;
}

.action-text {
  font-weight: 600;
  color: #2d3436;
  font-size: 14px;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.hover-text {
  font-size: 12px;
  color: #636e72;
}


/* 新增拖拽时禁用过渡 */
.floating-widget.is-dragging {
  transition: none !important;
}

/* 修复移动端触摸问题 */
@media (hover: none) {
  .floating-widget {
    touch-action: none;
  }
}

/* 悬停效果 */
.floating-widget:hover:not(.is-dragging) {
  transform: translateX(-5px);

  .widget-content {
  }

  .main-icon {
  }

  .action-text {
  }

}

/* 点击效果 */
.floating-widget.is-active .widget-content {
  transform: scale(1.1) rotate(15deg);
  box-shadow: 0 4px 20px var(--el-color-primary);
  background: linear-gradient(45deg, #f0f2f5, var(--el-color-primary));
}

/* 拖拽状态 */
.floating-widget.is-dragging {
  cursor: grabbing;

  .widget-content {
    background: linear-gradient(45deg, #f0f2f5, var(--el-color-primary));
  }

  .hover-text {
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  .floating-widget {
    right: 15px;
    bottom: 15px !important;
    top: auto !important;
  }

  .widget-content {
    padding: 10px 15px;
    border-radius: 25px;
  }

  .action-text {
    font-size: 13px;
  }

  .hover-text {
  }
}

@media (max-width: 480px) {
  .widget-content {
    padding: 8px 12px;
    gap: 8px;
  }

  .action-text {
    font-size: 12px;
  }

  .icon-wrapper {
    width: 35px;
    height: 35px;
  }
}
</style>
