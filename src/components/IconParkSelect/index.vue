<!-- 图标选择下拉框 -->
<template>
  <ele-icon-select
    clearable
    filterable
    :data="iconData"
    :model-value="modelValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :popper-width="420"
    :popper-height="294"
    :grid-style="{ gridTemplateColumns: 'repeat(6, 1fr)' }"
    :item-style="{ height: '52px' }"
    @update:modelValue="updateValue"
  >
    <template #icon="{ icon }">
      <IconPark :name="icon" size="18" strokeWidth="3" :color="color" />
    </template>
  </ele-icon-select>
</template>

<script setup>
  import { computed } from 'vue';
  import IconPark from '@/components/IconPark/index.vue';
  import * as IconParkListnew from '@icon-park/vue-next';
  const emit = defineEmits(['update:modelValue']);
  defineProps({
    /** 选中的图标 */
    modelValue: String,
    /** 是否禁用 */
    disabled: Boolean,
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择菜单图标'
    },
    /** 提示信息 */
    color: {
      type: String,
      default: 'rgba(0, 0, 0, 0.88)'
    }
  });
  console.log('IconParkListnew', IconParkListnew);
  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };
  const iconData = computed(() => {
    const iconNames = Object.keys(IconParkListnew);
    return [
      {
        title: '线框风格',
        icons: iconNames
      }
    ];
  });
</script>

<!-- <script>
  import * as IconParkList from '@icon-park/vue-next';
  export default {
    components: IconParkList,
    data() {
      const iconNames = Object.keys(IconParkList);
      return {
        iconData: [
          {
            title: '线框风格',
            icons: iconNames
          }
          // {
          //   title: '实底风格',
          //   icons: iconNames
          // }
        ]
      };
    }
  };
</script> -->
