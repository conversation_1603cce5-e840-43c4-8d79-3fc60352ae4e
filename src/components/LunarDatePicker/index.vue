<template>
    <el-date-picker v-model="dateValue" v-bind="$props">
        <template #default="dateCell">
            <el-tooltip
                    :disabled="!showLunarTip"
                    :show-after="lunarTipShowAfter"
                    :content="getLunarDateStr(dateCell.date)"
                    placement="bottom"
            >
                <div :class="getDateClass(dateCell)">
                    <span class="solar-text">{{ dateCell.date.getDate() }}</span>
                    <span class="lunar-tex">{{ getLunarDay(dateCell.date) }}</span>
                </div>
            </el-tooltip>
        </template>
    </el-date-picker>
</template>

<script setup lang="ts">
    import { JieQi, Solar } from 'lunar-typescript'
    import { propTypes } from '@/utils/propTypes'
    import { isEmpty } from '@/utils/is'
    import { datePickerProps } from 'element-plus'
    import type { DateCell } from 'element-plus/es/components/date-picker/src/date-picker.type'
    // 带农历日期显示的选择组件
    defineOptions({ name: 'LunarDatePicker' })

    const emit = defineEmits(['update:modelValue'])

    const props = defineProps({
        ...datePickerProps,
        showFestival: propTypes.bool.def(true), // 是否显示节日
        showJieQi: propTypes.bool.def(true), // 是否显示节气
        showLunarTip: propTypes.bool.def(true), // 是否使用 tooltip 显示农历日期
        lunarTipShowAfter: propTypes.number.def(0) // 在触发后多久使用 tooltip 显示农历日期，单位毫秒
    })

    const dateValue: Ref<typeof props.modelValue> = ref<typeof props.modelValue>('')

    watch(
        () => props.modelValue,
        (val: typeof props.modelValue) => {
            dateValue.value = val
        },
        {
            immediate: true
        }
    )

    watch(
        () => dateValue.value,
        (val) => {
            emit('update:modelValue', val)
        }
    )

    /**
     * 获取当前日期显示样式
     * @param dateCell 单元格日期信息
     */
    const getDateClass = (dateCell: DateCell) => {
        let cla = 'date-wrapper'
        if (dateCell.type === 'today') {
            cla += ' today'
        }

        if (dateCell.isCurrent || dateCell.isSelected || dateCell.start || dateCell.end) {
            cla += ' active'
        } else if (dateCell.inRange) {
            cla += ' in-range'
        }

        if (dateCell.disabled) {
            cla += ' disabled-date'
        }
        return cla
    }

    /**
     * 获取农历 day 显示文字
     */
    const getLunarDay = (date) => {
        const solarDate = Solar.fromDate(date)
        const lunarDate = solarDate.getLunar()
        // 每月第一天显示月数
        if (lunarDate.getDay() == 1) {
            return lunarDate.getMonthInChinese() + '月'
        }

        // 显示节日
        if (props.showFestival) {
            const festivals = lunarDate.getFestivals()
            if (!isEmpty(festivals)) {
                return festivals[0]
            }
        }

        // 显示节气
        if (props.showJieQi) {
            const currJieQi: JieQi = lunarDate.getCurrentJieQi() as JieQi
            if (currJieQi && currJieQi?.getName()) {
                return currJieQi?.getName()
            }
        }

        return lunarDate.getDayInChinese()
    }

    /**
     * 根据日历获取农历日期，包含年份干支和生肖
     */
    const getLunarDateStr = (date: Date): string => {
        const solarDate = Solar.fromDate(date)
        const lunarDate = solarDate.getLunar()
        return `${lunarDate.getYearInChinese()}年${lunarDate.getMonthInChinese()}月${lunarDate.getDayInChinese()} 【${lunarDate.getYearInGanZhi()}(${lunarDate.getYearShengXiao()})年】`
    }
</script>

<style lang="scss" scoped>
    .date-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        flex-direction: column;
        padding: 4px 0;
        line-height: 18px;
        text-align: center;

        .solar-text {
            font-size: 14px;
        }

        .lunar-text {
            white-space: nowrap;
        }
    }

    .today {
        font-weight: 700;
        color: var(--el-color-primary);
    }

    .active {
        color: #fff;
        background-color: var(--el-datepicker-active-color);
        border-radius: 5px;
    }

    .in-range {
        background-color: var(--el-datepicker-inrange-bg-color);
    }

    .disabled-date {
        cursor: not-allowed;
    }
</style>

