<template>
  <svg aria-hidden="true">
    <use :xlink:href="symbolId"></use>
  </svg>
</template>

<script setup>
import {computed} from 'vue';

const props = defineProps({
  // iconClass: {
  //   type: String,
  //   required: true
  // }
  prefix: {
    type: String,
    default: "icon",
  },
  name: {
    type: String,
    required: true,
  },
});

// const svgClass = computed(() => {
//   return `svg-icon ${props.iconClass}`;
// });

const symbolId = computed(() => {
  // return `#${props.iconClass}`;
  return `#${props.prefix}-${props.name}`;
});
</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  fill: currentColor;
  overflow: hidden;
  color: var(--el-color-primary);
}
</style>
