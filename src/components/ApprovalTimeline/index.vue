<template>
  <!-- 时间线组件，reverse表示是否倒序显示 -->
  <el-timeline :reverse="true">
    <!-- 遍历activities数组，生成时间线项 -->
    <template v-for="(activity, index) in activities" :key="`timeline-${index}`">
      <!-- 单个时间线项 -->
      <el-timeline-item :icon="activity.icon"
                        :type="activity.type"
                        :color="activity.color"
                        :size="activity.size"
                        :hollow="activity.hollow"
                        class="timeline-item">
        <!-- 时间线内容区域 -->
        <div class="timeline-content">
          <!-- 头部区域：标题、审核类型和状态 -->
          <div class="timeline-header">
            <!-- 左侧：标题和审核类型标签 -->
            <div class="header-left">
              <span class="list-title">{{ activity.title }}</span>  <!-- 节点标题 -->
              <el-tag v-if="activity.reviewType" size="small" class="review-type-tag">{{ activity.reviewType }}</el-tag>
              <!-- 审核类型标签 -->

              <!-- 右侧：节点状态 -->
              <div class="list-company" :style="{color: activity.color}">
                {{ activity.name }}
              </div>
            </div>
          </div>

          <!-- 会签类型的内容展示 -->
          <template v-if="activity.reviewType === '会签'">
            <div class="signature-container">
              <!-- 使用ele-check-card展示会签信息 -->
              <ele-check-card
                :items="activity.approvalNodeRecords"
                :item-style="{ width: '100%', marginBottom: '10px', padding: '12px' }"
              >
                <template #item="{ item }">
                  <!-- 单个会签项 -->
                  <div class="signature-item" :key="`signature-${item.nodeApproverId}`">
                    <div class="signature-info">
                      <!-- 审核人信息 -->
                      <ele-text size="md">审核人: {{ item.realName }}</ele-text>
                      <!-- 审核结果 -->
                      <div class="list-company1">{{ item.result }}</div>
                      <!-- 表单字段 -->
                      <div class="signature-fields">
                        <template
                          v-for="formField in getNodeFormsForApprover(activity.nodeForms, item.nodeApproverId)"
                          :key="formField.fieldEn">
                          <div class="field-item">
                            {{ formField.fieldZh }}: {{ item[formField.fieldEn] || '' }}
                          </div>
                        </template>
                      </div>
                      <!-- 操作时间 -->
                      <ele-text size="sm">操作时间: {{ item.createTime }}</ele-text>
                    </div>
                  </div>
                </template>
              </ele-check-card>
            </div>
          </template>

          <!-- 非会签类型的内容展示 -->
          <template v-else>
            <div class="approval-content">
              <!-- 如果有审批记录 -->
              <template v-if="activity.approvalNodeRecords && activity.approvalNodeRecords.length > 0">
                <div v-for="(item, idx) in activity.approvalNodeRecords" :key="`approval-${index}-${idx}`">
                  <!-- 只显示第一条记录 -->
                  <template v-if="idx === 0">
                    <div class="approval-info">
                      <!-- 审核人信息 -->
                      <div class="info-row">
                        <span class="info-label">审核人: </span>
                        <span class="info-value">{{ item.realName }}</span>
                      </div>
                      <!-- 审核状态 -->
                      <div class="info-row">
                        <span class="info-label">审核状态: </span>
                        <span class="info-value">{{ item.result }}</span>
                      </div>
                      <!-- 如果是退回状态，显示退回规则 -->
                      <div v-if="item.result === '退回'" class="info-row">
                        <span class="info-label">退回规则: </span>
                        <span class="info-value" :style="{color:activity.color}">
                          {{ getReturnWayLabel(item.returnWay) }}
                        </span>
                      </div>
                    </div>

                    <!-- 表单字段区域 -->
                    <div class="form-fields">
                      <template
                        v-for="formField in getNodeFormsForApprover(activity.nodeForms, item.nodeApproverId)"
                        :key="formField.fieldEn">
                        <div class="field-row">
                          <div>{{ formField.fieldZh }}:
                            <!-- 图片上传类型的特殊处理 -->
                            <template v-if="formField.controlType === 'imageUpload'&&item[formField.fieldEn]">
                              <el-avatar :size="95">
                                <img v-if="item[formField.fieldEn]"
                                     :src="getImageUrl(item[formField.fieldEn])"/>
                              </el-avatar>
                            </template>
                            <!-- 其他类型的字段显示 -->
                            <template v-else>
                              {{ item[formField.fieldEn] || '' }}
                            </template>
                          </div>
                        </div>
                      </template>
                    </div>
                    <!-- 操作时间 -->
                    <div class="operation-time">
                      操作时间: {{ item.createTime }}
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </template>
        </div>
      </el-timeline-item>
    </template>
  </el-timeline>
</template>

<script setup>
import {defineProps} from 'vue';
import {
  ElTimeline,
  ElTimelineItem,
  ElAvatar,
  ElTag,
} from 'element-plus';

// 定义组件props
const props = defineProps({
  activities: {
    type: Array,
    required: true,
    default: () => [],  // 时间线数据
  },
});

// 退回方式映射
const approvalReturnWays = [
  {name: 'APPLICANT', label: '退回申请人'},
  {name: 'PRE_NODE', label: '退回上一节点'},
];

/**
 * 根据审批人ID获取对应的表单字段
 * @param {Array} nodeForms - 节点表单数据
 * @param {String} approverId - 审批人ID
 * @returns {Array} 匹配的表单字段数组
 */
const getNodeFormsForApprover = (nodeForms, approverId) => {
  return nodeForms?.filter(node => node.nodeApproverId === approverId) || [];
};

/**
 * 获取退回方式的显示标签
 * @param {String} returnWay - 退回方式代码
 * @returns {String} 退回方式标签
 */
const getReturnWayLabel = (returnWay) => {
  const way = approvalReturnWays.find(w => w.name === returnWay);
  return way?.label || '-';
};

/**
 * 处理图片URL
 * @param {String} imageData - 图片数据(JSON字符串)
 * @returns {String} 图片URL
 */
const getImageUrl = (imageData) => {
  try {
    const images = JSON.parse(imageData);
    return images.length > 0 ? `/api/file/inline/${images[0].id}` : '';
  } catch {
    return '';
  }
};
</script>

<style scoped>
/* 时间线项样式 */
.timeline-item {
  padding-bottom: 10px;
}

/* 时间线内容区域 */
.timeline-content {
  padding-left: 8px;
}

/* 头部区域样式 */
.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 头部左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 标题样式 */
.list-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

/* 审核类型标签样式 */
.review-type-tag {
  margin-left: 8px;
}

/* 公司/状态信息样式 */
.list-company {
  font-size: 13px;
}

/* 会签容器样式 */
.signature-container {
  margin-top: 8px;
}

/* 会签项样式 */
.signature-item {
  .signature-info {
    > * {
      margin-bottom: 6px;
    }

    .list-company1 {
      font-weight: 500;
      color: #333;
    }

    /* 会签字段区域 */

    .signature-fields {
      margin: 8px 0;

      .field-item {
        margin-bottom: 4px;
        font-size: 13px;
        color: #666;
      }
    }
  }
}

/* 审批内容区域 */
.approval-content {
  .approval-info {
    margin-bottom: 8px;

    /* 信息行样式 */

    .info-row {
      margin-bottom: 4px;
      font-size: 13px;

      .info-label {
        color: #666;
        margin-right: 8px;
      }

      .info-value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  /* 表单字段区域 */

  .form-fields {
    margin: 8px 0;

    .field-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  /* 操作时间样式 */

  .operation-time {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
  }
}
</style>
