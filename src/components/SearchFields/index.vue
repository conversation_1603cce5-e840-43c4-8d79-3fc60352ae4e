<template>
  <!-- 学号 -->
  <el-col
    v-if="setSelfCount === '0' || setSelfCount === '2' || searchExpand"
    :lg="6"
    :md="12"
    :sm="12"
    :xs="12"
  >
    <el-form-item label="学号">
      <el-input clearable v-model.trim="form.xgh" placeholder="请输入学号" />
    </el-form-item>
  </el-col>

  <!-- 姓名 -->
  <el-col
    v-if="setSelfCount === '0' || searchExpand"
    :lg="6"
    :md="12"
    :sm="12"
    :xs="12"
  >
    <el-form-item label="姓名">
      <el-input clearable v-model.trim="form.xm" placeholder="请输入姓名" />
    </el-form-item>
  </el-col>

  <!-- 院系 -->
  <el-col
    v-if="setSelfCount === '0' || searchExpand"
    :lg="6"
    :md="12"
    :sm="12"
    :xs="12"
  >
    <el-form-item label="院系">
      <dict-data
        placeholder="请选择院系"
        code="xymc"
        :model-value="form.xyid"
        @update:modelValue="(value) => (form.xyid = value)"
      />
    </el-form-item>
  </el-col>

  <!-- 专业 -->
  <el-col v-if="searchExpand && showZy" :lg="6" :md="12" :sm="12" :xs="12">
    <el-form-item label="所属专业">
      <dict-data
        placeholder="请选择专业"
        code="zymc"
        :model-value="form.zyid"
        :refresh="form.xyid"
        :dic-query-params="{ params: { xyid: form.xyid } }"
        @update:modelValue="(value) => (form.zyid = value)"
      />
    </el-form-item>
  </el-col>

  <!-- 班级 -->
  <el-col v-if="searchExpand && showBj" :lg="6" :md="12" :sm="12" :xs="12">
    <el-form-item label="班级">
      <dict-data
        placeholder="请选择班级"
        code="bjmc"
        :model-value="form.bjid"
        :refresh="form.xyid || form.zyid"
        :dic-query-params="{ params: { xyid: form.xyid, zyid: form.zyid } }"
        @update:modelValue="(value) => (form.bjid = value)"
      />
    </el-form-item>
  </el-col>
</template>

<script setup>
  import { defineProps, defineEmits } from 'vue';

  const props = defineProps({
    //支持通过 v-model:form 实现与父组件的双向数据绑定，无需 emit，直接操作传入的 form
    form: {
      type: Object,
      required: true
    }, // ✅ 绑定外部传入的 form
    searchExpand: Boolean,
    setSelfCount: {
      type: String,
      default: '0'
    },
    /** 是否显示专业，默认显示 */
    showZy: {
      type: Boolean,
      default: true
    },
    /** 是否显示专业，默认显示 */
    showBj: {
      type: Boolean,
      default: true
    }
  });
  const { form } = props;
  const emit = defineEmits(['update:form']);

  const updateValue = (field, value) => {
    form[field] = value;
  };
</script>
