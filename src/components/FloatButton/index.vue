<template>
  <div
    ref="floatDrag"
    id="floatDrag"
    class="float-position"
    :style="{ top: data.top + 'px', right: data.right + 'px !important' }"
    @touchmove.prevent
    @mousemove.prevent
    @mousedown="mouseDown"
    @mouseup="mouseUp"
  >
    <div class="content">
      <el-row :gutter="10" v-if="needType.includes('statistic')">
        <el-col>
          <ele-tooltip :content="title" effect="light" placement="left">
            <el-button
              class="btn"
              size="small"
              type=""
              @click="onClick('statistic')"
              :title="title"
            >
              <el-icon size="20" class="btn-icon-tj"/>
            </el-button>
          </ele-tooltip>
        </el-col>
      </el-row>
      <el-row
        :gutter="10"
        style="margin-top: 10px"
        v-if="needType.includes('help')"
      >
        <el-col>
          <ele-tooltip content="使用帮助" effect="light" placement="left">
            <el-button
              class="btn btn-icon-bz"
              size="small"
              type=""
              @click="onClick('help')"
              title="使用帮助"
            >
              <el-icon size="20" class="icon-svg">
                <svg width="15" height="20" viewBox="0 0 15 24">
                  <g transform="matrix(1 0 0 1 -19 -160 )">
                    <path
                      d="M 0 6.8625  C 0.0875 2.8375  3.0625 0  8.0875 0  C 12.775 0  16 2.6  16 6.3375  C 16 8.8125  14.8 10.525  12.4875 11.8875  C 10.3125 13.15  9.575 14.075  9.575 15.675  L 9.575 16.6625  L 5.2375 16.6625  L 5.2 15.5875  C 4.9875 13.0125  5.8875 11.4125  8.15 10.0875  C 10.2625 8.825  11.15 8.025  11.15 6.475  C 11.15 4.925  9.65 3.7875  7.7875 3.7875  C 5.9 3.7875  4.5375 5.0125  4.4375 6.8625  L 0 6.8625  Z M 7.475 24  C 5.9875 24  4.8 22.875  4.8 21.425  C 4.8 19.9875  6 18.85  7.475 18.85  C 8.9625 18.85  10.1625 19.975  10.1625 21.425  C 10.1625 22.875  8.9625 24  7.475 24  Z "
                      fill-rule="nonzero"
                      fill=""
                      stroke="none"
                      transform="matrix(1 0 0 1 19 160 )"
                    />
                  </g>
                </svg>
              </el-icon>
            </el-button>
          </ele-tooltip>
        </el-col>
      </el-row>

      <!--      <div>-->
      <!--        <span class="close">-->
      <!--          <Icon icon="icon-guanbi" :size="18"/>-->
      <!--        </span>-->
      <!--        <Icon icon="icon-xuanzhong" :size="36" style="color: #fff"/>-->
      <!--      </div>-->
      <!--      <img src="../../assets/sanyth.png" alt=""/>-->
    </div>
  </div>
</template>

<script setup>
import {onMounted, reactive, nextTick, ref, onBeforeMount} from 'vue';
import {QuestionCircleOutlined} from '@/components/icons/index.js';

defineOptions({name: 'DragBall'});
const props = defineProps({
  title: {
    type: String,
    default: '学生统计'
  },
  distanceRight: {
    type: Number,
    default: 36
  },
  distanceBottom: {
    type: Number,
    default: 100
  },
  isScrollHidden: {
    type: Boolean,
    default: false
  },
  isCanDraggable: {
    type: Boolean,
    default: true
  },
  needType: Array
});
const emits = defineEmits(['handlepaly', 'floatClickEvent']);

const onClick = (type) => {
  emits('floatClickEvent', type);
};

const data = reactive({
  clientWidth: null,
  clientHeight: null,
  left: 0,
  top: 0,
  right: 0,
  timer: null,
  currentTop: 0,
  mousedownX: 0,
  mousedownY: 0
});
// const floatDrag = ref();
const floatDragDom = ref();
data.clientWidth = document.documentElement.clientWidth;
data.clientHeight = document.documentElement.clientHeight;
onMounted(() => {
  props.isCanDraggable &&
  nextTick(() => {
    // 获取元素位置属性
    floatDragDom.value = document.getElementById('floatDrag');
    // 设置初始位置
    data.right = props.distanceRight - 22;
    data.top =
      Number(data.clientHeight) -
      floatDragDom.value?.offsetHeight -
      props.distanceBottom +
      36;
    initDraggable();
  });
  // this.isScrollHidden && window.addEventListener('scroll', this.handleScroll);
  window.addEventListener('resize', handleResize);
});
onBeforeMount(() => {
  // window.removeEventListener('scroll', this.handleScroll);
  window.removeEventListener('resize', handleResize);
});
/**
 * 初始化draggable
 */
const initDraggable = () => {
  floatDragDom.value.addEventListener('touchstart', toucheStart);
  floatDragDom.value.addEventListener('touchmove', (e) => touchMove(e));
  floatDragDom.value.addEventListener('touchend', touchEnd);
};
const handleResize = () => {
  checkDraggablePosition();
};
/**
 * 判断元素显示位置
 * 在窗口改变和move end时调用
 */
const checkDraggablePosition = () => {
  data.clientWidth = document.documentElement.clientWidth;
  data.clientHeight = document.documentElement.clientHeight;
  if (
    data.right + floatDragDom.value.offsetWidth / 2 >=
    data.clientWidth / 2
  ) {
    // 判断位置是往左往右滑动
    data.right = data.clientWidth - floatDragDom.value.offsetWidth;
  } else {
    data.right = 0;
  }
  if (data.top < 0) {
    // 判断是否超出屏幕上沿
    data.top = 0;
  }
  if (data.top + floatDragDom.value.offsetHeight >= data.clientHeight) {
    // 判断是否超出屏幕下沿
    data.top = data.clientHeight - floatDragDom.value.offsetHeight;
  }
};
const canClick = ref(false);
const mouseDown = (e) => {
  const event = e || window.event;
  data.mousedownX = event.screenX;
  data.mousedownY = event.screenY;
  let floatDragWidth = floatDragDom.value.offsetWidth / 2;
  let floatDragHeight = floatDragDom.value.offsetHeight / 2;
  if (event.preventDefault) {
    event.preventDefault();
  }
  canClick.value = false;
  floatDragDom.value.style.transition = 'none';
  document.onmousemove = function (e) {
    var event = e || window.event;
    console.log(event.clientY);
    // 这里的判断是为了保证按钮只能在浏览器内拖动，不会超出
    data.right = Number(data.clientWidth) - event.clientX - floatDragWidth;
    data.top = event.clientY - floatDragHeight;
    if (data.right < 0) data.right = 0;
    if (data.top < 0) data.top = 0;
    // 鼠标移出可视区域后给按钮还原
    if (
      event.clientY < 0 ||
      event.clientY > Number(data.clientHeight) ||
      event.clientX > Number(data.clientWidth) ||
      event.clientX < 0
    ) {
      data.right = 0;
      data.top =
        Number(data.clientHeight) -
        floatDragDom.value?.offsetHeight -
        props.distanceBottom;
      document.onmousemove = null;
      floatDragDom.value.style.transition = 'all 0.3s';
      return;
    }
    if (
      data.right >=
      document.documentElement.clientWidth - floatDragWidth * 2
    ) {
      data.right = document.documentElement.clientWidth - floatDragWidth * 2;
    }
    if (data.top >= Number(data.clientHeight) - floatDragHeight * 2) {
      data.top = Number(data.clientHeight) - floatDragHeight * 2;
    }
  };
};
const mouseUp = (e) => {
  const event = e || window.event;
  //判断只是单纯的点击，没有拖拽
  if (data.mousedownY == event.screenY && data.mousedownX == event.screenX) {
    emits('handlepaly');
  }
  document.onmousemove = null;
  checkDraggablePosition();
  floatDragDom.value.style.transition = 'all 0.3s';
};
const toucheStart = () => {
  canClick.value = false;
  floatDragDom.value.style.transition = 'none';
};
const touchMove = (e) => {
  canClick.value = true;
  if (e.targetTouches.length === 1) {
    // 单指拖动
    let touch = e.targetTouches[0];
    data.right =
      Number(data.clientWidth) -
      touch.clientX -
      floatDragDom.value.offsetWidth / 2;
    data.top = touch.clientY - floatDragDom.value.offsetHeight / 2;
  }
};
const touchEnd = () => {
  if (!canClick.value) return; // 解决点击事件和touch事件冲突的问题
  floatDragDom.value.style.transition = 'all 0.3s';
  checkDraggablePosition();
};
</script>

<style>
html,
body {
  overflow: hidden;
}
</style>
<style scoped lang="less">
.float-position {
  position: fixed;
  z-index: 999 !important;
  right: 0;
  top: 66%;
  //width: 70px;
  //height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  border-radius: 50%;

  .content {
    position: relative;
    //padding: 0.8em;
    display: flex;
    flex-direction: column; /* 设置flex布局的方向为垂直 */
    //align-items: flex-start; /* 可选，根据需求调整对齐方式 */
    align-content: center;
    justify-content: center;
    //align-items: center;

    .btn {
      width: 40px;
      height: 40px;
      //background: #efeff6;
      //box-shadow: 0px 0px 10px 2px #efeff6;
      border-radius: 50%;
      border: 2px solid var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }

  .close {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    right: -10px;
    top: -12px;
    cursor: pointer;
  }
}

.cart {
  border-radius: 50%;
  width: 5em;
  height: 5em;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-notice {
  display: inline-block;
  transition: all 0.3s;

  span {
    vertical-align: initial;
  }

  .notice-badge {
    color: inherit;

    .header-notice-icon {
      font-size: 16px;
      padding: 4px;
    }
  }
}

.drag-ball .drag-content {
  overflow-wrap: break-word;
  font-size: 14px;
  color: #fff;
  letter-spacing: 2px;
}

.btn-icon-tj {
  background-color: var(--el-color-primary);
  mask-image: url(../../icons/svg/icon-统计_u2775.svg);
  -webkit-mask-image: url(../../icons/svg/icon-统计_u2775.svg);
  -webkit-mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-size: cover;
}

.icon-svg path {
  fill: var(--el-color-primary);
}
</style>
