<template>
  <ele-upload-list :limit="limit"
                   :drag="true"
                   :multiple="limit>1"
                   v-model="data"
                   :readonly="readonly"
                   :item-style="itemStyle"
                   :button-style="buttonStyle"
                   :style="{ maxWidth: void 0 }"
                   @upload="onUpload"
                   @remove="onRemove"/>
  <!--    @retry="(item) => onUpload(item, true)"-->
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {uploadFile} from '@/api/system/file';

const props = defineProps({
  /** 绑定值 */
  modelValue: String,
  /** 选择数量限制 */
  limit: {
    type: Number,
    default: 1
  },
  readonly: {
    type: Boolean,
    default: false
  },
  /** 文件大小限制, 单位MB */
  fileLimit: {
    type: Number,
    default: 20
  },
  /** 自定义样式 */
  itemStyle: Object,
  /** 自定义上传按钮样式 */
  buttonStyle: Object
});

const emit = defineEmits(['update:modelValue', 'change']);

/** 数据 */
const data = computed(() => {
  const result = [];
  const val = props.modelValue;
  if (val == null || val === '') {
    return result;
  }
  return Array.isArray(val) ? val : [val];
});

/** 要删除的附件ID*/
const deleteFileIds = ref([]);

// 上传事件
const onUpload = (d, retry) => {
  if (!d.file) {
    return;
  }
  if (!d.file.type.startsWith('image')) {
    EleMessage.error('只能选择图片');
    return;
  }
  if (d.file.size / 1024 / 1024 > props.fileLimit) {
    EleMessage.error(`大小不能超过 ${props.fileLimit}MB`);
    return;
  }
  data.value.push({...d});
  updateModelValue()
};

/** 删除事件 */
const onRemove = (item) => {
  if (item && item.status === 'done') deleteFileIds.value.push(item.key)
  data.value.splice(data.value.indexOf(item), 1);
  emit('change', deleteFileIds.value.join());
};

/** 更新modelValue */
const updateModelValue = () => {
  emit('update:modelValue', data.value);
};

/** 判断是否全部上传完成 */
const isDone = () => {
  return !data.value.some((d) => d.status !== 'done');
};

defineExpose({isDone});
</script>
