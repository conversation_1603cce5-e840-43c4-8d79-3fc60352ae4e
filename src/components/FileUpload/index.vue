<template>
  <ele-upload-list :limit="limit"
                   :drag="true"
                   :tools="true"
                   :multiple="limit>1"
                   v-model="data"
                   :item-style="itemStyle"
                   :button-style="buttonStyle"
                   :readonly="readonly"
                   :accept="accept"
                   :progress-props="{ strokeWidth: 2 }"
                   :sortable="{ forceFallback: true }"
                   :listType="listType"
                   :style="{ maxWidth: listType === 'file' ? '460px' : void 0 }"
                   :class="listType === 'file' ?'ele-attachment-upload':''"
                   @upload="onUpload"
                   @retry="(item) => onUpload(item, true)"
                   @remove="onRemove"
                   @itemClick="onItemClick">
    <template #thumbnail="{ item }">
      <div class="ele-upload-thumbnail" v-if="listType!=='image'">
        <ele-text size="md"
                  :icon="FileOutlined"
                  type="secondary"
                  style="margin-right: 6px; line-height: 1"/>
        <div class="ele-attachment-text">{{ item.name }}</div>
      </div>
    </template>
  </ele-upload-list>
</template>

<script setup>
import {ref, watch} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {FileOutlined} from '@/components/icons';

const props = defineProps({
  /** 绑定值 */
  modelValue: {
    type: [String, Array], // ✅ 支持多种类型
    default: () => []
  },
  /** 选择数量限制 */
  limit: {
    type: Number,
    default: 1
  },
  readonly: {
    type: Boolean,
    default: false
  },
  /** 文件大小限制, 单位MB */
  fileLimit: {
    type: Number,
    default: 20
  },
  /** 自定义样式 */
  itemStyle: Object,
  /** 自定义上传按钮样式 */
  buttonStyle: Object,
  /** 接受上传的文件类型 */
  accept: {
    type: String,
    default: ''
  },
  listType: {
    type: String,
    default: 'file'
  },
  contentId: {
    type: String,
    default: ''
  },
});

const emit = defineEmits(['update:modelValue', 'change']);

/** 数据 */
const data = ref([]);
/** 要删除的附件ID*/
const deleteFileIds = ref([]);

/** 上传事件 */
const onUpload = (d, retry) => {
  if (!d.file) {
    return;
  }
  if (props.accept === 'image/*') {
    if (!d.file.type.startsWith('image')) {
      EleMessage.error('只能选择图片');
      return;
    }
  } else if (props.accept === '.xls,.xlsx') {
    if (
      ![
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ].includes(d.file.type)
    ) {
      r
      EleMessage.error('只能选择 excel 文件');
      return false;
    }
  }
  if (d.file.size / 1024 / 1024 > props.fileLimit) {
    EleMessage.error(`大小不能超过 ${props.fileLimit}MB`);
    return;
  }
  if (!retry) {
    data.value.push({...d, contentId: props.contentId});
  }
  updateModelValue()
};

/** 删除事件 */
const onRemove = (item) => {
  if (item && item.status === 'done') deleteFileIds.value.push(item.key)
  data.value.splice(data.value.indexOf(item), 1);
  emit('change', deleteFileIds.value.join());
};

/** 点击事件 */
const onItemClick = (item) => {
  if (!item.isImageFile && item.fileUrl) window.open(item.fileUrl);
};

/** 更新绑定值 */
const updateModelValue = () => {
  emit('update:modelValue', data.value);
};

/** 判断是否全部上传完成 */
const isDone = () => {
  return !data.value.some((d) => d.status !== 'done');
};

// 同步绑定值
watch(data, () => {
    updateModelValue();
  },
  {deep: true}
);

watch(() => props.modelValue, (value) => {
    if (!value) {
      if (data.value.length) {
        data.value = [];
      }
      return;
    }
    data.value = value
  },
  {immediate: true}
);

defineExpose({isDone});
</script>

<style scoped lang="scss">

.ele-attachment-upload.ele-upload-list {
  width: 100%;

  :deep(.ele-upload-item) {
    display: block;
    width: 100%;
    height: 26px;
    margin: 0 0 6px 0;
    padding: 0 20px 0 8px;
    border: unset !important;
  }

  :deep(.ele-upload-button) {
    padding: 0;

    .ele-upload-icon {
      font-size: 15px;
    }
  }

  :deep(.ele-upload-progress) {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 0 20px 0 8px;

    .ele-upload-text {
      flex: 1;
      margin-bottom: 0;
    }

    .ele-upload-retry {
      margin: 0;
    }

    .el-progress {
      position: absolute;
      left: 4px;
      right: 4px;
      bottom: 0;
    }
  }

  .ele-upload-thumbnail {
    flex-direction: row;
    justify-content: flex-start;
  }

  .ele-attachment-text {
    flex: 1;
    font-size: 14px;
    line-height: 22px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }
}
</style>
