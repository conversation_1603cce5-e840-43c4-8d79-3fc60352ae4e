<template>
  <ele-page v-if="formItems" flex-table
            :style="{paddingTop:'0px!important', height: (pageHeight)+'px', overflow:'auto'}">
    <el-form
      ref="elFormRef"
      :model="localModel"
      :label-position="labelPosition"
      size="small"
      @submit.prevent=""
      :class="isReadonly?'border-only-bottom':''"
    >
      <el-row :gutter="8">
        <template v-for="item in visibleItems" :key="getItemKey(item)">
          <el-col :span="item.colProps || 24">
            <template v-if="shouldShowItem(item)">
              <!-- 特殊处理地址选择 -->
              <template v-if="item.type === 'Addresses'">
                <el-form-item :label="item.label" :prop="item.prop">
                  <el-row :gutter="4" style="width: 100%">
                    <el-col :span="18">
                      <ele-table-select
                        size="small"
                        clearable
                        :placeholder="item.placeholder || '从地址库选择'"
                        value-key="id"
                        label-key="name"
                        v-model="localModel[item.prop]"
                        :table-props="tableProps"
                        @select="(val) => handleAddressSelect(item, val)"
                        :popper-width="550"
                        style="width: 100%"
                        :disabled="isReadonly"
                      />
                    </el-col>
                    <el-col :span="6">
                      <el-button
                        type="primary"
                        :icon="MapLocation"
                        style="width: 100%"
                        @click="showMapDrawer = true"
                        :disabled="isReadonly"
                      >
                        地图上选择
                      </el-button>
                    </el-col>
                  </el-row>
                </el-form-item>
              </template>
              <!-- 普通表单项 -->
              <ProFormItem
                v-else
                :item="item"
                :model="localModel"
                :disabled="isReadonly"
                @updateItemValue="(prop, value) => handleFieldUpdate(item, prop, value)">
                <template v-for="(_, name) in $slots" #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"/>
                </template>
              </ProFormItem>
            </template>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <MapInitDrawer v-model="showMapDrawer" @done="handleMapAddressSelect"/>
  </ele-page>
</template>

<script setup>
import {ref, watch, nextTick, computed, onMounted} from 'vue';
import {MapLocation} from '@element-plus/icons-vue';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import {selectablePresetAddressPage} from '@/views/leave/auto-cancel-location/api/index.js';
import MapInitDrawer from '@/views/qgzx/components/MapInitDrawer.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const props = defineProps({
  formItems: {
    type: Array,
    default: () => [],
    validator: (value) => {
      return Array.isArray(value) && value.every(item => item.prop && item.label);
    },
  },
  formModel: {
    type: Object,
    default: () => ({}),
  },
  labelPosition: {
    type: String,
    default: 'right',
    validator: (value) => ['left', 'right', 'top'].includes(value),
  },
  isReadonly: {type: Boolean, default: false},
  isEditMode: {type: Boolean, default: false},
});

const emit = defineEmits([
  'update:formModel',
  'update:formItems',
  'onDoneDeleteFile',
]);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const elFormRef = ref(null);
const showMapDrawer = ref(false);
const localModel = ref({});
const localItems = ref([]);

// 性能优化：缓存计算结果
const visibleItems = computed(() => {
  return localItems.value.filter(item => shouldShowItem(item));
});

// 获取项的唯一键
const getItemKey = (item) => {
  return `${item.prop}_${item.type}_${item.showFlag}`;
};

// 判断是否应该显示该项
const shouldShowItem = (item) => {
  return item.showFlag === '是' || (props.isReadonly && item.showFlag === 'readonly');
};

// 地址库配置
const tableProps = ref({
  datasource: [],
  columns: [
    {prop: 'name', label: '签到位置', width: 190},
    {
      prop: 'type',
      label: '类型',
      formatter: (row) => ({
        poi: '圆形',
        Polygon: '多边形',
        Rectangle: '矩形',
      })[row.type] || row.type,
    },
    {prop: 'fwbj', label: '范围半径(米)', sortable: true},
  ],
  pagination: {pageSize: 6},
});

// 防抖加载地址数据
let loadAddressTimeout = null;
const loadAddressData = async () => {
  if (loadAddressTimeout) clearTimeout(loadAddressTimeout);
  loadAddressTimeout = setTimeout(async () => {
    try {
      const {list} = await selectablePresetAddressPage({type: 'poi'});
      tableProps.value.datasource = list || [];
    } catch (error) {
      console.error('加载地址数据失败:', error);
      tableProps.value.datasource = [];
    }
  }, 300);
};

// 处理地址选择
const handleAddressSelect = (item, selected) => {
  if (!selected) return;

  const updates = {
    [item.prop]: selected.name,
    jobAddresses: [selected],
    isFromMap: '是',
  };

  batchUpdateModel(updates);
};

const handleMapAddressSelect = (item) => {
  if (!item) return;

  batchUpdateModel({
    Addresses: item.name,
    jobAddresses: [item],
  });
};
let updateTimeout = null;
// 修改 handleFieldUpdate 方法，添加值变化判断
const handleFieldUpdate = (item, prop, value) => {
  // 只有当值确实发生变化时才处理
  if (localModel.value[prop] === value) return;
  if (updateTimeout) clearTimeout(updateTimeout);

  updateTimeout = setTimeout(() => {
    batchUpdateModel({[prop]: value});

    nextTick(() => {
      // 处理字段联动
      if (item.fieldLinks?.length) {
        handleFieldLinks(item, value);
      }

      // 处理自身字段联动
      if (item.selfFieldLink) {
        handleSelfFieldLink(item, value);
      }
    });
  }, 100);
};

// 同时修改 batchUpdateModel 方法，确保只有值变化时才更新
const batchUpdateModel = (updates) => {
  let hasChange = false;
  const newModel = {...localModel.value};

  Object.keys(updates).forEach(key => {
    if (newModel[key] !== updates[key]) {
      newModel[key] = updates[key];
      hasChange = true;
    }
  });

  if (hasChange) {
    localModel.value = newModel;
    emit('update:formModel', newModel);
  }
};

// 优化的字段联动处理
function handleFieldLinks(item, value) {
  if (!item.fieldLinks?.length) return;

  const updatedItems = [...localItems.value];
  let hasChanges = false;

  item.fieldLinks.forEach(link => {
    // 宽松的条件判断，处理初始值 undefined 的情况
    const isMatch = link.fieldEn === item.prop &&
      (link.fieldVal === undefined ||
        link.fieldVal === value ||
        (Array.isArray(link.fieldVal) && link.fieldVal.includes(value)) ||
        (value === undefined && link.fieldVal === null));

    if (isMatch) {
      const targetIndex = updatedItems.findIndex(i => i.prop === link.linkField);
      if (targetIndex !== -1) {
        const targetItem = updatedItems[targetIndex];
        const newShowFlag = props.isReadonly ? 'readonly' : link.showFlag;

        if (targetItem.showFlag !== newShowFlag) {
          updatedItems[targetIndex] = {
            ...targetItem,
            showFlag: newShowFlag,
            selfModifyFlag: link.showFlag,
          };
          hasChanges = true;
        }
      }
    }
  });

  if (hasChanges) {
    localItems.value = updatedItems;
    emit('update:formItems', updatedItems);
  }
}

// 优化的自身字段关联处理
function handleSelfFieldLink(item, value) {
  if (!item.selfFieldLink || !item.nextField) return;

  const linkedItem = localItems.value.find(f => f.selfFieldLink === item.nextField);
  if (!linkedItem) return;

  const updatedItems = localItems.value.map(i => {
    if (i.prop !== linkedItem.prop) return i;

    const newItem = {...i, props: {...i.props, refresh: value}};

    // 特殊联动处理
    if (item.nextField === 'zymc') {
      newItem.props.dicQueryParams = {
        ...newItem.props.dicQueryParams,
        params: {xyid: value},
      };
    } else if (item.nextField === 'bjmc') {
      newItem.props.dicQueryParams = {
        ...newItem.props.dicQueryParams,
        params: {zyid: value},
      };
    }

    return newItem;
  });

  localItems.value = updatedItems;
  emit('update:formItems', updatedItems);
}

// 优化的初始化数据监听
let initializing = false;
// 修改初始化数据监听部分
watch(() => props.formModel, (newModel) => {
  if (initializing) return;

  initializing = true;
  localModel.value = {...newModel};

  // 初始化地址显示
  if (newModel.jobAddresses?.[0]?.name) {
    localModel.value.Addresses = newModel.jobAddresses[0].name;
  }

  // 使用 Promise.resolve().then() 替代 nextTick 确保所有数据更新完成
  Promise.resolve().then(() => {
    // 初始值联动处理（分批执行避免卡顿）
    const batchSize = 5;
    const processBatch = (startIndex) => {
      const endIndex = Math.min(startIndex + batchSize, localItems.value.length);

      for (let i = startIndex; i < endIndex; i++) {
        const item = localItems.value[i];
        if (item.prop in newModel && newModel[item.prop] !== undefined) {
          // 直接调用联动处理，不使用 handleFieldUpdate 避免防抖延迟
          const value = newModel[item.prop];
          batchUpdateModel({[item.prop]: value});

          // 立即处理联动
          if (item.fieldLinks?.length) {
            handleFieldLinks(item, value);
          }
          if (item.selfFieldLink) {
            handleSelfFieldLink(item, value);
          }
        }
      }

      if (endIndex < localItems.value.length) {
        setTimeout(() => processBatch(endIndex), 50);
      } else {
        initializing = false;
      }
    };

    processBatch(0);
  });
}, {deep: true, immediate: true});

// 优化的字段配置监听
watch(() => props.formItems, (newItems) => {
  localItems.value = newItems.map(item => ({
    ...item,
    showFlag: item.showFlag ?? '是',
    colProps: item.colProps ?? 24,
  }));
}, {deep: true, immediate: true});

// 暴露方法
defineExpose({
  validate: () => new Promise(resolve => {
    elFormRef.value?.validate((valid) => {
      resolve(valid);
    });
  }),
  resetFields: () => {
    elFormRef.value?.resetFields();
    localModel.value = {};
  },
  clearValidate: () => {
    elFormRef.value?.clearValidate();
    localModel.value = {};
  },
  getFormData: () => ({...localModel.value}),
  setFieldsValue: (values) => {
    Object.keys(values).forEach(key => {
      localModel.value[key] = values[key];
    });
  },
});
// 初始化时清除验证
onMounted(() => {
  nextTick(() => {
    elFormRef.value.clearValidate();
  });
});
// 初始化加载
loadAddressData();
</script>

<style scoped>
@import "@/css/border-only-bottom.css";

/* 性能优化：减少不必要的样式计算 */
.el-form-item {
  margin-bottom: 16px;
  transition: none !important;
}

.el-row {
  flex-wrap: wrap;
}
</style>
