<template>
  <div v-if="formItems" :class="isReadonly?'border-only-bottom':''">
    <el-form ref="elFormRef"
             :model="formModel"
             :label-width="labelWidth"
             :label-position="labelPosition"
             size="small"
             @submit.prevent="">
      <el-row :gutter="8">
        <template v-for="item in visibleItems" :key="item.key">
          <el-col :span="item.colProps">
            <template v-if="item.type === 'Addresses'">
              <el-form-item :label="item.label" prop="Addresses">
                <el-row :gutter="4" style="width: 100%">
                  <el-col :span="18">
                    <ele-table-select
                      size="small"
                      clearable
                      placeholder="从地址库选择"
                      value-key="id"
                      label-key="name"
                      v-model="formModel.Addresses"
                      :table-props="tableProps"
                      @select="onSelectApresetAddress"
                      :popper-width="550"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="6">
                    <el-button type="primary" :icon="MapLocation" style="width: 100%"
                               @click="() => showMapDrawer = true">
                      地图上选择
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </template>
            <ProFormItem v-else :item="item"
                         :model="formModel"
                         @updateItemValue="(prop, value) => handleFieldUpdate(item, prop, value)">
              <template v-for="(_, name) in $slots" #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"/>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <MapInitDrawer v-model="showMapDrawer" @done="handleUpdateAddressMap"/>
  </div>
</template>

<script setup>
import {ref, computed, watch, reactive} from 'vue';
import {MapLocation} from '@element-plus/icons-vue';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import {selectablePresetAddressPage} from '@/views/leave/auto-cancel-location/api/index.js';
import MapInitDrawer from '@/views/qgzx/components/MapInitDrawer.vue';

const props = defineProps({
  formItems: Array,
  formModel: Object,
  labelWidth: {type: String, default: '100px'},
  labelPosition: {type: String, default: 'right'},
  isReadonly: Boolean,
});

const emit = defineEmits([
  'update:formModel',
  'update:formItems',
  'onDoneDeleteFile',
]);

const elFormRef = ref(null);
const localItems = ref([...props.formItems]);
const localModel = ref({...props.formModel});

// 地址库表格配置
const tableProps = reactive({
  datasource: [],
  columns: [
    {
      prop: 'name',
      label: '签到位置',
      width: 190,
    },
    {
      prop: 'type',
      label: '类型',
      formatter: (row) => {
        const types = {
          poi: '圆形',
          Polygon: '多边形',
          Rectangle: '矩形',
        };
        return types[row.type] || row.type;
      },
    },
    {
      prop: 'fwbj',
      label: '范围半径(米)',
      sortable: true,
    },
  ],
  pagination: {
    pageSize: 6,
    layout: 'total, prev, pager, next, jumper',
  },
});

// 初始化加载地址库数据
const loadAddressData = async () => {
  let data = {
    type: 'poi',
  };
  try {
    let datasource = await selectablePresetAddressPage(data);
    tableProps.datasource = datasource.list;
  } catch (e) {
    console.error('加载地址数据失败:', e);
  }
};
const showMapDrawer = ref(false);
const onChangeAddress = async (event) => {
  console.log(event);
  if (event === 'PresetAddress') {
    await loadAddressData();
  } else {
    showMapDrawer.value = true;
  }
};

const onSelectApresetAddress = ((item) => {
  localModel.value = {
    ...localModel.value,
    ['Addresses']: item.name,
    ['jobAddresses']: item,
    ['isFromMap']: '是',
  };
  emit('update:formModel', localModel.value);
});

const handleUpdateAddressMap = ((item) => {
  localModel.value = {
    ...localModel.value,
    ['Addresses']: item.name,
    ['jobAddresses']: item,
  };
  emit('update:formModel', localModel.value);
});

// 计算可见项
const visibleItems = computed(() => {
  return localItems.value.filter(item =>
    (item.showFlag === '是' && item.selfModifyFlag === '是') ||
    item.showFlag === 'readonly',
  );
});

// 处理字段更新
function handleFieldUpdate(item, prop, value) {
  localModel.value = {...localModel.value, [prop]: value};
  emit('update:formModel', localModel.value);

  // 处理联动逻辑
  if (item.fieldLinks) handleFieldLinks(item, value);
  if (item.selfFieldLink) handleSelfFieldLink(item, value);
}

// 字段联动处理
function handleFieldLinks(item, value) {
  const matchedLinks = item.fieldLinks.filter(link =>
    link.fieldEn === item.prop && link.fieldVal === value,
  );

  if (matchedLinks.length === 0) return;

  const updatedItems = localItems.value.map(originalItem => {
    const linkConfig = matchedLinks.find(link => link.linkField === originalItem.prop);
    if (!linkConfig) return originalItem;
    return {
      ...originalItem,
      showFlag: props.isReadonly ? 'readonly' : linkConfig.showFlag,
      selfModifyFlag: linkConfig.showFlag,
    };
  });

  localItems.value = updatedItems;
  emit('update:formItems', updatedItems);
}

// 自身字段关联处理
function handleSelfFieldLink(item, value) {
  const linkedItem = localItems.value.find(f => f.selfFieldLink === item.nextField);
  if (!linkedItem) return;

  const updatedItems = localItems.value.map(i => {
    if (i.prop !== linkedItem.prop) return i;

    const newItem = {...i, props: {...i.props, refresh: value}};

    // 特殊联动处理
    if (item.nextField === 'zymc') {
      newItem.props.dicQueryParams = {...newItem.props.dicQueryParams, params: {xyid: value}};
    } else if (item.nextField === 'bjmc') {
      newItem.props.dicQueryParams = {...newItem.props.dicQueryParams, params: {zyid: value}};
    }

    return newItem;
  });

  localItems.value = updatedItems;
  emit('update:formItems', updatedItems);
}

// 初始化联动逻辑
watch(() => props.formModel, (newModel) => {
  localModel.value = {
    ...newModel,
    Addresses: newModel.jobApplication?.jobAddresses?.name || '',
  };
  localItems.value.forEach(item => {
    if (item.fieldLinks && newModel[item.prop] !== undefined) {
      handleFieldLinks(item, newModel[item.prop]);
    }
  });
}, {immediate: true, deep: true});

watch(() => props.formItems, (newItems) => {
  localItems.value = [...newItems];
}, {deep: true});

// 暴露验证方法
defineExpose({
  validate: () => {
    return new Promise((resolve) => {
      elFormRef.value.validate((valid) => {
        resolve(valid);
      });
    });
  },
  resetFields: () => {
    elFormRef.value?.resetFields();
    localModel.value = {};
    localItems.value = [...props.formItems];
  },
});
loadAddressData();
</script>
