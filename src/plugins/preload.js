function All() {
}

All.prototype = {
  timer: "", debounce(fn, delay = 500) {
    var _this = this;
    return function (arg) {
      //获取函数的作用域和变量
      let that = this;
      let args = arg;
      clearTimeout(_this.timer) // 清除定时器
      _this.timer = setTimeout(function () {
        fn.call(that, args)
      }, delay)
    }
  }, setCookie(val) { //cookie设置[{key:value}]、获取key、清除['key1','key2']
    for (var i = 0, len = val.length; i < len; i++) {
      for (var key in val[i]) {
        document.cookie = key + '=' + encodeURIComponent(val[i][key]) + "; path=/";
      }
    }
  }, getCookie(name) {
    var strCookie = document.cookie;
    var arrCookie = strCookie.split("; ");
    for (var i = 0, len = arrCookie.length; i < len; i++) {
      var arr = arrCookie[i].split("=");
      if (name == arr[0]) {
        return decodeURIComponent(arr[1]);
      }
    }
  }, clearCookie(name) {
    var myDate = new Date();
    myDate.setTime(-1000); //设置时间
    for (var i = 0, len = name.length; i < len; i++) {
      document.cookie = "" + name[i] + "=''; path=/; expires=" + myDate.toGMTString();
    }
  }, arrToStr(arr) {
    if (arr) {
      return arr.map(item => {
        return item.name
      }).toString()
    }
  }, toggleClass(arr, elem, key = 'id') {
    return arr.some(item => {
      return item[key] == elem[key]
    });
  }, toChecked(arr, elem, key = 'id') {
    var isIncludes = this.toggleClass(arr, elem, key);
    !isIncludes ? arr.push(elem) : this.removeEle(arr, elem, key);
  }, removeEle(arr, elem, key = 'id') {
    var includesIndex;
    arr.map((item, index) => {
      if (item[key] == elem[key]) {
        includesIndex = index
      }
    });
    arr.splice(includesIndex, 1);
  }, setApproverStr(nodeConfig) {
    if (nodeConfig.settype == 2) {
      if (nodeConfig) {
        let nodeapprovers = nodeConfig.workflowNodeApprovers;
        if (nodeapprovers.length > 0) {
          if (nodeapprovers.length === 1) {
            let nodePersonName = nodeapprovers[0].selectorData ? JSON.parse(nodeapprovers[0].selectorData)['name'] : '';
            return nodePersonName + nodeConfig.reviewType
          } else {
            return nodeapprovers.length + "人" + nodeConfig.reviewType
          }
        }
      } else {
        return ""
      }
    }
  }, dealStr(str, obj) {
    let arr = [];
    let list = str.split(",");
    for (var elem in obj) {
      list.map(item => {
        if (item == elem) {
          arr.push(obj[elem].value)
        }
      })
    }
    return arr.join("或")
  }, conditionStr(nodeConfig, index) {
    var {conditionDetail, nodeUserList} = nodeConfig.conditionNodes[index];
    if (conditionDetail.length == 0) {
      return "请设置条件"
      // return (index == nodeConfig.conditionNodes.length - 1) && nodeConfig.conditionNodes[0].conditionDetail.length != 0 ? '其他条件进入此流程' : '请设置条件'
    } else {
      let str = ""
      for (var i = 0; i < conditionDetail.length; i++) {
        let currentData = conditionDetail[i];
        str += currentData.fieldZh + ','
      }
      return str ? str : '请设置条件'
    }
  }, copyerStr(nodeConfig) {
    console.log(nodeConfig)
    if (nodeConfig.nodeUserList&&nodeConfig.nodeUserList.length != 0) {
      return this.arrToStr(nodeConfig.nodeUserList)
    } else {
      if (nodeConfig.ccSelfSelectFlag == 1) {
        return "发起人自选"
      }
    }
  }, toggleStrClass(item, key) {
    let a = item.zdy1 ? item.zdy1.split(",") : []
    return a.some(item => {
      return item == key
    });
  },
}

export default new All();
