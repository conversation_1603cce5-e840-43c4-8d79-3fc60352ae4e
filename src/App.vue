<template>
  <el-config-provider :locale="elLocale">
    <ele-config-provider
      :locale="eleLocale"
      :map-key="MAP_KEY"
      :table="tableConfig"
      :license="LICENSE_CODE">
      <ele-app>
        <router-view/>
        <!--        <router-view :key="$route.path" />-->
      </ele-app>
    </ele-config-provider>
  </el-config-provider>
</template>

<script setup>
import {useThemeStore} from '@/store/modules/theme';
import {MAP_KEY, LICENSE_CODE} from '@/config/setting';
import {useLocale} from '@/i18n/use-locale';
import {EleApp, EleConfigProvider} from 'ele-admin-plus';
import {onMounted, provide, ref} from 'vue';
import store from '@/store/index.js';
import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user.js';
import {getToken} from '@/utils/token-util.js';
import {getSystemInfo} from '@/api/login/index.js';

// App.vue
const refreshFlag = ref(false);
provide('refreshEvent', {
  refreshFlag, // 提供响应式 ref
  triggerRefresh: () => {
    refreshFlag.value = !refreshFlag.value;
  },
});

//山东农业工程学院
window._AMapSecurityConfig = {
  securityJsCode: '028662055fcac38f2d455e76867204b7',
};

// 恢复主题
const themeStore = useThemeStore();
themeStore.recoverTheme();

// 国际化配置
const {elLocale, eleLocale} = useLocale();

// 高级表格全局配置
const tableConfig = ref({
  response: {
    dataName: 'list',
    countName: 'count',
  },
});

const systemInfo = ref([]);
const initSystemInfo = async () => {
  systemInfo.value = await getSystemInfo({});

// 调用设置 favicon 函数
  setDynamicFavicon();
};

// // 在创建应用后立即设置动态 favicon
function setDynamicFavicon() {
  // 从 store 中获取 systemInfo
  let faviconUrl = '/favicon.ico'; // 默认 favicon
  // 检查是否有自定义 favicon 配置
  if (systemInfo.value.favicon?.attachment) {
    try {
      const attachments = JSON.parse(systemInfo.value.favicon.attachment);
      if (attachments && attachments.length > 0) {
        faviconUrl = `/api/file/inline/${attachments[0].id}`;
      }
    } catch (e) {
      console.error('解析 favicon 附件失败:', e);
    }
  }
  // 创建或更新 favicon link 元素
  let link = document.querySelector('link[rel*=\'icon\']') ||
    document.createElement('link');
  link.type = 'image/x-icon';
  link.rel = 'shortcut icon';
  link.href = faviconUrl;

  // 添加到 head
  document.getElementsByTagName('head')[0].appendChild(link);
  // 添加时间戳避免缓存
  const timestamp = new Date().getTime();
  link.href = `${faviconUrl}?t=${timestamp}`;
}

// 初始化
onMounted(() => {
  initSystemInfo();
});
</script>

<style lang="scss">
.el-input-number--small {
  width: 100% !important;
}

.el-input-number .el-input__inner {
  text-align: left !important;
}

.el-input-number.is-controls-right .el-input__wrapper {
  padding-left: 8px !important;
}

/**隐藏高德自带的logo*/
.amap-logo {
  display: none !important;
}

.amap-copyright {
  bottom: -100px;
  display: none !important;
}

.ele-drawer-header {
  padding: 10px 16px !important;
}

.ele-drawer-title {
  font-size: 14px !important;
  font-weight: bold !important;
}

.ele-drawer-body {
  padding: 12px !important;
}

.ele-card-header {
  padding: 5px 10px 5px 10px !important;
  font-size: 14px !important;
}

.ele-card-body {
  padding: 16px 0 16px 0 !important;
}

.el-form-item {
  margin-bottom: 13px !important;
}

.el-button.help,
.el-button.statistic {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  right: 20px;
  z-index: 1000;
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 50%;
  border: 2px solid var(--el-color-primary);

  .statistic-icon {
    color: var(--el-color-primary);
  }
}

.el-button.help {
  bottom: 35px;
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary);
}

.el-button.statistic {
  bottom: 90px;
}

/* 全局修改 ele-pro-table 为空时的样式 start */
.ele-pro-table .el-empty__image {
  width: 300px !important;
  height: 300px !important;
  background: url('components/illustrations/empty-list.svg') no-repeat center center;
  background-size: contain;
}

.ele-pro-table .el-empty__image svg {
  display: none; /* 隐藏默认的 SVG */
}

/* 全局修改 ele-pro-table 为空时的样式 end */
.ele-menu-title {
  font-size: 13px !important;
}

/* 设置 ElMessageBox 宽度 */
.custom-alert-box {
  max-width: 820px !important;
}

.custom-alert-box .el-message-box__content {
  max-height: 560px;
  overflow-y: auto;
}
</style>

<style lang="scss">
/*elecard*/
/* 响应式模式容器 */
.mode-container {
  width: 100%;
}

/* 模式选项整体布局 */
.mode-options {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 单个模式选项 */
.mode-option {
  display: flex;
  height: 100%;
  padding: 8px;
  border-radius: 4px;
  /* background: var(--el-fill-color-light);*/
  transition: all 0.3s;
}

.mode-option:hover {
  //background: var(--el-fill-color);
}

/* 模式内容区域 */
.mode-content {
  flex: 1;
  overflow: hidden;
  padding-left: 4px;
}

/* 标题样式 */
.mode-title {
  display: block;
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
}

/* 描述文本 */
.mode-desc {
  display: block;
  font-size: 12px;
  line-height: 1.4;
  color: var(--el-text-color-secondary);
}

.ele-check-card.is-disabled {
  background: unset !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mode-options {
    grid-template-columns: 1fr;
  }

  .mode-option {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .mode-title {
    font-size: 13px;
  }

  .mode-desc {
    font-size: 11px;
  }
}


/**页面返回样式**/
.back-btn {
  width: 80px;
  cursor: pointer;
  color: var(--text-secondary);
  font-weight: 400;
  padding: 6px 10px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
  }

  span {
    margin-left: 4px;
  }
}

.el-date-editor {
  width: 100% !important;
}

.el-cascader {
  width: 100% !important;
}

</style>
