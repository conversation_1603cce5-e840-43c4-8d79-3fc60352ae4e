import request from '@/utils/request';
import {setToken} from '@/utils/token-util';
import {useUserStore} from '@/store/modules/user.js';
import {reactive} from 'vue';
import {defaultThemeConfig, safeParseTheme} from '@/utils/common_bak2.js';

/**
 * 登录
 */
export async function login(data) {
  const res = await request.post('/login', data);
  if (res.data.code === 0) {
    setToken(res.data.data?.access_token, data.remember);
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getSystemInfo(data) {
  const res = await request.post('/system-info', data);
  if (res.data.code === 0) {
    // 定义系统信息对象
    const systemInfo = reactive({
      'favicon': null,
      '学校名称': {paramValue: '智慧大学'}, // 默认值
      '学校地址': {paramValue: null}, // 默认值
      '备案号': {paramValue: null}, // 默认值
      '图标+学校名称': null,
      'themeConfig': {
        //默认蓝色主题
        ...defaultThemeConfig(),
      },
    });
    // 将API数据转换为更易使用的对象格式
    if (res.data.data.length > 0) res.data.data.forEach(item => {
      systemInfo[item.paramName] = item.paramName === 'themeConfig'
        ? safeParseTheme(item.paramValue)
        : item;
    });
    const userStore = useUserStore();
    userStore.setSystemInfo(systemInfo);

    return systemInfo;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取验证码
 */
export async function getCaptcha() {
  const res = await request.get('/captcha');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 退出
 */
export async function checkLogout() {
  const res = await request.get('/logout');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
