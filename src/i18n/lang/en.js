export const en = {
  layout: {
    home: 'Home',
    header: {
      profile: 'Profile',
      password: 'Password',
      logout: 'SignOut'
    },
    footer: {
      website: 'Website',
      document: 'Document',
      authorization: 'Authorization',
      copyright: 'Copyright © 2024 Wuhan EClouds Technology Co., Ltd'
    },
    logout: {
      title: 'Confirm',
      message: 'Are you sure you want to log out?'
    },
    tabs: {
      reload: 'Refresh',
      close: 'Close',
      closeLeft: 'Close Left',
      closeRight: 'Close Right',
      closeOther: 'Close Other',
      closeAll: 'Close All',
      fullscreen: 'Fullscreen',
      fullscreenExit: 'Fullscreen'
    },
    setting: {
      title: 'Theme Setting',
      sideStyles: {
        dark: 'Dark Sidebar',
        light: 'Light Sidebar'
      },
      headStyles: {
        light: 'Light Header',
        dark: 'Dark Header',
        primary: 'Primary Header'
      },
      layoutStyles: {
        side: 'Side Menu Layout',
        top: 'Top Menu Layout',
        mix: 'Mix Menu Layout'
      },
      colors: {
        default: 'Daybreak Blue',
        dust: 'Dust Blue',
        sunset: 'Sunset Orange',
        volcano: 'Volcano',
        purple: 'Golden Purple',
        green: 'Polar Green',
        geekblue: 'Geek Blue'
      },
      darkMode: 'Dark Mode',
      roundedTheme: 'Rounded Theme',
      layout: 'Navigation Mode',
      sidebarLayout: 'Sidebar Double Menu',
      fluid: 'Full Body Width',
      other: 'Other Setting',
      more: 'More Setting',
      tab: 'Tab Setting',
      fixedHeader: 'Fixed Header',
      fixedSidebar: 'Fixed Sidebar',
      fixedBody: 'Scrollbar In Content',
      logoInHeader: 'Logo In Header',
      colorfulIcon: 'Colorful Icon',
      uniqueOpened: 'Menu Unique Open',
      responsive: 'Responsive',
      weakMode: 'Weak Mode',
      showTabs: 'Show Tabs',
      fixedHome: 'Fixed Home Tab',
      tabInHeader: 'Tab In Header',
      tabStyle: 'Tab Style',
      tabStyles: {
        default: 'Default',
        dot: 'Dot',
        tag: 'Tag',
        card: 'Card'
      },
      menuItemTrigger: 'Menu Division',
      transitionName: 'Transition',
      transitions: {
        slideRight: 'Slide Right',
        slideBottom: 'Slide Bottom',
        zoomIn: 'Zoom In',
        zoomOut: 'Zoom Out',
        fade: 'Fade'
      },
      reset: 'Reset'
    }
  },
  login: {
    title: 'User Login',
    username: 'please input username',
    password: 'please input password',
    code: 'please input code',
    remember: 'remember',
    login: 'Login',
    passwordType: 'Password',
    qrcodeType: 'QR Code',
    refreshQrcode: 'Refresh'
  },
  list: {
    // 基础列表
    basic: {
      table: {
        avatar: 'Avatar',
        username: 'Username',
        nickname: 'Nickname',
        organizationName: 'Organization',
        phone: 'Phone',
        email: 'Email',
        roles: 'Roles',
        sexName: 'Sex',
        createTime: 'CreateTime',
        status: 'Status',
        action: 'Action'
      }
    }
  }
};
