import {createApp} from 'vue';
import App from './App.vue';
import store from './store/index.js';
import {useStore as vuexStore} from './store/store-vuex'; // 假设你的Vuex store文件名为store-vuex.js
import router from './router';

// 引入 Element Plus 样式（使用编译后的样式）
import 'element-plus/dist/index.css';
import '@/css/custom-tree-node.css';

import permission from './utils/permission';
import DictData from '@/components/DictData/index.vue';
import i18n from './i18n';
import installer from './as-needed';
import 'element-plus/theme-chalk/display.css';
import 'ele-admin-plus/es/style/nprogress.scss';
import './styles/themes/rounded.scss';
import './styles/themes/dark.scss';
import './styles/index.scss';

import Viewer from 'viewerjs';
import 'viewerjs/dist/viewer.css';

//导入Svg图片插件，可以在页面上显示Svg图片
import 'virtual:svg-icons-register';
import SvgIcon from './components/SvgIcon/index.vue';

// 如果您正在使用CDN引入，请删除下面一行。
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/theme-chalk/el-icon.css';
import 'element-plus/dist/index.css';

/**全局引入 IconPark 图标库**/
import '@icon-park/vue-next/styles/index.css';
import {install} from '@icon-park/vue-next/es/all';

const app = createApp(App);
// 使用自定义前缀
install(app, 'Icon'); // 使用 'Icon' 作为前缀，避免插槽名称冲突

window.previewImage = function(url, degree) {
  const img = document.createElement('img');
  img.src = url;
  const viewer = new Viewer(img, {
    title: 0,
    navbar: 0,
    toolbar: {
      zoomIn: 1,
      zoomOut: 1,
      oneToOne: 1,
      reset: 1,
      rotateLeft: 1,
      rotateRight: 1,
      flipHorizontal: 1,
      flipVertical: 1,
    },
    viewed() {
      if (degree) {
        viewer.zoomTo(0.8).rotateTo(degree);
      }
    },
  });
  img.onload = function() {
    viewer.show();
  };
};

// 全局注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(store);
app.use(vuexStore);
app.use(router);
app.use(permission);
app.use(i18n);
app.use(installer);

app.component('DictData', DictData);
app.component('SvgIcon', SvgIcon);
app.mount('#app');

import nodeWrap from '@/components/DrawFlow/nodeWrap.vue';

app.component('nodeWrap', nodeWrap); //初始化组件
import addNode from '@/components/DrawFlow/addNode.vue';

app.component('addNode', addNode); //初始化组件

app.directive('focus', {
  mounted(el) {
    el.focus();
  },
});

app.directive('enterNumber', {
  mounted(el, {value = 100}, vnode) {
    el = el.nodeName === 'INPUT' ? el : el.children[0];
    var RegStr = value === 0
      ? `^[\\+\\-]?\\d+\\d{0,0}`
      : `^[\\+\\-]?\\d+\\.?\\d{0,${value}}`;
    el.addEventListener('input', function() {
      el.value = el.value.match(new RegExp(RegStr, 'g'));
      el.dispatchEvent(new Event('input'));
    });
  },
});

