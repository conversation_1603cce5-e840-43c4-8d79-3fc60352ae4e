
import { ElLoading } from 'element-plus';

let loadingInstance = null; // 存储当前的加载实例

// 显示加载动画
const showLoading = (options = {}) => {
  if (!loadingInstance) {
    loadingInstance = ElLoading.service({
      lock: true, // 锁定屏幕，防止用户在加载过程中进行其他操作
      text: options.text || '加载中...', // 自定义加载提示文本
      background: options.background || 'rgba(0, 0, 0, 0.7)', // 自定义背景颜色
      spinner: options.spinner || null, // 自定义加载动画（可选）
      fullscreen: true, // 全屏模式
    });
  }
};

// 隐藏加载动画
const hideLoading = () => {
  if (loadingInstance) {
    loadingInstance.close(); // 关闭加载动画
    loadingInstance = null; // 重置实例
  }
};

export const withLoading = (fn, options = {}) => {
  const newFn = async (...args) => {
    try {
      showLoading(options); // 开始加载动画
      const result = await fn(...args); // 执行异步操作
      hideLoading(); // 完成后隐藏加载动画
      return result; // 返回结果
    } catch (err) {
      hideLoading(); // 出现错误时也要隐藏动画
      throw err; // 抛出错误给调用方处理
    }
  };
  return newFn; // 返回封装后的新函数
};
