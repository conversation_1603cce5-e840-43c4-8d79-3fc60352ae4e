// utils/path-util.js

export function setNestedProp(obj, path, value) {
  if (!path) return;
  const keys = path.split('.');
  let current = obj;
  keys.forEach((key, index) => {
    if (index === keys.length - 1) {
      current[key] = value;
    } else {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
  });
}

export function getNestedProp(obj, path) {
  return path.split('.').reduce((acc, key) => acc?.[key], obj);
}
