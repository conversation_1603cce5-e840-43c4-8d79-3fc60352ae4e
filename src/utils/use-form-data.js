// utils/use-form-data.js

import { reactive } from 'vue';
import { setNestedProp, getNestedProp } from './pathUtil.js';

export function useFormData(initialData = {}) {
  const form = reactive({ ...initialData });

  const resetFields = () => {
    Object.keys(form).forEach(key => delete form[key]);
    Object.assign(form, { ...initialData });
  };

  const assignFields = (data) => {
    Object.entries(data || {}).forEach(([key, value]) => {
      setNestedProp(form, key, value);
    });
  };

  const setFieldValue = (key, value) => {
    setNestedProp(form, key, value);
  };

  const getFieldValue = (key) => {
    return getNestedProp(form, key);
  };

  return [form, resetFields, assignFields, setFieldValue, getFieldValue];
}


// import { reactive, toRefs } from 'vue';
//
// /**
//  * 表单数据管理
//  * @param {Object} initForm - 初始表单数据
//  */
// export function useFormData(initForm = {}) {
//   const form = reactive({ ...initForm });
//   const defaultForm = JSON.parse(JSON.stringify(initForm)); // 深拷贝一份默认值
//
//   /**
//    * 重置表单
//    * @param {Object} [fields] - 可选，指定要重置的字段
//    */
//   // const resetFields = (fields) => {
//   //   if (fields) {
//   //     Object.keys(fields).forEach((key) => {
//   //       if (key in form) {
//   //         form[key] = fields[key];
//   //       }
//   //     });
//   //   } else {
//   //     Object.keys(defaultForm).forEach((key) => {
//   //       form[key] = defaultForm[key];
//   //     });
//   //   }
//   // };
//   const resetFields = (fields) => {
//       // 先把所有字段清掉
//       Object.keys(form).forEach((key) => {
//         delete form[key];
//       });
//       // 再重新赋值
//       Object.keys(fields || defaultForm).forEach((key) => {
//         form[key] = (fields || defaultForm)[key];
//       });
//     };
//
//   /**
//    * 批量赋值
//    * @param {Object} fields - 需要赋值的字段
//    */
//   // const assignFields = (fields) => {
//   //   Object.keys(fields || {}).forEach((key) => {
//   //     if (key in form) {
//   //       form[key] = fields[key];
//   //     } else {
//   //       form[key] = fields[key]; // 如果没有也动态加进去
//   //     }
//   //   });
//   // };
//
//   const assignFields=(data) =>{
//     Object.entries(data).forEach(([key, value]) => {
//       setNestedProp(form, key, value);
//     });
//   }
//
//   /**
//    * 单独设置字段
//    * @param {String} key - 字段名
//    * @param {*} value - 字段值
//    */
//   const setFieldValue = (key, value) => {
//     form[key] = value;
//   };
//
//   /**
//    *  •	resetFields() 👉 重置成默认的
//    *  •	resetFields({ name: '', agentId: '', ... }) 👉 动态重置部分字段
//    *  •	assignFields({ name: 'xx', agentId: 'xxx' }) 👉 动态填充字段
//    *  •	setFieldValue('name', '新名字') 👉 单独改一个字段
//    */
//   return [form, resetFields, assignFields, setFieldValue];
// }
