import { removeToken } from '@/utils/token-util';
import { removeCurrentRole } from '@/utils/current-role-util';
import { checkLogout } from '@/api/login';
import { ElMessage as EleMessage } from 'element-plus';

/**
 * 获取图片URL的方法
 * @param attachment
 * @returns {string}
 */
export function getImageUrl(attachment) {
  try {
    const attachments = JSON.parse(attachment);
    if (attachments && attachments.length > 0) {
      return `/api/file/inline/${attachments[0].id}`;
    }
    return '';
  } catch (e) {
    console.error('解析附件失败:', e);
    return '';
  }
}

/**
 * 默认蓝色主题
 * @returns {*}
 */
export function defaultThemeConfig() {
  return {
    primary: '#1890ff',
    gradientStart: '#096dd9',
    gradientEnd: '#1890ff',
    light: '#69c0ff',
    dark: '#0050b3',
    text: '#ffffff',
    bgStart: '#f0f9ff',
    bgEnd: '#e6f7ff',
    card: '#ffffff'
  };
}

/**
 * 安全解析主题配置
 * @param str
 * @returns {{}|any}
 */
export function safeParseTheme(str) {
  try {
    // 替换 key：加双引号
    let jsonStr = str.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
    // 替换 value 的单引号为双引号
    jsonStr = jsonStr.replace(/'([^']*)'/g, '"$1"');
    // 去除最后可能多余的逗号
    jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1');
    return JSON.parse(jsonStr);
  } catch (err) {
    console.error('主题配置解析失败：', err);
    return {};
  }
}

/**
 * 动态生成 form 的方法
 * @param columns
 * @returns {{}}
 */
export function generateForm(columns) {
  const form = {};
  columns.forEach((col) => {
    // 跳过 selection 类型
    if (col.type === 'selection') return;
    const prop = col.prop;
    if (!prop) return;
    // 根据类型推断默认值
    switch (col.type) {
      case 'input':
        form[col.prop] = '';
        break;
      case 'inputNumber':
        form[col.prop] = null;
        break;
      case 'switch':
        // form[col.prop] = false;
        form[col.prop] = '否';
        break;
      case 'array':
        form[col.prop] = [];
        break;
      case 'number':
        form[col.prop] = 0;
        break;
      default:
        form[col.prop] = '';
    }
  });
  return form;
}

// 动态生成验证规则
// export function generateRules(columns) {
//   const rules = {};
//   columns.forEach(col => {
//     // 跳过 selection 类型
//     if (col.type === 'selection') return;
//     const prop = col.prop;
//     console.log(prop)
//     const ruleList = [];
//
//     if (col.required) {
//       if (col.type === 'input') {
//         ruleList.push({
//           type: 'string',
//           message: `请输入${col.label}`,
//           trigger: ['blur', 'change']
//         });
//       }
//
//       if (col.type === 'switch') {
//         ruleList.push({
//           type: 'boolean',
//           message: `请选择${col.label}`,
//           trigger: ['blur', 'change']
//         });
//       }
//       if (ruleList.length > 0) {
//         rules[prop] = ruleList;
//       }
//     }
//   });
//   return rules;
// }

/**
 * 资助审核节点状态配置
 */
export function comApproveStatus() {
  return [
    {
      label: '', //用户自定义设置状态 值
      diyname: 'statePass', //用户自定义设置状态
      prename: 'presetStatePass', //系统内置状态
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 1
    },
    {
      label: '',
      diyname: 'stateTerminate', //用户自定义设置状态
      prename: 'presetStateTerminate', //系统内置状态
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 2
    },
    {
      label: '',
      diyname: 'stateReturn',
      prename: 'presetStateReturn',
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 3
    }
  ];
}

/**
 * 题库选项配置
 */
export function comQuestionSels() {
  return [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ];
}

/**
 * 公共表格列配置
 * @returns {[{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},null,null]}
 */
export function comTableCloumns() {
  return [
    {
      prop: 'xgh',
      label: '学工号'
    },
    {
      prop: 'xm',
      label: '姓名'
    },
    {
      prop: 'xymc',
      label: '所属院系'
    },
    {
      prop: 'zymc',
      label: '专业名称'
    },
    {
      prop: 'bjmc',
      label: '班级名称'
    },
    {
      prop: 'njmc',
      label: '年级名称'
    }
  ];
}

export function comCondition() {
  return [
    { label: '等于', value: 'EQ' },
    { label: '不等于', value: 'NE' },
    { label: '大于', value: 'GT' },
    { label: '大于等于', value: 'GE' },
    { label: '小于', value: 'LT' },
    { label: '小于等于', value: 'LE' },
    { label: '包含', value: 'LIKE' },
    { label: '不包含', value: 'NOT_LIKE' },
    { label: '结尾等于', value: 'LIKE_LEFT' },
    { label: '开头等于', value: 'LIKE_RIGHT' },
    { label: '为NULL', value: 'IS_NULL' },
    { label: '不为空', value: 'IS_NOT_NULL' },
    { label: 'IN', value: 'IN' },
    { label: 'NOT IN', value: 'NOT_IN' },
    { label: 'IN条件解析逗号分割', value: 'IN_STR' },
    { label: ' NOT IN条件解析逗号分割', value: 'NOT_IN_STR' }
  ];
}

/**
 * 退出登录
 * @param route 是否使用路由跳转
 * @param from 登录后跳转的地址
 * @param push 路由跳转方法
 */
export function logout(route, from, push) {
  checkLogout()
    .then(() => {
      removeToken();
      removeCurrentRole();
      if (route && push) {
        push({
          path: '/login',
          query: from ? { from: encodeURIComponent(from) } : void 0
        });
        return;
      }
      // 这样跳转避免再次登录重复注册动态路由
      const BASE_URL = import.meta.env.BASE_URL;
      const url = BASE_URL + 'login'; // hash 路由模式使用 '#/login'
      location.replace(from ? `${url}?from=${encodeURIComponent(from)}` : url);
    })
    .catch((e) => {
      EleMessage.error(e.message);
    });
}

/**
 * 下载文件
 * @param data 二进制数据
 * @param name 文件名
 * @param type 文件类型
 */
export function download(data, name, type) {
  const blob = new Blob([data], { type: type || 'application/octet-stream' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = name;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * 参数转url字符串
 * @param params 参数
 * @param url 需要拼接参数的地址
 */
export function toURLSearch(params, url) {
  if (typeof params !== 'object' || params == null) {
    return '';
  }
  const result = transformParams(params)
    .map((d) => `${encodeURIComponent(d[0])}=${encodeURIComponent(d[1])}`)
    .join('&');
  if (!url) {
    return result;
  }
  return (url.includes('?') ? `${url}&` : `${url}?`) + result;
}

/**
 * 判断是否为 Blob 或 File
 * @param obj
 */
function isBlobFile(obj) {
  return obj instanceof Blob || obj instanceof File;
}

/**
 * 通用 FormData 构造器
 * @param params 要转换的对象
 * @param options 选项：
 *  - fileArrayKeys: 特殊字段（如 ['userInfo.photos']），这些 key 是上传文件数组
 *  - useBracketStyle: 是否使用 a[b][c] 形式（默认 true），否则使用 a.b.c
 * @returns {FormData}
 */
export function toFormDataPlus(params, options = {}) {
  const { fileArrayKeys = [], useBracketStyle = true } = options;

  const formData = new FormData();

  const buildKey = (prefix, key) => {
    return useBracketStyle
      ? prefix
        ? `${prefix}[${key}]`
        : key
      : prefix
        ? `${prefix}.${key}`
        : key;
  };

  const appendFormData = (fd, keyPrefix, value) => {
    if (value === null || value === undefined || value === '') return;

    const isMatchFileArray = fileArrayKeys.includes(keyPrefix);

    // 特殊字段是文件数组
    if (isMatchFileArray && Array.isArray(value)) {
      value.forEach((item) => {
        if (
          item &&
          typeof item === 'object' &&
          'file' in item &&
          item.status !== 'done' &&
          isBlobFile(item.file)
        ) {
          fd.append(keyPrefix, item.file, item.name || item.file.name);
        }
      });
      return;
    }

    // 普通数组
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        appendFormData(fd, buildKey(keyPrefix, index), item);
      });
      return;
    }

    // 日期
    if (value instanceof Date) {
      fd.append(keyPrefix, value.toISOString());
      return;
    }

    // 嵌套对象（非文件）
    if (typeof value === 'object' && !isBlobFile(value)) {
      Object.keys(value).forEach((subKey) => {
        const newKey = buildKey(keyPrefix, subKey);
        appendFormData(fd, newKey, value[subKey]);
      });
      return;
    }

    // 原始类型或单个文件
    fd.append(keyPrefix, value);
  };

  Object.keys(params || {}).forEach((key) => {
    appendFormData(formData, key, params[key]);
  });

  return formData;
}

/**
 * 将参数转换为 FormData
 * @param params 参数对象
 * @param fileArrayKeys 特殊字段（如 ['zzzs', 'userInfo.photo']），这些 key 下是上传文件数组，每项都 append
 */
export function toFormData(params, fileArrayKeys) {
  const formData = new FormData();

  /**
   * 递归处理参数
   */
  const appendFormData = (formData, keyPrefix, value) => {
    if (value == null || value === '') return;

    // 特殊处理的 key，无嵌套层级（如 zzzs, userInfo.photo）
    if (fileArrayKeys.includes(keyPrefix) && Array.isArray(value)) {
      value.forEach((item) => {
        if (
          item &&
          typeof item === 'object' &&
          'file' in item &&
          item.status !== 'done' &&
          isBlobFile(item.file)
        ) {
          formData.append(keyPrefix, item.file, item.name || item.file.name);
        }
      });
      return;
    }

    // 普通数组（非文件字段）
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        appendFormData(formData, `${keyPrefix}[${index}]`, item);
      });
      return;
    }

    // 嵌套对象
    if (typeof value === 'object' && !isBlobFile(value)) {
      Object.keys(value).forEach((subKey) => {
        const newKey = `${keyPrefix}.${subKey}`;
        appendFormData(formData, newKey, value[subKey]);
      });
      return;
    }

    // 原始值或单个 Blob/File
    formData.append(keyPrefix, value);
  };

  Object.keys(params || {}).forEach((key) => {
    appendFormData(formData, key, params[key]);
  });

  return formData;
}

/**
 *  FormData 构造器
 * 递归将对象转换为FormData，用于文件上传等场景
 * @param {Object} obj - 要转换的源对象
 * @param {FormData} form - 可选的现有FormData对象
 * @param {String} namespace - 当前属性的命名空间（用于递归调用）
 * @returns {FormData} 生成的FormData对象
 */
// export function toFormDataWj(obj, form, namespace) {
//   // 如果没有传入FormData，则新建一个
//   const fd = form || new FormData();
//
//   // 遍历对象的所有属性
//   for (const property in obj) {
//     // 跳过原型链上的属性
//     if (!Object.prototype.hasOwnProperty.call(obj, property)) continue;
//
//     const value = obj[property];
//     // 跳过空值
//     if (value === null || value === undefined || value === '') continue;
//
//     // 构建当前属性的完整键名（考虑命名空间）
//     const formKey = namespace ? `${namespace}[${property}]` : property;
//
//     // 处理日期类型
//     if (value instanceof Date) {
//       fd.append(formKey, value.toISOString());
//     }
//     // 处理数组类型
//     else if (Array.isArray(value)) {
//       value.forEach((item, index) => {
//         // 构建数组元素的键名（从0开始的下标）
//         const itemKey = `${formKey}[${index}]`;
//
//         // 处理对象类型的数组元素
//         if (item && typeof item === 'object') {
//           // 跳过已上传完成的文件（status === 'done'）
//           if (item.status === 'done') {
//             return;
//           }
//
//           // 处理新上传的文件对象（包含file属性）
//           if ('file' in item && item.file instanceof File) {
//             // 添加文件内容（使用实际文件名）
//             fd.append(`${itemKey}[file]`, item.file,
//               item.name || item.file.name);
//           } else {
//             // 递归处理其他类型的对象
//             toFormDataWj(item, fd, itemKey);
//           }
//         }
//         // 处理基本类型的数组元素
//         else if (item !== null && item !== undefined && item !== '') {
//           fd.append(itemKey, item);
//         }
//       });
//     }
//     // 处理普通对象类型（非File对象）
//     else if (typeof value === 'object' && !(value instanceof File)) {
//       toFormDataWj(value, fd, formKey);
//     }
//     // 处理基本类型值
//     else {
//       fd.append(formKey, value);
//     }
//   }
//
//   return fd;
// }
/**
 * 递归将对象转换为FormData，用于文件上传等场景
 * @param {Object} obj - 要转换的源对象
 * @param {FormData} form - 可选的现有FormData对象
 * @param {String} namespace - 当前属性的命名空间（用于递归调用）
 * @returns {FormData} 生成的FormData对象
 */
export function toFormDataWj(obj, form, namespace) {
  // 如果没有传入FormData，则新建一个
  const fd = form || new FormData();

  // 遍历对象的所有属性
  for (const property in obj) {
    // 跳过原型链上的属性
    if (!Object.prototype.hasOwnProperty.call(obj, property)) continue;

    const value = obj[property];
    // 跳过空值
    if (value === null || value === undefined || value === '') continue;

    // 构建当前属性的完整键名（考虑命名空间）
    const formKey = namespace ? `${namespace}[${property}]` : property;

    // 处理日期类型
    if (value instanceof Date) {
      fd.append(formKey, value.toISOString());
    }
    // 处理数组类型
    else if (Array.isArray(value)) {
      // 判断是否是对象数组（包含文件对象）
      const isObjectArray = value.some(
        // (item) => item && typeof item === 'object' && 'file' in item
        (item) => item && typeof item === 'object'
      );
      if (isObjectArray) {
        // 对象数组处理逻辑
        value.forEach((item, index) => {
          const itemKey = `${formKey}[${index}]`;
          if (item && typeof item === 'object') {
            // 跳过已上传完成的文件
            if (item.status === 'done') {
              return;
            }

            // 处理新上传的文件
            if ('file' in item && item.file instanceof File) {
              fd.append(
                `${itemKey}[file]`,
                item.file,
                item.name || item.file.name
              );

              // 可选：添加文件元数据
              // if (item.name) fd.append(`${itemKey}[name]`, item.name);
              // if (item.key) fd.append(`${itemKey}[key]`, item.key);
            } else {
              // 递归处理其他对象
              toFormDataWj(item, fd, itemKey);
            }
          }
        });
      } else {
        // 非对象数组处理：拼接为逗号分隔的字符串
        const stringValue = value
          .filter((item) => item !== null && item !== undefined && item !== '')
          .join(',');

        if (stringValue) {
          fd.append(formKey, stringValue);
        }
      }
    }
    // 处理普通对象类型（非File对象）
    else if (typeof value === 'object' && !(value instanceof File)) {
      toFormDataWj(value, fd, formKey);
    }
    // 处理基本类型值
    else {
      fd.append(formKey, value);
    }
  }
  return fd;
}
/**
 * get请求处理数组和对象类型参数
 * @param params 参数
 */
export function transformParams(params) {
  const result = [];
  if (params != null && typeof params === 'object') {
    Object.keys(params).forEach((key) => {
      const value = params[key];
      if (value != null && value !== '') {
        //表单多附件上传处理
        if (Array.isArray(value) && value.length) {
          // && isBlobFile(value[0].file)
          if (typeof value[0] === 'object') {
            value.forEach((v) => {
              if (v.status !== 'done') result.push([key, v.file]);
            });
          } else {
            result.push([key, value.join(',')]);
          }
        } else if (typeof value === 'object' && !isBlobFile(value)) {
          getObjectParamsArray(value).forEach((item) => {
            result.push([`${key}${item[0]}`, item[1]]);
          });
        } else {
          result.push([key, value]);
        }
      }
    });
  }
  return result;
}

/**
 * 对象转参数数组
 * @param obj 对象
 */
export function getObjectParamsArray(obj) {
  const result = [];
  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    if (value != null && value !== '') {
      const name = `[${key}]`;
      if (typeof value === 'object' && !isBlobFile(value)) {
        getObjectParamsArray(value).forEach((item) => {
          result.push([`${name}${item[0]}`, item[1]]);
        });
      } else {
        result.push([name, value]);
      }
    }
  });
  return result;
}

/**
 * 切换主题过渡动画
 * @param callback 执行的方法
 * @param el 过渡动画触发元素
 * @param isOut 是否是退出方向
 * @param isBody 是否在 body 上执行动画
 */
export function doWithTransition(callback, el, isOut, isBody) {
  if (!el || typeof document.startViewTransition !== 'function') {
    callback().then(() => {});
    return;
  }
  document.documentElement.classList.add('disabled-transition');
  el.classList.add('view-transition-trigger');
  el.style.setProperty('view-transition-name', 'view-transition-trigger');
  if (isBody) {
    document.body.style.setProperty('view-transition-name', 'body');
  }
  const rect = el.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, innerWidth - x),
    Math.max(y, innerHeight - y)
  );
  document.startViewTransition(callback).ready.then(() => {
    const clipPath = [
      `circle(0px at ${x}px ${y}px)`,
      `circle(${endRadius}px at ${x}px ${y}px)`
    ];
    const anim = document.documentElement.animate(
      { clipPath: isOut ? [...clipPath].reverse() : clipPath },
      {
        duration: 400,
        easing: 'ease-in',
        pseudoElement: isOut
          ? `::view-transition-old(${isBody ? 'body' : 'root'})`
          : `::view-transition-new(${isBody ? 'body' : 'root'})`
      }
    );
    anim.onfinish = () => {
      document.body.style.removeProperty('view-transition-name');
      el.style.removeProperty('view-transition-name');
      el.classList.remove('view-transition-trigger');
      document.documentElement.classList.remove('disabled-transition');
    };
  });
}

/**
 * 将两个多维数组合并，并使用Set对象去除重复的子数组
 * @param arr1
 * @param arr2
 * @returns {*[]}
 */
export function mergeUnique(arr1, arr2, keyword) {
  const array = [...arr1, ...arr2];
  const map = new Map();
  return array.filter((item) => {
    const k = item[keyword];
    // 如果map中不存在当前关键字，则保留该对象，并将其添加到map中
    const exists = map.has(k);
    map.set(k, true);
    return !exists;
  });
}

export function mergeArraysWithoutDuplicates(arrays, elementToMerge) {
  const mergedArray = [];
  const seenElements = new Set();

  for (const array of arrays) {
    for (const item of array) {
      if (item[elementToMerge] && !seenElements.has(item[elementToMerge])) {
        seenElements.add(item[elementToMerge]);
        mergedArray.push(item);
      }
    }
  }
  return mergedArray;
  // 示例使用
  //   const arrays = [[{id: 1, name: 'Alice'}, {id: 2, name: 'Bob'}], [{id: 2, name: 'Bob'}, {id: 3, name: 'Charlie'}]]
  //   let newArray = []
  //   if (arrays.length > 0) newArray = mergeArraysWithoutDuplicates(arrays, 'id');
  //   console.log(newArray); // [{id: 1, name: 'Alice'}, {id: 2, name: 'Bob'}, {id: 3, name: 'Charlie'}]
}

/**使用 Set 对象去除重复项：*/
export function removeDuplicates(arr1, arr2) {
  const set1 = new Set(arr1);
  const set2 = new Set(arr2);
  const difference = new Set([...set1].filter((x) => !set2.has(x)));
  return [...difference];
}

/**
 * 两个数组取相同数据，返回name
 * @param arr1
 * @param arr2
 * @param type 取值字段名
 * @returns {string}
 */
export function transformDicDataName(arr1, arr2, type) {
  let arraySome = [];
  let newStr = '';
  if (arr1?.length > 0 && arr2.length > 0) {
    arraySome = arr1.filter((item) =>
      arr2.some((ele) => {
        if (type) {
          return ele[type] === item.dictDataCode;
        } else {
          return ele === item.dictDataCode;
        }
      })
    );
    let arraySomeMap = arraySome.map((item) => {
      return item.dictDataName;
    });
    newStr = arraySomeMap.join();
  }
  return newStr;
}

/**
 * 从arr2中删除在arr1中存在的数据
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function removeSomeData(arr1, arr2, type) {
  return arr2.filter(
    (item1) => !arr1.some((item2) => item1[type] === item2[type])
  );
}

/**
 * 两个数组取相同数据，返回code
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function transformDicDataCode(arr1, arr2, type) {
  let arraySome = [];
  let arraySomeMap = [];
  if (arr2.length > 0) {
    arraySome = arr1.filter((item) =>
      arr2.some((ele) => {
        if (type) {
          return ele[type] === item.dictDataCode;
        } else {
          return ele === item.dictDataCode;
        }
      })
    );
    arraySomeMap = arraySome.map((item) => {
      return item.dictDataCode;
    });
  }
  return arraySomeMap;
}

/**
 * 向数组指定下标添加元素
 * @param array
 * @param element
 * @param index
 * @returns {*}
 */
export function insertAtIndex(array, element, index) {
  return array.splice(index, 0, element);
}

/**
 * 生成随机字符串
 * 使用Math.random()函数生成随机数，并将其转换为字符串。然后使用slice()方法截取需要的长度。
 * @param length
 * @returns {string}
 */
export function generateRandomString(length) {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/** 判断是否是图片文件 */
export function isImageFile(item) {
  return !!(
    typeof item.contentType === 'string' &&
    item.contentType.startsWith('image/')
  );
}

/** js数组通过某个字段进行分组 */
export function groupArr(list, fieldMark, fieldId) {
  let field = fieldMark;
  //心理咨询师分组特殊处理
  if (fieldMark === 'xlzxEvent') field = 'xm';
  let fieldList = [],
    att = [];
  list.map((e) => {
    fieldList.push(e[field]);
  });
  //数组去重
  fieldList = fieldList.filter((e, i, self) => {
    return self.indexOf(e) === i;
  });
  for (let j = 0; j < fieldList.length; j++) {
    //过滤出匹配到的数据
    let arr = list.filter((e) => {
      return e[field] === fieldList[j];
    });
    //心理咨询师分组特殊处理
    const totalCapacity =
      fieldMark === 'xlzxEvent'
        ? arr.reduce((sum, item) => sum + (item.kyyrs || 0), 0)
        : '';
    att.push({
      // type: arr[0].sttype,
      type: arr[0][field],
      fieldId: fieldId ? arr[0][fieldId] : '',
      listLength: arr.length,
      list: arr,
      totalCapacity: totalCapacity
    });
  }
  return att;
}

/** sort方法根据数组中对象的某一个属性值进行排序 */
export function compare(property) {
  return function (a, b) {
    const value1 = a[property];
    const value2 = b[property];
    return value1 - value2;
  };
}

/**
 * 遍历一个树形结构并获取所有子节点
 * @param node childNode为Object对象
 * @returns {*[]}
 */
// export function traverseTree(node,) {
//     let result = [];
//     (function recurse(currentNode) {
//         if (currentNode) {
//             console.log(currentNode.type)
//             // 然后递归遍历这个子节点
//             // 如果children是单个对象，加入结果数组
//             if (currentNode.childNode) {
//                 let newNodeData = {
//                     ...currentNode.nodeUserList,
//                     id: currentNode.id,
//                     nodeName: currentNode.nodeName,
//                     settype: currentNode.settype,
//                     type: currentNode.type,
//                     error: currentNode.error,
//                     childNode: currentNode.childNode,
//                 }
//                 result.push(newNodeData);
//                 recurse(currentNode.childNode);
//                 // } else {
//                 //     result.splice(-1, 1); // 删除最后一个内嵌数组
//             }
//         }
//     })(node); // 调用时立即执行函数
//     return result;
// }
export function traverseTree(node) {
  let result = [];
  (function recurse(currentNode) {
    if (currentNode) {
      // 然后递归遍历这个子节点
      // 如果children是单个对象，加入结果数组
      if (currentNode.childNode) {
        let newNodeData = {
          ...currentNode.nodeUserList,
          id: currentNode.id,
          nodeName: currentNode.nodeName,
          settype: currentNode.settype,
          type: currentNode.type,
          error: currentNode.error
          // childNode: currentNode.childNode,
        };
        result.push(newNodeData);
        recurse(currentNode.childNode);
        // } else {
        //     result.splice(-1, 1); // 删除最后一个内嵌数组
      }
    }
  })(node); // 调用时立即执行函数
  return result;
}

/**
 * 通用排序函数：先按日期，再按时间段（小时/分钟）排序
 * @param {Array} list - 原始数组
 * @param {Object} options - 配置项
 * @param {string} options.dateField - 日期字段名，默认 'gzrq'
 * @param {string} options.startTimeField - 开始时间字段名，默认 'kssjStr'
 * @param {string} options.endTimeField - 结束时间字段名，默认 'jssjStr'
 * @returns {Array} 已排序的新数组
 */
export function sortByDateAndTime(
  list,
  {
    dateField = 'gzrq',
    startTimeField = 'kssjStr',
    endTimeField = 'jssjStr'
  } = {}
) {
  if (!Array.isArray(list)) {
    console.log('sortByDateAndTime expects an array, got:', list);
    return [];
  }

  const parseTimeToMinutes = (timeStr) => {
    if (!timeStr) return 0;
    const [h, m] = timeStr.split(':').map(Number);
    return h * 60 + m;
  };

  return list.slice().sort((a, b) => {
    const dateCompare = new Date(a[dateField]) - new Date(b[dateField]);
    if (dateCompare !== 0) return dateCompare;

    const aStart = parseTimeToMinutes(a[startTimeField]);
    const bStart = parseTimeToMinutes(b[startTimeField]);
    if (aStart !== bStart) return aStart - bStart;

    const aEnd = parseTimeToMinutes(a[endTimeField]);
    const bEnd = parseTimeToMinutes(b[endTimeField]);
    return aEnd - bEnd;
  });
}

/**
 * 递归数组，子节点childNode
 * @param arr
 * @param n
 * @returns {*}
 */
export function recursiveArrayFunction(arr, n) {
  if (n === arr.length - 1) {
    return arr[n];
  }
  arr[n].childNode = recursiveArrayFunction(arr, n + 1);
  return arr[n];
}
