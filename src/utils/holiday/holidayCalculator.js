import dayjs from 'dayjs';
import {solarToLunar, lunarToSolar, getQingmingDate, getSolarTerms} from './lunar'; // 你的 lunar.js 工具

/**
 * 获取全年节气（不标记为法定假期，仅作为节令参考）
 */
function generateSolarTerms(year) {
  const solarTerms = getSolarTerms(year); // [{ date: 'YYYY-MM-DD', name: '立春' }, ...]
  return solarTerms.map(term => ({
    date: term.date,
    name: term.name,
    type: 'solar-term',
    isHoliday: false,
  }));
}

/**
 * 查找某年农历正月初一（春节）对应的阳历日期
 */
function findLunarNewYearDate(year) {
  const solarStr = lunarToSolar(year, 1, 1);
  const date = dayjs(solarStr);
  if (!date.isValid()) {
    throw new Error(`无效的农历转换: ${solarStr}`);
  }
  return date;
}

/**
 * 获取清明节（节气）
 * 每年清明在 4 月 4 日或 5 日
 */
function generateQingmingHoliday(year) {
  const qingmingDate = getQingmingDate(year); // 应返回 YYYY-MM-DD 格式
  return [{
    date: qingmingDate,
    name: '清明节',
    type: 'holiday',
    isHoliday: true
  }];
}

/**
 * 获取农历节日假期
 * @param {string} name 节日名
 * @param {string} lunarMonthDay 农历日期，如 "05-05"
 */
function generateLunarFestivalHoliday(year, name, lunarMonthDay) {
  const [month, day] = lunarMonthDay.split('-').map(Number);

  const solarDate = lunarToSolar(year, month, day); // 返回 YYYY-MM-DD 格式
  return [{
    date: solarDate,
    name,
    type: 'holiday',
    isHoliday: true
  }];
}

/**
 * 获取春节假期（除夕 + 初一至初六）
 */
export function generateSpringFestivalHolidays(year) {
  const lunarNewYear = findLunarNewYearDate(year);
  const start = lunarNewYear.subtract(1, 'day'); // 除夕
  const end = lunarNewYear.add(6, 'day'); // 初六

  const holidays = [];
  let current = start;
  while (current.isSameOrBefore(end)) {
    holidays.push({
      date: current.format('YYYY-MM-DD'),
      name: '春节',
      type: 'holiday',
      isHoliday: true,
    });
    current = current.add(1, 'day');
  }
  return holidays;
}

/**
 * 获取其他固定公历节日（如元旦、劳动节、国庆节等）
 */
export function generateFixedSolarHolidays(year) {
  const list = [
    {date: `${year}-01-01`, name: '元旦'},
    {date: `${year}-05-01`, name: '劳动节'},
    {date: `${year}-10-01`, name: '国庆节'},
  ];
  return list.map(item => ({
    ...item,
    type: 'holiday',
    isHoliday: true,
  }));
}

/**
 * 汇总某一年的所有节假日（固定 + 春节）
 */
export function generateAllHolidays(year) {
  const holidays = [];
  // 春节（除夕 + 初一~初六）
  holidays.push(...generateSpringFestivalHolidays(year));
  // 清明节（节气）
  holidays.push(...generateQingmingHoliday(year));
  // 农历节日
  holidays.push(...generateLunarFestivalHoliday(year, '端午节', '05-05'));
  holidays.push(...generateLunarFestivalHoliday(year, '中秋节', '08-15'));
  holidays.push(...generateLunarFestivalHoliday(year, '元宵节', '01-15'));
  holidays.push(...generateLunarFestivalHoliday(year, '重阳节', '09-09'));
  // 固定公历节日
  holidays.push(...generateFixedSolarHolidays(year));
  // 所有节气（非假）
  holidays.push(...generateSolarTerms(year));
  return holidays;
}
