// utils/lunar.js
import dayjs from "dayjs";

/**
 * 农历1900-2100年信息表
 * 数据格式：[0,2,9,21936]
 * [是否闰月, 正月初一对应公历月份, 正月初一对应公历日期, 农历信息]
 */
const lunarInfo = [
  0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2, //1900-1909
  0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977, //1910-1919
  0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970, //1920-1929
  0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950, //1930-1939
  0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557, //1940-1949
  0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5d0, 0x14573, 0x052d0, 0x0a9a8, 0x0e950, 0x06aa0, //1950-1959
  0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0, //1960-1969
  0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b5a0, 0x195a6, //1970-1979
  0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570, //1980-1989
  0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0, //1990-1999
  0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5, //2000-2009
  0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930, //2010-2019
  0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530, //2020-2029
  0x05aa0, 0x076a3, 0x096d0, 0x04bd7, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45, //2030-2039
  0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0, //2040-2049
  0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0, //2050-2059
  0x092e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4, //2060-2069
  0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0, //2070-2079
  0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160, //2080-2089
  0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252, //2090-2099
  0x0d520                                                                                   //2100
];

// 二十四节气
const solarTerm = [
  "小寒", "大寒", "立春", "雨水", "惊蛰", "春分", "清明", "谷雨", "立夏", "小满", "芒种", "夏至",
  "小暑", "大暑", "立秋", "处暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至"
];

// 节气对应的黄经度数
const termInfo = [0, 21208, 42467, 63836, 85337, 107014, 128867, 150921, 173149, 195551, 218072, 240693, 263343, 285989, 308563, 331033, 353350, 375494, 397447, 419210, 440795, 462224, 483532, 504758];

// 农历月份名称
const monthNames = ["正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"];

// 农历日名称
const dayNames = [
  "初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
  "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
  "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十"
];

// 公历节日
const solarFestivals = {
  '01-01': '元旦',
  '02-14': '情人节',
  '03-08': '妇女节',
  '03-12': '植树节',
  '04-01': '愚人节',
  '05-01': '劳动节',
  '05-04': '青年节',
  '06-01': '儿童节',
  '07-01': '建党节',
  '08-01': '建军节',
  '09-10': '教师节',
  '10-01': '国庆节',
  '12-24': '平安夜',
  '12-25': '圣诞节'
};

// 农历节日
const lunarFestivals = {
  '01-01': '春节',
  '01-15': '元宵节',
  '02-02': '龙抬头',
  '05-05': '端午节',
  '07-07': '七夕节',
  '07-15': '中元节',
  '08-15': '中秋节',
  '09-09': '重阳节',
  '12-08': '腊八节',
  '12-23': '小年',
  '12-30': '除夕'
};

/**
 * 计算农历年份的信息
 */
function lunarYearDays(y) {
  let sum = 348;
  for (let i = 0x8000; i > 0x8; i >>= 1) {
    sum += (lunarInfo[y - 1900] & i) ? 1 : 0;
  }
  return sum + leapDays(y);
}

/**
 * 计算农历闰月天数
 */
function leapDays(y) {
  if (leapMonth(y)) {
    return (lunarInfo[y - 1900] & 0x10000) ? 30 : 29;
  }
  return 0;
}

/**
 * 计算农历闰月月份
 */
function leapMonth(y) {
  return lunarInfo[y - 1900] & 0xf;
}

/**
 * 计算农历月份天数
 */
function monthDays(y, m) {
  return (lunarInfo[y - 1900] & (0x10000 >> m)) ? 30 : 29;
}

/**
 * 公历转农历
 */
export function solarToLunar(date) {
  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  if (isNaN(date.getTime())) {
    throw new Error('无效的日期参数');
  }

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  // 计算与1900-01-31的时间差
  let offset = 0;
  for (let i = 1900; i < year; i++) {
    offset += isLeapYear(i) ? 366 : 365;
  }
  for (let i = 1; i < month; i++) {
    offset += getSolarMonthDays(year, i);
  }
  offset += day - 1;

  // 计算农历年份
  let lunarYear = 1900;
  let days = lunarYearDays(lunarYear);
  while (offset >= days) {
    offset -= days;
    lunarYear++;
    days = lunarYearDays(lunarYear);
  }

  // 计算农历月份
  let lunarMonth = 1;
  let leap = leapMonth(lunarYear);
  let isLeap = false;
  days = monthDays(lunarYear, lunarMonth);

  while (offset >= days) {
    offset -= days;
    lunarMonth++;
    if (lunarMonth === leap && !isLeap) {
      isLeap = true;
      days = leapDays(lunarYear);
    } else {
      days = monthDays(lunarYear, lunarMonth);
    }
  }

  // 计算农历日
  let lunarDay = offset + 1;

  // 获取节气
  const term = getSolarTerms(date);

  // 获取节日
  const lunarDateStr = `${lunarMonth.toString().padStart(2, '0')}-${lunarDay.toString().padStart(2, '0')}`;
  const solarDateStr = `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

  let festival = solarFestivals[solarDateStr] || lunarFestivals[lunarDateStr];

  return {
    lunarYear,
    lunarMonth,
    lunarDay,
    isLeap,
    monthName: monthNames[lunarMonth - 1] + (isLeap ? '闰' : '') + '月',
    dayName: dayNames[lunarDay - 1],
    term,
    festival
  };
}

/**
 * 获取某年清明节的公历日期（基于简化太阳黄经算法，足够用于节日计算）
 * 清明节大约在每年4月4日至5日
 * @param {number} year
 * @returns {string} YYYY-MM-DD
 */
export function getQingmingDate(year) {
  // 清明节日期近似公式（每年4月的第n天）
  // 通常为4月4日或5日
  // 公式出自《万年历算法》：Y × D + C − L
  const centuryValues = {
    1900: 5.59, // 1900-1999
    2000: 4.81  // 2000-2099
  };

  const C = year >= 2000 ? centuryValues[2000] : centuryValues[1900];
  const Y = year % 100;
  const D = 0.2422;
  const L = Math.floor(Y / 4); // 闰年修正

  const day = Math.floor(Y * D + C) - L;
  const date = dayjs(`${year}-04-${day.toString().padStart(2, '0')}`);

  return date.format('YYYY-MM-DD');
}

/**
 * 获取节气 节气计算核心函数
 */
export function getSolarTerms(year) {
  const baseDate = new Date(Date.UTC(1900, 0, 6, 2, 5)); // 1900-01-06 02:05 UTC+0
  const terms = [];

  for (let i = 0; i < 24; i++) {
    const minutesOffset = termInfo[i] + 525948.76 * (year - 1900);
    const termDate = new Date(baseDate.getTime() + minutesOffset * 60000);
    // 转为北京时间
    const beijingDate = new Date(termDate.getTime() + 8 * 60 * 60 * 1000);
    const dateStr = beijingDate.toISOString().split('T')[0];

    terms.push({
      name: solarTerm[i],
      date: dateStr
    });
  }

  return terms;
}

/**
 * 判断是否是闰年
 */
function isLeapYear(year) {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

/**
 * 获取公历月份天数
 */
function getSolarMonthDays(year, month) {
  const monthDays = [31, isLeapYear(year) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  return monthDays[month - 1];
}

/**
 * 农历转公历（返回 YYYY-MM-DD 格式）
 * @param {number} year - 农历年
 * @param {number} month - 农历月（1-12）
 * @param {number} day - 农历日（1-30）
 * @param {boolean} [isLeapMonth=false] - 是否闰月
 * @returns {string} 公历日期（YYYY-MM-DD）
 */
export function lunarToSolar(lunarYear, lunarMonth, lunarDay, isLeapMonth = false) {
  let offset = 0;

  for (let i = 1900; i < lunarYear; i++) {
    offset += lunarYearDays(i);
  }

  let leap = leapMonth(lunarYear);
  for (let i = 1; i < lunarMonth; i++) {
    offset += monthDays(lunarYear, i);
    if (i === leap) offset += leapDays(lunarYear); // 插入闰月
  }

  if (leap && lunarMonth === leap + 1 && isLeapMonth) {
    offset += monthDays(lunarYear, lunarMonth - 1); // 之前已经加过一次非闰月
  }

  offset += lunarDay - 1;

  const baseDate = new Date(1900, 0, 31);
  const targetDate = new Date(baseDate.getTime() + offset * 86400000);

  return dayjs(targetDate).format('YYYY-MM-DD');
}

