import dayjs from 'dayjs';
import {solarToLunar} from './lunar';

/**
 * 固定的公历节日
 */
const fixedHolidays = [
  {month: 1, day: 1, name: '元旦'},
  {month: 4, day: 4, name: '清明节'},
  {month: 5, day: 1, name: '劳动节'},
  {month: 10, day: 1, name: '国庆节'},
];

/**
 * 获取某年的春节假期（除夕+初一至初六）
 */
function generateSpringFestivalHolidays(year) {
  const lunarNewYear = solarToLunar(new Date(`${year}-01-01`));
  // const lunarNewYear = solarToLunar(`${year}-01-01`).nextMonthFirstDay;
  const start = dayjs(lunarNewYear).subtract(1, 'day'); // 除夕
  const end = start.add(6, 'day'); // 初六
  const holidays = [];

  let current = start.clone();
  while (current.isSameOrBefore(end)) {
    holidays.push({
      date: current.format('YYYY-MM-DD'),
      name: '春节',
      type: 'holiday',
      isHoliday: true
    });
    current = current.add(1, 'day');
  }
  return holidays;
}

/**
 * 补班日生成
 */
function generateWorkdays(workdayDates, year) {
  return workdayDates
    .map(date => dayjs(`${year}-${date}`))
    .filter(d => d.isValid())
    .map(d => ({
      date: d.format('YYYY-MM-DD'),
      name: '调休上班',
      type: 'work',
      isHoliday: true
    }));
}

/**
 * 生成节假日数据
 * @param {number} year - 年份
 * @param {string[]} workdays - 补班日期，格式如 ["02-18", "04-07"]
 * @returns {Array<{date: string, name: string, type: "holiday" | "work"}>}
 */
export function generateHolidays(year, workdays = []) {
  const holidays = [];

  holidays.push(...generateSpringFestivalHolidays(year));

  // 固定节日
  for (const {month, day, name} of fixedHolidays) {
    const date = dayjs(`${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`);
    holidays.push({
      date: date.format('YYYY-MM-DD'),
      name,
      type: 'holiday',
      isHoliday: true
    });
  }

  holidays.push(...generateWorkdays(workdays, year));

  console.log(year,workdays,holidays)
  return holidays;
}
