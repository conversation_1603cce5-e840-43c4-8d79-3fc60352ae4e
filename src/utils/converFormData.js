export async function convertToFormData(data, deletedFileMap = {}) {
  const formData = new FormData();

  for (const [key, value] of Object.entries(data)) {
    // 仅跳过 undefined（保留 null）
    if (value === undefined) continue;

    // 处理文件数组（特殊格式识别）
    if (key === 'file_icon' && Array.isArray(value)) {
      await handleFileIcon(formData, value);
      continue;
    }

    // 处理数组类型
    if (Array.isArray(value)) {
      await handleArrayField(formData, key, value);
      continue;
    }

    // 处理文件对象（增强识别）
    if (isFileValue(value)) {
      formData.append(key, getFileFromValue(value));
      continue;
    }

    // 处理其他类型
    appendBasicValue(formData, key, value);
  }

  handleDeletedFiles(formData, deletedFileMap);
  return formData;
}

// 新增辅助方法
function isFileValue(value) {
  return (
    value instanceof File ||
    (value?.file instanceof File) ||
    (value?.url && value?.status === 'done') // 识别已上传文件
  );
}

function getFileFromValue(value) {
  if (value instanceof File) return value;
  if (value?.file instanceof File) return value.file;
  return null; // 已上传文件不需要再次上传
}

async function handleFileIcon(formData, files) {
  for (const [index, file] of files.entries()) {
    if (file.url) {
      // 已上传文件，只需传ID
      formData.append(`file_icon[${index}][id]`, file.id || '');
    } else if (isFileValue(file)) {
      // 新上传文件
      formData.append(`file_icon[${index}]`, getFileFromValue(file));
    }
  }
}

function appendBasicValue(formData, key, value) {
  if (value === null) {
    formData.append(key, ''); // 显式处理null
  } else if (typeof value === 'object') {
    formData.append(key, JSON.stringify(value));
  } else {
    formData.append(key, String(value));
  }
}

/**
 * 处理数组字段
 */
async function handleArrayField(formData, key, array) {
  // 检查是否是文件数组
  const isFileArray = array.some(
    (item) =>
      item instanceof File ||
      (item && typeof item === "object" && item.file instanceof File),
  );

  if (isFileArray) {
    await appendFileArray(formData, key, array);
  } else {
    // 普通数组处理
    const hasObjects = array.some(
      (item) => typeof item === "object" && item !== null,
    );

    if (hasObjects) {
      // 如果数组包含对象，转换为 JSON 字符串
      formData.append(key, JSON.stringify(array));
    } else {
      // 简单数组转换为逗号分隔字符串
      formData.append(key, array.join(","));
    }
  }
}

/**
 * 处理删除的文件
 */
function handleDeletedFiles(formData, deletedFileMap) {
  const deleteFileIds = {};

  for (const [field, idSet] of Object.entries(deletedFileMap || {})) {
    const ids = Array.isArray(idSet)
      ? idSet
      : idSet instanceof Set
        ? [...idSet]
        : [];

    if (ids.length > 0) {
      deleteFileIds[field] = ids.join(",");
    }
  }

  if (Object.keys(deleteFileIds).length > 0) {
    formData.append("deleteFileIds", JSON.stringify(deleteFileIds));
  }
}
