import {computed, nextTick,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user';
import {listRoles} from "@/views/system/role/api";
import {getCodeDwb} from "@/views/base-code/dwb/api";
import {getCodeZyb} from "@/views/base-code/zyb/api";
import {getCodeBjb} from "@/views/base-code/bjb/api";
import {getDicFieldValueByUrl, getDictionaryFieldByGroupId} from "@/views/system/sphfw/dictionary-field/api";
import {getControlTypeData} from "@/utils/control-type-util";
import {getCodeType} from "@/views/base-code/dictionary/api";
import {getDictionaryGroup} from "@/views/system/sphfw/dictionary-group/api";
import {getListGroupConfig} from "@/views/system/sphfw/list-group/api";
import {getCodeData} from "@/views/base-code/dictionary/api/data-index";

// let result=[];
/**
 * 获取字典数据hook
 * @param codes 字典编码
 */
export function useDictData(code, dicQueryParams) {
  let groupId = dicQueryParams?.groupId;
  let dictionaryFieldUrl = dicQueryParams?.dictFieldUrl;
  let userType = dicQueryParams?.userType;
  let params = dicQueryParams?.params;
  let result = [];
  // const codeData = [];
  // 已缓存的字典
  const userStore = useUserStore();
  // const {dicts} = storeToRefs(userStore);
  // console.log(dicts)
  // codes.forEach((code) => {
  // let codeData = [];
  // result.push(computed(() => dicts.value[code] || []));
  // console.log(dicts.value[code])
  // data.value = result;
  // // 若还未缓存过则获取字典数据
  // if (dicts.value[code] != null) {
  //     return;
  // }
  // console.log(code, dicQueryParams)
  // console.log(dicts.value)
  // userStore.setDicts([], code);
  if (code === 'listRoles') {//角色下拉
    /** 获取角色数据 */
    listRoles().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.name}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'xymc') {//院系
    getCodeDwb(params).then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.name}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'zymc') {//专业
    getCodeZyb().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.name}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'bjmc') {//bjmc
    getCodeBjb().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.name}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'controlType') {//获取控件类型
    getControlTypeData().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.value, 'dictDataName': resData.label}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'listDicCode') {//加载数据
    getCodeType().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.code, 'dictDataName': resData.name}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        result.push({'dictDataCode': 'other', 'dictDataName': '其他'})
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'groupType') {//信息字段，所属字段组
    let obj = {}
    if (userType) obj = {
      userType: userType
    }
    getDictionaryGroup(obj).then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.groupName, ...resData}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else if (code === 'listGroup') {//信息字段，所属字段组
    getListGroupConfig().then((list) => {
      if (list) {
        list.forEach((resData => {
          let obj = {'dictDataCode': resData.id, 'dictDataName': resData.groupName}
          if (userType) obj.userType = userType
          result.push(obj)
        }))
        userStore.setDicts(result, code);
        // data.value = result;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  } else {//公共码表获取
    if (dictionaryFieldUrl) {
      if (code === 'dictionaryField' && groupId) {//字段英文名
        getDictionaryFieldByGroupId(groupId, dictionaryFieldUrl).then((list) => {
          if (list) {
            list.forEach((resData => {
              result.push({'dictDataCode': resData, 'dictDataName': resData})
            }))
            userStore.setDicts(result, code);
            data.value = result;
          }
        }).catch((e) => {
          EleMessage.error(e.message);
        });
      } else {
        getDicFieldValueByUrl(dictionaryFieldUrl, params).then((list) => {
          let valueField = dicQueryParams?.valueField ?? 'id';
          let textField = dicQueryParams?.textField ?? 'name';
          if (list) {
            list.forEach((resData => {
              result.push({'dictDataCode': resData[valueField], 'dictDataName': resData[textField], ...resData})
            }))
            userStore.setDicts(result, code);
          }
        }).catch((e) => {
          EleMessage.error(e.message);
        });
      }
    } else {
      //公共码表获取
      getCodeData({codeType: code}).then((list) => {
        let valueField = dicQueryParams?.valueField ?? 'id';
        let textField = dicQueryParams?.textField ?? 'name';
        if (list) {
          list.forEach((resData => {
            let obj = {'dictDataCode': resData[valueField], 'dictDataName': resData[textField], ...resData}
            if (userType) obj.userType = userType
            result.push(obj)
            // result.push({'dictDataCode': resData[valueField], 'dictDataName': resData[textField]})
          }))
          userStore.setDicts(result, code);
        }
      }).catch((e) => {
        EleMessage.error(e.message);
      });
    }
  }
  return result;
}
