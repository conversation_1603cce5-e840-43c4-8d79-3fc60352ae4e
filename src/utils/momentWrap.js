import moment from 'moment'

moment.locale('zh-cn');

export default {
    moment,
    dateFormatUnix(data) {
        return moment(data, 'YYYY-MM-DD HH:mm:ss').valueOf();
    },
    /**
     * 11
     * @param o
     * @returns {*|string}
     */
    dateFormat_h(o) {
        return this.commonHandle(o, 'HH')
    },
    /**
     * 当天0点的时间格式
     * @param o
     * @returns {string}
     */
    dateStartOf(o) {
        return  moment(o).startOf('day').format('YYYY-MM-DD HH:mm:ss') // 当天0点的时间格式
    },
    commonDiff(startDate, endDate) {
        if (startDate && endDate) {
            let a = moment(startDate);
            let b = moment(endDate);
            return b.diff(a, 'minute')
        } else {
            return 0;
        }
    },
    commonDiffSeconds(startDate, endDate) {
        if (startDate && endDate) {
            let a = moment(startDate);
            let b = moment(endDate);
            return b.diff(a, 'seconds')
        } else {
            return 0;
        }
    },
    commonHandle(date, format, orGetNow) {
        if (date) {
            return moment(date).format(format)
        } else if (orGetNow) {
            return moment().format(format)
        }
        return ''
    },
    /**
     * 两个时间比较大小
     * @param date
     * @param orGetNow
     * @returns {boolean}
     */
    dateSJC(date, orGetNow) {
        let a = moment(date).valueOf();
        let b = moment(orGetNow).valueOf();
        return a < b;
    },
    /**
     *  两个时间比较大小（包含date）
     * @param date
     * @param orGetNow
     * @returns {boolean}
     */
    dateSJCCurrtentDate(date, orGetNow) {
        let a = moment(date).valueOf();
        let b = moment(orGetNow).valueOf();
        return a <= b;
    },
    /**
     * 获取两个日期之间相隔多少天
     * @param sDate 开始日期
     * @param eDate 结束日期
     * @returns {number}
     */
    dateSJDays(sDate, eDate) {
        let days = moment(eDate).diff(moment(sDate), 'days')
        // let days = moment('2021-7-8').diff(moment('2021-4-5'),'days')
        //相隔的天数会把最后一天也就是7-8号也算上
        return days;
    },
    /**
     * 2019年7月18日
     * @param o
     * @returns {*|string}
     */
    dateFormat_ll(o) {
        return this.commonHandle(o, 'll')
    },
    /**
     * 获取之前的时间 (subtract)
     * 获取当前时间前 X 天
     * @param o
     * @returns {string}
     */
    subtractDate(o) {
        let seekTime = moment(o).subtract(1, "days").format("YYYY-MM-DD");
        console.log(seekTime) // 2020-03-29 15:54:00
        return seekTime
    },
    subtractDateYMDHMS(o) {
        let seekTime = moment(o).subtract(1, "days").endOf('day').format("YYYY-MM-DD HH:mm:ss");
        console.log(seekTime) // 2020-03-29 15:54:00
        return seekTime
    },
    /**
     * 2018-09-06
     * @param o
     * @returns {*|string}
     */
    dateFormat_YMD(o, orGetNow) {
        return this.commonHandle(o, 'YYYY-MM-DD', orGetNow)
    },
    /**
     * 2019-07-18 11:11:11
     * @param o
     * @returns {*|string}
     */
    dateFormat_date(o) {
        return this.commonHandle(o, 'YYYY-MM-DD HH:mm:ss')
    },
    /**
     * 2019年07月18日 11:00
     * @param o
     * @returns {*|string}
     */
    dateFormat_YHMHMC(o) {
        return this.commonHandle(o, 'YYYY年MM月DD日 HH:mm')
    },
    /**
     * 2019-07-18 11:00
     * @param o
     * @returns {*|string}
     */
    dateFormat_YHMHM(o) {
        return this.commonHandle(o, 'YYYY-MM-DD HH:mm')
    },
    /**
     * 2019-07
     * @param o
     * @returns {*|string}
     */
    dateFormat_YM(o) {
        return this.commonHandle(o, 'YYYY-MM')
    },
    /**
     * 2019-
     * @param o
     * @returns {*|string}
     */
    dateFormat_Y(o) {
        return this.commonHandle(o, 'YYYY')
    },
    /**
     * 201907
     * @param o
     * @returns {*|string}
     */
    dateFormat_YM2(o) {
        return this.commonHandle(o, 'YYYYMM')
    },
    /**
     * 2019年7月
     * @param o
     * @returns {*|string}
     */
    dateFormat_SYM(o) {
        return this.commonHandle(o, 'YYYY年MM月')
    },
    /**
     * 08-18
     * @param o
     * @returns {*|string}
     */
    dateFormat_md(o) {
        return this.commonHandle(o, 'MM-DD')
    },
    /**
     * 11:54
     * @param o
     * @returns {*|string}
     */
    dateFormat_hm(o) {
        return this.commonHandle(o, 'HH:mm')
    },
    /**
     * 自定义时间格式
     * @param o
     * @returns {string}
     */
    diyDateFormat(o,format) {
        return this.commonHandle(o, format)
    },
    /**
     * 在某段时间内
     * */
    timeIn(s, e) {
        let t = moment().format('YYYY-MM-DD')
        if (moment(t + ' ' + s).diff(moment()) < 0 && moment(t + ' ' + e).diff(moment()) > 0) {
            return true
        }
        return false
    },
    /**
     * 在某段时间内
     * */
    timeInByDate(start, end) {
        let a = this.commonDiff(start, new Date())
        let b = this.commonDiff(end, new Date())
        if (a > 0 && b < 0) {
            return true
        }
        return false
    },
}
