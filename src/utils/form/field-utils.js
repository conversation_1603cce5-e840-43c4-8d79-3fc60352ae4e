// @/utils/field-utils.js
// import { baseCodeTypes, regionsArrayTypes } from './form-constants';

import {baseCodeTypes} from '@/components/ProForm/util.js';

export function getFieldProps(field) {
  let props = {};
  let type = field.controlType;
  let typeKey = field.controlType;

  if (baseCodeTypes.includes(field.controlType)) {
    if (!field.options) {
    //   type = 'dictSelect';
    // } else {
      type = 'dictSelect';
      props = field.loadDataType && field.loadDataType !== 'other'
        ? createDictSelectProps(field)
        : createDiyCodeProps(field);
    }
  } else if (field.controlType === 'fileUpload' || field.controlType ===
    'imageUpload') {
    //附件上传数量
    props = {limit: field.regExpression ? Number(field.regExpression) : 1};
  }
  return {props, type, typeKey};
}

export function createFieldObject(field, index, {props, type, typeKey}) {
  return {
    key: `fieldKey_${index}`,
    label: field.fieldZh,
    prop: field.fieldEn,
    type,
    typeKey,
    required: field.required === '是',
    showFlag: field.showFlag,
    selfModifyFlag: field.showFlag,
    fieldLinks: field.fieldLinks,
    selfFieldLink: getSelfFieldLink(field),
    nextField: getNextField(field),
    colProps: getColSpan(typeKey, field?.span),
    props,
    options: field?.options || [],
  };
}

/**
 * 创建详细地址字段
 * @param field
 * @param index
 * @returns {{colProps: number, selfModifyFlag, typeKey: string, prop: string, label: string, type: string, key: string, required: boolean, showFlag, props: {placeholder: string}}}
 */
export function createDetailAddressField(field, index) {
  return {
    key: `detailAddress_${index}`,
    label: '详细地址',
    // prop: `${field.fieldEn}Detail`,
    prop: `${field.fieldEn}_regionsDetail`,
    type: 'input',
    typeKey: 'input',
    required: field.required === '是',
    showFlag: field.showFlag,
    selfModifyFlag: field.showFlag,
    colProps: 8,
    props: {
      placeholder: '请输入详细地址',
    },
  };
}

/**
 * 获取列跨度
 * @param typeKey
 * @param span
 * @returns {number}
 */
export function getColSpan(typeKey, span) {
  const wideFields = [
    'textareaBlobtext',
    'textarea',
    'smoothSignature',
    'radioButton',
    'checkboxButton'];
  return wideFields.includes(typeKey) ? 24 : (span ?? 8);
}

/**
 * 获取下一个关联字段
 * @param field
 * @returns {string}
 */
export function getNextField(field) {
  if (field.loadDataUrl === '/code/codeDwb') return 'zymc';
  if (field.loadDataUrl === '/code/codeZyb') return 'bjmc';
  return '';
}

/**
 * 获取自关联字段
 * @param field
 * @returns {string}
 */
export function getSelfFieldLink(field) {
  if (field.loadDataUrl === '/code/codeDwb') return 'xymc';
  if (field.loadDataUrl === '/code/codeZyb') return 'zymc';
  if (field.loadDataUrl === '/code/codeBjb') return 'bjmc';
  return '';
}

/**
 * 创建字典选择属性
 * @param field
 * @returns {{filterable: boolean, code: (string|null|*), dicQueryParams: {getValType: (string), valueField, textField}}}
 */
export function createDictSelectProps(field = {}) {
  const {
    moduleCode = '',
    loadDataType = '',
    valueField = 'value',
    textField = 'label',
  } = field;

  const nameTypeWhitelist = ['nodeStateJob', 'pycc', 'nj'];
  const getValType = nameTypeWhitelist.includes(loadDataType) ? '' : 'name';

  return {
    code: loadDataType,
    dicQueryParams: {
      getValType,
      valueField,
      textField,
      moduleCode,
    },
    filterable: true,
  };
}

/**
 * 创建自定义代码属性
 * @param field
 * @returns {{filterable: boolean, code: string, dicQueryParams: {valueField, dictFieldUrl: (string|*), textField}}}
 */
export function createDiyCodeProps(field) {
  let diyCode = '';
  if (field.loadDataUrl === '/code/codeDwb') {
    diyCode = 'xymc';
  } else if (field.loadDataUrl === '/code/codeZyb') {
    diyCode = 'zymc';
  } else if (field.loadDataUrl === '/code/codeBjb') {
    diyCode = 'bjmc';
  } else if (field.loadDataUrl === '/code/codeCommon/regionList') {
    diyCode = 'regions';
  } else if (field.loadDataUrl === '/workstudy/qgzx-job-approval/nodeState') {
    diyCode = 'nodeStateJob';
  } else {
    diyCode = field.loadDataType;
  }

  return {
    code: diyCode,
    filterable: true,
    dicQueryParams: {
      dictFieldUrl: field.loadDataUrl,
      valueField: field.valueField,
      textField: field.textField,
    },
  };
}
