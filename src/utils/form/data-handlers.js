import {isImageFile} from '@/utils/common_bak2.js';
const BASE_URL = import.meta.env.BASE_URL;

/**
 * 处理地区数据
 * @param dataValue
 * @param item
 * @returns {*}
 */
export function handleRegionsData(item, dataValue) {
  // 处理地区选择器数据
  if (Array.isArray(dataValue)) {
    return dataValue;
  }

  // 处理字符串格式的地区数据
  if (typeof dataValue === 'string') {
    return dataValue.split('/').filter(Boolean);
  }

  return null;
}

/**
 * 处理图片上传数据
 * @param item
 * @param dataValue
 * @returns {*|*[]}
 */
// export function handleImageUploadData(item, dataValue) {
//   console.log(item, dataValue)
//   return dataValue
//     ? JSON.parse(dataValue).map((d) => ({
//       key: d.id,
//       name: d.originalFilename,
//       contentType: d.contentType,
//       url: `${BASE_URL}api/file/inline/${d.id}`,
//       status: 'done'
//     }))
//     : [];
// }
export function handleImageUploadData(item, dataValue) {
  console.log('handleImageUploadData', item, dataValue);

  // dataValue 是空/null/undefined
  if (!dataValue) return [];

  // 如果 dataValue 是字符串 "[object Object],[object Object]"，直接过滤掉
  if (typeof dataValue === 'string' && dataValue.includes('[object Object]')) {
    return [];
  }

  let fileList = [];

  try {
    const parsed = typeof dataValue === 'string' ? JSON.parse(dataValue) : dataValue;

    // 如果是数组就处理
    if (Array.isArray(parsed)) {
      fileList = parsed.map((d) => ({
        key: d.id,
        name: d.originalFilename || d.name || '未知文件',
        contentType: d.contentType || 'application/octet-stream',
        url: `${BASE_URL}api/file/inline/${d.id}`,
        status: 'done'
      }));
    }
  } catch (e) {
    console.warn('handleImageUploadData: 无法解析 dataValue', e);
  }

  return fileList;
}

/**
 * 处理文件上传数据
 * @param dataValue
 * @returns {*|*[]}
 */
export function handleFileUploadData(dataValue) {
  return dataValue
    ? JSON.parse(dataValue).map((d) => {
      const isImage = isImageFile({
        name: d.originalFilename,
        contentType: d.contentType
      });
      return {
        key: d.id,
        name: d.originalFilename,
        contentType: d.contentType,
        fileUrl: `${BASE_URL}api/file/inline/${d.id}`,
        status: 'done',
        isImageFile: isImage,
        ...(isImage ? {url: `${BASE_URL}api/file/inline/${d.id}`} : {})
      };
    })
    : [];
}

