import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user';
import {listRoles} from '@/views/system/role/api';
import {getCodeDwb} from '@/views/base-code/dwb/api';
import {getCodeZyb} from '@/views/base-code/zyb/api';
import {getCodeBjb} from '@/views/base-code/bjb/api';
import {
  getDicFieldValueByUrl,
  getDictionaryFieldByGroupId,
} from '@/views/system/sphfw/dictionary-field/api';
import {getControlTypeData} from '@/utils/control-type-util';
import {getCodeType} from '@/views/base-code/dictionary/api';
import {getDictionaryGroup} from '@/views/system/sphfw/dictionary-group/api';
import {getListGroupConfig} from '@/views/system/sphfw/list-group/api';
import {getCodeData} from '@/views/base-code/dictionary/api/data-index';

/**
 * 获取字典数据hook
 * @param codes 字典编码
 * @param dicQueryParams
 */
export function useDictData(codes, dicQueryParams = {}) {
  return new Promise((resolve, reject) => {
    const groupId = dicQueryParams.groupId;
    const dictionaryFieldUrl = dicQueryParams.dictFieldUrl;
    const userType = dicQueryParams.userType;
    const params = dicQueryParams.params;
    const valueField = dicQueryParams.valueField ?? 'id';
    const textField = dicQueryParams.textField ?? 'name';

    const userStore = useUserStore();
    const {dicts} = storeToRefs(userStore);

    const tasks = codes.map((code) => {
      if (dicts.value[code] != null) {
        return Promise.resolve(); // 已缓存
      }

      let result = [];

      const pushAndSet = (list, valueGetter = (res) => ({
        dictDataCode: res[valueField],
        dictDataName: res[textField],
        ...res,
      })) => {
        console.log(list);
        list.forEach((res) => {
          const obj = valueGetter(res);
          if (userType) obj.userType = userType;
          result.push(obj);
        });
        console.log(result, code);
        userStore.setDicts(result, code);
      };

      // 根据不同 code 类型调用不同接口
      switch (code) {
        case 'listRoles':
          return listRoles().
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.id, dictDataName: res.name}))).
            catch(reject);
        case 'xymc':
          return getCodeDwb(params).
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.id, dictDataName: res.name}))).
            catch(reject);
        case 'zymc':
          return getCodeZyb().
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.id, dictDataName: res.name}))).
            catch(reject);
        case 'bjmc':
          return getCodeBjb().
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.id, dictDataName: res.name}))).
            catch(reject);
        case 'controlType':
          return getControlTypeData().
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.value, dictDataName: res.label}))).
            catch(reject);
        case 'listDicCode':
          return getCodeType().then((list) => {
            pushAndSet(list,
              (res) => ({dictDataCode: res.code, dictDataName: res.name}));
            result.push({dictDataCode: 'other', dictDataName: '其他'});
            userStore.setDicts(result, code);
          }).catch(reject);
        case 'groupType':
          return getDictionaryGroup(userType ? {userType} : {}).
            then((list) => pushAndSet(list, (res) => ({
              dictDataCode: res.id,
              dictDataName: res.groupName, ...res,
            }))).
            catch(reject);
        case 'listGroup':
          return getListGroupConfig().
            then((list) => pushAndSet(list,
              (res) => ({dictDataCode: res.id, dictDataName: res.groupName}))).
            catch(reject);
        default:
          if (dictionaryFieldUrl) {
            if (code.includes('_dictionaryField') && groupId) {
              return getDictionaryFieldByGroupId(groupId, dictionaryFieldUrl).
                then((list) => pushAndSet(list,
                  (res) => ({dictDataCode: res, dictDataName: res}))).
                catch(reject);
            } else {
              return getDicFieldValueByUrl(dictionaryFieldUrl, params).
                then((list) => pushAndSet(list)).
                catch(reject);
            }
          } else {
            return getCodeData({codeType: code}).
              then((list) => pushAndSet(list)).
              catch(reject);
          }
      }
    });

    Promise.all(tasks).then(() => resolve()).catch(reject);
  });
}

