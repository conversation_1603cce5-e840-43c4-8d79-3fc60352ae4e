/**
 * axios实例
 */
import axios from 'axios';
import {computed, unref} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {API_BASE_URL, LAYOUT_PATH} from '@/config/setting';
import router from '@/router';
import {getToken, removeToken, setToken} from './token-util';
import {getCurrentRole, removeCurrentRole} from './current-role-util';
import {logout, toURLSearch} from './common.js';
import {ElLoading} from 'element-plus';
// import {withLoading} from "@/utils/loading.js";

// 定义loading变量
let loadingInstance;
/** 创建axios实例 */
const service = axios.create({
  baseURL: API_BASE_URL,
});

/**
 * 添加请求拦截器
 */
service.interceptors.request.use(
  (config) => {
    // 条件判断，如果不存在loading实例就创建一个
    if (!loadingInstance) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
        fullscreen: true, // 全屏模式
      });
    }
    // 添加token到header
    const token = getToken();
    if (token && config.headers) {
      config.headers['Authorization'] = token;
    }
    /** 当前用户角色*/
    const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
    if (currentRoles && config.headers) {
      config.headers['roleId'] = currentRoles.roleId;
    }
    // get请求处理数组和对象类型参数
    if (config.method === 'get' && config.params) {
      config.url = toURLSearch(config.params, config.url);
      config.params = {};
    }

    // 条件判断，如果loading实例存在就关闭它
    if (loadingInstance) {
      loadingInstance.close();
    }
    return config;
  },
  (error) => {
    console.log('request===error==', error);
    // 条件判断，如果loading实例存在就关闭它
    if (loadingInstance) {
      loadingInstance.close();
    }
    // return Promise.reject(error);
    return Promise.reject(error.request.data);
  },
);

/**
 * 添加响应拦截器
 */
service.interceptors.response.use(
  (res) => {
    // 条件判断，如果不存在loading实例就创建一个
    if (!loadingInstance) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
        fullscreen: true, // 全屏模式
      });
    }

    // 登录过期处理，接口返回401直接到登录页重新登录
    if (res.data?.code === 401) {
      const {path, fullPath} = unref(router.currentRoute);
      ElMessageBox.close();
      ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
        confirmButtonText: '重新登录',
        callback: (action) => {
          if (action === 'confirm') {
            // logout(false, fullPath);
            removeToken();
            removeCurrentRole();
            // 这样跳转避免再次登录重复注册动态路由
            const BASE_URL = import.meta.env.BASE_URL;
            const url = BASE_URL + 'login'; // hash 路由模式使用 '#/login'
            location.replace(url);
          }
        },
        type: 'warning',
        draggable: true,
      });
      // }
      return Promise.reject(new Error(res.data.message));
    }
    // 续期token
    const newToken = res.headers['authorization'];
    if (newToken) {
      setToken(newToken);
    }

    // 条件判断，如果loading实例存在就关闭它
    if (loadingInstance) {
      loadingInstance.close();
    }
    return res;
  },
  (error) => {
    console.log('post===error==', error);
    // 响应错误处理...
    if (loadingInstance) {
      loadingInstance.close();
    }
    // return Promise.reject(error);
    // return Promise.reject(error.response.data);
    return error.response;
  },
);

export default service;
