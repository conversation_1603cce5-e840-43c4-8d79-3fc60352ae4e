// utils/use-return-refresh.js
import {onMounted, watch} from 'vue';
import {useRoute} from 'vue-router';

/**
 * 用于从详情页返回后，根据 enterType 判断是否刷新列表
 * @param {Function} onRefresh - 刷新函数（通常是 tableRef.value.reload 或自定义 reload）
 * @param {String} [flag='returnRefresh'] - 触发刷新所需的 enterType 标识
 */
export function useReturnRefresh(onRefresh, flag = 'returnRefresh') {
  const route = useRoute();

  const checkRefresh = () => {
    if (route.query.enterType === flag) {
      onRefresh?.();
    }
  };

  onMounted(checkRefresh);

  watch(() => route.query.enterType, (val) => {
    if (val === flag) {
      onRefresh?.();
    }
  });
}
