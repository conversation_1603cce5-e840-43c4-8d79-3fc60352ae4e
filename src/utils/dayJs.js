import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
// 使用 isBetween 插件
dayjs.extend(isBetween);
import 'dayjs/locale/zh-cn'; // 引入中文语言包
dayjs.locale('zh-cn'); // 设置为中文

/**
 * 检查一个时间段与另一个时间段的关系
 * @param {string} startStr - 开始时间
 * @param {string} endStr - 结束时间
 * @param {string} periodStartStr - 区间开始时间
 * @param {string} periodEndStr - 区间结束时间
 * @returns {object} - 检测结果
 */
export function checkPeriod(startStr, endStr, periodStartStr, periodEndStr) {
  const start = dayjs(startStr);
  const end = dayjs(endStr);
  const periodStart = dayjs(periodStartStr);
  const periodEnd = dayjs(periodEndStr);
  // console.log(start, end, periodStart, periodEnd)
  /**
   * isStartInPeriod  开始时间在某区间
   * isEndInPeriod 结束时间在某区间
   * isCrossPeriod 完全跨越整个区间
   * isOverlap 只要有一点点重叠就算
   */
  return {
    isStartInPeriod: start.isBetween(periodStart, periodEnd, null, '[]'), // [] 表示包含边界
    isEndInPeriod: end.isBetween(periodStart, periodEnd, null, '[]'),
    isCrossPeriod: start.isBefore(periodStart) && end.isAfter(periodEnd),
    isOverlap: start.isBefore(periodEnd) && end.isAfter(periodStart), // 有重叠的情况
  };
}

/**
 * 批量检测一个时间段与多个时间段的关系
 * @param {string} startStr - 开始时间
 * @param {string} endStr - 结束时间
 * @param {Array} periods - 区间数组 { periodStart, periodEnd }
 * @returns {Array} - 检测结果数组
 */
export function checkMultiplePeriods(startStr, endStr, periods) {
  return periods.map(({periodStart, periodEnd}) => {
    return {
      periodStart, periodEnd, ...checkPeriod(startStr, endStr, periodStart, periodEnd),
    };
  });
}

/**
 * 快速筛选出有冲突的时间段
 * @param {string} startStr - 开始时间
 * @param {string} endStr - 结束时间
 * @param {Array} periods - 区间数组 { periodStart, periodEnd }
 * @returns {Array} - 有冲突的区间数组
 */
export function findOverlappingPeriods(startStr, endStr, periods) {
  return periods.filter(({periodStart, periodEnd}) => {
    const {isOverlap} = checkPeriod(startStr, endStr, periodStart, periodEnd);
    return isOverlap;
  });
}


/**
 * 格式化日期显示
 * 'YYYY-MM-DD'   "2025-05-07"
 * 'YYYY-MM-DD dddd'   "2025-05-07  星期一"
 * 'YYYY-MM-DD HH:mm:ss' "2025-05-07 14:30:42"
 */
export function dayJsFormatDate(date, tm) {
  return dayjs(date).format(tm ? tm : 'YYYY-MM-DD')
}

/**
 * 判断一个时间点是不是落在某个时间段内
 * @param time 2025-04-08 10:30
 * @param startTime 2025-04-08 10:00
 * @param endTime 2025-04-08 11:00
 * @returns {boolean}
 */
export function isTimeInRange(time, startTime, endTime) {
  return dayjs(time).isBetween(startTime, endTime, null, '[]');
}

// 比较两个时间字符串（格式 HH:mm）
// 时间比较函数
export function compareTime(time1, time2) {
  const [h1, m1] = (time1 || '00:00').split(':').map(Number)
  const [h2, m2] = (time2 || '00:00').split(':').map(Number)
  return (h1 * 60 + m1) - (h2 * 60 + m2)
}

