/**
 * 下载文件工具函数
 */

/**
 * 下载Blob数据为文件
 * @param {Blob} blob - 要下载的Blob数据
 * @param {string} fileName - 文件名
 */
export function downloadBlob(blob, fileName) {
  const link = document.createElement('a');
  link.href = window.URL.createObjectURL(blob);
  link.download = fileName;
  link.click();
  window.URL.revokeObjectURL(link.href);
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} fileName - 文件名
 */
export function downloadFile(url, fileName) {
  fetch(url)
    .then(response => response.blob())
    .then(blob => {
      downloadBlob(blob, fileName);
    })
    .catch(error => {
      console.error('下载文件失败:', error);
    });
}
