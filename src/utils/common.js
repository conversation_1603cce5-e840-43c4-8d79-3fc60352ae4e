import {removeToken} from '@/utils/token-util';
import {removeCurrentRole} from '@/utils/current-role-util';
import {checkLogout} from '@/api/login';
import {ElMessage as EleMessage} from 'element-plus';

/**
 * 资助审核节点状态配置
 */
export function comApproveStatus() {
  return [
    {
      label: '', //用户自定义设置状态 值
      diyname: 'statePass', //用户自定义设置状态
      prename: 'presetStatePass', //系统内置状态
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 1,
    },
    {
      label: '',
      diyname: 'stateTerminate', //用户自定义设置状态
      prename: 'presetStateTerminate', //系统内置状态
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 2,
    },
    {
      label: '',
      diyname: 'stateReturn',
      prename: 'presetStateReturn',
      eventId: 'statePassEventId', //用户定义关联事件
      name: '', //系统内置状态  值
      sort: 3,
    },
  ];
}

/**
 * 题库选项配置
 */
export function comQuestionSels() {
  return [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ];
}

/**
 * 公共表格列配置
 * @returns {[{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},{prop: string, label: string},null,null]}
 */
export function comTableCloumns() {
  return [
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'xymc',
      label: '所属院系',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
    {
      prop: 'njmc',
      label: '年级名称',
    },
  ];
}

export function comCondition() {
  return [
    {label: '等于', value: 'EQ'},
    {label: '不等于', value: 'NE'},
    {label: '大于', value: 'GT'},
    {label: '大于等于', value: 'GE'},
    {label: '小于', value: 'LT'},
    {label: '小于等于', value: 'LE'},
    {label: '包含', value: 'LIKE'},
    {label: '不包含', value: 'NOT_LIKE'},
    {label: '结尾等于', value: 'LIKE_LEFT'},
    {label: '开头等于', value: 'LIKE_RIGHT'},
    {label: '为NULL', value: 'IS_NULL'},
    {label: '不为空', value: 'IS_NOT_NULL'},
    {label: 'IN', value: 'IN'},
    {label: 'NOT IN', value: 'NOT_IN'},
    {label: 'IN条件解析逗号分割', value: 'IN_STR'},
    {label: ' NOT IN条件解析逗号分割', value: 'NOT_IN_STR'},
  ];
}

/**
 * 退出登录
 * @param route 是否使用路由跳转
 * @param from 登录后跳转的地址
 * @param push 路由跳转方法
 */
export function logout(route, from, push) {
  checkLogout().then(() => {
    removeToken();
    removeCurrentRole();
    if (route && push) {
      push({
        path: '/login',
        query: from ? {from: encodeURIComponent(from)} : void 0,
      });
      return;
    }
    // 这样跳转避免再次登录重复注册动态路由
    const BASE_URL = import.meta.env.BASE_URL;
    const url = BASE_URL + 'login'; // hash 路由模式使用 '#/login'
    location.replace(from ? `${url}?from=${encodeURIComponent(from)}` : url);
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/**
 * 下载文件
 * @param data 二进制数据
 * @param name 文件名
 * @param type 文件类型
 */
export function download(data, name, type) {
  const blob = new Blob([data], {type: type || 'application/octet-stream'});
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = name;
  a.style.display = 'none';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * 动态生成 form 的方法
 * @param columns
 * @returns {{}}
 */
export function generateForm(columns) {
  const form = {};
  columns.forEach((col) => {
    // 跳过 selection 类型
    if (col.type === 'selection') return;
    const prop = col.prop;
    if (!prop) return;
    // 根据类型推断默认值
    switch (col.type) {
      case 'input':
        form[col.prop] = '';
        break;
      case 'switch':
        form[col.prop] = false;
        break;
      case 'array':
        form[col.prop] = [];
        break;
      case 'number':
        form[col.prop] = 0;
        break;
      default:
        form[col.prop] = '';
    }
  });
  return form;
}

/**
 * 参数转url字符串
 * @param params 参数
 * @param url 需要拼接参数的地址
 */
export function toURLSearch(params, url) {
  if (typeof params !== 'object' || params == null) {
    return '';
  }
  const result = transformParams(params).
    map((d) => `${encodeURIComponent(d[0])}=${encodeURIComponent(d[1])}`).
    join('&');
  if (!url) {
    return result;
  }
  return (url.includes('?') ? `${url}&` : `${url}?`) + result;
}

/**
 * 参数转表单数据
 * @param params 参数
 */
export function toFormData(params) {
  const formData = new FormData();
  if (typeof params !== 'object' || params == null) {
    return formData;
  }
  transformParams(params).forEach((d) => {
    formData.append(d[0], d[1]);
  });
  return formData;
}

/**
 * 问卷调查
 * 在FormData中发送包含文件的对象数组的嵌套对象
 * @param obj
 * @param form
 * @param namespace
 * @returns {FormData}
 */
export function toFormDataWj(obj, form, namespace) {
  // console.log(obj, form, namespace)
  let fd = form || new FormData();
  let formKey;
  for (let property in obj) {
    if (obj.hasOwnProperty(property) && obj[property]) {
      if (namespace) {
        formKey = namespace + '[' + property + ']';
        // formKey = namespace + '.' + property;
        // if ((obj[property] instanceof File)) {
        //   formKey = namespace + '[' + property + '_' + obj['contentId'] + ']';
        // }
      } else {
        formKey = property;
      }
      // if the property is an object, but not a File, use recursivity.
      if (obj[property] instanceof Date) {
        fd.append(formKey, obj[property].toISOString());
      } else if (
        typeof obj[property] === 'object' &&
        !(obj[property] instanceof File)
      ) {
        toFormDataWj(obj[property], fd, formKey);
      } else {
        // if it's a string or a File object
        fd.append(formKey, obj[property]);
      }
    }
  }
  return fd;
}

/**
 * get请求处理数组和对象类型参数
 * @param params 参数
 */
export function transformParams(params) {
  const result = [];
  if (params != null && typeof params === 'object') {
    Object.keys(params).forEach((key) => {
      const value = params[key];
      if (value != null && value !== '') {
        //表单多附件上传处理
        if (Array.isArray(value) && value.length) {
          // && isBlobFile(value[0].file)
          if (typeof value[0] === 'object') {
            value.forEach((v) => {
              if (v.status !== 'done') result.push([key, v.file]);
            });
          } else {
            result.push([key, value.join(',')]);
          }
        } else if (typeof value === 'object' && !isBlobFile(value)) {
          getObjectParamsArray(value).forEach((item) => {
            result.push([`${key}${item[0]}`, item[1]]);
          });
        } else {
          result.push([key, value]);
        }
      }
    });
  }
  return result;
}

/**
 * 对象转参数数组
 * @param obj 对象
 */
export function getObjectParamsArray(obj) {
  const result = [];
  Object.keys(obj).forEach((key) => {
    const value = obj[key];
    if (value != null && value !== '') {
      const name = `[${key}]`;
      if (typeof value === 'object' && !isBlobFile(value)) {
        getObjectParamsArray(value).forEach((item) => {
          result.push([`${name}${item[0]}`, item[1]]);
        });
      } else {
        result.push([name, value]);
      }
    }
  });
  return result;
}

/**
 * 判断是否是文件
 * @param obj 对象
 */
export function isBlobFile(obj) {
  return obj != null && (obj instanceof Blob || obj instanceof File);
}

/**
 * 切换主题过渡动画
 * @param callback 执行的方法
 * @param el 过渡动画触发元素
 * @param isOut 是否是退出方向
 * @param isBody 是否在 body 上执行动画
 */
export function doWithTransition(callback, el, isOut, isBody) {
  if (!el || typeof document.startViewTransition !== 'function') {
    callback().then(() => {});
    return;
  }
  document.documentElement.classList.add('disabled-transition');
  el.classList.add('view-transition-trigger');
  el.style.setProperty('view-transition-name', 'view-transition-trigger');
  if (isBody) {
    document.body.style.setProperty('view-transition-name', 'body');
  }
  const rect = el.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, innerWidth - x),
    Math.max(y, innerHeight - y),
  );
  document.startViewTransition(callback).ready.then(() => {
    const clipPath = [
      `circle(0px at ${x}px ${y}px)`,
      `circle(${endRadius}px at ${x}px ${y}px)`,
    ];
    const anim = document.documentElement.animate(
      {clipPath: isOut ? [...clipPath].reverse() : clipPath},
      {
        duration: 400,
        easing: 'ease-in',
        pseudoElement: isOut
          ? `::view-transition-old(${isBody ? 'body' : 'root'})`
          : `::view-transition-new(${isBody ? 'body' : 'root'})`,
      },
    );
    anim.onfinish = () => {
      document.body.style.removeProperty('view-transition-name');
      el.style.removeProperty('view-transition-name');
      el.classList.remove('view-transition-trigger');
      document.documentElement.classList.remove('disabled-transition');
    };
  });
}

/**
 * 将两个多维数组合并，并使用Set对象去除重复的子数组
 * @param arr1
 * @param arr2
 * @returns {*[]}
 */
export function mergeUnique(arr1, arr2, keyword) {
  const array = [...arr1, ...arr2];
  const map = new Map();
  return array.filter((item) => {
    const k = item[keyword];
    // 如果map中不存在当前关键字，则保留该对象，并将其添加到map中
    const exists = map.has(k);
    map.set(k, true);
    return !exists;
  });
}

export function mergeArraysWithoutDuplicates(arrays, elementToMerge) {
  const mergedArray = [];
  const seenElements = new Set();

  for (const array of arrays) {
    for (const item of array) {
      if (item[elementToMerge] && !seenElements.has(item[elementToMerge])) {
        seenElements.add(item[elementToMerge]);
        mergedArray.push(item);
      }
    }
  }
  return mergedArray;
  // 示例使用
  //   const arrays = [[{id: 1, name: 'Alice'}, {id: 2, name: 'Bob'}], [{id: 2, name: 'Bob'}, {id: 3, name: 'Charlie'}]]
  //   let newArray = []
  //   if (arrays.length > 0) newArray = mergeArraysWithoutDuplicates(arrays, 'id');
  //   console.log(newArray); // [{id: 1, name: 'Alice'}, {id: 2, name: 'Bob'}, {id: 3, name: 'Charlie'}]
}

/**使用 Set 对象去除重复项：*/
export function removeDuplicates(arr1, arr2) {
  const set1 = new Set(arr1);
  const set2 = new Set(arr2);
  const difference = new Set([...set1].filter((x) => !set2.has(x)));
  return [...difference];
}

/**
 * 两个数组取相同数据，返回name
 * @param arr1
 * @param arr2
 * @param type 取值字段名
 * @returns {string}
 */
export function transformDicDataName(arr1, arr2, type) {
  let arraySome = [];
  let newStr = '';
  if (arr1?.length > 0 && arr2.length > 0) {
    arraySome = arr1.filter((item) =>
      arr2.some((ele) => {
        if (type) {
          return ele[type] === item.dictDataCode;
        } else {
          return ele === item.dictDataCode;
        }
      }),
    );
    let arraySomeMap = arraySome.map((item) => {
      return item.dictDataName;
    });
    newStr = arraySomeMap.join();
  }
  return newStr;
}

/**
 * 从arr2中删除在arr1中存在的数据
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function removeSomeData(arr1, arr2, type) {
  return arr2.filter(
    (item1) => !arr1.some((item2) => item1[type] === item2[type]),
  );
}

/**
 * 两个数组取相同数据，返回code
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function transformDicDataCode(arr1, arr2, type) {
  let arraySome = [];
  let arraySomeMap = [];
  if (arr2.length > 0) {
    arraySome = arr1.filter((item) =>
      arr2.some((ele) => {
        if (type) {
          return ele[type] === item.dictDataCode;
        } else {
          return ele === item.dictDataCode;
        }
      }),
    );
    arraySomeMap = arraySome.map((item) => {
      return item.dictDataCode;
    });
  }
  return arraySomeMap;
}

/**
 * 向数组指定下标添加元素
 * @param array
 * @param element
 * @param index
 * @returns {*}
 */
export function insertAtIndex(array, element, index) {
  return array.splice(index, 0, element);
}

/**
 * 生成随机字符串
 * 使用Math.random()函数生成随机数，并将其转换为字符串。然后使用slice()方法截取需要的长度。
 * @param length
 * @returns {string}
 */
export function generateRandomString(length) {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/** 判断是否是图片文件 */
export function isImageFile(item) {
  return !!(
    typeof item.contentType === 'string' &&
    item.contentType.startsWith('image/')
  );
}

/** js数组通过某个字段进行分组 */
export function groupArr(list, fieldMark, fieldId) {
  let field = fieldMark;
  //心理咨询师分组特殊处理
  if (fieldMark === 'xlzxEvent') field = 'xm';
  let fieldList = [],
    att = [];
  list.map((e) => {
    fieldList.push(e[field]);
  });
  //数组去重
  fieldList = fieldList.filter((e, i, self) => {
    return self.indexOf(e) === i;
  });
  for (let j = 0; j < fieldList.length; j++) {
    //过滤出匹配到的数据
    let arr = list.filter((e) => {
      return e[field] === fieldList[j];
    });
    //心理咨询师分组特殊处理
    const totalCapacity =
      fieldMark === 'xlzxEvent'
        ? arr.reduce((sum, item) => sum + (item.kyyrs || 0), 0)
        : '';
    att.push({
      // type: arr[0].sttype,
      type: arr[0][field],
      fieldId: fieldId ? arr[0][fieldId] : '',
      listLength: arr.length,
      list: arr,
      totalCapacity: totalCapacity,
    });
  }
  return att;
}

/** sort方法根据数组中对象的某一个属性值进行排序 */
export function compare(property) {
  return function(a, b) {
    const value1 = a[property];
    const value2 = b[property];
    return value1 - value2;
  };
}

/**
 * 遍历一个树形结构并获取所有子节点
 * @param node childNode为Object对象
 * @returns {*[]}
 */
// export function traverseTree(node,) {
//     let result = [];
//     (function recurse(currentNode) {
//         if (currentNode) {
//             console.log(currentNode.type)
//             // 然后递归遍历这个子节点
//             // 如果children是单个对象，加入结果数组
//             if (currentNode.childNode) {
//                 let newNodeData = {
//                     ...currentNode.nodeUserList,
//                     id: currentNode.id,
//                     nodeName: currentNode.nodeName,
//                     settype: currentNode.settype,
//                     type: currentNode.type,
//                     error: currentNode.error,
//                     childNode: currentNode.childNode,
//                 }
//                 result.push(newNodeData);
//                 recurse(currentNode.childNode);
//                 // } else {
//                 //     result.splice(-1, 1); // 删除最后一个内嵌数组
//             }
//         }
//     })(node); // 调用时立即执行函数
//     return result;
// }

export function traverseTree(node) {
  let result = [];
  (function recurse(currentNode) {
    if (currentNode) {
      // 然后递归遍历这个子节点
      // 如果children是单个对象，加入结果数组
      if (currentNode.childNode) {
        let newNodeData = {
          ...currentNode.nodeUserList,
          id: currentNode.id,
          nodeName: currentNode.nodeName,
          settype: currentNode.settype,
          type: currentNode.type,
          error: currentNode.error,
          // childNode: currentNode.childNode,
        };
        result.push(newNodeData);
        recurse(currentNode.childNode);
        // } else {
        //     result.splice(-1, 1); // 删除最后一个内嵌数组
      }
    }
  })(node); // 调用时立即执行函数
  return result;
}

/**
 * 递归数组，子节点childNode
 * @param arr
 * @param n
 * @returns {*}
 */
export function recursiveArrayFunction(arr, n) {
  if (n === arr.length - 1) {
    return arr[n];
  }
  arr[n].childNode = recursiveArrayFunction(arr, n + 1);
  return arr[n];
}

/**
 * 将对象的属性值转换为get请求?后的字符串
 * @param Object
 * @returns {string}
 */
export function formMateSearchDataToStr(object) {
  let str = '';
  Object.entries(object).forEach(([key, value]) => {
    console.log(key, value);
    if (value) {
      str += `&${key}=${value}`;
    }
  });
  return str;
}
