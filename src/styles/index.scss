/** 全局样式 */
@use 'element-plus/theme-chalk/src/mixins/function.scss' as *;
@use './transition.scss' as *;

* {
  outline: none;
}

html {
  overflow: auto;
}

body .el-table--default {
  font-size: 12px !important;
}


/* 钉钉进步体*/
//@font-face {
//  font-family: 'DingTalk';
//  src: url('/font/DingTalkSans.ttf');
//  font-display: swap;
//}

@font-face {
  font-family: 'Inter-Regular';
  src: url('../assets/font/Inter/Inter-Regular.otf');
  font-display: swap;
}

@font-face {
  font-family: 'Inter-Medium';
  src: url('../assets/font/Inter/Inter-Medium.otf');
  font-display: swap;
}

@font-face {
  font-family: 'Inter-Bold';
  src: url('../assets/font/Inter/Inter-Bold.otf');
  font-display: swap;
}

@font-face {
  font-family: 'HarmonyOS_Sans_SC_Regular';
  src: url('../assets/font/HarmonyOS_Sans_SC/HarmonyOS_SansSC_Regular.ttf');
  font-display: swap;
}

@font-face {
  font-family: 'HarmonyOS_Sans_SC_Medium';
  src: url('../assets/font/HarmonyOS_Sans_SC/HarmonyOS_SansSC_Medium.ttf');
  font-display: swap;
}

@font-face {
  font-family: 'HarmonyOS_Sans_SC_Bold';
  src: url('../assets/font/HarmonyOS_Sans_SC/HarmonyOS_SansSC_Bold.ttf');
  font-display: swap;
}

body {
  margin: 0;
  line-height: 1.58;
  color: getCssVar('text-color', 'regular');
  font-size: getCssVar('font-size', 'base');
  font-family: getCssVar('font-family'), serif;
  //font-family: 'HarmonyOS_Sans_SC_Regular', 'HarmonyOS_Sans_SC_Medium','HarmonyOS_Sans_SC_Bold',serif, sans-serif;
  //font-family: 'Inter-Regular', 'Inter-Medium', 'Inter-Bold', serif, sans-serif;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow-x: hidden;
  overflow-y: auto;
  height: 100vh;
}

/* 关闭响应式 */
body.ele-body-limited {
  min-width: 1200px;
}

/* 色弱模式 */
.ele-admin-weak {
  filter: invert(0.8);
}

/* 按钮加图标减少间距 */
.el-button {
  font-family: "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
}

.ele-btn-icon.el-button,
.ele-btn-icon.el-button.is-round {
  padding-left: 10px;
  padding-right: 12px;
  // &.el-button--small {
  //   padding-left: 5px;
  //   padding-right: 6px;
  // }

  &.el-button--large {
    padding-left: 14px;
    padding-right: 16px;
  }
}

///* 导航菜单右侧展开小图标 */
//.ele-menu .el-sub-menu__title .el-sub-menu__icon-arrow {
//  font-size: 12px !important;
//}

/* 级联选择器增加高度 */
.ele-popper-higher .el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 280px;
}

/* 间距组件样式优化 */
.el-space--horizontal > .el-space__item:last-child {
  margin-right: 0 !important;
}

.el-space--vertical > .el-space__item:last-child {
  padding-bottom: 0 !important;
}

/* echarts */
.echarts > div > div {
  max-width: 100%;
  overflow: hidden;
}

/* 小屏幕时分页去掉一些组件 */
@media screen and (max-width: 768px) {
  .ele-pro-table .el-pagination {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
  }
}

/* 暗黑模式切换过渡 */
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 2147483646;
}

::view-transition-new(root) {
  z-index: 1;
}

.dark::view-transition-old(root) {
  z-index: 1;
}

.dark::view-transition-new(root) {
  z-index: 2147483646;
}

/** ele-card 全局样式重写 */
body .ele-card {
  margin-bottom: 8px !important;
  border-radius: 5px !important;
}

body .ele-page:not(.is-plain) {
  padding: 8px 8px 0 8px !important;
  border-radius: 5px !important;
}

////或者加!important
//.ele-card-body {
//  padding: 10px !important;
//}

/** el-radio-group 全局样式重写 */
body .el-radio-group .el-radio-button.is-active {
  --el-radio-button-checked-bg-color: var(--el-color-primary-light-9);
  --el-radio-button-checked-text-color: var(--el-color-primary);
  --el-radio-button-checked-border-color: var(--el-color-primary-light-5);
}

body .el-switch.is-checked .el-switch__core {
  background-color: var(--el-color-primary-light-9);
}

body .el-switch--small.is-checked .el-switch__core .el-switch__inner .is-text {
  color: var(--el-color-primary);
}

body .el-switch--small.is-checked .el-switch__core .el-switch__action {
  border: 1px solid var(--el-color-primary);
}

body .el-checkbox__input.is-checked .el-checkbox__inner::after {
  border-color: var(--el-color-primary);
}

body .el-checkbox.is-checked .el-checkbox__inner {
  background: #ffffff;
}
