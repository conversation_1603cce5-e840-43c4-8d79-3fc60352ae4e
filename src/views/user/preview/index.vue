<template>
  <ele-page hide-footer flex-table>
    <ele-card class="ele-card-cus">
      <template #header>
        <ele-text class="title">张国庆 - 成长档案</ele-text>
      </template>
      <template #extra>
        <el-button type="primary" :icon="edit" size="small">编辑</el-button>
      </template>
      <el-row>
        <el-col :span="3">
          <div class="left-bar">
            <div class="info-user">
              <div class="info-user-avatar">
                <el-avatar :size="95">
                  <img
                    src="/public/photo.png"/>
                </el-avatar>
              </div>
              <ele-text>
                张国庆
              </ele-text>
            </div>
            <el-divider></el-divider>
            <ele-card class="statistics-card">
              <ele-text type="placeholder" class="statistics-header">
                <div class="statistics-header-text">信息完整度</div>
              </ele-text>
              <ele-text class="statistics-value">68%</ele-text>
              <div class="statistics-body">
                <el-progress color="#13c2c2" :percentage="68" :show-text="false" :stroke-width="10"
                             style="width: 100%"/>
              </div>
            </ele-card>

            <el-anchor :container="containerRef" direction="vertical" type="default" :offset="30" marker="true"
                       @click="handleClick">
              <el-anchor-link href="#part1" active title="基本信息"/>
              <el-anchor-link href="#part2" title="学籍信息"/>
              <el-anchor-link href="#part3" title="课程成绩"/>
              <el-anchor-link href="#part4" title="学籍异动信息"/>
              <el-anchor-link href="#part5" title="校内信息"/>
              <el-anchor-link href="#part6" title="家庭成员信息"/>
              <el-anchor-link href="#part7" title="教育经历"/>
              <el-anchor-link href="#part8" title="校内住宿信息"/>
              <el-anchor-link href="#part9" title="家庭经济情况"/>
              <el-anchor-link href="#part10" title="学生干部履历"/>
              <el-anchor-link href="#part11" title="综合测评"/>

            </el-anchor>
          </div>
        </el-col>
        <el-col :span="21">
          <div ref="containerRef" class="ele-scrollbar-hover" style="height: 800px; overflow-y: auto">
            <ele-card id="part1" class="ele-card-item" collapsable="header" header="基本信息">
              <div style="padding: 0px 20px;">
                <el-descriptions column="4">
                  <el-descriptions-item label="姓名">张国庆</el-descriptions-item>
                  <el-descriptions-item label="学号">2020119045</el-descriptions-item>
                  <el-descriptions-item label="联系电话">84651019</el-descriptions-item>
                  <el-descriptions-item label="性别">男</el-descriptions-item>
                  <el-descriptions-item label="政治面貌">共青团员</el-descriptions-item>
                  <el-descriptions-item label="电子邮箱"><EMAIL></el-descriptions-item>
                  <el-descriptions-item label="民族">汉族</el-descriptions-item>
                  <el-descriptions-item label="证件类型">身份证</el-descriptions-item>
                  <el-descriptions-item label="身份证号">010************218</el-descriptions-item>
                  <el-descriptions-item label="籍贯">共青团员</el-descriptions-item>
                  <el-descriptions-item label="户口所在地">北京市通州区台湖镇</el-descriptions-item>
                  <el-descriptions-item label="入团日期">2017-03-21</el-descriptions-item>
                  <el-descriptions-item label="入党日期">-</el-descriptions-item>
                  <el-descriptions-item label="出生年月"> 2000-03-21</el-descriptions-item>

                </el-descriptions>
              </div>
              <template #collapseIcon="{ collapse }">
                <el-icon>
                  <CaretBottom v-if="collapse"/>
                  <CaretTop v-else/>
                </el-icon>
              </template>
            </ele-card>

            <ele-card id="part2" class="ele-card-item" collapsable="header" header="学籍信息">
              <div style="padding: 0px 20px;">
                <el-descriptions column="4">
                  <el-descriptions-item label="姓名">张国庆</el-descriptions-item>
                  <el-descriptions-item label="学号">2020119045</el-descriptions-item>
                  <el-descriptions-item label="联系电话">84651019</el-descriptions-item>
                  <el-descriptions-item label="性别">男</el-descriptions-item>
                  <el-descriptions-item label="政治面貌">共青团员</el-descriptions-item>
                  <el-descriptions-item label="电子邮箱"><EMAIL></el-descriptions-item>
                  <el-descriptions-item label="民族">汉族</el-descriptions-item>
                  <el-descriptions-item label="证件类型">身份证</el-descriptions-item>
                  <el-descriptions-item label="身份证号">010************218</el-descriptions-item>
                  <el-descriptions-item label="籍贯">共青团员</el-descriptions-item>
                  <el-descriptions-item label="户口所在地">北京市通州区台湖镇</el-descriptions-item>
                  <el-descriptions-item label="入团日期">2017-03-21</el-descriptions-item>
                  <el-descriptions-item label="入党日期">-</el-descriptions-item>
                  <el-descriptions-item label="出生年月"> 2000-03-21</el-descriptions-item>

                </el-descriptions>
              </div>
              <template #collapseIcon="{ collapse }">
                <el-icon>
                  <CaretBottom v-if="collapse"/>
                  <CaretTop v-else/>
                </el-icon>
              </template>
            </ele-card>

            <ele-card id="part2" class="ele-card-item" collapsable="header" header="课程成绩">
              <div style="padding: 0px 20px;">

                <ele-table size="small" style="table-layout: fixed;">
                  <thead>
                  <tr>
                    <th>学年学期</th>
                    <th>课程名</th>
                    <th>成绩</th>
                    <th>学分</th>
                    <th>班级排名</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(row, index) in projectList" :key="row.id">
                    <td>{{ row.d1 }}</td>
                    <td>{{ row.d2 }}</td>
                    <td>{{ row.d3 }}</td>
                    <td>{{ row.d4 }}</td>
                    <td>{{ row.d5 }}</td>
                  </tr>
                  </tbody>
                </ele-table>
              </div>
              <template #collapseIcon="{ collapse }">
                <el-icon>
                  <CaretBottom v-if="collapse"/>
                  <CaretTop v-else/>
                </el-icon>
              </template>
            </ele-card>

            <ele-card id="part3" class="ele-card-item" collapsable="header" header="获奖信息">
              <div style="padding: 0px 20px;">
                <el-empty :image-size="50"/>
              </div>
            </ele-card>

            <ele-card id="part3" class="ele-card-item" collapsable="header" header="家庭成员信息">
              <div style="padding: 0px 20px;">
                <el-empty :image-size="50"/>
              </div>
            </ele-card>
          </div>
        </el-col>

      </el-row>
    </ele-card>
  </ele-page>
</template>

<script setup lang="ts">
import {ref, unref} from 'vue'
import {CaretTop, CaretBottom} from '@element-plus/icons-vue'
import { ElMessage as EleMessage} from "element-plus";
import {useRoute, useRouter} from "vue-router";
import {getGroupList, operation, getPersonInfo} from '../../personInfo/st/api/index.js';

const containerRef = ref<HTMLElement | null>(null)
const route = useRoute();
let xgh = route.params?.id ?? '';
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let routeType = path.split("/")[4];


const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
/** 请求状态 */
const loading = ref(true);
/** 表单页面回显数据 */
const currentData = ref({});
/** 组 */
const groupData = ref([]);
/** 当前组 */
const currentGroup = ref();
/** 选中 */
const active = ref();
/** 菜单点击事件 */
const onMenuItemClick = (item) => {
  currentGroup.value = item
  active.value = item.index;
};
/** 查询详情 */
const queryDictionaryGroup = () => {
  if (xgh && xgh !== 'INSERT') queryPersonInfo();
  setTimeout(() => {
    loading.value = true;
    getGroupList(routeType).then((list) => {
      loading.value = false;
      if (list.length > 0) {
        list.forEach((e, index) => {
          e.data = currentData.value;
          e.index = e.id;
          e.title = e.groupName;
          delete e.icon;
        })
      }
      groupData.value = list
      currentGroup.value = list[0]
      onMenuItemClick(list[0])
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }, 500)
}
/** 根据学工号查询 */
const queryPersonInfo = () => {
  loading.value = true;
  getPersonInfo(routeType, xgh).then((list) => {
    loading.value = false;
    list['avatar'] = null
    currentData.value = list;
    let title = routeType === 'student' ? list.xm + '-成长档案' : list.xm + '-教师详情';
    setPageTabTitle(title);
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

const projectList = ref([
  {
    d1: '2019-2020-1',
    d2: '思想道德修养',
    d3: '85.5',
    d4: '2',
    d5: '12',
  },
  {
    d1: '2019-2020-1',
    d2: '公共英语-读写',
    d3: '85.5',
    d4: '2',
    d5: '12',
  },
  {
    d1: '2019-2020-1',
    d2: '普通教育学',
    d3: '85.5',
    d4: '2',
    d5: '12',
  },
  {
    d1: '2019-2020-1',
    d2: '公文写作',
    d3: '85.5',
    d4: '2',
    d5: '12',
  },
]);


queryDictionaryGroup();
</script>
<style lang="less" scoped>
.ele-page {
}

.ele-card-cus {
  :deep (.ele-card-header ) {
    padding: 10px 20px !important;
  }

  .title {
    font-weight: bold;
    font-size: 14px;
  }
}

.left-bar {
  padding: 5px 15px;

  .el-divider {
    margin: 8px 0;
    border-top: 1px solid #eee;
  }
}

.info-user {
  text-align: center;

  span {
    border: 3px solid #fff;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  }

  .info-user-avatar {
    margin-bottom: 5px;
  }

  .ele-text {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
  }
}

.statistics-card {
  height: 52px;
  color: #ffffff;
  background: linear-gradient(166.644155545969deg, rgba(45, 140, 240, 1) 37%, rgba(255, 255, 255, 1) 125%);
  border-radius: 5px !important;
  overflow: hidden;

  :deep (.ele-card-body) {
    padding: 5px 15px !important;
  }

  .ele-text {
    display: inline-block;
    color: #ffffff;
    margin-top: 1px;
    margin-bottom: 8px;
  }

  .statistics-value {
    float: right;
    font-weight: bold;
  }
}

.el-anchor {
  background: linear-gradient(90deg, rgba(240, 250, 255, 1) 52%, rgba(255, 255, 255, 1) 111%);
  border-radius: 5px;
  padding: 10px 0px;

  :deep (.el-anchor__marker ){
    width: 3px;
    height: 20px;
    border-radius: 0px !important;
    left: inherit;
    right: 10px;
  }
}

.ele-card-item {
  border: 1px solid var(--el-border-color-light);
  border-radius: 5px !important;
  margin-right: 15px;

  :deep (.ele-card-header) {
    padding: 5px 20px !important;
    background: linear-gradient(90deg, rgba(240, 250, 255, 1) 39%, rgba(255, 255, 255, 1) 99%);
  }
}

:deep (.el-descriptions) {
  .el-descriptions__label {
    font-size: 12px;
    min-width: 80px;
    display: inline-block;
  }

  .el-descriptions__content {
    color: #999 !important;
    font-size: 12px;
  }

  .el-descriptions__cell {
    padding-bottom: 8px !important;
  }
}
</style>
