// 模拟学生数据
export const mockStudentData = {
  basicInfo: {
    id: '20230001',
    name: '张三',
    gender: '男',
    age: 20,
    college: '计算机学院',
    major: '软件工程',
    class: '软件2001',
    phone: '13800138000',
    email: '<PERSON><PERSON><PERSON>@edu.cn',
    address: '北京市海淀区',
    politicalStatus: '共青团员',
    enrollmentDate: '2020-09-01'
  },

  academicData: {
    gpa: 3.6,
    creditCompleted: 98,
    creditRequired: 160,
    scoreTrend: [
      { semester: '2020-秋', gpa: 3.2 },
      { semester: '2021-春', gpa: 3.4 },
      { semester: '2021-秋', gpa: 3.5 },
      { semester: '2022-春', gpa: 3.6 },
      { semester: '2022-秋', gpa: 3.7 },
      { semester: '2023-春', gpa: 3.6 }
    ],
    coursePerformance: [
      { name: '数据结构', score: 85, type: '专业课' },
      { name: '高等数学', score: 78, type: '基础课' },
      { name: '大学英语', score: 92, type: '公共课' },
      { name: '算法设计', score: 88, type: '专业课' },
      { name: '数据库原理', score: 90, type: '专业课' }
    ]
  },

  behaviorData: {
    library: {
      monthlyVisits: [12, 15, 18, 20, 16, 14, 10, 8, 15, 18, 20, 22],
      bookCategories: [
        { name: '计算机', value: 45 },
        { name: '文学', value: 20 },
        { name: '经济', value: 15 },
        { name: '历史', value: 10 },
        { name: '其他', value: 10 }
      ]
    },
    consumption: {
      dailyAvg: 28.5,
      monthlyTotal: [850, 900, 780, 820, 950, 880, 920, 860, 890, 910, 930, 960],
      places: [
        { name: '食堂', percentage: 65 },
        { name: '超市', percentage: 20 },
        { name: '其他', percentage: 15 }
      ]
    },
    dormitory: {
      hygieneScores: [85, 90, 88, 92, 87, 91],
      lateReturns: 2,
      violations: 0
    }
  },

  warningTags: [
    { type: 'academic', level: 'low', text: '英语成绩偏低', time: '2022-12-15' },
    { type: 'behavior', level: 'medium', text: '图书馆访问减少', time: '2023-03-10' },
    { type: 'financial', level: 'high', text: '消费异常下降', time: '2023-04-05' }
  ]
}

// 获取学生画像数据
export function getStudentPortrait(studentId) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(mockStudentData)
    }, 500)
  })
}
