<template>
  <ele-page class="dashboard-page">
    <!-- 学生头部信息 -->
    <StudentHeader
      :student-data="studentData"
      :warning-data="warningData"
      @process="handleProcessWarning"
    />

    <!-- 统计卡片区域 -->
      <el-row :gutter="8">
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ studentData.gpa }}</div>
              <div class="stat-label">平均绩点</div>
              <div class="stat-desc">专业排名: {{ studentData.rank }}/{{ studentData.totalStudents }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ studentData.creditProgress }}%</div>
              <div class="stat-label">学分进度</div>
              <div class="stat-desc">已修学分/总学分</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ warningData.length }}</div>
              <div class="stat-label">预警信息</div>
              <div class="stat-desc">需关注事项</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ leaveData.totalDays }}</div>
              <div class="stat-label">本学期请假</div>
              <div class="stat-desc">累计天数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ leaveData.totalDays }}</div>
              <div class="stat-label">本学期请假</div>
              <div class="stat-desc">累计天数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6">
          <el-card class="stat-card" :body-style="{ padding: '5px!important' }">
            <div class="stat-content">
              <div class="stat-value">{{ leaveData.totalDays }}</div>
              <div class="stat-label">本学期请假</div>
              <div class="stat-desc">累计天数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

    <!-- 主要内容区 -->
    <div class="main-content">
      <!-- 左栏 -->
      <div class="left-column">
        <!-- 综合能力评估 -->
<!--        <el-card class="data-card">-->
<!--          <template #header>-->
<!--            <div class="card-header">-->
<!--              <span class="card-title">综合能力评估</span>-->
<!--              <el-tag type="success">最近更新: {{ lastUpdateTime }}</el-tag>-->
<!--            </div>-->
<!--          </template>-->
<!--          <AbilityRadar :data="abilityData"/>-->
<!--        </el-card>-->

        <!-- 学业表现 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">学业表现</span>
            </div>
          </template>
          <AcademicPerformance :data="academicData"/>
        </el-card>

        <!-- 综合测评 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">综合测评</span>
              <el-tag type="info">学年评定</el-tag>
            </div>
          </template>
          <EvaluationTable :data="evaluationData"/>
        </el-card>

        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">心理咨询记录</span>
              <el-tag type="warning">最近6个月</el-tag>
            </div>
          </template>
          <PsychologicalCounseling :data="psychologicalData"/>
        </el-card>
      </div>

      <!-- 右栏 -->
      <div class="right-column">
        <!-- 请销假统计 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">请销假记录</span>
              <el-tag type="warning">本学期</el-tag>
            </div>
          </template>
          <LeaveStatistics :data="leaveData"/>
        </el-card>

        <!-- 资助与奖学金 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">资助与奖学金</span>
              <el-tag type="danger">累计获得</el-tag>
            </div>
          </template>
          <FinancialAid :data="financialData"/>
        </el-card>

        <!-- 行为分析 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">行为分析</span>
              <el-tag>近30天数据</el-tag>
            </div>
          </template>
          <BehaviorAnalysis :data="behaviorData"/>
        </el-card>

        <!-- 预警信息 -->
        <WarningCards :warning-data="warningData" @process="handleProcessWarning"/>
      </div>
    </div>
  </ele-page>
</template>

<script setup>
import {ref, computed} from 'vue';
import AbilityRadar from './components/AbilityRadar.vue';
import AcademicPerformance from './components/AcademicPerformance.vue';
import BehaviorAnalysis from './components/BehaviorAnalysis.vue';
import WarningCards from './components/WarningCards.vue';
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';
import LeaveStatistics from './components/LeaveStatistics.vue';
import FinancialAid from './components/FinancialAid.vue';
import EvaluationTable from './components/EvaluationTable.vue';
import PsychologicalCounseling from '@/views/personInfo/views/components/PsychologicalCounseling.vue';

// 模拟数据
const avatarUrl = ref('https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png');
const lastUpdateTime = ref('2023-06-15');

const studentData = ref({
  id: '20230001',
  name: '张三',
  college: '计算机学院',
  major: '软件工程',
  ni: '2024',
  bjmc: '软件工程一班',
  counselors: [
    {name: '李老师', phone: '13800138000'},
  ],
  enrollmentDate: '2020年9月',
  gpa: 3.6,
  creditProgress: 68,
  rank: 20,
  totalStudents: 120,
  tags: ['优秀学生', '斜杠青年', '科技创新', '社团骨干', '志愿者', '活动积极分子', '优秀学生干部'],
});

const abilityData = ref([
  {name: '学业能力', value: 85},
  {name: '实践能力', value: 72},
  {name: '社交能力', value: 68},
  {name: '创新能力', value: 79},
  {name: '领导能力', value: 65},
]);

const psychologicalData = ref({
  totalSessions: 8,
  emergencyCases: 1,
  followUpRequired: 3,
  typeDistribution: [
    {value: 3, name: '学业压力'},
    {value: 2, name: '人际关系'},
    {value: 1, name: '情绪困扰'},
    {value: 1, name: '生涯规划'},
    {value: 1, name: '危机干预'},
  ],
  monthlyTrend: [1, 2, 1, 2, 1, 1],
  recentSessions: [
    {
      id: 1,
      date: '2023-06-10',
      consultant: '王心理咨询师',
      type: '学业压力',
      topic: '期末考试焦虑',
      duration: 50,
      status: '已完成',
      urgency: 2,
      summary: '学生表现出对期末考试的过度焦虑，通过认知行为疗法帮助其建立合理预期...',
      followUpPlan: '建议两周后回访评估焦虑水平',
    },
    {
      id: 2,
      date: '2023-05-28',
      consultant: '李心理咨询师',
      type: '人际关系',
      topic: '宿舍矛盾调解',
      duration: 60,
      status: '需跟进',
      urgency: 1,
      summary: '与室友生活习惯差异导致矛盾，已进行沟通技巧训练...',
      followUpPlan: '一个月后评估宿舍关系改善情况',
    },
    {
      id: 3,
      date: '2023-05-15',
      consultant: '张心理咨询师',
      type: '危机干预',
      topic: '情绪低落有自伤倾向',
      duration: 90,
      status: '进行中',
      urgency: 3,
      summary: '近期失恋导致情绪崩溃，已进行危机评估并制定安全计划...',
      followUpPlan: '每周一次咨询，密切监控情绪状态',
    },
  ],
});

const academicData = ref({
  scoreTrend: [
    {semester: '2020秋', gpa: 3.2, rank: 45},
    {semester: '2021春', gpa: 3.4, rank: 32},
    {semester: '2021秋', gpa: 3.5, rank: 28},
    {semester: '2022春', gpa: 3.6, rank: 22},
    {semester: '2022秋', gpa: 3.7, rank: 18},
    {semester: '2023春', gpa: 3.6, rank: 20},
  ],
  coursePerformance: [
    {name: '数据结构', score: 85, average: 72},
    {name: '高等数学', score: 78, average: 65},
    {name: '数据库原理', score: 90, average: 75},
    {name: '算法设计', score: 88, average: 70},
  ],
});

const behaviorData = ref({
  libraryVisits: 18,
  labVisits: 12,
  sportsActivities: 8,
  consumption: {
    dailyAverage: 28.5,
    places: [
      {name: '食堂', percentage: 65},
      {name: '超市', percentage: 20},
      {name: '其他', percentage: 15},
    ],
  },
});

const leaveData = ref({
  totalDays: 7,
  leaveRecords: [
    {type: '病假', days: 3, date: '2023-03-10', reason: '感冒发烧', status: '已销假'},
    {type: '事假', days: 2, date: '2023-04-15', reason: '家庭事务', status: '已销假'},
    {type: '公假', days: 2, date: '2023-05-20', reason: '参加竞赛', status: '已销假'},
  ],
  leaveTypeDistribution: [
    {name: '病假', value: 3},
    {name: '事假', value: 2},
    {name: '公假', value: 2},
  ],
  monthlyTrend: [1, 2, 3, 1, 0],
});

const financialData = ref({
  totalAmount: 12500,
  scholarships: [
    {name: '国家奖学金', amount: 8000, year: '2022'},
    {name: '校级一等奖学金', amount: 3000, year: '2022'},
    {name: '企业奖学金', amount: 1500, year: '2021'},
  ],
  financialAids: [
    {name: '国家助学金', amount: 3000, year: '2022', term: '全年'},
    {name: '临时困难补助', amount: 1000, year: '2021', term: '秋季学期'},
  ],
  applicationStatus: {
    current: '国家助学金申请中',
    progress: 60,
  },
});

const evaluationData = ref([
  {year: '2022-2023', moral: 90, academic: 85, physical: 88, total: 88, rank: 'A'},
  {year: '2021-2022', moral: 88, academic: 82, physical: 85, total: 85, rank: 'B'},
  {year: '2020-2021', moral: 85, academic: 78, physical: 80, total: 81, rank: 'B'},
]);

const warningData = ref([
  {
    type: 'academic',
    level: 'low',
    text: '英语成绩偏低',
    time: '2023-05-10',
    status: '未处理',
    description: '英语四级考试成绩低于专业平均水平，建议加强英语学习',
  },
  {
    type: 'behavior',
    level: 'medium',
    text: '上周缺勤2次',
    time: '2023-06-05',
    status: '处理中',
    description: '最近两周有3次课堂缺勤记录，需要了解具体原因',
  },
]);

const handleProcessWarning = (warning) => {
  console.log('处理预警:', warning);
  const index = warningData.value.findIndex(w => w.text === warning.text);
  if (index !== -1) {
    warningData.value[index].status = '已处理';
  }
};
</script>

<style scoped lang="scss">

.dashboard-page {
  padding: 0 !important;
  box-sizing: border-box;
}

.stat-card {
  border-radius: 12px;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 8px!important;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
  }
}

.stat-content {
  padding: 6px;
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #409EFF;
}

.stat-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-desc {
  font-size: 12px;
  color: #909399;
}

.main-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;

  @media (max-width: 1200px) {
    flex-direction: column;
  }
}

.left-column {
  flex: 1;
  min-width: 0; // 防止flex元素溢出
  display: flex;
  flex-direction: column;

  @media (min-width: 1201px) {
    flex: 3;
  }
}

.right-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;

  @media (min-width: 1201px) {
    flex: 2;
    max-width: calc(40% - 8px);
  }
}

.data-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 8px!important;

  &:deep(.el-card__header) {
    padding: 5px;
  }
  &:deep(.el-card__body) {
    padding: 10px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}


@media (max-width: 768px) {
  .stat-content {
    padding: 12px;
  }

  .stat-value {
    font-size: 24px;
  }
  .data-card:deep(.el-card__body) {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
