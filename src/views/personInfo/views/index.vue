<template>
  <ele-page class="dashboard-page">
    <!-- 学生头部信息 -->
    <StudentHeader
      :currentXgh="xgh"
      :routeType="routeType"
      @process="handleProcessWarning"
    />

    <!-- 统计卡片区域 -->
    <el-row :gutter="8">
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ studentData.gpa }}</div>
            <div class="stat-label">平均绩点</div>
            <div class="stat-desc">
              <el-progress
                :percentage="gpaPercentage"
                :stroke-width="6"
                :show-text="false"
                :color="gpaColor"
              />
              专业排名: {{ studentData.rank }}/{{ studentData.totalStudents }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ studentData.creditProgress }}%</div>
            <div class="stat-label">学分进度</div>
            <div class="stat-desc">
              <el-progress
                :percentage="studentData.creditProgress"
                :stroke-width="6"
                :show-text="false"
                :color="creditColor"
              />
              已修学分/总学分
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ warningData.length }}</div>
            <div class="stat-label">预警信息</div>
            <div class="stat-desc">
              <el-tag
                v-for="(warn, index) in warningTypes"
                :key="index"
                :type="warn.type"
                size="small"
                class="warning-tag"
              >
                {{ warn.count }} {{ warn.name }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ leaveData.totalDays }}</div>
            <div class="stat-label">本学期请假</div>
            <div class="stat-desc">
              <div class="mini-chart">
                <mini-bar-chart :data="leaveMonthlyData"/>
              </div>
              累计天数
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ libraryData.visits }}</div>
            <div class="stat-label">图书馆次数</div>
            <div class="stat-desc">
              <div class="mini-chart">
                <mini-line-chart :data="libraryTrendData"/>
              </div>
              近30天
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="4">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <div class="stat-value">{{ consumptionData.avg }}</div>
            <div class="stat-label">日均消费</div>
            <div class="stat-desc">
              <div class="mini-chart">
                <mini-pie-chart :data="consumptionDistribution"/>
              </div>
              本月平均值
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区 -->
    <div class="main-content">
      <!-- 左栏 -->
      <div class="left-column">
        <!-- 综合测评 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">综合测评</span>
              <div class="card-actions">
                <el-tag type="info">学年评定</el-tag>
                <el-button size="small" type="text">详情</el-button>
              </div>
            </div>
          </template>
          <EvaluationTable :data="evaluationData"/>
        </el-card>

        <!-- 资助与奖学金 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">资助与奖学金</span>
              <div class="card-actions">
                <el-tag type="danger">累计获得</el-tag>
                <el-button size="small" type="text">申请记录</el-button>
              </div>
            </div>
          </template>
          <FinancialAid :data="financialData"/>
        </el-card>
        <!-- 心理咨询记录 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">心理咨询记录</span>
              <div class="card-actions">
                <el-tag type="warning">最近6个月</el-tag>
                <el-button size="small" type="text">全部记录</el-button>
              </div>
            </div>
          </template>
          <PsychologicalCounseling :data="psychologicalData"/>
        </el-card>
        <!-- 新增：活动参与统计 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">活动参与统计</span>
              <div class="card-actions">
                <el-tag type="success">本学期</el-tag>
              </div>
            </div>
          </template>
          <ActivityParticipation :data="activityData"/>
        </el-card>
      </div>

      <!-- 右栏 -->
      <div class="right-column">
        <!-- 学业表现 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">学业表现</span>
              <div class="card-actions">
                <el-select
                  v-model="academicYear"
                  size="small"
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in academicYears"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </div>
            </div>
          </template>
          <AcademicPerformance
            :data="filteredAcademicData"
            :year="academicYear"
          />
        </el-card>
        <!-- 请销假统计 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">请销假记录</span>
              <div class="card-actions">
                <el-tag type="warning">本学期</el-tag>
                <el-button size="small" type="text">导出</el-button>
              </div>
            </div>
          </template>
          <LeaveStatistics :data="leaveData"/>
        </el-card>

        <!-- 行为分析 -->
        <el-card class="data-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">行为分析</span>
              <div class="card-actions">
                <el-tag>近30天数据</el-tag>
                <el-button size="small" type="text">趋势</el-button>
              </div>
            </div>
          </template>
          <BehaviorAnalysis :data="behaviorData"/>
        </el-card>

        <!-- 预警信息 -->
        <WarningCards :warning-data="warningData" @process="handleProcessWarning"/>

      </div>
    </div>
  </ele-page>
</template>

<script setup>
import {ref, computed, unref} from 'vue';
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';
import AcademicPerformance from './components/AcademicPerformance.vue';
import EvaluationTable from './components/EvaluationTable.vue';
import PsychologicalCounseling from '@/views/personInfo/views/components/PsychologicalCounseling.vue';
import LeaveStatistics from './components/LeaveStatistics.vue';
import FinancialAid from './components/FinancialAid.vue';
import BehaviorAnalysis from './components/BehaviorAnalysis.vue';
import WarningCards from './components/WarningCards.vue';
import ActivityParticipation from './components/ActivityParticipation.vue';
import MiniBarChart from './components/charts/MiniBarChart.vue';
import MiniLineChart from './components/charts/MiniLineChart.vue';
import MiniPieChart from './components/charts/MiniPieChart.vue';
import {useRoute, useRouter} from 'vue-router';

const route = useRoute();

const {currentRoute, push} = useRouter();
const {path,} = unref(currentRoute);
let xgh = route.params?.id ?? '';
let routeType = path.split('/')[3];
console.log(routeType, xgh)


// 模拟数据
const studentData = ref({
  id: '20230001',
  name: '张三',
  college: '计算机学院',
  major: '软件工程',
  ni: '2024',
  bjmc: '软件工程一班',
  counselors: [{name: '李老师', phone: '13800138000'}],
  enrollmentDate: '2020年9月',
  gpa: 3.6,
  creditProgress: 68,
  rank: 20,
  totalStudents: 120,
  tags: ['优秀学生', '科技创新', '社团骨干', '志愿者'],
});

// 计算GPA百分比和颜色
const gpaPercentage = computed(() => (studentData.value.gpa / 4) * 100);
const gpaColor = computed(() => {
  const gpa = studentData.value.gpa;
  return gpa >= 3.5 ? '#67C23A' : gpa >= 3.0 ? '#E6A23C' : '#F56C6C';
});

// 计算学分进度颜色
const creditColor = computed(() => {
  const progress = studentData.value.creditProgress;
  return progress >= 80 ? '#67C23A' : progress >= 60 ? '#E6A23C' : '#F56C6C';
});

// 预警类型统计
const warningTypes = computed(() => [
  {name: '学业', type: 'danger', count: warningData.value.filter(w => w.type === 'academic').length},
  {name: '行为', type: 'warning', count: warningData.value.filter(w => w.type === 'behavior').length},
  {name: '心理', type: 'info', count: warningData.value.filter(w => w.type === 'psychological').length},
]);

// 新增图书馆数据
const libraryData = ref({
  visits: 18,
  trend: [12, 15, 10, 18, 14, 16, 18],
});

// 消费数据
const consumptionData = ref({
  avg: 28.5,
  distribution: [
    {name: '食堂', value: 65},
    {name: '超市', value: 20},
    {name: '其他', value: 15},
  ],
});

// 活动数据
const activityData = ref({
  total: 12,
  types: [
    {name: '学术讲座', count: 4},
    {name: '社团活动', count: 5},
    {name: '志愿服务', count: 3},
  ],
  monthly: [1, 2, 3, 2, 1, 3],
});

// 图表数据
const leaveMonthlyData = computed(() => leaveData.value.monthlyTrend.map((val, idx) => ({
  month: `${idx + 1}月`,
  value: val,
})));

const libraryTrendData = computed(() => libraryData.value.trend.map((val, idx) => ({
  day: `第${idx + 1}周`,
  value: val,
})));

const consumptionDistribution = computed(() =>
  consumptionData.value.distribution.map(item => ({
    name: item.name,
    value: item.value,
  })),
);

// 学业数据筛选
const academicYear = ref('2022-2023');
const academicYears = ref(['2020-2021', '2021-2022', '2022-2023']);
const filteredAcademicData = computed(() => ({
  ...academicData.value,
  scoreTrend: academicData.value.scoreTrend.filter(
    item => item.semester.startsWith(academicYear.value.substring(0, 4)) ||
      item.semester.startsWith(academicYear.value.substring(5))),
}));

// 成绩数据筛选
const scoreSemester = ref('2023春');
const scoreSemesters = ref(['2020秋', '2021春', '2021秋', '2022春', '2022秋', '2023春']);
const filteredScoreData = computed(() =>
  academicData.value.coursePerformance,
);

const psychologicalData = ref({
  totalSessions: 8,
  emergencyCases: 1,
  followUpRequired: 3,
  typeDistribution: [
    {value: 3, name: '学业压力'},
    {value: 2, name: '人际关系'},
    {value: 1, name: '情绪困扰'},
    {value: 1, name: '生涯规划'},
    {value: 1, name: '危机干预'},
  ],
  monthlyTrend: [1, 2, 1, 2, 1, 1],
  recentSessions: [
    {
      id: 1,
      date: '2023-06-10',
      consultant: '王心理咨询师',
      type: '学业压力',
      topic: '期末考试焦虑',
      duration: 50,
      status: '已完成',
      urgency: 2,
      summary: '学生表现出对期末考试的过度焦虑，通过认知行为疗法帮助其建立合理预期...',
      followUpPlan: '建议两周后回访评估焦虑水平',
    },
    {
      id: 2,
      date: '2023-05-28',
      consultant: '李心理咨询师',
      type: '人际关系',
      topic: '宿舍矛盾调解',
      duration: 60,
      status: '需跟进',
      urgency: 1,
      summary: '与室友生活习惯差异导致矛盾，已进行沟通技巧训练...',
      followUpPlan: '一个月后评估宿舍关系改善情况',
    },
    {
      id: 3,
      date: '2023-05-15',
      consultant: '张心理咨询师',
      type: '危机干预',
      topic: '情绪低落有自伤倾向',
      duration: 90,
      status: '进行中',
      urgency: 3,
      summary: '近期失恋导致情绪崩溃，已进行危机评估并制定安全计划...',
      followUpPlan: '每周一次咨询，密切监控情绪状态',
    },
  ],
});

const academicData = ref({
  scoreTrend: [
    {semester: '2020秋', gpa: 3.2, rank: 45},
    {semester: '2021春', gpa: 3.4, rank: 32},
    {semester: '2021秋', gpa: 3.5, rank: 28},
    {semester: '2022春', gpa: 3.6, rank: 22},
    {semester: '2022秋', gpa: 3.7, rank: 18},
    {semester: '2023春', gpa: 3.6, rank: 20},
  ],
  coursePerformance: [
    {name: '数据结构', score: 85, average: 72},
    {name: '高等数学', score: 78, average: 65},
    {name: '数据库原理', score: 90, average: 75},
    {name: '算法设计', score: 88, average: 70},
  ],
});

const behaviorData = ref({
  libraryVisits: 18,
  labVisits: 12,
  sportsActivities: 8,
  consumption: {
    dailyAverage: 28.5,
    places: [
      {name: '食堂', percentage: 65},
      {name: '超市', percentage: 20},
      {name: '其他', percentage: 15},
    ],
  },
});

const leaveData = ref({
  totalDays: 7,
  leaveRecords: [
    {type: '病假', days: 3, date: '2023-03-10', reason: '感冒发烧', status: '已销假'},
    {type: '事假', days: 2, date: '2023-04-15', reason: '家庭事务', status: '已销假'},
    {type: '公假', days: 2, date: '2023-05-20', reason: '参加竞赛', status: '已销假'},
  ],
  leaveTypeDistribution: [
    {name: '病假', value: 3},
    {name: '事假', value: 2},
    {name: '公假', value: 2},
  ],
  monthlyTrend: [1, 2, 3, 1, 0],
});

const financialData = ref({
  totalAmount: 12500,
  scholarships: [
    {name: '国家奖学金', amount: 8000, year: '2022'},
    {name: '校级一等奖学金', amount: 3000, year: '2022'},
    {name: '企业奖学金', amount: 1500, year: '2021'},
  ],
  financialAids: [
    {name: '国家助学金', amount: 3000, year: '2022', term: '全年'},
    {name: '临时困难补助', amount: 1000, year: '2021', term: '秋季学期'},
  ],
  applicationStatus: {
    current: '国家助学金申请中',
    progress: 60,
  },
});

const evaluationData = ref([
  {year: '2022-2023', moral: 90, academic: 85, physical: 88, total: 88, rank: 'A'},
  {year: '2021-2022', moral: 88, academic: 82, physical: 85, total: 85, rank: 'B'},
  {year: '2020-2021', moral: 85, academic: 78, physical: 80, total: 81, rank: 'B'},
]);

const warningData = ref([
  {
    type: 'academic',
    level: 'low',
    text: '英语成绩偏低',
    time: '2023-05-10',
    status: '未处理',
    description: '英语四级考试成绩低于专业平均水平，建议加强英语学习',
  },
  {
    type: 'behavior',
    level: 'medium',
    text: '上周缺勤2次',
    time: '2023-06-05',
    status: '处理中',
    description: '最近两周有3次课堂缺勤记录，需要了解具体原因',
  },
]);

const handleProcessWarning = (warning) => {
  console.log('处理预警:', warning);
  const index = warningData.value.findIndex(w => w.text === warning.text);
  if (index !== -1) {
    warningData.value[index].status = '已处理';
  }
};
</script>

<style scoped lang="scss">
.dashboard-page {
  padding: 0 !important;
  box-sizing: border-box;
}

.stat-card {
  border-radius: 12px;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0 !important;
  height: 100%;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
  }
}

.stat-content {
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  height: 100%;

  .stat-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--el-color-primary);
  }

  .stat-label {
    font-size: 16px;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .stat-desc {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: auto;
    display: flex;
    flex-direction: column;
    align-items: center;

    :deep(.el-progress) {
      width: 80%;
      margin-bottom: 5px;
    }

    .warning-tag {
      margin: 2px;
    }

    .mini-chart {
      width: 80px;
      height: 30px;
      margin: 5px auto;
    }
  }
}

.main-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  margin-top: 12px;

  @media (max-width: 1200px) {
    flex-direction: column;
  }
}

.left-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 1201px) {
    flex: 3;
  }
}

.right-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;

  @media (min-width: 1201px) {
    flex: 2;
    max-width: calc(40% - 6px);
  }
}

.data-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0 !important;

  &:deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  &:deep(.el-card__body) {
    padding: 16px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .card-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .stat-content {
    padding: 8px;

    .stat-value {
      font-size: 24px;
    }
  }

  .data-card:deep(.el-card__body) {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
