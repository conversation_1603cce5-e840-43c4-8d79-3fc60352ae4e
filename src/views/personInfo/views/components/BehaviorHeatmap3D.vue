<template>
  <div ref="heatmap3d" class="heatmap-3d-container"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'

const heatmap3d = ref(null)

onMounted(() => {
  const chart = echarts.init(heatmap3d.value)

  // 生成模拟数据
  const hours = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const days = Array.from({ length: 24 }, (_, i) => i + ':00')

  const data = []
  for (let i = 0; i < hours.length; i++) {
    for (let j = 0; j < days.length; j++) {
      data.push([
        i,
        j,
        Math.round(Math.random() * 10)
      ])
    }
  }

  const option = {
    tooltip: {},
    visualMap: {
      max: 10,
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    xAxis3D: {
      type: 'category',
      data: hours,
      name: '星期',
      nameTextStyle: {
        fontSize: 14,
        color: '#666'
      },
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis3D: {
      type: 'category',
      data: days,
      name: '时间',
      nameTextStyle: {
        fontSize: 14,
        color: '#666'
      }
    },
    zAxis3D: {
      type: 'value',
      name: '活动频率',
      nameTextStyle: {
        fontSize: 14,
        color: '#666'
      }
    },
    grid3D: {
      boxWidth: 200,
      boxDepth: 80,
      viewControl: {
        alpha: 35,
        beta: 30,
        distance: 150
      },
      light: {
        main: {
          intensity: 1.2,
          shadow: true,
          shadowQuality: 'high'
        },
        ambient: {
          intensity: 0.3
        }
      }
    },
    series: [{
      type: 'bar3D',
      data: data.map(function (item) {
        return {
          value: [item[1], item[0], item[2]],
          itemStyle: {
            opacity: 0.8
          }
        }
      }),
      shading: 'color',
      label: {
        show: false
      },
      emphasis: {
        label: {
          show: false
        },
        itemStyle: {
          color: '#FF6B6B'
        }
      }
    }]
  }

  chart.setOption(option)

  window.addEventListener('resize', function() {
    chart.resize()
  })
})
</script>

<style scoped>
.heatmap-3d-container {
  width: 100%;
  height: 500px;
}
</style>
