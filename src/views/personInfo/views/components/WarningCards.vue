<template>
  <el-card class="warning-cards" :body-style="{ padding: '10px' }">
    <template #header>
      <div class="card-header">
        <span class="card-title">预警信息</span>
        <div class="card-actions">
          <el-tag type="danger">需关注: {{ warningData.length }}条</el-tag>
        </div>
      </div>
    </template>
    <div class="cards-container">
      <div
        v-for="(warning, index) in warningData"
        :key="index"
        class="warning-card"
        :class="`level-${warning.level}`"
        @click="handleCardClick(warning)"
      >
        <div class="card-header">
          <div class="warning-type">{{ getTypeText(warning.type) }}</div>
          <div class="warning-time">{{ warning.time }}</div>
        </div>
        <div class="card-content">
          <h4>{{ warning.text }}</h4>
          <p>{{ getWarningDescription(warning) }}</p>
        </div>
        <div class="card-footer">
          <el-tag :type="getStatusType(warning.status)" size="small">
            {{ warning.status }}
          </el-tag>
          <el-button
            size="small"
            :type="getActionType(warning.level)"
            @click.stop="handleProcess(warning)"
          >
            立即处理
          </el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  warningData: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['process'])

const typeMap = {
  academic: '学业预警',
  behavior: '行为预警',
  financial: '经济预警',
  psychological: '心理预警',
  other: '其他预警'
}

const levelMap = {
  high: '高级',
  medium: '中级',
  low: '低级'
}

const getTypeText = (type) => {
  return typeMap[type] || type
}

const getLevelText = (level) => {
  return levelMap[level] || level
}

const getStatusType = (status) => {
  const map = {
    '未处理': 'danger',
    '处理中': 'warning',
    '已处理': 'success'
  }
  return map[status] || 'info'
}

const getActionType = (level) => {
  const map = {
    high: 'danger',
    medium: 'warning',
    low: 'primary'
  }
  return map[level] || ''
}

const getWarningDescription = (warning) => {
  const descriptions = {
    academic: {
      high: '该生有多门课程不及格，存在严重学业风险。',
      medium: '该生部分课程成绩偏低，存在学业风险。',
      low: '该生个别课程表现不佳，建议关注。'
    },
    behavior: {
      high: '该生存在严重违纪行为，需立即处理。',
      medium: '该生行为出现异常，建议谈话。',
      low: '该生行为有轻微异常，建议关注。'
    },
    financial: {
      high: '该生消费异常，可能存在经济困难。',
      medium: '该生消费模式有显著变化。',
      low: '该生消费略有异常。'
    }
  }

  return descriptions[warning.type]?.[warning.level] ||
    `${getLevelText(warning.level)}级别${getTypeText(warning.type)}`
}

const handleCardClick = (warning) => {
  console.log('查看预警详情:', warning)
  // 可以在这里实现查看详情的逻辑
}

const handleProcess = (warning) => {
  ElMessage.success(`正在处理预警: ${warning.text}`)
  emit('process', warning)
}
</script>

<style scoped>
.warning-cards {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0 !important;
  &:deep(.el-card__header) {
    padding: 12px 16px 0;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  &:deep(.el-card__body) {
    padding: 16px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .card-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}


.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.warning-card {
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.warning-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
}

.warning-type {
  font-weight: bold;
}

.warning-time {
  color: #909399;
}

.card-content h4 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #303133;
}

.card-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

/* 不同级别卡片样式 */
.level-high {
  border-left-color: #F56C6C;
  background-color: #fff2f0;
}

.level-medium {
  border-left-color: #E6A23C;
  background-color: #fff7e6;
}

.level-low {
  border-left-color: #409EFF;
  background-color: #f0f9ff;
}
</style>
