<template>
  <v-chart class="mini-pie-chart" :option="chartOption" autoresize />
</template>

<script setup>
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import { TitleComponent } from 'echarts/components';
import VChart from 'vue-echarts';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent
]);

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#409EFF', '#67C23A', '#E6A23C']
  }
});

const chartOption = computed(() => {
  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['60%', '80%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false
        },
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: props.data.map((item, index) => ({
          ...item,
          itemStyle: {
            color: props.colors[index % props.colors.length]
          }
        }))
      }
    ]
  };
});
</script>

<style scoped>
.mini-pie-chart {
  width: 100%;
  height: 100%;
}
</style>
