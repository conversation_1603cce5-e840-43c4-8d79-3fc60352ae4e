<template>
  <v-chart class="mini-line-chart" :option="chartOption" autoresize />
</template>

<script setup>
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import { GridComponent } from 'echarts/components';
import VChart from 'vue-echarts';

use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  LineChart,
  GridComponent
]);

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  color: {
    type: String,
    default: '#67C23A'
  }
});

const chartOption = computed(() => {
  return {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.map(item => item.day),
      show: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: props.data.map(item => item.value),
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: props.color
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: props.color + '80'
            }, {
              offset: 1,
              color: props.color + '00'
            }]
          }
        }
      }
    ]
  };
});
</script>

<style scoped>
.mini-line-chart {
  width: 100%;
  height: 100%;
}
</style>
