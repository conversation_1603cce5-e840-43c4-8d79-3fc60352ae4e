<template>
  <v-chart class="mini-bar-chart" :option="chartOption" autoresize />
</template>

<script setup>
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { Bar<PERSON>hart } from 'echarts/charts';
import { GridComponent } from 'echarts/components';
import VChart from 'vue-echarts';

use([
  <PERSON>vas<PERSON><PERSON><PERSON>,
  BarChart,
  GridComponent
]);

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  color: {
    type: String,
    default: '#409EFF'
  }
});

const chartOption = computed(() => {
  return {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.month || item.day),
      show: false
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: props.data.map(item => item.value),
        type: 'bar',
        barWidth: '60%',
        itemStyle: {
          color: props.color,
          borderRadius: [2, 2, 0, 0]
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)',
          borderRadius: [2, 2, 0, 0]
        }
      }
    ]
  };
});
</script>

<style scoped>
.mini-bar-chart {
  width: 100%;
  height: 100%;
}
</style>
