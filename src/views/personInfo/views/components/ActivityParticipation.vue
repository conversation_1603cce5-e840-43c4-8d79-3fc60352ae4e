<template>
  <div class="activity-participation">
    <div class="summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-item">
            <div class="value">{{ data.total }}</div>
            <div class="label">总参与次数</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <div class="value">{{ mostActiveType }}</div>
            <div class="label">最活跃类型</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <div class="value">{{ avgPerMonth }}</div>
            <div class="label">月均参与</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="chart-container">
      <v-chart class="chart" :option="activityOption" autoresize />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';

use([
  CanvasRenderer,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});

const mostActiveType = computed(() => {
  if (!props.data.types || props.data.types.length === 0) return '-';
  const max = Math.max(...props.data.types.map(item => item.count));
  return props.data.types.find(item => item.count === max).name;
});

const avgPerMonth = computed(() => {
  if (!props.data.monthly || props.data.monthly.length === 0) return 0;
  const sum = props.data.monthly.reduce((a, b) => a + b, 0);
  return (sum / props.data.monthly.length).toFixed(1);
});

const activityOption = computed(() => {
  return {
    title: {
      text: '活动参与情况',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['参与次数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.types ? props.data.types.map(item => item.name) : [],
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '参与次数'
    },
    series: [
      {
        name: '参与次数',
        type: 'bar',
        data: props.data.types ? props.data.types.map(item => item.count) : [],
        itemStyle: {
          color: function(params) {
            const colorList = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'];
            return colorList[params.dataIndex % colorList.length];
          },
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  };
});
</script>

<style scoped>
.activity-participation {
  width: 100%;
  height: 100%;
}

.summary {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--el-bg-color-page);
}

.summary-item .value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 5px;
}

.summary-item .label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.chart-container {
  height: 250px;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
