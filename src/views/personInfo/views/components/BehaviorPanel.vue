<template>
  <div class="behavior-panel">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover">
          <div ref="libraryChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div ref="consumptionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-divider/>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover">
          <div ref="bookChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div class="dormitory-info">
            <h3>宿舍表现</h3>
            <div class="dormitory-item">
              <span>卫生平均分：</span>
              <el-progress
                :percentage="avgHygiene"
                :color="hygieneColor"
                :format="formatHygiene"
              />
            </div>
            <div class="dormitory-item">
              <span>晚归次数：</span>
              <span class="value">{{ data.dormitory.lateReturns }}次</span>
            </div>
            <div class="dormitory-item">
              <span>违规记录：</span>
              <span class="value">{{ data.dormitory.violations }}次</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue';
import {initLibraryChart, initConsumptionChart, initBookChart} from '../utils/charts';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const libraryChart = ref(null);
const consumptionChart = ref(null);
const bookChart = ref(null);

const avgHygiene = computed(() => {
  if (props.data) {
    const scores = props.data?.dormitory.hygieneScores;
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  } else {
    return 0;
  }
});

const hygieneColor = computed(() => {
  return avgHygiene.value >= 90 ? 'var(--el-color-success)'
    : avgHygiene.value >= 80 ? 'var(--el-color-warning)'
      : 'var(--el-color-danger)';
});

const formatHygiene = () => {
  return `${avgHygiene.value}分`;
};

onMounted(() => {
  initLibraryChart(libraryChart.value, props.data.library.monthlyVisits);
  initConsumptionChart(consumptionChart.value, props.data.consumption.monthlyTotal);
  initBookChart(bookChart.value, props.data.library.bookCategories);
});
</script>

<style scoped>
.behavior-panel {
  padding: 10px;
}

.dormitory-info {
  padding: 20px;
}

.dormitory-info h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--el-text-color-regular);
}

.dormitory-item {
  margin-bottom: 20px;
}

.dormitory-item .value {
  margin-left: 10px;
  color: var(--el-text-color-primary);
}
</style>
