<template>
  <div class="academic-performance">
    <div class="tabs">
      <el-radio-group v-model="activeTab">
        <el-radio-button label="scoreTrend">成绩趋势</el-radio-button>
        <el-radio-button label="courseCompare">课程对比</el-radio-button>
      </el-radio-group>
    </div>

    <div class="chart-container">
      <div v-if="activeTab === 'scoreTrend'" ref="scoreChart" class="chart" style="width: 100%; height: 100%"></div>
      <div v-else ref="courseChart" class="chart" style="width: 100%; height: 100%"></div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, onBeforeUnmount} from 'vue';
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const activeTab = ref('scoreTrend')
const scoreChart = ref(null)
const courseChart = ref(null)
let scoreChartInstance = null
let courseChartInstance = null

// 确保DOM已渲染
const initCharts = async () => {
  await nextTick()

  if (activeTab.value === 'scoreTrend' && scoreChart.value) {
    if (scoreChartInstance) {
      scoreChartInstance.dispose()
    }
    scoreChartInstance = echarts.init(scoreChart.value)
    renderScoreChart()
  } else if (courseChart.value) {
    if (courseChartInstance) {
      courseChartInstance.dispose()
    }
    courseChartInstance = echarts.init(courseChart.value)
    renderCourseChart()
  }
}

onMounted(() => {
  initCharts()
})

watch(activeTab, (newVal) => {
  initCharts()
})

const renderScoreChart = () => {
  if (!scoreChartInstance) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.scoreTrend.map(item => item.semester),
      axisLabel: {
        rotate: 30
      }
    },
    yAxis: [
      {
        type: 'value',
        name: 'GPA',
        min: 2,
        max: 4,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '排名',
        min: 0,
        max: Math.max(...props.data.scoreTrend.map(item => item.rank)) + 5,
        inverse: true,
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: 'GPA',
        type: 'line',
        smooth: true,
        data: props.data.scoreTrend.map(item => item.gpa),
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      },
      {
        name: '排名',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        data: props.data.scoreTrend.map(item => item.rank),
        lineStyle: {
          width: 3,
          color: '#67C23A'
        },
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }

  scoreChartInstance.setOption(option)
}

const renderCourseChart = () => {
  if (!courseChartInstance) return

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.coursePerformance.map(item => item.name),
      axisLabel: {
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '分数',
      min: 50,
      max: 100
    },
    series: [
      {
        name: '学生成绩',
        type: 'bar',
        barWidth: '30%',
        data: props.data.coursePerformance.map(item => item.score),
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '班级平均',
        type: 'bar',
        barWidth: '30%',
        data: props.data.coursePerformance.map(item => item.average),
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }

  courseChartInstance.setOption(option)
}

// 组件卸载时清理
onBeforeUnmount(() => {
  if (scoreChartInstance) {
    scoreChartInstance.dispose()
  }
  if (courseChartInstance) {
    courseChartInstance.dispose()
  }
})
</script>

<style scoped>
.academic-performance {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabs {
  margin-bottom: 20px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.chart {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
