<template>
  <div ref="radar3d" class="radar-3d-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
// 必须单独引入 echarts-gl
import 'echarts-gl'

const radar3d = ref(null)
let chart = null

onMounted(() => {
  // 1. 检查是否支持3D渲染
  if (!echarts.graphic || !echarts.graphic.extendShape) {
    console.error('ECharts GL not loaded!')
    return
  }

  // 2. 初始化图表
  chart = echarts.init(radar3d.value)

  // 3. 配置项
  const option = {
    // 必须包含 grid3D 配置
    grid3D: {
      viewControl: {
        autoRotate: true,
        autoRotateSpeed: 10
      }
    },
    xAxis3D: {
      type: 'value'
    },
    yAxis3D: {
      type: 'value'
    },
    zAxis3D: {
      type: 'value'
    },
    radar3D: {
      indicator: [
        {name: '学业能力', max: 100},
        {name: '实践能力', max: 100},
        {name: '社交能力', max: 100},
        {name: '创新能力', max: 100},
        {name: '领导能力', max: 100}
      ]
    },
    series: [
      {
        type: 'radar3D',
        data: [{value: [85, 72, 68, 79, 65]}]
      }]
  }

  chart.setOption(option)

  // 4. 响应式调整
  const handleResize = () => chart.resize()
  window.addEventListener('resize', handleResize)

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    chart.dispose()
  })
})
</script>

<style scoped>
.radar-3d-container {
  width: 100%;
  height: 500px;
}
</style>
