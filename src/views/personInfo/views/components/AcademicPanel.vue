<template>
  <div class="academic-panel">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card shadow="hover">
          <div class="academic-card">
            <div class="value">{{ data.gpa }}</div>
            <div class="label">平均绩点</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <div class="academic-card">
            <div class="value">{{ data.creditCompleted }}/{{ data.creditRequired }}</div>
            <div class="label">学分完成情况</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <div class="academic-card">
            <div class="value">{{ completionRate }}%</div>
            <div class="label">学分完成率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-divider />

    <el-row :gutter="20">
      <el-col :span="12">
        <el-card shadow="hover">
          <div ref="scoreChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div ref="courseChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { initScoreChart, initCourseChart } from '../utils/charts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const scoreChart = ref(null)
const courseChart = ref(null)

const completionRate = computed(() => {
  return Math.round((props.data.creditCompleted / props.data.creditRequired) * 100)
})

onMounted(() => {
  initScoreChart(scoreChart.value, props.data.scoreTrend)
  initCourseChart(courseChart.value, props.data.coursePerformance)
})
</script>

<style scoped>
.academic-panel {
  padding: 10px;
}

.academic-card {
  text-align: center;
  padding: 20px 0;
}

.academic-card .value {
  font-size: 28px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 8px;
}

.academic-card .label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}
</style>
