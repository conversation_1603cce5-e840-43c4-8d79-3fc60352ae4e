<template>
  <div class="financial-aid">
    <div class="summary-card">
      <div class="summary-value">¥{{ data.totalAmount }}</div>
      <div class="summary-label">累计获得资助与奖学金</div>
    </div>

    <el-tabs v-model="activeTab" class="financial-tabs">
      <el-tab-pane label="奖学金" name="scholarship">
        <ele-pro-table ref="tableRef"
                       row-key="id"
                       :columns="columns"
                       :datasource="datasource1"
                       :border="true"
                       :show-overflow-tooltip="true"
                       :pagination="false"
                       :toolbar="false"
                       tooltip-effect="light"
                       highlight-current-row
                       cache-key="systemUserTable"
                       :footer-style="{ paddingBottom: '3px' }"
                       style="padding-bottom: 0">
          <template #amount="{ row }">
            ¥{{ row.amount }}
          </template>
          <template #action="{ row }">
            <el-link type="primary" underline="never">
              详情
            </el-link>
          </template>
        </ele-pro-table>
      </el-tab-pane>

      <el-tab-pane label="助学金" name="financialAid">
        <ele-pro-table ref="tableRef"
                       row-key="id"
                       :columns="columns"
                       :datasource="datasource2"
                       :border="true"
                       :show-overflow-tooltip="true"
                       :pagination="false"
                       :toolbar="false"
                       tooltip-effect="light"
                       highlight-current-row
                       cache-key="systemUserTable"
                       :footer-style="{ paddingBottom: '3px' }"
                       style="padding-bottom: 0">
          <template #amount="{ row }">
            ¥{{ row.amount }}
          </template>
          <template #action="{ row }">
            <el-link type="primary" underline="never">
              详情
            </el-link>
          </template>
        </ele-pro-table>
      </el-tab-pane>
    </el-tabs>

    <div class="application-status" v-if="data.applicationStatus">
      <div class="status-header">
        <span>当前申请状态: {{ data.applicationStatus.current }}</span>
        <el-tag size="small">{{ data.applicationStatus.progress }}%</el-tag>
      </div>
      <el-progress
        :percentage="data.applicationStatus.progress"
        :stroke-width="12"
        :color="customColors"
      />
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {Bottom, Top} from '@element-plus/icons-vue';

const activeTab = ref('scholarship');

const customColors = [
  {color: '#f56c6c', percentage: 20},
  {color: '#e6a23c', percentage: 40},
  {color: '#5cb87a', percentage: 60},
  {color: '#1989fa', percentage: 80},
  {color: '#6f7ad3', percentage: 100},
];

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

/** 表格列配置 */
const columns = ref([
  {
    prop: 'name',
    label: '名称',
  },
  {
    prop: 'amount',
    slot: 'amount',
    label: '金额',
  },
  {
    prop: 'year',
    label: '年度',
  },
  // {
  //   prop: 'term',
  //   label: '学期',
  // },
  {
    columnKey: 'action',
    label: '操作',
    align: 'center',
    minWidth: 20,
    slot: 'action'
  }
]);

const datasource1 = async ({pages, filter}) => {
  return {
    count: props.data.scholarships.length,
    list: props.data.scholarships,
  };
};
const datasource2 = async ({pages, filter}) => {
  return {
    count: props.data.financialAids.length,
    list: props.data.financialAids,
  };
};
</script>

<style scoped>
.financial-aid {
  padding: 10px;
}

.summary-card {
  background: #f0f7ff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
}

.summary-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
}

.financial-tabs {
  margin-top: 15px;
}

.application-status {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #606266;
}
</style>
