<template>
  <div class="behavior-analysis">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Reading /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ data.libraryVisits }}</div>
          <div class="stat-label" :title="data.libraryVisits > 1000 ? '图书馆访问次数' : '图书馆访问'">
            {{ data.libraryVisits > 1000 ? '图书馆访问次数' : '图书馆访问' }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Cpu /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ data.labVisits }}</div>
          <div class="stat-label" title="实验室访问次数">实验室访问</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Basketball /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ data.sportsActivities }}</div>
          <div class="stat-label" title="体育活动参与次数">体育活动</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ data.consumption.dailyAverage }}</div>
          <div class="stat-label" title="日均消费金额">日均消费</div>
        </div>
      </div>
    </div>

    <div class="chart-container">
      <div class="chart-row">
        <div class="chart-wrapper">
          <div ref="consumptionChart" class="chart"></div>
        </div>
        <div class="chart-wrapper">
          <div ref="timeDistributionChart" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'
import {
  Reading,
  Cpu,
  Basketball,
  Money
} from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const consumptionChart = ref(null)
const timeDistributionChart = ref(null)

onMounted(() => {
  renderConsumptionChart()
  renderTimeDistributionChart()
})

const renderConsumptionChart = () => {
  const chart = echarts.init(consumptionChart.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    series: [
      {
        name: '消费分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.consumption.places.map(item => ({
          value: item.percentage,
          name: item.name
        }))
      }
    ]
  }

  chart.setOption(option)
}

const renderTimeDistributionChart = () => {
  const chart = echarts.init(timeDistributionChart.value)

  // 模拟时间段数据
  const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`)
  const activities = hours.map((_, i) => {
    return Math.round(Math.random() * 20 + (i > 8 && i < 18 ? 30 : 10))
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLabel: {
        interval: 3,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '活动频率'
    },
    series: [
      {
        name: '活动分布',
        type: 'bar',
        barWidth: '60%',
        data: activities,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }
    ]
  }

  chart.setOption(option)
}
</script>

<style scoped>
.behavior-analysis {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 10px;
  margin-bottom: 0;
}

.stat-card {
  background: #ffffff;
  border-radius: 10px;
  padding: 18px 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  min-height: 60px;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  border-color: #d9e7fd;
}

.stat-icon {
  width: 44px;
  height: 44px;
  background: #f0f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #1890ff;
  font-size: 20px;
  flex-shrink: 0;
}

.stat-content {
  min-width: 0; /* 防止flex布局挤压 */
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
  white-space: nowrap;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-row {
  display: flex;
  flex: 1;
  gap: 16px;
  min-height: 300px;
}

.chart-wrapper {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #ebeef5;
  min-width: 0; /* 防止flex布局挤压 */
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .chart-row {
    flex-direction: column;
  }

  .stat-card {
    padding: 14px 16px;
    min-height: 80px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-label {
    font-size: 13px;
  }
}
</style>
