<template>
  <div class="evaluation-table">

    <ele-pro-table ref="tableRef"
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   :pagination="false"
                   :toolbar="false"
                   :show-overflow-tooltip="true"
                   tooltip-effect="light"
                   highlight-current-row
                   cache-key="systemUserTable"
                   :footer-style="{ paddingBottom: '3px' }"
                   style="padding-bottom: 0">
      <template #moral="{ row }">
        <el-progress
          :percentage="row.moral"
          :color="getScoreColor(row.moral)"
          :show-text="false"
        />
        <span>{{ row.moral }}</span>
      </template>
      <template #academic="{ row }">
        <el-progress
          :percentage="row.academic"
          :color="getScoreColor(row.academic)"
          :show-text="false"
        />
        <span>{{ row.academic }}</span>
      </template>
      <template #physical="{ row }">
        <el-progress
          :percentage="row.physical"
          :color="getScoreColor(row.physical)"
          :show-text="false"
        />
        <span>{{ row.physical }}</span>
      </template>
      <template #total="{ row }">
        <el-tag :type="getRankType(row.rank)">{{ row.total }}</el-tag>
      </template>
      <template #rank="{ row }">
        <el-tag :type="getRankType(row.rank)" effect="dark">{{ row.rank }}</el-tag>
      </template>
      <template #qs="{ row , $index}">
        <el-icon v-if="$index > 0 && row.total > data[$index-1].total" color="#67C23A">
          <Top/>
        </el-icon>
        <el-icon v-else-if="$index > 0 && row.total < data[$index-1].total" color="#F56C6C">
          <Bottom/>
        </el-icon>
        <span v-else>-</span>
      </template>
    </ele-pro-table>
    <div class="evaluation-chart">
      <div ref="chart" class="chart"></div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import * as echarts from 'echarts';
import {Top, Bottom} from '@element-plus/icons-vue';

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
});

const chart = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'year',
    label: '学年',
  },
  {
    prop: 'moral',
    slot: 'moral',
    label: '德育',
  },
  {
    prop: 'academic',
    slot: 'academic',
    label: '智育',
  },
  {
    prop: 'physical',
    slot: 'physical',
    label: '体育',
  },
  {
    prop: 'total',
    slot: 'total',
    label: '总分',
  },
  {
    prop: 'rank',
    slot: 'rank',
    label: '等级',
  },
  {
    prop: 'qs',
    slot: 'qs',
    label: '趋势',
  },
]);

const datasource = async ({pages, filter}) => {
  return {
    count: props.data.length,
    list: props.data,
  };
};

const getScoreColor = (score) => {
  if (score >= 90) return '#67C23A';
  if (score >= 80) return '#409EFF';
  if (score >= 70) return '#E6A23C';
  return '#F56C6C';
};

const getRankType = (rank) => {
  const map = {
    'A': 'success',
    'B': 'primary',
    'C': 'warning',
    'D': 'danger',
  };
  return map[rank] || '';
};

const renderChart = () => {
  if (!chart.value) return;

  const chartInstance = echarts.init(chart.value);
  const option = {
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: ['德育', '智育', '体育', '总分'],
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.year),
    },
    yAxis: {
      type: 'value',
      min: 60,
      max: 100,
    },
    series: [
      {
        name: '德育',
        type: 'line',
        data: props.data.map(item => item.moral),
      },
      {
        name: '智育',
        type: 'line',
        data: props.data.map(item => item.academic),
      },
      {
        name: '体育',
        type: 'line',
        data: props.data.map(item => item.physical),
      },
      {
        name: '总分',
        type: 'line',
        data: props.data.map(item => item.total),
        lineStyle: {
          width: 3,
        },
        symbolSize: 8,
        label: {
          show: true,
          position: 'top',
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

onMounted(() => {
  renderChart();
});

watch(() => props.data, () => {
  renderChart();
}, {deep: true});
</script>

<style scoped>
.evaluation-table {
  padding: 10px;
}

.evaluation-chart {
  margin-top: 20px;
  height: 300px;
}

.chart {
  height: 100%;
  width: 100%;
}

.el-progress {
  display: inline-block;
  width: 60px;
  margin-right: 10px;
}
</style>
