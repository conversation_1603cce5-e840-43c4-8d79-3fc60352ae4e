<!--个人档案卡片-->
<template>
  <div class="profile-card">
    <div class="profile-banner" :style="{ backgroundImage: `url(${bannerUrl})` }">
      <div class="profile-avatar">
        <el-avatar :size="100" :src="avatarUrl" />
      </div>
    </div>

    <div class="profile-content">
      <h3>{{ data.name }}</h3>
      <p class="college">{{ data.college }} · {{ data.major }}</p>

      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-value">{{ data.gpa }}</div>
          <div class="stat-label">平均绩点</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ data.creditCompleted }}/{{ data.creditRequired }}</div>
          <div class="stat-label">学分进度</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ warningCount }}</div>
          <div class="stat-label">预警数量</div>
        </div>
      </div>

      <div class="progress-container">
        <div class="progress-label">学业完成度</div>
        <el-progress
          :percentage="completionRate"
          :stroke-width="12"
          :color="completionColor"
          striped
          striped-flow
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  warningCount: {
    type: Number,
    default: 0
  }
})

const bannerUrl = 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
const avatarUrl = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

const completionRate = computed(() => {
  return Math.round((props.data.creditCompleted / props.data.creditRequired) * 100)
})

const completionColor = computed(() => {
  if (completionRate.value >= 80) return '#67C23A'
  if (completionRate.value >= 60) return '#E6A23C'
  return '#F56C6C'
})
</script>

<style scoped>
.profile-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: white;
  transition: transform 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-5px);
}

.profile-banner {
  height: 120px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.profile-avatar {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid white;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-content {
  padding: 60px 20px 20px;
  text-align: center;
}

.profile-content h3 {
  margin: 0;
  font-size: 22px;
  color: #303133;
}

.profile-content .college {
  margin: 5px 0 20px;
  color: #909399;
  font-size: 14px;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}

.stat-item {
  padding: 0 10px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.progress-container {
  margin-top: 20px;
}

.progress-label {
  text-align: left;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
</style>
