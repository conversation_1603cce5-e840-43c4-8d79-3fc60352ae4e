<template>
  <div>
    <el-row :gutter="16">
      <el-col :span="12">
        <div class="chart-container">
          <h4>请假类型分布</h4>
          <div ref="typeChart" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <h4>月度请假趋势</h4>
          <div ref="trendChart" class="chart"></div>
        </div>
      </el-col>
    </el-row>

    <el-table :data="data.leaveRecords" style="width: 100%; margin-top: 20px" border>
      <el-table-column prop="date" label="日期" width="120"/>
      <el-table-column prop="type" label="类型" width="100">
        <template #default="{row}">
          <el-tag :type="getLeaveTypeTag(row.type)">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="days" label="天数" width="80"/>
      <el-table-column prop="reason" label="原因"/>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{row}">
          <el-tag :type="row.status === '已销假' ? 'success' : 'warning'">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const typeChart = ref(null);
const trendChart = ref(null);

const getLeaveTypeTag = (type) => {
  const map = {
    '病假': 'danger',
    '事假': 'warning',
    '公假': 'primary',
  };
  return map[type] || '';
};

const renderTypeChart = () => {
  const chart = echarts.init(typeChart.value);
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}天 ({d}%)',
    },
    series: [
      {
        name: '请假类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: props.data.leaveTypeDistribution,
      },
    ],
  };
  chart.setOption(option);
};

const renderTrendChart = () => {
  const chart = echarts.init(trendChart.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月'],
    },
    yAxis: {
      type: 'value',
      name: '请假天数',
    },
    series: [
      {
        name: '请假天数',
        type: 'bar',
        data: props.data.monthlyTrend,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {offset: 0, color: '#83bff6'},
            {offset: 0.5, color: '#188df0'},
            {offset: 1, color: '#188df0'},
          ]),
        },
      },
    ],
  };
  chart.setOption(option);
};

onMounted(() => {
  renderTypeChart();
  renderTrendChart();
});

watch(() => props.data, () => {
  renderTypeChart();
  renderTrendChart();
}, {deep: true});
</script>

<style scoped>

.chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  height: 250px;
}

.chart-container h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #606266;
}

.chart {
  height: 200px;
  width: 100%;
}
</style>
