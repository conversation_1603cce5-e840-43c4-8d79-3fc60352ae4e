<template>
  <div>
    <div ref="radarChart" class="radar-chart"></div>
    <div class="radar-legend">
      <div class="legend-item" v-for="(item, index) in legendData" :key="index">
        <div class="legend-marker" :style="{ backgroundColor: item.color }"></div>
        <div class="legend-label">{{ item.name }} ({{ item.value }}%)</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as echarts from 'echarts'

const radarChart = ref(null)

const legendData = ref([
  { name: '学业能力', value: 85, color: '#5470C6' },
  { name: '实践能力', value: 72, color: '#91CC75' },
  { name: '社交能力', value: 68, color: '#FAC858' },
  { name: '创新能力', value: 79, color: '#EE6666' },
  { name: '领导能力', value: 65, color: '#73C0DE' }
])

onMounted(() => {
  const chart = echarts.init(radarChart.value)

  const option = {
    tooltip: {
      trigger: 'item'
    },
    radar: {
      radius: '65%',
      center: ['50%', '50%'],
      indicator: legendData.value.map(item => ({
        name: item.name,
        max: 100
      })),
      axisName: {
        color: '#666',
        fontSize: 12
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(255, 255, 255, 0.5)'],
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: 10
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    series: [{
      type: 'radar',
      data: [{
        value: legendData.value.map(item => item.value),
        name: '能力评估',
        areaStyle: {
          color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [{
            offset: 0,
            color: 'rgba(64, 158, 255, 0.8)'
          }, {
            offset: 1,
            color: 'rgba(64, 158, 255, 0.1)'
          }])
        },
        lineStyle: {
          width: 2,
          color: '#409EFF'
        },
        symbolSize: 6,
        label: {
          show: true,
          formatter: function(params) {
            return params.value + '%'
          },
          color: '#fff',
          backgroundColor: '#409EFF',
          borderRadius: 3,
          padding: [3, 5]
        }
      }]
    }]
  }

  chart.setOption(option)

  window.addEventListener('resize', function() {
    chart.resize()
  })
})
</script>

<style scoped>
.radar-chart {
  width: 100%;
  height: 400px;
}

.radar-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-marker {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.legend-label {
  font-size: 14px;
  color: #606266;
}
</style>
