<template>
  <div class="academic-timeline">
    <h3 class="timeline-title">学业发展轨迹</h3>
    <div class="timeline-container">
      <div
        v-for="(item, index) in data"
        :key="index"
        class="timeline-item"
        :class="{ 'active': index === activeIndex }"
        @mouseenter="activeIndex = index"
      >
        <div class="timeline-dot"></div>
        <div class="timeline-content">
          <div class="timeline-time">{{ item.semester }}</div>
          <div class="timeline-gpa">
            <span>GPA: </span>
            <el-tag :type="getGpaType(item.gpa)" size="small">
              {{ item.gpa }}
            </el-tag>
          </div>
          <div class="timeline-courses" v-if="index === activeIndex">
            <div
              v-for="(course, cIndex) in getCourses(item.semester)"
              :key="cIndex"
              class="course-item"
            >
              <span class="course-name">{{ course.name }}</span>
              <el-progress
                :percentage="course.score"
                :stroke-width="8"
                :color="getScoreColor(course.score)"
                :show-text="false"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  courses: {
    type: Array,
    default: () => []
  }
})

const activeIndex = ref(0)

const getGpaType = (gpa) => {
  if (gpa >= 3.5) return 'success'
  if (gpa >= 3.0) return 'warning'
  return 'danger'
}

const getCourses = (semester) => {
  return props.courses.filter(c => c.semester === semester).slice(0, 3)
}

const getScoreColor = (score) => {
  if (score >= 85) return '#67C23A'
  if (score >= 60) return '#E6A23C'
  return '#F56C6C'
}
</script>

<style scoped>
.academic-timeline {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.timeline-title {
  margin: 0 0 20px;
  color: #303133;
  font-size: 18px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.timeline-container {
  position: relative;
  padding-left: 30px;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #EBEEF5;
}

.timeline-item {
  position: relative;
  padding-bottom: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-item.active {
  transform: translateX(5px);
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -30px;
  top: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #409EFF;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
}

.timeline-content {
  background: #F5F7FA;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.timeline-item.active .timeline-content {
  background: white;
  box-shadow: 0 5px 15px rgba(64, 158, 255, 0.1);
  transform: scale(1.02);
}

.timeline-time {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.timeline-gpa {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.timeline-courses {
  margin-top: 15px;
  border-top: 1px dashed #DCDFE6;
  padding-top: 15px;
}

.course-item {
  margin-bottom: 10px;
}

.course-item:last-child {
  margin-bottom: 0;
}

.course-name {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}
</style>
