<template>
  <div class="psychological-counseling">
    <el-row :gutter="16" class="stats-row">
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-value">{{ data.totalSessions }}</div>
          <div class="stat-label">咨询总次数</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-value">{{ data.emergencyCases }}</div>
          <div class="stat-label">紧急干预</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="stat-card">
          <div class="stat-value">{{ data.followUpRequired }}</div>
          <div class="stat-label">需跟进</div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="16" class="chart-row">
      <el-col :span="12">
        <div class="chart-container">
          <h4>咨询类型分布</h4>
          <div ref="typeChart" class="chart"></div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-container">
          <h4>月度咨询趋势</h4>
          <div ref="trendChart" class="chart"></div>
        </div>
      </el-col>
    </el-row>

    <el-table
      :data="data.recentSessions"
      style="width: 100%"
      border
      class="session-table"
    >
      <el-table-column prop="date" label="日期" width="120" />
      <el-table-column prop="consultant" label="咨询师" width="120" />
      <el-table-column prop="type" label="类型" width="120">
        <template #default="{row}">
          <el-tag :type="getTypeTag(row.type)" size="small">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="topic" label="主要议题" />
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{row}">
          <el-tag :type="getStatusTag(row.status)" size="small">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template #default="{row}">
          <el-button type="text" size="small" @click="viewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 咨询详情对话框 -->
    <el-dialog v-model="detailVisible" title="心理咨询详情" width="50%">
      <div v-if="currentSession" class="session-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="咨询日期">{{ currentSession.date }}</el-descriptions-item>
          <el-descriptions-item label="咨询时长">{{ currentSession.duration }}分钟</el-descriptions-item>
          <el-descriptions-item label="咨询师">{{ currentSession.consultant }}</el-descriptions-item>
          <el-descriptions-item label="咨询类型">{{ currentSession.type }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">{{ currentSession.status }}</el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-rate
              v-model="currentSession.urgency"
              disabled
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            />
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>主要议题</h4>
          <p>{{ currentSession.topic }}</p>
        </div>

        <div class="detail-section">
          <h4>咨询摘要</h4>
          <p>{{ currentSession.summary }}</p>
        </div>

        <div class="detail-section" v-if="currentSession.followUpPlan">
          <h4>跟进计划</h4>
          <p>{{ currentSession.followUpPlan }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const typeChart = ref(null)
const trendChart = ref(null)
const detailVisible = ref(false)
const currentSession = ref(null)

const getTypeTag = (type) => {
  const map = {
    '学业压力': 'primary',
    '人际关系': 'success',
    '情绪困扰': 'warning',
    '危机干预': 'danger',
    '生涯规划': 'info'
  }
  return map[type] || ''
}

const getStatusTag = (status) => {
  const map = {
    '已完成': 'success',
    '进行中': 'primary',
    '已取消': 'info',
    '需跟进': 'warning',
    '紧急': 'danger'
  }
  return map[status] || ''
}

const viewDetail = (session) => {
  currentSession.value = session
  detailVisible.value = true
}

const renderTypeChart = () => {
  const chart = echarts.init(typeChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}次 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [
      {
        name: '咨询类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        data: props.data.typeDistribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        itemStyle: {
          borderRadius: 5,
          borderColor: '#fff',
          borderWidth: 2
        }
      }
    ]
  }
  chart.setOption(option)
}

const renderTrendChart = () => {
  const chart = echarts.init(trendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '咨询次数',
        type: 'line',
        data: props.data.monthlyTrend,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(() => {
  renderTypeChart()
  renderTrendChart()
})

watch(() => props.data, () => {
  renderTypeChart()
  renderTrendChart()
}, { deep: true })
</script>

<style scoped>
.psychological-counseling {
  padding: 10px;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.chart-row {
  margin-bottom: 16px;
}

.chart-container {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  height: 300px;
}

.chart-container h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.chart {
  height: 240px;
  width: 100%;
}

.session-table {
  margin-top: 16px;
}

.detail-section {
  margin-top: 16px;
}

.detail-section h4 {
  margin: 16px 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}
</style>
