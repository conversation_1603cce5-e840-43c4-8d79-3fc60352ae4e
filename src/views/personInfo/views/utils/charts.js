import * as echarts from 'echarts'

export const initScoreChart = (dom, data) => {
  const chart = echarts.init(dom)
  const option = {
    title: { text: 'GPA变化趋势' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: data.map(item => item.semester)
    },
    yAxis: { type: 'value', min: 2, max: 4 },
    series: [{
      data: data.map(item => item.gpa),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(58, 77, 233, 0.8)' },
          { offset: 1, color: 'rgba(58, 77, 233, 0.1)' }
        ])
      },
      itemStyle: { color: '#3a4de9' }
    }]
  }
  chart.setOption(option)
}

export const initCourseChart = (dom, data) => {
  const chart = echarts.init(dom)
  const option = {
    title: { text: '课程成绩分布' },
    radar: {
      indicator: data.map(item => ({ name: item.name, max: 100 }))
    },
    series: [{
      type: 'radar',
      data: [{
        value: data.map(item => item.score),
        name: '成绩',
        areaStyle: { color: 'rgba(64, 158, 255, 0.4)' }
      }]
    }]
  }
  chart.setOption(option)
}

export const initLibraryChart = (dom, data) => {
  const chart = echarts.init(dom)
  const option = {
    title: { text: '图书馆月访问量' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: { type: 'value' },
    series: [{
      data: data,
      type: 'bar',
      itemStyle: { color: '#409EFF' }
    }]
  }
  chart.setOption(option)
}

export const initConsumptionChart = (dom, data) => {
  const chart = echarts.init(dom)
  const option = {
    title: { text: '月消费趋势' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: { type: 'value' },
    series: [{
      data: data,
      type: 'line',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.5)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
        ])
      },
      itemStyle: { color: '#409EFF' }
    }]
  }
  chart.setOption(option)
}

export const initBookChart = (dom, data) => {
  const chart = echarts.init(dom)
  const option = {
    title: { text: '图书借阅类别' },
    tooltip: { trigger: 'item' },
    series: [{
      name: '借阅类别',
      type: 'pie',
      radius: '70%',
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2
      }
    }]
  }
  chart.setOption(option)
}
