<template>
  <div class="dashboard-container">
    <!-- 学生头部信息 -->
    <StudentHeader
      :routeType="routeType"
     current-xgh="self"/>

    <!-- 顶部用户信息 -->
<!--    <el-row :gutter="20" class="user-info-card">-->
<!--      <el-col :span="6">-->
<!--        <el-avatar :size="80" :src="userInfo.avatar" />-->
<!--      </el-col>-->
<!--      <el-col :span="18">-->
<!--        <h2>欢迎回来，{{ userInfo.name }}</h2>-->
<!--        <p>班级：{{ userInfo.class }} | 学号：{{ userInfo.studentId }}</p>-->
<!--        <p>学院：{{ userInfo.college }}</p>-->
<!--        <p>今天是{{ currentDate }}</p>-->
<!--      </el-col>-->
<!--    </el-row>-->

    <!-- 功能卡片区 -->
    <el-row :gutter="10" class="feature-cards">
      <el-col
        v-for="(item, index) in features"
        :key="index"
        :xs="12" :sm="8" :md="6" :lg="4" :xl="4"
      >
        <el-card shadow="hover" @click="router.push(item.path)" class="feature-card">
          <div class="feature-item">
            <i :class="item.icon" style="font-size: 24px;"></i>
            <p>{{ item.title }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区 -->
    <el-row :gutter="10" class="main-content">
      <!-- 左侧近期事项 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card header="近期事项" class="recent-tasks">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in recentItems"
              :key="index"
              :icon="getItemIcon(item.type)"
              :type="getItemType(item.status)"
              :timestamp="item.deadline || item.date || item.time"
              placement="top"
            >
              <el-card shadow="hover" class="task-card">
                <div class="task-header">
                  <el-tag :type="getStatusTagType(item.status || item.read)" size="small">
                    {{ item.status || (item.read ? '已读' : '未读') }}
                  </el-tag>
                  <span class="task-title">{{ item.title }}</span>
                </div>
                <div class="task-content">
                  <p v-if="item.description">{{ item.description }}</p>
                  <div class="task-actions">
                    <el-button
                      v-if="item.type === '作业' && item.status === '未提交'"
                      type="primary"
                      size="small"
                      @click.stop="handleSubmitHomework(index)"
                    >
                      提交作业
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click.stop="handleViewDetail(item)"
                    >
                      查看详情
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>

      <!-- 右侧课程进度 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card header="课程进度" class="course-progress">
          <div v-for="course in courseProgress" :key="course.name" class="course-item">
            <div class="course-header">
              <h4>{{ course.name }}</h4>
              <span class="course-teacher">{{ course.teacher }}</span>
            </div>
            <el-progress
              :percentage="course.progress"
              :format="formatProgress"
              :color="customColors"
              :stroke-width="12"
            ></el-progress>
            <div class="course-footer">
              <span class="course-score">当前成绩: {{ course.score || '暂无' }}</span>
              <el-button
                type="text"
                size="small"
                @click="handleViewCourse(course)"
              >
                课程详情
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部公告区 -->
    <el-card header="最新公告" class="notice-board">
      <el-table
        :data="notices"
        style="width: 100%"
        :row-class-name="tableRowClassName"
        @row-click="handleNoticeClick"
      >
        <el-table-column prop="title" label="标题" width="300">
          <template #default="{ row }">
            <span :class="{ 'unread-notice': !row.read }">{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="publisher" label="发布部门" width="150"></el-table-column>
        <el-table-column prop="time" label="发布时间" width="120"></el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.read ? 'success' : 'danger'" size="small">
              {{ row.read ? '已读' : '未读' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="notice-pagination">
        <el-pagination
          small
          layout="prev, pager, next"
          :total="noticeTotal"
          :page-size="5"
          @current-change="handleNoticePageChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {ref, onMounted, unref} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import { ElMessage } from 'element-plus'
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';

const router = useRouter()
const route = useRoute();
const {currentRoute, push} = useRouter();
const {path,} = unref(currentRoute);
let routeType = path.split('/')[4];
console.log(routeType, )

// 当前日期
const currentDate = ref('')
const updateCurrentDate = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }
  currentDate.value = now.toLocaleDateString('zh-CN', options)
}

// 用户信息
const userInfo = ref({
  name: '张三',
  avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
  class: '计算机科学与技术2021级1班',
  studentId: '2021211001',
  college: '计算机科学与技术学院'
})

// 功能卡片
const features = ref([
  { icon: 'el-icon-notebook-2', title: '我的课程', path: '/student/courses' },
  { icon: 'el-icon-date', title: '课程表', path: '/student/schedule' },
  { icon: 'el-icon-document', title: '作业任务', path: '/student/homework' },
  { icon: 'el-icon-chat-line-round', title: '在线讨论', path: '/student/discussion' },
  { icon: 'el-icon-data-line', title: '成绩查询', path: '/student/scores' },
  { icon: 'el-icon-collection', title: '学习资源', path: '/student/resources' }
])

// 近期事项
const recentItems = ref([
  {
    type: '作业',
    title: '数学作业 - 第三章习题',
    description: '完成第三章所有习题并提交PDF文件',
    deadline: '2023-06-15',
    status: '未提交',
    course: '高等数学'
  },
  {
    type: '考试',
    title: '期中考试 - 大学英语',
    description: '考试范围：第一单元至第五单元',
    date: '2023-06-20',
    status: '待准备',
    course: '大学英语'
  },
  {
    type: '通知',
    title: '校园活动通知',
    description: '关于举办校园科技文化节的通知',
    time: '2023-06-10',
    read: false,
    publisher: '团委'
  }
])

// 课程进度
const courseProgress = ref([
  { name: '高等数学', teacher: '李教授', progress: 65, score: 85, courseId: 'MATH101' },
  { name: '大学英语', teacher: '王老师', progress: 50, score: 78, courseId: 'ENG201' },
  { name: '计算机基础', teacher: '张教授', progress: 80, score: 92, courseId: 'CS101' }
])

// 公告数据
const notices = ref([
  { id: 1, title: '关于期末考试安排的通知', time: '2023-06-01', publisher: '教务处', read: false },
  { id: 2, title: '校园文化活动周安排', time: '2023-05-28', publisher: '团委', read: true },
  { id: 3, title: '图书馆暑期开放时间调整', time: '2023-05-25', publisher: '图书馆', read: false },
  { id: 4, title: '2023年奖学金评选通知', time: '2023-05-20', publisher: '学生处', read: true },
  { id: 5, title: '校园网络安全注意事项', time: '2023-05-15', publisher: '信息中心', read: false }
])

const noticeTotal = ref(15)
const customColors = ref([
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
])

// 方法定义
const getItemIcon = (type) => {
  const icons = {
    '作业': 'el-icon-edit',
    '考试': 'el-icon-tickets',
    '通知': 'el-icon-bell'
  }
  return icons[type] || 'el-icon-info'
}

const getItemType = (status) => {
  if (status === '未提交') return 'danger'
  if (status === '待准备') return 'warning'
  return 'primary'
}

const getStatusTagType = (status) => {
  if (status === '未提交' || status === '未读') return 'danger'
  if (status === '待准备') return 'warning'
  return 'success'
}

const formatProgress = (percentage) => {
  return `已完成 ${percentage}%`
}

const tableRowClassName = ({ row }) => {
  return !row.read ? 'unread-row' : ''
}

const handleSubmitHomework = (index) => {
  ElMessage.success(`准备提交作业: ${recentItems.value[index].title}`)
  // 这里可以添加提交作业的逻辑
  // 模拟提交后状态变更
  setTimeout(() => {
    recentItems.value[index].status = '已提交'
  }, 1000)
}

const handleViewDetail = (item) => {
  ElMessage.info(`查看详情: ${item.title}`)
  // 根据不同类型跳转到不同页面
  if (item.type === '作业') {
    router.push(`/student/homework/detail?title=${encodeURIComponent(item.title)}`)
  } else if (item.type === '考试') {
    router.push(`/student/exam/detail?title=${encodeURIComponent(item.title)}`)
  } else {
    // 标记通知为已读
    item.read = true
    router.push(`/student/notice/detail?id=${item.id || 0}`)
  }
}

const handleViewCourse = (course) => {
  router.push(`/student/courses/${course.courseId}`)
}

const handleNoticeClick = (row) => {
  row.read = true
  router.push(`/student/notice/detail?id=${row.id}`)
}

const handleNoticePageChange = (page) => {
  ElMessage.info(`切换到公告第 ${page} 页`)
  // 这里可以添加加载对应页公告的API调用
}

// 模拟数据加载
const fetchDashboardData = () => {
  // 实际项目中这里应该是API调用
  console.log('加载仪表盘数据...')
}

// 生命周期钩子
onMounted(() => {
  updateCurrentDate()
  fetchDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 10px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 50px);

  .user-info-card {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;

    h2 {
      margin: 0 0 10px 0;
      font-size: 20px;
      color: #333;
    }

    p {
      margin: 5px 0;
      font-size: 14px;
      color: #666;
    }
  }

  .feature-cards {
    margin-bottom: 4px;

    .feature-card {
      margin-bottom: 5px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .feature-item {
      text-align: center;
      padding: 5px 0;
      cursor: pointer;

      i {
        display: block;
        margin-bottom: 10px;
        color: #409EFF;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #333;
      }
    }
  }

  .main-content {
    margin-bottom: 20px;

    .recent-tasks {
      height: 100%;

      .task-card {
        margin-bottom: 10px;
        cursor: pointer;

        .task-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .task-title {
            margin-left: 10px;
            font-weight: bold;
          }
        }

        .task-content {
          p {
            margin: 5px 0;
            font-size: 13px;
            color: #666;
          }

          .task-actions {
            margin-top: 10px;
            text-align: right;
          }
        }
      }
    }

    .course-progress {
      height: 100%;

      .course-item {
        margin-bottom: 10px;

        .course-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          h4 {
            margin: 0;
            font-size: 16px;
          }

          .course-teacher {
            font-size: 12px;
            color: #999;
          }
        }

        .course-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 5px;

          .course-score {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }

  .notice-board {
    .unread-notice {
      font-weight: bold;
    }

    .notice-pagination {
      margin-top: 15px;
      text-align: right;
    }
  }
}

// 全局样式（因为scoped无法影响el-table的row样式）
:deep(.unread-row) {
  font-weight: bold;

  td {
    background-color: #f0f7ff !important;
  }
}
</style>
