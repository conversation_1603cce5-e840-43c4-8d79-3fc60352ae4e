<template>
  <ele-page hide-footer flex-table :multi-card="false" style="padding:8px 0 0 8px !important">
    <!-- 加宽左侧导航区域 -->
    <ele-split-panel ref="splitRef"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     space="8px"
                     size="240px"
                     allow-collapse>
      <!-- 左侧导航区域 -->
      <div class="left-nav-container">
        <!-- 顶部操作按钮 -->
        <div class="nav-actions" v-if="enterType==='preview'">
          <el-button text @click="onBack" class="back-btn">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <span>返回</span>
          </el-button>
          <el-button type="primary" text @click="openEdit" class="edit-btn">
            <el-icon>
              <Edit/>
            </el-icon>
            <span>编辑</span>
          </el-button>
        </div>

        <!-- 用户信息区域 - 修改为更紧凑的布局 -->
        <div class="user-profile">
          <div class="avatar-container">
            <template v-if="imageLoaded&&currentData&&currentData.photo">
              <el-avatar :size="64" :src="'/api/file/inline/' + JSON.parse(currentData.photo)[0].id"/>
            </template>
            <template v-else>
              <el-avatar v-if="currentData.xb==='男'" :size="64"
                         src="/male.png"
                         :alt="currentData.xb"
                         class="icon-svg"/>
              <el-avatar v-else-if="currentData.xb==='女'" :size="64"
                         src="/female.png"
                         :alt="currentData.xb"
                         class="icon-svg"/>
              <!--              <el-icon size="60" v-if="currentData.xb==='女'">-->
              <!--                <SvgIcon name="女" class="icon-svg"/>-->
              <!--              </el-icon>-->
              <!--              <el-icon size="60" v-else-if="currentData.xb==='男'">-->
              <!--                <SvgIcon name="男" class="icon-svg"/>-->
              <!--              </el-icon>-->
            </template>
          </div>
          <div class="user-info">
            <h3 class="user-name">{{ currentData.xm }}</h3>
            <p class="student-id" v-if="currentData.xgh">{{ currentData.xgh }}</p>

            <!-- 整体完成度 - 修改为更紧凑的样式 -->
            <div class="completion-rate">
              <div class="completion-header">
                <span>完整度</span>
                <span class="completion-value">{{ overallCompletion }}%</span>
              </div>
              <el-progress
                :percentage="overallCompletion"
                :stroke-width="4"
                :show-text="false"
                :color="completionColor"/>
            </div>
          </div>
        </div>

        <!-- 导航树 - 修改为更紧凑的样式 -->
        <ele-loading :loading="loading" class="nav-loading">
          <el-tree ref="treeRef"
                   :data="groupData"
                   highlight-current
                   node-key="id"
                   :props="{ label: 'title' }"
                   :expand-on-click-node="false"
                   :default-expand-all="true"
                   :style="{
                      '--ele-tree-item-height': '34px',
                      '--ele-tree-expand-padding': 0,
                      '--ele-tree-expand-margin': 0
                   }"
                   @node-click="handleNodeClick">
            <template #default="{ data: d }">
              <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.title }}
                        </span>
                      </span>
                    </span>
              </div>
            </template>
          </el-tree>
        </ele-loading>
      </div>

      <!-- 右侧内容区域 -->
      <template #body>
        <div class="scroll-container content-container" ref="wrapRef" :style="{height:currentPageHeight+'px'}">
          <div class="content-body">
            <template v-if="groupData.length>0">
              <div v-for="(group, index) in groupData" :key="group.id"
                   :id="'part'+group.id">
                <table-preview v-if="group.listFlag === '是'"
                               :key="'table' + index"
                               :grid="config.grid"
                               :routeType="routeType"
                               :xgh="xgh"
                               :queryParams="{username:xgh}"
                               :currentGroup="group"
                               @completion-update="handleCompletionUpdate"/>
                <form-preview v-else
                              :ref="(el)=>setChildComponentRef(el,group.id)"
                              :key="'form' + index"
                              :routeType="routeType"
                              :currentData="currentData"
                              :currentGroup="group"
                              :queryParams="{username:xgh}"
                              @onDoneGroup="onDoneGroup"
                              @onDoneDeleteFile="onDoneDeleteFile"
                              @completion-update="handleCompletionUpdate"/>
              </div>
            </template>
          </div>
        </div>
        <!-- 设置位置 -->
        <el-backtop target=".scroll-container" :bottom="40" :right="18" style="position: absolute;"/>
      </template>
    </ele-split-panel>

    <!-- 底部工具栏 -->
    <template v-if="enterType!=='preview'">
      <ele-bottom-bar>
        <ele-text style="float: right;padding-right: 40px;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
        <template #extra>
          <el-button size="small" @click="onBack" plain>取消</el-button>
          <el-button size="small" type="primary" plain
                     :loading="loading" @click="onSubmit()">
            提交
          </el-button>
        </template>
      </ele-bottom-bar>
    </template>
  </ele-page>
</template>

<script setup>
import {ref, reactive, unref, nextTick, computed} from 'vue';
import {useRouter, useRoute} from 'vue-router';
import {ElMessage as EleMessage} from 'element-plus';
import {usePageTab} from '@/utils/use-page-tab';
import {getGroupList, operation, getPersonInfo, getPersonInfoData} from '../api/index';
import FormPreview from '../components/form-preview.vue';
import TablePreview from '../components/table-preview.vue';
import {CloseCircleOutlined} from '@/components/icons';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import IconPark from '@/components/IconPark/index.vue';
import {toFormDataWj} from '@/utils/common_bak2.js';

defineOptions({name: 'PersonInfoStDetails'});

const route = useRoute();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {removePageTab, getRouteTabKey, setPageTabTitle, reloadPageTab} =
  usePageTab();

const {path, query} = unref(currentRoute);
let enterType = query?.enterType;
let studentType = query?.studentType;
let xgh = route.params?.id ?? '';
let routeType = path.split('/')[4];
console.log(enterType, routeType, xgh, pageHeight);
const imageLoaded = ref(false);
const containerRef = ref(null);

const wrapRef = ref(null);

/** 页面高度设置 */
const currentPageHeight = computed(() => {
  return enterType === 'preview' ? pageHeight.value + 60 : pageHeight.value + 20;
});

/** 树组件 */
const treeRef = ref(null);

/** 编辑 */
const openEdit = () => {
  removePageTab({key: getRouteTabKey()});
  reloadPageTab({fullPath: '/personInfo/st/details/' + routeType + '/' + xgh, query: {enterType: 'edit',studentType: studentType}});
};

/** 表单页面回显数据 */
const currentData = ref({});
/** 表单配置 */
const config = reactive({
  labelWidth: '120px',
  grid: 3,
});

/** 是否显示裁剪弹窗 */
const visible = ref(false);

/** 打开图片裁剪 */
const openCropper = () => {
  visible.value = true;
};

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  console.log(data);
  let isExist = proFormGroup.value.filter(
    (obj) => obj.groupId === data.groupId,
  );
  if (isExist.length > 0) {
    proFormGroup.value.filter((obj) => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    });
  } else {
    proFormGroup.value.push(data);
  }
};

/** 存放删除的图片ID，附件ID */
const proFormDelFile = ref({});

const onDoneDeleteFile = (prop, data) => {
  // 保存到对象中
  proFormDelFile.value[prop] = data;
};

/** 头像裁剪完成回调 */
const onCrop = (result) => {
  visible.value = false;
  // emit('done', {avatar: result});
  /*  const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
updateUserInfo({ avatar: result })
  .then((data) => {
    loading.close();
    visible.value = false;
    EleMessage.success('修改成功');
    emit('done', data);
  })
  .catch((e) => {
    loading.close();
    EleMessage.error(e.message);
  }); */
};
const childComponentsRefs = ref({});

// 设置子组件的引用
const setChildComponentRef = (el, id) => {
  childComponentsRefs['value'][id] = el;
};

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([]);
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  let result = new Promise((resolve, reject) => {
    formChild.proFormRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true);
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors;
        resolve(false);
        reject();
      }
    });
  });
  resultArr.push(result);
};

const onSubmit = async () => {
  validMsgCount.value = 0;
  // 遍历表单数组，依次对每个表单进行校验
  Object.keys(childComponentsRefs['value']).forEach((key) => {
    checkForm(childComponentsRefs['value'][key]);
  });
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []; //每次请求完要清空数组
    if (allTrue) {
      // 执行后续操作
      save();
    } else {
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
};

/** 提交 */
const save = () => {
  let resData = proFormGroup.value;
  console.log(resData);
  let result = {};
  resData.forEach((res) => {
    const value = res.values;
    Object.keys(value).forEach((key) => {
      const resValue = value[key];
      if (resValue) result[key] = resValue;
    });
  });

  Object.keys(result).forEach((key) => {
    //处理type=regions类型，省市区+详细地址逻辑处理
    if (key.includes('_regionsDetail')) {
      let keyArray = key.split('_');
      result[keyArray[0]] = [result[keyArray[0]].join(), result[key]].join(
        ',',
      );
    }
  });
  console.log(result);
  // 设置到表单中（例如：提交前需要转成 JSON）
  if (proFormDelFile.value) result['deleteFileIds'] = JSON.stringify(proFormDelFile.value);
  let data = toFormDataWj({...result});
  loading.value = true;
  operation(routeType, data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    onBack();
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const selectedKey = ref(null);
const handleNodeClick = (tag) => {
  /**
   * 锚点滚动
   * 在不使用<a>标签的情况下实现锚点滚动，使用JavaScript来更改窗口的滚动位置。
   * 实现每个<div>设定了唯一的id，并且通过点击按钮触发scrollToDiv函数，该函数接受一个参数divId，
   * 根据这个id查找对应的元素，并使用scrollIntoView方法平滑滚动到该元素的位置
   * @type {HTMLElement}
   */
  if (tag) {
    const element = document.getElementById('part' + tag.id);
    if (element) {
      element.scrollIntoView();
      element.scrollIntoView({behavior: 'smooth'});
    }
    treeRef['value']?.setCurrentKey?.(tag.id);
    // 监听选中状态
    selectedKey.value = tag.id;
  }
};
/** 用户信息 */
const form = reactive({});

/** 组 */
const groupData = ref([]);
/** 当前组 */
const currentGroup = ref();

/** 请求状态 */
const loading = ref(true);

const isAddMark = ref(0);

/** table组件新增 */
const onClickAllTable = () => {
  isAddMark.value++;
};

/** 返回 关闭当前页面并跳转到列表页面 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  // push('/personInfo/st/' +routeType);
  if (studentType) {
    reloadPageTab({fullPath: '/personInfo/st/' + routeType + '/' + studentType, query: {enterType: 'returnRefresh'}});
  } else {
    reloadPageTab({fullPath: '/personInfo/st/' + routeType, query: {enterType: 'returnRefresh'}});
  }
};

// /** 关闭当前页面并刷新列表页面 */
// const handleDone = () => {
//   removePageTab({key: getRouteTabKey()});
//   reloadPageTab({fullPath: '/personInfo/st/' + routeType + '/' + studentType, query: {enterType: 'returnRefresh'}});
// };

/** 根据学工号查询 */
const queryPersonInfo = () => {
  if (xgh === 'self') {
    queryPersonData();
  } else {
    console.log(xgh);
    loading.value = true;
    getPersonInfo(routeType, xgh).then((list) => {
      loading.value = false;
      list['avatar'] = null;
      currentData.value = list;
      currentData.value.enType = enterType;
      // 图片加载完成
      imageLoaded.value = true;
      let title =
        routeType === 'student'
          ? list.xm + '-成长档案'
          : list.xm + '-教师详情';
      title = title + (enterType === 'preview' ? '查看' : '编辑');
      setPageTabTitle(title);
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
};

/** 获取当前用户信息 */
const queryPersonData = () => {
  // console.log(xgh)
  loading.value = true;
  getPersonInfoData().then((list) => {
    loading.value = false;
    list['avatar'] = null;
    currentData.value = list;
    currentData.value.enType = list.userType;
    // 图片加载完成
    imageLoaded.value = true;
    let title =
      list.userType === 'student'
        ? list.xm + '-成长档案'
        : list.xm + '-教师详情';
    title = title + (enterType === 'preview' ? '查看' : '编辑');
    setPageTabTitle(title);
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

/** 选中 */
const active = ref();

/** 菜单点击事件 */
const onMenuItemClick = (item) => {
  currentGroup.value = item;
  active.value = item.index;
};

const count = ref(0);
const load = () => {
  if (groupData.value.length > 0 && groupData.value.length !== count.value) {
    count.value += 1;
    // treeRef["value"]?.setCurrentKey?.(groupData.value[count.value].id);
    setTimeout(() => {
      nextTick(() => {
        handleNodeClick(groupData.value[count.value]);
      });
    }, 600);
  }
};

// 添加计算整体完成度的方法
const overallCompletion = ref(0);

// 处理完成度更新
const handleCompletionUpdate = (groupId, completion) => {
  const group = groupData.value.find(g => g.id === groupId);
  if (group) {
    group.completion = completion;
    calculateOverallCompletion();
  }
};

// 计算整体完成度
const calculateOverallCompletion = () => {
  const groupsWithCompletion = groupData.value.filter(g => g.completion !== undefined);
  if (groupsWithCompletion.length > 0) {
    const total = groupsWithCompletion.reduce((sum, group) => sum + group.completion, 0);
    overallCompletion.value = Math.round(total / groupsWithCompletion.length);
  }
};

// 在获取分组数据后初始化完成度
const queryDictionaryGroup = () => {
  if (xgh && xgh !== 'INSERT') queryPersonInfo();
  setTimeout(() => {
    loading.value = true;
    getGroupList(routeType, {showFlag: '是', username: xgh}).then((list) => {
      loading.value = false;
      if (list.length > 0) {
        list.forEach((e) => {
          e.data = currentData.value;
          e.index = e.id;
          e.title = e.groupName;
          e.enType = enterType;
          e.completion = 0; // 初始化为0
          delete e.icon;
        });
      }
      groupData.value = list;
      currentGroup.value = list[0];
      onMenuItemClick(list[0]);
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }, 500);
};

// 添加计算完成度颜色的方法
const completionColor = computed(() => {
  if (overallCompletion.value >= 90) return 'var(--success-color)';
  if (overallCompletion.value >= 60) return 'var(--warning-color)';
  return 'var(--danger-color)';
});

// 添加获取完成度标签类型的方法
const getCompletionTagType = (completion) => {
  if (completion >= 90) return 'success';
  if (completion >= 60) return 'warning';
  return 'danger';
};

queryDictionaryGroup();
</script>

<style lang="scss" scoped>
/* 紧凑布局变量 */
:root {
  --primary-color: #409EFF;
  --primary-light: rgba(64, 158, 255, 0.1);
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --border-color: #e4e7ed;
  --bg-color: #f5f7fa;
  --text-color: #303133;
  --text-secondary: #606266;
  --text-light: #909399;
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

/* 左侧导航区域 - 紧凑样式 */
.left-nav-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-right: 1px solid var(--border-color);
  padding: 0;
}

.nav-actions {
  padding: 2px 10px 0 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  background-color: #f9fbfd;

  .el-button {
    padding: 4px 8px;

    span {
      margin-left: 4px;
      font-size: 13px;
    }
  }
}

.user-profile {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid var(--border-color);

  .avatar-container {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: #f0f2f5;
    overflow: hidden;
    border: 2px solid white;
    box-shadow: var(--card-shadow);

    .el-avatar, .el-icon {
      width: 100%;
      height: 100%;
    }
  }

  .user-info {
    flex: 1;
    min-width: 0;

    .user-name {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .student-id {
      margin: 2px 0 0;
      font-size: 12px;
      color: var(--text-light);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .completion-rate {
      margin-top: 8px;

      .completion-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 12px;
        color: var(--text-secondary);

        .completion-value {
          color: var(--primary-color);
          font-weight: bold;
        }
      }
    }
  }
}

/* 右侧内容区域样式 */
.content-container {
  flex-direction: column;
}

.scroll-container {
  overflow: auto;
  max-height: 100%;
}

.content-body {
  flex: 1;
  padding: 1px 6px 0 0;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .left-nav-container {
    width: 260px;
  }

  .user-profile {
    padding: 6px 10px;

    .avatar-container {
      width: 84px;
      height: 84px;
    }
  }

  .content-body {
    padding: 0 8px 0 0;
  }

}

@media (max-width: 768px) {
  .left-nav-container {
    width: 220px;
  }

  .user-profile {
    padding: 6px 10px;

    .avatar-container {
      width: 72px;
      height: 72px;
    }

    .user-info h3 {
      font-size: 16px;
    }
  }

  .content-body {
    padding: 12px;
  }

}

.nav-loading {
  flex: 1;
  padding: 4px 0;
  overflow: auto;
}

</style>
