<template>
  <ele-page hide-footer flex-table>
    <ele-card class="ele-card-cus">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
      </template>
      <el-row>
        <el-col :span="4">
          <div class="left-bar">
            <div class="info-user">
              <div class="info-user-avatar">
                <el-avatar :size="95">
                  <img v-if="imageLoaded"
                       :src="'/api/file/inline/'+ JSON.parse(currentData.photo)[0].id"/>
                </el-avatar>
              </div>
              <ele-text>
                {{ currentData.xm }}
              </ele-text>
            </div>
            <el-divider></el-divider>
            <ele-card class="statistics-card">
              <ele-text type="placeholder" class="statistics-header">
                <div class="statistics-header-text">信息完整度</div>
              </ele-text>
              <ele-text class="statistics-value">68%</ele-text>
              <div class="statistics-body">
                <el-progress type="line" color="rgba(39, 157, 223, 1)" :percentage="68" :show-text="false"
                             :stroke-width="10"
                             style="width: 100%"/>
              </div>
            </ele-card>
            <el-anchor :container="containerRef" direction="vertical" type="default" :offset="30" marker="true"
                       @click="handleClick">
              <el-anchor-link v-for="(group, index) in groupData" :key="index" :href="'#part' + index" active
                              :title="group.title"/>
            </el-anchor>
          </div>
        </el-col>
        <el-col :span="20">
          <div ref="containerRef" class="" :style="{ height: (pageHeight-81)+'px',overflow:'auto'}">
            <div v-for=" (group, index) in groupData">
              <div :id="'part'+index">
                <table-preview v-if="group.listFlag==='是'"
                               :key="'formKey'+index"
                               :labelWidth="config.labelWidth"
                               :grid="config.grid"
                               :routeType="routeType"
                               :xgh="xgh"
                               :currentGroup="group"/>
                <form-preview v-else :ref="setChildComponentRef"
                              :key="'formKey'+index"
                              :labelWidth="config.labelWidth"
                              :readonly="true"
                              :routeType="routeType"
                              :currentData="currentData"
                              :currentGroup="group"
                              @onDoneGroup="onDoneGroup"
                              @onDoneDeleteFile="onDoneDeleteFile"/>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </ele-card>
  </ele-page>
</template>

<script setup lang="ts">
import {ref, unref, reactive,} from 'vue'
import { ElMessage as EleMessage} from "element-plus";
import {usePageTab} from '@/utils/use-page-tab';
import {useRoute, useRouter} from "vue-router";
import FormPreview from '../components/form-preview.vue';
import TablePreview from '../components/table-preview.vue';
import {getGroupList, getPersonInfo} from '../api/index.js';
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const containerRef = ref<HTMLElement | null>(null)
const route = useRoute();
let xgh = route.params?.id ?? '';
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let routeType = path.split("/")[4];

const {removePageTab, getRouteTabKey, setPageTabTitle} = usePageTab();

const handleClick = (e: MouseEvent) => {
  e.preventDefault()
}
/** 请求状态 */
const loading = ref(true);
/** 表单页面回显数据 */
const currentData = ref({
  enType: "view"
});

/** 组 */
const groupData = ref([]);
/** 当前组 */
const currentGroup = ref();
/** 表单配置 */
const config = reactive({
  labelWidth: 120,
  grid: 3
});
/** 选中 */
const active = ref();
/** 菜单点击事件 */
const onMenuItemClick = (item) => {
  currentGroup.value = item
  active.value = item.index;
};

const imageLoaded = ref(false);

/** 打开组group编辑弹窗 */
const openEdit = (xgh) => {
  // let json = {
  //   xgh: row?.xgh,
  //   xm: row?.xm,
  //   routeType: routeType,
  // }
  // setSharedPersonData(json)
  if (xgh) {
    push('/personInfo/st/details/' + routeType + '/' + xgh)
  } else {
    push('/personInfo/st/details/' + routeType + '/INSERT')
  }
};

/** 查询详情 */
const queryDictionaryGroup = () => {
  console.log(xgh)
  if (xgh && xgh !== 'INSERT') queryPersonInfo();
  setTimeout(() => {
    loading.value = true;
    getGroupList(routeType).then((data) => {
      loading.value = false;
      if (data.length > 0) {
        data.forEach((e, index) => {
          e.data = currentData.value;
          e.index = e.id;
          e.title = e.groupName;
          e.enType = 'preview';
          delete e.icon;
        })
      }
      groupData.value = data
      console.log(`groupData`, groupData)
      currentGroup.value = data[0]
      onMenuItemClick(data[0])
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }, 500)
}
/** 根据学工号查询 */
const queryPersonInfo = () => {
  loading.value = true;
  getPersonInfo(routeType, xgh).then((list) => {
    if (list) {
      // 图片加载完成
      imageLoaded.value = true;
      let title = routeType === 'student' ? list.xm + '-成长档案' : list.xm + '-教师详情';
      console.log('122222====', title)
      setPageTabTitle(title);
      list['avatar'] = null
      list['enType'] = "preview"
      currentData.value = list;
      loading.value = false;
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
};

queryDictionaryGroup();
</script>
<style lang="scss" scoped>
.ele-page {
}

.ele-card-cus {
  :deep .ele-card-header {
    padding: 10px 20px !important;
  }

  .title {
    font-weight: bold;
    font-size: 14px;
  }
}

.left-bar {
  padding: 5px 15px;

  .el-divider {
    margin: 8px 0;
    border-top: 1px solid #eee;
  }
}

.info-user {
  text-align: center;

  span {
    border: 3px solid #fff;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  }

  .info-user-avatar {
    margin-bottom: 5px;
  }

  // .ele-text {
  //   font-size: 14px;
  //   font-weight: bold;
  //   margin-bottom: 5px;
  // }
}

.statistics-card {
  height: 52px;
  color: #ffffff;
  // color: rgba(0,0,0,0.6);
  // background: linear-gradient(166.644155545969deg, rgba(45, 140, 240, 1) 37%, rgba(255, 255, 255, 1) 125%);
  border-radius: 5px !important;
  overflow: hidden;

  :deep .ele-card-body {
    padding: 5px 15px !important;
  }

  .ele-text {
    display: inline-block;
    // color: #ffffff;
    color: #4f4f4f;
    margin-top: 1px;
    margin-bottom: 8px;
  }

  .statistics-value {
    float: right;
    font-weight: bold;
  }
}

.el-anchor {
  background: linear-gradient(90deg, rgb(246, 250, 252) 52%, rgba(255, 255, 255, 1) 111%);
  border-radius: 5px;
  padding: 10px 0px;

  :deep .el-anchor__marker {
    width: 3px;
    height: 20px;
    border-radius: 0px !important;
    left: inherit;
    right: 10px;
  }
}

.ele-card-item {
  border: 1px solid var(--el-border-color-light);
  border-radius: 5px !important;
  margin-right: 15px;

  :deep .ele-card-header {
    padding: 5px 20px !important;
    background: linear-gradient(90deg, rgba(240, 250, 255, 1) 39%, rgba(255, 255, 255, 1) 99%);
  }
}

:deep .el-descriptions {
  .el-descriptions__label {
    font-size: 12px;
    min-width: 80px;
    display: inline-block;
  }

  .el-descriptions__content {
    color: #999 !important;
    font-size: 12px;
  }

  .el-descriptions__cell {
    padding-bottom: 8px !important;
  }
}

:deep .ele-card-item {
  border: 1px solid var(--el-border-color-light);
  border-radius: 5px !important;
  margin-right: 15px;

  :deep .ele-card-header {
    padding: 5px 20px !important;
    background: linear-gradient(90deg, rgba(240, 250, 255, 1) 39%, rgba(255, 255, 255, 1) 99%);
  }
}
</style>
