<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{
        padding: '5px 8px 10px 8px!important',
        overflow: 'hidden'
      }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search @search="searchReload" ref="searchRef" />
      </template>
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="xgh"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        @done="onDone"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
        cache-key="listBasicTable"
      >
        <template #toolbar>
          <el-button
            v-if="studentType === 'zxs'"
            class="ele-btn-icon"
            size="small"
            @click="openImport"
          >
            导入
          </el-button>
          <el-button
            v-if="studentType === 'zxs'"
            size="small"
            @click="openImportUpdate()"
          >
            批量更新
          </el-button>
          <el-button size="small" @click="exportBasDiySet()">
            自定义导出设置
          </el-button>
          <el-button
            v-if="studentType === 'zxs'"
            size="small"
            @click="openImportFile()"
          >
            照片批量上传
          </el-button>
          <el-button
            v-if="studentType === 'zxs'"
            size="small"
            @click="exportFile()"
          >
            照片打包下载
          </el-button>
          <el-button size="small" @click="openUpdateStuStatus()">
            批量更新人员状态
          </el-button>
        </template>
        <template #xgh="{ row }">
          <ele-tooltip content="详情" effect="light" placement="right-start">
            <el-link
              type="primary"
              underline="never"
              @click="openEdit(row, 'preview')"
            >
              {{ row.xgh }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #xm="{ row }">
          <ele-tooltip
            v-if="studentType"
            content="学生画像"
            placement="right-start"
            effect="light"
          >
            <el-link
              type="primary"
              underline="never"
              @click="openPersonalDataView(row)"
            >
              {{ row.xm }}
            </el-link>
          </ele-tooltip>
          <span v-else>{{ row.xm }}</span>
        </template>
        <template #ryztid="{ row }">
          <dict-data
            code="ryzt"
            v-model="row.ryztid"
            :dicQueryParams="{
              dictFieldUrl: '/code/codeXszt',
              valueField: 'id',
              textField: 'ztmc'
            }"
            placeholder="请选择"
            @update:modelValue="
              (value) => handelUpdateStudentStatus(row, value)
            "
          />
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 导入弹窗 -->
    <Import v-model="showImport" @done="reload" />
    <updateStudentStatus
      v-model="showUpdateStuStatus"
      :updataStatusDatas="updataStatusDatas"
      @done="reload"
    />
    <ImportUpdateField v-model="showImportUpdate" @done="reload" />
    <ImportFile v-model="showImportFile" @done="reload" />
    <ExportSet v-model="showBasDiySet" @done="updateExportData" />
    <FloatButton
      v-if="studentType === 'zxs'"
      :needType="['help', 'statistic']"
      :title="StatisticsTitle"
      @floatClickEvent="handleFloatClickEvent"
    />
    <statistic
      v-if="studentType === 'zxs'"
      v-model="statisticVisible"
      :title="StatisticsTitle"
    />
    <help v-if="studentType === 'zxs'" v-model="helpVisible" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import Import from './components/import.vue';
  import ImportUpdateField from './components/import-update-field.vue';
  import ImportFile from './components/import-file.vue';
  import ExportSet from './components/export-set.vue';
  import Search from './components/search.vue';
  import { queryPage, getFieldList, updateStudentStatus } from './api/index';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import FloatButton from '@/components/FloatButton/index.vue';
  import Statistic from '@/views/personInfo/st/components/view/statistic.vue';
  import Help from '@/views/personInfo/st/components/view/help.vue';
  import { useReturnRefresh } from '@/utils/useReturnRefresh.js';
  import DictData from '@/components/DictData/index.vue';
  import UpdateStudentStatus from './components/updateStudentStatus.vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { toFormDataWj } from '@/utils/common_bak2.js';

  defineOptions({ name: 'PersonInfo' });

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const { currentRoute, push } = useRouter();
  const { params, path, query } = unref(currentRoute);
  const routeType = path.split('/')[3];
  const studentType = path.split('/')[4];
  console.log(studentType);
  const helpVisible = ref(false);
  const StatisticsTitle = ref('');
  const statisticVisible = ref(false);
  if (routeType === 'student') {
    StatisticsTitle.value = '学生统计';
  } else if (routeType === 'teacher') {
    StatisticsTitle.value = '教师统计';
  }

  let enterType = query?.enterType;

  const handleFloatClickEvent = (data) => {
    if (data === 'help') {
      helpVisible.value = true;
    } else {
      statisticVisible.value = true;
    }
  };

  const userStore = useUserStore();
  /** 表格实例 */
  const tableRef = ref(null);
  const searchRef = ref(null);
  const currentPageData = ref([]);

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});

  /** 表格列配置 */
  const columns = ref([]);
  const initFieldList = async () => {
    let newColumn = [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 45,
        align: 'center',
        fixed: 'left',
        reserveSelection: true
      }
    ];
    /**
     * tempField: '否'过滤系统自定义的字段信息，xymc，zymc，bjmc。。。
     * @type {*}
     */
    const data = await getFieldList(routeType, {
      listShowFlag: '是',
      tempField: '否'
    });
    if (data.length > 0) {
      data.forEach((e) => {
        // e.controlType === 'select' || e.controlType ==='multipleSelect' &&
        if (e.loadDataType) {
          if (e.loadDataType === 'other') {
            let finallyColumn = {
              minWidth: 120,
              slot: e.fieldEn,
              prop: e.fieldEn,
              label: e.fieldZh,
              formatter: (row) => {
                let finallyVal = '';
                switch (e.loadDataUrl) {
                  case '/code/codeBjb':
                    finallyVal = row['bjmc'];
                    break;
                  case '/code/codeZyb':
                    finallyVal = row['zymc'];
                    break;
                  case '/code/codeDwb/listAll':
                    finallyVal = row['xymc'];
                    break;
                  case '/code/codeDwb':
                    finallyVal = row['xymc'];
                    break;
                  case '/code/codeXszt':
                    finallyVal = row['ryzt'];
                    break;
                  default:
                    finallyVal = row[e.fieldEn];
                    break;
                }
                return finallyVal;
              }
            };
            if (e?.sortable === '是') finallyColumn.sortable = 'custom';
            if (e?.fixed === '是') finallyColumn.fixed = 'left';
            newColumn.push(finallyColumn);
            let setFinallyEn = '';
            switch (e.loadDataUrl) {
              case '/code/codeBjb':
                setFinallyEn = 'bjmc';
                break;
              case '/code/codeZyb':
                setFinallyEn = 'zymc';
                break;
              case '/code/codeDwb':
                setFinallyEn = 'xymc';
                break;
              case '/code/codeDwb/listAll':
                setFinallyEn = 'xymc';
                break;
              case '/code/codeXszt':
                setFinallyEn = 'ryzt';
                break;
            }
          } else {
            let obj = {
              minWidth: 120,
              prop: e.fieldEn,
              label: e.fieldZh,
              slot: e.fieldEn,
              formatter: (row) => {
                let finallyVal = '';
                switch (e.loadDataType) {
                  case 'pycc':
                    finallyVal = row['pyccmc'];
                    break;
                  case 'nj':
                    finallyVal = row['njmc'];
                    break;
                  default:
                    finallyVal = row[e.fieldEn];
                    break;
                }
                return finallyVal;
              }
            };

            if (e?.sortable === '是') obj.sortable = 'custom';
            if (e?.fixed === '是') obj.fixed = 'left';
            newColumn.push(obj);
          }
        } else {
          let obj = {
            minWidth: 160,
            prop: e.fieldEn,
            label: e.fieldZh,
            slot: e.fieldEn
          };
          if (e?.sortable === '是') obj.sortable = 'custom';
          if (e?.fixed === '是') obj.fixed = 'left';
          newColumn.push(obj);
        }
      });
    }
    columns.value = newColumn;
    statisticVisible.value = true;
  };

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  const currentLimit = ref(10);
  const currentPage = ref(1);
  //在校生（sfzx:是,sfby:否），毕业生（sfby:是,sfzx:否），离校生（sfby:否,sfzx:否）
  const studentTypeMap = {
    zxs: { sfzx: '是', sfby: '否' },
    bys: { sfzx: '否', sfby: '是' },
    lxs: { sfzx: '否', sfby: '否' }
  };
  const datasource = ({
    page,
    limit,
    where = {},
    orders = {},
    filters = {}
  }) => {
    currentLimit.value = limit;
    if (studentTypeMap[studentType]) {
      Object.assign(where, studentTypeMap[studentType]);
    }
    return queryPage(routeType, {
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  const searchReload = (where) => {
    searchWhere.value = where;
    console.log('searchReload :>> ', searchWhere);
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 搜索 */
  const reload = (where) => {
    console.log(where);
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  const onDone = (result) => {
    const { data, page } = result;
    currentPage.value = page;
    currentPageData.value = data;
  };

  /** 自定义导出，导出数据处理 */
  const updateExportData = (data) => {
    if (data) {
      let type = data?.type ?? '';
      let newStr = '';
      let dataKeys = Object.keys(data);
      dataKeys.forEach((key) => {
        if (data[key]) newStr += '&' + key + '=' + data[key];
      });
      if (type === '1') {
        newStr += '&page=' + currentPage.value + '&limit=' + currentLimit.value;
      } else if (type === '2') {
        const rows = selections.value.length > 0 ? selections.value : [];
        newStr += '&xgh=' + rows.map((d) => d.xgh);
      } else if (type === '3') {
        let obj = searchWhere.value;
        let keys = Object.keys(obj);
        keys.forEach((key) => {
          if (obj[key]) newStr += '&' + key + '=' + obj[key];
        });
      }
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/exportData?access_token=' +
        accessToken +
        newStr;
    }
  };

  /** 打开组group查看弹窗 */
  const openView = (row) => {
    if (row) {
      push('/personInfo/st/previews/' + routeType + '/' + row?.xgh);
    } else {
      push('/personInfo/st/previews/' + routeType + '/INSERT');
    }
  };
  /** 打开组group查看个人数据看板 */
  const openPersonalDataView = (row) => {
    if (row) {
      // push('/personInfo/st/personal/' + routeType + '/' + row?.xgh);
      push('/personInfo/views/' + routeType + '/' + row?.xgh);
    }
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row, mark) => {
    if (row) {
      push({
        path: '/personInfo/st/details/' + routeType + '/' + row?.xgh,
        query: { enterType: mark, studentType: studentType }
      });
      // push('/personInfo/st/details/' + routeType + '/' + row?.xgh);
    } else {
      push({
        path: '/personInfo/st/details/' + routeType + '/INSERT',
        query: { studentType: studentType }
      });
      // push('/personInfo/st/details/' + routeType + '/INSERT');
    }
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  const showImportUpdate = ref(false);
  const showImportFile = ref(false);
  const showBasDiySet = ref(false);
  const showUpdateStuStatus = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  const openImportUpdate = () => {
    showImportUpdate.value = true;
  };
  const updataStatusDatas = ref({});
  const openUpdateStuStatus = () => {
    const rows = selections.value;
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    if (studentTypeMap[studentType]) {
      updataStatusDatas.value = studentTypeMap[studentType];
    }
    updataStatusDatas.value.selectedXghs = selections.value
      .flatMap((item) => [item.xgh])
      .join(',');
    // const xms = selections.value.flatMap(item => [item.xm]).join(',');
    // updataStatusDatas.value.xms = xms;
    showUpdateStuStatus.value = true;
  };
  /**
   * 单条数据更新学生状态
   * @param row
   * @param value
   */
  const handelUpdateStudentStatus = (row, value) => {
    loading.value = true;
    let data = toFormDataWj({ ...studentTypeMap[studentType], xgh: row.xgh });
    updateStudentStatus(value, data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        reload();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  /** 打开导入照片弹窗 */
  const openImportFile = () => {
    showImportFile.value = true;
  };

  /**  自定义导出设置  */
  const exportBasDiySet = () => {
    showBasDiySet.value = true;
  };

  /** 提交状态 */
  const loading = ref(false);

  const exportBas = () => {
    loading.value = true;
    let searchStr = formMateSearchToStr();
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/exportData?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };

  /** 照片打包下载 */
  const exportFile = () => {
    loading.value = true;
    let searchStr = formMateSearchToStr();
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/personInfo/' +
        routeType +
        '/downloadPhoto?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };

  const formMateSearchToStr = () => {
    let str = '';
    Object.entries(searchRef.value.initModel).forEach(([key, value]) => {
      console.log(key, value);
      if (value) {
        str += `&${key}=${value}`;
      }
    });
    return str;
  };

  const reloadRefresh = () => {
    selections.value = [];
    tableRef.value?.reload?.({
      where: { _refreshMark: Date.now() }
    });
  };

  initFieldList();
  // 自动在返回时刷新
  useReturnRefresh(reloadRefresh);
</script>

<style lang="scss" scoped></style>
