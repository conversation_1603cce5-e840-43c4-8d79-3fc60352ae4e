<!-- 快捷方式 -->
<template>
  <el-row :gutter="8" ref="wrapRef">
    <el-col
      v-for="item in data"
      :key="item.url"
      :lg="isSider ? 12 : 6"
      :md="isSider ? 12 : 6"
      :sm="isSider ? 12 : 6"
      :xs="12"
    >
      <ele-card shadow="hover" :body-style="{ padding: 0 }">
        <router-link :to="item.url" class="app-link">
          <el-row>
            <el-col :lg="8" :md="8" :sm="8" :xs="12">
              <el-icon class="app-link-icon" :style="{ color: item.color }">
                <component :is="item.icon" />
              </el-icon>
            </el-col>
            <el-col :lg="16" :md="16" :sm="16" :xs="12">
              <ele-text size="xxl" class="monitor-count-value">21.2 k</ele-text>
              <div class="app-link-title">{{ item.title }}</div>
            </el-col>
          </el-row>
        </router-link>
      </ele-card>
    </el-col>
  </el-row>
</template>

<script setup>
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import SortableJs from 'sortablejs';
  const CACHE_KEY = 'workplace-links';
  const props = defineProps({
    isSider: {
      type: Boolean,
      default: false
    }
  });
  const isSider = props.isSider;
  console.log('isSider :>> ', isSider);
  /** 默认顺序 */
  const DEFAULT = [
    {
      id: 1,
      icon: 'UserOutlined',
      title: '在校生总人数',
      url: '/system/user'
    },
    {
      id: 2,
      icon: 'UserOutlined',
      title: '专科生人数',
      url: '/dashboard/analysis',
      color: '#95de64'
    },
    {
      id: 3,
      icon: 'UserOutlined',
      title: '本科生人数',
      url: '/list/card/project',
      color: '#ff9c6e'
    },
    {
      id: 4,
      icon: 'UserOutlined',
      title: '研究生人数',
      url: '/list/basic',
      color: '#b37feb'
    }
  ];

  /** 数据 */
  const data = ref(
    (() => {
      try {
        const str = localStorage.getItem(CACHE_KEY);
        const temp = str ? JSON.parse(str) : null;
        if (temp) {
          const result = [...DEFAULT];
          result.sort((a, b) => temp.indexOf(a.id) - temp.indexOf(b.id));
          console.log('result :>> ', result);
          return result;
        }
      } catch (_e) {
        //
      }
      return [...DEFAULT];
    })()
  );

  /** 根节点 */
  const wrapRef = ref(null);

  /** 排序实例 */
  let sortableIns = null;

  /** 重置布局 */
  const reset = () => {
    data.value = [...DEFAULT];
    localStorage.removeItem(CACHE_KEY);
  };

  /** 缓存布局 */
  const cacheData = () => {
    localStorage.setItem(
      CACHE_KEY,
      JSON.stringify(data.value.map((d) => d.id))
    );
  };

  onMounted(() => {
    if ('ontouchstart' in document.documentElement) {
      return;
    }
    sortableIns = new SortableJs(wrapRef.value?.$el, {
      animation: 300,
      onUpdate: ({ oldIndex, newIndex }) => {
        if (typeof oldIndex === 'number' && typeof newIndex === 'number') {
          const temp = [...data.value];
          temp.splice(newIndex, 0, temp.splice(oldIndex, 1)[0]);
          data.value = temp;
          cacheData();
        }
      },
      setData: () => {}
    });
  });

  onBeforeUnmount(() => {
    if (sortableIns) {
      sortableIns.destroy();
      sortableIns = null;
    }
  });

  defineExpose({ reset });
</script>

<script>
  import {
    UserOutlined,
    AnalysisOutlined,
    ShoppingOutlined,
    LogOutlined,
    CopyOutlined,
    MailOutlined,
    TagOutlined,
    ControlOutlined
  } from '@/components/icons';

  export default {
    components: {
      UserOutlined,
      AnalysisOutlined,
      ShoppingOutlined,
      LogOutlined,
      CopyOutlined,
      MailOutlined,
      TagOutlined,
      ControlOutlined
    }
  };
</script>

<style lang="scss" scoped>
  .app-link {
    padding: 8px;
    display: block;
    text-align: center;
    text-decoration: none;
    color: inherit;

    :deep(.app-link-icon) {
      font-size: 30px;
      color: #69c0ff;
      margin: 6px 0 10px 0;

      & > svg {
        stroke-width: 3;
      }
    }
    .monitor-count-value {
      text-align: left;
    }
    .app-link-title {
      text-align: left;
    }
  }

  .el-col.sortable-ghost {
    opacity: 0;
  }
</style>
