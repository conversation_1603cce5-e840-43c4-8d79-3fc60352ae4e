<template>
    <ele-card header="按民族分布"
              :body-style="{ padding: '16px 6px 0 0' }">
        <template #extra>
            <el-link type="primary" underline="never">更多</el-link>
        </template>
        <v-chart ref="saleChartRef"
                 :option="saleChartOption"
                 style="height: 362px"/>
    </ele-card>
</template>

<script setup>
    import {ref, reactive} from 'vue';
    import { ElMessage as EleMessage} from "element-plus";
    import {use} from 'echarts/core';
    import {CanvasRenderer} from 'echarts/renderers';
    import {BarChart} from 'echarts/charts';
    import {GridComponent, TooltipComponent} from 'echarts/components';
    import VChart from 'vue-echarts';
    import {getSaleroomList} from '@/api/dashboard/analysis';
    import {useEcharts} from '@/utils/use-echarts';

    use([CanvasRenderer, BarChart, GridComponent, TooltipComponent]);

    const saleChartRef = ref(null);

    useEcharts([saleChartRef]);

    /** 销售额柱状图配置 */
    const saleChartOption = reactive({});

    /** 门店销售排名数据 */
    const saleroomRankData = ref([
        {name: '工专路 1 号店', value: '333,001'},
        {name: '工专路 2 号店', value: '333,002'},
        {name: '工专路 3 号店', value: '333,003'},
        {name: '工专路 4 号店', value: '333,004'},
        {name: '工专路 5 号店', value: '333,005'},
        {name: '工专路 6 号店', value: '333,006'},
        {name: '工专路 7 号店', value: '333,007'},
        {name: '工专路 8 号店', value: '333,007'},
        {name: '工专路 9 号店', value: '333,007'},
        {name: '工专路 10 号店', value: '333,007'},
    ]);

    /** 门店访问排名数据 */
    const visitsRankData = ref([
        {name: '工专路 1 号店', value: '222,001'},
        {name: '工专路 2 号店', value: '222,002'},
        {name: '工专路 3 号店', value: '222,002'},
        {name: '工专路 4 号店', value: '222,004'},
        {name: '工专路 5 号店', value: '222,005'},
        {name: '工专路 6 号店', value: '222,006'},
        {name: '工专路 7 号店', value: '222,007'}
    ]);

    /** 销售量趋势数据 */
    const saleroomData1 = ref([]);

    /** 访问量趋势数据 */
    const saleroomData2 = ref([]);

    /** 销售量搜索参数 */
    const saleSearch = reactive({
        type: 'saleroom',
        dateType: '1',
        datetime: ['2022-01-01 10:44', '2022-01-03 13:00']
    });

    /** 获取销售量数据 */
    const getSaleroomData = () => {

        saleroomData1.value = [
            {
                "month": "1月",
                "value": 816
            },
            {
                "month": "2月",
                "value": 542
            },
            {
                "month": "3月",
                "value": 914
            },
            {
                "month": "4月",
                "value": 781
            },
            {
                "month": "5月",
                "value": 355
            },
            {
                "month": "6月",
                "value": 796
            },
            {
                "month": "7月",
                "value": 714
            },
            {
                "month": "8月",
                "value": 1195
            },
            {
                "month": "9月",
                "value": 1055
            },
            {
                "month": "10月",
                "value": 271
            },
            {
                "month": "11月",
                "value": 384
            },
            {
                "month": "12月",
                "value": 1098
            }
        ];
        saleroomData2.value = [
            {
                "month": "1月",
                "value": 1098
            },
            {
                "month": "2月",
                "value": 384
            },
            {
                "month": "3月",
                "value": 271
            },
            {
                "month": "4月",
                "value": 1055
            },
            {
                "month": "5月",
                "value": 1195
            },
            {
                "month": "6月",
                "value": 714
            },
            {
                "month": "7月",
                "value": 796
            },
            {
                "month": "8月",
                "value": 355
            },
            {
                "month": "9月",
                "value": 781
            },
            {
                "month": "10月",
                "value": 914
            },
            {
                "month": "11月",
                "value": 542
            },
            {
                "month": "12月",
                "value": 816
            }
        ]
        onSaleTypeChange();
        // getSaleroomList()
        //     .then((data) => {
        //         saleroomData2.value = data.list2;
        //         onSaleTypeChange();
        //     })
        //     .catch((e) => {
        //         EleMessage.error(e.message);
        //     });
    };

    /** 销售量tab选择改变事件 */
    const onSaleTypeChange = () => {
        // if (saleSearch.type === 'saleroom') {
        Object.assign(saleChartOption, {
            tooltip: {
                trigger: 'axis'
            },
            xAxis: [
                {
                    type: 'category',
                    data: saleroomData1.value.map((d) => d.month)
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
                {
                    type: 'bar',
                    data: saleroomData1.value.map((d) => d.value)
                }
            ]
        });
        // } else {
        //     Object.assign(saleChartOption, {
        //         tooltip: {
        //             trigger: 'axis'
        //         },
        //         xAxis: [
        //             {
        //                 type: 'category',
        //                 data: saleroomData2.value.map((d) => d.month)
        //             }
        //         ],
        //         yAxis: [
        //             {
        //                 type: 'value'
        //             }
        //         ],
        //         series: [
        //             {
        //                 type: 'bar',
        //                 data: saleroomData2.value.map((d) => d.value)
        //             }
        //         ]
        //     });
        // }
    };

    getSaleroomData();
</script>

<style lang="scss" scoped>
    .sale-body {
        padding: 16px 0 10px 0;
    }

    .sale-body-title {
        padding: 6px 20px;
    }

    .sale-rank-item {
        display: flex;
        align-items: center;
        padding: 0 20px;
        margin-top: 18px;
        box-sizing: border-box;

        .sale-rank-item-text {
            flex: 1;
            padding-left: 12px;
        }
    }
</style>
