<template>
  <ele-card
    header="按生源地统计"
    style="padding-left: 10px; padding-right: 10px"
  >
    <template #extra>
      <el-link type="primary" underline="never">更多</el-link>
    </template>
    <el-row :gutter="8">
      <el-col :sm="!isSider ? 17 : 24" :xs="24">
        <v-chart
          ref="userMapChartRef"
          :option="userCountMapOption"
          style="height: 408px"
        />
      </el-col>
      <el-col :sm="!isSider ? 7 : 24" :xs="24">
        <div
          v-for="item in userCountDataRank"
          :key="item.name"
          class="user-item"
        >
          <div>{{ item.name }}</div>
          <div class="user-item-body">
            <el-progress
              :stroke-width="8"
              :show-text="false"
              :percentage="item.percent"
            />
          </div>
          <div>{{ item.value }}</div>
        </div>
      </el-col>
    </el-row>
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use, registerMap } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { MapChart } from 'echarts/charts';
  import {
    VisualMapComponent,
    GeoComponent,
    TooltipComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getChinaMapData, getUserCountList } from '@/api/dashboard/monitor';
  import { useEcharts } from '@/utils/use-echarts';
  const props = defineProps({
    isSider: {
      type: Boolean,
      default: false
    }
  });
  const isSider = props.isSider;
  use([
    CanvasRenderer,
    MapChart,
    VisualMapComponent,
    GeoComponent,
    TooltipComponent
  ]);

  const userMapChartRef = ref(null);

  useEcharts([userMapChartRef]);

  /** 用户分布前 10 名 */
  const userCountDataRank = ref([]);

  /** 用户分布地图配置 */
  const userCountMapOption = reactive({});

  /** 获取中国地图数据并注册地图 */
  const registerChinaMap = () => {
    getChinaMapData()
      .then((data) => {
        registerMap('china', data);
        getUserCountData();
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 获取用户分布数据 */
  const getUserCountData = () => {
    // getUserCountList().then((data) => {

    let data = [
      {
        name: '贵州',
        value: 570
      },
      {
        name: '云南',
        value: 8890
      },
      {
        name: '重庆',
        value: 10010
      },
      {
        name: '吉林',
        value: 5056
      },
      {
        name: '山西',
        value: 2123
      },
      {
        name: '天津',
        value: 9130
      },
      {
        name: '江西',
        value: 10170
      },
      {
        name: '广西',
        value: 6172
      },
      {
        name: '陕西',
        value: 9251
      },
      {
        name: '黑龙江',
        value: 5125
      },
      {
        name: '安徽',
        value: 9530
      },
      {
        name: '北京',
        value: 51919
      },
      {
        name: '福建',
        value: 3756
      },
      {
        name: '上海',
        value: 59190
      },
      {
        name: '湖北',
        value: 37109
      },
      {
        name: '湖南',
        value: 8966
      },
      {
        name: '四川',
        value: 31020
      },
      {
        name: '辽宁',
        value: 7222
      },
      {
        name: '河北',
        value: 3451
      },
      {
        name: '河南',
        value: 9693
      },
      {
        name: '浙江',
        value: 62310
      },
      {
        name: '山东',
        value: 39231
      },
      {
        name: '江苏',
        value: 35911
      },
      {
        name: '广东',
        value: 55891
      }
    ];

    const temp = data.sort((a, b) => b.value - a.value);
    const min = temp[temp.length - 1].value || 0;
    const max = temp[0].value || 1;
    //
    const list = temp.length > 10 ? temp.slice(0, 15) : temp;
    userCountDataRank.value = list.map((d) => {
      return {
        name: d.name,
        value: d.value,
        percent: (d.value / max) * 100
      };
    });
    //
    Object.assign(userCountMapOption, {
      tooltip: {
        trigger: 'item',
        borderWidth: 1
      },
      visualMap: {
        min: min,
        max: max,
        text: ['高', '低'],
        calculable: true,
        bottom: 10
      },
      series: [
        {
          name: '用户数',
          label: {
            show: true
          },
          type: 'map',
          map: 'china',
          data: data
        }
      ]
    });
    // }
    // )
    // .catch((e) => {
    //     EleMessage.error(e.message);
    // });
  };
  registerChinaMap();
</script>

<style lang="scss" scoped>
  .user-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .user-item-body {
      flex: 1;
      padding: 0 10px 0 12px;
    }

    :deep(.el-progress-bar__outer) {
      background-color: transparent;
    }
  }
</style>
