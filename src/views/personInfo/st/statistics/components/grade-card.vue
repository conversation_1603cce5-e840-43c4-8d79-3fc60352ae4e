<template>
  <ele-card header="按年级统计" :body-style="{ padding: '16px 6px 0 0' }">
    <template #extra>
      <el-link type="primary" underline="never">更多</el-link>
    </template>
    <v-chart
      ref="browserChartRef"
      :option="browserChartOption"
      style="height: 180px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart } from 'echarts/charts';
  import { TooltipComponent, LegendComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getBrowserCountList } from '@/api/dashboard/monitor';
  import { useEcharts } from '@/utils/use-echarts';

  use([<PERSON><PERSON><PERSON><PERSON><PERSON>, PieC<PERSON>, TooltipComponent, LegendComponent]);

  const browserChartRef = ref(null);

  useEcharts([browserChartRef]);

  /** 浏览器分布饼图配置 */
  const browserChartOption = reactive({});

  /** 获取用户浏览器分布数据 */
  const getBrowserCountData = () => {
    let data = [
      {
        name: '2024级',
        value: 9052
      },
      {
        name: '2023级',
        value: 8900
      },
      {
        name: '2022级',
        value: 9000
      },
      {
        name: '2021级',
        value: 9100
      }
    ];

    Object.assign(browserChartOption, {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      legend: {
        bottom: 0,
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        data: data.map((d) => d.name)
      },
      series: [
        {
          type: 'pie',
          radius: ['45%', '70%'],
          center: ['50%', '43%'],
          label: {
            show: true
          },
          data: data
        }
      ]
    });
  };

  getBrowserCountData();
</script>
