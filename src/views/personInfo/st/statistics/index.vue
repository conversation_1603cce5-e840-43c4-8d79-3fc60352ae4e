<template>
  <ele-page>
    <statistics-card />
    <el-row :gutter="8">
      <el-col :lg="18" :md="18" :sm="24" :xs="24">
        <map-card />
      </el-col>
      <el-col :lg="6" :md="6" :sm="24" :xs="24">
        <el-row :gutter="8">
          <el-col :lg="24" :md="24" :sm="12" :xs="12">
            <browser-card />
          </el-col>
          <el-col :lg="24" :md="24" :sm="12" :xs="12">
            <browser-card1 />
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <sale-card />
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <el-row :gutter="8">
          <el-col :lg="12" :md="12" :sm="12" :xs="12">
            <grade-card />
          </el-col>
          <el-col :lg="12" :md="12" :sm="12" :xs="12">
            <nation-card />
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <!-- <sale-card />
    <el-row :gutter="8">
      <el-col :lg="12" :md="24" :sm="24" :xs="24">
        <browser-card />
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <visit-hour />
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :lg="12" :md="24" :sm="24" :xs="24">
        <sale-card1 />
      </el-col>
      <el-col :lg="12" :md="12" :sm="24" :xs="24">
        <browser-card1 />
      </el-col>
    </el-row> -->
    <!--        <el-row :gutter="8">-->
    <!--            <el-col :lg="12" :md="24" :sm="24" :xs="24">-->
    <!--                <user-rate/>-->
    <!--            </el-col>-->
    <!--            <el-col :lg="12" :md="12" :sm="24" :xs="24">-->
    <!--                <user-satisfaction/>-->
    <!--            </el-col>-->
    <!--        </el-row>-->
  </ele-page>
</template>

<script setup>
  import StatisticsCard from './components/statistics-card.vue';
  import MapCard from './components/map-card.vue';
  import OnlineNum from './components/online-num.vue';
  import BrowserCard from './components/browser-card.vue';
  import BrowserCard1 from './components/browser-card1.vue';
  import GradeCard from './components/grade-card.vue';
  import NationCard from './components/nation-card.vue';

  import UserRate from './components/user-rate.vue';
  import SaleCard from './components/sale-card.vue';
  import SaleCard1 from './components/sale-card1.vue';
  import UserSatisfaction from './components/user-satisfaction.vue';
  import UserLiveness from './components/user-liveness.vue';
  import VisitHour from './components/visit-hour.vue';
</script>

<script>
  export default {
    name: 'DashboardMonitor'
  };
</script>
