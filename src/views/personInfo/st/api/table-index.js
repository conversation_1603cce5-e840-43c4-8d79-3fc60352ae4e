import request from '@/utils/request';

/**
 * 查询不分页
 */
export async function getUserListInfo(routeType,params) {
    const res = await request.get('/userListInfo/'+routeType, {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(routeType,params) {
    const res = await request.get('/userListInfo/'+routeType+'/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(routeType,data) {
    const res = await request.post('/userListInfo/'+routeType+'/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(routeType,data) {
    const res = await request.post('/userListInfo/'+routeType+'/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
