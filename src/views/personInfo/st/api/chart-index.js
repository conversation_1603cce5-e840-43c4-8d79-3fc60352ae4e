import request from '@/utils/request';

/**
 * 图表统计数据excel导出
 */
export async function importData(file, routeType) {
    const formData = new FormData();
    formData.append('file', file);
    // formData.append('codeType', codeType);
    const res = await request.post('/personInfo/' + routeType + '/chartDataExport', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 查询
 */
export async function getChartData(routeType, params) {
    const res = await request.get('/personInfo/' + routeType + '/chartData', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
