<template>
  <ele-card :header="header" :body-style="{ padding: '16px 0px 0px' }">
    <template #extra>
      <div class="hidden-xs-only" style="display: flex; align-items: center;">
        <el-radio-group size="small" v-if="header==='宿管数据'">
          <el-radio-button label="1">今天</el-radio-button>
          <el-radio-button label="2">本周</el-radio-button>
          <el-radio-button label="3">本月</el-radio-button>
          <el-radio-button label="4">本年</el-radio-button>
        </el-radio-group>
        <div v-else class="hidden-md-and-down">
          <el-select v-model="value"
                     placeholder="Select"
                     size="small"
                     style="width: 100px">
            <el-option v-for="item in optionsXnxq"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </div>
      </div>
      <el-link type="primary" underline="never">
        <el-icon>
          <ArrowRight/>
        </el-icon>
      </el-link>
    </template>
    <el-row :gutter="8" ref="wrapRef" style="padding: 0 10px">
      <el-col :lg="12" :md="12" :sm="12" :xs="24" v-for="zhcp in cardData" style="margin-bottom: 10px;">
        <div class="item-box">
          <div class="app-link">
            <div class="app-link-title"
                 style="font-size: 14px; font-weight: bold">{{ zhcp.value }}
            </div>
            <div class="app-link-title" style="color: #808695">{{ zhcp.name }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </ele-card>
</template>

<script setup>
import {ref,} from 'vue';

const props = defineProps({
  header: String,
  cardData: Array,
});
const value = ref('2024-2025')

const optionsXnxq = [
  {
    value: '2024-2025',
    label: '2024-2025',
  },
  {
    value: '2023-2024',
    label: '2023-2024',
  },
  {
    value: '2022-2023',
    label: '2022-2023',
  },
]
</script>

<style lang="scss" scoped>

.item-box {
  height: 41px;
  padding: 7px 3px;
  border-radius: 8px;
  background-color: rgba(245, 251, 255, 1);
}

.app-link {
  padding: 0px;
  display: block;
  text-align: center;
  text-decoration: none;
  user-select: none;
  color: inherit;
}

:deep(.ele-card-body) {
  padding: 16px 0;
}
</style>
