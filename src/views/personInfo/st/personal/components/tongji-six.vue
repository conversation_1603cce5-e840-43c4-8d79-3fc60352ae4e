<template>
  <ele-card header="xxx统计" :body-style="{ padding: '16px 6px' }">
    <template #extra>
      <el-link type="primary" underline="never">更多</el-link>
    </template>
    <v-chart
      ref="browserChartRef"
      :option="browserChartOption"
      style="height: 176px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart } from 'echarts/charts';
  import { TooltipComponent, LegendComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getBrowserCountList } from '@/api/dashboard/monitor';
  import { useEcharts } from '@/utils/use-echarts';

  use([Canvas<PERSON><PERSON><PERSON>, PieC<PERSON>, TooltipComponent, LegendComponent]);

  const browserChartRef = ref(null);

  useEcharts([browserChartRef]);

  /** 浏览器分布饼图配置 */
  const browserChartOption = reactive({});

  /** 获取用户浏览器分布数据 */
  const getBrowserCountData = () => {
    let data = [
      {
        name: '甲类',
        value: 200
      },
      {
        name: '乙类',
        value: 300
      },
      {
        name: '丙类',
        value: 400
      }
    ];

    Object.assign(browserChartOption, {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      legend: {
        bottom: 0,
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        data: data.map((d) => d.name)
      },
      series: [
        {
          type: 'pie',
          radius: ['45%', '70%'],
          center: ['50%', '43%'],
          label: {
            show: false
          },
          data: data
        }
      ]
    });
  };

  getBrowserCountData();
</script>
