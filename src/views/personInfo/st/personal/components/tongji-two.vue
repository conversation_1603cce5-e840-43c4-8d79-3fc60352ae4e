<template>
  <ele-card header="消费数据" :body-style="{ padding: '16px 0 0 ' }">
    <template #extra>
      <el-link type="primary" underline="never"
        ><el-icon><ArrowRight /></el-icon
      ></el-link>
    </template>
    <el-row :gutter="8" ref="wrapRef" style="padding: 0 16px">
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
<!--          <router-link class="app-link">-->
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >1640</div
            >
            <div class="app-link-title" style="color: #808695">消费金额</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >25</div
            >
            <div class="app-link-title" style="color: #808695">消费次数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >1500</div
            >
            <div class="app-link-title" style="color: #808695"
              >人均消费金额</div
            >
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >28</div
            >
            <div class="app-link-title" style="color: #808695"
              >人均消费次数</div
            >
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
    </el-row>
    <v-chart
      ref="visitHourChartRef"
      :option="visitHourChartOption"
      style="height: 135px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getVisitHourList } from '@/api/dashboard/analysis';
  import { useEcharts } from '@/utils/use-echarts';

  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  const visitHourChartRef = ref(null);

  useEcharts([visitHourChartRef]);

  /** 最近 1 小时访问情况折线图配置 */
  const visitHourChartOption = reactive({});

  /** 获取最近 1 小时访问情况数据 */
  const getVisitHourData = () => {
    let data = [
      {
        time: '1月',
        //visits: 15,
        views: 95
      },
      {
        time: '2月',
        //visits: 39,
        views: 120
      },
      {
        time: '3月',
        //visits: 152,
        views: 200
      },
      {
        time: '4月',
        //visits: 94,
        views: 180
      },
      {
        time: '5月',
        //visits: 102,
        views: 250
      },
      {
        time: '6月',
        //visits: 86,
        views: 150
      },
      {
        time: '7月',
        //visits: 39,
        views: 100
      },
      {
        time: '8月',
        //visits: 38,
        views: 50
      },
      {
        time: '9月',
        //visits: 95,
        views: 140
      },
      {
        time: '10月',
        //visits: 30,
        views: 180
      },
      {
        time: '11月',
        //visits: 30,
        views: 110
      },
      {
        time: '12月',
        //visits: 30,
        views: 150
      }
    ];
    Object.assign(visitHourChartOption, {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['xxx', '访问量'],
        right: 20
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: data.map((d) => d.time)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: 'xxx',
          type: 'line',
          smooth: true,
          symbol: 'none',
          areaStyle: {
            opacity: 0.5
          },
          data: data.map((d) => d.views)
        }
        // {
        //   name: '访问量',
        //   type: 'line',
        //   smooth: true,
        //   symbol: 'none',
        //   areaStyle: {
        //     opacity: 0.5
        //   },
        //   data: data.map((d) => d.visits)
        // }
      ]
    });
  };

  getVisitHourData();
</script>
<style lang="scss" scoped>
  .sale-rank-item {
    display: flex;
    align-items: center;
    /*padding: 0 20px;*/
    margin-top: 15px;
    box-sizing: border-box;

    .sale-rank-item-text {
      flex: 1;
      padding-left: 12px;
    }
  }

  .item-box {
    height: 41px;
    padding: 7px 3px;
    border-radius: 8px;
    background-color: rgba(245, 251, 255, 1);
  }

  .app-link {
    padding: 0px;
    display: block;
    text-align: center;
    text-decoration: none;
    user-select: none;
    color: inherit;

    // :deep(.app-link-icon) {
    //   font-size: 30px;
    //   color: #69c0ff;
    //   margin: 6px 0 10px 0;

    //   & > svg {
    //     stroke-width: 3;
    //   }
    // }
  }

  :deep(.ele-card-body) {
    padding: 16px 0;
  }
</style>
