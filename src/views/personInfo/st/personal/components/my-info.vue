<template>
  <ele-card header="基本信息">
    <div class="info-list">
      <div class="info-item" v-for="filed in baseFiledList">
        <div class="info-item-text">{{ filed.label }}：{{ currentData[filed.prop] }}</div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>

const props = defineProps({
  baseFiledList: Array,
  currentData: Object,
});


</script>

<style lang="scss" scoped>
.info-user {
  padding-top: 8px;
  box-sizing: border-box;
  text-align: center;

  .info-user-avatar {
    display: inline-block;
    position: relative;
    cursor: pointer;
    line-height: 0;

    .info-user-avatar-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 30px;
      display: none;
      z-index: 2;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background-color: transparent;
      transition: background-color 0.3s;
    }

    &:hover {
      .info-user-avatar-icon {
        display: block;
      }

      &::after {
        background-color: rgba(0, 0, 0, 0.4);
      }
    }
  }
}

.info-list {
  margin: 0;

  .info-item {
    display: flex;
    align-items: center;

    & > .el-icon {
      font-size: 16px;
    }

    .info-item-text {
      flex: 1;
      padding-left: 8px;
      box-sizing: border-box;
      color: #646464;
    }

    & + .info-item {
      margin-top: 4px;
    }
  }
}

.info-tags .el-tag {
  margin: 0 12px 8px 0;
}
</style>
