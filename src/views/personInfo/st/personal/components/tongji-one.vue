<template>
  <ele-card header="xxx统计" :body-style="{ padding: '16px 6px 0 0' }">
    <template #extra>
      <el-link type="primary" underline="never">更多</el-link>
    </template>
    <v-chart
      ref="visitHourChartRef"
      :option="visitHourChartOption"
      style="height: 191px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getVisitHourList } from '@/api/dashboard/analysis';
  import { useEcharts } from '@/utils/use-echarts';

  use([
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  const visitHourChartRef = ref(null);

  useEcharts([visitHourChartRef]);

  /** 最近 1 小时访问情况折线图配置 */
  const visitHourChartOption = reactive({});

  /** 获取最近 1 小时访问情况数据 */
  const getVisitHourData = () => {
    let data = [
      {
        time: '16:00',
        visits: 15,
        views: 45
      },
      {
        time: '16:05',
        visits: 39,
        views: 169
      },
      {
        time: '16:10',
        visits: 152,
        views: 400
      },
      {
        time: '16:15',
        visits: 94,
        views: 285
      },
      {
        time: '16:20',
        visits: 102,
        views: 316
      },
      {
        time: '16:25',
        visits: 86,
        views: 148
      },
      {
        time: '16:30',
        visits: 39,
        views: 150
      },
      {
        time: '16:35',
        visits: 38,
        views: 234
      },
      {
        time: '16:40',
        visits: 95,
        views: 158
      },
      {
        time: '16:45',
        visits: 30,
        views: 100
      },
      {
        time: '16:50',
        visits: 86,
        views: 266
      }
    ];
    Object.assign(visitHourChartOption, {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['类型1', '类型2'],
        right: 20
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: data.map((d) => d.time)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: '类型1',
          type: 'line',
          smooth: true,
          symbol: 'none',
          areaStyle: {
            opacity: 0.5
          },
          data: data.map((d) => d.views)
        },
        {
          name: '类型2',
          type: 'line',
          smooth: true,
          symbol: 'none',
          areaStyle: {
            opacity: 0.5
          },
          data: data.map((d) => d.visits)
        }
      ]
    });
  };

  getVisitHourData();
</script>
