<template>
  <ele-card header="日常行为" :body-style="{ padding: '16px 0px 0px' }">
    <template #extra>
      <el-link type="primary" underline="never">
        <el-icon><ArrowRight /></el-icon>
      </el-link>
    </template>
    <!-- <el-row :gutter="8">
      <el-col :md="24" :xs="24"> -->
    <el-row :gutter="8" ref="wrapRef" style="padding: 0 16px">
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >3</div
            >
            <div class="app-link-title" style="color: #808695">请假次数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >25</div
            >
            <div class="app-link-title" style="color: #808695">签到次数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >4</div
            >
            <div class="app-link-title" style="color: #808695">晚归次数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >1</div
            >
            <div class="app-link-title" style="color: #808695"
              >宿舍违纪次数</div
            >
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
    </el-row>
    <v-chart
      ref="saleChartRef"
      :option="saleChartOption"
      style="height: 164px"
    />
    <!-- </el-col> -->
    <!-- <el-col :md="7" :xs="24">
        <div
          v-for="(item, index) in saleroomRankData"
          :key="index"
          class="sale-rank-item"
        >
          <el-tag
            size="small"
            :disable-transitions="true"
            :type="index < 3 ? void 0 : 'info'"
            :effect="index < 3 ? 'dark' : 'light'"
            :color="index < 3 ? '#314659' : void 0"
            style="border: none; border-radius: 50%; width: 20px"
          >
            {{ index + 1 }}
          </el-tag>
          <ele-ellipsis class="sale-rank-item-text">
            {{ item.name }}
          </ele-ellipsis>
          <ele-text type="placeholder">{{ item.value }}</ele-text>
        </div>
      </el-col> -->
    <!-- </el-row> -->
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getSaleroomList } from '@/api/dashboard/analysis';
  import { useEcharts } from '@/utils/use-echarts';

  use([CanvasRenderer, BarChart, GridComponent, TooltipComponent]);

  const saleChartRef = ref(null);

  useEcharts([saleChartRef]);

  /** 销售额柱状图配置 */
  const saleChartOption = reactive({});

  /** 门店销售排名数据 */
  const saleroomRankData = ref([
    { name: '工专路 1 号店', value: '333,001' },
    { name: '工专路 2 号店', value: '333,002' },
    { name: '工专路 3 号店', value: '333,003' },
    { name: '工专路 4 号店', value: '333,004' },
    { name: '工专路 5 号店', value: '333,005' },
    { name: '工专路 6 号店', value: '333,006' },
    { name: '工专路 7 号店', value: '333,007' },
    { name: '工专路 8 号店', value: '333,007' },
    { name: '工专路 9 号店', value: '333,007' },
    { name: '工专路 10 号店', value: '333,007' }
  ]);

  /** 门店访问排名数据 */
  const visitsRankData = ref([
    { name: '工专路 1 号店', value: '222,001' },
    { name: '工专路 2 号店', value: '222,002' },
    { name: '工专路 3 号店', value: '222,002' },
    { name: '工专路 4 号店', value: '222,004' },
    { name: '工专路 5 号店', value: '222,005' },
    { name: '工专路 6 号店', value: '222,006' },
    { name: '工专路 7 号店', value: '222,007' }
  ]);

  /** 销售量趋势数据 */
  const saleroomData1 = ref([]);

  /** 访问量趋势数据 */
  const saleroomData2 = ref([]);

  /** 销售量搜索参数 */
  const saleSearch = reactive({
    type: 'saleroom',
    dateType: '1',
    datetime: ['2022-01-01 10:44', '2022-01-03 13:00']
  });

  /** 获取销售量数据 */
  const getSaleroomData = () => {
    saleroomData2.value = [
      {
        month: '学院1',
        value: 816
      },
      {
        month: '学院2',
        value: 542
      },
      {
        month: '学院3',
        value: 914
      },
      {
        month: '学院4',
        value: 781
      },
      {
        month: '学院5',
        value: 355
      },
      {
        month: '学院6',
        value: 796
      },
      {
        month: '学院7',
        value: 714
      },
      {
        month: '学院8',
        value: 1195
      },
      {
        month: '学院9',
        value: 1055
      },
      {
        month: '学院10',
        value: 271
      },
      {
        month: '学院11',
        value: 384
      },
      {
        month: '学院12',
        value: 1098
      },
      {
        month: '学院13',
        value: 1090
      },
      {
        month: '学院14',
        value: 990
      }
    ];
    saleroomData1.value = [
      {
        month: '1月',
        value: 1098
      },
      {
        month: '2月',
        value: 384
      },
      {
        month: '3月',
        value: 271
      },
      {
        month: '4月',
        value: 1055
      },
      {
        month: '5月',
        value: 1195
      },
      {
        month: '6月',
        value: 714
      },
      {
        month: '7月',
        value: 796
      },
      {
        month: '8月',
        value: 355
      },
      {
        month: '9月',
        value: 781
      },
      {
        month: '10月',
        value: 914
      },
      {
        month: '11月',
        value: 542
      },
      {
        month: '12月',
        value: 816
      }
    ];
    onSaleTypeChange();
    // getSaleroomList()
    //     .then((data) => {
    //         saleroomData2.value = data.list2;
    //         onSaleTypeChange();
    //     })
    //     .catch((e) => {
    //         EleMessage.error(e.message);
    //     });
  };

  /** 销售量tab选择改变事件 */
  const onSaleTypeChange = () => {
    // if (saleSearch.type === 'saleroom') {
    Object.assign(saleChartOption, {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: [
        {
          type: 'category',
          data: saleroomData1.value.map((d) => d.month)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          type: 'bar',
          data: saleroomData1.value.map((d) => d.value)
        }
      ]
    });
    // } else {
    //     Object.assign(saleChartOption, {
    //         tooltip: {
    //             trigger: 'axis'
    //         },
    //         xAxis: [
    //             {
    //                 type: 'category',
    //                 data: saleroomData2.value.map((d) => d.month)
    //             }
    //         ],
    //         yAxis: [
    //             {
    //                 type: 'value'
    //             }
    //         ],
    //         series: [
    //             {
    //                 type: 'bar',
    //                 data: saleroomData2.value.map((d) => d.value)
    //             }
    //         ]
    //     });
    // }
  };

  getSaleroomData();
</script>

<style lang="scss" scoped>
  .sale-rank-item {
    display: flex;
    align-items: center;
    /*padding: 0 20px;*/
    margin-top: 15px;
    box-sizing: border-box;

    .sale-rank-item-text {
      flex: 1;
      padding-left: 12px;
    }
  }
  .item-box {
    height: 41px;
    padding: 7px 3px;
    border-radius: 8px;
    background-color: rgba(245, 251, 255, 1);
  }
  .app-link {
    padding: 0px;
    display: block;
    text-align: center;
    text-decoration: none;
    user-select: none;
    color: inherit;

    // :deep(.app-link-icon) {
    //   font-size: 30px;
    //   color: #69c0ff;
    //   margin: 6px 0 10px 0;

    //   & > svg {
    //     stroke-width: 3;
    //   }
    // }
  }
  :deep(.ele-card-body) {
    padding: 16px 0;
  }
</style>
