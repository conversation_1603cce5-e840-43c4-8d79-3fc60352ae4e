<template>
  <ele-card header="二课活动" :body-style="{ padding: '16px 6px' }">
    <template #extra>
      <el-link type="primary" underline="never">
        <el-icon><ArrowRight /></el-icon>
      </el-link>
    </template>
    <v-chart
      ref="browserChartRef"
      :option="browserChartOption"
      style="height: 190px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { PieChart } from 'echarts/charts';
  import { TooltipComponent, LegendComponent } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getBrowserCountList } from '@/api/dashboard/monitor';
  import { useEcharts } from '@/utils/use-echarts';

  use([<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TooltipComponent, LegendComponent]);

  const browserChartRef = ref(null);

  useEcharts([browserChartRef]);

  /** 浏览器分布饼图配置 */
  const browserChartOption = reactive({});

  /** 获取用户浏览器分布数据 */
  const getBrowserCountData = () => {
    let data = [
      {
        name: 'A类',
        value: 100
      },
      {
        name: 'B类',
        value: 2016
      },
      {
        name: '其它',
        value: 869
      }
    ];

    Object.assign(browserChartOption, {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      legend: {
        bottom: 0,
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        data: data.map((d) => d.name)
      },
      series: [
        {
          type: 'pie',
          radius: ['45%', '70%'],
          center: ['50%', '43%'],
          label: {
            show: false
          },
          data: data
        }
      ]
    });
  };

  getBrowserCountData();
</script>
