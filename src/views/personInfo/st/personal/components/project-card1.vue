<!-- 项目进度 -->
<template>
  <ele-card
    header="奖惩信息"
    :body-style="{ padding: '10px', height: '221px' }"
  >
    <template #extra>
      <more-icon @command="handleCommand" />
    </template>
    <ele-pro-table
      :height="180"
      row-key="id"
      :columns="columns"
      :datasource="projectList"
      :show-overflow-tooltip="true"
      highlight-current-row
      :pagination="false"
      :toolbar="false"
      :bottom-line="false"
      size="large"
      class="project-table"
    >
      <template #projectName="{ row }">
        <el-link type="primary" underline="never">
          {{ row.projectName }}
        </el-link>
      </template>
      <template #status="{ row }">
        <ele-text v-if="row.status === 0" type="success">进行中</ele-text>
        <ele-text v-else-if="row.status === 1" type="danger">已延期</ele-text>
        <ele-text v-else-if="row.status === 2" type="warning">
          未开始
        </ele-text>
        <ele-text v-else-if="row.status === 3" type="info">已结束</ele-text>
      </template>
      <template #progress="{ row }">
        <el-progress :percentage="row.progress" />
      </template>
    </ele-pro-table>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import MoreIcon from './more-icon.vue';

  defineProps({
    title: String
  });

  const emit = defineEmits(['command']);

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'projectName',
      label: '评定学年',
      slot: 'projectName',
      minWidth: 110
    },
    {
      prop: 'startDate',
      label: '项目',
      minWidth: 110
    },
    {
      prop: 'endDate',
      label: '等级',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '金额',
      slot: 'status',
      width: 90
    }
  ]);

  /** 项目进度数据 */
  const projectList = ref([]);

  /** 查询项目进度 */
  const queryProjectList = () => {
    projectList.value = [
      {
        id: 1,
        projectName: '项目000000001',
        status: 0,
        startDate: '2020-03-01',
        endDate: '2020-06-01',
        progress: 30
      },
      {
        id: 2,
        projectName: '项目000000002',
        status: 0,
        startDate: '2020-03-01',
        endDate: '2020-08-01',
        progress: 10
      }
      // {
      //   id: 3,
      //   projectName: '项目000000003',
      //   status: 1,
      //   startDate: '2020-01-01',
      //   endDate: '2020-05-01',
      //   progress: 60
      // }
    ];
  };

  const handleCommand = (command) => {
    emit('command', command);
  };

  queryProjectList();
</script>

<style lang="scss" scoped>
  .project-table :deep(.el-progress__text) {
    font-size: 12px !important;
  }
</style>
