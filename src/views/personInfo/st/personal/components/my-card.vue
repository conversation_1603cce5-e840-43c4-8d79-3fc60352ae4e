<template>
  <div class="student-info">
    <div class="info-content" v-if="info">
      <el-avatar :size="66"
                 :src="info&&info.photo?('/api/file/inline/' + JSON.parse(info.photo)[0].id):''">
        {{ info?.xm?.charAt(0) || '' }}
      </el-avatar>
      <div class="details">
        <div class="student-info-container">
          <div class="student-name-row">
            <h3 class="student-name">你好，<span class="highlight">{{ info.xm }}</span></h3>
            <div class="student-meta">
              <span class="student-id">{{ info.xgh }}</span>
            </div>
          </div>

          <div class="student-contact-row">
            <div class="contact-item">
              <el-icon>
                <Iphone/>
              </el-icon>
              <span>{{ info.sjh || '未提供电话' }}</span>
            </div>
            <div class="contact-item">
              <span>{{ info.xymc }} {{ info.zymc }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {use,} from 'echarts/core';
import {CanvasRenderer} from 'echarts/renderers';
import {Iphone} from "@element-plus/icons-vue";
import {computed} from "vue";

use([CanvasRenderer]);

const props = defineProps({
  header: {
    type: String,
    default: '基本信息'
  },
  currentData: Object,
});

const info = computed(() => {
  return props.currentData;
});

</script>

<style lang="scss" scoped>

/* 学生信息区优化 */
.student-info {
  margin-bottom: 20px;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 2px;
}

.details {
  flex: 1;
}

.highlight {
  color: #409EFF;
  font-weight: bold;
}

.student-info-container {
  flex: 1;
  min-width: 0;

  .student-name-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;

    .student-name {
      margin: 0;
      font-size: 18px;
      color: #1e293b;
    }

    .student-meta {
      .student-id {
        font-size: 13px;
        color: #64748b;
      }
    }
  }

  .student-contact-row {
    margin-top: 18px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 13px;
    color: #475569;

    .contact-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        color: #94a3b8;
        font-size: 14px;
      }
    }
  }
}
</style>
