<template>
  <ele-card header="学业成绩" :body-style="{ padding: '16px 0 0 ' }">
    <template #extra>
      <el-link type="primary" underline="never"
        ><el-icon><ArrowRight /></el-icon
      ></el-link>
    </template>
    <el-row :gutter="8" ref="wrapRef" style="padding: 0 16px">
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >13</div
            >
            <div class="app-link-title" style="color: #808695">本期课程数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >2</div
            >
            <div class="app-link-title" style="color: #808695">挂科门数</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >78%</div
            >
            <div class="app-link-title" style="color: #808695">及格率</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
      <el-col :md="6" :sm="6" :xs="6">
        <!-- <ele-card shadow="hover" :body-style="{ padding: 0 }"> -->
        <!-- :to="item.url"  -->
        <div class="item-box">
          <div class="app-link">
            <div
              class="app-link-title"
              style="font-size: 14px; font-weight: bold"
              >101</div
            >
            <div class="app-link-title" style="color: #808695">专业排名</div>
          </div>
        </div>
        <!-- </ele-card> -->
      </el-col>
    </el-row>
    <v-chart
      ref="visitHourChartRef"
      :option="visitHourChartOption"
      style="height: 164px"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { use } from 'echarts/core';
  import { CanvasRenderer } from 'echarts/renderers';
  import { LineChart } from 'echarts/charts';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent
  } from 'echarts/components';
  import VChart from 'vue-echarts';
  import { getVisitHourList } from '@/api/dashboard/analysis';
  import { useEcharts } from '@/utils/use-echarts';

  use([
    CanvasRenderer,
    LineChart,
    GridComponent,
    TooltipComponent,
    LegendComponent
  ]);

  const visitHourChartRef = ref(null);

  useEcharts([visitHourChartRef]);

  /** 最近 1 小时访问情况折线图配置 */
  const visitHourChartOption = reactive({});

  /** 获取最近 1 小时访问情况数据 */
  const getVisitHourData = () => {
    let data = [
      {
        time: '2020 1',
        //visits: 15,
        views: 95
      },
      {
        time: '2020 2',
        //visits: 39,
        views: 120
      },
      {
        time: '2021 1',
        //visits: 152,
        views: 200
      },
      {
        time: '2021 2',
        //visits: 94,
        views: 180
      },
      {
        time: '2022 1',
        //visits: 102,
        views: 250
      },
      {
        time: '2022 2',
        //visits: 86,
        views: 150
      },
      {
        time: '2023 1',
        //visits: 39,
        views: 100
      },
      {
        time: '2023 2',
        //visits: 38,
        views: 50
      },
      {
        time: '2024 1',
        //visits: 95,
        views: 140
      },
      {
        time: '2024 2',
        //visits: 30,
        views: 180
      }
    ];
    Object.assign(visitHourChartOption, {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['xxx', '访问量'],
        right: 20
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: data.map((d) => d.time)
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: 'xxx',
          type: 'line',
          smooth: true,
          symbol: 'none',
          areaStyle: {
            opacity: 0.5
          },
          data: data.map((d) => d.views)
        }
        // {
        //   name: '访问量',
        //   type: 'line',
        //   smooth: true,
        //   symbol: 'none',
        //   areaStyle: {
        //     opacity: 0.5
        //   },
        //   data: data.map((d) => d.visits)
        // }
      ]
    });
  };

  getVisitHourData();
</script>
<style lang="scss" scoped>
  .sale-rank-item {
    display: flex;
    align-items: center;
    /*padding: 0 20px;*/
    margin-top: 15px;
    box-sizing: border-box;

    .sale-rank-item-text {
      flex: 1;
      padding-left: 12px;
    }
  }

  .item-box {
    height: 41px;
    padding: 7px 3px;
    border-radius: 8px;
    background-color: rgba(245, 251, 255, 1);
  }

  .app-link {
    padding: 0px;
    display: block;
    text-align: center;
    text-decoration: none;
    user-select: none;
    color: inherit;

    // :deep(.app-link-icon) {
    //   font-size: 30px;
    //   color: #69c0ff;
    //   margin: 6px 0 10px 0;

    //   & > svg {
    //     stroke-width: 3;
    //   }
    // }
  }

  :deep(.ele-card-body) {
    padding: 16px 0;
  }
</style>
