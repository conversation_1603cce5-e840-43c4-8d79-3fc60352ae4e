<template>
  <ele-page class="workplace-page">
    <el-row :gutter="8">
      <el-col :lg="6" :md="6" :sm="24" :xs="24">
        <ele-card :body-style="{ padding: '16px 0px 0px' }">
          <my-card :currentData="currentData"/>
        </ele-card>
        <tongji-zhcp header="综测成绩" :cardData="zhcpCkData"/>
        <tongji-zhcp header="宿管数据" :cardData="sgData"/>
      </el-col>
      <el-col :lg="9" :md="9" :sm="24" :xs="24">
        <tongji-three/>
        <project-card/>
        <tongji-five/>
      </el-col>
      <el-col :lg="9" :md="9" :sm="24" :xs="24">
        <tongji-four/>
        <project-card1/>
        <tongji-two/>
      </el-col>
    </el-row>
    <!--    <el-row :gutter="8">-->
    <!--      <el-col :lg="6" :md="6" :sm="24" :xs="24">-->
    <!--        <tongji-zhcp header="综测成绩" :cardData="zhcpCkData"/>-->
    <!--        <tongji-zhcp header="宿管数据" :cardData="sgData"/>-->
    <!--        &lt;!&ndash;        <my-info :baseFiledList="baseFiledList" :currentData="currentData"/>&ndash;&gt;-->
    <!--      </el-col>-->
    <!--      <el-col :lg="9" :md="9" :sm="24" :xs="24">-->
    <!--        <project-card/>-->
    <!--        <tongji-five/>-->
    <!--      </el-col>-->
    <!--      <el-col :lg="9" :md="9" :sm="24" :xs="24">-->
    <!--        <project-card1/>-->
    <!--        <tongji-two/>-->
    <!--      </el-col>-->
    <!--    </el-row>-->
  </ele-page>
</template>

<script setup>
import MyCard from './components/my-card.vue';
import MyInfo from './components/my-info.vue';
import TongjiFive from './components/tongji-five.vue';
import TongjiTwo from './components/tongji-two.vue';
import TongjiThree from './components/tongji-three.vue';
import TongjiFour from './components/tongji-four.vue';
import ProjectCard from './components/project-card.vue';
import ProjectCard1 from './components/project-card1.vue';
import {ref, unref} from "vue";
import {useRoute, useRouter} from "vue-router";
import {getFieldList, getPersonInfo} from "@/views/personInfo/st/api/index.js";
import {ElMessage as EleMessage} from "element-plus";
import TongjiZhcp from "@/views/personInfo/st/personal/components/tongji-zhcp.vue";

const route = useRoute();

const {currentRoute, push} = useRouter();
const {path,} = unref(currentRoute);
let xgh = route.params?.id ?? '';
let routeType = path.split('/')[4];
console.log(routeType, xgh)


/** 请求状态 */
const loading = ref(true);
/** 表单页面回显数据 */
const currentData = ref({});

const zhcpCkData = ref([
  {name: '综测成绩', value: '95'},
  {name: '等级', value: '优'},
  {name: '班级排名', value: '9'},
  {name: '专业排名', value: '35'},
  {name: '班级排名百分比', value: '9%'},
  {name: '专业排名百分比', value: '2%'},
]);

const sgData = ref([
  {name: '假期留宿申请数', value: '10'},
  {name: '宿舍报修次数', value: '3'},
  {name: '进宿次数', value: '9'},
  {name: '出宿次数', value: '5'},
]);


/** 根据学工号查询 */
const queryPersonInfo = () => {
  loading.value = true;
  getPersonInfo(routeType, xgh).then((list) => {
    loading.value = false;
    list['avatar'] = null;
    currentData.value = list;
    console.log(currentData.value)
    // currentData.value.enType = enterType;
    // 图片加载完成
    // imageLoaded.value = true;
    // let title =
    //   routeType === 'student'
    //     ? list.xm + '-成长档案'
    //     : list.xm + '-教师详情';
    // title = title + (enterType === 'preview' ? '查看' : '编辑')
    // setPageTabTitle(title);
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};


const baseFiledList = ref([]);
const initFieldList = async () => {
  const data = await getFieldList(routeType, {listShowFlag: '是', tempField: '否'});
  console.log(data)
  if (data.length > 0) {
    data.forEach((e) => {
      if (!["xgh", "xm"].includes(e.fieldEn)) {
        if (e.controlType === 'select' && e.loadDataType) {
          if (e.loadDataType === 'other') {
            let setFinallyEn = ""
            switch (e.loadDataUrl) {
              case '/code/codeBjb':
                setFinallyEn = "bjmc";
                break;
              case '/code/codeZyb':
                setFinallyEn = "zymc";
                break;
              case '/code/codeDwb/listAll':
                setFinallyEn = 'xymc';
                break;
              case '/code/codeDwb':
                setFinallyEn = "xymc";
                break;
            }
            baseFiledList.value.push({
              prop: setFinallyEn,
              label: e.fieldZh,
            });
          } else {
            let finallyProp = '';
            switch (e.loadDataType) {
              case 'pycc':
                finallyProp = 'pyccmc';
                break;
              case 'nj':
                finallyProp = 'njmc';
                break;
              default:
                finallyProp = e.fieldEn;
                break;
            }
            baseFiledList.value.push({
              // prop: e.loadDataType === "pycc" ? "pyccmc" : e.fieldEn,
              prop: finallyProp,
              label: e.fieldZh,
            });
          }
        } else {
          baseFiledList.value.push({
            prop: e.fieldEn,
            label: e.fieldZh,
          });
        }
      }
    });
  }
  console.log("baseFiledList===", baseFiledList.value)
};

initFieldList()
queryPersonInfo();
</script>
