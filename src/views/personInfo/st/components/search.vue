<!-- 搜索表单 -->
<template>
  <el-form ref="formRef"
           size="small"
           @keyup.enter="search"
           @submit.prevent=""
           label-width="auto">
    <el-row :gutter="8">
      <template v-for="(item, index) in initItems">
        <template v-if="index > 2">
          <el-col v-if="searchExpand" :lg="6" :md="12" :sm="12" :xs="12">
            <ProFormItem v-if="searchExpand"
                         :item="item"
                         :model="initModel"
                         @updateItemValue="
                  (prop, value) => updateFormValue(item, prop, value)">
              <template
                v-for="name in Object.keys($slots).filter(
                    (k) =>
                      !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
                  )"
                #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
        <el-col v-else :lg="6" :md="12" :sm="12" :xs="12">
          <ProFormItem :item="item"
                       :model="initModel"
                       @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)">
            <template v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
                )"
                      #[name]="slotProps">
              <slot :name="name" v-bind="slotProps || {}"></slot>
            </template>
          </ProFormItem>
        </el-col>
      </template>
      <el-col :lg="6" :md="12" :sm="12" :xs="12">
        <el-form-item>
          <div :style="{
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: searchExpand ? 'flex-end' : 'flex-start'
                  }">
            <el-button type="primary" size="small" plain @click="search">查询
            </el-button>
            <el-button size="small" @click="searchSet">高级查询
            </el-button>
            <el-button size="small" @click="reset">重置</el-button>
            <!--              @click="onExpend"-->
            <el-link type="primary"
                     underline="never"
                     @click="searchExpand = !searchExpand"
                     style="margin-left: 10px">
              <template v-if="searchExpand">
                <span>收起</span>
                <el-icon style="vertical-align: -1px">
                  <ArrowUp/>
                </el-icon>
              </template>
              <template v-else>
                <span>展开</span>
                <el-icon style="vertical-align: -2px">
                  <ArrowDown/>
                </el-icon>
              </template>
            </el-link>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!-- 编辑弹窗 -->
  <SearchSet v-model="showSet" @done="reload"/>
</template>

<script setup>
import {nextTick, ref, unref} from 'vue';
import {useRouter} from 'vue-router';
import {ArrowDown, ArrowUp} from '@/components/icons';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import SearchSet from '../components/search-set.vue';
import {getFieldList} from '../api/index';
import {
  arrayTypes,
  regionsArrayTypes,
  selectTypes,
  stringTypes,
  uploadTypes,
} from '@/components/ProForm/util';

const emit = defineEmits(['search']);
const formRef = ref(null);
const initItems = ref([]);
const initModel = ref({});
const showSet = ref(false);
const searchExpand = ref(false);

const {currentRoute} = useRouter();
const routeType = unref(currentRoute).path.split('/')[3];

const approvalData = ref({
  groupName: '',
  title: '',
  id: 'studentSearch',
  infoType: 'studentSearch',
  listFlag: '否',
});

const search = () => emit('search', {...initModel.value});

const reload = (data) => {
  emit('search', {customQueryData: data});
};

const searchSet = () => {
  showSet.value = true;
};

const reset = () => {
  nextTick(() => {
    formRef.value?.clearValidate?.();
    formRef.value?.resetFields?.();
    initModel.value = {};
    search();
  });
};

const updateFormValue = (item, prop, value) => {
  console.log('updateFormValue', item, prop, value);
  initModel.value[prop] = value;

  if (item.type === 'regions') {
    initItems.value.forEach((f) => {
      if (f.prop === `${item.prop}_regionsDetail`) f.showFlag = '是';
    });
  }
  // 院系 -> 专业 -> 班级联动
  if (item.selfFieldLink === 'xymc' && item.nextField === 'zymc') {
    // 清空专业和班级的值
    initModel.value.zyid = null;
    initModel.value.bjid = null;

    // 更新专业下拉框
    nextTick(() => {
      initItems.value.forEach((f) => {
        if (f.prop === 'zymc') {
          f.showFlag = '是';
          f.props.dicQueryParams.params = { xyid: value };
          f.props.refresh = Date.now(); // 强制刷新
        }
      });
    });
  }
  if (item.fieldLinks?.length) {
    const visibleFields = item.fieldLinks.filter((f) => f.fieldVal === value);
    nextTick(() => {
      visibleFields.forEach((link) => {
        initModel.value[link.linkField] = '';
        initItems.value.forEach((f) => {
          if (f.prop === link.linkField) {
            f.showFlag = link.showFlag;
            if (link.linkFieldDataType) {
              f.props = {
                code: link.linkFieldDataType,
                filterable: true,
              };
            }
          }
        });
      });
    });
  }
};

const initFieldList = async () => {
  const fields = await getFieldList(routeType, {
    tempField: '否',
    queryShowFlag: '是',
    listFlag: '否',
  });

  const items = [];
  const model = {};

  fields.forEach((e, index) => {
    let props = {};
    let selfFieldLink = '';
    let nextField = '';
    let type = e.controlType;

    if (e.loadDataType) {
      type = 'dictSelect';
      if (e.loadDataType && e.loadDataType !== 'other') {
        props = {
          code: e.loadDataType,
          dicQueryParams: {
            getValType: e.loadDataType !== 'pycc' ? (e.valueField || 'name') : '',
            valueField: e.valueField || 'id',
            textField: e.textField || 'name',
          },
          filterable: true,
        };
      } else {
        if (e.loadDataUrl === '/code/codeDwb') {
          selfFieldLink = 'xymc';
          nextField = 'zymc';
        } else if (e.loadDataUrl === '/code/codeZyb') {
          selfFieldLink = 'zymc';
          nextField = 'bjmc';
        } else if (e.loadDataUrl === '/code/codeBjb') {
          selfFieldLink = 'bjmc';
        } else {
          selfFieldLink = 'code_' + e.fieldEn;
        }
        props = {
          code: selfFieldLink,
          filterable: true,
          dicQueryParams: {
            dictFieldUrl: e.loadDataUrl,
            valueField: e.valueField,
            textField: e.textField,
          },
        };
      }
    } else if (['fileUpload', 'imageUpload'].includes(type)) {
      props = {limit: e.regExpression ? +e.regExpression : 1};
    }

    items.push({
      key: `fieldKey_${index}`,
      label: e.fieldZh,
      prop: e.fieldEn,
      type,
      typeKey: e.controlType,
      required: false,
      showFlag: e.showFlag,
      fieldLinks: e.fieldLinks,
      selfFieldLink,
      nextField,
      props,
    });

    if (type === 'regions') {
      items.push({
        key: `fieldKey_detail_${index}`,
        label: '详细地址',
        prop: `${e.fieldEn}_regionsDetail`,
        type: 'input',
        showFlag: '否',
        required: false,
      });
    }

    if (uploadTypes.includes(type) || regionsArrayTypes.includes(type) || arrayTypes.includes(type)) {
      model[e.fieldEn] = [];
    } else {
      model[e.fieldEn] = '';
    }
  });

  initItems.value = items;
  initModel.value = model;
  console.log(initItems.value);
};

initFieldList();
defineExpose({initModel});
</script>
