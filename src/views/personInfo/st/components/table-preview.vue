<template>
  <ele-card class="ele-card-item" :body-style="{height:'auto',padding: '10px 5px 10px 5px!important' }"
            :header="currentGroup.title">
    <template #extra>
      <el-link v-if="canOption" :icon="PlusOutlined"
               type="primary" underline="never"
               @click="add">新增
      </el-link>
    </template>
    <!--    currentGroup=={{ currentGroup }}<br/>-->
    <!--    form.users=={{form.users}}<br/>-->
    <el-form ref="formRef" size="small" :model="form" label-width="0px" @submit.prevent="">
      <div style="overflow: auto">
        <ele-table size="small" border style="min-width: 580px; table-layout: fixed">
          <colgroup>
            <col width="40px"/>
            <col v-for="header in initTableHeader"/>
            <col width="100px" v-if="canOption"/>
          </colgroup>
          <thead>
          <tr>
            <th style="position: sticky; left: 0; z-index: 98"></th>
            <th v-for="header in initTableHeader">{{ header.label }}</th>
            <th v-if="canOption" :style="{
                                  textAlign: 'center',
                                  position: 'sticky',
                                  right: 0,
                                  zIndex: 98
                                }"> 操作
            </th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(row, index) in form.users" :key="row.key" style="height: auto;">
            <td :style="{
                              textAlign: 'center',
                              position: 'sticky',
                              left: 0,
                              zIndex: 98 }">
              {{ index + 1 }}
            </td>
            <td v-for="item in initItems">
              <TableFormItem v-if="row.isEdit"
                             :item="item"
                             :index="index"
                             :model="row"
                             @updateValue="(value) => updateValue(index,item.prop, value)">
                <template
                  v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                  #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"></slot>
                </template>
              </TableFormItem>
              <div v-else class="editable-cell-text">{{ row[item.prop] }}</div>
            </td>
            <td v-if="canOption" :style="{ textAlign: 'center',
                              position: 'sticky',
                              right: 0,
                              zIndex: 98}">
              <div style="display: inline; align-items: center">
                <el-link v-if="row.isEdit"
                         type="success"
                         underline="never"
                         @click="done(row)">
                  完成
                </el-link>
                <el-link v-else type="primary"
                         underline="never"
                         @click="edit(row)">
                  编辑
                </el-link>
                <el-divider direction="vertical" style="margin: 0 8px"/>
                <el-link type="danger"
                         underline="never"
                         @click="remove(row, index)">
                  删除
                </el-link>
              </div>
            </td>
          </tr>
          <tr v-if="!form.users || !form.users.length">
            <td :colspan="canOption?initTableHeader.length+2:initTableHeader.length+1"
                style="text-align: center">
              <img style="width: 80px;margin-left: 13px;" src="/src/assets/u65.png">
              <ele-text style="padding:0" type="secondary">
                暂无数据
              </ele-text>
            </td>
          </tr>
          </tbody>
        </ele-table>
      </div>
    </el-form>
  </ele-card>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {PlusOutlined} from '@/components/icons';
import {getFieldList, operationGroupDtxx} from '@/views/personInfo/st/api';
import {getUserListInfo, operation, removes} from '../api/table-index';
import {arrayTypes, selectTypes, stringTypes} from '@/components/ProForm/util';
import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
import {useFormData} from '@/utils/use-form-data';
import {getFormTemplateField} from '@/views/zizhu/api/form-template-field-index.js';
import {getDictionaryField} from '@/views/system/sphfw/dictionary-field/api/index.js';
import {
  getApplicationListInfos,
  getDicListGroupField,
  getFormApplyFieldList, operationApplyListInfo, removesApplyListInfo,
} from '@/views/zizhu/apply/api/index.js';
import {useRouter} from 'vue-router';
import {useUserStore} from '@/store/modules/user.js';
import $func from '@/plugins/preload.js';
import {getListGroupConfigById} from '@/views/system/sphfw/list-group/api/index.js';

const props = defineProps({
  /** 表单标题宽度 */
  labelWidth: {
    type: String,
    default: '100px',
  },
  /** 表单显示列数 */
  grid: Number,
  currentGroup: Object,
  routeType: String,
  xgh: String,
  data: Object,
  isAddMark: Number,
  queryParams: Object,//根据xgh过滤培养层次
});
const {push, currentRoute} = useRouter();
const userStore = useUserStore();
/** 当前用户信息 */
const loginUser = computed(() => userStore.info ?? {});
/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields] = useFormData({
  users: [],
});

/** 添加 */
const add = () => {
  form.users.push({
    key: Date.now() + '-' + form.users.length,
    ...initModel.value,
    isEdit: true,
  });
};

/** 删除 */
const remove = (_row, index) => {
  form.users.splice(index, 1);
  if (_row.id) {
    // if (props.currentGroup && props.currentGroup.mark === 'zzapply') {
    if (props.currentGroup && props.currentGroup.enType !== 'preview') {
      if (props.currentGroup.listFlag === '是' && !props.currentGroup.infoType && listFlagConfigData.value) {
        removeGroupDtxx(_row);
      } else {
        removeDiyGroupData(_row);
      }
    } else {
      removeCom(_row);
    }
  }
};

const removeGroupDtxx = (_row) => {
  let str = listFlagConfigData.value.deleteDataUrl;
  let AjaxUrl = str.replace('{userType}', props.currentGroup.userType);
  operationGroupDtxx(AjaxUrl, [_row.id]).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};
const removeDiyGroupData = (_row) => {
  removesApplyListInfo([_row.id]).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};
const removeCom = (_row) => {
  removes(props.routeType, [_row.id]).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};
/** 资助自定义组，申请数据提交 */
const submitDiyGroupData = (row) => {
  loading.value = true;
  let obj = {
    xgh: loginUser.value.xgh,
    groupId: props.currentGroup.id,
    projectId: props.currentGroup.projectId,
    applicationId: props.currentGroup.applicationId,
    year: props.currentGroup.year,
    type: props.currentGroup.type,
    ...row,
  };
  operationApplyListInfo(obj).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    queryApplicationListInfos();
  });
};

/** 多条信息组表单提交 */
const submitGroupDtxx = (row) => {
  loading.value = true;
  let obj = {
    xgh: props.xgh,
    userType: props.routeType,
    groupId: props.currentGroup.id,
    groupName: props.currentGroup.title,
    ...row,
  };
  let str = listFlagConfigData.value.operationDataUrl;
  if (str) {
    let AjaxUrl = str.replace('{userType}', props.currentGroup.userType);
    operationGroupDtxx(AjaxUrl, obj).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      row.isEdit = false;
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      let newObj = {
        groupId: props.currentGroup.id,
        groupName: props.currentGroup.groupName,
        xgh: props.xgh,
      };
      queryUserListInfo(props.routeType, newObj);
    });
  }
};
/** 表单提交 */
const submit = (row) => {
  loading.value = true;
  let obj = {
    xgh: props.xgh,
    userType: props.routeType,
    groupId: props.currentGroup.id,
    groupName: props.currentGroup.title,
    ...row,
  };
  operation(props.routeType, obj).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    row.isEdit = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 更新值 */
const updateValue = (index, prop, value) => {
  form.users[index][prop] = value;
};

/** 完成 */
const done = (row) => {
  formRef['value']?.validate?.((valid, obj) => {
    if (!valid) {
      const errors = obj ? Object.keys(obj).length : 0;
      EleMessage.error(`有 ${errors} 项校验不通过`);
    } else {
      if (props.currentGroup && props.currentGroup.enType !== 'preview') {
        if (props.currentGroup.listFlag === '是' && !props.currentGroup.infoType && listFlagConfigData.value) {
          // .operationDataUrl
          submitGroupDtxx(row);
        } else {
          submitDiyGroupData(row);
        }
      } else {
        submit(row);
      }
    }
  });
};

/** 编辑 */
const edit = (row) => {
  row.isEdit = true;
};

/** 字段 */
const fieldData = ref([]);
/** 请求状态 */
const loading = ref(true);

/** 表单项 */
const initItems = ref([]);
/** 获取表单字段 */
const initModel = ref([]);
const initTableHeader = ref([]);

const listFlagConfigData = ref({});

const queryListGroupConfigById = async (row) => {
  if (row.listFlag === '是') {//是否多条
    if (row.listGroupId) {
      const result = await getListGroupConfigById(row.listGroupId).catch((e) => console.error(e));
      listFlagConfigData.value = result;
    }
  }
};

const queryDictionaryField = (group) => {
  loading.value = true;
  getFieldList(props.routeType, {
    groupId: group.id, groupName: group.groupName,
    ...props.queryParams,
  }).
    then((list) => {
      loading.value = false;
      fieldData.value = list;
    }).catch((e) => {
    EleMessage.error(e.message);
  });
};

const queryUserListInfo = (userType, obj) => {
  loading.value = true;
  getUserListInfo(userType, obj).then((list) => {
    loading.value = false;
    form.users = list;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

watch(
  () => fieldData.value, (fieldData) => {
    // console.log("table =====fieldData",fieldData)
    let fieldResult = [];
    initTableHeader.value = [];

    if (fieldData && fieldData.length > 0) {
      // ✅ 新增：过滤掉 showFlag === '否' 的字段
      const visibleFields = fieldData.filter((e) => e.showFlag !== '否');
      visibleFields.forEach((e, index) => {
        let tableColumns = {
          columnKey: e.fieldEn,
          label: e.fieldZh,
          minWidth: 120,
          // slot: 'name'
        };
        initTableHeader.value.push(tableColumns);

        let props = {};
        let type = e.controlType;
        if (e.controlType === 'select') {
          if (e.loadDataType && e.loadDataType !== 'other') {
            type = 'dictSelect';
            props = {
              code: e.loadDataType,
              filterable: true,
              dicQueryParams: {
                'valueField': 'name',
              },
            };
          } else {
            type = 'dictSelect';
            props = {
              code: e.loadDataType,
              filterable: true,
              dicQueryParams: {
                dictFieldUrl: e.loadDataUrl,
              },
            };
          }
        }
        let newObj = Object.assign({
          key: 'fieldKey_' + index,
          editable: true,
          label: e.fieldZh,
          prop: e.fieldEn,
          type: type,
          required: e.required === '是',
        }, {
          props: props,
        });
        fieldResult.push(newObj);
      });
      initItems.value = fieldResult;
    }

    let fieldFormResult = {};
    if (fieldResult && fieldResult.length > 0) {
      fieldResult.forEach(item => {
        if (item.type) {
          // if ('sliderRange' === item.type) {
          //     fieldResult[item.prop] = [30, 60];
          // }
          if (arrayTypes.includes(item.type)) {
            fieldFormResult[item.prop] = [];
          }
          if (stringTypes.includes(item.type) || selectTypes.includes(item.type)) {
            fieldFormResult[item.prop] = '';
          }
        }
      });
      initModel.value = fieldFormResult;
    }
  },
);

/** 资助申请查询组的申请字段信息 */
const queryFormTemplateField = () => {
  loading.value = true;
  getFormTemplateField({
    projectId: props.currentGroup.projectId,
    groupId: props.currentGroup.id,
    type: props.routeType,
  }).then((list) => {
    loading.value = false;
    fieldData.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/**资助项目申请- 查询组的申请字段信息 */
const queryFormApplyFieldList = () => {
  loading.value = true;
  getFormApplyFieldList({
    projectId: props.currentGroup.projectId,
    applicationId: props.currentGroup.applicationId,
    groupId: props.currentGroup.id,
    type: props.routeType,
  }).then((list) => {
    loading.value = false;
    fieldData.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const queryApplicationListInfos = () => {
  loading.value = true;
  getApplicationListInfos({
    projectId: props.currentGroup.projectId,
    applicationId: props.currentGroup.applicationId,
    groupId: props.currentGroup.id,
    type: props.routeType,
  }).then((list) => {
    console.log('22222=====', list);
    loading.value = false;
    list.forEach((item) => {
      item.isEdit = props.currentGroup.enType !== 'sqpreview';
    });
    form.users = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 学生/角色基础字段信息 */
const queryDicField = () => {
  loading.value = true;
  getDictionaryField({
    groupId: props.currentGroup.dicGroupId,
  }).then((list) => {
    loading.value = false;
    fieldData.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 资助项目申请- 学生/角色基础字段信息 */
const queryDicListGroupField = () => {
  loading.value = true;
  getDicListGroupField({
    projectId: props.currentGroup.projectId,
    dicGroupId: props.currentGroup.dicGroupId,
    applicationId: '',
  }).then((list) => {
    loading.value = false;
    fieldData.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

let canOption = computed(() => {
  return props.currentGroup && (!props.currentGroup.dicGroupId &&
    props.currentGroup.enType !== 'preview' && props.currentGroup.enType !== 'sqpreview');
});

watch(
  () => props.currentGroup, (groupData) => {
    if (groupData) {
      if (groupData.infoType) {
        if (groupData.dicGroupId) {
          // if (groupData.mark === 'zzapply') {
          if (groupData.enType !== 'preview') {
            queryDicListGroupField();
            if (loginUser.value.xgh) {
              let newObj = {
                groupId: groupData.dicGroupId,
                groupName: groupData.groupName,
                xgh: loginUser.value.xgh,
              };
              queryUserListInfo('student', newObj);
            }
          } else {
            queryDicField();
          }
        } else {
          // if (groupData.mark === 'zzapply') {
          if (groupData.enType !== 'preview') {
            queryFormApplyFieldList();
            queryApplicationListInfos();
          } else {
            queryFormTemplateField();
            queryApplicationListInfos();
          }
        }
      } else {
        queryDictionaryField(groupData);
        if (props.xgh) {
          let newObj = {
            groupId: groupData.id,
            groupName: groupData.groupName,
            xgh: props.xgh,
          };
          queryUserListInfo(props.routeType, newObj);
        }
        /**
         * 获取多条信息组的添加/编辑/删除接口（教育经历/家庭成员。。。）
         */
        queryListGroupConfigById(groupData);
      }
    }
  },
  {
    immediate: true,
  },
);
</script>
<style lang="scss" scoped>

/* 表单验证气泡形式 */
.form-error-popper.el-form-item > :deep(.el-form-item__content) {
  & > .el-form-item__error {
    position: absolute;
    left: 0;
    top: calc(0px - 100% - 6px);
    width: max-content;
    color: #fff;
    font-size: 12px;
    background: var(--el-color-danger);
    transition: all 0.2s;
    padding: 10px;
    border-radius: 4px;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;

    &:after {
      content: '';
      border: 6px solid transparent;
      border-top-color: var(--el-color-danger);
      position: absolute;
      left: 12px;
      bottom: -11px;
    }
  }

  &:hover > .el-form-item__error {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
  }
}
</style>
