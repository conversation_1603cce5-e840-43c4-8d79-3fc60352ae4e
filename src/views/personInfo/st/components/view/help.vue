<template>
  <ele-drawer title="使用帮助"
              :size="420"
              :append-to-body="true"
              style="max-width: 100%;"
              :model-value="modelValue"
              :body-style="{ padding: '5px!important' }"
              @update:modelValue="updateModelValue">
    <ele-card header="帮助引导"
              :body-style="{padding: '5px 8px 10px 8px!important',overflow: 'hidden'}">
      <div class="diyEletextColor">
        <ele-text size="md">1、业务模块简介</ele-text>
        <ele-text size="md"> 2、学生基本信息查看列表</ele-text>
        <ele-text size="md"> 3、学生基本信息查询</ele-text>
        <ele-text size="md"> 4、高级查询功能</ele-text>
        <ele-text size="md"> 5、导入和导出功能</ele-text>
        <ele-text size="md"> 6、学生基本信息修改</ele-text>
        <ele-text size="md"> 7、查看学生个人数据看板</ele-text>
      </div>
    </ele-card>
    <ele-card header="常见问题"
              :body-style="{padding: '5px 8px 10px 8px!important',overflow: 'hidden'}">
      <div class="diyEletextColor">
        <ele-text size="md">1、如何创建多条件组合查询？</ele-text>
        <ele-text size="md">2、如何修改基本信息？</ele-text>
        <ele-text size="md"> 3、导出出错有哪些原因？</ele-text>
        <ele-text size="md">4、如何删除记录？</ele-text>
        <ele-text size="md"> 5、自定义统计问题？</ele-text>
      </div>
    </ele-card>
    <ele-card header="高级查询功能"
              :body-style="{padding: '5px 8px 10px 8px!important',overflow: 'hidden'}">
      <ele-text size="md">从左侧菜单进行“学生基本信息管理”功能，在列表页的查询条件块找到“高级查询”按钮，如下图：
      </ele-text>
      <el-image :src="u811" fit="fill"/>
    </ele-card>
    <ele-card header="导入和导出功能"
              :body-style="{padding: '5px 8px 10px 8px!important',overflow: 'hidden'}">
      <ele-text size="md">学生基本信息数据支持导入和导出功能，和批量导入导出功能，具体操作流程如下：
      </ele-text>
      <el-image :src="u817" fit="fill"/>
    </ele-card>
  </ele-drawer>
</template>
<script setup>

import u811 from "@/assets/u811.png";
import u817 from "@/assets/u817.png";
// 定义emit
const emits = defineEmits(['update:modelValue'])
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  paperId: String,
  RandomString: String,
  testPaperData: Object
});

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

</script>

<style scoped lang="scss">

/*帮助手册，文字随系统色变*/
.diyEletextColor .ele-text {
  color: var(--el-color-primary);
}
</style>
