<template>
  <ele-drawer
    :title="title"
    :size="420"
    @opened="handleOpened"
    class="statisticDrawer"
    :append-to-body="true"
    :model-value="modelValue"
    :body-style="{ padding: '5px!important', background: '#eaecef' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-row :gutter="8" v-if="modelOpened">
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <StatisticsCard :isSider="true" />
      </el-col>
      <!--        <el-col :lg="24" :md="24" :sm="24" :xs="24">-->
      <!--          <MapCard v-if="echartVisible" :isSider="true"/>-->
      <!--        </el-col>-->
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <BrowserCard />
      </el-col>
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <BrowserCard1 />
      </el-col>
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <SaleCard />
      </el-col>
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <NationCard />
      </el-col>
      <el-col :lg="24" :md="24" :sm="24" :xs="24">
        <GradeCard />
      </el-col>
    </el-row>
  </ele-drawer>
</template>
<script setup>
import StatisticsCard from '@/views/personInfo/st/statistics/components/statistics-card.vue';
import BrowserCard from '@/views/personInfo/st/statistics/components/browser-card.vue';
import GradeCard from '@/views/personInfo/st/statistics/components/grade-card.vue';
import SaleCard from '@/views/personInfo/st/statistics/components/sale-card.vue';
import BrowserCard1 from '@/views/personInfo/st/statistics/components/browser-card1.vue';
import NationCard from '@/views/personInfo/st/statistics/components/nation-card.vue';
import { EleDrawer } from 'ele-admin-plus';
import { ref } from 'vue';
// 定义emit
const emits = defineEmits(['update:modelValue']);
const props = defineProps({
    /** 弹窗是否打开 */
        modelValue: Boolean,
        title: {
                type: String,
                default: '学生统计'
        }
});
//判断抽屉是否已经完全打开
const modelOpened = ref(false);
const handleOpened = () => {
    modelOpened.value = props.modelValue;
};
/** 更新modelValue */
const updateModelValue = (value) => {
        emits('update:modelValue', value);
};
</script>

<style scoped lang="scss">
  .el-link {
    font-weight: unset !important;
  }

  body .ele-card {
    border-radius: 0px 0px 5px 5px !important;
  }

  ::v-deep .ele-drawer > .el-drawer > .el-drawer__header {
    background: linear-gradient(
      90deg,
      rgb(240, 250, 255) 39%,
      rgb(255, 255, 255) 99%
    );
  }

  ::v-deep .el-drawer__body {
    background: #f0f0f0;
  }
</style>
