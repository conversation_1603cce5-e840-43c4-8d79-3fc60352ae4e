<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form ref="formRef"
             size="small"
             @keyup.enter="search"
             @submit.prevent=""
             label-width="auto"
             label-position="left"
             :inline="true"
             :model="initModel"
             :style="{ padding: '0px 10px 0px 10px' }">
      <el-row :gutter="10">
        <template v-for="(item, index) in initItems">
          <template v-if="index > 1">
            <el-col span="6"
                    v-if="searchExpand"
                    v-show="item.showFlag === '是'">
              <ProFormItem :item="item"
                           :model="initModel"
                           @updateItemValue="(prop, value) => updateFormValue(item, prop, value)">
                <template v-for="name in Object.keys($slots).filter(
                    (k) =>![
                        'default',
                        'footer',
                        'topExtra',
                        'bottomExtra'
                      ].includes(k))" #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"></slot>
                </template>
              </ProFormItem>
            </el-col>
          </template>
          <template v-else>
            <el-col span="6" v-show="item.showFlag === '是'">
              <ProFormItem :item="item"
                           :model="initModel"
                           @updateItemValue="(prop, value) => updateFormValue(item, prop, value)">
                <template v-for="name in Object.keys($slots).filter(
                    (k) => ![
                        'default',
                        'footer',
                        'topExtra',
                        'bottomExtra'
                      ].includes(k)
                  )" #[name]="slotProps">
                  <slot :name="name" v-bind="slotProps || {}"></slot>
                </template>
              </ProFormItem>
            </el-col>
          </template>
        </template>
        <el-col span="6">
          <el-form-item label="">
            <div
              :style="{marginLeft:'115px!important',flex: 1, display: 'flex', alignItems: 'center', justifyContent: searchExpand ? 'flex-end' : 'flex-start' }">
              <el-button type="primary" plain @click="search">查询</el-button>
              <el-button type="default" @click="searchSet">高级查询</el-button>
              <el-button @click="reset">重置</el-button>
              <!--              @click="onExpend"-->
              <el-link type="primary"
                       underline="never"
                       @click="searchExpand = !searchExpand"
                       style="margin-left: 12px">
                <template v-if="searchExpand">
                  <span>收起</span>
                  <el-icon style="vertical-align: -1px">
                    <ArrowUp/>
                  </el-icon>
                </template>
                <template v-else>
                  <span>展开</span>
                  <el-icon style="vertical-align: -2px">
                    <ArrowDown/>
                  </el-icon>
                </template>
              </el-link>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
  <!-- 编辑弹窗 -->
  <SearchSet v-model="showSet" @done="reload"/>
</template>

<script setup>
import {nextTick, ref, unref} from 'vue';
import {ArrowDown, ArrowUp} from '@/components/icons';
import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
import {useRouter} from 'vue-router';
import {getFieldList} from '../api/index';
import SearchSet from '../components/search-set.vue';
import {
  arrayTypes,
  regionsArrayTypes,
  selectTypes,
  stringTypes,
  uploadTypes
} from '@/components/ProForm/util';

const emit = defineEmits(['search']);

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const routeType = path.split('/')[3];

/** 检索条件区域 */
const searchContent = ref(null);
/** 表单实例 */
const formRef = ref(null);
/** 表单项 */
const initItems = ref([]);
/** 获取表单字段 */
const initModel = ref([]);

/** 是否显示编辑弹窗 */
const showSet = ref(false);
/** 搜索表单是否展开 */
const searchExpand = ref(false);

/** 搜索 */
const search = () => {
  emit('search', {...initModel.value});
};

/** 高级查询搜索 */
const reload = (data) => {
  emit('search', {customQueryData: data});
};
/** 展开/收起 */
const onExpend = () => {
  // console.log(
  //   'searchContent.value.$el :>> ',
  //   searchContent.value.$el.offsetHeight
  // );
  // nextTick(() => {
  //   console.log('变化后的', searchContent.value.$el.offsetHeight); // 输出 'Hello Vue 3!'
  // });
  searchExpand.value = !searchExpand.value;
  if (searchExpand.value) {
    searchContent.value.$el.style.height = '80px';
  } else {
    searchContent.value.$el.style.height = '40px';
  }
};
/** 高级查询 */
const searchSet = () => {
  showSet.value = true;
};
/** 更新表单数据 */
const updateFormValue = (item, prop, value) => {
  initModel.value[prop] = value;
  if (item.type === 'regions' && value) {
    initItems.value.filter((f) => {
      if (f.prop === item.prop + '_regionsDetail') f.showFlag = '是';
    });
  }
  if (item.selfFieldLink) {
    //院系专业班级三个字段页面逻辑特殊处理
    nextTick(() => {
      initItems.value.filter((f) => {
        if (f.selfFieldLink === item.nextField) {
          f.showFlag = '是';
          f.props['refresh'] = value; //watch每次刷新，重新请求
          if (item.nextField === 'zymc') {
            f.props.dicQueryParams['params'] = {xyid: value};
          }
          if (item.nextField === 'bjmc') {
            f.props.dicQueryParams['params'] = {zyid: value};
          }
        }
      });
    });
  }
  if (item.fieldLinks) {
    //获取选择值下面所有的显示字段信息
    let showfields = item.fieldLinks.filter((f) => {
      // return f.fieldValId === value
      return f.fieldVal === value;
    });
    if (showfields.length > 0) {
      showfields.forEach((sf) => {
        nextTick(() => {
          //切换选择值，清空之前选项所赋值
          initModel.value[sf.linkField] = '';
          //动态切换选择值，关联具体字段的显示隐藏
          initItems.value.filter((init) => {
            init.showFlag =
              sf.linkField === init.prop ? sf.showFlag : init.showFlag;
            if (sf.linkFieldDataType && sf.linkField === init.prop) {
              init.props = {
                code: sf.linkFieldDataType,
                filterable: true
              };
            }
          });
        });
      });
    }
  }
};

/**  重置 */
const reset = () => {
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
      formRef.value?.resetFields?.();
      search();
    });
  });
};
const initFieldList = async () => {
  const fieldData = await getFieldList(routeType, {
    tempField: '否',
    queryShowFlag: '是',
    listFlag: '否'
  });
  if (fieldData.length > 0) {
    let fieldResult = [];
    if (fieldData && fieldData.length > 0) {
      fieldData.forEach((e, index) => {
        let props = {};
        let selfFieldLink = '';
        let nextField = '';
        let type = e.controlType;
        if (e.controlType === 'select') {
          if (e.loadDataType && e.loadDataType !== 'other') {
            type = 'dictSelect';
            props = {
              code: e.loadDataType,
              dicQueryParams: {
                getValType: e.loadDataType !== 'pycc' ? 'name' : ''
              },
              filterable: true
            };
          } else {
            //院系专业班级页面逻辑处理
            type = 'dictSelect';
            if (e.loadDataUrl === '/code/codeDwb') {
              selfFieldLink = 'xymc';
              nextField = 'zymc';
            }
            if (e.loadDataUrl === '/code/codeZyb') {
              selfFieldLink = 'zymc';
              nextField = 'bjmc';
            }
            if (e.loadDataUrl === '/code/codeBjb') {
              selfFieldLink = 'bjmc';
            }
            props = {
              code: e.loadDataType,
              filterable: true,
              dicQueryParams: {
                dictFieldUrl: e.loadDataUrl,
                valueField: e.valueField,
                textField: e.textField
              }
            };
          }
        } else if (
          e.controlType === 'fileUpload' ||
          e.controlType === 'imageUpload'
        ) {
          //附件上传数量
          props = {
            limit: e.regExpression ? Number(e.regExpression) : 1
          };
        }
        // ...e,
        let newObj = Object.assign(
          {
            key: 'fieldKey_' + index,
            label: e.fieldZh,
            prop: e.fieldEn,
            type: type,
            typeKey: e.type,
            required: false,
            showFlag: e.showFlag,
            fieldLinks: e.fieldLinks,
            selfFieldLink: selfFieldLink,
            nextField: nextField
          },
          {
            props: props
          }
        );
        fieldResult.push(newObj);

        if (e.controlType === 'regions') {
          //省市区+详细地址信息
          fieldResult.push({
            key: 'fieldKey_99' + index,
            label: '详细地址',
            prop: e.fieldEn + '_regionsDetail',
            type: 'input',
            required: false,
            showFlag: '否'
          });
        }
      });
      initItems.value = fieldResult;
    }

    let fieldFormResult = {};
    if (fieldResult && fieldResult.length > 0) {
      fieldResult.forEach((item) => {
        if (item.type) {
          if (
            uploadTypes.includes(item.type) ||
            regionsArrayTypes.includes(item.type) ||
            arrayTypes.includes(item.type)
          ) {
            fieldFormResult[item.prop] = [];
          }
          if (
            stringTypes.includes(item.type) ||
            selectTypes.includes(item.type)
          ) {
            fieldFormResult[item.prop] = '';
          }
        }
      });
      initModel.value = fieldFormResult;
    }
  }
};
initFieldList();
</script>
<!--<style scoped>-->
<!--body .ele-card {-->
<!--  margin-bottom: 0 !important;-->
<!--  border-bottom: 1px solid #eeeeee;-->
<!--  border-radius: 5px 5px 0 0 !important;-->
<!--}-->

<!--//-->
<!--//.animated-div {-->
<!--//  transition: height 0.18s ease-in-out; /* 设置动画效果 */-->
<!--//  overflow: hidden; /* 防止内容溢出 */-->
<!--//}-->
<!--//-->
<!--///* 初始高度 */-->
<!--//.animated-div {-->
<!--//  height: 40px;-->
<!--//}-->
<!--</style>-->
