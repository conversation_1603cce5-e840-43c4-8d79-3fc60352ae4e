<!-- 编辑弹窗 -->
<template>
  <ele-drawer :size="730"
              title="自定义导出设置"
              :append-to-body="true"
              :destroy-on-close="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-page>
      <el-form ref="formRef"
               size="small"
               :model="form"
               :rules="rules"
               label-position="top"
               label-width="auto"
               @submit.prevent="">
        <el-form-item label="导出文件名" prop="fileName">
          <el-input clearable
                    v-model="form.fileName"
                    placeholder="请输入导出文件名"/>
        </el-form-item>
        <el-form-item label="导出数据" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio-button v-for="type in exportType" :key="type.label" :value="type.label">
              {{ type.name }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <ele-alert :title="'请选择需要导出的字段，勾选顺序即为表头字段顺序，当前已勾选 '+leftOperationArray.length+' 个。'"
                   show-icon
                   style="margin-bottom: 12px;"
                   :closable="false"/>
        <template v-if="initialized">
          <template v-for="item in initItems" :key="item.type">
            <el-form-item>
              <el-checkbox v-model="checkAll[item.type]"
                           :indeterminate="isIndeterminate[item.type]"
                           @change="handleCheckAll(item.type,$event)">
                {{ item.typeName }}
              </el-checkbox>
            </el-form-item>
            <el-form-item>
              <el-checkbox-group v-model="leftOperation[item.type]">
                <el-checkbox style="min-width: 80px;" v-for="field in item.list" :key="field.fieldEn"
                             :value="field.fieldEn"
                             @change="handleCheckedChange(field,item.type)">
                  {{ field.fieldZh }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-divider style="margin: 0 0 10px 0; opacity: 0.6"/>
          </template>
        </template>
        <div v-else style="text-align: center; padding: 20px;">
          加载中...
        </div>
      </el-form>
    </ele-page>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" plain type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref, onMounted} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {getFieldList} from '../api/index';
import {useRouter} from "vue-router";
import {groupArr, compare, mergeUnique} from '@/utils/common_bak2.js';
import {useUserStore} from "@/store/modules/user";
import {storeToRefs} from "pinia";
import {useDictData} from "@/utils/use-dict-data";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

const {currentRoute} = useRouter();
const {path} = unref(currentRoute);
const routeType = path.split("/")[3];

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

// 初始化状态
const initialized = ref(false);

/** 表单项 */
const initItems = ref([]);
const exportType = [
  {label: '1', name: '导出当前页数据'},
  {label: '2', name: '仅导出选中的数据'},
  {label: '3', name: '仅导出查询结果'},
  {label: '4', name: '导出全部数据'}
];

/** 提交状态 */
const loading = ref(false);
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields] = useFormData({
  fileName: '',
  type: '',
});

/** 表单验证规则 */
const rules = reactive({
  fileName: [
    {
      required: true,
      message: '请输入导出文件名',
      type: 'string',
      trigger: 'blur'
    }
  ],
  type: [
    {
      required: true,
      message: '请选择导出数据类型',
      type: 'string',
      trigger: 'blur'
    },
  ],
});

const isIndeterminate = ref({});
const checkAll = ref({});
const checkedGroup = ref(null);
const leftOperation = ref({});
const leftOperationArray = ref([]);

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) return;
    if (leftOperationArray.value.length === 0) {
      EleMessage.error('请选择需要导出的字段');
      return;
    }
    const newData = {
      ...form,
      fields: leftOperationArray.value.map(item => item.fieldEn).join(",")
    };
    updateModelValue(false);
    emit('done', newData);
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

/** 全选反选的多选框 */
const handleCheckAll = (type, val) => {
  checkedGroup.value = type;
  isIndeterminate.value[type] = false;
  const leftData = initItems.value.find(obj => obj.type === type);

  if (!leftData) return;

  const allDataCode = leftData.list.map(e => e.fieldEn);
  const allData = [...leftData.list];

  leftOperation.value[type] = val ? allDataCode : [];
  leftOperationArray.value = mergeUnique(
    val ? allData : [],
    leftOperationArray.value,
    'fieldEn'
  );
};

const handleCheckedChange = (e, type) => {
  checkedGroup.value = type;
  const checkedCount = leftOperation.value[type]?.length || 0;
  const typeData = initItems.value.find(obj => obj.type === type);
  const dataCount = typeData?.list?.length || 0;

  checkAll.value[type] = checkedCount === dataCount;
  isIndeterminate.value[type] = checkedCount > 0 && checkedCount < dataCount;

  const existingIndex = leftOperationArray.value.findIndex(obj => obj.fieldEn === e.fieldEn);
  if (existingIndex >= 0) {
    leftOperationArray.value.splice(existingIndex, 1);
  } else {
    leftOperationArray.value.push({...e});
  }
};

const initFieldList = async () => {
  try {
    const fieldData = await getFieldList(routeType, {
      exportFieldFlag: '是',
      listFlag: '否'
    });

    if (fieldData?.length > 0) {
      const rData = groupArr(fieldData, 'groupId');

      rData.forEach(data => {
        const dictData = dicts.value['groupType']?.find(gt => gt.dictDataCode === data.type);
        if (dictData) {
          data.sort = dictData.sort;
          data.typeName = dictData.dictDataName;
        }
      });

      initItems.value = rData.sort(compare('sort'));

      // 初始化复选框状态
      rData.forEach(item => {
        leftOperation.value[item.type] = [];
        checkAll.value[item.type] = false;
        isIndeterminate.value[item.type] = false;
      });
    }
  } catch (error) {
    console.error('初始化字段列表失败:', error);
    EleMessage.error('加载导出字段失败');
  }
};

// 初始化数据
const initialize = async () => {
  try {
    await useDictData(['groupType'], {userType: routeType});
    await initFieldList();
    initialized.value = true;
  } catch (error) {
    console.error('初始化失败:', error);
    EleMessage.error('初始化失败');
  }
};

// 组件挂载时初始化
onMounted(() => {
  initialize();
});

// 监听弹窗打开状态
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);

// 监听字典数据变化
watch(
  () => dicts.value?.groupType,
  (val) => {
    if (val && props.modelValue) {
      initFieldList();
    }
  }
);
</script>
