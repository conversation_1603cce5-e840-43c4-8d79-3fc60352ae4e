<!-- 更新学生状态 -->
<template>
  <ele-modal
    :width="460"
    :close-on-click-modal="false"
    title="更新人员状态"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <!-- form={{ form }}<br/> -->
    <el-form ref="formRef" size="small" :model="form" @submit.prevent="">
      <el-form-item label="人员状态">
        <dict-data
          code="ryzt"
          v-model="statusId"
          :dicQueryParams="{
            dictFieldUrl: '/code/codeXszt',
            valueField: 'id',
            textField: 'ztmc'
          }"
          placeholder="请选择"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)" size="small" plain
        >取消</el-button
      >
      <el-button
        size="small"
        plain
        type="primary"
        :loading="loading"
        @click="save"
      >
        提交
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { ElMessageBox } from 'element-plus';
  import { CloudUploadOutlined } from '@/components/icons';
  import { importData, updateStudentStatus } from '../api/index';

  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { useRouter } from 'vue-router';
  import { FILE_IMPORTERROREXCEL } from '@/config/setting';
  import DictData from '@/components/DictData/index.vue';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operationApply } from '@/views/zizhu/apply/api/index.js';
  import { toFormDataWj } from '@/utils/common_bak2.js';

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const emit = defineEmits(['done', 'update:modelValue']);

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[3];
  const props = defineProps({
    /** 是否打开弹窗 */
    modelValue: Boolean,
    updataStatusDatas: Object
  });

  /** 导入请求状态 */
  const loading = ref(false);
  const formRef = ref(null);
  const statusId = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({});

  /** 上传 */
  const save = () => {
    if (!statusId.value) {
      EleMessage.error('请选择人员状态');
      return;
    }
    loading.value = true;
    let data = toFormDataWj({ ...form, xgh: form.selectedXghs });
    updateStudentStatus(statusId.value, data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.updataStatusDatas) {
          assignFields(props.updataStatusDatas);
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .user-import-upload {
    margin-bottom: 12px;
  }
</style>
