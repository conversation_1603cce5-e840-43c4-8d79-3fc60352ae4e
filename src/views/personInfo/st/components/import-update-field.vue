<!-- 编辑弹窗 -->
<template>
  <ele-drawer :size="730"
              :title="'批量更新'+(routeType==='student'?'学生':'教师')"
              :append-to-body="true"
              :destroy-on-close="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-page>
      <div v-loading="loading" class="user-import-upload">
        <el-upload drag
                   action=""
                   accept=".xls,.xlsx"
                   :show-upload-list="false"
                   :before-upload="doUpload">
          <ele-text type="primary"
                    :icon="CloudUploadOutlined"
                    :icon-props="{ size: 52 }"
                    style="margin-bottom: 10px"/>
          <ele-text type="placeholder">只能上传 xls、xlsx 文件。<br/>将文件拖到此处, 或点击上传。</ele-text>
        </el-upload>
      </div>
      <el-form ref="formRef"
               size="small"
               :model="form"
               :rules="rules"
               label-width="auto"
               @submit.prevent="">
        <!--        请选择需要导出的字段，勾选顺序即为表头字段顺序，-->
        <ele-alert :title="'自定义更新字段设置。当前已勾选 '+leftOperationArray.length+' 个。'"
                   show-icon
                   style="margin: 16px 0"
                   :closable="false"/>
        <template v-for="item in initItems">
          <el-form-item>
            <el-checkbox v-model="checkAll[item.type]"
                         :indeterminate="isIndeterminate[item.type]"
                         @change="handleCheckAll(item.type,$event)">
              {{ item.typeName }}
            </el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox-group v-model="leftOperation[item.type]">
              <el-checkbox style="min-width: 80px;" v-for="field in item.list" :key="field.fieldEn"
                           :value="field.fieldEn"
                           @change="handleCheckedChange(field,item.type)">
                {{ field.fieldZh }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-divider style="margin: 0 0 10px 0; opacity: 0.6"/>
        </template>
      </el-form>
    </ele-page>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button size="small" type="primary" plain
                 underline="never"
                 @click="handleDownload"
                 download="批量更新模板.xlsx">
        点击下载模板
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {
  getFieldList, importUpdateData,
  operation
} from '../api/index';
import {useRouter} from "vue-router";
import {groupArr, compare, mergeUnique} from '@/utils/common_bak2.js';
import {useUserStore} from "@/store/modules/user";
import {storeToRefs} from "pinia";
import {useDictData} from "@/utils/use-dict-data";
import {CloudUploadOutlined} from "@/components/icons/index.js";
import {ElMessageBox} from "element-plus";
import {FILE_IMPORTERROREXCEL} from "@/config/setting.js";
import {getToken} from "@/utils/token-util.js";
import {getCurrentRole} from "@/utils/current-role-util.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});
const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const routeType = path.split("/")[3];

const userStore = useUserStore();
useDictData(['groupType'], {userType: routeType});
const {dicts} = storeToRefs(userStore);


/** 表单项 */
const initItems = ref([]);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({});

/** 表单验证规则 */
const rules = reactive({});

/** 保存编辑 */
const handleDownload = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    if (leftOperationArray.value.length === 0) {
      EleMessage.error('请选择需要导出的字段');
      return;
    }
    let newData = leftOperationArray.value.map(item => item.fieldZh).join(",")
    window.location.href = BASE_URL + 'api/personInfo/' + routeType + '/exportUpdateTemplate?access_token=' + accessToken  + '&title=' + newData;

    // updateModelValue(false);
    // emit('done', newData);
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  leftOperationArray.value = [];
  leftOperation.value = {};
  checkAll.value = {};
  isIndeterminate.value = {};
  emit('update:modelValue', value);
};

/** 上传 */
const doUpload = (file) => {
  if (
      ![
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ].includes(file.type)
  ) {
    EleMessage.error('只能选择 excel 文件');
    return false;
  }
  if (file.size / 1024 / 1024 > 10) {
    EleMessage.error('大小不能超过 10MB');
    return false;
  }
  loading.value = true;
  importUpdateData(file, routeType).then((data) => {
    loading.value = false;
    if (data.code === 0) {
      EleMessage.success(data.message);
    } else if (data.code === 2) {
      // EleMessage.success(data.message);
      open(data)
    }
    updateModelValue(false);
    emit('done');

  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
  return false;
};

const open = (data) => {
  ElMessageBox.confirm(
      data.message,
      '导入提示',
      {
        confirmButtonText: '下载',
        cancelButtonText: '关闭',
        type: 'warning',
      }
  ).then(() => {
    let downUrl = BASE_URL + FILE_IMPORTERROREXCEL + '?id=' + data.data + '&access_token=' + accessToken ;
    window.location.href = downUrl
  }).catch(() => {
    EleMessage({
      type: 'info',
      message: 'Delete canceled',
    })
  })
}

watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
);

const isIndeterminate = ref({});
const checkAll = ref({});
const checkedGroup = ref(null);
// 左侧数据
const leftOperation = ref({})
// 右侧数据
const leftOperationArray = ref([])

/** 全选反选的多选框 */
const handleCheckAll = (type, val) => {
  checkedGroup.value = type
  isIndeterminate.value[type] = false
  let allDataCode = []
  let allData = []
  let leftData = initItems.value.filter(obj => obj.type === type)
  if (leftData.length > 0) {
    leftData[0].list.forEach(e => {
      allDataCode.push(e.fieldEn)
      allData.push(e)
    })
  }
  if (val) {
    leftOperation.value[type] = allDataCode;

  } else {
    leftOperation.value[type] = [];
  }
  leftOperationArray.value = mergeUnique(leftOperationArray.value, allData, 'fieldEn')
}

const handleCheckedChange = (e, type) => {
  checkedGroup.value = type
  let checkedCount = leftOperation.value[type] ? leftOperation.value[type].length : 0;
  let existTypeData = initItems.value.filter(obj => obj.type === type)
  let dataCount = existTypeData.length > 0 ? existTypeData[0].listLength : 0;
  checkAll.value[type] = checkedCount === dataCount
  isIndeterminate.value[type] = checkedCount > 0 && checkedCount < dataCount;

  let isExist = leftOperationArray.value.filter(obj => obj.fieldEn === e.fieldEn)
  if (isExist.length > 0) {
    leftOperationArray.value.forEach((v, i) => {
      if (v.fieldEn === e.fieldEn) {
        leftOperationArray.value.splice(leftOperationArray.value.indexOf(v), 1)
      }
    })
  } else {
    leftOperationArray.value.push({
      ...e
    })
  }
}

const initFieldList = async () => {
  const fieldData = await getFieldList(routeType, {exportFieldFlag: '是', listFlag: '否'});
  if (fieldData.length > 0) {
    let rData = groupArr(fieldData, 'groupId')
    rData.forEach(data => {
      let dictData = dicts.value['groupType'].filter(gt => gt.dictDataCode === data.type)
      if (dictData.length > 0) {
        data.sort = dictData[0].sort
        data.typeName = dictData[0].dictDataName
      }
    })
    initItems.value = rData.sort(compare('sort'))
  }
};

initFieldList()
</script>
<style>
.el-upload-dragger {
  padding: 10px !important;
}
</style>
