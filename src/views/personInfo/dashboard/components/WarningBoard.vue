<template>
  <el-card class="warning-board-card">
    <template #header>
      <div class="card-header">
        <h3 class="card-title">综合预警看板</h3>
        <div class="card-actions">
          <el-tooltip content="导出Excel格式的预警名单" placement="top">
            <el-button
              type="primary"
              size="small"
              @click="exportWarningList"
              :loading="exportLoading"
            >
              <template #icon>
                <el-icon><Download /></el-icon>
              </template>
              导出预警名单
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>

    <el-table
      :data="tableData"
      border
      stripe
      highlight-current-row
      v-loading="loading"
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="id" label="学号" width="120" fixed="left" />
      <el-table-column prop="name" label="姓名" width="100" fixed="left" />
      <el-table-column prop="college" label="学院" width="150" show-overflow-tooltip />

      <el-table-column label="预警类型" width="180">
        <template #default="{row}">
          <el-space wrap>
            <el-tag
              v-for="type in row.warningTypes"
              :key="type"
              :type="getWarningTagType(type)"
              class="warning-tag"
              effect="dark"
            >
              {{ type }}
            </el-tag>
          </el-space>
        </template>
      </el-table-column>

      <el-table-column label="成绩趋势" width="200">
        <template #default="{row}">
          <mini-line-chart
            :data="row.scoreTrend"
            height="40px"
            :show-tooltip="true"
          />
        </template>
      </el-table-column>

      <el-table-column label="出勤率" width="120" sortable>
        <template #default="{row}">
          <el-progress
            :percentage="row.attendanceRate"
            :stroke-width="18"
            :text-inside="true"
            :status="getAttendanceStatus(row.attendanceRate)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="lastCounseling" label="最后咨询" width="140" sortable>
        <template #default="{row}">
          <span v-if="row.lastCounseling">{{ formatDate(row.lastCounseling) }}</span>
          <span v-else class="muted-text">暂无记录</span>
        </template>
      </el-table-column>

      <el-table-column label="风险等级" width="120" sortable>
        <template #default="{row}">
          <el-tag
            :type="getRiskTagType(row.riskLevel)"
            effect="light"
            round
          >
            {{ row.riskLevel }}
            <el-icon v-if="row.riskLevel === '高'" class="ml-2">
              <WarningFilled />
            </el-icon>
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{row}">
          <el-button
            size="small"
            @click.stop="handleDetail(row)"
            :icon="View"
          >
            详情
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click.stop="handleIntervene(row)"
            :icon="ChatDotRound"
          >
            干预
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container" v-if="pagination.total > 0">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>

<!--  &lt;!&ndash; 详情弹窗 &ndash;&gt;-->
<!--  <student-detail-dialog-->
<!--    v-model="detailDialogVisible"-->
<!--    :student-id="currentStudentId"-->
<!--    @refresh="handleRefresh"-->
<!--  />-->

<!--  &lt;!&ndash; 干预记录弹窗 &ndash;&gt;-->
<!--  <intervention-dialog-->
<!--    v-model="interventionDialogVisible"-->
<!--    :student-id="currentStudentId"-->
<!--    @refresh="handleRefresh"-->
<!--  />-->
</template>

<script setup>
import { ref, computed } from 'vue'
import { Download, WarningFilled, View, ChatDotRound } from '@element-plus/icons-vue'
import MiniLineChart from './charts/MiniLineChart.vue'
// import StudentDetailDialog from './dialog/StudentDetailDialog.vue'
// import InterventionDialog from './dialog/InterventionDialog.vue'
import { useDateFormat } from '@vueuse/core'
// import { exportExcel } from '@/utils/export'

// 组件属性
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
    validator: (value) => {
      return value.every(item =>
        item.id && item.name && Array.isArray(item.warningTypes))
    }
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  }
})

// 组件事件
const emit = defineEmits(['detail', 'intervene', 'page-change', 'export', 'refresh'])

// 状态
const exportLoading = ref(false)
const detailDialogVisible = ref(false)
const interventionDialogVisible = ref(false)
const currentStudentId = ref(null)

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: props.total
})

// 计算属性
const tableData = computed(() => props.data)

// 方法
const getWarningTagType = (type) => {
  const typeMap = {
    '学业预警': 'danger',
    '心理预警': 'warning',
    '行为预警': 'info',
    '经济困难': 'primary',
    '健康预警': 'success'
  }
  return typeMap[type] || ''
}

const getRiskTagType = (level) => {
  const levelMap = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return levelMap[level] || ''
}

const getAttendanceStatus = (rate) => {
  if (rate < 60) return 'exception'
  if (rate < 80) return 'warning'
  return 'success'
}

const formatDate = (date) => {
  return useDateFormat(date, 'YYYY-MM-DD HH:mm').value
}

const handleDetail = (row) => {
  currentStudentId.value = row.id
  detailDialogVisible.value = true
  emit('detail', row)
}

const handleIntervene = (row) => {
  currentStudentId.value = row.id
  interventionDialogVisible.value = true
  emit('intervene', row)
}

const handleRowClick = (row) => {
  // 可以在这里实现行点击逻辑
}

const handleRefresh = () => {
  emit('refresh')
}

const exportWarningList = async () => {
  try {
    exportLoading.value = true
    // await exportExcel({
    //   data: props.data,
    //   fileName: '学生预警名单',
    //   columns: [
    //     { label: '学号', prop: 'id' },
    //     { label: '姓名', prop: 'name' },
    //     { label: '学院', prop: 'college' },
    //     { label: '预警类型', prop: 'warningTypes', formatter: types => types.join(', ') },
    //     { label: '出勤率', prop: 'attendanceRate', formatter: rate => `${rate}%` },
    //     { label: '风险等级', prop: 'riskLevel' }
    //   ]
    // })
    emit('export')
  } finally {
    exportLoading.value = false
  }
}

const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  emitPageChange()
}

const handleCurrentChange = (page) => {
  pagination.value.currentPage = page
  emitPageChange()
}

const emitPageChange = () => {
  emit('page-change', {
    page: pagination.value.currentPage,
    size: pagination.value.pageSize
  })
}
</script>

<style scoped lang="scss">
.warning-board-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .warning-tag {
    margin-right: 6px;
    margin-bottom: 6px;
  }

  .muted-text {
    color: var(--el-text-color-secondary);
    font-style: italic;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: var(--el-table-row-hover-bg-color);
    }
  }
}
</style>
