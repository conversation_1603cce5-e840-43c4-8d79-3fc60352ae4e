<template>
  <el-card :body-style="{ padding: '10px' }">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <el-select
        v-model="sortType"
        size="small"
        style="width: 100px"
        @change="handleSortChange"
      >
        <el-option label="默认排序" value="default"/>
        <el-option label="升序" value="asc"/>
        <el-option label="降序" value="desc"/>
      </el-select>
    </div>
    <div v-loading="loading" :id="uniqueChartId" :style="{ height: height }"></div>
  </el-card>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, computed, onUnmounted} from 'vue';
import * as echarts from 'echarts';
import {useElementSize} from '@vueuse/core';

// 生成唯一ID
const uniqueChartId = ref(`bar-chart-${Date.now()}-${Math.floor(Math.random() * 10000)}`);

const props = defineProps({
  title: String,
  data: {
    type: [Array, Object],
    default: () => [],
    validator: (value) => {
      // 验证两种数据格式：
      // 1. 简单格式: [{name: string, value: number}]
      // 2. 复杂格式: {name: string, data: [{name: string, value: number}]}
      if (Array.isArray(value)) {
        return value.every(item => item && 'name' in item && 'value' in item);
      } else if (typeof value === 'object' && value !== null) {
        return 'name' in value && 'data' in value && Array.isArray(value.data) &&
          value.data.every(item => item && 'name' in item && 'value' in item);
      }
      return false;
    },
  },
  loading: Boolean,
  height: {
    type: String,
    default: '400px',
  },
  colorScheme: {
    type: Array,
    default: () => ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
  },
  // 是否显示百分比符号
  showPercent: {
    type: Boolean,
    default: true
  },
  // 是否为多系列数据
  isMultiSeries: {
    type: Boolean,
    default: false
  }
});

const sortType = ref('default');
let chartInstance = null;

// 处理数据格式，统一转换为标准格式
const normalizedData = computed(() => {
  if (!props.data) return [];

  // 处理多系列数据格式
  if (props.isMultiSeries && Array.isArray(props.data)) {
    return props.data.map(series => ({
      name: series.name,
      data: series.data.map(item => ({
        ...item,
        seriesName: series.name
      }))
    }));
  }

  // 处理单系列复杂格式
  if (typeof props.data === 'object' && !Array.isArray(props.data) && props.data.data) {
    return [{
      name: props.data.name || props.title,
      data: props.data.data
    }];
  }

  // 处理简单数组格式
  if (Array.isArray(props.data)) {
    return [{
      name: props.title,
      data: props.data
    }];
  }

  return [];
});

// 排序处理
const sortedData = computed(() => {
  return normalizedData.value.map(series => {
    let data = [...series.data];

    if (sortType.value === 'asc') {
      data.sort((a, b) => a.value - b.value);
    } else if (sortType.value === 'desc') {
      data.sort((a, b) => b.value - a.value);
    }

    return {
      name: series.name,
      data: data
    };
  });
});

const renderChart = () => {
  if (!sortedData.value || sortedData.value.length === 0) return;

  const chartDom = document.getElementById(uniqueChartId.value);
  if (!chartDom) {
    console.error(`无法找到图表容器: #${uniqueChartId.value}`);
    return;
  }

  // 先销毁旧实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 创建新实例
  chartInstance = echarts.init(chartDom);

  try {
    // 准备系列数据
    const series = sortedData.value.map((seriesItem, seriesIndex) => ({
      name: seriesItem.name,
      type: 'bar',
      data: seriesItem.data.map(item => item.value),
      itemStyle: {
        color: props.colorScheme[seriesIndex % props.colorScheme.length],
        borderRadius: [0, 4, 4, 0],
      },
      label: {
        show: true,
        position: 'right',
        formatter: props.showPercent ? '{c}%' : '{c}',
        color: '#666',
      },
    }));

    // 获取所有类目（x轴标签）
    const categories = sortedData.value[0]?.data?.map(item => item.name) || [];

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params) => {
          if (props.isMultiSeries) {
            return params.map(p => `${p.seriesName}: ${p.value}${props.showPercent ? '%' : ''}`).join('<br/>');
          }
          return `${params[0].name}: ${params[0].value}${props.showPercent ? '%' : ''}`;
        },
      },
      legend: props.isMultiSeries ? {
        data: sortedData.value.map(item => item.name),
        bottom: 0
      } : undefined,
      grid: {
        left: '3%',
        right: '4%',
        bottom: props.isMultiSeries ? '15%' : '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: props.showPercent ? '{value}%' : '{value}'
        },
        axisLine: { lineStyle: { color: '#ddd' } },
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLine: { lineStyle: { color: '#ddd' } },
      },
      series: series
    };

    chartInstance.setOption(option);
    chartInstance.resize();
  } catch (error) {
    console.error('图表渲染错误:', error);
  }
};

const handleSortChange = () => renderChart();

// 响应式调整
const { width } = useElementSize(document.getElementById(uniqueChartId.value));
watch(width, () => chartInstance?.resize());

watch(() => [props.data, props.isMultiSeries, props.showPercent], renderChart, { deep: true });

onMounted(() => nextTick(renderChart));
onUnmounted(() => chartInstance?.dispose());
</script>

<style scoped>
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}
</style>
