基本用法（单条线）
<template>
<line-chart
title="出勤率趋势"
:data="[95, 92, 89, 93, 96, 94]"
:x-axis="['9月', '10月', '11月', '12月', '1月', '2月']"
height="300px"
/>
</template>

<script>
import LineChart from '@/components/charts/LineChart.vue'

export default {
  components: {
    LineChart
  }
}
</script>

多条线用法
<template>
<line-chart
title="预警学生转化趋势"
:data="[
{ name: '解除预警', data: [12, 15, 18, 22, 25, 30] },
{ name: '新增预警', data: [25, 20, 22, 18, 15, 12] }
]"
:x-axis="['1月', '2月', '3月', '4月', '5月', '6月']"
height="400px"
show-legend
/>
</template>

<script>
import LineChart from '@/components/charts/LineChart.vue'

export default {
  components: {
    LineChart
  }
}
</script>


带面积图的用法
<template>
<line-chart
title="心理咨询人次趋势"
:data="[45, 52, 38, 65, 72, 60]"
:x-axis="['1月', '2月', '3月', '4月', '5月', '6月']"
height="350px"
area-style
/>
</template>


注意事项
确保在项目中已经安装并正确引入了ECharts库

组件会自动处理响应式变化，但需要确保容器有明确的宽度

对于大量数据点（>1000），建议关闭平滑效果以获得更好性能

可以通过修改props来自定义颜色和其他样式