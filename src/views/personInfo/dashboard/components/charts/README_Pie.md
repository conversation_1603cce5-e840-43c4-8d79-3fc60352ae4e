基本用法

<template>
  <pie-chart
    title="性别分布"
    :data="[
      { name: '男', value: 6542 },
      { name: '女', value: 6003 },
      { name: '其他', value: 23 }
    ]"
    height="300px"
  />
</template>

<script>
import PieChart from '@/components/charts/PieChart.vue'

export default {
  components: {
    PieChart
  }
}
</script>

环形图用法

<template>
  <pie-chart
    title="院系分布"
    :data="[
      { name: '计算机学院', value: 2568 },
      { name: '经济学院', value: 1987 },
      { name: '文学院', value: 1562 }
    ]"
    :radius="['40%', '70%']"
    height="400px"
  />
</template>

南丁格尔玫瑰图
<template>
<pie-chart
title="心理咨询问题类型分布"
:data="[
{ name: '学业压力', value: 156 },
{ name: '人际关系', value: 98 },
{ name: '情感问题', value: 75 }
]"
:radius="[30, '80%']"
rose-type="radius"
height="400px"
/>
</template>

自定义颜色和图例位置
<template>
<pie-chart
title="奖学金分布"
:data="[
{ name: '国家奖学金', value: 25, color: '#F56C6C' },
{ name: '校级一等奖', value: 120, color: '#E6A23C' },
{ name: '校级二等奖', value: 240, color: '#67C23A' }
]"
legend-position="bottom"
height="350px"
/>
</template>


确保在项目中已经安装并正确引入了ECharts库

数据格式必须包含name和value属性

环形图通过radius属性控制，第一个值是内半径，第二个是外半径

南丁格尔玫瑰图通过roseType属性启用，可选'radius'或'area'

图例位置可通过legendPosition调整，支持'top', 'right', 'bottom', 'left'

