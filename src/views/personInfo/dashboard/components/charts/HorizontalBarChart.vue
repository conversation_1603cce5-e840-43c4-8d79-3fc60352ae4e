<template>
  <div ref="chartRef" :style="{ width: width, height: height }"></div>
</template>

<script setup>
import {ref, onMounted, watch, onBeforeUnmount, computed} from 'vue';
import * as echarts from 'echarts'
import { useResizeObserver } from '@vueuse/core'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
    validator: value => value.every(item => 'name' in item && 'value' in item)
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  color: {
    type: String,
    default: '#409EFF'
  },
  showAxis: {
    type: Boolean,
    default: true
  },
  showLabel: {
    type: Boolean,
    default: true
  },
  barWidth: {
    type: Number,
    default: 12
  },
  sort: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  },
  grid: {
    type: Object,
    default: () => ({
      top: 30,
      right: 20,
      bottom: 20,
      left: 100
    })
  }
})

const emit = defineEmits(['click'])

const chartRef = ref(null)
let chartInstance = null

// 处理排序数据
const sortedData = computed(() => {
  if (!props.sort) return props.data
  return [...props.data].sort((a, b) => b.value - a.value)
})

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  updateChart()
  bindEvents()
}

// 更新图表配置
const updateChart = () => {
  if (!chartInstance) return

  const option = {
    title: {
      text: props.title,
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: params => {
        const { name, value, color } = params[0]
        return `
          <div style="font-weight:bold">${name}</div>
          <div style="display:flex;align-items:center">
            <span style="display:inline-block;width:10px;height:10px;background:${color};margin-right:5px"></span>
            ${value}
          </div>
        `
      }
    },
    grid: props.grid,
    xAxis: {
      type: 'value',
      show: props.showAxis,
      axisLine: {
        show: props.showAxis
      },
      axisTick: {
        show: props.showAxis
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: sortedData.value.map(item => item.name),
      axisLine: {
        show: props.showAxis
      },
      axisTick: {
        show: props.showAxis
      },
      axisLabel: {
        show: props.showAxis,
        formatter: value => {
          // 处理长文本显示
          const maxLength = 10
          if (value.length > maxLength) {
            return value.substring(0, maxLength) + '...'
          }
          return value
        }
      }
    },
    series: [
      {
        name: '数据',
        type: 'bar',
        data: sortedData.value.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color || props.color
          }
        })),
        barWidth: props.barWidth,
        label: {
          show: props.showLabel,
          position: 'right',
          formatter: '{c}'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

// 绑定事件
const bindEvents = () => {
  if (!chartInstance) return

  chartInstance.on('click', params => {
    emit('click', params)
  })
}

// 响应式调整
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 初始化
onMounted(() => {
  initChart()
  useResizeObserver(chartRef, resizeChart)
})

// 销毁
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听其他属性变化
watch(() => [
  props.width,
  props.height,
  props.color,
  props.showAxis,
  props.showLabel,
  props.barWidth,
  props.sort,
  props.title,
  props.grid
], () => {
  updateChart()
})
</script>

<style scoped>
/* 基础样式 */
div {
  transition: all 0.3s ease;
}
</style>
