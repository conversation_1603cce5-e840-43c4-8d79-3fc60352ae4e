<template>
  <el-card :body-style="{ padding: '10px' }">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <el-tag v-if="total" size="small">总计: {{ total }}</el-tag>
    </div>
    <div v-loading="loading" :id="uniqueChartId" :style="{ height: height }"></div>
  </el-card>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, onUnmounted} from 'vue';
import * as echarts from 'echarts'
import { useElementSize } from '@vueuse/core'

const props = defineProps({
  title: String,
  data: Array,
  loading: Boolean,
  height: {
    type: String,
    default: '300px'
  },
  showLegend: {
    type: Boolean,
    default: true
  },
})

// 生成唯一ID
const uniqueChartId = ref(`pie-chart-${Date.now()}-${Math.floor(Math.random() * 10000)}`);
const total = ref(0)
let chartInstance = null

const renderChart = () => {
  if (!props.data || props.data.length === 0) return

  total.value = props.data.reduce((sum, item) => sum + item.value, 0)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: props.showLegend ? {
      orient: 'vertical',
      right: 10,
      top: 'middle',
      textStyle: {
        color: '#666'
      }
    } : undefined,
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data
      }
    ]
  }

  if (!chartInstance) {
    const chartDom = document.getElementById(uniqueChartId.value)
    if (chartDom) {
      chartInstance = echarts.init(chartDom)
    }
  }

  chartInstance && chartInstance.setOption(option)
}

// 响应式调整
const { width } = useElementSize(document.getElementById(uniqueChartId.value))
watch(width, () => {
  chartInstance && chartInstance.resize()
})

watch(() => props.data, () => {
  renderChart()
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    renderChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}
</style>
