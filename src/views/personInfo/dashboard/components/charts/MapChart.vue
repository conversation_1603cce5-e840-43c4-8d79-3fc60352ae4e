<template>
  <ele-card class="box-card map-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">生源地分布热力图</span>
        <el-tooltip content="数据每2小时自动更新" placement="top">
          <el-icon class="header-icon">
            <InfoFilled/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <template #extra>
      <div class="map-controls">
        <el-select v-model="mapType" size="small" style="width: 120px" @change="handleMapTypeChange">
          <el-option label="热力图" value="heat"/>
          <el-option label="分级图" value="piecewise"/>
          <el-option label="3D效果" value="3d"/>
        </el-select>
        <div>
          <el-button size="small" @click="refreshMap" :loading="loading">刷新数据</el-button>
          <el-button size="small" @click="exportImage">导出图片</el-button>
        </div>
      </div>
    </template>

    <div class="map-container">
      <template v-if="!mapLoaded">
        <div class="map-loading">
          <el-skeleton :rows="5" animated/>
        </div>
      </template>
      <template v-else>
        <div v-if="is3DLoading" class="loading-overlay">
          <el-icon class="loading-icon">
            <Loading/>
          </el-icon>
          <span>地图加载中...</span>
        </div>

        <div class="map-content">
          <!-- 左侧地图 -->
          <div class="map-wrapper">
            <transition name="fade" mode="out-in">
              <v-chart
                v-if="showChart"
                :key="'map-' + mapType + forceUpdateKey"
                ref="userMapChartRef"
                :option="userCountMapOption"
                style="height: 100%"
                autoresize
                @click="handleMapClick"
                @rendered="handleChartRendered"
                @finished="handleChartReady"
              />
            </transition>
          </div>

          <!-- 右侧排名 -->
          <div class="rank-wrapper">
            <div class="rank-container">
              <h4 class="rank-title">
                <el-icon>
                  <Trophy/>
                </el-icon>
                生源地TOP10
              </h4>
              <div class="rank-list">
                <div
                  v-for="(item, index) in userCountDataRank"
                  :key="item.name"
                  class="rank-item"
                  @mouseenter="highlightProvince(item.originalName)"
                >
                  <div class="rank-index" :class="getRankClass(index)">
                    {{ index + 1 }}
                  </div>
                  <div class="rank-name">{{ item.originalName }}</div>
                  <div class="rank-value">
                    {{ item.value }}人
                  </div>
                  <el-progress
                    :percentage="item.percent"
                    :stroke-width="8"
                    :show-text="false"
                    :color="getProgressColor(index)"
                    class="rank-progress"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </ele-card>
</template>

<script setup>
import {ref, onMounted, computed, onUnmounted, nextTick} from 'vue';
import {InfoFilled, Trophy, Loading} from '@element-plus/icons-vue';
import {use} from 'echarts/core';
import {CanvasRenderer} from 'echarts/renderers';
import {MapChart} from 'echarts/charts';
import {Map3DChart} from 'echarts-gl/charts';
import 'echarts-gl';
import {
  VisualMapComponent,
  GeoComponent,
  TooltipComponent,
  TitleComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import * as echarts from 'echarts';
import chinaJson from '@/assets/map/china.json';

use([
  CanvasRenderer,
  MapChart,
  Map3DChart,
  VisualMapComponent,
  GeoComponent,
  TooltipComponent,
  TitleComponent,
]);

const props = defineProps({});

const userMapChartRef = ref(null);
const loading = ref(false);
const mapType = ref('heat');
const currentProvince = ref('');
const mapLoaded = ref(false);
const chartRendered = ref(false);
const is3DLoading = ref(false);
const forceUpdateKey = ref(0);
const showChart = ref(true);

// 颜色配置
const colorRange = [
  '#FF4500',
  '#FF8C00',
  '#90EE90',
  '#00BFFF',
  '#9370DB',
  '#FF69B4',
  // '#20B2AA',
  // '#778899',
  // '#CD5C5C',
  // '#6495ED',
  // '#FFA07A',
  // '#7B68EE',
  // '#00FA9A',
  // '#FF6347',
];

// 省份名称处理函数
const normalizeProvinceName = (name) => {
  if (!name) return '';
  const specialMap = {
    北京: '北京市',
    天津: '天津市',
    上海: '上海市',
    重庆: '重庆市',
    内蒙古: '内蒙古自治区',
    西藏: '西藏自治区',
    新疆: '新疆维吾尔自治区',
    宁夏: '宁夏回族自治区',
    广西: '广西壮族自治区',
    香港: '香港特别行政区',
    澳门: '澳门特别行政区',
  };
  return specialMap[name] || name;
};

const completeProvinceName = (name) => {
  if (!name) return '';
  if (/省|市|自治区|特别行政区$/.test(name)) return name;
  return normalizeProvinceName(name);
};

// 数据生成器
const generateMockData = () => {
  const provinces = [
    '北京市',
    '天津市',
    '上海市',
    '重庆市',
    '河北省',
    '山西省',
    '辽宁省',
    '吉林省',
    '黑龙江省',
    '江苏省',
    '浙江省',
    '安徽省',
    '福建省',
    '江西省',
    '山东省',
    '河南省',
    '湖北省',
    '湖南省',
    '广东省',
    '广西壮族自治区',
    '海南省',
    '四川省',
    '贵州省',
    '云南省',
    '西藏自治区',
    '陕西省',
    '甘肃省',
    '青海省',
    '宁夏回族自治区',
    '新疆维吾尔自治区',
    '内蒙古自治区',
    '香港特别行政区',
    '澳门特别行政区',
  ];

  const mockData = {};
  provinces.forEach((province) => {
    mockData[province] = Math.floor(Math.random() * 100) + 1;
  });

  mockData['北京市'] = Math.floor(Math.random() * 50) + 80;
  mockData['上海市'] = Math.floor(Math.random() * 50) + 70;
  mockData['广东省'] = Math.floor(Math.random() * 50) + 60;

  return Object.entries(mockData).map(([name, value]) => ({
    name: normalizeProvinceName(name),
    originalName: name,
    value,
    percent: 0,
  }));
};

const rawData = ref(generateMockData());

// 排名数据处理
const userCountDataRank = computed(() => {
  const sorted = [...rawData.value].filter((item) => item.value > 0).sort((a, b) => b.value - a.value);
  const max = sorted[0]?.value || 1;
  return sorted.slice(0, 10).map((item) => ({
    ...item,
    percent: (item.value / max) * 100,
  }));
});

// 地图注册
const registerMap = async () => {
  try {
    const processedGeoJSON = JSON.parse(JSON.stringify(chinaJson));
    processedGeoJSON.features.forEach((feature) => {
      const fullName = completeProvinceName(feature.properties.name);
      feature.properties.name = fullName;
      feature.properties.shortName = normalizeProvinceName(fullName);
    });
    echarts.registerMap('china', processedGeoJSON);
    mapLoaded.value = true;
  } catch (error) {
    console.error('地图注册失败:', error);
  }
};

const userCountMapOption = computed(() => {
  const total = rawData.value.reduce((sum, item) => sum + item.value, 0);
  const maxValue = Math.max(...rawData.value.map((item) => item.value));

  const seriesData = rawData.value.map((item) => ({
    name: item.originalName,
    value: item.value,
    percent: total > 0 ? parseFloat(((item.value / total) * 100).toFixed(1)) : 0,
  }));

  // 基础配置
  const baseOption = {
    backgroundColor: '#f5f7fa',
    title: {
      text: `学生生源地分布${mapType.value === '3d' ? '(3D)' : ''}`,
      subtext: `数据总量: ${total.toLocaleString()}人`,
      left: 'center',
      top: 10,
      textStyle: {
        color: '#333',
        fontSize: 18,
        fontWeight: 'bold',
      },
      subtextStyle: {
        color: '#666',
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const data = seriesData.find((item) => item.name === params.name);
        return `
          <div style="font-size:14px;font-weight:bold;margin-bottom:5px;color:#333">${params.name}</div>
          <div style="display:flex;justify-content:space-between;margin-bottom:3px">
            <span style="color:#666">人数:</span>
            <span style="font-weight:bold;color:#409EFF">${(data?.value || 0).toLocaleString()}人</span>
          </div>
          <div style="display:flex;justify-content:space-between">
            <span style="color:#666">占比:</span>
            <span style="font-weight:bold;color:#67C23A">${data?.percent || 0}%</span>
          </div>
        `;
      },
    },
    visualMap: {
      type: mapType.value === 'piecewise' ? 'piecewise' : 'continuous',
      min: 0,
      max: maxValue,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: colorRange,
      },
      textStyle: {
        color: '#666',
      },
      bottom: 30,
      left: 'right',
      pieces: mapType.value === 'piecewise'
        ? [
          {min: 80, label: '80人以上', color: colorRange[4]},
          {min: 60, max: 80, label: '60-80人', color: colorRange[3]},
          {min: 40, max: 60, label: '40-60人', color: colorRange[2]},
          {min: 20, max: 40, label: '20-40人', color: colorRange[1]},
          {max: 20, label: '20人以下', color: colorRange[0]},
        ]
        : undefined,
    },
  };

  if (mapType.value === '3d') {
    is3DLoading.value = true;
    return {
      ...baseOption,
      animation: false,
      series: [
        {
          type: 'map3D',
          map: 'china',
          data: rawData.value.map((item) => ({
            name: item.name,
            value: item.value,
            itemStyle: {
              color: getProvinceColor(item.value, maxValue, colorRange),
              opacity: 0.9,
              borderWidth: 0.5,
              borderColor: 'rgba(255,255,255,0.4)',
            },
            label: {
              show: true,
              textStyle: {
                fontSize: 10,
                color: '#fff',
                backgroundColor: 'rgba(0,0,0,0.7)',
                padding: [2, 4],
              },
            },
          })),
          viewControl: {
            distance: 100,
            alpha: 40,
            beta: 30,
            autoRotate: true,
          },
          light: {
            main: {
              intensity: 1.2,
              shadow: true,
            },
            ambient: {
              intensity: 0.3,
            },
          },
        },
      ],
    };
  } else {
    is3DLoading.value = false;
    return {
      ...baseOption,
      series: [
        {
          name: '学生人数',
          type: 'map',
          map: 'china',
          roam: true,
          scaleLimit: {
            min: 1,
            max: 3,
          },
          zoom: 1.5,
          center: [105, 36],
          data: seriesData,
          label: {
            show: true,
            color: '#333',
            fontSize: 10,
          },
          emphasis: {
            disabled: true, // 关键修复：禁用高亮覆盖
            itemStyle: {
              areaColor: undefined // 防止颜色被覆盖
            }
          },
          itemStyle: {
            areaColor: params => {
              const value = params.value || 0;
              if (value === 0) return '#f0f0f0';
              return colorRange[Math.min(
                Math.floor((value / maxValue) * (colorRange.length - 1)),
                colorRange.length - 1
              )];
            },
            borderColor: '#aaa',
            borderWidth: 0.5
          },
          visualMap: {
            show: true,
            min: 0,
            max: maxValue,
            inRange: {
              color: colorRange
            },
            calculable: true,
            seriesIndex: 0,
            dimension: 0
          }
        }
      ]
    };
  }
});

// 辅助函数
const getProvinceColor = (value, maxValue) => {
  if (value === 0) return '#f0f0f0';
  const ratio = value / maxValue;
  return colorRange[Math.min(Math.floor(ratio * (colorRange.length - 1)), colorRange.length - 1)];
};

// 交互方法
function highlightProvince(name) {
  if (userMapChartRef.value && chartRendered.value) {
    userMapChartRef.value.dispatchAction({
      type: 'highlight',
      name: name,
    });
  }
}

function handleMapClick(params) {
  currentProvince.value = params.name;
}

async function handleMapTypeChange() {
  try {
    is3DLoading.value = true;
    showChart.value = false;

    // 完全销毁旧图表实例
    if (userMapChartRef.value) {
      userMapChartRef.value.dispose();
    }

    await nextTick();
    forceUpdateKey.value++;

    // 重新创建图表实例
    showChart.value = true;

    await nextTick();

    // 确保图表完全渲染
    setTimeout(() => {
      if (userMapChartRef.value) {
        console.log('图表已重新创建===', userCountMapOption.value);
        userMapChartRef.value.resize();
      }
      is3DLoading.value = false;
    }, 300);
  } catch (error) {
    console.error('地图切换错误:', error);
    is3DLoading.value = false;
    showChart.value = true;
  }
}

function exportImage() {
  if (userMapChartRef.value && chartRendered.value) {
    const url = userMapChartRef.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#f5f7fa',
    });
    const link = document.createElement('a');
    link.href = url;
    link.download = `生源地分布图_${new Date().toLocaleDateString()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

async function refreshMap() {
  loading.value = true;
  try {
    rawData.value = generateMockData();
    await nextTick();
    if (userMapChartRef.value) {
      userMapChartRef.value.setOption(userCountMapOption.value, true);
    }
  } finally {
    loading.value = false;
  }
}

function getRankClass(index) {
  return ['rank-' + (index + 1), index < 3 ? 'top-rank' : ''];
}

function getProgressColor(index) {
  return colorRange[index % colorRange.length];
}

function handleChartRendered() {
  chartRendered.value = true;
  is3DLoading.value = false;
}

function handleChartReady() {
  is3DLoading.value = false;
  if (mapType.value === '3d' && userMapChartRef.value) {
    setTimeout(() => {
      try {
        userMapChartRef.value.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
        });
      } catch (e) {
        console.warn('标签高亮失败:', e);
      }
    }, 1000);
  }
}

function handleResize() {
  if (userMapChartRef.value && chartRendered.value) {
    userMapChartRef.value.resize();
  }
}

onMounted(async () => {
  await registerMap();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.map-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #fff;
}

.map-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-icon {
  color: #999;
  cursor: pointer;
  transition: color 0.2s;
}

.header-icon:hover {
  color: #409eff;
}

.map-loading {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.map-container {
  position: relative;
  overflow: hidden;
  background-color: #fff;
  padding: 0;
  height: 100%;
}

.map-content {
  display: flex;
  height: 500px;
}

.map-wrapper {
  flex: 1;
  padding: 0 10px;
  min-width: 0;
}

.rank-wrapper {
  width: 300px;
  padding: 10px;
  border-left: 1px solid #f0f0f0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
  color: #333;
  font-size: 16px;
  font-weight: bold;
  gap: 10px;
}

.loading-icon {
  color: #409eff;
  font-size: 32px;
  animation: rotating 1.5s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.map-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 0;
}

.rank-container {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  height: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
}

.rank-title {
  display: flex;
  align-items: center;
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.rank-title .el-icon {
  margin-right: 6px;
  color: #e6a23c;
}

.rank-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  gap: 8px;
  background-color: #f9f9f9;
}

.rank-item:hover {
  background-color: #f0f0f0;
}

.rank-index {
  flex: 0 0 24px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700, #ffa500);
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0, #a9a9a9);
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32, #a0522d);
}

.rank-4,
.rank-5,
.rank-6,
.rank-7,
.rank-8,
.rank-9,
.rank-10,
.rank-11,
.rank-12,
.rank-13,
.rank-14,
.rank-15 {
  background: #409eff;
  color: #fff;
}

.rank-name {
  flex: 1;
  font-size: 13px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 40px;
}

.rank-value {
  flex: 0 0 70px;
  font-size: 12px;
  font-weight: bold;
  color: #666;
  text-align: right;
}

.rank-progress {
  flex: 0 0 60px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 992px) {
  .map-content {
    flex-direction: column;
    height: auto;
  }

  .map-wrapper {
    height: 400px;
  }

  .rank-wrapper {
    width: 100%;
    border-left: none;
    border-top: 1px solid #f0f0f0;
  }
}

@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .rank-container {
    height: auto;
    max-height: 300px;
  }

  .rank-item {
    flex-wrap: wrap;
    gap: 4px;
  }

  .rank-progress {
    flex: 1 0 100%;
    order: 1;
  }
}
</style>
