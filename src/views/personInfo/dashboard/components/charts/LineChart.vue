<template>
  <el-card :body-style="{ padding: '10px' }" class="chart-card">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-actions">
        <el-tooltip content="刷新数据" placement="top">
          <el-button
            type="text"
            :icon="Refresh"
            circle
            size="small"
            @click="handleRefresh"
          />
        </el-tooltip>
        <el-tooltip content="下载图表" placement="top">
          <el-button
            type="text"
            :icon="Download"
            circle
            size="small"
            @click="handleDownload"
          />
        </el-tooltip>
      </div>
    </div>
    <div class="chart-container">
      <div
        v-loading="loading"
        :id="chartId"
        :style="{ height: height }"
      ></div>
    </div>
<!--    <div v-if="showLegend" class="legend-container">-->
<!--      <el-check-tag-->
<!--        v-for="(item, index) in legendData"-->
<!--        :key="index"-->
<!--        :checked="activeLegends.includes(item.name)"-->
<!--        @change="val => toggleLegend(item.name, val)"-->
<!--      >-->
<!--        {{ item.name }}-->
<!--      </el-check-tag>-->
<!--    </div>-->
  </el-card>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, computed, onUnmounted} from 'vue';
import * as echarts from 'echarts';
import {useElementSize} from '@vueuse/core';
import {Refresh, Download} from '@element-plus/icons-vue';

const props = defineProps({
  title: String,
  data: {
    type: [Array, Object],
    default: () => [],
  },
  xAxis: {
    type: Array,
    default: () => [],
  },
  height: {
    type: String,
    default: '300px',
  },
  showLegend: {
    type: Boolean,
    default: true,
  },
  smooth: {
    type: Boolean,
    default: true,
  },
  areaStyle: {
    type: Boolean,
    default: false,
  },
  loading: Boolean,
  colorScheme: {
    type: Array,
    default: () => ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
  },
  isMultiLine: {// 新增一个prop来明确指定是单线还是多线
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['refresh']);

const chartId = `line-chart-${Math.random().toString(36).substr(2, 9)}`;
let chartInstance = null;
const activeLegends = ref([]);

// 处理图例数据
const legendData = computed(() => {
  if (Array.isArray(props.data)) {
    return [{name: props.title || '数据'}];
  }
  return props.data.map(item => ({
    name: item.name,
    color: item.color || props.colorScheme[props.data.indexOf(item) % props.colorScheme.length],
  }));
});

// 初始化图例状态
const initLegends = () => {
  activeLegends.value = legendData.value.map(item => item.name);
};

// 切换图例显示
const toggleLegend = (name, visible) => {
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: 'legendToggleSelect',
      name: name,
    });

    if (visible) {
      activeLegends.value.push(name);
    } else {
      activeLegends.value = activeLegends.value.filter(item => item !== name);
    }
  }
};

// 初始化图表
const initChart = () => {
  const chartDom = document.getElementById(chartId);
  if (!chartDom) {
    console.error(`无法找到图表容器: #${chartId}`);
    return;
  }
  if (!props.data || props.data.length === 0) {
    console.error('LineChart: 数据为空或格式不正确');
    return;
  }

  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartDom);

  let seriesData = [];
  let xAxisData = [];
  if (props.isMultiLine) {
    // 多线条数据处理
    if (Array.isArray(props.data) && props.data.every(item => item.data)) {
      seriesData = props.data.map((item, index) =>
        generateSeries(item.name, item.data, item.color || props.colorScheme[index % props.colorScheme.length]),
      );
      // 获取x轴数据
      xAxisData = props.xAxis && props.xAxis.length > 0
        ? props.xAxis
        : Array.from({length: props.data[0].data.length}, (_, i) => `第${i + 1}期`);
    } else {
      console.error('多线条模式需要数据格式为: [{name: "系列1", data: [1,2,3]}, ...]');
      return;
    }
  } else {
    // 单线条数据处理
    if (Array.isArray(props.data)) {
      seriesData = [generateSeries(props.title || '数据', props.data, props.colorScheme[0])];
      xAxisData = props.xAxis && props.xAxis.length > 0
        ? props.xAxis
        : Array.from({length: props.data.length}, (_, i) => `第${i + 1}期`);
    } else {
      console.error('单线条模式需要数据格式为: [1,2,3] 或 {data: [1,2,3]}');
      return;
    }
  }

  const option = {
    title: {
      // text: props.title,
      left: 'left',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#606266',
      },
      padding: [0, 0, 5, 0],
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: params => {
        let html = `<div style="margin-bottom:5px;font-weight:bold">${params[0].axisValue}</div>`;
        params.forEach(item => {
          html += `
            <div style="display:flex;align-items:center;margin:3px 0">
              <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background:${item.color};margin-right:5px"></span>
              ${item.seriesName}: <strong style="margin-left:5px">${item.value}</strong>
            </div>
          `;
        });
        return html;
      },
    },
    legend: props.showLegend ? {
      type: 'scroll',
      bottom: 0,
      data: legendData.value.map(item => item.name),
      selected: Object.fromEntries(legendData.value.map(item => [item.name, activeLegends.value.includes(item.name)])),
    } : undefined,
    grid: {
      left: '3%',
      right: '4%',
      bottom: props.showLegend ? '20px' : '3%',
      top: props.title ? '30px' : '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#DCDFE6',
        },
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        color: '#909399',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#909399',
      },
      splitLine: {
        lineStyle: {
          color: ['#F2F6FC'],
          type: 'dashed',
        },
      },
    },
    series: seriesData,
  };

  chartInstance.setOption(option);

  // 添加图表点击事件
  chartInstance.on('click', params => {
    emit('chart-click', params);
  });
};

// 生成系列配置
const generateSeries = (name, data, color) => {
  return {
    name,
    type: 'line',
    smooth: props.smooth,
    data,
    showSymbol: data.length < 20,
    areaStyle: props.areaStyle ? {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {offset: 0, color: `${color}80`},
        {offset: 1, color: `${color}10`},
      ]),
    } : undefined,
    lineStyle: {
      width: 3,
      color,
    },
    symbol: 'circle',
    symbolSize: 8,
    itemStyle: {
      color,
      borderColor: '#fff',
      borderWidth: 2,
    },
    emphasis: {
      focus: 'series',
      itemStyle: {
        borderColor: color,
        borderWidth: 3,
      },
    },
  };
};

// 刷新图表
const handleRefresh = () => {
  emit('refresh');
};

// 下载图表
const handleDownload = () => {
  if (chartInstance) {
    const url = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff',
    });
    const link = document.createElement('a');
    link.href = url;
    link.download = `${props.title || 'chart'}_${new Date().toLocaleDateString()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 响应式调整
const {width} = useElementSize(document.getElementById(chartId));
watch(width, () => {
  chartInstance && chartInstance.resize();
});

watch(() => props.data, () => {
  initLegends();
  initChart();
}, {deep: true});

onMounted(() => {
  nextTick(() => {
    initLegends();
    initChart();
  });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped>
.chart-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  //margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  //margin-bottom: 15px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.chart-actions {
  display: flex;
  gap: 5px;
}

.chart-container {
  width: 100%;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #eee;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-actions {
    align-self: flex-end;
  }
}
</style>
