<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span class="card-title">心理健康与资助</span>
        <el-tag type="danger" effect="dark" size="small">
          敏感数据
        </el-tag>
      </div>
    </template>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="心理健康" name="mental" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <pie-chart
              title="心理咨询问题类型分布"
              :data="mentalData.counselingRecords"
              :loading="loading"
              height="400px"
              :show-legend="true"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <div class="intervention-container">
              <div class="intervention-rate">
                <h3>危机干预成功率</h3>
                <el-progress
                  type="dashboard"
                  :percentage="mentalData.interventionSuccessRate"
                  :color="getProgressColor(mentalData.interventionSuccessRate)"
                  :width="200"
                />
                <div class="rate-text">
                  {{ mentalData.interventionSuccessRate }}%
                  <span class="trend" :class="getTrendClass(mentalData.interventionTrend)">
                    <el-icon :size="14">
                      <CaretTop v-if="mentalData.interventionTrend >= 0" />
                      <CaretBottom v-else />
                    </el-icon>
                    {{ Math.abs(mentalData.interventionTrend) }}%
                  </span>
                </div>
              </div>
              <div class="intervention-stats">
                <div class="stat-item">
                  <div class="stat-label">本月干预次数</div>
                  <div class="stat-value">{{ mentalData.monthlyInterventions }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">成功次数</div>
                  <div class="stat-value success">{{ mentalData.successfulInterventions }}</div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">失败次数</div>
                  <div class="stat-value danger">{{ mentalData.failedInterventions }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never" class="high-risk-table">
              <template #header>
                <div class="table-header">
                  <span>高危学生跟踪</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="exportHighRiskData"
                  >
                    导出名单
                  </el-button>
                </div>
              </template>
              <el-table
                :data="mentalData.highRiskStudents"
                border
                style="width: 100%"
                v-loading="loading"
                @sort-change="handleSortChange"
              >
                <el-table-column
                  prop="id"
                  label="学号"
                  width="120"
                  sortable
                />
                <el-table-column
                  prop="name"
                  label="姓名"
                  width="100"
                />
                <el-table-column
                  prop="college"
                  label="学院"
                  width="150"
                  :filters="collegeFilters"
                  :filter-method="filterCollege"
                />
                <el-table-column
                  prop="riskLevel"
                  label="风险等级"
                  width="120"
                  sortable
                >
                  <template #default="{row}">
                    <el-tag
                      :type="getRiskTagType(row.riskLevel)"
                      effect="dark"
                    >
                      {{ row.riskLevel }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="intervention"
                  label="干预措施"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="status"
                  label="当前状态"
                  width="120"
                >
                  <template #default="{row}">
                    <el-tag :type="row.status === '稳定' ? 'success' : 'warning'">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="120"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="handleDetail(row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="mentalData.totalHighRisk"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="资助帮扶" name="support" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="aid-card">
              <template #header>
                <span>贫困生资助情况</span>
              </template>
              <div class="aid-info">
                <div class="info-row">
                  <div class="info-item">
                    <div class="info-label">贫困生人数</div>
                    <div class="info-value">
                      {{ financialData.povertyStudents }}
                      <span class="unit">人</span>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">资助覆盖率</div>
                    <div class="info-value">
                      {{ financialData.aidCoverage }}
                      <span class="unit">%</span>
                    </div>
                  </div>
                </div>
                <el-divider />
                <div class="info-row">
                  <div class="info-item">
                    <div class="info-label">勤工助学岗位</div>
                    <div class="info-value">
                      {{ financialData.workStudyPositions.available }}
                      <span class="unit">个</span>
                    </div>
                    <div class="info-sub">
                      申请人数: {{ financialData.workStudyPositions.applied }}人
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">资助总金额</div>
                    <div class="info-value">
                      {{ financialData.totalAidAmount }}
                      <span class="unit">万元</span>
                    </div>
                    <div class="info-sub">
                      人均: {{ financialData.averageAid }}元
                    </div>
                  </div>
                </div>
              </div>
              <div class="aid-chart">
                <bar-chart
                  title="各学院贫困生分布"
                  :data="financialData.collegeDistribution"
                  height="250px"
                  :color-scheme="['#409EFF']"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="loan-card">
              <template #header>
                <span>助学贷款情况</span>
              </template>
              <div class="loan-info">
                <div class="info-row">
                  <div class="info-item">
                    <div class="info-label">贷款总人数</div>
                    <div class="info-value">
                      {{ loanData.total }}
                      <span class="unit">人</span>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">逾期人数</div>
                    <div class="info-value danger">
                      {{ loanData.overdue }}
                      <span class="unit">人</span>
                    </div>
                  </div>
                </div>
                <el-divider />
                <div class="info-row">
                  <div class="info-item">
                    <div class="info-label">逾期率</div>
                    <div class="info-value">
                      {{ loanData.overdueRate }}
                      <span class="unit">%</span>
                    </div>
                    <div class="info-sub">
                      <el-tag :type="loanData.overdueTrend >= 0 ? 'danger' : 'success'" size="small">
                        <el-icon :size="12">
                          <CaretTop v-if="loanData.overdueTrend >= 0" />
                          <CaretBottom v-else />
                        </el-icon>
                        {{ Math.abs(loanData.overdueTrend) }}%
                      </el-tag>
                      同比
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">贷款总额</div>
                    <div class="info-value">
                      {{ loanData.totalAmount }}
                      <span class="unit">万元</span>
                    </div>
                    <div class="info-sub">
                      人均: {{ loanData.averageLoan }}元
                    </div>
                  </div>
                </div>
              </div>
              <div class="loan-chart">
                <line-chart
                  title="逾期率趋势"
                  :data="loanData.trendData"
                  :isMultiLine="true"
                  height="250px"
                  :color-scheme="['#F56C6C']"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <span>资助申请审批</span>
              </template>
              <el-table
                :data="financialData.applications"
                border
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column
                  prop="id"
                  label="申请编号"
                  width="120"
                />
                <el-table-column
                  prop="student"
                  label="学生信息"
                  width="180"
                >
                  <template #default="{row}">
                    <div>{{ row.student.name }}</div>
                    <div style="color: #909399; font-size: 12px">{{ row.student.id }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="资助类型"
                  width="120"
                />
                <el-table-column
                  prop="amount"
                  label="申请金额"
                  width="120"
                >
                  <template #default="{row}">
                    {{ row.amount }}元
                  </template>
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="审批状态"
                  width="120"
                >
                  <template #default="{row}">
                    <el-tag :type="getStatusTagType(row.status)">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="date"
                  label="申请日期"
                  width="120"
                />
                <el-table-column
                  label="操作"
                  width="120"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="handleApplicationDetail(row)"
                    >
                      {{ row.status === '待审批' ? '审批' : '详情' }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="appCurrentPage"
                  v-model:page-size="appPageSize"
                  :total="financialData.totalApplications"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleAppSizeChange"
                  @current-change="handleAppCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <!-- 学生详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="`学生详情 - ${currentStudent.name} (${currentStudent.id})`"
      width="60%"
    >
      <el-descriptions border>
        <el-descriptions-item label="学院">{{ currentStudent.college }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ currentStudent.major }}</el-descriptions-item>
        <el-descriptions-item label="年级">{{ currentStudent.grade }}</el-descriptions-item>
        <el-descriptions-item label="风险等级">
          <el-tag :type="getRiskTagType(currentStudent.riskLevel)" effect="dark">
            {{ currentStudent.riskLevel }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最近咨询时间">{{ currentStudent.lastCounseling }}</el-descriptions-item>
        <el-descriptions-item label="咨询次数">{{ currentStudent.counselingCount }}</el-descriptions-item>
        <el-descriptions-item label="主要问题" span="3">
          {{ currentStudent.mainIssues.join('、') }}
        </el-descriptions-item>
        <el-descriptions-item label="干预措施" span="3">
          {{ currentStudent.interventionMeasures }}
        </el-descriptions-item>
        <el-descriptions-item label="当前状态" span="3">
          <el-tag :type="currentStudent.status === '稳定' ? 'success' : 'warning'">
            {{ currentStudent.status }}
          </el-tag>
          <span style="margin-left: 10px">{{ currentStudent.statusDescription }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleFollowUp">
          跟踪记录
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import PieChart from './charts/PieChart.vue'
import BarChart from './charts/BarChart.vue'
import LineChart from './charts/LineChart.vue'

// 状态管理
const loading = ref(false)
const activeTab = ref('mental')
const currentPage = ref(1)
const pageSize = ref(10)
const appCurrentPage = ref(1)
const appPageSize = ref(10)
const detailDialogVisible = ref(false)
const currentStudent = ref({})

// 模拟数据 - 心理健康
const mentalData = ref({
  counselingRecords: [
    { name: '学业压力', value: 68 },
    { name: '人际关系', value: 45 },
    { name: '情绪困扰', value: 32 },
    { name: '家庭问题', value: 28 },
    { name: '其他问题', value: 15 }
  ],
  interventionSuccessRate: 82.5,
  interventionTrend: 3.2,
  monthlyInterventions: 24,
  successfulInterventions: 20,
  failedInterventions: 4,
  highRiskStudents: generateHighRiskStudents(10),
  totalHighRisk: 50
})

// 模拟数据 - 资助帮扶
const financialData = ref({
  povertyStudents: 1256,
  aidCoverage: '92.5',
  workStudyPositions: {
    available: 86,
    applied: 132
  },
  totalAidAmount: 385.6,
  averageAid: 3068,
  collegeDistribution: [
    { name: '计算机学院', value: 320 },
    { name: '电子学院', value: 280 },
    { name: '机械学院', value: 250 },
    { name: '经管学院', value: 220 },
    { name: '外语学院', value: 186 }
  ],
  applications: generateApplications(10),
  totalApplications: 10
})

// 模拟数据 - 助学贷款
const loanData = ref({
  total: 843,
  overdue: 28,
  overdueRate: '3.3',
  overdueTrend: -1.2,
  totalAmount: 1264.5,
  averageLoan: 15000,
  trendData: [
    { name: '逾期率', data: [4.2, 3.8, 3.5, 3.2, 3.6, 3.3] }
  ]
})

// 生成模拟高危学生数据
function generateHighRiskStudents(count) {
  const colleges = ['计算机学院', '电子学院', '机械学院', '经管学院', '外语学院']
  const levels = ['高危', '中危', '低危']
  const statuses = ['稳定', '关注', '不稳定']
  const interventions = [
    '心理咨询', '辅导员约谈', '家长沟通',
    '专业治疗', '朋辈帮扶', '定期随访'
  ]
  const issues = [
    '学业压力', '人际关系', '情绪低落',
    '家庭矛盾', '经济困难', '适应问题'
  ]

  return Array.from({ length: count }, (_, i) => ({
    id: `2023${String(i+1).padStart(4, '0')}`,
    name: `学生${i+1}`,
    college: colleges[i % colleges.length],
    riskLevel: levels[i % levels.length],
    intervention: interventions.slice(0, Math.floor(Math.random() * 3) + 1).join('、'),
    status: statuses[i % statuses.length],
    lastCounseling: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    counselingCount: Math.floor(Math.random() * 10) + 1,
    mainIssues: issues
    .sort(() => 0.5 - Math.random())
    .slice(0, Math.floor(Math.random() * 3) + 1),
    statusDescription: [
      '情绪稳定，学习生活正常',
      '仍需定期关注',
      '近期情绪波动较大'
    ][i % 3]
  }))
}

// 生成模拟资助申请数据
function generateApplications(count) {
  const types = ['国家助学金', '学费减免', '临时困难补助', '勤工助学']
  const statuses = ['待审批', '已通过', '已拒绝', '待补充材料']

  return Array.from({ length: count }, (_, i) => ({
    id: `AID${String(i+1000).padStart(4, '0')}`,
    student: {
      name: `学生${i+100}`,
      id: `2023${String(i+100).padStart(4, '0')}`
    },
    type: types[i % types.length],
    amount: (Math.floor(Math.random() * 10) + 1) * 1000,
    status: statuses[i % statuses.length],
    date: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`
  }))
}

// 计算学院筛选选项
const collegeFilters = computed(() => {
  const colleges = [...new Set(mentalData.value.highRiskStudents.map(item => item.college))]
  return colleges.map(college => ({
    text: college,
    value: college
  }))
})

// 方法定义
const getRiskTagType = (level) => {
  const map = {
    '高危': 'danger',
    '中危': 'warning',
    '低危': ''
  }
  return map[level] || 'info'
}

const getStatusTagType = (status) => {
  const map = {
    '已通过': 'success',
    '待审批': 'warning',
    '已拒绝': 'danger',
    '待补充材料': 'info'
  }
  return map[status] || ''
}

const getProgressColor = (percentage) => {
  if (percentage > 85) return '#67C23A'
  if (percentage > 70) return '#E6A23C'
  return '#F56C6C'
}

const getTrendClass = (trend) => {
  return trend >= 0 ? 'trend-up' : 'trend-down'
}

const handleDetail = (row) => {
  currentStudent.value = {
    ...row,
    major: ['计算机科学与技术', '电子信息', '机械设计', '工商管理', '英语'][row.id % 5],
    grade: `20${row.id % 4 + 20}级`,
    interventionMeasures: row.intervention + (row.riskLevel === '高危' ? '，每月定期随访' : '')
  }
  detailDialogVisible.value = true
}

const handleApplicationDetail = (row) => {
  if (row.status === '待审批') {
    ElMessageBox.prompt('请输入审批意见', '审批处理', {
      confirmButtonText: '通过',
      cancelButtonText: '拒绝',
      inputPattern: /.{5,}/,
      inputErrorMessage: '至少输入5个字符'
    }).then(({ value }) => {
      ElMessage.success('审批通过: ' + value)
    }).catch(() => {
      ElMessageBox.confirm('确定要拒绝该申请吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('申请已拒绝')
      })
    })
  } else {
    ElMessage.info(`查看申请详情: ${row.id}`)
  }
}

const handleFollowUp = () => {
  ElMessageBox.prompt('请输入跟踪记录', '跟踪记录', {
    confirmButtonText: '提交',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPattern: /.{10,}/,
    inputErrorMessage: '至少输入10个字符'
  }).then(({ value }) => {
    ElMessage.success('跟踪记录已提交')
    detailDialogVisible.value = false
  })
}

const exportHighRiskData = () => {
  ElMessageBox.confirm('确定要导出高危学生名单吗?', '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('导出成功')
  })
}

const handleChartClick = (params) => {
  console.log('图表点击:', params)
}

const handleTabChange = (tab) => {
  console.log('切换标签:', tab.props.name)
}

const filterCollege = (value, row) => {
  return row.college === value
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', prop, order)
}

const handleSizeChange = (val) => {
  console.log('每页条数:', val)
}

const handleCurrentChange = (val) => {
  console.log('当前页:', val)
}

const handleAppSizeChange = (val) => {
  console.log('申请每页条数:', val)
}

const handleAppCurrentChange = (val) => {
  console.log('申请当前页:', val)
}

// 初始化数据
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 800))
  } catch (error) {
    ElMessage.error('数据加载失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}


.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.mt-20 {
  margin-top: 20px;
}

.intervention-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.intervention-rate {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  //flex: 1;
}

.rate-text {
  margin-top: 30px;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.trend {
  font-size: 14px;
  margin-left: 8px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.intervention-stats {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
  margin-top: 28px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.danger {
  color: #f56c6c;
}

.high-risk-table {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.aid-card, .loan-card {
  height: 100%;
}

.aid-info, .loan-info {
  padding: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.info-item {
  flex: 1;
  padding: 0 10px;
}

.info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.info-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.info-value.danger {
  color: #f56c6c;
}

.info-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.unit {
  font-size: 14px;
  margin-left: 2px;
  font-weight: normal;
}

.aid-chart, .loan-chart {
  margin-top: 15px;
  padding: 0 15px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .info-row {
    flex-direction: column;
  }

  .info-item {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .intervention-container {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .intervention-rate {
    width: 100%;
    margin-bottom: 20px;
  }

  .intervention-stats {
    width: 100%;
  }

  .el-col {
    margin-bottom: 20px;
  }
}
</style>
