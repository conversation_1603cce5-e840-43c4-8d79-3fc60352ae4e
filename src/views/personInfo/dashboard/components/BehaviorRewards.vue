<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span class="card-title">学生基础信息</span>
        <el-tooltip content="数据更新于昨日24:00" placement="top">
          <el-icon class="header-icon">
            <InfoFilled/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>

    <el-row :gutter="20">
      <!-- 性别分布 -->
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <pie-chart
          title="性别分布"
          :data="genderDistribution"
          :loading="loading"
          height="300px"
        />
      </el-col>

      <!-- 年级分布 -->
      <el-col :xs="24" :sm="12"  :md="8" :lg="8" :xl="8">
        <bar-chart
          title="年级分布"
          :data="gradeDistribution"
          :loading="loading"
          height="300px"
        />
      </el-col>

      <!-- 院系分布 -->
      <el-col :xs="24" :sm="12"  :md="8" :lg="8" :xl="8">
        <pie-chart
          title="院系分布"
          :data="departmentDistribution"
          :loading="loading"
          height="300px"
          :show-legend="false"
        />
      </el-col>

      <!-- 生源地热力图 -->
<!--      <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">-->
<!--        <div class="map-container">-->
<!--          <div class="map-header">-->
<!--            <h3>生源地分布</h3>-->
<!--            <el-select-->
<!--              v-model="mapType"-->
<!--              size="small"-->
<!--              style="width: 120px"-->
<!--              @change="handleMapTypeChange"-->
<!--            >-->
<!--              <el-option label="热力图" value="heat"/>-->
<!--              <el-option label="分级图" value="piecewise"/>-->
<!--            </el-select>-->
<!--          </div>-->
<!--          <div-->
<!--            id="originMap"-->
<!--            style="height: 300px;"-->
<!--            v-loading="loading"-->
<!--          ></div>-->
<!--        </div>-->
<!--      </el-col>-->
    </el-row>
  </el-card>
</template>

<script setup>
import {ref, onMounted, watch, nextTick, onUnmounted} from 'vue';
import * as echarts from 'echarts';
import { InfoFilled } from '@element-plus/icons-vue';
import PieChart from './charts/PieChart.vue';
import BarChart from './charts/BarChart.vue';

// // 模拟中国地图数据（实际项目中应该从文件导入）
// const chinaJson = {
//   "type": "FeatureCollection",
//   "features": [
//     // 这里应该有完整的中国地图GeoJSON数据
//     // 实际项目中应该从文件导入: import chinaJson from '@/assets/map/china.json'
//   ]
// };
//
// // 注册中国地图
// echarts.registerMap('china', chinaJson);

const props = defineProps({
  loading: Boolean,
});

// 模拟数据
const genderDistribution = ref([
  {value: 6872, name: '男生'},
  {value: 5671, name: '女生'},
]);

const gradeDistribution = ref([
  {name: '大一', value: 3421},
  {name: '大二', value: 3287},
  {name: '大三', value: 2985},
  {name: '大四', value: 2850},
]);

const departmentDistribution = ref([
  {name: '计算机学院', value: 3200},
  {name: '电子学院', value: 2800},
  {name: '机械学院', value: 2500},
  {name: '经管学院', value: 2200},
  {name: '外语学院', value: 1843},
]);

const originData = ref({
  '北京': 532,
  '上海': 478,
  '广东': 865,
  '江苏': 723,
  '浙江': 645,
  '四川': 432,
  '湖北': 387,
  '山东': 568,
  '河南': 476,
  '陕西': 321,
  // 添加更多省份数据确保地图显示完整
  '天津': 210,
  '重庆': 345,
  '河北': 289,
  '山西': 187,
  '辽宁': 276,
  '吉林': 165,
  '黑龙江': 198,
  '安徽': 312,
  '福建': 287,
  '江西': 234,
  '湖南': 356,
  '广西': 278,
  '海南': 123,
  '贵州': 189,
  '云南': 267,
  '西藏': 45,
  '甘肃': 156,
  '青海': 78,
  '宁夏': 92,
  '新疆': 134,
  '内蒙古': 167,
});
//
// const mapType = ref('heat');
// let mapChart = null;
//
// // 渲染地图
// const renderChinaMap = () => {
//   const mapDom = document.getElementById('originMap');
//   if (!mapDom) {
//     console.error('地图容器未找到');
//     return;
//   }

  // 确保DOM已经完全渲染
  // nextTick(() => {
  //   try {
  //     // // 销毁旧实例
  //     // if (mapChart) {
  //     //   mapChart.dispose();
  //     // }
  //     //
  //     // // 初始化图表
  //     // mapChart = echarts.init(mapDom);
  //
  //     // 准备数据
  //     // const maxValue = Math.max(...Object.values(originData.value));
  //     // const data = Object.entries(originData.value).map(([name, value]) => ({
  //     //   name,
  //     //   value,
  //     // }));
  //
  //     // 基础配置
  //     // const baseOption = {
  //     //   backgroundColor: '#fff',
  //     //   title: {
  //     //     text: '生源地分布',
  //     //     left: 'center',
  //     //     top: 10,
  //     //     textStyle: {
  //     //       color: '#333',
  //     //       fontSize: 14
  //     //     }
  //     //   },
  //     //   tooltip: {
  //     //     trigger: 'item',
  //     //     formatter: params => {
  //     //       if (!params.value) return params.name;
  //     //       return `
  //     //         <div style="font-weight:bold;margin-bottom:5px">${params.name}</div>
  //     //         <div style="display:flex;justify-content:space-between">
  //     //           <span>学生人数:</span>
  //     //           <span style="font-weight:bold;color:#409EFF">${params.value}</span>
  //     //         </div>
  //     //         <div style="display:flex;justify-content:space-between">
  //     //           <span>占比:</span>
  //     //           <span style="font-weight:bold;color:#67C23A">${((params.value / 12543) * 100).toFixed(1)}%</span>
  //     //         </div>
  //     //       `;
  //     //     },
  //     //     backgroundColor: 'rgba(255,255,255,0.95)',
  //     //     borderColor: '#ddd',
  //     //     borderWidth: 1,
  //     //     padding: 10,
  //     //     textStyle: {
  //     //       color: '#333',
  //     //     },
  //     //   },
  //     //   visualMap: getVisualMapConfig(mapType.value, maxValue),
  //     //   series: [
  //     //     {
  //     //       name: '生源地分布',
  //     //       type: 'map',
  //     //       map: 'china',
  //     //       roam: true,
  //     //       scaleLimit: {
  //     //         min: 1,
  //     //         max: 3,
  //     //       },
  //     //       label: {
  //     //         show: true,
  //     //         color: '#333',
  //     //         fontSize: 10
  //     //       },
  //     //       emphasis: {
  //     //         label: {
  //     //           show: true,
  //     //           color: '#333',
  //     //         },
  //     //         itemStyle: {
  //     //           areaColor: '#FFD700',
  //     //           borderWidth: 1
  //     //         },
  //     //       },
  //     //       itemStyle: {
  //     //         areaColor: '#eee',
  //     //         borderColor: '#aaa',
  //     //         borderWidth: 0.5
  //     //       },
  //     //       data: data,
  //     //     }
  //     //   ]
  //     // };
  //     //
  //     // mapChart.setOption(baseOption);
  //
  //     // 添加resize监听
  //     // window.addEventListener('resize', handleResize);
  //
  //   } catch (error) {
  //     console.error('地图渲染错误:', error);
  //   }
  // });
// };

// 获取视觉映射配置
const getVisualMapConfig = (type, maxValue) => {
  if (type === 'heat') {
    return {
      type: 'continuous',
      min: 0,
      max: maxValue,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695'],
      },
      textStyle: {
        color: '#666',
      },
      bottom: 20,
      left: 'right'
    };
  } else {
    return {
      type: 'piecewise',
      pieces: [
        {min: 700, label: '700人以上', color: '#313695'},
        {min: 500, max: 700, label: '500-700人', color: '#4575b4'},
        {min: 300, max: 500, label: '300-500人', color: '#74add1'},
        {min: 100, max: 300, label: '100-300人', color: '#abd9e9'},
        {max: 100, label: '100人以下', color: '#e0f3f8'},
      ],
      textStyle: {
        color: '#666',
      },
      bottom: 20,
      left: 'right'
    };
  }
};

// const handleMapTypeChange = () => {
//   if (mapChart) {
//     const option = mapChart.getOption();
//     option.visualMap = getVisualMapConfig(mapType.value, Math.max(...Object.values(originData.value)));
//     mapChart.setOption(option);
//   }
// };
//
// const handleResize = () => {
//   mapChart && mapChart.resize();
// };
//
// onMounted(() => {
//   renderChinaMap();
// });

// onUnmounted(() => {
//   window.removeEventListener('resize', handleResize);
//   if (mapChart) {
//     mapChart.dispose();
//     mapChart = null;
//   }
// });
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-icon {
  color: #909399;
  cursor: pointer;
}

.map-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.map-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

#originMap {
  width: 100%;
  min-height: 300px;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }

  #originMap {
    height: 250px !important;
  }
}
</style>
