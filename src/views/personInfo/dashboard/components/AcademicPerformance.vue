<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span class="card-title">学业表现</span>
        <el-tooltip content="数据每2小时自动更新" placement="top">
          <el-icon class="header-icon">
            <InfoFilled/>
          </el-icon>
        </el-tooltip>
      </div>
    </template>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="成绩分析" name="score" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <line-chart
              title="各院系平均成绩对比"
              :data="scoreData.departmentScores"
              :loading="loading"
              height="400px"
              :color-scheme="departmentColors"
              :isMultiLine="true"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <bar-chart
              title="挂科率TOP10课程"
              :data="scoreData.failCourseTop10"
              height="400px"
              :loading="loading"
              :color-scheme="['#F56C6C']"
              @chart-click="handleChartClick"
            />
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <ele-pro-table ref="tableRef"
                           row-key="id"
                           :columns="columns"
                           :datasource="datasource"
                           :border="true"
                           :show-overflow-tooltip="true"
                           tooltip-effect="light"
                           :tools="['reload', 'size', 'columns', 'maximized']"
                           highlight-current-row
                           cache-key="systemUserTable"
                           :footer-style="{ paddingBottom: '3px' }"
                           style="padding-bottom: 0">
              <template #toolbar>
                <el-button size="small">
                  导出
                </el-button>
              </template>
              <template #percentage="{ row }">
                <el-progress
                  :percentage="parseFloat(row.percentage)"
                  :color="getGpaColor(row.range)"
                  :show-text="false"
                />
                <span style="margin-left: 10px">{{ row.percentage }}</span>
              </template>
              <template #trend="{ row }">
                <el-icon
                  v-if="row.trend === 'up'"
                  color="#F56C6C"
                  size="18"
                >
                  <Top/>
                </el-icon>
                <el-icon
                  v-else-if="row.trend === 'down'"
                  color="#67C23A"
                  size="18"
                >
                  <Bottom/>
                </el-icon>
                <span v-else>-</span>
              </template>
            </ele-pro-table>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="学业预警" name="warning" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <bar-chart
              title="学业预警等级分布"
              :data="warningData.levelDistribution"
              height="400px"
              :loading="loading"
              :color-scheme="['#F56C6C', '#E6A23C', '#909399']"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <line-chart
              title="预警学生转化趋势"
              :data="warningData.conversionTrend.data"
              :x-axis="warningData.conversionTrend.months"
              :loading="loading"
              :isMultiLine="true"
              height="400px"
              :area-style="true"
              @chart-click="handleChartClick"
            />
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-table
              :data="warningData.students"
              border
              style="width: 100%"
              v-loading="loading"
              @sort-change="handleSortChange"
            >
              <el-table-column
                prop="id"
                label="学号"
                width="120"
                sortable
              />
              <el-table-column
                prop="name"
                label="姓名"
                width="100"
              />
              <el-table-column
                prop="college"
                label="学院"
                width="150"
                :filters="collegeFilters"
                :filter-method="filterCollege"
              />
              <el-table-column
                prop="major"
                label="专业"
                width="150"
              />
              <el-table-column
                prop="warningLevel"
                label="预警等级"
                width="120"
                sortable
              >
                <template #default="{row}">
                  <el-tag
                    :type="getWarningTagType(row.warningLevel)"
                    effect="dark"
                  >
                    {{ row.warningLevel }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="warningReason"
                label="预警原因"
                show-overflow-tooltip
              />
              <el-table-column
                label="操作"
                width="120"
                fixed="right"
              >
                <template #default="{row}">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleDetail(row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="warningData.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import {ref, onMounted, computed} from 'vue';
import {ElMessage, ElLoading} from 'element-plus';
import {InfoFilled, Top, Bottom} from '@element-plus/icons-vue';
import LineChart from './charts/LineChart.vue';
import BarChart from './charts/BarChart.vue';
import {queryPage} from '@/views/base-code/dwb/api/index.js';

// 数据状态
const loading = ref(false);
const activeTab = ref('score');
const currentPage = ref(1);
const pageSize = ref(10);

// 颜色配置
const departmentColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#FF9F43', '#47C4C4', '#9860E5', '#FF6B6B', '#4ECDC4',
];

// 模拟数据 - 实际项目中替换为API调用
const scoreData = ref({
  departmentScores: [
    {name: '计算机学院', data: [85, 82, 88, 83, 86, 84]},
    {name: '电子学院', data: [78, 80, 82, 79, 81, 83]},
    {name: '机械学院', data: [76, 78, 75, 77, 79, 80]},
    {name: '经管学院', data: [82, 84, 83, 81, 85, 83]},
    {name: '外语学院', data: [88, 86, 87, 89, 85, 87]},
  ],
  failCourseTop10: [
    {name: '高等数学', value: 23.5},
    {name: '数据结构', value: 18.7},
    {name: '大学物理', value: 15.2},
    {name: '线性代数', value: 12.8},
    {name: '概率统计', value: 11.3},
    {name: '离散数学', value: 9.7},
    {name: '电路原理', value: 8.9},
    {name: '算法分析', value: 7.5},
    {name: '编译原理', value: 6.8},
    {name: '操作系统', value: 5.9},
  ],
  gpaDistribution: [
    {range: '3.5-4.0', count: 1254, percentage: '10.0%', trend: 'up'},
    {range: '3.0-3.5', count: 3762, percentage: '30.0%', trend: 'up'},
    {range: '2.5-3.0', count: 5016, percentage: '40.0%', trend: 'down'},
    {range: '2.0-2.5', count: 1881, percentage: '15.0%', trend: 'down'},
    {range: '0-2.0', count: 627, percentage: '5.0%', trend: 'down'},
  ],
});

const warningData = ref({
  levelDistribution: [
    {name: '红色预警', value: 56},
    {name: '橙色预警', value: 128},
    {name: '黄色预警', value: 342},
  ],
  conversionTrend: {
    months: ['9月', '10月', '11月', '12月', '1月', '2月'],
    data: [
      {name: '解除预警', data: [12, 18, 25, 32, 28, 35]},
      {name: '新增预警', data: [45, 38, 42, 36, 28, 22]},
    ],
  },
  students: generateWarningStudents(10),
  total: 100,
});

/** 表格列配置 */
const columns = ref([
  {
    prop: 'range',
    label: 'GPA区间',
  },
  {
    prop: 'count',
    label: '人数',
  },
  {
    prop: 'percentage',
    slot: 'percentage',
    label: '占比',
  },
  {
    prop: 'trend',
    slot: 'trend',
    label: '趋势',
  },
]);

const datasource = async ({pages, filter}) => {
  return {
    count: scoreData.value.gpaDistribution.length,
    list: scoreData.value.gpaDistribution,
  };
};

// const datasource = ({page, limit, where, orders, filters}) => {
//   return queryPage({...where, ...orders, ...filters, page, limit});
// };

// 生成模拟预警学生数据
function generateWarningStudents(count) {
  const colleges = ['计算机学院', '电子学院', '机械学院', '经管学院', '外语学院'];
  const majors = [
    '计算机科学与技术', '软件工程', '电子信息', '机械设计',
    '工商管理', '金融学', '英语', '日语',
  ];
  const levels = ['红色预警', '橙色预警', '黄色预警'];
  const reasons = [
    '累计挂科3门以上',
    '学期学分不足50%',
    '核心课程不及格',
    '连续两学期绩点低于2.0',
  ];

  return Array.from({length: count}, (_, i) => ({
    id: `2023${String(i + 1).padStart(4, '0')}`,
    name: `学生${i + 1}`,
    college: colleges[i % colleges.length],
    major: majors[i % majors.length],
    warningLevel: levels[i % levels.length],
    warningReason: reasons[i % reasons.length],
    gpa: (Math.random() * 2 + 1.5).toFixed(2),
  }));
}

// 计算学院筛选选项
const collegeFilters = computed(() => {
  const colleges = [...new Set(warningData.value.students.map(item => item.college))];
  return colleges.map(college => ({
    text: college,
    value: college,
  }));
});

// 方法定义
const getWarningTagType = (level) => {
  const map = {
    '红色预警': 'danger',
    '橙色预警': 'warning',
    '黄色预警': '',
  };
  return map[level] || 'info';
};

const getGpaColor = (range) => {
  const gpa = parseFloat(range.split('-')[0]);
  if (gpa >= 3.5) return '#67C23A';
  if (gpa >= 3.0) return '#409EFF';
  if (gpa >= 2.5) return '#E6A23C';
  return '#F56C6C';
};

const handleDetail = (row) => {
  ElMessage.info(`查看学生详情: ${row.name} (${row.id})`);
  // 实际项目中这里可以跳转到学生详情页或打开弹窗
};

const handleChartClick = (params) => {
  console.log('图表点击:', params);
};

const handleTabChange = (tab) => {
  console.log('切换标签:', tab.props.name);
};

const filterCollege = (value, row) => {
  return row.college === value;
};

const handleSortChange = ({column, prop, order}) => {
  console.log('排序变化:', prop, order);
};

const handleSizeChange = (val) => {
  console.log('每页条数:', val);
};

const handleCurrentChange = (val) => {
  console.log('当前页:', val);
};

// 初始化数据
const fetchData = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '数据加载中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    loading.value = true;
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 800));
  } catch (error) {
    ElMessage.error('数据加载失败: ' + error.message);
  } finally {
    loading.value = false;
    loadingInstance.close();
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-icon {
  color: #909399;
  cursor: pointer;
}

.mt-20 {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>
