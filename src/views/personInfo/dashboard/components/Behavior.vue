<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span>行为与奖惩</span>
        <el-tag type="warning" effect="dark" size="small">
          实时数据
        </el-tag>
      </div>
    </template>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="考勤管理" name="attendance" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <line-chart
              title="全校出勤率趋势"
              :data="attendanceData.rateTrend.data"
              :x-axis="attendanceData.rateTrend.months"
              :loading="loading"
              height="400px"
              :area-style="true"
              :color-scheme="['#409EFF']"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="table-card">
              <template #header>
                <div class="table-header">
                  <span>高频缺勤学生</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="exportAbsenceData"
                  >
                    导出数据
                  </el-button>
                </div>
              </template>
              <el-table
                :data="attendanceData.frequentAbsence"
                border
                style="width: 100%"
                v-loading="loading"
                @sort-change="handleSortChange"
              >
                <el-table-column
                  prop="id"
                  label="学号"
                  width="120"
                  sortable
                />
                <el-table-column
                  prop="name"
                  label="姓名"
                  width="100"
                />
                <el-table-column
                  prop="college"
                  label="学院"
                  width="150"
                  :filters="collegeFilters"
                  :filter-method="filterCollege"
                />
                <el-table-column
                  prop="absenceCount"
                  label="缺勤次数"
                  width="120"
                  sortable
                >
                  <template #default="{row}">
                    <el-tag :type="getAbsenceTagType(row.absenceCount)">
                      {{ row.absenceCount }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="courses"
                  label="主要缺勤课程"
                  show-overflow-tooltip
                >
                  <template #default="{row}">
                    <el-tag
                      v-for="(course, index) in row.courses.slice(0, 3)"
                      :key="index"
                      size="small"
                      class="course-tag"
                    >
                      {{ course }}
                    </el-tag>
                    <span v-if="row.courses.length > 3">等{{ row.courses.length }}门</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="80"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="handleDetail(row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="attendanceData.total"
                  :page-sizes="[5, 10, 20]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <bar-chart
              title="各学院出勤率对比"
              :data="attendanceData.collegeComparison"
              :loading="loading"
              height="300px"
              :color-scheme="departmentColors"
              @chart-click="handleChartClick"
            />
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="奖惩记录" name="rewards" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <pie-chart
              title="奖学金分布"
              :data="rewardData.scholarshipDistribution"
              :loading="loading"
              height="400px"
              :show-legend="true"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <bar-chart
              title="违纪行为统计"
              :data="rewardData.violationRecords"
              :loading="loading"
              height="400px"
              :color-scheme="['#F56C6C']"
            />
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <span>宿舍矛盾时段分析</span>
                  <el-radio-group
                    v-model="timeRange"
                    size="small"
                    @change="handleTimeRangeChange"
                  >
                    <el-radio-button label="week">本周</el-radio-button>
                    <el-radio-button label="month">本月</el-radio-button>
                    <el-radio-button label="semester">本学期</el-radio-button>
                  </el-radio-group>
                </div>
              </template>
              <bar-chart
                :data="rewardData.dormConflictAnalysis.data"
                :x-axis="rewardData.dormConflictAnalysis.time"
                :loading="loading"
                height="300px"
                :color-scheme="['#E6A23C']"
              />
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <span>典型案例分析</span>
              </template>
              <el-table
                :data="rewardData.caseAnalysis"
                border
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column
                  prop="type"
                  label="类型"
                  width="120"
                >
                  <template #default="{row}">
                    <el-tag :type="getCaseTagType(row.type)">
                      {{ row.type }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="title"
                  label="案例标题"
                />
                <el-table-column
                  prop="time"
                  label="高发时段"
                  width="150"
                />
                <el-table-column
                  prop="count"
                  label="发生次数"
                  width="120"
                  sortable
                />
                <el-table-column
                  label="操作"
                  width="120"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="showCaseDetail(row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <!-- 案例详情弹窗 -->
    <el-dialog
      v-model="caseDialogVisible"
      :title="currentCase.title"
      width="60%"
    >
      <div class="case-detail">
        <el-descriptions border>
          <el-descriptions-item label="发生时间">{{ currentCase.time }}</el-descriptions-item>
          <el-descriptions-item label="涉及人数">{{ currentCase.peopleCount }}人</el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="currentCase.resolved ? 'success' : 'danger'">
              {{ currentCase.resolved ? '已解决' : '处理中' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="案例描述" span="3">
            {{ currentCase.description }}
          </el-descriptions-item>
          <el-descriptions-item label="处理措施" span="3">
            {{ currentCase.solution }}
          </el-descriptions-item>
        </el-descriptions>
        <h4 class="detail-section-title">相关学生</h4>
        <el-table :data="currentCase.students" border>
          <el-table-column prop="id" label="学号" width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="college" label="学院" width="150" />
          <el-table-column prop="class" label="班级" width="120" />
          <el-table-column prop="punishment" label="处分结果" />
        </el-table>
      </div>
      <template #footer>
        <el-button @click="caseDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleCaseFollow">
          跟踪处理
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import LineChart from './charts/LineChart.vue'
import BarChart from './charts/BarChart.vue'
import PieChart from './charts/PieChart.vue'

// 数据状态
const loading = ref(false)
const activeTab = ref('attendance')
const currentPage = ref(1)
const pageSize = ref(10)
const timeRange = ref('month')
const caseDialogVisible = ref(false)
const currentCase = ref({})

// 颜色配置
const departmentColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#FF9F43', '#47C4C4', '#9860E5', '#FF6B6B', '#4ECDC4'
]

// 模拟数据 - 考勤管理
const attendanceData = ref({
  rateTrend: {
    months: ['9月', '10月', '11月', '12月', '1月', '2月'],
    data: [{ name: '出勤率', data: [92.5, 93.2, 94.1, 93.8, 95.2, 96.0] }]
  },
  frequentAbsence: generateAbsenceData(50),
  collegeComparison: [
    { name: '计算机学院', value: 94.5 },
    { name: '电子学院', value: 93.2 },
    { name: '机械学院', value: 91.8 },
    { name: '经管学院', value: 95.1 },
    { name: '外语学院', value: 96.3 }
  ],
  total: 50
})

// 模拟数据 - 奖惩记录
const rewardData = ref({
  scholarshipDistribution: [
    { name: '国家奖学金', value: 15 },
    { name: '校级一等奖', value: 45 },
    { name: '校级二等奖', value: 90 },
    { name: '校级三等奖', value: 150 },
    { name: '院级奖学金', value: 300 }
  ],
  violationRecords: [
    { name: '作弊', value: 12 },
    { name: '旷课', value: 45 },
    { name: '宿舍违规', value: 28 },
    { name: '打架斗殴', value: 5 },
    { name: '其他违纪', value: 10 }
  ],
  dormConflictAnalysis: {
    time: ['6-8时', '8-12时', '12-14时', '14-18时', '18-22时', '22-24时'],
    data: [{ name: '矛盾次数', data: [3, 5, 8, 12, 25, 18] }]
  },
  caseAnalysis: [
    {
      type: '宿舍矛盾',
      title: '噪音扰民引发冲突',
      time: '22:00-24:00',
      count: 8,
      caseId: 'C001'
    },
    // 其他案例...
  ]
})

// 生成模拟缺勤数据
function generateAbsenceData(count) {
  const colleges = ['计算机学院', '电子学院', '机械学院', '经管学院', '外语学院']
  const courses = [
    '高等数学', '大学英语', '数据结构', '线性代数',
    '大学物理', '概率统计', '离散数学', '电路原理'
  ]

  return Array.from({ length: count }, (_, i) => ({
    id: `2023${String(i+1).padStart(4, '0')}`,
    name: `学生${i+1}`,
    college: colleges[i % colleges.length],
    absenceCount: Math.floor(Math.random() * 15) + 5,
    courses: courses
    .sort(() => 0.5 - Math.random())
    .slice(0, Math.floor(Math.random() * 4) + 2)
  }))
}

// 计算学院筛选选项
const collegeFilters = computed(() => {
  const colleges = [...new Set(attendanceData.value.frequentAbsence.map(item => item.college))]
  return colleges.map(college => ({
    text: college,
    value: college
  }))
})

// 方法定义
const getAbsenceTagType = (count) => {
  if (count >= 15) return 'danger'
  if (count >= 10) return 'warning'
  return ''
}

const getCaseTagType = (type) => {
  const map = {
    '宿舍矛盾': 'warning',
    '作弊': 'danger',
    '打架斗殴': 'danger',
    '旷课': 'info'
  }
  return map[type] || ''
}

const handleDetail = (row) => {
  ElMessage.info(`查看学生详情: ${row.name} (${row.id})`)
}

const handleChartClick = (params) => {
  console.log('图表点击:', params)
}

const handleTabChange = (tab) => {
  console.log('切换标签:', tab.props.name)
}

const filterCollege = (value, row) => {
  return row.college === value
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', prop, order)
}

const handleSizeChange = (val) => {
  console.log('每页条数:', val)
}

const handleCurrentChange = (val) => {
  console.log('当前页:', val)
}

const handleTimeRangeChange = (val) => {
  console.log('时间范围:', val)
  // 实际项目中这里应该根据时间范围重新获取数据
}

const exportAbsenceData = () => {
  ElMessage.success('导出数据成功')
  // 实际项目中这里应该调用导出API
}

const showCaseDetail = (row) => {
  // 模拟获取案例详情
  currentCase.value = {
    title: row.title,
    type: row.type,
    time: `${row.time} (${timeRange.value === 'week' ? '本周' : timeRange.value === 'month' ? '本月' : '本学期'})`,
    peopleCount: Math.floor(Math.random() * 5) + 2,
    resolved: Math.random() > 0.3,
    description: `这是一起发生在${row.time}的${row.type}案例，主要原因是...`,
    solution: '已对相关学生进行批评教育，并给予相应处分...',
    students: Array.from({ length: Math.floor(Math.random() * 3) + 2 }, (_, i) => ({
      id: `2023${String(i+100).padStart(4, '0')}`,
      name: `学生${i+100}`,
      college: ['计算机学院', '电子学院', '机械学院'][i % 3],
      class: `20${i % 3 + 1}级${i % 4 + 1}班`,
      punishment: ['警告处分', '严重警告', '记过'][i % 3]
    }))
  }
  caseDialogVisible.value = true
}

const handleCaseFollow = () => {
  ElMessageBox.confirm('确定要跟踪处理此案例吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('已开始跟踪处理')
    caseDialogVisible.value = false
  })
}

// 初始化数据
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 800))
  } catch (error) {
    ElMessage.error('数据加载失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-card {
  height: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.mt-20 {
  margin-top: 20px;
}

.course-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.detail-section-title {
  margin: 20px 0 10px;
  color: #606266;
  font-weight: normal;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>
