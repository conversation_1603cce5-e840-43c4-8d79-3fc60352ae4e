<template>
  <el-card shadow="hover" class="base-info-card">
    <template #header>
      <div class="card-header">
        <h3>学生基础信息</h3>
      </div>
    </template>

    <el-row :gutter="8">
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <div class="chart-container">
          <pie-chart
            title="性别分布" :data="data.genderDistribution" height="200px"/>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <div class="chart-container">
          <bar-chart
            title="年级分布" :data="data.gradeDistribution" height="200px"/>
        </div>
      </el-col>

      <!--      <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6">-->
      <!--        <div class="chart-container">-->
      <!--          <horizontal-bar-chart-->
      <!--            title="院系分布" :data="data.departmentDistribution" height="200px" />-->
      <!--        </div>-->
      <!--      </el-col>-->

      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">少数民族学生:</span>
            <span class="value">{{ data.ethnicDistribution?.[1]?.value || 0 }}人</span>
          </div>
          <div class="info-item">
            <span class="label">国际学生:</span>
            <span class="value">{{ data.internationalStudents || 0 }}人</span>
          </div>
          <div class="info-item">
            <span class="label">城镇/农村:</span>
            <span class="value">{{ data.urbanRuralRatio?.[0]?.value || 0 }}/{{
                data.urbanRuralRatio?.[1]?.value || 0
              }}</span>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup>
import PieChart from './charts/PieChart.vue';
import BarChart from './charts/BarChart.vue';
import HorizontalBarChart from './charts/HorizontalBarChart.vue';

defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
</script>

<style scoped lang="scss">
.base-info-card {
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 16px;
  }
}

.chart-container {

  h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  padding: 10px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;

  .label {
    color: var(--el-text-color-secondary);
  }

  .value {
    font-weight: bold;
  }
}
</style>
