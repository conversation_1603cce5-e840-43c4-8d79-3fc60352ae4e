<template>
  <el-card shadow="hover" class="stat-card">
    <div class="card-content">
      <div class="card-icon" :style="{ backgroundColor: color }">
        <el-icon>
          <component :is="icon"/>
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ title }}</div>
        <div class="card-value">
          <span class="value">{{ formattedValue }}</span>
          <span class="unit">{{ unit }}</span>
        </div>
        <div class="card-trend" :class="trendClass">
          <el-icon v-if="trendIcon">
            <component :is="trendIcon"/>
          </el-icon>
          <span>{{ trendText }}</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import {computed} from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  value: {
    type: [Number, String],
    required: true,
  },
  icon: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '#409EFF',
  },
  trend: {
    type: Number,
    default: 0,
  },
  unit: {
    type: String,
    default: '',
  },
  precision: {
    type: Number,
    default: 2,
  },
});

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toFixed(props.precision);
  }
  return props.value;
});

const trendClass = computed(() => {
  if (props.trend > 0) return 'up';
  if (props.trend < 0) return 'down';
  return 'neutral';
});

const trendIcon = computed(() => {
  if (props.trend > 0) return 'Top';
  if (props.trend < 0) return 'Bottom';
  return null;
});

const trendText = computed(() => {
  if (props.trend === 0) return '持平';
  return `${Math.abs(props.trend)}%`;
});
</script>

<style scoped lang="scss">
.stat-card {
  height: 100%;

  :deep(.el-card__body) {
    padding: 10px 16px 0 16px;
  }
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;

  .el-icon {
    font-size: 24px;
    color: white;
  }
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.card-value {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;

  .value {
    font-size: 24px;
    font-weight: bold;
    margin-right: 4px;
  }

  .unit {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.card-trend {
  font-size: 12px;
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 2px;
  }

  &.up {
    color: var(--el-color-success);
  }

  &.down {
    color: var(--el-color-danger);
  }

  &.neutral {
    color: var(--el-text-color-secondary);
  }
}
</style>
