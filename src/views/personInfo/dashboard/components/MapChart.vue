<template>
  <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <span>生源地分布</span>
        <el-tooltip content="数据每2小时自动更新" placement="top">
          <el-icon class="header-icon"><InfoFilled /></el-icon>
        </el-tooltip>
      </div>
    </template>

    <div class="map-container">
      <div class="map-controls">
        <el-select
          v-model="mapType"
          size="small"
          style="width: 120px"
          @change="handleMapTypeChange"
        >
          <el-option label="热力图" value="heat" />
          <el-option label="分级图" value="piecewise" />
        </el-select>
        <el-button
          size="small"
          @click="refreshMap"
          :loading="loading"
        >
          刷新数据
        </el-button>
      </div>

      <div
        id="chinaMap"
        style="height: 500px; width: 100%"
        v-loading="loading"
      ></div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { InfoFilled } from '@element-plus/icons-vue';

// 完整的中国地图GeoJSON数据（简化版，实际项目应从文件导入）
const chinaJson = {
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "北京",
        "cp": [116.405285, 39.904989]
      },
      "geometry": { /* 几何数据 */ }
    },
    // 其他省份数据...
  ]
};

// 注册地图
echarts.registerMap('china', chinaJson);

const loading = ref(false);
const mapType = ref('heat');
let chartInstance = null;

// 模拟生源地数据
const originData = ref({
  '北京': 532, '上海': 478, '广东': 865, '江苏': 723, '浙江': 645,
  '四川': 432, '湖北': 387, '山东': 568, '河南': 476, '陕西': 321,
  '天津': 210, '重庆': 345, '河北': 289, '山西': 187, '辽宁': 276,
  '吉林': 165, '黑龙江': 198, '安徽': 312, '福建': 287, '江西': 234,
  '湖南': 356, '广西': 278, '海南': 123, '贵州': 189, '云南': 267,
  '西藏': 45, '甘肃': 156, '青海': 78, '宁夏': 92, '新疆': 134,
  '内蒙古': 167
});

// 初始化地图
const initMap = () => {
  const dom = document.getElementById('chinaMap');
  if (!dom) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(dom);
  renderMap();
};

// 渲染地图
const renderMap = () => {
  if (!chartInstance) return;

  const data = Object.entries(originData.value).map(([name, value]) => ({
    name,
    value
  }));

  const maxValue = Math.max(...Object.values(originData.value));
  const totalStudents = Object.values(originData.value).reduce((a, b) => a + b, 0);

  const option = {
    backgroundColor: '#f5f7fa',
    title: {
      text: '学生生源地分布',
      subtext: `总学生数: ${totalStudents}人`,
      left: 'center',
      top: 10,
      textStyle: {
        color: '#333',
        fontSize: 18
      },
      subtextStyle: {
        color: '#666',
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: params => {
        const percent = ((params.value / totalStudents) * 100).toFixed(1);
        return `
          <div style="font-weight:bold;margin-bottom:5px;font-size:14px">${params.name}</div>
          <div style="display:flex;justify-content:space-between">
            <span>人数:</span>
            <span style="font-weight:bold;color:#409EFF">${params.value}人</span>
          </div>
          <div style="display:flex;justify-content:space-between">
            <span>占比:</span>
            <span style="font-weight:bold;color:#67C23A">${percent}%</span>
          </div>
        `;
      }
    },
    visualMap: getVisualMapConfig(),
    series: [{
      name: '生源地',
      type: 'map',
      map: 'china',
      roam: true,
      scaleLimit: {
        min: 1,
        max: 3
      },
      label: {
        show: true,
        fontSize: 10,
        color: '#333'
      },
      emphasis: {
        label: {
          show: true,
          color: '#fff'
        },
        itemStyle: {
          areaColor: '#FFD700',
          borderWidth: 1
        }
      },
      itemStyle: {
        areaColor: '#eee',
        borderColor: '#aaa',
        borderWidth: 0.5
      },
      data: data
    }]
  };

  chartInstance.setOption(option);
};

// 获取视觉映射配置
const getVisualMapConfig = () => {
  const maxValue = Math.max(...Object.values(originData.value));

  if (mapType.value === 'heat') {
    return {
      type: 'continuous',
      min: 0,
      max: maxValue,
      text: ['高', '低'],
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      },
      textStyle: {
        color: '#666'
      },
      bottom: 30,
      left: 'right'
    };
  } else {
    return {
      type: 'piecewise',
      pieces: [
        { min: 800, label: '800人以上', color: '#313695' },
        { min: 600, max: 800, label: '600-800人', color: '#4575b4' },
        { min: 400, max: 600, label: '400-600人', color: '#74add1' },
        { min: 200, max: 400, label: '200-400人', color: '#abd9e9' },
        { max: 200, label: '200人以下', color: '#e0f3f8' }
      ],
      textStyle: {
        color: '#666'
      },
      bottom: 30,
      left: 'right'
    };
  }
};

// 切换地图类型
const handleMapTypeChange = () => {
  if (chartInstance) {
    const option = chartInstance.getOption();
    option.visualMap = getVisualMapConfig();
    chartInstance.setOption(option);
  }
};

// 刷新地图数据
const refreshMap = () => {
  loading.value = true;
  // 模拟API请求
  setTimeout(() => {
    // 这里可以替换为实际的数据获取逻辑
    loading.value = false;
    renderMap();
  }, 800);
};

// 窗口大小变化时重绘
const handleResize = () => {
  chartInstance && chartInstance.resize();
};

onMounted(() => {
  initMap();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-icon {
  color: #909399;
  cursor: pointer;
}

.map-container {
  padding: 15px;
}

.map-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

#chinaMap {
  border-radius: 4px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  #chinaMap {
    height: 350px !important;
  }
}
</style>
