<template>
  <el-dialog
    v-model="visible"
    title="学生详情"
    width="60%"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本信息" name="basic">
        <student-basic-info :student-id="studentId" />
      </el-tab-pane>
      <el-tab-pane label="学业情况" name="academic">
        <student-academic-info :student-id="studentId" />
      </el-tab-pane>
      <el-tab-pane label="预警记录" name="warnings">
        <student-warning-records :student-id="studentId" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
      <el-button type="primary" @click="handleIntervene">发起干预</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import StudentBasicInfo from './StudentBasicInfo.vue'
import StudentAcademicInfo from './StudentAcademicInfo.vue'
import StudentWarningRecords from './StudentWarningRecords.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'intervene', 'refresh'])

const visible = ref(false)
const activeTab = ref('basic')

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const handleClosed = () => {
  activeTab.value = 'basic'
}

const handleIntervene = () => {
  emit('intervene', props.studentId)
  visible.value = false
}
</script>

<style scoped>
:deep(.el-tabs__content) {
  padding: 0 20px;
}
</style>
