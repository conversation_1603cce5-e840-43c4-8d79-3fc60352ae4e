<template>
  <el-dialog
    v-model="visible"
    title="学生干预记录"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="intervention-container">
      <div class="intervention-history">
        <h4>历史干预记录</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(record, index) in records"
            :key="index"
            :timestamp="formatDate(record.time)"
            placement="top"
          >
            <el-card>
              <div class="record-header">
                <span class="record-type">{{ record.type }}</span>
                <el-tag :type="getStatusTagType(record.status)" size="small">
                  {{ record.status }}
                </el-tag>
              </div>
              <div class="record-content">{{ record.content }}</div>
              <div class="record-footer">
                <span class="record-person">记录人: {{ record.person }}</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>

      <div class="intervention-form">
        <h4>新增干预记录</h4>
        <el-form :model="form" label-width="80px">
          <el-form-item label="干预类型">
            <el-select v-model="form.type" placeholder="请选择干预类型">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="干预内容">
            <el-input
              v-model="form.content"
              type="textarea"
              :rows="4"
              placeholder="请输入干预内容"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useDateFormat } from '@vueuse/core'
import { getInterventionRecords, addInterventionRecord } from '@/api/warning'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = ref(false)
const loading = ref(false)
const records = ref([])

const form = ref({
  type: '',
  content: ''
})

const typeOptions = [
  { value: '学业指导', label: '学业指导' },
  { value: '心理辅导', label: '心理辅导' },
  { value: '行为纠正', label: '行为纠正' },
  { value: '经济援助', label: '经济援助' },
  { value: '其他', label: '其他' }
]

watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    await fetchRecords()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const formatDate = (date) => {
  return useDateFormat(date, 'YYYY-MM-DD HH:mm').value
}

const getStatusTagType = (status) => {
  const map = {
    '已完成': 'success',
    '进行中': 'warning',
    '未开始': 'info'
  }
  return map[status] || ''
}

const fetchRecords = async () => {
  try {
    loading.value = true
    const res = await getInterventionRecords(props.studentId)
    records.value = res.data
  } finally {
    loading.value = false
  }
}

const submitForm = async () => {
  try {
    loading.value = true
    await addInterventionRecord({
      studentId: props.studentId,
      ...form.value
    })
    resetForm()
    await fetchRecords()
    emit('refresh')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.value = {
    type: '',
    content: ''
  }
}
</script>

<style scoped>
.intervention-container {
  display: flex;
  gap: 20px;
}

.intervention-history {
  flex: 1;
}

.intervention-form {
  width: 40%;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.record-content {
  margin-bottom: 8px;
  line-height: 1.5;
}

.record-footer {
  text-align: right;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: var(--el-text-color-primary);
}
</style>
