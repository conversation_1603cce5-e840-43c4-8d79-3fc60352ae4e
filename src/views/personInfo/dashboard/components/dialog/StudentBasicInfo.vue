<template>
  <div class="student-basic-info">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="学号">{{ studentInfo.id || '-' }}</el-descriptions-item>
      <el-descriptions-item label="姓名">{{ studentInfo.name || '-' }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ formatGender(studentInfo.gender) }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{ studentInfo.age || '-' }}</el-descriptions-item>
      <el-descriptions-item label="学院">{{ studentInfo.college || '-' }}</el-descriptions-item>
      <el-descriptions-item label="专业">{{ studentInfo.major || '-' }}</el-descriptions-item>
      <el-descriptions-item label="班级">{{ studentInfo.class || '-' }}</el-descriptions-item>
      <el-descriptions-item label="入学时间">{{ formatDate(studentInfo.enrollmentDate) }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ studentInfo.phone || '-' }}</el-descriptions-item>
      <el-descriptions-item label="电子邮箱">{{ studentInfo.email || '-' }}</el-descriptions-item>
      <el-descriptions-item label="家庭住址" :span="2">{{ studentInfo.address || '-' }}</el-descriptions-item>
    </el-descriptions>

    <div class="warning-summary" v-if="studentInfo.warningTypes && studentInfo.warningTypes.length">
      <h4>当前预警状态</h4>
      <el-space wrap>
        <el-tag
          v-for="type in studentInfo.warningTypes"
          :key="type"
          :type="getWarningTagType(type)"
          effect="dark"
          size="large"
        >
          {{ type }}
        </el-tag>
      </el-space>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useDateFormat } from '@vueuse/core'
import { getStudentBasicInfo } from '@/api/student'

const props = defineProps({
  studentId: {
    type: [String, Number],
    required: true
  }
})

const studentInfo = ref({})

const getWarningTagType = (type) => {
  const typeMap = {
    '学业预警': 'danger',
    '心理预警': 'warning',
    '行为预警': 'info',
    '经济困难': 'primary',
    '健康预警': 'success'
  }
  return typeMap[type] || ''
}

const formatGender = (gender) => {
  return gender === 'male' ? '男' : gender === 'female' ? '女' : '-'
}

const formatDate = (date) => {
  if (!date) return '-'
  return useDateFormat(date, 'YYYY-MM-DD').value
}

const fetchData = async () => {
  try {
    const res = await getStudentBasicInfo(props.studentId)
    studentInfo.value = res.data
  } catch (error) {
    console.error('获取学生基本信息失败:', error)
    studentInfo.value = {}
  }
}

watch(() => props.studentId, fetchData, { immediate: true })
</script>

<style scoped>
.student-basic-info {
  padding: 10px;
}

.warning-summary {
  margin-top: 20px;

  h4 {
    margin-bottom: 10px;
    color: var(--el-text-color-primary);
  }
}

:deep(.el-descriptions__body) {
  background-color: var(--el-fill-color-light);
}
</style>
