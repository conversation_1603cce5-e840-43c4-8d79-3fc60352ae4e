<template>
  <div class="student-warning-records">
    <el-timeline>
      <el-timeline-item
        v-for="(record, index) in warningRecords"
        :key="index"
        :timestamp="formatDateTime(record.time)"
        placement="top"
        :type="getTimelineItemType(record.level)"
        :color="getTimelineItemColor(record.level)"
      >
        <el-card shadow="hover">
          <div class="record-header">
            <span class="record-type">{{ record.type }}</span>
            <el-space>
              <el-tag :type="getLevelTagType(record.level)" size="small">
                {{ record.level }}
              </el-tag>
              <el-tag :type="getStatusTagType(record.status)" size="small">
                {{ record.status }}
              </el-tag>
            </el-space>
          </div>
          <div class="record-content">
            <p>{{ record.content }}</p>
            <div v-if="record.details" class="record-details">
              <el-collapse>
                <el-collapse-item title="详情">
                  <pre>{{ formatDetails(record.details) }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
          <div class="record-footer">
            <span class="record-person">记录人: {{ record.creator }}</span>
            <span class="record-actions" v-if="record.status === '未处理'">
              <el-button type="text" size="small" @click="handleProcess(record)">标记为处理中</el-button>
            </span>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>

    <el-empty v-if="!warningRecords.length" description="暂无预警记录" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useDateFormat } from '@vueuse/core'
import { getStudentWarningRecords, processWarningRecord } from '@/api/warning'

const props = defineProps({
  studentId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['refresh'])

const warningRecords = ref([])

const formatDateTime = (date) => {
  if (!date) return ''
  return useDateFormat(date, 'YYYY-MM-DD HH:mm').value
}

const getLevelTagType = (level) => {
  const map = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return map[level] || ''
}

const getStatusTagType = (status) => {
  const map = {
    '已解决': 'success',
    '处理中': 'warning',
    '未处理': 'danger'
  }
  return map[status] || ''
}

const getTimelineItemType = (level) => {
  const map = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return map[level] || ''
}

const getTimelineItemColor = (level) => {
  const map = {
    '高': '#f56c6c',
    '中': '#e6a23c',
    '低': '#909399'
  }
  return map[level] || ''
}

const formatDetails = (details) => {
  try {
    if (typeof details === 'string') {
      return details
    }
    return JSON.stringify(details, null, 2)
  } catch {
    return '无法解析详情内容'
  }
}

const handleProcess = async (record) => {
  try {
    await processWarningRecord(record.id)
    emit('refresh')
  } catch (error) {
    console.error('处理预警记录失败:', error)
  }
}

const fetchData = async () => {
  try {
    const res = await getStudentWarningRecords(props.studentId)
    warningRecords.value = res.data
  } catch (error) {
    console.error('获取学生预警记录失败:', error)
    warningRecords.value = []
  }
}

watch(() => props.studentId, fetchData, { immediate: true })
</script>

<style scoped>
.student-warning-records {
  padding: 10px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .record-type {
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
}

.record-content {
  margin-bottom: 10px;

  p {
    margin: 0;
    line-height: 1.6;
  }

  .record-details {
    margin-top: 10px;

    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      font-family: inherit;
      margin: 0;
    }
  }
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);

  .record-actions {
    margin-left: auto;
  }
}
</style>
