<template>
  <div class="student-academic-info">
    <div class="academic-overview">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in overviewItems" :key="item.label">
          <div class="overview-item">
            <div class="overview-label">{{ item.label }}</div>
            <div class="overview-value" :class="item.class">{{ item.value }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="academic-details">
      <el-tabs type="border-card">
        <el-tab-pane label="课程成绩">
          <el-table :data="courseScores" border style="width: 100%">
            <el-table-column prop="courseName" label="课程名称" width="180" />
            <el-table-column prop="credit" label="学分" width="80" />
            <el-table-column prop="score" label="成绩" width="100">
              <template #default="{row}">
                <span :class="getScoreClass(row.score)">{{ row.score }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="ranking" label="班级排名" width="100">
              <template #default="{row}">
                {{ row.ranking ? `${row.ranking}/${academicInfo.classSize}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="teacher" label="任课教师" width="120" />
            <el-table-column prop="semester" label="学期" width="120" />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="考勤记录">
          <el-table :data="attendanceRecords" border style="width: 100%">
            <el-table-column prop="courseName" label="课程名称" width="150" />
            <el-table-column prop="date" label="日期" width="120">
              <template #default="{row}">
                {{ formatDate(row.date) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{row}">
                <el-tag :type="getAttendanceTagType(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="teacher" label="记录人" width="120" />
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useDateFormat } from '@vueuse/core'
import { getStudentAcademicInfo } from '@/api/student'

const props = defineProps({
  studentId: {
    type: [String, Number],
    required: true
  }
})

const academicInfo = ref({})

const overviewItems = computed(() => [
  { label: '平均成绩', value: academicInfo.value.avgScore || '-', class: 'score-value' },
  { label: 'GPA', value: academicInfo.value.gpa || '-', class: 'score-value' },
  { label: '出勤率', value: academicInfo.value.attendanceRate ? `${academicInfo.value.attendanceRate}%` : '-',
    class: academicInfo.value.attendanceRate < 60 ? 'low-attendance' :
      academicInfo.value.attendanceRate < 80 ? 'medium-attendance' : 'high-attendance' },
  { label: '挂科数', value: academicInfo.value.failedCourses || '0',
    class: academicInfo.value.failedCourses > 0 ? 'failed-courses' : '' }
])

const courseScores = computed(() => academicInfo.value.courseScores || [])
const attendanceRecords = computed(() => academicInfo.value.attendanceRecords || [])

const formatDate = (date) => {
  if (!date) return '-'
  return useDateFormat(date, 'YYYY-MM-DD').value
}

const getScoreClass = (score) => {
  if (!score) return ''
  if (score < 60) return 'failed-score'
  if (score < 70) return 'low-score'
  if (score < 85) return 'medium-score'
  return 'high-score'
}

const getAttendanceTagType = (status) => {
  const map = {
    '出勤': 'success',
    '迟到': 'warning',
    '早退': 'warning',
    '缺勤': 'danger',
    '请假': 'info'
  }
  return map[status] || ''
}

const fetchData = async () => {
  try {
    const res = await getStudentAcademicInfo(props.studentId)
    academicInfo.value = res.data
  } catch (error) {
    console.error('获取学生学业信息失败:', error)
    academicInfo.value = {}
  }
}

watch(() => props.studentId, fetchData, { immediate: true })
</script>

<style scoped>
.student-academic-info {
  padding: 10px;
}

.overview-item {
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
  padding: 15px;
  text-align: center;

  .overview-label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: 8px;
  }

  .overview-value {
    font-size: 20px;
    font-weight: bold;

    &.score-value {
      color: var(--el-color-primary);
    }

    &.low-attendance {
      color: var(--el-color-danger);
    }

    &.medium-attendance {
      color: var(--el-color-warning);
    }

    &.high-attendance {
      color: var(--el-color-success);
    }

    &.failed-courses {
      color: var(--el-color-danger);
    }
  }
}

.academic-details {
  margin-top: 20px;
}

.failed-score {
  color: var(--el-color-danger);
  font-weight: bold;
}

.low-score {
  color: var(--el-color-warning);
}

.medium-score {
  color: var(--el-color-primary);
}

.high-score {
  color: var(--el-color-success);
  font-weight: bold;
}
</style>
