<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span class="card-title">就业与发展</span>
        <el-tag type="success" effect="dark" size="small">
          最新数据
        </el-tag>
      </div>
    </template>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="毕业去向" name="destination" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <pie-chart
              title="毕业去向分布"
              :data="employmentData.destination"
              :loading="loading"
              height="400px"
              :show-legend="true"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <bar-chart
              title="各专业就业率与平均薪资"
              :data="formatMajorData(employmentData.byMajor)"
              :loading="loading"
              height="400px"
              :is-multi-series="true"
              :show-percent="false"
              :color-scheme="['#409EFF', '#67C23A']"
              @chart-click="handleChartClick"
            />
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-table
              :data="employmentData.topEmployers"
              border
              style="width: 100%"
              v-loading="loading"
            >
              <el-table-column
                prop="rank"
                label="排名"
                width="80"
              />
              <el-table-column
                prop="company"
                label="用人单位"
              />
              <el-table-column
                prop="count"
                label="录用人数"
                width="120"
                sortable
              />
              <el-table-column
                prop="industry"
                label="行业"
                width="120"
              />
              <el-table-column
                prop="avgSalary"
                label="平均薪资"
                width="150"
                sortable
              >
                <template #default="{row}">
                  {{ row.avgSalary.toLocaleString() }}元
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="120"
                fixed="right"
              >
                <template #default="{row}">
                  <el-button
                    size="small"
                    @click="showEmployerDetail(row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="薪资分布" name="salary" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <pie-chart
              title="薪资区间分布"
              :data="salaryData.distribution"
              :loading="loading"
              height="400px"
              :show-legend="true"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <div class="metrics-container">
              <div class="metric-card">
                <h3>用人单位满意度</h3>
                <el-rate
                  v-model="salaryData.satisfaction.rate"
                  disabled
                  show-score
                  :max="50"
                  :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                  score-template="{value}%"
                  class="satisfaction-rate"
                />
                <div class="trend">
                  <span>同比</span>
                  <el-tag
                    :type="salaryData.satisfaction.trend >= 0 ? 'success' : 'danger'"
                    size="small"
                  >
                    <el-icon :size="12">
                      <CaretTop v-if="salaryData.satisfaction.trend >= 0" />
                      <CaretBottom v-else />
                    </el-icon>
                    {{ Math.abs(salaryData.satisfaction.trend) }}%
                  </el-tag>
                </div>
              </div>
              <div class="metric-card">
                <h3>平均薪资</h3>
                <div class="salary-value">
                  {{ salaryData.avgSalary.toLocaleString() }}<span class="unit">元</span>
                </div>
                <div class="trend">
                  <span>同比</span>
                  <el-tag
                    :type="salaryData.salaryTrend >= 0 ? 'success' : 'danger'"
                    size="small"
                  >
                    <el-icon :size="12">
                      <CaretTop v-if="salaryData.salaryTrend >= 0" />
                      <CaretBottom v-else />
                    </el-icon>
                    {{ Math.abs(salaryData.salaryTrend) }}%
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <line-chart
              title="近五年薪资趋势"
              :data="salaryData.trend"
              :loading="loading"
              :isMultiLine="true"
              height="350px"
              :color-scheme="['#E6A23C']"
              :area-style="true"
            />
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="校友发展" name="alumni" lazy>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-timeline>
              <el-timeline-item
                v-for="(path, index) in alumniData.careerPaths"
                :key="index"
                :timestamp="path.year"
                placement="top"
                :type="getTimelineType(index)"
                :color="getTimelineColor(index)"
              >
                <el-card shadow="hover">
                  <div class="alumni-path">
                    <div class="path-header">
                      <h3>{{ path.year }}届毕业生发展路径</h3>
                      <el-tag :type="getEmploymentTag(path.employmentRate)">
                        就业率: {{ path.employmentRate }}%
                      </el-tag>
                    </div>
                    <div class="path-content">
                      <div class="positions">
                        <h4>典型职位</h4>
                        <div class="position-tags">
                          <el-tag
                            v-for="(position, pIndex) in path.positions"
                            :key="pIndex"
                            :type="getPositionTagType(pIndex)"
                            class="position-tag"
                          >
                            {{ position }}
                          </el-tag>
                        </div>
                      </div>
                      <div class="companies">
                        <h4>代表企业</h4>
                        <div class="company-list">
                          <el-tag
                            v-for="(company, cIndex) in path.companies"
                            :key="cIndex"
                            type="info"
                            class="company-tag"
                          >
                            {{ company }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <!-- 用人单位详情弹窗 -->
    <el-dialog
      v-model="employerDialogVisible"
      :title="`用人单位详情 - ${currentEmployer.company}`"
      width="50%"
    >
      <el-descriptions border>
        <el-descriptions-item label="行业">{{ currentEmployer.industry }}</el-descriptions-item>
        <el-descriptions-item label="企业性质">{{ currentEmployer.type }}</el-descriptions-item>
        <el-descriptions-item label="录用人数">{{ currentEmployer.count }}人</el-descriptions-item>
        <el-descriptions-item label="平均薪资">{{ currentEmployer.avgSalary.toLocaleString() }}元</el-descriptions-item>
        <el-descriptions-item label="满意度评分">{{ currentEmployer.satisfaction }}%</el-descriptions-item>
        <el-descriptions-item label="主要岗位" span="3">
          <el-tag
            v-for="(position, index) in currentEmployer.positions"
            :key="index"
            type="info"
            size="small"
            class="mr-5 mb-5"
          >
            {{ position }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="合作历史" span="3">
          <el-steps :active="currentEmployer.cooperationYears" finish-status="success">
            <el-step
              v-for="year in 5"
              :key="year"
              :title="`${year}年`"
            />
          </el-steps>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="employerDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="contactEmployer">
          联系企业
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import PieChart from './charts/PieChart.vue'
import BarChart from './charts/BarChart.vue'
import LineChart from './charts/LineChart.vue'

// 状态管理
const loading = ref(false)
const activeTab = ref('destination')
const employerDialogVisible = ref(false)
const currentEmployer = ref({})

// 模拟数据 - 毕业去向
const employmentData = ref({
  destination: [
    { name: '就业', value: 68 },
    { name: '国内升学', value: 18 },
    { name: '出国深造', value: 8 },
    { name: '自主创业', value: 4 },
    { name: '其他', value: 2 }
  ],
  byMajor: [
    { major: '计算机科学与技术', employmentRate: 96, avgSalary: 8500 },
    { major: '软件工程', employmentRate: 95, avgSalary: 8200 },
    { major: '电子信息', employmentRate: 92, avgSalary: 7800 },
    { major: '机械设计', employmentRate: 88, avgSalary: 6500 },
    { major: '工商管理', employmentRate: 85, avgSalary: 6000 },
    { major: '英语', employmentRate: 82, avgSalary: 5500 }
  ],
  topEmployers: [
    {
      rank: 1,
      company: '华为技术有限公司',
      count: 32,
      industry: '通信技术',
      avgSalary: 12000,
      satisfaction: 92
    },
    // 其他企业数据...
  ]
})

// 模拟数据 - 薪资分布
const salaryData = ref({
  distribution: [
    { name: '5k以下', value: 8 },
    { name: '5k-8k', value: 32 },
    { name: '8k-12k', value: 45 },
    { name: '12k-15k', value: 10 },
    { name: '15k以上', value: 5 }
  ],
  satisfaction: {
    rate: 88,
    trend: 2.5
  },
  avgSalary: 8560,
  salaryTrend: 5.8,
  trend: [
    {
      name: '平均薪资',
      data: [6500, 7200, 7800, 8200, 8560]
    }
  ]
})

// 模拟数据 - 校友发展
const alumniData = ref({
  careerPaths: [
    {
      year: '2022',
      employmentRate: 95,
      positions: ['前端开发工程师', 'Java开发工程师', '产品经理', '测试工程师'],
      companies: ['腾讯', '阿里巴巴', '字节跳动', '百度']
    },
    // 其他年份数据...
  ]
})

// 格式化专业数据
const formatMajorData = (majors) => {
  return [
    {
      name: '就业率',
      data: majors.map(m => ({
        name: m.major,
        value: m.employmentRate
      }))
    },
    {
      name: '平均薪资',
      data: majors.map(m => ({
        name: m.major,
        value: m.avgSalary / 100
      }))
    }
  ]
}

// 获取时间线类型
const getTimelineType = (index) => {
  const types = ['primary', 'success', 'warning', 'danger', 'info']
  return types[index % types.length]
}

// 获取时间线颜色
const getTimelineColor = (index) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  return colors[index % colors.length]
}

// 获取就业率标签类型
const getEmploymentTag = (rate) => {
  if (rate >= 95) return 'success'
  if (rate >= 90) return 'primary'
  if (rate >= 85) return 'warning'
  return 'danger'
}

// 获取职位标签类型
const getPositionTagType = (index) => {
  const types = ['', 'success', 'warning', 'danger', 'info']
  return types[index % types.length]
}

// 显示用人单位详情
const showEmployerDetail = (row) => {
  currentEmployer.value = {
    ...row,
    type: ['民营企业', '国有企业', '外资企业'][row.rank % 3],
    positions: [
      '软件开发工程师',
      '测试工程师',
      '产品经理',
      'UI设计师'
    ].slice(0, row.rank % 3 + 2),
    cooperationYears: row.rank % 5 + 1
  }
  employerDialogVisible.value = true
}

// 联系企业
const contactEmployer = () => {
  ElMessage.success(`已发送联系请求给 ${currentEmployer.value.company}`)
  employerDialogVisible.value = false
}

// 图表点击事件
const handleChartClick = (params) => {
  console.log('图表点击:', params)
}

// 标签页切换
const handleTabChange = (tab) => {
  console.log('切换标签:', tab.props.name)
}
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}


.mt-20 {
  margin-top: 20px;
}

.metrics-container {
  display: flex;
  flex-direction: column;
  height: 97%;
  gap: 16px;
}

.metric-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f8f8;
}

.satisfaction-rate {
  margin: 15px 0;
}

.salary-value {
  font-size: 28px;
  font-weight: bold;
  color: #E6A23C;
  margin: 10px 0;
}

.salary-value .unit {
  font-size: 16px;
  margin-left: 2px;
  font-weight: normal;
}

.trend {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.trend span {
  margin-right: 5px;
  color: #909399;
  font-size: 14px;
}

.alumni-path {
  padding: 10px;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.path-content {
  display: flex;
  gap: 20px;
}

.path-content > div {
  flex: 1;
}

.position-tags, .company-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.position-tag, .company-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.mr-5 {
  margin-right: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .path-content {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>
