<template>
  <el-card class="box-card mb-20">
    <template #header>
      <div class="card-header">
        <span class="card-title">宿舍与生活</span>
        <el-tag type="info" effect="dark" size="small">
          实时监控
        </el-tag>
      </div>
    </template>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="宿舍管理" name="dorm" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <bar-chart
              title="宿舍卫生评分TOP10"
              :data="dormData.hygieneTop10"
              :loading="loading"
              height="400px"
              :color-scheme="['#67C23A']"
              @chart-click="handleChartClick"
            />
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="repair-card">
              <template #header>
                <div class="table-header">
                  <span>设备报修响应时效</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="showRepairDialog"
                  >
                    报修统计
                  </el-button>
                </div>
              </template>
              <el-table
                :data="formatRepairData(dormData.repairResponse)"
                border
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column
                  prop="type"
                  label="报修类型"
                  width="120"
                />
                <el-table-column
                  prop="time"
                  label="平均响应时间"
                  width="150"
                  sortable
                >
                  <template #default="{row}">
                    {{ row.time }} <span style="color: #909399">小时</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="trend"
                  label="趋势"
                  width="120"
                >
                  <template #default="{row}">
                    <el-tag :type="row.trend >= 0 ? 'danger' : 'success'" size="small">
                      <el-icon :size="12">
                        <CaretTop v-if="row.trend >= 0" />
                        <CaretBottom v-else />
                      </el-icon>
                      {{ Math.abs(row.trend) }}%
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="本月报修"
                  width="120"
                  sortable
                />
                <el-table-column
                  label="操作"
                  width="80"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="showRepairDetail(row.type)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <span>晚归记录分析</span>
                  <el-select
                    v-model="lateReturnRange"
                    size="small"
                    style="width: 120px"
                    @change="handleLateReturnRangeChange"
                  >
                    <el-option label="近7天" value="week" />
                    <el-option label="近30天" value="month" />
                    <el-option label="本学期" value="semester" />
                  </el-select>
                </div>
              </template>
              <line-chart
                :data="formatLateReturnData(dormData.lateReturn)"
                :loading="loading"
                :isMultiLine="true"
                height="300px"
                :color-scheme="['#E6A23C']"
              />
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <span>违规电器使用记录</span>
              </template>
              <el-table
                :data="dormData.electricViolations"
                border
                style="width: 100%"
                v-loading="loading"
              >
                <el-table-column
                  prop="date"
                  label="日期"
                  width="120"
                  sortable
                />
                <el-table-column
                  prop="building"
                  label="楼栋"
                  width="120"
                />
                <el-table-column
                  prop="room"
                  label="房间号"
                  width="120"
                />
                <el-table-column
                  prop="type"
                  label="电器类型"
                  width="150"
                />
                <el-table-column
                  prop="students"
                  label="涉及学生"
                >
                  <template #default="{row}">
                    {{ row.students.join('、') }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="处理状态"
                  width="120"
                >
                  <template #default="{row}">
                    <el-tag :type="row.status === '已处理' ? 'success' : 'danger'">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="120"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="handleViolationDetail(row)"
                    >
                      {{ row.status === '已处理' ? '详情' : '处理' }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="violationPage"
                  v-model:page-size="violationPageSize"
                  :total="dormData.totalViolations"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleViolationSizeChange"
                  @current-change="handleViolationPageChange"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="消费行为" name="consumption" lazy>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="consumption-card">
              <template #header>
                <span>消费统计分析</span>
              </template>
              <div class="consumption-stats">
                <div class="stat-row">
                  <div class="stat-item">
                    <div class="stat-label">日均消费</div>
                    <div class="stat-value">
                      ¥{{ consumptionData.avgDailyConsumption }}
                    </div>
                    <div class="stat-trend">
                      <el-tag :type="consumptionData.consumptionTrend >= 0 ? 'danger' : 'success'" size="small">
                        <el-icon :size="12">
                          <CaretTop v-if="consumptionData.consumptionTrend >= 0" />
                          <CaretBottom v-else />
                        </el-icon>
                        {{ Math.abs(consumptionData.consumptionTrend) }}%
                      </el-tag>
                      同比
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">月均消费</div>
                    <div class="stat-value">
                      ¥{{ consumptionData.avgMonthlyConsumption }}
                    </div>
                  </div>
                </div>
                <el-divider />
                <div class="stat-row">
                  <div class="stat-item">
                    <div class="stat-label">低消费学生</div>
                    <div class="stat-value warning">
                      {{ consumptionData.lowConsumptionStudents }}
                      <span class="unit">人</span>
                    </div>
                    <div class="stat-sub">
                      日均低于 ¥{{ consumptionData.lowConsumptionThreshold }}
                    </div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">高消费学生</div>
                    <div class="stat-value danger">
                      {{ consumptionData.highConsumptionStudents }}
                      <span class="unit">人</span>
                    </div>
                    <div class="stat-sub">
                      日均高于 ¥{{ consumptionData.highConsumptionThreshold }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="consumption-chart">
                <pie-chart
                  title="消费类型分布"
                  :data="consumptionData.typeDistribution"
                  height="250px"
                />
              </div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
            <el-card shadow="never" class="abnormal-card">
              <template #header>
                <div class="table-header">
                  <span>异常消费记录</span>
                  <el-tooltip content="自动检测高频小额转账等异常模式" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-table
                :data="consumptionData.abnormalTransactions"
                border
                style="width: 100%"
                v-loading="loading"
                @sort-change="handleAbnormalSortChange"
              >
                <el-table-column
                  prop="id"
                  label="学号"
                  width="120"
                  sortable
                />
                <el-table-column
                  prop="name"
                  label="姓名"
                  width="100"
                />
                <el-table-column
                  prop="college"
                  label="学院"
                  width="150"
                  :filters="collegeFilters"
                  :filter-method="filterCollege"
                />
                <el-table-column
                  prop="pattern"
                  label="异常模式"
                  width="150"
                >
                  <template #default="{row}">
                    <el-tag :type="getPatternTagType(row.pattern)">
                      {{ row.pattern }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="count"
                  label="异常次数"
                  width="120"
                  sortable
                />
                <el-table-column
                  prop="lastTime"
                  label="最近时间"
                  width="150"
                  sortable
                />
                <el-table-column
                  label="操作"
                  width="120"
                  fixed="right"
                >
                  <template #default="{row}">
                    <el-button
                      size="small"
                      @click="handleAbnormalDetail(row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
<!--              <div class="pagination-container">-->
<!--                <el-pagination-->
<!--                  v-model:current-page="abnormalPage"-->
<!--                  v-model:page-size="abnormalPageSize"-->
<!--                  :total="consumptionData.totalAbnormal"-->
<!--                  :page-sizes="[10, 20, 50]"-->
<!--                  layout="total, sizes, prev, pager, next, jumper"-->
<!--                  @size-change="handleAbnormalSizeChange"-->
<!--                  @current-change="handleAbnormalPageChange"-->
<!--                />-->
<!--              </div>-->
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-card shadow="never">
              <template #header>
                <div class="chart-header">
                  <span>消费趋势分析</span>
                  <el-radio-group
                    v-model="consumptionRange"
                    size="small"
                    @change="handleConsumptionRangeChange"
                  >
                    <el-radio-button label="week">近7天</el-radio-button>
                    <el-radio-button label="month">近30天</el-radio-button>
                    <el-radio-button label="semester">本学期</el-radio-button>
                  </el-radio-group>
                </div>
              </template>
              <line-chart
                :data="consumptionData.trend"
                :loading="loading"
                :isMultiLine="true"
                height="350px"
                :color-scheme="['#409EFF', '#F56C6C']"
                :area-style="true"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>

    <!-- 报修统计弹窗 -->
    <el-dialog
      v-model="repairDialogVisible"
      title="本月报修统计"
      width="70%"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <pie-chart
            title="报修类型分布"
            :data="dormData.repairTypeDistribution"
            height="300px"
          />
        </el-col>
        <el-col :span="12">
          <bar-chart
            title="各楼栋报修数量"
            :data="dormData.repairByBuilding"
            height="300px"
          />
        </el-col>
      </el-row>
      <template #footer>
        <el-button @click="repairDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="exportRepairData">
          导出数据
        </el-button>
      </template>
    </el-dialog>

    <!-- 异常消费详情弹窗 -->
    <el-dialog
      v-model="abnormalDialogVisible"
      :title="`异常消费详情 - ${currentAbnormal.name} (${currentAbnormal.id})`"
      width="60%"
    >
      <el-descriptions border>
        <el-descriptions-item label="学院">{{ currentAbnormal.college }}</el-descriptions-item>
        <el-descriptions-item label="专业">{{ currentAbnormal.major }}</el-descriptions-item>
        <el-descriptions-item label="异常模式">
          <el-tag :type="getPatternTagType(currentAbnormal.pattern)">
            {{ currentAbnormal.pattern }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="异常次数">{{ currentAbnormal.count }}次</el-descriptions-item>
        <el-descriptions-item label="首次出现">{{ currentAbnormal.firstTime }}</el-descriptions-item>
        <el-descriptions-item label="最近一次">{{ currentAbnormal.lastTime }}</el-descriptions-item>
        <el-descriptions-item label="典型交易" span="3">
          <el-table :data="currentAbnormal.transactions" border size="small">
            <el-table-column prop="time" label="时间" width="150" />
            <el-table-column prop="amount" label="金额" width="100">
              <template #default="{row}">
                ¥{{ row.amount }}
              </template>
            </el-table-column>
            <el-table-column prop="location" label="地点" />
            <el-table-column prop="type" label="类型" width="120" />
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="风险等级" span="3">
          <el-progress
            :percentage="currentAbnormal.riskLevel * 25"
            :format="formatRiskLevel"
            :color="getRiskColor(currentAbnormal.riskLevel)"
          />
          <div style="margin-top: 10px">
            <el-tag :type="getRiskTagType(currentAbnormal.riskLevel)" effect="dark">
              {{ getRiskText(currentAbnormal.riskLevel) }}
            </el-tag>
            <span style="margin-left: 10px">{{ currentAbnormal.riskDescription }}</span>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="abnormalDialogVisible = false">关闭</el-button>
        <el-button type="warning" @click="handleWarningStudent">
          发送提醒
        </el-button>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled, CaretTop, CaretBottom } from '@element-plus/icons-vue'
import BarChart from './charts/BarChart.vue'
import LineChart from './charts/LineChart.vue'
import PieChart from './charts/PieChart.vue'

// 状态管理
const loading = ref(false)
const activeTab = ref('dorm')
const lateReturnRange = ref('week')
const consumptionRange = ref('week')
const violationPage = ref(1)
const violationPageSize = ref(10)
const abnormalPage = ref(1)
const abnormalPageSize = ref(10)
const repairDialogVisible = ref(false)
const abnormalDialogVisible = ref(false)
const currentAbnormal = ref({})

// 模拟数据 - 宿舍管理
const dormData = ref({
  hygieneTop10: [
    { name: '3号楼502', value: 98 },
    { name: '5号楼306', value: 97 },
    { name: '1号楼412', value: 96 },
    { name: '4号楼208', value: 95 },
    { name: '2号楼315', value: 94 },
    { name: '6号楼107', value: 93 },
    { name: '3号楼224', value: 92 },
    { name: '5号楼419', value: 91 },
    { name: '1号楼103', value: 90 },
    { name: '4号楼511', value: 89 }
  ],
  repairResponse: {
    electric: { time: 3.2, trend: -0.5, count: 28 },
    plumbing: { time: 4.5, trend: 1.2, count: 15 },
    furniture: { time: 5.1, trend: -2.3, count: 8 },
    network: { time: 2.8, trend: -1.7, count: 12 },
    other: { time: 6.3, trend: 0.8, count: 5 }
  },
  repairTypeDistribution: [
    { name: '电力', value: 28 },
    { name: '管道', value: 15 },
    { name: '网络', value: 12 },
    { name: '家具', value: 8 },
    { name: '其他', value: 5 }
  ],
  repairByBuilding: [
    { name: '1号楼', value: 12 },
    { name: '2号楼', value: 18 },
    { name: '3号楼', value: 22 },
    { name: '4号楼', value: 15 },
    { name: '5号楼', value: 10 },
    { name: '6号楼', value: 8 }
  ],
  lateReturn: {
    week: {
      days: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      counts: [8, 5, 6, 7, 12, 25, 18]
    },
    // 其他时间范围数据...
  },
  electricViolations: generateViolations(10),
  totalViolations: 10
})

// 模拟数据 - 消费行为
const consumptionData = ref({
  avgDailyConsumption: 32.5,
  avgMonthlyConsumption: 975,
  consumptionTrend: 5.2,
  lowConsumptionStudents: 86,
  lowConsumptionThreshold: 15,
  highConsumptionStudents: 45,
  highConsumptionThreshold: 60,
  typeDistribution: [
    { name: '餐饮', value: 58 },
    { name: '超市', value: 22 },
    { name: '水电', value: 12 },
    { name: '其他', value: 8 }
  ],
  abnormalTransactions: generateAbnormalTransactions(10),
  totalAbnormal: 30,
  trend: [
    { name: '日均消费', data: [28, 30, 32, 31, 33, 35, 34] },
    { name: '异常消费', data: [2, 3, 5, 4, 6, 8, 7] }
  ]
})

// 生成模拟违规记录
function generateViolations(count) {
  const buildings = ['1号楼', '2号楼', '3号楼', '4号楼', '5号楼', '6号楼']
  const types = ['电热水壶', '电饭煲', '电磁炉', '电热毯', '其他大功率电器']

  return Array.from({ length: count }, (_, i) => ({
    date: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    building: buildings[i % buildings.length],
    room: `${i % 6 + 1}${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
    type: types[i % types.length],
    students: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, j) => `学生${i * 3 + j + 1}`),
    status: ['已处理', '未处理'][i % 2]
  }))
}

// 生成模拟异常消费记录
function generateAbnormalTransactions(count) {
  const patterns = ['高频小额', '深夜消费', '异地消费', '大额转账']
  const colleges = ['计算机学院', '电子学院', '机械学院', '经管学院', '外语学院']

  return Array.from({ length: count }, (_, i) => ({
    id: `2023${String(i+1).padStart(4, '0')}`,
    name: `学生${i+1}`,
    college: colleges[i % colleges.length],
    pattern: patterns[i % patterns.length],
    count: Math.floor(Math.random() * 10) + 1,
    lastTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`
  }))
}

// 格式化报修数据
const formatRepairData = (repairData) => {
  return Object.entries(repairData).map(([type, data]) => ({
    type: type === 'electric' ? '电力' :
      type === 'plumbing' ? '管道' :
        type === 'furniture' ? '家具' :
          type === 'network' ? '网络' : '其他',
    time: data.time.toFixed(1),
    trend: data.trend,
    count: data.count
  }))
}

// 格式化晚归数据
const formatLateReturnData = (records) => {
  const rangeData = records[lateReturnRange.value]
  return [{
    name: '晚归人数',
    data: rangeData.counts
  }]
}

// 计算学院筛选选项
const collegeFilters = computed(() => {
  const colleges = [...new Set(consumptionData.value.abnormalTransactions.map(item => item.college))]
  return colleges.map(college => ({
    text: college,
    value: college
  }))
})

// 获取异常模式标签类型
const getPatternTagType = (pattern) => {
  const map = {
    '高频小额': 'danger',
    '深夜消费': 'warning',
    '异地消费': 'info',
    '大额转账': 'primary'
  }
  return map[pattern] || ''
}

// 获取风险标签类型
const getRiskTagType = (level) => {
  const map = {
    1: 'success',
    2: 'info',
    3: 'warning',
    4: 'danger'
  }
  return map[level] || ''
}

// 获取风险文本
const getRiskText = (level) => {
  const texts = ['低风险', '中风险', '高风险', '极高风险']
  return texts[level - 1] || '未知'
}

// 获取风险颜色
const getRiskColor = (level) => {
  const colors = ['#67C23A', '#409EFF', '#E6A23C', '#F56C6C']
  return colors[level - 1] || '#909399'
}

// 格式化风险等级
const formatRiskLevel = (percentage) => {
  const level = Math.ceil(percentage / 25)
  return getRiskText(level)
}

// 显示报修统计弹窗
const showRepairDialog = () => {
  repairDialogVisible.value = true
}

// 显示报修详情
const showRepairDetail = (type) => {
  ElMessage.info(`查看${type}报修详情`)
}

// 导出报修数据
const exportRepairData = () => {
  ElMessage.success('报修数据导出成功')
  repairDialogVisible.value = false
}

// 显示异常消费详情
const handleAbnormalDetail = (row) => {
  currentAbnormal.value = {
    ...row,
    major: ['计算机科学与技术', '电子信息', '机械设计', '工商管理', '英语'][row.id % 5],
    firstTime: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')}`,
    riskLevel: Math.floor(Math.random() * 4) + 1,
    riskDescription: [
      '可能为正常消费模式',
      '建议关注消费行为',
      '存在明显异常特征',
      '疑似违规行为需立即核查'
    ][Math.floor(Math.random() * 4)],
    transactions: Array.from({ length: 5 }, (_, i) => ({
      time: `2023-${String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      amount: (Math.floor(Math.random() * 100) + 1) * (row.pattern === '大额转账' ? 100 : 1),
      location: ['学校食堂', '校内超市', '校外商户', '线上支付'][Math.floor(Math.random() * 4)],
      type: ['餐饮', '购物', '转账', '其他'][Math.floor(Math.random() * 4)]
    }))
  }
  abnormalDialogVisible.value = true
}

// 发送提醒给学生
const handleWarningStudent = () => {
  ElMessageBox.confirm(`确定要发送消费提醒给 ${currentAbnormal.value.name} 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('提醒已发送')
    abnormalDialogVisible.value = false
  })
}

// 处理违规记录
const handleViolationDetail = (row) => {
  if (row.status === '未处理') {
    ElMessageBox.confirm(`确定要处理 ${row.building}${row.room} 的违规电器记录吗?`, '处理确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      ElMessage.success('违规记录已处理')
    })
  } else {
    ElMessage.info('查看违规记录详情')
  }
}

// 图表点击事件
const handleChartClick = (params) => {
  console.log('图表点击:', params)
}

// 标签页切换
const handleTabChange = (tab) => {
  console.log('切换标签:', tab.props.name)
}

// 晚归时间范围变化
const handleLateReturnRangeChange = (val) => {
  console.log('晚归时间范围:', val)
}

// 消费时间范围变化
const handleConsumptionRangeChange = (val) => {
  console.log('消费时间范围:', val)
}

// 学院筛选
const filterCollege = (value, row) => {
  return row.college === value
}

// 异常消费排序
const handleAbnormalSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', prop, order)
}

// 分页处理
const handleViolationSizeChange = (val) => {
  console.log('违规每页条数:', val)
}

const handleViolationPageChange = (val) => {
  console.log('违规当前页:', val)
}

const handleAbnormalSizeChange = (val) => {
  console.log('异常每页条数:', val)
}

const handleAbnormalPageChange = (val) => {
  console.log('异常当前页:', val)
}
</script>

<style scoped>
.box-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.mt-20 {
  margin-top: 20px;
}

.repair-card, .consumption-card, .abnormal-card {
  height: 96%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.consumption-stats {
  padding: 15px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  flex: 1;
  padding: 0 10px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.stat-value.warning {
  color: #E6A23C;
}

.stat-value.danger {
  color: #F56C6C;
}

.stat-trend {
  display: flex;
  align-items: center;
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

.stat-trend .el-tag {
  margin-left: 5px;
}

.stat-sub {
  font-size: 12px;
  color: #909399;
  margin-top: 3px;
}

.unit {
  font-size: 14px;
  margin-left: 2px;
  font-weight: normal;
}

.consumption-chart {
  margin-top: 15px;
  padding: 0 15px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .stat-row {
    flex-direction: column;
  }

  .stat-item {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .el-col {
    margin-bottom: 20px;
  }
}
</style>
