<template>
  <ele-page class="dashboard-page">
    <div class="dashboard-header mb-20">
      <div class="header-background"></div>
      <div class="header-content">
        <div class="title-section">
          <h2 class="main-title">综合数据看板</h2>
          <p class="sub-title">实时监控 · 数据分析 · 智能预警</p>
        </div>

        <div class="header-actions">
          <!-- 时间选择器 -->
          <div class="action-item time-picker">
            <span class="action-label">数据时段</span>
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleTimeChange"
              size="large"
              popper-class="dashboard-time-picker"
            />
          </div>

          <!-- 操作按钮组 -->
          <div class="action-buttons">
            <el-tooltip content="刷新最新数据" placement="bottom">
              <el-button
                type="primary"
                size="large"
                circle
                @click="refreshData"
                :loading="refreshing"
              >
                <el-icon :size="18">
                  <Refresh/>
                </el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip content="导出当前报表" placement="bottom">
              <el-button
                type="success"
                size="large"
                circle
                @click="exportDashboard"
              >
                <el-icon :size="18">
                  <Download/>
                </el-icon>
              </el-button>
            </el-tooltip>

            <el-tooltip content="全屏显示" placement="bottom">
              <el-button
                type="info"
                size="large"
                circle
                @click="toggleFullscreen"
              >
                <el-icon :size="18">
                  <FullScreen/>
                </el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <div class="dashboard-container">
      <!-- 核心指标 -->
      <el-row :gutter="8" class="mb-20">
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <stat-card
            title="在校生人数"
            :value="coreData.studentCount"
            icon="user"
            color="#409EFF"
            :trend="coreData.studentTrend"
            unit="人"
            :precision="0"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <stat-card
            title="当月违纪率"
            :value="coreData.violationRate"
            icon="warning"
            color="#F56C6C"
            :trend="coreData.violationTrend"
            unit="%"
            :precision="1"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <stat-card
            title="心理预警人数"
            :value="coreData.mentalWarning"
            icon="phone"
            color="#E6A23C"
            :trend="coreData.mentalTrend"
            unit="人"
            :precision="0"
          />
        </el-col>
        <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
          <stat-card
            title="学业预警人数"
            :value="coreData.academicWarning"
            icon="notebook"
            color="#67C23A"
            :trend="coreData.academicTrend"
            unit="人"
            :precision="0"
          />
        </el-col>
      </el-row>

      <!--      <map-card/>-->
      <!--      生源地分布热力图-->
      <map-chart class="mb-20"/>

      <!-- 基础信息 -->
      <base-info
        :data="baseData"
        :loading="loading"
        class="mb-20"
      />
      <!-- 学业表现 -->
      <academic-performance
        :data="academicData"
        :loading="loading"
        class="mb-20"
      />

      <!-- 行为与奖惩 -->
      <behavior-rewards
        :data="behaviorData"
        :loading="loading"
        class="mb-20"
      />

      <!-- 心理健康与资助 -->
      <mental-support
        v-if="showMentalSupport"
        :data="mentalData"
        :loading="loading"
        class="mb-20"
      />

      <!-- 就业与发展 -->
      <employment-development
        v-if="showEmploymentDev"
        :data="employmentData"
        :loading="loading"
        class="mb-20"
      />

      <!-- 宿舍与生活 -->
      <dormitory-life
        v-if="showDormitoryLife"
        :data="dormitoryData"
        :loading="loading"
        class="mb-20"
      />

      <!-- 综合预警 -->
      <warning-board
        v-if="showWarningBoard"
        :data="warningData"
        :loading="loading"
      />
    </div>

    <!-- 全屏加载 -->
    <el-dialog
      v-model="fullscreenLoading"
      title="数据加载中"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-progress :percentage="loadProgress"/>
      <span class="loading-text">{{ loadingText }}</span>
    </el-dialog>
  </ele-page>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {
  getCoreData,
  getBaseData,
  getAcademicData,
  getBehaviorData,
  getMentalData,
  getEmploymentData,
  getDormitoryData,
  getWarningData,
} from './api/dashboard';
import StatCard from './components/StatCard.vue';
import BaseInfo from './components/BaseInfo.vue';
import AcademicPerformance from './components/AcademicPerformance.vue';
import BehaviorRewards from './components/BehaviorRewards.vue';
import MentalSupport from './components/MentalSupport.vue';
import EmploymentDevelopment from './components/EmploymentDevelopment.vue';
import DormitoryLife from './components/DormitoryLife.vue';
import WarningBoard from './components/WarningBoard.vue';
import MapCard from '@/views/personInfo/st/statistics/components/map-card.vue';
import MapChart from '@/views/personInfo/dashboard/components/charts/MapChart.vue';

// 状态管理
const loading = ref(false);
const refreshing = ref(false);
const fullscreenLoading = ref(false);
const loadProgress = ref(0);
const loadingText = ref('正在加载数据...');
const timeRange = ref([
  new Date(new Date().getFullYear(), 0, 1),
  new Date(),
]);

// 模块显示控制 (可根据权限动态控制)
const showMentalSupport = ref(true);
const showEmploymentDev = ref(true);
const showDormitoryLife = ref(true);
const showWarningBoard = ref(true);

// 数据状态
const coreData = ref({});
const baseData = ref({});
const academicData = ref({});
const behaviorData = ref({});
const mentalData = ref({});
const employmentData = ref({});
const dormitoryData = ref({});
const warningData = ref([]);

// 新增全屏切换方法
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      ElMessage.error(`全屏模式错误: ${err.message}`);
    });
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};
// 加载数据
const loadData = async () => {
  loading.value = true;
  fullscreenLoading.value = true;
  loadProgress.value = 0;
  loadingText.value = '正在加载核心指标数据...';

  try {
    // 分步加载数据
    const loadSteps = [
      {
        name: '核心指标数据',
        loader: getCoreData,
        target: coreData,
        progress: 15,
      },
      {
        name: '基础信息数据',
        loader: getBaseData,
        target: baseData,
        progress: 25,
      },
      {
        name: '学业表现数据',
        loader: getAcademicData,
        target: academicData,
        progress: 35,
      },
      {
        name: '行为与奖惩数据',
        loader: getBehaviorData,
        target: behaviorData,
        progress: 45,
      },
      {
        name: '心理健康数据',
        loader: getMentalData,
        target: mentalData,
        progress: 55,
      },
      {
        name: '就业发展数据',
        loader: getEmploymentData,
        target: employmentData,
        progress: 65,
      },
      {
        name: '宿舍生活数据',
        loader: getDormitoryData,
        target: dormitoryData,
        progress: 75,
      },
      {
        name: '综合预警数据',
        loader: getWarningData,
        target: warningData,
        progress: 90,
      },
    ];

    for (const step of loadSteps) {
      loadingText.value = `正在加载${step.name}...`;
      const res = await step.loader();
      step.target.value = res;
      loadProgress.value = step.progress;
    }

    loadingText.value = '数据加载完成';
    await new Promise(resolve => setTimeout(resolve, 500));

  } catch (error) {
    ElMessage.error(`数据加载失败: ${error.message}`);
  } finally {
    loading.value = false;
    fullscreenLoading.value = false;
  }
};

// 刷新数据
const refreshData = async () => {
  refreshing.value = true;
  await loadData();
  refreshing.value = false;
  ElMessage.success('数据刷新成功');
};

// 导出报表
const exportDashboard = () => {
  ElMessageBox.confirm('确定要导出当前看板数据吗?', '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    ElMessage.success('导出报表成功');
    // 实际项目中这里调用导出API
  }).catch(() => {});
};

// 时间范围变化处理
const handleTimeChange = (val) => {
  timeRange.value = val;
  loadData();
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.dashboard-page {
  padding: 0;
}

/* 顶部区域样式 - 优化后 */
.dashboard-header {
  position: relative;
  padding: 20px 30px;
  margin-bottom: 10px;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1976d2 0%, #2196f3 100%);
    z-index: 0;
    opacity: 0.9;
  }

  .header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .title-section {
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .main-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 6px 0;
      letter-spacing: 1px;
    }

    .sub-title {
      font-size: 14px;
      margin: 0;
      opacity: 0.9;
      font-weight: 300;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .action-item {
      display: flex;
      align-items: center;
      gap: 10px;

      .action-label {
        color: #fff;
        font-size: 14px;
        white-space: nowrap;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;

      .el-button {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: #fff;
        transition: all 0.3s;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  /* 时间选择器样式覆盖 */
  :deep(.el-range-editor.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .el-range-input {
      color: #333;
      background: transparent;
    }

    .el-range-separator {
      color: #666;
    }
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .dashboard-header {
    padding: 18px 20px;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .header-actions {
      width: 100%;
      flex-wrap: wrap;

      .action-item.time-picker {
        width: 100%;
        margin-bottom: 12px;

        .action-label {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    .title-section {
      .main-title {
        font-size: 20px;
      }

      .sub-title {
        font-size: 13px;
      }
    }

    .header-actions {
      gap: 12px;

      .action-buttons {
        .el-button {
          padding: 10px;
        }
      }
    }
  }
}

/* 时间选择器下拉菜单样式 */
:deep(.dashboard-time-picker) {
  .el-picker-panel__sidebar {
    background: #f5f7fa;
  }

  .el-date-table th {
    color: #409EFF;
  }

  .el-date-table td.current:not(.disabled) span {
    background-color: #409EFF;
    color: #fff;
  }
}

.dashboard-container {
  padding: 0;
}

.mb-20 {
  margin-bottom: 12px !important;
}

.loading-text {
  display: block;
  margin-top: 10px;
  text-align: center;
  color: var(--el-text-color-secondary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
