import {
  getCoreData,
  getBaseData,
  getAcademicData,
  getBehaviorData,
  getMentalData,
  getEmploymentData,
  getDormitoryData,
  getWarningData
} from '../api/dashboard'

const state = {
  coreData: {},
  baseData: {},
  academicData: {},
  behaviorData: {},
  mentalData: {},
  employmentData: {},
  dormitoryData: {},
  warningData: []
}

const mutations = {
  SET_CORE_DATA: (state, data) => {
    state.coreData = data
  },
  SET_BASE_DATA: (state, data) => {
    state.baseData = data
  },
  SET_ACADEMIC_DATA: (state, data) => {
    state.academicData = data
  },
  SET_BEHAVIOR_DATA: (state, data) => {
    state.behaviorData = data
  },
  SET_MENTAL_DATA: (state, data) => {
    state.mentalData = data
  },
  SET_EMPLOYMENT_DATA: (state, data) => {
    state.employmentData = data
  },
  SET_DORMITORY_DATA: (state, data) => {
    state.dormitoryData = data
  },
  SET_WARNING_DATA: (state, data) => {
    state.warningData = data
  }
}

const actions = {
  async getCoreData({ commit }) {
    try {
      const data = await getCoreData()
      commit('SET_CORE_DATA', data)
      return data
    } catch (error) {
      console.error('获取核心数据失败:', error)
      throw error
    }
  },

  async getBaseData({ commit }) {
    try {
      const data = await getBaseData()
      commit('SET_BASE_DATA', data)
      return data
    } catch (error) {
      console.error('获取基础数据失败:', error)
      throw error
    }
  },

  async getAcademicData({ commit }) {
    try {
      const data = await getAcademicData()
      commit('SET_ACADEMIC_DATA', data)
      return data
    } catch (error) {
      console.error('获取学业数据失败:', error)
      throw error
    }
  },

  async getBehaviorData({ commit }) {
    try {
      const data = await getBehaviorData()
      commit('SET_BEHAVIOR_DATA', data)
      return data
    } catch (error) {
      console.error('获取行为数据失败:', error)
      throw error
    }
  },

  async getMentalData({ commit }) {
    try {
      const data = await getMentalData()
      commit('SET_MENTAL_DATA', data)
      return data
    } catch (error) {
      console.error('获取心理数据失败:', error)
      throw error
    }
  },

  async getEmploymentData({ commit }) {
    try {
      const data = await getEmploymentData()
      commit('SET_EMPLOYMENT_DATA', data)
      return data
    } catch (error) {
      console.error('获取就业数据失败:', error)
      throw error
    }
  },

  async getDormitoryData({ commit }) {
    try {
      const data = await getDormitoryData()
      commit('SET_DORMITORY_DATA', data)
      return data
    } catch (error) {
      console.error('获取宿舍数据失败:', error)
      throw error
    }
  },

  async getWarningData({ commit }) {
    try {
      const data = await getWarningData()
      commit('SET_WARNING_DATA', data)
      return data
    } catch (error) {
      console.error('获取预警数据失败:', error)
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
