import request from '@/utils/request'

// 模拟数据 - 核心指标
const mockCoreData = {
  studentCount: 12568,
  studentTrend: 2.5,
  violationRate: 3.2,
  violationTrend: -1.2,
  mentalWarning: 156,
  mentalTrend: 0.8,
  academicWarning: 342,
  academicTrend: -0.5
}

// 模拟数据 - 基础信息
const mockBaseData = {
  genderDistribution: [
    { name: "男", value: 6542 },
    { name: "女", value: 6003 },
    { name: "其他", value: 23 }
  ],
  gradeDistribution: [
    { name: "大一", value: 3256 },
    { name: "大二", value: 3120 },
    { name: "大三", value: 2987 },
    { name: "大四", value: 3205 }
  ],
  departmentDistribution: [
    { name: "计算机学院", value: 2568 },
    { name: "经济学院", value: 1987 },
    { name: "文学院", value: 1562 },
    { name: "法学院", value: 1320 },
    { name: "医学院", value: 1875 },
    { name: "艺术学院", value: 1025 },
    { name: "其他", value: 2231 }
  ],
  originDistribution: {
    "北京": 1256,
    "上海": 987,
    "广东": 1568,
    "江苏": 1325,
    "浙江": 1187,
    "四川": 987,
    "湖北": 856,
    "陕西": 765,
    "山东": 1324,
    "河南": 1568,
    "其他": 1745
  },
  ethnicDistribution: [
    { name: "汉族", value: 11568 },
    { name: "少数民族", value: 1000 }
  ],
  internationalStudents: 156,
  urbanRuralRatio: [
    { name: "城镇", value: 8562 },
    { name: "农村", value: 4006 }
  ]
}

// 模拟数据 - 学业表现
const mockAcademicData = {
  departmentScores: [
    { name: "计算机学院", value: 82.5 },
    { name: "经济学院", value: 78.2 },
    { name: "文学院", value: 85.6 },
    { name: "法学院", value: 79.8 },
    { name: "医学院", value: 83.1 },
    { name: "艺术学院", value: 81.7 }
  ],
  failCourseTop10: [
    { name: "高等数学(上)", value: 18.5 },
    { name: "大学物理", value: 16.2 },
    { name: "数据结构", value: 15.8 },
    { name: "微观经济学", value: 14.6 },
    { name: "有机化学", value: 13.9 },
    { name: "概率统计", value: 12.5 },
    { name: "电路原理", value: 11.8 },
    { name: "算法分析", value: 10.6 },
    { name: "生物化学", value: 9.8 },
    { name: "计量经济学", value: 8.9 }
  ],
  gpaDistribution: [
    { range: "4.0-3.5", count: 1256, percentage: "10.0%" },
    { range: "3.5-3.0", count: 3768, percentage: "30.0%" },
    { range: "3.0-2.5", count: 5024, percentage: "40.0%" },
    { range: "2.5-2.0", count: 1884, percentage: "15.0%" },
    { range: "<2.0", count: 628, percentage: "5.0%" }
  ],
  academicWarningLevels: [
    { name: "红色预警", value: 56 },
    { name: "橙色预警", value: 128 },
    { name: "黄色预警", value: 158 }
  ],
  warningConversionTrend: {
    months: ["1月", "2月", "3月", "4月", "5月", "6月"],
    data: [
      { name: "解除预警", data: [12, 15, 18, 22, 25, 30] },
      { name: "新增预警", data: [25, 20, 22, 18, 15, 12] }
    ]
  },
  warningStudents: [
    {
      id: "20210001",
      name: "张三",
      college: "计算机学院",
      major: "计算机科学与技术",
      warningLevel: "红色预警",
      warningReason: "累计挂科5门，GPA1.8"
    },
    {
      id: "20210032",
      name: "李四",
      college: "经济学院",
      major: "金融学",
      warningLevel: "橙色预警",
      warningReason: "连续2学期GPA低于2.0"
    },
    {
      id: "20210125",
      name: "王五",
      college: "医学院",
      major: "临床医学",
      warningLevel: "黄色预警",
      warningReason: "本学期3门课程不及格"
    }
  ]
}

// 模拟数据 - 行为与奖惩
const mockBehaviorData = {
  attendanceRate: {
    months: ["9月", "10月", "11月", "12月", "1月", "2月"],
    data: [95.2, 94.8, 93.5, 92.1, 91.8, 95.6]
  },
  frequentAbsence: [
    {
      id: "20210045",
      name: "周八",
      college: "艺术学院",
      absenceCount: 15,
      courses: ["艺术史", "设计基础"]
    },
    {
      id: "20210112",
      name: "吴九",
      college: "经济学院",
      absenceCount: 12,
      courses: ["微观经济学", "统计学"]
    }
  ],
  scholarshipDistribution: [
    { name: "国家奖学金", count: 25 },
    { name: "校级一等奖", count: 120 },
    { name: "校级二等奖", count: 240 },
    { name: "校级三等奖", count: 480 },
    { name: "专项奖学金", count: 156 }
  ],
  violationRecords: [
    { name: "考试作弊", count: 12 },
    { name: "旷课", count: 56 },
    { name: "宿舍违纪", count: 28 },
    { name: "打架斗殴", count: 5 },
    { name: "其他", count: 15 }
  ],
  dormConflictAnalysis: {
    time: ["8-10点", "10-12点", "12-14点", "14-16点", "16-18点", "18-20点", "20-22点", "22-24点"],
    data: [5, 8, 3, 2, 6, 15, 22, 18]
  }
}

// 模拟数据 - 心理健康与资助
const mockMentalData = {
  counselingRecords: [
    { name: "学业压力", value: 156 },
    { name: "人际关系", value: 98 },
    { name: "情感问题", value: 75 },
    { name: "家庭问题", value: 42 },
    { name: "就业焦虑", value: 68 },
    { name: "其他", value: 35 }
  ],
  highRiskStudents: [
    {
      id: "20210078",
      name: "孙十一",
      college: "文学院",
      riskLevel: "高危",
      intervention: "已介入",
      status: "好转"
    },
    {
      id: "20210156",
      name: "刘十二",
      college: "医学院",
      riskLevel: "中危",
      intervention: "已介入",
      status: "稳定"
    }
  ],
  interventionSuccessRate: 82.5,
  financialAid: {
    povertyStudents: 1256,
    aidCoverage: 98.2,
    workStudyPositions: {
      available: 256,
      applied: 312
    }
  },
  loanStatus: {
    total: 568,
    overdue: 23,
    overdueRate: 4.05
  }
}

// 模拟数据 - 就业与发展
const mockEmploymentData = {
  graduationDestination: [
    { name: "就业", value: 68.5 },
    { name: "国内升学", value: 20.3 },
    { name: "出国深造", value: 8.2 },
    { name: "自主创业", value: 2.1 },
    { name: "其他", value: 0.9 }
  ],
  employmentByMajor: [
    {
      major: "计算机科学与技术",
      employmentRate: 95.6,
      avgSalary: 12500
    },
    {
      major: "金融学",
      employmentRate: 92.3,
      avgSalary: 9800
    }
  ],
  salaryDistribution: [
    { range: "5000以下", percentage: 5.2 },
    { range: "5000-8000", percentage: 22.6 },
    { range: "8000-10000", percentage: 31.8 },
    { range: "10000-15000", percentage: 28.5 },
    { range: "15000以上", percentage: 11.9 }
  ],
  employerSatisfaction: 92.5,
  alumniCareerPaths: [
    {
      year: "毕业1年",
      positions: ["初级工程师", "助理", "管培生"]
    },
    {
      year: "毕业3年",
      positions: ["工程师", "专员", "主管"]
    }
  ]
}

// 模拟数据 - 宿舍与生活
const mockDormitoryData = {
  dormitoryHygiene: [
    { building: "1号楼", score: 92.5 },
    { building: "2号楼", score: 88.7 },
    { building: "3号楼", score: 85.2 },
    { building: "4号楼", score: 90.1 },
    { building: "5号楼", score: 87.6 }
  ],
  repairResponseTime: {
    electric: 3.2,
    plumbing: 4.5,
    furniture: 2.8,
    network: 6.1,
    other: 5.3
  },
  consumptionAnalysis: {
    avgDailyConsumption: 28.5,
    lowConsumptionStudents: 256,
    highConsumptionStudents: 189,
    abnormalTransactions: [
      {
        id: "20210012",
        name: "林十四",
        pattern: "高频小额转账",
        count: 23
      }
    ]
  },
  lateReturnRecords: [
    { date: "2023-05-01", count: 12 },
    { date: "2023-05-02", count: 8 },
    { date: "2023-05-03", count: 15 },
    { date: "2023-05-04", count: 6 },
    { date: "2023-05-05", count: 18 }
  ]
}

// 模拟数据 - 综合预警
const mockWarningData = [
  {
    id: "20210001",
    name: "张三",
    college: "计算机学院",
    warningTypes: ["学业预警", "心理预警"],
    scoreTrend: [78, 72, 65, 58, 52],
    attendanceRate: 62.5,
    lastCounseling: "2023-05-15",
    riskLevel: "高"
  },
  {
    id: "20210045",
    name: "周八",
    college: "艺术学院",
    warningTypes: ["行为预警", "学业预警"],
    scoreTrend: [82, 75, 68, 62, 55],
    attendanceRate: 58.3,
    lastCounseling: "2023-04-20",
    riskLevel: "中"
  }
]

// API接口
export function getCoreData() {
  return Promise.resolve(mockCoreData)
}

export function getBaseData() {
  return Promise.resolve(mockBaseData)
}

export function getAcademicData() {
  return Promise.resolve(mockAcademicData)
}

export function getBehaviorData() {
  return Promise.resolve(mockBehaviorData)
}

export function getMentalData() {
  return Promise.resolve(mockMentalData)
}

export function getEmploymentData() {
  return Promise.resolve(mockEmploymentData)
}

export function getDormitoryData() {
  return Promise.resolve(mockDormitoryData)
}

export function getWarningData() {
  return Promise.resolve(mockWarningData)
}
