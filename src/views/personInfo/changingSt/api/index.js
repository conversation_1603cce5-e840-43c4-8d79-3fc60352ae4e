/**
 * 学籍异动功能
 */
import request from '@/utils/request.js';

/**
 * 分页查询学籍异动数据
 */
export async function getStudentRecordChangePage(params) {
  const res = await request.get('/userInfo/student-record-change/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除学籍异动学生
 */
export async function removesStudentRecordChange(data) {
  const res = await request.post(
    '/userInfo/student-record-change/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询某个学生本班学生互评列表 管理端
 */
export async function getEvaluatePeerReviewRecordPage(params) {
  const res = await request.get('/evaluate/evaluate-peer-review-record/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学院评分 管理端
 */
export async function updateDeptScore(data) {
  const res = await request.post(
    '/evaluate/evaluate-review-score/updateDeptScore',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导入
 */
export async function importData(file, configId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('configId', configId);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/evaluate/evaluate-review-score/dataImport',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}
