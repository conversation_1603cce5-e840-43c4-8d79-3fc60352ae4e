export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      slot: 'name',
      label: '名称',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'agentId',
      label: 'agentId',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'clientId',
      label: 'clientId',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'clientSecret',
      label: 'clientSecret',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'mobileRedirectPath',
      label: 'Mobile 重定向路由',
      type: 'input',
    },
    {
      prop: 'mobileRedirectUrl',
      label: 'Mobile 重定向URL',
      type: 'input',
    },
    {
      prop: 'description',
      label: '备注',
      type: 'input',
    },
    {
      prop: 'debug',
      label: 'debug',
      type: 'switch',
    },
  ]
}
