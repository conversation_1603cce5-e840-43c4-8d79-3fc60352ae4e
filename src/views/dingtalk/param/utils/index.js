export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'key',
      slot: 'key',
      label: 'key',
      type: 'key',
      // props: {
      //   code: 'dingTalkOptionalKey',
      //   filterable: true,
      //   dicQueryParams: {
      //     dictFieldUrl: '/platform/dingtalk/param/optionalKey',
      //     valueField: 'id',
      //     textField: 'text',
      //     valueType: 'Object'
      //   },
      // },
      showFlag: '是',
      selfModifyFlag: '是',
      minWidth: 140,
      formatter: (row) => {
        return row.key ? JSON.stringify(row.key) : ''
      },
      required: true
    },
    {
      prop: 'value',
      label: 'value',
      type: 'input',
    },
    {
      prop: 'enabled',
      label: 'enabled',
      type: 'switch',
    },
    {
      prop: 'description',
      label: '备注',
      type: 'input',
    },
    {
      prop: 'createdAtText',
      slot: 'createdAtText',
      label: 'createdAtText',
      type: 'input',
      minWidth: 110,
      disabled: true,
    },
    {
      prop: 'updatedAtText',
      label: 'updatedAtText',
      type: 'input',
      disabled: true,
    },
  ]
}
