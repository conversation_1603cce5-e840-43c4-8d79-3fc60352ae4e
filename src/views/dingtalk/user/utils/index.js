export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'systemUsername',
      slot: 'systemUsername',
      label: '系统用户',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'dingUserId',
      label: 'dingUserId',
      type: 'input',
    },
    {
      prop: 'dingUsername',
      label: 'dingUsername',
      type: 'input',
    },
    {
      prop: 'createdAtText',
      label: 'createdAtText',
      type: 'input',
      minWidth: 110,
      disabled: true,
    },
    {
      prop: 'updatedAtText',
      label: 'updatedAtText',
      type: 'input',
      disabled: true,
    },
  ]
}
