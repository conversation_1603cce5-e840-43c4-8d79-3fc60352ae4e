<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <!-- 左侧信息区 -->
      <!-- 辅导员信息卡片 -->
      <ele-card :body-style="{padding: '5px!important'}">
        <template #header>
          <el-text text @click="onBack" class="back-btn">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <span>返回</span>
          </el-text>
        </template>
        <template #extra>
          <div v-if="optMark!=='set'">
            <el-button size="small"
                       type="primary"
                       plain
                       @click="onSet">带班设置
            </el-button>
          </div>
        </template>
        <my-card style="margin: 10px 0 10px 10px;" header="" :currentData="currentData"/>
      </ele-card>
      <div class="stats-card" v-if="optMark !== 'set'">
        <div class="stats-header">
          <el-icon>
            <DataAnalysis/>
          </el-icon>
          <span>带班范围统计</span>
        </div>
        <div class="stats-content">
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'count' }"
            @click="handleStatClick('count')"
          >
            <span class="stats-label">学生总数</span>
            <span class="stats-value">{{ total }}</span>
          </div>
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'male' }"
            @click="handleStatClick('male')"
          >
            <span class="stats-label">男生数量</span>
            <span class="stats-value">{{ maleCount }}</span>
          </div>
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'female' }"
            @click="handleStatClick('female')"
          >
            <span class="stats-label">女生数量</span>
            <span class="stats-value">{{ femaleCount }}</span>
          </div>
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'allYx' }"
            @click="handleStatClick('allYx')"
          >
            <span class="stats-label">院系数量</span>
            <span class="stats-value">{{ yxCount }}</span>
          </div>
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'allZy' }"
            @click="handleStatClick('allZy')"
          >
            <span class="stats-label">专业数量</span>
            <span class="stats-value">{{ zyCount }}</span>
          </div>
          <div
            class="stats-item"
            :class="{ 'stats-item-active': activeStat === 'allBj' }"
            @click="handleStatClick('allBj')"
          >
            <span class="stats-label">班级数量</span>
            <span class="stats-value">{{ classCount }}</span>
          </div>
        </div>
      </div>

      <!-- 基础设置 -->
      <div class="settings-section">
        <h3 class="section-title">
          <el-icon class="icon">
            <Collection/>
          </el-icon>
          {{ optMark !== 'set' ? '已设置的带班范围' : '带班范围设置' }}
        </h3>
        <div class="form-item">
          <div class="form-label">培养层次</div>
          <!-- 显示已选内容 -->
          <div class="selected-tags" v-if="optMark!=='set' && formData.pyccmc">
            <el-tag
              v-for="(item, index) in formData.pyccmc.split(',')"
              :key="item"
              :type="activeFilter.pyccid === formData.pyccidArray[index] ? 'primary' : 'info'"
              class="class-tag clickable"
              @click="handleFilterClick('pyccid', formData.pyccidArray[index], index)"
            >
              {{ item }} <span class="count-badge">{{
                formData.pyccidStats[formData.pyccidArray[index]]?.count || 0
              }}</span>
            </el-tag>
          </div>
          <!-- 设置模式 -->
          <dict-data
            v-else
            placeholder="请选择培养层次"
            code="pycc"
            type="checkbox"
            :model-value="formData['pyccidArray']"
            :dicQueryParams="{
                  getValType: 'id',
                  valueField:'id' }"
            @update:modelValue="(value) => updateBasicValue('pyccid', value)"
          />
        </div>

        <div class="form-item">
          <div class="form-label">年级</div>
          <div class="selected-tags" v-if="optMark!=='set' && formData.njmc">
            <el-tag
              v-for="(item, index) in formData.njmc.split(',')"
              :key="item"
              :type="activeFilter.njid === formData.njidArray[index] ? 'primary' : 'info'"
              class="class-tag clickable"
              @click="handleFilterClick('njid', formData.njidArray[index], index)"
            >
              {{ item }} <span class="count-badge">{{
                formData.njidStats[formData.njidArray[index]]?.count || 0
              }}</span>
            </el-tag>
          </div>
          <dict-data
            v-else
            placeholder="请选择年级"
            code="nj"
            type="checkbox"
            :model-value="formData['njidArray']"
            :dicQueryParams="{
                  getValType: 'id',
                  valueField:'id' }"
            @update:modelValue="(value) => updateBasicValue('njid', value)"
          />
        </div>

        <div class="form-item">
          <div class="form-label">院系</div>
          <div class="selected-tags" v-if="optMark!=='set' && formData.xymc">
            <el-tag
              v-for="(item, index) in formData.xymc.split(',')"
              :key="item"
              :type="activeFilter.xyid === formData.xyidArray[index] ? 'primary' : 'info'"
              class="class-tag clickable"
              @click="handleFilterClick('xyid', formData.xyidArray[index], index)"
            >
              {{ item }} <span class="count-badge">{{
                formData.xyidStats[formData.xyidArray[index]]?.count || 0
              }}</span>
            </el-tag>
          </div>
          <dict-data
            v-else
            placeholder="请选择院系"
            code="xymc"
            type="checkbox"
            :model-value="formData['xyidArray']"
            @update:modelValue="(value) => updateValue('xyid', value)"
          />
        </div>

        <div class="form-item" v-if="optMark!=='set'&&formData.zymc">
          <div class="form-label">专业</div>
          <div class="selected-tags">
            <el-tag
              v-for="(item, index) in formData.zymc.split(',')"
              :key="item"
              :type="activeFilter.zyid === formData.zyidArray[index] ? 'primary' : 'info'"
              class="class-tag clickable"
              @click="handleFilterClick('zyid', formData.zyidArray[index], index)"
            >
              {{ item }} <span class="count-badge">{{
                formData.zyidStats[formData.zyidArray[index]]?.count || 0
              }}</span>
            </el-tag>
          </div>
        </div>
        <div class="form-item" v-else-if="optMark==='set'">
          <div class="form-label">专业</div>
          <ele-table-select
            size="small"
            multiple
            clearable
            placeholder="请选择专业"
            value-key="id"
            label-key="name"
            v-model="formData['zyidArray']"
            :table-props="tablePropsZymc"
            :cache-data="cacheDataZymc"
            :maxTagCount="4"
            :maxTagTextLength="10"
            :popper-width="520"
            @clear="onClearZy"
            @removeTag="onRemoveTagZy"
            @select="onSelectZy"
          />
        </div>

        <div class="form-item" v-if="optMark!=='set'&&formData.bjmc">
          <div class="form-label">班级</div>
          <div class="selected-tags">
            <el-tag
              v-for="(item, index) in formData.bjmc.split(',')"
              :key="item"
              :type="activeFilter.bjid === formData.bjidArray[index] ? 'primary' : 'info'"
              class="class-tag clickable"
              @click="handleFilterClick('bjid', formData.bjidArray[index], index)"
            >
              {{ item }} <span class="count-badge">{{
                formData.bjidStats[formData.bjidArray[index]]?.count || 0
              }}</span>
            </el-tag>
          </div>
        </div>
        <div class="form-item" v-else-if="optMark==='set'">
          <div class="form-label">班级</div>
          <ele-table-select
            size="small"
            multiple
            clearable
            placeholder="请选择班级"
            value-key="id"
            label-key="name"
            v-model="formData['bjidArray']"
            :table-props="tablePropsBjmc"
            :cache-data="selectBjData"
            :maxTagCount="4"
            :maxTagTextLength="10"
            :popper-width="580"
            @clear="onClearBj"
            @removeTag="onRemoveTagBj"
            @select="onSelectBj"
          />
        </div>
      </div>
      <template #body>
        <!-- 右侧学生列表区 -->
        <ele-card flex-table header="学生列表"
                  :body-style="{padding: '5px!important',height:(pageHeight)+'px'}">
          <template #extra>
            <div v-if="optMark==='set'">
              <el-button size="small" @click="onBack">取消</el-button>
              <el-button
                size="small"
                type="primary"
                plain
                :loading="loading"
                @click="onSubmit"
              >
                提交
              </el-button>
            </div>
          </template>
          <div class="class-tags-container" v-if="optMark==='set'&&bjMenusData.length > 0">
            <div class="tags-wrapper">
              <el-tag
                v-for="item in bjMenusData"
                :key="item.id"
                :type="current?.id === item.id ? 'primary' : 'info'"
                size="small"
                class="class-tag"
                @click="handleNodeClick(item)"
              >
                {{ item.name }}
                <span class="student-count" v-if="item.studentCount">({{ item.studentCount }})</span>
              </el-tag>
            </div>
          </div>
          <!-- 学生列表 -->
          <xs-data-list
            :key="listKey"
            :listQuery="listQuery"
            :optMark="optMark"
            :xghs="formData['xghArray']"
            @change="handleStudentSelectionChange"
          />
        </ele-card>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {
  ref,
  watch,
  reactive,
  computed,
  unref,
  onMounted,
} from 'vue';
import {ElMessage as EleMessage, ElIcon} from 'element-plus';
import {DataAnalysis, Collection} from '@element-plus/icons-vue';
import {operation, getUserDataScope} from '../api/index';
import XsDataList from '../components/xs-data-list.vue';
import {getCodeZyb, queryPage as queryZyb} from '@/views/base-code/zyb/api';
import {queryPage as queryBjb, getCodeBjb} from '@/views/base-code/bjb/api';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab';
import MyCard from '@/views/personInfo/st/personal/components/my-card.vue';
import {getCountByRoleDuty, getPersonInfo} from '@/views/personInfo/st/api/index.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import IconPark from '@/components/IconPark/index.vue';

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {removePageTab, getRouteTabKey, setPageTabTitle} = usePageTab();

// 获取浏览器参数
const {path, query} = unref(currentRoute);
const pathArray = path.split('/');
const roleId = pathArray[4];
const glzXgh = pathArray[5];
const optMark = ref(query.mark || 'count');
const currentCount = ref(query?.currentCount ? JSON.parse(query.currentCount) : null);

// 表单数据
const formRef = ref(null);
const formData = ref({
  pyccidArray: [],
  njidArray: [],
  xyidArray: [],
  zyidArray: [],
  bjidArray: [],
  xghArray: [],
  // 新增统计字段
  pyccidStats: {},
  njidStats: {},
  xyidStats: {},
  zyidStats: {},
  bjidStats: {},
});
const currentData = ref({});
const tableRef = ref(null);
const splitRef = ref(null);

const checkedStudent = ref([]);
const current = ref(null);
const loading = ref(false);
const isUpdate = ref(false);

// 统计数据
const classCount = ref(0);
const total = ref(0);
const maleCount = ref(0);
const femaleCount = ref(0);
const yxCount = ref(0);
const zyCount = ref(0);
// 班级相关
const selectBjData = ref([]);

// 处理并合并数据
const processDataScope = (dataScope, countData) => {
  // 初始化结果对象
  const result = {
    ...dataScope,
    // 为每个ID字段创建对应的统计信息
    pyccidStats: {},
    njidStats: {},
    xyidStats: {},
    zyidStats: {},
    bjidStats: {},
  };

  // 处理培养层次(pyccid)
  if (dataScope.pyccid) {
    const ids = dataScope.pyccid.split(',');
    const names = dataScope.pyccmc ? dataScope.pyccmc.split(',') : [];
    result.pyccidStats = ids.reduce((acc, id, index) => {
      acc[id] = {
        name: names[index] || '',
        count: countData[id] || 0,
      };
      return acc;
    }, {});
  }

  // 处理年级(njid)
  if (dataScope.njid) {
    const ids = dataScope.njid.split(',');
    const names = dataScope.njmc ? dataScope.njmc.split(',') : [];
    result.njidStats = ids.reduce((acc, id, index) => {
      acc[id] = {
        name: names[index] || '',
        count: countData[id] || 0,
      };
      return acc;
    }, {});
  }

  // 处理院系(xyid)
  if (dataScope.xyid) {
    const ids = dataScope.xyid.split(',');
    const names = dataScope.xymc ? dataScope.xymc.split(',') : [];
    result.xyidStats = ids.reduce((acc, id, index) => {
      acc[id] = {
        name: names[index] || '',
        count: countData[id] || 0,
      };
      return acc;
    }, {});
  }

  // 处理专业(zyid)
  if (dataScope.zyid) {
    const ids = dataScope.zyid.split(',');
    const names = dataScope.zymc ? dataScope.zymc.split(',') : [];
    result.zyidStats = ids.reduce((acc, id, index) => {
      acc[id] = {
        name: names[index] || '',
        count: countData[id] || 0,
      };
      return acc;
    }, {});
  }

  // 处理班级(bjid)
  if (dataScope.bjid) {
    const ids = dataScope.bjid.split(',');
    const names = dataScope.bjmc ? dataScope.bjmc.split(',') : [];
    result.bjidStats = ids.reduce((acc, id, index) => {
      acc[id] = {
        name: names[index] || '',
        count: countData[id] || 0,
        studentCount: countData[id] || 0,
      };
      return acc;
    }, {});
  }
  return result;
};

// 计算属性
const isRangeSelected = computed(() => {
  return formData.value.xyid || formData.value.zyid || formData.value.bjid;
});

const listQuery = ref({});

const bjMenusData = computed(() => {
  if (!selectBjData.value) return [];

  // ✅ 按 sort 升序排列（默认 0 避免 undefined）
  const sorted = [...selectBjData.value].sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));

  return sorted.map((d, index) => {
    return {
      ...d,
      path: '/jsglfw/fdydbgl/dbsz?type=' + d.id,
      studentCount: formData.value.bjidStats?.[d.id]?.count || 0,
      badge: {value: index + 1},
    };
  });
});

// 监听班级数据变化，自动选中第一个
watch(
  () => bjMenusData.value,
  (newVal, oldVal) => {
    if (optMark.value === 'set' && newVal.length > 0) {
      // 只有当数据真正变化时才重新选中
      if (JSON.stringify(newVal) !== JSON.stringify(oldVal) || !current.value) {
        handleNodeClick(newVal[0]);
      }
    }
  },
  {deep: true, immediate: true},
);
const activeStat = ref(null);
const activeFilter = reactive({
  pyccid: null,
  njid: null,
  xyid: null,
  zyid: null,
  bjid: null,
});

// 点击统计数字
const handleStatClick = (type) => {
  // 如果点击的是当前已激活的相同类型，则不执行任何操作
  if (optMark.value === type && activeStat.value === type) return;
  // 更新当前激活的统计类型
  optMark.value = type;
  activeStat.value = type;
  // 重置所有筛选条件
  Object.keys(activeFilter).forEach(key => {
    activeFilter[key] = null;
  });
  // 构建查询参数
  const newQuery = {
    username: glzXgh,
    roleId: roleId,
  };
  // 只有点击"male"或"female"时添加性别条件
  if (type === 'female' || type === 'male') {
    newQuery.xb = type === 'female' ? '女' : '男';
  }
  // 其他类型("count", "allYx", "allZy", "allBj")不添加额外条件，即查询全部
  // 只有当查询参数真正变化时才更新
  if (JSON.stringify(listQuery.value) !== JSON.stringify(newQuery)) {
    listQuery.value = newQuery;
  }
};

// 静默更新列表查询参数
const updateListQuerySilently = () => {
  if (optMark.value === 'set') {
    listQuery.value = {
      pyccid: formData.value.pyccid,
      njid: formData.value.njid,
      xyid: formData.value.xyid,
      zyid: formData.value.zyid,
      bjid: formData.value.bjid,
    };
  } else {
    const newQuery = {
      username: glzXgh,
      roleId: roleId,
    };
    if (optMark.value !== 'count' && optMark.value !== 'allZy') {
      newQuery.xb = optMark.value === 'female' ? '女' : '男';
    }
    // 只更新必要的字段，避免不必要的刷新
    if (JSON.stringify(listQuery.value) !== JSON.stringify(newQuery)) {
      listQuery.value = newQuery;
    }
  }
};

// 点击筛选条件
const handleFilterClick = (filterType, value, index) => {
  activeFilter[filterType] = activeFilter[filterType] === value ? null : value;
  Object.keys(activeFilter).forEach(key => {
    if (key !== filterType) activeFilter[key] = null;
  });
  activeStat.value = null;

  const newQuery = {
    username: glzXgh,
    roleId: roleId,
  };
  // 静默更新查询参数
  Object.assign(newQuery, {[filterType]: activeFilter[filterType] || undefined});
  if (JSON.stringify(listQuery.value) !== JSON.stringify(newQuery)) {
    listQuery.value = newQuery;
  }
};

const onSet = () => {
  push({
    path: '/jsglfw/fdydbgl/dbsz/' + roleId + '/' + glzXgh,
    query: {
      mark: 'set',
    },
  });
};

const updateBasicValue = (prop, value) => {
  formData.value[prop + 'Array'] = value;
  formData.value[prop] = value.join(',');
  // 重置下级参数
  if (prop === 'pyccid') {
    formData.value.njidArray = [];
    formData.value.njid = '';
    formData.value.njmc = '';
    formData.value.xyidArray = [];
    formData.value.xyid = '';
    formData.value.xymc = '';
    formData.value.zyidArray = [];
    formData.value.zyid = '';
    formData.value.zymc = '';
    formData.value.bjidArray = [];
    formData.value.bjid = '';
    formData.value.bjmc = '';
    formData.value.xyidStats = [];
    formData.value.zyidStats = [];
    formData.value.bjidStats = [];
  } else if (prop === 'njid') {
    formData.value.xyidArray = [];
    formData.value.xyid = '';
    formData.value.xymc = '';
    formData.value.zyidArray = [];
    formData.value.zyid = '';
    formData.value.zymc = '';
    formData.value.bjidArray = [];
    formData.value.bjid = '';
    formData.value.bjmc = '';
    formData.value.xyidStats = [];
    formData.value.zyidStats = [];
    formData.value.bjidStats = [];
  }
  updateListQuerySilently();
};

const updateValue = (prop, value) => {
  formData.value[prop + 'Array'] = value;
  formData.value[prop] = formData.value[prop + 'Array'].join();

  if (prop === 'xyid') {
    formData.value.zyidArray = [];
    formData.value.zyid = '';
    formData.value.zymc = '';
    formData.value.bjidArray = [];
    formData.value.bjid = '';
    formData.value.bjmc = '';
    formData.value.xgh = '';
    formData.value.xghArray = [];
    current.value = null;
    formData.value.zyidStats = [];
    formData.value.bjidStats = [];
  } else if (prop === 'zyid') {
    formData.value.bjidArray = [];
    formData.value.bjid = '';
    formData.value.bjmc = '';
    current.value = null;
    formData.value.xgh = '';
    formData.value.xghArray = [];
    formData.value.bjidStats = [];
  }
  updateListQuerySilently();
};

// 处理学生选择变化
const handleStudentSelectionChange = (selectedXghs) => {
  checkedStudent.value = selectedXghs;
  formData.value['xghArray'] = selectedXghs;
  formData.value['xgh'] = selectedXghs.join(',');
  // 更新统计数据
  updateStatistics();
};

// 处理班级节点点击
const handleNodeClick = (row) => {
  if (!row) return;
  // 更新当前班级
  current.value = row;
  // 更新查询参数
  const newQuery = {
    ...listQuery.value,
    bjid: row.id,
  };
  if (JSON.stringify(listQuery.value) !== JSON.stringify(newQuery)) {
    listQuery.value = newQuery;
  }
  // 强制刷新列表以确保选中状态正确恢复
  listKey.value = `${formData.value.pyccid}-${formData.value.njid}-${formData.value.xyid}-${formData.value.zyid}-${row.id}-${optMark.value}`;
};

// 自动选中第一个班级（当在设置模式下）
const autoSelectFirstClass = () => {
  if (optMark.value === 'set' && bjMenusData.value.length > 0 && !current.value) {
    handleNodeClick(bjMenusData.value[0]);
  }
};

// 更新统计数据
const updateStatistics = () => {
  if (!currentCount.value) return;
  // 班级数量
  classCount.value = formData.value.bjidArray?.length ?? 0;
  yxCount.value = formData.value.xyidArray?.length ?? 0;
  zyCount.value = formData.value.zyidArray?.length ?? 0;
  // 学生总数
  if (currentCount.value) {
    total.value = currentCount.value.count;
  } else {
    total.value = 0;
  }
  // 示例性别比例，实际应根据API返回数据
  maleCount.value = currentCount.value.maleCount;
  femaleCount.value = currentCount.value.femaleCount;
};

const onSubmit = () => {
  if (!isRangeSelected.value) {
    EleMessage.error('请至少设置一个带班范围');
    return;
  }
  loading.value = true;
  let newObject = {
    roleId: roleId,
    glzXgh: glzXgh,
    pyccid: formData.value.pyccid,
    njid: formData.value.njid,
    xyid: formData.value.xyid,
    zyid: formData.value.zyid,
    bjid: formData.value.bjid,
    xgh: checkedStudent?.value.join(','),
  };
  operation(newObject).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    onBack();
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/jsglfw/fdydbgl');
};

// 专业相关
const cacheDataZymc = ref([]);
const selectZyData = ref([]);

const tablePropsZymc = reactive({
  datasource: [],
  virtual: true,
  height: 288,
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true,
    },
    {
      prop: 'code',
      label: '代码',
      minWidth: 110,
    },
    {
      prop: 'name',
      label: '专业名称',
      minWidth: 150,
    },
    {
      prop: 'xymc',
      label: '院系',
      minWidth: 150,
    },
  ],
  showOverflowTooltip: true,
  highlightCurrentRow: true,
  toolbar: false,
  pagination: {
    layout: 'total, prev, pager, next, sizes',
    teleported: false,
  },
  rowStyle: {cursor: 'pointer'},
  rowClickChecked: true,
});

const onSelectZy = (item) => {
  selectZyData.value = item;
  formData.value['zyid'] = formData.value['zyidArray'].join();
  updateListQuerySilently();
  queryCodeBjb();
};

const onRemoveTagZy = (value) => {
  let index = selectZyData.value.findIndex((obj) => obj.id === value);
  selectZyData.value.splice(index, 1);
  formData.value['zyid'] = formData.value['zyidArray'].join();
  updateListQuerySilently();
};

const onClearZy = () => {
  selectZyData.value = [];
  selectBjData.value = [];
  formData.value.zyid = null;
  formData.value['bjidArray'] = [];
  formData.value.bjid = null;
  formData.value['zyidArray'] = [];
  updateListQuerySilently();
};

const tablePropsBjmc = reactive({
  datasource: [],
  virtual: true,
  height: 288,
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true,
    },
    {
      prop: 'name',
      label: '班级名称',
      minWidth: 150,
    },
    {
      prop: 'xymc',
      label: '院系',
      minWidth: 150,
    },
    {
      prop: 'zymc',
      label: '专业',
      minWidth: 150,
    },
    {
      prop: 'njmc',
      label: '年级',
      minWidth: 70,
    },
  ],
  showOverflowTooltip: true,
  highlightCurrentRow: true,
  toolbar: false,
  pagination: {
    layout: 'total, prev, pager, next, sizes',
    teleported: false,
  },
  rowStyle: {cursor: 'pointer'},
  rowClickChecked: true,
});

const onSelectBj = (item) => {
  selectBjData.value = item;
  if (formData.value['bjidArray'].length > 0) {
    formData.value['bjid'] = formData.value['bjidArray'].join();
  }
  updateListQuerySilently();
};

const onRemoveTagBj = (value) => {
  let index = selectBjData.value.findIndex((obj) => obj.id === value);
  selectBjData.value.splice(index, 1);
  formData.value['bjid'] = formData.value['bjidArray'].join();
  updateListQuerySilently();
};

const onClearBj = () => {
  selectBjData.value = [];
  formData.value.bjid = null;
  formData.value['bjidArray'] = [];
  updateListQuerySilently();
};

const queryCodeBjb = () => {
  if (formData.value['bjid']) {
    selectBjData.value = [];
    getCodeBjb({
      id: formData.value['bjid'],
      njid: formData.value?.njid ?? '',
    }).then((list) => {
      if (list) {
        selectBjData.value = list;
        // 设置模式下自动选中第一个班级
        if (optMark.value === 'set') {
          autoSelectFirstClass();
        }
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
};

const queryCodeZyb = () => {
  if (formData.value['zyid']) {
    cacheDataZymc.value = [];
    getCodeZyb({id: formData.value['zyid']}).then((list) => {
      if (list.length > 0) {
        cacheDataZymc.value = list;
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
};

const queryUserDataScope = () => {
  loading.value = true;
  Promise.all([
    getUserDataScope(glzXgh, roleId),
    getCountByRoleDuty({username: glzXgh, roleId: roleId}),
  ]).then(([dataScope, countData]) => {
    loading.value = false;
    if (dataScope) {
      // 合并处理数据
      const processedData = processDataScope(dataScope, countData);

      const result = {};
      Object.keys(processedData).forEach((key) => {
        let value = processedData[key];
        if (value) {
          if (
            ['pyccid', 'njid', 'xyid', 'zyid', 'bjid', 'xgh'].includes(key)
          ) {
            result[key + 'Array'] = value.split(',');
          }
        }
      });

      formData.value = Object.assign(processedData, result);
      isUpdate.value = true;
      // 初始化统计数据
      updateStatistics();
      // 更新查询参数
      updateListQuerySilently();
      // 处理班级数据，添加studentCount
      if (formData.value.bjidArray && formData.value.bjidStats) {
        selectBjData.value = formData.value.bjidArray.map(id => ({
          id,
          name: formData.value.bjidStats[id]?.name || '',
          studentCount: formData.value.bjidStats[id]?.count || 0,
        }));
        // 设置模式下自动选中第一个班级
        if (optMark.value === 'set') {
          autoSelectFirstClass();
        }
      }
    } else {
      isUpdate.value = false;
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const queryPersonInfo = () => {
  loading.value = true;
  getPersonInfo('teacher', glzXgh).then((list) => {
    loading.value = false;
    list['avatar'] = null;
    currentData.value = list;
    setPageTabTitle(list?.xm + '带班' + (optMark.value === 'set' ? '设置' : '信息'));
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

// 添加listKey计算属性
const listKey = computed(() => {
  return `${formData.value.pyccid}-${formData.value.njid}-${formData.value.xyid}-${formData.value.zyid}-${current.value?.id}-${optMark.value}`;
});

// 监听路由变化
watch(
  () => currentRoute.value.query.mark,
  (newMark) => {
    optMark.value = newMark || 'count';
    updateListQuerySilently();
  },
);

// 监听班级数据变化，自动选中第一个
watch(
  () => bjMenusData.value,
  (newVal) => {
    if (optMark.value === 'set' && newVal.length > 0 && !current.value) {
      autoSelectFirstClass();
    }
  },
  {deep: true},
);

// 监听器
watch(
  () => formData.value.xyid,
  (xyIds) => {
    if (xyIds) {
      queryCodeZyb();
    } else {
      selectZyData.value = [];
      selectBjData.value = [];
      formData.value.zyid = null;
      formData.value.zymc = null;
      formData.value['zyidArray'] = [];
      formData.value.bjid = null;
      formData.value.bjmc = null;
      formData.value['bjidArray'] = [];
    }
    tablePropsZymc.datasource = ({page, limit, where, orders}) => {
      return queryZyb({...where, ...orders, page, limit, xyid: xyIds});
    };
  },
);

watch(
  () => formData.value['zyid'],
  (zyIds) => {
    if (zyIds) {
      queryCodeBjb();
    } else {
      selectBjData.value = [];
      formData.value.zyid = null;
      formData.value.zymc = null;
      formData.value['zyidArray'] = [];
      formData.value.bjid = null;
      formData.value.bjmc = null;
      formData.value['bjidArray'] = [];
    }
    tablePropsBjmc.datasource = ({page, limit, where, orders}) => {
      return queryBjb({
        ...where,
        ...orders,
        page,
        limit,
        zyid: zyIds,
        njid: formData.value?.njid ?? '',
      });
    };
  },
);

onMounted(() => {
  if (glzXgh) {
    queryPersonInfo();
    queryUserDataScope();
  }
});

</script>

<style lang="scss" scoped>
.info-card-with-actions {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  .card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    border-bottom: 1px solid #eee;
    margin-bottom: 18px;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      color: var(--el-color-primary);

      &:hover {
        opacity: 0.8;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

.stats-card {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 16px;
  margin-bottom: 8px;

  .stats-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
  }

  .stats-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .stats-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: #e9f5ff;
        transform: translateY(-2px);
      }

      .stats-label {
        font-size: 14px;
        color: #666;
      }

      .stats-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
    }
  }
}

.settings-section {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 10px;
  overflow-y: auto;
  max-height: calc(100vh - 285px);
  min-height: 300px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 1200px) {
    max-height: none;
    overflow-y: visible;
  }
}

.section-title {
  margin: 0 0 10px 6px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;

  .icon {
    margin-right: 6px;
    color: var(--el-color-primary);
  }
}

.form-item {
  padding: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  margin-bottom: 6px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 10px;

  @media (max-width: 1200px) {
    height: 500px;
  }
}

.class-tags-container {
  border-radius: 4px;
  padding: 6px;
  background-color: #f8f9fa;

  .tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 18px;
    padding: 2px;
  }

  :deep(.el-scrollbar__view) {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

.class-tag {
  cursor: pointer;
  transition: all 0.3s;
  padding: 0 6px;
  height: 32px;
  line-height: 30px;
  font-size: 14px;
  flex-shrink: 0;

  &:hover {
    opacity: 0.8;
  }

  .student-count {
    font-size: 12px;
    margin-left: 4px;
    opacity: 0.8;
  }
}

.stats-item-active {
  background-color: #e1f3ff !important;
  border: 1px solid var(--el-color-primary) !important;

  .stats-value {
    color: var(--el-color-primary) !important;
    font-weight: bold;
  }
}

/* Enhance clickable styles */
.clickable {
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.selected-tags {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;

  .selected-tag {
    .el-tag__close {
      display: none;
    }
  }
}

.count-badge {
  display: inline-block;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  //background-color: var(--el-color-primary);
  color: var(--el-color-primary);
  border-radius: 10px;
  padding: 0px;
  margin-left: 4px;
  font-size: 16px;
  font-weight: bold;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
  .info-card-with-actions {
    .card-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding: 8px 12px;

      .action-buttons {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  .stats-card {
    padding: 12px;

    .stats-content {
      grid-template-columns: 1fr;
    }
  }

  .settings-section {
    padding: 8px;
    min-height: auto;
  }

  .form-item {
    margin-bottom: 8px;
  }

  .class-tags-container {
    .tags-wrapper {
      flex-wrap: nowrap;
      padding-bottom: 8px;
    }
  }

  .class-tag {
    flex-shrink: 0;
  }
}

@media (max-width: 480px) {
  .right-panel {
    padding: 8px;
  }

  .class-tag {
    padding: 0 12px;
    font-size: 13px;
  }

  .info-card-with-actions {
    .card-actions {
      .action-buttons {
        flex-wrap: wrap;

        .el-button {
          flex: 1;
          min-width: 100px;
        }
      }
    }
  }
}
</style>
