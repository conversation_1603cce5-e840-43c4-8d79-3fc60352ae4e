<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <!-- 搜索表单 -->
        <search @search="handleSearch" ref="searchRef"/>
      </template>
      <data-list
        v-if="current"
        :roleId="current.id"
        ref="dataListRef"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { ElMessage as EleMessage } from "element-plus";
import DataList from './components/data-list.vue';
import { listRoles } from '@/views/system/role/api';
import Search from './components/search.vue';

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const data = ref([]);

/** 选中数据 */
const current = ref(null);

/** 搜索组件引用 */
const searchRef = ref(null);

/** 子组件引用 */
const dataListRef = ref(null);

/** 处理搜索 */
const handleSearch = (where) => {
  // 调用子组件的reload方法
  dataListRef.value?.reload?.(where);
};

/** 查询角色列表 */
const query = () => {
  loading.value = true;
  listRoles({ roleScope: '班级' })
  .then((list) => {
    loading.value = false;
    data.value = list ?? [];
    nextTick(() => {
      handleNodeClick(data.value[0]);
    });
  })
  .catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 选择数据 */
const handleNodeClick = (row) => {
  if (row && row.id) {
    current.value = row;
  } else {
    current.value = null;
  }
};

// 初始化查询
query();
</script>

<script>
export default {
  name: 'JsglfwFdydbgl'
};
</script>
