import request from '@/utils/request';

/**
 * 查询不分页
 */
export async function getCodeType(params) {
  const res = await request.get('/code/codeType', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 * 辅导员带班设置，带班数据回显，带统计
 */
export async function queryPage(params) {
  // const res = await request.get('/system/account/queryPage', {params});
  const res = await request.get('/userInfo/userDataScope/listDutyClassUserPage',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getcodeTypeById(id) {
  const res = await request.post('/code/codeType/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 带班设置
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/userInfo/userDataScope/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 带班数据回显
 * @param glzXgh
 * @param roleId
 * @returns {Promise<*>}
 */
export async function getUserDataScope(glzXgh, roleId) {
  const res = await request.get(
    '/userInfo/userDataScope/' + glzXgh + '/' + roleId);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/code/codeType/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导入
 */
export async function importData(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post('/userInfo/userDataScope/dataImport', formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}
