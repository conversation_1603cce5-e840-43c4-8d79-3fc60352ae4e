<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form size="small" label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :md="7" :sm="12" :xs="24">
          <el-form-item label="用户账号">
            <el-input clearable
                      v-model.trim="form.username"
                      placeholder="请输入用户账号"/>
          </el-form-item>
        </el-col>
        <el-col :md="7" :sm="12" :xs="24">
          <el-form-item label="用户名">
            <el-input clearable
                      v-model.trim="form.realName"
                      placeholder="请输入用户名"/>
          </el-form-item>
        </el-col>
        <el-col :md="7" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" size="small" plain @click="search">查询</el-button>
            <el-button @click="reset" size="small">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data';

const emit = defineEmits(['search']);

/** 表单数据 */
const [form, resetFields] = useFormData({
});

/** 搜索 */
const search = () => {
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
</script>
