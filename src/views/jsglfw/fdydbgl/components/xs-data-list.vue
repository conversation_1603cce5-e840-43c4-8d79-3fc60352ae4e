<template>
  <ele-pro-table
    ref="tableRef"
    row-key="xgh"
    flex-table
    :columns="columns"
    :datasource="datasource"
    :border="true"
    :show-overflow-tooltip="true"
    v-model:selections="selections"
    highlight-current-row
    tooltip-effect="light"
    cache-key="xsDataTable"
    :cache-data="cacheData"
    @select-all="onSelect"
    @select="onSelect"
    @done="onDone"
    :footer-style="{ paddingBottom: '3px' }"
    style="padding-bottom: 0"
  />
</template>

<script setup>
import {ref, watch, nextTick, reactive, computed} from 'vue';
import {queryPage, getFieldList, queryListByRoleDuty} from '@/views/personInfo/st/api';
import {useRouter} from 'vue-router';
import {useUserStore} from '@/store/modules/user';
import {storeToRefs} from 'pinia';

const emit = defineEmits(['change']);

const userStore = useUserStore();
const {currentRoute, push} = useRouter();
const userType = ref('student');
const {pageHeight} = storeToRefs(userStore);

const props = defineProps({
  optMark: String,
  xghs: Array,
  listQuery: Object,
  bjId: String,
});

const searchRef = ref(null);
const tableRef = ref(null);
const checkedRowData = ref([]);
const columns = ref([]);
const selections = ref([]);
const cacheData = ref([]); // 用于缓存选中数据

// 初始化表格列
const initFieldList = async () => {
  let newColumn = [];

  if (props.optMark === 'set') {
    newColumn.push({
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    });
  }

  const data = await getFieldList(userType.value, {
    listShowFlag: '是',
    tempField: '否',
  });

  if (data.length > 0) {
    data.forEach((e) => {
      if (e.controlType === 'select' && e.loadDataType) {
        if (e.loadDataType === 'other') {
          let finallyColumn = {
            prop: e.fieldEn,
            label: e.fieldZh,
            formatter: (row) => {
              let finallyVal = '';
              switch (e.loadDataUrl) {
                case '/code/codeBjb':
                  finallyVal = row['bjmc'];
                  break;
                case '/code/codeZyb':
                  finallyVal = row['zymc'];
                  break;
                case '/code/codeDwb':
                case '/code/codeDwb/listAll':
                  finallyVal = row['xymc'];
                  break;
                case '/code/codeXszt':
                  finallyVal = row['ryzt'];
                  break;
                default:
                  break;
              }
              return finallyVal;
            },
          };
          if (e?.sortable === '是') finallyColumn.sortable = 'custom';
          if (e?.fixed === '是') finallyColumn.fixed = 'left';
          newColumn.push(finallyColumn);
        } else {
          let finallyColumn = {
            prop: e.fieldEn,
            label: e.fieldZh,
            formatter: (row) => {
              let finallyVal = '';
              switch (e.loadDataType) {
                case 'pycc':
                  finallyVal = row['pyccmc'];
                  break;
                case 'nj':
                  finallyVal = row['njmc'];
                  break;
                default:
                  finallyVal = row[e.fieldEn];
                  break;
              }
              return finallyVal;
            },
          };
          if (e?.sortable === '是') finallyColumn.sortable = 'custom';
          if (e?.fixed === '是') finallyColumn.fixed = 'left';
          newColumn.push(finallyColumn);
        }
      } else {
        let finallyColumn = {
          prop: e.fieldEn,
          label: e.fieldZh,
        };
        if (e?.sortable === '是') finallyColumn.sortable = 'custom';
        if (e?.fixed === '是') finallyColumn.fixed = 'left';
        newColumn.push(finallyColumn);
      }
    });
  }
  columns.value = newColumn;
};

// 表格数据源
const datasource = async ({page, limit, where, orders, filters}) => {

  // 无查询条件，直接返回空表格
  if (!props.listQuery) {
    return {
      list: [],
      total: 0,
    };
  }

  // ✅ count 模式下，必须包含 roleId 和 username
  if (props.optMark === 'count') {
    const {roleId, username} = props.listQuery;
    if (!roleId || !username) {
      return {
        list: [],
        total: 0,
      };
    }
  }

  const queryParams = {
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    ...props.listQuery,
  };

  const pageData =
    props.optMark === 'set'
      ? await queryPage(userType.value, queryParams)
      : await queryListByRoleDuty(queryParams);

  // 初始回显数据处理
  if (pageData.list.length > 0 && props.xghs?.length > 0) {
    checkedRowData.value = pageData.list.filter(item =>
      props.xghs.includes(item.xgh),
    );
  }

  return pageData;
};

// 刷新表格
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    tableRef.value?.reload?.();
  }
};

// 维护一个完整的选中状态数组
const allSelectedStudents = ref([]);

// 处理选中事件
const onSelect = (selection) => {
  // 当前页选中的学生
  const currentPageSelected = selection.map(item => item.xgh);

  // 当前页未选中的学生
  const currentPageUnselected = tableRef.value.tableData.filter(item => !currentPageSelected.includes(item.xgh)).
    map(item => item.xgh);

  // 更新完整选中状态
  allSelectedStudents.value = [
    ...allSelectedStudents.value.filter(xgh => !currentPageUnselected.includes(xgh)),
    ...currentPageSelected.filter(xgh => !allSelectedStudents.value.includes(xgh)),
  ];

  emit('change', allSelectedStudents.value);
};

// 表格数据加载完成事件
const onDone = () => {
  nextTick(() => {
    // 清除当前选中
    tableRef.value?.clearSelection?.();
    // 恢复当前页的选中状态
    if (allSelectedStudents.value.length > 0) {
      const rowsToSelect = tableRef.value.tableData.filter(row =>
        allSelectedStudents.value.includes(row.xgh),
      );
      rowsToSelect.forEach(row => {
        tableRef.value?.toggleRowSelection(row, true);
      });
    }
    // 如果有初始xghs数据，初始化选中状态
    if (props.xghs?.length > 0 && allSelectedStudents.value.length === 0) {
      allSelectedStudents.value = [...props.xghs];
      const rowsToSelect = tableRef.value.tableData.filter(row =>
        allSelectedStudents.value.includes(row.xgh),
      );
      rowsToSelect.forEach(row => {
        tableRef.value?.toggleRowSelection(row, true);
      });
      selections.value = rowsToSelect;
    }
    onSelect(selections.value);
  });
};

// 监听外部传入的xghs变化（如清空选择时）
watch(() => props.xghs, (newVal) => {
  if (newVal && newVal.length === 0) {
    allSelectedStudents.value = [];
    tableRef.value?.clearSelection?.();
  }
}, {deep: true});

// 监听列表查询参数变化
watch(
  () => props.listQuery,
  (listQuery) => {
    if (listQuery) {
      initFieldList();
      searchRef.value?.resetFields?.();
      reload({});
    }
  },
  {immediate: true},
);

</script>
