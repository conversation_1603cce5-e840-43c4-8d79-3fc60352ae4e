<template>
  <ele-pro-table
    ref="tableRef"
    flex-table
    row-key="id"
    :columns="columns"
    :datasource="datasource"
    :border="true"
    :show-overflow-tooltip="true"
    v-model:selections="selections"
    highlight-current-row
    tooltip-effect="light"
    cache-key="systemDictDataTable"
    :footer-style="{ paddingBottom: '3px' }"
    style="padding-bottom: 0"
  >
    <template #toolbar>
      <el-button class="ele-btn-icon" size="small" @click="openImport()">
        导入
      </el-button>
    </template>
    <template #action="{ row }">
      <el-link type="primary" underline="never" @click="openFdyDb(row, 'set')">
        带班设置
      </el-link>
    </template>
    <template #count="{ row }">
      <el-link
        type="primary"
        underline="never"
        @click.stop="openFdyDb(row, 'count')"
      >
        {{ row?.count }}
      </el-link>
    </template>
    <template #maleCount="{ row }">
      <el-link
        type="primary"
        underline="never"
        @click.stop="openFdyDb(row, 'male')"
      >
        {{ row?.maleCount }}
      </el-link>
    </template>
    <template #femaleCount="{ row }">
      <el-link
        type="primary"
        underline="never"
        @click.stop="openFdyDb(row, 'female')"
      >
        {{ row?.femaleCount }}
      </el-link>
    </template>
    <!-- 表头查询 -->
    <template #nameHeader="{ column }">
      <div style="display: flex; align-items: center">
        <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
          {{ column.label }}
        </div>
        <name-filter @search="onNameFilter" />
      </div>
    </template>
  </ele-pro-table>

  <!-- 导入弹窗 -->
  <import v-model="showImport" @done="reload" />
</template>

<script setup>
  import { ref, watch, defineExpose } from 'vue';
  import { queryPage } from '../api/index';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { useRouter } from 'vue-router';
  import NameFilter from '@/views/exam/exam-management/components/name-filter.vue';
  import Import from './import.vue';

  const { currentRoute, push } = useRouter();

  const props = defineProps({
    /** 角色ID */
    roleId: String
  });

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  /** 表格实例 */
  const tableRef = ref(null);

  /** 是否显示导入弹窗 */
  const showImport = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'username',
      label: '用户账号',
      headerSlot: 'nameHeader'
    },
    {
      prop: 'realName',
      label: '用户名',
      slot: 'realName'
    },
    {
      prop: 'gender',
      label: '性别',
      sortable: 'custom',
      columnKey: 'gender',
      filters: [
        { text: '男', value: '男' },
        { text: '女', value: '女' }
      ],
      filterMultiple: false
    },
    {
      prop: 'telMobile',
      label: '手机号',
      slot: 'telMobile'
    },
    {
      prop: 'count',
      label: '总人数',
      slot: 'count'
    },
    {
      prop: 'maleCount',
      label: '男生人数',
      slot: 'maleCount'
    },
    {
      prop: 'femaleCount',
      label: '女生人数',
      slot: 'femaleCount'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      roleId: props.roleId
    });
  };

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 打开辅导员带班设置 */
  const openFdyDb = (row, mark) => {
    push({
      path: '/jsglfw/fdydbgl/dbsz/' + props.roleId + '/' + row.username,
      query: {
        mark: mark,
        currentCount: JSON.stringify({
          count: row.count,
          maleCount: row.maleCount,
          femaleCount: row.femaleCount
        })
      }
    });
  };

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  /** 刷新表格 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      tableRef.value?.reload?.();
    }
  };

  /** 执行表格刷新 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        username: nameFilterValue.value
      });
      nameFilterValue.value = null;
    } else {
      reload();
    }
  };

  // 暴露方法给父组件
  defineExpose({
    reload
  });

  // 监听roleId变化
  watch(
    () => props.roleId,
    () => {
      reload();
    }
  );
</script>
