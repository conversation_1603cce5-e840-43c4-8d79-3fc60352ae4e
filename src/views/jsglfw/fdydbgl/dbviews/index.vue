<template>
  <ele-page hide-footer flex-table>
    <div class="page-container">
      <!-- 顶部信息栏 -->
      <div class="top-bar">
        <!-- 辅导员信息卡片 -->
        <div class="info-card">
          <my-card header="" :currentData="currentData" simple-mode />
        </div>

        <!-- 带班范围筛选条件 -->
        <div class="filter-conditions">
          <div class="filter-item" @click="handleFilterClick('pyccid')">
            <span class="label">培养层次：</span>
            <span class="value">{{ currentScopeData.pyccmc }}</span>
          </div>
          <div class="filter-item" @click="handleFilterClick('njid')">
            <span class="label">年级：</span>
            <span class="value">{{ currentScopeData.njmc }}</span>
          </div>
          <div class="filter-item" @click="handleFilterClick('xyid')">
            <span class="label">院系：</span>
            <span class="value">{{ currentScopeData.xymc }}</span>
          </div>
          <div class="filter-item" @click="handleFilterClick('zyid')">
            <span class="label">专业：</span>
            <span class="value">{{ currentScopeData.zymc }}</span>
          </div>
          <div class="filter-item" @click="handleFilterClick('bjid')">
            <span class="label">班级：</span>
            <span class="value">{{ currentScopeData.bjmc }}</span>
          </div>
        </div>

        <!-- 右侧操作按钮 -->
        <div class="action-buttons">
          <el-button
            size="small"
            type="primary"
            plain
            @click="onSet"
            v-if="optMark !== 'set'"
            class="set-btn"
          >
            带班设置
          </el-button>
          <el-text text @click="onBack" class="back-btn">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <span>返回</span>
          </el-text>
        </div>
      </div>

      <!-- 学生列表区 -->
      <div class="student-list-section">
        <div class="list-header">
          <h3 class="section-title">
            <el-icon>
              <User/>
            </el-icon>
            学生列表 ({{ total }}人)
          </h3>

          <div class="header-actions">
            <el-button
              size="small"
              @click="refreshData"
              :loading="loading"
            >
              <el-icon>
                <Refresh/>
              </el-icon>
              刷新
            </el-button>
            <el-button
              size="small"
              type="primary"
              plain
              @click="exportData"
              :loading="exportLoading"
            >
              <el-icon>
                <Download/>
              </el-icon>
              导出
            </el-button>
          </div>
        </div>


        <xs-data-list
          :key="listKey"
          :pyccId="formData?.pyccid"
          :njId="formData?.njid"
          :xyId="formData?.xyid"
          :zyId="formData?.zyid"
          :bjId="formData?.bjid"
          :xghs="formData['xghArray']"
        />
        <!--        :xb="currentXb"-->
        <!--        @change="updataChange"-->
      </div>
    </div>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, unref} from 'vue';
import {ElMessage} from 'element-plus';
import {User, Refresh, Download} from '@element-plus/icons-vue';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab';
import MyCard from '@/views/personInfo/st/personal/components/my-card.vue';
import IconPark from '@/components/IconPark/index.vue';
import {getPersonInfo} from '@/views/personInfo/st/api/index.js';
import {getUserDataScope} from '../api/index';
import XsDataList from '@/views/jsglfw/fdydbgl/components/xs-data-list.vue';

const router = useRouter();
const {currentRoute, push} = router;
const {removePageTab, getRouteTabKey, setPageTabTitle} = usePageTab();

// 获取路由参数
const {path, query} = unref(currentRoute);
const pathArray = path.split('/');
const roleId = pathArray[4];
const glzXgh = pathArray[5];
const optMark = query.mark;

// 数据状态
const loading = ref(false);
const exportLoading = ref(false);
const formData = ref({});
const currentScopeData = ref({});
const currentData = ref({});
const studentList = ref([]);
const total = ref(0);

// 添加listKey计算属性
const listKey = computed(() => {
  return `${formData.value.pyccid}-${formData.value.njid}-${formData.value.xyid}-${formData.value.zyid}`;
});

// 当前选中的筛选条件
const activeFilter = ref('');

const queryUserDataScope = () => {
  loading.value = true;
  getUserDataScope(glzXgh, roleId).then((data) => {
    loading.value = false;
    if (data) {
      const result = {};
      Object.keys(data).forEach((key) => {
        let value = data[key];
        if (value) {
          if (
            ['pyccid', 'njid', 'xyid', 'zyid', 'bjid', 'xgh'].includes(key)
          ) {
            result[key + 'Array'] = value.split(',');
          }
        }
      });
      formData.value = Object.assign(data, result);

      currentScopeData.value = data;
      console.log('带班范围数据:', currentScopeData.value);
    }
  }).catch((e) => {
    loading.value = false;
    ElMessage.error(e.message);
  });
};

// 点击筛选条件
const handleFilterClick = (filterType) => {
  activeFilter.value = filterType;
  // 这里可以添加筛选逻辑，比如高亮显示当前选中的条件
  // 然后根据选中的条件查询学生列表
};

// 在initData函数中初始化数据
const initData = async () => {
  try {
    loading.value = true;
    // 获取辅导员信息
    await getPersonInfo('teacher', glzXgh).then(data => {
      currentData.value = data;
      setPageTabTitle(`${data?.xm}带班${optMark === 'set' ? '设置' : '信息'}`);
    });

    // 获取带班范围
    queryUserDataScope();

  } catch (error) {
    ElMessage.error(error.message || '获取数据失败');
  } finally {
    loading.value = false;
  }
};


// 导出数据
const exportData = async () => {
  try {
    exportLoading.value = true;

    const params = {
      glzXgh,
      roleId,
    };

    // await exportStudentList(params);
    ElMessage.success('导出任务已开始，请稍后查看下载中心');

  } catch (error) {
    ElMessage.error(error.message || '导出失败');
  } finally {
    exportLoading.value = false;
  }
};

// 查看学生详情
const viewStudentDetail = (row) => {
  router.push(`/xsgl/xsxx/detail/${row.xh}`);
};

// 带班设置
const onSet = () => {
  push({
    path: `/jsglfw/fdydbgl/dbsz/${roleId}/${glzXgh}`,
    query: {mark: 'set'},
  });
};

// 返回
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/jsglfw/fdydbgl');
};

// 初始化
onMounted(() => {
  initData();
});
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background-color: #f5f7fa;
  gap: 16px;
}

.top-bar {
  display: flex;
  align-items: center;
  gap: 24px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  position: relative;

  .info-card {
    flex-shrink: 0;
    width: 280px;
  }

  .filter-conditions {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    min-width: 0;

    .filter-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #e6f7ff;
        color: var(--el-color-primary);
      }

      &.active {
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }

      .label {
        color: #666;
        font-size: 14px;
        margin-right: 4px;
      }

      .value {
        font-weight: 500;
        color: #333;
      }
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;

    .back-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      color: var(--el-color-primary);
      font-size: 14px;
      white-space: nowrap;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.student-list-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;

      .el-icon {
        color: var(--el-color-primary);
      }
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .top-bar {
    .filter-conditions {
      gap: 12px;
    }
  }
}

@media (max-width: 992px) {
  .top-bar {
    flex-wrap: wrap;

    .info-card {
      width: 100%;
      margin-bottom: 12px;
    }

    .filter-conditions {
      min-width: 100%;
      order: 3;
      margin-top: 12px;
    }

    .action-buttons {
      position: absolute;
      top: 16px;
      right: 16px;
    }
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 12px;
    gap: 12px;
  }

  .top-bar,
  .student-list-section {
    border-radius: 6px;
    padding: 12px;
  }

  .top-bar {
    .filter-conditions {
      .filter-item {
        padding: 6px 8px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 8px;
    gap: 8px;
  }

  .header-actions {
    flex-wrap: wrap;

    .el-button {
      flex: 1;
      min-width: 100px;
    }
  }
}
</style>
