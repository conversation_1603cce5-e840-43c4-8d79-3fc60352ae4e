<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="25%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <ele-card :body-style="{ padding: '5px 0!important' }">
        <template #header>
          <div style="display: flex; align-items: center;">
            <el-input clearable size="small"
                      :maxlength="20"
                      v-model="keywords"
                      placeholder="输入名称搜索"
                      :prefix-icon="SearchOutlined"/>
          </div>
        </template>
        <div :style="{height:pageHeight+'px', overflow:'auto'}">
          <ele-loading :loading="loading">
            <el-tree ref="treeRef"
                     :data="data"
                     highlight-current
                     node-key="id"
                     :props="{ label: 'name' }"
                     :expand-on-click-node="false"
                     :default-expand-all="true"
                     :filter-node-method="filterNode"
                     :style="{
                              '--ele-tree-item-height': '34px',
                              '--ele-tree-expand-padding': 0,
                              '--ele-tree-expand-margin': 0
                            }"
                     @node-click="handleNodeClick">
              <template #default="{ data: d }">
                <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.name }}
                        </span>
                      </span>
                    </span>
                </div>
              </template>
            </el-tree>
          </ele-loading>
        </div>
      </ele-card>
      <template #body>
        <DataList v-if="current && current.id"
                  :currentGroupId="current.id"
                  :currentGroupName="current.groupName"/>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {ref, nextTick, watch, unref} from 'vue';
import {SearchOutlined} from '@/components/icons';
import {useMobile} from '@/utils/use-mobile';
import DataList from './components/data-list.vue';
import {useRouter} from "vue-router";
import {getCodeDwb} from "@/views/base-code/dwb/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {currentRoute, push} = useRouter();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
//获取浏览器参数
const {params, path, query} = unref(currentRoute);
const userType = path.split("/")[4];
/** 是否是移动端 */
const {mobile} = useMobile();

/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const data = ref([]);

/** 选中数据 */
const current = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);

/**
 * 获取组信息
 */
const queryCodeDwb = () => {
  loading.value = true;
  getCodeDwb().then((list) => {
    loading.value = false;
    data.value = list ?? [];
    nextTick(() => {
      handleNodeClick(data.value[0]);
    });
  });
}

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  // 移动端自动收起左侧
  if (current.value != null && mobile.value) {
    splitRef.value?.toggleCollapse?.(true);
  }
  if (row && row.id) {
    current.value = row;
    treeRef.value?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id
  } else {
    current.value = null;
  }
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.name && data.name.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});

queryCodeDwb();
</script>

<script>
export default {
  name: 'BASECODEZYB'
};
</script>
