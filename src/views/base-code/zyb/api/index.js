import request from '@/utils/request';

/**
 * 导入
 */
export async function importData(file) {
    const formData = new FormData();
    formData.append('file', file);
    const res = await request.post('/code/codeZyb/importData', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/code/codeZyb/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getCodeZyb(params) {
    const res = await request.get('/code/codeZyb', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加
 */
export async function add(data) {
    const res = await request.post('/code/codeZyb', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 修改
 */
export async function update(data) {
    const res = await request.put('/code/codeZyb', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.delete('/code/codeZyb/batch', {
        data
    });
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
