import request from '@/utils/request';

/**
 * 导入
 */
export async function importData(file) {
    const formData = new FormData();
    formData.append('file', file);
    const res = await request.post('/code/codeDwb/importData', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getCodeDwb(params) {
    const res = await request.get('/code/codeDwb', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/code/codeDwb/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加
 */
export async function add(data) {
    const res = await request.post('/code/codeDwb', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 修改
 */
export async function update(data) {
    const res = await request.put('/code/codeDwb', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.delete('/code/codeDwb/batch', {
        data
    });
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
