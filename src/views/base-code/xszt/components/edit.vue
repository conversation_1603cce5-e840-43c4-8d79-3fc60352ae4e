<!-- 编辑弹窗 -->
<template>
    <ele-drawer :size="520"
                :title="isUpdate ? '修改学生状态' : '添加学生状态'"
                :append-to-body="true"
                style="max-width: 100%"
                :model-value="modelValue"
                :body-style="{ paddingBottom: '8px' }"
                @update:modelValue="updateModelValue">
        <pro-form ref="formRef"
                  size="small"
                  :model="form"
                  :items="items"
                  :grid="{ span: 24 }"
                  @updateValue="setFieldValue">
        </pro-form>
        <template #footer>
            <el-button size="small" @click="updateModelValue(false)">取消</el-button>
            <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
                保存
            </el-button>
        </template>
    </ele-drawer>
</template>

<script setup>
    import {ref, reactive, watch} from 'vue';
    import {ElMessage as EleMessage} from "element-plus";
    import {useFormData} from '@/utils/use-form-data';
    import ProForm from '@/components/ProForm/index.vue';
    import {operation} from '../api/index';

    const emit = defineEmits(['done', 'update:modelValue']);

    const props = defineProps({
        /** 弹窗是否打开 */
        modelValue: Boolean,
        /** 修改回显的数据 */
        data: Object
    });

    /** 是否是修改 */
    const isUpdate = ref(false);

    /** 提交状态 */
    const loading = ref(false);

    /** 表单实例 */
    const formRef = ref(null);
    /** 表单数据 */
    const [form, resetFields, assignFields, setFieldValue] = useFormData({
        id: void 0,
        ztmc: '',
        sfzx: '',
        sfzj: '',
        sfzd: '',
        sfby: '',
        sort: void 0,
        bz: '',
    });

    /** 表单项 */
    const items = ref([
        {prop: 'ztmc', label: '状态名称', type: 'input', required: true},
        {
            prop: 'sfzx',
            label: '是否在校',
            type: 'select',
            options: [
                {label: '是', value: '是'},
                {label: '否', value: '否'},
            ],
            required: true
        },
        {
            prop: 'sfzj',
            label: '是否在籍',
            type: 'select',
            options: [
                {label: '是', value: '是'},
                {label: '否', value: '否'},
            ],
            required: true
        },
        {
            prop: 'sfzd',
            label: '是否在读',
            type: 'select',
            options: [
                {label: '是', value: '是'},
                {label: '否', value: '否'},
            ],
            required: true
        },
        {
            prop: 'sfby',
            label: '是否毕业',
            type: 'select',
            options: [
                {label: '是', value: '是'},
                {label: '否', value: '否'},
            ],
            required: true
        },
        {
            prop: 'sort',
            label: '排序',
            type: 'inputNumber',
        },
        {
            prop: 'bz',
            label: '备注',
            type: 'textarea',
            colProps: {span: 24}
        }
    ]);

    /** 保存编辑 */
    const onSubmit = () => {
        formRef.value?.validate?.((valid) => {
            if (!valid) {
                return;
            }
            loading.value = true;
            operation(form).then((msg) => {
                loading.value = false;
                EleMessage.success(msg);
                updateModelValue(false);
                emit('done');
            }).catch((e) => {
                loading.value = false;
                EleMessage.error(e.message);
            });
        });
    };

    /** 更新modelValue */
    const updateModelValue = (value) => {
        emit('update:modelValue', value);
    };

    watch(
        () => props.modelValue,
        (modelValue) => {
            if (modelValue) {
                if (props.data) {
                    assignFields({
                        ...props.data,
                    });
                    isUpdate.value = true;
                } else {
                    isUpdate.value = false;
                }
            } else {
                resetFields();
                formRef.value?.clearValidate?.();
            }
        }
    );
</script>
