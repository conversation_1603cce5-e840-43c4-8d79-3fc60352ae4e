import request from '@/utils/request';

/**
 * 导入
 */
export async function importData(file) {
    const formData = new FormData();
    formData.append('file', file);
    const res = await request.post('/code/codeXszt/importData', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/code/codeXszt/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getCodeXszt(params) {
    const res = await request.get('/code/codeXszt', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/code/codeXszt/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/code/codeXszt/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
