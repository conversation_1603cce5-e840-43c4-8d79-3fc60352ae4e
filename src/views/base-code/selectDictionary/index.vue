<template>
  <ele-page hide-footer flex-table>
    <!-- 搜索表单 -->
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :tools="['reload', 'size', 'columns', 'maximized']"
        highlight-current-row
        tooltip-effect="light"
        cache-key="systemUserTable"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            v-if="hasPermission('dictionary:selectControlApi:operation')"
            size="small"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            v-if="hasPermission('dictionary:selectControlApi:remove')"
            size="small"
            @click="removeOpt()"
          >
            删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link
              v-if="hasPermission('dictionary:selectControlApi:operation')"
              type="primary"
              underline="never"
              @click="openEdit(row)"
            >
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { PlusOutlined, DeleteOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';

  import { queryPage, removes } from './api/index';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { usePermission } from '@/utils/use-permission';

  const { hasPermission } = usePermission();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'name',
      slot: 'name',
      label: '名称'
    },
    {
      prop: 'url',
      label: '接口'
    },
    {
      prop: 'textField',
      label: 'textField'
    },
    {
      prop: 'valueField',
      label: 'valueField'
    },
    {
      prop: 'sort',
      label: '排序',
      sortable: 'custom'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 导出excel */
  const exportBas = () => {
    window.location.href =
      BASE_URL + 'api/code/codeDwb/exportData?access_token=' + accessToken;
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const removeOpt = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SelectDictionary'
  };
</script>
