<template>
  <ele-card :body-style="{ padding: '5px!important' }">
    <!-- 搜索表单 -->
    <search ref="searchRef" @search="reload" />
    <!-- 表格 -->
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :border="true"
      :show-overflow-tooltip="true"
      v-model:selections="selections"
      highlight-current-row
      cache-key="systemUserTable"
      tooltip-effect="light"
      :footer-style="{ paddingBottom: '3px' }"
      :table-style="{ height: pageHeight - 90 + 'px', overflow: 'auto' }"
    >
      <template #toolbar>
        <el-button size="small" @click="openEdit()"> 新建 </el-button>
        <el-button size="small" @click="removeOpt()"> 删除 </el-button>
        <el-button class="ele-btn-icon" size="small" @click="openImport">
          导入
        </el-button>
        <el-button size="small" @click="exportBas()"> 导出 </el-button>
      </template>
      <template #name="{ row }">
        <ele-tooltip content="点我编辑" effect="light" placement="left">
          <el-link type="primary" underline="never" @click="openEdit(row)">
            {{ row.name }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
  </ele-card>
  <!-- 编辑弹窗 -->
  <edit
    v-model="showEdit"
    :data="current"
    :currentGroupId="currentGroupId"
    @done="reload"
  />
  <!-- 导入弹窗 -->
  <import v-model="showImport" @done="reload" />
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import Search from './search.vue';
  import Edit from './edit.vue';
  import { queryPage, removes } from '../api/index';
  import Import from './import.vue';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const props = defineProps({
    /** 字典id */
    currentGroupId: String,
    currentGroupName: String
  });

  let groupId = props.currentGroupId;
  const groupName = props.currentGroupName;
  /** 搜索栏实例 */
  const searchRef = ref(null);
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'code',
      label: '代码'
    },
    {
      prop: 'name',
      slot: 'name',
      label: '班级名称'
    },
    {
      prop: 'xymc',
      label: '所属院系'
    },
    {
      prop: 'zymc',
      label: '所属专业',
      group: true
    },
    {
      prop: 'njmc',
      label: '年级',
      group: true
    },
    {
      prop: 'sort',
      label: '排序',
      sortable: 'custom'
    },
    {
      prop: 'status',
      label: '状态'
    },
    {
      prop: 'bz',
      label: '备注'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 导出excel */
  const exportBas = () => {
    window.location.href =
      BASE_URL + 'api/code/codeBjb/exportData?access_token=' + accessToken;
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders }) => {
    return queryPage({ ...where, ...orders, page, limit, xyid: groupId });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const removeOpt = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  // 监听字典id变化
  watch(
    () => props.currentGroupId,
    (currentGroupId) => {
      groupId = currentGroupId;
      searchRef['value']?.resetFields?.();
      reload({});
    }
  );
</script>

<script>
  export default {
    name: 'BaseCodeBjb'
  };
</script>
