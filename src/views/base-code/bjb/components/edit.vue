<!-- 编辑弹窗 -->
<template>
  <ele-drawer :size="630"
              :title="isUpdate ? '修改班级' : '添加班级'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-position="top"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="代码" prop="code">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.code"
                  placeholder="请输入代码"/>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.name"
                  placeholder="请输入名称"/>
      </el-form-item>
      <!--      <el-form-item label="所属学院" prop="xyid">-->
      <!--        <ele-table-select ref="selectRef"-->
      <!--                          clearable-->
      <!--                          placeholder="请选择"-->
      <!--                          value-key="id"-->
      <!--                          label-key="name"-->
      <!--                          v-model="form.xyid"-->
      <!--                          :table-props="tableProps"-->
      <!--                          :cache-data="cacheData"-->
      <!--                          :popper-width="520">-->
      <!--          <template #topExtra>-->
      <!--            <advanced-search @search="onSearch"/>-->
      <!--          </template>-->
      <!--        </ele-table-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="所属专业" prop="zyid">
        <el-select v-model="form.zyid" placeholder="请选择">
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属年级" prop="njid">
        <dict-data placeholder="请选择年级"
                   code="nj"
                   type="radio"
                   :model-value="form.njid"
                   :dicQueryParams="{ getValType: 'id' }"
                   @update:modelValue="(value) => updateValue('njid', value)"/>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="启用">启用</el-radio>
          <el-radio value="停用">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序号" prop="sort">
        <el-input-number :min="0"
                         :max="99999"
                         v-model="form.sort"
                         placeholder="请输入排序号"
                         controls-position="right"
                         class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="备注">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.bz"
                  placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {add, update} from '../api/index';
import {getCodeZyb} from '@/views/base-code/zyb/api';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
  currentGroupId: String,
});

// const tableProps = reactive({
//   datasource: ({page, limit, where, orders}) => queryPage({...where, ...orders, page, limit}),
//   virtual: true,
//   height: 288,
//   columns: [
//     {prop: 'code', label: '代码', minWidth: 110},
//     {prop: 'name', label: '名称', minWidth: 150}
//   ],
//   showOverflowTooltip: true,
//   highlightCurrentRow: true,
//   toolbar: false,
//   pagination: {
//     layout: 'total, prev, pager, next, sizes',
//     teleported: false
//   },
//   rowStyle: {cursor: 'pointer'}
// });
//
// const selectRef = ref(null);
const isUpdate = ref(false);
const loading = ref(false);
const formRef = ref(null);

const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  code: '',
  name: '',
  xyid: '',
  zyid: '',
  njid: '',
  status: '',
  sort: void 0,
  bz: ''
});

const rules = reactive({
  code: [{required: true, message: '请输入代码', trigger: 'blur'}],
  name: [{required: true, message: '请输入名称', trigger: 'blur'}],
  xyid: [{required: true, message: '请选择所属学院', trigger: 'blur'}],
  zyid: [{required: true, message: '请选择专业', trigger: 'blur'}],
  njid: [{required: true, message: '请选择年级', trigger: 'blur'}],
  status: [{required: true, message: '请选择状态', trigger: 'blur'}]
});

const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) return;
    loading.value = true;
    const saveOrUpdate = isUpdate.value ? update : add;
    saveOrUpdate(form)
      .then((msg) => {
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => EleMessage.error(e.message))
      .finally(() => (loading.value = false));
  });
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const updateValue = (prop, value) => {
  form[prop] = value;
};


const options = ref([]);
const getZyByxyid = async () => {
  const rData = await getCodeZyb({xyid: form.xyid});
  options.value = rData?.map((d) => ({value: d.id, label: d.name})) ?? [];
};

watch(
  () => form.xyid,
  (xyid) => {
    if (xyid) {
      getZyByxyid()
    }
  }
);

const cacheData = ref();
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields({...props.data,});
        isUpdate.value = true;
        cacheData.value = [{id: props.data.xyid, name: props.data.xymc}];
      } else {
        form.xyid = props.currentGroupId
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);
</script>
