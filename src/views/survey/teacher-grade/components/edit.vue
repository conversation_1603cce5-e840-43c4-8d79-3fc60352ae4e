<template>
  <ele-drawer
    size="40%"
    :title="isUpdate ? '修改成绩等级 [' + data.zbmc + ']' : '添加成绩等级'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" size="small" :model="form" labelWidth="auto">
      <el-row :gutter="4">
        <template v-for="item in baseFileds">
          <el-col
            :span="item.colProps"
            v-if="item.showFlag === '是' && item.selfModifyFlag === '是'"
          >
            <ProFormItem
              :item="item"
              :model="form"
              @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)
              "
            >
              <template
                v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(
                      k
                    )
                )"
                #[name]="slotProps"
              >
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)"
        >{{ enterType === 'preview' ? '关闭' : '取消' }}
      </el-button>
      <el-button
        v-if="enterType !== 'preview'"
        plain
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { operationFdycpGrade } from '../api/index.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    enterType: String, //perview：考试详情预览 add:添加考试 edit:编辑考试
    /** 添加时机构id */
    organizationId: String,
    routeType: String,
    currentEvaluateConfigData: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    grade: '',
    minScore: void 0,
    maxScore: void 0
  });

  const updateFormValue = (item, prop, newVal) => {
    form[item.prop] = newVal;
  };

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 提交 */
  const onSubmit = () => {
    console.log('form:', JSON.stringify(form));
    formRef['value']?.validate?.((valid, obj) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let data = {
        ...form
      };
      operationFdycpGrade(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const baseFileds = ref([]);
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        baseFileds.value = [
          {
            prop: 'grade',
            label: '等级',
            type: 'input',
            required: true,
            showFlag: '是',
            selfModifyFlag: '是'
          },
          {
            prop: 'minScore',
            label: '最小值',
            type: 'inputNumber',
            required: true,
            showFlag: '是',
            selfModifyFlag: '是'
          },
          {
            prop: 'maxScore',
            label: '最大值',
            type: 'inputNumber',
            required: true,
            showFlag: '是',
            selfModifyFlag: '是'
          }
        ];
        if (props.data) {
          assignFields({
            ...props.data
          });
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
