<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <ele-pro-table ref="tableRef"
                     flex-table
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     v-model:current="current"
                     :tools="false"
                     highlight-current-row
                     tooltip-effect="light"
                     style="padding-bottom: 0;flex: 1; display: flex; flex-direction: column; overflow: auto"
                     :table-style="{ flex: 1, height: '100%', overflow: 'hidden' }"
                     cache-key="BankDataTable"
                     :footer-style="{ paddingBottom: '3px' }">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()">
            新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()">
            删除
          </el-button>
        </template>
        <template #grade="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.grade }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit"
          :data="current"
          @done="reload"/>
  </ele-page>
</template>

<script setup>
import {ref, watch, computed, reactive, unref} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import { ElMessage as EleMessage} from "element-plus";
import Edit from './components/edit.vue';
import {queryFdycpGradePage, removesFdycpGrade} from './api/index.js';
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {useDictData} from "@/utils/use-dict-data.js";
import {storeToRefs} from "pinia";
import {ElLoading} from "element-plus";

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const userStore = useUserStore();
useDictData(['listRoles'],);
const {dicts} = storeToRefs(userStore);

/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = computed(() => {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'grade',
      slot: 'grade',
      label: '等级',
    },
    {
      prop: 'minScore',
      label: '最小值',
    },
    {
      prop: 'maxScore',
      label: '最大值',
    },
  ]
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryFdycpGradePage({
    ...where, ...orders, ...filters,
    page,
    limit,
  });
};

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 刷新表格 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removesFdycpGrade(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};
</script>
