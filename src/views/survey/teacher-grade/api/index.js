/**
 * 成绩等级控制器
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getFdycpGrade(params) {
  const res = await request.get('/teacherEvaluate/fdycp-grade', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryFdycpGradePage(params) {
  const res = await request.get('/teacherEvaluate/fdycp-grade/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFdycpGradeById(id) {
  const res = await request.get('/teacherEvaluate/fdycp-grade/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operationFdycpGrade(data) {
  const res = await request.post('/teacherEvaluate/fdycp-grade/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removesFdycpGrade(data) {
  const res = await request.post('/teacherEvaluate/fdycp-grade/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
