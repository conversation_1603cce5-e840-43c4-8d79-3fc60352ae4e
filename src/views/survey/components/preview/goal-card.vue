<!-- 考试时间倒计时 -->
<template>
  <ele-card :body-style="{ padding: '0 8px 10px 8px!important'}">
    <div style="text-align: center">
<!--      <ele-text type="placeholder" size="md">考试时间</ele-text>-->
<!--      <ele-text size="lg" style="margin-bottom: 10px;">{{ uptime }}分钟</ele-text>-->
      <el-progress type="circle" :percentage="percentage"
                   :format="formatTime">
        <template #default>
          <span class="percentage-value">{{ formatTime() }}</span>
          <span class="percentage-label">{{ uptime }}分钟</span>
        </template>
      </el-progress>
    </div>
  </ele-card>
</template>

<script setup>
import {ref, computed, onMounted, onBeforeUnmount, onUnmounted, inject} from 'vue';
import MomentWrap from "@/utils/momentWrap.js";

const emit = defineEmits(['autoSumbit']);

const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

const props = defineProps({
  title: String,
  // uptime: String,
  // effectiveTime: Number,
  baseData: Object,
});

const uptime = ref(0)
const totalTime = ref(0)// 总时长30分钟（秒）
const currentTime = ref(0)// 当前时间（秒）
const timer = ref(null) // 计时器
const effectiveTime = ref(0)

/**倒计时 */
const percentage = computed(() => Math.floor((currentTime.value / totalTime.value) * 100));

const formatTime = () => {
  const hours = Math.floor(currentTime.value / 3600);
  const minutes = Math.floor((currentTime.value % 3600) / 60);
  const seconds = currentTime.value % 60;
  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}


onMounted(() => {
  uptime.value = props.baseData.uptime;
  console.log(uptime.value)
  if (uptime.value) {
    totalTime.value = uptime.value * 60;// 总时长（秒）
    //开始时间，结束时间范围时长
    effectiveTime.value = MomentWrap.commonDiffSeconds(new Date(), props.baseData.jssj)
    if (effectiveTime.value < totalTime.value) totalTime.value = effectiveTime.value
    console.log(totalTime.value, effectiveTime.value)
    // totalTime.value = 3 * 60;// 总时长30分钟（秒）
    timer.value = setInterval(() => {
      if (currentTime.value >= totalTime.value) {
        // 倒计时结束,系统自动交卷
        sharedPaperUpTimeData.value = currentTime.value
        console.log('倒计时结束,系统自动交卷')
        emit('autoSumbit', '考试倒计时结束,系统自动交卷');
        clearInterval(timer.value);

      } else {
        currentTime.value++;
        sharedPaperUpTimeData.value = currentTime.value
      }
    }, 1000); // 每分钟检查一次
  }
})

onBeforeUnmount(() => {
  // 销毁定时器
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>
<style scoped>
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 16px;
}

.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}

.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}

.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
