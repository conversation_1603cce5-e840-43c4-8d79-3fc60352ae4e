<!-- 试卷试题详情页 -->
<template>
  <ele-card :body-style="{ padding: '7px 8px 6px 8px',height: (pageHeight-50)+'px',overflow:'auto' }">
    <div class="list-item-body">
      <div style="flex: 1">
        <template v-if="currentQuestion" :key="currentQuestion.id">
          <div style="margin-top: 16px; display: flex; align-items: center">
            <ele-text size="xxl" style="flex: 1; padding-left: 1px">
              {{ currentQuestion.sort + 1 }}、 [{{ currentQuestion.qtype }}] {{ currentQuestion.name }}
            </ele-text>
          </div>
          <div style="margin-top: 12px;">
            <el-input v-if="currentQuestion.qtype==='问答题'"
                      style="margin-bottom: 10px;"
                      :rows="6"
                      type="textarea"
                      show-word-limit
                      maxlength="255"
                      placeholder="请输入答案"
                      v-model="answersList[currentIndex]['result']"
                      :readonly="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"/>
            <el-radio-group v-else-if="currentQuestion.qtype==='判断题'"
                            :disabled="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"
                            v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in ['对','错']" :key="qoption">
                <el-radio :value="qoption" :label="qoption">
                  {{ qoption }}
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <template v-else-if="currentQuestion.qtype==='填空题'">
              <el-input :rows="6"
                        type="textarea"
                        show-word-limit
                        maxlength="255"
                        v-model="answersList[currentIndex]['result']"
                        :disabled="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"
                        @blur="onUpdateOptionValue"
                        placeholder="请输入"/>
            </template>
            <template v-else-if="currentQuestion.qtype==='上传题'">
              <file-upload :limit="8" :modelValue="answersList[currentIndex]['result']" @update:modelValue="updateValue"
                           @change="changeDelIds" list-type="image"/>
            </template>
            <template v-else-if="currentQuestion.qtype==='评分题'">
              <el-rate v-model="answersList[currentIndex]['result']" allow-half :max="currentQuestion.maxScore"
                       size="large"
                       :disabled="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"/>
            </template>
            <el-radio-group v-else-if="currentQuestion.qtype==='单选题'"
                            :disabled="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"
                            v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in currentQuestion.surveyPquestionsOptionsList"
                        :key="qoption.code">
                <el-radio size="large" :value="qoption.code"
                          :label="qoption.code">
                  <ele-text size="md">
                    {{ qoption.code }}、{{ qoption.name }} ({{ qoption.score }}分)
                  </ele-text>
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <el-checkbox-group v-else-if="currentQuestion.qtype==='多选题'"
                               :disabled="['perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked'].includes(enterType)"
                               v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in currentQuestion.surveyPquestionsOptionsList"
                        :key="qoption.code">
                <el-checkbox size="large"
                             :value="qoption.code"
                             :label="qoption.code">
                  <ele-text size="md">
                    {{ qoption.code }}、{{ qoption.name }} ({{ qoption.score }}分)
                  </ele-text>
                </el-checkbox>
                <br/>
              </template>
            </el-checkbox-group>
          </div>
        </template>
      </div>
    </div>
    <div style="margin: 20px; ">
      <el-button v-if="currentIndex!==0" type="primary" plain size="small" @click="handleClickUp">上一题</el-button>
      <el-button v-if="currentIndex !== total" type="primary" plain size="small" @click="handleClickNext">
        下一题
      </el-button>
    </div>
  </ele-card>
</template>
<script setup>
import {computed, onMounted, ref, watch,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {inject} from 'vue';
import {operation} from "@/views/exam/my-exam/api/index.js";
import FileUpload from "@/components/FileUpload/index.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  ksId: String,//考试ID
  ksms: String,//考试模式
  baseData: Object,
  enterType: String,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

// 使用 inject 函数接收提供的数据
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');
const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

defineOptions({name: 'EXAMCARDINFO'});

const loading = ref(false);
const cacheExamPaperData = ref([]);
const total = ref(0);
const currentIndex = ref(0);
const answersList = ref([])

const currentQuestion = computed(() => {
  currentIndex.value = sharedData.value || sharedData.value === 0 ? sharedData.value : currentIndex.value;
  let question = {}
  if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
    question = cacheExamPaperData.value[currentIndex.value]
    if (question) sharedData.value = null;//清空右侧点击的试题编号
  }
  return question;
});

const handleClickUp = () => {
  if (currentIndex.value !== 0) {
    currentIndex.value--;
  }
};

/** 提交 */
const handleClickNext = () => {
  console.log(currentIndex.value === total.value)
  if (currentIndex.value === total.value) {
  } else {
    emit('updateAnswerVal', answersList.value);
    sharedPaperData.value = answersList.value
    if (currentIndex.value < total.value) {
      currentIndex.value++;
    }
  }
};

watch(
  () => props.ksId,
  (ksId) => {
    if (ksId) {
      if (props.baseData) {
        cacheExamPaperData.value = props.baseData?.surveyPaperQuestionsVOList
        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              qtype: cacheExam.qtype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
            }
            let result = ""
            if (cacheExam.qtype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
);

</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 0 24px;
  //min-height: 280px;
}

.el-radio-group {
  display: unset !important;
}

.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__label {
  font-size: 14px !important;
}
</style>
