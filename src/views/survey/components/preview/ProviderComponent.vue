<!-- 共享数据的组件 -->
<template>
  <div>
    <!--    <p>Shared Data: {{ sharedData }}</p>-->
    <slot></slot>
    <!--   /注意这里的插槽，用于存放兄弟组件-->
  </div>
</template>

<script setup>
import {ref, provide} from 'vue';

const sharedData = ref('');
provide('sharedData', sharedData);

const sharedPaperData = ref('');
provide('sharedPaperData', sharedPaperData);

const sharedPaperUpTimeData = ref('');
provide('sharedPaperUpTimeData', sharedPaperUpTimeData);
</script>
