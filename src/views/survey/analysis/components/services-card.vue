<template>
  <ele-card
    header="快捷操作区"
    :header-style="{ paddingTop: 0, paddingBottom: 0 }"
    :body-style="{ padding: '3px 8px 0 8px!important' }"
  >
    <ele-check-card
      v-if="cardOptData.length > 0"
      :items="cardOptData"
      :row="{ gutter: 6 }"
      style="margin: 3px"
    >
      <template #item="{ item }">
        <div
          style="display: flex; padding: 10px; min-height: 42px"
          @click="item.onClick"
        >
          <div style="flex: 1; min-width: 0">
            <ele-text size="md" style="font-size: 13px !important">{{
              item?.title
            }}</ele-text>
            <ele-ellipsis
              size="sm"
              type="secondary"
              :max-line="1"
              :tooltip="{
                effect: 'light',
                offset: 6,
                placement: 'right-start',
                bodyStyle: {
                  maxWidth: '210px',
                  minHeight: '25px',
                  marginTop: '10px'
                }
              }"
            >
              {{ item.content }}
            </ele-ellipsis>
          </div>
          <IconPark :name="item.icon" size="20" strokeWidth="3" />
        </div>
      </template>
    </ele-check-card>
  </ele-card>
</template>

<script setup>
  import { computed, unref } from 'vue';
  import IconPark from '@/components/IconPark/index.vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const props = defineProps({});

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push, replace } = useRouter();
  const { params, path, query } = unref(currentRoute);
  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];

  let cardOptData = computed(() => {
    let baseData = [
      {
        id: 1,
        title: '题库管理',
        content: '定义测评题库，管理员可自定义测评题目的类型和答案',
        btnName: '题库设置',
        icon: 'setting-two',
        isDisplay: true,
        onClick: () => {
          push('/survey/survey-questions/bank/survey/' + routeType);
        }
      },
      {
        id: 2,
        title: '测评指标配置',
        content: '定义测评的指标，管理员可自定义被测评人角色和答卷人角色',
        btnName: '指标配置',
        icon: 'file-settings',
        isDisplay: true,
        onClick: () => {
          push('/survey/teacher-evaluate');
        }
      },
      {
        id: 3,
        title: '成绩等级',
        content: '定义测评成绩的等级，管理员可自定义每个等级的分数',
        btnName: '范围设置',
        icon: 'setting-config',
        isDisplay: true,
        onClick: () => {
          push('/survey/teacher-grade');
        }
      },
      {
        id: 4,
        title: '测评问卷维护',
        content: '自定义设置测评审核时间',
        btnName: '测评问卷维护',
        icon: 'file-editing',
        isDisplay: true,
        onClick: () => {
          push('/survey/teacher-survey/teacher/' + routeType);
        }
      },
      {
        id: 5,
        title: '测评问卷答卷',
        content: '快速查看问卷答卷的记录',
        btnName: '测评问卷答卷',
        icon: 'doc-search-two',
        isDisplay: true,
        onClick: () => {
          push('/survey/teacher-answer/teacher/' + routeType);
        }
      }
    ];

    return baseData.map((d) => {
      return {
        ...d,
        col: { md: 4, sm: 12, xs: 24, style: { marginBottom: '6px' } }
      };
    });
  });
</script>
