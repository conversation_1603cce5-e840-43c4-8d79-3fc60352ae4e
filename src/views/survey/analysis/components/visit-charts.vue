<template>
  <ele-card :body-style="{ padding: '16px 6px 0 0' }">
    <v-chart ref="visitChartRef"
             :option="visitChartOption"
             style="height: 382px"/>
  </ele-card>
</template>

<script setup>
import {ref, computed} from 'vue';
import {use} from 'echarts/core';
import {CanvasRenderer} from 'echarts/renderers';
import {Line<PERSON><PERSON>, <PERSON><PERSON>hart, RadarChart} from 'echarts/charts';

import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import {useEcharts} from '@/utils/use-echarts.js';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Radar<PERSON><PERSON>,
  TitleComponent,
  GridComponent,
  TooltipComponent,
  LegendComponent
]);

const props = defineProps({
  type: String,
  header: String,
});
const visitChartRef = ref(null);

useEcharts([visitChartRef]);

let visitChartOption = computed(() => {
  let option = null;
  if (props.type === "line") {
    option = chartOptionLine.value
  } else if (props.type === "bar") {
    option = chartOptionBar.value
  } else if (props.type === "radar") {
    option = chartOptionRadar.value
  } else if (props.type === "kcbar") {
    option = chartOptionKcBar.value
  }
  return option
})

const chartOptionLine = ref({
  title: {
    text: '学生成绩趋势',
    subtext: '某学生成绩变化',
    left: "center",
    textStyle: {
      fontSize: 14 // 调小主标题字体大小
    },
    subtextStyle: {
      fontSize: 12 // 调小副标题字体大小
    }
    // padding: [10, 20, 30, 20] // 让标题和图表之间有更大间距
  },
  grid: {
    top: "25%" // 让图表整体往下移动
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['2021年秋', '2022年春', '2022年秋', '2023年春', '2023年秋']
  },
  yAxis: {
    type: 'value',
    name: '成绩'
  },
  series: [
    {
      data: [78, 85, 92, 88, 90],
      type: 'line',
      smooth: true,
      lineStyle: {
        width: 2
      }
    }
  ]
})
const chartOptionRadar = ref({
  "title": {
    "text": "班级平均分统计",
    "subtext": "班级各科目平均成绩",
    "left": "center",
    textStyle: {
      fontSize: 14 // 调小主标题字体大小
    },
    subtextStyle: {
      fontSize: 12 // 调小副标题字体大小
    }
  },
  "tooltip": {
    "trigger": "item"
  },
  "radar": {
    center: ["50%", "60%"], // 下移雷达图
    "indicator": [
      {"name": "数学", "max": 100},
      {"name": "英语", "max": 100},
      {"name": "物理", "max": 100},
      {"name": "化学", "max": 100},
      {"name": "生物", "max": 100}
    ]
  },
  "series": [
    {
      "name": "班级平均成绩",
      "type": "radar",
      "data": [
        {
          "value": [100, 80, 95, 88, 68],
          "name": "班级1"
        },
        {
          "value": [85, 50, 85, 28, 98],
          "name": "班级2"
        },
        {
          "value": [45, 80, 65, 88, 28],
          "name": "班级3"
        }
      ]
    }
  ]
})
const chartOptionBar = ref({
  "title": {
    "text": "班级成绩分布",
    "subtext": "班级成绩的分布",
    "left": "center",
    textStyle: {
      fontSize: 14 // 调小主标题字体大小
    },
    subtextStyle: {
      fontSize: 12 // 调小副标题字体大小
    }
  },
  grid: {
    top: "25%" // 让图表整体往下移动
  },
  "tooltip": {
    "trigger": "axis"
  },
  "xAxis": {
    "type": "category",
    "data": ["0-59", "60-69", "70-79", "80-89", "90-100"]
  },
  "yAxis": {
    "type": "value",
    "name": "人数"
  },
  "series": [
    {
      "data": [5, 15, 20, 12, 8],
      "type": "bar"
    }
  ]
})
const chartOptionKcBar = ref({
    "title": {
      "text": "课程平均分统计",
      "subtext": "不同课程的平均成绩",
      "left": "center",
      textStyle: {
        fontSize: 14 // 调小主标题字体大小
      },
      subtextStyle: {
        fontSize: 12 // 调小副标题字体大小
      }
    },
    grid: {
      top: "25%" // 让图表整体往下移动
    },
    "tooltip": {
      "trigger": "axis"
    },
    "xAxis": {
      "type": "category",
      "data": ["数学", "英语", "物理", "化学", "生物"]
    },
    "yAxis": {
      "type": "value",
      "name": "平均分"
    },
    "series": [
      {
        "data": [75, 80, 85, 90, 78],
        "type": "bar"
      }
    ]
  }
)
</script>
