<template>
  <ele-card
    header="数据总揽"
    :shadow="shadow"
    :header-style="{ paddingTop: 0, paddingBottom: 0 }"
    :body-style="{ padding: '3px 8px 0 8px!important' }"
  >
    <template #extra>
      <div class="hidden-xs-only" style="display: flex; align-items: center">
        <!--        <el-radio-group-->
        <!--          v-model="saleSearch.dateType"-->
        <!--          @change="handleDateTypeChange"-->
        <!--        >-->
        <!--          <el-radio-button value="1" label="今天"/>-->
        <!--          <el-radio-button value="2" label="本周"/>-->
        <!--          <el-radio-button value="3" label="本月"/>-->
        <!--          <el-radio-button value="4" label="本年"/>-->
        <!--        </el-radio-group>-->
        <ele-text>测评问卷:</ele-text>
        <div class="hidden-md-and-down" style="width: 320px; margin-left: 12px">
          <el-select
            v-model="configId"
            placeholder="请选择测评问卷"
            @change="selectCpnfChange"
          >
            <el-option
              v-for="item in cpnfOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!--        <div class="hidden-md-and-down" style="width: 320px; margin-left: 12px">-->
        <!--          <el-date-picker-->
        <!--            unlink-panels-->
        <!--            type="datetimerange"-->
        <!--            v-model="saleSearch.datetime"-->
        <!--            range-separator="-"-->
        <!--            format="YYYY-MM-DD HH:mm"-->
        <!--            value-format="YYYY-MM-DD HH:mm"-->
        <!--            start-placeholder="开始时间"-->
        <!--            end-placeholder="结束时间"-->
        <!--            class="ele-fluid"-->
        <!--          />-->
        <!--        </div>-->
      </div>
    </template>

    <statistics-card />
    <!--    <div class="top-search">-->
    <!--      <el-input v-model="keyword" placeholder="请输入内容"/>-->
    <!--      <el-button type="primary" @click="handleSearch">搜索</el-button>-->
    <!--    </div>-->
  </ele-card>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import StatisticsCard from '@/views/evaluate/analysis/components/statistics-card.vue';
  // import { getEvaluateConfig } from '@/views/evaluate/evaluate-config/api/index.js';
  import { queryFdycpSurveyPage } from '../../teacher-survey/api/index.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    shadow: String,
    currentData: Object,
    setEnterType: String,
    setConfigId: String,
    RandomString: String
  });

  const emit = defineEmits(['search']);

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push, replace } = useRouter();
  const { params, path, query } = unref(currentRoute);

  const enterType = ref(null);
  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];
  let currentConfigId = query?.configId ?? props.setConfigId;
  enterType.value = query?.enterType ?? props.setEnterType;

  const keyword = ref('');

  const handleSearch = () => {
    emit('search', keyword.value);
  };
  /** 销售量搜索参数 */
  const saleSearch = reactive({
    type: 'saleroom',
    dateType: '2',
    datetime: void 0
  });
  /** 日期单选切换事件 */
  const handleDateTypeChange = (dateType) => {
    const type = ['day', 'week', 'month', 'year'][Number(dateType) - 1];
    const now = dayjs();
    const formatter = 'YYYY-MM-DD HH:mm';
    const start = now.startOf(type).format(formatter);
    const end = now.endOf(type).format(formatter);
    saleSearch.datetime = [start, end];
  };
  const cpnfOptions = ref([]);
  const configId = ref(null);

  const currentEvaluateConfigData = ref(null);
  const selectCpnfChange = (event) => {
    if (event) {
      configId.value = event;
      currentEvaluateConfigData.value = null;
      // if (configId.value) queryEvaluateConfigById(configId.value)
    }
  };
  const queryEvaluateConfig = async () => {
    const rData = await queryFdycpSurveyPage({
      type: routeType,
      page: 1,
      limit: 100
    });
    let rArray = [];
    if (rData.list) {
      let obj = {};
      rData.list.forEach((d) => {
        obj = {
          value: d.id,
          label: d.name
        };
        rArray.push(obj);
      });
      if (rArray.length > 0) {
        configId.value = currentConfigId ? currentConfigId : rArray[0].value;
        // if (configId.value) queryEvaluateConfigById(configId.value)
        // queryEvaluateItem();
      }
    }
    cpnfOptions.value = rArray;
  };
  queryEvaluateConfig();
</script>

<style lang="scss" scoped>
  .top-search {
    max-width: 480px;
    margin: 8px auto;
    display: flex;

    //:deep(.el-input__wrapper) {
    //  border-top-right-radius: 0;
    //  border-bottom-right-radius: 0;
    //}
    //
    //.el-button {
    //  margin-left: -1px;
    //  position: relative;
    //  border-top-left-radius: 0;
    //  border-bottom-left-radius: 0;
    //}
  }
</style>
