<template>
  <ele-page>
    <services-card />
    <div class="top-search-container">
      <top-search />
    </div>
    <approval-card />
    <el-row :gutter="8">
      <el-col :md="12" :sm="14" :xs="24">
        <visit-charts header="班级成绩分布" type="bar" />
      </el-col>
      <el-col :md="12" :sm="10" :xs="24">
        <visit-charts header="班级平均分雷达图" type="radar" />
      </el-col>
    </el-row>
    <el-row :gutter="8">
      <el-col :md="12" :sm="14" :xs="24">
        <visit-charts header="学生成绩趋势" type="line" />
      </el-col>
      <el-col :md="12" :sm="10" :xs="24">
        <visit-charts header="课程平均分柱状图" type="kcbar" />
      </el-col>
    </el-row>
  </ele-page>
</template>

<script setup>
  import VisitCharts from './components/visit-charts.vue';
  import TopSearch from './components/top-search.vue';
  import ServicesCard from './components/services-card.vue';
  import ApprovalCard from './components/approval-card.vue';

  defineOptions({ name: 'EvaluateAnalysis' });
</script>

<!--<style scoped>-->
<!--//.top-search-container {-->
<!--//  position: fixed;-->
<!--//  top: 0;-->
<!--//  left: 0;-->
<!--//  width: 100%;-->
<!--//  //z-index: 1000;-->
<!--//  background: white;-->
<!--//  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);-->
<!--//  padding: 10px 20px;-->
<!--//}-->
<!--//-->
<!--//ele-page {-->
<!--//  padding-top: 160px; /* 预留空间，避免内容被遮挡 */-->
<!--//}-->
<!--</style>-->
