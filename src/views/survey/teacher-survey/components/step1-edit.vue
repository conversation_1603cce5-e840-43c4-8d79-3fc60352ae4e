<template>
  <ele-drawer
    size="43%"
    :title="readonlyVal ? '问卷详情' : isUpdate ? '编辑问卷' : '新建问卷'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      size="small"
      :model="form"
      :rules="rules"
      labelWidth="96"
    >
      <el-row :gutter="4">
        <template v-for="item in formItems" :key="item.prop">
          <el-col :span="item.colProps">
            <el-form-item
              v-if="item.prop === 'itemId'"
              :label="item.label"
              :prop="item.prop"
            >
              <dict-data
                :placeholder="'请选择' + item.label"
                code="fdycpzb"
                type="select"
                :dicQueryParams="{
                  dictFieldUrl: '/teacherEvaluate/fdycp-item',
                  valueField: 'id',
                  textField: 'khpc'
                }"
                :model-value="form[item.prop]"
                @update:modelValue="
                  (value) => updateFormValue(item, item.prop, value)
                "
              />
            </el-form-item>
            <ProFormItem
              v-else
              :item="item"
              :model="form"
              @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)
              "
            >
              <template
                v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(
                      k
                    )
                )"
                #[name]="slotProps"
              >
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <el-form
      v-if="form['itemId'] && form['item']"
      size="small"
      :model="form['item']"
      labelWidth="96"
    >
      <el-row :gutter="4">
        <template v-for="item in evaluateItemsFileds">
          <el-col :span="item.colProps">
            <el-form-item :label="item.label">
              {{ form['item'][item.prop] }}
            </el-form-item>
          </el-col>
        </template>
      </el-row>
      <el-form-item label="备注">
        <el-input
          :rows="6"
          type="textarea"
          show-word-limit
          maxlength="255"
          v-model="form.bz"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <!-- 底部工具栏 -->
    <template #footer>
      <template v-if="readonlyVal">
        <el-button size="small" @click="updateModelValue(false)"
          >关闭</el-button
        >
      </template>
      <template v-else>
        <el-button size="small" @click="updateModelValue(false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="submit('design')" plain
          >保存并进入问卷设计
        </el-button>
        <el-button
          size="small"
          type="primary"
          plain
          :loading="loading"
          @click="submit"
        >
          保存
        </el-button>
      </template>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { ElMessage as EleMessage } from 'element-plus';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data.js';
  import { editFileds } from '@/views/survey/teacher-evaluate/js/fileds.js';
  import { operationFdycpSurvey } from '@/views/survey/teacher-survey/api/index.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    paperId: String,
    userType: String,
    routeType: String,
    readonlyVal: Boolean, //是否只读
    /** 修改回显的数据 */
    data: Object
  });

  const userStore = useUserStore();
  useDictData(['fdycpzb'], {
    dictFieldUrl: '/teacherEvaluate/fdycp-item',
    valueField: 'id',
    textField: 'khpc'
  });
  useDictData(['listRoles']);
  const { dicts } = storeToRefs(userStore);

  /** 提交状态 */
  const loading = ref(false);

  /** 是否是修改 */
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref(null);

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入问卷名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    itemId: [
      {
        required: true,
        message: '请选择考核方式',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    name: '',
    dcfs: '',
    type: props.routeType,
    item: {},
    itemId: '',
    bz: ''
  });

  const updateFormValue = (item, prop, newVal) => {
    form[item.prop] = newVal;
    if (prop === 'itemId') {
      let fdycpzbValues = dicts.value['fdycpzb'].find(
        (item) => item.id === newVal
      );
      console.log('fdycpzbValues :>> ', fdycpzbValues);
      if (fdycpzbValues) {
        let djrjsData = dicts.value['listRoles'].find(
          (item) => item.dictDataCode === fdycpzbValues['djrjs']
        );
        let resultObj = { ...fdycpzbValues };
        delete resultObj.id;
        setFieldValue('item', {
          ...resultObj,
          djrjs: djrjsData.dictDataName,
          cpdxArray: fdycpzbValues.cpdx,
          cpdxjsidArray: fdycpzbValues.cpdxjs
        });
      }
    }
  };

  /** 表单项 */
  const evaluateItemsFileds = computed(() => {
    let baseFileds = editFileds();
    baseFileds.forEach((filed) => {
      if (
        ['dictSelect', 'select', 'switch', 'radioButton'].includes(filed.type)
      ) {
        filed['props'] = {
          readonly: true,
          disabled: true
        };
      } else {
        filed['props'] = {
          readonly: true
        };
      }
    });
    return baseFileds.slice(0, -1);
  });

  const formItems = computed(() => {
    let baseFileds = [
      { prop: 'name', label: '问卷名称', type: 'input', required: true },
      {
        prop: 'dcfs',
        label: '调查方式',
        type: 'radioButton',
        options: [
          { label: '实名', value: '实名' },
          { label: '匿名', value: '匿名' }
        ],
        required: true
      },
      {
        prop: 'itemId',
        label: '考核方式',
        type: 'dictSelect',
        // typeKey: 'select',
        // props: {
        //   code: "fdycpzb",
        //   filterable: true,
        //   dicQueryParams: {
        //     dictFieldUrl: "/teacherEvaluate/fdycp-item",
        //     valueField: "id",
        //     textField: "khpc"
        //   }
        // },
        required: true
      }
    ];
    baseFileds.forEach((filed) => {
      if (props.readonlyVal) {
        if (
          ['dictSelect', 'select', 'switch', 'radioButton'].includes(filed.type)
        ) {
          filed['props'] = {
            readonly: props.readonlyVal,
            disabled: props.readonlyVal
          };
        } else {
          filed['props'] = {
            readonly: props.readonlyVal
            // "disabled": props.enterType === 'preview',
          };
        }
      }
    });
    return baseFileds;
  });

  /** 步骤一提交 */
  const submit = (type) => {
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operationFdycpSurvey(form)
        .then((data) => {
          loading.value = false;
          if (data) {
            EleMessage.success(
              isUpdate.value ? '问卷维护成功' : '问卷创建成功'
            );
            updateModelValue(false);
            data.submitType = type;
            console.log(data);
            emit('done', data);
          }
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          if (props.data.itemId) {
            let fdycpzbValues = dicts.value['fdycpzb'].find(
              (item) => item.id === props.data.itemId
            );
            if (fdycpzbValues) {
              let djrjsData = dicts.value['listRoles'].find(
                (item) => item.dictDataCode === fdycpzbValues['djrjs']
              );
              setFieldValue('item', {
                ...fdycpzbValues,
                djrjs: djrjsData.dictDataName,
                cpdxArray: fdycpzbValues.cpdx,
                cpdxjsidArray: fdycpzbValues.cpdxjs
              });
            }
          }
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
