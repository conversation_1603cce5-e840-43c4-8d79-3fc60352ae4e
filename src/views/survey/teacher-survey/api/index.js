/**
 * 成绩等级控制器
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getFdycpSurvey(params) {
  const res = await request.get('/teacherEvaluate/fdycp-survey', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryFdycpSurveyPage(params) {
  const res = await request.get('/teacherEvaluate/fdycp-survey/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFdycpSurveyById(id) {
  const res = await request.get('/teacherEvaluate/fdycp-survey/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取问卷答卷详情（权限标识：teacherEvaluate:survey:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function getFdycpSurveyVO(params) {
  const res = await request.get('/teacherEvaluate/fdycp-survey/getSurveyVO', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 答卷人分页查询可答问卷（权限标识：teacherEvaluate:surveyAnswer:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function querySurveys(params) {
  const res = await request.get('/teacherEvaluate/fdycp-survey/querySurveys', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operationFdycpSurvey(data) {
  const res = await request.post(
    '/teacherEvaluate/fdycp-survey/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removesFdycpSurvey(data) {
  const res = await request.post('/teacherEvaluate/fdycp-survey/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operationFdycpSurveyState(data) {
  const res = await request.post('/teacherEvaluate/fdycp-survey/state', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
