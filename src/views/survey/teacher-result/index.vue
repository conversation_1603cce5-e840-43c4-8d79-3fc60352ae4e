<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search
          @search="searchReload"
          ref="searchRef"
          @survey-items="getSurveyItems"
        />
      </template>
      <!-- 表格  loadOnCreated：默认不请求数据，等待search中的年份被赋值后再进行请求-->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :loadOnCreated="false"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="summaryResult()"
          >
            汇总测评结果
          </el-button>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="exportData()"
          >
            导出
          </el-button>
          <el-button size="small" plain class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <!-- 指标项点击跳转至包含该指标的问卷列表页 -->
        <template
          v-for="survey in surveyItems"
          #[survey.itemId]="{ row }"
          :key="survey.itemId"
        >
          <div class="link-text" @click="toTeacherSurvey(row, survey)">{{
            row[survey.itemId]
          }}</div>
        </template>
        <template #action="{ row }">
          <!-- <el-link
            type="primary"
            underline="never"
            @click="handlePaperPerview(row)"
          >
            预览
          </el-link> -->
          <!-- <template v-if="row.answered"> -->
          <!-- <template v-if="!row.answered">
            <el-divider direction="vertical" />
            <el-link
              type="primary"
              underline="never"
              @click="openAnswerList(row)"
            >
              答题记录
            </el-link>
          </template> -->
          <!-- <template v-if="row.status === '启用' && !row.answered">
            <el-divider direction="vertical" />
            <el-link
              type="primary"
              underline="never"
              @click="summaryResult(row)"
            >
              编辑
            </el-link>
            <el-divider direction="vertical" />
            <el-link
              type="primary"
              underline="never"
              @click="openStep2Edit(row)"
            >
              问卷设计
            </el-link>
          </template>
          <el-divider v-if="row.status === '结束'" direction="vertical" />
          <el-link
            v-if="row.status === '结束'"
            type="primary"
            underline="never"
            @click="openDataTongJi(row)"
          >
            数据统计
          </el-link> -->
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { nextTick, onMounted, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage } from 'element-plus';
  import {
    operationFdycpResult,
    getFdycpResultPage,
    removesFdycpResult,
    getFdycpResultSurveyItems
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { ElLoading } from 'element-plus';
  import Search from './components/search.vue';
  import { formMateSearchDataToStr } from '@/utils/common.js';
  import { getToken } from '@/utils/token-util';
  import { usePageTab } from '@/utils/use-page-tab.js';
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  const surveyItems = ref([]); // 测评指标

  let pathArray = path.split('/');
  console.log(pathArray);
  let userType = pathArray[3];
  let routeType = pathArray[4];

  /** 表格实例 */
  const tableRef = ref(null);
  /** 检索条件 */
  const searchRef = ref(null);

  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});

  /** 加载状态 */
  const loading = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'nf',
      label: '年份',
      minWidth: 110
    },
    { prop: 'bcpr', label: '工号', minWidth: 110 },
    { prop: 'bcprxm', label: '姓名' },
    // { prop: 'bdcrdw', label: '学院' },
    // { prop: 'dtrscope', label: '答题人角色' },
    { prop: 'score', label: '测评总分' },
    { prop: 'rank', label: '排名' },
    { prop: 'grade', label: '等级' }
    // {
    //   columnKey: 'action',
    //   label: '操作',
    //   slot: 'action',
    //   minWidth: 160
    // }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getFdycpResultPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType
    });
    res.list.map((item) => {
      if (item.scoreDetail) {
        let scoreDetail = JSON.parse(item.scoreDetail);
        Object.entries(scoreDetail).map(([key, value]) => {
          item[key] = value;
        });
      }
    });
    console.log('datasource :>> ', res);
    return {
      list: res.list,
      count: res.count
    };
  };

  const searchReload = (where) => {
    searchWhere.value = where;
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const summaryResult = () => {
    const where = searchRef.value.initModel;
    let data = { ...where };
    operationFdycpResult(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        searchReload(data);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  const exportData = () => {
    const where = searchRef.value.initModel;
    loading.value = true;
    let searchStr = formMateSearchDataToStr(where);
    console.log('searchStr :>> ', searchStr);
    setTimeout(() => {
      window.location.href =
        BASE_URL +
        'api/teacherEvaluate/fdycp-result/export?access_token=' +
        accessToken +
        searchStr;
      loading.value = false;
    }, 3500);
  };
  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.bcprxm).join(', ') + '”的汇总结果吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesFdycpResult(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
  const getSurveyItems = async (where) => {
    let data = { nf: where.nf };
    const res = await getFdycpResultSurveyItems(data);
    console.log('res', res);
    if (res.length > 0 && surveyItems.value.length == 0) {
      res.map((item) => {
        item.prop = item.itemId;
        item.label = item.khpc;
        item.slot = item.itemId;
      });
      surveyItems.value = res;
      columns.value.splice(4, 0, ...res);
    }
  };
  /**
   * 跳转至问卷列表
   */
  const toTeacherSurvey = (row, survey) => {
    console.log('row :>> ', row);
    removePageTab({ key: getRouteTabKey() });
    push({
      path:
        '/survey/teacher-result/surveyList/' +
        userType +
        '/' +
        routeType +
        '/' +
        row.nf +
        '/' +
        row.bcpr +
        '/' +
        survey.itemId,
      query: { cpzb: survey.label, bcprxm: row.bcprxm }
    });
  };
</script>

<script>
  export default {
    name: 'TEACHERRESULTINDEX'
  };
</script>
<style lang="scss" scoped>
  .link-text {
    color: var(--el-color-primary);
    cursor: pointer;
  }
</style>
