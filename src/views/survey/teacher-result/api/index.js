/**
 * 辅导员测评汇总结果-管理端
 */
import request from '@/utils/request.js';

/**
 * 查询全部辅导员测评结果（不分页） 管理端
 */
export async function getFdycpResult(params) {
  const res = await request.get('/teacherEvaluate/fdycp-result', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询辅导员测评结果 管理端
 */
export async function getFdycpResultPage(params) {
  const res = await request.get('/teacherEvaluate/fdycp-result/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询辅导员测评结果 管理端
 */
export async function getFdycpResultById(id) {
  const res = await request.get('/teacherEvaluate/fdycp-result/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 辅导员测评结果汇总 管理端
 */
export async function operationFdycpResult(data) {
  const res = await request.post(
    '/teacherEvaluate/fdycp-result/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除辅导员测评结果汇总 管理端
 */
export async function removesFdycpResult(data) {
  const res = await request.post('/teacherEvaluate/fdycp-result/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 被测评人参与者信息统计 管理端
 */
export async function queryAnswererInfoCount(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-result/answererInfoCount',
    {
      params
    }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 被测评人答题者分数情况汇总 管理端
 * @param params
 * @returns {Promise<*>}
 */
export async function getAnswererScoreSummary(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-result/answererScoreSummary',
    {
      params
    }
  );
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导出 管理端
 */
export async function getFdycpResultExport(params) {
  const res = await request.get('/teacherEvaluate/fdycp-result/export', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取汇总结果指标
 */
export async function getFdycpResultSurveyItems(params) {
  const res = await request.get('/teacherEvaluate/fdycp-result/surveyItems', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取包含该指标的试卷列表
 *nf=&bcpr=&itemId=
 */
export async function getFdycpResultAnswererInfoCount(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-result/answererInfoCount',
    {
      params
    }
  );
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取某试卷的所有答题记录列表
 *surveyId
 */
export async function getFdycpResultPageAnswerInfo(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-result/pageAnswerInfo',
    {
      params
    }
  );
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据答卷id查询答题结果
 */
export async function getFdycpResultSurveyAnswer(params) {
  const res = await request.get('teacherEvaluate/fdycp-result/surveyAnswer', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
