<!-- 试卷列表 -->
<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <div
            style="display: flex; align-items: center; margin-right: 5px"
            @click="onBack()"
          >
            <IconPark name="return" size="18" strokeWidth="3" />
            <ele-text
              style="padding: 2px; cursor: pointer"
              type="secondary"
              size="sm"
            >
              返回
            </ele-text>
          </div>
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click="handlePaperAnswerList(row)"
          >
            答题记录
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { getFdycpResultAnswererInfoCount } from '@/views/survey/teacher-result/api/index.js';
  import IconPark from '@/components/IconPark/index.vue';

  const { removePageTab, getRouteTabKey, setPageTab, setPageTabTitle } =
    usePageTab();
  const { currentRoute, push } = useRouter();
  const { path, query } = unref(currentRoute);
  if (query.cpzb)
    setPageTabTitle(query.bcprxm + '[' + query.cpzb + ']问卷列表');

  /** 表格实例 */
  const tableRef = ref(null);
  let pathArray = path.split('/');
  console.log(pathArray);
  const userType = pathArray[4];
  const routeType = pathArray[5];
  let nf = pathArray[6];
  let bcpr = pathArray[7];
  let surveyItemId = pathArray[8]; // 测评指标id

  /** 跳转试卷答题详情页 */
  const handlePaperAnswerList = (row) => {
    removePageTab({ key: getRouteTabKey() });
    push({
      path:
        '/survey/teacher-result/details/' +
        userType +
        '/' +
        routeType +
        '/' +
        nf +
        '/' +
        bcpr +
        '/' +
        surveyItemId +
        '/' +
        row.sid,
      query: { currentKsmcc: row.name, ...query }
    });
  };

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'name',
      label: '问卷名称'
    },
    {
      prop: 'createDate',
      label: '发布时间'
    },
    {
      prop: 'ycprs',
      label: '应测评人数'
    },
    {
      prop: 'sjcprs',
      label: '实际测评人数'
    },
    {
      prop: 'sjcprszb',
      label: '比例'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 150,
      slot: 'action'
    }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getFdycpResultAnswererInfoCount({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      nf,
      bcpr,
      itemId: surveyItemId
    });
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    push({
      path: '/survey/teacher-result/' + userType + '/' + routeType
    });
  };
</script>

<script>
  export default {
    name: 'SRUVEYDETAILSINDEX'
  };
</script>
