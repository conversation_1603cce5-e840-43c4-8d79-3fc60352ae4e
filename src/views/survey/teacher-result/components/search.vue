<template>
  <el-form
    ref="formRef"
    :model="initModel"
    label-position="left"
    size="small"
    @submit.prevent=""
  >
    <el-row :gutter="8">
      <template v-for="(item, index) in initItems">
        <template v-if="index > 2">
          <el-col
            v-if="searchExpand"
            :lg="6"
            :md="12"
            :sm="12"
            :xs="12"
            :key="item.key"
          >
            <ProFormItem
              v-if="searchExpand"
              :item="item"
              :model="initModel"
              @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)
              "
            >
              <template
                v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(
                      k
                    )
                )"
                #[name]="slotProps"
              >
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
        <el-col v-else :lg="6" :md="12" :sm="12" :xs="12" :key="item.key">
          <ProFormItem
            :item="item"
            :model="initModel"
            @updateItemValue="
              (prop, value) => updateFormValue(item, prop, value)
            "
          >
            <template
              v-for="name in Object.keys($slots).filter(
                (k) =>
                  !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
              )"
              #[name]="slotProps"
            >
              <slot :name="name" v-bind="slotProps || {}"></slot>
            </template>
          </ProFormItem>
        </el-col>
      </template>
      <el-col :lg="6" :md="12" :sm="12" :xs="12">
        <el-form-item>
          <div
            :style="{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: searchExpand ? 'flex-end' : 'flex-start'
            }"
          >
            <el-button type="primary" size="small" plain @click="search"
              >查询
            </el-button>
            <el-button size="small" @click="reset">重置</el-button>
            <!--              @click="onExpend"-->
            <el-link
              type="primary"
              underline="never"
              @click="searchExpand = !searchExpand"
              style="margin-left: 10px"
            >
              <template v-if="searchExpand">
                <span>收起</span>
                <el-icon style="vertical-align: -1px">
                  <ArrowUp />
                </el-icon>
              </template>
              <template v-else>
                <span>展开</span>
                <el-icon style="vertical-align: -2px">
                  <ArrowDown />
                </el-icon>
              </template>
            </el-link>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { ref, nextTick, reactive, watch, onMounted } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const emit = defineEmits(['search', 'survey-items']);

  // 已缓存的字典
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  const searchExpand = ref(false);

  const formRef = ref();

  /** 表单项 */
  const initItems = ref([
    {
      key: 'fieldKey_0',
      label: '年份',
      prop: 'nf',
      type: 'dictSelect',
      required: false,
      showFlag: '是',
      fieldLinks: null,
      props: {
        code: 'nf',
        filterable: true,
        refresh: true,
        dicQueryParams: {
          dictFieldUrl: '/teacherEvaluate/fdycp-result/years',
          valueField: 'name',
          textField: 'name'
        }
      }
    },
    {
      key: 'fieldKey_1',
      label: '工号',
      prop: 'bcpr',
      type: 'input',
      required: false,
      showFlag: '是',
      fieldLinks: null
    },
    {
      key: 'fieldKey_2',
      label: '姓名',
      prop: 'bcprxm',
      type: 'input',
      required: false,
      showFlag: '是',
      fieldLinks: null
    },
    {
      key: 'fieldKey_3',
      label: '学院',
      prop: 'bdcrdw',
      type: 'dictSelect',
      required: false,
      showFlag: '是',
      fieldLinks: null,
      // selfFieldLink: 'xymc',
      // nextField: 'zymc',
      props: {
        code: 'other',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: '/code/codeDwb',
          valueField: 'id',
          textField: 'name'
        }
      }
    }
  ]);
  /** 获取表单字段 */
  const initModel = ref({ nf: '', bcpr: '', bcprxm: '', bdcrdw: '' });
  const rules = reactive({});
  /** 表单数据 */
  const [form, resetFields, setFieldValue] = useFormData(initModel);

  /** 更新表单数据 */
  const updateFormValue = (item, prop, value) => {
    initModel.value[item.prop] = value;
  };

  const reset = () => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
      formRef.value?.resetFields?.();
      initModel.value = {};
      if (dicts.value['nf'] && dicts.value['nf'].length > 0) {
        initModel.value['nf'] = dicts.value['nf'][0].name;
      }
      search();
    });
  };
  const search = () => emit('search', { ...initModel.value });

  watch(
    () => dicts.value['nf'],
    (newVal) => {
      if (newVal && newVal.length > 0) {
        initModel.value['nf'] = newVal[0].name; // 年份默认选中第一个
        search();
        emit('survey-items', { nf: initModel.value['nf'] });
      }
    }
  );

  defineExpose({ initModel });
</script>
