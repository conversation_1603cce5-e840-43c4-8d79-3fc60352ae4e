<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <div
            style="display: flex; align-items: center; margin-right: 5px"
            @click="onBack()"
          >
            <IconPark name="return" size="18" strokeWidth="3" />
            <ele-text
              style="padding: 2px; cursor: pointer"
              type="secondary"
              size="sm"
            >
              返回
            </ele-text>
          </div>
        </template>
        <template #xm="{ row }"> 匿名用户 </template>
        <template #xgh="{ row }"> ******** </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click="handlePaperAnswerList(row)"
          >
            答题详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview
      v-model="showPaperPerview"
      :paperId="ksid"
      enterType="perviewAlreadyMarkedFdycpResult"
      :perviewParams="perviewParams"
      :RandomString="RandomString"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import PaperPerview from '@/views/exam/components/paper-preview.vue';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { getFdycpResultPageAnswerInfo } from '../api/index.js';
  import IconPark from '@/components/IconPark/index.vue';

  const { removePageTab, getRouteTabKey, setPageTab, setPageTabTitle } =
    usePageTab();
  const { currentRoute, push } = useRouter();
  const { path, query } = unref(currentRoute);

  if (query.currentKsmcc) setPageTabTitle(query.currentKsmcc + '答题记录');

  /** 表格实例 */
  const tableRef = ref(null);
  const showPaperPerview = ref(false);
  let ksid = ref(null);
  let perviewParams = ref({});
  const RandomString = ref(null);
  let pathArray = path.split('/');
  console.log(pathArray);
  const userType = pathArray[4];
  const routeType = pathArray[5];
  const nf = pathArray[6];
  const bcpr = pathArray[7];
  const surveyItemId = pathArray[8];
  const sid = pathArray[9];
  /** 加载状态 */
  const loading = ref(false);

  /** 试卷答题 */
  const handlePaperAnswerList = (row) => {
    console.log('row :>> ', row);
    RandomString.value = generateRandomString(10);
    console.log(row);
    ksid.value = row.sid;
    perviewParams.value = {
      id: row.id, //答题ID
      sid: row.sid, //问卷ID
      xgh: row.xgh, //学工号
      bcpr: row.bcpr, //被测评人
      surveyItemId: row.surveyItemId //测评指标ID
    };
    showPaperPerview.value = true;
  };

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'xgh',
      slot: 'xgh',
      label: '学工号'
    },
    {
      prop: 'xm',
      slot: 'xm',
      label: '答题人'
    },
    {
      prop: 'bcprxm',
      label: '被测评人'
    },
    {
      prop: 'score',
      label: '测评分数'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 150,
      slot: 'action'
    }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getFdycpResultPageAnswerInfo({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      // surveyItemId,
      sid,
      bcpr
    });
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    push({
      path:
        '/survey/teacher-result/surveyList/' +
        userType +
        '/' +
        routeType +
        '/' +
        nf +
        '/' +
        bcpr +
        '/' +
        surveyItemId,
      query: query
    });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'FDYCPRESULTDETAILSINDEX'
  };
</script>
