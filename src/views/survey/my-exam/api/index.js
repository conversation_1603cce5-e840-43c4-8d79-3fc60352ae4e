import request from '@/utils/request';

/**
 * 分页查询
 */
export async function querySurveys(params) {
    const res = await request.get('/survey/survey/querySurveys', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
export async function querySurveyAnswerInfoPage(params) {
    const res = await request.get('/survey/survey-answer-info/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function getSurveyAnswerInfo(params) {
    const res = await request.get('/survey/survey-answer-info/getSurveyAnswerInfo',{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 答题
 */
export async function operation(data) {
    const res = await request.post('/exam/exam-answer/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
