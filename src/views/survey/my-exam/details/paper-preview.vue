<!--试卷预览-->
<template>
  <ele-drawer size="75%"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="false"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0px 0px 5px 0px !important' ,background: 'rgb(236 236 236)',height: (pageHeight+60)+'px'}"
              @update:modelValue="updateModelValue">
    <template #header="{ close, titleId, titleClass }">
      <span :id="titleId" :class="titleClass" v-if="paperViewData" style="font-size: 16px;">
        {{ paperViewData.name }}
       <span>[共{{ paperViewData?.subjectSum ?? 0 }}题]</span>
    </span>
    </template>
    <ele-page v-if="cardData.length>0" class="workplace-page">
      <ProviderComponent>
        <el-row ref="wrapRef" :gutter="8">
          <el-col v-for="item in cardData"
                  :key="item.name"
                  :md="item.md"
                  :sm="item.sm"
                  :xs="item.xs">
            <template v-if="paperViewData">
              <component v-if="item.name==='exam-group-card'"
                         :is="item.name"
                         :ksId="ksId"
                         :paperId="paperId"
                         :title="item.title"
                         :enterType="enterType"
                         :baseData="paperViewData"
                         @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
                         @autoSumbit="handleUpdateAutoSumbit"
                         @updateUpTimeVal="handleUpdateUpTimeVal"/>
              <component v-else :is="item.name"
                         :ksId="ksId"
                         :paperId="paperId"
                         :title="item.title"
                         :ksms="paperViewData?.ksms"
                         :enterType="enterType"
                         :baseData="paperViewData"
                         @updateAnswerVal="handleUpdateAnswerVal"/>
            </template>
          </el-col>
        </el-row>
      </ProviderComponent>
    </ele-page>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button
        v-if="['toAnswerdDetail','perview','perviewPaper','perviewExamPaper','perviewAlreadyMarked','perviewPaperXlzx'].includes(enterType)"
        size="small"
        @click="updateModelValue(false)">
        关闭
      </el-button>
      <el-button v-else type="primary" plain size="small" @click="handleSumbitAnswer">
        <ele-dot :ripple="true" text=" " size="16px" style="cursor: pointer;"/>
        提 交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {nextTick, onMounted, ref, watch} from 'vue';
import ProviderComponent from '../../components/preview/ProviderComponent.vue';
import ExamCardInfo from '../../components/preview/exam-card-info.vue';
import ExamCardXlzx from '../../components/preview/exam-card-xlzx.vue';
import ExamCard from '../../components/preview/exam-card.vue';
import GoalCard from '../../components/preview/goal-card.vue';
import ExamGroupCard from '../../components/preview/exam-group-card.vue';
import ExamGroupCardXlzx from '../../components/preview/exam-group-card-xlzx.vue';
import { ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from "element-plus";
import {setPageExamAnswerScoring} from "@/views/exam/exam-management/api/index-kscj.js";
import {getSurveyById, getSurveyVO} from "../../test-paper/api/index.js";
import {surveyAnswerOperation} from "@/views/survey/my-exam/api/index-answer.js";
import {getSurveyAnswerInfo} from "@/views/survey/my-exam/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {xljkSurveyOperation} from "@/views/mentalHealth/my-exam/api/index.js";
import {toFormDataWj} from '@/utils/common_bak2.js';
import {getSurveyManageAnswer, getSurveyManageById} from "@/views/mentalHealth/test-paper/api/index.js";

defineOptions({
  name: 'PaperPreview',
  components: {
    ExamGroupCard,
    ExamCardInfo,
    GoalCard,
    ExamCard,
    ExamCardXlzx,
    ExamGroupCardXlzx,
  }
});

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  ksId: String,
  paperId: String,
  pxsid: String,
  RandomString: String,
  enterType: String,//perview：答题详情预览 toAnswer:去答题 perviewPaper:试卷预览  perviewExamPaper:考试试卷预览
  //perviewAlreadyMarked-查看已答题的问卷详情
  perviewParams: Object,//预览查询参数
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const paperData = ref(null);
// const paperAnswerData = ref(null);
const paperViewData = ref(null);

/** 容器 */
const wrapRef = ref(null);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const cardData = ref([
  {"name": "exam-group-card", "title": "答题卡", "md": 8, "sm": 24, "xs": 24},
  {"name": "exam-card", "title": "题", "md": 16, "sm": 24, "xs": 24},
  // {"name": "goal-card", "title": "考试信息", "md": 4, "sm": 24, "xs": 24},
]);
const upTimeVal = ref(null);
const handleUpdateUpTimeVal = (value) => {
  upTimeVal.value = value;
};
const upNoAnswerNumber = ref(null);
const handleUpdateNoAnswerNumber = (value) => {
  upNoAnswerNumber.value = value;
};
/** 加载状态 */
const loading = ref(false);

const answerVals = ref([]);
const handleUpdateAnswerVal = (value) => {
  answerVals.value = []
  answerVals.value = value
}

const handleSumbitGrading = () => {
  ElMessageBox.confirm(`你确定提交阅卷信息？`, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    if (answerVals.value.length > 0) {
      answerVals.value.forEach(chk => {
        if (chk.sttype === "多选题") chk.result = chk.result && chk.result.length > 0 ? chk.result.join(',') : ''
      })
    }
    const result = {
      examId: props.ksId,
      paperId: paperData.value.pid,
      examInfoId: paperData.value.examAnswerInfo.id,
      // usedTime: upTimeVal.value,
      answers: answerVals.value,
    };
    // console.log(JSON.stringify(result))
    loading.value = true;
    setPageExamAnswerScoring(result).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

const handleSumbitAnswer = () => {
  let confirmMsg = `你确定要提交问卷！`
  if (upNoAnswerNumber.value > 0) confirmMsg = `你确定要提交问卷！你还有“${upNoAnswerNumber.value}”题没有答?`
  // let totalTime = paperData.value.uptime * 60;// 总时长（秒）
  // if (upTimeVal.value < totalTime) {
  //   confirmMsg = `考试时间还没到，你确定要提交问卷！`
  // }
  ElMessageBox.confirm(confirmMsg, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    save()
  }).catch(() => {
  });
};

const save = () => {
  console.log(props.paperId)
  console.log(answerVals.value)
  if (answerVals.value.length > 0) {
    answerVals.value.forEach(chk => {
      if (chk.qtype === "多选题") {
        chk.result = chk.result && chk.result.length > 0 ? chk.result.join(',') : ''

      } else if (chk.qtype === "上传题") {
        let fileArray = []
        if (chk.result.length > 0) {
          chk.result.forEach((e) => {
            if (e.status !== 'done') {
              fileArray.push(e)
            }
          })
        }
        // let data = toFormData({attachment: fileArray,})
        // console.log(data)
        chk.result = fileArray
      }
    })
  }
  const result = {
    sid: props.paperId,
    answers: answerVals.value,
  };
  // console.log(JSON.stringify(result))

  loading.value = true;
  if (props.enterType === 'toAnswerXlzx') {
    result.xsid = props.pxsid
    let newObj = toFormDataWj({...result})
    handleXljkSurveyOperation(newObj)
  } else {
    let newObj = toFormDataWj({...result})
    handleSurveyAnswerOperation(newObj)
  }
}

const handleSurveyAnswerOperation = (object) => {
  surveyAnswerOperation(object).then((msg) => {
    loading.value = false;
    EleMessage.success("交卷成功");
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

const handleXljkSurveyOperation = (object) => {
  xljkSurveyOperation(object).then((msg) => {
    loading.value = false;
    EleMessage.success("交卷成功");
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

/**
 * 倒计时结束,系统自动交卷
 */
const handleUpdateAutoSumbit = () => {
  save()
};

/** 试卷预览 查询试卷试题详情*/
const QuerySurveyById = () => {
  if (props.paperId) {
    paperViewData.value = null;
    loading.value = true;
    getSurveyById(props.paperId).then((list) => {
      loading.value = false;
      paperViewData.value = list ?? {};
      console.log(paperViewData.value)
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      // showPaperPerview.value = true;
    });
  }
}


/** 心理咨询-管理端 试卷预览查询试卷试题详情（带答案）*/
const QuerySurveyManageAnswer = () => {
  if (props.paperId) {
    paperViewData.value = null;
    loading.value = true;
    getSurveyManageAnswer({sid: props.paperId, xsid: props.pxsid}).then((list) => {
      loading.value = false;
      paperViewData.value = list ?? {};
      console.log(paperViewData.value)
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      // showPaperPerview.value = true;
    });
  }
}
/** 心理咨询-管理端 试卷预览查询试卷试题详情*/
const QuerySurveyManage = () => {
  if (props.paperId) {
    paperViewData.value = null;
    loading.value = true;
    getSurveyManageById(props.paperId).then((list) => {
      loading.value = false;
      paperViewData.value = list ?? {};
      console.log(paperViewData.value)
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      // showPaperPerview.value = true;
    });
  }
}

/** 试卷预览 查询试卷试题详情*/
const QuerySurveyVO = () => {
  paperViewData.value = null;
  loading.value = true;
  let queryData = {}
  if (['perviewAlreadyMarked'].includes(props.enterType)) {
    queryData = props.perviewParams;
  }
  getSurveyVO(queryData).then((list) => {
    loading.value = false;
    paperData.value = list;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
  });
}
/** 试卷预览 查询试卷试题详情*/
const QuerySurveyAnswerInfo = () => {
  paperViewData.value = null;
  loading.value = true;
  getSurveyAnswerInfo({sid: props.paperId}).then((list) => {
    console.log(list)
    loading.value = false;
    // paperData.value = list;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
  });
}

watch(
  () => props.RandomString,
  (RandomString) => {
    nextTick(() => {
      let enterType = props.enterType;
      if (enterType) {
        console.log(RandomString, props.paperId)
        console.log("enterType===", enterType)
        // 根据 enterType 设置卡片内容
        if (enterType === "perviewPaperXlzx" || enterType === "toAnswerXlzx") {
          cardData.value = [
            {name: "exam-group-card-xlzx", title: "答题卡", md: 8, sm: 24, xs: 24},
            {name: "exam-card-xlzx", title: "题", md: 16, sm: 24, xs: 24},
          ];
        } else {
          cardData.value = [
            {name: "exam-group-card", title: "答题卡", md: 8, sm: 24, xs: 24},
            {name: "exam-card", title: "题", md: 16, sm: 24, xs: 24},
          ];
        }
        //perview：答题详情预览 toAnswer:去答题 perviewPaper:试卷预览  perviewExamPaper:考试试卷预览
        //perviewAlreadyMarked-查看已答题的问卷详情
        if (enterType === "perviewAlreadyMarked") {
          QuerySurveyVO()
        } else if (enterType === "toAnswerdDetail") {
          QuerySurveyAnswerInfo()
        } else if (["toAnswerXlzx", "perviewPaperXlzx"].includes(enterType)) {
          QuerySurveyManage()
        } else if (["toAnswer", "perviewPaper",].includes(enterType)) {
          QuerySurveyById()
        }
      }
    })
  }
);

</script>

<style>
.span-wrap {
  display: inline-block;
  white-space: pre-wrap; /* 保留空白符序列，但是当到达容器的末端时进行换行 */
}
</style>
