<template>
  <ele-page hide-footer flex-table>
    <ProviderComponent>
      <ele-split-panel ref="splitRef"
                       :allow-collapse="true"
                       :resizable="true"
                       :responsive="true"
                       flex-table
                       space="3px"
                       size="320px"
                       allow-collapse
                       :custom-style="{ borderWidth: '0 1px 0 0'}"
                       :body-style="{height: (pageHeight-58)+'px',overflow:'hidden'}"
                       :style="{ height: '100%', overflow: 'visible'}">
        <ele-card :body-style="{ padding: '7px 8px 6px 8px!important'}" :style="{marginBottom:'5px!important'}">
          <div class="profile-wrapper" v-if="paperViewData">
            <div class="profile-main">
              <div class="profile-body">
                <ele-text size="lg" type="heading" style="font-weight: normal">
                  {{ paperViewData.name }}[共{{ paperViewData?.subjectSum ?? 0 }}题]
                </ele-text>
                <ele-text size="md" type="placeholder">
                  调查方式:{{ paperViewData.dcfs }}
                </ele-text>
              </div>
            </div>
          </div>
        </ele-card>
        <template v-for="item in cardData">
          <component v-if="item.name==='exam-group-card'&&paperViewData"
                     :is="item.name"
                     :paperId="paperId"
                     :title="item.title"
                     :enterType="enterType"
                     :currentPageHeight="pageHeight-104"
                     :baseData="paperViewData"
                     @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
                     @autoSumbit="handleUpdateAutoSumbit"
                     @updateUpTimeVal="handleUpdateUpTimeVal"/>
        </template>
        <template #body>
          <template v-for="item in cardData">
            <component v-if="item.name!=='exam-group-card'&&paperViewData" :is="item.name"
                       :paperId="paperId"
                       :title="item.title"
                       :ksms="paperViewData?.ksms"
                       :enterType="enterType"
                       :currentPageHeight="pageHeight"
                       :baseData="paperViewData"
                       @updateAnswerVal="handleUpdateAnswerVal"/>
          </template>
        </template>
      </ele-split-panel>
    </ProviderComponent>

    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <template #extra>
        <el-button size="small" @click="onBack">取消</el-button>
        <el-button size="small" type="primary" plain :loading="loading" @click="handleSumbitAnswer">
          提交aaa
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
import {nextTick, onMounted, ref, unref, watch} from 'vue';
import ProviderComponent from '../../components/preview/ProviderComponent.vue';
import ExamCardInfo from '../../components/preview/exam-card-info.vue';
import ExamCard from '../../components/preview/exam-card.vue';
import GoalCard from '../../components/preview/goal-card.vue';
import ExamGroupCard from '../../components/preview/exam-group-card.vue';
import { ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from "element-plus";
import {getSurveyById, getSurveyVO} from "../../test-paper/api/index.js";
import {surveyAnswerOperation} from "@/views/survey/my-exam/api/index-answer.js";
import {getSurveyAnswerInfo} from "@/views/survey/my-exam/api/index.js";
import {toFormDataWj} from "@/utils/common_bak2.js";
import {usePageTab} from '@/utils/use-page-tab';
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();

defineOptions({
  name: 'MYEXAMANSWER',
  components: {
    ExamGroupCard,
    ExamCardInfo,
    GoalCard,
    ExamCard,
  }
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push,} = useRouter();
const {params, path, query} = unref(currentRoute);
console.log(params, path, query)
/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);

let pathArray = path.split("/")
console.log(pathArray)
let userType = pathArray[4];
let routeType = pathArray[5];
let paperId = params?.id ?? null
let RandomString = null
let enterType = "toAnswer"

const paperData = ref(null);
// const paperAnswerData = ref(null);
const paperViewData = ref(null);

/** 容器 */
const wrapRef = ref(null);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const cardData = ref([
  {"name": "exam-group-card", "title": "答题卡", "md": 8, "sm": 24, "xs": 24},
  {"name": "exam-card", "title": "题", "md": 16, "sm": 24, "xs": 24},
  // {"name": "goal-card", "title": "考试信息", "md": 4, "sm": 24, "xs": 24},
]);
const upTimeVal = ref(null);
const handleUpdateUpTimeVal = (value) => {
  upTimeVal.value = value;
};
const upNoAnswerNumber = ref(null);
const handleUpdateNoAnswerNumber = (value) => {
  upNoAnswerNumber.value = value;
};
/** 加载状态 */
const loading = ref(false);

const answerVals = ref([]);
const handleUpdateAnswerVal = (value) => {
  answerVals.value = []
  answerVals.value = value
}

const handleSumbitAnswer = () => {
  let confirmMsg = `你确定要提交问卷！`
  if (upNoAnswerNumber.value > 0) confirmMsg = `你确定要提交问卷！你还有“${upNoAnswerNumber.value}”题没有答?`
  // let totalTime = paperData.value.uptime * 60;// 总时长（秒）
  // if (upTimeVal.value < totalTime) {
  //   confirmMsg = `考试时间还没到，你确定要提交问卷！`
  // }
  ElMessageBox.confirm(confirmMsg, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    save()
  }).catch(() => {
  });
};

const save = () => {
  if (answerVals.value.length > 0) {
    answerVals.value.forEach(chk => {
      if (chk.qtype === "多选题") {
        chk.result = chk.result && chk.result.length > 0 ? chk.result.join(',') : ''
      } else if (chk.qtype === "上传题") {
        let fileArray = []
        if (chk.result.length > 0) {
          chk.result.forEach((e) => {
            if (e.status !== 'done') {
              fileArray.push(e)
            }
          })
        }
        chk.result = fileArray
      }
    })
  }
  const result = {
    sid: paperId,
    answers: answerVals.value,
  };
  console.log(JSON.stringify(result))
  let rdata = toFormDataWj({...result})
  // loading.value = true;
  // surveyAnswerOperation(rdata).then((msg) => {
  //   loading.value = false;
  //   EleMessage.success("交卷成功");
  //   onBack()
  // }).catch((e) => {
  //   loading.value = false;
  //   EleMessage.error(e.message);
  // });
}

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/my-exam/' + userType + '/' + routeType,
  })
};
/**
 * 倒计时结束,系统自动交卷
 */
const handleUpdateAutoSumbit = () => {
  save()
};

/** 试卷预览 查询试卷试题详情*/
const QuerySurveyById = () => {
  if (paperId) {
    paperViewData.value = null;
    loading.value = true;
    getSurveyById(paperId).then((list) => {
      loading.value = false;
      paperViewData.value = list ?? {};
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      // showPaperPerview.value = true;
    });
  }
}

/** 试卷预览 查询试卷试题详情*/
const QuerySurveyVO = () => {
  paperViewData.value = null;
  loading.value = true;
  let queryData = {}
  if (['perviewAlreadyMarked'].includes(props.enterType)) {
    queryData = props.perviewParams;
  }
  console.log(queryData)
  getSurveyVO(queryData).then((list) => {
    loading.value = false;
    paperData.value = list;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
  });
}
/** 试卷预览 查询试卷试题详情*/
const QuerySurveyAnswerInfo = () => {
  paperViewData.value = null;
  loading.value = true;
  getSurveyAnswerInfo({sid: paperId}).then((list) => {
    console.log(list)
    loading.value = false;
    // paperData.value = list;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
  });
}

QuerySurveyById()
</script>
