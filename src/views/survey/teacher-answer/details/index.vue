<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
<!--          <el-icon :size="22" style="align-self: center; ">-->
<!--            <ArrowLeftBold/>-->
<!--          </el-icon>-->
<!--          <el-icon :size="22" style="align-self: center; ">-->
<!--            <DArrowLeft/>-->
<!--          </el-icon>-->
<!--          <el-icon :size="22" style="align-self: center; ">-->
<!--            <ArrowLeft/>-->
<!--          </el-icon>-->

          <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
              返回
            </ele-text>
          </div>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handlePaperAnswerList(row)">
            答题详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview v-model="showPaperPerview" :paperId="ksid"
                  :enterType="enterType" :perviewParams="perviewParams"
                  :RandomString="RandomString"
                  @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref, watch,} from 'vue';
import {useRouter} from "vue-router";
import {
  queryExamAnswerInfoPagePersonal
} from "@/views/exam/my-exam/api/index-answer.js";
import PaperPerview from "../details/paper-preview.vue"
import { ElMessage as EleMessage} from "element-plus";
import {getExamVO} from "@/views/exam/exam-management/api/index.js";
import {generateRandomString, insertAtIndex} from '@/utils/common_bak2.js';
import {usePageTab} from "@/utils/use-page-tab.js";
import {ArrowLeftBold, DArrowLeft} from "@element-plus/icons-vue";
import {QuestionCircleOutlined} from "@/components/icons/index.js";
import {querySurveyAnswerInfoPage} from "@/views/survey/my-exam/api/index.js";
import Step2Edit from "@/views/exam/test-paper/details/components/step2-edit.vue";
import IconPark from "@/components/IconPark/index.vue";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
if (query.currentKsmcc) setPageTabTitle(query.currentKsmcc + '答题记录')

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let ksid = ref(null);
let perviewParams = ref({});
let enterType = ref('perview');
const RandomString = ref(null)
let pathArray = path.split("/")
console.log(pathArray)
const userType = pathArray[4];
const routeType = pathArray[5];
let examId = pathArray[6];
/** 加载状态 */
const loading = ref(false);

/** 试卷答题 */
const handlePaperAnswerList = (row) => {
  RandomString.value = generateRandomString(10)
  console.log(row)
  enterType.value = "perviewAlreadyMarked";
  ksid = row.sid;
  perviewParams.value = {
    id: row.id,//答题ID
    sid: row.sid//问卷ID
  }
  showPaperPerview.value = true;
}

/** 表格列配置 */
const columns = ref([
  {
    prop: 'createDate',
    label: '答题提交时间',
  },
  {
    prop: 'userInfo.xm',
    label: '答题人',
  },
  {
    prop: 'userInfo.xgh',
    label: '学工号',
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 150,
    slot: 'action'
  }
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return querySurveyAnswerInfoPage({...where, ...orders, ...filters, page, limit, sid: examId})
};

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/test-paper/' + userType + '/' + routeType,
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

</script>

<script>
export default {
  name: 'SURVEYTEACHERANSWERDETAILSINDEX'
};
</script>
