<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #action="{ row }">
          <template v-if="row.status === '启用' && !row.answered">
            <el-link
              type="primary"
              underline="never"
              @click="handlePaperPerview(row)"
            >
              <!--              {{ row.answered ? '重新答卷' : '答卷' }}-->
              答卷
            </el-link>
            <el-divider v-if="row.answered" direction="vertical" />
          </template>
          <template v-if="row.answered">
            <el-link
              type="primary"
              underline="never"
              @click="handlePaperAnswerList(row)"
            >
              答题记录
            </el-link>
          </template>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview
      v-model="showPaperPerview"
      :paperId="ksid"
      :bcpr="bcpr"
      :surveyItemId="surveyItemId"
      @done="reload"
      :RandomString="RandomString"
      :enterType="enterType"
      :routeType="routeType"
    />
  </ele-page>
</template>

<script setup>
  import { onMounted, reactive, ref, unref } from 'vue';
  import NameFilter from './components/name-filter.vue';
  import { pageFdycpSurveyList } from './api/index.js';
  import { useRouter } from 'vue-router';
  // import PaperPerview from './details/paper-preview.vue';
  import PaperPerview from '@/views/exam/components/paper-preview.vue';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);
  let pathArray = path.split('/');
  console.log(pathArray);
  let userType = pathArray[3];
  let routeType = pathArray[4];

  /** 表格实例 */
  const tableRef = ref(null);
  const showPaperPerview = ref(false);
  let ksid = ref(null);
  let bcpr = ref(null);
  let surveyItemId = ref(null);
  let enterType = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 试卷答题 */
  const handlePaperPerview = (row) => {
    removePageTab({ key: getRouteTabKey() });
    push({
      path:
        '/survey/teacher-answer/answer/' +
        userType +
        '/' +
        routeType +
        '/' +
        row.id +
        '/' +
        row.bcpr +
        '/' +
        row.surveyItemId,
      query: { bcprjs: row.bcprjs }
    });
  };

  const RandomString = ref(null);
  /** 答题记录列表*/
  const handlePaperAnswerList = (row) => {
    RandomString.value = generateRandomString(10);
    ksid.value = row.id;
    bcpr.value = row?.bcpr;
    surveyItemId.value = row?.surveyItemId;
    enterType.value = 'toAnswerdDetailFdycp';
    showPaperPerview.value = true;
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'name',
      label: '问卷名称',
      headerSlot: 'nameHeader',
      minWidth: 110
    },
    {
      prop: 'bcprxm',
      label: '被测评人'
    },
    {
      prop: 'dcfs',
      label: '调查方式'
    },
    {
      prop: 'createTime',
      label: '发布时间'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 150,
      slot: 'action'
    }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return pageFdycpSurveyList({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType
    });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'SURVEYTEACHERANSWERINDEX'
  };
</script>
