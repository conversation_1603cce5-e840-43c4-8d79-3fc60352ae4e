import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryFdycpAnswer(params) {
  const res = await request.get('/teacherEvaluate/fdycp-answer', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function pageFdycpSurveyList(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-answerer/pageSurveyList',
    { params }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
export async function queryFdycpAnswerPage(params) {
  const res = await request.get('/teacherEvaluate/fdycp-answer/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getFdycpAnswerInfo(params) {
  const res = await request.get(
    '/teacherEvaluate/fdycp-answerer/surveyAnswer',
    { params }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 答题
 */
export async function operation(data) {
  const res = await request.post(
    '/teacherEvaluate/fdycp-answer/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改辅导员测评卷答案（权限标识：teacherEvaluate:answerer:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operationFdycpAnswerer(data) {
  const res = await request.post(
    '/teacherEvaluate/fdycp-answerer/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
