import request from '@/utils/request';

/**
 * 不分页查询
 */
export async function queryExamAnswerInfo(params) {
  const res = await request.get('/survey/survey-answer', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询 考生成绩成绩 管理者
 */
export async function queryExamAnswerInfoPage(params) {
  const res = await request.get('/survey/survey-answer/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询 考生成绩成绩 考生
 */
export async function queryExamAnswerInfoPagePersonal(params) {
  const res = await request.get('/survey/survey-answer/pagePersonal', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询（权限标识：exam:examAnswerInfo:list）
 */
export async function getExamAnswerInfo(answerId) {
  const res = await request.get('/survey/survey-answer/' + answerId,);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取考试统计数据
 * examId 考试id
 */
export async function getExamStatistics(params) {
  const res = await request.get('/survey/survey-answer/getExamStatistics', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取考试按学院分组统计数据
 * examId 考试id
 * @param params
 * @returns {Promise<*>}
 */
export async function getExamStatisticsGroup(params) {
  const res = await request.get('/survey/survey-answer/getExamStatisticsGroup', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 答题
 */
export async function surveyAnswerOperation(data) {
  const res = await request.post('/survey/survey-answer/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
