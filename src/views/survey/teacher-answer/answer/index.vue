<!--试卷预览-->
<template>
  <ele-page hide-footer flex-table>
    <BaseComponent
      :paperId="paperId"
      :bcpr="bcpr"
      :surveyItemId="surveyItemId"
      :RandomString="RandomString"
      enterType="toAnswerFdycp"
      @doneRefesh="handleDoneRefesh"
    />
  </ele-page>
</template>

<script setup>
  import { inject, ref, unref, watch } from 'vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useRoute, useRouter } from 'vue-router';
  import BaseComponent from '@/views/exam/components/baseComponent.vue';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import { usePageTab } from '@/utils/use-page-tab.js';

  defineOptions({
    name: 'SURVEYANSWERINDEX',
    components: {}
  });
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const emit = defineEmits(['done', 'update:modelValue']);

  const route = useRoute();
  const { currentRoute, push } = useRouter();
  const { path, query } = unref(currentRoute);
  let pathArray = path.split('/');
  console.log(path, query, pathArray);
  // let paperName = query?.paperName ?? ''
  // let ksId = pathArray[3];
  const routeType = pathArray[5]; // 试卷类型 fdycp
  let paperId = pathArray[6];
  let bcpr = pathArray[7];
  let surveyItemId = pathArray[8];
  const RandomString = ref(null);

  const { triggerRefresh } = inject('refreshEvent');

  const handleDoneRefesh = () => {
    triggerRefresh();
    removePageTab({ key: getRouteTabKey() });
    push({
      path: '/survey/teacher-answer/' + pathArray[4] + '/' + pathArray[5]
    });
  };

  watch(
    () => paperId,
    (value) => {
      if (value) {
        RandomString.value = generateRandomString(10);
      }
    },
    { immediate: true }
  );
</script>
