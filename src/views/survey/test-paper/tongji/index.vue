<template>
  <ele-page>
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <!--    flex-table-->
    <!--    :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"-->
    <ele-card :body-style="{padding:'0px!important',backgroundColor: '#e6e9ec',height:pageHeight+'px'}">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
        <el-button size="small" type="primary" plain
                   class="ele-btn-icon"
                   @click="handleExportDownload">导出问卷答题情况
        </el-button>
        <el-button size="small" type="primary" plain
                   class="ele-btn-icon"
                   @click="openExportTask">问卷答题情况导出任务列表查看
        </el-button>
      </template>
      <template #extra>
        <ele-tabs type="tag"
                  size="small"
                  v-model="activeName"
                  :items="tabsItems"
                  @tabClick="onFormTabChange">
          <template #label="{ item, label}">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <Kaoshimingdan v-if="['weicanyumingdan','canyumingdan'].includes(activeName)" :ksId="ksId"
                     :enterType="activeName" :tabs="tabsItems" :RandomString="RandomString"/>
      <Shujutongji v-else-if="['shujutongji'].includes(activeName)" :ksId="ksId"
                   :enterType="activeName" :tabs="tabsItems"/>
    </ele-card>
    <Download v-model="showDownload" :sid="ksId" @done="openExportTask"/>
    <DownloadExportTask v-model="showExportTask"/>
  </ele-page>
</template>

<script setup>
import {onMounted, ref, unref,} from 'vue';
import {useRouter} from "vue-router";
import Kaoshimingdan from "./components/kaoshimingdan.vue";
import Shujutongji from "./components/shujutongji.vue";
import {usePageTab} from "@/utils/use-page-tab.js";
import {generateRandomString} from '@/utils/common_bak2.js';
import Download from "@/views/survey/test-paper/tongji/components/download.vue";
import DownloadExportTask from "@/views/survey/test-paper/tongji/components/downloadExportTask.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
// currentKsmcc
let pathArray = path.split("/")
console.log(pathArray)
const userType = pathArray[4];
const routeType = pathArray[5];
let ksId = pathArray[6];
console.log(ksId)
if (query.currentTitle) setPageTabTitle(query.currentTitle + '数据统计')
/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);

/** 标签页选中 */
const activeName = ref('shujutongji');
const tabsItems = ref([{
  label: '数据统计',
  name: 'shujutongji',
}, {
  label: '未参与名单',
  name: 'weicanyumingdan',
  enterType: 'weicanyumingdan',
}, {
  label: '参与名单',
  name: 'canyumingdan',
  enterType: 'canyumingdan',
},])

const showDownload = ref(false);

/** 下载导出文件 */
const handleExportDownload = () => {
  showDownload.value = true;
  // window.location.href = BASE_URL + 'api/survey/survey-statistics/asyncExportSurveyAnswers/' + props.ksId + '?access_token=' + accessToken
};

const showExportTask = ref(false)
/** 分页查询自己的导出任务 */
const openExportTask = () => {
  showExportTask.value = true;
  // window.location.href = BASE_URL + 'api/survey/survey-statistics/asyncExportSurveyAnswers/' + props.ksId + '?access_token=' + accessToken
};


/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/test-paper/' + userType + '/' + routeType,
  })
};

const RandomString = ref(null)
RandomString.value = generateRandomString(10)
/** tab选择改变事件 */
const onFormTabChange = (event) => {
  console.log(event.props.name)
  activeName.value = event.props.name
  RandomString.value = generateRandomString(10)
};

</script>

<script>
export default {
  name: 'SURVEYTESTPAPERTONGJI'
};
</script>
