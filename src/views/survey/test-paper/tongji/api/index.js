/**问卷统计*/
import request from '@/utils/request';

/**
 * 问卷选项答题统计 （权限标识：survey:survey:statistics）
 * @param params
 * @returns {Promise<*>}
 */
export async function getAnswerStatistics(params) {
  const res = await request.get('/survey/survey-statistics/getAnswerStatistics', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function asyncExportSurveyAnswers(params) {
  const res = await request.get('/survey/survey-statistics/asyncExportSurveyAnswers', {params});
  if (res.data.code === 0 && res.data.message) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 参与人员名单 （权限标识：survey:survey:list）
 */
export async function pageAnsweredUserInfo(params) {
  const res = await request.get('/survey/survey-statistics/pageAnsweredUserInfo', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 未参与人员名单 （权限标识：survey:survey:list）
 */
export async function pageNoAnsweredUserInfo(params) {
  const res = await request.get('/survey/survey-statistics/pageNoAnsweredUserInfo', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 下钻问卷选项答题统计人员名单 （权限标识：survey:survey:statistics）
 * @param params
 * @returns {Promise<*>}
 */
export async function pageAnswerStatisticsUserInfo(params) {
  const res = await request.get('/survey/survey-statistics/pageAnswerStatisticsUserInfo', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取问卷统计 （权限标识：survey:survey:list）
 */
export async function selectSurveyWithStats(params) {
  const res = await request.get('survey/survey-statistics/selectSurveyWithStats', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
