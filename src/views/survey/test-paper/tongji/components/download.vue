<!-- 异步导出问卷答题情况 -->
<template>
  <ele-modal :width="360"
             title="异步导出问卷答题情况"
             :model-value="modelValue"
             @update:modelValue="updateModelValue">
    <ele-text size="md">请选择导出类型</ele-text>
    <div style="margin-left: -8px;">
      <ele-check-card v-model="checkedNodeId"
                      :items="stateArray"
                      :item-style="{ margin: '8px', padding: '8px 12px', display: 'flex' }"
                      style="display: flex; flex-wrap: wrap">
      </ele-check-card>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" type="primary" plain @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
import {ref} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {asyncExportSurveyAnswers} from '../api/index';
import {getToken,} from '@/utils/token-util';
import {getCurrentRole,} from '@/utils/current-role-util';

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 是否打开弹窗 */
  modelValue: Boolean,
  sid: String,
});

/** 导入请求状态 */
const loading = ref(false);
const checkedNodeId = ref(null)
const stateArray = ref([
  {
    value: 'WORD',
  },
  {
    value: 'EXCEL',
  }
])

const handleSubmit = () => {
  if (!checkedNodeId["value"]) {
    EleMessage.error('请选择你要导出的文件类型');
    return false;
  } else {
    asyncExportSurveyAnswers({sid: props.sid, exportType: checkedNodeId["value"]}).then((resData) => {
      console.log(resData)
      // answerInfoData.value = resData
      if (resData) {
        EleMessage.success('异步导出问卷答题情况任务创建成功');
        updateModelValue(false);
        emit('done');
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
}
/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
</script>
