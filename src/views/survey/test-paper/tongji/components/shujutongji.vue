<template>
  <ele-page>
    <ele-card v-if="answerInfoData" :body-style="{height:(pageHeight-50)+'px',overflow:'auto' }">
      <div v-for="(item,index) in answerInfoData.questionsGroupList" :key="index">
        <div class="list-item-body">
          <div style="flex: 1">
            <!--          <ele-text size="xxl">{{ item.name }} （共{{ item?.qsum ?? 0 }}题）</ele-text>-->
            <template v-for="(question,qindex) in item.surveyPquestionsVOList" :key="question.id">
              <ele-table size="small" border
                         style="min-width: 580px; table-layout: fixed">
                <thead>
                <tr>
                  <td>
                    {{ question.sort }}、[{{ question.qtype }}] {{ question.name }}
                  </td>
                  <td style="width: 250px;">占比</td>
                  <td style="width: 120px;text-align: right;">参与人数</td>
                </tr>
                </thead>
                <tbody>
                <template v-if="['单选题','多选题'].includes(question.qtype)">
                  <tr style="height: auto;" v-for="qoption in question.surveyPquestionsOptionsList"
                      :key="qoption.code">
                    <td>
                      <div class="editable-cell-text">{{ qoption.code }}、{{ qoption.name }}</div>
                    </td>
                    <td>
                      <div class="editable-cell-text">
                        <el-progress :text-inside="true" :stroke-width="26" :percentage="qoption?.percentage??0"/>
                      </div>
                    </td>
                    <td style="text-align: right;">
                      <div v-if="qoption?.optionAnswerCount>0"
                           @click="onClickCanyumingdan({contentId:question.id,result:qoption.code})"
                           class="editable-cell-text"
                           style="padding-right: 10px;color: var(--el-color-primary);cursor: pointer;">{{
                          qoption?.optionAnswerCount ?? 0
                        }}
                      </div>
                      <div v-else class="editable-cell-text" style="padding-right: 10px;">{{
                          qoption?.optionAnswerCount ?? 0
                        }}
                      </div>
                    </td>
                  </tr>
                </template>
                <template v-else>
                  <tr style="height: auto;">
                    <td>
                      <!--                    <div class="editable-cell-text">{{ qoption.code }}、{{ qoption.name }}</div>-->
                    </td>
                    <td>
                      <div class="editable-cell-text">
                        <!--                      <el-progress :text-inside="true" :stroke-width="26" :percentage="qoption?.percentage??0"/>-->
                      </div>
                    </td>
                    <td style="text-align: right;">
                      <div v-if="question?.answerCount>0"
                           @click="onClickCanyumingdan({contentId:question.id})"
                           class="editable-cell-text"
                           style="padding-right: 10px;color: var(--el-color-primary);cursor: pointer;">{{
                          question?.answerCount ?? 0
                        }}
                      </div>
                      <div v-else class="editable-cell-text" style="padding-right: 10px;">{{
                          question?.answerCount ?? 0
                        }}
                      </div>
                    </td>
                  </tr>
                </template>
                </tbody>
              </ele-table>
            </template>
          </div>
        </div>
      </div>
    </ele-card>
    <Canyumingdan v-model="showCanyumingdan" :ksId="ksId"
                  :queryParams="queryParams"
                  :enterType="enterType" :tabs="tabs" :RandomString="RandomString"/>

<!--    &lt;!&ndash; 底部工具栏 &ndash;&gt;-->
<!--    <ele-bottom-bar>-->
<!--      <template #extra>-->
<!--        <el-button size="small" @click="onBack">返回</el-button>-->
<!--        <el-button size="small" type="primary" plain :loading="loading" @click="handleExportDownload">-->
<!--          导出问卷答题情况-->
<!--        </el-button>-->
<!--      </template>-->
<!--    </ele-bottom-bar>-->
<!--    <Download v-model="showDownload" :sid="ksId" @done="openExportTask"/>-->
<!--    <DownloadExportTask v-model="showExportTask"/>-->
  </ele-page>
</template>

<script setup>
import {computed, onMounted, reactive, ref, unref, watch,} from 'vue';
import {
  asyncExportSurveyAnswers,
  getAnswerStatistics,
} from "../api/index.js";
import {useRouter} from "vue-router";
import { ElMessage as EleMessage} from "element-plus";
import Canyumingdan from "./canyumingdan.vue"
import {generateRandomString} from '@/utils/common_bak2.js';
import {usePageTab} from "@/utils/use-page-tab.js";
import {getToken} from "@/utils/token-util.js";
import Download from "./download.vue"
import DownloadExportTask from "./downloadExportTask.vue"
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let pathArray = path.split("/")
console.log(pathArray)
const userType = pathArray[4];
const routeType = pathArray[5];

const props = defineProps({
  ksId: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
});

/** 加载状态 */
const loading = ref(false);
const showDownload = ref(false);

/** 表格实例 */
let ksid = ref(null);

const answerInfoData = ref(null)
/** 查询数据 */
const queryAnswerStatistics = () => {
  getAnswerStatistics({sid: props.ksId}).then((resData) => {
    console.log(resData)
    answerInfoData.value = resData
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/test-paper/' + userType + '/' + routeType,
  })
};

const queryParams = ref(null)
const showCanyumingdan = ref(false);
const RandomString = ref(null)
RandomString.value = generateRandomString(10)

/** 下转参与名单 */
const onClickCanyumingdan = (params) => {
  queryParams.value = params
  // console.log(q, qoption)
  showCanyumingdan.value = true;
  RandomString.value = generateRandomString(10)
};

watch(() => props.enterType, (val) => {
  if (val) {
    queryAnswerStatistics()
  }
}, {immediate: true})

</script>

<script>
export default {
  name: 'SHJUTONGJI'
};
</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 5px 15px;
}

.el-radio-group {
  display: unset !important;
}

:deep(.el-radio .el-radio__label,) {
  font-size: 14px !important;
}

:deep(.el-checkbox .el-checkbox__label) {
  font-size: 14px !important;
}

</style>
