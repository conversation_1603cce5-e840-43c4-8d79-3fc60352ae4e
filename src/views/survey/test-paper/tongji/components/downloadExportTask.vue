<template>
  <ele-drawer title="文件导出任务列表"
              :append-to-body="true"
              size="50%"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px!important' }"
              @update:modelValue="updateModelValue">
    <ele-page flex-table :style="{height: pageHeight+'px',overflow:'auto'}">
      <ele-card flex-table
                :body-style="{ padding: '0 5px 0 5px!important',  }">
        <ExportTaskBaseList/>
      </ele-card>
    </ele-page>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import ExportTaskBaseList from "@/views/system/export-task/components/base-list.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};


</script>
