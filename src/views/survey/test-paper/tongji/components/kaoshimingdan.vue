<template>
  <ele-page>
    <div class="treeTransfer" :style="{ height: pageHeight+'px',}">
      <el-row :gutter="10">
        <el-col v-for="item in rDataList"
                :key="item.id" :md="enterType === 'shujutongji'?6:3" :sm="6" :xs="12">
          <ele-card shadow="hover"
                    :body-style="{ padding: '3px!important' }" style="overflow: hidden; cursor: pointer">
            <div style="padding: 5px;text-align: center;">
              <ele-text size="md">
                {{ item.xm }}
              </ele-text>
              <div style="display: flex; align-items: center">
                <ele-text type="placeholder" style="flex: 1">
                  {{ item.xgh }}
                </ele-text>
              </div>
            </div>
          </ele-card>
        </el-col>
      </el-row>
    </div>
    <el-pagination @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page="currentPage"
                   :page-sizes="[80, 100, 200]"
                   :page-size="pageSize"
                   :total="total"
                   :small="true"
                   :background="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :style="{
                    marginTop:'8px',
                    marginBottom:'8px',
                    justifyContent: 'center',
                    '--ele-pagination-hover-bg': 'transparent',
                    '--ele-pagination-hover-color': 'var(--el-color-primary)'
                  }">
    </el-pagination>
  </ele-page>
</template>

<script setup>
import {onMounted, reactive, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import { ElMessage as EleMessage} from "element-plus";
import {useRouter} from "vue-router";
import {
  getAnswerStatistics,
  pageAnsweredUserInfo,
  pageAnswerStatisticsUserInfo,
  pageNoAnsweredUserInfo
} from "../api/index.js"
import {ElPagination} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const props = defineProps({
  ksId: String,
  RandomString: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
  queryParams: Object,//参数
  currentPageHeight: String,
});

/** 加载状态 */
const loading = ref(false);
/** 数据 */
const data = ref([]);

const total = ref(null); // 假设总数据量为100
const pageSize = ref(80); // 每页显示10条数据
const currentPage = ref(1); // 当前页码
// const queryParams = ref({});
const rDataList = ref([]);


/** 查询数据 */
const queryPageList = () => {
  let AjaxUrl = pageAnsweredUserInfo
  if (props.enterType === "weicanyumingdan") {
    AjaxUrl = pageNoAnsweredUserInfo
  } else if (props.enterType === "shujutongji") {
    AjaxUrl = pageAnswerStatisticsUserInfo
  }
  let obj = {page: currentPage.value, limit: pageSize.value, sid: props.ksId}
  let newObj = Object.assign(obj, props?.queryParams ?? {});
  AjaxUrl(newObj).then((resData) => {
    total.value = resData.count
    let resList = resData.list;
    if (resList.length > 0) {
      rDataList.value = resList;
    } else {
      rDataList.value = [];
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}


watch(
  currentPage,
  (newVal) => {
    if (newVal) queryPageList()
  },
  {immediate: true}
)

watch(
  pageSize,
  (newVal) => {
    if (newVal) queryPageList()
  },
  {immediate: true}
)

// 分页大小改变时的回调
const handleSizeChange = (val) => {
  pageSize.value = val;
}

// 当前页改变时的回调
const handleCurrentChange = (val) => {
  currentPage.value = val;
}

watch(
  () => props.RandomString,
  (RandomString) => {
    console.log(RandomString, props.enterType)
    if (RandomString && props.enterType) {
      rDataList.value = [];
      queryPageList()
    }
  },
  {immediate: true}
)

</script>

<script>
export default {
  name: 'KAOSHIMINGDAN'
};
</script>
