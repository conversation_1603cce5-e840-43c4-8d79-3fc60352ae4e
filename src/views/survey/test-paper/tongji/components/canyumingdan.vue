<!--参与名单-->
<template>
  <ele-drawer size="30%"
              title="参与名单"
              :append-to-body="true"
              :model-value="modelValue"
              :body-style="{padding: '0px!important',background: 'rgb(236 236 236)', overflow:'hidden'}"
              @update:modelValue="updateModelValue">
    <Kaoshimingdan :ksId="ksId"
                   :currentPageHeight="pageHeight"
                   :enterType="enterType" :RandomString="RandomString" :queryParams="queryParams"/>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button
        size="small"
        @click="updateModelValue(false)">
        关闭
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {onMounted, ref,} from 'vue';
import Kaoshimingdan from "./kaoshimingdan.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

defineOptions({
  name: 'Canyumingdan',
});

const emit = defineEmits(['done', 'update:modelValue']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  ksId: String,
  paperId: String,
  RandomString: String,
  enterType: String,
  queryParams: Object,
});

/** 容器 */
const wrapRef = ref(null);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

</script>

<style>
.span-wrap {
  display: inline-block;
  white-space: pre-wrap; /* 保留空白符序列，但是当到达容器的末端时进行换行 */
}
</style>
