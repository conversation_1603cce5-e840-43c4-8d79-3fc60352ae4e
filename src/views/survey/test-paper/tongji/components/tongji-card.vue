<!-- 考试统计 -->
<template>
  <el-row :gutter="6" ref="wrapRef">
    <el-col v-for="(item,index) in data" :key="index" :span="3">
      <ele-card bordered shadow="always" :body-style="{ padding: '5px !important' ,margin:'1px!important'}">
        <!--        <router-link class="app-link">-->
        <!--          &lt;!&ndash;          :to="item.url"&ndash;&gt;-->
        <!--          &lt;!&ndash;            <el-icon class="app-link-icon" :style="{ color: item.color }">&ndash;&gt;-->
        <!--          &lt;!&ndash;              <component :is="item.icon"/>&ndash;&gt;-->
        <!--          &lt;!&ndash;            </el-icon>&ndash;&gt;-->
        <!--          <ele-text size="xxl" class="app-link-icon">{{ item.count }}</ele-text>-->
        <!--          <div class="app-link-title">{{ item.title }}</div>-->
        <!--        </router-link>-->
        <div class="app-link">
          <ele-text size="xxl" class="app-link-icon">{{ item.count }}</ele-text>
          <div class="app-link-title">{{ item.title }}</div>
        </div>
      </ele-card>
    </el-col>
  </el-row>
</template>

<script setup>
import {ref} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {getExamStatistics} from "@/views/exam/my-exam/api/index-answer.js";

const props = defineProps({
  ksId: String,
});
const data = ref([])
/** 根节点 */
const wrapRef = ref(null);

const loading = ref(false)
/** 查询试卷试题详情 */
const queryExamStatistics = () => {
  loading.value = true;
  getExamStatistics({examId: props.ksId}).then((list) => {
    loading.value = false;
    if (list) {
      data.value = [
        {
          count: list.shouldAttendCount,
          icon: 'UserOutlined',
          title: '应考人数',
          url: '/system/user'
        },
        {
          count: list.actualAttendCount,
          icon: 'AnalysisOutlined',
          title: '实际参考人数',
          url: '/dashboard/analysis',
          color: '#95de64'
        },
        {
          count: list.absentCount,
          icon: 'ShoppingOutlined',
          title: '缺考人数',
          url: '/list/card/project',
          color: '#ff9c6e'
        },
        {
          count: list.passCount,
          icon: 'LogOutlined',
          title: '及格人数',
          url: '/list/basic',
          color: '#b37feb'
        },
        {
          count: list.failCount,
          icon: 'CopyOutlined',
          title: '不及格人数',
          url: '/list/advanced',
          color: '#ffd666'
        },
        {
          count: list.highestScore,
          icon: 'TagOutlined',
          title: '最高分',
          url: '/extension/tag',
          color: '#ff85c0'
        },
        {
          count: list.lowestScore,
          icon: 'TagOutlined',
          title: '最低分',
          url: '/extension/tag',
          color: '#ff85c0'
        },
        {
          count: list.passRate + "/" + list.averageScore,
          icon: 'MailOutlined',
          title: '及格率/平均分',
          url: '/user/message',
          color: '#5cdbd3'
        },
      ];
      console.log(list)
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;

  });
}

if (props.ksId) {
  queryExamStatistics()
}
</script>

<script>
import {
  UserOutlined,
  AnalysisOutlined,
  ShoppingOutlined,
  LogOutlined,
  CopyOutlined,
  MailOutlined,
  TagOutlined,
  ControlOutlined
} from '@/components/icons';

export default {
  components: {
    UserOutlined,
    AnalysisOutlined,
    ShoppingOutlined,
    LogOutlined,
    CopyOutlined,
    MailOutlined,
    TagOutlined,
    ControlOutlined
  }
};
</script>

<style lang="scss" scoped>
.app-link {
  //padding: 6px;
  display: block;
  text-align: center;
  text-decoration: none;
  //color: inherit;
  color: var(--el-color-primary);

  :deep(.app-link-icon) {
    font-size: 30px;
    font-weight: 700;
    //color: #69c0ff;
    color: var(--el-color-primary);
    margin: 6px 0 10px 0;

    & > svg {
      stroke-width: 3;
    }
  }
}

.el-col.sortable-ghost {
  opacity: 0;
}
</style>
