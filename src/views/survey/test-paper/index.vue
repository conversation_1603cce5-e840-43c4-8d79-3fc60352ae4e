<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     highlight-current-row
                     tooltip-effect="light"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="点我查看问卷详情" placement="left" effect="light">
            <el-link type="primary"
                     underline="never"
                     @click="openEdit(row,true)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #status="{ row }">
          <el-switch v-model="row.status"
                     size="small"
                     inline-prompt
                     active-text="启用"
                     active-value="启用"
                     inactive-text="结束"
                     inactive-value="结束"
                     @change="handleChangeState($event,row)"/>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handlePaperPerview(row)">
            预览
          </el-link>
          <template v-if="row.answered">
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openAnswerList(row)">
              答题记录
            </el-link>
          </template>
          <template v-if="row.status==='启用'&&!row.answered">
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openEdit(row)">
              编辑
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openStep2Edit(row)">
              问卷设计
            </el-link>
          </template>
          <el-divider v-if="row.status==='结束'" direction="vertical"/>
          <el-link v-if="row.status==='结束'" type="primary" underline="never" @click="openDataTongJi(row)">
            数据统计
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter"/>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview v-model="showPaperPerview"
                  :paperId="pid"
                  enterType="perviewPaperServey"
                  :RandomString="RandomString"
                  @done="reload"/>
    <Step1Edit v-model="showStep1Edit" :paperId="pid" :readonlyVal="readonlyVal" :data="current"
               :userType="userType"
               :routeType="routeType"
               @done="handleDonePaper"/>
    <Step2Edit v-model="showStep2Edit" :paperId="Step2Pid" :testPaperData="Step2PData"
               :RandomString="RandomString"
               :userType="userType"
               :routeType="routeType"
               @done="reload"/>
  </ele-page>
</template>

<script setup>
import {onMounted, reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import NameFilter from './components/name-filter.vue';
import {operationSurveyState, queryPage, removes} from './api/index.js';
import {useRouter} from "vue-router";
// import PaperPerview from "../my-exam/details/paper-preview.vue";
import PaperPerview from "@/views/exam/components/paper-preview.vue"
import {usePageTab} from "@/utils/use-page-tab.js";
import Step1Edit from "./details/components/step1-edit.vue"
import Step2Edit from "./details/components/step2-edit.vue";
import {generateRandomString} from '@/utils/common_bak2.js';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import DataEdit from "@/views/exam/test-paper/bank-data/components/edit.vue";

const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

let currentProject = null
let pathArray = path.split("/")
console.log(pathArray)
let userType = pathArray[3];
let routeType = pathArray[4];

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);

const pid = ref(null)
/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showStep1Edit = ref(false);
const showStep2Edit = ref(false);

const Step2Pid = ref(null)
const Step2PData = ref(null)
const RandomString = ref(null)

/** 加载状态 */
const loading = ref(false);
/** 试卷预览 */
const handlePaperPerview = (row) => {
  RandomString.value = generateRandomString(10)
  pid.value = row.id;
  showPaperPerview.value = true;
}

const handleChangeState = (value, row) => {
  console.log(value, row)
  if (value) {
    operationSurveyState({
      id: row.id,
      status: value
    }).then(() => {
      reload();
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
}

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'name',
    label: '问卷名称',
    headerSlot: 'nameHeader',
    slot: 'name',
    minWidth: 110
  },
  {
    prop: 'wjms',
    label: '问卷模式',
    minWidth: 110
  },
  {
    prop: 'dcfs',
    label: '调查方式',
  },
  {
    prop: 'sfkpl',
    label: '是否可评论',
  },
  {
    prop: 'sfknmpl',
    label: '是否可匿名评论',
  },
  {
    prop: 'sfjf',
    label: '是否记分',
  },
  {
    prop: 'xm',
    label: '创建人',
  },
  {
    prop: 'createTime',
    label: '创建时间',
  },
  {
    prop: 'status',
    slot: 'status',
    label: '状态',
  },
  {
    columnKey: 'action',
    label: '操作',
    slot: 'action',
    minWidth: 120
  }
]);

/** 列表选中数据 */
const selections = ref([]);
const readonlyVal = ref(false);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit, type: routeType})
};


/** 打开编辑弹窗 */
const openEdit = (row, readonly) => {
  readonlyVal.value = false;
  if (readonly) readonlyVal.value = readonly;
  pid.value = row?.id;
  current.value = row ?? null;
  showStep1Edit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

const openDataTongJi = (row) => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/test-paper/tongji/' + userType + '/' + routeType + '/' + row.id,
    query: {currentTitle: row.name}
  })
}

/**
 * 试卷设计
 * @param row
 */
const openStep2Edit = (row) => {
  showStep2Edit.value = true;
  RandomString.value = generateRandomString(10)
  Step2PData.value = row;
  Step2Pid.value = row.id;
  console.log(Step2Pid.value)
};

const openAnswerList = (row) => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/survey/my-exam/details/' + userType + '/' + routeType + '/' + row.id,
    query: {currentKsmcc: row.name}
  })
};

const handleDonePaper = (data) => {
  console.log(data)
  if (data && data.submitType === 'design') {//保存并进入试卷设计
    RandomString.value = generateRandomString(10)
    Step2PData.value = data;
    Step2Pid.value = data.id;
    showStep2Edit.value = true;
  } else {
    reload()
  }
};


</script>

<script>
export default {
  name: 'TESTPAPERINDEX'
};
</script>
