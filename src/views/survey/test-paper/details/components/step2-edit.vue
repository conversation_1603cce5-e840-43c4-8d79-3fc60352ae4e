<template>
  <ele-drawer
    size="70%"
    :title="title"
    :destroy-on-close="true"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ padding: '5px!important' }"
    :before-close="handleBeforeClose"
  >
    <ele-page
      flex-table
      :style="{
        height: pageHeight + 'px',
        overflow: 'auto',
        paddingTop: '0px!important'
      }"
    >
      <ele-split-panel
        ref="splitRef"
        :allow-collapse="true"
        :resizable="true"
        :responsive="true"
        flex-table
        space="6px"
        size="270px"
        allow-collapse
        :custom-style="{ borderWidth: '0 1px 0 0' }"
        :body-style="{ padding: '0', overflow: 'hidden' }"
        :style="{ height: '100%', overflow: 'visible' }"
      >
        <div style="padding-top: 8px; margin-bottom: 12px">
          <el-button style="width: 94%" size="small" plain @click="openEdit()">
            新建组
          </el-button>
        </div>
        <ele-loading
          :loading="loading"
          :style="{ flex: 1, paddingRight: '16px', overflow: 'auto' }"
        >
          <el-tree
            ref="treeRef"
            :data="data"
            highlight-current
            node-key="id"
            :props="{ label: 'name' }"
            :expand-on-click-node="false"
            :default-expand-all="true"
            :filter-node-method="filterNode"
            :style="{
              '--ele-tree-item-height': '68px',
              '--ele-tree-expand-padding': 0,
              '--ele-tree-expand-margin': 0
            }"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <div class="el-tree-node__label" style="margin: 10px 0">
                <span
                  class="custom-tree-node"
                  :class="{ selected: selectedKey === data.id }"
                >
                  <span class="span-wrap">{{ data.name }}</span>
                  <span style="float: right; position: absolute; right: 10px">
                    <a @click="openEdit(data)">
                      <el-icon><FormOutlined /></el-icon
                    ></a>
                    <a style="margin-left: 10px" @click="remove(data)">
                      <el-icon><Delete /></el-icon
                    ></a>
                  </span>
                </span>
                <ele-text type="placeholder" style="flex: 1; margin-top: 3px">
                  共{{ data?.qsum ?? 0 }}题
                </ele-text>
              </div>
            </template>
          </el-tree>
        </ele-loading>
        <template #body>
          <BankData
            v-if="paperData?.wjms === '固定选题' && current && current.id"
            :currentPId="pid"
            :currentGId="current.id"
            :dictName="current.name"
            :userType="userType"
            :routeType="routeType"
            :RandomString="RandomString"
            @doneRefreshGroup="handleDoneRefreshGroup"
          />
          <TablePreview
            v-else-if="paperData?.wjms === '随机抽题' && current && current.id"
            :ref="(el) => getRiskSpreadRef(el, 0)"
            :approverId="current?.id"
            :currentNode="current"
            dataName="抽题规则设置"
            :approverType="'randomSurveyTopicSelection_' + current?.id"
            @onDoneGroup="handleDoneGroup"
            @doneRefreshGroup="handleDoneRefreshGroup"
          />
        </template>
      </ele-split-panel>
    </ele-page>
    <template #footer>
      <el-button size="small" v-if="validMsg" style="border: unset !important">
        <ele-text type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
      </el-button>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain @click="saveConditionField">
        保存
      </el-button>
    </template>
    <Edit v-model="showEdit" :data="editData" :paperId="pid" @done="reload" />
    <FloatButton
      :needType="['help']"
      @floatClickEvent="handleFloatClickEvent"
    />
    <help v-model="helpVisible" :paperData="paperData" />
  </ele-drawer>
</template>

<script setup>
  import { ref, watch, nextTick, onMounted, reactive } from 'vue';
  import {
    CloseCircleOutlined,
    FormOutlined
  } from '@/components/icons/index.js';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
  import BankData from '../../bank-data/index.vue';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import Edit from '../../components/edit.vue';
  import { ElMessageBox, ElNotification } from 'element-plus';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import FloatButton from '@/components/FloatButton/index.vue';
  import Help from '../../details/components/help.vue';
  import { getSurveyQgroup, removes } from '../../api/qgroup-index.js';
  import { getSurveyById } from '../../api/index.js';
  import { operationSurveyQuestionsRuleBatch } from '@/views/survey/test-paper/api/rule-index.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useMobile } from '@/utils/use-mobile';

  const emit = defineEmits(['done', 'back', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    paperId: String,
    RandomString: String,
    routeType: String,
    userType: String,
    testPaperData: Object
  });
  const userStore = useUserStore();
  const mobile = useMobile();
  const { pageHeight } = storeToRefs(userStore);
  const showPaperPerview = ref(false);
  /** 是否是修改 */
  const isUpdate = ref(false);
  let pid = null;
  /** 提交状态 */
  const loading = ref(false);

  let newGroupId = null;
  /** 表单实例 */
  const formRef = ref(null);
  /** 分割面板组件 */
  const splitRef = ref(null);
  /** 树组件 */
  const treeRef = ref(null);

  /** 机构搜索关键字 */
  const keywords = ref('');

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  const helpVisible = ref(false);

  const paperData = ref(null);

  /** 编辑回显数据 */
  const editData = ref(null);
  /** 树形数据 */
  const data = ref([]);
  /** 选中数据 */
  const current = ref(null);

  const handleFloatClickEvent = (data) => {
    if (data === 'help') {
      helpVisible.value = true;
    }
  };
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    pid = row?.pid ?? pid;
    editData.value = row ?? null;
    showEdit.value = true;
  };

  /** 上一步 */
  const back = () => {
    emit('back');
  };

  /** 删除组 */
  const remove = (data) => {
    const rows = [data];
    if (!rows) {
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' +
        rows.map((d) => d.name).join(', ') +
        '”吗?该组下面的所有试题系统会一并删除！',
      {
        type: 'warning',
        draggable: true
      }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes({
          id: rows.map((d) => d.id).join(),
          sid: pid
        })
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload(data);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const RandomString = ref(null);

  /** 刷新组 */
  const reload = (data) => {
    console.log('刷新组==', data);
    pid = data?.sid;
    newGroupId = data?.id;
    RandomString.value = generateRandomString(10);
    querySurveyById();
    querySurveyQgroup();
  };

  /** 存放form提交字段*/
  const proFormData = ref(null);
  const handleDoneGroup = async (data) => {
    proFormData.value = data;
  };

  /**问卷试题编辑刷新组数据*/
  const handleDoneRefreshGroup = (data) => {
    console.log(data);
    pid = data?.pid;
    newGroupId = data?.gid;
    querySurveyById();
    querySurveyQgroup();
  };

  /** 查询 */
  const querySurveyById = () => {
    console.log('query====', pid);
    if (pid) {
      loading.value = true;
      getSurveyById(pid)
        .then((list) => {
          loading.value = false;
          if (list) {
            paperData.value = list ?? [];
            title.value =
              paperData.value.name +
              ' (问卷共' +
              (paperData.value?.subjectSum ?? 0) +
              '题)';
          }
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    }
  };

  /** 查询问卷试题组 */
  const querySurveyQgroup = () => {
    loading.value = true;
    getSurveyQgroup({ sid: pid })
      .then((list) => {
        loading.value = false;
        data.value = list ?? [];
        let refreshData = data.value[0];
        if (newGroupId) {
          //选中左侧编辑的组数据
          refreshData = data.value.find((item) => item.id === newGroupId);
        }
        nextTick(() => {
          handleNodeClick(refreshData);
        });
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  const selectedKey = ref(null);
  /** 选择数据 */
  const handleNodeClick = (row) => {
    // 移动端自动收起左侧
    if (current.value != null && mobile.value) {
      splitRef.value?.toggleCollapse?.(true);
    }
    if (row && row.id) {
      current.value = row;
      treeRef.value?.setCurrentKey?.(row.id);
      // 监听选中状态
      selectedKey.value = row.id;
    } else {
      current.value = null;
    }
  };

  /** 树过滤方法 */
  const filterNode = (value, data) => {
    if (value) {
      return !!(data.name && data.name.includes(value));
    }
    return true;
  };

  /** 树过滤 */
  watch(keywords, (value) => {
    treeRef['value']?.filter?.(value);
  });

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  const validMsgCount = ref(0);
  /** 存放子组件的数组 */
  let resultArr = reactive([]);
  /** 用来创建 Promise 实例，为多个组件校验使用 */
  const checkForm = (formChild) => {
    validMsg.value = null;
    let result = new Promise((resolve, reject) => {
      formChild.formRef?.validate((valid, obj) => {
        if (valid) {
          resolve(true);
        } else {
          const errors = obj ? Object.keys(obj).length : 0;
          validMsgCount.value += errors;
          resolve(false);
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  const riskSpreadRefList = ref([]);
  const getRiskSpreadRef = (el, index) => {
    if (el) {
      riskSpreadRefList.value[index] = el;
    }
  };

  const saveConditionField = async () => {
    await riskSpreadRefList.value?.forEach((child) => {
      checkForm(child);
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []; //每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save();
        // if (beforeCloseMark.value)
        // submitBeforeCloseMsgBox()
      } else {
        validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
        // submitBeforeCloseMsgBox()
        // confirmBeforeCloseMsgBox()
        if (beforeCloseMark.value) {
          confirmBeforeCloseMsgBox();
        } else {
          ElNotification({
            title: '系统提示',
            message: '问卷随机模式，请完善抽题规则信息设置',
            type: 'warning',
            // duration: 1500,
            showClose: false,
            position: 'top-right'
          });
        }
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  };

  const beforeCloseMark = ref(false);
  const handleBeforeClose = () => {
    beforeCloseMark.value = true;
    // 只有随机抽题模式并且proFormData.value.length > 0的情况，关闭前才需要保存；
    if (
      paperData.value?.wjms === '随机抽题' &&
      proFormData.value &&
      proFormData.value.length > 0
    ) {
      saveConditionField();
    } else {
      // 固定试题模式直接关闭
      updateModelValue(false);
    }
  };

  const confirmBeforeCloseMsgBox = () => {
    ElMessageBox.confirm(
      '问卷抽题规则未设置完整，您是否要关闭该弹出层?',
      '系统提示',
      {
        confirmButtonText: '关闭',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        updateModelValue(false);
        beforeCloseMark.value = false;
      })
      .catch(() => {
        beforeCloseMark.value = false;
        validMsg.value = '';
        validMsgCount.value = 0;
      });
  };
  /** 提交 */
  const save = () => {
    console.log(proFormData.value);
    if (proFormData.value && proFormData.value.length > 0) {
      loading.value = true;
      let arrData = proFormData.value;
      console.log('batch===', JSON.stringify(arrData));
      operationSurveyQuestionsRuleBatch(arrData)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    } else {
      if (paperData.value?.wjms === '随机抽题') {
        ElNotification({
          title: '系统提示',
          message: '请设置抽题规则设置',
          type: 'warning',
          // duration: 1500,
          showClose: false,
          position: 'top-right'
        });
      } else {
        updateModelValue(false);
      }
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    // beforeCloseMark.value = false
    validMsg.value = '';
    validMsgCount.value = 0;
    emit('update:modelValue', value);
    emit('done', value);
  };

  const title = ref(null);
  watch(
    () => props.modelValue,
    (modelValue) => {
      console.log(modelValue, props.paperId, props.RandomString);
      if (modelValue) {
        if (props.testPaperData) {
          paperData.value = props.testPaperData;
          title.value =
            paperData.value.name +
            ' (问卷共' +
            (paperData.value?.subjectSum ?? 0) +
            '题)';
        }
        if (props.RandomString) {
          pid = props.paperId;
          console.log(pid);
          /** 获取问卷组数据 */
          querySurveyById();
          querySurveyQgroup();
        }
      } else {
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>

<style>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 40px;
    margin-bottom: 5px;
  }

  .span-wrap {
    display: inline-block;
    white-space: pre-wrap; /* 保留空白符序列，但是当到达容器的末端时进行换行 */
  }
</style>
