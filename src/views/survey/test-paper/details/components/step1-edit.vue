<template>
  <ele-drawer size="50%"
              :title="readonlyVal?'问卷详情':(isUpdate?'编辑问卷':'新建问卷')"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">

    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             labelWidth="auto">
      <el-row :gutter="4">
        <template v-for="item in formItems">
          <el-col :md="item.colProps">
            <el-form-item prop="wjms" v-if="item.prop==='wjms'" :label="item.label">
              <div class="mode-container">
                <ele-check-card v-model="form.wjms"
                                :items="itemsType" @change="changeSjms"
                                :disabled="readonlyVal"
                                class="mode-options">
                  <template #item="{ item }">
                    <div class="mode-option">
                      <div class="mode-content">
                        <ele-text class="mode-title">{{ item.name }}</ele-text>
                        <ele-text class="mode-desc" type="placeholder">
                          {{ item.description }}
                        </ele-text>
                      </div>
                    </div>
                  </template>
                </ele-check-card>
              </div>
            </el-form-item>
            <el-form-item :label="item.label" v-else-if="item.prop==='sfzdry'">
              <div v-if="!readonlyVal" style="margin-left: 1px;cursor: pointer;" @click="openPerSelector()">
                <ele-text type="primary" style="float: left;" @click="openPerSelector()">
                  <el-icon>
                    <CirclePlus/>
                  </el-icon>
                  选择答卷名单
                </ele-text>
              </div>
              <el-input :rows="6"
                        type="textarea"
                        readonly
                        v-model="qddxData"
                        placeholder="选择答卷名单"/>
            </el-form-item>
            <ProFormItem v-else :item="item"
                         :model="form"
                         @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
              <template
                v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>

    <!--    <pro-form ref="formRef"-->
    <!--              size="small"-->
    <!--              :model="form"-->
    <!--              :items="formItems"-->
    <!--              :rules="rules"-->
    <!--              label-width="auto"-->
    <!--              :grid="{ span: 24 }"-->
    <!--              @updateValue="setFieldValue">-->
    <!--      <template #wjms="{ item, model, updateValue }">-->
    <!--        <ele-check-card v-model="form.wjms" :items="itemsType" @change="changeSjms"-->
    <!--                        :item-style="{ padding: '6px', display: 'flex',marginRight:'6px' }"-->
    <!--                        :disabled="readonlyVal"-->
    <!--                        style="display: flex;">-->
    <!--          <template #item="{ item }">-->
    <!--            <div style="display: flex;">-->
    <!--              <div style="flex: 1; overflow: hidden; padding-left: 2px;width: 232px;">-->
    <!--                <ele-text size="md" style="font-weight:600;font-size: 13px!important;">{{ item.name }}</ele-text>-->
    <!--                <ele-text size="sm" type="placeholder" style="line-height: 16px!important;">-->
    <!--                  {{ item.description }}-->
    <!--                </ele-text>-->
    <!--              </div>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </ele-check-card>-->
    <!--      </template>-->
    <!--      <template #sfzdry="{ item, model, updateValue }">-->
    <!--        <div v-if="!readonlyVal" style="margin-left: 1px;cursor: pointer;" @click="openPerSelector()">-->
    <!--          <ele-text type="primary" style="float: left;" @click="openPerSelector()">-->
    <!--            <el-icon>-->
    <!--              <CirclePlus/>-->
    <!--            </el-icon>-->
    <!--            选择答卷名单-->
    <!--          </ele-text>-->
    <!--        </div>-->
    <!--        <el-input :rows="6"-->
    <!--                  type="textarea"-->
    <!--                  readonly-->
    <!--                  v-model="qddxData"-->
    <!--                  placeholder="选择答卷名单"/>-->
    <!--        &lt;!&ndash;        </template>&ndash;&gt;-->
    <!--      </template>-->
    <!--    </pro-form>-->
    <!-- 底部工具栏 -->
    <template #footer>
      <template v-if="readonlyVal">
        <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      </template>
      <template v-else>
        <el-button size="small" @click="updateModelValue(false)">取消</el-button>
        <el-button size="small" type="primary" @click="submit('design')" plain>保存并进入问卷设计
        </el-button>
        <el-button size="small" type="primary" plain :loading="loading" @click="submit">
          保存
        </el-button>
      </template>
    </template>
  </ele-drawer>
  <PerSelector v-model="showPerSelector"
               ref="treeTransferRef"
               node-key="id"
               paramMode="base"
               :userType="userType"
               :perSelectedData="perSelectedData"
               @done="onDoneSelector"/>
</template>

<script setup>
import {ref, reactive, h, watch, computed, nextTick} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import { ElMessage as EleMessage} from "element-plus";
import ProForm from "@/components/ProForm/index.vue";
import {getSurveyById, operation} from "@/views/survey/test-paper/api/index.js";
import PerSelector from "@/components/PerSelector/index.vue";
import auth from "@/views/system/role/auth/index.vue";
import file from "@/views/system/file/index.vue";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  paperId: String,
  userType: String,
  routeType: String,
  readonlyVal: Boolean,//是否只读
  /** 修改回显的数据 */
  data: Object
});

/** 提交状态 */
const loading = ref(false);

/** 是否是修改 */
const isUpdate = ref(false);
/** 表单实例 */
const formRef = ref(null);

// 试卷数据
const paperData = ref([]);

/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入问卷名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  wjms: [
    {
      required: true,
      message: '请选择问卷模式',
      type: 'string',
      trigger: 'blur'
    }
  ],
})

const showPerSelector = ref(false);
const perSelectedData = ref([]);
const openPerSelector = () => {
  showPerSelector.value = true;
};

const onDoneSelector = (data) => {
  perSelectedData.value = data;
  form.sfzdry = '是'
  form.selectorDatas = data
};

const qddxData = computed(() => {
  let allDataCode = []
  if (perSelectedData.value.length > 0) {
    perSelectedData.value.forEach(e => {
      allDataCode.push(e.dictDataName ? e.dictDataName : e.name)
    })
  }
  return allDataCode.join(',');
});

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  name: '',
  dcfs: '',
  wjms: '',
  mdlx: '',
  sfkpl: '',
  sfknmpl: '',
  sfjf: '',
  sfbl: '',
  bz: '',
  type: props.routeType,
  userType: props.userType,
  selectorDatas: [],
});

const itemsType = ref([
  {
    description: "通过从题库自助选题生成的试卷，试题内容是相同的",
    name: "固定选题",
    value: "固定选题",
  },
  {
    description: "依据设定的规则，随机自动抽取指定数量的试题生成试卷",
    name: "随机抽题",
    value: "随机抽题",
  }])
;
const updateFormValue = (item, prop, newVal) => {
  console.log(item, prop, newVal)
  let value = newVal
  form[item.prop] = value;
  if (item.fieldLinks) {
    //获取选择值下面所有的显示字段信息
    let showfields = item.fieldLinks.filter(f => {
      return f.fieldVal === value
    })
    if (showfields.length > 0) {
      showfields.forEach(sf => {
        nextTick(() => {
          //切换选择值，清空之前选项所赋值
          form[sf.linkField] = '';
          //动态切换选择值，关联具体字段的显示隐藏
          baseFileds.value.filter((init => {
              init.showFlag = sf.linkField === init.prop ? sf.showFlag : init.showFlag
              init.selfModifyFlag = init.showFlag
            }
          ))
        });
      })
    }
  }
}
/** 表单项 */
const formItems = computed(() => {
  let baseFileds = []
  let comFileds = [
    {prop: 'name', label: '问卷名称', type: 'input', required: true},
  ]
  let wjdcFileds = [
    {prop: 'wjms', label: '问卷模式', type: 'wjms', required: true},
    {
      prop: 'dcfs', label: '调查方式',
      type: 'radioButton',
      options: [
        {label: '实名', value: '实名'},
        {label: '匿名', value: '匿名'},
      ],
      required: true, colProps: {span: 8}
    },
    {
      prop: 'sfkpl', label: '是否评论',
      type: 'switch', colProps: {span: 5}
    },
    {
      prop: 'sfknmpl', label: '是否匿名评论',
      type: 'switch', colProps: {span: 5}
    },
    {
      prop: 'sfjf', label: '是否记分',
      type: 'switch', colProps: {span: 5}
    },
    {
      prop: 'sfzdry', label: '指定名单',
      type: 'sfzdry',
    },
    {prop: 'bz', label: '备注', type: 'textarea'},
  ]
  let fdycpFileds = [
    {
      prop: 'fdycpzbid', label: '考核方式',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: "fdycpzb",
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: "/teacherEvaluate/fdycp-item",
          valueField: "id",
          textField: "khpc"
        }
      },
      required: true
    },
  ]
  console.log(props.routeType)
  if (props.routeType === 'wjdc') {
    baseFileds = comFileds.concat(wjdcFileds)
  } else {
    baseFileds = comFileds.concat(fdycpFileds)
  }
  baseFileds.forEach(filed => {
    if (props.readonlyVal) {
      if (["dictSelect", "select", "switch", "radioButton"].includes(filed.type)) {
        filed['props'] = {
          "readonly": props.readonlyVal,
          "disabled": props.readonlyVal,
        }
      } else {
        filed['props'] = {
          "readonly": props.readonlyVal,
          // "disabled": props.enterType === 'preview',
        }
      }
    }
  })
  return baseFileds;

});

const changeSjms = (value) => {
  if (value) {
    form.wjms = value;
  }
};


/** 步骤一提交 */
const submit = (type) => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((data) => {
      loading.value = false;
      if (data) {
        EleMessage.success(isUpdate.value ? "问卷维护成功" : "问卷创建成功");
        updateModelValue(false)
        data.submitType = type
        emit('done', data);
      }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 查询 */
const query = () => {
  if (props.paperId && props.paperId !== 'ad') {
    loading.value = true;
    getSurveyById(props.paperId).then((resData) => {
      loading.value = false;
      if (resData) {
        if (resData.selectorDatas && resData.selectorDatas.length > 0) {
          resData.sfzdry = '是'
          perSelectedData.value = resData.selectorDatas
        }
      }
      // paperData.value = list ?? [];
      // if (paperData.value) {
      //   assignFields({
      //     ...paperData.value,
      //   });
      // }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      perSelectedData.value = [];
      if (props.data) {
        query();
        assignFields({
          ...props.data,
          // answer: props.data.sttype === '多选题' ? props.data.answer.split(',') : props.data.answer
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);

</script>
