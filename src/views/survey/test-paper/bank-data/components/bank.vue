<!-- 从题库中选择试题 -->
<template>
  <ele-drawer size="55%"
              title="从题库中选择试题"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px!important' }"
              @update:modelValue="updateModelValue">
    <SelectBankData pageType="selectBank" :currentPId="currentPId"
                    :userType="userType"
                    :routeType="routeType"
                    @selectDone="handleSelectDone"
                    @setGroupScore="handleSetGroupScore"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {ElNotification} from 'element-plus/es';
import {useFormData} from '@/utils/use-form-data.js';
import {saveSurveyPquestionsByQuestions} from '../api/index.js';
import SelectBankData from '../../../survey-questions/bank-data/select-data-table.vue'

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentPId: String,
  currentGId: String,
  dictName: String,
  routeType: String,
  userType: String,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  sid: '',
  gid: '',
  qid: '',
});

const formData = ref(null);
const handleSelectDone = (data) => {
  formData.value = data;
};

const groupScore = ref(null);
const handleSetGroupScore = (data) => {
  groupScore.value = data.score;
};

/** 保存编辑 */
const save = () => {
    if (!formData.value) {
      // EleMessage.error("请选择试题")
      // return;
      ElNotification({
        title: '系统提示',
        message: '请选择试题',
        type: 'warning',
        duration: 1500,
        position: 'top-right'
      });
      return false
    }
    form.sid = props.currentPId
    form.gid = props.currentGId
    form.score = groupScore.value;
    form.qid = formData.value && formData.value.length > 0 ? formData.value.join(',') : ''
    saveSurveyPquestionsByQuestions(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      // EleMessage.error(e.message);
      ElNotification({
        title: '系统提示',
        message: e.message,
        type: 'error',
        duration: 1500,
        offset: 15,
        position: 'top-right'
      });
    }).finally(() => {
    });
  }
;

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
</script>
