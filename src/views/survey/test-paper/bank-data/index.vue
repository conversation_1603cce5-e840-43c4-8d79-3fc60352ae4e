<template>
  <ele-pro-table ref="tableRef"
                 row-key="id"
                 :columns="columns"
                 :datasource="datasource"
                 :border="true"
                 :show-overflow-tooltip="true"
                 v-model:selections="selections"
                 highlight-current-row
                 tooltip-effect="light"
                 cache-key="systemDictDataTable"
                 :footer-style="{ paddingBottom: '3px' }"
                 style="padding-bottom: 0">
    <template #toolbar>
      <el-button v-if="hasPermission('code:codeCommon:save')" size="small"
                 @click="openEdit()">
        新建试题
      </el-button>
      <el-button v-if="hasPermission('code:codeCommon:save')" size="small"
                 @click="openDataBank()">
        从题库中选择试题
      </el-button>
      <el-button size="small"
                 class="ele-btn-icon"
                 @click="remove()"> 删除
      </el-button>
    </template>
    <template #name="{ row }">
      <el-link type="primary" underline="never" @click="openEdit(row)">
        {{ row.name }}
      </el-link>
    </template>
    <template #score="{ row }">
      <el-form-item label=""
                    class="form-error-popper"
                    style="margin-bottom: 0 !important">
        <el-input-number size="small" :min="0"
                         :max="99999"
                         v-model="row.score"
                         placeholder="请输入试题分数"
                         controls-position="right"
                         class="ele-fluid"
                         @blur="updateBlurValue(row)"/>
      </el-form-item>
    </template>
    <!-- 表头查询 -->
    <template #nameHeader="{ column }">
      <div style="display: flex; align-items: center">
        <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
          {{ column.label }}
        </div>
        <name-filter @search="onNameFilter"/>
      </div>
    </template>
  </ele-pro-table>
  <DataEdit v-model="showEdit"
            :data="current"
            :currentPId="currentPId"
            :currentGId="currentGId"
            :dictName="dictName"
            :userType="userType"
            :routeType="routeType"
            @done="reload"/>
  <DataBank v-model="showDataBank"
            :data="current"
            :currentPId="currentPId"
            :currentGId="currentGId"
            :dictName="dictName"
            :userType="userType"
            :routeType="routeType"
            @done="reload"/>
</template>

<script setup>
import {ref, watch, computed, reactive} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import DataEdit from './components/edit.vue';
import DataBank from './components/bank.vue';
import nameFilter from './components/name-filter.vue';
import {usePermission} from '@/utils/use-permission';
import {getToken} from "@/utils/token-util";
import {getCurrentRole} from "@/utils/current-role-util";
import {
  getSurveyPaperQuestions,
  removes, saveSurveyQuestions,
} from "../bank-data/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import NameFilter from "@/views/exam/my-exam/components/name-filter.vue";
import {useDictData} from "@/utils/use-dict-data.js";
import Step2Edit from "@/views/survey/test-paper/details/components/step2-edit.vue";

const emit = defineEmits(['doneRefreshGroup']);
const {hasPermission,} = usePermission();

const props = defineProps({
  /** 试卷组id */
  currentGId: String,
  /**试卷ID*/
  currentPId: String,
  dictName: String,
  RandomString: String,
  routeType: String,
  userType: String,
});
const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
useDictData(['surveyBankData'], {
  dictFieldUrl: '/survey/survey-questions-calss',
  getValType: 'id'
});
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

/** 表格实例 */
const tableRef = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 表格列配置 */
const columns = computed(() => {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'qtype',
      label: '题型',
    },
    {
      prop: 'name',
      label: '试题名称',
      headerSlot: 'nameHeader',
      slot: 'name'
    },
    {
      prop: 'cid',
      label: '试题分类',
      formatter: (row) => {
        let dictsData = dicts.value['surveyBankData'];
        let existData = dictsData.filter(obj => obj.dictDataCode === row.cid)
        return existData.length > 0 ? existData[0].dictDataName : '';
      },
    },
  ]
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);
const showDataBank = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getSurveyPaperQuestions({
    ...where, ...orders, ...filters,
    page,
    limit,
    sid: props.currentPId,
    gid: props.currentGId,
  });
};

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reloadCom({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reloadCom(lastWhere);
  }
};

/** 刷新表格 */
const reloadCom = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

/** 刷新表格 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
  emit('doneRefreshGroup', {pid: props.currentPId, gid: props.currentGId});
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 打开编辑弹窗 */
const openDataBank = (row) => {
  current.value = row ?? null;
  showDataBank.value = true;
};

const updateBlurValue = (row) => {
  if (row && row.score) {
    loading.value = true;
    saveSurveyQuestions(row).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      reload()
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }
}

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    let data = {
      id: rows.map((d) => d.id).join(','),
      sid: props.currentPId,
      gid: props.currentGId,
    }
    removes(data).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

// 监听字典id变化
watch(
  () => props.currentGId,
  () => {
    reloadCom();
  }
);
// 监听字典id变化
watch(
  () => props.RandomString,
  () => {
    reloadCom();
  }
);
</script>
