import request from '@/utils/request';

/**
 * 管理着根据问卷ID，答题ID获取问卷答题详情信息
 */
export async function getSurveyVO(params) {
    const res = await request.get('/survey/survey/getSurveyVO', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getExamPaper(params) {
    const res = await request.get('/survey/survey', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/survey/survey/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getSurveyById(id) {
    const res = await request.get('/survey/survey/' + id);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/survey/survey/operation', data);
    if (res.data.code === 0&&res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function operationSurveyState(data) {
    const res = await request.post('/survey/survey/state', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/survey/survey/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
