<template>
  <ele-drawer
    size="40%"
    :title="isUpdate ? '修改测评指标 [' + data.zbmc + ']' : '添加测评指标'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" size="small" :model="form" labelWidth="auto">
      <el-row :gutter="4">
        <template v-for="item in baseFileds">
          <el-col
            :span="item.colProps"
            v-if="item.showFlag === '是' && item.selfModifyFlag === '是'"
          >
            <ProFormItem
              :item="item"
              :model="form"
              @updateItemValue="
                (prop, value) => updateFormValue(item, prop, value)
              "
            >
              <template
                v-for="name in Object.keys($slots).filter(
                  (k) =>
                    !['default', 'footer', 'topExtra', 'bottomExtra'].includes(
                      k
                    )
                )"
                #[name]="slotProps"
              >
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)"
        >{{ enterType === 'preview' ? '关闭' : '取消' }}
      </el-button>
      <el-button
        v-if="enterType !== 'preview'"
        plain
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { operationFdycpItem } from '../api/index.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
  import { editFileds } from '@/views/survey/teacher-evaluate/js/fileds.js';
  import { useDictData } from '@/utils/use-dict-data.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    enterType: String, //perview：考试详情预览 add:添加考试 edit:编辑考试
    /** 添加时机构id */
    organizationId: String,
    routeType: String,
    currentEvaluateConfigData: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    khpc: '',
    zblx: '',
    cpdxArray: [],
    cpdxjsidArray: [],
    zbckz: '',
    sfcyjs: '',
    djrjs: '',
    cpdx: '',
    sort: void 0
  });

  const updateFormValue = (item, prop, newVal) => {
    console.log(item, prop, newVal);
    let value = newVal;
    form[item.prop] = value;
    if (item.fieldLinks) {
      //获取选择值下面所有的显示字段信息
      let showfields = item.fieldLinks.filter((f) => {
        return f.fieldVal === value;
      });
      if (showfields.length > 0) {
        showfields.forEach((sf) => {
          nextTick(() => {
            //切换选择值，清空之前选项所赋值
            form[sf.linkField] = '';
            //动态切换选择值，关联具体字段的显示隐藏
            baseFileds.value.filter((init) => {
              init.showFlag =
                sf.linkField === init.prop ? sf.showFlag : init.showFlag;
              init.selfModifyFlag = init.showFlag;
            });
          });
        });
      }
    }
  };

  /** 表单实例 */
  const formRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 提交 */
  const onSubmit = () => {
    formRef['value']?.validate?.((valid, obj) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      if (form.cpdxArray) form.cpdx = form.cpdxArray.join(',') ?? '';
      if (form.cpdxjsidArray)
        form.cpdxjsid = form.cpdxjsidArray.join(',') ?? '';
      let data = {
        ...form
      };
      operationFdycpItem(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        })
        .finally(() => {
          userStore.setDicts([], 'fdycpzb');
          useDictData(['fdycpzb'], {
            dictFieldUrl: '/teacherEvaluate/fdycp-item',
            valueField: 'id',
            textField: 'khpc'
          });
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const baseFileds = ref([]);
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        baseFileds.value = editFileds();
        if (props.data) {
          assignFields({
            ...props.data,
            cpdxArray: props.data.cpdx ? props.data.cpdx.split(',') : [],
            cpdxjsidArray: props.data.cpdxjsid
              ? props.data.cpdxjsid.split(',')
              : []
          });
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
  watch(
    () => props.enterType,
    (enterType) => {
      if (enterType) {
        baseFileds.value.forEach((filed) => {
          if (enterType === 'preview') {
            if (filed.type === 'dictSelect') {
              filed.props.disabled = props.enterType === 'preview';
              filed.props.readonly = props.enterType === 'preview';
            } else if (
              filed.type === 'switch' ||
              filed.type === 'radioButton'
            ) {
              filed['props'] = {
                readonly: props.enterType === 'preview',
                disabled: props.enterType === 'preview'
              };
            } else {
              filed['props'] = {
                readonly: props.enterType === 'preview'
              };
            }
          }
        });
      }
    }
  );
</script>
