/**
 * 题库选项配置
 */
export function editFileds() {
  return [
    {
      prop: 'khpc',
      label: '考核批次',
      type: 'input',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是'
    },
    {
      prop: 'zblx',
      label: '指标类型',
      type: 'radioButton',
      options: [
        { label: '分值', value: '分值' },
        { label: '比例', value: '比例' }
      ],
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 9
    },
    {
      prop: 'zbckz',
      label: '分值/百分比',
      type: 'inputNumber',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 9
    },
    {
      prop: 'sfcyjs',
      label: '是否参与计算',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 4
    },
    {
      prop: 'djrjs',
      label: '答卷人角色',
      showFlag: '是',
      required: true,
      selfModifyFlag: '是',
      type: 'dictSelect',
      // typeKey: 'multipleSelect',
      props: {
        code: 'listRoles',
        refresh: true
      }
    },
    {
      prop: 'cpdxArray',
      type: 'dictSelect',
      typeKey: 'multipleSelect',
      label: '测评对象岗位',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      props: {
        code: 'fdycpdx',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: '/teacherEvaluate/fdycp-item/evaluateStaff',
          valueField: 'name',
          textField: 'name'
        }
      }
    },
    {
      prop: 'cpdxjsidArray',
      label: '测评对象角色',
      showFlag: '是',
      required: true,
      selfModifyFlag: '是',
      type: 'dictSelect',
      typeKey: 'multipleSelect',
      props: {
        code: 'listRoles',
        refresh: true,
        dicQueryParams: {
          params: { roleScope: '班级' }
        }
      }
    },
    {
      prop: 'sort',
      label: '排序',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是'
    }
  ];
}
