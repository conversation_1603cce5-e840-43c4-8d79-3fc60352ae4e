/**
 * 测评指标控制器
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getFdycpItem(params) {
  const res = await request.get('/teacherEvaluate/fdycp-item/page', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryFdycpItemPage(params) {
  const res = await request.get('/teacherEvaluate/fdycp-item/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFdycpItemById(id) {
  const res = await request.get('/teacherEvaluate/fdycp-item/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operationFdycpItem(data) {
  const res = await request.post('/teacherEvaluate/fdycp-item/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removesFdycpItem(data) {
  const res = await request.post('/teacherEvaluate/fdycp-item/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
