/**
 * 试题控制器
 */
import request from '@/utils/request.js';

/**
 * 导入
 */
export async function importData(file, projectId,) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('projectId', projectId);
  // formData.append('type', userType);
  const res = await request.post('/exam/exam-questions/importExamQuestions', formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取题数
 * @param params {cid：试题分类，sttype：题型}
 */
export async function gettSurveyQuestionsSum(params) {
  const res = await request.get('/survey/survey-questions/getSum', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getSurveyQuestions(params) {
  const res = await request.get('/survey/survey-questions/', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/survey/survey-questions/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 固定试题模式，题库试题去重
 */
export async function queryQuestionsNotInPaper(params) {
  const res = await request.get('/survey/survey-questions/getQuestionsNotInPaper', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getSurveyQuestionsById(id) {
  const res = await request.get('/survey/survey-questions/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/survey/survey-questions/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/survey/survey-questions/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
