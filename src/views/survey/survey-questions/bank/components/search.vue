<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }" :footer-style="{padding:'8px 12px!important'}">
    <el-form size="small" label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
         <el-col  :md="6" :sm="12" :xs="24">
          <el-form-item label="流程名称">
            <el-input clearable
                      v-model.trim="form.name"
                      placeholder="请输入工作流名称"/>
          </el-form-item>
        </el-col>
         <el-col  :md="6" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" type="primary" v-for="item in data" @click="handleCilck(item)">
        {{ item.title }}
      </el-button>
    </template>
  </ele-card>
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data.js';
import {PlusOutlined} from "@/components/icons/index.js";
import {ref} from "vue";

const emit = defineEmits(['search', 'handleClick']);

const props = defineProps({
  userType: String,
});

/** 表单数据 */
const [form, resetFields] = useFormData({
  name: '',
});


const data = ref(
    [
      {
        value: 'add',
        title: ' + 新建',
      },
    ]
);

/** 搜索 */
const search = () => {
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
/** 打开组group编辑弹窗 */
const handleCilck = (row) => {
  emit('handleClick', row.value);
};

</script>
