<!-- 搜索表单 -->
<template>
  <el-form size="small" @keyup.enter="search" @submit.prevent="">
    <el-row :gutter="8">
      <el-col  :md="6" :sm="12" :xs="24">
        <el-form-item label="试题分类">
          <dict-data :code="'surveyBankData_'+routeType"
                     :dicQueryParams="{
                        dictFieldUrl:'/survey/survey-questions-calss',
                        getValType:'id',
                        params:{type:routeType}
                      }"
                     v-model="form.cid" placeholder="请选择试题分类"/>
        </el-form-item>
      </el-col>
      <el-col  :md="6" :sm="12" :xs="24">
        <el-form-item label="试题类型">
          <dict-data code="qtype" type="select"
                     :dicQueryParams="{getValType:'name'}"
                     v-model="form.qtype" placeholder="请选择试题类型"/>
        </el-form-item>
      </el-col>
      <el-col  :md="6" :sm="12" :xs="24">
        <el-form-item label-width="16px">
          <el-button type="primary" size="small" plain @click="search">查询</el-button>
          <el-button @click="reset" size="small">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data.js';

const emit = defineEmits(['search', 'handleClick']);

const props = defineProps({
  userType: String,
  routeType: String,
});

/** 表单数据 */
const [form, resetFields] = useFormData({
  cid: '',
  sttype: '',
});

/** 搜索 */
const search = () => {
  console.log(form)
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};

</script>
