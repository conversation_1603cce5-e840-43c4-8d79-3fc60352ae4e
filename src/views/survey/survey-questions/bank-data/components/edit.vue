<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    size="65%"
    :title="
      (isUpdate ? '修改' : '添加') + '[' + dictName + ']' + '试题' + routeType
    "
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
      label-width="auto"
      size="small"
      @submit.prevent=""
    >
      <el-form-item label="试题名称" prop="name">
        <el-input clearable v-model="form.name" placeholder="请输入试题名称" />
      </el-form-item>
      <el-row :gutter="8">
        <el-col :span="12">
          <el-form-item label="试题类型" prop="qtype">
            <dict-data
              :key="dictKey"
              code="qtype"
              type="radioButton"
              :dicQueryParams="{ getValType: 'name' }"
              v-model="form.qtype"
              placeholder="请选择试题类型"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="routeType === 'xljkjy'" :span="12">
          <el-form-item label="排序">
            <el-input-number
              :min="0"
              :max="99999"
              v-model="form.sort"
              placeholder="请输入排序值"
              controls-position="right"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item
        label=""
        v-if="form.qtype && ['单选题', '多选题'].includes(form.qtype)"
      >
        <TablePreview
          :ref="(el) => getRiskSpreadRef(el, 0)"
          :approverId="data ? data.id : ''"
          dataName="选项设置"
          :approverType="'surveyQuestionSel_' + form.qtype"
          @onDoneGroup="handleDoneGroup"
        />
      </el-form-item>
      <el-row v-if="form.qtype === '评分题'" :gutter="8">
        <el-col :span="12">
          <el-form-item label="最小分值" prop="sort">
            <el-input-number
              :min="0"
              :max="99999"
              v-model="form.minScore"
              placeholder="请输入最小分值"
              controls-position="right"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大分值" prop="sort">
            <el-input-number
              :min="0"
              :max="99999"
              v-model="form.maxScore"
              placeholder="请输入最大分值"
              controls-position="right"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item v-if="routeType === 'xljkjy'" label="备注">
        <el-input
          clearable
          v-model="form.bz"
          :rows="6"
          type="textarea"
          show-word-limit
          maxlength="255"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <ele-text
        style="float: left"
        v-if="validMsg"
        type="danger"
        :icon="CloseCircleOutlined"
      >
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="handleSubmit"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operation } from '../../api/index.js';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
  import { CloseCircleOutlined } from '@/components/icons/index.js';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const emit = defineEmits(['done', 'update:modelValue']);

  // 已缓存的字典
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  const dictKey = ref(1);
  console.log('dicts :>> ', dicts.value['qtype']);
  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    codeType: String,
    dictName: String,
    routeType: String,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    qtype: '',
    type: props.routeType.toUpperCase(),
    name: '',
    sort: void 0,
    cid: '',
    bz: '',
    minScore: 0,
    maxScore: 10,
    surveyQuestionsOptionsList: null
  });

  /** 表单验证规则 */
  const rules = reactive({
    qtype: [
      {
        required: true,
        message: '请选择试题类型',
        type: 'string',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入名称',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const riskSpreadRefList = ref([]);
  const getRiskSpreadRef = (el, index) => {
    if (el) {
      riskSpreadRefList.value[index] = el;
    }
  };

  const formData = ref(null);
  const handleDoneGroup = async (data) => {
    formData.value = data;
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  const validMsgCount = ref(0);
  /** 存放子组件的数组 */
  let resultArr = reactive([]);
  /** 用来创建 Promise 实例，为多个组件校验使用 */
  const checkForm = (formChild) => {
    validMsg.value = null;
    let result = new Promise((resolve, reject) => {
      formChild.formRef?.validate((valid, obj) => {
        if (valid) {
          resolve(true);
        } else {
          const errors = obj ? Object.keys(obj).length : 0;
          validMsgCount.value += errors;
          resolve(false);
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  const handleSubmit = async () => {
    await riskSpreadRefList.value?.forEach((child) => {
      checkForm(child);
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []; //每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save();
      } else {
        validMsg.value = `选项设置共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  };

  /** 保存编辑 */
  const save = () => {
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const data = {
        ...form,
        cid: props.codeType,
        surveyQuestionsOptionsList: formData.value
        // answer: form.qtype === '多选题' ? form.answer.join(',') : form.answer,
      };
      data.type = data.type.toUpperCase();
      console.log('save', JSON.stringify(data));
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => form.qtype,
    (qtype) => {
      console.log(qtype);
    }
  );

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
            // answer: props.data.qtype === '多选题' ? props.data.answer.split(',') : props.data.answer
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  watch(
    () => dicts.value['qtype'],
    (newVal) => {
      // 辅导员测评目前只让添加单选和多选，方便汇总算分
      if (newVal && newVal.length > 2 && props.routeType == 'fdycp') {
        let result = newVal.slice(0, 2);
        userStore.setDicts(result, 'qtype');
        dictKey.value = dictKey.value + 1;
      }
    }
  );
</script>
