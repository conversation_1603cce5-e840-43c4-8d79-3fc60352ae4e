import request from '@/utils/request';
import {setToken} from '@/utils/token-util.js';
import * as qs from 'postcss';
import * as http from 'node:http';

/**
 * 获取OAuth token
 * @param ticket
 * @returns {Promise<*>}
 */
export async function getOAuthToken(ticket) {
  console.log('获取OAuth===', ticket);
  const res = await request.post('/cas/callback',
    `ticket=${ticket}`,  // 注意这里改为字符串形式
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      },
    }
  );
  console.log('获取OAuth token===', res);
  if (res.data.code === 0) {
    setToken(res.data.data?.access_token, res.data.data?.remember);
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取返回后跳转地址
 * ticket
 */
export async function getOAuthLoginUrl(data) {
  const res = await request.post('/cas/loginUrl', data);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
