<template>
  <div class="oauth-callback-container">
    <!-- 科技感背景元素（与登录页保持一致） -->
    <div class="tech-bg">
      <div class="tech-grid"></div>
      <div class="tech-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>
      <div class="tech-lines">
        <div class="line line-1"></div>
        <div class="line line-2"></div>
      </div>
    </div>

    <div class="status-container">
      <div class="status-card">
        <!-- 头部LOGO -->
        <div class="status-header">
          <h1 class="status-title">中国农业大学</h1>
          <div class="status-subtitle">统一身份认证中转</div>
        </div>

        <!-- 状态内容 -->
        <div class="status-content">
          <div class="loading-animation">
            <el-icon class="loading-icon" :size="40">
              <Loading/>
            </el-icon>
          </div>
          <div class="status-message">{{ statusMessage }}</div>
          <div class="status-details" v-if="errorDetails">
            <el-icon>
              <InfoFilled/>
            </el-icon>
            <span>{{ errorDetails }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="status-actions" v-if="showActions">
          <el-button type="primary" @click="retryOAuth" :loading="loading">
            重试
          </el-button>
          <el-button @click="goToLogin">返回登录页</el-button>
        </div>
      </div>
      <div class="copyright">© 中国农业大学 版权所有 | 技术支持：北京三易拓科技有限公司</div>
    </div>
  </div>
</template>
<script setup>
import {ref, onMounted, nextTick} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import {Loading, InfoFilled} from '@element-plus/icons-vue';
import {getOAuthToken} from './api/index.js';
import {usePageTab} from '@/utils/use-page-tab.js';

const router = useRouter();
const route = useRoute();
const {goHomeRoute, cleanPageTabs} = usePageTab();

// 状态管理
const statusMessage = ref('正在处理认证信息...');
const errorDetails = ref('');
const loading = ref(false);
const showActions = ref(false);

// 处理OAuth回调
const handleOAuthCallback = async () => {
  try {
    loading.value = true;
    statusMessage.value = '正在验证授权信息...';
    // 1. 从URL获取授权码和状态
    const {ticket} = route.query;
    console.log('处理OAuth回调', ticket);
    // 检查是否有错误
    if (!ticket) {
      // 如果没有code参数，可能是直接访问了回调页面
      throw new Error('未获取到授权码，请从登录页重新发起认证');
    }
    console.log('test===', ticket);

    // 2. 使用授权码获取access token
    statusMessage.value = '正在获取访问令牌...';
    await getOAuthToken(encrypt(ticket));

    // 3. 处理登录成功逻辑
    statusMessage.value = '登录成功，正在跳转...';

    // 4. 清除页签并跳转
    cleanPageTabs();
    goHome();

  } catch (error) {
    console.error('OAuth登录失败:', error);
    statusMessage.value = '登录失败';
    errorDetails.value = error.message || '未知错误';
    showActions.value = true;

    ElMessage.error({
      message: `第三方登录失败: ${error.message || '未知错误'}`,
      duration: 5000,
    });
  } finally {
    loading.value = false;
  }
};

// 跳转到首页
const goHome = () => {
  const {query} = router.currentRoute.value;
  console.log('跳转到首页query===', query);
  goHomeRoute(query.from);
};

// 重试OAuth流程
const retryOAuth = () => {
  showActions.value = false;
  errorDetails.value = '';
  window.location.href = '/login'; // 返回登录页重新发起认证
};

// 返回登录页
const goToLogin = () => {
  router.replace('/login');
};

// 修改后的 insertScript 函数
const insertScript = () => {
  return new Promise((resolve, reject) => {
    if (window.encrypt) {
      return resolve();
    }
    const scriptElement = document.createElement('script');
    scriptElement.type = 'text/javascript';
    scriptElement.async = true;
    scriptElement.src = window.location.origin + '/api/sec_js';

    scriptElement.onload = () => {
      if (window.encrypt) {
        resolve();
      } else {
        reject(new Error('加密工具未正确加载'));
      }
    };

    scriptElement.onerror = () => {
      reject(new Error('加载加密脚本失败'));
    };

    document.head.appendChild(scriptElement);
  });
};

// 在 onMounted 中
onMounted(async () => {
  try {
    await insertScript();
    await handleOAuthCallback();
  } catch (error) {
    console.error('初始化失败:', error);
    statusMessage.value = '系统初始化失败';
    errorDetails.value = error.message;
    showActions.value = true;
  }
});
</script>
<style lang="scss" scoped>
.oauth-callback-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 复用登录页的科技背景样式 */
.tech-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px),
  linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px);
  background-size: 60px 60px;
  opacity: 0.5;
}

.tech-circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .circle {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.2;
    animation: float 20s infinite ease-in-out;

    &.circle-1 {
      width: 400px;
      height: 400px;
      background: #3b82f6;
      top: -50px;
      left: -50px;
      animation-delay: 0s;
    }

    &.circle-2 {
      width: 500px;
      height: 500px;
      background: #6366f1;
      bottom: -100px;
      right: -100px;
      animation-delay: 5s;
    }

    &.circle-3 {
      width: 300px;
      height: 300px;
      background: #60a5fa;
      top: 50%;
      left: 30%;
      animation-delay: 8s;
    }
  }
}

.tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);

    &.line-1 {
      top: 20%;
      left: 0;
      width: 100%;
      height: 1px;
      animation: slideRight 15s linear infinite;
    }

    &.line-2 {
      top: 60%;
      left: 0;
      width: 100%;
      height: 1px;
      animation: slideLeft 20s linear infinite;
      animation-delay: 3s;
    }
  }
}

.status-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 500px;
  padding: 20px;
}

.status-card {
  border-radius: 10px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.96);
  box-shadow: 0 12px 28px rgba(59, 130, 246, 0.15);
  padding: 30px;
  text-align: center;
}

.status-header {
  margin-bottom: 30px;

  .status-title {
    font-size: 24px;
    font-weight: 700;
    color: #1e40af;
    margin: 0;
  }

  .status-subtitle {
    font-size: 14px;
    color: #64748b;
    margin-top: 8px;
  }
}

.status-content {
  padding: 20px 0;

  .loading-animation {
    margin-bottom: 20px;

    .loading-icon {
      color: #3b82f6;
      animation: rotate 2s linear infinite;
    }
  }

  .status-message {
    font-size: 18px;
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 10px;
  }

  .status-details {
    font-size: 14px;
    color: #ef4444;
    background: #fef2f2;
    padding: 10px;
    border-radius: 6px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-icon {
      margin-right: 8px;
    }
  }
}

.status-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.copyright {
  margin-top: 20px;
  color: #94a3b8;
  font-size: 12px;
  text-align: center;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(10px, 10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes slideLeft {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style>
