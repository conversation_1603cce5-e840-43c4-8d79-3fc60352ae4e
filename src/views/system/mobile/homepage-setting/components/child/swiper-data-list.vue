<template>
  <el-row style="margin: 10px">
    <el-button
      size="small"
      class="ele-btn-icon"
      @click="openEdit()"
      style="margin-bottom: 10px"
    >
      新建子项
    </el-button>
    <ele-table size="small" style="table-layout: fixed; text-align: left">
      <colgroup>
        <col width="20" />
        <col width="78" />
        <col width="78" />
        <col width="30" />
        <col width="30" />
        <col width="40" />
      </colgroup>
      <thead>
        <tr>
          <th></th>
          <th>标题</th>
          <th>描述</th>
          <th>排序</th>
          <th>是否启用</th>
          <th>操作</th>
        </tr>
      </thead>
      <vue-draggable
        tag="tbody"
        item-key="id"
        v-model="itemList"
        handle=".sort-handle"
        :animation="300"
        :set-data="() => void 0"
        @update="updateSort"
      >
        <template #item="{ element }">
          <tr>
            <td style="text-align: center">
              <ele-text :icon="Rank" class="sort-handle" style="cursor: move" />
            </td>
            <td>{{ element.title }}</td>
            <td>{{ element.description }}</td>
            <td>
              {{ element.sortOrder }}
            </td>
            <td>
              {{ element.enabled ? '是' : '否' }}
            </td>
            <td>
              <el-link
                type="primary"
                underline="never"
                @click="openEdit(element)"
              >
                修改
              </el-link>
              <el-divider direction="vertical" />
              <el-link
                type="primary"
                underline="never"
                @click="remove(element)"
              >
                删除
              </el-link>
            </td>
          </tr>
        </template>
      </vue-draggable>
    </ele-table>
    <swiper-data-edit
      v-model="showEdit"
      :data="current"
      :currentId="currentId"
      @done="handleChildDone"
    />
  </el-row>
</template>

<script setup>
  import VueDraggable from 'vuedraggable';
  import { Rank } from '@element-plus/icons-vue';

  import { ref, watch, computed, reactive } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage} from "element-plus";
  import SwiperDataEdit from './swiper-data-edit.vue';

  const props = defineProps({
    dataList: {
      type: Array,
      default: () => []
    }
  });

  const itemList = ref(props.dataList);
  itemList.value.map((i, index) => {
    i.sortOrder = index + 1;
  });

  /** 当前编辑数据 */
  const current = ref(null);
  /** 当前编辑数据的id */
  const currentId = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    currentId.value = row && row.id ? row.id : null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    const rows = [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        itemList.value = itemList.value.filter(
          (item) => item.sortOrder !== row.sortOrder
        );
      })
      .catch(() => {});
  };

  // 更新次序
  const updateSort = (evt) => {
    evt.preventDefault();
    itemList.value.map(function (value, index) {
      value.sortOrder = index + 1;
      return value;
    });
  };
  const handleChildDone = (obj) => {
    if (obj.id) {
      // 修改已上传过的图片
      itemList.value.map((item) => {
        if (item.id == obj.id) {
          Object.assign(item, obj);
          if (item.deleteImage) {
            delete item.imageDataUrl;
          }
        }
      });
    } else {
      if (obj.sortOrder) {
        // 新上传的修改
        itemList.value.map((item) => {
          if (item.sortOrder == obj.sortOrder) {
            Object.assign(item, obj);
          }
        });
      } else {
        // 新增的
        itemList.value.push(obj);
        itemList.value.map((item, index) => {
          item.sortOrder = index + 1;
        });
      }
    }
  };
  defineExpose({ itemList });
</script>
