<template>
  <el-row style="margin: 10px">
    <el-button
      size="small"
      class="ele-btn-icon"
      @click="openEdit()"
      style="margin-bottom: 10px"
    >
      维护应用
    </el-button>
    <ele-table size="small" style="table-layout: fixed; text-align: left">
      <colgroup>
        <col width="20" />
        <col width="78" />
        <col width="78" />
        <col width="30" />
        <!-- <col width="30" />
        <col width="40" /> -->
      </colgroup>
      <thead>
        <tr>
          <th></th>
          <th>应用</th>
          <th>路由</th>
          <th>排序</th>
          <!-- <th>是否启用</th>
          <th>操作</th> -->
        </tr>
      </thead>
      <vue-draggable
        tag="tbody"
        item-key="id"
        v-model="itemList"
        handle=".sort-handle"
        :animation="300"
        :set-data="() => void 0"
        @update="updateSort"
      >
        <template #item="{ element }">
          <tr>
            <td style="text-align: center">
              <ele-text :icon="Rank" class="sort-handle" style="cursor: move" />
            </td>
            <td>
              <div style="display: flex; align-items: center">
                <IconPark
                  v-if="element.app && element.app.icon"
                  size="14"
                  :name="element.app && element.app.icon"
                  :color="element.app && element.app.iconColor"
                  style="margin-right: 4px"
                />
                {{ element.app && element.app.name }}
              </div>
            </td>
            <td>{{ element.app && element.app.path }}</td>
            <td>
              {{ element.sortOrder }}
            </td>
          </tr>
        </template>
      </vue-draggable>
    </ele-table>
    <!-- </el-col> -->
    <menu-data-edit
      v-model="showEdit"
      :dataList="itemList"
      :roleId="roleId"
      :currentId="currentId"
      @done="handleChildDone"
    />
  </el-row>
</template>

<script setup>
  import VueDraggable from 'vuedraggable';
  import { Rank } from '@element-plus/icons-vue';
  import { ref, watch, computed, reactive } from 'vue';
  import IconPark from '@/components/IconPark/index.vue';
  import MenuDataEdit from './menu-data-edit.vue';

  const props = defineProps({
    /** 角色id */
    roleId: String,
    dataList: {
      type: Array,
      default: () => []
    }
  });

  const itemList = ref(props.dataList);
  itemList.value.map((i, index) => {
    i.sortOrder = index + 1;
  });
  /** 当前编辑数据 */
  const current = ref(null);
  /** 当前编辑数据的id */
  const currentId = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    currentId.value = row && row.id ? row.id : null;
    showEdit.value = true;
  };
  /** 更新次序 */
  const updateSort = (evt) => {
    evt.preventDefault();
    itemList.value.map(function (value, index) {
      value.sortOrder = index + 1;
      return value;
    });
  };
  const handleChildDone = (obj) => {
    itemList.value = obj;
  };
  defineExpose({ itemList });
</script>
