<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="660"
    :title="'维护应用'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      size="small"
      :model="form"
      :rules="rules"
      label-width="auto"
      @submit.prevent=""
    >
      <el-form-item label="应用" prop="item">
        <el-select v-model="form.item" multiple placeholder="请选择">
          <el-option-group
            v-for="group in appList"
            :key="group.id"
            :label="group.name"
          >
            <el-option
              v-for="item in group.apps"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              @click="onAppChange(item)"
            >
              <div style="display: flex; align-items: center">
                <IconPark
                  v-if="item.icon"
                  size="14"
                  :name="item.icon"
                  :color="item.iconColor"
                  style="margin-right: 4px"
                />
                {{ item.name }}
              </div>
            </el-option>
          </el-option-group>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch, nextTick } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { useFormData } from '@/utils/use-form-data';
  import { optionalApp } from '../../api/child';
  import IconPark from '@/components/IconPark/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    currentId: String,
    /** 角色Id */
    roleId: String,
    /** 已选中的应用 */
    dataList: {
      type: Array,
      default: () => []
    }
  });
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 可选择应用列表 */
  const appList = ref([]);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    url: void 0,
    description: '',
    enabled: true,
    uploadImage: '',
    deleteImage: false,
    item: []
  });

  /** 表单验证规则 */
  const rules = reactive({
    title: [
      {
        required: true,
        message: '请输入标题',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      let childList = appList.value.map((i) => i.apps).flat(); // 所有的子菜单并铺平为一维数组
      let selectedList = childList.filter((c) => form.item.includes(c.id));
      let result = [];
      selectedList.forEach((item, index) => {
        result.push({
          app: {
            id: item.id,
            name: item.name,
            icon: item.icon,
            iconColor: item.iconColor,
            path: item.path
          },
          enabled: true,
          sortOrder: index + 1
        });
        item.sortOrder = index + 1;
      });
      console.log('result', result);
      emit('done', result);
      updateModelValue(false); //关闭弹窗
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        getDataList();
        /**回显选中的应用 */
        if (props.dataList) {
          assignFields({
            item: props.dataList.map((item) => item.app && item.app.id)
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  /** 选中应用 */
  const onAppChange = (item) => {
    console.log('item', item, form.item, form);
  };
  const getDataList = () => {
    optionalApp({ roleId: props.roleId })
      .then((res) => {
        appList.value = res;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
</script>
