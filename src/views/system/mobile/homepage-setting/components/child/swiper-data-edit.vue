<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="660"
    :title="isUpdate ? '修改子项 [' + data.title + ']' : '添加子项'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      size="small"
      :model="form"
      :rules="rules"
      label-width="auto"
      @submit.prevent=""
    >
      <el-form-item label="标题" prop="title">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.title"
          placeholder="请输入标题"
        />
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          :rows="1"
          type="textarea"
          show-word-limit
          maxlength="12"
          v-model="form.description"
          placeholder="请输入描述"
        />
      </el-form-item>
      <el-form-item prop="url">
        <template #label>
          <span>路由地址</span>
        </template>
        <el-input
          clearable
          :maxlength="100"
          v-model="form.url"
          :placeholder="'请输入路由地址'"
        />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch
          inline-prompt
          active-text="是"
          inactive-text="否"
          :model-value="form.enabled"
          @change="updateEnableValue"
        />
      </el-form-item>
      <el-form-item label="上传图片">
        <file-upload
          :limit="1"
          v-model="form.uploadImage"
          listType="image"
          @change="changeDelIds"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import FileUpload from '@/components/FileUpload/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    currentId: String,
    /** 修改回显的数据 */
    data: Object
  });
  console.log('111', props.data);
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    title: '',
    url: void 0,
    description: '',
    enabled: true,
    uploadImage: '',
    deleteImage: false
  });

  /** 表单验证规则 */
  const rules = reactive({
    title: [
      {
        required: true,
        message: '请输入标题',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 要删除的附件ID，字符串逗号分割*/
  const deleteFileIds = ref(null);
  /** 是否启用选择改变 */
  const updateEnableValue = (value) => {
    form.enabled = value;
  };
  /** changeDelIds */
  const changeDelIds = (value) => {
    deleteFileIds.value = value;
  };
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      let fileArray = [];
      let doneArray = [];
      if (form.uploadImage.length > 0) {
        form.uploadImage.forEach((e) => {
          if (e.status !== 'done') {
            fileArray.push(e);
          } else {
            doneArray.push(e);
          }
        });
      }
      let result = {
        ...form
      };
      if (fileArray.length > 0) {
        result.uploadImage = fileArray[0].file;
      }
      /** 上传组件里有照片且设置了只能上传一张 */
      if (props.data && props.data.imageDataUrl) {
        // 已上传过照片并存上了
        if (form.uploadImage.length > 0) {
          if (doneArray.length == 0) {
            result.deleteImage = true;
          }
        } else {
          result.deleteImage = true;
        }
      } else {
        result.deleteImage = false;
      }
      emit('done', result);
      updateModelValue(false); //关闭弹窗
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          let oldFiles = [];
          if (props.data.imageDataUrl) {
            oldFiles = [
              {
                url: props.data.imageDataUrl,
                isImageFile: true,
                status: 'done'
              }
            ];
          }
          assignFields({
            ...props.data,
            uploadImage: oldFiles.length > 0 ? oldFiles : ''
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
