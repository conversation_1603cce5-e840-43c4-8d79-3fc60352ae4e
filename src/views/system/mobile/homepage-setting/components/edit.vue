<template>
  <ele-card
    :body-style="{ padding: '5px!important', minHeight: pageHeight + 6 + 'px' }"
  >
    <template #header>
      <ele-text style="padding: 2px">{{
        isUpdate ? '修改模块[' + data.name + ']' : '添加模块'
      }}</ele-text>
    </template>
    <template #extra>
      <el-button
        plain
        size="small"
        type="primary"
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="small"
      label-position="top"
      style="margin: 10px"
      @submit.prevent=""
    >
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="模块名称" prop="name">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.name"
              placeholder="请输入模块名称"
            />
          </el-form-item>
          <el-form-item label="功能描述" prop="functionType">
            <el-input
              clearable
              v-model="form.functionType"
              placeholder="请输入功能描述"
            />
          </el-form-item>
          <el-form-item label="是否展示">
            <el-switch
              inline-prompt
              active-text="是"
              inactive-text="否"
              :model-value="form.enabled"
              @change="updateEnableValue"
            />
            <ele-tooltip
              content="选择不展示只注册路由不展示在侧边栏, 比如添加页面应该选择不展示"
              :popper-style="{ maxWidth: '240px' }"
            >
              <el-icon :size="15" style="margin-left: 16px; cursor: help">
                <QuestionCircleOutlined style="opacity: 0.6" />
              </el-icon>
            </ele-tooltip>
          </el-form-item>
          <!-- <el-form-item label="角色配置" prop="roleId">
            <el-select
              clearable
              v-model="form.roleId"
              placeholder="请选择"
              class="ele-fluid"
            >
              <el-option
                v-for="item in rolesList"
                @click="onRolesChange(item)"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item> -->
        </el-col>
        <el-col :sm="12" :xs="24">
          <!-- <el-form-item label="模块类型" prop="moduleType">
            <el-select
              clearable
              v-model="form.moduleType"
              placeholder="请选择"
              class="ele-fluid"
            >
              <el-option
                v-for="item in moduleTypeList"
                @click="onModuleTypeChange(item)"
                :key="item.id"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="展示类型" prop="displayType">
            <el-select
              clearable
              v-model="form.displayType"
              placeholder="请选择"
              class="ele-fluid"
              @clear="handleDisplayTypeClear"
            >
              <el-option
                v-for="item in displayTypeList"
                :key="item.id"
                :label="item.text"
                :value="item.id"
                @click="onDisplayTypeChange(item)"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="排序号" prop="sortOrder">
            <el-input-number
              :min="0"
              :max="99999"
              v-model="form.sortOrder"
              placeholder="请输入排序号"
              controls-position="right"
              class="ele-fluid"
            />
          </el-form-item>
          <el-form-item label="备注" prop="description">
            <el-input
              :rows="1"
              type="textarea"
              show-word-limit
              maxlength="255"
              v-model="form.description"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-divider
      v-if="
        form &&
        form.id &&
        (form.displayType == '轮播图' ||
          form.displayType == '菜单组件' ||
          form.displayType == '服务卡片组件')
      "
      content-position="left"
      >子项设置 [拖拽调整顺序]</el-divider
    >
    <swiper-data-list
      v-if="form.displayType == '轮播图'"
      ref="childRef"
      :dataList="itemList"
    />
    <menu-data-list
      v-if="form.displayType == '菜单组件'"
      :roleId="form.role && form.role.id"
      ref="childRef"
      :dataList="itemList"
    />
    <service-data-list
      ref="childRef"
      v-if="form.displayType == '服务卡片组件'"
      :roleId="form.role && form.role.id"
      :dataList="itemList"
    />
  </ele-card>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { useFormData } from '@/utils/use-form-data';
  import { operation, detailByID } from '../api/module';
  import { toFormData } from '@/utils/common_bak2.js';
  import { QuestionCircleOutlined } from '@/components/icons';
  import SwiperDataList from './child/swiper-data-list.vue';
  import MenuDataList from './child/menu-data-list.vue';
  import ServiceDataList from './child/service-data-list.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const emit = defineEmits(['done', 'update:modelValue']);
  const props = defineProps({
    /** 当前修改数据的id */
    currentId: String,
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 角色Id */
    roleId: String,
    /** 角色名称 */
    roleName: String
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 子项实例 */
  const childRef = ref(null);
  /** 子项列表 */
  const itemList = ref([]);

  /** 模块类型 */
  // const moduleTypeList = ref(null);

  /** 模块类型 */
  const displayTypeList = ref([
    {
      id: '轮播图',
      text: '轮播图'
    },
    {
      id: '滚动公告',
      text: '滚动公告'
    },
    {
      id: '菜单组件',
      text: '菜单组件'
    },
    {
      id: '服务卡片组件',
      text: '服务卡片组件'
    },
    {
      id: '数据概览-数据量',
      text: '数据概览-数据量'
    },
    {
      id: '数据概览-表格',
      text: '数据概览-表格'
    },
    {
      id: '数据概览-列表',
      text: '数据概览-列表'
    },
    {
      id: '数据概览-条形图',
      text: '数据概览-条形图'
    },
    {
      id: '数据概览-扇形图',
      text: '数据概览-扇形图'
    },
    {
      id: '数据概览-柱状图',
      text: '数据概览-柱状图'
    }
  ]);

  /** 角色列表 */
  // const rolesList = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    functionType: void 0,
    type: {},
    // moduleType: void 0,
    displayType: void 0,
    sortOrder: void 0,
    enabled: true,
    roleId: void 0,
    description: '',
    role: {
      id: props.roleId,
      text: props.roleName
    }
  });
  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入模块名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    displayType: [
      {
        required: true,
        message: '请选择展示类型',
        type: 'string',
        trigger: 'blur'
      }
    ],
    sortOrder: [
      {
        message: '请输入排序号',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });
  /** 是否启用选择改变 */
  const updateEnableValue = (value) => {
    form.enabled = value;
  };
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let result = {
        ...form,
        componentId: form.id,
        role: {
          id: props.roleId,
          text: props.roleName
        }
      };
      delete result.createdAtText;
      delete result.updatedAtText;
      console.log('result', result);
      if (childRef.value) {
        let itemList = childRef.value.itemList;
        if (itemList && itemList.length > 0) {
          itemList.forEach((item) => {
            delete item.createdAtText;
            delete item.updatedAtText;
            if (
              item.uploadImage &&
              item.uploadImage.length > 0 &&
              item.uploadImage[0].status == 'done'
            ) {
              delete item.uploadImage;
            }
          });
        }
        result.items = itemList;
      }
      let data = objectToFormData(result);
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新模块类型 */
  // const onModuleTypeChange = (item) => {
  //   console.log('item', item);
  //   form.type = {
  //     id: item.id,
  //     text: item.text
  //   };
  //   console.log('form', form);
  // };
  /** 更新展示类型 */
  const onDisplayTypeChange = (item) => {
    itemList.value = [];
    // console.log('form', form, itemList.value);
  };
  /** 展示类型清空事件 */
  const handleDisplayTypeClear = () => {
    form.displayType = null;
  };
  /** 更新角色 */
  // const onRolesChange = (item) => {
  //   console.log('item', item);
  //   form.role = {
  //     id: item.id,
  //     text: item.text
  //   };
  //   console.log('form', form);
  // };
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.currentId,
    (currentId) => {
      if (currentId) {
        isUpdate.value = true;
        getDataByID();
      } else {
        isUpdate.value = false;
        resetFields();
        assignFields({
          enabled: true,
          role: {
            id: props.roleId,
            text: props.roleName
          }
        });
        formRef.value?.clearValidate?.();
      }
    }
  );
  const getDataByID = () => {
    /** 获取详情 */
    detailByID(props.currentId)
      .then((data) => {
        assignFields({
          ...data
        });
        itemList.value = data.items;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  //动态获取模块类型列表
  // const OptionalType = () => {
  //   optionalType()
  //     .then((data) => {
  //       moduleTypeList.value = data;
  //       console.log('moduleTypeList', moduleTypeList.value);
  //     })
  //     .catch((e) => {
  //       EleMessage.error(e.message);
  //     });
  // };
  //动态获取角色列表
  // const OptionalRole = () => {
  //   optionalRole()
  //     .then((data) => {
  //       rolesList.value = data;
  //       console.log('rolesList', rolesList.value);
  //     })
  //     .catch((e) => {
  //       EleMessage.error(e.message);
  //     });
  // };
  const objectToFormData = (obj, formData = new FormData(), parentKey = '') => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] != null) {
        const value = obj[key];
        const fullKey = parentKey ? `${parentKey}.${key}` : key;

        if (value instanceof Date) {
          // 如果值是日期类型，转换为 ISO 字符串
          formData.append(fullKey, value.toISOString());
        } else if (value instanceof File || value instanceof Blob) {
          // 如果值是文件类型，直接添加到 FormData
          formData.append(fullKey, value);
        } else if (Array.isArray(value)) {
          // 如果值是数组，递归处理数组中的每个元素
          for (let i = 0; i < value.length; i++) {
            const arrayKey = `${fullKey}[${i}]`;
            if (value[i] !== null && typeof value[i] === 'object') {
              // 如果数组元素是对象，继续递归
              objectToFormData(value[i], formData, arrayKey);
            } else {
              // 否则直接添加到 FormData
              formData.append(arrayKey, value[i]);
            }
          }
        } else if (value !== null && typeof value === 'object') {
          // 如果值是对象，递归处理
          objectToFormData(value, formData, fullKey);
        } else {
          // 其他类型直接添加到 FormData
          formData.append(fullKey, value);
        }
      }
    }
    return formData;
  };
</script>
<style scoped>
  @import '@/css/border-only-bottom.css';
</style>
