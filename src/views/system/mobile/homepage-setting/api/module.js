import request from '@/utils/request';

/**
 * 分页查询首页模块列表
 */
export async function pageComponent(params) {
  const res = await request.get('/mobile/home/<USER>/page', { params });
  console.log('listMenus', res);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加/修改模块
 */
export async function operation(data) {
  const res = await request.post('/mobile/home/<USER>/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除模块
 * @param { Array } data - id数组
 */
export async function removes(data) {
  const res = await request.post('/mobile/home/<USER>/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询模块详情
 */
export async function detailByID(id) {
  const res = await request.get('/mobile/home/<USER>/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 可选模块类型
 */
export async function optionalType(params) {
  const res = await request.get('/mobile/home/<USER>/optionalType', {
    params
  });
  console.log('optionalType', res);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 查询可选角色列表
 */
export async function optionalRole(params) {
  const res = await request.get('/mobile/home/<USER>/optionalRole', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

// /**
//  * 分页查询分组列表
//  */
// export async function pageGroup(params) {
//   const res = await request.get('/mobile/group/page', { params });
//   console.log('pageGroup', res);
//   if (res.data.code === 0 && res.data.data) {
//     return res.data.data;
//   }
//   return Promise.reject(new Error(res.data.message));
// }

// /**
//  * 添加/修改分组
//  */
// export async function operation(data) {
//   const res = await request.post('/mobile/group/operation', data);
//   if (res.data.code === 0) {
//     return res.data.message;
//   }
//   return Promise.reject(new Error(res.data.message));
// }

// /**
//  * 删除分组
//  * @param { Array } data - id数组
//  */
// export async function removes(data) {
//   const res = await request.post('/mobile/group/remove', data);
//   if (res.data.code === 0) {
//     return res.data.message;
//   }
//   return Promise.reject(new Error(res.data.message));
// }
