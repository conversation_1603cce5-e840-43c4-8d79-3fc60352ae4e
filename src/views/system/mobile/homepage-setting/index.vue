<template>
  <ele-page hide-footer flex-table>
    <el-row :gutter="8">
      <el-col :md="6" :sm="12" :xs="24">
        <ele-card :body-style="{ padding: '5px 0!important' }">
          <template #header>
            模块管理[{{ roleName }}]
            <ele-tooltip
              style="cursor: pointer"
              effect="light"
              :content="'维护' + roleName + '角色移动端首页信息'"
              placement="top"
              :offset="3"
            >
              <IconPark name="help" size="20" strokeWidth="3" />
            </ele-tooltip>
          </template>
          <template #extra>
            <div style="display: flex; align-items: center" @click="openEdit()">
              <IconPark name="add-one" size="18" strokeWidth="3" />
              <ele-text
                style="padding: 2px; cursor: pointer"
                type="secondary"
                size="sm"
              >
                新建模块
              </ele-text>
            </div>
          </template>
          <div style="padding: 5px 5px 8px">
            <el-input
              clearable
              size="small"
              :maxlength="20"
              v-model="keywords"
              placeholder="输入模块搜索"
              :prefix-icon="SearchOutlined"
            />
          </div>
          <div :style="{ height: pageHeight - 40 + 'px', overflow: 'auto' }">
            <ele-loading :loading="loading">
              <el-tree
                ref="treeRef"
                :data="data"
                highlight-current
                node-key="id"
                :props="{ label: 'name' }"
                :expand-on-click-node="false"
                :default-expand-all="true"
                :filter-node-method="filterNode"
                :style="{
                  '--ele-tree-item-height': '34px',
                  '--ele-tree-expand-padding': 0,
                  '--ele-tree-expand-margin': 0
                }"
                @node-click="handleNodeClick"
              >
                <template #default="{ data: d }">
                  <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.name }}
                          <!-- [{{ d.code }}] -->
                        </span>
                      </span>
                      <!-- 图标容器 -->
                      <span v-if="currentId === d.id" class="icon-container">
                        <ele-tooltip
                          style="cursor: pointer"
                          effect="light"
                          content="编辑"
                        >
                          <a @click="openEdit(d)">
                            <IconPark
                              name="edit-two"
                              size="18"
                              strokeWidth="3"
                              color="#d0021b"
                            />
                          </a>
                        </ele-tooltip>
                        <ele-tooltip
                          style="cursor: pointer"
                          effect="light"
                          content="删除"
                        >
                          <a @click="remove(d)">
                            <IconPark
                              style="margin-left: 10px"
                              name="delete"
                              size="18"
                              strokeWidth="3"
                              color="#d0021b"
                            />
                          </a>
                        </ele-tooltip>
                      </span>
                    </span>
                  </div>
                </template>
              </el-tree>
            </ele-loading>
          </div>
        </ele-card>
      </el-col>
      <el-col :md="18" :sm="12" :xs="24">
        <edit
          :currentId="currentId"
          :data="editData"
          :roleId="roleId"
          :roleName="roleName"
          @done="getDataList"
        />
      </el-col>
    </el-row>
  </ele-page>
</template>

<script setup>
  import { ref, nextTick, watch, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage} from "element-plus";
  import { SearchOutlined } from '@/components/icons';
  import { useMobile } from '@/utils/use-mobile';
  import Edit from './components/edit.vue';
  import { removes, pageComponent } from './api/module';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import IconPark from '@/components/IconPark/index.vue';
  import { useRouter } from 'vue-router';
  import {ElLoading} from "element-plus";
  const { currentRoute, push } = useRouter();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { query } = unref(currentRoute);
  const roleId = query.roleId;
  const roleName = query.roleName;
  const currentLimit = ref(1000);
  const currentPage = ref(1);

  /** 是否是移动端 */
  const { mobile } = useMobile();

  /** 分割面板组件 */
  const splitRef = ref(null);

  /** 树组件 */
  const treeRef = ref(null);

  /** 加载状态 */
  const loading = ref(true);

  /** 树形数据 */
  const data = ref([]);

  /** 选中模块数据 */
  const current = ref(null);

  /** 选中模块id */
  const currentId = ref(null);

  /** 机构搜索关键字 */
  const keywords = ref('');

  /** 是否显示编辑弹窗 */
  const showEdit = ref(true);

  /** 编辑回显数据 */
  const editData = ref(null);

  /** 查询 */
  const getDataList = () => {
    loading.value = true;
    let obj = {
      page: currentPage.value,
      limit: currentLimit.value,
      roleId: roleId
    };
    pageComponent(obj)
      .then((res) => {
        loading.value = false;
        if (res.list && res.list.length > 0) {
          nextTick(() => {
            data.value = res.list ?? [];
            handleNodeClick(data.value[0]);
            editData.value = data.value[0] ?? null;
            currentId.value =
              data.value[0] && data.value[0].id ? data.value[0].id : null;
          });
        }
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  const selectedKey = ref(null);
  /** 选择数据 */
  const handleNodeClick = (row) => {
    // 移动端自动收起左侧
    if (current.value != null && mobile.value) {
      splitRef.value?.toggleCollapse?.(true);
    }
    if (row && row.id) {
      current.value = row;
      editData.value = row;
      currentId.value = row.id;
      selectedKey.value = row.id;
      treeRef['value']?.setCurrentKey?.(row.id);
    } else {
      current.value = null;
      editData.value = null;
      currentId.value = null;
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    currentId.value = row && row.id ? row.id : null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    if (!row) {
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' +
        row.name +
        '”吗？此操作会删除该模块下的所有应用，请谨慎操作！',
      {
        type: 'warning',
        draggable: true
      }
    )
      .then(() => {
         const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
        removes([row.id])
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            getDataList();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 树过滤方法 */
  const filterNode = (value, data) => {
    if (value) {
      return !!(data.name && data.name.includes(value));
    }
    return true;
  };

  /** 树过滤 */
  watch(keywords, (value) => {
    treeRef.value?.filter?.(value);
  });

  getDataList();
</script>

<script>
  export default {
    name: 'SystemDictionary'
  };
</script>
