<template>
  <ele-card
    :body-style="{ padding: '5px!important', height: pageHeight + 6 + 'px' }"
  >
    <template #header>
      <ele-text style="padding: 2px">{{ groupName + '设置' }}</ele-text>
    </template>
    <ele-pro-table
      ref="tableRef"
      row-key="id"
      :columns="columns"
      :datasource="datasource"
      :border="true"
      :show-overflow-tooltip="true"
      highlight-current-row
      tooltip-effect="light"
      cache-key="systemDictDataTable"
      :footer-style="{ paddingBottom: '3px' }"
      :table-style="{ height: pageHeight - 90 + 'px', overflow: 'auto' }"
    >
      <template #toolbar>
        <el-button size="small" class="ele-btn-icon" @click="openEdit()">
          新建应用
        </el-button>
      </template>
      <template #name="{ row }">
        <el-icon
          v-if="row.icon"
          :size="15"
          style="margin-right: 8px; vertical-align: -3px"
        >
          <IconPark :name="row.icon" size="16" :color="row.iconColor" />
        </el-icon>
        <span>{{ row.name }}</span>
      </template>
      <template #action="{ row }">
        <el-link type="primary" underline="never" @click="openEdit(row)">
          修改
        </el-link>
        <el-divider direction="vertical" />
        <el-link type="primary" underline="never" @click="remove(row)">
          删除
        </el-link>
      </template>
      <!-- 表头查询 -->
      <template #nameHeader="{ column }">
        <div style="display: flex; align-items: center">
          <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
            {{ column.label }}
          </div>
          <name-filter @search="onNameFilter" />
        </div>
      </template>
    </ele-pro-table>
    <app-data-edit
      v-model="showEdit"
      :data="current"
      :groupId="groupId"
      :currentId="currentId"
      @done="reload"
    />
  </ele-card>
</template>

<script setup>
  import { ref, watch, computed, reactive } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage} from "element-plus";
  import IconPark from '@/components/IconPark/index.vue';
  import AppDataEdit from './app-data-edit.vue';
  import nameFilter from './name-filter.vue';
  import { pageApp, removeApp } from '../api/apps';
  import { usePermission } from '@/utils/use-permission';
  import { getToken } from '@/utils/token-util';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import {ElLoading} from "element-plus";

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const props = defineProps({
    /** 分组id */
    groupId: String,
    /** 分组名称 */
    groupName: String
  });

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        prop: 'name',
        slot: 'name',
        label: '应用名称',
        headerSlot: 'nameHeader'
      },
      {
        prop: 'roles',
        label: '角色',
        formatter: (row) => {
          return row.roles.map((item) => item.text).join(',');
        }
      },
      {
        prop: 'path',
        label: '路由地址'
      },
      {
        prop: 'component',
        label: '组件路径'
      },
      {
        prop: 'sortOrder',
        label: '排序'
      },
      {
        prop: 'enabled',
        label: '是否启用',
        formatter: (row) => {
          return row.enabled ? '是' : '否';
        }
      },
      {
        columnKey: 'action',
        label: '操作',
        slot: 'action',
        width: 100
      }
    ];
  });

  /** 当前编辑数据 */
  const current = ref(null);
  /** 当前编辑数据的id */
  const currentId = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return pageApp({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      groupId: props.groupId
    });
  };

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    console.log('name', name);
    nameFilterValue.value = name;
    doReload();
  };

  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        nameLike: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 刷新表格 */
  const reload = (where) => {
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    currentId.value = row && row.id ? row.id : null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    const rows = [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
         const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
        removeApp(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  // 监听分组id变化
  watch(
    () => props.groupId,
    () => {
      searchRef.value?.resetFields?.();
      reload({});
    }
  );
</script>
