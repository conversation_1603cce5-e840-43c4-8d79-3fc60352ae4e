<template>
  <el-row :gutter="24">
    <el-col :span="12">
      <vue-draggable
        v-model="list1"
        item-key="id"
        :animation="300"
        group="demoDragList"
        :set-data="() => void 0"
        class="demo-list"
      >
        <template #item="{ element }">
          <div>
            <el-tag
              size="large"
              :disable-transitions="true"
              style="margin-bottom: 6px; width: 230px"
              >{{ element.text }}
            </el-tag>
          </div>
        </template>
      </vue-draggable>
    </el-col>
    <el-col :span="12">
      <!--      :model-value="props.modelValue"-->
      <vue-draggable
        v-model="list2"
        item-key="id"
        :animation="300"
        group="demoDragList"
        :set-data="() => void 0"
        class="demo-list"
        @update:modelValue="updateValue"
      >
        <template #item="{ element }">
          <el-tag
            size="large"
            :disable-transitions="true"
            style="margin-bottom: 6px; width: 230px"
            >{{ element.text }}
          </el-tag>
        </template>
      </vue-draggable>
    </el-col>
  </el-row>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue';
  import VueDraggable from 'vuedraggable';
  import { EleMessage } from 'ele-admin-plus';
  import { listRoles } from '@/views/system/mobile/menu/api/apps';

  const emit = defineEmits(['update:modelValue']);
  const props = defineProps({
    /** 选中的角色 */
    modelValue: Array
  });
  /** 角色数据 */
  const baseData = ref([]);
  const list1 = ref([]);
  const list2 = ref([]);
  const queryListRoles = () => {
    /** 获取角色数据 */
    listRoles()
      .then((list) => {
        baseData.value = list;
        list1.value = list;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', list2.value);
  };

  watch(
    () => props.modelValue,
    (data) => {
      nextTick(() => {
        if (data) {
          list2.value = data;
          list1.value = baseData.value.filter(
            (item) => !list2.value.some((ele) => ele.id === item.id)
          );
        } else {
          list1.value = baseData.value;
        }
      });
      // queryListRoles()
    },
    { immediate: true }
  );

  queryListRoles();
</script>

<style lang="scss" scoped>
  .demo-list {
    overflow-y: auto;
    width: 250px;
    margin: 0 auto;
    max-width: 100%;
    height: 312px;
    border-radius: 4px;
    border: 1px solid hsla(0, 0%, 60%, 0.2);
    box-sizing: border-box;
    padding: 8px 8px 0 8px;
  }

  .demo-list-item {
    color: #fff;
    background: #1677ff;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 8px;
    cursor: move;

    &.sortable-ghost {
      opacity: 0;
    }
  }
</style>
