<!-- 字典编辑弹窗 -->
<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改分组 [' + data.name + ']' : '添加分组'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="small"
      label-width="auto"
      @submit.prevent=""
    >
      <el-form-item label="分组名称" prop="name">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.name"
          placeholder="请输入分组名称"
        />
      </el-form-item>
      <el-form-item label="应用图标" prop="icon">
        <IconParkSelect v-model="form.icon" />
      </el-form-item>
      <el-form-item label="图标颜色">
        <el-input :model-value="form.iconColor" readonly placeholder="请选择">
          <template #append>
            <div class="setting-wrapper">
              <div class="setting-colors">
                <!-- 颜色选择器 -->
                <el-color-picker
                  ref="colorPickerRef"
                  v-model="colorValue"
                  :predefine="predefineColors"
                  class="setting-color-picker"
                  @change="updateColor"
                  @activeChange="onColorChange"
                />
              </div>
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="排序号" prop="sortOrder">
        <el-input-number
          :min="0"
          :max="9999"
          v-model="form.sortOrder"
          placeholder="请输入排序号"
          controls-position="right"
          class="ele-fluid"
        />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch
          inline-prompt
          active-text="是"
          inactive-text="否"
          :model-value="form.enabled"
          @change="updateEnableValue"
        />
        <ele-tooltip
          content="选择不展示只注册路由不展示在侧边栏, 比如添加页面应该选择不展示"
          :popper-style="{ maxWidth: '240px' }"
        >
          <el-icon :size="15" style="margin-left: 16px; cursor: help">
            <QuestionCircleOutlined style="opacity: 0.6" />
          </el-icon>
        </ele-tooltip>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          :rows="1"
          type="textarea"
          show-word-limit
          maxlength="12"
          v-model="form.description"
          placeholder="请输入描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch, nextTick } from 'vue';
  import { ElMessage as EleMessage} from "element-plus";
  import { useFormData } from '@/utils/use-form-data';
  import { operation, detailByID } from '../api/groups';
  import { QuestionCircleOutlined } from '@/components/icons';
  import IconParkSelect from '@/components/IconParkSelect/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 颜色选择器选中颜色 */
  const colorValue = ref(void 0);
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    name: '',
    icon: void 0,
    iconColor: void 0,
    sortOrder: void 0,
    enabled: true,
    description: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入分组名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入字典值',
        type: 'string',
        trigger: 'blur'
      }
    ],
    sortOrder: [
      {
        message: '请输入排序号',
        type: 'number',
        trigger: 'blur'
      }
    ]
  });
  /** 颜色选择器预设颜色 */
  const predefineColors = ref([
    '#EF4444',
    '#F97316',
    '#22C55E',
    '#3B82F6',
    '#A855F7'
    // '#f5222d',
    // '#fa541c',
    // '#fa8c16',
    // '#faad14',
    // '#a0d911',
    // '#52c41a',
    // '#13c2c2',
    // '#2f54eb',
    // '#722ed1',
    // '#eb2f96'
  ]);
  /** 是否启用选择改变 */
  const updateEnableValue = (value) => {
    form.enabled = value;
  };
  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data && props.data.id) {
          getDataByID(props.data.id);
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  const getDataByID = (id) => {
    /** 获取详情 */
    detailByID(id)
      .then((data) => {
        console.log('getDataByID', data);
        assignFields({
          ...data
        });
        initColorValue();
        console.log('getDataByID赋值', form);
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  /** 更新颜色选择器选中 */
  const initColorValue = () => {
    if (form.iconColor) {
      colorValue.value = form.iconColor;
    } else {
      colorValue.value = void 0;
    }
  };
  // 解决ElColorPicker第一次点击预设颜色未选中的问题
  const colorPickerRef = ref(null);
  const onColorChange = (color) => {
    const index = predefineColors.value.indexOf(color.toLowerCase());
    const ins = colorPickerRef.value?.$refs?.predefine;
    if (index !== -1 && ins !== null && !ins.rgbaColors[index].selected) {
      nextTick(() => {
        ins.rgbaColors[index].selected = true;
      });
    }
  };
  const updateColor = async (value) => {
    console.log('updateColor', value);
    form.iconColor = value ?? null;
    initColorValue();
  };
</script>
<style lang="scss" scoped>
  .setting-wrapper {
    /* 主题色选择 */
    .setting-colors {
      color: #fff;
      line-height: 0;
    }

    .setting-color-picker {
      background: conic-gradient(
        from 90deg at 50% 50%,
        red -19.41deg,
        red 18.76deg,
        #ff8a00,
        #ffe600 99.87deg,
        #14ff00 141.65deg,
        #00a3ff 177.72deg,
        #0500ff 220.23deg,
        #ad00ff 260.13deg,
        #ff00c7 300.69deg,
        red 340.59deg,
        red 378.76deg
      );
    }

    /* 颜色选择器 */
    .setting-colors :deep(.el-color-picker) {
      line-height: 0;

      .el-color-picker__trigger {
        padding: 0;
        width: 23px;
        height: 23px;
        border: none;
      }

      .el-color-picker__color {
        border: none;
      }

      .el-color-picker__empty {
        background: conic-gradient(
          from 90deg at 50% 50%,
          rgb(255, 0, 0) -19.41deg,
          rgb(255, 0, 0) 18.76deg,
          rgb(255, 138, 0) 59.32deg,
          rgb(255, 230, 0) 99.87deg,
          rgb(20, 255, 0) 141.65deg,
          rgb(0, 163, 255) 177.72deg,
          rgb(5, 0, 255) 220.23deg,
          rgb(173, 0, 255) 260.13deg,
          rgb(255, 0, 199) 300.69deg,
          rgb(255, 0, 0) 340.59deg,
          rgb(255, 0, 0) 378.76deg
        );
        height: 100%;
        width: 100%;

        & > svg {
          display: none;
        }
      }
    }
  }
</style>
