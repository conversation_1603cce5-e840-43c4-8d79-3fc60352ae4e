<template>
  <ele-page hide-footer flex-table>
    <el-row :gutter="8">
      <el-col :md="6" :sm="12" :xs="24">
        <ele-card :body-style="{ padding: '5px 0!important' }">
          <template #header>
            分组管理
            <ele-tooltip
              style="cursor: pointer"
              effect="light"
              content="维护菜单分组"
              placement="top"
              :offset="3"
            >
              <IconPark name="help" size="20" strokeWidth="3" />
            </ele-tooltip>
          </template>
          <template #extra>
            <div style="display: flex; align-items: center" @click="openEdit()">
              <IconPark name="add-one" size="18" strokeWidth="3" />
              <ele-text
                style="padding: 2px; cursor: pointer"
                type="secondary"
                size="sm"
              >
                新建分组
              </ele-text>
            </div>
          </template>
          <div style="padding: 5px 5px 8px">
            <el-input
              clearable
              size="small"
              :maxlength="20"
              v-model="keywords"
              placeholder="输入分组名称搜索"
              :prefix-icon="SearchOutlined"
            />
          </div>
          <div :style="{ height: pageHeight - 40 + 'px', overflow: 'auto' }">
            <ele-loading :loading="loading">
              <el-tree
                ref="treeRef"
                :data="data"
                highlight-current
                node-key="id"
                :props="{ label: 'name' }"
                :expand-on-click-node="false"
                :default-expand-all="true"
                :filter-node-method="filterNode"
                :style="{
                  '--ele-tree-item-height': '34px',
                  '--ele-tree-expand-padding': 0,
                  '--ele-tree-expand-margin': 0
                }"
                @node-click="handleNodeClick"
              >
                <template #default="{ data: d }">
                  <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.name }}
                          <!-- [{{ d.code }}] -->
                        </span>
                      </span>
                      <!-- 图标容器 -->
                      <span v-if="selectedKey === d.id" class="icon-container">
                        <ele-tooltip
                          style="cursor: pointer"
                          effect="light"
                          content="编辑"
                        >
                          <a @click="openEdit(d)">
                            <IconPark
                              name="edit-two"
                              size="18"
                              strokeWidth="3"
                              color="#d0021b"
                            />
                          </a>
                        </ele-tooltip>
                        <ele-tooltip
                          style="cursor: pointer"
                          effect="light"
                          content="删除"
                        >
                          <a @click="remove(d)">
                            <IconPark
                              style="margin-left: 10px"
                              name="delete"
                              size="18"
                              strokeWidth="3"
                              color="#d0021b"
                            />
                          </a>
                        </ele-tooltip>
                      </span>
                    </span>
                  </div>
                </template>
              </el-tree>
            </ele-loading>
          </div>
        </ele-card>
      </el-col>
      <el-col :md="18" :sm="12" :xs="24">
        <app-data-list
          v-if="current && current.id"
          :groupName="current.name"
          :groupId="current.id"
        />
      </el-col>
      <group-edit v-model="showEdit" :data="editData" @done="query" />
    </el-row>
  </ele-page>
</template>

<script setup>
  import { ref, nextTick, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage} from "element-plus";
  import { SearchOutlined } from '@/components/icons';
  import { useMobile } from '@/utils/use-mobile';
  import AppDataList from './components/app-data-list.vue';
  import GroupEdit from './components/group-edit.vue';
  import { removes, pageGroup } from './api/groups';
  import IconPark from '@/components/IconPark/index.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import {ElLoading} from "element-plus";

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const currentLimit = ref(1000);
  const currentPage = ref(1);

  /** 是否是移动端 */
  const { mobile } = useMobile();

  /** 分割面板组件 */
  const splitRef = ref(null);

  /** 树组件 */
  const treeRef = ref(null);

  /** 加载状态 */
  const loading = ref(true);

  /** 树形数据 */
  const data = ref([]);

  /** 选中数据 */
  const current = ref(null);

  /** 选中模块id */
  const currentId = ref(null);

  /** 机构搜索关键字 */
  const keywords = ref('');

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);

  /** 查询 */
  const query = () => {
    loading.value = true;
    let obj = {
      page: currentPage.value,
      limit: currentLimit.value
    };
    pageGroup(obj)
      .then((res) => {
        loading.value = false;
        if (res.list && res.list.length > 0) {
          nextTick(() => {
            data.value = res.list ?? [];
            handleNodeClick(data.value[0]);
            currentId.value =
              data.value[0] && data.value[0].id ? data.value[0].id : null;
          });
        }
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };
  const selectedKey = ref(null);
  /** 选择数据 */
  const handleNodeClick = (row) => {
    // 移动端自动收起左侧
    if (current.value != null && mobile.value) {
      splitRef.value?.toggleCollapse?.(true);
    }
    if (row && row.id) {
      current.value = row;
      treeRef.value?.setCurrentKey?.(row.id);
      // 监听选中状态
      selectedKey.value = row.id
    } else {
      current.value = null;
    }
  };


  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    currentId.value = row && row.id ? row.id : null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    if (!row) {
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' +
        row.name +
        '”吗？此操作会删除该分组下的所有应用，请谨慎操作！',
      {
        type: 'warning',
        draggable: true
      }
    )
      .then(() => {
         const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
        removes([row.id])
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            query();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 树过滤方法 */
  const filterNode = (value, data) => {
    if (value) {
      return !!(data.name && data.name.includes(value));
    }
    return true;
  };

  /** 树过滤 */
  watch(keywords, (value) => {
    treeRef.value?.filter?.(value);
  });

  query();
</script>

<script>
  export default {
    name: 'SystemDictionary'
  };
</script>
