import request from '@/utils/request';

/**
 * 分页查询应用列表
 */
export async function pageApp(params) {
  const res = await request.get('/mobile/app/page', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加/修改应用
 */
export async function operationApp(data) {
  const res = await request.post('/mobile/app/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除应用
 */
export async function removeApp(data) {
  const res = await request.post('/mobile/app/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询应用列表
 */
export async function optionalRole(params) {
  const res = await request.get('/mobile/app/optionalRole', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询角色列表
 */
export async function listRoles(params) {
  const res = await request.get('/mobile/app/optionalRole', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 根据id查询应用详情
 */
export async function appByID(id) {
  const res = await request.get('/mobile/app/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
