import request from '@/utils/request';

/**
 * 分页查询分组列表
 */
export async function pageGroup(params) {
  const res = await request.get('/mobile/group/page', { params });
  console.log('pageGroup', res);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加/修改分组
 */
export async function operation(data) {
  const res = await request.post('/mobile/group/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除分组
 * @param { Array } data - id数组
 */
export async function removes(data) {
  const res = await request.post('/mobile/group/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询分组详情
 */
export async function detailByID(id) {
  const res = await request.get('/mobile/group/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
