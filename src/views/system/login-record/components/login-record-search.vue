<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form size="small" label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label="用户账号">
            <el-input clearable
                      v-model.trim="form.username"
                      placeholder="请输入用户账号"/>
          </el-form-item>
        </el-col>
        <!--                 <el-col  :md="6" :sm="12" :xs="24">-->
        <!--                    <el-form-item label="登录时间">-->
        <!--                        <el-date-picker unlink-panels-->
        <!--                                        type="daterange"-->
        <!--                                        v-model="dateRange"-->
        <!--                                        range-separator="-"-->
        <!--                                        value-format="YYYY-MM-DD"-->
        <!--                                        start-placeholder="开始日期"-->
        <!--                                        end-placeholder="结束日期"-->
        <!--                                        class="ele-fluid"/>-->
        <!--                    </el-form-item>-->
        <!--                </el-col>-->
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" size="small" plain @click="search">查询</el-button>
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import {ref} from 'vue';
import {useFormData} from '@/utils/use-form-data';

const props = defineProps({
  /** 默认搜索条件 */
  where: Object
});

const emit = defineEmits(['search']);

/** 表单数据 */
const [form, resetFields] = useFormData({
  username: '',
  nickname: '',
  ...(props.where || {})
});

/** 日期范围 */
const dateRange = ref(['', '']);

/** 搜索 */
const search = () => {
  const [d1, d2] = dateRange.value || [];
  emit('search', {
    ...form,
    createTimeStart: d1 ? `${d1} 00:00:00` : '',
    createTimeEnd: d2 ? `${d2} 23:59:59` : ''
  });
};

/**  重置 */
const reset = () => {
  resetFields();
  dateRange.value = ['', ''];
  search();
};
</script>
