<!-- 角色权限分配弹窗 -->
<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="65%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <ele-card flex-table
                :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
        <template #header>
          <div style="display: flex; align-items: center;">
            菜单授权
            <el-text text @click="onBack" class="back-btn">
              <IconPark name="return" size="18" strokeWidth="3"/>
              <span>返回</span>
            </el-text>
          </div>
        </template>
        <template #extra>
          <el-input clearable size="small"
                    style="width: 50%;"
                    :maxlength="20"
                    v-model="filterText"
                    placeholder="菜单名称搜索"
                    :prefix-icon="SearchOutlined"
                    @input="filterTree"/>
        </template>
        <commonTree ref="menuTreeRef" routeType="menu"/>
      </ele-card>
      <template #body>
        <ele-card flex-table
                  :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
          <template #header>
            <div style="display: flex; align-items: center;">
              按钮接口授权
              <el-text text @click="onBack" class="back-btn">
                <IconPark name="return" size="18" strokeWidth="3"/>
                <span>返回</span>
              </el-text>
            </div>
          </template>
          <template #extra>
            <el-input clearable size="small"
                      style="width: 50%;"
                      :maxlength="20"
                      v-model="filterBtnText"
                      placeholder="按钮接口权限名称搜索"
                      :prefix-icon="SearchOutlined"
                      @input="filterBtnTree"/>
          </template>
          <commonTree ref="btnTreeRef" routeType="btn"/>
        </ele-card>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {ref, unref, watch} from 'vue';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab";
import commonTree from "@/views/system/role/auth/components/common-tree.vue";
import {SearchOutlined} from "@/components/icons/index.js";
import IconPark from "@/components/IconPark/index.vue";

const {currentRoute, push} = useRouter();
const {removePageTab, getRouteTabKey} = usePageTab();

const {query} = unref(currentRoute);
const currentId = query.id;

// 菜单树引用
const menuTreeRef = ref(null);
// 按钮树引用
const btnTreeRef = ref(null);

/** 菜单搜索文本 */
const filterText = ref('');
/** 按钮搜索文本 */
const filterBtnText = ref('');

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/system/role');
};

/** 过滤菜单树 */
const filterTree = () => {
  menuTreeRef.value?.filterTree(filterText.value);
};

/** 过滤按钮树 */
const filterBtnTree = () => {
  btnTreeRef.value?.filterTree(filterBtnText.value);
};

// 监听菜单搜索文本变化
watch(filterText, (text) => {
  filterTree();
});

// 监听按钮搜索文本变化
watch(filterBtnText, (text) => {
  filterBtnTree();
});
</script>

<script>
import * as MenuIcons from '@/layout/menu-icons';

export default {
  components: MenuIcons
};
</script>
