<template>
  <ele-page hide-footer  flex-table>
    <!-- 搜索表单 -->
    <role-search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        cache-key="systemRoleTable"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <!--                    <ele-tooltip content="编辑" placement="top">-->
          <!--                        <Edit style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;"-->
          <!--                              @click="openEdit(row)"/>-->
          <!--                    </ele-tooltip>-->
          <!--                    <ele-tooltip content="删除" placement="top">-->
          <!--                        <Delete style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;"-->
          <!--                                @click="remove(row)"/>-->
          <!--                    </ele-tooltip>-->
          <!--                    &lt;!&ndash;                    <el-icon><HelpFilled /></el-icon>&ndash;&gt;-->
          <!--                    <ele-tooltip content="分配权限" placement="top">-->
          <!--                        <ClusterOutlined-->
          <!--                                style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;stroke-width: 3;"-->
          <!--                                @click="openAuth(row)"/>-->
          <!--                    </ele-tooltip>-->

          <!--                    <el-link style="width: 1.2em; height: 1.2em;margin-right: 12px;" href="https://element-plus.org" target="_blank">默认</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="primary">主题</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="success">成功</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="warning">警告</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="danger">预警</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="info">信息</el-link>-->
          <el-link type="primary" underline="never" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="never" @click="openAuth(row)">
            分配权限
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="never" @click="openMobile(row)">
            移动端首页
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="never" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <role-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 权限分配弹窗 -->
    <role-auth v-model="showAuth" :data="current" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage} from "element-plus";
  import {
    PlusOutlined,
    DeleteOutlined,
    ClusterOutlined
  } from '@/components/icons';
  import RoleSearch from './components/role-search.vue';
  import RoleEdit from './components/role-edit.vue';
  import RoleAuth from './components/role-auth.vue';
  import { pageRoles, removeRoles } from './api/index';
  import { useRouter } from 'vue-router';
  import Search from '@/views/base-code/dwb/components/search.vue';
  import {ElLoading} from "element-plus";

  const { currentRoute, push } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '角色名称',
      sortable: 'custom'
    },
    {
      prop: 'roleScope',
      label: '角色标识',
      sortable: 'custom'
    },
    {
      prop: 'remark',
      label: '备注',
      sortable: 'custom'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 260,
      slot: 'action'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示权限分配弹窗 */
  const showAuth = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders }) => {
    return pageRoles({ ...where, ...orders, page, limit });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开权限分配弹窗 */
  const openAuth = (row) => {
    push({
      path: '/system/role/auth',
      // path: '/roleAuth',
      query: { id: row.id }
    });
    //
    // current.value = row ?? null;
    // showAuth.value = true;
  };
  /** 打开移动端首页配置 */
  const openMobile = (row) => {
    push({
      path: '/system/mobile/homepage-setting',
      query: { roleId: row.id, roleName: row.name }
    });
    //
    // current.value = row ?? null;
    // showAuth.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
         const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
        removeRoles(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SystemRole'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
