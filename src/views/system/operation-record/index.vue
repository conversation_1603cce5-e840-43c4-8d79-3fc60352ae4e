<template>
  <ele-page hide-footer  flex-table>
    <operation-record-search @search="reload"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     highlight-current-row
                     tooltip-effect="light"
                     cache-key="systemOperationRecordTable"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small" @click="exportData">
            导出
          </el-button>
        </template>
        <template #status="{ row }">
          <el-tag v-if="row.status === 0"
                  size="small"
                  type="success"
                  :disable-transitions="true">
            正常
          </el-tag>
          <el-tag v-else-if="row.status === 1"
                  size="small"
                  type="danger"
                  :disable-transitions="true">
            异常
          </el-tag>
        </template>
        <template #username="{ row }">
          <ele-tooltip content="查看详情" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openDetail(row)">
              {{ row.username }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 详情弹窗 -->
    <operation-record-detail v-model="showInfo" :data="current"/>
  </ele-page>
</template>

<script setup>
import {ref} from 'vue';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {utils, writeFile} from 'xlsx';
import {DownloadOutlined} from '@/components/icons';
import OperationRecordSearch from './components/operation-record-search.vue';
import OperationRecordDetail from './components/operation-record-detail.vue';
import {queryPage} from './api';
import Search from "@/views/base-code/dwb/components/search.vue";

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'username',
    slot: 'username',
    label: '账号',
    sortable: 'custom',
    minWidth: 110
  },
  {
    prop: 'realName',
    label: '用户名',
    sortable: 'custom',
    minWidth: 110
  },
  {
    prop: 'module',
    label: '操作模块',
    sortable: 'custom',
    minWidth: 110
  },
  {
    prop: 'description',
    label: '操作功能',
    sortable: 'custom',
    minWidth: 110
  },
  {
    prop: 'url',
    label: '请求地址',
    sortable: 'custom',
    minWidth: 110
  },
  {
    prop: 'requestMethod',
    label: '请求方式',
    sortable: 'custom',
    width: 110,
    align: 'center'
  },
  {
    prop: 'status',
    label: '状态',
    sortable: 'custom',
    width: 100,
    slot: 'status',
    filters: [
      {
        text: '正常',
        value: '0'
      },
      {
        text: '异常',
        value: '1'
      }
    ],
    filterMultiple: false
  },
  {
    prop: 'spendTime',
    label: '耗时',
    sortable: 'custom',
    width: 100,
    formatter: (row) => `${row.spendTime / 1000}s`
  },
  {
    prop: 'createTime',
    label: '操作时间',
    sortable: 'custom',
    minWidth: 110
  },
]);

/** 当前选中数据 */
const current = ref({
  module: '',
  description: '',
  url: '',
  requestMethod: '',
  method: '',
  params: '',
  result: '',
  error: '',
  spendTime: 0,
  os: '',
  device: '',
  browser: '',
  ip: '',
  status: 0,
  createTime: '',
  nickname: '',
  username: ''
});

/** 是否显示查看弹窗 */
const showInfo = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({
    ...where,
    ...orders,
    ...filters,
    page,
    limit
  });
};

/** 列表选中数据 */
const selections = ref([]);

/** 刷新表格 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 详情 */
const openDetail = (row) => {
  current.value = row;
  showInfo.value = true;
};

/** 导出数据 */
const exportData = () => {
  const array = [
    [
      '账号',
      '用户名',
      '操作模块',
      '操作功能',
      '请求地址',
      '请求方式',
      '状态',
      '耗时',
      '操作时间'
    ]
  ];
  // 请求查询全部(不分页)的接口
   const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
  tableRef.value?.fetch?.(({where, orders, filters}) => {
    // listOperationRecords({...where, ...orders, ...filters})
    //     .then((data) => {
    //         loading.close();
    //         data.forEach((d) => {
    //             array.push([
    //                 d.username,
    //                 d.nickname,
    //                 d.module,
    //                 d.description,
    //                 d.url,
    //                 d.requestMethod,
    //                 ['正常', '异常'][d.status],
    //                 d.spendTime / 1000 + 's',
    //                 d.createTime
    //             ]);
    //         });
    //         writeFile(
    //             {
    //                 SheetNames: ['Sheet1'],
    //                 Sheets: {
    //                     Sheet1: utils.aoa_to_sheet(array)
    //                 }
    //             },
    //             '操作日志.xlsx'
    //         );
    //     })
    //     .catch((e) => {
    //         loading.close();
    //         EleMessage.error(e.message);
    //     });
  });
};
</script>

<script>
export default {
  name: 'SystemOperationRecord'
};
</script>

<style scoped>
.el-link {
  font-weight: unset !important;
}
</style>
