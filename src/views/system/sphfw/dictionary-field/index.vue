<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="25%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <ele-card :body-style="{ padding: '5px 0!important', height: `${pageHeight+15}px`, overflow: 'auto'}">
        <template #header>
          <div style="display: flex; align-items: center;">
            <el-text text @click="onBack" class="back-btn">
              <IconPark name="return" size="18" strokeWidth="3"/>
              <span>返回</span>
            </el-text>
            <el-input clearable size="small"
                      :maxlength="20"
                      v-model="keywords"
                      placeholder="输入名称搜索"
                      :prefix-icon="SearchOutlined"/>
          </div>
        </template>
        <ele-loading :loading="loading">
          <el-tree ref="treeRef"
                   :data="data"
                   highlight-current
                   node-key="id"
                   :props="{ label: 'groupName' }"
                   :expand-on-click-node="false"
                   :default-expand-all="true"
                   :filter-node-method="filterNode"
                   :style="{
                              '--ele-tree-item-height': '34px',
                              '--ele-tree-expand-padding': 0,
                              '--ele-tree-expand-margin': 0
                            }"
                   @node-click="handleNodeClick">
            <template #default="{ data: d }">
              <div class="el-tree-node__label">
                <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                  <!-- 核心修改：使用 flex 布局 -->
                  <span class="text-container">
                    <span class="span-wrap">
                      {{ d.groupName }}
                    </span>
                  </span>
                </span>
              </div>
            </template>
          </el-tree>
        </ele-loading>
      </ele-card>
      <template #body>
        <ele-card flex-table
                  :body-style="{ padding: '5px 5px!important', overflow: 'hidden' }">
          <DataList v-if="current && current.id"
                    :currentGroup="current"
                    :currentGroupId="current.id"
                    :currentGroupName="current.groupName"/>
        </ele-card>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {ref, nextTick, watch, unref} from 'vue';
import {SearchOutlined} from '@/components/icons';
import {useMobile} from '@/utils/use-mobile';
import DataList from './components/data-list.vue';
import {getDictionaryGroup} from "@/views/system/sphfw/dictionary-group/api/index.js";
import {useRouter} from "vue-router";
import {getListGroupConfigById} from "@/views/system/sphfw/list-group/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";
import {usePageTab} from "@/utils/use-page-tab.js";

const {currentRoute, push} = useRouter();
const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();
//获取浏览器参数
const {params, path, query} = unref(currentRoute);
const userType = path.split("/")[4];
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
/** 是否是移动端 */
const {mobile} = useMobile();

/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const data = ref([]);

/** 选中数据 */
const current = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);
/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/system/sphfw/dictionary-group/' + userType);
};

/**
 * 获取组信息
 */
const queryGroupData = () => {
  loading.value = true;
  let newObj = {
    userType: userType,
  }
  getDictionaryGroup(newObj).then((list) => {
    loading.value = false;
    data.value = list ?? [];
    let checkDataVal = data.value[0]
    if (params?.groupId) {
      const checkData = data.value.filter(e => e.id === params.groupId)
      checkDataVal = checkData[0]
    }
    nextTick(() => {
      handleNodeClick(checkDataVal);
    });
  });
}

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = async (row) => {
  if (!row?.id || current.value?.id === row.id) return;

  if (mobile.value) {
    splitRef.value?.toggleCollapse?.(true);
  }

  current.value = row;
  treeRef.value?.setCurrentKey?.(row.id);
  // 监听选中状态
  selectedKey.value = row.id
  if (row.listFlag === '是') {
    if (row.listGroupId) {
      try {
        const result = await getListGroupConfigById(row.listGroupId);
        current.value.dictFieldUrl = result?.dictionaryFieldUrl || '';
      } catch (e) {
        current.value.dictFieldUrl = '';
        console.error(e);
      }
    } else {
      current.value.dictFieldUrl = '';
    }
  } else {
    current.value.dictFieldUrl = '/dictionary/dictionaryField/userInfo/';
  }
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.groupName && data.groupName.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});

queryGroupData();
</script>

<script>
export default {
  name: 'SystemDictionaryField'
};
</script>
