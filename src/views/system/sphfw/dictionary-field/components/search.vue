<!-- 搜索表单 -->
<template>
  <ele-card :style="{marginBottom:'0px!important'}"
            :body-style="{ padding: '6px 0 0px 0px !important' }">
    <el-form size="small" @keyup.enter="search" @submit.prevent=""
             label-width="auto"
             label-position="right"
             style="margin: 0 5px;">
      <el-row :gutter="8">
        <!--         <el-col  :md="6" :sm="12" :xs="24">-->
        <!--          <el-form-item label="所属信息组">-->
        <!--            <dict-data code="groupType" :dicQueryParams="{userType:userType}" v-model="form.groupId"-->
        <!--                       placeholder="请选择所属信息组"/>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label="字段中文名">
            <el-input clearable
                      v-model.trim="form.fieldZh"
                      placeholder="请输入字段中文名"/>
          </el-form-item>
        </el-col>
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label="是否必填">
            <el-select clearable
                       v-model="form.required"
                       placeholder="请选择是否必填"
                       class="ele-fluid">
              <el-option value="是" label="是"/>
              <el-option value="否" label="否"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label="是否显示">
            <el-select clearable
                       v-model="form.showFlag"
                       placeholder="请选择是否显示"
                       class="ele-fluid">
              <el-option value="是" label="是"/>
              <el-option value="否" label="否"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="searchExpand" :md="6" :sm="12" :xs="24">
          <el-form-item label="控件类型">
            <dict-data code="controlType" v-model="form.controlType" placeholder="请选择控件类型"/>
          </el-form-item>
        </el-col>
        <el-col v-if="searchExpand" :md="6" :sm="12" :xs="24">
          <el-form-item label="是否统计">
            <el-select clearable
                       v-model="form.countFlag"
                       placeholder="请选择是否统计"
                       class="ele-fluid">
              <el-option value="是" label="是"/>
              <el-option value="否" label="否"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="searchExpand" :md="6" :sm="12" :xs="24">
          <el-form-item label="培养层次">
            <dict-data code="pycc" v-model="form.eduLevelId" placeholder="请选择培养层次"/>
          </el-form-item>
        </el-col>
        <el-col :md="6" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" size="small" plain @click="search">查询</el-button>
            <el-button @click="reset" size="small">重置</el-button>
            <el-link type="primary"
                     underline="never"
                     @click="toggleExpand"
                     style="margin-left: 12px">
              <template v-if="searchExpand">
                <span>收起</span>
                <el-icon style="vertical-align: -1px">
                  <ArrowUp/>
                </el-icon>
              </template>
              <template v-else>
                <span>展开</span>
                <el-icon style="vertical-align: -2px">
                  <ArrowDown/>
                </el-icon>
              </template>
            </el-link>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import {ref, unref} from 'vue';
import {ArrowDown, ArrowUp} from '@/components/icons';
import {useFormData} from '@/utils/use-form-data';
import {useRouter} from "vue-router";

const emit = defineEmits(['search']);

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const userType = path.split("/")[4];

/** 表单数据 */
const [form, resetFields] = useFormData({
  groupId: '',
  fieldZh: '',
  required: '',
  showFlag: '',
  controlType: '',
  countFlag: '',
  eduLevelId: '',
});

/** 日期范围 */
const dateRange = ref(['', '']);

/** 搜索表单是否展开 */
const searchExpand = ref(false);

/** 搜索 */
const search = () => {
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  dateRange.value = ['', ''];
  search();
};

/** 搜索展开/收起 */
const toggleExpand = () => {
  searchExpand.value = !searchExpand.value;
};
</script>
