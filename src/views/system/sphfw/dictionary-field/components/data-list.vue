<template>
  <!-- 搜索表单 -->
  <search ref="searchRef" @search="searchReload" />
  <!-- 表格 -->
  <ele-pro-table
    ref="tableRef"
    row-key="id"
    :columns="columns"
    :datasource="datasource"
    :border="true"
    :show-overflow-tooltip="true"
    v-model:selections="selections"
    highlight-current-row
    tooltip-effect="light"
    cache-key="sphfwDictionary"
    style="
      padding-bottom: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    "
    :table-style="{ flex: 1, height: '100%', overflow: 'hidden' }"
    :footer-style="{ paddingBottom: '3px' }"
  >
    <template #toolbar>
      <div style="display: flex; align-items: center">
        <el-button
          v-if="groupId"
          size="small"
          class="ele-btn-icon"
          @click="openEdit()"
        >
          新建
        </el-button>
        <el-button size="small" class="ele-btn-icon" @click="remove()">
          删除
        </el-button>
      </div>
    </template>
    <template #fieldZh="{ row }">
      <ele-tooltip content="点我去设置字段信息" effect="light">
        <el-link type="primary" underline="never" @click="openEdit(row)">
          {{ row.fieldZh }}
        </el-link>
      </ele-tooltip>
    </template>

    <template #action="{ row }">
      <template
        v-if="['select', 'radio', 'radioButton'].includes(row['controlType'])"
      >
        <el-link type="primary" underline="never" @click="dropClick(row)">
          设置字段显示隐藏
        </el-link>
      </template>
    </template>
  </ele-pro-table>
  <!-- 编辑弹窗 -->
  <edit v-model="showEdit" :data="current" @done="reload" />
  <!-- 字段显示隐藏设置-->
  <field-link
    v-model="showFieldLink"
    :userType="userType"
    :data="currentFieldLink"
    @done="reload"
  />
  <!-- 字段值下拉及联-->
  <field-value-link
    v-model="showFieldValueLink"
    :userType="userType"
    :data="currentFieldLink"
    @done="reload"
  />
</template>

<script setup>
  import { computed, onActivated, reactive, ref, unref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    ArrowLeftOutlined
  } from '@/components/icons';
  import Edit from './edit.vue';
  import fieldLink from './field-link.vue';
  import fieldValueLink from './field-value-link.vue';
  import nameFilter from './name-filter.vue';
  import Search from './search.vue';
  import { queryPage, removes } from '../api/index.js';
  import { useDictData } from '@/utils/use-dict-data';
  import { transformDicDataName } from '@/utils/common_bak2.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { SearchOutlined } from '@/components/icons/index.js';
  import IconPark from '@/components/IconPark/index.vue';

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const ROUTE_PATH = '/system/sphfw/dictionary-field';

  const props = defineProps({
    currentGroup: Object,
    /** 字典id */
    currentGroupId: String,
    currentGroupName: String
  });

  //获取浏览器参数
  const { params, path, query } = unref(currentRoute);
  console.log(query);
  const userType = path.split('/')[4];
  let groupId = props.currentGroupId;
  const groupName = props.currentGroupName;
  const dictFieldUrl = query?.dictFieldUrl;

  const userStore = useUserStore();
  let dicCodes = [
    'groupType',
    'controlType',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode'
  ];
  useDictData(dicCodes, { userType: userType });
  const { dicts } = storeToRefs(userStore);
  console.log(dicts.value);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 45,
        align: 'center',
        fixed: 'left',
        reserveSelection: true
      },
      {
        prop: 'fieldZh',
        label: '字段中文名',
        slot: 'fieldZh',
        minWidth: 110,
        fixed: 'left'
      },
      {
        prop: 'fieldEn',
        label: '字段英文名',
        minWidth: 110
      },
      {
        prop: 'required',
        label: '是否必填',
        minWidth: 110
      },
      {
        prop: 'showFlag',
        label: '是否显示',
        minWidth: 110
      },
      {
        prop: 'listShowFlag',
        label: '列表是否显示',
        minWidth: 110
      },
      {
        prop: 'sort',
        label: '排序',
        sortable: 'custom',
        minWidth: 110
      },
      {
        prop: 'startTime',
        label: '开始时间',
        minWidth: 110
      },
      {
        prop: 'endTime',
        label: '截至时间',
        minWidth: 110
      },
      {
        prop: 'controlType',
        label: '控件类型',
        slot: 'controlTypeName',
        formatter: (row) => {
          let newArray = row.controlType ? [row.controlType] : [];
          return transformDicDataName(dicts.value['controlType'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'regExpression',
        label: '正则验证表达式',
        minWidth: 110
      },
      {
        prop: 'loadDataType',
        label: '加载数据',
        formatter: (row) => {
          let newArray = row.loadDataType ? [row.loadDataType] : [];
          return transformDicDataName(dicts.value['listDicCode'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'loadDataUrl',
        label: '加载数据URL',
        minWidth: 110
      },
      { prop: 'valueField', label: 'value字段名' },
      { prop: 'textField', label: 'text字段名' },
      {
        prop: 'countFlag',
        label: '是否统计',
        minWidth: 110
      },
      {
        prop: 'tipText',
        label: '温馨提示',
        minWidth: 110
      },
      {
        prop: 'selfModifyFlag',
        label: '是否可修改',
        minWidth: 110
      },
      {
        prop: 'selfModifyVerifyFlag',
        label: '修改是否审核',
        minWidth: 110
      },
      {
        prop: 'modifyRole',
        label: '可修改角色',
        formatter: (row) => {
          let newArray = row.modifyRole ? row.modifyRole.split(',') : [];
          return transformDicDataName(dicts.value['listRoles'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'hideRoles',
        label: '不可见角色',
        formatter: (row) => {
          let newArray = row.hideRoles;
          return transformDicDataName(
            dicts.value['listRoles'],
            newArray,
            'roleId'
          );
        }
      },
      {
        prop: 'eduLevels',
        label: '培养层次',
        formatter: (row) => {
          let newArray = row.eduLevels;
          return transformDicDataName(
            dicts.value['pycc'],
            newArray,
            'eduLevelId'
          );
        }
      },
      {
        columnKey: 'action',
        label: '操作',
        slot: 'action',
        fixed: 'right',
        minWidth: 125
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 是否打开关联关系设置 */
  const showFieldLink = ref(false);
  const showFieldValueLink = ref(false);
  const currentFieldLink = ref(null);

  /** 修改页签标题 */
  const setUserTabTitle = () => {
    if (groupId && unref(currentRoute).path.startsWith(ROUTE_PATH)) {
      setPageTab({
        key: getRouteTabKey(),
        // title: `aaa[${form.nickname}]`
        title: (userType === 'student' ? '学生' : '教师') + groupName
      });
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    setUserTabTitle();
    if (userType) where['userType'] = userType;
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      tempField: '否',
      groupId: groupId
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  const searchReload = (where) => {
    reload(where);
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      tableRef['value']?.reload?.();
    }
  };

  /** 关联关系设置 */
  const dropClick = async (row, valLink) => {
    currentFieldLink.value = {
      groupId: row.groupId,
      fieldId: row.id,
      fieldEn: row.fieldEn,
      controlType: row.controlType,
      loadDataType: row.loadDataType,
      loadDataUrl: row.loadDataUrl
    };
    if (valLink) {
      showFieldValueLink.value = true;
    } else {
      showFieldLink.value = true;
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    if (row) {
      //编辑
      if (row.startTime && row.endTime)
        row.timeRange = [row.startTime, row.endTime];
      current.value = row;
    } else {
      //添加(字段组页面进入用添加功能)
      current.value = {
        userType: userType,
        enterType: 'group',
        dictFieldUrl: dictFieldUrl ?? props.currentGroup.dictFieldUrl,
        groupId: groupId
      };
    }
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  onActivated(() => {
    setUserTabTitle();
  });

  // 监听字典id变化
  watch(
    () => props.currentGroupId,
    (currentGroupId) => {
      groupId = currentGroupId;
      searchRef['value']?.resetFields?.();
      reload({});
    }
  );
</script>

<script>
  export default {
    name: 'SphfwDictionaryField'
  };
</script>
