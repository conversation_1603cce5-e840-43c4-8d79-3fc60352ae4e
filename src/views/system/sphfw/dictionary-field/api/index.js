import request from '@/utils/request';

/**
 * type=other,
 */
export async function getDicFieldValueByUrl(dictionaryFieldUrl, params) {
    const res = await request.get(dictionaryFieldUrl, {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询用户信息字段
 */
export async function getDictionaryFieldByGroupId(groupId, dictionaryFieldUrl) {
    const res = await request.get(dictionaryFieldUrl + groupId);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据groupId查询
 */
export async function getDictionaryFieldById(id) {
    const res = await request.get('/dictionary/dictionaryField/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 查询不分页
 */
export async function getDictionaryField(params) {
    const res = await request.get('/dictionary/dictionaryField', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/dictionary/dictionaryField/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 添加&修改
 */
export async function operation(params) {
    const res = await request.post('/dictionary/dictionaryField/operation', params);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/dictionary/dictionaryField/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

