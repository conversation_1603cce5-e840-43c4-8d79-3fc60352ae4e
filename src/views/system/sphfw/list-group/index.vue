<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        cache-key="sphfwDictionary"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #groupName="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.groupName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    ClusterOutlined
  } from '@/components/icons';
  import Edit from './components/edit.vue';
  import { queryPage, removes } from './api/index';
  import Search from '@/views/base-code/dwb/components/search.vue';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'groupName',
      slot: 'groupName',
      label: '组名称',
      headerSlot: 'groupNameHeader',
      minWidth: 110,
      fixed: 'left'
    },
    {
      prop: 'loadDataUrl',
      label: '加载数据接口',
      minWidth: 110
    },
    {
      prop: 'operationDataUrl',
      label: '新增及修改数据接口',
      minWidth: 110
    },
    {
      prop: 'deleteDataUrl',
      label: '删除数据接口',
      minWidth: 110
    },
    {
      prop: 'dictionaryFieldUrl',
      label: '字典数据接口',
      minWidth: 110
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.groupName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SphfwDictionary'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
