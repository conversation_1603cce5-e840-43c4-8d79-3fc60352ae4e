<!-- 列表数据源编辑弹窗 -->
<template>
  <ele-drawer :size="510"
              :title="isUpdate ? '修改列表数据源' : '添加列表数据源'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-position="top"
             label-width="120px"
             @submit.prevent="">
      <el-form-item label="组名称" prop="groupName">
        <el-input clearable
                  v-model="form.groupName"
                  placeholder="请输入组名称"/>
      </el-form-item>
      <el-form-item label="加载数据接口" prop="loadDataUrl">
        <el-input clearable
                  v-model="form.loadDataUrl"
                  placeholder="请输入加载数据接口"/>
      </el-form-item>
      <el-form-item label="新增及修改数据接口" prop="operationDataUrl">
        <el-input clearable
                  v-model="form.operationDataUrl"
                  placeholder="请输入新增及修改数据接口"/>
      </el-form-item>
      <el-form-item label="删除数据接口" prop="deleteDataUrl">
        <el-input clearable
                  v-model="form.deleteDataUrl"
                  placeholder="请输入删除数据接口"/>
      </el-form-item>
      <el-form-item label="字典数据接口" prop="dictionaryFieldUrl">
        <el-input clearable
                  v-model="form.dictionaryFieldUrl"
                  placeholder="请输入字典数据接口"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation} from '../api/index';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  groupName: '',
  loadDataUrl: '',
  operationDataUrl: '',
  deleteDataUrl: '',
  dictionaryFieldUrl: '',
});

/** 表单验证规则 */
const rules = reactive({
  groupName: [
    {
      required: true,
      message: '请输入组名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields(props.data);
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);
</script>
