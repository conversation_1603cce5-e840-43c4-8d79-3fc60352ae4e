<template>
    <div>
        <ele-upload-list :drag="true"
                         :limit="limit"
                         v-model="images"
                         :readonly="loading"
                         :disabled="disabled"
                         @upload="onUpload"
                         @remove="onRemove"/>
    </div>
</template>

<script setup>
    import {computed, ref,} from 'vue';
    import {ElMessageBox} from 'element-plus/es';
    import { ElMessage as EleMessage} from "element-plus";

    const emit = defineEmits(['update:modelValue']);

    const props = defineProps({
        /** 数据 */
        modelValue: Array,
        limit: Number,
    });

    /** 数据 */
    const images = computed(() => {
        const result = [];
        const val = props.modelValue;
        if (val == null || val === '') {
            return result;
        }
        const values = Array.isArray(val) ? val : [val];
        return values;
    });

    /** 是否禁用 */
    const disabled = ref(false);

    /** 上传状态 */
    const loading = ref(false);

    /** 上传事件, 只添加, 不请求后台 */
    const onUpload = (item) => {
        if (!item.file) {
            return;
        }
        if (item.file.size / 1024 / 1024 > 2) {
            EleMessage.error('大小不能超过 2MB');
            return;
        }
        images.value.push({...item});
        updateModelValue()
    };

    /** 更新modelValue */
    const updateModelValue = () => {
        emit('update:modelValue', images.value);
    };

    /** 删除事件 */
    const onRemove = (item) => {
        ElMessageBox.confirm('确定要删除吗?', '系统提示', {
            type: 'warning',
            draggable: true
        }).then(() => {
            images.value.splice(images.value.indexOf(item), 1);
        }).catch(() => {
        });
    };

</script>
