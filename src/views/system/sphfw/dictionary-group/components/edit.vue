<template>
  <ele-drawer size="40%"
              :title="isUpdate ? '修改信息组' : '新建信息组'"
              :append-to-body="true"
              style="max-width: 100%"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <CommonElForm
      ref="formRef"
      :form-items="formItems"
      :form-model="formModel"
      :label-width="'auto'"
      :label-position="'top'"
      :is-edit-mode="isUpdate"
      @update:form-model="handleModelUpdate"
      @update:form-items="handleItemsUpdate">
      <template
        v-for="name in Object.keys($slots).filter(k => !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
        #[name]="slotProps">
        <slot :name="name" v-bind="slotProps || {}"></slot>
      </template>
    </CommonElForm>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch, unref} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {operation} from '../api/index';
import {useRouter} from 'vue-router';
import {toFormDataPlus, generateForm} from '@/utils/common_bak2.js';
import CommonElForm from '@/components/CommonElForm/index.vue';
import {comColumns} from '../utils/index.js';

const BASE_URL = import.meta.env.BASE_URL;
const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
});

const {currentRoute} = useRouter();
const {params, path} = unref(currentRoute);
const userType = path.split('/')[4];

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单实例 */
const formRef = ref(null);

const formItems = ref([]);
const formModel = ref({});

// 数据更新处理
function handleModelUpdate(newModel) {
  formModel.value = newModel;
}

function handleItemsUpdate(newItems) {
  formItems.value = newItems;
}

/** 提交状态 */
const loading = ref(false);
/** 提交 */
const onSubmit = async () => {
  try {
    const isValid = await formRef.value?.validate();
    console.log('验证结果', isValid); // 调试3
    if (!isValid) {
      console.warn('验证未通过');
      return false;
    }
    let formData = toFormDataPlus({
      ...formModel.value,
      eduLevels: null,
      userType: userType,
    });
    if (Array.isArray(formModel.value.eduLevels) && formModel.value.eduLevels.length > 0) {
      formModel.value.eduLevels.forEach((item, index) => {
        const id = typeof item === 'string' ? item : item?.eduLevelId;
        if (id) {
          formData.append(`eduLevels[${index}].eduLevelId`, id);
        }
      });
    }
    // 手动检查FormData内容
    for (const [key, value] of formData.entries()) {
      console.log(key, value);
    }
    loading.value = true;
    operation(formData).then(msg => {
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch(e => EleMessage.error(e.message)).finally(() => loading.value = false);
  } catch (e) {
    console.error('保存出错', e);
  }
};

/** 更新modelValue并重置表单 */
const updateModelValue = (value) => {
  if (!value) {
    // 关闭时重置表单
    formRef.value?.resetFields?.();
    formModel.value = {};
  }
  emit('update:modelValue', value);
};

// 修改watch逻辑，初始化时清空表单
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      const baseColumns = comColumns(userType) || [];
      formItems.value = baseColumns.filter(item => item.prop && !item.disabled);

      if (props.data) {

        const oldFiles = props.data.icon
          ? JSON.parse(props.data.icon).map((d, i) => {
            return {key: i, url: BASE_URL + 'api/file/inline/' + d.id, status: 'done'};
          }) : [];

        let newArray = props.data?.eduLevels;
        const eduLevelIds = newArray.map(item => item.eduLevelId);

        formModel.value = {
          ...props.data,
          file_icon: oldFiles,
          eduLevels: eduLevelIds,
        };
        isUpdate.value = true;
      } else {
        // 新建时确保表单是干净的
        formModel.value = generateForm(formItems.value);
        isUpdate.value = false;
      }
    } else {
      formRef.value?.clearValidate?.();
    }
  },
  {immediate: true},
);
</script>

