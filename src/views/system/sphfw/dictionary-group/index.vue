<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        cache-key="sphfwDictionaryGroup"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #groupName="{ row }">
          <ele-tooltip content="字段列表维护" effect="light" placement="left">
            <el-link
              type="primary"
              underline="never"
              @click="dropClick('fieldList', row)"
            >
              {{ row.groupName }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #action="{ row }">
          <!--                    <el-divider direction="vertical"/>-->
          <el-link type="primary" underline="never" @click="openEdit(row)">
            修改
          </el-link>
          <!--                    <el-divider direction="vertical"/>-->
          <!--                    <el-link type="primary" underline="never" @click="remove(row)">-->
          <!--                        删除-->
          <!--                    </el-link>-->
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            underline="never"
            @click="dropClick('createField', row)"
          >
            新建字段
          </el-link>
          <!--          <ele-dropdown :items="optItems(row)"-->
          <!--                        @command="(key) => dropClick(key, row)">-->
          <!--            <el-link type="primary" underline="never">-->
          <!--              <span>更多</span>-->
          <!--              <el-icon :size="12">-->
          <!--                <ArrowDown/>-->
          <!--              </el-icon>-->
          <!--            </el-link>-->
          <!--          </ele-dropdown>-->
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 编辑碎片组件弹窗 -->
    <editField v-model="showEditField" :data="currentField" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import {
    ElMessageBox,
    ElMessage as EleMessage,
    ElLoading
  } from 'element-plus';
  import Edit from './components/edit.vue';
  import EditField from '@/views/system/sphfw/dictionary-field/components/edit.vue';
  import nameFilter from './components/name-filter.vue';
  import { queryPage, removes } from './api/index';

  import { useRouter } from 'vue-router';
  import { useDictData } from '@/utils/use-dict-data';
  import { insertAtIndex, transformDicDataName } from '@/utils/common_bak2.js';
  import { getListGroupConfigById } from '@/views/system/sphfw/list-group/api';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import NameFilter from '@/views/exam/exam-management/components/name-filter.vue';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];
  /** 所属信息组字典数据 */
  const userStore = useUserStore();
  let dicCodes = [
    'groupType',
    'controlType',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode'
  ];
  useDictData(dicCodes, { userType: userType });
  const { dicts } = storeToRefs(userStore);
  console.log(dicts.value);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'groupName',
      label: '字段组',
      headerSlot: 'groupNameHeader',
      slot: 'groupName',
      fixed: 'left'
    },
    {
      prop: 'showFlag',
      label: '是否显示'
    },
    {
      prop: 'activeFlag',
      label: '是否需要激活'
    },
    {
      prop: 'addFlag',
      label: '是否可添加'
    },
    {
      prop: 'modifyFlag',
      label: '是否可修改'
    },
    {
      prop: 'emptyDataHide',
      label: '空数据隐藏'
    },
    {
      prop: 'listFlag',
      label: '是否列表'
    },
    {
      prop: 'listGroupId',
      label: '数据来源',
      formatter: (row) => {
        let newArray = row.listGroupId ? [row.listGroupId] : [];
        return transformDicDataName(dicts.value['listGroup'], newArray);
      }
      // type: 'listGroup',
    },

    {
      prop: 'sort',
      label: '排序',
      width: 80,
      sortable: 'custom'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 140,
      fixed: 'right'
    }
  ]);
  let pyccItem = {
    prop: 'eduLevels',
    label: '培养层次',
    formatter: (row) => {
      let newArray = row.eduLevels;
      return transformDicDataName(dicts.value['pycc'], newArray, 'eduLevelId');
    }
  };

  // 在下标为9的位置插入培养层次
  if (userType === 'student') insertAtIndex(columns.value, pyccItem, 9);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 当前字段编辑数据 */
  const currentField = ref({});

  /** 是否显示字段编辑弹窗 */
  const showEditField = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      userType: userType
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.groupName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 操作更多*/
  const optItems = () => {
    let itemsArray = [
      { title: '新建字段', command: 'createField' },
      { title: '字段维护', command: 'fieldList' }
    ];
    return itemsArray;
  };

  /** 下拉菜单点击事件 */
  const dropClick = async (key, row) => {
    currentField['value'].userType = userType;
    currentField['value'].dictFieldUrl =
      '/dictionary/dictionaryField/userInfo/';
    if (row.listGroupId) {
      const result = await getListGroupConfigById(row.listGroupId).catch((e) =>
        console.error(e)
      );
      currentField['value'].dictFieldUrl = result.dictionaryFieldUrl;
    }
    /** 打开字段field编辑弹窗 */
    if (key === 'createField') {
      currentField['value'].groupId = row.id;
      currentField['value'].enterType = 'group';
      showEditField.value = true;
    } else if (key === 'fieldList') {
      push({
        path:
          '/system/sphfw/dictionary-field/' +
          userType +
          '/' +
          row.id +
          '/' +
          row.groupName,
        query: { dictFieldUrl: currentField['value'].dictFieldUrl }
      });
    }
  };
</script>

<script>
  export default {
    name: 'SphfwDictionaryGroup'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
