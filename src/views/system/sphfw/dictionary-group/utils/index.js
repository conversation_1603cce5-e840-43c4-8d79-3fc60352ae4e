export function comColumns(usertype) {
  const baseColumns = [
    {
      prop: 'groupName', label: '字段名', type: 'input', required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 24,
    },
    {
      prop: 'listFlag',
      label: '是否列表',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
      fieldLinks: [
        {
          fieldEn: 'listFlag',
          fieldVal: '是',
          showFlag: '是',
          linkField: 'listGroupId',
        },
        {
          fieldEn: 'listFlag',
          fieldVal: '否',
          showFlag: '否',
          linkField: 'listGroupId',
        },
      ],
    },
    {
      prop: 'listGroupId',
      label: '数据来源',
      // type: 'listGroup',
      type: 'dictSelect',
      typekey: 'select',
      props: {
        code: 'listGroup',
        filterable: true,
      },
      required: true,
      showFlag: '否',
      selfModifyFlag: '否',
      colProps: 6,
    },
    {
      prop: 'showFlag',
      label: '是否显示',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'activeFlag',
      label: '是否需要激活',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'addFlag',
      label: '是否可添加',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'modifyFlag',
      label: '是否可修改',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'emptyDataHide',
      label: '空数据隐藏',
      type: 'switch',
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'sort',
      label: '排序',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 6,
    },
    {
      prop: 'file_icon',
      label: '图标',
      // type: 'fileUpload',
      type: 'imageUpload',
      // type: 'dicIcon',
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 24,
    },
  ];

  // 动态添加学生专属字段
  if (usertype === 'student') {
    baseColumns.splice(1, 0, {
      prop: 'eduLevels',
      label: '培养层次',
      type: 'dictSelect',
      typeKey: 'multipleSelect',
      props: {code: 'pycc'},
      required: true,
      showFlag: '是',
      selfModifyFlag: '是',
      colProps: 24,
    });
  }

  return baseColumns;
}
