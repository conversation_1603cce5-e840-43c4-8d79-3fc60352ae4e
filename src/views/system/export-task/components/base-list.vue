<template>
  <!-- 表格 -->
  <ele-pro-table ref="tableRef"
                 row-key="id"
                 :columns="columns"
                 :datasource="datasource"
                 :border="true"
                 :show-overflow-tooltip="true"
                 v-model:selections="selections"
                 tooltip-effect="light"
                 :tools="['reload', 'size', 'columns', 'maximized']"
                 highlight-current-row
                 cache-key="systemUserTable"
                 :footer-style="{ paddingBottom: '3px' }"
                 style="padding-bottom: 0">
    <template #toolbar>
      <el-button size="small"
                 @click="removeOpt()">
        删除
      </el-button>
    </template>
    <template #taskName="{ row }">
      <ele-tooltip content="下载导出文件" placement="left" effect="light">
        <el-link type="primary" underline="never"
                 @click="handleExportTaskDownload(row)">
          {{ row.taskName }}
        </el-link>
      </ele-tooltip>
    </template>
  </ele-pro-table>
</template>

<script setup>
import {ref} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";

import {queryPage, removeBatch,} from '../api/index';
import {getToken} from "@/utils/token-util";
import {getCurrentRole} from "@/utils/current-role-util";
import {usePermission} from '@/utils/use-permission';

const {hasPermission,} = usePermission();


const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'taskName',
    slot: 'taskName',
    label: '任务名称',
  },
  {
    prop: 'businessType',
    label: '业务类型',
  },
  {
    prop: 'exportType',
    label: '导出类型',
  },
  {
    prop: 'fileName',
    label: '文件名',
  },
  {
    prop: 'status',
    slot: 'status',
    label: '状态',
  },
  {
    prop: 'createTime',
    label: '创建时间',
  },
  {
    prop: 'expireTime',
    label: '过期时间',
  },
]);

/** 表格选中数据 */
const selections = ref([]);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit});
};

/** 下载导出文件 */
const handleExportTaskDownload = (row) => {
  window.location.href = BASE_URL + 'api/system/export-task/download/' + row.id + '?access_token=' + accessToken
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

/** 删除 */
const removeOpt = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removeBatch(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'EXPORTTASKBASELIST'
};
</script>
