/**
 * 文件导出任务
 */
import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryPage(routeType, params) {
    const res = await request.get('system/export-task/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removeBatch(routeType,data) {
    const res = await request.post('/system/export-task/removeBatch', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 下载导出文件
 */
export async function exportTaskDownload(taskId) {
  const res = await request.get('/system/export-task/download/'+taskId);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

