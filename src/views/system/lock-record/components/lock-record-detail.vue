<template>
  <el-dialog
    :model-value="modelValue"
    title="锁定记录详情"
    width="800px"
    :destroy-on-close="true"
    @update:model-value="updateModelValue"
  >
    <div v-if="record" class="lock-record-detail">
      <!-- 基本信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">基本信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">用户账号：</span>
              <span class="detail-value">{{ record.username || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">IP地址：</span>
              <span class="detail-value">{{ record.clientIp || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">锁定类型：</span>
              <el-tag :type="getLockTypeTagType(record.lockType)">
                {{ getLockTypeText(record.lockType) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">锁定状态：</span>
              <el-tag :type="getStatusTagType(record.status)">
                {{ getStatusText(record.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 策略信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">策略信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">策略名称：</span>
              <span class="detail-value">{{ record.policyName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">触发次数：</span>
              <span class="detail-value">{{ record.triggerCount || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">策略详情：</span>
              <div class="detail-value policy-details">
                {{ record.policyDetails || '-' }}
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 时间信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">时间信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">锁定开始时间：</span>
              <span class="detail-value">{{ record.lockStartTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">锁定结束时间：</span>
              <span class="detail-value">{{ record.lockEndTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">锁定时长：</span>
              <span class="detail-value">{{ getLockDuration() }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">创建时间：</span>
              <span class="detail-value">{{ record.createTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 设备信息 -->
      <el-card shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">设备信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">操作系统：</span>
              <span class="detail-value">{{ record.os || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">浏览器：</span>
              <span class="detail-value">{{ record.browser || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">设备信息：</span>
              <span class="detail-value">{{ record.device || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">User Agent：</span>
              <span class="detail-value user-agent">{{ record.userAgent || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 解锁信息 -->
      <el-card v-if="record.unlockTime || record.unlockReason" shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">解锁信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">解锁时间：</span>
              <span class="detail-value">{{ record.unlockTime || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">解锁操作人：</span>
              <span class="detail-value">{{ record.unlockOperator || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="detail-item">
              <span class="detail-label">解锁原因：</span>
              <div class="detail-value">
                {{ record.unlockReason || '-' }}
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="record.remark" shadow="never" class="detail-card">
        <template #header>
          <span class="card-title">备注信息</span>
        </template>
        <div class="detail-item">
          <div class="detail-value remark">
            {{ record.remark }}
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="updateModelValue(false)">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue']);

/**
 * 更新显示状态
 */
function updateModelValue(value) {
  emit('update:modelValue', value);
}

/**
 * 获取锁定类型文本
 */
function getLockTypeText(type) {
  const typeMap = {
    1: '登录失败锁定',
    2: 'IP限流锁定',
    3: '路径限流锁定',
    4: '手动锁定'
  };
  return typeMap[type] || '未知';
}

/**
 * 获取锁定类型标签类型
 */
function getLockTypeTagType(type) {
  const typeMap = {
    1: 'warning',
    2: 'danger',
    3: 'info',
    4: ''
  };
  return typeMap[type] || '';
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
  const statusMap = {
    1: '锁定中',
    2: '已过期',
    3: '已解锁'
  };
  return statusMap[status] || '未知';
}

/**
 * 获取状态标签类型
 */
function getStatusTagType(status) {
  const statusMap = {
    1: 'danger',
    2: 'info',
    3: 'success'
  };
  return statusMap[status] || '';
}

/**
 * 获取锁定时长
 */
function getLockDuration() {
  if (!props.record?.lockStartTime || !props.record?.lockEndTime) {
    return '-';
  }
  
  const start = new Date(props.record.lockStartTime);
  const end = new Date(props.record.lockEndTime);
  const duration = end - start;
  
  const minutes = Math.floor(duration / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}天${hours % 24}小时${minutes % 60}分钟`;
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`;
  } else {
    return `${minutes}分钟`;
  }
}
</script>

<style scoped>
.lock-record-detail {
  max-height: 600px;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
}

.detail-item {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.detail-value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.policy-details {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
}

.user-agent {
  font-size: 12px;
  color: #909399;
  max-width: 200px;
}

.remark {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  white-space: pre-wrap;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 