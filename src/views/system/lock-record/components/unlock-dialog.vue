<template>
  <el-dialog
    :model-value="modelValue"
    title="解锁用户"
    width="600px"
    :destroy-on-close="true"
    @update:model-value="updateModelValue"
  >
    <div v-if="record" class="unlock-dialog">
      <!-- 锁定信息展示 -->
      <el-alert
        :title="`即将解锁${getTargetInfo()}`"
        type="warning"
        :closable="false"
        show-icon
        class="unlock-alert"
      >
        <template #default>
          <div class="lock-info">
            <p><strong>锁定类型：</strong>{{ getLockTypeText(record.lockType) }}</p>
            <p><strong>策略名称：</strong>{{ record.policyName || '-' }}</p>
            <p><strong>锁定时间：</strong>{{ record.lockStartTime || '-' }}</p>
            <p v-if="record.policyDetails"><strong>策略详情：</strong>{{ record.policyDetails }}</p>
          </div>
        </template>
      </el-alert>

      <!-- 解锁表单 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="unlock-form"
      >
        <el-form-item label="解锁原因" prop="reason" required>
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="4"
            placeholder="请详细说明解锁原因，此信息将被记录到系统日志中"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="解锁范围">
          <el-radio-group v-model="form.unlockScope">
            <el-radio value="current">仅解锁当前记录</el-radio>
            <el-radio
              v-if="record.username && record.clientIp"
              value="user"
            >
              解锁用户所有锁定
            </el-radio>
            <el-radio
              v-if="record.clientIp"
              value="ip"
            >
              解锁IP所有锁定
            </el-radio>
          </el-radio-group>
          <div class="unlock-scope-tip">
            <el-text type="info" size="small">
              选择解锁范围会影响解锁的记录数量，请谨慎选择
            </el-text>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="unlock-warning">
            <el-icon><WarningFilled /></el-icon>
            <span>解锁操作将立即生效，用户可以重新尝试登录或访问。此操作将被记录到审计日志中。</span>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="handleUnlock"
      >
        确认解锁
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { WarningFilled } from '@element-plus/icons-vue';
import { unlockUser } from '../api/index';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'done']);

// 表单引用
const formRef = ref(null);

// 加载状态
const loading = ref(false);

// 表单数据
const form = reactive({
  reason: '',
  unlockScope: 'current'
});

// 表单验证规则
const rules = {
  reason: [
    { required: true, message: '请输入解锁原因', trigger: 'blur' },
    { min: 10, message: '解锁原因至少需要10个字符', trigger: 'blur' },
    { max: 500, message: '解锁原因不能超过500个字符', trigger: 'blur' }
  ]
};

// 监听弹窗显示状态，重置表单
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

/**
 * 更新显示状态
 */
function updateModelValue(value) {
  emit('update:modelValue', value);
}

/**
 * 重置表单
 */
function resetForm() {
  form.reason = '';
  form.unlockScope = 'current';
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

/**
 * 获取目标信息
 */
function getTargetInfo() {
  if (!props.record) return '';

  const parts = [];
  if (props.record.username) {
    parts.push(`用户: ${props.record.username}`);
  }
  if (props.record.clientIp) {
    parts.push(`IP: ${props.record.clientIp}`);
  }

  return parts.join(', ') || '当前锁定';
}

/**
 * 获取锁定类型文本
 */
function getLockTypeText(type) {
  const typeMap = {
    1: '登录失败锁定',
    2: 'IP限流锁定',
    3: '路径限流锁定',
    4: '手动锁定'
  };
  return typeMap[type] || '未知';
}

/**
 * 处理解锁
 */
async function handleUnlock() {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    loading.value = true;

    // 构建解锁参数
    const params = new URLSearchParams();
    params.append('reason', form.reason);

    // 根据解锁范围设置参数
    switch (form.unlockScope) {
      case 'current':
        // 当前记录：传递具体的用户名和IP
        if (props.record.username) {
          params.append('username', props.record.username);
        }
        if (props.record.clientIp) {
          params.append('clientIp', props.record.clientIp);
        }
        break;
      case 'user':
        // 用户所有锁定：只传用户名
        if (props.record.username) {
          params.append('username', props.record.username);
        }
        break;
      case 'ip':
        // IP所有锁定：只传IP
        if (props.record.clientIp) {
          params.append('clientIp', props.record.clientIp);
        }
        break;
    }

    await unlockUser('', params);

    ElMessage.success('解锁成功');
    updateModelValue(false);
    emit('done');

  } catch (error) {
    ElMessage.error(error.message || '解锁失败');
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.unlock-dialog {
  padding: 0;
}

.unlock-alert {
  margin-bottom: 20px;
}

.lock-info p {
  margin: 5px 0;
  font-size: 14px;
}

.unlock-form {
  margin-top: 20px;
}

.unlock-scope-tip {
  margin-top: 8px;
}

.unlock-warning {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

.unlock-warning .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

:deep(.el-alert__content) {
  padding-left: 0;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio) {
  margin-right: 0;
}
</style>
