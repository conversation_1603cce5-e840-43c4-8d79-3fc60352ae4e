<template>
  <div class="ele-body">

    <el-card shadow="never">
      <!-- 查询表单 -->
      <el-form
        :model="where"
        label-width="80px"
        class="ele-form-search"
        @keyup.enter="reload"
        @submit.prevent=""
      >
        <el-row :gutter="15">
          <el-col :lg="6" :md="12">
            <el-form-item label="用户账号">
              <el-input
                clearable
                v-model="where.username"
                placeholder="请输入用户账号"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <el-form-item label="IP地址">
              <el-input
                clearable
                v-model="where.clientIp"
                placeholder="请输入IP地址"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <el-form-item label="锁定类型">
              <el-select
                clearable
                v-model="where.lockType"
                placeholder="请选择锁定类型"
                class="ele-fluid"
              >
                <el-option label="登录失败锁定" :value="1" />
                <el-option label="IP限流锁定" :value="2" />
                <el-option label="路径限流锁定" :value="3" />
                <el-option label="手动锁定" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <el-form-item label="锁定状态">
              <el-select
                clearable
                v-model="where.lockStatus"
                placeholder="请选择锁定状态"
                class="ele-fluid"
              >
                <el-option label="锁定中" :value="1" />
                <el-option label="已过期" :value="2" />
                <el-option label="已解锁" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <el-form-item label="策略名称">
              <el-input
                clearable
                v-model="where.policyName"
                placeholder="请输入策略名称"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <el-form-item label="创建时间">
              <el-date-picker
                clearable
                v-model="createTimeRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="ele-fluid"
                @change="onCreateTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="12">
            <div class="ele-form-actions">
              <el-button
                type="primary"
                icon="Search"
                class="ele-btn-icon"
                @click="reload"
              >
                查询
              </el-button>
              <el-button @click="reset">重置</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card shadow="never" class="ele-margin-top-sm">
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        :tools="['reload', 'size', 'columns', 'maximized']"
        highlight-current-row
        cache-key="systemLockRecordTable"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-space>
            <el-button
              size="small"
              icon="Refresh"
              @click="processExpired"
              v-hasPermission="'system:lock-record:manage'"
            >
              处理过期记录
            </el-button>
          </el-space>
        </template>

        <template #lockType="{ record }">
          <el-tag :type="getLockTypeTagType(record.lockType)">
            {{ getLockTypeText(record.lockType) }}
          </el-tag>
        </template>

        <template #status="{ record }">
          <el-tag :type="getStatusTagType(record.lockStatus)">
            {{ getStatusText(record.lockStatus) }}
          </el-tag>
        </template>

        <template #policyDetails="{ record }">
          <el-tooltip
            v-if="record.policyDetails"
            :content="record.policyDetails"
            placement="top"
            :show-after="500"
          >
            <span class="text-ellipsis" style="max-width: 200px; display: inline-block;">
              {{ record.policyDetails }}
            </span>
          </el-tooltip>
          <span v-else>-</span>
        </template>

        <template #lockDuration="{ record }">
          <span v-if="record.lockStartTime && record.lockEndTime">
            {{ formatDuration(record.lockStartTime, record.lockEndTime) }}
          </span>
          <span v-else>-</span>
        </template>

        <template #action="{ record }">
          <el-space>
            <el-link
              type="primary"
              :underline="false"
              @click="showDetail(record)"
            >
              详情
            </el-link>
            <el-link
              v-if="record.lockStatus === 1"
              type="warning"
              :underline="false"
              @click="unlock(record)"
              v-hasPermission="'system:lock-record:unlock'"
            >
              解锁
            </el-link>
          </el-space>
        </template>
      </ele-pro-table>
    </el-card>

    <!-- 详情弹窗 -->
    <lock-record-detail
      v-model="showDetailDialog"
      :record="currentRecord"
    />

    <!-- 解锁弹窗 -->
    <unlock-dialog
      v-model="showUnlockDialog"
      :record="currentRecord"
      @done="reload"
    />

  </div>
</template>

<script setup>
import {ref, reactive} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {pageLockRecords, processExpiredLocks} from './api/index';
import {usePermission} from '@/utils/use-permission';
import LockRecordDetail from './components/lock-record-detail.vue';
import UnlockDialog from './components/unlock-dialog.vue';

const {hasPermission} = usePermission();

// 表格实例
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'username',
    label: '用户账号',
    width: 120,
    showOverflowTooltip: true
  },
  {
    prop: 'clientIp',
    label: 'IP地址',
    width: 140,
    showOverflowTooltip: true
  },
  {
    prop: 'lockType',
    label: '锁定类型',
    width: 120,
    slot: 'lockType'
  },
  {
    prop: 'lockStatus',
    label: '锁定状态',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'policyName',
    label: '策略名称',
    width: 150,
    showOverflowTooltip: true
  },
  {
    prop: 'policyDetails',
    label: '策略详情',
    width: 200,
    slot: 'policyDetails'
  },
  {
    prop: 'triggerCount',
    label: '触发次数',
    width: 100,
    align: 'center'
  },
  {
    prop: 'lockStartTime',
    label: '锁定开始时间',
    width: 180,
    showOverflowTooltip: true
  },
  {
    prop: 'lockEndTime',
    label: '锁定结束时间',
    width: 180,
    showOverflowTooltip: true
  },
  {
    prop: 'lockDuration',
    label: '锁定时长',
    width: 120,
    slot: 'lockDuration'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180,
    showOverflowTooltip: true
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 120,
    slot: 'action',
    align: 'center',
    fixed: 'right'
  }
]);

/** 查询参数 */
const where = reactive({
  username: '',
  clientIp: '',
  lockType: undefined,
  lockStatus: undefined,
  policyName: '',
  createTimeStart: '',
  createTimeEnd: ''
});

/** 创建时间范围 */
const createTimeRange = ref([]);

/** 表格选中数据 */
const selections = ref([]);

/** 弹窗控制 */
const showDetailDialog = ref(false);
const showUnlockDialog = ref(false);
const currentRecord = ref(null);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return pageLockRecords('', {...where, ...orders, ...filters, page, limit});
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    tableRef.value?.reload?.();
  }
};

/** 重置 */
const reset = () => {
  Object.assign(where, {
    username: '',
    clientIp: '',
    lockType: undefined,
    lockStatus: undefined,
    policyName: '',
    createTimeStart: '',
    createTimeEnd: ''
  });
  createTimeRange.value = [];
  reload();
};

/** 创建时间范围改变 */
const onCreateTimeChange = (value) => {
  if (value && value.length === 2) {
    where.createTimeStart = value[0];
    where.createTimeEnd = value[1];
  } else {
    where.createTimeStart = '';
    where.createTimeEnd = '';
  }
};

/** 获取锁定类型文本 */
const getLockTypeText = (type) => {
  const typeMap = {
    1: '登录失败锁定',
    2: 'IP限流锁定',
    3: '路径限流锁定',
    4: '手动锁定'
  };
  return typeMap[type] || '未知';
};

/** 获取锁定类型标签类型 */
const getLockTypeTagType = (type) => {
  const typeMap = {
    1: 'warning',
    2: 'danger',
    3: 'info',
    4: ''
  };
  return typeMap[type] || '';
};

/** 获取状态文本 */
const getStatusText = (status) => {
  const statusMap = {
    1: '锁定中',
    2: '已过期',
    3: '已解锁'
  };
  return statusMap[status] || '未知';
};

/** 获取状态标签类型 */
const getStatusTagType = (status) => {
  const statusMap = {
    1: 'danger',
    2: 'info',
    3: 'success'
  };
  return statusMap[status] || '';
};

/** 格式化时长 */
const formatDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '-';

  const start = new Date(startTime);
  const end = new Date(endTime);
  const duration = end - start;

  const minutes = Math.floor(duration / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天${hours % 24}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

/** 显示详情 */
const showDetail = (record) => {
  currentRecord.value = record;
  showDetailDialog.value = true;
};

/** 解锁 */
const unlock = (record) => {
  currentRecord.value = record;
  showUnlockDialog.value = true;
};

/** 处理过期记录 */
const processExpired = () => {
  ElMessageBox.confirm('确定要处理过期的锁定记录吗？', '提示', {
    type: 'warning'
  }).then(() => {
    processExpiredLocks('').then((msg) => {
      ElMessage.success(msg);
      reload();
    }).catch(e => {
      ElMessage.error(e.message || '处理失败');
    });
  }).catch(() => {});
};


</script>

<script>
export default {
  name: 'SYSTEMLOCKRECORD'
};
</script>

<style scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
