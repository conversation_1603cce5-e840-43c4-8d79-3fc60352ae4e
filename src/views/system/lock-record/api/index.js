/**
 * 锁定记录管理
 */
import request from '@/utils/request';

/**
 * 分页查询锁定记录
 */
export async function pageLockRecords(routeType, params) {
  const res = await request.get('system/lock-record/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询用户当前锁定状态
 */
export async function getCurrentLock(routeType, params) {
  const res = await request.get('system/lock-record/current-lock', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 手动解锁用户
 */
export async function unlockUser(routeType, data) {
  const res = await request.post('system/lock-record/unlock', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 处理过期锁定记录
 */
export async function processExpiredLocks(routeType) {
  const res = await request.post('system/lock-record/process-expired');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
