import request from '@/utils/request';


/**
 * 分页查询模块列表
 */
export async function list(params) {
  const res = await request.get('/system/sys-module-setup', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询模块列表
 */
export async function queryPage(params) {
  const res = await request.get('/system/sys-module-setup/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加/修改模块
 */
export async function operation(data) {
  const res = await request.post('/system/sys-module-setup/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除模块
 */
export async function removes(ids) {
  const res = await request.delete('/system/sys-module-setup/remove', {
    data: ids
  });
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询模块
 */
export async function getById(id) {
  const res = await request.get(`/system/sys-module-setup/${id}`);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
