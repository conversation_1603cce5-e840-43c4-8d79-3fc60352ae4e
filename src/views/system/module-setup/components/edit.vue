<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改模块信息' : '新建模块信息'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      labelPosition="top"
      @updateValue="setFieldValue"
    >
    </pro-form>
    <template #footer>
      <div style="text-align: right; padding: 0 12px">
        <el-button size="small" @click="updateModelValue(false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          :loading="loading"
          @click="submit"
          >确定</el-button
        >
      </div>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data';
  import { getById, operation } from '../api';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    /** 是否显示 */
    modelValue: Boolean,
    /** 编辑数据 */
    data: Object
  });

  /** 更新显示状态 */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    name: '',
    code: '',
    parentCode: '',
    year: '',
    semester: '',
    sfgllc: '否',
    description: ''
  });

  /** 表单项 */
  const items = ref([
    { prop: 'name', label: '模块名称', type: 'input', required: true },
    { prop: 'code', label: '模块编码', type: 'input', required: true },
    {
      prop: 'parentCode',
      label: '父模块编码',
      type: 'input',
      placeholder: '留空表示一级模块'
    },
    {
      prop: 'year',
      label: '年份',
      type: 'input',
      required: true,
      placeholder: '子模块将继承父模块年份'
    },
    { prop: 'semester', label: '学期', type: 'input' },
    {
      prop: 'sfgllc',
      label: '是否关联流程',
      type: 'radioButton',
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' }
      ],
      required: true
    },
    { prop: 'description', label: '备注', type: 'textarea', rows: 3 }
  ]);

  /* 监听数据变化 */
  watch(
    () => props.data,
    (val) => {
      resetFields();
      if (val) {
        isUpdate.value = true;
        const loading = ElLoading.service();
        getById(val.id)
          .then((data) => {
            loading.close();
            assignFields(data);
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      } else {
        isUpdate.value = false;
      }
    },
    { immediate: true }
  );

  /* 提交表单 */
  const submit = () => {
    formRef.value
      ?.validate?.()
      .then(() => {
        loading.value = true;
        operation(form)
          .then(() => {
            loading.value = false;
            EleMessage.success(isUpdate.value ? '修改成功' : '添加成功');
            updateModelValue(false);
            emit('done');
          })
          .catch((e) => {
            loading.value = false;
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
