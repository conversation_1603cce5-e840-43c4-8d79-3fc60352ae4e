export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'username',
      label: '用户账号',
      type: 'input',
      minWidth: 110,
      showFlag: 'readonly',
      selfModifyFlag: '否',
      colProps: {span: 12},
    },
    {
      prop: 'realName',
      slot: 'realName',
      label: '用户名',
      type: 'input',
      minWidth: 110,
      showFlag: 'readonly',
      selfModifyFlag: '否',
      colProps: {span: 12},
    },
    {
      prop: 'gender',
      label: '性别',
      sortable: 'custom',
      width: 90,
      columnKey: 'gender',
      showFlag: 'readonly',
      selfModifyFlag: '否',
      filters: [
        {text: '男', value: '男'},
        {text: '女', value: '女'},
      ],
      filterMultiple: false,
      colProps: {span: 12},
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'xb',
        filterable: true,
        dicQueryParams: {
          getValType: 'name',
        },
      },
    },
    {
      prop: 'idType',
      label: '身份类型',
      slot: 'idType',
      type: 'input',
      minWidth: 110,
      showFlag: 'readonly',
      selfModifyFlag: '否',
      colProps: {span: 12},
    },
    {
      prop: 'idCode',
      label: '身份号码',
      slot: 'idCode',
      type: 'input',
      minWidth: 110,
      showFlag: 'readonly',
      selfModifyFlag: '否',
      colProps: {span: 12},
    },
    {
      columnKey: 'roleName',
      label: '角色',
      slot: 'roleName',
      colProps: {span: 12},
      disabled: true,
    },
    {
      prop: 'telMobile',
      label: '手机号',
      type: 'input',
      slot: 'telMobile',
      showFlag: 'readonly',
      selfModifyFlag: '否',
      colProps: {span: 12},
    },
    {
      prop: 'status',
      label: '是否启用',
      width: 110,
      sortable: 'custom',
      formatter: (row) => {
        return row.status ? (row.status === 1 ? '启用' : '禁用') : '';
      },
      colProps: {span: 12},
      showFlag: '是',
      selfModifyFlag: '是',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'qyjy',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
      },
    },
    {
      prop: 'lockStatus',
      label: '锁定状态',
      width: 130,
      sortable: 'custom',
      formatter: (row) => {
        return row.lockStatus ? (row.lockStatus === 1 ? '正常' : '锁定') : '';
      },
      colProps: {span: 12},
      showFlag: '是',
      selfModifyFlag: '是',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'lockStatus',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
      },
    },
    {
      prop: 'passwordExpireTime',
      label: '密码过期时间',
      type: 'datetime',
      colProps: {span: 12},
      showFlag: '是',
      selfModifyFlag: '是',
    },
    {
      prop: 'accountExpireTime',
      label: '帐户过期时间',
      type: 'datetime',
      colProps: {span: 12},
      showFlag: '是',
      selfModifyFlag: '是',
    },
    {
      prop: 'passwordLastUpdateTime',
      label: '最后更新密码时间',
      type: 'datetime',
      colProps: {span: 12},
      showFlag: '是',
      selfModifyFlag: '是',
      disabled: true,
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      // fixed: 'right'
    },
  ];
}
