<template>
  <ele-drawer
    :size="680"
    :title="isUpdate ? '用户权限设置' : '新建用户'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px', paddingTop: '1px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="small"
      label-position="top"
      @submit.prevent=""
    >
      <!-- 角色设置部分 -->
      <el-divider content-position="left">角色设置</el-divider>
      <dragsort-list v-model="form.roleList"/>
      <!-- 用户信息部分 -->
      <el-divider content-position="left">用户信息</el-divider>
      <pro-form
        ref="proFormRef"
        size="small"
        :model="form"
        :items="formItems"
        :grid="{ span: 24 }"
        label-width="auto"
        label-position="top"
        @updateValue="setFieldValue"
      />
    </el-form>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" plain size="small" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, nextTick} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {useFormData} from '@/utils/use-form-data';
import DragsortList from './dragsort-list.vue';
import ProForm from '@/components/ProForm/index.vue';
import {operationUser, checkExistence} from '../api/index';
import {comColumns} from '../utils/index.js';
import {generateForm} from '@/utils/common_bak2.js';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
const proFormRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  // username: '',
  // realName: '',
  // gender: '',
  // idType: '',
  // idCode: '',
  // telMobile: '',
  // status: '1',
  // lockStatus: '0',
  // roleList: [],
  // accountNonExpired: true,
  // accountExpireTime: null,
  // passwordLastUpdateTime: null,
  // lastUsedRole: null
});

const formItems = ref([]);

/** 表单验证规则 */
const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入用户账号',
      type: 'string',
      trigger: 'blur',
    },
    {
      min: 4,
      message: '账号长度最少为4位',
      type: 'string',
      trigger: 'blur',
    },
    {
      type: 'string',
      trigger: 'blur',
      validator: (_rule, value, callback) => {
        checkExistence({'username': value}).then(() => {
          if (isUpdate.value) {
            callback();
          } else {
            callback(new Error('账号已经存在'));
          }
        }).catch(() => {
          callback();
        });
      },
    },
  ],
  roleList: [
    {
      required: true,
      message: '请选择角色',
      type: 'array',
      trigger: 'blur',
    },
  ],
});

/** 格式化表单数据 */
const formatFormData = (data) => {
  if (!data) return {};
  return {
    ...data,
    status: String(data.status || '1'),
    lockStatus: data.lockStatus ? String(data.lockStatus) : '',
  };
};

/** 保存编辑 */
const save = async () => {
  try {
    // 验证表单
    await formRef.value.validate();

    loading.value = true;

    // 准备提交数据
    const submitData = {
      ...form,
      // 确保状态字段是后端需要的格式
      status: parseInt(form.status),
      lockStatus: form.lockStatus ? parseInt(form.lockStatus) : 0,
    };

    const msg = await operationUser(submitData);
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  } catch (e) {
    if (e.message) {
      EleMessage.error(e.message);
    }
  } finally {
    loading.value = false;
  }
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  async (modelValue) => {
    if (modelValue) {
      // 初始化表单字段
      const baseColumns = comColumns() || [];
      formItems.value = baseColumns.filter(item => item.prop && !item.disabled);
      const formInitData = generateForm(formItems.value);

      if (props.data) {
        try {
          // 获取用户详情
          checkExistence({'username': props.data.username}).then((res) => {
            assignFields({
              ...formatFormData(res),
            });
          }).catch(() => {
          });

          isUpdate.value = true;
        } catch (e) {
          EleMessage.error('获取用户信息失败');
          console.error(e);
        }
      } else {
        // 新建用户时重置表单
        resetFields(formInitData);
        isUpdate.value = false;
      }

      // 确保ProForm更新
      await nextTick();
      if (proFormRef.value) {
        proFormRef.value.resetFields();
      }
    } else {
      // 关闭弹窗时重置表单
      resetFields();
      if (formRef.value) {
        formRef.value.clearValidate();
      }
    }
  },
  {immediate: true},
);
</script>

<style scoped>
/* 可以添加一些自定义样式 */
.el-divider {
  margin: 20px 0;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}
</style>
