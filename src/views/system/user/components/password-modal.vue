<template>
  <!--  <ele-drawer-->
  <!--    :size="430"-->
  <!--    title="重置密码"-->
  <!--    :append-to-body="true"-->
  <!--    style="max-width: 100%"-->
  <!--    :model-value="modelValue"-->
  <!--    :body-style="{ paddingBottom: '8px' }"-->
  <!--    @update:modelValue="updateModelValue"-->
  <!--    @closed="onCancel"-->
  <!--  >-->


  <ele-modal
    :width="420"
    title="重置密码"
    :close-on-click-modal="false"
    :append-to-body="true"
    :model-value="modelValue"
    @update:model-value="updateModelValue"
    @closed="onCancel"
  >
    <el-form
      ref="formRef"
      :model="form"
      size="small"
      :rules="rules"
      label-width="80px"
      @submit.prevent="onOk"
    >
      <el-form-item label="新密码" prop="password">
        <el-input
          show-password
          type="password"
          :maxlength="20"
          v-model="form.password"
          placeholder="请输入6-18位新密码"
          @keyup.enter="onOk"
        />
        <div class="password-tips">
          <span :class="{ active: passwordStrength.length }">长度6-18位</span>
          <span :class="{ active: passwordStrength.complexity }">包含字母和数字</span>
        </div>
      </el-form-item>
      <el-form-item label="确认密码" prop="password2">
        <el-input
          show-password
          type="password"
          :maxlength="20"
          v-model="form.password2"
          placeholder="请再次输入新密码"
          @keyup.enter="onOk"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        type="primary"
        size="small"
        plain
        :loading="loading"
        @click="onOk"
        :disabled="!formValid"
      >
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import {ref, computed, watch, nextTick} from 'vue';
import {ElMessage} from 'element-plus';
import {updateUserPassword} from '../api/index';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
});

// 确保form对象始终有初始值
const form = ref({
  password: '',
  password2: '',
});

const loading = ref(false);
const formRef = ref(null);

// 密码强度计算
const passwordStrength = computed(() => {
  const pwd = form.value.password || '';
  return {
    length: pwd.length >= 6 && pwd.length <= 18,
    complexity: /[a-zA-Z]/.test(pwd) && /[0-9]/.test(pwd),
  };
});

// 表单验证规则
const rules = {
  password: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {min: 6, max: 18, message: '密码长度需在6-18位之间', trigger: 'blur'},
    {
      validator: (_, value, callback) => {
        if (!/[a-zA-Z]/.test(value) || !/[0-9]/.test(value)) {
          callback(new Error('密码需包含字母和数字'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  password2: [
    {required: true, message: '请再次输入新密码', trigger: 'blur'},
    {
      validator: (_, value, callback) => {
        if (value !== form.value.password) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
};

// 表单有效性
const formValid = computed(() => {
  return form.value.password &&
    form.value.password2 &&
    form.value.password === form.value.password2 &&
    passwordStrength.value.length &&
    passwordStrength.value.complexity;
});

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const onOk = async () => {
  try {
    if (!formRef.value) return;

    await formRef.value.validate();

    loading.value = true;
    const response = await updateUserPassword({
      id: props.data?.id || '',
      password: encrypt(form.value.password),
    });

    ElMessage.success(response?.message || '密码修改成功');
    updateModelValue(false);
  } catch (error) {
    if (!error?.errors) {
      ElMessage.error(error?.message || '密码修改失败');
    }
  } finally {
    loading.value = false;
  }
};

const onCancel = () => {
  form.value = {password: '', password2: ''};
  formRef.value?.resetFields?.();
};

// 加载加密脚本
const insertScript = () => {
  return new Promise((resolve, reject) => {
    if (window.encrypt) {
      return resolve();
    }
    const scriptElement = document.createElement('script');
    scriptElement.type = 'text/javascript';
    scriptElement.async = true;
    scriptElement.src = window.location.origin + '/api/sec_js';

    scriptElement.onload = () => {
      if (window.encrypt) {
        resolve();
      } else {
        reject(new Error('加密工具未正确加载'));
      }
    };

    scriptElement.onerror = () => {
      reject(new Error('加载加密脚本失败'));
    };

    document.head.appendChild(scriptElement);
  });
};

watch(() => props.modelValue, (val) => {
  if (val) {
    insertScript();
    nextTick(() => {
      form.value = {password: '', password2: ''};
      formRef.value?.resetFields?.();
    });
  }
});
insertScript();
</script>

<style scoped>
.password-tips {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: flex;
  gap: 12px;
}

.password-tips span {
  position: relative;
  padding-left: 16px;
}

.password-tips span::before {
  content: "";
  position: absolute;
  left: 4px;
  top: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ddd;
}

.password-tips span.active {
  color: #67c23a;
}

.password-tips span.active::before {
  background: #67c23a;
}
</style>
