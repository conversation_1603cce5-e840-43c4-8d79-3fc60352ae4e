<template>
  <el-form label-width="60px"
           size="small"
           class="demo-search"
           @submit.prevent="">
    <el-row>
      <el-col  :md="9" :sm="12" :xs="24">
        <el-form-item label="代码">
          <el-input clearable
                    v-model.trim="form.code"
                    placeholder="请输入"/>
        </el-form-item>
      </el-col>
      <el-col  :md="9" :sm="12" :xs="24">
        <el-form-item label="名称">
          <el-input clearable
                    v-model.trim="form.name"
                    placeholder="请输入"/>
        </el-form-item>
      </el-col>
      <el-col  :md="6" :sm="12" :xs="24">
        <el-form-item label-width="12px">
          <el-button type="primary" size="small" plain @click="search">查询</el-button>
          <el-button @click="reset" size="small">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data';

const emit = defineEmits(['search']);

/** 表单数据 */
const [form, resetFields] = useFormData({
  code: '',
  name: '',
});

/** 搜索 */
const search = () => {
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
</script>

<style lang="scss" scoped>
.demo-search :deep(.el-form-item) {
  margin-bottom: 8px;
}
</style>
