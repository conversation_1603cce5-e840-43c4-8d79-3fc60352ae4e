<template>
    <el-row :gutter="16" justify="center">
      <el-col :span="12">
        <div class="list-title">可选角色（{{list1.length}}）</div>
        <vue-draggable
          v-model="list1"
          item-key="id"
          :animation="300"
          group="demoDragList"
          :set-data="() => void 0"
          class="demo-list"
        >
          <template #item="{ element }">
            <el-tag
              class="drag-item"
              size="large"
              :disable-transitions="true"
              effect="plain"
            >
              <span class="tag-content">{{ element.name }}</span>
            </el-tag>
          </template>
        </vue-draggable>
      </el-col>
      <el-col :span="12">
        <div class="list-title">已选角色（{{list2.length}}）</div>
        <vue-draggable
          v-model="list2"
          item-key="id"
          :animation="300"
          group="demoDragList"
          :set-data="() => void 0"
          class="demo-list selected-list"
          @update:modelValue="updateValue"
        >
          <template #item="{ element }">
            <el-tag
              class="drag-item"
              size="large"
              :disable-transitions="true"
              type="success"
              effect="light"
            >
              <span class="tag-content">{{ element.name }}</span>
              <el-icon class="tag-icon"><check /></el-icon>
            </el-tag>
          </template>
        </vue-draggable>
      </el-col>
    </el-row>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue';
import VueDraggable from 'vuedraggable';
import { ElMessage as EleMessage, ElIcon } from "element-plus";
import { Check } from '@element-plus/icons-vue';
import {listRoles} from '@/views/system/role/api/index';

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  /** 选中的角色 */
  modelValue: Array,
});

/** 角色数据 */
const baseData = ref([]);
const list1 = ref([]);
const list2 = ref([]);

const queryListRoles = () => {
  /** 获取角色数据 */
  listRoles().then((list) => {
    baseData.value = list;
    list1.value = list;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 更新选中数据 */
const updateValue = (value) => {
  emit('update:modelValue', list2.value);
};

watch(() => props.modelValue, (data) => {
  nextTick(() => {
    if (data) {
      list2.value = data;
      list1.value = baseData.value.filter(item => !list2.value.some(ele => ele.id === item.id));
    } else {
      list1.value = baseData.value;
    }
  });
}, { immediate: true });

queryListRoles();
</script>

<style scoped lang="scss">
.drag-container {
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.list-title {
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  text-align: left;

  &::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 14px;
    background-color: var(--el-color-primary);
    margin-right: 8px;
    vertical-align: middle;
  }
}

.demo-list {
  height: 366px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }
}

.selected-list {
  border-color: var(--el-color-success-light-5);
}

.drag-item {
  width: 100%;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: move;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.tag-content {
  flex: 1;
  text-align: center;
  font-size: 14px;
}

.tag-icon {
  margin-left: 8px;
  font-size: 14px;
}

/* 拖拽样式优化 */
:deep(.sortable-ghost) {
  opacity: 0.7;
  background-color: var(--el-color-primary-light-9);
}

:deep(.sortable-chosen) {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
