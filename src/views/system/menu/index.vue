<template>
  <ele-page hide-footer flex-table>
    <menu-search @search="searchReload"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <ele-pro-table sticky
                     ref="tableRef"
                     row-key="menuKey"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     :lazy="true"
                     :load="tableLoad"
                     highlight-current-row
                     @done="handleDone"
                     :footer-style="{ paddingBottom: '5px'}"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit(null,'add')">
            新建
          </el-button>
        </template>
        <template #title="{ row }">
          <el-icon v-if="row.icon"
                   :size="15"
                   style="margin-right: 8px; vertical-align: -2px">
            <component :is="row.icon"/>
          </el-icon>
          <span>{{ row.title }}</span>
        </template>
        <template #menuType="{ row }">
          <el-tag v-if="isExternalLink(row.path)"
                  size="small"
                  type="danger"
                  :disable-transitions="true">
            外链
          </el-tag>
          <el-tag v-else-if="isExternalLink(row.component)"
                  size="small"
                  type="warning"
                  :disable-transitions="true">
            内链
          </el-tag>
          <el-tag v-else-if="row.menuType === 0"
                  size="small"
                  :disable-transitions="true">
            目录
          </el-tag>
          <el-tag v-else-if="row.menuType === 1"
                  size="small"
                  type="success"
                  :disable-transitions="true">
            菜单
          </el-tag>
          <el-tag v-else-if="row.menuType === 2"
                  size="small"
                  type="info"
                  :disable-transitions="true">
            按钮
          </el-tag>
        </template>
        <template #action="{ row }">
          <el-link v-if="row.hasChildren" type="primary"
                   underline="never"
                   @click="openEdit(row,'add')">
            添加
          </el-link>
          <el-divider v-if="row.hasChildren" direction="vertical"/>
          <el-link type="primary" underline="never" @click="openEdit(row,'edit')">
            修改
          </el-link>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="remove(row)">
            删除
          </el-link>
          <template v-if="row.hasChildren">
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never"
                     @click="reloadChild(row, $event)">
              刷新子级
            </el-link>
          </template>
          <template v-if="row.hasChildren&&row.menuType !== 0">
            <el-divider direction="vertical"/>
            <el-link type="primary"
                     underline="never"
                     @click="reloadParent(row)">
              刷新父级
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <menu-edit v-model="showEdit" :data="current" :parentData="currentClick" :parent-id="parentId" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {ref, unref, nextTick} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from 'element-plus/es';
import {isExternalLink,} from 'ele-admin-plus/es';
import {
  PlusOutlined,
} from '@/components/icons';
import MenuSearch from './components/menu-search.vue';
import MenuEdit from './components/menu-edit.vue';
import {listMenus, removeMenu, pageMenus} from '@/api/system/menu';
import {useUserStore} from "@/store/modules/user.js";
import {useRouter} from "vue-router";
import {insertAtIndex} from '@/utils/common_bak2.js';
import dayjs from 'dayjs';
import {ElLoading} from "element-plus";


const userStore = useUserStore();
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let routeType = path.split("/")[3];

/** 表格实例 */
const tableRef = ref(null);
const columns = ref([]);

/** 表格列配置 */
const baseColumns = ref([
  {
    prop: 'title',
    label: '菜单名称',
    slot: 'title',
  },
  {
    prop: 'sortNumber',
    label: '排序',
    sortable: 'custom',
  },
  {
    prop: 'hide',
    label: '可见',
    formatter: (row) => ['是', '否'][row.hide]
  },
  {
    prop: 'menuType',
    label: '类型',
    slot: 'menuType'
  },
  {
    columnKey: 'action',
    label: '操作',
    slot: 'action',
    width: 300,
  }
]);

columns.value = baseColumns.value
if (routeType === 'btn') {
  insertAtIndex(columns.value, {
    prop: 'authority',
    label: '权限标识',
  }, 1);
} else {
  insertAtIndex(columns.value,
    {
      prop: 'path',
      label: '路由地址',
    }, 1);
}
/** 当前编辑数据 */
const current = ref(null);
const currentClick = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 上级菜单id */
const parentId = ref();

/** 表格数据源 */
const datasource = async ({page, limit, where, orders, parent}) => {
  //默认排序
  orders = Object.keys(orders).length > 0 ? orders : {
    sort: 'sortNumber',
    order: 'asc'
  }
  const now = Date.now();
  if (parent) {
    const data = await listMenus({
      ...where, ...orders,
      parentId: parent?.menuId || 0,
      menuType: routeType === 'btn' ? 2 : '',
    });
    parent._oldChildrenLength = parent.children?.length || 0;
    return data.map((d) => {
      return {
        ...d,
        updateTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss.SSS'),
        menuKey: d.menuId + '-' + now,
        hasChildren: d?.menuType === 2 ? false : true,
        _parentTableResolve: parent._tableResolve,
        parent
      };
    });


    // const data = await listMenus({
    //   ...where, ...orders,
    //   parentId: parent?.menuId || 0,
    //   menuType: routeType === 'btn' ? 2 : '',
    // });
    // // 为子级数据记录父级的 ElTable 懒加载的 resolve 方法
    // if (parent) {
    //   data.forEach((d) => {
    //     d.hasChildren = d?.menuType === 2 ? false : true;
    //     if (parent && parent._tableResolve) {
    //       d._parentTableResolve = parent._tableResolve;
    //     }
    //   });
    // }
    // return data;
  } else {
    let obj = {
      ...where, page, limit, ...orders,
    }
    obj.menuType = parent?.menuId || 0;
    obj.parentId = parent?.parentId || routeType === 'btn' ? '11111111111111111111111111111111' : '00000000000000000000000000000000'
    // return pageMenus(obj);

    const res = await pageMenus(obj);
    return {
      count: res?.count,
      list: res?.list?.map?.((d) => {
        return {
          ...d,
          hasChildren: d?.menuType === 2 ? false : true,
          updateTime: dayjs(now).format('YYYY-MM-DD HH:mm:ss.SSS'),
          menuKey: d.menuId + '-' + now
        };
      })
    };
  }
};

/** 表格数据加载完成事件 */
const handleDone = ({response}, parent) => {
  // 解决懒加载下子级无法清空
  if (parent && !response.length && parent._oldChildrenLength) {
    nextTick(() => {
      parent.hasChildren = parent?.menuType !== 2;
      parent.menuKey = parent.menuId + '-' + Date.now();
    });
  }
};

/** 刷新节点的子级数据 */
const reloadChild = (row, e) => {
  if (row._tableResolve) {
    tableRef.value?.reload?.(void 0, row, row._tableResolve);
    return;
  } else {

  }
  const target = e?.currentTarget;
  const $tr = target?.parentElement?.parentElement?.parentElement;
  const $icon = $tr?.querySelector?.('.el-table__expand-icon');
  $icon && $icon.click();
};

/** 刷新父级节点的子级数据 */
const reloadParent = (row) => {
  if (row._parentTableResolve) {
    const parent = {
      _tableResolve: row._parentTableResolve,
      menuId: row.parentId
    };
    tableRef.value?.reload?.(void 0, parent, row._parentTableResolve);
  }
};


/** 重写树表格懒加载方法 */
const tableLoad = (row, treeNode, resolve) => {
  // 记录 ElTable 懒加载的 resolve 方法, 刷新时需要这个方法来更新子级的数据W
  row._tableResolve = resolve;
  tableRef.value?.reload(void 0, row, resolve);
};

/** 刷新表格 */
const reload = (row) => {
  if (row) {
    nextTick(() => {
      if (row.menuType === 0) {
        if (row.isUpdate && ['11111111111111111111111111111111', '00000000000000000000000000000000'].includes(row.parentId)) {
          searchReload()
        } else {
          reloadChild(row);
        }
      } else {
        reloadParent(row);
      }
    })
  } else {
    searchReload()
  }
};
/** 查询刷新*/
const searchReload = (where) => {
  tableRef.value?.reload?.({where});
};

/** 打开编辑弹窗 */
const openEdit = (row, type) => {
  if (type === 'add') {
    current.value = null;//添加data全部设置为空
    parentId.value = row ? row.menuId : routeType === 'btn' ? '11111111111111111111111111111111' : '00000000000000000000000000000000';//行内添加菜单，把父级菜单ID带上
  } else {
    current.value = row;
    parentId.value = row.parentId;
  }
  currentClick.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  if (row.children?.length) {
    EleMessage.error('请先删除子节点');
    return;
  }
  ElMessageBox.confirm('确定要删除“' + row.title + '”吗?', '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removeMenu(row.menuId).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      if (row.menuType === 0) {
        searchReload();
      } else if (row.menuType === 1) {
        reloadParent(row)
      } else if (row.menuType === 2) {
        reloadParent(row)
      }
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
import * as MenuIcons from '@/layout/menu-icons';

export default {
  name: 'SystemMenu',
  components: MenuIcons
};
</script>
