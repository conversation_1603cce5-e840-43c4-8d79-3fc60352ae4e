<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref} from 'vue';
import {queryQgzxJobTypePage, removes} from './api/index.js';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import {ElMessageBox} from 'element-plus';
import Edit from './components/edit.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {comColumns} from './utils/index.js';

const {removePageTab, getRouteTabKey, setPageTab, routeTabKey} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

/** 表格实例 */
const tableRef = ref(null);

/** 加载状态 */
const loading = ref(false);

/** 是否显示表单弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);
/** 打开编辑弹窗 */
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true},
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)',
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 是否显示导入弹窗 */
const showImport = ref(false);
/** 表格列配置 */
const columns = ref([]);
columns.value = comColumns();

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value,
    });
  } else {
    reload(lastWhere);
  }
};
/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryQgzxJobTypePage({...where, ...orders, ...filters, page, limit});
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

</script>

<script>
export default {
  name: 'JOBTYLEINDEX',
};
</script>
