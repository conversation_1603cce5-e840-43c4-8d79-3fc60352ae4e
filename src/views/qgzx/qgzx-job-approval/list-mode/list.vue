<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <ele-tabs type="indicator"
                    size="small"
                    v-model="activeName"
                    :items="tabsItems">
            <template #label="{ item, label}">
              <span>{{ label }}</span>
            </template>
          </ele-tabs>
        </template>
        <template #jobName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref, watch} from 'vue';
import {queryQgzxJobApprovalPage, removes, getNodeState} from './api/index.js';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import {ElMessageBox} from 'element-plus';
import Edit from './components/edit.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {comColumns} from './utils/index.js';
import {comApproveStatus, insertAtIndex} from '@/utils/common_bak2.js';

const {removePageTab, getRouteTabKey, setPageTab, routeTabKey} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);

/** 表格实例 */
const tableRef = ref(null);

/** 加载状态 */
const loading = ref(false);

/** 是否显示表单弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);
/** 打开编辑弹窗 */
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true},
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)',
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 标签页选中 */
const activeName = ref(null);
const tabsItems = ref([]);

let checkedNode = ref(null);
let checkedNodeId = ref(null);

let backNodeId = query?.currentNodeId ?? null;
checkedNodeId.value = backNodeId;
activeName.value = query?.currentResult ?? null;
const dialogFormVisible = ref(false);
let activeNameBack = '';

/** 查询tabs */
const nodeStateArray = ref();
const queryNodeState = () => {
  getNodeState({moduleCode: 'qgzx_gwsb'}).then((data) => {
    if (data) {
      data.forEach(item => {
        item.value = item.nodeId;
      });
      nodeStateArray.value = data ?? [];
      checkedNodeId.value = backNodeId || query?.currentNodeId || data[0]?.nodeId;
      const resultToUse = query?.currentResult || backNodeId;

      if (data.length > 1) {
        if (checkedNodeId.value) {
          checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
          handleDialogSubmit(resultToUse);
        } else {
          dialogFormVisible.value = true;
        }
      } else {
        checkedNodeId.value = data[0].nodeId;
        handleDialogSubmit(resultToUse);
      }
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

/** 搜索 */
const reloadData = (where) => {
  lastWhere.value = where ? where : {};
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

const handleDialogSubmit = (presetResult = null) => {
  if (!checkedNodeId.value) {
    EleMessage.error('请选择你要审核的节点信息');
    return false;
  }

  checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
  let baseItmes = comApproveStatus();
  baseItmes.forEach(item => {
    item.label = checkedNode.value[item.diyname];
    item.name = checkedNode.value[item.prename];
  });
  insertAtIndex(baseItmes, {
    label: '待审批',
    name: '待审批',
  }, 0);

  activeName.value = presetResult || activeNameBack || baseItmes[0].name;
  tabsItems.value = baseItmes;
  dialogFormVisible.value = false;

  reloadData();
};

watch(
  activeName,
  (newVal) => {
    if (newVal) {
      reloadData();
    }
  },
  {immediate: false},
);

/** 是否显示导入弹窗 */
const showImport = ref(false);
/** 表格列配置 */
const columns = ref([]);
columns.value = comColumns();

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value,
    });
  } else {
    reload(lastWhere);
  }
};
/** 列表选中数据 */
const selections = ref([]);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  if (!activeName.value) {
    return Promise.resolve({
      data: [],
      total: 0,
    });
  }
  return queryQgzxJobApprovalPage({
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    spzt: activeName.value,
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

queryNodeState();
</script>

<script>
export default {
  name: 'KSBINDEX',
};
</script>
