<template>
  <ele-page hide-footer>
    <ele-card :body-style="{ padding: '16px', overflow: 'hidden' }">
      <!-- 顶部标签栏 -->
      <template #header>
        <ele-tabs
          type="indicator"
          size="small"
          v-model="activeName"
          :items="tabsItems">
          <template #label="{ item, label }">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>

      <!-- 流体式内容区域 -->
      <div class="fluid-container">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated/>
        </div>

        <template v-else>
          <div v-if="dataList.length === 0" class="empty-container">
            <el-empty description="暂无数据"/>
          </div>

          <div v-else class="card-list">
            <div v-for="item in dataList" :key="item.id" class="card-item">
              <div class="card-header">
                <el-link
                  type="primary"
                  underline="never"
                  @click="openEdit(item)">
                  {{ item.jobName }}
                </el-link>
              </div>
              <div class="card-content">
                <!-- 这里可以添加其他字段的展示 -->
                <div class="content-row">
                  <span class="label">用工单位:</span>
                  <span>{{ item.employer?.name }}</span>
                </div>
                <div class="content-row">
                  <span class="label">申请时间:</span>
                  <span>{{ item.createTime }}</span>
                </div>
                <!-- 更多字段... -->
              </div>
              <div class="card-footer">
                <el-tag >
                  {{ item.status }}aaa
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 分页控件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              layout="total, prev, pager, next"
              @current-change="handlePageChange"
            />
          </div>
        </template>
      </div>
    </ele-card>

    <edit v-model="showEdit" :data="editData" @done="reloadData"/>
  </ele-page>
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import {queryQgzxJobApprovalPage, getNodeState} from './api/index.js';
import {useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import Edit from './components/edit.vue';
import {comApproveStatus, insertAtIndex} from '@/utils/common_bak2.js';

const router = useRouter();
const {query} = router.currentRoute.value;

// 数据相关
const dataList = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 标签页相关
const activeName = ref(null);
const tabsItems = ref([]);
const nodeStateArray = ref([]);
const checkedNodeId = ref(query?.currentNodeId ?? null);

// 编辑相关
const showEdit = ref(false);
const editData = ref(null);

// 初始化加载
onMounted(() => {
  queryNodeState();
});

// 查询节点状态
const queryNodeState = async () => {
  try {
    const data = await getNodeState({moduleCode: 'qgzx_gwsb'});
    if (data) {
      nodeStateArray.value = data.map(item => ({
        ...item,
        value: item.nodeId,
      }));

      checkedNodeId.value = query?.currentNodeId || data[0]?.nodeId;
      const resultToUse = query?.currentResult;

      handleDialogSubmit(resultToUse);
    }
  } catch (e) {
    ElMessage.error(e.message);
  }
};

// 处理对话框提交
const handleDialogSubmit = (presetResult = null) => {
  if (!checkedNodeId.value) {
    ElMessage.error('请选择你要审核的节点信息');
    return;
  }

  const checkedNode = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
  let baseItems = comApproveStatus();

  baseItems.forEach(item => {
    item.label = checkedNode[item.diyname];
    item.name = checkedNode[item.prename];
  });

  insertAtIndex(baseItems, {
    label: '待审批',
    name: '待审批',
  }, 0);

  activeName.value = presetResult || baseItems[0].name;
  tabsItems.value = baseItems;

  // 初始化加载数据
  fetchData();
};

// 获取数据
const fetchData = async () => {
  if (!activeName.value) return;

  try {
    loading.value = true;
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      spzt: activeName.value,
    };

    const res = await queryQgzxJobApprovalPage(params);
    console.log(res);
    dataList.value = res.list || [];
    total.value = res.count || 0;
  } catch (e) {
    ElMessage.error(e.message);
  } finally {
    loading.value = false;
  }
};

// 分页变化
const handlePageChange = () => {
  fetchData();
};

// 打开编辑
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

// 重新加载数据
const reloadData = () => {
  currentPage.value = 1;
  fetchData();
};


watch(
  activeName,
  (newVal) => {
    if (newVal) {
      reloadData();
    }
  },
  {immediate: false},
);
</script>

<style scoped>

.fluid-container {
  width: 100%;
}

.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 0;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.card-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  transition: all 0.3s;
}

.card-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
}

.card-content {
  margin-bottom: 12px;
}

.content-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.content-row .label {
  color: #909399;
  margin-right: 8px;
  width: 80px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>

<script>
export default {
  name: 'KSBINDEX',
};
</script>
