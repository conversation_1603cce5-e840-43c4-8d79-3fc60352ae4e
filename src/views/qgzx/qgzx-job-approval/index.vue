<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <ele-tabs
          type="tag"
          size="small"
          v-model="activeName"
          :items="tabsItems">
          <template #label="{ item, label }">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <div class="view-switch">
          <el-radio-group v-model="displayMode" size="small">
            <el-radio-button label="fluid">
              <el-icon>
                <Menu/>
              </el-icon>
              卡片视图
            </el-radio-button>
            <el-radio-button label="table">
              <el-icon>
                <Grid/>
              </el-icon>
              表格视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <!-- 表格模式 -->
      <ele-pro-table v-if="displayMode === 'table'"
                     ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }">
        <template #jobName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
      <!-- 流体卡片模式 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated/>
        </div>
        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无数据"/>
          </div>
          <el-row v-else :gutter="10">
            <el-col
              v-for="item in fluidData"
              :key="item.id"
              :lg="8"
              :md="12"
              :sm="12"
              :xs="24">
              <job-card :data="item" @edit="openEdit" @detail="openEdit"/>
            </el-col>
          </el-row>
          <div v-if="fluidData.length >0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>

    <edit v-model="showEdit" :data="editData" @done="reloadData"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue';
import {Grid, Menu, PriceTag, Timer} from '@element-plus/icons-vue';
import {queryQgzxJobApprovalPage, getNodeState} from './api/index.js';
import {useRouter} from 'vue-router';
import {ElMessage} from 'element-plus';
import Edit from './components/edit.vue';
import {comColumns} from './utils/index.js';
import {comApproveStatus, insertAtIndex} from '@/utils/common_bak2.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {Wallet} from '@icon-park/vue-next';
import _ from 'lodash';
import IconPark from '@/components/IconPark/index.vue';
import JobCard from '@/views/qgzx/components/JobCard.vue';

const router = useRouter();
const {query} = router.currentRoute.value;
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

// 显示模式
const displayMode = ref('fluid'); // 'table' 或 'fluid'

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 表格相关
const tableRef = ref(null);
const selections = ref([]);
const columns = ref(comColumns());

// 标签页相关
const activeName = ref(null);
const tabsItems = ref([]);
const nodeStateArray = ref([]);
const checkedNodeId = ref(query?.currentNodeId ?? null);

// 编辑相关
const showEdit = ref(false);
const editData = ref(null);

const bodyStyle = computed(() => {
  return {
    overflow: 'hidden',
    ...(displayMode.value === 'fluid' ? {background: '#f0f2f5'} : {}),
    ...(displayMode.value === 'fluid' ? {padding: '0 0 10px !important'} : {padding: '0 10px 10px !important'}),
  };
});

// 计算属性 - 获取当前查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  spzt: activeName.value,
}));

// 初始化加载
onMounted(() => {
  queryNodeState();
});

// 查询节点状态
const queryNodeState = async () => {
  try {
    const data = await getNodeState({moduleCode: 'qgzx_gwsb'});
    if (data) {
      nodeStateArray.value = data.map(item => ({
        ...item,
        value: item.nodeId,
      }));

      checkedNodeId.value = query?.currentNodeId || data[0]?.nodeId;
      const resultToUse = query?.currentResult;

      handleDialogSubmit(resultToUse);
    }
  } catch (e) {
    ElMessage.error(e.message);
  }
};

// 处理对话框提交
const handleDialogSubmit = (presetResult = null) => {
  if (!checkedNodeId.value) {
    ElMessage.error('请选择你要审核的节点信息');
    return;
  }

  const checkedNode = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
  let baseItems = comApproveStatus();

  baseItems.forEach(item => {
    item.label = checkedNode[item.diyname];
    item.name = checkedNode[item.prename];
  });

  insertAtIndex(baseItems, {
    label: '待审批',
    name: '待审批',
  }, 0);

  activeName.value = presetResult || baseItems[0].name;
  tabsItems.value = baseItems;
};

// 获取数据（添加防抖）
const fetchData = _.debounce(async () => {
  if (!activeName.value) return;

  try {
    loading.value = true;

    const res = await queryQgzxJobApprovalPage(queryParams.value);

    if (displayMode.value === 'fluid') {
      fluidData.value = res.list || [];
    }

    total.value = res.count || 0;

  } catch (e) {
    ElMessage.error(e.message);
  } finally {
    loading.value = false;
  }
}, 300);

// 切换显示模式时重新加载数据
watch(displayMode, (newVal) => {
  currentPage.value = 1;
  if (activeName.value) {
    if (newVal === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
});

// 监听activeName变化
watch(activeName, (newVal) => {
  if (newVal) {
    currentPage.value = 1;
    if (displayMode.value === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
}, {immediate: false});

// 分页变化
const handlePageChange = () => {
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 打开编辑
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

// 重新加载数据
const reloadData = () => {
  currentPage.value = 1;
  if (displayMode.value === 'fluid') {
    fetchData();
  } else {
    tableRef.value?.reload?.();
  }
};

// 表格数据源
const datasource = ({page, limit, where, orders, filters}) => {
  if (!activeName.value) {
    return Promise.resolve({
      data: [],
      total: 0,
    });
  }

  return queryQgzxJobApprovalPage({
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    spzt: activeName.value,
  });
};

// 获取状态标签类型
const getStatusType = (status) => {
  const map = {
    '待审批': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger',
    '已撤销': 'info',
  };
  return map[status] || '';
};
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid var(--el-border-color-light);
}

.list-item-body {
  display: flex;
  padding: 10px;
}

@media screen and (max-width: 576px) {
  .list-item-body {
    display: block;
  }
}
</style>
