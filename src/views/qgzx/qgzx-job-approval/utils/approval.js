export function comColumns() {
  return [
    {
      prop: 'result',
      fieldEn: 'result',
      fieldZh: '审核状态',
      showFlag: '是',
      required: '是',
      span: 8,
      controlType: 'select',
      loadDataType: 'nodeStateJob',
      fieldLinks: [
        {
          fieldEn: 'result',
          fieldVal: '通过',
          showFlag: '否',
          linkField: 'returnWay',
        },
        {
          fieldEn: 'result',
          fieldVal: '不通过',
          showFlag: '否',
          linkField: 'returnWay',
        },
        {
          fieldEn: 'result',
          fieldVal: '退回',
          showFlag: '是',
          linkField: 'returnWay',
        },
      ],
    },
    {
      prop: 'returnWay',
      fieldEn: 'returnWay',
      fieldZh: '退回规则',
      controlType: 'select',
      options: [
        {
          label: '退回到上一节点',
          value: 'PRE_NODE',
        }, {
          label: '退回到申请人',
          value: 'APPLICANT',
        }],
      showFlag: '否',
      required: '是',
      span: 8,
    },
  ];
}
