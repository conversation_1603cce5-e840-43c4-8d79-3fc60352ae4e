<template>
  <ele-drawer size="76%"
              style="max-width: 100%"
              v-model="drawerVisible"
              :body-style="{ padding: '8px!important' ,background:'#f0f2f5',height: (pageHeight+98)+'px', overflow:'auto'}"
              :modal-append-to-body="true"
              :append-to-body="true"
              :close-on-click-modal="false"
              class="modern-job-drawer"
              :show-close="false">
    <!-- 自定义抽屉头部 - 保持不变 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-content">
          <div class="header-left">
            <div class="header-title">{{ title }} {{ filteredList.length }}人
            </div>
            <div class="modern-header-line"></div>
          </div>
          <div class="header-right">
            <el-button type="danger" @click="updateModelValue(false)">
              <el-icon class="el-icon--left">
                <CircleCloseFilled/>
              </el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </template>

    <!-- 筛选栏 - 保持不变 -->
    <div class="filter-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索姓名/学号"
            clearable
            class="modern-input"
            @keyup.enter="fetchData"
          >
            <template #prefix>
              <el-icon class="input-icon">
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="statusFilter"
            placeholder="面试状态"
            clearable
            class="modern-select"
          >
            <el-option label="待面试" value="待面试"></el-option>
            <el-option label="已通过" value="已通过"></el-option>
            <el-option label="未通过" value="未通过"></el-option>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="jobFilter"
            placeholder="岗位筛选"
            clearable
            class="modern-select"
          >
            <el-option
              v-for="job in uniqueJobs"
              :key="job"
              :label="job"
              :value="job"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="6" class="action-buttons">
          <el-button
            type="primary"
            @click="fetchData"
            class="search-btn"
            :icon="Search"
          >
            搜索
          </el-button>
          <el-button
            @click="resetFilters"
            class="reset-btn"
            :icon="Refresh"
          >
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容 -->
    <div class="modern-layout">
      <!-- 加载状态 - 保持不变 -->
      <div v-if="loading" class="loading-container">
        <div class="skeleton-grid">
          <div v-for="i in 6" :key="i" class="skeleton-card">
            <el-skeleton :rows="3" animated :loading="loading"/>
          </div>
        </div>
      </div>

      <!-- 空状态 - 保持不变 -->
      <div v-else-if="filteredList.length === 0" class="empty-container">
        <el-empty description="暂无面试数据">
          <template #image>
            <div class="empty-illustration">
              <svg width="200" height="150" viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M50 50H150V100H50V50Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
                <circle cx="75" cy="75" r="15" fill="#E4E7ED"/>
                <rect x="100" y="65" width="40" height="20" rx="2" fill="#E4E7ED"/>
                <path d="M30 120H170V130H30V120Z" fill="#F5F7FA" stroke="#E4E7ED" stroke-width="2"/>
              </svg>
            </div>
          </template>
          <el-button type="primary" @click="resetFilters">重新加载</el-button>
        </el-empty>
      </div>

      <!-- 数据展示 - 主要修改部分 -->
      <template v-else>
        <div class="card-grid">
          <div
            v-for="item in paginatedData"
            :key="item.id"
            class="tech-card"
            @click="openDetail(item)"
            :class="[getStatusClass(item.interviewResult)]"
          >
            <!-- 学生信息卡片 - 顶部显示 -->
            <div class="student-card">
              <div class="avatar-container">
                <template v-if="item.userInfo">
                  <el-avatar
                    v-if="item.userInfo.photo"
                    :size="60"
                    class="user-avatar"
                    :src="item.userInfo.xgh ? `/api/personInfo/${item.userInfo.xgh}/photo?access_token=${accessToken}` : ''">
                    {{ item.userInfo?.xm?.charAt(0) || '' }}
                  </el-avatar>
                  <template v-else>
                    <el-avatar
                      v-if="item.userInfo.xb === '男'"
                      :size="60"
                      src="/male.png"
                      :alt="item.userInfo.xb"
                      class="user-avatar"/>
                    <el-avatar
                      v-else-if="item.userInfo.xb === '女'"
                      :size="60"
                      src="/female.png"
                      :alt="item.userInfo.xb"
                      class="user-avatar"/>
                  </template>
                </template>
                <div class="status-badge" :class="getStatusClass(item.interviewResult)">
                  {{ item.interviewResult || '待安排' }}
                </div>
              </div>

              <div class="student-info">
                <h3 class="student-name">
                  {{ item.userInfo.xm || '--' }}
                  <span class="student-id">{{ item.userInfo.xgh || '--' }}</span>
                </h3>

                <div class="contact-info">
                  <div class="contact-item">
                    <el-icon>
                      <Phone/>
                    </el-icon>
                    <span>{{ item.userInfo.sjh || '未提供' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 面试信息 - 独立行显示 -->
            <div class="info-section interview-info">
              <div class="section-title">
                <el-icon>
                  <Clock/>
                </el-icon>
                <span>面试信息</span>
              </div>
              <div class="info-content">
                <div class="info-row">
                  <span class="info-label">时间：</span>
                  <span class="info-value">{{
                      item.interviewTime ? formatDateTime(item.interviewTime) : '待安排'
                    }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">地点：</span>
                  <span class="info-value">{{ item.interviewLocation || '待安排' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">申请时间：</span>
                  <span class="info-value">{{ formatDate(item.createTime) }}</span>
                </div>
              </div>
            </div>

            <!-- 岗位信息 - 独立行显示 -->
            <div class="info-section job-info">
              <div class="section-title">
                <el-icon>
                  <Briefcase/>
                </el-icon>
                <span>岗位信息</span>
              </div>
              <div class="info-content">
                <div class="info-row">
                  <span class="info-label">岗位：</span>
                  <span class="info-value">{{ item.jobName || '未知岗位' }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">部门：</span>
                  <span class="info-value">{{ item.employerName || '未知部门' }}</span>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button size="small"
                         type="primary"
                         plain
                         @click.stop="handlePass(item)"
                         v-if="item.interviewResult === '待面试'">
                面试结果设置
              </el-button>
              <el-button size="small"
                         type="danger"
                         plain
                         @click.stop="handleReject(item)"
                         v-if="item.interviewResult === '待面试'">
                缺席
              </el-button>
              <el-button size="small"
                         type=""
                         plain
                         @click.stop="openDetail(item)">
                详情
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="filteredList.length > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="filteredList.length"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            background
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </template>
    </div>
    <InterViewDetailDrawer v-model="detailVisible" :data="detailData"/>
  </ele-drawer>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {queryQgzxInterviewRecordEmployerPage} from '@/views/qgzx/interview-record/api/index.js';
import {ElMessage, ElMessageBox} from 'element-plus';
import {
  Search,
  Refresh,
  CloseBold,
  Iphone,
  Calendar,
  Briefcase,
  OfficeBuilding,
  Location,
  Clock,
  LocationFilled,
  Message,
} from '@element-plus/icons-vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {operation} from '@/views/qgzx/qgzx-student-approval/api/index.js';
import InterViewDetailDrawer from '@/views/qgzx/interview-record/components/InterViewDetailDrawer.vue';
import {getToken} from '@/utils/token-util.js';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  formModel: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: '面试名单',
  },
  jobId: {
    type: String,
  },
  enterType: {
    type: String,
    default: 'apply',
  },
});

const emit = defineEmits(['update:modelValue', 'apply']);
const drawerVisible = ref(props.modelValue);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const accessToken = getToken();

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const statusFilter = ref('');
const jobFilter = ref('');

const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  jobId: props.jobId,
}));

const uniqueJobs = computed(() => {
  const jobs = new Set();
  fluidData.value.forEach(item => {
    if (item.jobName) jobs.add(item.jobName);
  });
  return Array.from(jobs);
});

const filteredList = computed(() => {
  return fluidData.value.filter(item => {
    const matchesSearch = searchQuery.value === '' ||
      (item.userInfo?.xm?.includes(searchQuery.value)) ||
      (item.userInfo?.xgh?.includes(searchQuery.value));

    const matchesStatus = statusFilter.value === '' ||
      (item.interviewResult === statusFilter.value);

    const matchesJob = jobFilter.value === '' ||
      (item.jobName === jobFilter.value);

    return matchesSearch && matchesStatus && matchesJob;
  });
});

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredList.value.slice(start, end);
});

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await queryQgzxInterviewRecordEmployerPage(queryParams.value);
    fluidData.value = res.list || [];
  } catch (e) {
    ElMessage.error(e.message || '获取数据失败');
  } finally {
    loading.value = false;
  }
};

const resetFilters = () => {
  searchQuery.value = '';
  statusFilter.value = '';
  jobFilter.value = '';
  currentPage.value = 1;
  fetchData();
};

const handlePageChange = (page) => {
  currentPage.value = page;
  window.scrollTo({top: 0, behavior: 'smooth'});
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

const formatDate = (dateString) => {
  if (!dateString) return '--';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'});
};

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '--';
  const date = new Date(dateTimeString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }).replace(/\//g, '-');
};

const getStatusClass = (status) => {
  switch (status) {
    case '待面试':
      return 'status-pending';
    case '通过':
      return 'status-passed';
    case '不通过':
      return 'status-failed';
    default:
      return 'status-quexi';
  }
};
const detailVisible = ref(false);
const detailData = ref(null);
const openDetail = (item) => {
  detailData.value = item;
  detailVisible.value = true;
};

const handlePass = (item) => {
  if (item.interviewResult === '待面试') {
    ElMessageBox.prompt(`确定要通过 ${item.userInfo.xm} 的面试吗? 请输入备注`, `面试结果设置`, {
      confirmButtonText: '通过',
      cancelButtonText: '不通过',
      inputPattern: /.{5,}/,
      inputErrorMessage: '至少输入5个字符',
    }).then(({value}) => {
      let data = {
        id: item.id,
        interviewResult: '通过',
        interviewComment: value,
      };
      operation(data).then(() => {
        ElMessage.success('面试结果设置成功');
        fetchData(); // 操作成功后刷新数据
      }).catch(error => {
        ElMessage.error('操作失败: ' + (error.message || '未知错误'));
      });
    }).catch(() => {
      ElMessageBox.confirm(`确定要拒绝 ${item.userInfo.xm} 的申请吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let data = {
          id: item.id,
          interviewResult: '不通过',
        };
        operation(data).then(() => {
          ElMessage.success('申请已拒绝');
          fetchData(); // 操作成功后刷新数据
        }).catch(error => {
          ElMessage.error('操作失败: ' + (error.message || '未知错误'));
        });
      });
    });
  }
};

const handleReject = (item) => {
  ElMessageBox.confirm(
    `你确定要把 ${item.userInfo.xm} 的面试标记为缺席吗?`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    let data = {
      id: item.id,
      interviewResult: '缺席',
    };
    operation(data).then(() => {
      ElMessage.success('申请已标记为缺席');
      fetchData(); // 操作成功后刷新数据
    }).catch(error => {
      ElMessage.error('操作失败: ' + (error.message || '未知错误'));
    });
  }).catch(() => {});
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(() => props.modelValue, (val) => {
  drawerVisible.value = val;
  if (val) {
    fetchData();
  }
});

watch(() => props.jobId, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    fetchData();
  }
}, {immediate: true});

defineExpose({
  getFormModel: () => props.formModel,
});
</script>

<style lang="scss" scoped>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}


.modern-job-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 24px;
    background: linear-gradient(135deg, #f6f8fc, #f0f4f9);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  }

  .drawer-header {
    width: 100%;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .header-left {
        flex: 1;
        position: relative;

        .header-title {
          font-size: 0.8rem;
          font-weight: 500;
          margin: 0 0 0.5rem 0;
          color: #2c3e50;
          position: relative;
        }

        .modern-header-line {
          height: 3px;
          width: 80px;
          background: linear-gradient(90deg, var(--el-color-primary), rgba(var(--el-color-primary-rgb), 0.3));
          border-radius: 3px;
        }
      }

      .header-right {
        margin-left: 20px;

        .close-btn {
          border-radius: 6px;
          transition: all 0.3s ease;
          font-weight: 500;
          border: 1px solid rgba(245, 108, 108, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
            background: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }

  .filter-bar {
    background: white;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    margin-bottom: 10px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    }

    .modern-input {
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #e2e8f0 inset;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #cbd5e1 inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #4361ee inset;
        }
      }

      .input-icon {
        color: #64748b;
      }
    }

    .modern-select {
      :deep(.el-input__wrapper) {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #e2e8f0 inset;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #cbd5e1 inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #4361ee inset;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;

      .search-btn, .reset-btn {
        transition: all 0.3s ease;
        border-radius: 8px;
        font-weight: 500;

        &:hover {
          transform: translateY(-2px);
        }
      }

      .search-btn:hover {
        box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
      }

      .reset-btn:hover {
        box-shadow: 0 4px 12px rgba(100, 116, 139, 0.2);
      }
    }
  }

  .loading-container {
    padding: 16px;

    .skeleton-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 10px;

      .skeleton-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      }
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .empty-illustration {
      svg {
        path, circle, rect {
          transition: all 0.3s ease;
        }

        &:hover {
          path {
            fill: #f0f4f9;
            stroke: #cbd5e1;
          }

          circle {
            fill: #e2e8f0;
          }

          rect {
            fill: #e2e8f0;
          }
        }
      }
    }
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    animation: fadeIn 0.5s ease-out;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }
  }

  .tech-card {
    cursor: pointer;
    background: white;
    border-radius: 14px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;
    overflow: hidden;
    min-height: 380px; /* 固定卡片高度 */

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
      border-color: rgba(67, 97, 238, 0.2);
    }

    .student-card {
      display: flex;
      gap: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #e2e8f0;

      .avatar-container {
        position: relative;
        flex-shrink: 0;

        .user-avatar {
          border: 2px solid white;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .student-info {
        flex: 1;
        min-width: 0; /* 防止内容溢出 */

        .student-name {
          font-size: 1.2rem;
          margin: 0 0 4px 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          .student-id {
            font-size: 0.85rem;
            color: #64748b;
            margin-left: 8px;
          }
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 6px;
          margin-top: 8px;

          .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85rem;
            color: #475569;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .el-icon {
              color: #64748b;
              flex-shrink: 0;
            }
          }
        }
      }
    }

    .info-section {
      background: rgba(241, 245, 249, 0.3);
      border-radius: 8px;
      padding: 12px;

      &.interview-info {
        margin-top: auto; /* 使这部分靠上 */
      }

      &.job-info {
        margin-bottom: auto; /* 使这部分靠下 */
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-color-primary);
        margin-bottom: 8px;
        font-size: 0.65rem;

        .el-icon {
          font-size: 0.6rem;
        }
      }

      .info-content {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .info-row {
          display: flex;
          font-size: 0.6rem;

          .info-label {
            color: #64748b;
            min-width: 60px;
          }

          .info-value {
            color: #1e293b;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;
      margin-top: auto; /* 使按钮始终在底部 */
      padding-top: 12px;
      border-top: 1px dashed #e2e8f0;

      .el-button {
        flex: 1;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .loading-container {
    padding: 16px;

    .skeleton-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 10px;

      .skeleton-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      }
    }
  }

  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    .empty-illustration {
      svg {
        path, circle, rect {
          transition: all 0.3s ease;
        }

        &:hover {
          path {
            fill: #f0f4f9;
            stroke: #cbd5e1;
          }

          circle {
            fill: #e2e8f0;
          }

          rect {
            fill: #e2e8f0;
          }
        }
      }
    }
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 10px;
    animation: fadeIn 0.5s ease-out;
  }

  .tech-card {
    background: white;
    border-radius: 14px;
    padding: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
      border-color: rgba(67, 97, 238, 0.2);

      .student-name {
        color: #4361ee;
      }
    }

    &.status-pending {
      border-left: 4px solid #f59e0b;
    }

    &.status-passed {
      border-left: 4px solid #10b981;
    }

    &.status-failed {
      border-left: 4px solid rgba(241, 99, 99, 0.68);
    }

    &.status-quexi {
      border-left: 4px solid #ef4444;
    }

    .student-card {
      display: flex;
      gap: 10px;

      .avatar-container {
        position: relative;

        .user-avatar {
          border: 2px solid white;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          background: linear-gradient(135deg, #4361ee, #3a0ca3);
          color: white;
          font-weight: bold;

          &:hover {
            transform: scale(1.05);
          }
        }

        .status-badge {
          position: absolute;
          bottom: -5px;
          right: -5px;
          padding: 4px 10px;
          border-radius: 10px;
          font-size: 0.5rem;
          font-weight: 400;
          color: white;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

          &.status-pending {
            background: linear-gradient(135deg, #f59e0b, #d97706);
          }

          &.status-passed {
            background: linear-gradient(135deg, #10b981, #059669);
          }

          &.status-failed {
            background: linear-gradient(135deg, #ef4444, #dc2626);
          }
        }
      }

      .student-info {
        flex: 1;

        .student-name {
          font-size: 0.7rem;
          font-weight: 500;
          margin: 0 0 4px 0;
          color: #1e293b;
          transition: all 0.3s ease;

          .student-id {
            font-size: 0.65rem;
            color: #64748b;
            font-weight: normal;
            margin-left: 8px;
          }
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 6px;
          margin: 10px 0;

          .contact-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.65rem;
            color: #475569;

            .el-icon {
              color: #64748b;
              font-size: 0.65rem;
            }
          }
        }

        .apply-time {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 0.7rem;
          color: #64748b;
          margin-top: 8px;

          .el-icon {
            font-size: 0.9rem;
          }
        }
      }
    }

    .interview-meta, .job-meta {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      padding: 16px;
      background: rgba(241, 245, 249, 0.5);
      border-radius: 8px;

      .meta-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        .el-icon {
          color: #4361ee;
          font-size: 1.1rem;
          margin-top: 2px;
        }

        .meta-label {
          font-size: 0.75rem;
          color: #64748b;
          margin-bottom: 2px;
        }

        .meta-value {
          font-size: 0.9rem;
          font-weight: 500;
          color: #1e293b;
        }
      }
    }

    .job-meta {
      background: rgba(241, 245, 249, 0.3);

      .meta-item .el-icon {
        color: #64748b;
      }
    }

    .action-buttons {
      display: flex;
      gap: 10px;
      margin-top: 10px;

      .el-button {
        flex: 1;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

@media (max-width: 768px) {
  .modern-job-drawer {
    :deep(.el-drawer) {
      width: 100% !important;
    }

    .container-fluid {
      padding: 12px;
    }

    .filter-bar {
      .el-col {
        margin-bottom: 12px;
      }

      .action-buttons {
        flex-direction: column;
      }
    }

    .card-grid {
      grid-template-columns: 1fr;
    }

    .tech-card {
      padding: 18px;

      .interview-meta, .job-meta {
        grid-template-columns: 1fr;
      }

      .action-buttons {
        flex-direction: column;
      }
    }
  }
}

:deep(.detail-modal) {
  border-radius: 10px;

  .el-message-box__header {
    background: linear-gradient(135deg, #f6f8fc, #f0f4f9);
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .el-message-box__title {
    color: #4361ee;
    font-weight: 600;
  }

  .el-message-box__content {
    padding: 20px;
  }

  .el-message-box__btns {
    padding: 16px 20px;
    border-radius: 0 0 12px 12px;
    background: #f8fafc;
  }
}
</style>
