<template>
  <ele-drawer
    size="70%"
    title="详情"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <StudentHeader v-if="userInfo"
                   routeType="student"
                   :current-xgh="userInfo.xgh"/>

    <!-- 主要内容区域 -->
    <div class="modern-job-detail">
      <div class="modern-content">
        <!-- 学生申请基本信息卡片 -->
        <div class="modern-card modern-main-info">
          <div class="modern-card-header">
            <div class="modern-card-icon">
              <IconPark name="schedule" size="18" strokeWidth="3" theme="filled"/>
            </div>
            <h3>学生申请信息</h3>
            <div class="status-badge" :class="getStatusClass(studentApplyInfo.spzt)">
              {{ studentApplyInfo.spzt || '-' }}
            </div>
          </div>
          <div class="modern-card-body">
            <div class="modern-info-grid">
              <div class="modern-info-item">
                <label>学年学期</label>
                <div class="modern-info-value">{{ studentApplyInfo.xnxq || '-' }}</div>
              </div>
              <div class="modern-info-item">
                <label>申请时间</label>
                <div class="modern-info-value">{{ studentApplyInfo.sqsj || '-' }}</div>
              </div>
              <div class="modern-info-item">
                <label>申请岗位</label>
                <div class="modern-info-value">{{ studentApplyInfo.xxmc || '-' }}</div>
              </div>
              <div class="modern-info-item">
                <label>是否服从安排</label>
                <div class="modern-info-value">{{ studentApplyInfo.sffcap || '-' }}</div>
              </div>
            </div>
            <div class="modern-info-row">
              <div class="modern-info-item">
                <label>申请理由</label>
                <div class="modern-info-value long-text">{{ studentApplyInfo.sqly || '-' }}</div>
              </div>
            </div>
            <div class="modern-info-row">
              <div class="modern-info-item">
                <label>特长优势</label>
                <div class="modern-info-value long-text">{{ studentApplyInfo.tcys || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <JobDetailViews :form-model="qgzJobInfo"
                    enter-type="views"
                    class="drawer-content"/>
    <!-- 底部操作按钮 -->
    <!--    <template #footer>-->
    <!--      <el-button size="small" @click="updateModelValue(false)" class="cancel-btn">-->
    <!--        取消-->
    <!--      </el-button>-->
    <!--      <el-button size="small"-->
    <!--                 type="primary"-->
    <!--                 :loading="loading"-->
    <!--                 @click="save"-->
    <!--                 class="submit-btn">-->
    <!--        提交审核-->
    <!--      </el-button>-->
    <!--    </template>-->
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {
  ElMessage,
  ElCard,
  ElButton,
  ElIcon,
  ElTimeline,
  ElTimelineItem,
  ElAvatar,
} from 'element-plus';
import {ArrowRight, Briefcase} from '@element-plus/icons-vue';
import {
  operation, queryQgzxInterviewRecordApplyById,
} from '../api/index.js';
import {comColumns} from '../utils/index.js';
import {
  comApproveStatus,
  generateForm,
  toFormDataWj,
} from '@/utils/common_bak2.js';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import JobDetailDrawer from '@/views/qgzx/components/JobDetailDrawer.vue';
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';
import {queryQgzxJobApplyById} from '@/views/qgzx/qgzx-job-apply/api/index.js';
import ApprovalTimeline from '@/components/ApprovalTimeline/index.vue';
import {statusColor} from '@/utils/status-color/index.js';
import {getQqgzxStudentApprovalNodesBySqId} from '@/views/qgzx/qgzx-student-approval/api/index.js';
import JobDetailViews from '@/views/qgzx/components/JobDetailViews.vue';
import IconPark from '@/components/IconPark/index.vue';
import {getStatusClass} from '@/views/qgzx/utils/index.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const emit = defineEmits(['done', 'update:modelValue']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const props = defineProps({
  modelValue: Boolean,
  data: Object,
  enterType: String,//studentJob=学生角色，已面试
});

// 状态管理
const loading = ref(false);
const formModel = ref({});
const studentApplyInfo = ref({});
const userInfo = ref({});
const approvalData = ref(null);
const proFormGroup = ref([]);
const qgzJobInfo = ref(null);
const activities = ref([]);

const updateModelValue = (value) => {
  proFormGroup.value = null;
  emit('update:modelValue', value);
};

const onDoneGroup = (data) => {
  const existingGroup = proFormGroup.value.find(obj => obj.groupId === data.groupId);
  if (existingGroup) {
    existingGroup.values = data.values;
  } else {
    proFormGroup.value.push(data);
  }
};

// const queryQqgzxStudentApprovalNodesBySqId = async () => {
//   try {
//     const data = await getQqgzxStudentApprovalNodesBySqId(props.data.id);
//     activities.value = data.map(item => {
//       const statusObj = statusColor().find(obj => obj.label === item.approvalNode.result) || {};
//       return {
//         ...item,
//         title: item.approvalNode.nodeName,
//         name: item.approvalNode.result,
//         timestamp: item.approvalNode.updateTime,
//         size: 'large',
//         ...statusObj,
//         icon: item.reviewType === '会签' ? 'Stamp' : statusObj.icon || 'CircleCloseFilled',
//       };
//     });
//   } catch (e) {
//     ElMessage.error(e.message);
//   }
// };

// 初始化
watch(() => props.modelValue, async (modelValue) => {
  if (modelValue && props.data) {
    console.log(props.data);
    // await Promise.all([
    //   // queryNodeState(),
    //   queryQgzxInterviewRecordApplyInfo(),
    //   // queryQqgzxStudentApprovalNodesBySqId(),
    // ]);
    userInfo.value = props.data.userInfo;
    studentApplyInfo.value = props.data.studentApply;
    qgzJobInfo.value = {
      ...props.data.jobApplication,
      workDays: typeof props.data?.jobApplication.workDays === 'string'
        ? props.data.jobApplication.workDays.split(',')
        : Array.isArray(props.data?.jobApplication.workDays)
          ? props.data.jobApplication.workDays
          : [],
      Addresses: props.data?.jobApplication.jobAddresses?.[0]?.name || '-',
    };

    approvalData.value = {
      groupName: '审核信息',
      title: '',
      id: props.data.id,
      sqId: props.data.id,
      nodeId: props.data.nodeId,
      infoType: 'QgzxStudentApproval',
      workflowId: props.data.workflowId,
      listFlag: '否',
    };
  }
}, {immediate: true});
</script>

<style lang="scss" scoped>
@import "../../css/job-detail.css";

.modern-info-item {
  margin-bottom: 0.5rem;
}
</style>
