import request from '@/utils/request';

/**
 * 查询面试记录列表（权限标识：workstudy:qgzxInterviewRecord:list）
 */
export async function queryQgzxInterviewRecord(params) {
  const res = await request.get('/workstudy/qgzx-interview-record/list',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordPage(params) {
  const res = await request.get('/workstudy/qgzx-interview-record/page',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询面试记录详情（权限标识：workstudy:qgzxInterviewRecord:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordById(id) {
  const res = await request.get('/workstudy/qgzx-interview-record/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 新增或修改面试记录（权限标识：workstudy:qgzxInterviewRecord:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-interview-record/operation',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-interview-record/remove',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 用人单位分页查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordEmployerPage(params) {
  const res = await request.get(
    '/workstudy/qgzx-interview-record/employer/page',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生查询自己的面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordStudentPage(params) {
  const res = await request.get('/workstudy/qgzx-interview-record/student/page',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据学生申请ID查询面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordApplyById(id) {
  const res = await request.get(
    '/workstudy/qgzx-interview-record/by-apply/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生查询自己的面试记录（权限标识：workstudy:qgzxInterviewRecord:list）
 * @returns {Promise<*>}
 */
export async function queryQgzxInterviewRecordStudentList() {
  const res = await request.get(
    '/workstudy/qgzx-interview-record/student/list');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量创建面试记录（权限标识：workstudy:qgzxInterviewRecord:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function batchCreate(data) {
  const res = await request.post(
    '/workstudy/qgzx-interview-record/batch-create',
    data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
