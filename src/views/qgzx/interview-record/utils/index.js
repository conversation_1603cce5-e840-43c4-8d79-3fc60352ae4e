export function comColumns() {
  return [
    // Selection column for table operations
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'interviewTime',
      label: '面试时间',
      type: 'input',
      minWidth: 150,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {required: true, message: '请输入岗位名称', trigger: 'blur'},
        {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'},
      ],
    },
    {
      prop: 'interviewLocation',
      label: '面试地点',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      type: 'select',
      props: {
        code: 'employerList',
        filterable: true,
      },
      formatter: (row) => row.employer?.name || '',
      colProps: 6,
    },
    {
      prop: 'interviewerName',
      label: '面试官姓名',
      type: 'select',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      props: {
        code: 'academicTerm',
        filterable: true,
      },
      colProps: 6,
    },
    {
      prop: 'interviewerContact',
      label: '面试官联系方式',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'jobTypeData',
        dicQueryParams: {
          dictFieldUrl: '/workstudy/qgzx-job-type',
        },
      },
      colProps: 6,
    },
    {
      prop: 'interviewResult',
      label: '面试结果',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 12,
      type: 'dictSelect',
      typeKey: 'multipleSelect',
      props: {
        code: 'weekDaysFull',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
      },
    },
    {
      prop: 'interviewScore',
      label: '面试评分',
      type: 'date',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
    },
    {
      prop: 'interviewComment',
      label: '面试评价',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        rows: 4,
        maxlength: 1000,
        showWordLimit: true,
      },
    },
  ];
}
