/**
 * 平铺嵌套的表单数据
 * @param {Object} data - 原始表单数据
 * @returns {Object} - 平铺后的数据
 */
export function flattenFormData(data) {
  if (!data) return {};

  const flatData = { ...data };

  // 处理 userInfo 嵌套
  if (data.userInfo) {
    Object.entries(data.userInfo).forEach(([key, value]) => {
      flatData[key] = value;
    });
  }

  // 处理 jobApplication 嵌套
  if (data.jobApplication) {
    Object.entries(data.jobApplication).forEach(([key, value]) => {
      // 避免覆盖已有字段
      if (!flatData.hasOwnProperty(key)) {
        flatData[key] = value;
      }
    });
  }

  // 处理特殊字段
  if (data.workDays) {
    flatData.workDays = Array.isArray(data.workDays)
      ? data.workDays
      : data.workDays.split(',');
  }

  // 处理地址数据
  if (Array.isArray(data.jobAddresses) && data.jobAddresses.length > 0) {
    flatData.Addresses = data.jobAddresses[0].name;
    flatData.jobAddresses = data.jobAddresses;
  }

  return flatData;
}

/**
 * 恢复嵌套的表单数据结构
 * @param {Object} flatData - 平铺后的表单数据
 * @returns {Object} - 恢复嵌套结构的数据
 */
export function nestFormData(flatData) {
  if (!flatData) return {};

  // 提取 userInfo 相关字段
  const userInfoFields = ['xgh', 'xm', 'sjh', 'zjhm'];
  const userInfo = {};

  userInfoFields.forEach(field => {
    if (flatData.hasOwnProperty(field)) {
      userInfo[field] = flatData[field];
      delete flatData[field];
    }
  });

  // 构建最终数据结构
  return {
    ...flatData,
    userInfo,
    jobApplication: {
      jobName: flatData.jobName,
      employer: flatData.employer,
      jobTypeId: flatData.jobTypeId,
      workDays: Array.isArray(flatData.workDays)
        ? flatData.workDays.join(',')
        : flatData.workDays,
      // 其他 jobApplication 字段...
    },
    // 恢复 workDays 为字符串格式
    workDays: Array.isArray(flatData.workDays)
      ? flatData.workDays.join(',')
      : flatData.workDays
  };
}

/**
 * 处理表单项配置，适配平铺数据结构
 * @param {Array} columns - 原始表单项配置
 * @returns {Array} - 处理后的表单项配置
 */
export function processFormItems(columns) {
  return columns.map(item => {
    // 处理嵌套属性路径 (如 userInfo.xm → xm)
    if (item.prop && item.prop.includes('.')) {
      const propPath = item.prop.split('.');
      return {
        ...item,
        prop: propPath.pop(), // 取最后一级作为属性名
        originalProp: item.prop // 保留原始路径用于特殊处理
      };
    }
    return item;
  }).filter(item => !item.disabled);
}
