import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryQgzxJobApproval(params) {
  const res = await request.get('/workstudy/qgzx-student-approval', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzStudentApprovalPage(params) {
  const res = await request.get('/workstudy/qgzx-student-approval/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请信息ID查询所有审核节点，审核记录
 * @param sqId
 * @returns {Promise<*>}
 */
export async function getQqgzxStudentApprovalNodesBySqId(sqId) {
  const res = await request.get('/workstudy/qgzx-student-approval/' + sqId + '/approvalNodes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxJobApprovalById(id) {
  const res = await request.get('/workstudy/qgzx-student-approval/'+id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请信息查询当前用户审核表单字段
 * @param params
 * @returns {Promise<*>}
 */
export async function nodeFormFieldListQgzxStudent(params) {
  const res = await request.get('/workstudy/qgzx-student-approval/nodeFormFieldList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
/**
 * 根据申请记录ID或项目ID,年份查询节点自定义状态
 */
export async function getNodeState(params) {
  const res = await request.get('/workstudy/qgzx-student-approval/nodeState',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-student-approval/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-student-approval/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
