<template>
  <ele-drawer
    size="70%"
    title="岗位申请审核"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    class="modern-approval-drawer"
  >
    <StudentHeader v-if="formModel.userInfo"
                   routeType="student"
                   :current-xgh="formModel.userInfo.xgh"/>

    <!-- 学生申请基本信息卡片 -->
    <ele-card shadow="hover" class="modern-card"
              :body-style="{padding: '15px 0 2px 0!important'}">
      <template #header>
        <div class="modern-card-header">
          <h3>学生申请信息</h3>
          <template v-if="qgzJobInfo">
            <el-button
              type="primary"
              size="small"
              link
              @click="showJobDetail"
              class="detail-btn"
            >
              查看岗位详情
              <el-icon>
                <ArrowRight/>
              </el-icon>
            </el-button>
          </template>
        </div>
      </template>

      <div class="modern-card-body">
        <div class="modern-info-grid">
          <div class="modern-info-item">
            <label>学年学期</label>
            <div class="modern-info-value">{{ formModel.xnxq || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>申请时间</label>
            <div class="modern-info-value">{{ formModel.sqsj || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>申请岗位</label>
            <div class="modern-info-value">{{ formModel.xxmc || '-' }}</div>
          </div>
          <div class="modern-info-item">
            <label>是否服从安排</label>
            <div class="modern-info-value">{{ formModel.sffcap || '-' }}</div>
          </div>
        </div>
        <div class="modern-info-row">
          <div class="modern-info-item">
            <label>申请理由</label>
            <div class="modern-info-value long-text">{{ formModel.sqly || '-' }}</div>
          </div>
        </div>
        <div class="modern-info-row">
          <div class="modern-info-item">
            <label>特长优势</label>
            <div class="modern-info-value long-text">{{ formModel.tcys || '-' }}</div>
          </div>
        </div>
      </div>
    </ele-card>

    <!-- 审核表单区域 -->
    <ele-card class="modern-card" shadow="hover"
              :body-style="{padding: '5px 0 2px 0!important'}">
      <template #header>
        <div class="modern-card-header">
          <h3>审核信息</h3>
        </div>
      </template>
      <div class="form-container">
        <form-preview
          v-if="approvalData"
          key="formRef"
          label-position="top"
          route-type="QgzxStudentApproval"
          :current-group="approvalData"
          @on-done-group="onDoneGroup"
        />
      </div>
    </ele-card>

    <!-- 审核记录时间线 -->
    <ele-card class="modern-card" shadow="hover"
              :body-style="{padding: '15px 0 2px 0!important'}">
      <template #header>
        <div class="modern-card-header">
          <h3>审核记录</h3>
        </div>
      </template>
      <div class="time-line-container">
        <ApprovalTimeline :activities="activities"/>
      </div>
    </ele-card>

    <!-- 底部操作按钮 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)" class="cancel-btn">
        取消
      </el-button>
      <el-button size="small"
                 type="primary"
                 :loading="loading"
                 @click="save"
                 class="submit-btn">
        提交审核
      </el-button>
    </template>

    <JobDetailDrawer
      v-if="qgzJobInfo"
      :model-value="detailVisible"
      enter-type="views"
      :form-model="qgzJobInfo"
      @update:model-value="handleDetailDrawerClose"
    />
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {
  ElMessage,
  ElCard,
  ElButton,
  ElIcon,
  ElTimeline,
  ElTimelineItem,
  ElAvatar,
} from 'element-plus';
import {ArrowRight} from '@element-plus/icons-vue';
import {
  operation,
  getNodeState,
  getQqgzxStudentApprovalNodesBySqId,
} from '../api/index.js';
import {comColumns} from '../utils/index.js';
import {
  comApproveStatus,
  generateForm,
  toFormDataWj,
} from '@/utils/common_bak2.js';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import JobDetailDrawer from '@/views/qgzx/components/JobDetailDrawer.vue';
import StudentHeader from '@/views/personInfo/views/components/StudentHeader.vue';
import {queryQgzxJobApplyById} from '@/views/qgzx/qgzx-job-apply/api/index.js';
import ApprovalTimeline from '@/components/ApprovalTimeline/index.vue';
import {statusColor} from '@/utils/status-color/index.js';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
});

// 状态管理
const formRef = ref(null);
const loading = ref(false);
const formModel = ref({});
const approvalData = ref(null);
const proFormGroup = ref([]);
const detailVisible = ref(false);
const qgzJobInfo = ref(null);
const activities = ref([]);

// 业务方法
const handleDetailDrawerClose = (value) => {
  detailVisible.value = value;
};

const showJobDetail = () => {
  detailVisible.value = true;
};

const updateModelValue = (value) => {
  formRef.value?.clearValidate?.();
  proFormGroup.value = [];
  emit('update:modelValue', value);
};

const onDoneGroup = (data) => {
  const existingGroup = proFormGroup.value.find(obj => obj.groupId === data.groupId);
  if (existingGroup) {
    existingGroup.values = data.values;
  } else {
    proFormGroup.value.push(data);
  }
};

const queryQqgzxStudentApprovalNodesBySqId = async () => {
  try {
    const data = await getQqgzxStudentApprovalNodesBySqId(props.data.id);
    activities.value = data.map(item => {
      const statusObj = statusColor().find(obj => obj.label === item.approvalNode.result) || {};
      return {
        ...item,
        title: item.approvalNode.nodeName,
        name: item.approvalNode.result,
        timestamp: item.approvalNode.updateTime,
        size: 'large',
        ...statusObj,
        icon: item.reviewType === '会签' ? 'Stamp' : statusObj.icon || 'CircleCloseFilled',
      };
    });
  } catch (e) {
    ElMessage.error(e.message);
  }
};

const save = async () => {
  try {
    if (!proFormGroup.value?.length) {
      ElMessage.warning('表单数据未加载完成');
      return;
    }

    const validationResults = await Promise.all(
      proFormGroup.value.map(group => group.validate?.() ?? Promise.resolve(true)),
    );

    if (!validationResults.every(Boolean)) {
      ElMessage.error('请检查表单填写是否正确');
      return;
    }

    const resData = proFormGroup.value[0]?.values || {};
    if (!resData.result) {
      ElMessage.error('请选择审核状态');
      return;
    }

    loading.value = true;
    const formData = toFormDataWj({...resData, applicationId: props.data.id});
    await operation(formData);

    ElMessage.success('操作成功');
    updateModelValue(false);
    emit('done');
  } catch (error) {
    ElMessage.error(error.message || '操作失败');
  } finally {
    loading.value = false;
  }
};

const queryNodeState = async () => {
  try {
    const data = await getNodeState({moduleCode: 'qgzx_sqgw'});
    const baseItems = comApproveStatus();
    baseItems.forEach(item => {
      item.label = data[0][item.diyname];
      item.name = data[0][item.prename];
    });
  } catch (e) {
    ElMessage.error(e.message);
  }
};

const queryQgzxJobInfoById = async () => {
  try {
    const rData = await queryQgzxJobApplyById(props.data.jobId);
    qgzJobInfo.value = {
      ...rData,
      workDays: typeof rData?.workDays === 'string'
        ? rData.workDays.split(',')
        : Array.isArray(rData?.workDays)
          ? rData.workDays
          : [],
      Addresses: rData?.jobAddresses?.[0]?.name || '-',
    };
  } catch (e) {
    ElMessage.error(e.message);
  }
};

// 初始化
watch(() => props.modelValue, async (modelValue) => {
  if (modelValue && props.data) {
    await Promise.all([
      queryNodeState(),
      queryQgzxJobInfoById(),
      queryQqgzxStudentApprovalNodesBySqId(),
    ]);

    const baseColumns = comColumns() || [];
    formModel.value = {
      ...generateForm(baseColumns.filter(item => item.prop && !item.disabled)),
      ...props.data,
      workDays: typeof props.data?.workDays === 'string'
        ? props.data.workDays.split(',')
        : Array.isArray(props.data?.workDays)
          ? props.data.workDays
          : [],
      Addresses: props.data?.jobAddresses?.[0]?.name || '-',
    };

    approvalData.value = {
      groupName: '审核信息',
      title: '',
      id: props.data.id,
      sqId: props.data.id,
      nodeId: props.data.nodeId,
      infoType: 'QgzxStudentApproval',
      workflowId: props.data.workflowId,
      listFlag: '否',
    };
  } else {
    formRef.value?.clearValidate?.();
  }
}, {immediate: true});
</script>

<style lang="scss" scoped>
@import "../../css/job-detail.css";

.modern-approval-drawer {
  .modern-card {
    margin-bottom: 6px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
    }

    .modern-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .detail-btn {
        padding: 0;
      }
    }

    .modern-card-body {
      padding: 0 16px !important;
    }

  }

  .modern-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
    margin-bottom: 10px;

    .modern-info-item {
      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
      }

      .modern-info-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        word-break: break-word;
      }
    }
  }

  .modern-info-row {
    margin-bottom: 10px;

    .modern-info-item {
      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
      }

      .modern-info-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;

        &.long-text {
          white-space: pre-wrap;
          line-height: 1.5;
        }
      }
    }
  }

  .time-line-container {
    max-height: 400px;
    overflow-y: auto;
    margin-left: -20px;
  }

  .form-container {
    padding: 0 8px;
  }

  .info-user-avatar {
    margin-top: 8px;
  }
}

.custom-card :deep(.ele-card__body) {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}
</style>
