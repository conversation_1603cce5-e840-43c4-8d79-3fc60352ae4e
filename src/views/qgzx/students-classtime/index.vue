<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="{ padding: '16px', overflow: 'hidden' }">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
        <el-tab-pane label="空闲时间" name="free-time">
          <div class="free-time-container">
            <div class="free-time-header">
              <div class="header-title">
                <el-icon><Calendar /></el-icon>
                <span>空闲时间安排</span>
              </div>
              <div class="header-actions">
                <el-button
                  type="primary"
                  size="small"
                  :icon="Plus"
                  @click="handleAddFreeTime"
                >
                  添加空闲时间
                </el-button>
              </div>
            </div>

            <div class="free-time-content">
              <!-- 空闲时间表格 -->
              <ele-pro-table
                ref="freeTableRef"
                row-key="id"
                :columns="freeTimeColumns"
                :datasource="freeTimeDatasource"
                :border="true"
                :show-overflow-tooltip="true"
                v-model:selections="freeTimeSelections"
                tooltip-effect="light"
                highlight-current-row
                :footer-style="{ paddingBottom: '3px' }"
                style="padding-bottom: 0"
              >
                <template #toolbar>
                  <el-button
                    size="small"
                    :icon="Delete"
                    @click="handleBatchDeleteFreeTime"
                    :disabled="!freeTimeSelections.length"
                  >
                    批量删除
                  </el-button>
                </template>

                <template #weekDay="{ row }">
                  {{ getWeekDayName(row.weekDay) }}
                </template>

                <template #timeRange="{ row }">
                  {{ getTimeRange(row) }}
                </template>

                <template #weekText="{ row }">
                  {{ formatWeekText(row) }}
                </template>

                <template #actions="{ row }">
                  <el-button
                    size="small"
                    type="text"
                    :icon="Delete"
                    @click="handleDeleteFreeTime(row)"
                  >
                    删除
                  </el-button>
                </template>
              </ele-pro-table>
            </div>
          </div>
        </el-tab-pane>
<!--        <el-tab-pane label="课表视图" name="schedule">
          <class-schedule-view
            :schedule-data="scheduleData"
            :time-slots="timeSlots"
            :readonly="false"
            @add-time-slot="handleAddTimeSlot"
            @edit-item="handleEditItem"
            @delete-item="handleDeleteItem"
            @cell-click="handleCellClick"
            @refresh="handleRefresh"
            @drag-drop="handleDragDrop"
          />
        </el-tab-pane>
        <el-tab-pane label="列表视图" name="list">
          <div class="list-view-container">
            &lt;!&ndash; 搜索区域 &ndash;&gt;
            <div class="search-section">
              <el-form :model="searchForm" inline @submit.prevent="handleSearch">
                <el-form-item label="课程名称">
                  <el-input
                    v-model="searchForm.name"
                    placeholder="请输入课程名称"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="星期">
                  <el-select
                    v-model="searchForm.weekDay"
                    placeholder="请选择星期"
                    clearable
                    style="width: 120px"
                  >
                    <el-option
                      v-for="day in weekDays"
                      :key="day.value"
                      :label="day.label"
                      :value="day.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                  <el-button @click="handleResetSearch">重置</el-button>
                </el-form-item>
              </el-form>
            </div>

            &lt;!&ndash; 表格 &ndash;&gt;
            <ele-pro-table
              ref="tableRef"
              row-key="id"
              :columns="columns"
              :datasource="datasource"
              :border="true"
              :show-overflow-tooltip="true"
              v-model:selections="selections"
              tooltip-effect="light"
              highlight-current-row
              :footer-style="{ paddingBottom: '3px' }"
              style="padding-bottom: 0"
            >
              <template #toolbar>
                <el-button
                  size="small"
                  type="primary"
                  :icon="Plus"
                  @click="handleAdd"
                >
                  新建
                </el-button>
                <el-button
                  size="small"
                  :icon="Delete"
                  @click="handleBatchDelete"
                  :disabled="!selections.length"
                >
                  批量删除
                </el-button>
                <el-button
                  size="small"
                  :icon="Upload"
                  @click="handleBatchSave"
                  :disabled="!hasChanges"
                >
                  批量保存
                </el-button>
              </template>

              <template #weekDay="{ row }">
                {{ getWeekDayName(row.weekDay) }}
              </template>

              <template #timeRange="{ row }">
                {{ getTimeRange(row) }}
              </template>

              <template #weekText="{ row }">
                {{ formatWeekText(row) }}
              </template>

              <template #actions="{ row }">
                <el-button
                  size="small"
                  type="text"
                  :icon="Edit"
                  @click="handleEditItem(row)"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="text"
                  :icon="Delete"
                  @click="handleDeleteItem(row)"
                >
                  删除
                </el-button>
              </template>
            </ele-pro-table>
          </div>
        </el-tab-pane>-->
      </el-tabs>
    </ele-card>

    <!-- 编辑弹窗 -->
    <time-slot-editor
      v-model="showEdit"
      :data="editData"
      :time-slots="timeSlots"
      @done="handleEditDone"
    />

    <!-- 批量操作弹窗 -->
    <batch-operations
      v-model="showBatchOps"
      :selected-items="selections"
      :time-slots="timeSlots"
      @done="handleBatchOpsDone"
    />

    <!-- 空闲时间选择器弹窗 -->
    <el-dialog
      v-model="showFreeTimeSelector"
      title="选择空闲时间"
      width="80%"
      :before-close="handleCloseFreeTimeSelector"
    >
      <free-time-selector
        v-model="showFreeTimeSelector"
        :existing-schedule="scheduleData"
        :default-selection="defaultFreeTimeSelection"
        @confirm="handleFreeTimeConfirm"
        @cancel="handleCloseFreeTimeSelector"
      />
    </el-dialog>
  </ele-page>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { Plus, Delete, Upload, Edit, Calendar } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { useRouter } from 'vue-router';
import { usePageTab } from '@/utils/use-page-tab.js';
import { useUserStore } from '@/store/modules/user.js';
import { storeToRefs } from 'pinia';

// API
import {
  queryQgzXsAvailability,
  queryQgzXsAvailabilityPage,
  queryKsbList,
  queryCourseTimeSlots,
  queryFreeTimeSlots,
  saveBatch,
  removes
} from './api/index.js';

// 组件
import ClassScheduleView from './components/ClassScheduleView.vue';
import TimeSlotEditor from './components/TimeSlotEditor.vue';
import BatchOperations from './components/BatchOperations.vue';
import FreeTimeSelector from './components/FreeTimeSelector.vue';

// 工具函数
import { getWeekDayName } from './utils/timeUtils.js';
import { formatWeekText, generateScheduleGrid } from './utils/scheduleUtils.js';
import { detectConflicts } from './utils/conflictDetector.js';

// 路由和状态管理
const { removePageTab, getRouteTabKey, setPageTab, routeTabKey } = usePageTab();
const userStore = useUserStore();
const { pageHeight } = storeToRefs(userStore);
const { currentRoute, push } = useRouter();

// 响应式数据
const activeTab = ref('free-time'); // 当前激活的标签页
const loading = ref(false);
const tableRef = ref(null);
const selections = ref([]);

// 数据
const scheduleData = ref([]); // 学生课表数据
const timeSlots = ref([]); // 课时表数据（COURSE类型）
const freeTimeSlots = ref([]); // 空闲时间段数据（FREE类型）
const hasChanges = ref(false); // 是否有未保存的更改

// 弹窗状态
const showEdit = ref(false);
const showBatchOps = ref(false);
const showFreeTimeSelector = ref(false);
const editData = ref(null);
const defaultFreeTimeSelection = ref([]);

// 空闲时间相关数据
const freeTimeSelections = ref([]);
const freeTableRef = ref(null);

// 搜索表单
const searchForm = reactive({
  name: '',
  weekDay: null
});

// 周几选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 表格列配置
const columns = computed(() => [
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'weekDay',
    slot: 'weekDay',
    label: '星期',
    width: 80,
    align: 'center'
  },
  {
    prop: 'timeRange',
    slot: 'timeRange',
    label: '时间',
    width: 120,
    align: 'center'
  },
  {
    prop: 'name',
    label: '课程名称',
    minWidth: 150,
    formatter: (row) => getTimeSlotName(row)
  },
  {
    prop: 'weekText',
    slot: 'weekText',
    label: '周次',
    minWidth: 200
  },
  {
    prop: 'actions',
    slot: 'actions',
    label: '操作',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]);

// 表格数据源
const datasource = ({ page, limit, where, orders, filters }) => {
  const params = {
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    ...searchForm
  };
  return queryQgzXsAvailabilityPage(params);
};

// 空闲时间表格列配置
const freeTimeColumns = computed(() => [
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'weekDay',
    slot: 'weekDay',
    label: '星期',
    // width: 80,
    align: 'center'
  },
  {
    prop: 'timeRange',
    slot: 'timeRange',
    label: '时间',
    // width: 120,
    align: 'center'
  },
  {
    prop: 'name',
    label: '空闲时间段',
    // minWidth: 150,
    formatter: (row) => getFreeTimeSlotName(row)
  },
  /*{
    prop: 'weekText',
    slot: 'weekText',
    label: '周次',
    minWidth: 200
  },*/
  {
    prop: 'actions',
    slot: 'actions',
    label: '操作',
    width: 120,
    align: 'center',
    fixed: 'right'
  }
]);

// 空闲时间数据源
const freeTimeDatasource = ({ page, limit, where, orders, filters }) => {
  const params = {
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    // 只查询使用了FREE类型时间段的课表数据
    freeTimeOnly: true
  };
  return queryQgzXsAvailabilityPage(params);
};

// 计算属性
const ksbMap = computed(() => {
  const map = {};
  timeSlots.value.forEach(slot => {
    map[slot.id] = slot;
  });
  return map;
});

const freeTimeMap = computed(() => {
  const map = {};
  freeTimeSlots.value.forEach(slot => {
    map[slot.id] = slot;
  });
  return map;
});

const allTimeSlotsMap = computed(() => {
  return { ...ksbMap.value, ...freeTimeMap.value };
});

// 工具方法
const getTimeSlotName = (row) => {
  const ksb = allTimeSlotsMap.value[row.ksbId];
  return ksb ? ksb.name : '未知课程';
};

const getFreeTimeSlotName = (row) => {
  const ksb = freeTimeMap.value[row.ksbId];
  return ksb ? ksb.name : '未知空闲时间';
};

const getTimeRange = (row) => {
  const ksb = allTimeSlotsMap.value[row.ksbId];
  return ksb ? `${ksb.kssj}-${ksb.jssj}` : '';
};

// 事件处理方法
const handleTabChange = (tabName) => {
  activeTab.value = tabName;
  if (tabName === 'list') {
    // 切换到列表视图时刷新表格
    tableRef.value?.reload?.();
  } else if (tabName === 'free-time') {
    // 切换到空闲时间视图时刷新表格
    freeTableRef.value?.reload?.();
  }
};

const handleSearch = () => {
  if (activeTab.value === 'list') {
    tableRef.value?.reload?.({ page: 1 });
  }
};

const handleResetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    weekDay: null
  });
  handleSearch();
};

const handleAdd = () => {
  editData.value = null;
  showEdit.value = true;
};

const handleEditItem = (item) => {
  editData.value = item;
  showEdit.value = true;
};

const handleDeleteItem = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个时间段吗？`,
      '确认删除',
      { type: 'warning' }
    );

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...'
    });

    await removes([item.id]);
    loadingInstance.close();
    ElMessage.success('删除成功');

    // 刷新数据
    await loadData();
    if (activeTab.value === 'list') {
      tableRef.value?.reload?.();
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

const handleBatchDelete = async () => {
  if (!selections.value.length) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selections.value.length} 条数据吗？`,
      '确认删除',
      { type: 'warning' }
    );

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...'
    });

    await removes(selections.value.map(item => item.id));
    loadingInstance.close();
    ElMessage.success('删除成功');

    // 刷新数据
    await loadData();
    if (activeTab.value === 'list') {
      tableRef.value?.reload?.();
    }
    selections.value = [];
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

const handleBatchSave = () => {
  showBatchOps.value = true;
};

const handleEditDone = async () => {
  showEdit.value = false;
  editData.value = null;

  // 刷新数据
  await loadData();
  if (activeTab.value === 'list') {
    tableRef.value?.reload?.();
  }
};

const handleBatchOpsDone = async () => {
  showBatchOps.value = false;

  // 刷新数据
  await loadData();
  if (activeTab.value === 'list') {
    tableRef.value?.reload?.();
  }
};

// 课表视图事件处理
const handleAddTimeSlot = () => {
  handleAdd();
};

const handleCellClick = ({ weekDay, timeSlot }) => {
  // 创建新的课表项
  editData.value = {
    weekDay,
    ksbId: timeSlot.id,
    zcType: 'ALL',
    zcStart: 1,
    zcEnd: 20,
    zcCustom: null
  };
  showEdit.value = true;
};

const handleRefresh = async () => {
  await loadData();
};

const handleDragDrop = async ({ originalItem, newItem }) => {
  try {
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '保存中...'
    });

    // 更新数据
    await saveBatch([newItem]);

    loadingInstance.close();
    ElMessage.success('移动成功');

    // 刷新数据
    await loadData();
  } catch (error) {
    ElMessage.error(error.message || '移动失败');
  }
};

// 数据加载
const loadData = async () => {
  try {
    loading.value = true;

    // 并行加载数据
    const [scheduleResult, timeSlotsResult, freeTimeSlotsResult] = await Promise.all([
      queryQgzXsAvailability({}),
      queryCourseTimeSlots({}),
      queryFreeTimeSlots({})
    ]);

    scheduleData.value = scheduleResult || [];
    timeSlots.value = timeSlotsResult || [];
    freeTimeSlots.value = freeTimeSlotsResult || [];

  } catch (error) {
    ElMessage.error(error.message || '数据加载失败');
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  loadData();
});

// 空闲时间相关方法
const handleAddFreeTime = () => {
  showFreeTimeSelector.value = true;
  defaultFreeTimeSelection.value = [];
};

const handleEditFreeTime = (item) => {
  editData.value = item;
  showEdit.value = true;
};

const handleDeleteFreeTime = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个空闲时间安排吗？`,
      '确认删除',
      { type: 'warning' }
    );

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...'
    });

    await removes([item.id]);
    loadingInstance.close();
    ElMessage.success('删除成功');

    // 刷新数据
    await loadData();
    freeTableRef.value?.reload?.();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

const handleBatchDeleteFreeTime = async () => {
  if (!freeTimeSelections.value.length) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${freeTimeSelections.value.length} 条空闲时间安排吗？`,
      '确认删除',
      { type: 'warning' }
    );

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '删除中...'
    });

    await removes(freeTimeSelections.value.map(item => item.id));
    loadingInstance.close();
    ElMessage.success('删除成功');

    // 刷新数据
    await loadData();
    freeTableRef.value?.reload?.();
    freeTimeSelections.value = [];
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败');
    }
  }
};

const handleFreeTimeConfirm = async ({ selectedCells, selectionMode }) => {
  try {
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '保存中...'
    });

    // 构建保存数据
    const saveData = selectedCells.map(cell => ({
      weekDay: cell.weekDay,
      ksbId: cell.timeSlotId,
      zcType: 'ALL', // 默认全部周次
      zcStart: 1,
      zcEnd: 20,
      zcCustom: null
    }));

    await saveBatch(saveData);

    loadingInstance.close();
    ElMessage.success(`成功添加 ${selectedCells.length} 个空闲时间安排`);

    showFreeTimeSelector.value = false;

    // 刷新数据
    await loadData();
    if (activeTab.value === 'free-time') {
      freeTableRef.value?.reload?.();
    }
  } catch (error) {
    ElMessage.error(error.message || '保存失败');
  }
};

const handleCloseFreeTimeSelector = () => {
  showFreeTimeSelector.value = false;
  defaultFreeTimeSelection.value = [];
};

// 监听数据变化
watch(scheduleData, () => {
  hasChanges.value = false; // 重置更改标识
}, { deep: true });
</script>

<script>
export default {
  name: 'StudentsClassTimeIndex'
};
</script>

<style scoped>
.list-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 空闲时间视图样式 */
.free-time-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.free-time-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.free-time-content {
  flex: 1;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    padding: 12px;
  }

  .search-section .el-form {
    flex-direction: column;
  }

  .search-section .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .free-time-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }
}
</style>
