import request from '@/utils/request';

/**
 * 分页查询学生课表时间
 */
export async function queryQgzXsAvailability(params) {
  const res = await request.get('/workstudy/qgzx-students-classtime', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询学生课表时间
 */
export async function queryQgzXsAvailabilityPage(params) {
  const res = await request.get('/workstudy/qgzx-students-classtime/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询学生课表时间
 */
export async function queryQgzXsAvailabilityById(id) {
  const res = await request.get('/workstudy/qgzx-students-classtime/'+id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存或更新学生课表时间
 */
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-students-classtime/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存学生课表时间
 */
export async function saveBatch(data) {
  const res = await request.post('/workstudy/qgzx-students-classtime/saveBatch', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除学生课表时间
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-students-classtime/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询课时表数据
 */
export async function queryKsbList(params) {
  const res = await request.get('/workstudy/qgzx-ksb', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询课表时间段（COURSE类型）
 */
export async function queryCourseTimeSlots(params) {
  return queryKsbList({ ...params, timeType: 'COURSE' });
}

/**
 * 查询空闲时间段（FREE类型）
 */
export async function queryFreeTimeSlots(params) {
  return queryKsbList({ ...params, timeType: 'FREE' });
}

/**
 * 检查时间冲突
 */
export async function checkTimeConflict(data) {
  const res = await request.post('/workstudy/qgzx-students-classtime/checkConflict', data);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
