/**
 * 冲突检测工具函数
 * <AUTHOR>
 * @since 2025-07-28
 */

import { isTimeOverlap } from './timeUtils.js';
import { hasWeekOverlap, getEffectiveWeeks } from './scheduleUtils.js';

/**
 * 冲突类型枚举
 */
export const ConflictType = {
  TIME_OVERLAP: 'TIME_OVERLAP',     // 时间重叠
  WEEK_OVERLAP: 'WEEK_OVERLAP',     // 周次重叠
  SAME_SLOT: 'SAME_SLOT'            // 相同时间段
};

/**
 * 冲突严重程度枚举
 */
export const ConflictSeverity = {
  ERROR: 'error',     // 错误级别，不允许保存
  WARNING: 'warning', // 警告级别，可以保存但需要确认
  INFO: 'info'        // 信息级别，仅提示
};

/**
 * 检测课表冲突
 * @param {Array} scheduleList - 课表列表
 * @param {Object} targetSchedule - 目标课表项（可选，用于检测新增或修改时的冲突）
 * @param {Object} ksbMap - 课时表映射
 * @returns {Array} 冲突列表
 */
export function detectConflicts(scheduleList, targetSchedule = null, ksbMap = {}) {
  const conflicts = [];
  const checkList = targetSchedule ? [...scheduleList, targetSchedule] : scheduleList;
  
  for (let i = 0; i < checkList.length; i++) {
    for (let j = i + 1; j < checkList.length; j++) {
      const schedule1 = checkList[i];
      const schedule2 = checkList[j];
      
      // 跳过相同的记录
      if (schedule1.id && schedule2.id && schedule1.id === schedule2.id) {
        continue;
      }
      
      const conflict = checkScheduleConflict(schedule1, schedule2, ksbMap);
      if (conflict) {
        conflicts.push(conflict);
      }
    }
  }
  
  return conflicts;
}

/**
 * 检查两个课表项是否冲突
 * @param {Object} schedule1 - 课表项1
 * @param {Object} schedule2 - 课表项2
 * @param {Object} ksbMap - 课时表映射
 * @returns {Object|null} 冲突信息或null
 */
export function checkScheduleConflict(schedule1, schedule2, ksbMap) {
  if (!schedule1 || !schedule2) return null;
  
  // 不同天不冲突
  if (schedule1.weekDay !== schedule2.weekDay) return null;
  
  const ksb1 = ksbMap[schedule1.ksbId];
  const ksb2 = ksbMap[schedule2.ksbId];
  
  if (!ksb1 || !ksb2) return null;
  
  // 检查时间是否重叠
  const timeOverlap = isTimeOverlap(ksb1.kssj, ksb1.jssj, ksb2.kssj, ksb2.jssj);
  if (!timeOverlap) return null;
  
  // 检查周次是否重叠
  const weekOverlap = hasWeekOverlap(schedule1, schedule2);
  if (!weekOverlap) return null;
  
  // 确定冲突类型和严重程度
  let conflictType = ConflictType.TIME_OVERLAP;
  let severity = ConflictSeverity.ERROR;
  
  // 如果是完全相同的时间段
  if (ksb1.kssj === ksb2.kssj && ksb1.jssj === ksb2.jssj) {
    conflictType = ConflictType.SAME_SLOT;
    severity = ConflictSeverity.ERROR;
  }
  
  return {
    id: `${schedule1.id || 'new'}_${schedule2.id || 'new'}`,
    type: conflictType,
    severity,
    schedule1,
    schedule2,
    ksb1,
    ksb2,
    message: generateConflictMessage(schedule1, schedule2, ksb1, ksb2, conflictType),
    weekDay: schedule1.weekDay,
    timeRange: `${ksb1.kssj}-${ksb1.jssj}`,
    conflictWeeks: getConflictWeeks(schedule1, schedule2)
  };
}

/**
 * 生成冲突提示信息
 * @param {Object} schedule1 - 课表项1
 * @param {Object} schedule2 - 课表项2
 * @param {Object} ksb1 - 课时表1
 * @param {Object} ksb2 - 课时表2
 * @param {string} conflictType - 冲突类型
 * @returns {string} 冲突信息
 */
export function generateConflictMessage(schedule1, schedule2, ksb1, ksb2, conflictType) {
  const weekDayNames = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const weekDayName = weekDayNames[schedule1.weekDay];
  
  switch (conflictType) {
    case ConflictType.SAME_SLOT:
      return `${weekDayName} ${ksb1.kssj}-${ksb1.jssj} 时间段重复：${ksb1.name} 与 ${ksb2.name}`;
    case ConflictType.TIME_OVERLAP:
      return `${weekDayName} 时间冲突：${ksb1.name}(${ksb1.kssj}-${ksb1.jssj}) 与 ${ksb2.name}(${ksb2.kssj}-${ksb2.jssj})`;
    default:
      return `${weekDayName} 存在时间冲突`;
  }
}

/**
 * 获取冲突的周次
 * @param {Object} schedule1 - 课表项1
 * @param {Object} schedule2 - 课表项2
 * @returns {Array} 冲突的周次列表
 */
export function getConflictWeeks(schedule1, schedule2) {
  const weeks1 = getEffectiveWeeks(schedule1);
  const weeks2 = getEffectiveWeeks(schedule2);
  
  return weeks1.filter(week => weeks2.includes(week));
}

/**
 * 按严重程度分组冲突
 * @param {Array} conflicts - 冲突列表
 * @returns {Object} 按严重程度分组的冲突
 */
export function groupConflictsBySeverity(conflicts) {
  const grouped = {
    [ConflictSeverity.ERROR]: [],
    [ConflictSeverity.WARNING]: [],
    [ConflictSeverity.INFO]: []
  };
  
  conflicts.forEach(conflict => {
    grouped[conflict.severity].push(conflict);
  });
  
  return grouped;
}

/**
 * 检查是否有阻止保存的冲突
 * @param {Array} conflicts - 冲突列表
 * @returns {boolean} 是否有阻止保存的冲突
 */
export function hasBlockingConflicts(conflicts) {
  return conflicts.some(conflict => conflict.severity === ConflictSeverity.ERROR);
}

/**
 * 生成冲突摘要信息
 * @param {Array} conflicts - 冲突列表
 * @returns {Object} 冲突摘要
 */
export function generateConflictSummary(conflicts) {
  const grouped = groupConflictsBySeverity(conflicts);
  
  return {
    total: conflicts.length,
    errors: grouped[ConflictSeverity.ERROR].length,
    warnings: grouped[ConflictSeverity.WARNING].length,
    infos: grouped[ConflictSeverity.INFO].length,
    hasBlockingConflicts: hasBlockingConflicts(conflicts),
    canSave: !hasBlockingConflicts(conflicts)
  };
}

/**
 * 获取冲突解决建议
 * @param {Object} conflict - 冲突对象
 * @returns {Array} 解决建议列表
 */
export function getConflictResolutionSuggestions(conflict) {
  const suggestions = [];
  
  switch (conflict.type) {
    case ConflictType.SAME_SLOT:
      suggestions.push('删除其中一个重复的时间段');
      suggestions.push('修改其中一个时间段的时间');
      suggestions.push('修改其中一个时间段的周次安排');
      break;
    case ConflictType.TIME_OVERLAP:
      suggestions.push('调整时间段，避免重叠');
      suggestions.push('修改周次安排，错开冲突周次');
      suggestions.push('删除其中一个时间段');
      break;
    default:
      suggestions.push('请检查并调整冲突的时间安排');
  }
  
  return suggestions;
}
