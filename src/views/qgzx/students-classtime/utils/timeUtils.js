/**
 * 时间处理工具函数
 * <AUTHOR>
 * @since 2025-07-28
 */

import dayjs from 'dayjs';

/**
 * 时间格式化
 * @param {string|Date} time - 时间
 * @param {string} format - 格式
 * @returns {string} 格式化后的时间
 */
export function formatTime(time, format = 'HH:mm') {
  if (!time) return '';
  return dayjs(time, 'HH:mm:ss').format(format);
}

/**
 * 解析时间字符串为分钟数
 * @param {string} timeStr - 时间字符串 (HH:mm)
 * @returns {number} 分钟数
 */
export function timeToMinutes(timeStr) {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * 分钟数转换为时间字符串
 * @param {number} minutes - 分钟数
 * @returns {string} 时间字符串 (HH:mm)
 */
export function minutesToTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

/**
 * 检查两个时间段是否重叠
 * @param {string} start1 - 时间段1开始时间
 * @param {string} end1 - 时间段1结束时间
 * @param {string} start2 - 时间段2开始时间
 * @param {string} end2 - 时间段2结束时间
 * @returns {boolean} 是否重叠
 */
export function isTimeOverlap(start1, end1, start2, end2) {
  const start1Minutes = timeToMinutes(start1);
  const end1Minutes = timeToMinutes(end1);
  const start2Minutes = timeToMinutes(start2);
  const end2Minutes = timeToMinutes(end2);
  
  return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
}

/**
 * 获取时间段的持续时间（分钟）
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @returns {number} 持续时间（分钟）
 */
export function getTimeDuration(startTime, endTime) {
  return timeToMinutes(endTime) - timeToMinutes(startTime);
}

/**
 * 验证时间格式
 * @param {string} time - 时间字符串
 * @returns {boolean} 是否有效
 */
export function isValidTime(time) {
  if (!time) return false;
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}

/**
 * 获取一天的时间段列表
 * @param {number} startHour - 开始小时
 * @param {number} endHour - 结束小时
 * @param {number} interval - 间隔（分钟）
 * @returns {Array} 时间段列表
 */
export function getDayTimeSlots(startHour = 8, endHour = 22, interval = 30) {
  const slots = [];
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += interval) {
      const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      const endMinute = minute + interval;
      const endHour = endMinute >= 60 ? hour + 1 : hour;
      const endMin = endMinute >= 60 ? endMinute - 60 : endMinute;
      const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
      
      if (endHour <= endHour) {
        slots.push({
          startTime,
          endTime,
          label: `${startTime}-${endTime}`
        });
      }
    }
  }
  return slots;
}

/**
 * 获取周几的中文名称
 * @param {number} weekDay - 周几 (1-7)
 * @returns {string} 中文名称
 */
export function getWeekDayName(weekDay) {
  const names = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  return names[weekDay] || '';
}

/**
 * 获取周几的简称
 * @param {number} weekDay - 周几 (1-7)
 * @returns {string} 简称
 */
export function getWeekDayShort(weekDay) {
  const names = ['', '一', '二', '三', '四', '五', '六', '日'];
  return names[weekDay] || '';
}
