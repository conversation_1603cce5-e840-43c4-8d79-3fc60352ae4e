/**
 * 课表处理工具函数
 * <AUTHOR>
 * @since 2025-07-28
 */

import { isTimeOverlap, getWeekDayName } from './timeUtils.js';

/**
 * 周次类型枚举
 */
export const WeekType = {
  ALL: 'ALL',      // 全部周次
  ODD: 'ODD',      // 单周
  EVEN: 'EVEN',    // 双周
  CUSTOM: 'CUSTOM' // 自定义周次
};

/**
 * 时间段类型枚举
 */
export const TimeType = {
  COURSE: 'COURSE', // 课表时间
  FREE: 'FREE'      // 空闲时间
};

/**
 * 检查指定周次是否在课程安排中
 * @param {Object} classTime - 课程时间对象
 * @param {number} weekNumber - 周次
 * @returns {boolean} 是否在安排中
 */
export function hasClassInWeek(classTime, weekNumber) {
  if (!classTime || !weekNumber) return false;
  
  const { zcType, zcStart, zcEnd, zcCustom } = classTime;
  
  // 检查周次范围
  if (zcStart && zcEnd && (weekNumber < zcStart || weekNumber > zcEnd)) {
    return false;
  }
  
  switch (zcType) {
    case WeekType.ALL:
      return true;
    case WeekType.ODD:
      return weekNumber % 2 === 1;
    case WeekType.EVEN:
      return weekNumber % 2 === 0;
    case WeekType.CUSTOM:
      try {
        const customWeeks = JSON.parse(zcCustom || '[]');
        return customWeeks.includes(weekNumber);
      } catch (e) {
        console.warn('解析自定义周次失败:', e);
        return false;
      }
    default:
      return false;
  }
}

/**
 * 格式化周次显示文本
 * @param {Object} classTime - 课程时间对象
 * @returns {string} 格式化的周次文本
 */
export function formatWeekText(classTime) {
  if (!classTime) return '';
  
  const { zcType, zcStart, zcEnd, zcCustom } = classTime;
  let typeText = '';
  
  switch (zcType) {
    case WeekType.ALL:
      typeText = '全部周次';
      break;
    case WeekType.ODD:
      typeText = '单周';
      break;
    case WeekType.EVEN:
      typeText = '双周';
      break;
    case WeekType.CUSTOM:
      try {
        const customWeeks = JSON.parse(zcCustom || '[]');
        typeText = `第${customWeeks.join(',')}周`;
      } catch (e) {
        typeText = '自定义周次';
      }
      break;
    default:
      typeText = '未知';
  }
  
  if (zcStart && zcEnd) {
    return `${zcStart}-${zcEnd}周 (${typeText})`;
  }
  
  return typeText;
}

/**
 * 检查课表时间冲突
 * @param {Array} scheduleList - 课表列表
 * @param {Object} newSchedule - 新的课表项
 * @param {Object} ksbMap - 课时表映射
 * @returns {Array} 冲突的课表项
 */
export function checkScheduleConflicts(scheduleList, newSchedule, ksbMap) {
  if (!scheduleList || !newSchedule || !ksbMap) return [];
  
  const conflicts = [];
  const newKsb = ksbMap[newSchedule.ksbId];
  
  if (!newKsb) return conflicts;
  
  scheduleList.forEach(schedule => {
    if (schedule.id === newSchedule.id) return; // 跳过自己
    
    const ksb = ksbMap[schedule.ksbId];
    if (!ksb) return;
    
    // 检查是否同一天
    if (schedule.weekDay !== newSchedule.weekDay) return;
    
    // 检查时间是否重叠
    if (!isTimeOverlap(ksb.kssj, ksb.jssj, newKsb.kssj, newKsb.jssj)) return;
    
    // 检查周次是否重叠
    if (hasWeekOverlap(schedule, newSchedule)) {
      conflicts.push({
        schedule,
        ksb,
        reason: '时间冲突'
      });
    }
  });
  
  return conflicts;
}

/**
 * 检查两个课表项的周次是否重叠
 * @param {Object} schedule1 - 课表项1
 * @param {Object} schedule2 - 课表项2
 * @returns {boolean} 是否重叠
 */
export function hasWeekOverlap(schedule1, schedule2) {
  // 获取两个课表项的有效周次列表
  const weeks1 = getEffectiveWeeks(schedule1);
  const weeks2 = getEffectiveWeeks(schedule2);
  
  // 检查是否有交集
  return weeks1.some(week => weeks2.includes(week));
}

/**
 * 获取课表项的有效周次列表
 * @param {Object} schedule - 课表项
 * @returns {Array} 有效周次列表
 */
export function getEffectiveWeeks(schedule) {
  if (!schedule) return [];
  
  const { zcType, zcStart, zcEnd, zcCustom } = schedule;
  const start = zcStart || 1;
  const end = zcEnd || 20; // 默认20周
  const weeks = [];
  
  switch (zcType) {
    case WeekType.ALL:
      for (let i = start; i <= end; i++) {
        weeks.push(i);
      }
      break;
    case WeekType.ODD:
      for (let i = start; i <= end; i++) {
        if (i % 2 === 1) weeks.push(i);
      }
      break;
    case WeekType.EVEN:
      for (let i = start; i <= end; i++) {
        if (i % 2 === 0) weeks.push(i);
      }
      break;
    case WeekType.CUSTOM:
      try {
        const customWeeks = JSON.parse(zcCustom || '[]');
        customWeeks.forEach(week => {
          if (week >= start && week <= end) {
            weeks.push(week);
          }
        });
      } catch (e) {
        console.warn('解析自定义周次失败:', e);
      }
      break;
  }
  
  return weeks;
}

/**
 * 生成课表网格数据
 * @param {Array} scheduleList - 课表列表
 * @param {Object} ksbMap - 课时表映射
 * @returns {Object} 网格数据
 */
export function generateScheduleGrid(scheduleList, ksbMap) {
  const grid = {};
  
  // 初始化7天的网格
  for (let day = 1; day <= 7; day++) {
    grid[day] = [];
  }
  
  scheduleList.forEach(schedule => {
    const ksb = ksbMap[schedule.ksbId];
    if (!ksb) return;
    
    const gridItem = {
      ...schedule,
      ksb,
      weekDayName: getWeekDayName(schedule.weekDay),
      weekText: formatWeekText(schedule),
      timeText: `${ksb.kssj}-${ksb.jssj}`,
      name: ksb.name
    };
    
    grid[schedule.weekDay].push(gridItem);
  });
  
  // 按时间排序
  Object.keys(grid).forEach(day => {
    grid[day].sort((a, b) => {
      return a.ksb.sort - b.ksb.sort || a.ksb.kssj.localeCompare(b.ksb.kssj);
    });
  });
  
  return grid;
}

/**
 * 创建新的课表项
 * @param {Object} params - 参数
 * @returns {Object} 新的课表项
 */
export function createScheduleItem(params) {
  const {
    ksbId,
    weekDay,
    zcType = WeekType.ALL,
    zcStart = 1,
    zcEnd = 20,
    zcCustom = null
  } = params;
  
  return {
    id: null, // 新建时为null
    ksbId,
    weekDay,
    zcType,
    zcStart,
    zcEnd,
    zcCustom: zcCustom ? JSON.stringify(zcCustom) : null
  };
}
