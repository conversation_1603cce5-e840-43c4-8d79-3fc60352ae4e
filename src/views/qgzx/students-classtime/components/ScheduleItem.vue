<!--
  课表项组件
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <div 
    class="schedule-item"
    :class="itemClasses"
    :draggable="!readonly"
    @dragstart="handleDragStart"
    @click.stop="handleClick"
  >
    <!-- 课程名称 -->
    <div class="item-name" :title="data.ksb?.name">
      {{ data.ksb?.name || '未知课程' }}
    </div>
    
    <!-- 时间信息 -->
    <div v-if="detailed" class="item-time">
      {{ data.ksb?.kssj }}-{{ data.ksb?.jssj }}
    </div>
    
    <!-- 周次信息 -->
    <div class="item-weeks" :title="weekText">
      {{ weekText }}
    </div>
    
    <!-- 冲突标识 -->
    <div v-if="hasConflicts" class="conflict-indicator">
      <el-icon><Warning /></el-icon>
    </div>
    
    <!-- 操作按钮 -->
    <div v-if="!readonly" class="item-actions">
      <el-button 
        size="small" 
        type="text" 
        :icon="Edit"
        @click.stop="handleEdit"
      />
      <el-button 
        size="small" 
        type="text" 
        :icon="Delete"
        @click.stop="handleDelete"
      />
    </div>
    
    <!-- 详细信息（详细视图） -->
    <div v-if="detailed" class="item-details">
      <div class="detail-row">
        <span class="detail-label">类型:</span>
        <span class="detail-value">{{ getTimeTypeText() }}</span>
      </div>
      <div v-if="hasConflicts" class="detail-row conflict">
        <span class="detail-label">冲突:</span>
        <span class="detail-value">{{ conflicts.length }}个</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject } from 'vue';
import { Edit, Delete, Warning } from '@element-plus/icons-vue';
import { formatWeekText } from '../utils/scheduleUtils.js';

const props = defineProps({
  /** 课表项数据 */
  data: {
    type: Object,
    required: true
  },
  /** 冲突列表 */
  conflicts: {
    type: Array,
    default: () => []
  },
  /** 是否显示详细信息 */
  detailed: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['edit', 'delete', 'drag-start', 'click']);

// 注入上下文
const scheduleContext = inject('scheduleContext', {});
const readonly = computed(() => scheduleContext.readonly?.value || false);

// 计算属性
const hasConflicts = computed(() => props.conflicts.length > 0);

const weekText = computed(() => {
  return formatWeekText(props.data);
});

const itemClasses = computed(() => {
  return {
    'has-conflicts': hasConflicts.value,
    'readonly': readonly.value,
    'detailed': props.detailed,
    'draggable': !readonly.value
  };
});

// 方法
const getTimeTypeText = () => {
  const timeType = props.data.ksb?.timeType;
  switch (timeType) {
    case 'COURSE':
      return '课表时间';
    case 'FREE':
      return '空闲时间';
    default:
      return '未知';
  }
};

const handleClick = () => {
  emit('click', props.data);
};

const handleEdit = () => {
  emit('edit', props.data);
};

const handleDelete = () => {
  emit('delete', props.data);
};

const handleDragStart = (event) => {
  if (readonly.value) {
    event.preventDefault();
    return;
  }
  
  event.dataTransfer.effectAllowed = 'move';
  event.dataTransfer.setData('text/plain', JSON.stringify(props.data));
  emit('drag-start', props.data);
};
</script>

<style scoped>
.schedule-item {
  position: relative;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 6px 8px;
  margin: 2px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  line-height: 1.2;
}

.schedule-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.schedule-item.draggable {
  cursor: move;
}

.schedule-item.draggable:hover {
  transform: translateY(-1px);
}

.schedule-item.has-conflicts {
  border-color: #f56c6c;
  background: #fef0f0;
}

.schedule-item.readonly {
  cursor: default;
  opacity: 0.8;
}

.schedule-item.detailed {
  padding: 12px;
  margin: 4px;
  font-size: 13px;
}

.item-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-time {
  color: #606266;
  font-size: 11px;
  margin-bottom: 4px;
}

.item-weeks {
  color: #909399;
  font-size: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detailed .item-weeks {
  font-size: 11px;
  margin-bottom: 8px;
}

.conflict-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  color: #f56c6c;
  font-size: 12px;
}

.item-actions {
  position: absolute;
  top: 2px;
  right: 2px;
  display: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  padding: 2px;
}

.schedule-item:hover .item-actions {
  display: flex;
}

.item-actions .el-button {
  padding: 2px 4px;
  font-size: 12px;
  min-height: auto;
}

.item-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #909399;
  font-weight: 500;
}

.detail-value {
  color: #606266;
}

.detail-row.conflict .detail-value {
  color: #f56c6c;
}

/* 拖拽状态 */
.schedule-item:active {
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-item {
    padding: 4px 6px;
    font-size: 11px;
  }
  
  .schedule-item.detailed {
    padding: 8px;
    font-size: 12px;
  }
  
  .item-name {
    font-size: 11px;
  }
  
  .item-time {
    font-size: 10px;
  }
  
  .item-weeks {
    font-size: 9px;
  }
  
  .detailed .item-weeks {
    font-size: 10px;
  }
  
  .conflict-indicator {
    font-size: 10px;
  }
  
  .item-actions .el-button {
    font-size: 10px;
    padding: 1px 2px;
  }
}

/* 动画效果 */
@keyframes conflict-pulse {
  0% { border-color: #f56c6c; }
  50% { border-color: #f89898; }
  100% { border-color: #f56c6c; }
}

.schedule-item.has-conflicts {
  animation: conflict-pulse 2s infinite;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .schedule-item {
    background: #2d2d2d;
    border-color: #4c4d4f;
    color: #e4e7ed;
  }
  
  .schedule-item:hover {
    border-color: #409eff;
  }
  
  .schedule-item.has-conflicts {
    background: #3d2626;
    border-color: #f56c6c;
  }
  
  .item-name {
    color: #e4e7ed;
  }
  
  .item-time {
    color: #c0c4cc;
  }
  
  .item-weeks {
    color: #909399;
  }
  
  .detail-label {
    color: #909399;
  }
  
  .detail-value {
    color: #c0c4cc;
  }
  
  .item-actions {
    background: rgba(45, 45, 45, 0.9);
  }
  
  .item-details {
    border-top-color: #4c4d4f;
  }
}
</style>
