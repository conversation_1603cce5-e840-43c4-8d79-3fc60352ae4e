<!--
  课表视图组件
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <div class="class-schedule-view">
    <!-- 工具栏 -->
    <div class="schedule-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            :type="viewMode === 'week' ? 'primary' : ''" 
            size="small"
            @click="setViewMode('week')"
          >
            周视图
          </el-button>
          <el-button 
            :type="viewMode === 'day' ? 'primary' : ''" 
            size="small"
            @click="setViewMode('day')"
          >
            日视图
          </el-button>
        </el-button-group>
        
        <el-select 
          v-if="viewMode === 'day'" 
          v-model="selectedDay" 
          size="small" 
          style="margin-left: 8px; width: 100px;"
          @change="handleDayChange"
        >
          <el-option 
            v-for="day in weekDays" 
            :key="day.value" 
            :label="day.label" 
            :value="day.value"
          />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button 
          size="small" 
          type="primary" 
          :icon="Plus" 
          @click="handleAddTimeSlot"
        >
          添加时间段
        </el-button>
        <el-button 
          size="small" 
          :icon="Refresh" 
          @click="handleRefresh"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 课表网格 -->
    <div class="schedule-grid" :class="{ 'day-view': viewMode === 'day' }">
      <!-- 时间轴 -->
      <div class="time-axis">
        <div class="time-header">时间</div>
        <div 
          v-for="timeSlot in timeSlots" 
          :key="timeSlot.id" 
          class="time-slot"
          :style="{ height: timeSlotHeight + 'px' }"
        >
          <span class="time-label">{{ timeSlot.name }}</span>
          <span class="time-range">{{ timeSlot.kssj }}-{{ timeSlot.jssj }}</span>
        </div>
      </div>

      <!-- 周视图 -->
      <template v-if="viewMode === 'week'">
        <div 
          v-for="day in displayDays" 
          :key="day.value" 
          class="day-column"
        >
          <div class="day-header">{{ day.label }}</div>
          <div 
            v-for="timeSlot in timeSlots" 
            :key="`${day.value}-${timeSlot.id}`"
            class="schedule-cell"
            :style="{ height: timeSlotHeight + 'px' }"
            @click="handleCellClick(day.value, timeSlot)"
            @drop="handleDrop($event, day.value, timeSlot)"
            @dragover.prevent
            @dragenter.prevent
          >
            <schedule-item
              v-for="item in getScheduleItems(day.value, timeSlot.id)"
              :key="item.id"
              :data="item"
              :conflicts="getItemConflicts(item)"
              @edit="handleEditItem"
              @delete="handleDeleteItem"
              @drag-start="handleDragStart"
            />
          </div>
        </div>
      </template>

      <!-- 日视图 -->
      <template v-else>
        <div class="day-column single-day">
          <div class="day-header">{{ getSelectedDayLabel() }}</div>
          <div 
            v-for="timeSlot in timeSlots" 
            :key="`${selectedDay}-${timeSlot.id}`"
            class="schedule-cell detailed"
            :style="{ height: timeSlotHeight * 2 + 'px' }"
            @click="handleCellClick(selectedDay, timeSlot)"
            @drop="handleDrop($event, selectedDay, timeSlot)"
            @dragover.prevent
            @dragenter.prevent
          >
            <schedule-item
              v-for="item in getScheduleItems(selectedDay, timeSlot.id)"
              :key="item.id"
              :data="item"
              :conflicts="getItemConflicts(item)"
              :detailed="true"
              @edit="handleEditItem"
              @delete="handleDeleteItem"
              @drag-start="handleDragStart"
            />
          </div>
        </div>
      </template>
    </div>

    <!-- 冲突提示 -->
    <div v-if="conflicts.length > 0" class="conflict-panel">
      <el-alert
        title="检测到时间冲突"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="conflict-list">
            <div 
              v-for="conflict in conflicts.slice(0, 3)" 
              :key="conflict.id"
              class="conflict-item"
            >
              {{ conflict.message }}
            </div>
            <div v-if="conflicts.length > 3" class="conflict-more">
              还有 {{ conflicts.length - 3 }} 个冲突...
            </div>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 加载状态 -->
    <el-loading v-if="loading" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, provide } from 'vue';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ScheduleItem from './ScheduleItem.vue';
import { getWeekDayName } from '../utils/timeUtils.js';
import { generateScheduleGrid, checkScheduleConflicts } from '../utils/scheduleUtils.js';
import { detectConflicts } from '../utils/conflictDetector.js';

const props = defineProps({
  /** 课表数据 */
  scheduleData: {
    type: Array,
    default: () => []
  },
  /** 课时表数据 */
  timeSlots: {
    type: Array,
    default: () => []
  },
  /** 是否只读 */
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'add-time-slot',
  'edit-item', 
  'delete-item',
  'cell-click',
  'refresh',
  'drag-drop'
]);

// 响应式数据
const loading = ref(false);
const viewMode = ref('week'); // 'week' | 'day'
const selectedDay = ref(1); // 当前选中的天（日视图用）
const timeSlotHeight = ref(60); // 时间段高度
const draggedItem = ref(null); // 拖拽的项目

// 周几选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 显示的天数（周视图显示全部，日视图显示选中的）
const displayDays = computed(() => {
  return viewMode.value === 'week' ? weekDays : weekDays.filter(day => day.value === selectedDay.value);
});

// 课时表映射
const ksbMap = computed(() => {
  const map = {};
  props.timeSlots.forEach(slot => {
    map[slot.id] = slot;
  });
  return map;
});

// 课表网格数据
const scheduleGrid = computed(() => {
  return generateScheduleGrid(props.scheduleData, ksbMap.value);
});

// 冲突检测
const conflicts = computed(() => {
  return detectConflicts(props.scheduleData, null, ksbMap.value);
});

// 方法
const setViewMode = (mode) => {
  viewMode.value = mode;
};

const handleDayChange = (day) => {
  selectedDay.value = day;
};

const getSelectedDayLabel = () => {
  const day = weekDays.find(d => d.value === selectedDay.value);
  return day ? day.label : '';
};

const getScheduleItems = (weekDay, timeSlotId) => {
  return props.scheduleData.filter(item => 
    item.weekDay === weekDay && item.ksbId === timeSlotId
  );
};

const getItemConflicts = (item) => {
  return conflicts.value.filter(conflict => 
    conflict.schedule1.id === item.id || conflict.schedule2.id === item.id
  );
};

const handleAddTimeSlot = () => {
  emit('add-time-slot');
};

const handleRefresh = () => {
  emit('refresh');
};

const handleCellClick = (weekDay, timeSlot) => {
  if (props.readonly) return;
  emit('cell-click', { weekDay, timeSlot });
};

const handleEditItem = (item) => {
  emit('edit-item', item);
};

const handleDeleteItem = (item) => {
  emit('delete-item', item);
};

const handleDragStart = (item) => {
  draggedItem.value = item;
};

const handleDrop = (event, weekDay, timeSlot) => {
  if (!draggedItem.value || props.readonly) return;
  
  event.preventDefault();
  
  const newItem = {
    ...draggedItem.value,
    weekDay,
    ksbId: timeSlot.id
  };
  
  emit('drag-drop', {
    originalItem: draggedItem.value,
    newItem,
    weekDay,
    timeSlot
  });
  
  draggedItem.value = null;
};

// 提供给子组件的数据
provide('scheduleContext', {
  readonly: computed(() => props.readonly),
  ksbMap,
  conflicts
});

onMounted(() => {
  // 组件挂载后的初始化逻辑
});
</script>

<style scoped>
.class-schedule-view {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.schedule-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.schedule-grid {
  flex: 1;
  display: flex;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: auto;
}

.time-axis {
  min-width: 120px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
}

.time-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.time-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #e4e7ed;
  padding: 4px;
}

.time-label {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.time-range {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.day-column {
  flex: 1;
  min-width: 120px;
  border-right: 1px solid #e4e7ed;
}

.day-column:last-child {
  border-right: none;
}

.day-column.single-day {
  min-width: 300px;
}

.day-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  background: #f5f7fa;
}

.schedule-cell {
  position: relative;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  transition: background-color 0.2s;
}

.schedule-cell:hover {
  background-color: #f5f7fa;
}

.schedule-cell.detailed {
  padding: 8px;
}

.conflict-panel {
  margin-top: 16px;
}

.conflict-list {
  font-size: 12px;
}

.conflict-item {
  margin-bottom: 4px;
}

.conflict-more {
  color: #909399;
  font-style: italic;
}

/* 日视图样式调整 */
.day-view .schedule-grid {
  max-width: 500px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .time-axis {
    min-width: 80px;
  }
  
  .day-column {
    min-width: 80px;
  }
  
  .time-slot {
    padding: 2px;
  }
  
  .time-label {
    font-size: 10px;
  }
  
  .time-range {
    font-size: 8px;
  }
}
</style>
