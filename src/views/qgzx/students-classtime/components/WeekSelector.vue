<!--
  周次选择器组件
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <div class="week-selector">
    <!-- 周次类型选择 -->
    <div class="week-type-section">
      <label class="section-label">周次类型</label>
      <el-radio-group v-model="weekType" @change="handleWeekTypeChange">
        <el-radio label="ALL">全部周次</el-radio>
        <el-radio label="ODD">单周</el-radio>
        <el-radio label="EVEN">双周</el-radio>
        <el-radio label="CUSTOM">自定义</el-radio>
      </el-radio-group>
    </div>

    <!-- 周次范围选择 -->
    <div class="week-range-section">
      <label class="section-label">周次范围</label>
      <div class="range-inputs">
        <el-input-number
          v-model="weekStart"
          :min="1"
          :max="30"
          size="small"
          controls-position="right"
          @change="handleRangeChange"
        />
        <span class="range-separator">至</span>
        <el-input-number
          v-model="weekEnd"
          :min="weekStart"
          :max="30"
          size="small"
          controls-position="right"
          @change="handleRangeChange"
        />
        <span class="range-unit">周</span>
      </div>
    </div>

    <!-- 自定义周次选择 -->
    <div v-if="weekType === 'CUSTOM'" class="custom-weeks-section">
      <label class="section-label">选择周次</label>
      <div class="custom-weeks-grid">
        <el-checkbox-group v-model="customWeeks" @change="handleCustomWeeksChange">
          <el-checkbox
            v-for="week in availableWeeks"
            :key="week"
            :label="week"
            class="week-checkbox"
          >
            {{ week }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      
      <!-- 快捷选择 -->
      <div class="quick-select">
        <el-button size="small" @click="selectAllWeeks">全选</el-button>
        <el-button size="small" @click="selectOddWeeks">选择单周</el-button>
        <el-button size="small" @click="selectEvenWeeks">选择双周</el-button>
        <el-button size="small" @click="clearWeeks">清空</el-button>
      </div>
    </div>

    <!-- 预览信息 -->
    <div class="preview-section">
      <label class="section-label">预览</label>
      <div class="preview-content">
        <div class="preview-text">{{ previewText }}</div>
        <div class="preview-weeks">
          <el-tag
            v-for="week in effectiveWeeks.slice(0, 10)"
            :key="week"
            size="small"
            class="week-tag"
          >
            第{{ week }}周
          </el-tag>
          <el-tag v-if="effectiveWeeks.length > 10" size="small" type="info">
            ...还有{{ effectiveWeeks.length - 10 }}周
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { WeekType, getEffectiveWeeks, formatWeekText } from '../utils/scheduleUtils.js';

const props = defineProps({
  /** 周次类型 */
  modelValue: {
    type: Object,
    default: () => ({
      zcType: WeekType.ALL,
      zcStart: 1,
      zcEnd: 20,
      zcCustom: null
    })
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 响应式数据
const weekType = ref(WeekType.ALL);
const weekStart = ref(1);
const weekEnd = ref(20);
const customWeeks = ref([]);

// 计算属性
const availableWeeks = computed(() => {
  const weeks = [];
  for (let i = weekStart.value; i <= weekEnd.value; i++) {
    weeks.push(i);
  }
  return weeks;
});

const effectiveWeeks = computed(() => {
  const scheduleData = {
    zcType: weekType.value,
    zcStart: weekStart.value,
    zcEnd: weekEnd.value,
    zcCustom: weekType.value === WeekType.CUSTOM ? JSON.stringify(customWeeks.value) : null
  };
  return getEffectiveWeeks(scheduleData);
});

const previewText = computed(() => {
  const scheduleData = {
    zcType: weekType.value,
    zcStart: weekStart.value,
    zcEnd: weekEnd.value,
    zcCustom: weekType.value === WeekType.CUSTOM ? JSON.stringify(customWeeks.value) : null
  };
  return formatWeekText(scheduleData);
});

// 方法
const handleWeekTypeChange = () => {
  if (weekType.value !== WeekType.CUSTOM) {
    customWeeks.value = [];
  }
  emitChange();
};

const handleRangeChange = () => {
  // 确保结束周次不小于开始周次
  if (weekEnd.value < weekStart.value) {
    weekEnd.value = weekStart.value;
  }
  
  // 更新自定义周次选择范围
  if (weekType.value === WeekType.CUSTOM) {
    customWeeks.value = customWeeks.value.filter(week => 
      week >= weekStart.value && week <= weekEnd.value
    );
  }
  
  emitChange();
};

const handleCustomWeeksChange = () => {
  emitChange();
};

const selectAllWeeks = () => {
  customWeeks.value = [...availableWeeks.value];
  emitChange();
};

const selectOddWeeks = () => {
  customWeeks.value = availableWeeks.value.filter(week => week % 2 === 1);
  emitChange();
};

const selectEvenWeeks = () => {
  customWeeks.value = availableWeeks.value.filter(week => week % 2 === 0);
  emitChange();
};

const clearWeeks = () => {
  customWeeks.value = [];
  emitChange();
};

const emitChange = () => {
  const value = {
    zcType: weekType.value,
    zcStart: weekStart.value,
    zcEnd: weekEnd.value,
    zcCustom: weekType.value === WeekType.CUSTOM ? JSON.stringify(customWeeks.value) : null
  };
  
  emit('update:modelValue', value);
  emit('change', value);
};

// 初始化数据
const initializeData = () => {
  const value = props.modelValue;
  if (value) {
    weekType.value = value.zcType || WeekType.ALL;
    weekStart.value = value.zcStart || 1;
    weekEnd.value = value.zcEnd || 20;
    
    if (value.zcCustom && weekType.value === WeekType.CUSTOM) {
      try {
        customWeeks.value = JSON.parse(value.zcCustom);
      } catch (e) {
        console.warn('解析自定义周次失败:', e);
        customWeeks.value = [];
      }
    }
  }
};

// 监听props变化
watch(() => props.modelValue, initializeData, { deep: true });

onMounted(() => {
  initializeData();
});
</script>

<style scoped>
.week-selector {
  padding: 16px;
}

.section-label {
  display: block;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.week-type-section,
.week-range-section,
.custom-weeks-section,
.preview-section {
  margin-bottom: 20px;
}

.week-type-section:last-child,
.week-range-section:last-child,
.custom-weeks-section:last-child,
.preview-section:last-child {
  margin-bottom: 0;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-separator,
.range-unit {
  color: #606266;
  font-size: 14px;
}

.custom-weeks-grid {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  background: #fafafa;
  max-height: 200px;
  overflow-y: auto;
}

.week-checkbox {
  margin-right: 12px;
  margin-bottom: 8px;
  width: 60px;
}

.quick-select {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-section {
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.preview-content {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.preview-text {
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.preview-weeks {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.week-tag {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .week-selector {
    padding: 12px;
  }
  
  .range-inputs {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .custom-weeks-grid {
    padding: 8px;
  }
  
  .week-checkbox {
    width: 50px;
    margin-right: 8px;
    margin-bottom: 6px;
  }
  
  .quick-select {
    gap: 6px;
  }
  
  .quick-select .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
  
  .preview-weeks {
    gap: 2px;
  }
  
  .week-tag {
    font-size: 10px;
  }
}

/* 滚动条样式 */
.custom-weeks-grid::-webkit-scrollbar {
  width: 6px;
}

.custom-weeks-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-weeks-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-weeks-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .section-label {
    color: #e4e7ed;
  }
  
  .range-separator,
  .range-unit {
    color: #c0c4cc;
  }
  
  .custom-weeks-grid {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .preview-content {
    background: #2d2d2d;
  }
  
  .preview-text {
    color: #e4e7ed;
  }
  
  .custom-weeks-grid::-webkit-scrollbar-track {
    background: #2d2d2d;
  }
  
  .custom-weeks-grid::-webkit-scrollbar-thumb {
    background: #4c4d4f;
  }
  
  .custom-weeks-grid::-webkit-scrollbar-thumb:hover {
    background: #606266;
  }
}
</style>
