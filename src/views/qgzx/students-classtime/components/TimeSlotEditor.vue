<!--
  时间段编辑器组件
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <el-drawer
    v-model="visible"
    :title="isUpdate ? '编辑课表时间' : '添加课表时间'"
    size="600px"
    :before-close="handleClose"
  >
    <div class="editor-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          
          <el-form-item label="星期" prop="weekDay">
            <el-select
              v-model="form.weekDay"
              placeholder="请选择星期"
              style="width: 100%"
            >
              <el-option
                v-for="day in weekDays"
                :key="day.value"
                :label="day.label"
                :value="day.value"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="时间段" prop="ksbId">
            <el-select
              v-model="form.ksbId"
              placeholder="请选择时间段"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="slot in availableTimeSlots"
                :key="slot.id"
                :label="`${slot.name} (${slot.kssj}-${slot.jssj})`"
                :value="slot.id"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 周次设置 -->
        <div class="form-section">
          <h4 class="section-title">周次设置</h4>
          
          <week-selector
            v-model="weekConfig"
            @change="handleWeekConfigChange"
          />
        </div>

        <!-- 预览信息 -->
        <div class="form-section">
          <h4 class="section-title">预览信息</h4>
          
          <div class="preview-card">
            <div class="preview-item">
              <span class="preview-label">时间：</span>
              <span class="preview-value">{{ previewInfo.time }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">星期：</span>
              <span class="preview-value">{{ previewInfo.weekDay }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">周次：</span>
              <span class="preview-value">{{ previewInfo.weeks }}</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">课程：</span>
              <span class="preview-value">{{ previewInfo.course }}</span>
            </div>
          </div>
        </div>

        <!-- 冲突检测 -->
        <div v-if="conflicts.length > 0" class="form-section">
          <h4 class="section-title">冲突检测</h4>
          
          <el-alert
            title="检测到时间冲突"
            type="warning"
            :closable="false"
            show-icon
          >
            <div class="conflict-list">
              <div
                v-for="conflict in conflicts"
                :key="conflict.id"
                class="conflict-item"
              >
                <el-icon class="conflict-icon"><Warning /></el-icon>
                <span>{{ conflict.message }}</span>
              </div>
            </div>
          </el-alert>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          :disabled="hasBlockingConflicts"
          @click="handleSave"
        >
          {{ hasBlockingConflicts ? '存在冲突，无法保存' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { Warning } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import WeekSelector from './WeekSelector.vue';
import { operation, queryQgzXsAvailability } from '../api/index.js';
import { getWeekDayName } from '../utils/timeUtils.js';
import { WeekType, formatWeekText } from '../utils/scheduleUtils.js';
import { detectConflicts, hasBlockingConflicts as checkBlockingConflicts } from '../utils/conflictDetector.js';

const props = defineProps({
  /** 是否显示 */
  modelValue: {
    type: Boolean,
    default: false
  },
  /** 编辑数据 */
  data: {
    type: Object,
    default: null
  },
  /** 可用时间段 */
  timeSlots: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'done']);

// 响应式数据
const visible = ref(false);
const saving = ref(false);
const formRef = ref(null);
const allScheduleData = ref([]); // 所有课表数据，用于冲突检测

// 表单数据
const form = reactive({
  id: null,
  weekDay: null,
  ksbId: null
});

// 周次配置
const weekConfig = ref({
  zcType: WeekType.ALL,
  zcStart: 1,
  zcEnd: 20,
  zcCustom: null
});

// 周几选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 表单验证规则
const rules = {
  weekDay: [
    { required: true, message: '请选择星期', trigger: 'change' }
  ],
  ksbId: [
    { required: true, message: '请选择时间段', trigger: 'change' }
  ]
};

// 计算属性
const isUpdate = computed(() => !!form.id);

const availableTimeSlots = computed(() => {
  return props.timeSlots.filter(slot => slot.timeType === 'COURSE');
});

const selectedTimeSlot = computed(() => {
  return availableTimeSlots.value.find(slot => slot.id === form.ksbId);
});

const previewInfo = computed(() => {
  const timeSlot = selectedTimeSlot.value;
  const weekDay = weekDays.find(day => day.value === form.weekDay);
  
  return {
    time: timeSlot ? `${timeSlot.kssj}-${timeSlot.jssj}` : '未选择',
    weekDay: weekDay ? weekDay.label : '未选择',
    weeks: formatWeekText({
      zcType: weekConfig.value.zcType,
      zcStart: weekConfig.value.zcStart,
      zcEnd: weekConfig.value.zcEnd,
      zcCustom: weekConfig.value.zcCustom
    }),
    course: timeSlot ? timeSlot.name : '未选择'
  };
});

// 冲突检测
const conflicts = computed(() => {
  if (!form.weekDay || !form.ksbId) return [];
  
  const currentItem = {
    id: form.id,
    weekDay: form.weekDay,
    ksbId: form.ksbId,
    zcType: weekConfig.value.zcType,
    zcStart: weekConfig.value.zcStart,
    zcEnd: weekConfig.value.zcEnd,
    zcCustom: weekConfig.value.zcCustom
  };
  
  const ksbMap = {};
  props.timeSlots.forEach(slot => {
    ksbMap[slot.id] = slot;
  });
  
  return detectConflicts(allScheduleData.value, currentItem, ksbMap);
});

const hasBlockingConflicts = computed(() => {
  return checkBlockingConflicts(conflicts.value);
});

// 方法
const handleWeekConfigChange = (config) => {
  weekConfig.value = config;
};

const handleClose = () => {
  visible.value = false;
  emit('update:modelValue', false);
};

const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate();
    
    if (hasBlockingConflicts.value) {
      ElMessage.error('存在冲突，无法保存');
      return;
    }
    
    saving.value = true;
    
    // 构建保存数据
    const saveData = {
      id: form.id,
      weekDay: form.weekDay,
      ksbId: form.ksbId,
      zcType: weekConfig.value.zcType,
      zcStart: weekConfig.value.zcStart,
      zcEnd: weekConfig.value.zcEnd,
      zcCustom: weekConfig.value.zcCustom
    };
    
    await operation(saveData);
    
    ElMessage.success(isUpdate.value ? '修改成功' : '添加成功');
    
    handleClose();
    emit('done');
    
  } catch (error) {
    ElMessage.error(error.message || '保存失败');
  } finally {
    saving.value = false;
  }
};

const resetForm = () => {
  Object.assign(form, {
    id: null,
    weekDay: null,
    ksbId: null
  });
  
  weekConfig.value = {
    zcType: WeekType.ALL,
    zcStart: 1,
    zcEnd: 20,
    zcCustom: null
  };
  
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

const loadScheduleData = async () => {
  try {
    const result = await queryQgzXsAvailability({});
    allScheduleData.value = result || [];
  } catch (error) {
    console.warn('加载课表数据失败:', error);
    allScheduleData.value = [];
  }
};

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    loadScheduleData();
    
    if (props.data) {
      // 编辑模式
      Object.assign(form, {
        id: props.data.id,
        weekDay: props.data.weekDay,
        ksbId: props.data.ksbId
      });
      
      weekConfig.value = {
        zcType: props.data.zcType || WeekType.ALL,
        zcStart: props.data.zcStart || 1,
        zcEnd: props.data.zcEnd || 20,
        zcCustom: props.data.zcCustom
      };
    } else {
      // 新建模式
      resetForm();
    }
  }
});

watch(visible, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});
</script>

<style scoped>
.editor-container {
  height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 0 16px;
}

.form-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-card {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
}

.preview-item {
  display: flex;
  margin-bottom: 8px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-label {
  width: 60px;
  color: #909399;
  font-weight: 500;
}

.preview-value {
  color: #303133;
  flex: 1;
}

.conflict-list {
  margin-top: 8px;
}

.conflict-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.conflict-item:last-child {
  margin-bottom: 0;
}

.conflict-icon {
  color: #e6a23c;
  margin-right: 8px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-container {
    padding: 0 12px;
  }
  
  .form-section {
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .preview-card {
    padding: 12px;
  }
  
  .preview-label {
    width: 50px;
    font-size: 12px;
  }
  
  .preview-value {
    font-size: 12px;
  }
  
  .drawer-footer {
    padding: 12px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: #e4e7ed;
  }
  
  .preview-card {
    background: #2d2d2d;
  }
  
  .preview-label {
    color: #c0c4cc;
  }
  
  .preview-value {
    color: #e4e7ed;
  }
  
  .drawer-footer {
    border-top-color: #4c4d4f;
  }
}
</style>
