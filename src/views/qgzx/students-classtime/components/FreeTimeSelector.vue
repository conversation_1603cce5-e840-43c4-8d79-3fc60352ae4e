<!--
  空闲时间选择器组件
  专门处理FREE类型的时间段选择
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <div class="free-time-selector">
    <!-- 选择模式切换 -->
    <div class="selector-header">
      <div class="header-title">
        <el-icon><Clock /></el-icon>
        <span>空闲时间安排</span>
      </div>
      <div class="header-actions">
        <el-button-group>
          <el-button 
            :type="selectionMode === 'single' ? 'primary' : ''" 
            size="small"
            @click="setSelectionMode('single')"
          >
            单选模式
          </el-button>
          <el-button 
            :type="selectionMode === 'multiple' ? 'primary' : ''" 
            size="small"
            @click="setSelectionMode('multiple')"
          >
            多选模式
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 空闲时间网格 -->
    <div class="free-time-grid">
      <!-- 时间轴 -->
      <div class="time-axis">
        <div class="time-header">时间段</div>
        <div 
          v-for="timeSlot in freeTimeSlots" 
          :key="timeSlot.id" 
          class="time-slot"
        >
          <div class="time-label">{{ timeSlot.name }}</div>
          <div class="time-range">{{ timeSlot.kssj }}-{{ timeSlot.jssj }}</div>
        </div>
      </div>

      <!-- 周几列 -->
      <div 
        v-for="day in weekDays" 
        :key="day.value" 
        class="day-column"
      >
        <div class="day-header">{{ day.label }}</div>
        <div 
          v-for="timeSlot in freeTimeSlots" 
          :key="`${day.value}-${timeSlot.id}`"
          class="free-time-cell"
          :class="getCellClasses(day.value, timeSlot.id)"
          @click="handleCellClick(day.value, timeSlot.id)"
        >
          <!-- 选择状态指示器 -->
          <div class="selection-indicator">
            <el-icon v-if="isCellSelected(day.value, timeSlot.id)">
              <Check />
            </el-icon>
          </div>
          
          <!-- 已有安排显示 -->
          <div 
            v-if="hasExistingSchedule(day.value, timeSlot.id)" 
            class="existing-schedule"
          >
            <div class="schedule-info">
              {{ getExistingScheduleInfo(day.value, timeSlot.id) }}
            </div>
          </div>
          
          <!-- 空闲状态提示 -->
          <div v-else class="free-status">
            <span class="free-text">空闲</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择摘要 -->
    <div v-if="selectedCells.length > 0" class="selection-summary">
      <div class="summary-header">
        <span>已选择 {{ selectedCells.length }} 个时间段</span>
        <el-button size="small" type="text" @click="clearSelection">清空选择</el-button>
      </div>
      <div class="summary-list">
        <el-tag
          v-for="cell in selectedCells.slice(0, 10)"
          :key="`${cell.weekDay}-${cell.timeSlotId}`"
          size="small"
          closable
          @close="removeCellSelection(cell.weekDay, cell.timeSlotId)"
        >
          {{ getWeekDayName(cell.weekDay) }} {{ getTimeSlotName(cell.timeSlotId) }}
        </el-tag>
        <el-tag v-if="selectedCells.length > 10" size="small" type="info">
          ...还有{{ selectedCells.length - 10 }}个
        </el-tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="selector-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        :disabled="selectedCells.length === 0"
        @click="handleConfirm"
      >
        确认选择 ({{ selectedCells.length }})
      </el-button>
    </div>

    <!-- 使用说明 -->
    <div class="usage-tips">
      <el-alert
        title="使用说明"
        type="info"
        :closable="false"
        show-icon
      >
        <ul>
          <li><strong>空闲时间</strong>：可以自由安排的时间段，用于勤工助学、自习等</li>
          <li><strong>单选模式</strong>：每次只能选择一个时间段</li>
          <li><strong>多选模式</strong>：可以同时选择多个时间段</li>
          <li><strong>已占用</strong>：显示灰色，表示该时间段已有安排</li>
          <li><strong>可选择</strong>：显示绿色边框，点击可选择</li>
        </ul>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Clock, Check } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { queryFreeTimeSlots } from '../api/index.js';
import { getWeekDayName } from '../utils/timeUtils.js';

const props = defineProps({
  /** 是否显示 */
  modelValue: {
    type: Boolean,
    default: false
  },
  /** 已有的课表数据 */
  existingSchedule: {
    type: Array,
    default: () => []
  },
  /** 默认选中的时间段 */
  defaultSelection: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

// 响应式数据
const selectionMode = ref('single'); // 'single' | 'multiple'
const selectedCells = ref([]); // 选中的时间段
const freeTimeSlots = ref([]); // 空闲时间段数据

// 周几选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 计算属性
const existingScheduleMap = computed(() => {
  const map = {};
  props.existingSchedule.forEach(schedule => {
    const key = `${schedule.weekDay}-${schedule.ksbId}`;
    map[key] = schedule;
  });
  return map;
});

// 方法
const setSelectionMode = (mode) => {
  selectionMode.value = mode;
  if (mode === 'single' && selectedCells.value.length > 1) {
    // 切换到单选模式时，只保留第一个选择
    selectedCells.value = selectedCells.value.slice(0, 1);
  }
};

const isCellSelected = (weekDay, timeSlotId) => {
  return selectedCells.value.some(cell => 
    cell.weekDay === weekDay && cell.timeSlotId === timeSlotId
  );
};

const hasExistingSchedule = (weekDay, timeSlotId) => {
  const key = `${weekDay}-${timeSlotId}`;
  return !!existingScheduleMap.value[key];
};

const getExistingScheduleInfo = (weekDay, timeSlotId) => {
  const key = `${weekDay}-${timeSlotId}`;
  const schedule = existingScheduleMap.value[key];
  return schedule ? '已安排' : '';
};

const getCellClasses = (weekDay, timeSlotId) => {
  return {
    'selected': isCellSelected(weekDay, timeSlotId),
    'occupied': hasExistingSchedule(weekDay, timeSlotId),
    'available': !hasExistingSchedule(weekDay, timeSlotId)
  };
};

const getTimeSlotName = (timeSlotId) => {
  const timeSlot = freeTimeSlots.value.find(slot => slot.id === timeSlotId);
  return timeSlot ? timeSlot.name : '';
};

const handleCellClick = (weekDay, timeSlotId) => {
  // 如果已有安排，不允许选择
  if (hasExistingSchedule(weekDay, timeSlotId)) {
    ElMessage.warning('该时间段已有安排，无法选择');
    return;
  }

  const cellIndex = selectedCells.value.findIndex(cell => 
    cell.weekDay === weekDay && cell.timeSlotId === timeSlotId
  );

  if (cellIndex >= 0) {
    // 取消选择
    selectedCells.value.splice(cellIndex, 1);
  } else {
    // 添加选择
    if (selectionMode.value === 'single') {
      selectedCells.value = [{ weekDay, timeSlotId }];
    } else {
      selectedCells.value.push({ weekDay, timeSlotId });
    }
  }
};

const removeCellSelection = (weekDay, timeSlotId) => {
  const cellIndex = selectedCells.value.findIndex(cell => 
    cell.weekDay === weekDay && cell.timeSlotId === timeSlotId
  );
  if (cellIndex >= 0) {
    selectedCells.value.splice(cellIndex, 1);
  }
};

const clearSelection = () => {
  selectedCells.value = [];
};

const handleConfirm = () => {
  if (selectedCells.value.length === 0) {
    ElMessage.warning('请至少选择一个时间段');
    return;
  }

  emit('confirm', {
    selectedCells: selectedCells.value,
    selectionMode: selectionMode.value
  });
};

const handleCancel = () => {
  emit('cancel');
};

const loadFreeTimeSlots = async () => {
  try {
    const result = await queryFreeTimeSlots({});
    freeTimeSlots.value = result || [];
  } catch (error) {
    ElMessage.error('加载空闲时间段失败');
    console.error(error);
  }
};

// 初始化默认选择
const initializeDefaultSelection = () => {
  if (props.defaultSelection && props.defaultSelection.length > 0) {
    selectedCells.value = [...props.defaultSelection];
  }
};

// 生命周期
onMounted(() => {
  loadFreeTimeSlots();
});

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initializeDefaultSelection();
  } else {
    clearSelection();
  }
});

watch(() => props.defaultSelection, initializeDefaultSelection, { deep: true });
</script>

<style scoped>
.free-time-selector {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.free-time-grid {
  display: flex;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
}

.time-axis {
  min-width: 120px;
  background: #f5f7fa;
  border-right: 1px solid #e4e7ed;
}

.time-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  background: #f0f2f5;
}

.time-slot {
  padding: 8px;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.time-label {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
}

.time-range {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
}

.day-column {
  flex: 1;
  min-width: 100px;
  border-right: 1px solid #e4e7ed;
}

.day-column:last-child {
  border-right: none;
}

.day-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  background: #f0f2f5;
}

.free-time-cell {
  position: relative;
  min-height: 60px;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.free-time-cell.available {
  background: #f0f9ff;
  border-left: 3px solid transparent;
}

.free-time-cell.available:hover {
  background: #e6f7ff;
  border-left-color: #1890ff;
}

.free-time-cell.selected {
  background: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.free-time-cell.occupied {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

.selection-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  color: #52c41a;
  font-size: 14px;
}

.existing-schedule {
  text-align: center;
}

.schedule-info {
  font-size: 12px;
  color: #666;
}

.free-status {
  text-align: center;
}

.free-text {
  font-size: 12px;
  color: #52c41a;
}

.selection-summary {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #303133;
}

.summary-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.selector-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-bottom: 16px;
}

.usage-tips {
  margin-top: 16px;
}

.usage-tips ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.usage-tips li {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .selector-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .time-axis {
    min-width: 80px;
  }
  
  .day-column {
    min-width: 60px;
  }
  
  .free-time-cell {
    min-height: 40px;
  }
  
  .time-slot {
    min-height: 40px;
    padding: 4px;
  }
  
  .time-label {
    font-size: 10px;
  }
  
  .time-range {
    font-size: 8px;
  }
}
</style>
