<!-- 试卷试题组编辑弹窗 -->
<template>
  <ele-drawer :size="530"
              :title="isUpdate ? '修改学生空余时间 ['+data.name+']' : '添加学生空余时间'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="学工号" prop="name">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.xgh"
                  placeholder="请输入学工号"/>
      </el-form-item>
      <el-form-item label="工作日" prop="weekday">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.weekday"
                  placeholder="请输入工作日"/>
      </el-form-item>
      <el-form-item label="开始时间" prop="kssj">
        <el-date-picker class="ele-fluid"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择开始时间"
                        v-model="form.kssj"/>
      </el-form-item>
      <el-form-item label="结束时间" prop="jssj">
        <el-date-picker class="ele-fluid"
                        value-format="YYYY-MM-DD"
                        placeholder="请选择结束时间"
                        v-model="form.jssj"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from "../api/index.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  name: '',
  yzgbc: void 0,
  yzdgs: void 0,
});

/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  yzgbc: [
    {
      message: '请输入每月最高报酬',
      type: 'number',
      trigger: 'blur'
    }
  ],
  yzdgs: [
    {
      message: '请输入月最大工时',
      type: 'number',
      trigger: 'blur'
    }
  ]
});

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {...form}
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields(props.data);
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
