<!--
  批量操作组件
  <AUTHOR>
  @since 2025-07-28
-->
<template>
  <el-dialog
    v-model="visible"
    title="批量操作"
    width="800px"
    :before-close="handleClose"
  >
    <div class="batch-operations-container">
      <!-- 操作类型选择 -->
      <div class="operation-type-section">
        <h4 class="section-title">选择操作类型</h4>
        <el-radio-group v-model="operationType" @change="handleOperationTypeChange">
          <el-radio label="copy">复制到其他周次</el-radio>
          <el-radio label="move">移动到其他时间</el-radio>
          <el-radio label="modify">批量修改周次</el-radio>
          <el-radio label="delete">批量删除</el-radio>
        </el-radio-group>
      </div>

      <!-- 选中项目预览 -->
      <div class="selected-items-section">
        <h4 class="section-title">选中的项目 ({{ selectedItems.length }}个)</h4>
        <div class="selected-items-list">
          <div
            v-for="item in selectedItems"
            :key="item.id"
            class="selected-item"
          >
            <div class="item-info">
              <span class="item-name">{{ getItemName(item) }}</span>
              <span class="item-time">{{ getItemTime(item) }}</span>
              <span class="item-week">{{ getItemWeek(item) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作配置 -->
      <div class="operation-config-section">
        <h4 class="section-title">操作配置</h4>
        
        <!-- 复制操作 -->
        <div v-if="operationType === 'copy'" class="config-content">
          <el-form :model="copyConfig" label-width="100px">
            <el-form-item label="目标周次">
              <week-selector
                v-model="copyConfig.targetWeeks"
                @change="handleCopyConfigChange"
              />
            </el-form-item>
            <el-form-item label="冲突处理">
              <el-radio-group v-model="copyConfig.conflictStrategy">
                <el-radio label="skip">跳过冲突项</el-radio>
                <el-radio label="replace">替换冲突项</el-radio>
                <el-radio label="cancel">取消操作</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>

        <!-- 移动操作 -->
        <div v-if="operationType === 'move'" class="config-content">
          <el-form :model="moveConfig" label-width="100px">
            <el-form-item label="目标星期">
              <el-select v-model="moveConfig.targetWeekDay" placeholder="请选择星期">
                <el-option
                  v-for="day in weekDays"
                  :key="day.value"
                  :label="day.label"
                  :value="day.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="目标时间段">
              <el-select v-model="moveConfig.targetTimeSlot" placeholder="请选择时间段">
                <el-option
                  v-for="slot in timeSlots"
                  :key="slot.id"
                  :label="`${slot.name} (${slot.kssj}-${slot.jssj})`"
                  :value="slot.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>

        <!-- 修改操作 -->
        <div v-if="operationType === 'modify'" class="config-content">
          <el-form :model="modifyConfig" label-width="100px">
            <el-form-item label="新周次设置">
              <week-selector
                v-model="modifyConfig.newWeeks"
                @change="handleModifyConfigChange"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 删除操作 -->
        <div v-if="operationType === 'delete'" class="config-content">
          <el-alert
            title="警告"
            type="warning"
            :closable="false"
            show-icon
          >
            <p>确定要删除选中的 {{ selectedItems.length }} 个时间段吗？</p>
            <p>此操作不可撤销，请谨慎操作。</p>
          </el-alert>
        </div>
      </div>

      <!-- 预览结果 -->
      <div v-if="previewResults.length > 0" class="preview-section">
        <h4 class="section-title">操作预览</h4>
        <div class="preview-list">
          <div
            v-for="result in previewResults.slice(0, 10)"
            :key="result.id"
            class="preview-item"
            :class="{ 'has-conflict': result.hasConflict }"
          >
            <div class="preview-info">
              <span class="preview-action">{{ result.action }}</span>
              <span class="preview-detail">{{ result.detail }}</span>
            </div>
            <div v-if="result.hasConflict" class="conflict-warning">
              <el-icon><Warning /></el-icon>
              <span>冲突</span>
            </div>
          </div>
          <div v-if="previewResults.length > 10" class="preview-more">
            还有 {{ previewResults.length - 10 }} 项...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="executing"
          :disabled="!canExecute"
          @click="handleExecute"
        >
          {{ getExecuteButtonText() }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { Warning } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import WeekSelector from './WeekSelector.vue';
import { saveBatch, removes } from '../api/index.js';
import { getWeekDayName } from '../utils/timeUtils.js';
import { WeekType, formatWeekText } from '../utils/scheduleUtils.js';

const props = defineProps({
  /** 是否显示 */
  modelValue: {
    type: Boolean,
    default: false
  },
  /** 选中的项目 */
  selectedItems: {
    type: Array,
    default: () => []
  },
  /** 可用时间段 */
  timeSlots: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'done']);

// 响应式数据
const visible = ref(false);
const executing = ref(false);
const operationType = ref('copy');

// 操作配置
const copyConfig = reactive({
  targetWeeks: {
    zcType: WeekType.ALL,
    zcStart: 1,
    zcEnd: 20,
    zcCustom: null
  },
  conflictStrategy: 'skip'
});

const moveConfig = reactive({
  targetWeekDay: null,
  targetTimeSlot: null
});

const modifyConfig = reactive({
  newWeeks: {
    zcType: WeekType.ALL,
    zcStart: 1,
    zcEnd: 20,
    zcCustom: null
  }
});

// 周几选项
const weekDays = [
  { value: 1, label: '周一' },
  { value: 2, label: '周二' },
  { value: 3, label: '周三' },
  { value: 4, label: '周四' },
  { value: 5, label: '周五' },
  { value: 6, label: '周六' },
  { value: 7, label: '周日' }
];

// 计算属性
const canExecute = computed(() => {
  switch (operationType.value) {
    case 'copy':
      return true; // 复制操作总是可以执行
    case 'move':
      return moveConfig.targetWeekDay && moveConfig.targetTimeSlot;
    case 'modify':
      return true; // 修改操作总是可以执行
    case 'delete':
      return props.selectedItems.length > 0;
    default:
      return false;
  }
});

const previewResults = computed(() => {
  const results = [];
  
  switch (operationType.value) {
    case 'copy':
      props.selectedItems.forEach(item => {
        results.push({
          id: `copy-${item.id}`,
          action: '复制',
          detail: `${getItemName(item)} → ${formatWeekText(copyConfig.targetWeeks)}`,
          hasConflict: false // TODO: 实际冲突检测
        });
      });
      break;
      
    case 'move':
      if (moveConfig.targetWeekDay && moveConfig.targetTimeSlot) {
        const targetSlot = props.timeSlots.find(slot => slot.id === moveConfig.targetTimeSlot);
        const targetDay = weekDays.find(day => day.value === moveConfig.targetWeekDay);
        
        props.selectedItems.forEach(item => {
          results.push({
            id: `move-${item.id}`,
            action: '移动',
            detail: `${getItemName(item)} → ${targetDay?.label} ${targetSlot?.name}`,
            hasConflict: false // TODO: 实际冲突检测
          });
        });
      }
      break;
      
    case 'modify':
      props.selectedItems.forEach(item => {
        results.push({
          id: `modify-${item.id}`,
          action: '修改',
          detail: `${getItemName(item)} → ${formatWeekText(modifyConfig.newWeeks)}`,
          hasConflict: false
        });
      });
      break;
      
    case 'delete':
      props.selectedItems.forEach(item => {
        results.push({
          id: `delete-${item.id}`,
          action: '删除',
          detail: getItemName(item),
          hasConflict: false
        });
      });
      break;
  }
  
  return results;
});

// 方法
const getItemName = (item) => {
  const timeSlot = props.timeSlots.find(slot => slot.id === item.ksbId);
  const weekDay = weekDays.find(day => day.value === item.weekDay);
  return `${weekDay?.label} ${timeSlot?.name || '未知课程'}`;
};

const getItemTime = (item) => {
  const timeSlot = props.timeSlots.find(slot => slot.id === item.ksbId);
  return timeSlot ? `${timeSlot.kssj}-${timeSlot.jssj}` : '';
};

const getItemWeek = (item) => {
  return formatWeekText(item);
};

const getExecuteButtonText = () => {
  const actionTexts = {
    copy: '执行复制',
    move: '执行移动',
    modify: '执行修改',
    delete: '确认删除'
  };
  return actionTexts[operationType.value] || '执行操作';
};

const handleOperationTypeChange = () => {
  // 重置配置
  Object.assign(copyConfig, {
    targetWeeks: {
      zcType: WeekType.ALL,
      zcStart: 1,
      zcEnd: 20,
      zcCustom: null
    },
    conflictStrategy: 'skip'
  });
  
  Object.assign(moveConfig, {
    targetWeekDay: null,
    targetTimeSlot: null
  });
  
  Object.assign(modifyConfig, {
    newWeeks: {
      zcType: WeekType.ALL,
      zcStart: 1,
      zcEnd: 20,
      zcCustom: null
    }
  });
};

const handleCopyConfigChange = (config) => {
  copyConfig.targetWeeks = config;
};

const handleModifyConfigChange = (config) => {
  modifyConfig.newWeeks = config;
};

const handleClose = () => {
  visible.value = false;
  emit('update:modelValue', false);
};

const handleExecute = async () => {
  try {
    // 确认操作
    if (operationType.value === 'delete') {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${props.selectedItems.length} 个时间段吗？`,
        '确认删除',
        { type: 'warning' }
      );
    }
    
    executing.value = true;
    
    switch (operationType.value) {
      case 'copy':
        await executeCopy();
        break;
      case 'move':
        await executeMove();
        break;
      case 'modify':
        await executeModify();
        break;
      case 'delete':
        await executeDelete();
        break;
    }
    
    ElMessage.success('操作执行成功');
    handleClose();
    emit('done');
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作执行失败');
    }
  } finally {
    executing.value = false;
  }
};

const executeCopy = async () => {
  const newItems = props.selectedItems.map(item => ({
    ...item,
    id: null, // 新建项目
    zcType: copyConfig.targetWeeks.zcType,
    zcStart: copyConfig.targetWeeks.zcStart,
    zcEnd: copyConfig.targetWeeks.zcEnd,
    zcCustom: copyConfig.targetWeeks.zcCustom
  }));
  
  await saveBatch(newItems);
};

const executeMove = async () => {
  const updatedItems = props.selectedItems.map(item => ({
    ...item,
    weekDay: moveConfig.targetWeekDay,
    ksbId: moveConfig.targetTimeSlot
  }));
  
  await saveBatch(updatedItems);
};

const executeModify = async () => {
  const updatedItems = props.selectedItems.map(item => ({
    ...item,
    zcType: modifyConfig.newWeeks.zcType,
    zcStart: modifyConfig.newWeeks.zcStart,
    zcEnd: modifyConfig.newWeeks.zcEnd,
    zcCustom: modifyConfig.newWeeks.zcCustom
  }));
  
  await saveBatch(updatedItems);
};

const executeDelete = async () => {
  const ids = props.selectedItems.map(item => item.id);
  await removes(ids);
};

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    // 重置操作类型和配置
    operationType.value = 'copy';
    handleOperationTypeChange();
  }
});

watch(visible, (newVal) => {
  if (!newVal) {
    // 清理状态
    executing.value = false;
  }
});
</script>

<style scoped>
.batch-operations-container {
  max-height: 600px;
  overflow-y: auto;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.operation-type-section,
.selected-items-section,
.operation-config-section,
.preview-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.operation-type-section:last-child,
.selected-items-section:last-child,
.operation-config-section:last-child,
.preview-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.selected-items-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.selected-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.selected-item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  gap: 12px;
  font-size: 14px;
}

.item-name {
  font-weight: 500;
  color: #303133;
  min-width: 120px;
}

.item-time {
  color: #606266;
  min-width: 100px;
}

.item-week {
  color: #909399;
  flex: 1;
}

.config-content {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
}

.preview-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
  max-height: 300px;
  overflow-y: auto;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-item:last-child {
  border-bottom: none;
}

.preview-item.has-conflict {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.preview-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.preview-action {
  font-weight: 500;
  color: #409eff;
  min-width: 60px;
}

.preview-detail {
  color: #606266;
  flex: 1;
}

.conflict-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
}

.preview-more {
  padding: 8px 12px;
  text-align: center;
  color: #909399;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 滚动条样式 */
.selected-items-list::-webkit-scrollbar,
.preview-list::-webkit-scrollbar,
.batch-operations-container::-webkit-scrollbar {
  width: 6px;
}

.selected-items-list::-webkit-scrollbar-track,
.preview-list::-webkit-scrollbar-track,
.batch-operations-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.selected-items-list::-webkit-scrollbar-thumb,
.preview-list::-webkit-scrollbar-thumb,
.batch-operations-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.selected-items-list::-webkit-scrollbar-thumb:hover,
.preview-list::-webkit-scrollbar-thumb:hover,
.batch-operations-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .item-info {
    flex-direction: column;
    gap: 4px;
  }
  
  .item-name,
  .item-time {
    min-width: auto;
  }
  
  .preview-info {
    flex-direction: column;
    gap: 4px;
  }
  
  .preview-action {
    min-width: auto;
  }
  
  .config-content {
    padding: 12px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: #e4e7ed;
  }
  
  .selected-items-list,
  .preview-list {
    background: #2d2d2d;
    border-color: #4c4d4f;
  }
  
  .selected-item,
  .preview-item {
    border-bottom-color: #4c4d4f;
  }
  
  .item-name {
    color: #e4e7ed;
  }
  
  .item-time {
    color: #c0c4cc;
  }
  
  .item-week {
    color: #909399;
  }
  
  .config-content {
    background: #2d2d2d;
  }
  
  .preview-detail {
    color: #c0c4cc;
  }
  
  .preview-item.has-conflict {
    background: #3d2626;
  }
}
</style>
