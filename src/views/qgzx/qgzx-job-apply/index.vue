<template>
  <ele-page hide-footer flex-table>
    <search @search="reload"/>
    <ele-card flex-table :body-style="bodyStyle">
      <template #header>
        <ele-tabs
          type="tag"
          size="small"
          v-model="activeName"
          :items="tabsItems">
          <template #label="{ item, label }">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <div class="toolbar-container">
          <div class="view-switch">
            <el-radio-group v-model="displayMode" size="small">
              <el-radio-button value="fluid">
                <el-icon>
                  <Menu/>
                </el-icon>
                卡片视图
              </el-radio-button>
              <el-radio-button value="table">
                <el-icon>
                  <Grid/>
                </el-icon>
                表格视图
              </el-radio-button>
            </el-radio-group>
          </div>
          <el-button v-if="displayMode === 'fluid'" size="small" type="primary" @click="openEdit()" class="create-btn">
            <el-icon>
              <Plus/>
            </el-icon>
            新建
          </el-button>
        </div>
      </template>
      <!-- 表格模式 -->
      <ele-pro-table v-if="displayMode === 'table'"
                     ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #jobName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.jobName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
      <!-- 流体卡片模式 -->
      <div v-else>
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated/>
        </div>
        <template v-else>
          <div v-if="fluidData.length === 0" class="empty-container">
            <el-empty description="暂无数据"/>
          </div>
          <el-row v-else :gutter="10">
            <el-col v-for="item in fluidData"
                    :key="item.id"
                    :lg="8"
                    :md="12"
                    :sm="12"
                    :xs="24">
              <ele-card shadow="hover"
                        @click.stop="viewDetails(item)"
                        :style="{margin:'0!important'}"
                        :body-style="{ padding: '0 5px!important',marginTop: '8px',marginBottom: '0!important',cursor: 'pointer',overflow: 'hidden' }">
                <div :key="item.id">
                  <div class="list-item-body">
                    <div style="flex: 1">
                      <ele-text type="primary" size="md">
                        <IconPark name="app-store" size="18" strokeWidth="3"/>
                        {{ item.jobName }}
                      </ele-text>
                      <div style="margin-top: 3px; display: flex; align-items: center">
                        <el-tag
                          v-for="tag in item.tags"
                          :key="tag"
                          type="info"
                          size="small"
                          :disable-transitions="true"
                          style="margin-right: 8px"
                        >
                          {{ tag }}
                        </el-tag>
                      </div>
                      <div class="description-container">
                        <ele-ellipsis
                          :content="item.bz || '暂无描述'"
                          :max-line="1"
                          :tooltip="{
                            effect: 'light',
                            placement: 'top',
                            popperStyle: {
                              width: '380px',
                              maxWidth: '90%',
                              wordBreak: 'break-all',
                            },
                            bodyStyle: {
                              maxWidth: 'calc(100vw - 32px)',
                              maxHeight: '252px',
                              overflowY: 'auto',
                            },
                            offset: 4,
                          }">
                          <span class="ellipsis-text ">{{ item.bz || '暂无描述' }}</span>
                        </ele-ellipsis>
                      </div>
                      <div class="job-details">
                        <div class="detail-row">
                          <IconPark name="local" size="16" strokeWidth="3"/>
                          <span class="truncate-text">{{ item.xqmc || '未知校区' }}</span>
                        </div>
                        <div class="compact-time-display">
                          <div class="compact-time-item">
                            <IconPark name="calendar" size="16" strokeWidth="3"/>
                            <span class="truncate-text">{{ formatDateRange(item?.startDate, item?.endDate) }}</span>
                          </div>
                          <div class="compact-time-item">
                            <IconPark name="time" size="16" strokeWidth="3"/>
                            <span class="truncate-text">{{ item?.startTime }}~{{ item?.endTime }}</span>
                          </div>
                        </div>
                        <div class="time-grid">
                          <div class="time-item detail-row">
                            <IconPark name="plan" size="16" strokeWidth="3"/>
                            <span class="span-wrap">{{ formatWorkDays(item?.workDays) }}</span>
                          </div>
                        </div>
                      </div>
                      <div style="">
                        <ele-text type="secondary" style="flex: 1; " class="ellipsis-text">
                          {{ item.employer?.name }} 申请于 {{ item.createTime }}
                        </ele-text>
                      </div>

                      <div style="margin-top: 10px; display: flex; align-items: center">
                        <ele-tooltip content="每月工作小时数" placement="bottom" effect="light">
                          <ele-text :icon="Timer"
                                    type="placeholder"
                                    style="cursor: pointer">
                            &nbsp;{{ item.workHous }}
                          </ele-text>
                        </ele-tooltip>
                        <el-divider direction="vertical" style="margin: 0 14px"/>
                        <ele-tooltip content="时薪" placement="bottom" effect="light">
                          <ele-text :icon="Wallet"
                                    type="placeholder"
                                    style="cursor: pointer">
                            &nbsp;{{ item.hourlyRate }}
                          </ele-text>
                        </ele-tooltip>
                        <el-divider direction="vertical" style="margin: 0 14px"/>
                        <ele-tooltip content="月最高报酬" placement="bottom" effect="light">
                          <ele-text :icon="PriceTag"
                                    type="placeholder"
                                    style="cursor: pointer">
                            &nbsp;{{ item.yzgbc }}
                          </ele-text>
                        </ele-tooltip>
                      </div>
                      <!-- 动态操作按钮区域 -->
                      <div class="action-area">
                        <template v-if="item.spzt === '待审批'">
                          <el-button
                            size="small"
                            type="primary"
                            class="action-btn"
                            @click.stop="openEdit(item)">
                            <span>编辑</span>
                          </el-button>
                          <el-button
                            size="small"
                            type="danger"
                            class="action-btn"
                            @click.stop="handleDelete(item)">
                            <span>删除</span>
                          </el-button>
                        </template>

                        <template v-else-if="item.spzt === '通过'">
                          <el-button v-if="item.sfms==='是'"
                                     size="small"
                                     type="success"
                                     class="action-btn"
                                     @click.stop="viewInterviewRecords(item)">
                            <span>面试</span>
                          </el-button>
                          <el-button
                            size="small"
                            type="warning"
                            class="action-btn"
                            @click.stop="handleAttendance(item)">
                            <span>考勤</span>
                          </el-button>
                          <el-button
                            size="small"
                            type="primary"
                            class="action-btn"
                            @click.stop="handleSalary(item)">
                            <span>薪酬</span>
                          </el-button>
                        </template>

                        <template v-else>
                          <el-button
                            size="small"
                            type="primary"
                            class="action-btn"
                            @click.stop="openEdit(item)">
                            <span>编辑</span>
                          </el-button>
                          <el-button
                            size="small"
                            type="danger"
                            class="action-btn"
                            @click.stop="handleDelete(item)">
                            <span>删除</span>
                          </el-button>
                        </template>

                        <el-button
                          size="small"
                          class="action-btn"
                          @click.stop="viewDetails(item)">
                          <span>详情</span>
                        </el-button>
                      </div>

                    </div>
                  </div>
                </div>
              </ele-card>
            </el-col>
          </el-row>
          <div v-if="fluidData.length >0" class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :total="total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </template>
      </div>
    </ele-card>

    <edit v-model="showEdit" :data="editData" @done="reloadData"/>
    <JobDetailDrawer :modelValue="showDetail" enterType="views" :formModel="currentItem"
                     @update:modelValue="handleDetailDrawerClose"/>
    <InterviewDrawer :modelValue="showInterview" enterType="views" :jobId="currentItem?.id"
                     @update:modelValue="handleInterviewDrawerClose"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, watch, reactive} from 'vue';
import {Grid, Menu, PriceTag, Timer} from '@element-plus/icons-vue';
import {queryQgzxJobApplyPage, removes} from './api/index.js';
import {useRouter} from 'vue-router';
import {ElMessage, ElMessageBox} from 'element-plus';
import Edit from './components/edit.vue';
import {comColumns} from './utils/index.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {Wallet} from '@icon-park/vue-next';
import _ from 'lodash';
import IconPark from '@/components/IconPark/index.vue';
import {statusColor} from '@/utils/status-color/index.js';
import {formatDateRange, formatWorkDays} from '../utils/index.js';
import JobDetailDrawer from '@/views/qgzx/components/JobDetailDrawer.vue';
import Search from './components/search.vue';
import InterviewDrawer from '@/views/qgzx/interview-record/components/InterviewDrawer.vue';

const router = useRouter();
const {query} = router.currentRoute.value;
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

// 显示模式
const displayMode = ref('fluid'); // 'table' 或 'fluid'

// 数据相关
const fluidData = ref([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 表格相关
const tableRef = ref(null);
const selections = ref([]);
const columns = ref(comColumns());

// 标签页相关
const activeName = ref(null);
const tabsItems = ref([]);
const nodeStateArray = ref([]);
const checkedNodeId = ref(query?.currentNodeId ?? null);

// 编辑相关
const showEdit = ref(false);
const editData = ref(null);

// 详情相关
const showDetail = ref(false);
const currentItem = ref(null);

// 面试记录相关
const showInterview = ref(false);

const bodyStyle = computed(() => {
  return {
    overflow: 'hidden',
    ...(displayMode.value === 'fluid' ? {background: '#f0f2f5'} : {}),
    ...(displayMode.value === 'fluid' ? {padding: '0 0 10px !important'} : {padding: '0 10px 10px !important'}),
  };
});

// 计算属性 - 获取当前查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  limit: pageSize.value,
  spzt: activeName.value,
}));

// 初始化加载
onMounted(() => {
  tabsItems.value = statusColor();
  tabsItems.value.forEach(item => {
    item.name = item.label;
  });

  activeName.value = tabsItems.value[0].name;
});

/** 表格搜索参数 */
const searchRef = ref(null);
const lastWhere = ref({});

const reload = (where) => {
  lastWhere.value = where || {};
  // 重置页码
  currentPage.value = 1;
  // 根据当前显示模式执行不同的刷新逻辑
  if (displayMode.value === 'fluid') {
    // 流体卡片模式
    fetchData();
  } else {
    // 表格模式
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({page: 1, where});
    } else {
      tableRef.value?.reload?.();
    }
  }
};

// 修改fetchData函数以考虑搜索条件
const fetchData = _.debounce(async () => {
  if (!activeName.value) return;
  try {
    loading.value = true;
    const params = {
      ...queryParams.value,
      ...lastWhere.value,
    };
    const res = await queryQgzxJobApplyPage(params);
    fluidData.value = res.list || [];
    total.value = res.count || 0;
  } catch (e) {
    ElMessage.error(e.message);
  } finally {
    loading.value = false;
  }
}, 300);

// 切换显示模式时重新加载数据
watch(displayMode, (newVal) => {
  currentPage.value = 1;
  if (activeName.value) {
    if (newVal === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
});

// 监听activeName变化
watch(activeName, (newVal) => {
  if (newVal) {
    currentPage.value = 1;
    if (displayMode.value === 'fluid') {
      fetchData();
    } else {
      tableRef.value?.reload?.();
    }
  }
}, {immediate: false});

// 分页变化
const handlePageChange = () => {
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  if (displayMode.value === 'fluid') {
    fetchData();
  }
};

// 打开编辑
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

// 查看详情
const viewDetails = (row) => {
  currentItem.value = row;
  currentItem.value = {
    ...row,
    workDays: typeof row?.workDays === 'string'
      ? row.workDays.split(',')
      : Array.isArray(row?.workDays)
        ? row.workDays
        : [],
    Addresses: row?.jobAddresses?.[0]?.name || '-',
  };
  showDetail.value = true;
};

// 处理子抽屉关闭事件
const handleDetailDrawerClose = (value) => {
  showDetail.value = value;
};
const handleInterviewDrawerClose = (value) => {
  showInterview.value = value;
};

// 查看面试记录
const viewInterviewRecords = (row) => {
  currentItem.value = row;
  showInterview.value = true;
};

// 删除操作
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该记录吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await removes([row.id]);
    ElMessage.success('删除成功');
    reloadData();
  } catch (e) {
    if (e !== 'cancel') {
      ElMessage.error(e.message || '删除失败');
    }
  }
};

// 考勤管理
const handleAttendance = (row) => {
  router.push({
    path: '/attendance',
    query: {jobId: row.id},
  });
};

// 薪酬管理
const handleSalary = (row) => {
  router.push({
    path: '/salary',
    query: {jobId: row.id},
  });
};

// 重新加载数据
const reloadData = () => {
  currentPage.value = 1;
  if (displayMode.value === 'fluid') {
    fetchData();
  } else {
    tableRef.value?.reload?.();
  }
};

// 表格数据源
const datasource = ({page, limit, where, orders, filters}) => {
  if (!activeName.value) {
    return Promise.resolve({
      data: [],
      total: 0,
    });
  }
  return queryQgzxJobApplyPage({
    ...where,
    ...orders,
    ...filters,
    page,
    limit,
    spzt: activeName.value,
  });
};

</script>
<style scoped>
.description-container {
  margin-top: 6px;
}

.ellipsis-text {
  color: #555;
  font-size: 14px;
  line-height: 1.5;
}

.toolbar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-switch {
  margin-right: 12px;
}

.create-btn {
  margin-left: auto;
}

@media screen and (max-width: 768px) {
  .toolbar-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .view-switch {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .create-btn {
    margin-left: 0;
    width: 100%;
  }
}

.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid var(--el-border-color-light);
}

.list-item-body {
  padding: 10px;
}

.action-area {
  cursor: pointer;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px dashed #eee;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media screen and (max-width: 576px) {
  .list-item-body {
    display: block;
  }

  .action-area {
    flex-direction: column;
  }
}


.job-details {
  margin-top: 12px;
}

.detail-row {
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #555;
}

.detail-row .span-wrap {
  padding-left: 10px;
  margin-right: 8px;
  color: #555;
  font-size: 14px;
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}

.compact-time-display {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.compact-time-item {
  display: -webkit-box; /* 兼容旧版 QQ 浏览器 */
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 12px;
  padding: 6px 8px 6px 0;
  -webkit-border-radius: 6px;
  border-radius: 6px;
}

.truncate-text {
  padding-left: 10px;
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  font-size: 14px;
  color: #555;
}

@media screen and (max-width: 768px) {
  .compact-time-display {
    flex-direction: column;
    gap: 4px;
  }

  .truncate-text {
    max-width: 100%;
    white-space: normal;
  }
}

.time-grid {
  margin-top: 8px;
}

.time-item {
  display: -webkit-box; /* 兼容旧版 QQ 浏览器 */
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 6px 8px 6px 0;
  -webkit-border-radius: 6px;
  border-radius: 6px;
}

.action-area {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px dashed var(--el-border-color-light);
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.action-btn {
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 7px 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-btn .el-icon {
  margin-right: 5px;
  font-size: 14px;
}

.action-btn span {
  font-size: 13px;
  font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .action-area {
    flex-direction: column;
    gap: 10px;
  }

  .btn-group {
    width: 100%;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 按钮颜色微调 */
.el-button--primary.action-btn {
  background: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.el-button--success.action-btn {
  background: var(--el-color-success);
  border-color: var(--el-color-success);
}

.el-button--warning.action-btn {
  background: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

.el-button--danger.action-btn {
  background: var(--el-color-danger);
  border-color: var(--el-color-danger);
}

.el-button--info.action-btn {
  background: var(--el-color-info);
  border-color: var(--el-color-info);
}

</style>
