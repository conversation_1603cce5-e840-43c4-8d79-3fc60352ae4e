export function comColumns() {
  return [
    // Selection column for table operations
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },

    // Basic Information Section
    {
      prop: 'jobName',
      slot: 'jobName',
      label: '岗位名称',
      type: 'input',
      minWidth: 150,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {required: true, message: '请输入岗位名称', trigger: 'blur'},
        {min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur'},
      ],
      marker: 'search',
    },
    {
      prop: 'employer',
      label: '用工单位',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      type: 'select',
      props: {
        code: 'employerList',
        filterable: true,
      },
      formatter: (row) => row.employer?.name || '',
      colProps: 6,
    },
    {
      prop: 'xnxq',
      label: '学年学期',
      type: 'select',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      props: {
        code: 'academicTerm',
        filterable: true,
      },
      colProps: 6,
      marker: 'search',
    },

    // Job Type and Schedule Section
    {
      prop: 'jobTypeId',
      label: '岗位类别/类型',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'jobTypeData',
        dicQueryParams: {
          dictFieldUrl: '/workstudy/qgzx-job-type',
        },
      },
      colProps: 6,
      marker: 'search',
    },
    {
      prop: 'workDays',
      label: '工作日',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 12,
      type: 'dictSelect',
      typeKey: 'multipleSelect',
      props: {
        code: 'weekDaysFull',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
      },
      formatter: (row) => {
        const weekDays = [
          '星期一',
          '星期二',
          '星期三',
          '星期四',
          '星期五',
          '星期六',
          '星期日'];
        return row.workDays?.split(',').
          map(d => weekDays[parseInt(d) - 1]).
          join('，') || '';
      },
      marker: 'search',
    },

    // Time and Compensation Section
    {
      prop: 'startDate',
      label: '开始日期',
      type: 'date',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {
          validator: (rule, value, callback) => {
            if (!value) return callback(new Error('请选择开始日期'));
            callback();
          },
        },
      ],
    },
    {
      prop: 'endDate',
      label: '结束日期',
      type: 'date',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {
          validator: (rule, value, callback) => {
            if (!value) return callback(new Error('请选择结束日期'));
            // Additional validation to ensure end date is after start date
            if (this.formModel.startDate && new Date(value) <
              new Date(this.formModel.startDate)) {
              return callback(new Error('结束日期不能早于开始日期'));
            }
            callback();
          },
        },
      ],
    },
    {
      prop: 'startTime',
      label: '工作开始时间',
      type: 'time',
      valueFormat: 'HH:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
    },
    {
      prop: 'endTime',
      label: '工作结束时间',
      type: 'time',
      valueFormat: 'HH:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {
          validator: (rule, value, callback) => {
            if (!value) return callback(new Error('请选择结束时间'));
            // Additional validation for time range
            if (this.formModel.startTime && value <= this.formModel.startTime) {
              return callback(new Error('结束时间必须晚于开始时间'));
            }
            callback();
          },
        },
      ],
    },
    {
      prop: 'workHous',
      label: '每月工作小时数',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      // props: {
      //   min: 1,
      //   max: 200,
      //   // precision: 1
      // }
    },
    {
      prop: 'hourlyRate',
      label: '时薪',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      // props: {
      //   min: 0,
      //   precision: 2,
      //   formatter: value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      //   parser: value => value.replace(/¥\s?|(,*)/g, '')
      // }
    },
    {
      prop: 'yzgbc',
      label: '月最高报酬',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      // props: {
      //   min: 0,
      //   precision: 2,
      //   formatter: value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      //   parser: value => value.replace(/¥\s?|(,*)/g, '')
      // }
    },
    {
      prop: 'ygrs',
      label: '用工人数',
      type: 'inputNumber',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      // props: {
      //   min: 1,
      //   max: 100
      // }
    },

    // Location and Contact Section
    {
      prop: 'xqmc',
      label: '所在校区',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'xq',
        filterable: true,
        dicQueryParams: {
          valueField: 'name',
          textField: 'name',
        },
      },
      colProps: 6,
      marker: 'search',
    },
    {
      prop: 'lxfs',
      label: '联系方式',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 6,
      rules: [
        {
          validator: (rule, value, callback) => {
            if (!value) return callback(new Error('请输入联系方式'));
            if (!/^1[3-9]\d{9}$/.test(value) &&
              !/^(\d{3,4}-)?\d{7,8}$/.test(value)) {
              return callback(new Error('请输入正确的手机号或固定电话'));
            }
            callback();
          },
        },
      ],
    },
    // {
    //   prop: 'address',
    //   label: '办公地点',
    //   type: 'address',
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true,
    //   colProps: 6,
    //   fieldLinks: [
    //     {
    //       fieldEn: 'address',
    //       fieldVal: 'PresetAddress',
    //       showFlag: '是',
    //       linkField: 'presetAddressId',
    //     },
    //     {
    //       fieldEn: 'address',
    //       fieldVal: 'Map',
    //       showFlag: '否',
    //       linkField: 'presetAddressId',
    //     }],
    //   rules: [
    //     {required: true, message: '请输入办公地点', trigger: 'blur'},
    //   ],
    // },

    // Status and Settings Section
    {
      prop: 'Addresses',
      label: '工作地点',
      type: 'Addresses',
      // showFlag: '否',
      // selfModifyFlag: '否',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 12,
    },
    // Status and Settings Section
    {
      prop: 'spzt',
      label: '审批状态',
      type: 'select',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
      // props: {
      //   code: 'approvalStatus',
      // },
      colProps: 6,
    },
    {
      prop: 'published',
      label: '是否对外发布',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
    },
    {
      prop: 'sfxwg',
      label: '是否校外岗',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
    },
    {
      prop: 'sfxzsksj',
      label: '是否限制上课时间',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
    },
    {
      prop: 'ygjssfsh',
      label: '用工结束是否审核',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
    },
    {
      prop: 'sfms',
      label: '是否面试',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
      fieldLinks: [
        // {
        //   fieldEn: 'sfms',
        //   fieldVal: '是',
        //   showFlag: '是',
        //   linkField: 'mssj',
        // },
        {
          fieldEn: 'sfms',
          fieldVal: '是',
          showFlag: '是',
          linkField: 'msdd',
        },
        // {
        //   fieldEn: 'sfms',
        //   fieldVal: '否',
        //   showFlag: '否',
        //   linkField: 'mssj',
        // },
        {
          fieldEn: 'sfms',
          fieldVal: '否',
          showFlag: '否',
          linkField: 'msdd',
        },
      ],
    },

    {
      prop: 'sfqd',
      label: '是否签到',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: 4,
      activeValue: '是',
      inactiveValue: '否',
      fieldLinks: [
        {
          fieldEn: 'sfqd',
          fieldVal: '是',
          showFlag: '是',
          linkField: 'dkfw',
        },
        {
          fieldEn: 'sfqd',
          fieldVal: '否',
          showFlag: '否',
          linkField: 'dkfw',
        },
      ],
    },
    // Conditional Fields (shown based on switches)
    // {
    //   prop: 'mssj',
    //   label: '面试时间',
    //   type: 'datetime',
    //   showFlag: '否',
    //   selfModifyFlag: '否',
    //   required: true,
    //   colProps: 6,
    //   rules: [
    //     {
    //       validator: (rule, value, callback) => {
    //         if (this.formModel.sfms === '是' && !value) {
    //           return callback(new Error('请选择面试时间'));
    //         }
    //         callback();
    //       },
    //     },
    //   ],
    // },
    {
      prop: 'msdd',
      label: '面试地点',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true,
      colProps: 6,
      rules: [
        {
          validator: (rule, value, callback) => {
            if (this.formModel.sfms === '是' && !value) {
              return callback(new Error('请输入面试地点'));
            }
            callback();
          },
        },
      ],
    },
    {
      prop: 'dkfw',
      label: '打卡范围(米)',
      type: 'inputNumber',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true,
      colProps: 6,
      props: {
        min: 50,
        max: 1000,
      },
      rules: [
        {
          validator: (rule, value, callback) => {
            if (this.formModel.sfqd === '是' && !value) {
              return callback(new Error('请输入打卡范围'));
            }
            callback();
          },
        },
      ],
    },

    // Description Sections
    {
      prop: 'bz',
      label: '岗位描述',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        rows: 3,
        maxlength: 500,
        showWordLimit: true,
      },
    },
    {
      prop: 'gwzz',
      label: '岗位职责',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        rows: 4,
        maxlength: 1000,
        showWordLimit: true,
      },
    },
    {
      prop: 'gwyq',
      label: '岗位要求',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        rows: 4,
        maxlength: 1000,
        showWordLimit: true,
      },
    },
    {
      prop: 'zptj',
      label: '招聘条件',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        rows: 4,
        maxlength: 1000,
        showWordLimit: true,
      },
    },
  ];
}
