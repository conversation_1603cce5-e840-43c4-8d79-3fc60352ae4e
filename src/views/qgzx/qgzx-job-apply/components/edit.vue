<template>
  <ele-drawer size="56%"
              :title="isUpdate ? '修改岗位' : '添加岗位信息'"
              :append-to-body="true"
              :destroy-on-close="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <CommonElForm
      ref="formRef"
      :form-items="formItems"
      :form-model="formModel"
      label-position="top"
      @update:form-model="handleModelUpdate"
      @update:form-items="handleItemsUpdate">
      <template
        v-for="name in Object.keys($slots).filter(k => !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
        #[name]="slotProps">
        <slot :name="name" v-bind="slotProps || {}"></slot>
      </template>
    </CommonElForm>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {operation} from '../api/index.js';
import {comColumns} from '../utils/index.js';
import {generateForm} from '@/utils/common_bak2.js';
import CommonElForm from '@/components/CommonElForm/index.vue';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
});

const isUpdate = ref(false);
const loading = ref(false);
const formRef = ref(null);
const formItems = ref([]);
const formModel = ref({});

// 数据更新处理
function handleModelUpdate(newModel) {
  formModel.value = newModel;
}

function handleItemsUpdate(newItems) {
  formItems.value = newItems;
}

// 保存逻辑
const save = async () => {
  console.log('保存按钮点击'); // 调试1
  try {
    console.log('开始验证', formRef.value); // 调试2
    const isValid = await formRef.value?.validate();
    console.log('验证结果', isValid); // 调试3

    if (!isValid) {
      console.warn('验证未通过');
      return false;
    }
    // 处理特殊字段格式
    const submitData = {
      ...formModel.value,
      workDays: formModel.value.workDays.join(','),
    };
    console.log('提交数据', submitData);
    loading.value = true;
    operation(submitData).then(msg => {
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch(e => EleMessage.error(e.message)).finally(() => loading.value = false);
  } catch (e) {
    console.error('保存出错', e);
  }
};

const updateModelValue = (value) => {
  formRef.value?.clearValidate?.();
  emit('update:modelValue', value);
};

// 初始化表单
watch(() => props.modelValue, (modelValue) => {
  if (modelValue) {
    const baseColumns = comColumns() || [];
    formItems.value = baseColumns.filter(item => item.prop && !item.disabled);
    formModel.value = {};
    if (props.data) {
      const hasJobAddresses = Array.isArray(props.data?.jobAddresses) && props.data.jobAddresses.length > 0;
      formModel.value = {
        ...generateForm(formItems.value),
        ...props.data,
        workDays: props.data?.workDays?.split(',') || [],
        Addresses: hasJobAddresses ? props.data?.jobAddresses[0].name : '',
      };
      isUpdate.value = true;
    } else {
      formModel.value = generateForm(formItems.value);
      isUpdate.value = false;
    }
  } else {
    formRef.value?.clearValidate?.();
  }
}, {immediate: true});
</script>
