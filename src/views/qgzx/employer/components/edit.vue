<!-- 试卷试题组编辑弹窗 -->
<template>
  <ele-drawer :size="530"
              :title="isUpdate ? '修改用人单位 ['+data.name+']' : '添加用人单位'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             label-position="top"
             size="small"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="单位名称" prop="name">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.name"
                  placeholder="请输入单位名称"/>
      </el-form-item>
      <el-form-item label="办公电话" prop="bgdh">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.bgdh"
                  placeholder="请输入办公电话"
                  controls-position="right"
                  class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="单位地址" prop="dwdz">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.dwdz"
                  placeholder="请输入单位地址"
                  controls-position="right"
                  class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="负责人" prop="fzr">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.fzr"
                  placeholder="请输入负责人"
                  controls-position="right"
                  class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="联系人" prop="lxrlx">
        <el-radio-group v-model="form.lxrlx">
          <el-radio-button value="xtxz">系统选择</el-radio-button>
          <el-radio-button value="sdwh">手动维护</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.lxrlx==='xtxz'" label=" " prop="selectorDatas">
        <div style="margin-bottom: 6px;cursor: pointer;" @click="openPerSelector()">
          <ele-text type="primary" style="float: left;" @click="openPerSelector()">
            <el-icon>
              <CirclePlus/>
            </el-icon>
            选择联系人
          </ele-text>&nbsp;
        </div>
        <el-input :rows="6"
                  type="textarea"
                  readonly
                  v-model="qddxData"
                  placeholder="请选择联系人"/>
      </el-form-item>
      <template v-if="form.lxrlx==='sdwh'">
        <el-form-item label="联系人账号" prop="xgh">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.xgh"
                    placeholder="请输入联系人账号"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="联系人姓名" prop="xm">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.xm"
                    placeholder="请输入联系人姓名"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="联系人性别" prop="xb">
          <el-radio-group v-model="form.xb">
            <el-radio-button value="男">男</el-radio-button>
            <el-radio-button value="女">女</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="手机号" prop="sjh">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.sjh"
                    placeholder="请输入手机号"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="证件号码" prop="zjhm">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.zjhm"
                    placeholder="请输入证件号码"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
      </template>
      <!--      <el-form-item label="资质证明" prop="zzzz">-->
      <!--        <file-upload :limit="8" v-model="form.zzzz"/>-->
      <!--      </el-form-item>-->
      <el-form-item label="单位描述">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.bz"
                  placeholder="请输入单位描述"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
    <PerSelector v-model="showPerSelector"
                 ref="treeTransferRef"
                 node-key="id"
                 header="联系人"
                 paramMode="base"
                 selectMaxNubmer="1"
                 userType="teacher"
                 :perSelectedData="perSelectedData"
                 @done="onDoneSelector"/>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, computed} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../api/index.js';
import {CirclePlus} from "@element-plus/icons-vue";
import PerSelector from "@/components/PerSelector/index.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  /** 试卷ID*/
  paperId: String,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  name: '',
  lxrlx: '',
  fzr: '',
  sort: void 0,
  bgdh: '',
  dwdz: '',
  zzzz: '',
  xgh: '',
  xm: '',
  sjh: '',
  zjhm: '',
  bz: '',
});

/** =====人员选择器=====start=*/
const perSelectedData = ref([]);
const showPerSelector = ref(false)

const onDoneSelector = (data) => {
  console.log(data)
  perSelectedData.value = data;
};

const openPerSelector = () => {
  showPerSelector.value = true;
};

const qddxData = computed(() => {
  let allDataCode = []
  if (perSelectedData.value.length > 0) {
    perSelectedData.value.forEach(e => {
      form.xgh = e.dictDataCode
      form.xm = e.dictDataName
      allDataCode.push(e.dictDataName ? e.dictDataName : e.name)
    })
  }
  return allDataCode.join(',');
});


/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  sort: [
    {
      message: '请输入排序号',
      type: 'number',
      trigger: 'blur'
    }
  ]
});

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {...form,}
    // let data = toFormData({...result})
    console.log(JSON.stringify(data))
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done', isUpdate.value, data);
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields(props.data);
        if (props.data.userInfo) {
          form.xgh = props.data.userInfo.xgh
          form.xm = props.data.userInfo.xm
          form.sjh = props.data.userInfo.sjh
          form.zjhm = props.data.userInfo.zjhm
          form.xb = props.data.userInfo.xb
          perSelectedData.value = [{
            configId: "dbdf65be75416ec85397e6706f202db4",
            configKey: "xgh",
            configName: "person",
            configTitle: "教师",
            dictDataCode: props.data.userInfo.xgh,
            dictDataName: props.data.userInfo.xm,
            name: props.data.userInfo.xm,
            userType: "teacher",
            value: props.data.userInfo.xgh,
          }]
        }

        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
