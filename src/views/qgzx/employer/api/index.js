import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryQgzxEmployer(params) {
    const res = await request.get('/workstudy/qgzx-employer', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxEmployerPage(params) {
    const res = await request.get('/workstudy/qgzx-employer/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
    const res = await request.post('/workstudy/qgzx-employer/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-employer/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
