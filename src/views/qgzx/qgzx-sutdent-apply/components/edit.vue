<template>
  <ele-drawer size="66%"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0px!important' }"
              @update:modelValue="updateModelValue"
              :modal-append-to-body="true"
              :append-to-body="true"
              class="modern-job-drawer"
              :show-close="false">
    <!-- 自定义抽屉头部 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-content">
          <div class="header-left">
            <div class="header-title">
              {{ formModel.jobName || '岗位详情' }}
            </div>
            <div class="modern-header-line"></div>
          </div>
          <div class="header-right">
            <el-button
              type="primary"
              @click="handleApply"
              class="apply-btn"
            >
              立即申请
            </el-button>
            <el-button type="danger" @click="updateModelValue(false)">
              <el-icon class="el-icon--left">
                <CircleCloseFilled/>
              </el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </template>
    <JobDetailViews :form-items="formItems"
                    :form-model="formModel"
                    enter-type="apply"
                    class="drawer-content"/>
    <apply-model v-model="showApplyModel" :jobId="formModel.id"/>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {comColumns} from '../utils/index.js';
import {generateForm} from '@/utils/common_bak2.js';
import JobDetailViews from '@/views/qgzx/components/JobDetailViews.vue';
import ApplyModel from '@/views/qgzx/qgzx-sutdent-apply/components/apply-model.vue';

const emit = defineEmits(['done', 'update:modelValue','refresh']);

const props = defineProps({
  modelValue: Boolean,
  data: Object,
});

const showApplyModel = ref(false);
const isUpdate = ref(false);
const loading = ref(false);
const formRef = ref(null);
const formItems = ref([]);
const formModel = ref({});

const updateModelValue = (value) => {
  emit('update:modelValue', value);
  if (!value) {
    // 当关闭抽屉时触发刷新事件
    emit('refresh');
  }
};

const handleApply = () => {
  // 这里可以添加申请逻辑
  showApplyModel.value = true;
};

const approvalData = ref(null);

// 初始化表单
watch(() => props.modelValue, (modelValue) => {
  if (modelValue) {
    const baseColumns = comColumns() || [];
    formItems.value = baseColumns.filter(item => item.prop && !item.disabled);
    formModel.value = {};

    approvalData.value = {
      groupName: '申请信息',
      title: '',
      infoType: 'QgzxJobApply',
      listFlag: '否',
    };

    if (props.data) {
      const hasJobAddresses = Array.isArray(props.data?.jobAddresses) && props.data.jobAddresses.length > 0;
      formModel.value = {
        ...generateForm(formItems.value),
        ...props.data,
        workDays: typeof props.data?.workDays === 'string'
          ? props.data.workDays.split(',')
          : Array.isArray(props.data?.workDays)
            ? props.data.workDays
            : [],
        Addresses: hasJobAddresses ? props.data?.jobAddresses[0].name : '',
      };
      isUpdate.value = true;
    } else {
      formModel.value = generateForm(formItems.value);
      isUpdate.value = false;
    }
  } else {
    formRef.value?.clearValidate?.();
  }
}, {immediate: true});
</script>

<style lang="scss" scoped>
.modern-job-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(64, 158, 255, 0.02));
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .drawer-header {
    width: 100%;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .header-left {
        flex: 1;
        position: relative;

        .header-title {
          font-size: 0.8rem;
          font-weight: 500;
          margin: 0 0 0.5rem 0;
          color: #2c3e50;
          position: relative;
        }

        .modern-header-line {
          height: 3px;
          width: 80px;
          background: linear-gradient(90deg, var(--el-color-primary), rgba(var(--el-color-primary-rgb), 0.3));
          border-radius: 3px;
        }
      }

      .header-right {
        margin-left: 20px;
        margin-right: 5px;

        .apply-btn {
          border-radius: 4px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }
  }

  .drawer-content {
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 16px 20px;
  }
}
</style>
