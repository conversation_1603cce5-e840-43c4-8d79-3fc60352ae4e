<template>
  <ele-page hide-footer flex-table>
    <!-- 统计卡片区域 -->
    <div class="statistics-container">
      <el-row :gutter="8" style="margin-bottom: 0;">
        <el-col :span="6">
          <ele-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background-color: #f0f7ff;">
                <el-icon color="#409EFF" :size="24">
                  <Briefcase/>
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">可申请岗位</div>
                <div class="stat-value">{{ statistics.availableJobs }}</div>
              </div>
            </div>
          </ele-card>
        </el-col>
        <el-col :span="6">
          <ele-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background-color: #f0f9eb;">
                <el-icon color="#67C23A" :size="24">
                  <DocumentChecked/>
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">已申请岗位</div>
                <div class="stat-value">{{ statistics.appliedJobs }}</div>
              </div>
            </div>
          </ele-card>
        </el-col>
        <el-col :span="6">
          <ele-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background-color: #fdf6ec;">
                <el-icon color="#E6A23C" :size="24">
                  <Clock/>
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">待面试岗位</div>
                <div class="stat-value">{{ statistics.pendingInterviews }}</div>
              </div>
            </div>
          </ele-card>
        </el-col>
        <el-col :span="6">
          <ele-card shadow="hover" class="stat-card">
            <div class="stat-content">
              <div class="stat-icon" style="background-color: #fef0f0;">
                <el-icon color="#F56C6C" :size="24">
                  <Warning/>
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-title">待签到岗位</div>
                <div class="stat-value">{{ statistics.pendingSignIn }}</div>
              </div>
            </div>
          </ele-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主内容区域 -->
    <ele-card flex-table :body-style="bodyStyle">
      <!-- 标签页头部 -->
      <template #header>
        <ele-tabs type="tag"
                  size="small"
                  v-model="activeName"
                  :items="tabsItems">
          <template #label="{ item, label}">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>

      <!-- 工具栏区域 -->
      <template #extra>
        <div class="toolbar-container">
          <div class="view-switch">
            <el-radio-group v-model="displayMode" size="small" @change="handleViewModeChange">
              <el-radio-button value="fluid">
                <el-icon>
                  <Menu/>
                </el-icon>
                卡片视图
              </el-radio-button>
              <el-radio-button value="table">
                <el-icon>
                  <Grid/>
                </el-icon>
                表格视图
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <template v-if="displayMode === 'table'">
        <ele-pro-table ref="tableRef"
                       row-key="id"
                       :columns="columns"
                       :datasource="tableDataSource"
                       :border="true"
                       :show-overflow-tooltip="true"
                       v-model:selections="selections"
                       tooltip-effect="light"
                       highlight-current-row
                       :footer-style="{ paddingBottom: '3px' }"
                       style="padding-bottom: 0">
          <template #toolbar>
            <div class="table-header">
              <div class="quick-actions">
                <el-button v-if="activeName === 'yishenqing'"
                           type="primary"
                           size="small"
                           @click="handleQuickSignIn"
                           :disabled="!hasPendingSignIn">
                  <el-icon>
                    <Position/>
                  </el-icon>
                  <span>快速签到</span>
                </el-button>
                <el-button v-if="activeName === 'yishenqing'"
                           type="success"
                           size="small"
                           @click="handleViewMyJobs">
                  <el-icon>
                    <View/>
                  </el-icon>
                  <span>我的岗位</span>
                </el-button>
              </div>
            </div>
          </template>

          <!-- 自定义列插槽 -->
          <template #jobName="{ row }">
            <ele-tooltip content="申请" placement="left" effect="light">
              <el-link type="primary" underline="never"
                       @click="openEdit(row)">
                {{ row.jobName }}
              </el-link>
            </ele-tooltip>
          </template>
          <template #userInfoXm="{ row }">
            <ele-tooltip content="详情" placement="left" effect="light">
              <el-link type="primary" underline="never"
                       @click="openDetail(row)">
                {{ row.userInfo?.xm }}
              </el-link>
            </ele-tooltip>
          </template>
          <template #status="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
          <template #actions="{ row }">
            <el-button v-if="row.status === 'pending_sign_in'"
                       type="primary"
                       size="small"
                       @click="handleSignIn(row)"
                       plain>
              签到
            </el-button>
            <el-button v-else-if="row.status === 'approved'"
                       type="info"
                       size="small"
                       @click="handleViewJobDetail(row)"
                       plain>
              查看
            </el-button>
          </template>
        </ele-pro-table>
      </template>

      <!-- 卡片视图 -->
      <template v-else>
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated/>
        </div>

        <!-- 空状态 -->
        <template v-else>
          <div v-if="cardData.length === 0" class="empty-container">
            <el-empty description="暂无数据"/>
          </div>

          <!-- 可申请岗位卡片 -->
          <template v-if="activeName === 'keshenqing'">
            <el-row :gutter="10">
              <el-col v-for="item in cardData"
                      :key="item.id"
                      :lg="8"
                      :md="12"
                      :sm="12"
                      :xs="24">
                <job-card :data="item" @edit="openEdit" @detail="openEdit"/>
              </el-col>
            </el-row>
          </template>

          <!-- 已申请岗位卡片 -->
          <template v-else>
            <el-row :gutter="8">
              <el-col v-for="item in cardData"
                      :key="item.id"
                      :lg="8"
                      :md="12"
                      :sm="12"
                      :xs="24">
                <application-card :data="item" @edit="openEdit" @detail="openDetail"/>
              </el-col>
            </el-row>
          </template>
        </template>

        <!-- 分页 -->
        <div class="pagination-container" v-if="pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </template>
    </ele-card>

    <!-- 弹窗组件 -->
    <edit v-model="showEdit" :data="editData" @done="reload"
          @refresh="handleRefresh"/>
    <InterViewDetailDrawer v-model="detailVisible" :data="detailData" enterType="studentJob"/>
  </ele-page>
</template>

<script setup>
import {ref, reactive, computed, watch, onMounted, nextTick, unref} from 'vue';
import {queryQgzxJobApplyPage, queryQgzxJobApplyPageJob} from './api/index.js';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import Edit from './components/edit.vue';
import JobCard from '../components/JobCard.vue';
import ApplicationCard from '../components/ApplicationCard.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {comColumns} from './utils/index.js';
import {comColumns as comColumnsApproval} from '@/views/qgzx/qgzx-student-approval/utils/index.js';
import InterViewDetailDrawer from '@/views/qgzx/interview-record/components/InterViewDetailDrawer.vue';
import {useDictData} from '@/utils/use-dict-data.js';
import {
  Briefcase,
  DocumentChecked,
  Clock,
  Warning,
  Position,
  View,
  Menu,
  Grid,
} from '@element-plus/icons-vue';
import {debounce} from 'lodash-es';

// 工具函数和Store
const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

// 视图模式：table(表格)/fluid(卡片)
const displayMode = ref('fluid');

// 计算卡片样式
const bodyStyle = computed(() => ({
  overflow: 'hidden',
  background: displayMode.value === 'fluid' ? '#f0f2f5' : '',
  padding: displayMode.value === 'fluid' ? '0 0 10px !important' : '0 10px 10px !important',
}));

// 表格实例
const tableRef = ref(null);

// 加载状态
const loading = ref(false);

// 标签页选中
const activeName = ref('keshenqing');
const tabsItems = ref([
  {label: '可申请岗位列表', name: 'keshenqing'},
  {label: '已申请岗位列表', name: 'yishenqing'},
]);

// 弹窗相关
const showEdit = ref(false);
const detailVisible = ref(false);
const detailData = ref(null);
const editData = ref(null);

// 卡片视图数据
const cardData = ref([]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 统计信息
const statistics = reactive({
  availableJobs: 0,
  appliedJobs: 0,
  pendingInterviews: 0,
  pendingSignIn: 0,
});

// 是否有待签到岗位
const hasPendingSignIn = computed(() => statistics.pendingSignIn > 0);

// 表格列配置
const columns = ref([]);
columns.value = comColumns();

// 表格搜索参数
const lastWhere = reactive({});
// 列表选中数据
const selections = ref([]);

// 岗位类型缓存
const jobTypeCache = ref(null);

/**
 * 获取统计信息
 */
const fetchStatistics = async () => {
  try {
    // const res = await getStudentJobStatistics();
    // Object.assign(statistics, res.data);
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
};

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const map = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'pending_interview': '',
    'pending_sign_in': 'warning',
    'completed': 'info',
  };
  return map[status] || '';
};

/**
 * 获取状态文本
 */
const getStatusText = (status) => {
  const map = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝',
    'pending_interview': '待面试',
    'pending_sign_in': '待签到',
    'completed': '已完成',
  };
  return map[status] || status;
};

/**
 * 打开详情弹窗
 */
const openDetail = (item) => {
  detailData.value = {
    studentApply: item,
    jobApplication: item.jobApplication,
    userInfo: item.userInfo,
  };
  detailVisible.value = true;
};

/**
 * 打开编辑弹窗
 */
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

/**
 * 查看岗位详情
 */
const handleViewJobDetail = (row) => {
  currentJobData.value = row;
  showJobDetail.value = true;
};

/**
 * 签到
 */
const handleSignIn = (row) => {
  currentJobData.value = row;
  showSignInDialog.value = true;
};

/**
 * 快速签到
 */
const handleQuickSignIn = () => {
  const pendingSignInJob = selections.value.find(job => job.status === 'pending_sign_in') ||
    tableRef.value?.getTableData()?.find(job => job.status === 'pending_sign_in');
  if (pendingSignInJob) {
    handleSignIn(pendingSignInJob);
  } else {
    ElMessage.warning('没有找到待签到的岗位');
  }
};

/**
 * 查看我的岗位
 */
const handleViewMyJobs = () => {
  reload({myJobs: true});
};

/**
 * 视图模式变化处理
 */
const handleViewModeChange = (mode) => {
  if (mode === 'fluid') {
    loadCardData();
  }
};

/**
 * 表格数据源
 */
const tableDataSource = ({page, limit, where, orders, filters}) => {
  if (activeName.value === 'yishenqing') {
    return queryQgzxJobApplyPage({
      ...where, ...orders, ...filters, page, limit,
    });
  } else {
    return queryQgzxJobApplyPageJob({
      ...where, ...orders, ...filters, page, limit,
    });
  }
};

/**
 * 加载卡片数据
 */
const loadCardData = async (params = {}) => {
  try {
    loading.value = true;
    const {page = 1, limit = pagination.pageSize, where = {}} = params;

    let res;
    if (activeName.value === 'yishenqing') {
      res = await queryQgzxJobApplyPage({page, limit, ...where});
    } else {
      res = await queryQgzxJobApplyPageJob({page, limit, ...where});
    }

    cardData.value = res?.list || [];
    pagination.total = res?.count || 0;
    pagination.currentPage = page;
  } catch (error) {
    console.error('加载卡片数据失败:', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 分页变化处理
 */
const handlePageChange = (page) => {
  loadCardData({page, where: lastWhere});
};

/**
 * 每页条数变化处理
 */
const handleSizeChange = (size) => {
  pagination.pageSize = size;
  if (displayMode.value === 'fluid') {
    loadCardData({page: 1, limit: size, where: lastWhere});
  }
};

// 刷新列表的方法
const handleRefresh = async () => {
  try {
    // 添加防抖和加载状态检查
    if (loading.value) return;

    loading.value = true;
    await reload();
  } catch (error) {
    console.error('刷新列表失败:', error);
  } finally {
    loading.value = false;
  }
};
/**
 * 重新加载数据
 */
const reload = (where) => {
  // 添加日志检查是否被多次调用
  console.log('reload called');

  selections.value = [];
  if (where) {
    Object.assign(lastWhere, where);
  }

  // 使用防抖
  debounceReload();
};

// 添加防抖方法
const debounceReload = debounce(() => {
  if (displayMode.value === 'fluid') {
    loadCardData({page: 1, where: lastWhere});
  } else {
    nextTick(() => {
      tableRef.value?.reload?.({page: 1, where: lastWhere});
    });
  }

  fetchStatistics();
}, 300);

/**
 * 获取岗位类型数据
 */
const getJobTypeName = async () => {
  if (!dicts.value['jobTypeData'] && !jobTypeCache.value) {
    try {
      await useDictData(['jobTypeData'], {
        dictFieldUrl: '/workstudy/qgzx-job-type',
      });
      jobTypeCache.value = dicts.value['jobTypeData'];
    } catch (error) {
      console.error('获取岗位类型数据失败:', error);
    }
  }
};

// 监听标签页变化
watch(activeName, (newVal) => {
  if (newVal) {
    getJobTypeName();
    if (newVal === 'yishenqing') {
      // 初始化表单字段
      const baseColumns = comColumnsApproval(dicts.value) || [];
      columns.value = baseColumns.filter(item => item.prop && item.showList);
      // 添加操作列
      columns.value.push({
        prop: 'actions',
        label: '操作',
        width: 120,
        align: 'center',
        fixed: 'right',
        slot: 'actions',
      });
    } else {
      columns.value = comColumns(dicts.value);
    }
    reload();
  }
}, {immediate: true});

// 初始化时获取统计信息
onMounted(() => {
  fetchStatistics();
});
</script>

<script>
export default {
  name: 'KSBINDEX',
};
</script>

<style scoped>
.statistics-container {
  margin: 3px 0 2px 0;
}

.stat-card {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.stat-content {
  margin: 10px;
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.toolbar-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-container {
  padding: 20px;
}

.empty-container {
  padding: 40px 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.el-button [class*=el-icon-] + span {
  margin-left: 4px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid var(--el-border-color-light);
}

@media screen and (max-width: 768px) {
  .toolbar-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .view-switch {
    margin-bottom: 8px;
  }
}
</style>
