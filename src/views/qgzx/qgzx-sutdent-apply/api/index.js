import request from '@/utils/request';

/**
 * 学生查询申请记录(我的岗位)（权限标识：workstudy:qgzxStudentApply:list）
 */
export async function queryQgzxJobApply(params) {
  const res = await request.get('/workstudy/qgzx-job-apply', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生分页查询申请记录(我的岗位)（权限标识：workstudy:qgzxStudentApply:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxJobApplyPage(params) {
  const res = await request.get('/workstudy/qgzx-student-apply/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生查询全部可申请岗位（权限标识：workstudy:qgzxJobApply:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxJobApplyListJob(params) {
  const res = await request.get('/workstudy/qgzx-student-apply/listJob', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生分页查询可申请岗位（权限标识：workstudy:qgzxJobApply:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxJobApplyPageJob(params) {
  const res = await request.get('/workstudy/qgzx-student-apply/pageJob', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 用人单位查询需要面试的学生申请（权限标识：workstudy:qgzxStudentApply:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function queryQgzxJobApplyPageForInterview(params) {
  const res = await request.get('/workstudy/qgzx-student-apply/pageForInterview',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询申请详情（权限标识：workstudy:qgzxStudentApply:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function queryQgzxJobApplyById(id) {
  const res = await request.get('/workstudy/qgzx-student-apply/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学生申请岗位（权限标识：workstudy:qgzxStudentApply:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-student-apply/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-student-apply/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
