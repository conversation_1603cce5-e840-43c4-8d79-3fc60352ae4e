<template>
  <ele-drawer
    size="55%"
    style="max-width: 100%"
    v-model="drawerVisible"
    :body-style="{ padding: '4px!important' ,background:'#f0f2f5',height: (pageHeight+58)+'px', overflow:'auto'}"
    :modal-append-to-body="true"
    :append-to-body="true"
    class="modern-job-drawer"
    :show-close="false"
  >
    <!-- 自定义抽屉头部 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-content">
          <div class="header-left">
            <div class="header-title">
              {{ formModel?.jobName || title }}
            </div>
            <div class="modern-header-line"></div>
          </div>
          <div class="header-right">
            <el-button
              v-if="enterType==='apply'"
              type="primary"
              @click="handleApply"
              class="apply-btn"
            >
              立即申请
            </el-button>
            <el-button type="danger" @click="updateModelValue(false)">
              <el-icon class="el-icon--left">
                <CircleCloseFilled/>
              </el-icon>
              关闭
            </el-button>
          </div>
        </div>
      </div>
    </template>
    <JobDetailViews :form-model="formModel"
                    :enter-type="enterType"
                    class="drawer-content"/>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import JobDetailViews from '@/views/qgzx/components/JobDetailViews.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const props = defineProps({
  // 控制抽屉显示
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 岗位数据
  formModel: {
    type: Object,
    default: () => ({}),
  },
  // 抽屉标题
  title: {
    type: String,
    default: '岗位详情',
  },
  // 进入类型
  enterType: {
    type: String,
    default: 'apply',
  },
});

const emit = defineEmits(['update:modelValue', 'apply']);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
// 使用内部状态管理
const drawerVisible = ref(props.modelValue);

// 监听父组件传递的值变化
watch(() => props.modelValue, (val) => {
  drawerVisible.value = val;
});

// 监听内部状态变化并通知父组件
watch(drawerVisible, (val) => {
  emit('update:modelValue', val);
});

const showApplyModel = ref(false);

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const handleApply = () => {
  emit('apply', props.formModel);
  showApplyModel.value = true;
};

// 初始化表单
watch(() => props.modelValue, (modelValue) => {
  if (modelValue) {
  }
}, {immediate: true});

// 暴露方法，可以在父组件中调用
defineExpose({
  getFormModel: () => props.formModel,
});
</script>

<style lang="scss" scoped>
.modern-job-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(64, 158, 255, 0.02));
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .drawer-header {
    width: 100%;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .header-left {
        flex: 1;
        position: relative;

        .header-title {
          font-size: 0.8rem;
          font-weight: 500;
          margin: 0 0 0.5rem 0;
          color: #2c3e50;
          position: relative;
        }

        .modern-header-line {
          height: 3px;
          width: 80px;
          background: linear-gradient(90deg, var(--el-color-primary), rgba(var(--el-color-primary-rgb), 0.3));
          border-radius: 3px;
        }
      }

      .header-right {
        margin-left: 20px;

        .close-btn {
          border-radius: 6px;
          transition: all 0.3s ease;
          font-weight: 500;
          border: 1px solid rgba(245, 108, 108, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
            background: rgba(245, 108, 108, 0.1);
          }
        }
      }
    }
  }

  .drawer-content {
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 16px 20px;
  }
}
</style>
