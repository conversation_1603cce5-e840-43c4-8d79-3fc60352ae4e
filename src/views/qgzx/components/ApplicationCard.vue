<template>
  <div class="tech-card" @click="$emit('detail', data)">
    <!-- 顶部信息栏 -->
    <div class="card-header">
      <div class="user-info">
        <template v-if="data?.userInfo">
          <el-avatar
            v-if="data.userInfo.photo"
            :size="60"
            class="user-avatar"
            :src="data.userInfo.xgh ? `/api/personInfo/${data.userInfo.xgh}/photo?access_token=${accessToken}` : ''">
            {{ data.userInfo?.xm?.charAt(0) || '' }}
          </el-avatar>
          <template v-else>
            <el-avatar
              v-if="data.userInfo.xb === '男'"
              :size="60"
              src="/male.png"
              :alt="data.userInfo.xb"
              class="user-avatar"/>
            <el-avatar
              v-else-if="data.userInfo.xb === '女'"
              :size="60"
              src="/female.png"
              :alt="data.userInfo.xb"
              class="user-avatar"/>
          </template>
        </template>

        <div class="user-details">
          <div class="student-name-row">
            <h3 class="student-name">
              <span class="highlight">{{ data?.userInfo?.xm || '--' }}</span>
            </h3>
            <div class="student-meta">
              <span>{{ data?.userInfo?.xgh || '--' }}</span>
            </div>
            <div class="status-badge" :class="getStatusClass(data.spzt)">
              {{ data.spzt || '-' }}
            </div>
          </div>
          <div class="meta-info">
            <span class="apply-time detail-row">
              <IconPark name="history" size="15" strokeWidth="3"/>
              <span class="span-wrap">{{ formatDateTime(data.sqsj) }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 岗位信息卡片 -->
    <div class="job-card">
      <div class="job-title detail-row">
        <IconPark name="app-store" size="18" strokeWidth="3"/>
        <span class="span-wrap">{{ data.jobApplication?.jobName || '未知岗位' }}</span>
      </div>

      <div class="job-details">
        <div class="detail-row">
          <IconPark name="local" size="16" strokeWidth="3"/>
          <span class="span-wrap">{{ data.jobApplication?.xqmc || '未知校区' }}</span>
        </div>

        <div class="detail-row">
          <IconPark name="calendar" size="16" strokeWidth="3"/>
          <span class="span-wrap">{{
              formatDateRange(data.jobApplication?.startDate, data.jobApplication?.endDate)
            }}</span>
          <IconPark name="time" size="16" strokeWidth="3"/>
          <span class="span-wrap">{{ data.jobApplication?.startTime }}~{{
              data.jobApplication?.endTime
            }}</span>
        </div>
        <div class="time-grid">
          <div class="time-item detail-row">
            <IconPark name="plan" size="16" strokeWidth="3"/>
            <span class="span-wrap">{{ formatWorkDays(data.jobApplication?.workDays) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <el-button class="action-btn" @click.stop="$emit('detail', data)">
        <IconPark name="eyes" size="16"/>
        <span>详情查看</span>
      </el-button>
      <template v-if="data.spzt === '通过'">
        <el-button class="action-btn" @click.stop="$emit('interview')">
          <IconPark name="file-text" size="16"/>
          <span>面试记录</span>
        </el-button>
        <el-button class="action-btn" @click.stop="$emit('attendance')">
          <IconPark name="checklist" size="16"/>
          <span>签到管理</span>
        </el-button>
        <el-button class="action-btn" @click.stop="$emit('salary')">
          <IconPark name="income-one" size="16"/>
          <span>薪酬管理</span>
        </el-button>
      </template>
    </div>
  </div>
</template>

<script setup>
import {formatDateRange, formatWorkDays, formatDateTime, getStatusClass} from '@/views/qgzx/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';
import {getToken} from '@/utils/token-util.js';

defineProps({
  data: {
    type: Object,
    required: true,
  },
});
const accessToken = getToken();

defineEmits(['detail', 'interview', 'attendance', 'salary']);
</script>

<style scoped>
@import "../css/job-detail.css";

.tech-card {
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin: 8px 0;
  cursor: pointer;
}

.tech-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(99, 102, 241, 0.2);
}

.card-header {
  padding: 6px 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-left: 10px;
  margin-right: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.student-name-row {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  justify-content: space-between; /* 添加这一行 */
}

.status-badge {
  margin-left: auto; /* 这将使 badge 自动填充左侧空间，从而靠右 */
  margin-top: 5px;
}

.student-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.student-meta {
  padding-top: 5px;
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
}

.meta-info {
  margin: 4px 0 0;
  color: #606266;
  font-size: 13px;
}

.apply-time {
  display: inline-flex;
  align-items: center;
}

.job-card {
  padding: 10px 16px 5px;
  background: white;
}

</style>
