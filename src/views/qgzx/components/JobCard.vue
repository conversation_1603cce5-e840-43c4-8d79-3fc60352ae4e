<template>
  <ele-card shadow="hover"
            :style="{margin:'0!important'}"
            :body-style="{ padding: '0 5px!important', marginTop: '8px', cursor: 'pointer', overflow: 'hidden' }">
    <div class="list-item-body" @click.stop="$emit('detail', data)">
      <div style="flex: 1">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <ele-text type="primary" size="md">
            <IconPark name="app-store" size="18" strokeWidth="3"/>
            {{ data.jobName }}
          </ele-text>
          <el-tag v-if="data.jobTypeId" size="small" effect="light">
            {{ jobTypeName }}
          </el-tag>
        </div>
        <div style="margin-top: 3px; display: flex; align-items: center">
          <el-tag v-for="tag in data.tags"
                  :key="tag"
                  type="info"
                  size="small"
                  :disable-transitions="true"
                  style="margin-right: 8px">
            {{ tag }}
          </el-tag>
        </div>
        <div class="description-container">
          <ele-ellipsis
            :content="data.bz || '暂无描述'"
            :max-line="1"
            :tooltip="{
              effect: 'light',
              placement: 'top',
              popperStyle: {
                width: '380px',
                maxWidth: '90%',
                wordBreak: 'break-all',
              },
              bodyStyle: {
                maxWidth: 'calc(100vw - 32px)',
                maxHeight: '252px',
                overflowY: 'auto',
              },
              offset: 4,
            }">
            <span class="ellipsis-text ">{{ data.bz || '暂无描述' }}</span>
          </ele-ellipsis>
        </div>

        <div class="job-details">
          <div class="detail-row">
            <IconPark name="local" size="16" strokeWidth="3"/>
            <span class="span-wrap">{{ data.xqmc || '未知校区' }}</span>
          </div>
          <div class="detail-row">
            <IconPark name="calendar" size="16" strokeWidth="3"/>
            <span class="span-wrap">{{ formatDateRange(data?.startDate, data?.endDate) }}</span>
            <IconPark name="time" size="16" strokeWidth="3"/>
            <span class="span-wrap">{{ data?.startTime }}~{{ data?.endTime }}</span>
          </div>
          <div class="time-grid">
            <div class="time-item detail-row">
              <IconPark name="plan" size="16" strokeWidth="3"/>
              <span class="span-wrap">{{ formatWorkDays(data?.workDays) }}</span>
            </div>
          </div>
        </div>
        <div style="margin-top: 10px; display: flex; align-items: center; flex-wrap: wrap; gap: 8px">
          <ele-tooltip content="用工人数" placement="top" effect="light">
            <div class="info-item">
              <el-icon>
                <User/>
              </el-icon>
              <span>{{ data.ygrs }}人</span>
            </div>
          </ele-tooltip>

          <ele-tooltip content="每月工作小时数" placement="top" effect="light">
            <div class="info-item">
              <el-icon>
                <Timer/>
              </el-icon>
              <span>{{ data.workHous }}小时</span>
            </div>
          </ele-tooltip>

          <ele-tooltip content="时薪" placement="top" effect="light">
            <div class="info-item">
              <el-icon>
                <Wallet/>
              </el-icon>
              <span>{{ data.hourlyRate }}元/时</span>
            </div>
          </ele-tooltip>

          <ele-tooltip content="月最高报酬" placement="top" effect="light">
            <div class="info-item">
              <el-icon>
                <PriceTag/>
              </el-icon>
              <span>{{ data.yzgbc }}元/月</span>
            </div>
          </ele-tooltip>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import {Timer, Wallet, PriceTag, User} from '@element-plus/icons-vue';
import {formatDateRange, formatWorkDays, getJobTypeName} from '@/views/qgzx/utils/index.js';
import IconPark from '@/components/IconPark/index.vue';
import {ref, watchEffect} from 'vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

defineEmits(['edit', 'detail']);
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

// 创建一个响应式变量来存储岗位类型名称
const jobTypeName = ref('');

// 监听 jobTypeId 变化
watchEffect(async () => {
  if (props.data?.jobTypeId) {
    jobTypeName.value = await getJobTypeName(props.data.jobTypeId);
  }
});
// 这里可以添加组件特定的方法
</script>
<style scoped>
@import "../css/job-detail.css";

.list-item-body {
  padding: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
}

.info-item .el-icon {
  font-size: 14px;
}
</style>
