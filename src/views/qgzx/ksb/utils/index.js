import {transformDicDataName} from '@/utils/common_bak2.js';

export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'xnxq',
      label: '学年学期',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'name',
      label: '名称',
      slot: 'name',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'kssj',
      label: '开始时间',
      type: 'time',
      valueFormat: 'hh:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'jssj',
      label: '结束时间',
      type: 'time',
      valueFormat: 'hh:mm',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'weekDay',
      label: '周',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'weekDaysFull',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
        filterable: true,
      },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      formatter: (row) => {
        const weekDayMap = {
          1: '周一',
          2: '周二',
          3: '周三',
          4: '周四',
          5: '周五',
          6: '周六',
          7: '周日'
        };
        return weekDayMap[row.weekDay] || row.weekDay;
      },
    },
    {
      prop: 'timeType',
      label: '时间段类型',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'TimeType',
        dicQueryParams: {
          valueField: 'code',
          textField: 'name',
        },
        filterable: true,
      },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      formatter: (row) => {
        const timeTypeMap = {
          'COURSE': '课表时间',
          'FREE': '空闲时间'
        };
        return timeTypeMap[row.timeType] || row.timeType;
      },
    },
  ];
}
