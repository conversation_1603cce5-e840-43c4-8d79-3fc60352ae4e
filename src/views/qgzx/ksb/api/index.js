import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryQgzxJobKsb(params) {
  const res = await request.get('/workstudy/qgzx-ksb', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxJobKsbPage(params) {
  const res = await request.get('/workstudy/qgzx-ksb/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function queryQgzxJobKsbeById(id) {
  const res = await request.get('/workstudy/qgzx-ksb/'+id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post('/workstudy/qgzx-ksb/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/workstudy/qgzx-ksb/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
