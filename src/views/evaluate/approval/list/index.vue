<template>
  <ele-page hide-footer  flex-table>
    <ApprovalIndex v-if="pageConfig" :pageConfig="pageConfig"/>
  </ele-page>
</template>

<script setup>
import {ref, unref,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import ApprovalIndex from "../components/approval-index.vue";
import {getEvaluateConfigById} from "@/views/evaluate/evaluate-config/api/index.js";
import {ElLoading} from "element-plus";

const props = defineProps({});

const pageConfig = ref(null)
const configId = ref(null)
const {push, currentRoute} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
console.log(pathArray)
configId.value = pathArray[5]
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} =
  usePageTab();

const queryEvaluateConfigById = () => {
   const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
  getEvaluateConfigById(configId.value).then((data) => {
    pageConfig.value = data
  }).catch((e) => {
    loading.close();
    EleMessage.error(e.message);
  }).finally(() => {
    loading.close();
    setPageTabTitle(pageConfig.value.title + '审核列表')
  });
};
if (configId.value) {
  queryEvaluateConfigById()
}
</script>
