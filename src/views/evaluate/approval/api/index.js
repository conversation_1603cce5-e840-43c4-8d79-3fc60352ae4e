/**
 * 参数配置信息管理
 */
import request from '@/utils/request.js';

export async function getEvaluateApprovalNodeState(params) {
  const res = await request.get('/evaluate/approval/nodeState', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 当前用户审批节点
 */
export async function getApprovalNodeList(params) {
  const res = await request.get('/evaluate/approval/approvalNodeList',
    {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getPageConfig(params) {
  const res = await request.get('/evaluate/approval/pageConfig', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请信息查询当前用户审核表单字段
 * @param params
 * @returns {Promise<*>}
 */
export async function nodeFormFieldList(params) {
  const res = await request.get('/evaluate/approval/nodeFormFieldList',
    {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 综测年份
 * @param params
 * @returns {Promise<*>}
 */
export async function getYearList(params) {
  const res = await request.get('/evaluate/approval/yearList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 工作流审核流程
 */
export async function queryApprovalWorkflow(params) {
  const res = await request.get('/evaluate/approval/workflow', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量审批
 * @param data
 * @returns {Promise<*>}
 */
export async function approvalBatchOperation(data) {
  const res = await request.post('/evaluate/approval/batchOperation', data);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data));
}

/**
 * 加减分项审批列表（权限标识：evaluate:approval:list）
 */
export async function pageApprovalList(params) {
  const res = await request.get('/evaluate/approval/pageApprovalList',
    {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请ID查询审核记录
 * @param sqId
 * @returns {Promise<*>}
 */
export async function getApprovalNodesBySqId(sqId) {
  const res = await request.get(
    '/evaluate/approval/' + sqId + '/approvalNodes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询加减分项申请信息（权限标识：evaluate:approval:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function getApprovalApplicationInfo(id) {
  const res = await request.get(
    '/evaluate/approval/' + id + '/applicationInfo');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 单个或批量审核操作（权限标识：evaluate:approval:operation）
 */
export async function operation(data) {
  const res = await request.post('/evaluate/approval/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除加减分项申请信息（权限标识：evaluate:approval:remove）
 */
export async function removeApplicationInfo(nodeId) {
  const res = await request.post(
    'evaluate/approval/' + nodeId + '/removeApplicationInfo');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导入
 */
export async function importData(file, nodeId) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post(
    '/evaluate/approval/dataImport?nodeId=' + nodeId, formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data, nodeId) {
  const res = await request.post(
    '/evaluate/approval/' + nodeId + '/removeApplicationInfo', {
      data,
    });
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
