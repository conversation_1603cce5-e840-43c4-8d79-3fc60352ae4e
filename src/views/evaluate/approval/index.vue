<template>
  <ele-page hide-footer  flex-table>
    <ConfigIndex v-if="pageConfigCount>1" :routeType="routeType"/>
    <ApprovalIndex v-else-if="pageConfigCount===1&&pageConfig" :pageConfigCount="pageConfigCount"
                   :pageConfig="pageConfig" :routeType="routeType"/>
  </ele-page>
</template>

<script setup>
import {ref, unref,} from 'vue';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {
  getPageConfig,
} from './api';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import ConfigIndex from "./components/config-index.vue";
import ApprovalIndex from "./components/approval-index.vue";

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
const routeType = pathArray[3];
const userType = pathArray[4];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

const pageConfigCount = ref(0);
const pageConfig = ref(null);
const queryPageConfig = () => {
   const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
  getPageConfig({page: 1, limit: 10}).then((data) => {
    console.log(data)
    loading.close();
    pageConfigCount.value = data.count
    pageConfig.value = data.list[0]
  }).catch((e) => {
    loading.close();
    EleMessage.error(e.message);
  });
};
queryPageConfig()
</script>

<script>
export default {
  name: 'EVALUATEAPPROVALINDEX'
};
</script>

