<!-- 加分项审批 -->
<template>
  <ele-drawer size="45%"
              title="加分项批量审核"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-card  class="ele-card-item"
               :body-style="{height:'auto',padding: '10px 5px 0 10px !important' }">
      <el-form ref="formRef"
               :model="approvalForm"
               :rules="approvalRules"
               label-width="109px"
               label-position="top"
               @submit.prevent="">
        <el-form-item label="审核状态" prop="result">
          <el-radio-group v-model="approvalForm.result" @change="onChange">>
            <template v-for="item in approvalStatus"
                      :key="item.diyname">
              <el-radio :value="item.name"
                        :label="item.label">
                {{ item.label }}
              </el-radio>
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="approvalForm.result==='退回'" label="退回规则" prop="returnWay">
          <el-radio-group v-model="approvalForm.returnWay">
            <template v-for="item in approvalreturnWay"
                      :key="item.name">
              <el-radio :value="item.name"
                        :label="item.label">
                {{ item.label }}
              </el-radio>
              <br/>
            </template>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </ele-card>
    <form-preview labelWidth="109px"
                  routeType="zhcpApproval"
                  labelPosition="top"
                  :currentGroup="approvalData"
                  @onDoneGroup="onDoneGroup"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {
  getEvaluateApprovalNodeState,
  operation, getApprovalApplicationInfo, getApprovalNodesBySqId,
} from '../api/index';
import FileUpload from "@/components/FileUpload/index.vue";
import {comApproveStatus, groupArr, isImageFile, toFormData} from '@/utils/common_bak2.js';
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 参数配置信息 */
  pageConfig: Object,
  /** 当前审核节点信息 */
  nodeData: Object,
});

/** 表单验证规则 */
const approvalRules = reactive({
  result: [
    {
      required: true,
      message: '请选择审核状态',
      type: 'string',
      trigger: 'blur'
    }
  ],
  returnWay: [
    {
      required: true,
      message: '请选择退回规则',
      type: 'string',
      trigger: 'blur'
    }
  ],
})

let routeType = ''
let currentProjectId = ''
let currentSqId = ''
let currentNodeId = ''
let currentWorkflowId = ''
const BASE_URL = import.meta.env.BASE_URL;
/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
const formData = ref(null);

/** changeDelIds */
const changeDelIds = (value) => {
  deleteFileIds.value = value
};

/** 表单验证规则 */
const rules = reactive({
  fjxx: [
    {
      required: true,
      message: '请上传证明材料',
      type: 'array',
      trigger: 'blur'
    },
  ],
  fs: [
    {
      required: true,
      message: '请输入加分数',
      type: 'number',
      trigger: 'blur'
    },
  ],
  jfyy: [
    {
      required: true,
      message: '请输入加分原因',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
  console.log("proFormGroup.value===", proFormGroup.value)
};

const onChange = (newValue) => {
  if (newValue) {
    approvalForm.value.returnWay = '';
  }
}
const approvalForm = ref(null);

/** 要删除的附件ID，字符串逗号分割*/
const deleteFileIds = ref(null);
/** 保存编辑 */
const save = () => {
  let resData = proFormGroup.value;
  console.log(resData.length)
  if (!approvalForm.value.result) {
    EleMessage.error('请选择审核状态');
    return;
  }
  console.log(approvalForm.value)
  // let newObj = Object.assign(approvalForm.value, resData[0].values,);
  let newObj = Object.assign(approvalForm.value, resData.length > 0 ? resData[0].values : {},);
  console.log(newObj)
  loading.value = true;
  let data = toFormData({...newObj})
  operation(data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
  // });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};


const approvalTimeIsCorrect = ref(true);

// const activities = ref([])

/** 审核信息*/
const approvalData = ref(null);
const approvalStatus = ref(null);
const approvalreturnWay = ref([{
  label: '退回到上一节点',
  name: 'PRE_NODE',
}, {
  label: '退回到申请人',
  name: 'APPLICANT',
}]);

/** 查询tabs */
const queryNodeState = () => {
  getEvaluateApprovalNodeState({
    id: currentWorkflowId,
    // year: approvalApplicationInfo.value.year,
    nodeId: currentNodeId
  }).then((data) => {
    let baseItmes = comApproveStatus()
    baseItmes.forEach(item => {
      item.label = data[0][item.diyname]
      item.name = data[0][item.prename]
    })
    approvalStatus.value = baseItmes
    approvalTimeIsCorrect.value = data[0].approvalTimeIsCorrect;
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue && props.pageConfig && props.nodeData) {
      currentProjectId = props.pageConfig.id
      // currentSqId = props.data.id
      currentNodeId = props.nodeData.nodeId
      currentWorkflowId = props.nodeData.workflowId
      approvalData.value = {
        groupName: "审核信息",
        title: "",
        id: currentSqId,
        workflowId: currentWorkflowId,
        nodeId: currentNodeId,
        infoType: "zhcpApproval",
        listFlag: "否",
      }
      approvalForm.value = {
        projectId: currentProjectId,
        id: [currentSqId],
        nodeId: currentNodeId,
        year: '',
        result: '',
        returnWay: '',
      }
      isUpdate.value = true;
      // activities.value = [];
      // queryApprovalApplicationInfo()
      queryNodeState();
    }
  },
  {immediate: true}
);
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}

.el-collapse {
  border-bottom: unset !important;
}

.el-collapse-item {
  padding-left: 12px !important;
}

.list-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #181b1e;
}

.list-company {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.list-company1 {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  //margin-top: 15px;
}

.list-desc {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #596878;
}

</style>
