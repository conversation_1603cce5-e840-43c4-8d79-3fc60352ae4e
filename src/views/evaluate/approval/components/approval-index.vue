<template>
  <ele-card flex-table
            :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
    <template #header>
      <ele-tabs type="indicator"
                size="small"
                v-model="activeName"
                :items="tabsItems">
        <template #label="{ item, label}">
          <span>{{ label }}</span>
        </template>
      </ele-tabs>
    </template>
    <template #extra>
      <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
        <IconPark name="return" size="18" strokeWidth="3"/>
        <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
          返回
        </ele-text>
      </div>
      <el-button v-if="activeName&&nodeStateArray.length>1" type="primary" size="small"
                 class="ele-btn-icon" plain
                 @click="dialogFormVisible=true"> 切换审核的节点信息
      </el-button>
    </template>
    <search v-if="activeName" :routeType="routeType" :currentEvaluateConfigData="pageConfig" @search="reload"/>
    <el-divider style="margin: 16px 0 2px 0; opacity: 0.6"/>
    <!-- 表格 -->
    <ele-pro-table v-if="activeName" ref="tableRef"
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   v-model:selections="selections"
                   :show-overflow-tooltip="true"
                   tooltip-effect="light"
                   highlight-current-row
                   :footer-style="{ paddingBottom: '3px' }"
                   style="padding-bottom: 0">
      <template #toolbar>
        <el-button size="small"
                   class="ele-btn-icon"
                   @click="handleApproval()"> 批量审核
        </el-button>
        <el-button class="ele-btn-icon" size="small"
                   @click="openImport()">
          导入
        </el-button>
        <el-button class="ele-btn-icon" size="small"
                   @click="exportData()">
          导出
        </el-button>
        <el-button size="small"
                   @click="removeOpt()">
          删除
        </el-button>
      </template>
      <template #jjfmx="{ row }">
        <ele-tooltip content="审核" placement="left" effect="light">
          <el-link type="primary" underline="never" @click="openToEdit(row)">
            {{ row.jjfmx }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
  </ele-card>
  <Edit v-model="showEdit" :data="current" @done="reload" :enterType="enterType"/>
  <el-dialog v-model="showApprovalInfo" title="批量审批" width="500"
             :close-on-click-modal="false"
             draggable
             align-center>
    <!--      <ele-text size="md">请选择审批的状态：<br/></ele-text>-->
    <div style="margin-left: -8px;">
      <!--        :currentWorkflowId="workflow"-->
      <!--        :currentProjectData="currentProjectData"-->
      <approval-status ref="childFormRef"
                       :pageConfig="pageConfig"
                       :nodeData="checkedNode"
                       :currentNodeId="checkedNodeId"/>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showApprovalInfo = false">关闭</el-button>
        <el-button type="primary" plain @click="handleBatchOperation">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  <!--    <ApprovalInfo v-model="showApprovalInfo" :pageConfig="pageConfig" :nodeData="checkedNode"/>-->
  <!-- 多审核节点选择弹框 -->
  <el-dialog v-model="dialogFormVisible" title="系统提示" width="500"
             :show-close="false"
             :close-on-click-modal="false"
             draggable
             align-center>
    <ele-text size="md">你当前有多个审核节点，请选择你要审核的节点信息：<br/></ele-text>
    <div style="margin-left: -8px;">
      <ele-check-card v-model="checkedNodeId"
                      :items="nodeStateArray"
                      :item-style="{ margin: '8px', padding: '8px 12px', display: 'flex' }"
                      style="display: flex; flex-wrap: wrap">
        <template #item="{ item }">
          <el-icon :size="22" style="margin-right: 8px;">
            <SetUp/>
          </el-icon>
          {{ item.nodeName }}
        </template>
      </ele-check-card>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <!--          <el-button @click="dialogFormVisible = false">Cancel</el-button>-->
        <el-button type="primary" plain @click="handleDialogSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 导入弹窗 -->
  <import v-model="showImport" :currentNodeId="currentNodeId" @done="reload"/>
</template>

<script setup>
import {reactive, ref, toRaw, unref, watch,} from 'vue';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {
  getApprovalNodeList,
  getEvaluateApprovalNodeState,
  pageApprovalList,
  removes
} from '../api/index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import Edit from "../components/edit.vue";
import {comApproveStatus, insertAtIndex, toFormDataWj} from '@/utils/common_bak2.js';
import IconPark from "@/components/IconPark/index.vue";
import ApprovalStatus from "@/views/evaluate/approval/components/approvalStatus.vue";
import {approvalBatchOperation} from "../api/index.js";
import Search from "../components/search.vue";
import {ElMessageBox} from "element-plus";
import {FILE_IMPORTERROREXCEL} from "@/config/setting.js";
import {getToken} from "@/utils/token-util.js";
import {getCurrentRole} from "@/utils/current-role-util.js";
// import Import from "@/views/base-code/dwb/components/import.vue";
import Import from "./import.vue";

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
const routeType = pathArray[4];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

const props = defineProps({
  pageConfig: Object,
  routeType: String
});

/** 表格实例 */
const tableRef = ref(null);
/** 搜索栏实例 */
const searchRef = ref(null);

/** 返回 */
const onBack = () => {
  console.log(routeType)
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/evaluate/approval/' + routeType,
  })
};

const loading = ref(false)

const childFormRef = ref(null);
const handleBatchOperation = async () => {
  try {
    // 调用子组件的验证方法
    await childFormRef.value.validate();
    // 获取子组件的表单数据
    console.log("表单数据:", childFormRef.value.approvalForm);
    console.log("表单数据lastWhere.value:", lastWhere.value);

    // console.log(childFormRef.value.getFormData?.()); // 或调用方法
    // 提交逻辑...
    loading.value = true;
    let data = Object.assign({'applicationInfo.id': selections.value?.map((d) => d.id).join(',')}, lastWhere.value, childFormRef.value.approvalForm)
    console.log(JSON.stringify(data))
    let rData = toFormDataWj({...data})
    approvalBatchOperation(rData).then((data) => {
      console.log( data)
      loading.value = false;
      if (data.code === 0) {
        EleMessage.success(data.message);
      } else if (data.code === 2) {
        open(data)
      }
      showApprovalInfo.value = false
      reload()
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  } catch (error) {
    console.error("表单验证失败", error);
  }
};

const open = (data) => {
  ElMessageBox.confirm(
    data.message,
    '批量审核提示',
    {
      confirmButtonText: '下载',
      cancelButtonText: '关闭',
      type: 'warning',
    }
  ).then(() => {
    window.location.href = BASE_URL + FILE_IMPORTERROREXCEL + '?id=' + data.data + '&access_token=' + accessToken
  }).catch(() => {
    EleMessage({
      type: 'info',
      message: 'Delete canceled',
    })
  })
}
/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'zbmc',
      label: '指标名称',
    },
    {
      prop: 'jjfmx',
      slot: 'jjfmx',
      label: '加分明细',
    },
    {
      prop: 'jfyy',
      label: '加分原因',
    },
    {
      prop: 'fs',
      label: '分数',
    },
    {
      prop: 'xymc',
      label: '所属院系',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
    {
      prop: 'createTime',
      label: '创建时间',
    },
  ]
);

/** 标签页选中 */
const activeName = ref(null);
const tabsItems = ref([])
const nodeStateArray = ref(null);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);
/** 是否显示用户导入弹窗 */
const showImport = ref(false);

/** 打开导入弹窗 */
const openImport = () => {
  showImport.value = true;
};
/** 用户名筛选值 */
const nameFilterValue = ref('');
const enterType = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

watch(
  activeName,
  (newVal) => {
    if (newVal) reload()
  },
  {immediate: true}
)

const openToEdit = (row) => {
  row.nodeId = currentNodeId.value
  current.value = Object.assign(row, lastWhere.value);
  enterType.value = activeName.value === "待审批" ? "shenpi" : "preview"
  showEdit.value = true
};

let checkedNode = ref(null)
let checkedNodeId = ref(null)

let backNodeId = null;
checkedNodeId.value = backNodeId;


const currentNodeId = ref(null);
const dialogFormVisible = ref(false);
/** 查询tabs */
const queryNodeState = () => {
  if (props.pageConfig) {
    getEvaluateApprovalNodeState({
      projectId: props.pageConfig.id,
      year: props.pageConfig.cpnf,
    }).then((data) => {
      if (data) {
        data.forEach(item => {
          item.value = item.nodeId;
        })
        nodeStateArray.value = data ?? [];
        if (data.length > 1) {
          if (checkedNodeId.value) {//审核页返回到列表处理
            checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value)
            handleDialogSubmit()
          } else {
            dialogFormVisible.value = true
          }
        } else {
          // checkedNode.value = data[0]
          checkedNodeId.value = data[0].nodeId
          handleDialogSubmit()
        }
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    }).finally(() => {
    });
  }
};
let activeNameBack = ""

const handleDialogSubmit = () => {
  if (!checkedNodeId.value) {
    EleMessage.error('请选择你要审核的节点信息');
    return false;
  } else {
    checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value)
    let baseItmes = comApproveStatus()
    baseItmes.forEach(item => {
      item.label = checkedNode.value[item.diyname]
      item.name = checkedNode.value[item.prename]
    })
    insertAtIndex(baseItmes, {
      label: '待审批',
      name: '待审批',
    }, 0);
    //返回后在当前tab下刷新
    activeName.value = activeNameBack ? activeNameBack : baseItmes[0].name
    tabsItems.value = baseItmes
    dialogFormVisible.value = false

    currentNodeId.value = checkedNode.value.nodeId;
    reload()
  }
}
const showApprovalInfo = ref(false)
const sqIds = ref(null)
const handleApproval = (row) => {
  const rows = row == null ? selections.value : [row];
  // if (!rows.length) {
  //   EleMessage.error('请至少选择一条数据');
  //   return;
  // }
  // sqIds.value = rows.map((d) => d.id)
  showApprovalInfo.value = true;
};

/** 审核节点下拉框数据 */
const optionsNodes = ref([]);
const queryApprovalNodeList = async () => {
  if (props.pageConfig) {
    const rData = await getApprovalNodeList({configId: props.pageConfig.id, cpnf: props.pageConfig.cpnf});
    let rArray = []
    if (rData) {
      let obj = {}
      rData.forEach((d) => {
        obj = {
          value: d.id,
          label: d.nodeName,
        }
        rArray.push(obj)
      });
    }
    optionsNodes.value = rArray

    if (rArray.length > 0) {
      currentNodeId.value = rArray[0].value
      queryNodeState()
    }
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  let whereData = {
    ...where, ...orders, ...filters,
    'applicationInfo.page': page,
    'applicationInfo.limit': limit,
    'approvalNode.nodeId': currentNodeId.value,
    'applicationInfo.configId': props.pageConfig?.id,
    'approvalNode.result': activeName?.value,
    'applicationInfo.type': props.routeType
  }
  lastWhere.value = whereData
  return pageApprovalList(whereData);
};

/** 导出excel */
const exportData = () => {
  const raw = toRaw(lastWhere.value);
  const cleaned = Object.fromEntries(
    Object.entries(raw).filter(([_, v]) => v !== undefined)
  );
  let p = new URLSearchParams(cleaned).toString();
  window.location.href = BASE_URL + 'api/evaluate/approval/export?access_token=' + accessToken + "&" + p;
};

/** 删除 */
const removeOpt = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除选中的数据吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id),currentNodeId.value).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

watch(
  props.pageConfig,
  (pageConfig) => {
    if (pageConfig) queryApprovalNodeList()
  },
  {immediate: true}
)

</script>

<script>
export default {
  name: 'APPROVALINDEX'
};
</script>

