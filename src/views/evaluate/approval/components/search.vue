<!-- 搜索表单 -->
<template>
<!--  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">-->
<!--    <template #header>-->
      <el-form style="margin-top: 6px; margin-bottom: -12px;"
               label-width="60px"
               size="small" @keyup.enter="search"
               @submit.prevent="">
        <el-row :gutter="8">
          <el-col :md="6" :sm="12" :xs="24">
            <el-form-item label="学号">
              <el-input clearable
                        v-model.trim="formData['applicationInfo.xgh']"
                        placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :md="6" :sm="12" :xs="24">
            <el-form-item label="姓名">
              <el-input clearable
                        v-model.trim="formData['applicationInfo.xm']"
                        placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年级">
              <dict-data placeholder="请选择年级"
                         code="nj"
                         type="multipleSelect"
                         :model-value="formData['njidArray']"
                         @update:modelValue="(value) => updateValue('njid',value)"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="培养层次">
              <dict-data placeholder="请选择培养层次"
                         code="pycc"
                         type="multipleSelect"
                         :model-value="formData['pyccidArray']"
                         @update:modelValue="(value) => updateValue('pyccid',value)"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="院系">
              <dict-data placeholder="请选择院系"
                         code="xymc"
                         type="multipleSelect"
                         :model-value="formData['xyidArray']"
                         @update:modelValue="(value) => updateValue('xyid',value)"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="指标名称">
              <zbSelect v-model="formData['applicationInfo.itemId']" :routeType="routeType"
                        :configId="currentEvaluateConfigData?.id"
                        placeholder="请选择上级指标"/>
              <!--              <dict-data placeholder="请选择指标名称"-->
              <!--                         code="xymc"-->
              <!--                         type="multipleSelect"-->
              <!--                         :model-value="formData['xyidArray']"-->
              <!--                         @update:modelValue="(value) => updateValue('xyid',value)"/>-->
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label=" ">
              <el-button type="primary" size="small" plain @click="search">查询</el-button>
              <el-button @click="reset" size="small">重置</el-button>
            </el-form-item>
          </el-col>
          <!--       <el-col  :md="6" :sm="12" :xs="24">-->
          <!--        <el-form-item label="测评年份">-->
          <!--          <el-select v-model="form.cpnf" placeholder="请选择"-->
          <!--                     @change="selectChange">-->
          <!--            <el-option v-for="item in optionsCpnf"-->
          <!--                       :key="item.value"-->
          <!--                       :label="item.label"-->
          <!--                       :value="item.value"/>-->
          <!--          </el-select>-->
          <!--        </el-form-item>-->
          <!--      </el-col>-->
          <!--       <el-col  :md="6" :sm="12" :xs="24">-->
          <!--        <el-form-item label="审批节点">-->
          <!--          <el-select v-model="form.nodeId" placeholder="请选择">-->
          <!--            <el-option v-for="item in optionsNodes"-->
          <!--                       :key="item.value"-->
          <!--                       :label="item.label"-->
          <!--                       :value="item.value"/>-->
          <!--          </el-select>-->
          <!--        </el-form-item>-->
          <!--      </el-col>-->
          <!--          <el-col :md="6" :sm="12" :xs="24">-->
          <!--            <el-form-item label-width="16px">-->
          <!--              <el-button type="primary" @click="search">查询</el-button>-->
          <!--              <el-button @click="reset">重置</el-button>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>
      </el-form>
<!--    </template>-->
<!--  </ele-card>-->
</template>

<script setup>
import {ref, unref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
// import {useRouter} from "vue-router";
// import {useUserStore} from "@/store/modules/user";
// import {storeToRefs} from "pinia";
import {getApprovalNodeList, getYearList} from "@/views/evaluate/approval/api/index.js";
import {removeDuplicates} from '@/utils/common_bak2.js';
import ZbSelect from "../../item/components/zb-select.vue";

const props = defineProps({
  routeType: String,
  currentEvaluateConfigData: Object,
});


const emit = defineEmits(['search']);


const formData = ref({});
/** 更新值 */
const updateValue = (prop, value) => {
  formData["value"][prop + 'Array'] = value;
  formData["value"]['applicationInfo.' + prop] = formData["value"][prop + 'Array'].join()
}

/** 搜索 */
const search = () => {
  console.log(formData["value"])
  emit('search', {...formData["value"]});
};

/**  重置 */
const reset = () => {
  formData["value"] = {}
  search();
};

</script>
