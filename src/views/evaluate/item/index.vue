<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="28%"
                     :min-size="340"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <ele-card :body-style="{ padding: '5px 0!important' }">
        <template #header>
          指标管理
          <ele-tooltip style="cursor: pointer;" v-if="currentEvaluateConfigData?.state==='启用'" effect="light"
                       content="左侧树还支持鼠标右键"
                       placement="left" :offset="3">
            <IconPark name="help" size="18" strokeWidth="3"/>
          </ele-tooltip>
        </template>
        <template #extra>
          <div style="display: flex; align-items: center" @click="openEdit(void 0)">
            <IconPark name="add-one" size="18" strokeWidth="3"/>
            <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
              新建指标
            </ele-text>
          </div>

          <el-icon :size="15" style="margin: 0 2px 0 1px" color="var(--el-color-primary)">
            <ColumnHeightOutlined @click="expandAll"/>
          </el-icon>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm" @click="expandAll">
            展开全部
          </ele-text>

          <el-icon :size="13" style="margin: 0 2px 0 1px" color="var(--el-color-primary)">
            <VerticalAlignMiddleOutlined @click="foldAll"/>
          </el-icon>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm" @click="foldAll">
            折叠全部
          </ele-text>
        </template>
        <div :style="{height: (pageHeight-3)+'px', overflow:'auto'}">
          <div v-if="setEnterType!=='config'" style="padding: 5px 5px 8px">
            <el-select v-model="configId" placeholder="请选择测评年份"
                       @change="selectCpnfChange">
              <el-option v-for="item in cpnfOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"/>
            </el-select>
          </div>
          <ele-loading v-if="cpnfOptions.length>0" :loading="loading">
            <el-tree ref="treeRef"
                     :data="treeData"
                     highlight-current
                     :indent="20"
                     node-key="id"
                     :props="{ label: 'zbmc' }"
                     :expand-on-click-node="false"
                     :default-expand-all="true"
                     :filter-node-method="filterNode"
                     :style="{
                              '--ele-tree-item-height': '34px',
                              '--ele-tree-expand-padding': '8px',
                              '--ele-tree-expand-margin': 0
                            }"
                     @node-click="handleNodeClick"
                     @node-contextmenu="handleTreeContextmenu">
              <template #default="{ data: d }">
                <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.zbmc }}
                        </span>
                      </span>
                      <!-- 图标容器 -->
                      <span v-if="selectedKey===d.id&&currentEvaluateConfigData?.state==='启用'" class="icon-container">
                         <ele-tooltip style="cursor: pointer;" effect="light"
                                      content="编辑">
                            <a @click="openEdit(d)">
                               <IconPark name="edit-two" size="18" strokeWidth="3" color="#d0021b"/>
                            </a>
                        </ele-tooltip>
                         <ele-tooltip style="cursor: pointer;" effect="light" content="删除">
                            <a @click="remove(d)">
                              <IconPark style="margin-left: 10px;" name="delete" size="18" strokeWidth="3"
                                        color="#d0021b"/>
                            </a>
                         </ele-tooltip>
                      </span>
                    </span>
                </div>
              </template>

            </el-tree>
          </ele-loading>
        </div>
      </ele-card>
      <template #body>
        <template v-if="current">
          <itemDetail :itemId="current.id"
                      :currentData="current"
                      :configId="configId"
                      :routeType="routeType"
                      :enterType="enterType" :isCanOpt="currentEvaluateConfigData?.state==='启用'"/>
        </template>
        <template v-else>
          <el-result title="">
            <template #icon>
              <empty-page/>
            </template>
            <template #sub-title>
              <ele-text type="placeholder">请在左侧选择指标</ele-text>
            </template>
          </el-result>
        </template>
      </template>
    </ele-split-panel>

    <edit v-model="showEdit" :data="editData" :organization-id="organizationId"
          :routeType="routeType"
          :currentEvaluateConfigData="currentEvaluateConfigData" @done="queryEvaluateItem"/>
    <ele-dropdown
      ref="ctxMenuDropdownRef"
      trigger="contextmenu"
      :trigger-keys="[]"
      :icon-props="{ size: 15 }"
      :popper-options="{
        strategy: 'fixed',
        modifiers: [{ name: 'offset', options: { offset: [0, -4] } }]
      }"
      :persistent="false"
      component-type="pro"
      :prevent-contextmenu="true"
      :virtual-triggering="true"
      :virtual-ref="ctxMenuDropdownVirtualRef"
      :disabled="!ctxMenuDropdownItems.length"
      :items="ctxMenuDropdownItems"
      :popper-style="ctxMenuDropdownStyle"
      @command="handleItemCommand"
    />
  </ele-page>
</template>

<script setup>
import {ref, nextTick, watch, reactive, markRaw, unref, onMounted,} from 'vue';
import {useUserStore} from "@/store/modules/user";
import {ElMessageBox} from 'element-plus/es';
import {
  EleMessage,
  toTree,
  queryChild,
  ElePage,
  EleCard,
  EleDropdown,
  EleSplitPanel,
  EleLoading
} from 'ele-admin-plus/es';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@/components/icons';
import {useMobile} from '@/utils/use-mobile';
import itemDetail from '../item-detail/index.vue';
import Edit from './components/edit.vue';

import {getEvaluateItem, removes} from "@/views/evaluate/item/api/index.js";
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {getEvaluateConfig, getEvaluateConfigById} from "@/views/evaluate/evaluate-config/api/index.js";
import {getCodeDwb} from "@/views/base-code/dwb/api/index.js";
import {getCodeData} from "@/views/base-code/dictionary/api/data-index.js";
import IconPark from "@/components/IconPark/index.vue";
import {storeToRefs} from "pinia";
import EmptyPage from "@/components/illustrations/empty-page.vue";
import {ColumnHeightOutlined, VerticalAlignMiddleOutlined} from "ele-admin-plus/es/icons";
import {ArrowDown} from "@/components/icons/index.js";
import {ElLoading} from 'element-plus';

defineOptions({name: 'EvaluateItem'})

const props = defineProps({
  currentData: Object,
  setEnterType: String,
  setConfigId: String,
  RandomString: String,
});


// 已缓存的字典
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push, replace} = useRouter();
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} =
  usePageTab();
const {params, path, query,} = unref(currentRoute);

const enterType = ref(null);
let pathArray = path.split("/");
console.log(pathArray)
let routeType = pathArray[3]
let currentConfigId = query?.configId ?? props.setConfigId;
enterType.value = query?.enterType ?? props.setEnterType;
console.log(currentConfigId)
const cpnfOptions = ref([]);
const configId = ref(null);

/** 是否是移动端 */
const {mobile} = useMobile();

/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const treeData = ref([]);

/** 选中数据 */
const current = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示表单弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);

/** 添加时上级 */
const organizationId = ref();

/** 查询 */
const queryEvaluateItem = () => {
  loading.value = true;
  getEvaluateItem({configId: configId.value}).then((list) => {
    loading.value = false;
    treeData.value = toTree({
      data: list,
      idField: 'id',
      parentIdField: 'parentId'
    });
    nextTick(() => {
      handleNodeClick(treeData.value[0]);
    });
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  // 移动端自动收起左侧
  if (current.value != null && mobile.value) {
    splitRef["value"]?.toggleCollapse?.(true);
  }
  if (row && row.id) {
    current.value = row;
    treeRef["value"]?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id;

    // Ensure the node's parents are expanded
    const node = treeRef.value?.getNode(row.id);
    if (node) {
      let parent = node.parent;
      while (parent) {
        parent.expanded = true;
        parent = parent.parent;
      }
    }
  } else {
    current.value = null;
  }
};

/** 打开编辑弹窗 */
const openEdit = (item, checkedId) => {
  editData.value = item ?? null;
  organizationId.value = checkedId
  if (!item) {
    treeRef["value"]?.setCurrentKey?.(null);
    current.value = null
  }
  showEdit.value = true;
};

/** 删除 */
const remove = (item) => {
  const row = item;
  if (!row) {
    return;
  }
  ElMessageBox.confirm(`确定要删除“${row.zbmc}”吗?`, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes([row.id]).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      queryEvaluateItem();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 展开全部 */
const expandAll = () => {
  loading.value = true;
  nextTick(() => {
    const nodes = treeRef.value?.store?.nodesMap;
    if (nodes) {
      Object.keys(nodes).forEach(key => {
        nodes[key].expanded = true;
      });
    }
    loading.value = false;
  });
};

/** 折叠全部 */
const foldAll = () => {
  loading.value = true;
  nextTick(() => {
    const nodes = treeRef.value?.store?.nodesMap;
    if (nodes) {
      Object.keys(nodes).forEach(key => {
        nodes[key].expanded = false;
      });
      if (current.value?.id) {
        const currentNode = nodes[current.value.id];
        if (currentNode) {
          currentNode.expanded = true;
          let parent = currentNode.parent;
          while (parent) {
            parent.expanded = true;
            parent = parent.parent;
          }
        }
      }
    }
    loading.value = false;
  });
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.zbmc && data.zbmc.includes(value));
  }
  return true;
};

const selectCpnfChange = (event) => {
  if (event) {
    configId.value = event
    treeData.value = null;
    currentEvaluateConfigData.value = null;
    if (configId.value) queryEvaluateConfigById(configId.value)
    queryEvaluateItem();
  }
};
/** 右键菜单组件 */
const ctxMenuDropdownRef = ref(null);

/** 右键菜单数据 */
const ctxMenuDropdownItems = ref([]);

/** 右键菜单虚拟触发节点 */
const ctxMenuDropdownVirtualRef = ref();

/** 当前打开的右键菜单对应的数据 */
let ctxMenuCurrentData = null;

/** 右键菜单调整位置 */
const ctxMenuDropdownStyle = reactive({marginLeft: '0px'});

/** 获取右键菜单数据 */
const getContextMenus = (_item) => {
  return [
    {title: '添加下级', command: 'add', icon: markRaw(PlusOutlined)},
    {title: '修改指标', command: 'edit', icon: markRaw(EditOutlined)},
    {
      title: '删除指标',
      command: 'del',
      icon: markRaw(DeleteOutlined),
      divided: true,
      danger: true
    }
  ];
};

/** 打开右键菜单 */
const openCtxMenuDropdown = (triggerEl, item) => {
  ctxMenuDropdownRef["value"] && ctxMenuDropdownRef["value"].handleClose();
  nextTick(() => {
    ctxMenuCurrentData = item;
    ctxMenuDropdownItems.value = getContextMenus(item) || [];
    ctxMenuDropdownVirtualRef.value = triggerEl;
    if (ctxMenuDropdownItems.value.length) {
      nextTick(() => {
        ctxMenuDropdownRef["value"] && ctxMenuDropdownRef["value"].handleOpen();
      });
    }
  });
};

/** 右键菜单项点击事件 */
const handleItemCommand = (command) => {
  if (ctxMenuCurrentData == null) {
    return;
  }
  if (command === 'add') {
    openEdit(void 0, ctxMenuCurrentData.id);
  } else if (command === 'edit') {
    openEdit(ctxMenuCurrentData);
  } else if (command === 'del') {
    remove(ctxMenuCurrentData);
  }
};

/** 树组件右键事件 */
const handleTreeContextmenu = (e, item) => {
  if (currentEvaluateConfigData.value?.state === '启用') {
    const triggerEl = queryChild(e.currentTarget, 'el-tree-node__content');
    const rect = triggerEl.getBoundingClientRect();
    // ctxMenuDropdownStyle.marginLeft = `${e.clientX - rect.left - rect.width / 2}px`;
    openCtxMenuDropdown(triggerEl, item);
  }
};


/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});

const queryEvaluateConfig = async () => {
  const rData = await getEvaluateConfig({type: routeType});
  let rArray = []
  if (rData) {
    let obj = {}
    rData.forEach((d) => {
      obj = {
        value: d.id,
        label: d.title,
      }
      rArray.push(obj)
    });
    if (rArray.length > 0) {
      configId.value = currentConfigId ? currentConfigId : rArray[0].value
      if (configId.value) queryEvaluateConfigById(configId.value)
      queryEvaluateItem();
    }
  }
  cpnfOptions.value = rArray
};
const currentEvaluateConfigData = ref(null)

const queryEvaluateConfigById = (configIdValue) => {
  getEvaluateConfigById(configIdValue).then((resData) => {
    if (resData) {
      currentEvaluateConfigData.value = resData
      if (resData.pyccid) {
        queryCodeData('pycc', resData.pyccid)
      }
      if (resData.njid) {
        queryCodeData('nj', resData.njid)
      }
      if (resData.xyid) {
        queryCodeDwb(resData.xyid)
      }
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

const queryCodeData = (code, ids) => {
  loading.value = true;
  let newObj = {
    codeType: code,
    id: ids,
  }
  getCodeData(newObj).then((list) => {
    userStore.setDicts([], code + '_evaluate');
    if (list) {
      loading.value = false;
      userStore.setDicts(list, code + '_evaluate');
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

const queryCodeDwb = (ids) => {
  userStore.setDicts([], 'dwb_evaluate');
  loading.value = true;
  let newObj = {
    id: ids,
  }
  getCodeDwb(newObj).then((list) => {
    if (list) {
      loading.value = false;
      userStore.setDicts(list, 'dwb_evaluate');
    }
  });
}

queryEvaluateConfig()
</script>
