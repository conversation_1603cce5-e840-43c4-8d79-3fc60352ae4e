/**
 * 综合测评指标
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getEvaluateItem(params) {
    const res = await request.get('/evaluate/evaluate-item', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/evaluate/evaluate-item/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 根据id查询
 */
export async function getEvaluateItemById(id) {
    const res = await request.get('/evaluate/evaluate-item/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/evaluate/evaluate-item/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/evaluate/evaluate-item/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
