<!-- 上级菜单选择下拉框 -->
<template>
  <el-tree-select clearable
                  filterable
                  lazy
                  :load="loadTreeData"
                  check-strictly
                  node-key="id"
                  :props="{ label: 'zbmc' }"
                  :placeholder="placeholder"
                  :model-value="modelValue || void 0"
                  class="ele-fluid"
                  @update:modelValue="updateValue"/>
</template>

<script setup>
import {ElMessage as EleMessage} from "element-plus";
import {getEvaluateItem} from "@/views/evaluate/item/api/index.js";

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  /** 选中的菜单 */
  modelValue: [Number, String],
  /** 提示信息 */
  placeholder: {
    type: String,
    default: '请选择上级菜单'
  },
  routeType: String,
  configId: String,
});

/** 更新选中数据 */
const updateValue = (value) => {
  emit('update:modelValue', value || 0);
};

/** 懒加载 获取菜单数据*/
const loadTreeData = (node, resolve) => {
  let obj = {parentId: node.data?.id ?? '', type: props.routeType, configId: props.configId}
  getEvaluateItem(obj).then((data) => {
    resolve(data);
  }).catch((e) => {
    EleMessage.error(e.message);
    resolve([]);
  });
};

</script>
