<template>
  <ele-drawer size="36%"
              :title="isUpdate ? '修改指标 ['+data.zbmc+']' : '添加指标'"
              :append-to-body="true"
              style="max-width: 100%;"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             label-position="top"
             labelWidth="auto">
      <el-row :gutter="8">
        <template v-for="item in baseFileds">
          <el-col :md="item.colProps" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
            <template v-if="item.prop==='parentId'">
              <el-form-item label="上级指标" prop="parentId">
                <zbSelect v-model="form.parentId" :routeType="routeType" :configId="currentEvaluateConfigData?.id"
                          placeholder="请选择上级指标"/>
              </el-form-item>
            </template>
            <template v-else-if="item.prop==='cjly'">
              <el-form-item :label="item.label">
                <dict-data :placeholder="'请选择'+item.label"
                           code="zbcjly"
                           type="select"
                           :dicQueryParams="{
                            dictFieldUrl: '/code/score-source-config',
                            valueField: 'id',
                            textField: 'name',
                            params:{type:routeType}
                         }"
                           :model-value="form[item.prop]"
                           @update:modelValue="(value) => updateFormValue(item,item.prop, value)"/>
              </el-form-item>
            </template>
            <ProFormItem v-else :item="item"
                         :model="form"
                         @updateItemValue="(prop,value) => updateFormValue(item,prop, value, 'change')">
              <template
                v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)">{{ enterType === 'preview' ? '关闭' : '取消' }}
      </el-button>
      <el-button v-if="enterType !== 'preview'" plain size="small" type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {getEvaluateItemById, operation} from "../api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";
import zbSelect from "./zb-select.vue"

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  enterType: String,//perview：考试详情预览 add:添加考试 edit:编辑考试
  /** 添加时机构id */
  organizationId: String,
  routeType: String,
  currentEvaluateConfigData: Object,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  configId: '',
  cpnf: '',
  parentId: '',
  zbmc: '',
  xyid: '',
  pyccid: '',
  cjly: '',
  type: '',
  zblx: '',
  sfcyjs: '',
  sfxs: null,
  bfb: null,
  zdz: '',
  gdjf: 0,
  remark: '',
});

const updateFormValue = (item, prop, newVal,mark) => {
  console.log(item, prop, newVal)
  let value = newVal
  form[item.prop] = value;
  if (item.fieldLinks) {
    //获取选择值下面所有的显示字段信息
    let showfields = item.fieldLinks.filter(f => {
      return f.fieldVal === value
    })
    if (showfields.length > 0) {
      showfields.forEach(sf => {
        nextTick(() => {
          //切换选择值，清空之前选项所赋值
         if(mark === 'change') form[sf.linkField] = '';
          //动态切换选择值，关联具体字段的显示隐藏
          baseFileds.value.filter((init => {
              init.showFlag = sf.linkField === init.prop ? sf.showFlag : init.showFlag
              init.selfModifyFlag = init.showFlag
            }
          ))
        });
      })
    }
  }
}

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 提交 */
const onSubmit = () => {
  console.log('form:', JSON.stringify(form));
  formRef["value"]?.validate?.((valid, obj) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    if (form.pyccid) form.pyccid = form.pyccid.join(',')
    if (form.xyid) form.xyid = form.xyid.join(',')
    let data = {
      ...form,
      parentId: form.parentId === 0 ? "" : form.parentId,
    };
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
const baseFileds = ref([])
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);


const queryEvaluateItemInfo = () => {
  getEvaluateItemById(props.data.id).then((resData) => {
    if (resData) {
      if (resData.kssj && resData.jssj) {
        resData.kssjfw = [resData.kssj, resData.jssj]
      }
      if (resData.selectorDatas && resData.selectorDatas.length > 0) {
        resData.sfzdry = '是'
        perSelectedData.value = resData.selectorDatas
      }
      if (resData.pyccid) resData.pyccid = resData.pyccid.split(',')
      if (resData.xyid) resData.xyid = resData.xyid.split(',')
      assignFields({
        ...resData,
      })

      let filedItem = baseFileds.value.filter(obj => obj.prop === 'zblx')
      if (filedItem.length > 0) {
        updateFormValue(filedItem[0], 'zblx', resData.zblx)
      }
      isUpdate.value = true;
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      baseFileds.value = [
        {
          prop: 'parentId', label: '上级指标',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'zbmc', label: '指标名称', type: 'input', required: true,
          showFlag: '是',
          selfModifyFlag: '是',
          colProps: {span: 12},
        }, {
          prop: 'cjly',
          label: '成绩来源',
          type: 'select',
          options: [
            {label: '平均成绩', value: '平均成绩'},
            {label: '加权成绩', value: '加权成绩'},
            {label: '体育成绩', value: '体育成绩'},
          ],
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
          colProps: {span: 12},
        },
        {
          prop: 'zblx', label: '记分方式',
          type: 'radioButton',
          options: [{label: '比例', value: '比例'},
            {label: '分值', value: '分值'},
          ],
          // colProps: {span: 8},
          showFlag: '是',
          selfModifyFlag: '是',
          fieldLinks: [
            {
              "fieldEn": "zblx",
              "fieldVal": "比例",
              "showFlag": "是",
              "linkFieldDataType": null,
              "linkFieldDataUrl": null,
              "linkField": "bfb",
            },
            {
              "fieldEn": "zblx",
              "fieldVal": "比例",
              "showFlag": "否",
              "linkFieldDataType": null,
              "linkFieldDataUrl": null,
              "linkField": "zdz",
            },
            {
              "fieldEn": "zblx",
              "fieldVal": "分值",
              "showFlag": "是",
              "linkFieldDataType": null,
              "linkFieldDataUrl": null,
              "linkField": "zdz",
            },
            {
              "fieldEn": "zblx",
              "fieldVal": "分值",
              "showFlag": "否",
              "linkFieldDataType": null,
              "linkFieldDataUrl": null,
              "linkField": "bfb",
            },
          ],
          required: true ,
          colProps: {span: 12},
        },
        {
          prop: 'sfcyjs',
          label: '是否参与计算',
          type: 'switch',
          required: true,
          showFlag: '是',
          selfModifyFlag: '是',
          colProps: {span: 6}
        },
        {
          prop: 'sfxs',
          label: '是否显示',
          type: 'switch',
          required: true,
          showFlag: '是',
          selfModifyFlag: '是',
          colProps: {span: 6}
        },
        {
          prop: 'bfb', label: '指标百分比', type: 'inputNumber', required: true,
          showFlag: '否',
          selfModifyFlag: '否',
          colProps: {span: 12},
        },
        {
          prop: 'zdz', label: '加分项最大值', type: 'inputNumber', required: true,
          showFlag: '否',
          selfModifyFlag: '否',
          colProps: {span: 12},
        },
        {
          prop: 'gdjf', label: '固定加分', type: 'inputNumber', required: true,
          showFlag: '是',
          selfModifyFlag: '是',
          colProps: {span: 12},
        },
        // {prop: 'zdz', label: '综测成绩最大值', type: 'inputNumber', required: true},
        {
          prop: 'remark', label: '备注', type: 'textarea',
          showFlag: '是',
          selfModifyFlag: '是',
        },
      ]
      if (props.data) {
        queryEvaluateItemInfo()
      } else {
        form.parentId = props.organizationId;
        form.configId = props.currentEvaluateConfigData?.id;
        form.cpnf = props.currentEvaluateConfigData?.cpnf;
        form.type = props.routeType;
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  },
);
watch(
  () => props.enterType,
  (enterType) => {
    if (enterType) {
      baseFileds.value.forEach(filed => {
        if (enterType === 'preview') {
          if (filed.type === "dictSelect") {
            filed.props.disabled = props.enterType === 'preview';
            filed.props.readonly = props.enterType === 'preview';
          } else if (filed.type === "switch" || filed.type === "radioButton") {
            filed['props'] = {
              "readonly": props.enterType === 'preview',
              "disabled": props.enterType === 'preview',
            }
          } else {
            filed['props'] = {
              "readonly": props.enterType === 'preview',
              // "disabled": props.enterType === 'preview',
            }
          }
        }
      })
    }
  },
);
</script>
