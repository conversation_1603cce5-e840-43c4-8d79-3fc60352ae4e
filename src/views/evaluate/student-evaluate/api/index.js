/**
 * 本班学生互评功能
 */
import request from '@/utils/request.js';

/**
 * 分页查询本班学生互评 管理端
 */
export async function getEvaluatePeerReviewScorePage(params) {
  const res = await request.get('/evaluate/evaluate-review-score/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 计算学生互评平均分 管理端
 */
export async function operationEvaluateReviewScore(data) {
  const res = await request.post(
    '/evaluate/evaluate-review-score/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除学生互评平均分 管理端
 */
export async function removesEvaluateReviewScore(data) {
  const res = await request.post(
    '/evaluate/evaluate-review-score/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询某个学生本班学生互评列表 管理端
 */
export async function getEvaluatePeerReviewRecordPage(params) {
  const res = await request.get('/evaluate/evaluate-peer-review-record/page', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 学院评分 管理端
 */
export async function updateDeptScore(data) {
  const res = await request.post(
    '/evaluate/evaluate-review-score/updateDeptScore',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导入
 */
export async function importData(file, configId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('configId', configId);
  // formData.append('codeType', codeType);
  const res = await request.post(
    '/evaluate/evaluate-review-score/dataImport',
    formData
  );
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}
