<!-- 更新学生状态 -->
<template>
  <ele-modal
    :width="460"
    :close-on-click-modal="false"
    title="更新学生状态"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" size="small" :model="form" @submit.prevent="">
      <el-form-item label="分数">
        <el-input-number
          :max="5"
          :min="0"
          controls-position="right"
          class="ele-fluid"
          :model-value="xypf"
          size="small"
          :precision="2"
          @update:modelValue="(value) => updateValue(value)"
          @blur="handleBlur()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)" size="small" plain
        >取消</el-button
      >
      <el-button
        size="small"
        plain
        type="primary"
        :loading="loading"
        @click="save"
      >
        提交
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { updateDeptScore } from '../api/index';

  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { useRouter } from 'vue-router';
  import { useFormData } from '@/utils/use-form-data.js';

  /** 当前用户角色*/

  const emit = defineEmits(['done', 'update:modelValue']);

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const props = defineProps({
    /** 是否打开弹窗 */
    modelValue: Boolean,
    updataScoreDatas: {
      type: Array,
      default: () => []
    }
  });

  /** 导入请求状态 */
  const loading = ref(false);
  const formRef = ref(null);
  const xypf = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({});
  const updateValue = (value) => {
    xypf.value = value;
  };
  const handleBlur = () => {
    if (xypf.value) {
      xypf.value = xypf.value.toFixed(2);
    }
  };
  /** 上传 */
  const save = () => {
    if (!xypf.value) {
      EleMessage.error('请输入分数');
      return;
    }
    loading.value = true;
    let data = props.updataScoreDatas;
    data.map((item) => {
      item.xypf = xypf.value;
    });
    updateDeptScore(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.updataScoreDatas) {
          assignFields(props.updataScoreDatas);
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped></style>
