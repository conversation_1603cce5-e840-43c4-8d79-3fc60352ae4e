<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search @search="searchReload" ref="searchRef" />
      </template>
      <!-- 表格  loadOnCreated：默认不请求数据，等待search中的年份被赋值后再进行请求-->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { nextTick, onMounted, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage } from 'element-plus';
  import { getEvaluatePeerReviewRecordPage } from '../api/index.js';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { ElLoading } from 'element-plus';
  import Search from './components/search.vue';
  import { getToken } from '@/utils/token-util';
  import { usePageTab } from '@/utils/use-page-tab.js';
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();

  let pathArray = path.split('/');
  console.log(pathArray);
  let userType = pathArray[4];
  let configId = pathArray[5];
  let xgh = pathArray[6];
  /** 表格实例 */
  const tableRef = ref(null);
  /** 检索条件 */
  const searchRef = ref(null);

  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});

  /** 加载状态 */
  const loading = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'cpnf',
      label: '测评年份'
    },
    { prop: 'xgh', label: '学号' },
    { prop: 'xm', label: '姓名' },
    { prop: 'bjmc', label: '班级' },
    { prop: 'raterAccount', label: '评分者学号' },
    { prop: 'rater', label: '评分者' },
    // { prop: 'pyccmc', label: '培养层次' },
    { prop: 'score', label: '评分' }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getEvaluatePeerReviewRecordPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      configId,
      xgh
      // type: routeType
    });
    res.list.map((item) => {
      if (item.scoreDetail) {
        let scoreDetail = JSON.parse(item.scoreDetail);
        Object.entries(scoreDetail).map(([key, value]) => {
          item[key] = value;
        });
      }
    });
    return {
      list: res.list,
      count: res.count
    };
  };

  const searchReload = (where) => {
    searchWhere.value = where;
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'TEACHERRESULTINDEX'
  };
</script>
<style lang="scss" scoped>
  .link-text {
    color: var(--el-color-primary);
    cursor: pointer;
  }
</style>
