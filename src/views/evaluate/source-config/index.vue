<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <ele-pro-table ref="tableRef"
                     flex-table
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     tooltip-effect="light"
                     v-model:selections="selections"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()">
            新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()">
            删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit :data="current"
          v-model="showEdit"
          :routeType="routeType"
          @done="reload"/>
  </ele-page>
</template>

<script setup>
import {ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import Edit from './components/edit.vue';
import {queryPage, removes} from "./api/index.js";
import {useRouter} from "vue-router";

const props = defineProps({});

const {push, currentRoute, replace} = useRouter();
const {path, query} = unref(currentRoute);
let pathArray = path.split("/");
console.log(pathArray)
let routeType = pathArray[3]
/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'name',
    slot: 'name',
    label: '成绩类型名称',
  },
  {
    prop: 'tableName',
    label: '存储成绩表名',
  },
  {
    prop: 'xhColumn',
    label: '学号',
  },
  {
    prop: 'cpnfColumn',
    label: '测评年份',
  },
  {
    prop: 'scoreColumn',
    label: '成绩',
  },
  {
    prop: 'sort',
    label: '排序',
    sortable: 'custom',
    width: 90,
  },
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({pages, where, orders}) => {
  return queryPage({
    ...where,
    ...orders,
    ...pages,
    type: routeType
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({page: 1, where});
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.jjfmx).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};


</script>
