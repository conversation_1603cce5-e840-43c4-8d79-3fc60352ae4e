<template>
  <ele-drawer size="36%"
              :title="isUpdate ? '修改成绩来源 ['+data.name+']' : '添加成绩来源'"
              :append-to-body="true"
              style="max-width: 100%;"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             label-position="top"
             labelWidth="auto">
      <el-row :gutter="8">
        <template v-for="item in baseFileds">
          <el-col :span="item.colProps" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
            <ProFormItem :item="item"
                         :model="form"
                         @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
              <template
                v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)">取消
      </el-button>
      <el-button plain size="small" type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {getEvaluateItemById, operation} from "../api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";
import zbSelect from "./zb-select.vue"

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  routeType: String
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  name: '',
  tableName: '',
  xhColumn: '',
  cpnfColumn: '',
  scoreColumn: '',
  sort: '',
});

const updateFormValue = (item, prop, newVal) => {
  console.log(item, prop, newVal)
  let value = newVal
  form[item.prop] = value;
  if (item.fieldLinks) {
    //获取选择值下面所有的显示字段信息
    let showfields = item.fieldLinks.filter(f => {
      // return f.fieldValId === value
      return f.fieldVal === value
    })
    console.log(showfields)
    if (showfields.length > 0) {
      showfields.forEach(sf => {
        nextTick(() => {
          //切换选择值，清空之前选项所赋值
          form[sf.linkField] = '';
          //动态切换选择值，关联具体字段的显示隐藏
          baseFileds.value.filter((init => {
              init.showFlag = sf.linkField === init.prop ? sf.showFlag : init.showFlag
              init.selfModifyFlag = init.showFlag
            }
          ))
        });
      })
      console.log("baseFileds.value=", baseFileds.value)
    }
  }
}

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 提交 */
const onSubmit = () => {
  console.log('form:', JSON.stringify(form));
  formRef["value"]?.validate?.((valid, obj) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {
      ...form,
      parentId: form.parentId === 0 ? "" : form.parentId,
    };
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
const baseFileds = ref([])

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

const queryEvaluateItemInfo = () => {
  getEvaluateItemById(props.data.id).then((resData) => {
    if (resData) {
      if (resData.kssj && resData.jssj) {
        resData.kssjfw = [resData.kssj, resData.jssj]
      }
      if (resData.selectorDatas && resData.selectorDatas.length > 0) {
        resData.sfzdry = '是'
        perSelectedData.value = resData.selectorDatas
      }
      assignFields({
        ...resData,
      });
      isUpdate.value = true;
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      baseFileds.value = [
        {
          prop: 'name', label: '成绩类型名称', type: 'input', required: true,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'tableName', label: '成绩表名', type: 'input', required: true,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xhColumn', label: '学号', type: 'input', required: true,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'cpnfColumn', label: '测评年份', type: 'input',
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'scoreColumn', label: '成绩', type: 'input',
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'sort', label: '排序', type: 'inputNumber',
          showFlag: '是',
          selfModifyFlag: '是',
        },
      ]
      if (props.data) {
        queryEvaluateItemInfo()
      } else {
        form.type = props.routeType;
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  },
);
</script>
