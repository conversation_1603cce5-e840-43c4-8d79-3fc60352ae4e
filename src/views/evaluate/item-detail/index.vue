<template>
  <ele-card :body-style="{padding: '5px!important',height:(pageHeight+6)+'px' }">
    <template #header>
      <ele-text style="padding: 2px;">{{ currentData?.zbmc + '加减分项设置' }}</ele-text>
    </template>
    <ele-pro-table ref="tableRef"
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   v-model:current="current"
                   highlight-current-row
                   tooltip-effect="light"
                   cache-key="evaluateItemDetailTable"
                   :footer-style="{ paddingBottom: '3px' }"
                   :table-style="{  height: (pageHeight-90)+'px', overflow: 'auto' }">
      <template #toolbar>
        <template v-if="isCanOpt">
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()">
            新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()">
            删除
          </el-button>
        </template>
      </template>
      <template #jjfmx="{ row }">
        <ele-tooltip content="去编辑" placement="left" effect="light">
          <el-link type="primary" underline="never"
                   @click="openEdit(row)">
            {{ row.jjfmx }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
  </ele-card>
  <Edit :data="current"
        :configId="configId"
        v-model="showEdit"
        :isReadonly="isCanOpt"
        :itemId="itemId"
        @done="reload"/>
</template>

<script setup>
import {ref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import Edit from './components/edit.vue';
import {queryPage, removes} from "@/views/evaluate/item-detail/api/index.js";
import {usePageTab} from "@/utils/use-page-tab.js";
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();
const {currentRoute, push} = useRouter();
const props = defineProps({
  /** 父 id */
  itemId: String,
  currentData: Object,
  configId: String,
  enterType: String,
  routeType: String,
  isCanOpt: Boolean,
});

/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'jjfmx',
    slot: 'jjfmx',
    label: '加减分明细',
    minWidth: 230,
  },
  {
    prop: 'sffdz',
    label: '是否范围值',
  },
  {
    columnKey: 'jfz',
    label: '加分值',
    slot: 'jfz',
    formatter: (row) => {
      let rVal = ''
      if (row.sffdz === '是') rVal = row.zxz + '~' + row.zdz;
      if (row.sffdz === '否') rVal = row.gdfz;
      return rVal
    }
  },
  {
    prop: 'sfxs',
    label: '是否显示',
  },
  {
    prop: 'pyccmc',
    label: '培养层次',
    formatter: (row) => {
      return row?.pyccmc ?? '不限制'
    },
  },
  {
    prop: 'xymc',
    label: '院系',
    formatter: (row) => {
      return row?.xymc ?? '不限制'
    },
  },
  {
    prop: 'zymc',
    label: '专业',
    formatter: (row) => {
      return row?.zymc ?? '不限制'
    },
  },
  {
    prop: 'njmc',
    label: '年级',
    formatter: (row) => {
      return row?.njmc ?? '不限制'
    },
  },
  {
    prop: 'sort',
    label: '排序',
    sortable: 'custom',
    width: 90,
  },
]);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({pages, where, orders}) => {
  return queryPage({
    ...where,
    ...orders,
    ...pages,
    itemId: props.itemId
  });
};

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/evaluate/evaluate-config/' + props.routeType,
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.jjfmx).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

// 监听机构 id 变化
watch(
  () => props.itemId,
  () => {
    searchRef["value"]?.resetFields?.();
    reload({});
  }
);

</script>
