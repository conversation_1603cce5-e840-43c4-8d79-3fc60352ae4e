<!-- 编辑弹窗 -->
<template>
  <ele-drawer size="40%"
              :title="title+'指标加分规则'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="加分明细" prop="jjfmx">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.jjfmx"
                  :readonly="!isReadonly"
                  placeholder="请输入加分明细"/>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="是否范围值" prop="sffdz">
            <el-switch v-model="form.sffdz"
                       :disabled="!isReadonly"
                       size="small"
                       inline-prompt
                       active-text="是"
                       active-value="是"
                       inactive-text="否"
                       inactive-value="否"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否显示" prop="sfxs">
            <el-switch v-model="form.sfxs"
                       :disabled="!isReadonly"
                       size="small"
                       inline-prompt
                       active-text="是"
                       active-value="是"
                       inactive-text="否"
                       inactive-value="否"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.sffdz==='是'">
        <el-col :span="12">
          <el-form-item label="最小值" prop="name">
            <el-input-number
              v-model="form.zxz"
              :readonly="!isReadonly"
              placeholder="请输入最小值"
              controls-position="right"
              class="ele-fluid"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大值" prop="name">
            <el-input-number
              v-model="form.zdz"
              :readonly="!isReadonly"
              placeholder="请输入最大值"
              controls-position="right"
              class="ele-fluid"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item v-else-if="form.sffdz==='否'" label="固定分值" prop="name">
        <el-input-number
          v-model="form.gdfz"
          :readonly="!isReadonly"
          placeholder="请输入固定分值"
          controls-position="right"
          class="ele-fluid"/>
      </el-form-item>
      <ele-alert show-icon
                 :closable="false"
                 :style="{ margin: '6px 0' }">
        <template #title>
          <span>培养层次，年级，院系这三个字段信息，页面不设置值，默认为参数设置的全部范围值。</span>
        </template>
      </ele-alert>
      <el-form-item label="培养层次">
        <el-checkbox-group v-model="form['pyccidArray']" @change="onchangePycc"
                           :disabled="!isReadonly">
          <el-checkbox v-for="item in pyccidOptions" :key="item.id" :value="item.id" :label="item.name">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="年级">
        <el-checkbox-group v-model="form['njidArray']" @change="onchangeNj"
                           :disabled="!isReadonly">
          <el-checkbox v-for="item in njidOptions" :key="item.id" :value="item.id" :label="item.name">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="院系">
        <el-select v-model="form['xyidArray']" filterable @change="onchangeXy"
                   multiple
                   placeholder="请选择院系"
                   :disabled="!isReadonly">
          <el-option v-for="item in xyidOptions"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="专业">
        <ele-table-select :disabled="!form.xyid || !isReadonly"
                          size="small"
                          multiple
                          clearable
                          placeholder="请选择专业"
                          value-key="id"
                          label-key="name"
                          v-model="form['zyidArray']"
                          :table-props="tablePropsZymc"
                          :cache-data="cacheDataZymc"
                          :maxTagCount="3"
                          :popper-width="520"
                          @clear="onClearZy"
                          @removeTag="onRemoveTagZy"
                          @select="onSelectZy">
        </ele-table-select>
      </el-form-item>
      <el-form-item label="排序号" prop="sort">
        <el-input-number :min="0"
                         :max="99999"
                         :readonly="!isReadonly"
                         v-model="form.sort"
                         placeholder="请输入排序号"
                         controls-position="right"
                         class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="备注">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  :readonly="!isReadonly"
                  v-model="form.remark"
                  placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">{{ !isReadonly ? '关闭' : '取消' }}</el-button>
      <el-button v-if="isReadonly" size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch,} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation,} from '../api/index';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {getCodeZyb, queryPage as queryZyb} from "@/views/base-code/zyb/api/index.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  isReadonly: Boolean,//是否可编辑
  /** 添加时机构id */
  itemId: String,
  /** 修改回显的数据 */
  data: Object,
});

// 已缓存的字典
const userStore = useUserStore();
/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
const title = ref(null)

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  jjfmx: '',
  sfxs: '是',
  sffdz: '否',
  zxz: '',
  zdz: '',
  gdfz: '',
  itemId: '',
  sort: void 0,
  pyccidArray: [],
  njidArray: [],
  xyidArray: [],
  zyidArray: [],
  pyccid: null,
  njid: null,
  xyid: null,
  zyid: null,
  remark: null,
});

/** 表单验证规则 */
const rules = reactive({
  gdfz: [
    {
      required: true,
      message: '请输入固定分值',
      type: 'string',
      trigger: 'blur'
    },
  ],
  zxz: [
    {
      required: true,
      message: '请输入最小值',
      type: 'string',
      trigger: 'blur'
    }
  ],
  zdz: [
    {
      required: true,
      message: '请输入最大值',
      type: 'string',
      trigger: 'blur'
    }
  ],
  jjfmx: [
    {
      required: true,
      message: '请输入加减分明细',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const pyccidOptions = ref([])
const njidOptions = ref([])
const xyidOptions = ref([])

/** 专业缓存数据 */
const cacheDataZymc = ref([]);

const tablePropsZymc = reactive({
  datasource: [],
  virtual: true,
  height: 288,
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'code',
      label: '代码',
      minWidth: 110
    },
    {
      prop: 'name',
      label: '专业名称',
      minWidth: 150,
    },
    {
      prop: 'xymc',
      label: '所属院系',
      minWidth: 150,
    },
  ],
  showOverflowTooltip: true,
  highlightCurrentRow: true,
  toolbar: false,
  pagination: {
    layout: 'total, prev, pager, next, sizes',
    teleported: false,
  },
  rowStyle: {cursor: 'pointer'},
  rowClickChecked: true
});

const onchangeXy = (e) => {
  form.xyid = e.join()
  /** 查询院系下的专业数据 */
  tablePropsZymc.datasource = ({page, limit, where, orders}) => {
    return queryZyb({...where, ...orders, page, limit, xyid: form.xyid});
  };
}


/** 根据zyid查询所属xyid */
const queryCodeZyb = () => {
  if (props.data.zyid) {
    cacheDataZymc.value = []
    getCodeZyb({id: props.data.zyid}).then((list) => {
      if (list.length > 0) {
        cacheDataZymc.value = list
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    }).finally(() => {
    });
  }
}
const selectZyData = ref([]);
/* 当用户手动勾选数据行的 Checkbox 时触发的事件	 */
const onSelectZy = (item) => {
  selectZyData.value = item;
  console.log(selectZyData.value)
  // form['zyidArray'] = item.map(item => item.id)
  form['zyid'] = item.map(item => item.id).join()
};
const onchangePycc = (item) => {
  form['pyccid'] = item.join()
};

const onchangeNj = (item) => {
  form['njid'] = item.join()
};

/* 当用户手动clear时触发的事件	 */
const onClearZy = () => {
  selectZyData.value = [];
  form.zyid = null
  form['zyidArray'] = []
};

const onRemoveTagZy = (value) => {
  let index = selectZyData.value.findIndex(obj => obj.id === value)
  selectZyData.value.splice(index, 1)
  form['zyid'] = form['zyidArray'].join()
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        title.value = props.isReadonly ? '编辑' : '查看'
        assignFields({
          pyccidArray: props.data.pyccid ? props.data.pyccid.split(',') : [],
          njidArray: props.data.njid ? props.data.njid.split(',') : [],
          xyidArray: props.data.xyid ? props.data.xyid.split(',') : [],
          zyidArray: props.data.zyid ? props.data.zyid.split(',') : [],
          ...props.data,
        });
        if (props.data.xyid) {
          /** 查询院系下的专业数据 */
          tablePropsZymc.datasource = ({page, limit, where, orders}) => {
            return queryZyb({...where, ...orders, page, limit, xyid: props.data.xyid});
          };
        }
        if (props.data.zyid) {
          queryCodeZyb()
        }
        isUpdate.value = true;
      } else {
        title.value = '添加'
        form.itemId = props.itemId;
        isUpdate.value = false;
      }
      const {dicts} = storeToRefs(userStore);
      console.log("已缓存的字典======", dicts.value)
      let pyccData = dicts.value['pycc_evaluate'];
      let njData = dicts.value['nj_evaluate'];
      let xyData = dicts.value['dwb_evaluate'];
      pyccidOptions.value = pyccData;
      njidOptions.value = njData;
      xyidOptions.value = xyData;
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);

</script>
