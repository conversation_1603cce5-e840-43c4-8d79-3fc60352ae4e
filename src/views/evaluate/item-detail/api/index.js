/**
 * 综合测评指标 指标加减分规则
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getEvaluateItemDetail(params) {
  const res = await request.get('/evaluate/evaluate-item-detail', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/evaluate/evaluate-item-detail/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getEvaluateItemDetailById(id) {
  const res = await request.get('/evaluate/evaluate-item-detail/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/evaluate/evaluate-item-detail/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/evaluate/evaluate-item-detail/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
