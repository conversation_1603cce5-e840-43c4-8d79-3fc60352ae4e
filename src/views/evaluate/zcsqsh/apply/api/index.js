/**
 * 申请
 */
import request from '@/utils/request.js';

/**
 * 分页查询加减分项申请信息
 */
export async function getApplyPageApplicationInfo(params) {
    const res = await request.get('/evaluate/apply/pageApplicationInfo', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询加减分项申请信息
 */
export async function getApplyApplicationInfoById(id) {
  const res = await request.get('evaluate/apply/'+ id+'/applicationInfo');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除加减分项申请信息
 */
export async function removes(data) {
  const res = await request.post('/evaluate/apply/removeApplicationInfo', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 指标项申请操作（权限标识：evaluate:evaluateApply:operation）
 */
export async function operation(data) {
  const res = await request.post('/evaluate/apply/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 综测年份
 */
export async function queryApplyYearList(params) {
    const res = await request.get('/evaluate/apply/yearList');
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据当前用户查询指标
 */
export async function queryApplyItemList(params) {
    const res = await request.get('/evaluate/apply/itemList',{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据当前用户查询指标加减分规则
 */
export async function queryApplyItemDetailList(params) {
    const res = await request.get('/evaluate/apply/itemDetailList',{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

