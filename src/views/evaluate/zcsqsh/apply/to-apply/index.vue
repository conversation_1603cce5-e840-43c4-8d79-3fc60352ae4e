<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <ele-card :body-style="{ padding: '5px 5px 6px 5px!important', height: `${pageHeight+10}px`, overflow: 'auto'}">
        <template #header>
          <div style="padding: 1px 0 2px 5px">
            <el-input clearable size="small"
                      :maxlength="20"
                      v-model="keywords"
                      placeholder="左侧树还支持鼠标右键"
                      :prefix-icon="SearchOutlined"/>
          </div>
        </template>
        <ele-loading :loading="loading"
                     :style="{ flex: 1, paddingRight: '16px', overflow: 'auto' }">
          <el-tree ref="treeRef"
                   :data="data"
                   highlight-current
                   node-key="id"
                   :props="{ label: 'zbmc' }"
                   :expand-on-click-node="false"
                   :default-expand-all="true"
                   :filter-node-method="filterNode"
                   :style="{ '--ele-tree-item-height': '32px' }"
                   @node-click="handleNodeClick">
            <template #default="scope">
              <span class="el-tree-node__label" style="margin-top: 2px">
                {{ scope.data.zbmc }}
              </span>
            </template>
          </el-tree>
        </ele-loading>
      </ele-card>
      <template #body>
        <ZzjfxIndex v-if="current" :data="current"
                    :organization-id="current.id"
                    :routeType="routeType"
                    @done="onDone"/>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {ref, nextTick, watch, unref,} from 'vue';
import {EleMessage, toTree,} from 'ele-admin-plus/es';
import {
  SearchOutlined,
} from '@/components/icons/index.js';
import {useMobile} from '@/utils/use-mobile.js';
import ZzjfxIndex from '../zzjfx/index.vue';
import {queryApplyItemList} from "@/views/evaluate/zcsqsh/apply/api/index.js";
import {usePageTab} from "@/utils/use-page-tab.js";
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

defineOptions({name: 'EvaluateItem'});
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
console.log(pathArray)
const routeType = pathArray[5];
/** 是否是移动端 */
const {mobile} = useMobile();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const data = ref([]);

/** 选中数据 */
const current = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示表单弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);

/** 添加时上级 */
const organizationId = ref();

/** 查询 */
const query = () => {
  setPageTabTitle('个人加减分项申请');
  loading.value = true;
  queryApplyItemList({type: routeType}).then((list) => {
    loading.value = false;
    data.value = toTree({
      data: list,
      idField: 'id',
      parentIdField: 'parentId'
    });
    nextTick(() => {
      handleNodeClick(data.value[0]);
    });
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const onDone = () => {
  removePageTab({key: getRouteTabKey()});
  reloadPageTab({
    fullPath: '/evaluate/zcsqsh/apply/' + routeType,
  });
}
const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  // 移动端自动收起左侧
  if (current.value != null && mobile.value) {
    splitRef.value?.toggleCollapse?.(true);
  }
  if (row && row.id) {
    current.value = row;
    treeRef.value?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id
  } else {
    current.value = null;
  }
};


/** 打开编辑弹窗 */
const openEdit = (item, id) => {
  editData.value = item ?? null;
  organizationId.value = id;
  showEdit.value = true;
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.zbmc && data.zbmc.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});

query();

</script>
