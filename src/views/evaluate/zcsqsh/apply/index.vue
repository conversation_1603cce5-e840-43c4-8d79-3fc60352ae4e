<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()"> 申请
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #cpnf="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openToEdit(row)">
              {{ row.cpnf }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit" :data="current" @done="reload" enterType="edit"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import { ElMessage as EleMessage} from "element-plus";
import {getApplyPageApplicationInfo, removes} from './api';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import Edit from "@/views/evaluate/zcsqsh/apply/components/edit.vue";
import {ElLoading} from "element-plus";

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
const routeType = pathArray[4];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'cpnf',
      slot: 'cpnf',
      label: '测评年份',
    },
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'zbmc',
      label: '指标名称',
    },
    {
      prop: 'jjfmx',
      label: '加分明细',
    },
    {
      prop: 'jfyy',
      label: '加分原因',
    },
    {
      prop: 'xymc',
      label: '所属院系',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
    {
      prop: 'spjg',
      label: '审批结果',
    },
    {
      prop: 'createTime',
      label: '创建时间',
    },
  ]
);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getApplyPageApplicationInfo({...where, ...orders, ...filters, page, limit, type: routeType});
};
/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 搜索 */
const reload = (where) => {
  console.log(where)
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

const openEdit = (row) => {
  removePageTab({key: getRouteTabKey()});
  reloadPageTab({
    fullPath: '/evaluate/zcsqsh/apply/to-apply/' + routeType,
  });
};
const openToEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.jjfmx).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'checkInItemIndex'
};
</script>

