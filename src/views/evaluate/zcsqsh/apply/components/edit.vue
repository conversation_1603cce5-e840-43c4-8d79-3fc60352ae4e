<!-- 加分项申请 -->
<template>
  <ele-drawer size="50%"
              title="加分项申请"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-position="top"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="指标名称">
        {{ form.zbmc }}
      </el-form-item>
      <el-form-item label="加分明细">
        {{ form.jjfmx }}
      </el-form-item>
      <el-row>
        <el-col :span="8">
          <el-form-item label="sffdz是否范围值">
            {{ form.sffdz }}
          </el-form-item>
        </el-col>
        <el-col v-if="form.sffdz==='是'" :span="8">
          <el-form-item label="zxz参考分数最小值">
            {{ form.zxz }}
          </el-form-item>
        </el-col>
        <el-col v-if="form.sffdz==='是'" :span="8">
          <el-form-item label="zdz参考分数最大值">
            {{ form.zdz }}
          </el-form-item>
        </el-col>
        <el-col v-if="form.sffdz==='否'" :span="8">
          <el-form-item label="gdfz参考分数值">
            {{ form.gdfz }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="加分备注">
        {{ form.bz }}
      </el-form-item>
      <el-form-item label="证明材料" prop="fjxx">
        <file-upload :limit="8" v-model="form.fjxx" listType="image"
                     @change="changeDelIds"/>
      </el-form-item>
      <el-form-item v-if="form.sffdz==='是'" label="加分数" prop="fs">
        <el-input-number
          v-model="form.fs"
          placeholder="请输入加分数"
          :min="form.zxz"
          :max="form.zdz"
          controls-position="right"
          class="ele-fluid"/>
      </el-form-item>
      <el-form-item v-if="form.sffdz==='否'" label="加分数" prop="fs">
        <el-input-number
          v-model="form.fs"
          placeholder="请输入加分数"
          :max="form.gdfz"
          controls-position="right"
          class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="加分原因" prop="jfyy">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.jfyy"
                  placeholder="请输入加分原因"/>
      </el-form-item>
      <el-form-item label="备注">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.remark"
                  placeholder="请输入备注"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch,} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation,} from '../api/index';
import FileUpload from "@/components/FileUpload/index.vue";
import {isImageFile, toFormData} from '@/utils/common_bak2.js';
import {toFormDataWj} from "@/utils/common_bak2.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 添加时机构id */
  organizationId: String,
  enterType: String,
  routeType: String,
  /** 修改回显的数据 */
  data: Object
});

const BASE_URL = import.meta.env.BASE_URL;
/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  zbmc: '',
  jjfmx: '',
  sfxs: '',
  sffdz: '',
  zxz: '',
  zdz: '',
  gdfz: '',
  type: '',
  itemId: '',//指标ID
  itemDetailId: '',//指标加减分项ID
  sort: void 0,
  bz: '',
  jfyy: '',
  fjxx: '',
  remark: '',
  fs: '',
  sftj: '',
});

/** changeDelIds */
const changeDelIds = (value) => {
  deleteFileIds.value = value
};

/** 表单验证规则 */
const rules = reactive({
  fjxx: [
    {
      required: true,
      message: '请上传证明材料',
      type: 'array',
      trigger: 'blur'
    },
  ],
  fs: [
    {
      required: true,
      message: '请输入加分数',
      type: 'number',
      trigger: 'blur'
    },
  ],
  jfyy: [
    {
      required: true,
      message: '请输入加分原因',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 要删除的附件ID，字符串逗号分割*/
const deleteFileIds = ref(null);
/** 保存编辑 */
const save = () => {
  let result = {};
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    let fileArray = []
    if (form.fjxx.length > 0) {
      form.fjxx.forEach((e) => {
        if (e.status !== 'done') {
          fileArray.push(e)
        }
      })
    }
    result = {...form, fjxx: fileArray}
    if (deleteFileIds.value && deleteFileIds.value.length > 0) result.deleteFileIds = JSON.stringify({fjxx: deleteFileIds.value})
    // let data = toFormData(result)
    let data = toFormDataWj(result)
    loading.value = true;
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      deleteFileIds.value = null;
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        if (props.enterType === 'edit') {
          const oldFiles = props.data.fjxx
            ? JSON.parse(props.data.fjxx).map((d, i) => {
              let newObj = {
                key: d.id,
                name: d.originalFilename,
                contentType: d.contentType,
                fileUrl: BASE_URL + 'api/file/inline/' + d.id,
                status: 'done'
              }
              let mark = isImageFile(newObj)
              newObj.isImageFile = mark;
              if (mark) newObj.url = newObj.fileUrl
              return newObj;
            }) : [];
          assignFields({
            ...props.data,
            fjxx: oldFiles,
          });
        } else {
          let rData = props.data
          let newData = {
            ...rData,
            type: props.routeType,
            itemDetailId: props.data.id,
          }
          delete newData.id;
          assignFields({
            ...newData
          });
        }
        isUpdate.value = true;
      } else {
        form.itemId = props.organizationId;
        form.type = props.routeType;
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
