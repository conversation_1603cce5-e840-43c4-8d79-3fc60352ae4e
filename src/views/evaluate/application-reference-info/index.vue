<template>
  <ele-page hide-footer  flex-table>
    <search @search="reload" ref="searchRef"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     v-model:selections="selections"
                     :show-overflow-tooltip="true"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button class="ele-btn-icon" size="small" @click="handleApplicationReferenceInfoOperation">
            同步数据至加减分项申请表
          </el-button>
        </template>
        <template #cpnf="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openToEdit(row)">
              {{ row.cpnf }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {applicationReferenceInfoOperation, getApplicationReferencePage,} from './api';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import Search from "./components/search.vue";
import {getToken} from "@/utils/token-util.js";
import {getCurrentRole} from "@/utils/current-role-util.js";
import { ElMessage as EleMessage} from "element-plus";

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
const routeType = pathArray[3];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

/** 表格实例 */
const tableRef = ref(null);
/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'source',
      slot: 'source',
      label: '业务模块来源',
    },
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'cpnf',
      label: '测评年份',
    },
    {
      prop: 'itemDetailName',
      label: '所属指标加减分规则名称',
    },
    {
      prop: 'fs',
      label: '分数',
    },
    {
      prop: 'remark',
      label: '备注',
    },
  ]
);


/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getApplicationReferencePage({...where, ...orders, ...filters, page, limit, type: routeType});
};
/** 用户名筛选值 */
const nameFilterValue = ref('');
const enterType = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});
const reload = (where) => {
  lastWhere.value = where
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

/** 提交状态 */
const loading = ref(false);

/** 同步数据至加减分项申请表 */
const handleApplicationReferenceInfoOperation = () => {
  applicationReferenceInfoOperation(lastWhere.value).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};


</script>

<script>
export default {
  name: 'EVALUATEAPPROVALINDEX'
};
</script>

