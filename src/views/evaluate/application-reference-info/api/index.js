/**
 * 指标加减分参考项
 */
import request from '@/utils/request.js';

/**
 * 查询全部指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
 */
export async function getApplicationReferenceInfo(params) {
  const res = await request.get('/evaluate/v-evaluate-application-reference-info', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function getApplicationReferencePage(params) {
  const res = await request.get('/evaluate/v-evaluate-application-reference-info/page', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询指标加减分参考项（权限标识：evaluate:vEvaluateApplicationReferenceInfo:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function getApplicationReferenceInfoById(id) {
  const res = await request.get('/evaluate/v-evaluate-application-reference-info/'+id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 同步数据至加减分项申请表
 */
export async function applicationReferenceInfoOperation(data) {
  const res = await request.post('/evaluate/v-evaluate-application-reference-info/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
