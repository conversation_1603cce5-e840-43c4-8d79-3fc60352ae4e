<!--参数配置所选范围列表-->
<template>
  <ele-drawer
    size="60%"
    :title="'本班学生互评'"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ padding: '5px 8px!important', marginTop: 0 }"
    @update:modelValue="updateModelValue"
  >
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important' }"
      :header-style="{ borderBottom: 'unset!important' }"
    >
      <div :style="{ height: pageHeight - 10 + 'px', overflow: 'auto' }">
        <el-form
          ref="formRef"
          size="small"
          :model="form"
          label-width="0px"
          @submit.prevent=""
        >
          <ele-table size="small" border style="table-layout: fixed">
            <thead>
              <tr>
                <th
                  v-for="header in initTableHeader"
                  :style="
                    header?.hide
                      ? 'display: none;'
                      : 'width:' + header.minWidth + 'px'
                  "
                  :key="header.prop"
                >
                  {{ header.label }}
                </th>
              </tr>
            </thead>
            <tr v-for="(row, index) in form.users" :key="row.key">
              <td
                v-for="item in initTableHeader"
                :style="item?.hide ? 'display: none;' : ''"
                :key="item.prop"
              >
                <el-form-item
                  v-if="
                    [
                      'xm',
                      'xgh',
                      'nodeStatus',
                      'questionSelCode',
                      'extendsField',
                      'pyccmc',
                      'njmc',
                      'xymc'
                    ].includes(item.type)
                  "
                  label=""
                  class="form-error-popper"
                  style="margin-bottom: 0 !important"
                >
                  <div class="editable-cell-text">{{ row[item.prop] }}</div>
                </el-form-item>
                <template v-else>
                  <TableFormItem
                    v-if="row.isEdit"
                    :item="item"
                    :index="index"
                    :model="row"
                    @updateValue="
                      (value) => updateValue(index, item.prop, value)
                    "
                  >
                    <template
                      v-for="name in Object.keys($slots).filter(
                        (k) =>
                          ![
                            'default',
                            'footer',
                            'topExtra',
                            'bottomExtra'
                          ].includes(k)
                      )"
                      #[name]="slotProps"
                    >
                      <slot :name="name" v-bind="slotProps || {}"></slot>
                    </template>
                  </TableFormItem>
                  <div v-else class="editable-cell-text">{{
                    row[item.prop]
                  }}</div>
                </template>
              </td>
            </tr>
          </ele-table>
        </el-form>
      </div>
    </ele-card>
    <!-- 分页按钮 -->
    <ele-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[20, 40, 60, 80, 100, 200]"
      :page-size="pageSize"
      :total="total"
      size="small"
      :background="true"
      layout="total, prev, pager, next,  sizes,jumper"
      :style="{
        marginTop: '8px',
        marginBottom: '8px',
        justifyContent: 'center'
      }"
    />
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)"
        >{{ enterType === 'preview' ? '关闭' : '取消' }}
      </el-button>
      <el-button
        v-if="enterType !== 'preview'"
        plain
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue';
  import {
    queryMyPeerReviewUsers,
    operationEvaluatePeerReviewRecord
  } from '../api/index.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
  import { useFormData } from '@/utils/use-form-data.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const emits = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    RandomString: String,
    // currentData: Object,
    configId: String
  });

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields] = useFormData({
    users: []
  });

  /** 请求状态 */
  const loading = ref(false);

  const total = ref(null); // 假设总数据量为100
  const pageSize = ref(20); // 每页显示10条数据
  const currentPage = ref(1); // 当前页码
  const queryParams = ref({});
  const rDataList = ref([]);
  const initTableHeader = ref([
    {
      prop: 'xm',
      label: '姓名',
      type: 'xm'
    },
    {
      prop: 'xgh',
      label: '学号',
      type: 'xgh'
    },
    {
      prop: 'score',
      label: '评分',
      type: 'inputNumber',
      precision: 2,
      min: 0,
      max: 5,
      editable: true,
      required: true
    }
  ]);

  const onSubmit = () => {
    loading.value = true;
    form.users.map((item) => {
      item.configId = props.configId;
    });
    operationEvaluatePeerReviewRecord(form.users)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emits('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 更新值 */
  const updateValue = (index, prop, value) => {
    console.log('更新值=====', index, prop, value, form.users[index]);
    form.users[index][prop] = value;
  };

  const QueryEvaluateConfigScopeList = () => {
    rDataList.value = [];
    form.users = [];
    let obj = {
      page: currentPage.value,
      limit: pageSize.value
    };
    let newObj = Object.assign(obj, queryParams.value);
    queryMyPeerReviewUsers(newObj)
      .then((resData) => {
        total.value = resData.length;
        let resList = resData;
        if (resList.length > 0) {
          resList.forEach((item) => {
            // item.score = [item.sqkssj, item.sqjzsj];
            item.isEdit = true;
          });
          rDataList.value = resList;
          form.users = rDataList.value;
        } else {
          rDataList.value = [];
          form.users = [];
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  // 分页大小改变时的回调
  const handleSizeChange = (val) => {
    pageSize.value = val;
  };

  // 当前页改变时的回调
  const handleCurrentChange = (val) => {
    currentPage.value = val;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emits('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue) {
        QueryEvaluateConfigScopeList();
      }
    }
  );
</script>

<script>
  export default {
    NAME: 'EVALUATESCOPELIST'
  };
</script>
