/**
 * 学生互评  学生端
 */
import request from '@/utils/request.js';

/**
 * 本人互评记录分页查询  学生端
 */
export async function queryEvaluatePeerReviewRecordMypage(params) {
  const res = await request.get(
    '/evaluate/evaluate-peer-review-record/myPage',
    {
      params
    }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operationEvaluatePeerReviewRecord(data) {
  const res = await request.post(
    '/evaluate/evaluate-peer-review-record/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removesEvaluatePeerReviewRecord(data) {
  const res = await request.post(
    '/evaluate/evaluate-peer-review-record/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 本人互评的同班同学列表  学生端
 */
export async function queryMyPeerReviewUsers(params) {
  const res = await request.get(
    '/evaluate/evaluate-peer-review-record/myPeerReviewUsers',
    {
      params
    }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
// export async function getFdycpGrade(params) {
//   const res = await request.get('/teacherEvaluate/fdycp-grade', { params });
//   if (res.data.code === 0 && res.data.data) {
//     return res.data.data;
//   }
//   return Promise.reject(new Error(res.data.message));
// }

/**
 * 根据id查询
 */
export async function getFdycpGradeById(id) {
  const res = await request.get('/teacherEvaluate/fdycp-grade/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getEvaluateConfigScopeList(params) {
  const res = await request.get('/evaluate/evaluate-config/pageScopeList', {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 综测参数范围维护（权限标识：evaluate:evaluateConfig:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operationScope(data) {
  const res = await request.post(
    '/evaluate/evaluate-config/operationScope',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
