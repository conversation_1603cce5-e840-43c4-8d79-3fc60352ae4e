<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <!-- 搜索表单 -->
        <search @search="searchReload" ref="searchRef" :routeType="routeType" />
      </template>
      <ele-pro-table
        ref="tableRef"
        flex-table
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :loadOnCreated="false"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        v-model:current="current"
        :tools="false"
        highlight-current-row
        tooltip-effect="light"
        style="
          padding-bottom: 0;
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: auto;
        "
        :table-style="{ flex: 1, height: '100%', overflow: 'hidden' }"
        cache-key="BankDataTable"
        :footer-style="{ paddingBottom: '3px' }"
      >
        <template #toolbar>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="handleAnswer()"
          >
            本班互评
          </el-button>
          <!-- <el-button size="small" plain class="ele-btn-icon" @click="remove()">
            删除
          </el-button> -->
        </template>
        <template #score="{ row }">
          <el-input-number
            :max="5"
            :min="0"
            controls-position="right"
            class="ele-fluid"
            :model-value="row.score"
            size="small"
            :precision="2"
            @update:modelValue="(value) => updateValue(row, value)"
            @blur="handleUpdate(row)"
          />
        </template>
        <!-- <template #action="{ row }">
          <template v-if="row">
            <el-link
              type="primary"
              underline="never"
              @click="handleAnswer(row)"
            >
              评分
            </el-link>
            <el-divider v-if="row.answered" direction="vertical" />
          </template>
          <template v-if="row.answered">
            <el-link
              type="primary"
              underline="never"
              @click="handleAnswerPreview(row)"
            >
              评分记录
            </el-link>
          </template>
        </template> -->
      </ele-pro-table>
    </ele-card>
    <Edit
      v-model="showEdit"
      :data="current"
      @done="reload"
      :configId="configId"
    />
  </ele-page>
</template>

<script setup>
  import { ref, watch, computed, reactive, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElMessage as EleMessage } from 'element-plus';
  import Search from './components/search.vue';
  import Edit from './components/edit.vue';
  import {
    queryEvaluatePeerReviewRecordMypage,
    removesEvaluatePeerReviewRecord,
    operationEvaluatePeerReviewRecord
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user.js';
  import { useDictData } from '@/utils/use-dict-data.js';
  import { storeToRefs } from 'pinia';
  import { ElLoading } from 'element-plus';

  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  const userStore = useUserStore();
  useDictData(['listRoles']);
  const { dicts } = storeToRefs(userStore);

  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);
  const configId = ref('');

  /** 普通查询 -查询参数*/
  const searchWhere = reactive({});

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 45,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'cpnf',
        label: '年份'
      },
      {
        prop: 'xgh',
        label: '学号'
      },
      {
        prop: 'xm',
        label: '被评人'
      },
      // {
      //   prop: 'xymc',
      //   label: '学院'
      // },
      // {
      //   prop: 'zymc',
      //   label: '专业'
      // },
      {
        prop: 'bjmc',
        label: '班级'
      },
      {
        prop: 'score',
        slot: 'score',
        label: '评分'
      }
      // {
      //   columnKey: 'action',
      //   label: '操作',
      //   width: 150,
      //   slot: 'action'
      // }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await queryEvaluatePeerReviewRecordMypage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
    res.list.map((item) => {
      item.newScore = item.score;
    });
    return res;
  };
  const searchReload = (where) => {
    searchWhere.value = where;
    configId.value = searchWhere.value.configId;
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 用户名筛选值 */
  // const nameFilterValue = ref('');

  /** 表格搜索参数 */
  // const lastWhere = reactive({});

  /** 用户名筛选事件 */
  // const onNameFilter = (name) => {
  //   nameFilterValue.value = name;
  //   doReload();
  // };

  /** 表格搜索 */
  // const doReload = () => {
  //   if (nameFilterValue.value) {
  //     reload({
  //       ...lastWhere,
  //       name: nameFilterValue.value
  //     });
  //   } else {
  //     reload(lastWhere);
  //   }
  // };
  /** 刷新表格 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */

  const handleAnswer = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
    console.log('handleAnswer :>> ', row);
  };

  const handleAnswerPreview = (row) => {
    console.log('handleAnswerPreview :>> ', row);
  };

  const updateValue = (row, value) => {
    row.newScore = value;
  };

  const handleUpdate = (row) => {
    console.log('handleUpdate :>> ', row);
    if (row.newScore && row.score == row.newScore.toFixed(2)) return;
    row.score = row.newScore.toFixed(2);
    operationEvaluatePeerReviewRecord([row])
      .then((msg) => {
        EleMessage.success(msg);
        searchReload(searchWhere.value);
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除对“' + rows.map((d) => d.xm).join(', ') + '”评分吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesEvaluatePeerReviewRecord(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>
