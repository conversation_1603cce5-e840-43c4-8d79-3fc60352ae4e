<!-- 加分项审批详情 -->
<template>
  <ele-drawer size="45%"
              title="加分项审批详情"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-card v-if="formData" header="申请加分信息" :body-style="{  height:'auto' }">
      <el-form ref="formRef"
               size="small"
               label-width="110px"
               @submit.prevent="">
        <el-form-item label="指标名称">
          {{ formData.zbmc }}
        </el-form-item>
        <el-form-item label="加分明细">
          {{ formData.jjfmx }}
        </el-form-item>
        <el-form-item label="是否范围值">
          {{ formData.sffdz }}
        </el-form-item>
        <el-row v-if="formData.sffdz==='是'">
          <el-col :span="12">
            <el-form-item label="参考分数最小值">
              {{ formData.zxz }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参考分数最大值">
              {{ formData.zdz }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item v-else-if="formData.sffdz==='否'" label="参考分数值">
          {{ formData.gdfz }}
        </el-form-item>
        <el-form-item label="加分备注">
          {{ formData.bz }}
        </el-form-item>
        <el-form-item v-if="formData.sffdz==='是'" label="加分数" prop="fs">
          {{ formData.fs }}
        </el-form-item>
        <el-form-item v-if="formData.sffdz==='否'" label="加分数" prop="fs">
          {{ formData.fs }}
        </el-form-item>
        <el-form-item label="证明材料">
          <file-upload :limit="8" v-model="formData.fjxx" readonly listType="image"
                       @change="changeDelIds"/>
        </el-form-item>
        <el-form-item label="加分原因">
          {{ formData.jfyy }}
        </el-form-item>
        <el-form-item label="备注">
          {{ formData.remark }}
        </el-form-item>
      </el-form>
    </ele-card>

    <ele-card header="审核记录"
              :body-style="{  height:'auto' ,padding: '10px!important'}">
      <div class="time-line">
        <el-timeline :reverse="true">
          <el-timeline-item v-for="(activity, index) in activities"
                            :key="index"
                            :icon="activity.icon"
                            :type="activity.type"
                            :color="activity.color"
                            :size="activity.size"
                            :hollow="activity.hollow">
            <!--            :timestamp="activity.timestamp"-->
            <div class="ml10">
              <div class="list-title">{{ activity.title }} [{{ activity.approvalNode.reviewType }}]</div>
              <div class="list-company" :style="{color:activity.color}">节点状态:{{ activity.name }}</div>
              <template v-if="activity.approvalNode.reviewType==='会签'">
                <!--                <ele-check-card :items="activity.approvalNodeRecordsData"-->
                <!--                                :item-style="{ width:'45%', marginRight:'10px',padding: '8px 12px', display: 'flex' }"-->
                <!--                                style="display: flex; flex-wrap: wrap">-->
                <!--                #item="{ item }"-->
                <template v-for="item in activity.approvalNodeRecordsData">
                  <div style="flex: 1; overflow: hidden;margin-top: 10px;">
                    <ele-text size="sm">审核人:{{ item.list[0].realName }}</ele-text>
                    <!--                          <div class="list-desc">:{{ item.realName }}<br/></div>-->
                    <ele-text size="sm">{{ item.list[0].result }}</ele-text>
                    <template
                        v-for="formFiled in activity.nodeForms.filter(node => node.nodeApproverId===item.list[0].nodeApproverId)">
                      {{ formFiled.fieldZh }}:{{ item['list'][0][formFiled.fieldEn] }}<br/>
                    </template>
                    <ele-text size="sm">操作时间:{{ item.list[0].createTime }}</ele-text>
                  </div>
                </template>
                <!--                </ele-check-card>-->
              </template>
              <template v-else>
                <template v-for="(item,index) in activity.approvalNodeRecords">
                  <template v-if="index===0">
                    <ele-text size="sm">审核人:{{ item.realName }}</ele-text>
                    <ele-text size="sm">审核状态:{{ item.result }}</ele-text>
                    <ele-text size="sm" v-if="item.result==='退回'" :style="{color:activity.color}">退回规则:{{
                        approvalreturnWay.filter(way => way.name === item.returnWay)[0].label
                      }}<br/>
                    </ele-text>
                    <ele-text
                        v-for="formFiled in activity.nodeForms.filter(node => node.nodeApproverId===item.nodeApproverId)">
                      {{ formFiled.fieldZh }}:{{ item[formFiled.fieldEn] }}<br/>
                    </ele-text>
                    <ele-text size="sm">操作时间:{{ item.createTime }}</ele-text>
                  </template>
                </template>
              </template>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </ele-card>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <!--      v-if="enterType!=='preview'"-->
      <!--      <el-button size="small" type="primary" plain :loading="loading" @click="save">-->
      <!--        提交-->
      <!--      </el-button>-->
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {
  getHistoryApplicationInfoById, getHistoryApprovalNodesById
} from '../api/index';
import FileUpload from "@/components/FileUpload/index.vue";
import {comApproveStatus, groupArr, isImageFile, toFormData} from '@/utils/common_bak2.js';
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {statusColor} from '@/utils/status-color/index.js';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 添加时机构id */
  organizationId: String,
  enterType: String,
  /** 修改回显的数据 */
  data: Object
});

/** 表单验证规则 */
const approvalRules = reactive({
  result: [
    {
      required: true,
      message: '请选择审核状态',
      type: 'string',
      trigger: 'blur'
    }
  ],
  returnWay: [
    {
      required: true,
      message: '请选择退回规则',
      type: 'string',
      trigger: 'blur'
    }
  ],
})

let routeType = ''
let currentProjectId = ''
let currentSqId = ''
let currentNodeId = ''
let currentWorkflowId = ''
const BASE_URL = import.meta.env.BASE_URL;
/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
const formData = ref(null);

/** changeDelIds */
const changeDelIds = (value) => {
  deleteFileIds.value = value
};

/** 表单验证规则 */
const rules = reactive({
  fjxx: [
    {
      required: true,
      message: '请上传证明材料',
      type: 'array',
      trigger: 'blur'
    },
  ],
  fs: [
    {
      required: true,
      message: '请输入加分数',
      type: 'number',
      trigger: 'blur'
    },
  ],
  jfyy: [
    {
      required: true,
      message: '请输入加分原因',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
  console.log("proFormGroup.value===", proFormGroup.value)
};

const onChange = (newValue) => {
  if (newValue) {
    approvalForm.value.returnWay = '';
  }
}
const approvalForm = ref(null);

/** 要删除的附件ID，字符串逗号分割*/
const deleteFileIds = ref(null);
/** 保存编辑 */
const save = () => {
  let resData = proFormGroup.value;
  console.log(resData.length)
  if (!approvalForm.value.result) {
    EleMessage.error('请选择审核状态');
    return;
  }
  console.log(approvalForm.value)
  // let newObj = Object.assign(approvalForm.value, resData[0].values,);
  let newObj = Object.assign(approvalForm.value, resData.length > 0 ? resData[0].values : {},);
  console.log(newObj)
  loading.value = true;
  let data = toFormData({...newObj})
  operation(data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
  // });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};


const approvalTimeIsCorrect = ref(true);

const activities = ref([])

/** 审核信息*/
const approvalData = ref(null);
const approvalStatus = ref(null);
const approvalreturnWay = ref([{
  label: '退回到上一节点',
  name: 'PRE_NODE',
}, {
  label: '退回到申请人',
  name: 'APPLICANT',
}]);


/** 查询tabs */
const queryNodeState = () => {
  getEvaluateApprovalNodeState({
    id: currentWorkflowId,
    // year: approvalApplicationInfo.value.year,
    nodeId: currentNodeId
  }).then((data) => {
    let baseItmes = comApproveStatus()
    baseItmes.forEach(item => {
      item.label = data[0][item.diyname]
      item.name = data[0][item.prename]
    })
    approvalStatus.value = baseItmes
    approvalTimeIsCorrect.value = data[0].approvalTimeIsCorrect;
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

const queryApprovalApplicationInfo = () => {
  getHistoryApplicationInfoById(currentSqId).then((list) => {
    if (list) {
      list.fjxx = list.fjxx
          ? JSON.parse(list.fjxx).map((d, i) => {
            let newObj = {
              key: d.id,
              name: d.originalFilename,
              contentType: d.contentType,
              fileUrl: BASE_URL + 'api/file/inline/' + d.id,
              status: 'done'
            }
            let mark = isImageFile(newObj)
            newObj.isImageFile = mark;
            if (mark) newObj.url = newObj.fileUrl
            return newObj;
          }) : []
      formData.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
    // queryNodeState();
    queryApprovalNodesBySqId();
  });
};

const queryApprovalNodesBySqId = () => {
  getHistoryApprovalNodesById(currentSqId).then((data) => {
    if (data.length > 0) {
      data.forEach(item => {
        let newObj = statusColor().filter(obj => obj.label === item.approvalNode.result)
        let obj = {
          title: item.approvalNode.nodeName,
          name: item.approvalNode.result,
          content: '',
          timestamp: item.approvalNode.updateTime,
          size: 'large',
          ...newObj[0],
          ...item,
        }
        obj.icon = item.reviewType === '会签' ? 'Stamp' : 'CircleCloseFilled';
        if (item.approvalNodeRecords) {
          let approvalNodeRecordsData = groupArr(item.approvalNodeRecords, 'username');
          console.log(approvalNodeRecordsData)
          obj.approvalNodeRecordsData = approvalNodeRecordsData
        }
        activities.value.push(obj)
      })
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue && props.data) {
        currentProjectId = props.data.itemId
        currentSqId = props.data.id
        currentNodeId = props.data.nodeId
        currentWorkflowId = props.data.workflowId
        approvalData.value = {
          groupName: "审核信息",
          title: "",
          id: props.data.id,
          workflowId: currentWorkflowId,
          nodeId: props.data.nodeId,
          infoType: "zhcpApproval",
          listFlag: "否",
        }
        approvalForm.value = {
          projectId: currentProjectId,
          id: [currentSqId],
          nodeId: currentNodeId,
          year: '',
          result: '',
          returnWay: '',
        }
        isUpdate.value = true;
        activities.value = [];
        queryApprovalApplicationInfo()
      }
    },
    {immediate: true}
);
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}

.el-collapse {
  border-bottom: unset !important;
}

.el-collapse-item {
  padding-left: 12px !important;
}

.list-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #181b1e;
}

.list-company {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.list-company1 {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  //margin-top: 15px;
}

.list-desc {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #596878;
}

</style>
