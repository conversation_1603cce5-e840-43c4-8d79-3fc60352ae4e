<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
      </template>
      <!-- 表格 -->
      <ele-pro-table v-if="currentConfigId"
                     ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     v-model:selections="selections"
                     :show-overflow-tooltip="true"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #jjfmx="{ row }">
          <ele-tooltip content="详情" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openToDetails(row)">
              {{ row.jjfmx }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit" :data="current" @done="reload" :enterType="enterType"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {pageApplicationInfo,} from '../api/index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import Edit from '../components/edit.vue'
import IconPark from "@/components/IconPark/index.vue";

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
console.log(pathArray)
const currentConfigId = pathArray[4];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

const props = defineProps({});

/** 表格实例 */
const tableRef = ref(null);
/** 搜索栏实例 */
const searchRef = ref(null);

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/evaluate/history',
  })
};

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'zbmc',
      label: '指标名称',
    },
    {
      prop: 'jjfmx',
      slot: 'jjfmx',
      label: '加分明细',
    },
    {
      prop: 'jfyy',
      label: '加分原因',
    },
    {
      prop: 'fs',
      label: '分数',
    },
    {
      prop: 'xymc',
      label: '所属院系',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
    {
      prop: 'createTime',
      label: '创建时间',
    },
  ]
);

/** 标签页选中 */
const activeName = ref(null);
const tabsItems = ref([])
const nodeStateArray = ref(null);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 用户名筛选值 */
const nameFilterValue = ref('');
const enterType = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});
const reload = (where) => {
  lastWhere.value = where
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};


const openToDetails = (row) => {
  current.value = Object.assign(row, lastWhere.value);
  showEdit.value = true
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return pageApplicationInfo({
    ...where, ...orders, ...filters,
    page,
    limit,
    // nodeId: currentNodeId.value,
    configId: currentConfigId,
    // result: activeName?.value
  });
};

// watch(
//   props.pageConfig,
//   (pageConfig) => {
//     if (pageConfig) queryApprovalNodeList()
//   },
//   {immediate: true}
// )

</script>

<script>
export default {
  name: 'APPROVALINDEX'
};
</script>

