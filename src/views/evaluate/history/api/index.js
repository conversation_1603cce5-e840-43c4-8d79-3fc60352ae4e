/**
 * 历史记录信息管理
 */
import request from '@/utils/request.js';

/**
 * 分页查询综合测评参数配置（权限标识：evaluate:history:list）
 */
export async function getHistoryPageConfig(params) {
  const res = await request.get('/evaluate/evaluate-history-info/pageConfig', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 加减分申请列表（权限标识：evaluate:history:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function pageApplicationInfo(params) {
  const res = await request.get('/evaluate/evaluate-history-info/pageApplicationInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询加减分项申请信息（权限标识：evaluate:history:list）
 */
export async function getHistoryApplicationInfoById(id) {
  const res = await request.get('/evaluate/evaluate-history-info/' + id + '/applicationInfo');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请信息ID查询所有审核节点，审核记录
 */
export async function getHistoryApprovalNodesById(id) {
  const res = await request.get('/evaluate/evaluate-history-info/'+id+'/approvalNodes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
