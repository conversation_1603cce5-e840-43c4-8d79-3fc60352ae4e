<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     v-model:selections="selections"
                     :show-overflow-tooltip="true"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #title="{ row }">
          <ele-tooltip content="申请列表" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="handleToApply(row)">
              {{ row.title }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {ref, unref,} from 'vue';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {getHistoryPageConfig} from "@/views/evaluate/history/api/index.js";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
const routeType = pathArray[3];

/** 表格实例 */
const tableRef = ref(null);
/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'cpnf',
    label: '测评年份',
  },
  {
    prop: 'title',
    slot: 'title',
    label: '名称',
  },
  {
    prop: 'sqkssj',
    label: '申请开始时间',
  },
  {
    prop: 'sqjzsj',
    label: '申请截止时间',
  },
  {
    prop: 'sfkjscj',
    label: '是否可计算成绩',
  },
  {
    prop: 'sfsyxjydqxx',
    label: '是否使用学籍异动前信息',
  },
  {
    prop: 'state',
    label: '状态',
    slot: 'state',
  },
]);

/** 打开申请列表 */
const handleToApply = (row) => {
  removePageTab({key: getRouteTabKey()});
  // ${routeType}/
  push({
    path: `/evaluate/history/list/${row.id}`
  })
};

/** 表格选中数据 */
const selections = ref([]);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getHistoryPageConfig({
    ...where, ...orders, ...filters,
    page,
    limit,
    type: routeType
  });
};
</script>

<script>
export default {
  name: 'CONFIGINDEX'
};
</script>

