<template>
  <ele-page flex-table :style="{paddingTop:'0px!important', height: (pageHeight+50)+'px', overflow:'auto',}">
    <ele-card :header="cardTitle"
              :body-style="{ padding: '5px 0!important',}">
      <template #extra>
        <ele-text style="float: left;padding-right: 5px;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
        <template v-if="currentEnterType!=='preview'">
          <el-button v-if="currentEnterType==='copy'" plain size="small" type="primary" :loading="loading"
                     @click="handleCancelCopy">
            取消复制
          </el-button>
          <el-button v-if="currentEnterType!=='add'" plain size="small" type="primary" :loading="loading" @click="onClickCancel">
            取消保存
          </el-button>
          <el-button plain size="small" type="primary" :loading="loading" @click="submitInfo">
            保存
          </el-button>
        </template>
        <template v-if="currentEnterType==='preview'">
          <ele-tooltip content="快速复用已有参数配置，提升创建效率。调整复制后的参数名称、年份等差异化信息" effect="light"
                       placement="left">
            <el-button plain size="small" type="primary" :loading="loading"
                       @click="onClickCopy">
              复制模式
            </el-button>
          </ele-tooltip>
          <ele-tooltip content="调整已创建的测评参数，仅限管理员操作" effect="light" placement="left">
            <el-button plain size="small" type="primary" :loading="loading" @click="onClickEdit">
              编辑模式
            </el-button>
          </ele-tooltip>
        </template>
      </template>
      <ele-alert v-if="currentEnterType!=='preview'" show-icon
                 :closable="false"
                 :style="{ margin: '6px' }">
        <template #title>
          <span>点击保存按钮系统自动检查必填项（标 <span style="color: red">*</span> 字段）和格式合法性</span>
        </template>
      </ele-alert>
      <div :style="{overflow:'auto'}">
        <el-form ref="formRef"
                 size="small"
                 :model="form"
                 label-position="top"
                 style="margin: 10px;">
          <el-row :gutter="8">
            <template v-for="item in formItems">
              <el-col :span="item.colProps.span" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
                <template v-if="item.prop==='xyid'">
                  <el-form-item :label="item.label" required>
                    <xy-multiple
                      :cacheData="cacheDataXy"
                      v-model="form[item.prop]"/>
                  </el-form-item>
                </template>
                <ProFormItem v-else :item="item"
                             :model="form"
                             @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </ProFormItem>
              </el-col>
              <!--            -->
              <el-col :span="item.colProps.span" v-else class="border-only-bottom">
                <ProFormItem :item="item"
                             :model="form"
                             @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </ProFormItem>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
    </ele-card>
    <!--    操作区-->
    <div style="padding: 0;">
      <el-row :gutter="8">
        <template v-for="item in cardOptData">
          <el-col v-if="item.isDisplay"
                  :key="item.id"
                  :lg="6"
                  :md="8"
                  :sm="12"
                  :xs="24">
            <ele-card shadow="hover"
                      :body-style="{ padding: '8px 8px 10px 8px!important', }"
                      style="overflow: hidden; cursor: pointer;">
              <ele-text size="md" style="max-height: 100%;font-weight: bold;font-size: 13px;">
                {{ item.title }}
                <div style="display: flex; align-items: center;float: right">
                  <IconPark :name="item.icon" size="20" strokeWidth="3"/>
                </div>
              </ele-text>
              <ele-ellipsis :max-line="2"
                            type="placeholder"
                            :tooltip="{ effect: 'light', offset:6, placement:'left-start',
                            bodyStyle: { maxWidth: '210px', minHeight: '25px' ,marginTop: '10px'} }"
                            style="margin: 8px 0 10px 0; height:38px;">
                <ele-text type="secondary" size="sm">
                  {{ item.content }}
                </ele-text>
              </ele-ellipsis>
              <div style=" align-items: center;text-align: center">
                <el-button class="ele-btn-icon"
                           plain
                           style="width: 100%"
                           underline="never"
                           @click="item.onClick">
                  {{ item.btnName }}
                </el-button>
              </div>
            </ele-card>
          </el-col>
        </template>
      </el-row>
    </div>

    <WorkFlowSet v-if="showWorkFlowSet"
                 v-model="showWorkFlowSet"
                 :currentData="form"
                 :currentId="form.id"
                 :bindWorkFlowInfo="bindWorkFlowInfo"
                 :RandomString="initRandomString"/>
    <WorkflowNode v-if="showWorkflowNode"
                  :currentData="form"
                  :RandomString="initRandomString"
                  :currentId="form.id"
                  v-model="showWorkflowNode"/>
    <ScopeList v-if="showScopeList"
               :currentData="form"
               :RandomString="initRandomString"
               :currentId="form.id"
               v-model="showScopeList"/>
    <ItemSet v-if="showItemSet"
             :currentData="form"
             :RandomString="initRandomString"
             :currentId="form.id"
             v-model="showItemSet"/>
    <ScoreView v-if="showScoreView"
               :currentData="form"
               :RandomString="initRandomString"
               :currentId="form.id"
               :routerTypeSv="routerTypeSv"
               :viewName="viewName"
               v-model="showScoreView"/>
    <EvaluateScore v-if="showEvaluateScore"
                   :currentData="form"
                   :RandomString="initRandomString"
                   :currentId="form.id"
                   v-model="showEvaluateScore"/>
  </ele-page>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {
  copyOperation,
  getEvaluateConfigById,
  getEvaluateConfigWorkflowById,
  operation
} from "../api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";
import {CloseCircleOutlined} from "@/components/icons/index.js";
import XyMultiple from "@/views/evaluate/evaluate-config/components/xy-multiple.vue";
import IconPark from "@/components/IconPark/index.vue";
import WorkFlowSet from "../workflow-set/index.vue"
import {generateRandomString} from '@/utils/common_bak2.js';
import WorkflowNode from "@/views/evaluate/evaluate-config/workflow-node/index.vue";
import ScopeList from "@/views/evaluate/evaluate-config/scope-list/index.vue";
import ItemSet from "@/views/evaluate/evaluate-config/item-set/index.vue";
import ScoreView from "@/views/evaluate/evaluate-config/score-view/index.vue";
import EvaluateScore from "@/views/evaluate/evaluate-config/evaluate-score/index.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {ElMessageBox} from "element-plus";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  currentId: String,
  RandomString: String,
  routeType: String,
  enterType: String,//preview：详情 add:添加 edit:编辑
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const validMsg = ref(null);
const validMsgCount = ref(0);

const initRandomString = ref(null);
const currentEnterType = ref(null);
currentEnterType.value = props.enterType;

const showWorkFlowSet = ref(false);
const showWorkflowNode = ref(false);
const showScopeList = ref(false);
const showItemSet = ref(false);
const showScoreView = ref(false);
const routerTypeSv = ref(null);
const viewName = ref(null);
const showEvaluateScore = ref(false);

let cardOptData = computed(() => {
  return [
    {
      id: 6,
      title: '工作流设置',
      content: '配置测评审核流程，确保流程规范化。',
      btnName: '工作流设置',
      icon: 'setting-two',
      isDisplay: !!(form && form.id),
      onClick: () => {
        if (form && form.id) {
          showWorkFlowSet.value = true
          initRandomString.value = generateRandomString(10)
        } else {
          EleMessage.warning("参数配置信息不完整，请先设置");
        }
      }
    },
    {
      id: 3,
      title: '指标设置',
      content: '定义测评的量化指标，管理员可自定义测评指标维度与评分权重标准',
      btnName: '指标设置',
      icon: 'list-top',
      isDisplay: !!(form && form.id),
      onClick: () => {
        if (form && form.id) {
          showItemSet.value = true
          initRandomString.value = generateRandomString(10)
        } else {
          EleMessage.warning("参数配置信息不完整，请先设置");
        }
      }
    },
    {
      id: 2,
      title: '范围设置',
      content: '限定测评的适用范围，支持精准设置筛选的目标群体',
      btnName: '范围设置',
      icon: 'setting-config',
      isDisplay: !!(form && form.id),
      onClick: () => {
        if (form && form.id) {
          showScopeList.value = true
          initRandomString.value = generateRandomString(10)
        } else {
          EleMessage.warning("参数配置信息不完整，请先设置");
        }
      }
    },
    {
      id: 5,
      title: '审核时间设置',
      content: '自定义设置测评审核时间',
      btnName: '审核时间设置',
      icon: 'time',
      isDisplay: !!(form && form.id),
      onClick: () => {
        if (form && form.id) {
          if (bindWorkFlowInfo.value) {
            showWorkflowNode.value = true
            initRandomString.value = generateRandomString(10)
          } else {
            EleMessage.warning("参数配置信息不完整，请先设置工作流");
          }
        } else {
          EleMessage.warning("参数配置信息不完整，请先设置");
        }
      }
    },
    // {
    //   id: 1,
    //   title: '编辑测评参数',
    //   content: '调整已创建的测评参数，仅限管理员操作',
    //   btnName: '编辑',
    //   icon: 'file-editing',
    //   isDisplay: !!(form && form.id),
    //   onClick: () => {
    //     currentEnterType.value = 'edit';
    //   }
    // },
    // {
    //   id: 4,
    //   title: '参数复制',
    //   content: '快速复用已有参数配置，提升创建效率。调整复制后的参数名称、年份等差异化信息',
    //   btnName: '参数复制',
    //   isDisplay: !!(form && form.id),
    //   icon: 'copy-one',
    //   onClick: () => {
    //     if (form && form.id) {
    //       ElMessageBox.confirm(
    //         '您确定要复制“' + form.title + '”吗?',
    //         '系统提示',
    //         {type: 'warning', draggable: true}
    //       ).then(() => {
    //         currentEnterType.value = 'copy';
    //         form.title = form.title + "-副本"
    //       }).catch(() => {
    //       });
    //     } else {
    //       EleMessage.warning("参数配置信息不完整，请先设置");
    //     }
    //   }
    // },
    // {
    //   id: 7,
    //   title: '劳育成绩',
    //   content: '快速查看劳育成绩',
    //   btnName: '查看',
    //   isDisplay: !!(form && form.id),
    //   icon: 'five-star-badge',
    //   onClick: () => {
    //     if (form && form.id) {
    //       routerTypeSv.value = 'lycj'
    //       showScoreView.value = true
    //       viewName.value = '劳育成绩'
    //       initRandomString.value = generateRandomString(10)
    //     } else {
    //       EleMessage.warning("参数配置信息不完整，请先设置");
    //     }
    //   }
    // },
    // {
    //   id: 7,
    //   title: '美育成绩',
    //   content: '快速查看美育成绩',
    //   btnName: '查看',
    //   isDisplay: !!(form && form.id),
    //   icon: 'four-leaves',
    //   onClick: () => {
    //     if (form && form.id) {
    //       routerTypeSv.value = 'mycj'
    //       showScoreView.value = true
    //       viewName.value = '美育成绩'
    //       initRandomString.value = generateRandomString(10)
    //     } else {
    //       EleMessage.warning("参数配置信息不完整，请先设置");
    //     }
    //   }
    // },
    // {
    //   id: 7,
    //   title: '体育成绩',
    //   content: '快速查看体育成绩',
    //   btnName: '查看',
    //   isDisplay: !!(form && form.id),
    //   icon: 'soccer',
    //   onClick: () => {
    //     if (form && form.id) {
    //       routerTypeSv.value = 'tycj'
    //       showScoreView.value = true
    //       viewName.value = '体育成绩'
    //       initRandomString.value = generateRandomString(10)
    //     } else {
    //       EleMessage.warning("参数配置信息不完整，请先设置");
    //     }
    //   }
    // },
    {
      id: 7,
      title: '计算总成绩',
      content: '计算总成绩及查看排名',
      btnName: '计算',
      isDisplay: !!(form && form.id),
      icon: 'calculator-one',
      onClick: () => {
        if (form && form.id) {
          // routerTypeSv.value = 'tycj'
          showEvaluateScore.value = true
          // viewName.value = '体育成绩'
          initRandomString.value = generateRandomString(10)
        } else {
          EleMessage.warning("参数配置信息不完整，请先设置");
        }
      }
    }]
})

const onClickEdit = () => {
  currentEnterType.value = 'edit';
}
const onClickCancel = () => {
  currentEnterType.value = 'preview';
}
const onClickCopy = () => {
  if (form && form.id) {
    ElMessageBox.confirm(
      '您确定要复制“' + form.title + '”吗?',
      '系统提示',
      {type: 'warning', draggable: true}
    ).then(() => {
      currentEnterType.value = 'copy';
      form.title = form.title + "-副本"
    }).catch(() => {
    });
  } else {
    EleMessage.warning("参数配置信息不完整，请先设置");
  }
}

const handleCancelCopy = () => {
  currentEnterType.value = 'preview';
  form.title = form.title.replace(/-.*/, '')
};


let cardTitle = computed(() => {
  let title = null;
  if (currentEnterType.value === "preview") {
    title = form.title + "预览"
  } else if (currentEnterType.value === "add") {
    title = "新建"
  } else if (currentEnterType.value === "edit") {
    title = form.title + "编辑"
  } else if (currentEnterType.value === "copy") {
    title = form.title + "复制"
    // props.data.title +
  }
  return title
})

/** 是否是修改 */
const isUpdate = ref(false);
/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
  emit('done');
};

/** 表单数据 */
let [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  cpnf: '',
  title: '',
  dateRange: [],
  pyccid: '',
  njid: '',
  xyid: '',
  sqkssj: '',
  sqjzsj: '',
  sfkjscj: '',
  sfsyxjydqxx: '',
  sort: '',
  type: '',
});


/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

const baseFileds = ref([])

baseFileds.value = [
  {
    prop: 'title', label: '方案名称', type: 'input', required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
  {
    prop: 'cpnf', label: '测评年份', type: 'input', required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
  {
    prop: 'njid',
    label: '测评年级',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    props: {code: 'nj', filterable: true, type: 'checkboxButton',},
    colProps: {span: 12},
  },
  {
    prop: 'pyccid',
    label: '培养层次',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    props: {code: 'pycc', type: 'checkboxButton',},
    colProps: {span: 12},
  },
  {
    prop: 'xyid',
    label: '测评院系',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
    props: {code: 'xymc', filterable: true, isColProps: true, type: 'checkboxButton'},
  },
  {
    prop: 'dateRange',
    label: '申请时间',
    type: 'datetimerange',
    required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
    props: {
      placeholder: ''
    }
  },
  {
    prop: 'sfkjscj', label: '是否可计算成绩', type: 'switch',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},

  },
  {
    prop: 'sfsyxjydqxx', label: '是否使用学籍异动前信息', type: 'switch',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
]

const formItems = computed(() => {
  baseFileds.value.forEach(filed => {
    filed.showFlag = currentEnterType.value === 'preview' ? 'readonly' : '是'
    if (filed.type === "dictSelect") {
      filed.props.disabled = currentEnterType.value === 'preview';
      filed.props.readonly = currentEnterType.value === 'preview';
    } else if (filed.type === "switch" || filed.type === "radioButton") {
      filed['props'] = {
        "readonly": currentEnterType.value === 'preview',
        "disabled": currentEnterType.value === 'preview',
      }
    } else {
      filed['props'] = {
        "readonly": currentEnterType.value === 'preview',
      }
    }
  })
  return baseFileds.value;
})

// 回显时当前页不存在的数据
const cacheDataXy = ref();

const updateFormValue = (item, prop, newVal) => {
  form[item.prop] = newVal;
  if (prop === 'dateRange') {
    form['sqkssj'] = newVal[0]
    form['sqjzsj'] = newVal[1]
  }
}

/** 提交 */
const submitInfo = () => {
  formRef["value"]?.validate?.((valid, obj) => {
    if (valid) {
      loading.value = true;
      let rData = Object.assign({}, form)
      if (rData.pyccid) rData.pyccid = rData.pyccid.join(',')
      if (rData.xyid) rData.xyid = rData.xyid.join(',')
      if (rData.njid) rData.njid = rData.njid.join(',')
      if (currentEnterType.value === 'copy') {
        copyOperation(rData).then((msg) => {
          loading.value = false;
          if (msg) {
            EleMessage.success(msg);
            emit('done');
          }
        }).catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        }).finally(() => {
        });
      } else {
        operation(rData).then((msg) => {
          loading.value = false;
          if (msg) {
            EleMessage.success(msg);
            emit('done');
          }
        }).catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        }).finally(() => {
        });
      }
    } else {
      const errors = obj ? Object.keys(obj).length : 0;
      validMsgCount.value += errors;
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  });
};

const queryEvaluateConfig = () => {
  getEvaluateConfigById(props.currentId).then((resData) => {
    if (resData) {
      if (resData.pyccid) resData.pyccid = resData.pyccid.split(',')
      if (resData.njid) resData.njid = resData.njid.split(',')
      if (resData.xyid) resData.xyid = resData.xyid.split(',')
      if (resData.sqkssj && resData.sqjzsj) resData.dateRange = [resData.sqkssj, resData.sqjzsj]
      cacheDataXy.value = resData.xyid
      assignFields({
        ...resData,
      });
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 已绑定的工作流 */
const bindWorkFlowInfo = ref(null);

const queryEvaluateConfigWorkflowById = () => {
  getEvaluateConfigWorkflowById(props.currentId).then((list) => {
    bindWorkFlowInfo.value = list;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

watch(
  () => props.RandomString,
  (RandomString) => {
    currentEnterType.value = props.enterType;
    bindWorkFlowInfo.value=null
    validMsg.value=null
    resetFields()
    if (RandomString && props.currentId) {
      queryEvaluateConfig()
      //查询已绑定的工作流
      queryEvaluateConfigWorkflowById()
    } else {
      assignFields({
        type: props.routeType,
      });
    }
  }, {immediate: true})

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      isUpdate.value = !!props.currentId;
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  })

</script>
<style scoped>
@import "@/css/border-only-bottom.css";
</style>
