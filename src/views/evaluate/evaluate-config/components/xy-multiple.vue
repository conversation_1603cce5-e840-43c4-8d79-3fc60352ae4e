<template>
  <ele-table-select multiple
                    size="small"
                    clearable
                    placeholder="请选择院系"
                    value-key="id"
                    label-key="name"
                    :maxTagCount="2"
                    :maxTagTextLength="10"
                    v-model="selectedValue"
                    :table-props="tableProps"
                    :cache-data="cacheDataArrray"
                    :popper-width="520">
    <!--        @select="onSelectClick"-->
  </ele-table-select>
</template>

<script setup>
import {watch, ref, reactive} from 'vue';
import {getCodeDwb} from "@/views/base-code/dwb/api/index.js";

const props = defineProps({
  cacheData: Array,
});
/** 表格下拉选中值 */
const selectedValue = ref([]);

/** 表格配置 */
const tableProps = reactive({
  datasource: [],
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'code',
      label: '代码',
    },
    {
      prop: 'name',
      label: '院系名称',
      width: 210
    },
    {
      prop: 'sfjxdw',
      label: '是否教学单位',
    },
  ],
  showOverflowTooltip: true,
  toolbar: false,
  pagination: {
    pageSize: 6,
    layout: 'total, prev, pager, next, jumper',
    style: {padding: '0px'}
  },
  rowClickChecked: true
});

/** 查询表格数据 */
getCodeDwb().then((data) => {
  tableProps.datasource = data;
});

// 回显数据
const cacheDataArrray = ref();
/** 更新选中数据 */
watch(() => props.cacheData, (val) => {
    if (val) {
      cacheDataArrray.value = val;
      selectedValue.value = val.map(item => item.id);
    }
  },
  {
    immediate: true,
  }
);

</script>
