<!-- 搜索表单 -->
<template>
  <el-form size="small" @keyup.enter="search" @submit.prevent="">
    <el-row :gutter="8">
      <el-col :span="6">
        <el-form-item label="年级">
          <dict-data placeholder="请选择年级"
                     code="nj"
                     type="multipleSelect"
                     :model-value="formData['njidArray']"
                     @update:modelValue="(value) => updateValue('njid',value)"/>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="培养层次">
          <dict-data placeholder="请选择培养层次"
                     code="pycc"
                     type="multipleSelect"
                     :model-value="formData['pyccidArray']"
                     @update:modelValue="(value) => updateValue('pyccid',value)"/>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="院系">
          <dict-data placeholder="请选择院系"
                     code="xymc"
                     type="multipleSelect"
                     :model-value="formData['xyidArray']"
                     @update:modelValue="(value) => updateValue('xyid',value)"/>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="16px">
          <el-button type="primary" size="small" plain @click="search">查询</el-button>
          <el-button @click="reset" size="small">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import {ref} from "vue";

const emit = defineEmits(['search']);

const props = defineProps({});

const formData = ref({});

/** 更新值 */
const updateValue = (prop, value) => {
  formData["value"][prop + 'Array'] = value;
  formData["value"][prop] = formData["value"][prop + 'Array'].join()
}

/** 搜索 */
const search = () => {
  console.log(formData["value"])
  emit('search', {...formData["value"]});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};

</script>
