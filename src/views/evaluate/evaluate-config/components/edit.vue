<template>
  <ele-drawer size="55%"
              :title="cardTitle "
              style="max-width: 100%;"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              :model-value="modelValue"
              :body-style="{padding:'0 0 8px 0!important'}"
              @update:modelValue="updateModelValue">
    <ele-card flex-table
              :body-style="{ padding: '8px 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <ele-tabs type="plain"
                  size="small"
                  v-model="active"
                  :items="tabsItems"
                  @tabClick="onFormTabChange">
          <template #label="{ item, label}">
            <ele-text :type="active===item.name?'primary':''">
              <el-button :type="active===item.name?'primary':''"
                         plain circle size="small" style="width: 20px;height: 20px;"> {{ item.sort }}
              </el-button>
              {{ label }}
            </ele-text>
          </template>
        </ele-tabs>
      </template>
      <div :style="{height: (pageHeight-5)+'px',overflow:'auto'}">
        <el-form v-if="active === 'info'"
                 ref="formRef"
                 size="small"
                 :model="form"
                 style="margin: 10px;"
                 label-position="top"
                 labelWidth="auto">
          <el-row :gutter="8">
            <template v-for="item in formItems">
              <el-col :span="item.colProps.span" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
                <template v-if="item.prop==='xyid'">
                  <el-form-item :label="item.label" required>
                    <xy-multiple
                      :cacheData="cacheDataXy"
                      v-model="form[item.prop]"/>
                  </el-form-item>
                </template>
                <ProFormItem v-else :item="item"
                             :model="form"
                             @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </ProFormItem>
              </el-col>
              <el-col :span="item.colProps.span" v-else class="border-only-bottom">
                <template v-if="item.typeKey==='radio'">
                  <el-form-item :label="item.label">
                    <el-input clearable
                              readonly
                              :maxlength="20"
                              :model-value="form[item.prop]"/>
                  </el-form-item>
                </template>
                <template v-else>
                  <ProFormItem :item="item"
                               :model="form"
                               @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                    <template
                      v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                      #[name]="slotProps">
                      <slot :name="name" v-bind="slotProps || {}"></slot>
                    </template>
                  </ProFormItem>
                </template>
              </el-col>
            </template>
          </el-row>
        </el-form>
        <div v-if="active === 'workflow'">
          <ele-alert effect="light" v-if="!projectWorkFlow" :closable="false"
                     style="margin: 5px;padding: 8px!important;">
            <span
              style="font-size: 12px;">项目审核流程设置系统提供两种方式，方式一您如需要自定义流程，请点击创建流程模板按钮，系统会自动生成流程模板，您可以根据流程模板进行修改，修改完成后点击保存按钮，即可完成流程模板的创建;方式二您可以选择使用系统默认流程。</span>
          </ele-alert>
          <el-form v-if="!projectWorkFlow" size="small" @submit.prevent="" style="margin: 10px 8px -12px!important">
            <el-row :gutter="10">
              <el-col  :md="6" :sm="12" :xs="24">
                <el-form-item label="方式一：">
                  <el-button type="primary" plain @click="createWorkFlow">新建流程模板</el-button>
                </el-form-item>
              </el-col>
              <!--              <el-col :lg="1" :md="12" :sm="12" :xs="24">-->
              <!--              </el-col>-->
              <el-col :span="12">
                <el-form-item label="方式二：请选择现有审核流程">
                  <el-select v-model="workFlowSelected" class="ele-fluid" @change="workFlowSelectedChange">
                    <el-option v-for="wflow in workFlowData" :key="wflow.id" :value="wflow.id" :label="wflow.name"/>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <Workflow :pageHeight="projectWorkFlow?pageHeight-30:pageHeight-98"
                    :workFlowReadonly="enterType==='preview'"
                    :workflowId="workFlowSelected?workFlowSelected:workFlowSelectedInit"
                    @update:nodeConfig="handleDoneNodeConfig"/>
        </div>
      </div>
    </ele-card>
    <template #footer>
      <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" plain @click="updateModelValue(false)">关闭
      </el-button>
      <template v-if="enterType!=='preview'">
        <el-button v-if="active === 'info'" plain size="small" type="primary" :loading="loading" @click="submitInfo">
          保存并下一步
        </el-button>
        <el-button v-if="active === 'workflow'" plain size="small" type="primary" :loading="loading"
                   @click="saveWorkFlow">
          提交
        </el-button>
      </template>
    </template>
  </ele-drawer>
</template>

<script setup>
import {computed, onMounted, ref, unref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {
  evaluateConfigWorkflowById, getEvaluateConfigById,
  getEvaluateConfigWorkflowById,
  operation
} from "../api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";
import Workflow from "@/views/dingding-flow/components/workflow.vue";
import {getWorkFlow} from "@/views/dingding-flow/api/index.js";
import {mapMutations} from "@/plugins/lib.js";
import {CloseCircleOutlined} from "@/components/icons/index.js";
import ProForm from "@/components/ProForm/index.vue";
import XyMultiple from "@/views/evaluate/evaluate-config/components/xy-multiple.vue";
import AddressMultiple from "@/views/checkin/item/details/address-multiple.vue";
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emit = defineEmits(['done', 'update:modelValue']);

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  currentId: String,
  RandomString: String,
  routeType: String,
  enterType: String,//preview：详情 add:添加 edit:编辑
});

let cardTitle = computed(() => {
  let title = null;
  if (props.enterType === "preview") {
    title = props.data.title + "详情信息"
  } else if (props.enterType === "add") {
    title = "新建参数配置"
  } else if (props.enterType === "edit") {
    title = "编辑参数配置" + props.data.title
  }
  return title
})

/** 是否是修改 */
const isUpdate = ref(false);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
  emit('done');
};

const {setIsTried} = mapMutations()
/** 标签页选中 */
const active = ref('info');
const tabsItems = computed(() => {
  return [
    {
      label: '测评年份设置',
      name: 'info',
      sort: 1
    },
    {
      label: '加分项申请审核流程设置',
      name: 'workflow',
      sort: 2
    },
  ]
})
/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  cpnf: '',
  title: '',
  dateRange: [],
  pyccid: '',
  njid: '',
  xyid: '',
  sqkssj: '',
  sqjzsj: '',
  sfkjscj: '',
  sfsyxjydqxx: '',
  sort: '',
  type: '',
});

/** 已绑定的工作流 */
const projectWorkFlow = ref(null);

const workFlow = ref(false);
const workFlowData = ref([]);
const workFlowSelected = ref(null);
const workFlowSelectedInit = ref(null);
const formData = ref(null);
const validMsg = ref(null);
const validMsgCount = ref(0);
const tipList = ref([]);
const tipVisible = ref(false);
/** 新建流程模板 */
const createWorkFlow = () => {
  workFlow.value = true;
  workFlowSelectedInit.value = 'workFlowSelected';
  workFlowSelected.value = null;
};

/** 选择编辑流程模版 */
const workFlowSelectedChange = (event) => {
  if (event) {
    workFlowSelectedInit.value = null;
    workFlow.value = true
  }
};

/** 流程配置 */
const processConfig = ref({});
const handleDoneNodeConfig = (data) => {
  processConfig.value = data
};

const queryWorkFlows = () => {
  getWorkFlow().then((list) => {
    if (list) {
      workFlowData.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
const queryEvaluateConfigWorkflowById = () => {
  if (props.currentId) {
    getEvaluateConfigWorkflowById(props.currentId).then((list) => {
      if (list) {
        projectWorkFlow.value = list;
        workFlowSelected.value = list.id
        workFlowSelectedChange(true)
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    });
  }
}

const reErr = ({childNode}) => {
  if (childNode) {
    let {type, error, nodeName, conditionNodes} = childNode;
    if (type === 1 || type === 2) {
      if (error) {
        tipList.value.push({
          name: nodeName,
          type: ["", "审核人", "抄送人"][type],
        });
      }
      reErr(childNode);
    } else if (type === 3) {
      reErr(childNode);
    } else if (type === 4) {
      reErr(childNode);
      for (var i = 0; i < conditionNodes.length; i++) {
        if (conditionNodes[i].error) {
          tipList.value.push({name: conditionNodes[i].nodeName, type: "条件"});
        }
        reErr(conditionNodes[i]);
      }
    }
  } else {
    childNode = null;
  }
};
const saveWorkFlow = async () => {
  setIsTried(true);
  tipList.value = [];
  reErr(processConfig.value.nodeConfig);
  if (tipList.value.length !== 0) {
    tipVisible.value = true;
    return;
  }
  let FlowData = processConfig.value;
  let workflowData = {
    id: workFlowSelected.value ? workFlowSelected.value : workFlowSelectedInit.value,
    name: FlowData.nodeConfig.nodeName,//流程名称
    projectId: form.id,//项目ID
    year: '',//年份
    workflowNodes: FlowData.nodeConfig.childNode
  }
  loading.value = true;
  evaluateConfigWorkflowById(form.id, workflowData).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    if (msg) updateModelValue(false)
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
};

/** tab选择改变事件 */
const onFormTabChange = (event) => {
  projectWorkFlow.value = null;
  workFlowSelected.value = null
  workFlowData.value = null;
  workFlowSelectedInit.value = null;
  workFlow.value = false
  validMsg.value = null;
  validMsgCount.value = 0;
  if (event.props.name === 'workflow') {
    queryWorkFlows();
    queryEvaluateConfigWorkflowById()
  }
};
/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

const baseFileds = ref([])

baseFileds.value = [
  {
    prop: 'title', label: '名称', type: 'input', required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
  {
    prop: 'cpnf', label: '测评年份', type: 'input', required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
  {
    prop: 'dateRange',
    label: '申请时间范围',
    type: 'datetimerange',
    required: true,
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 24},
    props: {
      placeholder: ''
    }
  },
  // {
  //   prop: 'sqkssj',
  //   label: '申请开始时间',
  //   type: 'datetime',
  //   required: true,
  //   showFlag: '是',
  //   selfModifyFlag: '是',
  //   colProps: {span: 12},
  //   props: {
  //     placeholder: ''
  //   }
  // }, {
  //   prop: 'sqjzsj',
  //   label: '申请截止时间',
  //   type: 'datetime',
  //   required: true,
  //   showFlag: '是',
  //   selfModifyFlag: '是',
  //   colProps: {span: 12},
  //   props: {
  //     placeholder: ''
  //   }
  // },
  {
    prop: 'njid',
    label: '年级',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    props: {code: 'nj', filterable: true, type: 'checkboxButton',},
    colProps: {span: 12},
  },
  {
    prop: 'pyccid',
    label: '培养层次',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    props: {code: 'pycc', type: 'checkboxButton',},
    colProps: {span: 12},
  },
  {
    prop: 'xyid',
    label: '院系',
    // type: 'dictCheckbox',
    type: 'dictSelect',
    required: true,
    typeKey: 'multipleSelect',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 24},
    props: {code: 'xymc', filterable: true, isColProps: true, type: 'checkboxButton'},
  },
  {
    prop: 'sfkjscj', label: '是否可计算成绩', type: 'switch',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},

  },
  {
    prop: 'sfsyxjydqxx', label: '是否使用学籍异动前信息', type: 'switch',
    showFlag: '是',
    selfModifyFlag: '是',
    colProps: {span: 12},
  },
]

const formItems = computed(() => {
  baseFileds.value.forEach(filed => {
    filed.showFlag = props.enterType === 'preview' ? 'readonly' : '是'
    if (filed.type === "dictSelect") {
      filed.props.disabled = props.enterType === 'preview';
      filed.props.readonly = props.enterType === 'preview';
    } else if (filed.type === "switch" || filed.type === "radioButton") {
      filed['props'] = {
        "readonly": props.enterType === 'preview',
        "disabled": props.enterType === 'preview',
      }
    } else {
      filed['props'] = {
        "readonly": props.enterType === 'preview',
      }
    }
  })
  return baseFileds.value;
})

// 回显时当前页不存在的数据
const cacheDataXy = ref();

const updateFormValue = (item, prop, newVal) => {
  form[item.prop] = newVal;
  if (prop === 'dateRange') {
    form['sqkssj'] = newVal[0]
    form['sqjzsj'] = newVal[1]
  }
}


/** 提交 */
const submitInfo = () => {
  formRef["value"]?.validate?.((valid, obj) => {
    if (valid) {
      loading.value = true;
      let rData = Object.assign({}, form)
      if (rData.pyccid) rData.pyccid = rData.pyccid.join(',')
      if (rData.xyid) rData.xyid = rData.xyid.join(',')
      if (rData.njid) rData.njid = rData.njid.join(',')
      operation(rData).then((msg) => {
        loading.value = false;
        if (msg) {
          EleMessage.success(msg);
          active.value = "workflow";
          queryEvaluateConfigWorkflowById();
        }
      }).catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      }).finally(() => {
      });
    } else {
      const errors = obj ? Object.keys(obj).length : 0;
      validMsgCount.value += errors;
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;

    }
  });
};

const queryEvaluateConfig = () => {
  getEvaluateConfigById(props.currentId).then((resData) => {
    if (resData) {
      if (resData.pyccid) resData.pyccid = resData.pyccid.split(',')
      if (resData.njid) resData.njid = resData.njid.split(',')
      if (resData.xyid) resData.xyid = resData.xyid.split(',')
      if (resData.sqkssj && resData.sqjzsj) resData.dateRange = [resData.sqkssj, resData.sqjzsj]

      cacheDataXy.value = resData.xyid
      assignFields({
        ...resData,
      });
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

watch(
  () => props.RandomString,
  (RandomString) => {
    active.value = 'info'
    if (RandomString && props.currentId) {
      queryEvaluateConfig()
    } else {
      assignFields({
        type: props.routeType,
      });
    }
  }, {immediate: true})

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      isUpdate.value = !!props.currentId;
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  })

</script>
