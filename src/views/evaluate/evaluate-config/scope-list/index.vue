<!--参数配置所选范围列表-->
<template>
  <ele-drawer size="60%"
              :title="'['+(currentData&&currentData.title)+'] 参数配置所选范围维护'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px 8px!important' ,marginTop:0}"
              @update:modelValue="updateModelValue">
    <ele-card flex-table
              :body-style="{ padding: '0 5px 10px 5px!important' }"
              :header-style="{borderBottom:'unset!important'}">
      <template #header>
        <SelectSearch @search="onDoneSearch"/>
      </template>
      <!--      <TablePreview v-if="rDataList.length>0" :ref="el => getRiskSpreadRef(el, 0)"-->
      <!--                    dataName=""-->
      <!--                    approverType="evaluateScopeList"-->
      <!--                    :approverData="rDataList"-->
      <!--                    :RandomString="RandomString"-->
      <!--                    :approverId="currentId"-->
      <!--                    @onDoneGroup="handleDoneGroup"/>-->
      <div :style="{ height:(pageHeight-10)+'px',overflow:'auto' }">
        <el-form ref="formRef" size="small" :model="form" label-width="0px" @submit.prevent="">
          <ele-table size="small" border style="table-layout: fixed;">
            <thead>
            <tr>
              <th v-for="header in initTableHeader"
                  :style="header?.hide? 'display: none;':('width:'+header.minWidth+'px')">
                {{ header.label }}
              </th>
            </tr>
            </thead>
            <tr v-for="(row, index) in form.users" :key="row.key">
              <td v-for="item in initTableHeader" :style="item?.hide? 'display: none;':''">
                <el-form-item
                  v-if="['nodeStatus','questionSelCode','extendsField','pyccmc','njmc','xymc'].includes(item.type)"
                  label=""
                  class="form-error-popper"
                  style="margin-bottom: 0 !important">
                  <div class="editable-cell-text">{{ row[item.prop] }}</div>
                </el-form-item>
                <template v-else>
                  <TableFormItem v-if="row.isEdit"
                                 :item="item"
                                 :index="index"
                                 :model="row"
                                 @updateValue="(value) => updateValue(index,item.prop, value)">
                    <template
                      v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                      #[name]="slotProps">
                      <slot :name="name" v-bind="slotProps || {}"></slot>
                    </template>
                  </TableFormItem>
                  <div v-else class="editable-cell-text">{{ row[item.prop] }}</div>
                </template>
              </td>
            </tr>
          </ele-table>
        </el-form>
      </div>
    </ele-card>
    <!-- 分页按钮 -->
    <ele-pagination @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[20,40,60,80, 100, 200]"
                    :page-size="pageSize"
                    :total="total"
                    size="small"
                    :background="true"
                    layout="total, prev, pager, next,  sizes,jumper"
                    :style="{
                    marginTop:'8px',
                    marginBottom:'8px',
                    justifyContent: 'center',
                  }">
    </ele-pagination>
    <!--    <template #footer>-->
    <!--      <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">-->
    <!--        <span>{{ validMsg }}</span>-->
    <!--      </ele-text>-->
    <!--      <el-button size="small" @click="updateModelValue(false)">返回</el-button>-->
    <!--      <el-button size="small" type="primary" plain @click="saveConditionField">-->
    <!--        保存-->
    <!--      </el-button>-->
    <!--    </template>-->
  </ele-drawer>
</template>

<script setup>
import {onMounted, ref, watch,} from 'vue';
import {getEvaluateConfigScopeList, operationScope} from '../api/index.js';
import { ElMessage as EleMessage} from "element-plus";
import SelectSearch from "../components/search.vue"
import {generateRandomString} from '@/utils/common_bak2.js';
import TableFormItem from "@/components/ProForm/components/table-form-item.vue";
import {useFormData} from "@/utils/use-form-data.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  RandomString: String,
  currentData: Object,
  currentId: String,
});

const initRandomString = ref('')

/** 表单实例 */
const formRef = ref(null);
/** 表单数据 */
const [form, resetFields] = useFormData({
  users: []
});

/** 请求状态 */
const loading = ref(true);

const total = ref(null); // 假设总数据量为100
const pageSize = ref(20); // 每页显示10条数据
const currentPage = ref(1); // 当前页码
const queryParams = ref({});
const rDataList = ref([]);
const initTableHeader = ref([
  {
    prop: 'njmc',
    label: '年级',
    type: 'njmc',
    minWidth: 80,
  },
  {
    prop: 'pyccmc',
    label: '培养层次',
    type: 'pyccmc',
    minWidth: 100,
  },
  {
    prop: 'xymc',
    label: '院系',
    type: 'xymc',
  },
  {
    prop: 'dateRange',
    label: '申请时间范围',
    type: 'datetimerange',
    minWidth: 330,
    editable: true,
    required: true,
  },
  // {
  //   prop: 'sqkssj',
  //   label: '申请开始时间',
  //   type: 'datetime',
  //   editable: true,
  //   required: true,
  // },
  // {
  //   prop: 'sqjzsj',
  //   label: '申请截止时间',
  //   type: 'datetime',
  //   editable: true,
  //   required: true,
  // },
  {
    prop: 'stageState',
    label: '状态',
    type: 'select',
    options: [
      {label: '待审核', value: '0'},
      {label: '待计算', value: '1'},
      {label: '已结束', value: '2'},
    ],
    editable: true,
  },
])

const saveEvaluateConfig = (row) => {
  loading.value = true;
  operationScope(row).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    row.isEdit = true;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 更新值 */
const updateValue = (index, prop, value) => {
  console.log("更新值=====", index, prop, value, form.users[index])
  form.users[index][prop] = value
  if (prop === 'dateRange') {
    form.users[index]['sqkssj'] = value[0]
    form.users[index]['sqjzsj'] = value[1]
  }
  saveEvaluateConfig(form.users[index])
}

const onDoneSearch = (data) => {
  currentPage.value = 1;
  pageSize.value = 20;
  queryParams.value = data;
  initRandomString.value = generateRandomString(10);
  QueryEvaluateConfigScopeList()
};

const QueryEvaluateConfigScopeList = () => {
  rDataList.value = []
  form.users = []
  let obj = {configId: props.currentId, page: currentPage.value, limit: pageSize.value}
  let newObj = Object.assign(obj, queryParams.value);
  getEvaluateConfigScopeList(newObj).then((resData) => {
    total.value = resData.count
    let resList = resData.list;
    if (resList.length > 0) {
      resList.forEach(item => {
        item.dateRange = [item.sqkssj, item.sqjzsj]
        item.isEdit = true
      })
      rDataList.value = resList;
      form.users = rDataList.value
    } else {
      rDataList.value = []
      form.users = []
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};
// 分页大小改变时的回调
const handleSizeChange = (val) => {
  pageSize.value = val;
}

// 当前页改变时的回调
const handleCurrentChange = (val) => {
  currentPage.value = val;
}

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(
  currentPage,
  (newVal) => {
    if (newVal) QueryEvaluateConfigScopeList()
  },
  {immediate: true}
)

watch(
  pageSize,
  (newVal) => {
    if (newVal) QueryEvaluateConfigScopeList()
  },
  {immediate: true}
)


watch(
  () => props.RandomString,
  (RandomString) => {
    initRandomString.value = RandomString
    if (RandomString && props.currentId) {
      QueryEvaluateConfigScopeList()
    }
  }, {immediate: true})

</script>

<script>
export default {
  NAME: 'EVALUATESCOPELIST'
};
</script>
