<!--指标设置-->
<template>
  <ele-card header="操作指引" :body-style="{ padding: '5px 10px !important' }">
    <template #extra>
      <div style="display: flex; align-items: center" @click="onClose">
        <IconPark name="preview-close" size="20" strokeWidth="3"/>
        <ele-text style="cursor: pointer;" type="secondary" size="xs">
          点我关闭操作指引
        </ele-text>
      </div>
    </template>
    <div :style="{height: (pageHeight+3)+'px', overflow:'auto'}">
      <div class="article-list" v-if="showOptView">
        <div class="list-item" v-for="(item,index) in optViewsData">
          <div class="list-item-body">
            <div style="flex: 1">
              <ele-text size="md" style="padding: 5px;">{{ item.group }}</ele-text>
              <div style="margin-top: 2px; display: flex; align-items: center">
                <ele-dashboard size="20px" space="2px">
                  <div style="font-size: 8px">{{ index + 1 }}</div>
                </ele-dashboard>
                <span style="padding-left: 3px;"> {{ item.title }}</span>
              </div>
              <ele-text type="secondary" size="sm" style="margin-top: 6px;flex: 1; padding-left: 6px">
                {{ item.content }}
              </ele-text>
              <ele-text type="secondary" size="sm" style="flex: 1; padding-left: 6px">
                {{ item.applicableScenarios }}
              </ele-text>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import {ref,} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";

const emits = defineEmits(['doneClose']);

const props = defineProps({
  showOptView: Boolean,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const optViewsData = ref([
  {
    id: 1,
    group: '左侧方案维护',
    title: '输入名称搜索',
    content: '功能说明：通过输入方案名称快速定位已有测评配置，支持模糊匹配。',
    applicableScenarios: '适用场景：需要快速查找或筛选特定测评方案时使用。',
  },
  {
    id: 6,
    group: '',
    title: '新建方案',
    content: '功能说明：创建新的测评方案配置，支持基础信息录入和方案逻辑设置。',
    applicableScenarios: '核心操作:调整方案的名称、年份、测评时间、范围等配置。',
  },
  {
    id: 2,
    group: '中间操作区',
    title: '范围设置',
    content: '功能说明：限定测评的适用范围，支持精准筛选目标群体。',
    applicableScenarios: '配置项：可以自定义限定测评适用范围的申请时间及状态。',
  },
  {
    id: 3,
    group: '',
    title: '指标设置',
    content: '功能说明：定义测评的量化指标，管理员可自定义测评指标维度与评分权重标准',
    applicableScenarios: '核心功能：添加/删除测评维度（如学术成绩、实践能力）；设置指标权重（百分比分配）。',
  },
  {
    id: 4,
    group: '',
    title: '方案复制',
    content: '功能说明：快速复用已有方案配置，提升创建效率。调整复制后的方案名称、年份等差异化信息',
    applicableScenarios: '核心功能：选择需要复制的测评方案模板；调整复制后的方案名称、年份等差异化信息；保存为新配置。',
  },
  {
    id: 5,
    group: '',
    title: '审核时间设置',
    content: '功能说明：自定义设置测评审核时间',
    applicableScenarios: '配置项：可以自定义审核时间。',
  },])

const onClose = () => {
  emits('doneClose');
};

</script>

<script>
export default {
  NAME: 'OPTVIEWINDEX'
};
</script>
