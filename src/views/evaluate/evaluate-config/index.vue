<template>
  <ele-page hide-footer flex-table>
    <el-row>
      <el-col :md="5" :sm="12" :xs="24">
        <ele-card header="方案管理" :body-style="{ padding: '5px 0!important' }">
          <template #extra>
            <div style="display: flex; align-items: center" @click="openEdit('add',null)">
              <IconPark name="add-one" size="18" strokeWidth="3"/>
              <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
                新建方案
              </ele-text>
            </div>
          </template>
          <div :style="{height: (pageHeight)+'px', overflow:'auto'}">
            <div style="padding: 6px 10px 10px 6px">
              <el-input clearable size="small"
                        :maxlength="20"
                        v-model="keywords"
                        placeholder="输入名称搜索"
                        :prefix-icon="SearchOutlined"/>
            </div>
            <ele-loading :loading="loading"
                         :style="{overflow: 'auto' }">
              <el-tree ref="treeRef"
                       :data="data"
                       highlight-current
                       :indent="20"
                       node-key="id"
                       :props="{ label: 'title' }"
                       :expand-on-click-node="false"
                       :default-expand-all="true"
                       :filter-node-method="filterNode"
                       :style="{
                              '--ele-tree-item-height': '34px',
                              '--ele-tree-expand-padding': 0,
                              '--ele-tree-expand-margin': 0
                            }"
                       @node-click="handleNodeClick">
                <template #default="{ data: d }">
                  <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.title }}
                        </span>
                      </span>
                      <!-- 图标容器 -->
                      <span v-if="selectedKey===d.id" class="icon-container">
                        <a @click="remove(d)">
                          <IconPark name="delete" size="18" strokeWidth="3" color="#d0021b"/>
                        </a>
                      </span>
                    </span>
                  </div>
                </template>
              </el-tree>
            </ele-loading>
          </div>
        </ele-card>
      </el-col>
      <!-- 中间编辑区，动态调整宽度 -->
      <el-col :md="showOptView ? 14 : 19" :sm="12" :xs="24">
        <!-- 中间编辑区-->
        <EditInfo v-if="enterType" :data="current"
                  :routeType="routeType"
                  :enterType="enterType"
                  :RandomString="RandomString"
                  :currentId="currentId"
                  @done="reload"/>
      </el-col>
      <el-col v-if="showOptView" :md="5" :sm="12" :xs="24">
        <!-- 右侧操作指引-->
        <OptView :showOptView="showOptView" @doneClose="showOptView=false"/>
      </el-col>
    </el-row>
    <!-- 操作指引悬浮层-->
    <FloatingActionButton v-if="!showOptView" @doneClick="handleDoneClick"/>
  </ele-page>
</template>

<script setup>
import {nextTick, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import EditInfo from './components/edit-info.vue';
import {getEvaluateConfig, queryPage, removes} from "./api/index.js";
import {generateRandomString} from '@/utils/common_bak2.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import FloatingActionButton from '@/components/FluidCursor/FloatingActionButton.vue'
import {SearchOutlined} from "@/components/icons/index.js";
import IconPark from "@/components/IconPark/index.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import OptView from "./opt-view/index.vue"

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {push, currentRoute, replace} = useRouter();
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} =
  usePageTab();

const enterType = ref(null);
const {path, query} = unref(currentRoute);
let pathArray = path.split("/");
console.log(pathArray)
let routeType = pathArray[3]

/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'cpnf',
    slot: 'cpnf',
    label: '测评年份',
  },
  {
    prop: 'title',
    label: '名称',
  },
  {
    prop: 'sqkssj',
    label: '申请开始时间',
  },
  {
    prop: 'sqjzsj',
    label: '申请截止时间',
  },
  {
    prop: 'sfkjscj',
    label: '是否可计算成绩',
  },
  {
    prop: 'sfsyxjydqxx',
    label: '是否使用学籍异动前信息',
  },
  {
    prop: 'state',
    label: '状态',
    slot: 'state',
  },
  {
    columnKey: 'action',
    label: '操作',
    slot: 'action',
    width: 340,
  }
]);

/** 表格选中数据 */
const selections = ref([]);

/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 机构搜索关键字 */
const keywords = ref('');

const showOptView = ref(true);

/** 树形数据 */
const data = ref([]);

/** 当前编辑数据 */
const current = ref(null);
const currentId = ref(null);
const RandomString = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/**
 * 获取组信息
 */
const queryEvaluateConfig = () => {
  loading.value = true;
  let newObj = {
    type: routeType,
  }
  getEvaluateConfig(newObj).then((list) => {
    loading.value = false;
    data.value = list ?? [];
    nextTick(() => {
      handleNodeClick(data.value[0]);
    });
  });
}

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  enterType.value = "preview"
  RandomString.value = generateRandomString(10)
  if (row && row.id) {
    current.value = row;
    currentId.value = row.id
    treeRef["value"]?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id
  } else {
    current.value = null;
  }

};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.name && data.name.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});


/** 表格数据源 */
const datasource = ({pages, where, orders}) => {
  return queryPage({
    ...where,
    ...orders,
    ...pages,
    type: routeType,
    // itemId: props.organizationId
  });
};

/** 搜索 */
const reload = (where) => {
  const loading = ElLoading.service('页面数据请求中..');
  tableRef["value"]?.reload?.({page: 1, where});
  reloadPageTab();
  loading.close();
};

const handleDoneClick = () => {
  showOptView.value = true
};

/** 打开编辑弹窗 */
const openEdit = (type, row) => {
  enterType.value = type;
  currentId.value = null;
  current.value = null;
  if (type !== 'add' && row) {
    currentId.value = row.id
    current.value = row
  } else {
    selectedKey.value = null;
    // treeRef.value?.setCurrentKey(Symbol()); // 或一个随机不存在的 key
    // treeRef.value?.setCurrentKey(null);
  }
  RandomString.value = generateRandomString(10)
};

const showScopeList = ref(false);

const openScopeList = (row) => {
  current.value = row ?? null;
  if (row) currentId.value = row.id
  RandomString.value = generateRandomString(10)
  showScopeList.value = true;
};

const showWorkflowNode = ref(false);
const openWorkflowNode = (row) => {
  current.value = row ?? null;
  if (row) currentId.value = row.id
  RandomString.value = generateRandomString(10)
  showWorkflowNode.value = true;
};

const openEvaluateItem = (row) => {
  removePageTab({key: getRouteTabKey()});
  // config 参数过去的指标管理，需要返回到参数管理
  replace({
    path: '/evaluate/item/' + routeType,
    query: {configId: row.id, enterType: 'config'}
  });
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.title).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

queryEvaluateConfig()
</script>
