<!--德美体成绩查看-->
<template>
  <ele-drawer size="60%"
              :title="'['+(currentData&&currentData.title)+'] '+viewName"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0px!important' ,marginTop:0,background:'#f0f2f5',height:(pageHeight+60)+'px' }"
              @update:modelValue="updateModelValue">
    <ScoreIndex :setConfigId="currentId"
                setEnterType="config"
                :configRouterType="currentData?.type"
                :routerTypeSv="routerTypeSv"
                :RandomString="RandomString"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref,} from 'vue';
import ScoreIndex from "../../score/index.vue"
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentData: Object,
  currentId: String,
  RandomString: String,
  viewName: String,
  routerTypeSv: String,//lycj，mycj，tycj
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};
</script>

<script>
export default {
  NAME: 'SCOREVIEWINDEX'
};
</script>
