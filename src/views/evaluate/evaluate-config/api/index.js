/**
 * 参数配置信息管理
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getEvaluateConfig(params) {
    const res = await request.get('/evaluate/evaluate-config', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/evaluate/evaluate-config/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 根据id查询
 */
export async function getEvaluateConfigById(id) {
    const res = await request.get('/evaluate/evaluate-config/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function getEvaluateConfigScopeList(params) {
    const res = await request.get('/evaluate/evaluate-config/pageScopeList', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 综测参数范围维护（权限标识：evaluate:evaluateConfig:operation）
 * @param data
 * @returns {Promise<*>}
 */
export async function operationScope(data) {
  const res = await request.post('/evaluate/evaluate-config/operationScope', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function updateNodeApprovalTime(data) {
  const res = await request.post('/workflow/updateNodeApprovalTime', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getWorkflowAllNode(params) {
  const res = await request.get('/workflow/getAllNode', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
 */
export async function operation(data) {
    const res = await request.post('/evaluate/evaluate-config/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 综测配置信息复制
 * @param data
 * @returns {Promise<*>}
 */
export async function copyOperation(data) {
    const res = await request.post('/evaluate/evaluate-config/copyOperation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 综测加减分项审核工作流配置
 */
export async function evaluateConfigWorkflowById(id,data) {
    const res = await request.post('/evaluate/evaluate-config/'+id+'/workflow',data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询工作流
 */
export async function getEvaluateConfigWorkflowById(id) {
  const res = await request.get('/evaluate/evaluate-config/'+id+'/workflow');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/evaluate/evaluate-config/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
