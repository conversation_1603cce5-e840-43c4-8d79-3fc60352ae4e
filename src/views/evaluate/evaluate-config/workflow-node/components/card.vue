<!--批量更新审核时间-->
<template>
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important', height: '300px',overflow:'auto' }">
      <el-form ref="formRef" size="small" :model="form" label-width="0px" @submit.prevent="">
        <ele-table size="small" border style="min-width: 580px; table-layout: fixed">
          <thead>
          <tr>
            <th v-for="header in initTableHeader"
                :style="header?.hide? 'display: none;':('width:'+header.minWidth+'px')">
              {{ header.label }}s s
            </th>
          </tr>
          </thead>
          <tr v-for="(row, index) in form.users" :key="row.key">
            <td v-for="item in initTableHeader" :style="item?.hide? 'display: none;':''">
              <el-form-item
                v-if="['nodeName','reviewType'].includes(item.type)"
                label=""
                class="form-error-popper"
                style="margin-bottom: 0 !important">
                <div class="editable-cell-text">{{ row[item.prop] }}</div>
              </el-form-item>
              <template v-else>
                <TableFormItem v-if="row.isEdit"
                               :item="item"
                               :index="index"
                               :model="row"
                               @updateValue="(value) => updateValue(index,item.prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </TableFormItem>
                <div v-else class="editable-cell-text">{{ row[item.prop] }}</div>
              </template>
            </td>
          </tr>
        </ele-table>
      </el-form>
    </ele-card>
</template>

<script setup>
import { ref, watch,} from 'vue';
import {getWorkflowAllNode,  updateNodeApprovalTime} from '../../api/index.js';
import { ElMessage as EleMessage} from "element-plus";
import TableFormItem from "@/components/ProForm/components/table-form-item.vue";
import {useFormData} from "@/utils/use-form-data.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  currentData: Object,
  currentId: String,
  RandomString: String,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const initRandomString = ref('')

/** 表单实例 */
const formRef = ref(null);
/** 表单数据 */
const [form, resetFields] = useFormData({
  users: []
});

/** 请求状态 */
const loading = ref(true);
const rDataList = ref([]);
const initTableHeader = ref([
  {
    prop: 'nodeName',
    label: '审核节点',
    type: 'nodeName',
    minWidth: 100,
  },
  {
    prop: 'reviewType',
    label: '审批类型',
    type: 'reviewType',
    minWidth: 80,
  },
  {
    prop: 'dateRange',
    label: '审核时间范围',
    type: 'datetimerange',
    minWidth: 330,
    editable: true,
    required: true,
  },
])

const saveUpdateNodeApprovalTime = (row) => {
  loading.value = true;
  updateNodeApprovalTime([row]).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    row.isEdit = true;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 更新值 */
const updateValue = (index, prop, value) => {
  console.log("更新值=====", index, prop, value, form.users[index])
  form.users[index][prop] = value
  if (prop === 'dateRange') {
    form.users[index]['startTime'] = value[0]
    form.users[index]['endTime'] = value[1]
  }
  saveUpdateNodeApprovalTime(form.users[index])
}

const QueryWorkflowAllNode = () => {
  rDataList.value = []
  form.users = []
  let obj = {projectId: props.currentId, year: props.cpnf}
  getWorkflowAllNode(obj).then((resData) => {
    console.log(resData)
    if (resData.length > 0) {
      resData.forEach(item => {
        item.dateRange = [item.startTime, item.endTime]
        item.isEdit = true
      })
      rDataList.value = resData;
      form.users = rDataList.value
    } else {
      rDataList.value = []
      form.users = []
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};


watch(
  () => props.RandomString,
  (RandomString) => {
    initRandomString.value = RandomString
    if (RandomString && props.currentId) {
      QueryWorkflowAllNode()
    }
  }, {immediate: true})

</script>

<script>
export default {
  NAME: 'WORKFLOWALLNODECARD'
};
</script>
