<!--指标设置-->
<template>
  <ele-drawer size="70%"
              :title="'['+(currentData&&currentData.title)+'] 指标设置'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0px!important' ,marginTop:0,background:'#f0f2f5',height:'auto'}"
              @update:modelValue="updateModelValue">
    <ItemIndex :setConfigId="currentId" setEnterType="config"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {onMounted, ref, watch,} from 'vue';
import ItemIndex from "../../item/index.vue"
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentData: Object,
  currentId: String,
  RandomString: String,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const initRandomString = ref('')
/** 提交状态 */
const loading = ref(false);
/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(
  () => props.RandomString,
  (RandomString) => {
    initRandomString.value = RandomString
    if (RandomString) {

    }
  }, {immediate: true})

</script>

<script>
export default {
  NAME: 'ITEMSETINDEX'
};
</script>
