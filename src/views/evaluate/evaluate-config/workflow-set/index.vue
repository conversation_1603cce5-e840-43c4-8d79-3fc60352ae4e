<!--工作流设置-->
<template>
  <ele-drawer size="60%"
              :title="'['+(currentData&&currentData.title)+'] 工作流设置'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px 8px!important' ,marginTop:0,}"
              @update:modelValue="updateModelValue">
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important', height: '300px',overflow:'auto' }">
      <ele-alert effect="light" v-if="!projectWorkFlow" :closable="false"
                 style="margin: 5px;padding: 8px!important;">
            <span
              style="font-size: 12px;">项目审核流程设置系统提供两种方式，方式一您如需要自定义流程，请点击创建流程模板按钮，系统会自动生成流程模板，您可以根据流程模板进行修改，修改完成后点击保存按钮，即可完成流程模板的创建;方式二您可以选择使用系统默认流程。</span>
      </ele-alert>
      <el-form v-if="!projectWorkFlow" size="small" @submit.prevent="" style="margin: 10px 8px -12px!important">
        <el-row :gutter="10">
          <el-col  :md="6" :sm="12" :xs="24">
            <el-form-item label="方式一：">
              <el-button type="primary" plain @click="createWorkFlow">新建流程模板</el-button>
            </el-form-item>
          </el-col>
          <!--              <el-col :lg="1" :md="12" :sm="12" :xs="24">-->
          <!--              </el-col>-->
          <el-col :span="12">
            <el-form-item label="方式二：请选择现有审核流程">
              <el-select v-model="workFlowSelected" class="ele-fluid" @change="workFlowSelectedChange">
                <el-option v-for="wflow in workFlowData" :key="wflow.id" :value="wflow.id" :label="wflow.name"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <Workflow :pageHeight="projectWorkFlow?pageHeight-30:pageHeight-98"
                :workFlowReadonly="enterType==='preview'"
                :workflowId="workFlowSelected?workFlowSelected:workFlowSelectedInit"
                @update:nodeConfig="handleDoneNodeConfig"/>
    </ele-card>
    <template #footer>
      <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button plain size="small" type="primary" :loading="loading"
                 @click="saveWorkFlow">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {onMounted, ref, watch,} from 'vue';
import {
  evaluateConfigWorkflowById,
  getEvaluateConfigScopeList,
  getEvaluateConfigWorkflowById,
  getWorkflowAllNode,
  operationScope,
  updateNodeApprovalTime
} from '../api/index.js';
import { ElMessage as EleMessage} from "element-plus";
import SelectSearch from "../components/search.vue"
import {generateRandomString} from '@/utils/common_bak2.js';
import TableFormItem from "@/components/ProForm/components/table-form-item.vue";
import {useFormData} from "@/utils/use-form-data.js";
import Workflow from "@/views/dingding-flow/components/workflow.vue";
import {getWorkFlow} from "@/views/dingding-flow/api/index.js";
import {mapMutations} from "@/plugins/lib.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentData: Object,
  bindWorkFlowInfo: Object,//已绑定的工作流
  currentId: String,
  RandomString: String,
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const initRandomString = ref('')
/** 提交状态 */
const loading = ref(false);
const workFlow = ref(false);
const workFlowData = ref([]);
const workFlowSelected = ref(null);
const workFlowSelectedInit = ref(null);
const formData = ref(null);
const validMsg = ref(null);
const validMsgCount = ref(0);
const tipList = ref([]);
const tipVisible = ref(false);
/** 新建流程模板 */
const createWorkFlow = () => {
  workFlow.value = true;
  workFlowSelectedInit.value = 'workFlowSelected';
  workFlowSelected.value = null;
};

/** 选择编辑流程模版 */
const workFlowSelectedChange = (event) => {
  if (event) {
    workFlowSelectedInit.value = null;
    workFlow.value = true
  }
};

/** 流程配置 */
const processConfig = ref({});
const handleDoneNodeConfig = (data) => {
  processConfig.value = data
};

const queryWorkFlows = () => {
  getWorkFlow().then((list) => {
    if (list) {
      workFlowData.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 已绑定的工作流 */
const projectWorkFlow = ref(null);

// const queryEvaluateConfigWorkflowById = () => {
//   if (props.currentId) {
//     getEvaluateConfigWorkflowById(props.currentId).then((list) => {
//       if (list) {
//         projectWorkFlow.value = list;
//         workFlowSelected.value = list.id
//         workFlowSelectedChange(true)
//       }
//     }).catch((e) => {
//       EleMessage.error(e.message);
//     });
//   }
// }

const reErr = ({childNode}) => {
  if (childNode) {
    let {type, error, nodeName, conditionNodes} = childNode;
    if (type === 1 || type === 2) {
      if (error) {
        tipList.value.push({
          name: nodeName,
          type: ["", "审核人", "抄送人"][type],
        });
      }
      reErr(childNode);
    } else if (type === 3) {
      reErr(childNode);
    } else if (type === 4) {
      reErr(childNode);
      for (var i = 0; i < conditionNodes.length; i++) {
        if (conditionNodes[i].error) {
          tipList.value.push({name: conditionNodes[i].nodeName, type: "条件"});
        }
        reErr(conditionNodes[i]);
      }
    }
  } else {
    childNode = null;
  }
};
const {setIsTried} = mapMutations()
const saveWorkFlow = async () => {
  setIsTried(true);
  tipList.value = [];
  reErr(processConfig.value.nodeConfig);
  if (tipList.value.length !== 0) {
    tipVisible.value = true;
    return;
  }
  let FlowData = processConfig.value;
  let workflowData = {
    id: workFlowSelected.value ? workFlowSelected.value : workFlowSelectedInit.value,
    name: FlowData.nodeConfig.nodeName,//流程名称
    projectId: props.currentId,//项目ID
    year: '',//年份
    workflowNodes: FlowData.nodeConfig.childNode
  }
  loading.value = true;
  evaluateConfigWorkflowById(props.currentId, workflowData).then((msg) => {
    loading.value = false;
    EleMessage.success("工作流设置" + msg);
    if (msg) updateModelValue(false)
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(
  () => props.RandomString,
  (RandomString) => {
    initRandomString.value = RandomString
    if (RandomString) {
      queryWorkFlows();
      if (props.bindWorkFlowInfo) {
        projectWorkFlow.value = props.bindWorkFlowInfo;
        workFlowSelected.value = props.bindWorkFlowInfo.id
        workFlowSelectedChange(true)
      }
      // queryEvaluateConfigWorkflowById()
    }
  }, {immediate: true})

</script>

<script>
export default {
  NAME: 'WORKFLOWALLNODEINDEX'
};
</script>
