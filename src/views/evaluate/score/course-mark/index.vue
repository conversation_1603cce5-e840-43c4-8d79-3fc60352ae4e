<!--课程标记-->
<template>
  <ele-page
    hide-footer
    flex-table
    :style="{ height: pageHeight + 62 + 'px', overflow: 'auto' }"
  >
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <template #header>
        <search @search="reload" />
      </template>
      <!-- 表格 -->
      <ele-pro-table
        v-if="configId"
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        v-model:selections="selections"
        :show-overflow-tooltip="true"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <div style="display: flex; align-items: center">
            <!-- <el-select
              v-if="setEnterType !== 'config'"
              style="width: 260px; margin-right: 15px"
              size="small"
              v-model="configId"
              placeholder="请选择测评年份"
              @change="selectCpnfChange"
            >
              <el-option
                v-for="item in cpnfOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> -->
            <el-button size="small" @click="openEdit()"> 新建 </el-button>
            <el-button size="small" class="ele-btn-icon" @click="remove()">
              删除
            </el-button>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="currentEvaluateConfigData" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import edit from './components/edit.vue';
  import search from './components/search.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import {
    getEvaluateConfig,
    getEvaluateConfigById
  } from '@/views/evaluate/evaluate-config/api/index.js';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { getCodeData } from '@/views/base-code/dictionary/api/data-index.js';
  import { getCodeDwb } from '@/views/base-code/dwb/api/index.js';
  import {
    getScoreCourseMarkPage,
    removes
  } from '@/views/evaluate/score/course-mark/api/index.js';
  import { ElMessageBox } from 'element-plus';

  const props = defineProps({
    setEnterType: String, //配置页面过来的-config
    setConfigId: String, //当前参数ID
    routerTypeSv: String, //成绩类型-lycj，mycj，tycj
    RandomString: String,
    configRouterType: String //参数对应的type
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { params, path, meta } = unref(currentRoute);
  let pathArray = path.split('/');
  let routeType = pathArray[4];
  console.log(pathArray, routeType);
  const { removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle } =
    usePageTab();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  /** 表格实例 */
  const tableRef = ref(null);
  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'xnxq',
      label: '学年学期'
    },
    {
      prop: 'kch',
      label: '课程号'
    },
    {
      prop: 'kcm',
      label: '课程名'
    },
    {
      prop: 'kcsxmc',
      label: '课程属性名称'
    },
    {
      prop: 'xymc',
      label: '学院名称'
    },
    {
      prop: 'bjmc',
      label: '班级名称'
    },
    {
      prop: 'markType',
      label: '标记类型'
    }
  ]);
  /** 表格选中数据 */
  const selections = ref([]);

  /** 是否显示表单弹窗 */
  const showEdit = ref(false);
  const openEdit = () => {
    showEdit.value = true;
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  const reload = (where) => {
    lastWhere.value = where;
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getScoreCourseMarkPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      configId: configId.value
    });
  };

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  const cpnfOptions = ref([]);
  const configId = ref(null);

  /** 查询 */
  const queryEvaluateConfig = async () => {
    const rData = await getEvaluateConfig({ type: routeType });
    let rArray = [];
    if (rData) {
      let obj = {};
      rData.forEach((d) => {
        obj = {
          value: d.id,
          label: d.title
        };
        rArray.push(obj);
      });
      if (rArray.length > 0) {
        configId.value = props?.setConfigId ?? rArray[0].value;
        if (configId.value) queryEvaluateConfigById(configId.value);
      }
    }
    cpnfOptions.value = rArray;
  };

  /** 提交状态 */
  const loading = ref(false);

  // const queryCodeData = (code, ids) => {
  //   loading.value = true;
  //   let newObj = {
  //     codeType: code,
  //     id: ids,
  //   }
  //   getCodeData(newObj).then((list) => {
  //     userStore.setDicts([], code + '_evaluate');
  //     if (list) {
  //       loading.value = false;
  //       userStore.setDicts(list, code + '_evaluate');
  //     }
  //   }).catch((e) => {
  //     EleMessage.error(e.message);
  //   });
  // };

  // const queryCodeDwb = (ids) => {
  //   userStore.setDicts([], 'dwb_evaluate');
  //   loading.value = true;
  //   let newObj = {
  //     id: ids,
  //   }
  //   getCodeDwb(newObj).then((list) => {
  //     if (list) {
  //       loading.value = false;
  //       userStore.setDicts(list, 'dwb_evaluate');
  //     }
  //   });
  // }

  const currentEvaluateConfigData = ref(null);
  const queryEvaluateConfigById = (configIdValue) => {
    getEvaluateConfigById(configIdValue)
      .then((resData) => {
        if (resData) {
          currentEvaluateConfigData.value = resData;
          // if (resData.pyccid) {
          //   queryCodeData('pycc', resData.pyccid)
          // }
          // if (resData.njid) {
          //   queryCodeData('nj', resData.njid)
          // }
          // if (resData.xyid) {
          //   queryCodeDwb(resData.xyid)
          // }
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.kcm).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const selectCpnfChange = (event) => {
    if (event) {
      configId.value = event;
      reload();
    }
  };

  watch(
    () => props.RandomString,
    (RandomString) => {
      if (RandomString) {
        routeType = props.configRouterType;
        configId.value = props.setConfigId;
      }
    },
    { immediate: true }
  );

  queryEvaluateConfig();
</script>

<script>
  export default {
    name: 'EVALUATESCOREINDEX'
  };
</script>
