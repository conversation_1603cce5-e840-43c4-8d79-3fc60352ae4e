<template>
  <ele-table-select multiple
                    ref="selectTableRef"
                    size="small"
                    clearable
                    placeholder="请选择课程"
                    value-key="kch"
                    label-key="kcm"
                    v-model="selectedValue"
                    :table-props="tableProps"
                    @select="onSelectClick"
                    popper-width="auto">
    <template #topExtra>
      <TableSelectSearch @search="onSearch"/>
    </template>
  </ele-table-select>
</template>

<script setup>
import {ref, reactive} from 'vue';
import {getScoreItemDistinctDataPage} from "@/views/evaluate/score/api/score-item-index.js";
import AdvancedSearch from "@/views/base-code/bjb/components/advanced-search.vue";
import TableSelectSearch from './table-select-search.vue'

const emit = defineEmits(['selectedData']);

const props = defineProps({});
const selectTableRef = ref(null);
/** 表格下拉选中值 */
const selectedValue = ref([]);

/** 表格配置 */
const tableProps = reactive({
  datasource: [],
  columns: [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'xnxq',
      label: '学年学期',
    },
    {
      prop: 'kch',
      label: '课程号',
    },
    {
      prop: 'kcm',
      label: '课程名称',
    },
    {
      prop: 'kcsxdm',
      label: '课程属性代码',
    },
    {
      prop: 'kcsxmc',
      label: '课程属性名称',
    },
  ],
  showOverflowTooltip: true,
  toolbar: false,
  pagination: {
    pageSize: 6,
    layout: 'total, prev, pager, next, jumper',
    style: {padding: '0px'}
  },
  rowClickChecked: true
});

/** 查询表格数据 */
tableProps.datasource = ({page, limit, where, orders}) => {
  return getScoreItemDistinctDataPage({...where, ...orders, page, limit, fieldName: 'xnxq,kch,kcm,kcsxmc,kcsxdm'});
};

/** 搜索 */
const onSearch = (where) => {
  selectTableRef["value"]?.tableRef?.reload?.({where, page: 1});
};

const selectArrayData = ref([]);
/* 当用户手动勾选数据行的 Checkbox 时触发的事件	 */
const onSelectClick = (item) => {
  selectArrayData.value = item;
  emit('selectedData', selectArrayData.value)
};
</script>
