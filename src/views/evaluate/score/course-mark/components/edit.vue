<!-- 课程标记 -->
<template>
  <ele-drawer
    size="61%"
    :title="'课程标记设置'"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="formData"
      size="small"
      :rules="rules"
      label-position="top"
      @submit.prevent=""
    >
      <el-form-item label="院系">
        <dict-data
          placeholder="请选择院系"
          code="xymc"
          type="multipleSelect"
          :model-value="formData['xyid']"
          @update:modelValue="(value) => updateValue('xyid', value)"
        />
      </el-form-item>
      <el-form-item label="班级">
        <dict-data
          placeholder="请选择班级"
          code="bjmc"
          :refresh="formData.xyid"
          :dic-query-params="{ params: { xyid: formData.xyid } }"
          type="multipleSelect"
          :model-value="formData['bjid']"
          @update:modelValue="(value) => updateValue('bjid', value)"
        />
      </el-form-item>
      <el-form-item label="课程" prop="selectDataIds">
        <KcMultiple
          v-model="formData.selectDataIds"
          @selectedData="handleSelectedData"
        />
      </el-form-item>
      <el-form-item label="标记类型" prop="markType">
        <el-radio-group v-model="formData['markType']">
          <el-radio
            v-for="item in markTypeOptions"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)" size="small" plain
        >取消</el-button
      >
      <el-button
        size="small"
        plain
        type="primary"
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { EleMessage, emailReg } from 'ele-admin-plus/es';
  import { useUserStore } from '@/store/modules/user.js';
  import { operation } from '../api/index.js';
  import KcMultiple from './kc-multiple.vue';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  // 已缓存的字典
  const userStore = useUserStore();
  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const formData = ref({
    xyid: null,
    markType: null,
    selectData: [],
    selectDataIds: []
  });
  /** 表单验证规则 */
  const rules = reactive({
    selectDataIds: [
      {
        required: true,
        message: '请选择课程',
        type: 'array',
        trigger: 'change'
      }
    ],
    markType: [
      {
        required: true,
        message: '请选择标记类型',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const handleSelectedData = (data) => {
    formData.value['selectData'] = data;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operation(formData.value)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新值 */
  const updateValue = (prop, value) => {
    formData.value[prop] = value;
  };

  const markTypeOptions = ref([
    {
      id: 'CanYuJiSuan',
      name: '参与计算'
    },
    {
      id: 'BuCanYuJiSuan',
      name: '不参与计算'
    }
  ]);

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        isUpdate.value = !!props.data;
      } else {
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
