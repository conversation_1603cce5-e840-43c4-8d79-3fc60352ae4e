/**
 * 标记课程
 */
import request from '@/utils/request.js';

/**
 * 分页查询
 */
export async function getScoreCourseMarkPage(params) {
  const res = await request.get('/score/course-mark/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部
 * @param params
 * @returns {Promise<*>}
 */
export async function getScoreCourseMark(params) {
  const res = await request.get('/score/course-mark', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getScoreCourseMarkById(id) {
  const res = await request.get('/score/course-mark/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
 */
export async function operation(data) {
  const res = await request.post('/score/course-mark/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/score/course-mark/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
