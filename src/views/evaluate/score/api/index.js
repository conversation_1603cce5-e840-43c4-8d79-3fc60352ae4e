/**
 * 劳育成绩
 */
import request from '@/utils/request.js';

/**
 * 公共导入
 * @param importUrl --导入接口
 * @param file      --导入文件
 * @returns {Promise<any>}
 */
export async function importDataComplete(importUrl,file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post(importUrl, formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询
 */
export async function getScore(url,params) {
  const res = await request.get(url, {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getScoreById(url) {
  const res = await request.get(url);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
