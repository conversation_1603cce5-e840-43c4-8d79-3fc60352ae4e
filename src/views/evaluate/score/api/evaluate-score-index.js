/**
 * 综合测评成绩
 */
import request from '@/utils/request.js';

/**
 * 分页查询综合测评成绩（权限标识：evaluate:evaluateScore:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function getEvaluateScorePage(params) {
  const res = await request.get('/evaluate/evaluate-score/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部
 * @param params
 * @returns {Promise<*>}
 */
export async function getEvaluateScore(params) {
  const res = await request.get('/evaluate/evaluate-score', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getEvaluateScoreById(id) {
  const res = await request.get('/evaluate/evaluate-score/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 综合测评成绩计算（权限标识：evaluate:evaluateScore:operation）
 */
export async function operation(data) {
  const res = await request.post('/evaluate/evaluate-score/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/evaluate/evaluate-score/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
