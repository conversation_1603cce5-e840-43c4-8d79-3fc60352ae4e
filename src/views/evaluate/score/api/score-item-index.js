/**
 * 学生各科课程成绩
 */
import request from '@/utils/request.js';

/**
 * 分页查询
 */
export async function getScoreItemPage(params) {
  const res = await request.get('/score/course-score-item/page', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据字段名distinct获取数据
 * @param params fieldName=kcsxmc
 * @returns {Promise<*>}
 */
export async function getScoreItemDistinctDataPage(params) {
  const res = await request.get('/score/course-score-item/pageDistinctData', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部
 * @param params
 * @returns {Promise<*>}
 */
export async function getScoreItem(params) {
  const res = await request.get('/score/course-score-item', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getScoreItemById(id) {
  const res = await request.get('/score/course-score-item/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
 */
export async function operation(data) {
  const res = await request.post('/score/course-score-item/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/score/course-score-item/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
