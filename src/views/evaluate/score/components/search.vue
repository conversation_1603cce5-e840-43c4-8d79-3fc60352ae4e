<!-- 搜索表单 -->
<template>
  <ele-card :style="{marginBottom:'0px!important'}"
            :body-style="{ padding: '6px 0 0px 0px !important' }">
    <el-form size="small" @keyup.enter="search" @submit.prevent=""
             label-width="auto">
      <el-row :gutter="8">
        <search-fields v-model:form="form" :searchExpand="searchExpand" setSelfCount="0"/>
        <el-col :lg="6" :md="12" :sm="12" :xs="12">
          <el-form-item label-width="60px" label=" ">
            <el-button type="primary" size="small" plain @click="search">查询</el-button>
            <el-button @click="reset" size="small">重置</el-button>

            <el-link
              type="primary"
              underline="never"
              @click="toggleExpand"
              style="margin-left: 12px"
            >
              <template v-if="searchExpand">
                <span>收起</span>
                <el-icon style="vertical-align: -1px">
                  <ArrowUp/>
                </el-icon>
              </template>
              <template v-else>
                <span>展开</span>
                <el-icon style="vertical-align: -2px">
                  <ArrowDown/>
                </el-icon>
              </template>
            </el-link>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data';
import {ref,} from "vue";
import searchFields from "@/components/SearchFields/index.vue"

const emit = defineEmits(['search']);

/** 表单数据 */
const [form, resetFields] = useFormData({});

/** 搜索 */
const search = () => {
  emit('search', {...form});
};

/** 搜索表单是否展开 */
const searchExpand = ref(false);

/** 搜索展开/收起 */
const toggleExpand = () => {
  searchExpand.value = !searchExpand.value;
};
/** 更新值 */
const updateValue = (prop, value) => {
  if (prop === 'xyid') {
    form.zyid = null
    form.bjid = null
  }
  if (prop === 'zyid') {
    form.bjid = null
  }
  form[prop] = value
}

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
</script>
