/**
 * 课程属性占比配置
 */
import request from '@/utils/request.js';

/**
 * 分页查询
 */
export async function getScoreCoursePropertiesPercentPage(params) {
  const res = await request.get('/score/course-properties-percent/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部
 * @param params
 * @returns {Promise<*>}
 */
export async function getScoreCoursePropertiesPercent(params) {
  const res = await request.get('/score/course-properties-percent', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getScoreCoursePropertiesPercentById(id) {
  const res = await request.get('/score/course-properties-percent/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
 */
export async function operationCoursePropertiesPercent(data) {
  const res = await request.post('/score/course-properties-percent/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removesCoursePropertiesPercent(data) {
  const res = await request.post('/score/course-properties-percent/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
