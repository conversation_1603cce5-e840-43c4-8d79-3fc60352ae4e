<!-- 课程属性占比配置 -->
<template>
  <ele-modal form
             :width="640"
             v-model="visible"
             center
             title="课程属性占比配置"
             @open="handleOpen">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             label-width="80px"
             @submit.prevent="">
      <el-form-item label="学年">
        <el-input clearable
                  readonly
                  :maxlength="20"
                  :model-value="form[item.prop]"/>
      </el-form-item>
      <el-form-item label="培养层次">
        <el-checkbox-group v-model="form['pyccidArray']" @change="onchangePycc">
          <el-checkbox v-for="item in pyccidOptions" :key="item.id" :value="item.id" :label="item.name">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="年级">
        <el-checkbox-group v-model="form['njidArray']" @change="onchangeNj">
          <el-checkbox v-for="item in njidOptions" :key="item.id" :value="item.id" :label="item.name">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="院系">
        <!--        <el-select v-model="form['xyidArray']" filterable @change="onchangeXy"-->
        <!--                   multiple-->
        <!--                   placeholder="请选择院系">-->
        <!--          <el-option v-for="item in xyidOptions"-->
        <!--                     :key="item.id"-->
        <!--                     :label="item.name"-->
        <!--                     :value="item.id"/>-->
        <!--        </el-select>-->
        <el-checkbox-group v-model="form['xyidArray']" @change="onchangeXy">
          <el-checkbox v-for="item in xyidOptions" :key="item.id" :value="item.id" :label="item.name">
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel" size="small" plain>取消</el-button>
      <el-button size="small" plain type="primary" :loading="loading" @click="save">
        开始计算
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import {ref, reactive, nextTick} from 'vue';
import {EleMessage, emailReg,} from 'ele-admin-plus/es';
import {useFormData} from '@/utils/use-form-data';
import {addUser, updateUser,} from '@/api/system/user';
import {storeToRefs} from "pinia";
import {useUserStore} from "@/store/modules/user.js";
import {operation} from "@/views/evaluate/score/api/evaluate-score-index.js";

const props = defineProps({
  /** 修改回显的数据 */
  data: Object,
});

// 已缓存的字典
const userStore = useUserStore();
const emit = defineEmits(['done']);

/** 弹窗是否打开 */
const visible = defineModel({type: Boolean});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  pyccidArray: [],
  njidArray: [],
  xyidArray: [],
  pyccid: null,
  njid: null,
  xyid: null,
});

/** 表单验证规则 */
const rules = reactive({
  roles: [
    {
      required: true,
      message: '请选择角色',
      type: 'array',
      trigger: 'change'
    }
  ],
  email: [
    {
      pattern: emailReg,
      message: '邮箱格式不正确',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 关闭弹窗 */
const handleCancel = () => {
  visible.value = false;
};

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    console.log("form===", JSON.stringify(form))
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      handleCancel();
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

const onchangePycc = (item) => {
  form['pyccid'] = item.join()
};

const onchangeNj = (item) => {
  form['njid'] = item.join()
};

const onchangeXy = (e) => {
  form.xyid = e.join()
}

const pyccidOptions = ref([])
const njidOptions = ref([])
const xyidOptions = ref([])
/** 弹窗打开事件 */
const handleOpen = () => {

  const {dicts} = storeToRefs(userStore);
  console.log("已缓存的字典======", dicts.value)
  let pyccData = dicts.value['pycc_evaluate'];
  let njData = dicts.value['nj_evaluate'];
  let xyData = dicts.value['dwb_evaluate'];
  pyccidOptions.value = pyccData;
  njidOptions.value = njData;
  xyidOptions.value = xyData;
  // if (props.data) {
  //   assignFields({
  //     // pyccidArray: props.data.pyccid ? props.data.pyccid.split(',') : [],
  //     // njidArray: props.data.njid ? props.data.njid.split(',') : [],
  //     // xyidArray: props.data.xyid ? props.data.xyid.split(',') : [],
  //     // zyidArray: props.data.zyid ? props.data.zyid.split(',') : [],
  //     // ...props.data,
  //   });
  //   // assignFields({...props.data, password: ''});
  //   isUpdate.value = true;
  // } else {
  resetFields();
  //   isUpdate.value = false;
  // }
  nextTick(() => {
    nextTick(() => {
      formRef.value?.clearValidate?.();
    });
  });
};
</script>
