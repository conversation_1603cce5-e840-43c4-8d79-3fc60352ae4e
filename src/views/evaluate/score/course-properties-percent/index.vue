<!--课程属性占比配置-->
<template>
  <ele-page
    hide-footer
    flex-table
    :style="{ height: pageHeight + 62 + 'px', overflow: 'auto' }"
  >
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- <template #header>
        <div style="display: flex; align-items: center">
          <el-select v-if="setEnterType!=='config'"
                     style="width: 260px; margin-left: 5px;"
                     size="small" v-model="configId" placeholder="请选择测评年份"
                     @change="selectCpnfChange">
            <el-option v-for="item in cpnfOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"/>
          </el-select>
        </div>
      </template> -->
      <TablePreview
        v-if="configId && currentRandomString"
        :ref="(el) => getRiskSpreadRef(el, 0)"
        dataName=""
        :currentProject="currentEvaluateConfigData"
        :approverId="configId"
        :RandomString="currentRandomString"
        approverType="coursePropertiesPercent"
      />
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import {
    getEvaluateConfig,
    getEvaluateConfigById
  } from '@/views/evaluate/evaluate-config/api/index.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
  import { generateRandomString } from '@/utils/common_bak2.js';

  const props = defineProps({
    setEnterType: String, //配置页面过来的-config
    setConfigId: String, //当前参数ID
    RandomString: String,
    configRouterType: String //参数对应的type
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { params, path, meta } = unref(currentRoute);
  let pathArray = path.split('/');
  let routeType = pathArray[4];
  console.log(pathArray, routeType);
  const { removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle } =
    usePageTab();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  /** 表格实例 */
  const tableRef = ref(null);
  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xn',
      label: '学年'
    },
    {
      prop: 'kch',
      label: '课程号'
    },
    {
      prop: 'kcsxdm',
      label: '课程属性代码'
    },
    {
      prop: 'kcsxmc',
      label: '课程属性名称'
    },
    {
      prop: 'percent',
      label: '百分比'
    }
  ]);

  const cpnfOptions = ref([]);
  const configId = ref(null);

  /** 查询 */
  const queryEvaluateConfig = async () => {
    const rData = await getEvaluateConfig({ type: routeType });
    let rArray = [];
    if (rData) {
      let obj = {};
      rData.forEach((d) => {
        obj = {
          value: d.id,
          label: d.title
        };
        rArray.push(obj);
      });
      if (rArray.length > 0) {
        configId.value = props?.setConfigId ?? rArray[0].value;
        if (configId.value) queryEvaluateConfigById(configId.value);
      }
    }
    cpnfOptions.value = rArray;
    currentRandomString.value = generateRandomString(10);
  };

  /** 提交状态 */
  const loading = ref(false);

  const riskSpreadRefList = ref([]);
  const getRiskSpreadRef = (el, index) => {
    if (el) {
      riskSpreadRefList.value[index] = el;
    }
  };

  const currentEvaluateConfigData = ref(null);
  const queryEvaluateConfigById = (configIdValue) => {
    getEvaluateConfigById(configIdValue)
      .then((resData) => {
        if (resData) {
          currentEvaluateConfigData.value = resData;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  let currentRandomString = ref('');
  const selectCpnfChange = (event) => {
    if (event) {
      configId.value = event;
      currentRandomString.value = generateRandomString(10);
      // reload()
    }
  };

  watch(
    () => props.RandomString,
    (RandomString) => {
      if (RandomString) {
        currentRandomString.value = RandomString;
        routeType = props.configRouterType;
        configId.value = props.setConfigId;
      }
    },
    { immediate: true }
  );

  queryEvaluateConfig();
</script>

<script>
  export default {
    name: 'EVALUATESCOREINDEX'
  };
</script>
