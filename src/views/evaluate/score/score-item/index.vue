<!--学生每科课程成绩-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :header-style="{fontWeight:'unset!important' }"
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <search @search="reload"/>
      </template>
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     v-model:selections="selections"
                     :show-overflow-tooltip="true"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <!--          <el-button class="ele-btn-icon" size="small"-->
          <!--                     @click="openEdit">-->
          <!--            添加-->
          <!--          </el-button>-->
          <el-button class="ele-btn-icon" size="small"
                     @click="remove()"> 删除
          </el-button>
          <!--          <el-button size="small"-->
          <!--                     @click="">-->
          <!--            导出-->
          <!--          </el-button>-->
        </template>
        <template #kcm="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.kcm }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit" :data="current" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {getScoreItemPage, removes} from '../api/score-item-index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {getToken,} from '@/utils/token-util';
import {getCurrentRole,} from '@/utils/current-role-util';
import Edit from './components/edit.vue'
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from "element-plus";
import Search from "./components/search.vue";

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
let pathArray = path.split("/")
console.log(pathArray)
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const props = defineProps({});

const showEdit = ref(false);
const current = ref(null);

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 表格实例 */
const tableRef = ref(null);
/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xnxq',
      label: '学年学期',
    },
    {
      prop: 'xn',
      label: '学年',
    },
    {
      prop: 'xgh',
      label: '学号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'xymc',
      label: '所属院系',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
    {
      prop: 'njmc',
      label: '年级名称',
    },
    {
      prop: 'kch',
      label: '课程号',
    },
    {
      prop: 'kcm',
      slot: 'kcm',
      label: '课程名',
    },
    {
      prop: 'kccj',
      label: '课程成绩',
    },
    {
      prop: 'xf',
      label: '学分',
    },
    {
      prop: 'kcsxmc',
      label: '课程属性名称',
    },
    {
      prop: 'kslxmc',
      label: '考试类型名称',
    },
    {
      prop: 'xdfsmc',
      label: '修读方式名称',
    },
    {
      prop: 'bkfsmc',
      label: '补考方式名称',
    },
    {
      prop: 'cjbs',
      label: '成绩标识',
    },
    {
      prop: 'wtgyysm',
      label: '未通过原因说明',
    },
    // ...comTableCloumns()
  ]
);

/** 表格选中数据 */
const selections = ref([]);

/** 表格搜索参数 */
const lastWhere = reactive({});
const reload = (where) => {
  lastWhere.value = where
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getScoreItemPage({
    ...where, ...orders, ...filters,
    page,
    limit,
  });
};

/** 删除单个 */
const remove = (row) => {
  console.log("删除单个===", row)
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.kcm).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'SCOREITEMINDEX'
};
</script>

