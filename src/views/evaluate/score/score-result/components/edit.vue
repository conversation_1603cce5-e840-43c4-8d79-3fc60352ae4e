<template>
  <ele-drawer size="40%"
              title="编辑成绩"
              :append-to-body="true"
              style="max-width: 100%;"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             labelWidth="auto">
      <el-row :gutter="4">
        <template v-for="item in baseFileds">
          <el-col :span="12" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
            <ProFormItem :item="item"
                         :model="form"
                         @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
              <template
                v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                #[name]="slotProps">
                <slot :name="name" v-bind="slotProps || {}"></slot>
              </template>
            </ProFormItem>
          </el-col>

          <el-col :span="12" v-else class="border-only-bottom">
            <el-form-item :label="item.label">
              {{ form[item.prop] }}
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)">取消
      </el-button>
      <el-button v-if="enterType !== 'preview'" plain size="small" type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {nextTick, ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {operation,} from "../../api/score-item-index.js";
import { ElMessage as EleMessage} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  enterType: String,//perview：考试详情预览 add:添加考试 edit:编辑考试
  /** 添加时机构id */
  organizationId: String,
  routeType: String,
  currentEvaluateConfigData: Object,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
});

const updateFormValue = (item, prop, newVal) => {
  form[item.prop] = newVal;
}

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 提交 */
const onSubmit = () => {
  console.log('form:', JSON.stringify(form));
  formRef["value"]?.validate?.((valid, obj) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {
      ...form,
    };
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const baseFileds = ref([])
const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);


watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      baseFileds.value = [
        {
          prop: 'xgh',
          label: '学工号',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        {
          prop: 'xm',
          label: '姓名',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        {
          prop: 'xymc',
          label: '所属院系',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        {
          prop: 'zymc',
          label: '专业名称',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        // {
        //   prop: 'kcm',
        //   label: '班级号',
        //   type: 'input',
        //   required: false,
        //   showFlag: '是',
        //   selfModifyFlag: '是',
        // },
        {
          prop: 'bjmc',
          label: '班级名称',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        {
          prop: 'njmc',
          label: '年级名称',
          type: 'input',
          required: false,
          showFlag: '否',
          selfModifyFlag: '否',
        },
        {
          prop: 'kccj',
          label: '课程成绩',
          type: 'inputNumber',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kch',
          label: '课程号',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kcm',
          label: '课程名',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kcsxdm',
          label: '课程属性代码',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kcsxmc',
          label: '课程属性名称',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xs',
          label: '学时',
          type: 'inputNumber',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xf',
          label: '学分',
          type: 'inputNumber',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xnxq',
          label: '学年学期',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xn',
          label: '学年',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kxh',
          label: '课序号',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xnxq',
          label: '等级成绩111',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'jdcj',
          label: '绩点成绩',
          type: 'inputNumber',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kslxdm',
          label: '考试类型代码',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kslxmc',
          label: '考试类型名称',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xdfsdm',
          label: '修读方式代码',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'xdfsmc',
          label: '修读方式名称',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'wtgyydm',
          label: '未通过愿意代码',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'wtgyysm',
          label: '未通过愿意说明',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'bkfsdm',
          label: '补考方式代码',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'bkfsmc',
          label: '补考方式名称',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'kssj',
          label: '考试时间',
          type: 'datetime',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        {
          prop: 'fl',
          label: '分类',
          type: 'input',
          required: false,
          showFlag: '是',
          selfModifyFlag: '是',
        },
        // {
        //   prop: 'minScore', label: '最小值', type: 'inputNumber',
        //   required: true,
        //   showFlag: '是',
        //   selfModifyFlag: '是',
        // },
        // {
        //   prop: 'maxScore', label: '最大值', type: 'inputNumber',
        //   required: true,
        //   showFlag: '是',
        //   selfModifyFlag: '是',
        // },
      ]
      baseFileds.value.forEach((item) => {
        form[item.prop] = ""
      })
      console.log(props.data)
      if (props.data) {
        assignFields({
          ...props.data,
        });
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  },
);

</script>
