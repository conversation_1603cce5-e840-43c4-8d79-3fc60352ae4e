/**
 * 课程成绩结果
 */
import request from '@/utils/request.js';

/**
 * 导入
 */
export async function importData(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post('/score/course-score-result/importData', formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function getScoreResultPage(params) {
  const res = await request.get('/score/course-score-result/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部
 * @param params
 * @returns {Promise<*>}
 */
export async function getScoreResult(params) {
  const res = await request.get('/score/course-score-result', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据ID查询
 */
export async function getById(id) {
  const res = await request.get('/score/course-score-result/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改综测指标成绩来源（权限标识：code:scoreSourceConfig:operation）
 */
export async function operation(data) {
  const res = await request.post('/score/course-score-result/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/score/course-score-result/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
