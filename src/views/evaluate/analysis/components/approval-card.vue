<template>
  <ele-card header="待审批信息" :header-style="{ paddingTop: 0, paddingBottom: 0 }"
            :body-style="{ padding: '3px 8px 0 8px!important' }">
    <template #extra>
      <IconPark name="all-application" size="20" strokeWidth="3"/>
      <ele-text style="cursor: pointer;">更多</ele-text>
    </template>
    <ele-check-card :items="cardOptData2" :row="{ gutter: 6 }" style="margin: 3px;">
      <template #item="{ item }">
        <div style="flex: 1; padding: 10px;min-height: 137px">
          <div style="display: flex; align-items: center">
            <ele-text size="md" style="font-size: 13px !important;flex: 1">{{ item.title }}</ele-text>
            <ele-text type="secondary">
              {{ item.datetime }}
            </ele-text>
          </div>
          <div style="margin-top: 8px; display: flex; align-items: center">
            <el-tag size="small" :disable-transitions="true"> {{ item.btnName }}</el-tag>
          </div>
          <div style="margin-top: 8px">
            <ele-ellipsis size="sm" type="secondary" :max-line="2"
                          style="height:36px;">
              {{ item?.content }}
            </ele-ellipsis>
          </div>
          <div style="margin-top: 8px; display: flex; align-items: center">
            <el-button class="ele-btn-icon"
                       plain
                       style="width: 100%"
                       underline="never">
              &nbsp;查看详情
            </el-button>
            <el-button class="ele-btn-icon"
                       plain
                       style="width: 100%"
                       underline="never">
              &nbsp;审核
            </el-button>
          </div>
        </div>
      </template>
    </ele-check-card>
  </ele-card>
</template>

<script setup>
import {computed} from 'vue';
import IconPark from "@/components/IconPark/index.vue";

const props = defineProps({});

let cardOptData2 = computed(() => {
  let baseData = [
    {
      id: 6,
      title: '2023学年综合测评',
      content: '配置测评审核流程，确保流程规范化。',
      btnName: '待审批',
      datetime: '2025-01-10',
      icon: 'setting-two',
      isDisplay: true,
      onClick: () => {
      }
    },
    {
      id: 3,
      title: '2024学年综合测评',
      content: '定义测评的量化指标，管理员可自定义测评指标维度与评分权重标准',
      btnName: '指标设置',
      datetime: '2025-01-10',
      icon: 'file-settings',
      isDisplay: true,
      onClick: () => {
      }
    },
    {
      id: 2,
      title: '2025学年综合测评',
      content: '限定测评的适用范围，支持精准设置筛选的目标群体',
      btnName: '范围设置',
      datetime: '2025-01-10',
      icon: 'setting-config',
      isDisplay: true,
      onClick: () => {
      }
    },
    // {
    //   id: 2,
    //   title: '2025学年综合测评',
    //   content: '限定测评的适用范围，支持精准设置筛选的目标群体，支持精准设置筛选的目标群体',
    //   btnName: '范围设置',
    //   datetime: '2025-01-10',
    //   icon: 'setting-config',
    //   isDisplay: true,
    //   onClick: () => {
    //   }
    // },
  ]

  return baseData.map((d) => {
    return {
      ...d,
      col: {md: 8, sm: 12, xs: 24, style: {marginBottom: '6px',}}
    };
  });
})
</script>
