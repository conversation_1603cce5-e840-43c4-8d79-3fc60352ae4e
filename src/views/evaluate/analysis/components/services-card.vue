<template>
  <ele-card header="快捷操作区" :header-style="{ paddingTop: 0, paddingBottom: 0 }"
            :body-style="{ padding: '3px 8px 0 8px!important' }">
    <ele-check-card v-if="cardOptData.length>0" :items="cardOptData" :row="{ gutter: 6 }" style="margin: 3px;">
      <template #item="{ item }">
        <div style="display: flex; padding: 10px; min-height: 42px"
             @click="item.onClick">
          <div style="flex: 1;  min-width: 0;">
            <ele-text size="md"
                      style="font-size: 13px !important">{{ item?.title }}</ele-text>
            <ele-ellipsis size="sm" type="secondary"
                          :max-line="1"
                          :tooltip="{ effect: 'light', offset:6, placement:'right-start',
                          bodyStyle: { maxWidth: '210px', minHeight: '25px' ,marginTop: '10px'} }">
              {{ item.content }}
            </ele-ellipsis>
          </div>
          <IconPark :name="item.icon" size="20" strokeWidth="3"/>
        </div>
      </template>
    </ele-check-card>
  </ele-card>
</template>

<script setup>
import {computed, unref,} from 'vue';
import IconPark from "@/components/IconPark/index.vue";
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const props = defineProps({});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push, replace} = useRouter();
const {params, path, query,} = unref(currentRoute);
let pathArray = path.split("/");
console.log(pathArray)
let routeType = pathArray[3]

let cardOptData = computed(() => {
  let baseData = [
    {
      id: 6,
      title: '参数配置',
      content: '配置测评审核流程，确保流程规范化。',
      btnName: '工作流设置',
      icon: 'setting-two',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/evaluate-config/' + routeType);
      }
    },
    {
      id: 3,
      title: '指标成绩来源配置',
      content: '定义测评的量化指标，管理员可自定义测评指标维度与评分权重标准',
      btnName: '指标设置',
      icon: 'file-settings',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/source-config/' + routeType);
      }
    },
    {
      id: 2,
      title: '指标加减分参考项设置',
      content: '限定测评的适用范围，支持精准设置筛选的目标群体',
      btnName: '范围设置',
      icon: 'setting-config',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/application-reference-info/' + routeType);
      }
    },
    {
      id: 5,
      title: '历史数据',
      content: '自定义设置测评审核时间',
      btnName: '审核时间设置',
      icon: 'time',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/history/' + routeType);
      }
    },
    {
      id: 3,
      title: '指标设置',
      content: '定义测评的量化指标，管理员可自定义测评指标维度与评分权重标准',
      btnName: '指标设置',
      icon: 'list-top',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/item/' + routeType);
      }
    },
    {
      id: 1,
      title: '劳育成绩',
      content: '快速查看劳育成绩',
      btnName: '编辑',
      icon: 'five-star-badge',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/' + routeType + '/lycj');
      }
    },
    {
      id: 1,
      title: '美育成绩',
      content: '快速查看美育成绩',
      btnName: '编辑',
      icon: 'four-leaves',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/' + routeType + '/mycj');
      }
    },
    {
      id: 1,
      title: '体育成绩',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'soccer',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/' + routeType + '/tycj');
      }
    },
    {
      id: 1,
      title: '课程成绩',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'schedule',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/score-item/' + routeType);
      }
    },
    {
      id: 1,
      title: '课程成绩结果',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'analysis',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/score-result/' + routeType);
      }
    },
    {
      id: 1,
      title: '成绩计算及排名',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'arithmetic',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/evaluate-score/' + routeType);
      }
    },
    {
      id: 1,
      title: '课程标记',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'mark',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/course-mark/' + routeType);
      }
    },
    {
      id: 1,
      title: '课程属性占比设置',
      content: '快速查看体育成绩',
      btnName: '编辑',
      icon: 'percentage',
      isDisplay: true,
      onClick: () => {
        push('/evaluate/score/course-properties-percent/' + routeType);
      }
    },
  ]

  return baseData.map((d) => {
    return {
      ...d,
      col: {md: 4, sm: 12, xs: 24, style: {marginBottom: '6px',}}
    };
  });
})
</script>
