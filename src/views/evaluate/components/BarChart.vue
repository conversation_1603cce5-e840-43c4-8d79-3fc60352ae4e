<template>
  <canvas ref="chartCanvas"></canvas>
</template>

<script>
import { Chart, registerables } from "chart.js";
Chart.register(...registerables);

export default {
  props: {
    data: {
      type: Array,
      required: true
    }
  },
  mounted() {
    new Chart(this.$refs.chartCanvas, {
      type: "bar",
      data: {
        labels: this.data.map(item => item.label),
        datasets: [{
          label: "人数",
          data: this.data.map(item => item.value),
          backgroundColor: "#007bff"
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true }
        }
      }
    });
  }
};
</script>
