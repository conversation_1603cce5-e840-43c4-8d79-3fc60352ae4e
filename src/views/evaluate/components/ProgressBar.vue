<template>
  <div class="progress-bar">
    <div class="progress" :style="{ width: value + '%' }"></div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      required: true
    }
  }
};
</script>

<style scoped>
.progress-bar {
  width: 100%;
  height: 10px;
  background: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}
.progress {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease-in-out;
}
</style>
