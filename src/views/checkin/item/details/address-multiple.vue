<template>
  <div class="address-multiple-container">
    <!-- 地址库选择 -->
    <div class="address-library-section">
      <ele-table-select
        multiple
        size="small"
        clearable
        placeholder="从地址库选择"
        value-key="id"
        label-key="name"
        v-model="selectedAddressIds"
        :table-props="tableProps"
        :cache-data="cachedAddressData"
        :popper-width="580"
      />
    </div>
    <!-- 地图添加的地址 -->
    <div v-if="mapAddedAddresses.length > 0" class="map-addresses-section">
      <div class="section-title">手动从地图添加的位置</div>
      <div
        v-for="(addr, index) in mapAddedAddresses"
        :key="addr.id"
        class="map-address-item"
        :class="{
          'is-poi': addr.type === 'poi',
          'is-polygon': addr.type === 'Polygon',
          'is-rectangle': addr.type === 'Rectangle'
        }"
      >
        <div class="address-info">
          <el-icon class="address-icon">
            <Location />
          </el-icon>
          <div class="address-details">
            <div class="address-name">{{ addr.name }}</div>
            <div class="address-type">{{ formatAddressType(addr.type) }}</div>
            <div v-if="addr.type === 'poi'" class="address-radius">
              半径: {{ addr.fwbj }}米
            </div>
          </div>
        </div>
        <el-button
          @click="removeMapAddress(index)"
          size="small"
          type="danger"
          text
          circle
        >
          <el-icon>
            <Close />
          </el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, reactive } from 'vue';
  import { Location, Close } from '@element-plus/icons-vue';
  import { getCheckinAddress } from '../../address/api/index.js';

  const props = defineProps({
    cacheDataQdfw: Array,
    modelValue: Array
  });

  const emit = defineEmits(['update:modelValue', 'update:isAddAddress']);

  // 从地址库选择的ID
  const selectedAddressIds = ref([]);

  // 从地图添加的地址
  const mapAddedAddresses = ref([]);

  // 地址库表格配置
  const tableProps = reactive({
    datasource: [],
    columns: [
      {
        type: 'selection',
        width: 45,
        fixed: 'left',
        reserveSelection: true
      },
      {
        prop: 'name',
        label: '签到位置',
        width: 190
      },
      {
        prop: 'type',
        label: '类型',
        formatter: (row) => {
          const types = {
            poi: '圆形',
            Polygon: '多边形',
            Rectangle: '矩形'
          };
          return types[row.type] || row.type;
        }
      },
      {
        prop: 'fwbj',
        label: '范围半径(米)',
        sortable: true
      }
    ],
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper'
    }
  });

  // 缓存数据（用于回显）
  const cachedAddressData = ref([]);

  // 合并后的地址数据
  const mergedAddresses = computed(() => {
    const fromLib = tableProps.datasource.filter((item) =>
      selectedAddressIds.value.includes(item.id)
    );
    return [...fromLib, ...mapAddedAddresses.value];
  });

  // 初始化加载地址库数据
  const loadAddressData = async () => {
    try {
      let datasource = await getCheckinAddress();
      if (datasource.length > 0)
        datasource.forEach((item) => {
          item.isFromMap = '是';
        });
      tableProps.datasource = datasource;
      emit('update:isAddAddress', false);
    } catch (e) {
      console.error('加载地址数据失败:', e);
    }
  };

  // 处理props变化
  watch(
    () => props.cacheDataQdfw,
    (val) => {
      if (val) {
        // 区分已有地址和新地址
        const fromLib = val.filter((item) => item.isFromMap === '是');
        const fromMap = val.filter((item) => item.isFromMap === '否');

        selectedAddressIds.value = fromLib.map((item) => item.addressId);
        mapAddedAddresses.value = fromMap;
        cachedAddressData.value = fromLib;
      }
    },
    { immediate: true }
  );

  // 添加从地图来的地址
  const addMapAddress = async (address) => {
    if (address.isFromMap === '是') {
      //刷新地址库数据，并选中新加的地址信息
      cachedAddressData.value.push(address);
      selectedAddressIds.value = cachedAddressData.value.map(
        (item) => item.addressId
      );

      // 重新加载地址库（可选，如果你想刷新最新地址列表）
      await loadAddressData();
      // 判断 cachedAddressData 中是否已存在该地址
      const exists = cachedAddressData.value.some(
        (item) => item.id === address.id
      );
      if (!exists) {
        cachedAddressData.value.push(address);
      }

      // 同步 selectedAddressIds，确保选中
      if (!selectedAddressIds.value.includes(address.id)) {
        selectedAddressIds.value.push(address.id);
      }
    } else {
      mapAddedAddresses.value.push(address);
    }
    emit('update:isAddAddress', true);
  };

  // 移除地图地址
  const removeMapAddress = (index) => {
    mapAddedAddresses.value.splice(index, 1);
    if (mapAddedAddresses.value.length === 0) {
      emit('update:isAddAddress', false);
    }
  };

  // 格式化地址类型显示
  const formatAddressType = (type) => {
    const types = {
      poi: '圆形范围',
      Polygon: '多边形区域',
      Rectangle: '矩形区域',
      Circle: '圆形区域'
    };
    return types[type] || type;
  };

  // 监听内部变化更新v-model
  watch(
    [selectedAddressIds, mapAddedAddresses],
    () => {
      emit('update:modelValue', mergedAddresses.value);
    },
    { deep: true }
  );

  // 初始化加载数据
  loadAddressData();

  // 暴露方法
  defineExpose({
    addMapAddress
  });
</script>

<style scoped>
  .address-multiple-container {
    width: 100%;
  }

  .address-library-section {
    margin-bottom: 16px;
  }

  .map-addresses-section {
    margin-top: 0;
    border-top: 1px solid #eee;
    padding-top: 3px;
  }

  .section-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
  }

  .map-address-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    transition: all 0.3s;
  }

  .map-address-item:hover {
    background-color: #f0f0f0;
  }

  .address-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
  }

  .address-icon {
    margin-right: 10px;
    font-size: 18px;
  }

  .address-details {
    flex-grow: 1;
  }

  .address-name {
    font-weight: 500;
    margin-bottom: 2px;
  }

  .address-type,
  .address-radius {
    font-size: 12px;
    color: #888;
  }

  /* 不同类型的不同样式 */
  .is-poi {
    border-left: 3px solid #409eff;
  }

  .is-polygon {
    border-left: 3px solid #67c23a;
  }

  .is-rectangle {
    border-left: 3px solid #e6a23c;
  }
</style>
