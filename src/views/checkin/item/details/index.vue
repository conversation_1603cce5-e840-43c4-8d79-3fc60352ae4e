<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel
      ref="splitRef"
      space="6px"
      size="60%"
      :allow-collapse="true"
      :resizable="true"
      :responsive="true"
      flex-table
      :custom-style="{ border: 'none', borderWidth: '0', width: '100%' }"
    >
      <div ref="containerRef">
        <ele-card
          :body-style="{
            height: pageHeight + 8 + 'px',
            overflow: 'auto',
            padding: '10px 12px !important'
          }"
        >
          <el-form
            status-icon
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="70px"
            size="small"
            @submit.prevent=""
          >
            <el-form-item label="签到名称" prop="name">
              <el-input
                clearable
                v-model="form.name"
                placeholder="请输入签到名称"
              />
            </el-form-item>
            <el-form-item label="签到方式" prop="qdfs">
              <ele-check-card
                v-model="form.qdfs"
                :items="items['hdQdfs']"
                :row="{ gutter: 12 }"
                @change="changeQdfs"
                class="mode-options"
              >
                <template #item="{ item }">
                  <div class="mode-option">
                    <div class="mode-content">
                      <ele-text class="mode-title">{{ item.name }}</ele-text>
                      <ele-text class="mode-desc" type="placeholder">
                        {{ item.description }}
                      </ele-text>
                    </div>
                  </div>
                </template>
              </ele-check-card>
            </el-form-item>
            <div v-if="form.qdfs !== 'smqd'">
              <el-form-item label="签到条件" prop="qdtj">
                <el-radio-group v-model="form.qdtj">
                  <el-radio-button :value="'1'"
                    >必须在签到范围内</el-radio-button
                  >
                  <el-radio-button :value="'2'">可在范围外签到</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <!--              <el-form-item label="签到范围" prop="addressesIds">-->
              <!--                <AddressMultiple :cacheDataQdfw="cacheDataQdfw"-->
              <!--                                 v-model="form.addressesIds"-->
              <!--                                 :isAddAddress="isAddAddress"/>-->
              <!--              </el-form-item>-->
              <!-- 表单内容保持不变，只修改地址范围部分 -->
              <el-form-item label="签到范围" prop="checkinItemAddresses">
                <AddressMultiple
                  ref="addressMultipleRef"
                  :cache-data-qdfw="cacheDataQdfw"
                  v-model="form.checkinItemAddresses"
                  @update:is-add-address="isAddAddress = $event"
                />
              </el-form-item>
            </div>
            <div v-if="form.qdfs === 'smqd' || form.qdfs === 'dwsmqd'">
              <el-form-item label="二维码类型" prop="qrlx">
                <el-radio-group v-model="form.qrlx">
                  <el-radio-button :value="'1'"
                    >动态二维码（自动刷新）
                  </el-radio-button>
                  <el-radio-button :value="'2'"
                    >静态二维码（适合打印张贴）
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-row :gutter="24">
                <el-col :md="12" :sm="12" :xs="24">
                  <el-form-item label="二维码名称" prop="qrmc">
                    <el-input
                      clearable
                      v-model="form.qrmc"
                      placeholder="请输入二维码名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :sm="12" :xs="24" v-if="form.qrlx === '1'">
                  <el-form-item label="二维码有效时间">
                    <el-input
                      type="number"
                      v-model="form.qryxsj"
                      placeholder="请输入二维码有效时间"
                    >
                      <template #append>秒</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-form-item label="时间模式" prop="qdsjlx">
              <ele-check-card
                v-model="form.qdsjlx"
                :items="items['hdQdsjlx']"
                :row="{ gutter: 12 }"
                @change="changeQdsjlx"
                class="mode-options"
              >
                <template #item="{ item }">
                  <div class="mode-option">
                    <div class="mode-content">
                      <ele-text class="mode-title">{{ item.name }}</ele-text>
                      <ele-text class="mode-desc" type="placeholder">
                        {{ item.description }}
                      </ele-text>
                    </div>
                  </div>
                </template>
              </ele-check-card>
            </el-form-item>
            <template v-if="form.qdsjlx === 'gjms'">
              <el-form-item label="签到日期" prop="qdrqxs">
                <el-date-picker
                  style="width: 100%"
                  :rows="3"
                  v-model="form.qdrqxs"
                  type="dates"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="changeQdrq"
                >
                  <template #default="cell">
                    <div v-if="isHoliday(cell)" class="el-date-table-cell">
                      <span
                        class="el-date-table-cell__text"
                        style="font-size: 12px !important; width: 40px"
                        >{{ isHoliday(cell) }}</span
                      >
                    </div>
                  </template>
                </el-date-picker>
              </el-form-item>
              <el-form-item label="签到时间" prop="qdsjxs">
                <el-col :md="6" :sm="12" :xs="24">
                  <div style="margin-bottom: 6px" @click="onClickqdsj">
                    <ele-text
                      type="primary"
                      style="float: left; cursor: pointer"
                    >
                      <el-icon>
                        <CirclePlus />
                      </el-icon>
                      选择签到时间
                    </ele-text>
                  </div>
                </el-col>
                <el-col :md="18" :sm="12" :xs="24">
                  <div v-show="qdsjShow">
                    <el-form-item label=" ">
                      <el-time-picker
                        is-range
                        v-model="form.qdsjxs"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        @change="onChangeRangeSjjssj"
                        format="HH:mm:ss"
                        value-format="HH:mm:ss"
                      />
                    </el-form-item>
                  </div>
                </el-col>
                <el-col :md="24" :sm="12" :xs="24">
                  <el-tag
                    v-for="(tag, index) in formSjjssj"
                    @close="deleteSjjsSJ(index)"
                    :key="tag"
                    closable
                    effect="plain"
                    style="margin: 10px 10px 10px 0; color: rgb(18 12 12)"
                  >
                    {{ tag }}
                  </el-tag>
                </el-col>
              </el-form-item>
            </template>
            <template v-if="form.qdsjlx === 'zqxhms'">
              <el-form-item label="循环频次" prop="xhpc">
                <el-checkbox-group
                  v-model="form.xhpc"
                  @change="handleCheckedCitiesChange"
                >
                  <el-checkbox
                    v-for="week in items['weeks']"
                    :key="week.id"
                    :value="week.code"
                  >
                    {{ week.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </template>
            <div v-if="form.qdsjlx === 'jdms' || form.qdsjlx === 'zqxhms'">
              <el-form-item label="起止日期" prop="ksjsrqfw">
                <el-date-picker
                  unlink-panels
                  type="daterange"
                  v-model="form.ksjsrqfw"
                  range-separator="-"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="ele-fluid"
                >
                  <template #default="cell">
                    <div v-if="isHoliday(cell)" class="el-date-table-cell">
                      <span
                        class="el-date-table-cell__text"
                        style="font-size: 12px !important; width: 40px"
                        >{{ isHoliday(cell) }}</span
                      >
                    </div>
                  </template>
                </el-date-picker>
              </el-form-item>
              <el-row :gutter="24">
                <el-col :md="12" :sm="12" :xs="24">
                  <el-form-item label="开始时间" prop="kssj">
                    <el-time-select
                      v-model="form.kssj"
                      :max-time="form.jssj"
                      class="mr-4"
                      placeholder="请选择开始时间"
                      start="00:00"
                      step="00:01"
                      end="23:59"
                    />
                  </el-form-item>
                </el-col>
                <el-col :md="12" :sm="12" :xs="24">
                  <el-form-item label="结束时间" prop="jssj">
                    <el-time-select
                      v-model="form.jssj"
                      :min-time="form.kssj"
                      placeholder="请选择结束时间"
                      start="00:00"
                      step="00:01"
                      end="23:30"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <el-form-item label="签到对象" prop="selectorDatas">
              <div
                style="margin-bottom: 6px; cursor: pointer"
                @click="openPerSelector()"
              >
                <ele-text
                  type="primary"
                  style="float: left"
                  @click="openPerSelector()"
                >
                  <el-icon>
                    <CirclePlus />
                  </el-icon>
                  选择签到对象 </ele-text
                >&nbsp;
              </div>
              <el-input
                :rows="6"
                type="textarea"
                readonly
                v-model="qddxData"
                placeholder="请选择签到对象"
              />
            </el-form-item>
            <el-form-item label="签到说明" prop="">
              <el-input
                :rows="6"
                type="textarea"
                show-word-limit
                maxlength="255"
                v-model="form.bz"
                placeholder="请输入签到说明"
              />
            </el-form-item>
            <el-row :gutter="24">
              <el-col :md="6" :sm="12" :xs="24">
                <el-form-item label="是否提醒" prop="sftx">
                  <el-radio-group v-model="form.sftx">
                    <el-radio-button value="是">是</el-radio-button>
                    <el-radio-button value="否">否</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :md="8" :sm="12" :xs="24">
                <el-form-item label="限签次数">
                  <el-input
                    type="number"
                    v-model="form.qdcs"
                    placeholder="请输入限签次数"
                  />
                </el-form-item>
              </el-col>
              <el-col :md="10" :sm="12" :xs="24">
                <el-form-item
                  v-if="form.sftx === '是'"
                  label-width="120px"
                  label="提前提醒时间"
                  prop="tqtxsj"
                >
                  <el-input
                    type="number"
                    v-model="form.tqtxsj"
                    placeholder="请输入提前提醒时间"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </ele-card>
      </div>
      <!-- 底部工具栏 -->
      <ele-bottom-bar>
        <template #extra>
          <el-button size="small" @click="onBack">取消</el-button>
          <el-button
            size="small"
            type="primary"
            plain
            :loading="loading"
            @click="onSubmit"
          >
            提交
          </el-button>
        </template>
      </ele-bottom-bar>
      <template #body>
        <ele-card
          header="签到位置"
          :body-style="{ padding: '10px 12px!important' }"
        >
          <template #extra>
            <el-button
              size="small"
              type="primary"
              plain
              @click="onSubmitAddress"
            >
              保存
            </el-button>
          </template>
          <MapInit
            ref="mapInitRef"
            :moduleParam="routeType"
            :page-height="pageHeight - 119"
            @update-address="handleMapAddressUpdate"
          />
        </ele-card>
      </template>
    </ele-split-panel>
    <!-- 清空父组件数据 -->
    <PerSelector
      v-model="showPerSelector"
      ref="treeTransferRef"
      node-key="id"
      header="签到对象"
      paramMode="base"
      :userType="userType"
      :perSelectedData="perSelectedData"
      @done="onDoneSelector"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, unref, computed, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { ElMessage as EleMessage } from 'element-plus';
  import { usePageTab } from '@/utils/use-page-tab';
  import { getCodeData } from '@/views/base-code/dictionary/api/data-index';
  import { useThemeStore } from '@/store/modules/theme';
  import { operation, getCheckinItemById } from '../api/index';
  import dataUtil from '@/utils/date-util';
  import AddressMultiple from './address-multiple.vue';
  import PerSelector from '@/components/PerSelector/index.vue';
  import MapInit from '@/views/checkin/address/map/index.vue';
  import { CirclePlus } from '@element-plus/icons-vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const themeStore = useThemeStore();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle } =
    usePageTab();
  const { params, path, query } = unref(currentRoute);
  const routeType = path.split('/')[4];
  const xmId = query?.xmId;
  const xm = query?.xm;
  const title = query?.title;
  const userType = query?.userType;
  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /**功能业务类型*/
    paramMode: String, //base=通用业务类型
    header: String,
    projectId: String, //项目ID
    userType: String, //用户类型
    perSelectedData: Array
  });

  if (title) setPageTabTitle(title + '项目维护');
  /** 表单配置 */
  const config = reactive({
    labelWidth: 120,
    grid: 3
  });

  // 回显时当前页不存在的数据
  const cacheDataQdfw = ref();
  /** 卡片数据 */
  const items = ref({});

  const formQdrq = ref([]);
  const formSjjssj = ref([]);

  const qdsjShow = ref(false);

  const showPerSelector = ref(false);
  const perSelectedData = ref([]);

  /** 公共码表获取 */
  const queryCodeData = (code) => {
    getCodeData({ codeType: code })
      .then((list) => {
        if (list) {
          items.value[code] = list.map((d) => {
            d.value = d.code;
            return {
              ...d,
              col: { md: 6, sm: 12, xs: 24, style: { marginBottom: '12px' } }
            };
          });
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  let codeTypes = ['hdQdfs', 'hdQdsjlx', 'weeks'];
  codeTypes.forEach((code) => {
    if (code) queryCodeData(code);
  });

  /** 提交状态 */
  const loading = ref(false);

  const checkAll = ref(false);
  const isIndeterminate = ref(true);

  /** 表单实例 */
  const formRef = ref(null);
  const form = ref({
    qdfs: 'dwqd',
    qdsjlx: 'jdms',
    checkinItemAddresses: [], // 现在包含两种地址数据
    qdsjxs: null,
    sjjssj: [],
    selectorDatas: [],
    sftx: null,
    tqtxsj: null
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入签到名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    qdfs: [
      {
        required: true,
        message: '请选择签到方式',
        type: 'string',
        trigger: 'blur'
      }
    ],
    qdtj: [
      {
        required: true,
        message: '请选择签到条件',
        trigger: 'change'
      }
    ],
    checkinItemAddresses: [
      {
        required: true,
        message: '请选择签到范围',
        trigger: 'change'
      }
    ],
    publicType: [
      {
        required: true,
        message: '请选择二维码类型',
        trigger: 'change'
      }
    ],
    qdsjlx: [
      {
        required: true,
        message: '请选择时间模式',
        trigger: 'change'
      }
    ],
    qdrqxs: [
      {
        required: true,
        message: '请选择签到日期',
        trigger: 'change'
      }
    ],
    qdsjxs: [
      {
        required: true,
        message: '请选择签到时间',
        trigger: 'change'
      }
    ],
    xhpc: [
      {
        required: true,
        message: '请选择循环频次',
        trigger: 'change'
      }
    ],
    ksjsrqfw: [
      {
        required: true,
        message: '请选择开始日期-结束日期范围',
        trigger: 'change'
      }
    ],
    kssj: [
      {
        required: true,
        message: '请选择开始时间',
        trigger: 'change'
      }
    ],
    jssj: [
      {
        required: true,
        message: '请选择结束时间',
        trigger: 'change'
      }
    ],
    selectorDatas: [
      {
        required: true,
        message: '请选择签到对象',
        trigger: 'change'
      }
    ]
  });

  const changeQdsjlx = (value) => {
    if (value === 'gjms') {
      form.value.xhpc = null;
      form.value.ksjsrqfw = null;
      form.value.kssj = null;
      form.value.jssj = null;
    } else {
      formQdrq.value = [];
      form.value.qdsjxs = null;
      form.value.qdrq = null;
      formSjjssj.value = [];
    }
  };
  const changeQdfs = (value) => {
    if (value) {
      form.value.qdtj = null;
      form.value.checkinItemAddresses = null;
      form.value.qrlx = null;
      form.value.qryxsj = null;
      form.value.qrmc = null;
    }
  };

  const openPerSelector = () => {
    showPerSelector.value = true;
  };

  const onDoneSelector = (data) => {
    console.log('onDoneSelector====', data);
    perSelectedData.value = data;
    form.value.selectorDatas = data;
  };

  const qddxData = computed(() => {
    let allDataCode = [];
    if (perSelectedData.value.length > 0) {
      perSelectedData.value.forEach((e) => {
        allDataCode.push(e.dictDataName ? e.dictDataName : e.name);
      });
    }
    return allDataCode.join(',');
  });

  const onClickqdsj = () => {
    qdsjShow.value = true;
  };
  const onChangeRangeSjjssj = (e) => {
    formSjjssj.value.push(e.join('-'));
    qdsjShow.value = false;
  };

  const changeQdrq = (newValue) => {
    formQdrq.value = newValue;
  };

  const deleteSjjsSJ = (index) => {
    formSjjssj.value.splice(index, 1);
  };
  // 获取节日信息
  const isHoliday = (cell) => {
    let nowDate = cell.date;
    let year = nowDate.getFullYear();
    let month = nowDate.getMonth() + 1;
    let day = nowDate.getDate();
    let recentHoliday = dataUtil.solar2lunar(year, month, day);
    return recentHoliday?.festival ?? recentHoliday?.lunarFestival;
  };
  // 在父组件中
  const addressMultipleRef = ref();
  const mapInitRef = ref();

  // 处理地图更新的地址
  const handleMapAddressUpdate = (addressData) => {
    console.log(addressData);
    if (addressData.isFromMap === '是') {
      //入地址库地址信息
      addressData.addressId = addressData.id;
    }
    addressMultipleRef.value?.addMapAddress(addressData);
  };

  // 准备提交数据
  const prepareSubmitData = () => {
    const submitData = { ...form.value };
    // 处理地址数据
    submitData.addresses = form.value.checkinItemAddresses.map((addr) => {
      if (addr.isFromMap) {
        return {
          name: addr.name,
          type: addr.type,
          coordinates: addr.coordinates,
          radius: addr.radius,
          isNew: true
        };
      }
      return {
        id: addr.id // 地址库中的地址只需ID
      };
    });

    // 删除原始地址ID数组
    delete submitData.checkinItemAddresses;
    console.log(submitData);
    return submitData;
  };

  /**用于更新address数据*/
  // const isAddAddress = ref(false);
  //
  // const child = ref(null);
  const onSubmitAddress = () => {
    if (mapInitRef.value) {
      mapInitRef.value.onSubmitAddress();
    }
  };

  /** 提交 */
  const onSubmit = () => {
    console.log('提交', form.value);
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // const data = prepareSubmitData();
      // let newObj = Object.assign(form.value, data);
      let newObj = form.value;
      newObj.type = routeType;
      newObj.qdrq = formQdrq.value.length > 0 ? formQdrq.value.join(',') : null;
      newObj.qdsj =
        formSjjssj.value.length > 0 ? formSjjssj.value.join(',') : null;
      if (form.value.ksjsrqfw) {
        let fw = form.value.ksjsrqfw;
        newObj.ksrq = fw[0];
        newObj.jsrq = fw[1];
      }
      newObj.xhpc =
        form.value.xhpc && form.value.xhpc.length > 0
          ? form.value.xhpc.join(',')
          : null;
      console.log(JSON.stringify(newObj));
      loading.value = true;
      operation(newObj)
        .then(() => {
          loading.value = false;
          EleMessage.success('操作成功');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        })
        .finally(() => {
          onBack();
          loading.value = false;
        });
    });
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    reloadPageTab({
      fullPath: '/checkin/item/' + routeType,
      query: { enterType: 'edit' }
    });
  };

  const handleCheckedCitiesChange = (value) => {
    const checkedCount = value.length;
    checkAll.value = checkedCount === items.value['weeks'].length;
    isIndeterminate.value =
      checkedCount > 0 && checkedCount < items.value['weeks'].length;
  };

  const queryCheckinItemById = () => {
    getCheckinItemById(xmId)
      .then((resData) => {
        if (resData) {
          if (resData.ksrq && resData.jsrq) {
            resData.ksjsrqfw = [resData.ksrq, resData.jsrq];
          }
          if (resData.selectorDatas.length > 0) {
            perSelectedData.value = resData.selectorDatas;
          }
          if (resData.checkinItemAddresses.length > 0) {
            // resData.addressesIds = resData.checkinItemAddresses.map((item) => item.id);
            cacheDataQdfw.value = resData.checkinItemAddresses;
          }

          formSjjssj.value = resData.qdsj ? resData.qdsj.split(',') : [];
          resData.qdsjxs = resData.qdsj ? formSjjssj.value[0].split('-') : [];
          let qdrq = resData.qdrq ? resData.qdrq.split(',') : [];
          formQdrq.value = qdrq;
          resData.qdrqxs = qdrq;
          resData.xhpc = resData.xhpc ? resData.xhpc.split(',') : [];
          form.value = resData;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  if (xmId) queryCheckinItemById();
</script>

<script>
  export default {
    name: 'CheckInItemDetails'
  };
</script>

<style lang="scss" scoped>
  #pickerBox {
    position: absolute;
    background-color: white;
    z-index: 999;
    top: 15px;
    left: 15px;
    width: 310px;
  }

  #drawBox {
    position: absolute;
    background-color: white;
    z-index: 9999;
    top: 15px;
    left: 15px;
    padding: 0 10px 10px;
  }

  #panel {
    position: absolute;
    background-color: white;
    overflow-y: auto;
    width: 310px;
  }
</style>
