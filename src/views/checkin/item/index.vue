<template>
  <ele-page hide-footer  flex-table>
    <search @search="reload"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import search from './components/search.vue';
import {queryPage, removes} from './api';
import {useRouter} from "vue-router";
import Search from "@/views/base-code/dwb/components/search.vue";
import {usePageTab} from "@/utils/use-page-tab.js";

const {currentRoute, push,} = useRouter();
const {params, path, meta} = unref(currentRoute);
console.log(meta.title)
let pathArray = path.split("/")
const routeType = pathArray[3];
const userType = pathArray[4];
const {removePageTab, getRouteTabKey, reloadPageTab, setPageTabTitle} = usePageTab();

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      slot: 'name',
      label: '签到名称',
    },
    {
      prop: 'cjrxm',
      label: '创建人',
    },
    {
      prop: 'cjr',
      label: '创建人账号',
    },
    {
      prop: 'cjsj',
      label: '创建时间',
    },
    {
      prop: 'status',
      label: '状态',
    },
  ]
);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit, type: routeType});
};
/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 搜索 */
const reload = (where) => {
  console.log(where)
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 打开组group编辑弹窗 */
const openEdit = (row) => {
  removePageTab({key: getRouteTabKey()});
  reloadPageTab({
    fullPath: '/checkin/item/details/' + routeType + (userType ? '/' + userType : ''),
    query: {
      xmId: row?.id,
      userType: userType,
      title: meta.title + (row ? '-' + row.name : '')
    }
  });
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.paramName).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'checkInItemIndex'
};
</script>

