<template>
  <el-form
    ref="formRefMap"
    @submit.prevent=""
    size="small"
    label-width="84px"
    style="margin: 3px 6px 0"
    :model="formMapAddress"
    :rules="rulesFormRefMap"
  >
    <el-form-item label="自定义名称" prop="name">
      <el-input
        clearable
        v-model="formMapAddress.name"
        placeholder="请输入自定义位置名称"
      />
    </el-form-item>
    <el-form-item label="选择模式">
      <el-radio-group v-model="formMapAddress.type" @change="onChange">
        <el-radio-button value="poi">查询模式</el-radio-button>
        <el-radio-button value="draw" v-if="!onlyPoi">绘制模式</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="formMapAddress.type === 'poi'" label="范围半径(米)">
      <el-slider
        v-model="formMapAddress.fwbj"
        :max="5000"
        size="small"
        @change="onDrawCircle"
        @input="onDrawCircle"
      />
    </el-form-item>
  </el-form>
  <div
    ref="locationMapRef"
    :style="{
      height: pageHeight + 'px',
      marginTop: '10px',
      position: 'relative'
    }"
  >
    <div id="pickerBox" v-if="formMapAddress.type === 'poi'">
      <el-input
        v-model="searchAddress"
        placeholder="请输入搜索关键词"
        clearable
        @change="onSearch"
      >
        <template #append>
          <el-button
            :icon="SearchOutlined"
            class="ele-btn-icon"
            @click="onSearch"
          />
        </template>
      </el-input>
      <div id="panel"></div>
    </div>
    <div id="drawBox" v-else>
      <div style="margin: 12px 10px 3px">
        <el-icon>
          <Bell/>
        </el-icon
        >&nbsp;&nbsp;请点击您要绘制的覆盖物类型，双击鼠标完成绘制
      </div>
      <ele-check-card
        v-model="drawTypeSet"
        :items="itemsDrawType"
        @change="onDraw"
        :item-style="{ margin: '8px', padding: '8px 12px', display: 'flex' }"
        style="display: flex; flex-wrap: wrap"
      >
        <template #item="{ item }">
          <ele-text
            type="danger"
            v-if="item.value === 'Clear'"
            :icon="CircleClose"
          >{{ item.title }}
          </ele-text
          >
          <ele-text type="heading" v-else>{{ item.title }}</ele-text>
        </template>
      </ele-check-card>
    </div>
    <div
      id="clearBox"
      v-if="lnglat && formMapAddress.type === 'draw'"
      style="padding-top: 10px; cursor: pointer"
      @click="onClear"
    >
      <el-icon color="red" style="top: 1px">
        <DeleteFilled/>
      </el-icon
      >&nbsp;<span style="color: red">清除覆盖物</span>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, onBeforeUnmount} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElMessage as EleMessage} from 'element-plus';
import {SearchOutlined} from '@/components/icons';
import {operation as addressOperation} from '../api/index';
import {useThemeStore} from '@/store/modules/theme';
import AMapLoader from '@amap/amap-jsapi-loader';
import {MAP_KEY} from '@/config/setting';
import {Bell, CircleClose, DeleteFilled} from '@element-plus/icons-vue';

const emits = defineEmits(['updateAddress']);
const themeStore = useThemeStore();

const props = defineProps({
  pageHeight: Number,
  /** 模块类型 */
  moduleParam: String,
  /** 请假类型id */
  presetAddressId: String,
  /** 只为查询模式 */
  onlyPoi: {
    type: Boolean,
    default: false,
  },
});

// 地图相关变量
const locationMapRef = ref(null);
const searchAddress = ref(null);
const slectedAddress = ref(null);
const drawTypeSet = ref('');
/** 存经纬度，*/
const lnglat = ref('');
/** 官网底部地图的实例 */
let mapInsLocation;
let mouseTool;
let placeSearch;

/** 卡片数据 */
const items = ref({});
const formRefMap = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
const form = ref({
  qdfs: 'dwqd',
  qdsjlx: 'jdms',
  qdfw: [],
  sjjssj: [],
  selectorDatas: [],
});

const itemsDrawType = ref([
  {
    value: 'Polygon',
    title: '绘制多边形',
  },
  {
    value: 'Rectangle',
    title: '绘制矩形',
  },
  {
    value: 'Circle',
    title: '绘制圆形',
  },
]);

const onDraw = (event) => {
  mapInsLocation.clearMap(); // 清空之前的图层
  switch (event) {
    case 'Polyline':
      drawPolyline();
      break;
    case 'Polygon':
      drawPolygon();
      break;
    case 'Rectangle':
      drawRectangle();
      break;
    default:
      drawCircle();
      break;
  }
};
const onClear = () => {
  mapInsLocation.clearMap(); // 清空之前的图层
};

// 初始化地图
// const renderLocationMap = () => {
//   AMapLoader.load({
//     key: MAP_KEY,
//     version: '2.0',
//     plugins: ['AMap.PlaceSearch', 'AMap.MouseTool']
//   }).then((AMap) => {
//     mapInsLocation = new AMap.Map(locationMapRef.value, {
//       zoom: 11,
//       resizeEnable: true
//     });
//
//     // 初始化绘图工具
//     mouseTool = new AMap.MouseTool(mapInsLocation);
//     mouseTool.on('draw', handleDrawComplete);
//
//     // 添加地图控件
//     mapInsLocation.addControl(new AMap.ControlBar({
//       showZoomBar: true,
//       showControlButton: true,
//       position: {right: '10px', top: '10px'}
//     }));
//
//   }).catch((e) => {
//     console.error("地图加载失败:", e);
//     EleMessage.error("地图加载失败，请刷新页面重试");
//   });
// };

/** 渲染高德底部地图 */
const renderLocationMap = () => {
  AMapLoader.load({
    key: MAP_KEY, // 高德地图API Key
    version: '2.0', // 指定API版本
    plugins: [
      'AMap.Geolocation',
      'AMap.PlaceSearch',
      'AMap.Scale',
      'AMap.Marker',
      'AMap.MouseTool',
    ], // 需要使用的插件
  }).then((AMap) => {
    mapInsLocation = new AMap.Map(locationMapRef.value, {
      zoom: 11, // 初始缩放级别
      resizeEnable: true,
    });
    mouseTool = new AMap.MouseTool(mapInsLocation);
    mouseTool.on('draw', function(event) {
      // event.obj 为绘制出来的覆盖物对象
      console.log('覆盖物对象绘制完成===', event);
      // event.obj 为绘制出来的覆盖物对象
      let path = event.obj.getPath(); // 获取多边形的路径
      let str = '';
      let pathArr = [];
      for (let i = 0; i < path.length; i++) {
        pathArr.push([path[i].getLng(), path[i].getLat()]);
        str += path[i].getLng() + '#' + path[i].getLat() + ',';
      }
      console.log('绘制的点的坐标数组===', pathArr);
      console.log('转换为字符串===', str);
      lnglat.value = str;
      // this.geometryArr = pathArr; // 这个就是
      // this.addForm.lnglat = str;  // 转换为字符串
    });
  }).catch((e) => {
    console.error(e);
  });
};

// 重置表单
const resetForm = () => {
  formMapAddress.name = '';
  formMapAddress.bz = '';
  formMapAddress.fwbj = 200;
  lnglat.value = '';
  searchAddress.value = '';
  mapInsLocation.clearMap();
};

const destroyMap = () => {
  if (mapInsLocation) {
    // 组件销毁前，清理地图资源
    mapInsLocation.destroy();
    mapInsLocation = null;
  }
};

//根据名字地址去搜索结果
let initPlaceSearch = (name) => {
  console.log(name);
  AMap.plugin(['AMap.PlaceSearch'], function() {
    //构造地点查询类
    placeSearch = new AMap.PlaceSearch({
      pageSize: 10, // 单页显示结果条数
      pageIndex: 1, // 页码
      // city: "010", // 兴趣点城市
      // citylimit: true,  //是否强制限制在设置的城市内搜索
      map: mapInsLocation, // 展现结果的地图实例
      panel: 'panel', // 结果列表将在此容器中进行展示。
      autoFitView: true, // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
    });
    //关键字查询
    placeSearch.search(name);
    //点击panel列表的点标记
    AMap.Event.addListener(placeSearch, 'listElementClick', function(e) {
      if (e.data) {
        slectedAddress.value = e.data;
        formMapAddress.name = e.data?.name;
        formMapAddress.bz = e.data?.address;
        lnglat.value = e.data.location.lng + '#' + e.data.location.lat;
      }
      if (formMapAddress.fwbj) onDrawCircle();
    });
  });
};

/** 表单数据 */
const formMapAddress = reactive({
  name: '',
  bz: '',
  type: 'poi',
  fwbj: 200,
  jwdzb: '',
  // "tbdz": false,
});

/** 表单验证规则 */
const rulesFormRefMap = reactive({
  name: [
    {
      required: true,
      message: '请输入自定义位置名称',
      type: 'string',
      trigger: 'blur',
    },
  ],
});

const onSubmitAddress = () => {
  console.log('子组件的方法被调用');
  formRefMap.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }

    if (formMapAddress.type === 'poi' && !lnglat.value) {
      EleMessage.error('请在地图上输入您要搜索的位置信息');
      return;
    } else if (formMapAddress.type === 'draw' && !lnglat.value) {
      EleMessage.error('请在地图上绘制范围信息');
      return;
    }
    //
    // const addressData = {
    //   // id: `map-${Date.now()}`,
    //   isFromMap: true,
    // };
    const newObj = {...formMapAddress, moduleParam: props.moduleParam};
    newObj.type = drawTypeSet.value ? drawTypeSet.value : formMapAddress.type;
    newObj.jwdzb = lnglat.value;
    // 加一层确认
    // ElMessageBox.confirm('是否将该地址同步到地址库？', '确认操作', {
    //   confirmButtonText: '是',
    //   cancelButtonText: '否',
    //   type: 'warning',
    // }).then(() => {
    //   // 用户点击“是”
    //   loading.value = true;
    //   addressOperation(newObj).then((resData) => {
    //     if (resData) {
    //       loading.value = false;
    //       formMapAddress.type = 'poi';
    //       onChange('poi');
    //       resData.isFromMap = '是';
    //       emits('updateAddress', resData);
    //       EleMessage.success('操作成功');
    //     }
    //   }).catch((e) => {
    //     loading.value = false;
    //     EleMessage.error(e.message);
    //   });
    // }).catch(() => {
    newObj.isFromMap = '否';
    // 用户点击“否”
    emits('updateAddress', newObj);
    //   resetForm();
    //   EleMessage.success('位置已添加（未同步地址库）');
    // });
  });
};

// 绘图完成回调
const handleDrawComplete = (event) => {
  let coordinates = '';
  if (
    event.obj instanceof AMap.Polygon ||
    event.obj instanceof AMap.Polyline
  ) {
    const path = event.obj.getPath();
    coordinates = path.map((point) => `${point.lng},${point.lat}`).join(';');
  } else if (event.obj instanceof AMap.Circle) {
    const center = event.obj.getCenter();
    coordinates = `${center.lng},${center.lat}`;
    formMapAddress.fwbj = event.obj.getRadius();
  }

  lnglat.value = coordinates;
  formMapAddress.type = drawTypeSet.value;
};

const onChange = (newValue) => {
  if (newValue) {
    placeSearch?.clear(); // 清除标记
    mapInsLocation.clearMap(); // 清空之前的图层
    searchAddress.value = null;
    drawTypeSet.value = null;
    formMapAddress.name = null;
    formMapAddress.bz = null;
    formMapAddress.fwbj = newValue === 'poi' ? 500 : 0;
    lnglat.value = null;
  }
};

/**
 * 页面输入半径地图画圆
 */
const onDrawCircle = () => {
  mapInsLocation.clearMap(); // 清空之前的图层
  if (slectedAddress.value) {
    let circle = new AMap.Circle({
      center: [
        slectedAddress.value.location.lng,
        slectedAddress.value.location.lat,
      ],
      radius: formMapAddress.fwbj, //半径
      borderWeight: 1,
      strokeStyle: 'dashed',
      strokeDasharray: [10, 10],
      // 线样式还支持 'dashed'
      strokeColor: '#3366FF', //线颜色
      strokeOpacity: 0.3, //线透明度
      strokeWeight: 3, //线粗细度
      fillColor: '#FFA500', //填充颜色
      fillOpacity: 0.35, //填充透明度
      zIndex: 50,
    });
    mapInsLocation.add(circle);
    // 缩放地图到合适的视野级别
    mapInsLocation.setFitView([circle]);
  }
};

/**
 * 根据关键字查询经纬度信息
 */
const onSearch = () => {
  if (!searchAddress.value) {
    EleMessage.error('请输入搜索关键词');
    return;
  }
  initPlaceSearch(searchAddress.value);
};

const drawPolyline = () => {
  mouseTool.polyline({
    strokeColor: '#031bb7',
    strokeOpacity: 1,
    strokeWeight: 4,
    // 线样式还支持 'dashed'
    strokeStyle: 'solid',
    // strokeStyle是dashed时有效
    // strokeDasharray: [10, 5],
  });
};

const drawPolygon = () => {
  mouseTool.polygon({
    strokeColor: '#3366FF', //线颜色
    strokeOpacity: 0.3, //线透明度
    strokeWeight: 3, //线粗细度
    fillColor: '#FFA500', //填充颜色
    fillOpacity: 0.35, //填充透明度
    // 线样式还支持 'dashed'
    strokeStyle: 'solid',
    // strokeStyle是dashed时有效
    // strokeDasharray: [30,10],
  });
};

const drawRectangle = () => {
  mouseTool.rectangle({
    strokeColor: '#3366FF', //线颜色
    strokeOpacity: 0.3, //线透明度
    strokeWeight: 3, //线粗细度
    fillColor: '#FFA500', //填充颜色
    fillOpacity: 0.35, //填充透明度
    // strokeStyle还支持 solid
    strokeStyle: 'solid',
    // strokeDasharray: [30,10],
  });
};

const drawCircle = () => {
  mouseTool.circle({
    strokeColor: '#3366FF', //线颜色
    strokeOpacity: 0.3, //线透明度
    strokeWeight: 3, //线粗细度
    fillColor: '#FFA500', //填充颜色
    fillOpacity: 0.35, //填充透明度
    strokeStyle: 'solid',
    // 线样式还支持 'dashed'
    // strokeDasharray: [30,10],
  });
};

/** 渲染地图 */
onMounted(() => {
  renderLocationMap();
});

/** 销毁地图 */
onBeforeUnmount(() => {
  if (mapInsLocation) {
    mapInsLocation.destroy();
  }
});
const getMapAddress = () => {
  let obj = {
    ...formMapAddress,
    jwdzb: lnglat.value,
    type: drawTypeSet.value ? drawTypeSet.value : formMapAddress.type,
  };
  console.log('getMapAddress :>> ', obj);
  return obj;
};
defineExpose({
  onSubmitAddress,
  getMapAddress,
});
</script>

<script>
export default {
  name: 'ADDRESSMAPINIT',
};
</script>

<style lang="scss" scoped>
#pickerBox {
  position: absolute;
  background-color: white;
  z-index: 999;
  top: 6px;
  left: 6px;
  width: 310px;
}

#drawBox {
  position: absolute;
  background-color: white;
  z-index: 999;
  top: 6px;
  left: 6px;
  padding: 0 10px 10px;
}

#clearBox {
  position: absolute;
  background-color: white;
  z-index: 999;
  top: 6px;
  right: 6px;
  padding: 0 10px 10px;
}

#panel {
  position: absolute;
  background-color: white;
  overflow-y: auto;
  width: 310px;
}
</style>
