<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="830"
    :title="(isUpdate ? '修改' : '添加') + '签到地址'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-card :body-style="{ padding: '0!important', height: 'auto' }">
      <MapInit
        ref="child"
        :pageHeight="pageHeight - 140"
        :moduleParam="moduleParam"
        @updateAddress="handleUpdateAddress"
      />
    </ele-card>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        type="primary"
        size="small"
        plain
        :loading="loading"
        @click="callChildMethod"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import MapInit from '../map/index.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const emits = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 模块类型 */
    moduleParam: String,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  const child = ref(null);

  /** 保存编辑 */
  const handleUpdateAddress = () => {
    emits('done');
  };
  const callChildMethod = () => {
    if (child.value) {
      child.value.onSubmitAddress();
      updateModelValue(false);
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emits('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      console.log(modelValue);
    }
  );
</script>
