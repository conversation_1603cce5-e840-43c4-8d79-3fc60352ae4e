<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <!--        <template #action="{ row }">-->
        <!--          &lt;!&ndash;                    <el-link type="primary" underline="never" @click="openEdit(row)">&ndash;&gt;-->
        <!--          &lt;!&ndash;                        修改&ndash;&gt;-->
        <!--          &lt;!&ndash;                    </el-link>&ndash;&gt;-->
        <!--          &lt;!&ndash;                    <el-divider direction="vertical"/>&ndash;&gt;-->
        <!--          <el-link type="primary" underline="never" @click="remove(row)">-->
        <!--            删除-->
        <!--          </el-link>-->
        <!--        </template>-->
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="delReload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import nameFilter from './components/name-filter.vue';
  import { queryPage, removes } from './api';
  import { useRouter } from 'vue-router';
  import { useDictData } from '@/utils/use-dict-data.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import NameFilter from '@/views/exam/my-exam/components/name-filter.vue';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[4];

  const userStore = useUserStore();
  useDictData(['listRoles'], { routeType: routeType });
  const { dicts } = storeToRefs(userStore);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '地址名称',
      headerSlot: 'nameHeader',
      slot: 'name'
    },
    {
      prop: 'type',
      label: '类型',
      formatter: (row) => {
        let finallyVal = '';
        switch (row.type) {
          case 'poi':
            finallyVal = '圆形';
            break;
          case 'Polygon':
            finallyVal = '多边形';
            break;
          case 'Rectangle':
            finallyVal = '矩形';
            break;
          default:
            finallyVal = '圆形';
            break;
        }
        return finallyVal;
      }
    },
    {
      prop: 'fwbj',
      label: '范围/半径'
    },
    {
      prop: 'sort',
      label: '排序',
      sortable: 'custom'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };
  /** 搜索 */
  const delReload = () => {
    selections.value = [];
    tableRef.value?.reload?.();
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            delReload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SelectorConfig'
  };
</script>
