<template>
  <ele-page
    flex-table
    :style="{
      padding: !enterType ? '8px 8px 0 8px !important' : 'unset!important'
    }"
  >
    <!-- 搜索表单 -->
    <!--    <search @search="searchReload"/>-->
    <ele-card
      flex-table
      :body-style="{
        padding: '10px 8px !important',
        height: pageHeight + 45 + 'px',
        overflow: 'auto',
        flex: enterType ? 'unset' : '1'
      }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            size="small"
            class="ele-btn-icon"
            @click="openPerSelector()"
          >
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
          <el-button class="ele-btn-icon" size="small" @click="openImport">
            导入
          </el-button>
        </template>
        <!--        <template #action="{ row }">-->
        <!--          <el-link type="primary" underline="never" @click="remove(row)">-->
        <!--            删除-->
        <!--          </el-link>-->
        <!--        </template>-->
      </ele-pro-table>
    </ele-card>
    <!-- 新建 -->
    <template v-if="userType">
      <PerSelector
        v-model="showPerSelector"
        ref="treeTransferRef"
        node-key="id"
        paramMode="base"
        :projectId="currentXmId"
        :userType="userType"
        @done="onDoneSelector"
      />
    </template>
    <!-- 导入弹窗 -->
    <import v-model="showImport" :projectId="currentXmId" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import {
    PlusOutlined,
    DeleteOutlined,
    UploadOutlined
  } from '@/components/icons';
  import Import from './components/import.vue';
  import { formProjectSpecialAdd, queryPage, removes } from './api/index';
  import { useRouter } from 'vue-router';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import PerSelector from '@/components/PerSelector/index.vue';
  import { getFormProjectById } from '@/views/zizhu/api/index.js';
  import { usePageTab } from '@/utils/use-page-tab.js';

  const { removePageTab, getRouteTabKey, addPageTab, setPageTabTitle } =
    usePageTab();

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const props = defineProps({
    pageHeight: Number
  });

  const { currentRoute, push } = useRouter();
  const { params, path, query } = unref(currentRoute);
  console.log(params, path, query);
  // const userType = path.split("/")[3];
  const userType = ref(null);
  const currentXmId = params.xmId;
  let enterType = query?.enterType;
  let routeType = path.split('/')[3];

  /** 表格实例 */
  const tableRef = ref(null);

  /** 提交状态 */
  const loading = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'userInfo.xgh',
      label: '学号'
    },
    {
      prop: 'userInfo.xm',
      label: '姓名'
    },
    {
      prop: 'userInfo.xymc',
      label: '学院'
    },
    {
      prop: 'userInfo.zymc',
      label: '专业'
    },
    {
      prop: 'userInfo.njmc',
      label: '年级'
    },
    {
      prop: 'userInfo.bjmc',
      label: '班级'
    }
    // {
    //   columnKey: 'action',
    //   label: '操作',
    //   slot: 'action',
    //   width: 100,
    //   fixed: 'right'
    // }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  const showPerSelector = ref(false);
  const perSelectedData = ref([]);

  const openPerSelector = () => {
    userType.value = currentProjectData.value.userType;
    showPerSelector.value = true;
  };

  const onDoneSelector = (data) => {
    handleFormProjectSpecialAdd(data);
  };

  /**
   * 资助特殊名单保存
   * zzProjectSpecialList
   */
  const handleFormProjectSpecialAdd = (data) => {
    if (currentXmId) {
      formProjectSpecialAdd(currentXmId, data)
        .then((msg) => {
          // leftOperationArray.value = [];
          // leftOperation.value = {};
          // checkAll.value = {};
          // isIndeterminate.value = {};
          EleMessage.success(msg);
          reload();
          // updateModelValue(false)
          // emits('done', leftOperationArray.value);
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      projectId: currentXmId
    });
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.userInfo.xm).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 项目信息*/
  const currentProjectData = ref();

  const queryFormProjectById = () => {
    getFormProjectById(currentXmId)
      .then((data) => {
        if (data) {
          currentProjectData.value = data;
          userType.value = currentProjectData.value.userType;
        }
        loading.value = false;
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      })
      .finally(() => {
        loading.value = false;
        if (!enterType) {
          addPageTab({
            title: `${currentProjectData.value.xmmc}-特殊名单`,
            key: `/zizhu/project-special-list/${routeType}/${currentProjectData.value?.id}`,
            closable: true
          });
        }
        // setPageTabTitle(currentProjectData.value.xmmc + '审核')
      });
  };

  if (currentXmId) queryFormProjectById();
</script>

<script>
  export default {
    name: 'FORMPROJECTSPECIALLIST'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
