/**
 * 特殊名单
 * */
import request from '@/utils/request.js';

/**
 * 导入
 */
export async function importData(file, projectId, ) {
    const formData = new FormData();
    formData.append('file', file);
    // formData.append('projectId', projectId);
    // formData.append('type', userType);
    const res = await request.post('/form/form-project-special-list/'+projectId+'/importData', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getFormProjectSpecial(params) {
    const res = await request.get('/form/form-project-special-list', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/form/form-project-special-list/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFormProjectSpecialById(id) {
    const res = await request.get('/form/form-project-special-list/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/form/form-project-special-list/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加
 * @param id 项目ID
 * @param data
 * @returns {Promise<*>}
 */
export async function formProjectSpecialAdd(id,data) {
    const res = await request.post('/form/form-project-special-list/'+id+'/add', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/form/form-project-special-list/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
