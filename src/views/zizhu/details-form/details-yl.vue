<!--表单预览-->
<template>
  <ele-drawer :size="830"
              title="表单预览"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-card :body-style="{padding: '0px !important' }">
      <div ref="containerRef" :style="{height: pageHeight+'px',overflow:'auto'}">
        <div v-for=" (group, index) in data">
          <div :id="'anchor'+index">
            <table-preview v-if="group.listFlag==='是'"
                           :key="'formKey'+index"
                           :labelWidth="config.labelWidth"
                           :grid="config.grid"
                           :routeType="routeType"
                           :currentGroup="group"/>
            <form-preview v-else :key="'formKey'+index"
                          :routeType="routeType"
                          :currentGroup="group"/>
          </div>
        </div>
      </div>
    </ele-card>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
        发布
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, onMounted, watch, reactive,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {getFormGroup,} from "@/views/zizhu/api/form-group-index.js";
import TablePreview from "@/views/personInfo/st/components/table-preview.vue";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {compare} from '@/utils/common_bak2.js';
import {projectPublish} from "@/views/zizhu/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emits = defineEmits(['done', 'update:modelValue']);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentXmId: String,
  routeType: String,
});
console.log("currentXmId===", props.currentXmId)

/** 组数据 */
const data = ref([]);
/** 提交状态 */
const loading = ref(false);
/** 表单配置 */
const config = reactive({
  labelWidth: 120,
  grid: 3
});

/** 查询 */
const query = () => {
  loading.value = true;
  getFormGroup({
    projectId: props.currentXmId,
    type: props.routeType,
  }).then((list) => {
    loading.value = false;
    if (list) {
      list.forEach((item, index) => {
        item.enType = "preview"
        if (item.infoType === 'base') {
          item.sort = 1
        } else if (item.infoType === 'apply') {
          item.sort = 2
          // } else {
          //   item.sort = index + 3
        }
        item.title = item.groupName
      })
    }
    list = list.sort(compare('sort'))
    data.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const onSubmit = () => {
  projectPublish(currentXmId).then((data) => {
    if (data) {
      EleMessage.success('操作成功！');
      updateModelValue(false)
    }
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      query();
    }
  }
);

</script>

<script>
export default {
  name: 'ZIZHUDETAILSFORM'
};
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}
</style>
