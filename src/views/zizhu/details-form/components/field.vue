<template>
  <ele-page class="selector-container" style="padding: 6px 0 0 2px !important;">
  <ele-split-panel ref="splitRef"
                   space="6px"
                   size="230px"
                   :allow-collapse="true"
                   :resizable="true"
                   :responsive="true"
                   flex-table
                   :custom-style="{overflow: 'hidden',borderWidth: '0 1px 0 0'}"
                   :body-style="{overflow: 'hidden',height: (pageHeight-43)+'px'}"
                   :style="{ height: '100%', overflow: 'visible' }">
    <ele-card :body-style="{ padding: '5px 0!important' }">
      <template #header>
        表单组维护
        <ele-tooltip style="cursor: pointer;" effect="light"
                     content="表单组信息维护"
                     placement="left" :offset="3">
          <IconPark name="help" size="18" strokeWidth="3"/>
        </ele-tooltip>
      </template>
      <template #extra>
        <div style="display: flex; align-items: center" @click="openEdit(void 0)">
          <IconPark name="add-one" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            添加
          </ele-text>
        </div>
      </template>
      <div :style="{height: (pageHeight-100)+'px', overflow:'auto'}">
        <ele-loading :loading="loading">
          <el-tree ref="treeRef"
                   :data="data"
                   highlight-current
                   node-key="id"
                   :props="{ label: 'groupName' }"
                   :expand-on-click-node="false"
                   :default-expand-all="true"
                   :filter-node-method="filterNode"
                   :style="{
                              '--ele-tree-item-height': '34px',
                              '--ele-tree-expand-padding': 0,
                              '--ele-tree-expand-margin': 0
                            }"
                   @node-click="handleNodeClick">
            <template #default="{ data: d }">
              <div class="el-tree-node__label">
                <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                  <!-- 核心修改：使用 flex 布局 -->
                  <span class="text-container">
                    <span class="span-wrap">
                      {{ d.groupName }}
                    </span>
                  </span>

                  <!-- 图标容器 -->
                      <span v-if="selectedKey===d.id" class="icon-container">
                         <ele-tooltip style="cursor: pointer;" effect="light"
                                      content="编辑">
                            <a @click="openEdit(d)">
                               <IconPark name="edit-two" size="18" strokeWidth="3" color="#d0021b"/>
                            </a>
                        </ele-tooltip>
                         <ele-tooltip style="cursor: pointer;" effect="light" content="删除">
                            <a @click="remove(d)">
                              <IconPark style="margin-left: 10px;" name="delete" size="18" strokeWidth="3"
                                        color="#d0021b"/>
                            </a>
                         </ele-tooltip>
                      </span>
                </span>
              </div>
            </template>
          </el-tree>
        </ele-loading>
      </div>
    </ele-card>
    <template #body>
      <TablePreview v-if="current" :ref="el => getRiskSpreadRef(el, 0)"
                    :dataName="current.groupName+'字段维护'"
                    approverType="zizhu"
                    :currentNode="current"
                    :approverId="current.id"
                    :RandomString="RandomStringfield"
                    :approverData="proFormData"
                    @onDoneGroup="handleDoneGroup"
                    @child-event="parentMethod"
                    @FieldLinkEvent="handleFieldLinkEvent"/>
    </template>
  </ele-split-panel>
  <fieldGroupEdit v-model="showEdit" :currentXmId="currentXmId" :data="editData" @done="query"/>
  <!-- 字段显示隐藏设置-->
  <fieldLink v-model="showFieldLink" :data="currentFieldLink"/>
    </ele-page>
</template>

<script setup>
import {ref, nextTick, watch, unref, onMounted, reactive,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {SearchOutlined} from '@/components/icons';
import {useMobile} from '@/utils/use-mobile';
import {getFormGroup, removes} from '../../api/form-group-index.js'
import TablePreview from "@/components/DrawFlow/drawer/table-preview.vue";
import fieldGroupEdit from "./field-group-edit.vue"
import {useRouter} from "vue-router";
import {generateRandomString,} from '@/utils/common_bak2.js';
import {
  operation as formTemplatefieldOperation,
} from "@/views/zizhu/api/form-template-field-index.js";
import {getFormTemplateField} from "@/views/zizhu/api/form-template-field-index.js";
import fieldLink from '@/views/system/sphfw/dictionary-field/components/field-link.vue'
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
// 定义emit
const emits = defineEmits(['onDoneGroup', 'childValidMsg', 'childSave'])
const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const routeType = path.split("/")[3];

const props = defineProps({
  currentXmId: String,
  RandomString: String,
  approverData: Array,
});

/** 是否是移动端 */
const {mobile} = useMobile();

/** 分割面板组件 */
const splitRef = ref(null);

/** 树组件 */
const treeRef = ref(null);

/** 加载状态 */
const loading = ref(true);

/** 树形数据 */
const data = ref([]);

/** 选中数据 */
const current = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);


/** 是否打开关联关系设置 */
const showFieldLink = ref(false);
const currentFieldLink = ref(null);


const handleFieldLinkEvent = (data) => {
  console.log('handleFieldLinkEvent', data)
  showFieldLink.value = true
  currentFieldLink.value = data
}

/** 查询 */
const query = () => {
  loading.value = true;
  getFormGroup({
    projectId: props.currentXmId,
    type: routeType,
    infoType: ['list', 'apply'],
    dicGroupIdIsNull: true
  }).then((list) => {
    loading.value = false;
    data.value = list ?? [];
    nextTick(() => {
      handleNodeClick(data.value[0]);
    });
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const RandomStringfield = ref(null)

const parentMethod = (node) => {
  handleNodeClick(node)
  console.log('Parent method called');
}

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  if (row && row.id) {
    current.value = row;
    treeRef.value?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id
    queryFormTemplateField()
    if (props.approverData && current.value) {
      RandomStringfield.value = generateRandomString(10)
    }
  } else {
    current.value = null;
  }
};

/** 查询组的申请字段信息 */
const queryFormTemplateField = () => {
  loading.value = true;
  getFormTemplateField({projectId: props.currentXmId, groupId: current.value.id}).then((list) => {
    loading.value = false;
    proFormData.value = list ?? [];
    RandomStringfield.value = generateRandomString(10)
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};


/** 打开编辑弹窗 */
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = () => {
  const rows = [current.value];
  if (!rows) {
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    {
      type: 'warning',
      draggable: true
    }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      query();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.groupName && data.groupName.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef.value?.filter?.(value);
});

const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};

/** 存放form提交字段*/
const proFormData = ref(null);
/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  validMsg.value = null;
  let result = new Promise((resolve, reject) => {
    formChild.formRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}


/** 存放form提交字段*/
// const proFormGroup = ref([]);
const handleDoneGroup = (data) => {
  console.log(data)
  proFormData.value = data;
  emits('onDoneGroup', proFormData.value);
};

const saveField = async () => {
  console.log('Child method called');
  if (proFormData.value && proFormData.value.length > 0) {
    await riskSpreadRefList.value?.forEach((child) => {
      checkForm(child)
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []//每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save()
        // proFormData.value = data;
        // emits('onDoneGroup', proFormData.value);
        emits('childValidMsg', '');
      } else {
        console.log(2)
        validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
        emits('childValidMsg', validMsg.value);
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  } else {
    EleMessage.error('项目申请字段未设置');
  }
};

const save = () => {
  console.log('save')
  proFormData.value.forEach((row) => {
    for (let i in row) {
      if (typeof row[i] === 'boolean') {
        row[i] = row[i] === true ? '是' : '否'
      }
    }
  })
  loading.value = true;
  formTemplatefieldOperation(proFormData.value).then((msg) => {
    // newRandomStringfield.value = generateRandomString(18)
    loading.value = false;
    if (msg) {
      EleMessage.success("设置成功");
      emits('childSave');
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  })
}

watch(
  () => props.RandomString,
  (RandomString) => {
    console.log('RandomString======', RandomString)
    if (RandomString) {
      query()
      if (props.approverData) {
        proFormData.value = props.approverData
        emits('onDoneGroup', proFormData.value);
      }
    }
  },
  {immediate: true}
)

query();
defineExpose({
  saveField,
});
</script>

<script>
export default {
  name: 'DETAILSFORMFIELD'
};
</script>

<style>

.el-popper.is-customized {
  color: white;
  padding: 6px 12px;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary));
}

.el-popper.is-customized .el-popper__arrow::before {
  color: white;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary));
  right: 0;

}
</style>
