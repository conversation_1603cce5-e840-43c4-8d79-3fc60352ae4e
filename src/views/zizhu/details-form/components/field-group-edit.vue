<!-- 字段组编辑弹窗 -->
<template>
  <ele-drawer :size="610"
              :title="isUpdate ? '修改字段组 ['+data.groupName+']' : '添加字段组'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-position="top"
             label-width="auto"
             @submit.prevent="">
      <el-form-item v-if="form.infoType!=='2'" label="自定义组名" prop="groupName">
        <el-input clearable :maxlength="20"
                  v-model="form.groupName"
                  placeholder="请输入组名称"/>
      </el-form-item>

      <el-row :gutter="8">
        <el-col :md="12" :sm="12" :xs="24">
          <el-form-item v-if="form.infoType!=='1'" label="是否列表" prop="listFlag">
            <el-radio-group v-model="form.listFlag">
              <el-radio-button value="是">是</el-radio-button>
              <el-radio-button value="否">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :md="12" :sm="12" :xs="24">
          <el-form-item v-if="isUpdate" label="排序号" prop="sortNumber">
            <el-input-number :min="0"
                             :max="9999"
                             v-model="form.sort"
                             placeholder="请输入排序号"
                             controls-position="right"
                             class="ele-fluid"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="handleSubmit">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref, onMounted} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation} from '../../api/form-group-index.js'
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {useDictData} from "@/utils/use-dict-data.js";
import {useRouter} from "vue-router";
import {getDictionaryField} from "@/views/system/sphfw/dictionary-field/api/index.js";
import {getDictionaryGroup} from "@/views/system/sphfw/dictionary-group/api/index.js";
import {groupArr} from '@/utils/common_bak2.js';

const emit = defineEmits(['done', 'update:modelValue']);

const userStore = useUserStore();
useDictData(['zizuywlx']);
const {dicts} = storeToRefs(userStore);


const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

let userType = path.split("/")[3];

let dictionaryGroup = ref([])
let dictionaryFieldGroup = ref([])
const listFlag = ref('否')


const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentXmId: String,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  infoType: 'list',
  groupName: '',
  groupList: [],
  formTemplateFields: [],
  listFlag: '否',
  type: userType,
  dicGroupId: '',
  year: '',
  projectId: props.currentXmId,
  sort: void 0,
});

onMounted(() => {
});

/** 表单验证规则 */
const rules = reactive({
  infoType: [
    {
      required: true,
      message: '请选择组数据类型',
      type: 'string',
      trigger: 'blur'
    }
  ],
  groupName: [
    {
      required: true,
      message: '请输入组名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  groupList: [
    {
      required: true,
      message: '请选择数据来源',
      type: 'array',
      trigger: 'change'
    }
  ],
  formTemplateFields: [
    {
      required: true,
      message: '请选择字段',
      type: 'array',
      trigger: 'change'
    }
  ],
  listFlag: [
    {
      required: true,
      message: '请选择是否列表',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 保存编辑 */
const handleSubmit = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    if (form.infoType === '2') {
      form.groupList.forEach((groupId) => {
        console.log(groupId)
        form.groupName = dictionaryGroup.value.filter((d) => d.id === groupId)[0].groupName;
        form.dicGroupId = groupId;
        console.log("form2=======", form)
        save(form)
      })
    } else {
      if (form.formTemplateFields && form.formTemplateFields.length > 0) {
        form.formTemplateFields.forEach((item) => {
          item.projectId = form.projectId;
          item.infoType = form.infoType;
        })
      }
      console.log("else=====", form)
      save(form)
    }
  });
};

const save = (form) => {
  loading.value = true;
  operation(form).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

/** 更新值 */
const updateValue = (value) => {
  let dicUrlData = dicts.value['zizuywlx'];
  if (dicUrlData) {
    dicUrlData.forEach((item) => {
      if (item.type === value) {
        form.year = item.year;
      }
    });
  }
};

const onChangeInfoType = async (event) => {
  form.dicGroupId = '';
  form.groupName = '';
  form.groupList = [];
  form.formTemplateFields = [];
  if (event !== '3') {
    listFlag.value = event === '2' ? '是' : '否'
    queryGroupData()
  }
  if (event === '2') {
    form.listFlag = '是'
  }
}

/**
 * 获取组信息
 */
const queryGroupData = () => {
  let newObj = {
    userType: 'student',
    listFlag: listFlag.value,
  }
  getDictionaryGroup(newObj).then((arr) => {
    dictionaryGroup.value = arr
  });
}

/**
 * 获取组-字段信息
 */
const queryGroupFieldData = (groupIds) => {
  let newObj = {
    userType: 'student',
    groupId: groupIds,
  }
  getDictionaryField(newObj).then((arr) => {
    dictionaryFieldGroup.value = groupArr(arr, 'groupId')
  });
}

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => form.groupList,
  (groupList) => {
    dictionaryFieldGroup.value = []
    if (groupList && form.infoType === '1') {
      console.log("watch====", groupList)
      if (groupList.join()) queryGroupFieldData(groupList.join())
    }
  }
);

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      queryGroupData();
      if (props.data) {
        assignFields(props.data);
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);

</script>
