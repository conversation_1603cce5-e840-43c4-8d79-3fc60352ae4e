<template>
  <ele-page flex-table style="padding: 0!important;">
<!--    <ele-card :body-style="{padding: '0px !important' , overflow: 'hidden' }">-->
<!--      <template #header>-->
<!--        <ele-tabs type="plain"-->
<!--                  size="small"-->
<!--                  v-model="active"-->
<!--                  :items="tabsItems" @tabClick="onFormTabChange">-->
<!--          <template #label="{ item, label}">-->
<!--            <ele-text>-->
<!--              &lt;!&ndash;  <el-avatar :size="20" shape="square"&ndash;&gt;-->
<!--              &lt;!&ndash;                              :style="'background-color:'+(!item.disabled? 'var(&#45;&#45;el-color-primary)':'')">&ndash;&gt;-->
<!--              &lt;!&ndash;                  {{ item.sort }}&ndash;&gt;-->
<!--              &lt;!&ndash;                </el-avatar>&ndash;&gt;-->
<!--              &lt;!&ndash;              {{ label }}&ndash;&gt;-->
<!--              <el-button type="primary" plain circle size="small"> {{ item.sort }}</el-button>-->
<!--              {{ label }}-->
<!--            </ele-text>-->
<!--          </template>-->
<!--        </ele-tabs>-->
<!--      </template>-->
<!--      <template #extra>-->
<!--        &lt;!&ndash;        <el-button size="small" plain type="primary" @click="handlePreview">&ndash;&gt;-->
<!--        &lt;!&ndash;          申请表单预览&ndash;&gt;-->
<!--        &lt;!&ndash;        </el-button>&ndash;&gt;-->
<!--        <el-button size="small" plain type="primary" @click="handleProjectPublish">-->
<!--          发布-->
<!--        </el-button>-->
<!--      </template>-->
<!--      &lt;!&ndash;      <div ref="containerRef" :style="{height: pageHeight+'px',overflow:'auto'}">&ndash;&gt;-->
<!--      <div v-if="active === 'info'&&RandomString">-->
<!--        <SetPerSelector paramMode="zzbdweh"-->
<!--                        hearder="项目基础设置"-->
<!--                        :perSelectedData="perSelectedData"-->
<!--                        :RandomString="RandomString"-->
<!--                        :currentXmId="currentXmId"-->
<!--                        :userType="userType"-->
<!--                        :pageHeight="pageHeight-10"-->
<!--                        @done="onDoneSelector"/>-->
<!--      </div>-->
<!--      <div v-if="active === 'field'">-->
<!--        <DetailsFormfield ref="childFieldRef"-->
<!--                          :RandomString="newRandomStringfield?newRandomStringfield:RandomStringfield"-->
<!--                          :currentXmId="currentXmId"-->
<!--                          :approverData="formData"-->
<!--                          @childValidMsg="handleChildValidMsg"/>-->
<!--        &lt;!&ndash;        @onDoneGroup="handleDoneGroup"&ndash;&gt;-->
<!--      </div>-->
<!--      <div v-if="active === 'workflow'">-->
<!--        <ele-alert effect="light" v-if="!projectWorkFlow" :closable="false" style="margin: 5px;padding: 8px!important;">-->
<!--            <span-->
<!--              style="font-size: 12px;">项目审核流程设置系统提供两种方式，方式一您可以选择使用系统默认流程；方式二您如需要自定义流程，请点击创建流程模板按钮，系统会自动生成流程模板，您可以根据流程模板进行修改，修改完成后点击保存按钮，即可完成流程模板的创建。</span>-->
<!--        </ele-alert>-->
<!--        <el-form v-if="!projectWorkFlow" size="small" @submit.prevent="" style="margin: 10px 5px 0 5px!important">-->
<!--          <el-row>-->
<!--            <el-col  :md="6" :sm="12" :xs="24">-->
<!--              <el-form-item label="方式一：请选择现有审核流程">-->
<!--                <el-select v-model="workFlowSelected" class="ele-fluid" @change="workFlowSelectedChange">-->
<!--                  <el-option v-for="wflow in workFlowData" :key="wflow.id" :value="wflow.id" :label="wflow.name"/>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--            <el-col :lg="1" :md="12" :sm="12" :xs="24">-->
<!--            </el-col>-->
<!--             <el-col  :md="6" :sm="12" :xs="24">-->
<!--              <el-form-item label="方式二：">-->
<!--                <el-button type="primary" plain @click="createWorkFlow">新建流程模板</el-button>-->
<!--              </el-form-item>-->
<!--            </el-col>-->
<!--          </el-row>-->
<!--        </el-form>-->

<!--        <Workflow v-if="workFlow" :pageHeight="projectWorkFlow?pageHeight:pageHeight-110"-->
<!--                  :workflowId="workFlowSelected?workFlowSelected:workFlowSelectedInit"-->
<!--                  :currentProject="currentProject"-->
<!--                  @update:nodeConfig="handleDoneNodeConfig"/>-->
<!--      </div>-->
<!--      &lt;!&ndash;      </div>&ndash;&gt;-->
<!--    </ele-card>-->
<!--    &lt;!&ndash; 底部工具栏 &ndash;&gt;-->
<!--    <template #footer>-->
<!--      <ele-text style="float: left;" v-if="validMsg&&active === 'field'" type="danger" :icon="CloseCircleOutlined">-->
<!--        <span>{{ validMsg }}</span>-->
<!--      </ele-text>-->
<!--      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>-->
<!--      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">-->
<!--        保存-->
<!--      </el-button>-->
<!--    </template>-->
<!--    <DETAILSYL v-model="showPreview" :currentXmId="currentXmId" :routeType="routeType"/>-->
<!--    &lt;!&ndash;    <edit v-model="Preview" :currentXmId="currentXmId" :routeType="routeType" enterType="preview" @done="reload"/>&ndash;&gt;-->
  </ele-page>
</template>

<script setup>
import {ref, computed,  unref, } from 'vue';
import {useUserStore} from '@/store/modules/user';
import {getWorkFlow,} from "@/views/dingding-flow/api/index.js";
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {generateRandomString, groupArr,} from '@/utils/common_bak2.js';
import {editBaseInfo,} from "@/views/zizhu/api/form-group-index.js";
import {mapMutations,} from "@/plugins/lib.js";
import {getProjectWorkFlow, operationWorkflow, projectPublish} from "@/views/zizhu/api/index.js";
import {storeToRefs} from "pinia";

let {setSharedZizhuData} = mapMutations();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {removePageTab, getRouteTabKey, addPageTab} = usePageTab();
const {setIsTried} = mapMutations()

const emits = defineEmits(['done', 'update:modelValue']);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentProject: Object,
  currentXmId: String,
});

const {currentRoute, push} = useRouter();
const {path,} = unref(currentRoute);
console.log(path.split("/"))
let routeType = path.split("/")[3];
// let currentXmId = props.xmId ? props.xmId : path.split("/")[4];
let userType = props.currentProject?.userType;
console.log(props.currentXmId)

/** 提交状态 */
const loading = ref(false);

/** 标签页选中 */
const active = ref('info');

const workFlow = ref(false);
const workFlowData = ref([]);
const workFlowSelected = ref(null);
const workFlowSelectedInit = ref(null);
const newRandomStringfield = ref(null);

const tipList = ref([]);
const tipVisible = ref(false);
/** 已绑定的工作流 */
const projectWorkFlow = ref(null);

const perSelectedData = ref([]);
const onDoneSelector = (data) => {
  perSelectedData.value = data;
};

/** 新建流程模板 */
const createWorkFlow = () => {
  workFlow.value = true;
  workFlowSelectedInit.value = 'workFlowSelected';
  workFlowSelected.value = null;
};

/** 选择编辑流程模版 */
const workFlowSelectedChange = (event) => {
  if (event) {
    workFlowSelectedInit.value = null;
    workFlow.value = true
  }
};
/** 表单资助申请字段 */
const formData = ref(null);
const validMsg = ref(null);
// const handleDoneGroup = (data, validMsg) => {
//   formData.value = data ?? []
// };

const handleChildValidMsg = (data) => {
  validMsg.value = data;
};


/** 是否显示编辑弹窗 */
const showPreview = ref(false);

const handlePreview = () => {
  showPreview.value = true;
};
/** 流程配置 */
const processConfig = ref({});
const handleDoneNodeConfig = (data) => {
  processConfig.value = data
};

const getWorkFlowById = () => {
  getWorkFlow().then((list) => {
    if (list) {
      workFlowData.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
/**
 * 项目发布
 */
const handleProjectPublish = () => {
  projectPublish(props.currentXmId).then((data) => {
    if (data) {
      EleMessage.success('操作成功！');
      updateModelValue(false)
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
const queryProjectWorkFlow = () => {
  console.log(props.currentXmId)
  getProjectWorkFlow(props.currentXmId).then((list) => {
    if (list) {
      projectWorkFlow.value = list;
      workFlowSelected.value = list.id
      workFlowSelectedChange(true)
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

const childFieldRef = ref(null);
const onSubmit = async () => {
  if (active.value === 'info') {
    let setInfoData = groupArr(perSelectedData.value, 'configTitle', 'configKey');
    let newFromArray = []
    setInfoData.forEach((item) => {
      if (item.fieldId === 'zzdtxx') {//多条信息，直接循环创建组信息
        item.list.forEach((ilist) => {
          let form = {
            infoType: 'list',
            groupName: ilist.name,
            dicGroupId: ilist.value,
            listFlag: '是',
            type: routeType,
            projectId: props.currentXmId,
          }
          newFromArray.push(form)
        })
      } else if (item.fieldId === 'zzjbxx') {//基本信息，重新组装数据（formTemplateFields）
        item.list.forEach((ilist) => {
          ilist.projectId = props.currentXmId;
          ilist.infoType = 'base';
        })
        let form = {
          infoType: 'base',
          groupName: item.type,
          dicGroupId: '',
          formTemplateFields: item.list,
          listFlag: '否',
          type: routeType,
          projectId: props.currentXmId,
        }
        newFromArray.push(form)
      }
    })
    save(newFromArray)
  } else if (active.value === 'field') {
    childFieldRef.value.saveField()
    // formData.value.forEach((row) => {
    //   for (let i in row) {
    //     if (typeof row[i] === 'boolean') {
    //       row[i] = row[i] === true ? '是' : '否'
    //     }
    //   }
    // })
    // loading.value = true;
    // formTemplatefieldOperation(formData.value).then((msg) => {
    //   newRandomStringfield.value = generateRandomString(18)
    //   loading.value = false;
    //   EleMessage.success(msg);
    // }).catch((e) => {
    //   loading.value = false;
    //   EleMessage.error(e.message);
    // });
  } else if (active.value === 'workflow') {
    saveWorkFlow()
  }
}

const reErr = ({childNode}) => {
  if (childNode) {
    let {type, error, nodeName, conditionNodes} = childNode;
    if (type === 1 || type === 2) {
      if (error) {
        tipList.value.push({
          name: nodeName,
          type: ["", "审核人", "抄送人"][type],
        });
      }
      reErr(childNode);
    } else if (type === 3) {
      reErr(childNode);
    } else if (type === 4) {
      reErr(childNode);
      for (var i = 0; i < conditionNodes.length; i++) {
        if (conditionNodes[i].error) {
          tipList.value.push({name: conditionNodes[i].nodeName, type: "条件"});
        }
        reErr(conditionNodes[i]);
      }
    }
  } else {
    childNode = null;
  }
};
const saveWorkFlow = async () => {
  setIsTried(true);
  tipList.value = [];
  reErr(processConfig.value.nodeConfig);
  if (tipList.value.length != 0) {
    tipVisible.value = true;
    return;
  }
  let FlowData = processConfig.value;
  let workflowData = {
    id: workFlowSelected.value ? workFlowSelected.value : workFlowSelectedInit.value,
    name: FlowData.nodeConfig.nodeName,//流程名称
    projectId: props.currentXmId,//项目ID
    year: '',//年份
    workflowNodes: FlowData.nodeConfig.childNode
  }
  // console.log("workflowData=====", workflowData)
  // console.log("workflowData===JSON==", JSON.stringify(workflowData))
  loading.value = true;
  operationWorkflow(props.currentXmId, workflowData).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    queryProjectWorkFlow();
    getWorkFlowById();
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
};

const save = (form) => {
  if (form.length > 0) {
    loading.value = true;
    editBaseInfo(props.currentXmId, form).then((msg) => {
      loading.value = false;
      EleMessage.success("设置成功");
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  } else {
    EleMessage.error('你选择你要设置的参数信息');
  }
}

const tabsItems = computed(() => {
  return [
    {
      label: '项目基础设置',
      name: 'info',
      disabled: false,
      sort: 1
    },
    {
      label: '申请字段设置',
      name: 'field',
      sort: 2
    },
    {
      label: '审核流程设置',
      name: 'workflow',
      sort: 3
    },
  ]
})

const RandomString = computed(() => {
  return active.value === 'info' ? generateRandomString(10) : ''
})

const RandomStringfield = computed(() => {
  return active.value === 'field' ? generateRandomString(18) : ''
})

/** tab选择改变事件 */
const onFormTabChange = (event) => {
  projectWorkFlow.value = null;
  workFlowSelected.value = null
  workFlowData.value = null;
  workFlowSelectedInit.value = null;
  workFlow.value = false
  if (event.props.name === 'workflow') {
    queryProjectWorkFlow();
    getWorkFlowById();
  }
};

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  let json = {}
  if (props.currentXmId) json.xmId = props.currentXmId
  let path = enterType === 'list' ? '/zizhu/info/' : '/zizhu/details/'
  setSharedZizhuData(json)
  push({
    path: path + routeType,
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  active.value = 'info';
  userType = null;
  formData.value = [];
  emits('update:modelValue', value);
};

</script>

<script>
export default {
  name: 'ZIZHUDETAILSFORM'
};
</script>
