<template>
  <ele-page class="project-settings-container" hide-footer flex-table style="padding: 8px 8px 8px 14px !important">
    <ele-split-panel
      ref="splitRef"
      space="6px"
      size="18%"
      :allow-collapse="true"
      :resizable="true"
      :responsive="true"
      flex-table
      allow-collapse
      :custom-style="{borderWidth: '0 1px 0 0', width: '100%'}"
      :body-style="{
        overflow: 'hidden',
        borderWidth: '0 1px 0 0',
        height: 'auto'
      }"
      :style="{ height: '100%', overflow: 'visible' }">
      <!-- 左侧导航面板 -->
      <ele-card class="navigation-card"
                header="项目配置"
                :body-style="{
                  height: (pageHeight+16)+'px',
                  overflow:'auto',
                  padding: '12px 8px'
                }">

        <template #extra>
          <el-text style="padding-left: 24px;" text @click="onBack" class="back-btn">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <span>返回</span>
          </el-text>
        </template>
        <ele-check-card v-model="activeTab"
                        :items="navigationItems"
                        :row="{ gutter: 12 }"
                        class="navigation-menu">
          <template #item="{ item }">
            <div class="nav-item" :class="{ 'disabled': item.disabled }">
              <div class="nav-item-content">
                <div class="nav-icon-wrapper">
                  <el-icon class="nav-icon">
                    <component :is="getNavIcon(item.value)"/>
                  </el-icon>
                </div>
                <div class="nav-text">
                  <ele-text size="md" class="nav-title">{{ item.title }}</ele-text>
                  <ele-text v-if="item.description"
                            size="sm"
                            type="placeholder"
                            class="nav-description">
                    {{ item.description }}
                  </ele-text>
                </div>
              </div>
              <!--              <el-tag v-if="item.disabled"-->
              <!--                      size="small"-->
              <!--                      type="info"-->
              <!--                      class="nav-tag">-->
              <!--                请先创建项目-->
              <!--              </el-tag>-->
            </div>
          </template>
        </ele-check-card>
      </ele-card>
      <!-- 右侧内容面板 -->
      <template #body>
        <div class="content-panel">
          <component :is="activeTab"
                     :currentProject="currentProjectData"
                     :pageHeight="pageHeight"
                     :currentId="params.xmId"
                     :RandomString="RandomString"
                     class="settings-content"/>
        </div>
      </template>
    </ele-split-panel>
  </ele-page>
</template>

<script setup>
import {ref, computed, unref} from 'vue';
import {useRouter} from 'vue-router';
import {
  FolderOpened, Setting, Document,
  Warning, User, Star, Timer,
} from '@element-plus/icons-vue';
import DetailsForm from '@/views/zizhu/components/setting/details-form.vue';
import ConditionfFormRestrict from '@/views/zizhu/components/setting/condition-form-restrict.vue';
import FormLimitQuota from '@/views/zizhu/form-limit-quota/index.vue';
import Xxinfo from '@/views/zizhu/components/setting/xxinfo.vue';
import TsmdInfo from '@/views/zizhu/project-special-list/index.vue';
import FormProjectTemplateField from '@/views/zizhu/form-project-template-field/index.vue';
import WorkflowNode from '@/views/evaluate/evaluate-config/workflow-node/components/card.vue';
import {getFormProjectById} from '@/views/zizhu/api/index.js';
import {ElMessage as EleMessage} from 'element-plus';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import IconPark from '@/components/IconPark/index.vue';
import {usePageTab} from '@/utils/use-page-tab.js';
import {generateRandomString} from '@/utils/common_bak2.js';

const {pageHeight} = storeToRefs(useUserStore());
const {currentRoute, push} = useRouter();
const {params, query, path} = unref(currentRoute);
const enterType = query?.enterType;
let routeType = path.split('/')[3];
const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();

// 响应式数据
const activeTab = ref(enterType || 'xxinfo');
const currentProjectData = ref(null);
const loading = ref(false);

const RandomString = ref(null);
/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/zizhu/info/' + routeType);
};

// 导航菜单数据
const navigationData = [
  {
    value: 'xxinfo',
    title: '项目维护',
    description: '项目基础信息维护及设置',
  },
  {
    value: 'details-form',
    title: '申请表单',
    description: '表单字段及审核流程设置',
  },
  {
    value: 'workflow-node',
    title: '批量更新审核时间',
    description: '批量自定义设置审核时间',
  },
  {
    value: 'FormProjectTemplateField',
    title: '拓展信息',
    description: '自定义项目表单字段配置',
  },
  {
    value: 'ConditionfFormRestrict',
    title: '限制条件',
    description: '设置项目申请的限制条件',
  },
  {
    value: 'FormLimitQuota',
    title: '名额设置',
    description: '设置项目申请名额限制',
  },
  {
    value: 'TsmdInfo',
    title: '特殊名单',
    description: '管理项目特殊名单',
  },
];

// 获取导航图标
const getNavIcon = (value) => {
  const icons = {
    'xxinfo': FolderOpened,
    'FormProjectTemplateField': Setting,
    'details-form': Document,
    'ConditionfFormRestrict': Warning,
    'FormLimitQuota': User,
    'TsmdInfo': Star,
    'workflow-node': Timer,
  };
  return icons[value] || Setting;
};

// 计算属性
const navigationItems = computed(() => {
  return navigationData.map((item, index) => ({
    ...item,
    index: index + 1,
    disabled: item.value !== 'xxinfo' && !params?.xmId,
    col: {
      md: 24,
      sm: 24,
      xs: 24,
      style: {marginBottom: '12px'},
    },
  }));
});

// 方法
const queryFormProjectById = async () => {
  if (!params?.xmId) return;

  loading.value = true;
  try {
    currentProjectData.value = await getFormProjectById(params.xmId);
  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    loading.value = false;
  }
};

// 初始化
if (params?.xmId) {
  RandomString.value = generateRandomString(10)
  queryFormProjectById();
}

defineOptions({
  name: 'ZIZHUDETAILS',
  components: {
    Xxinfo,
    DetailsForm,
    TsmdInfo,
    ConditionfFormRestrict,
    FormLimitQuota,
    FormProjectTemplateField,
    WorkflowNode,
    // 图标组件
    FolderOpened, Setting, Document, Warning, User, Star, Timer,
  },
});
</script>

<style lang="scss" scoped>
.project-settings-container {
  height: 100%;
  //background-color: #f8fafc;

  :deep(.ele-split-panel) {
    .navigation-card {
      border-radius: 0;
      box-shadow: none;
      //border-right: 1px solid var(--el-border-color-light);
      //background-color: #f8fafc;

      :deep(.el-card__header) {
        padding: 14px 16px;
        background-color: #f8fafc;
        border-bottom: 1px solid var(--el-border-color-light);
        font-weight: 500;
        font-size: 15px;
        color: #2d3748;
      }

      .navigation-menu {
        margin: 0 6px;

        :deep(.ele-check-card-item) {
          border-radius: 8px;
          border: 1px solid var(--el-border-color-light);
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          background-color: #fff;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
          padding: 0;
          overflow: hidden;

          &:hover {
            border-color: var(--el-color-primary-light-5);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
          }

          &.is-checked {
            border-color: var(--el-color-primary);
            background-color: rgba(var(--el-color-primary-rgb), 0.05);

            .nav-icon {
              color: var(--el-color-primary);
              background-color: rgba(var(--el-color-primary-rgb), 0.1);
            }
          }

          &.is-disabled {
            opacity: 0.7;
            cursor: not-allowed;

            &:hover {
              border-color: var(--el-border-color-light);
              box-shadow: none;
              transform: none;
            }
          }
        }

        .nav-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          min-height: 56px;

          &-content {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
          }

          .nav-icon-wrapper {
            margin-right: 12px;
            flex-shrink: 0;

            .nav-icon {
              width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 8px;
              background-color: #f1f5f9;
              color: #64748b;
              font-size: 16px;
              transition: all 0.25s ease;
            }
          }

          .nav-text {
            flex: 1;
            min-width: 0;
          }

          .nav-title {
            font-weight: 500;
            margin-bottom: 4px;
            display: block;
            color: #1e293b;
            font-size: 14px;
          }

          .nav-description {
            display: block;
            line-height: 1.4;
            white-space: normal;
            color: #64748b;
            font-size: 12px;
          }

          .nav-tag {
            margin-left: 8px;
            flex-shrink: 0;
            background-color: #f1f5f9;
            color: #64748b;
            border: none;
          }
        }
      }
    }

    .content-panel {
      height: 100%;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      .settings-content {
        height: 100%;
        padding: 16px 20px;
        overflow-y: auto;
      }
    }
  }
}

@media (max-width: 992px) {
  .project-settings-container {
    :deep(.ele-split-panel) {
      flex-direction: column;

      .navigation-card {
        border-right: none;
        border-bottom: 1px solid var(--el-border-color-light);

        :deep(.el-card__body) {
          height: auto !important;
          max-height: 240px;
        }
      }

      .content-panel {
        border-radius: 0;
      }
    }
  }
}
</style>
