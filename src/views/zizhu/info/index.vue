<template>
  <ele-page hide-footer flex-table>
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :border="true"
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            size="small"
            class="ele-btn-icon"
            @click="openEdit(null, 'xxinfo')"
          >
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
          <el-input
            style="width: 260px; margin-left: 14px"
            size="small"
            v-model="nameFilterValue"
            placeholder="请输入你要查询的项目名称"
            clearable
            :suffix-icon="Search"
            @blur="onNameFilter"
          />

          <!--          <el-input label="项目名称" placeholder="请输入你要查询的项目名称"/>-->
        </template>
        <template #xmmc="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'xxinfo')"
          >
            {{ row.xmmc }}
          </el-link>
        </template>
        <template #kzzd="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'FormProjectTemplateField')"
          >
            拓展
          </el-link>
        </template>
        <template #bd="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'details-form')"
          >
            <!--                   @click.stop="openFormEdit(row,'details-form')">-->
            设计
          </el-link>
          <el-link type="info" underline="never" @click.stop="openEdit(row)">
            / 复制
          </el-link>
        </template>
        <template #szme="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'FormLimitQuota')"
          >
            <!--                   @click.stop="handleSzme(row)">-->
            设置
          </el-link>
        </template>
        <template #tsmd="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'TsmdInfo')"
          >
            <!--                   @click.stop="handleProjectSpecial(row)">-->
            {{ row?.specialListCount }}
          </el-link>
        </template>
        <template #lssj="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openImportLssj(row)"
          >
            Excel
          </el-link>
        </template>
        <template #sqb="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openImport(row, 'sqb')"
          >
            Excel
          </el-link>
        </template>
        <template #hzb="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openImport(row, 'hzb')"
          >
            Excel
          </el-link>
        </template>
        <template #sqxztj="{ row }">
          <el-link
            type="primary"
            underline="never"
            @click.stop="openEdit(row, 'ConditionfFormRestrict')"
          >
            <!--                   @click.stop="openSqxztj(row)">-->
            {{ row?.restrictCount }}
          </el-link>
        </template>
        <template #scxzmd="{ row }">
          <el-link type="info" underline="never" @click.stop="openEdit(row)">
            办理
          </el-link>
        </template>

        <template #action="{ row }">
          <!--                    <el-divider direction="vertical"/>-->
          <!--          <el-link type="primary" underline="never" @click="openEdit(row)">-->
          <!--            编辑-->
          <!--          </el-link>-->
          <template
            v-if="row.spzt === '待审批' && currentRoles.roleScope === '全校'"
          >
            <el-divider direction="vertical" />
            <el-link type="primary" underline="never" @click="onYxXmqkb(row)">
              审批
            </el-link>
            <el-divider direction="vertical" />
          </template>
          <!--          <el-link type="info" underline="never" @click="remove(row)">-->
          <el-link type="info" underline="never"> 通知 </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!--    <DetailsForm v-model="showDetailsForm" :currentXmId="currentXmId" :currentProject="currentProject"/>-->
    <Import
      v-model="showImport"
      :currentProject="currentProject"
      :currentType="currentType"
      @done="reload"
    />
    <ImportLssj
      v-model="showImportLssj"
      :currentProject="currentProject"
      @done="reload"
    />
    <ConditionfFormRestrict
      v-model="showSqxztjForm"
      :currentProject="currentProject"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { ElNotification } from 'element-plus/es';
  import { Search } from '@element-plus/icons-vue';
  import { computed, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';

  import { projectApprove, queryPage, removes } from '../api/index.js';

  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab';
  import { mapState } from '@/plugins/lib.js';
  import Import from '../components/import.vue';
  import ImportLssj from '../components/import-lssj.vue';
  import DetailsForm from '../details-form/index.vue';
  import ConditionfFormRestrict from '../condition/form-restrict/index.vue';
  import { getCurrentRole } from '@/utils/current-role-util.js';

  const { removePageTab, getRouteTabKey, addPageTab, setPageTabTitle } =
    usePageTab();

  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  let currentProject = null;
  let currentXmId = null;
  let currentType = null;
  let routeType = null;
  let xmId = null;

  console.log(11111);
  let { sharedZizhuData } = mapState();

  let params = computed({
    get() {
      return sharedZizhuData.value;
    }
  });
  console.log(22222);
  console.log(params);
  console.log(33333);
  xmId = params.value?.xmId;

  console.log(xmId);
  routeType = routeType ? routeType : path.split('/')[3];

  /** 表格实例 */
  const tableRef = ref(null);

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  const showImportLssj = ref(false);
  const showDetailsForm = ref(false);
  const showSqxztjForm = ref(false);

  /** 打开导入弹窗 */
  const openImport = (row, type) => {
    currentProject = row;
    currentType = type;
    showImport.value = true;
  };

  /** 打开历史数据导入弹窗 */
  const openImportLssj = (row) => {
    currentProject = row;
    showImportLssj.value = true;
  };

  /** 打开申请限制条件弹窗 */
  const openSqxztj = (row) => {
    currentProject = row;
    showSqxztjForm.value = true;
  };

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'xmmc',
      slot: 'xmmc',
      label: '项目名称',
      minWidth: 110
    },
    // {
    //   prop: 'year',
    //   label: '年份',
    // },
    {
      prop: 'kzzd',
      label: '拓展信息',
      slot: 'kzzd'
    },
    {
      prop: 'bd',
      label: '表单',
      slot: 'bd'
    },
    {
      prop: 'lssj',
      label: '数据导入',
      slot: 'lssj'
    },
    {
      prop: 'sqb',
      label: '申请表',
      slot: 'sqb'
    },
    {
      prop: 'hzb',
      label: '汇总表',
      slot: 'hzb'
    },
    {
      prop: 'szme',
      label: '设置名额',
      slot: 'szme'
    },
    {
      prop: 'sqxztj',
      label: '限制条件',
      slot: 'sqxztj'
    },
    {
      prop: 'tsmd',
      label: '特殊名单',
      slot: 'tsmd'
    },
    // {
    //   prop: 'scxzmd',
    //   label: '生成限制名单',
    //   slot: 'scxzmd'
    // },
    {
      prop: 'sqsj',
      label: '可申请时间',
      formatter: (row) => {
        return row.sqkssj && row.sqjzsj
          ? row.sqkssj + '至' + row.sqjzsj
          : '无限制';
      }
    },
    {
      prop: 'spzt',
      label: '审核状态'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action'
    }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    //默认排序
    // orders = Object.keys(orders).length > 0 ? orders : {
    //     sort: 'sortNumber',
    //     order: 'asc'
    // }
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType
    });
  };

  const handleClickEvent = (type) => {
    console.log(type);
    if (type === 'add') openEdit();
  };

  //审核学院添加的项目
  const onYxXmqkb = (row) => {
    ElMessageBox.confirm('审核学院添加的项目“' + row.xmmc + '”吗?', '通过', {
      distinguishCancelAndClose: true,
      confirmButtonText: '通过',
      cancelButtonText: '不通过'
    })
      .then(() => {
        console.log('saved');
        handleProjectApprove({ id: row.id, spzt: '通过' });
      })
      .catch((action) => {
        if (action === 'cancel')
          handleProjectApprove({ id: row.id, spzt: '不通过' });
      });
  };
  const handleProjectApprove = (obj) => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    projectApprove(obj)
      .then((msg) => {
        loading.close();
        EleMessage.success(msg);
        reload();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.message);
      });
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row, enterType) => {
    let path = row
      ? `/zizhu/details/${routeType}/${row.id}`
      : `/zizhu/details/${routeType}`;
    addPageTab({
      title: row ? `${row.xmmc}-项目设置` : '新建项目',
      key: path,
      closable: true
    });
    push({
      path: path,
      query: { enterType: enterType }
    });
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      // EleMessage.error('请至少选择一条数据');
      ElNotification({
        title: '系统提示',
        message: '请至少选择一条数据 ',
        type: 'warning',
        duration: 2500,
        position: 'top-right'
      });
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.xmmc).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 用户名筛选事件 */
  const onNameFilter = () => {
    doReload();
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        xmmc: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'ZUZHUINDEX'
  };
</script>
