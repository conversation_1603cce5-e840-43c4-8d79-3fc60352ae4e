<!--通用表单业务类型-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #eventName="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.eventName }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #operation="{ row }">
          <el-button size="small" type="primary" link @click="openEdit(row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" link @click="remove(row)">
            删除
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { queryWorkflowEventPage, removes } from './api';

  import { useRouter } from 'vue-router';
  import Edit from './components/edit.vue';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[3];

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'eventName',
      slot: 'eventName',
      label: '事件名称',
      width: 150
    },
    {
      prop: 'eventType',
      label: '事件类型',
      width: 120
    },
    {
      prop: 'enabled',
      label: '启用状态',
      width: 80,
      formatter: (row) => (row.enabled ? '启用' : '禁用')
    },
    {
      prop: 'eventClassName',
      label: '触发service',
      width: 180,
      showOverflowTooltip: true,
      formatter: (row) => {
        const serviceMap = {
          applicationItemDetailScoreCheck: '申请项目明细分数检查',
          workflowEventMessageObserver: '工作流事件消息观察者'
        };
        return serviceMap[row.eventClassName] || row.eventClassName || '-';
      }
    },
    {
      prop: 'configInfo',
      label: '配置信息',
      showOverflowTooltip: true,
      formatter: (row) => {
        if (row.eventType === '消息通知') {
          const receiverTypeMap = {
            APPLICANT: '申请人',
            APPROVER: '审批人',
            BOTH: '申请人和审批人',
            CUSTOM: '自定义接收人'
          };
          const receiverType =
            receiverTypeMap[row.receiverType] || row.receiverType;

          if (row.receiverType === 'BOTH') {
            // 对于BOTH类型，显示更详细的配置信息
            const applicantConfigured =
              row.applicantMessage && row.applicantMessageTitle;
            const approverConfigured =
              row.approverMessage && row.approverMessageTitle;
            return `接收者：${receiverType} | 申请人消息：${applicantConfigured ? '已配置' : '未配置'} | 审批人消息：${approverConfigured ? '已配置' : '未配置'}`;
          } else if (row.receiverType === 'CUSTOM') {
            return `接收者：${receiverType} | 自定义接收人：${row.customReceivers || '未设置'} | 标题：${row.messageTitle || '未设置'}`;
          } else {
            return `接收者：${receiverType || '未设置'} | 标题：${row.messageTitle || '未设置'}`;
          }
        } else if (row.eventType === '字段更新') {
          return `表名：${row.setTable || '未设置'} | 表达式：${row.setExpression || '未设置'}`;
        } else if (row.eventType === '审核表单校验') {
          return `校验表达式：${row.whereExpression || '未设置'}`;
        } else {
          return `消息：${row.message ? '已配置' : '未配置'} | 条件：${row.whereExpression ? '已配置' : '未配置'}`;
        }
      }
    },
    {
      prop: 'mobilePath',
      label: '移动端路径',
      width: 120,
      showOverflowTooltip: true,
      formatter: (row) => {
        return (
          row.mobilePath || (row.eventType === '消息通知' ? '未设置' : '-')
        );
      }
    },
    {
      prop: 'pcPath',
      label: 'PC端路径',
      width: 120,
      showOverflowTooltip: true,
      formatter: (row) => {
        return row.pcPath || (row.eventType === '消息通知' ? '未设置' : '-');
      }
    },
    {
      prop: 'remark',
      label: '备注',
      width: 100,
      showOverflowTooltip: true,
      formatter: (row) => row.remark || '-'
    },
    {
      prop: 'operation',
      label: '操作',
      width: 120,
      align: 'center',
      fixed: 'right',
      slot: 'operation'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryWorkflowEventPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除"' + rows.map((d) => d.eventName).join(', ') + '"吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SelectorConfig'
  };
</script>
