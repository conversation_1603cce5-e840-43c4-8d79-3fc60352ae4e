/**
 * 工作流管理模块事件配置
 * */
import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getWorkflowEvent(params) {
    const res = await request.get('/workflow/workflow-event', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryWorkflowEventPage(params) {
    const res = await request.get('/workflow/workflow-event/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getWorkflowEventById(id) {
    const res = await request.get('/workflow/workflow-event/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改工作流管理模块事件配置（权限标识：workflow:workflowEvent:operation）
 */
export async function operation(data) {
    const res = await request.post('/workflow/workflow-event/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/workflow/workflow-event/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
