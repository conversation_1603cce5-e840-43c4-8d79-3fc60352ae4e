<template>
  <div class="variable-helper">
    <el-button
      type="info"
      :icon="InfoFilled"
      size="small"
      @click="showDialog = true"
      style="margin-left: 8px;"
    >
      变量助手
    </el-button>

    <el-dialog
      v-model="showDialog"
      title="消息模板变量助手"
      width="600px"
      draggable
    >
      <div class="variable-content">
        <el-alert
          title="使用说明"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 16px;"
        >
          <template #default>
            点击下方变量名称可直接插入到消息模板中。变量格式：<code>${变量名}</code>
          </template>
        </el-alert>

        <el-input
          v-model="searchText"
          placeholder="搜索变量..."
          :prefix-icon="Search"
          style="margin-bottom: 16px;"
          clearable
        />

        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="申请相关" name="application">
            <div class="variable-list">
              <div
                v-for="variable in filteredVariables.application"
                :key="variable.name"
                class="variable-item"
                @click="insertVariable(variable.name)"
              >
                <div class="variable-name">${{variable.name}}</div>
                <div class="variable-desc">{{variable.description}}</div>
                <div class="variable-example" v-if="variable.example">
                  示例：{{variable.example}}
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="审批相关" name="approval">
            <div class="variable-list">
              <div
                v-for="variable in filteredVariables.approval"
                :key="variable.name"
                class="variable-item"
                @click="insertVariable(variable.name)"
              >
                <div class="variable-name">${{variable.name}}</div>
                <div class="variable-desc">{{variable.description}}</div>
                <div class="variable-example" v-if="variable.example">
                  示例：{{variable.example}}
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <el-button @click="showDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { InfoFilled, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request.js'

const emit = defineEmits(['insert-variable'])

const showDialog = ref(false)
const searchText = ref('')
const activeTab = ref('application')

// 模板变量定义
const variables = ref({
  application: [],
  approval: [],
  system: []
})

// 获取变量列表
const getTemplateVariables = async () => {
  variables.value = getDefaultVariables()
}

// 获取默认变量
const getDefaultVariables = () => ({
  application: [
    { name: '申请人姓名', description: '发起申请的用户姓名', example: '张三' },
    { name: '申请人工号', description: '发起申请的用户工号', example: 'XGH001' },
    { name: '申请人学院', description: '发起申请的用户学院', example: '法学院' },
    { name: '申请人专业', description: '发起申请的用户专业', example: '法学' },
    { name: '申请人班级', description: '发起申请的用户班级', example: '法学250601' },
    { name: '项目名称', description: '申请项目名称', example: '岗位申请' },
  ],
  approval: [
    { name: '当前审批人', description: '当前正在审批的人员姓名', example: '李四' },
    { name: '当前审批人学院', description: '当前审批人学院', example: '法学院' },
    { name: '审批状态', description: '当前审批流程状态', example: '待审批' },
    { name: '下一审批人', description: '下一个需要审批的人员', example: '王五' },
  ]
})

onMounted(() => {
  getTemplateVariables()
})

// 根据搜索文本过滤变量
const filteredVariables = computed(() => {
  if (!searchText.value) {
    return variables.value
  }

  const result = {}
  Object.keys(variables.value).forEach(category => {
    result[category] = variables.value[category].filter(variable =>
      variable.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
      variable.description.toLowerCase().includes(searchText.value.toLowerCase())
    )
  })
  return result
})

// 插入变量
const insertVariable = (variableName) => {
  emit('insert-variable', `\${${variableName}}`)
  ElMessage.success(`已插入变量：\${${variableName}}`)
  showDialog.value = false
}
</script>

<style scoped>
.variable-content {
  max-height: 500px;
}

.variable-list {
  max-height: 300px;
  overflow-y: auto;
}

.variable-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.variable-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
  transform: translateY(-1px);
}

.variable-name {
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
  margin-bottom: 4px;
}

.variable-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
}

.variable-example {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

code {
  background-color: #f5f7fa;
  color: #e6a23c;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}
</style>
