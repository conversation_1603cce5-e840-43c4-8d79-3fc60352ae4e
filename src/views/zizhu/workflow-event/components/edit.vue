<!-- 字段组编辑弹窗 -->
<template>
  <ele-drawer :size="530"
              :title="isUpdate ? '修改 ['+data.eventName+']' : '添加工作流事件'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-position="top"
             label-width="auto"
             @submit.prevent="">
      <el-form-item label="事件名称" prop="eventName">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.eventName"
                  placeholder="请输入事件名称"/>
      </el-form-item>
      <el-form-item label="触发service" prop="eventClassName">
        <el-select v-model="form.eventClassName" placeholder="请选择触发service" clearable>
          <el-option label="请选择" value=""/>
          <el-option label="申请项目明细分数检查" value="applicationItemDetailScoreCheck"/>
          <el-option label="工作流事件消息观察者" value="workflowEventMessageObserver"/>
        </el-select>
        <div style="margin-top: 5px; font-size: 12px; color: #909399;">
          <template v-if="form.eventClassName === 'applicationItemDetailScoreCheck'">
            用于检查申请项目的明细分数，在特定条件下触发相关业务逻辑
          </template>
          <template v-else-if="form.eventClassName === 'workflowEventMessageObserver'">
            用于监听工作流事件并发送相应的消息通知
          </template>
          <template v-else-if="!form.eventClassName">
            可选择系统预设的service或留空使用自定义逻辑
          </template>
        </div>
      </el-form-item>
      <el-form-item label="事件类型" prop="eventType">
        <el-radio-group v-model="form.eventType">
          <el-radio value="消息通知">消息通知</el-radio>
          <el-radio value="字段更新">字段更新</el-radio>
          <el-radio value="审核表单校验">审核表单校验</el-radio>
          <el-radio value="其他">其他</el-radio>
        </el-radio-group>
        <div style="margin-top: 8px; font-size: 12px; color: #909399;">
          <template v-if="form.eventType === '消息通知'">
            用于发送消息通知，需配置接收者和消息模板
          </template>
          <template v-else-if="form.eventType === '字段更新'">
            用于更新数据库字段，需配置表名和更新表达式
          </template>
          <template v-else-if="form.eventType === '审核表单校验'">
            用于表单校验逻辑，需配置校验条件表达式
          </template>
          <template v-else-if="form.eventType === '其他'">
            自定义事件类型，可灵活配置相关参数
          </template>
        </div>
      </el-form-item>

      <!-- 消息通知类型相关字段 -->
      <template v-if="form.eventType === '消息通知'">
        <el-form-item label="消息接收者类型" prop="receiverType">
          <el-select v-model="form.receiverType" placeholder="请选择消息接收者类型" clearable>
            <el-option label="申请人" value="APPLICANT"/>
            <el-option label="审批人" value="APPROVER"/>
            <el-option label="申请人和审批人" value="BOTH"/>
            <el-option label="自定义接收人" value="CUSTOM"/>
          </el-select>
        </el-form-item>

        <!-- 当选择申请人和审批人时，分别配置消息 -->
        <template v-if="form.receiverType === 'BOTH'">
          <el-tabs type="border-card" style="margin: 10px 0;">
            <el-tab-pane label="申请人消息配置">
                             <el-form-item label="申请人消息标题" prop="applicantMessageTitle">
                 <MessageInput
                   v-model="form.applicantMessageTitle"
                   :maxlength="200"
                   :show-tips="false"
                   placeholder="请输入发送给申请人的消息标题模板"/>
               </el-form-item>
                             <el-form-item label="申请人消息内容" prop="applicantMessage">
                 <MessageInput
                   v-model="form.applicantMessage"
                   type="textarea"
                   :rows="4"
                   :maxlength="1000"
                   placeholder="请输入发送给申请人的消息模板，例如：您的${申请类型}已提交，申请编号：${申请编号}，请耐心等待审批。"/>
               </el-form-item>
            </el-tab-pane>
            <el-tab-pane label="审批人消息配置">
                             <el-form-item label="审批人消息标题" prop="approverMessageTitle">
                 <MessageInput
                   v-model="form.approverMessageTitle"
                   :maxlength="200"
                   :show-tips="false"
                   placeholder="请输入发送给审批人的消息标题模板"/>
               </el-form-item>
                             <el-form-item label="审批人消息内容" prop="approverMessage">
                 <MessageInput
                   v-model="form.approverMessage"
                   type="textarea"
                   :rows="4"
                   :maxlength="1000"
                   placeholder="请输入发送给审批人的消息模板，例如：您有一个${申请类型}待审批，申请人：${申请人姓名}，请及时处理。"/>
               </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </template>

        <!-- 当选择单一接收者类型时，使用通用配置 -->
        <template v-else-if="form.receiverType && form.receiverType !== 'CUSTOM'">
          <el-form-item label="消息标题模板" prop="messageTitle">
            <MessageInput
              v-model="form.messageTitle"
              :maxlength="200"
              :show-tips="false"
              placeholder="请输入消息标题模板"/>
          </el-form-item>
          <el-form-item label="消息通知模板" prop="message">
            <MessageInput
              v-model="form.message"
              type="textarea"
              :rows="3"
              :maxlength="1000"
              placeholder="请输入消息通知模板，支持使用模板变量"/>
          </el-form-item>
        </template>

        <!-- 自定义接收人配置 -->
        <template v-if="form.receiverType === 'CUSTOM'">
          <el-form-item label="自定义接收人" prop="customReceivers">
            <el-input clearable
                      :maxlength="500"
                      v-model="form.customReceivers"
                      placeholder="多个接收人用逗号分隔"/>
          </el-form-item>
          <el-form-item label="消息标题模板" prop="messageTitle">
            <MessageInput
              v-model="form.messageTitle"
              :maxlength="200"
              :show-tips="false"
              placeholder="请输入消息标题模板"/>
          </el-form-item>
          <el-form-item label="消息通知模板" prop="message">
            <MessageInput
              v-model="form.message"
              type="textarea"
              :rows="3"
              :maxlength="1000"
              placeholder="请输入消息通知模板，支持使用模板变量"/>
          </el-form-item>
        </template>

        <el-form-item label="移动端跳转路径">
          <el-input clearable
                    :maxlength="200"
                    v-model="form.mobilePath"
                    placeholder="请输入移动端跳转路径"/>
        </el-form-item>
        <el-form-item label="PC端跳转路径">
          <el-input clearable
                    :maxlength="200"
                    v-model="form.pcPath"
                    placeholder="请输入PC端跳转路径"/>
        </el-form-item>
      </template>

      <!-- 字段更新类型相关字段 -->
      <template v-if="form.eventType === '字段更新'">
        <el-form-item label="字段更新表名" prop="setTable">
          <el-input clearable
                    :maxlength="200"
                    v-model="form.setTable"
                    placeholder="请输入字段更新表名"/>
        </el-form-item>
        <el-form-item label="字段更新表达式" prop="setExpression">
          <el-input type="textarea"
                    :rows="3"
                    :maxlength="1000"
                    v-model="form.setExpression"
                    placeholder="请输入字段更新表达式，如：status = '已审核', update_time = NOW()"/>
        </el-form-item>
        <el-form-item label="条件表达式">
          <el-input type="textarea"
                    :rows="2"
                    :maxlength="500"
                    v-model="form.whereExpression"
                    placeholder="请输入条件表达式，如：id = #{id} AND status = '待审批'"/>
        </el-form-item>
      </template>

      <!-- 审核表单校验类型相关字段 -->
      <template v-if="form.eventType === '审核表单校验'">
        <el-form-item label="校验条件表达式" prop="whereExpression">
          <el-input type="textarea"
                    :rows="3"
                    :maxlength="500"
                    v-model="form.whereExpression"
                    placeholder="请输入校验条件表达式，用于表单校验逻辑"/>
        </el-form-item>
      </template>

      <!-- 其他类型或公共字段 -->
      <template v-if="form.eventType === '其他'">
        <el-form-item label="条件表达式">
          <el-input type="textarea"
                    :rows="2"
                    :maxlength="500"
                    v-model="form.whereExpression"
                    placeholder="请输入条件表达式"/>
        </el-form-item>
        <el-form-item label="消息通知模板">
          <MessageInput
            v-model="form.message"
            type="textarea"
            :rows="2"
            :maxlength="1000"
            placeholder="请输入消息通知模板（可选），支持使用模板变量"/>
        </el-form-item>
      </template>
      <el-form-item label="是否启用" prop="enabled">
        <el-switch v-model="form.enabled" :active-value="true" :inactive-value="false"/>
      </el-form-item>
      <el-form-item label="备注说明">
        <el-input type="textarea"
                  :rows="3"
                  :maxlength="500"
                  v-model="form.remark"
                  placeholder="请输入备注说明"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" size="small" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation} from '../api/index.js'
import {useRouter} from "vue-router";
import MessageInput from './MessageInput.vue';

const emit = defineEmits(['done', 'update:modelValue']);

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  eventName: '',
  eventClassName: '',
  eventType: '',
  setExpression: '',
  whereExpression: '',
  message: '',
  setTable: '',
  receiverType: '',
  customReceivers: '',
  messageTitle: '',
  applicantMessage: '',
  approverMessage: '',
  applicantMessageTitle: '',
  approverMessageTitle: '',
  enabled: true,
  mobilePath: '',
  pcPath: '',
  remark: '',
});

/** 表单验证规则 */
const rules = reactive({
  eventName: [
    {
      required: true,
      message: '请输入事件名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  eventClassName: [
    {
      required: false,
      message: '请选择触发service',
      type: 'string',
      trigger: 'change'
    }
  ],
  eventType: [
    {
      required: true,
      message: '请选择事件类型',
      type: 'string',
      trigger: 'change'
    }
  ],
  enabled: [
    {
      required: true,
      message: '请选择是否启用',
      type: 'boolean',
      trigger: 'change'
    }
  ],
});

// 根据事件类型动态添加验证规则
const updateValidationRules = () => {
  // 清空动态规则
  delete rules.message;
  delete rules.messageTitle;
  delete rules.receiverType;
  delete rules.setTable;
  delete rules.setExpression;
  delete rules.whereExpression;
  delete rules.applicantMessage;
  delete rules.approverMessage;
  delete rules.applicantMessageTitle;
  delete rules.approverMessageTitle;
  delete rules.customReceivers;

  // 根据事件类型添加对应的验证规则
  if (form.eventType === '消息通知') {
    rules.receiverType = [
      {
        required: true,
        message: '请选择消息接收者类型',
        type: 'string',
        trigger: 'change'
      }
    ];

    // 根据接收者类型设置不同的验证规则
    if (form.receiverType === 'BOTH') {
      // 申请人和审批人分别配置
      rules.applicantMessageTitle = [
        {
          required: true,
          message: '请输入申请人消息标题',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.applicantMessage = [
        {
          required: true,
          message: '请输入申请人消息内容',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.approverMessageTitle = [
        {
          required: true,
          message: '请输入审批人消息标题',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.approverMessage = [
        {
          required: true,
          message: '请输入审批人消息内容',
          type: 'string',
          trigger: 'blur'
        }
      ];
    } else if (form.receiverType === 'CUSTOM') {
      // 自定义接收者
      rules.customReceivers = [
        {
          required: true,
          message: '请输入自定义接收人',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.messageTitle = [
        {
          required: true,
          message: '请输入消息标题模板',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.message = [
        {
          required: true,
          message: '请输入消息通知模板',
          type: 'string',
          trigger: 'blur'
        }
      ];
    } else if (form.receiverType) {
      // 单一接收者类型（申请人或审批人）
      rules.messageTitle = [
        {
          required: true,
          message: '请输入消息标题模板',
          type: 'string',
          trigger: 'blur'
        }
      ];
      rules.message = [
        {
          required: true,
          message: '请输入消息通知模板',
          type: 'string',
          trigger: 'blur'
        }
      ];
    }
  } else if (form.eventType === '字段更新') {
    rules.setTable = [
      {
        required: true,
        message: '请输入字段更新表名',
        type: 'string',
        trigger: 'blur'
      }
    ];
    rules.setExpression = [
      {
        required: true,
        message: '请输入字段更新表达式',
        type: 'string',
        trigger: 'blur'
      }
    ];
  }
  /*else if (form.eventType === '审核表单校验') {
    rules.whereExpression = [
      {
        required: true,
        message: '请输入校验条件表达式',
        type: 'string',
        trigger: 'blur'
      }
    ];
  }*/
};

// 清空不相关字段的值
const clearUnrelatedFields = () => {
  if (form.eventType === '消息通知') {
    // 保留消息通知相关字段，清空其他字段
    form.setTable = '';
    form.setExpression = '';
    form.whereExpression = '';
  } else if (form.eventType === '字段更新') {
    // 保留字段更新相关字段，清空其他字段
    form.message = '';
    form.messageTitle = '';
    form.receiverType = '';
    form.customReceivers = '';
    form.applicantMessage = '';
    form.approverMessage = '';
    form.applicantMessageTitle = '';
    form.approverMessageTitle = '';
    form.mobilePath = '';
    form.pcPath = '';
  } else if (form.eventType === '审核表单校验') {
    // 保留校验相关字段，清空其他字段
    form.message = '';
    form.messageTitle = '';
    form.receiverType = '';
    form.customReceivers = '';
    form.applicantMessage = '';
    form.approverMessage = '';
    form.applicantMessageTitle = '';
    form.approverMessageTitle = '';
    form.mobilePath = '';
    form.pcPath = '';
    form.setTable = '';
    form.setExpression = '';
  } else if (form.eventType === '其他') {
    // 保留通用字段，清空特定类型字段
    form.messageTitle = '';
    form.receiverType = '';
    form.customReceivers = '';
    form.applicantMessage = '';
    form.approverMessage = '';
    form.applicantMessageTitle = '';
    form.approverMessageTitle = '';
    form.mobilePath = '';
    form.pcPath = '';
    form.setTable = '';
    form.setExpression = '';
  }
};

// 监听事件类型变化
watch(
  () => form.eventType,
  (newType, oldType) => {
    if (newType && oldType !== undefined) {
      // 只有在用户主动切换事件类型时才清空不相关字段
      updateValidationRules();
      if (!isUpdate.value) {
        // 新建模式下切换类型时清空不相关字段
        clearUnrelatedFields();
      }
      // 清除表单验证错误
      formRef.value?.clearValidate?.();
    }
  }
);

// 监听接收者类型变化
watch(
  () => form.receiverType,
  (newType, oldType) => {
    if (newType && oldType !== undefined && form.eventType === '消息通知') {
      // 当接收者类型变化时，重新设置验证规则
      updateValidationRules();
      // 清除表单验证错误
      formRef.value?.clearValidate?.();

      // 当从BOTH切换到其他类型时，清空专用字段
      if (oldType === 'BOTH' && newType !== 'BOTH') {
        form.applicantMessage = '';
        form.approverMessage = '';
        form.applicantMessageTitle = '';
        form.approverMessageTitle = '';
      }

      // 当从其他类型切换到CUSTOM时，清空自定义接收者字段
      if (oldType !== 'CUSTOM' && newType === 'CUSTOM') {
        form.customReceivers = '';
      }
    }
  }
);

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields(props.data);
        isUpdate.value = true;
        // 编辑时，根据数据的事件类型更新验证规则，但不清空字段
        setTimeout(() => {
          updateValidationRules();
        }, 100);
      } else {
        isUpdate.value = false;
        // 新建时，重置所有字段并更新验证规则
        setTimeout(() => {
          if (form.eventType) {
            updateValidationRules();
          }
        }, 100);
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
