<template>
  <div class="message-input-wrapper">
    <el-input
      ref="inputRef"
      v-model="inputValue"
      :type="type"
      :rows="rows"
      :maxlength="maxlength"
      :placeholder="placeholder"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    <div class="input-tools">
      <VariableHelper @insert-variable="insertVariable" />
      <div class="variable-tips" v-if="showTips">
        <el-text size="small" type="info">
          <el-icon><InfoFilled /></el-icon>
          支持变量：${申请人姓名}、${项目名称}、${审批状态} 等
        </el-text>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'
import VariableHelper from './VariableHelper.vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  rows: {
    type: Number,
    default: 3
  },
  maxlength: {
    type: Number,
    default: 1000
  },
  placeholder: {
    type: String,
    default: ''
  },
  showTips: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'input', 'blur', 'focus'])

const inputRef = ref(null)
const inputValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

const handleInput = (value) => {
  emit('input', value)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}

// 插入变量到光标位置
const insertVariable = async (variable) => {
  await nextTick()

  if (inputRef.value) {
    const input = inputRef.value.ref || inputRef.value.textarea
    const start = input.selectionStart
    const end = input.selectionEnd
    const currentValue = inputValue.value || ''

    // 在光标位置插入变量
    const newValue = currentValue.substring(0, start) + variable + currentValue.substring(end)
    inputValue.value = newValue

    // 设置新的光标位置
    await nextTick()
    const newPosition = start + variable.length
    input.setSelectionRange(newPosition, newPosition)
    input.focus()
  }
}
</script>

<style scoped>
.message-input-wrapper {
  position: relative;
}

.input-tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.variable-tips {
  flex: 1;
}

.variable-tips .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
