<!--审批结果记录-->
<template>
  <ele-page hide-footer  flex-table>
    <!-- 搜索表单 -->
    <search @search="reload"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small" @click="exportBas()">
            导出
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {ref, unref,} from 'vue';
import {queryPage,} from './api';
import {useRouter} from "vue-router";
import {getToken} from "@/utils/token-util.js";
import Search from './components/search.vue';

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const routeType = path.split("/")[3];

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'year',
      slot: 'name',
      label: '评定年份',
    },
    {
      prop: 'xmmc',
      label: '项目名称',
    },
    {
      prop: 'xgh',
      label: '学号',
    },
    {
      prop: 'xm',
      label: '姓名',
    },
    {
      prop: 'xymc',
      label: '学院',
    },
    {
      prop: 'zymc',
      label: '专业',
    },
    {
      prop: 'njmc',
      label: '年级',
    },
    {
      prop: 'bjmc',
      label: '班级',
    },
  ]
);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit, type: routeType});
};

/** 导出excel */
const exportBas = () => {
  window.location.href = BASE_URL + 'api/form/form-approval-result/exportData?access_token='
    + accessToken + '&type=' + routeType
};
/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

</script>

<script>
export default {
  name: 'SelectorConfig'
};
</script>

