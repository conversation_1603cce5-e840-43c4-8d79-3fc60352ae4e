<!--资助表单申请-->
<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <ele-tabs type="plain"
                  size="small"
                  v-model="activeName"
                  :items="tabsItems">
          <template #label="{ item, label}">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #xmmc="{ row }">
          <el-link type="primary" underline="never"
                   @click.stop="openProjectInfo(row)">
            {{ row.xmmc }}
          </el-link>
        </template>
        <template #action="{ row }">
          <template v-if="activeName==='sqjl'">
            <el-link type="primary" underline="never" @click="openApply(row,'edit')">
              编辑
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openApply(row,'sqpreview')">
              查看
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="remove(row)">
              删除
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="info" underline="never">
              下载申请表
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="info" underline="never">
              荣誉证书
            </el-link>
          </template>
          <template v-else>
            <el-link type="primary" underline="never" @click="openApply(row,'add')">
              申请
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :currentXmId="currentXmId" :routeType="routeType" :currentXmData="current" @done="reload"/>
    <ProjectInfo v-model="showProjectInfo" :currentData="currentData"/>
  </ele-page>
</template>

<script setup>
import {computed, reactive, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {queryPageApplicationInfo, queryPageProjectInfo, removesApply} from './api';

import {useRouter} from "vue-router";
import Edit from "./components/edit.vue";
import {insertAtIndex, transformDicDataName} from '@/utils/common_bak2.js';
import {usePageTab} from "@/utils/use-page-tab.js";
import ProjectInfo from "@/views/zizhu/components/projectInfo.vue";

const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
const routeType = path.split("/")[3];

/** 表格实例 */
const tableRef = ref(null);
const columns = ref([]);


/** 标签页选中 */
const activeName = ref('ksq');

let backActiveName = query?.currentActiveName ?? "ksq";
activeName.value = backActiveName;

const tabsItems = computed(() => {
  return [
    {
      label: '可申请',
      name: 'ksq',
      disabled: false,
      sort: 1
    },
    {
      label: '已申请',
      name: 'ysq',
      sort: 2
    },
    {
      label: '不可申请',
      name: 'bksq',
      sort: 2
    },
    {
      label: '全部申请记录',
      name: 'sqjl',
      sort: 3
    },
  ]
})

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);
const currentXmId = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  if (activeName.value === 'sqjl') {// 我的申请记录
    return queryPageApplicationInfo(routeType, {
      ...where, ...orders, ...filters, page, limit, type: routeType,
    });
    // projectId: currentXmId?.value
  } else {
    return queryPageProjectInfo(activeName.value, {
      ...where, ...orders, ...filters,
      page,
      limit,
      type: routeType,
      sqzt: activeName.value
    });
  }
};

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 搜索 */
const reloadData = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

watch(
  activeName,
  (newVal) => {
    if (newVal) {
      columns.value = [];
      let newColumns = [
        {
          prop: 'xmmc',
          slot: 'xmmc',
          label: '项目名称',
        },
        {
          prop: 'year',
          label: '年份',
        },
      ];
      if (newVal === 'sqjl') {
        insertAtIndex(newColumns, {
          prop: 'createTime',
          label: '申请时间',
        }, 3);
      } else {
        insertAtIndex(newColumns, {
          prop: 'sqsj',
          label: '可申请时间范围',
          formatter: (row) => {
            let sqkssj = row.sqkssj ? row.sqkssj : ''
            let sqjzsj = row.sqjzsj ? row.sqjzsj : ''
            return (sqkssj && sqjzsj) ? sqkssj + '至' + sqjzsj : '不限制';
          },
        }, 3);
      }
      if (['ksq', 'ysq', 'bksq'].includes(newVal)) {
        let addcolumns = [
          {
            prop: 'ksqts',
            label: '可申请次数',
          },
          {
            prop: 'mrksqts',
            label: '每日可申请次数',
          },]
        newColumns = newColumns.concat(addcolumns)
      }
      columns.value = newColumns;
      if (['ksq', 'sqjl'].includes(newVal)) {
        columns.value = newColumns.concat({
          columnKey: 'action',
          label: '操作',
          slot: 'action',
          fixed: 'right'
        },)
      }
      reloadData()
    }
  },
  {immediate: true}
)

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      groupName: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

const showProjectInfo = ref(false);
const currentData = ref({});

const openProjectInfo = (row) => {
  console.log(row)
  showProjectInfo.value = true;
  currentData.value = row
  console.log(row)
}
/** 打开组group编辑弹窗 */
const openApply = (row, enType) => {
  // row.enType = enType
  // if (enType === 'add') row.enTypeName = row.xmmc + ' 申请'
  // if (enType === 'edit') row.enTypeName = row.xmmc + ' 申请记录编辑'
  // if (enType === 'sqpreview') row.enTypeName = row.xmmc + ' 项目申请查看'
  // current.value = row ?? null;
  // currentXmId.value = enType === 'add' ? row.id : row.projectId;
  // showEdit.value = true;

  removePageTab({key: getRouteTabKey()});
  // ksid = row.id;
  let query = {enterType: enType, userType: row.userType, activeName: activeName.value}
  if (enType !== 'add') query.applicationId = row.id;
  push({
    path: '/zizhu/apply/details/' + routeType + '/' + (enType === 'add' ? row.id : row.projectId),
    query: query
  })
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.xmmc).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removesApply(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'APPLYINDEX'
};
</script>

