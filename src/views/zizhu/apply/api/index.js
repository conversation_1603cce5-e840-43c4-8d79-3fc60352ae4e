/**
 * 表单申请
 * */
import request from '@/utils/request.js';

/**
 * 分页查询表单项目记录（权限标识：form:apply:list）
 * sqzt 申请状态
 *      ksq :可申请
 *      ysq :已申请
 *      bksq :不可申请
 */
export async function queryPageProjectInfo(type, params) {
  const res = await request.get('/form/apply/' + type + '/pageProjectInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询表单申请记录（权限标识：form:apply:list）
 */
export async function queryPageApplicationInfo(type, params) {
  const res = await request.get('/form/apply/' + type + '/pageApplicationInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请ID查询项目信息
 */
export async function getFormApplyProjectInfoById(id) {
  const res = await request.get('/form/apply/' + id + '/projectInfo');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询表单申请记录（权限标识：form:apply:list）
 */
export async function getFormApplyApplicationInfo(params) {
  const res = await request.get('/form/apply/applicationInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目id查询表单组
 * projectId 项目id
 * nodeID 节点id
 * applicationId 申请记录ID
 */
export async function getFormApplyFieldGroupList(params) {
  const res = await request.get('/form/apply/fieldGroupList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 申请表单子列表信息
 * @param params
 * @returns {Promise<*>}
 */
export async function getApplicationListInfos(params) {
  const res = await request.get('/form/apply/applicationListInfos', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询字典列表信息组
 */
export async function getDicListGroup(id) {
  const res = await request.get('/form/apply/' + id + '/dicListGroup');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询字典列表信息字段
 */
export async function getDicListGroupField(params) {
  const res = await request.get('/form/apply/dicListGroupField', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目id查询表单字段
 */
export async function getFormApplyFieldList(params) {
  const res = await request.get('/form/apply/fieldList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改表单申请记录（权限标识：form:apply:operation）
 * form
 */
export async function operationApply(data) {
  const res = await request.post('/form/apply/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改表单申请记录（权限标识：form:apply:operation）
 * table
 */
export async function operationApplyListInfo(data) {
  const res = await request.post('/form/apply/listInfoOperation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除表单申请记录（权限标识：form:apply:remove）
 */
export async function removesApply(data) {
  const res = await request.post('/form/apply/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除表单列表记录（权限标识：form:apply:remove）
 */
export async function removesApplyListInfo(data) {
  const res = await request.post('/form/apply/listInfoRemove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
