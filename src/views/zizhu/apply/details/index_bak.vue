<!--审核详情页-->
<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', borderWidth: '0',  width: '100%' ,}">
      <div ref="containerRef" :style="{height: pageHeight+'px',overflow:'auto',background:'#ffffff'}">
        <UserCard :config="config" :routeType="routeType" :leftData="leftData"/>
      </div>
      <template #body>
        <template v-for=" (group, index) in rightData">
          <table-preview v-if="group.listFlag==='是'"
                         :key="'formKey'+index"
                         :labelWidth="config.labelWidth"
                         :grid="config.grid"
                         :routeType="routeType"
                         :currentGroup="group"/>
          <form-preview v-else :ref="setChildComponentRef"
                        :key="'formKey'+index"
                        :labelWidth="config.labelWidth"
                        :routeType="routeType"
                        :currentData="group.currentData"
                        :currentGroup="group"
                        @onDoneGroup="onDoneGroup"
                        @onDoneDeleteFile="onDoneDeleteFile"/>
        </template>
      </template>
    </ele-split-panel>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text style="float: right;padding-right: 40px;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button size="small" @click="onBack" plain>返回到列表</el-button>
        <!--        <el-button v-if="enterType!=='sqpreview'" size="small" type="primary" :loading="loading"-->
        <!--                   @click="onSubmit">-->
        <!--          提交-->
        <!--        </el-button>-->
        <el-button v-if="currentData&&currentData.sfxszc==='是'" size="small" plain
                   @click="onSubmit('是')">暂存
        </el-button>
        <el-button v-if="enterType!=='sqpreview'" size="small" type="primary" plain
                   :loading="loading" @click="onSubmit('否')">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
    <!--    项目申请开启验证码-->
    <el-dialog v-model="dialogCaptchaVisible" title="项目申请开启验证码" width="420"
               :close-on-click-modal="false"
               append-to-body
               draggable
               align-center>
      <GraphicCaptcha @updateCaptchaCode="handleCaptchaCode"/>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="dialogCaptchaVisible = false">取消</el-button>
          <el-button size="small" type="primary" plain @click="handleCaptchaCodeSubmit">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup>
import {ref, onMounted, reactive, unref, watch,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import TablePreview from "@/views/personInfo/st/components/table-preview.vue";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {compare, groupArr, toFormData} from "@/utils/common_bak2.js";
import {useRouter} from "vue-router";
import UserCard from "../../components/user-card.vue";
import {usePageTab} from "@/utils/use-page-tab.js";
import {
  getFormApplyApplicationInfo,
  getFormApplyFieldGroupList, getFormApplyProjectInfoById, operationApply
} from "@/views/zizhu/apply/api/index.js";
import GraphicCaptcha from "@/views/zizhu/components/GraphicCaptcha.vue";
import {CloseCircleOutlined} from "@/components/icons/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const emits = defineEmits(['done']);
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
let enterType = query?.enterType ?? ''
let applicationId = query?.applicationId ?? ''
let userType = query?.userType ?? ''
let activeName = query?.activeName ?? ''
let pathArray = path.split("/");
console.log(pathArray, enterType)
let routeType = pathArray[4]
let currentProjectId = pathArray[5]

/** 数据 */
const data = ref([]);
const leftData = ref([]);
const rightData = ref([]);
const currentData = ref({});

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
};

/** 提交状态 */
const loading = ref(false);
/** 表单配置 */
const config = reactive({
  labelWidth: '110px',
  grid: 1
});

/** 查询资助申请组信息 */
const queryFormApplyFieldGroupList = async () => {
  leftData.value = []
  rightData.value = []
  loading.value = true;
  await getFormApplyFieldGroupList({
    projectId: currentProjectId,
    applicationId: '',
  }).then((list) => {
    console.log(list)
    loading.value = false;
    let leftArray = []
    if (list) {
      list.forEach((item, index) => {
        item.applicationId = applicationId
        item.enType = enterType
        item.userType = userType
        item.title = item.groupName
        // item.mark = 'zzapply'
        if (item.infoType === 'base') {
          item.sort = index + 1
          item.span = 24
          leftArray.push(item)
        } else {
          item.sort = item.dicGroupId ? index + 1 : index + 10
          rightData.value.push(item)
        }
      })
      leftArray = leftArray.sort(compare('sort'))
      rightData.value = rightData.value.sort(compare('sort'))
      leftData.value = groupArr(leftArray, 'infoType');
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/zizhu/apply/' + routeType,
    query: {currentActiveName: activeName}
  })
};

const queryFormApplyApplicationInfo = () => {
  loading.value = true;
  getFormApplyApplicationInfo({
    projectId: currentProjectId,
    // applicationId: '',
  }).then((list) => {
    loading.value = false;
    if (list) {
      applicationId = list.id
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    queryFormApplyFieldGroupList()
  });
};

const queryFormApplyProjectInfoById = () => {
  loading.value = true;
  getFormApplyProjectInfoById(currentProjectId).then((list) => {
    loading.value = false;
    if (list) {
      let enTypeName = "";
      if (enterType === 'add') enTypeName = list.xmmc + ' 申请'
      if (enterType === 'edit') enTypeName = list.xmmc + ' 申请记录编辑'
      if (enterType === 'sqpreview') enTypeName = list.xmmc + ' 项目申请查看'
      if (enTypeName) setPageTabTitle(enTypeName)
      currentData.value = list
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

const childComponentsRefs = ref([]);

// 设置子组件的引用
const setChildComponentRef = (el) => {
  if (el) {
    childComponentsRefs.value.push(el);
  }
}

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  let result = new Promise((resolve, reject) => {
    formChild.proFormRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}
const dialogCaptchaVisible = ref(false);

const setCaptchaData = ref(null)
const handleCaptchaCode = (data) => {
  setCaptchaData.value = data
}

const handleCaptchaCodeSubmit = () => {
  dialogCaptchaVisible.value = false
  save();
}
const setSfzc = ref(null)
const onSubmit = async (sfzc) => {
  setSfzc.value = sfzc;
  // 遍历表单数组，依次对每个表单进行校验
  await childComponentsRefs.value.map((child) => {
    checkForm(child)
  })
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []//每次请求完要清空数组
    if (allTrue) {
      //启用验证码
      if (currentData.value && currentData.value.qysqyzm === '是') {
        dialogCaptchaVisible.value = true;
      } else {
        // 执行后续操作
        save()
      }
    } else {
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
}

/** 提交 */
const save = () => {
  let resData = proFormGroup.value;
  console.log(resData)
  let result = {};
  resData.forEach(res => {
    const value = res.values;
    Object.keys(value).forEach((key) => {
      const resValue = value[key];
      if (resValue) result[key] = resValue;
    })
  })
  Object.keys(result).forEach((key) => {
    //处理type=regions类型，省市区+详细地址逻辑处理
    if (key.includes('_regionsDetail')) {
      let keyArray = key.split('_')
      result[keyArray[0]] = [result[keyArray[0]].join(), result[key]].join(",")
    }
  })
  let delFileIdData = proFormDelFile.value;
  let resultFile = {};
  delFileIdData.forEach(res => {
    resultFile[res.prop] = res.values;
  })
  if (resultFile) result['deleteFileIds'] = JSON.stringify(resultFile)
  result.type = routeType;
  result.projectId = currentProjectId;
  result.sfzc = setSfzc.value;//是否暂存
  if (enterType === 'edit') result.id = currentProjectId
  if (applicationId) result.id = applicationId
  if (setCaptchaData.value) {
    result = Object.assign(result, setCaptchaData.value)
  }
  console.log("resData====", resData)
  console.log("result====", result)
  let data = toFormData({...result})
  loading.value = true;
  operationApply(data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    onBack();
    emits('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 存放删除的图片ID，附件ID*/
const proFormDelFile = ref([]);

const onDoneDeleteFile = (data) => {
  let isExist = proFormDelFile.value.filter(obj => obj.prop === data.prop)
  if (isExist.length > 0) {
    proFormDelFile.value.filter(obj => {
      if (obj.prop === data.prop) obj.values = data.values;
    })
  } else {
    proFormDelFile.value.push(data)
  }
};

if (currentProjectId) {
  queryFormApplyProjectInfoById()
  if (enterType === 'add') {
    console.log(1)
    queryFormApplyApplicationInfo()
  } else {
    console.log(2)
    queryFormApplyFieldGroupList();
  }
}

</script>

<script>
export default {
  name: 'ZIZHUAPPLYDETAILS'
};
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}

.el-collapse {
  border-bottom: unset !important;
}

.el-collapse-item {
  padding-left: 12px !important;
}

</style>
<style lang="scss" scoped>
.time-line {
  //margin-top: 50px;
  //margin-left: 200px;
}

.list-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #181b1e;
}

.list-company {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.list-company1 {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  //color: #2991ff;
  //margin-top: 15px;
}

.list-desc {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #596878;
}

</style>
