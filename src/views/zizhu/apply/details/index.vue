<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', width: '100%'}">
      <div ref="containerRef" class="user-container" :style="{height: (pageHeight-22)+'px',overflow:'auto',background:'#ffffff'}">
        <UserCard :config="config" :route-type="routeType" :left-data="leftData"/>
      </div>
      <template #body>
        <template v-for="(group, index) in rightData" :key="'formKey'+index">
          <table-preview v-if="group.listFlag==='是'"
                         :label-width="config.labelWidth"
                         :grid="config.grid"
                         :route-type="routeType"
                         :current-group="group"/>
          <form-preview v-else
                        :ref="setChildComponentRef"
                        :route-type="routeType"
                        :current-data="group.currentData"
                        :current-group="group"
                        @on-done-group="onDoneGroup"
                        @on-done-delete-file="onDoneDeleteFile"/>
        </template>
      </template>
    </ele-split-panel>

    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <transition name="el-fade-in">
        <ele-text v-if="validMsg" class="error-message" type="danger" :icon="CloseCircleOutlined">
          {{ validMsg }}
        </ele-text>
      </transition>

      <template #extra>
        <el-button size="small" @click="handleBack" plain>返回到列表</el-button>
        <el-button v-if="currentData?.sfxszc==='是'" size="small" plain
                   @click="handleSubmit('是')">暂存
        </el-button>
        <el-button v-if="routeParams.enterType!=='sqpreview'" size="small" type="primary" plain
                   :loading="loading" @click="handleSubmit('否')">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>

    <!-- 验证码弹窗 -->
    <el-dialog v-model="dialogCaptchaVisible" title="项目申请开启验证码" width="420"
               :close-on-click-modal="false"
               append-to-body
               draggable
               align-center>
      <graphic-captcha @update-captcha-code="handleCaptchaCode"/>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="dialogCaptchaVisible = false">取消</el-button>
          <el-button size="small" type="primary" plain @click="handleCaptchaSubmit">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>

<script setup>
import {ref, onMounted, computed, reactive} from 'vue';
import {useRouter} from 'vue-router';
import {ElMessageBox} from 'element-plus/es';
import {ElMessage as EleMessage} from 'element-plus';
import {storeToRefs} from 'pinia';
import {useUserStore} from '@/store/modules/user.js';
import {usePageTab} from '@/utils/use-page-tab.js';
import {compare, groupArr, toFormData, toFormDataWj} from '@/utils/common_bak2.js';
import {CloseCircleOutlined} from '@/components/icons/index.js';
import TablePreview from '@/views/personInfo/st/components/table-preview.vue';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import UserCard from '../../components/user-card.vue';
import GraphicCaptcha from '@/views/zizhu/components/GraphicCaptcha.vue';
import {
  getFormApplyApplicationInfo,
  getFormApplyFieldGroupList,
  getFormApplyProjectInfoById,
  operationApply,
} from '@/views/zizhu/apply/api/index.js';

// 存储和路由
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTabTitle} = usePageTab();
const router = useRouter();
const {currentRoute, push} = router;
const {path, query} = currentRoute.value;

// 路由参数解析
const routeParams = computed(() => ({
  enterType: query?.enterType ?? '',
  applicationId: query?.applicationId ?? '',
  userType: query?.userType ?? '',
  activeName: query?.activeName ?? '',
}));

console.log(routeParams.value, routeParams.value.enterType);
const pathArray = path.split('/');
const routeType = pathArray[4];
const currentProjectId = pathArray[5];

// 数据状态
const loading = ref(false);
const leftData = ref([]);
const rightData = ref([]);
const currentData = ref({});
const hasUnsavedChanges = ref(false);

// 表单配置
const config = reactive({
  labelWidth: '110px',
  grid: 1,
});

// 表单相关数据
const proFormGroup = ref([]);
const proFormDelFile = ref([]);
const childComponentsRefs = ref([]);

// 验证相关
const validMsg = ref('');
const validMsgCount = ref(0);
const dialogCaptchaVisible = ref(false);
const captchaData = ref(null);
const submitType = ref(null);

// 设置子组件引用
const setChildComponentRef = (el) => {
  if (el) {
    childComponentsRefs.value.push(el);
  }
};

// 表单分组数据处理
const onDoneGroup = (data) => {
  const index = proFormGroup.value.findIndex(obj => obj.groupId === data.groupId);
  if (index >= 0) {
    proFormGroup.value[index].values = data.values;
  } else {
    proFormGroup.value.push(data);
  }
  hasUnsavedChanges.value = true;
};

// 删除文件处理
const onDoneDeleteFile = (data) => {
  const index = proFormDelFile.value.findIndex(obj => obj.prop === data.prop);
  if (index >= 0) {
    proFormDelFile.value[index].values = data.values;
  } else {
    proFormDelFile.value.push(data);
  }
  hasUnsavedChanges.value = true;
};

// 验证码处理
const handleCaptchaCode = (data) => {
  console.log("验证码处理===",data)
  captchaData.value = data;
};

// 返回处理
const handleBack = async () => {
  if (hasUnsavedChanges.value&&query?.enterType!=='sqpreview') {
    try {
      await ElMessageBox.confirm('有未保存的更改，确定要离开吗？', '提示', {
        type: 'warning',
      });
      doBack();
    } catch {
      // 用户取消
    }
  } else {
    doBack();
  }
};

const doBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/zizhu/apply/' + routeType,
    query: {currentActiveName: routeParams.value.activeName},
  });
};

// 表单验证
const validateAllForms = async () => {
  validMsg.value = '';
  validMsgCount.value = 0;

  try {
    const results = await Promise.all(
      childComponentsRefs.value.map(child =>
        new Promise(resolve => {
          child.proFormRef?.validate((valid, obj) => {
            if (!valid && obj) {
              validMsgCount.value += Object.keys(obj).length;
            }
            resolve(valid);
          });
        }),
      ),
    );

    return results.every(Boolean);
  } catch (error) {
    console.error('验证出错:', error);
    return false;
  }
};

// 准备提交数据
const prepareSubmitData = () => {
  const result = {};

  // 处理表单数据
  proFormGroup.value.forEach(res => {
    Object.entries(res.values).forEach(([key, value]) => {
      if (value) {
        // 处理特殊字段类型
        if (key.includes('_regionsDetail')) {
          const [prefix] = key.split('_');
          result[prefix] = [result[prefix].join(), value].join(',');
        } else {
          result[key] = value;
        }
      }
    });
  });

  // 处理删除的文件
  if (proFormDelFile.value.length > 0) {
    const resultFile = {};
    proFormDelFile.value.forEach(res => {
      resultFile[res.prop] = res.values;
    });
    result.deleteFileIds = JSON.stringify(resultFile);
  }

  // 添加其他必要字段
  let resData = {
    ...result,
    type: routeType,
    projectId: currentProjectId,
    sfzc: submitType.value,
    ...(routeParams.value.applicationId && {id: routeParams.value.applicationId}),
    ...(captchaData.value && captchaData.value),
  };
  console.log(JSON.stringify(resData),resData,captchaData.value)
  return resData
};

// 提交处理
const handleSubmit = async (type) => {
  submitType.value = type;

  if (!(await validateAllForms())) {
    validMsg.value = `共有 ${validMsgCount.value} 项校验不通过`;
    validMsgCount.value = 0;
    return;
  }

  // 检查是否需要验证码
  if (currentData.value?.qysqyzm === '是') {
    dialogCaptchaVisible.value = true;
  } else {
    await doSubmit();
  }
};

const handleCaptchaSubmit = () => {
  dialogCaptchaVisible.value = false;
  doSubmit();
};

const doSubmit = async () => {
  const data = prepareSubmitData();
  loading.value = true;

  try {
    console.log(JSON.stringify(data));
    // const formData = toFormData(data);
    const formData = toFormDataWj(data);
    console.log(formData);
    const msg = await operationApply(formData);
    EleMessage.success(msg);
    hasUnsavedChanges.value = false;
    doBack();
  } catch (e) {
    EleMessage.error(e.message || '提交失败');
  } finally {
    loading.value = false;
  }
};

// 数据获取函数
const fetchProjectInfo = async () => {
  loading.value = true;
  try {
    const data = await getFormApplyProjectInfoById(currentProjectId);
    if (data) {
      let title = '';
      if (routeParams.value.enterType === 'add') title = `${data.xmmc} 申请`;
      if (routeParams.value.enterType === 'edit') title = `${data.xmmc} 申请记录编辑`;
      if (routeParams.value.enterType === 'sqpreview') title = `${data.xmmc} 项目申请查看`;

      if (title) setPageTabTitle(title);
      currentData.value = data;
    }
  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    loading.value = false;
  }
};

const fetchApplicationInfo = async () => {
  loading.value = true;
  try {
    const data = await getFormApplyApplicationInfo({projectId: currentProjectId});
    if (data) {
      routeParams.value.applicationId = data.id;
    }
  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    loading.value = false;
  }
};

const fetchFieldGroupList = async () => {
  leftData.value = [];
  rightData.value = [];
  loading.value = true;

  try {
    const list = await getFormApplyFieldGroupList({
      projectId: currentProjectId,
      applicationId: routeParams.value.applicationId,
    });
    console.log(list);
    if (list) {
      const leftArray = [];

      list.forEach((item, index) => {
        item.applicationId = routeParams.value.applicationId;
        item.enType = routeParams.value.enterType;
        item.userType = routeParams.value.userType;
        item.title = item.groupName;

        if (item.infoType === 'base') {
          item.sort = index + 1;
          item.span = 24;
          item.enType = 'preview';
          leftArray.push(item);
        } else {
          item.sort = item.dicGroupId ? index + 1 : index + 10;
          rightData.value.push(item);
        }
      });

      leftData.value = groupArr(leftArray.sort(compare('sort')), 'infoType');
      rightData.value = rightData.value.sort(compare('sort'));
      console.log(JSON.stringify(leftData.value),rightData.value)
    }
  } catch (e) {
    EleMessage.error(e.message);
  } finally {
    loading.value = false;
  }
};

// 初始化加载数据
onMounted(async () => {
  if (!currentProjectId) return;

  await fetchProjectInfo();

  if (routeParams.value.enterType === 'add') {
    await fetchApplicationInfo();
  }

  await fetchFieldGroupList();
});

// 组件名称
defineOptions({
  name: 'ZIZHUAPPLYDETAILS',
});
</script>

<style lang="scss" scoped>
.user-container {
  overflow: auto;
  background: #ffffff;
  padding: 16px;
}

.error-message {
  float: right;
  padding-right: 40px;
  transition: all 0.3s;
}

:deep(.el-collapse) {
  border-bottom: unset !important;
}

:deep(.el-collapse-item) {
  padding-left: 12px !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
