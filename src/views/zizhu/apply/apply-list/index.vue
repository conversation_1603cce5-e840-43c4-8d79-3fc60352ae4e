<!--资助申请记录列表-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="openApply(row)">
            编辑/查看
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" underline="never" @click="remove(row)">
            删除
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="info" underline="never"> 下载申请表 </el-link>
          <el-divider direction="vertical" />
          <el-link type="info" underline="never"> 荣誉证书 </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit
      v-model="showEdit"
      :currentXmId="currentXmId"
      :routeType="routeType"
      :currentXmData="current"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { queryPageApplicationInfo, removesApply } from '../api';
  import { useRouter } from 'vue-router';
  import Edit from '@/views/zizhu/apply/components/edit.vue';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const routeType = path.split('/')[4];

  let currentXmId = params?.id;

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'year',
      label: '评定年份'
    },
    {
      prop: 'createTime',
      label: '申请时间'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPageApplicationInfo(routeType, {
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType,
      projectId: currentXmId
    });
  };

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开组group编辑弹窗 */
  const openApply = (row) => {
    current.value = row ?? null;
    currentXmId = row?.projectId ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removesApply(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SelectorConfig'
  };
</script>
