<!--资助申请-->
<template>
  <ele-drawer size="60%"
              :title="enterType==='preview'?'申请表单预览':(currentXmData&&currentXmData.enTypeName)"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-card :body-style="{padding: '0px !important' }">
      <div ref="containerRef" :style="{height: pageHeight+'px',overflow:'auto'}">
        <div v-for=" (left, index) in leftData">
          <template v-if="left.type==='base'">
            <form-preview v-for="group in left.list" :ref="setChildComponentRef"
                          :key="'formKey'+index"
                          :routeType="routeType"
                          :userType="currentXmData?.userType"
                          :currentData="currentData"
                          :currentGroup="group"
                          @onDoneGroup="onDoneGroup"
                          @onDoneDeleteFile="onDoneDeleteFile"/>
          </template>
          <template v-else>
            <el-divider border-style="dashed" style="margin: 0"/>
            <ele-text size="md" style="margin-top: 10px;margin-left: 10px;">多条信息</ele-text>
            <div class="info-tags" style="margin-top: 12px;margin-left: 10px;">
              <el-tag v-for="group in left.list" type="info" size="small" :disable-transitions="true">
                {{ group.title }}
              </el-tag>
            </div>
            <el-divider border-style="dashed" style="margin: 0"/>
          </template>
        </div>
        <template v-for="group in rightData">
          <!--          group=={{ group }}<br/>-->
          <table-preview v-if="group.listFlag==='是'"
                         :key="'formKey'+index"
                         :labelWidth="config.labelWidth"
                         :grid="config.grid"
                         :routeType="routeType"
                         :currentGroup="group"/>
          <form-preview v-else :ref="setChildComponentRef"
                        :key="'formKey'+index"
                        :routeType="routeType"
                        :userType="currentXmData?.userType"
                        :currentData="currentData"
                        :currentGroup="group"
                        @onDoneGroup="onDoneGroup"
                        @onDoneDeleteFile="onDoneDeleteFile"/>
        </template>
      </div>
    </ele-card>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">返回</el-button>
      <el-button v-if="currentXmData&&currentXmData.enType==='add'&&currentXmData.sfxszc==='是'" size="small"
                 :loading="loading"
                 @click="onSubmit('是')" plain>暂存
      </el-button>
      <el-button v-if="currentXmData&&['add','edit'].includes(currentXmData.enType)" size="small" type="primary"
                 plain :loading="loading" @click="onSubmit('否')">
        提交
      </el-button>
    </template>
    <!--    项目申请开启验证码-->
    <el-dialog v-model="dialogCaptchaVisible" title="项目申请开启验证码" width="420"
               :close-on-click-modal="false"
               append-to-body
               draggable
               align-center>
      <GraphicCaptcha @updateCaptchaCode="handleCaptchaCode"/>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="dialogCaptchaVisible = false">取消</el-button>
          <el-button size="small" plain type="primary" @click="handleCaptchaCodeSubmit">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </ele-drawer>
</template>

<script setup>
import {ref, onMounted, watch, reactive, h} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import TablePreview from "@/views/personInfo/st/components/table-preview.vue";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {compare, groupArr, toFormData} from '@/utils/common_bak2.js';
import {getFormApplyApplicationInfo, getFormApplyFieldGroupList, operationApply} from '../api/index.js'
import {usePageTab} from "@/utils/use-page-tab.js";
import GraphicCaptcha from "../../components/GraphicCaptcha.vue"
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {removePageTab, getRouteTabKey,} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emits = defineEmits(['done', 'update:modelValue']);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  currentXmId: String,
  routeType: String,
  enterType: String,//申请表单预览
  currentXmData: Object,
});

/** 表单页面回显数据 */
const currentData = ref({});

/** 组数据 */
const data = ref([]);
/** 提交状态 */
const loading = ref(false);
/** 表单配置 */
const config = reactive({
  labelWidth: 120,
  grid: 3
});

const queryFormApplyApplicationInfo = async () => {
  loading.value = true;
  await getFormApplyApplicationInfo({
    projectId: props.currentXmId,
    // applicationId: '',
  }).then((list) => {
    loading.value = false;
    if (list) {
      currentData.value.applicationId = list.id
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    query()
  });
};

const leftData = ref([]);
const rightData = ref([]);

/** 查询资助申请组信息 */
const query = async () => {
  leftData.value = []
  rightData.value = []
  loading.value = true;
  await getFormApplyFieldGroupList({
    projectId: props.currentXmId,
    applicationId: '',
  }).then((list) => {
    loading.value = false;
    let leftArray = []
    if (list) {
      list.forEach((item, index) => {
        item.applicationId = currentData.value?.applicationId ?? ""
        item.enType = currentData.value?.enType ?? props.enterType
        item.userType = currentData.value?.userType ?? ""
        item.title = item.groupName
        // item.mark = 'zzapply'
        if (item.infoType === 'base') {
          item.sort = index + 1
          item.span = 24
          leftArray.push(item)
        } else {
          item.sort = item.dicGroupId ? index + 1 : index + 10
          if (item.infoType === 'apply') {
            rightData.value.push(item)
          } else {
            if (item.dicGroupId) {
              leftArray.push(item)
            } else {
              rightData.value.push(item)
            }
          }
        }
      })
      leftArray = leftArray.sort(compare('sort'))
      rightData.value = rightData.value.sort(compare('sort'))
      leftData.value = groupArr(leftArray, 'infoType');
      // leftData.value = leftArray;
    }
    // list = list.sort(compare('sort'))
    // data.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const childComponentsRefs = ref([]);

// 设置子组件的引用
const setChildComponentRef = (el) => {
  if (el) {
    childComponentsRefs.value.push(el);
  }
}

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  let result = new Promise((resolve, reject) => {
    formChild.proFormRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}
const dialogCaptchaVisible = ref(false);

const setCaptchaData = ref(null)
const handleCaptchaCode = (data) => {
  setCaptchaData.value = data
}

const handleCaptchaCodeSubmit = () => {
  dialogCaptchaVisible.value = false
  save();
}
const setSfzc = ref(null)
const onSubmit = async (sfzc) => {
  setSfzc.value = sfzc;
  // 遍历表单数组，依次对每个表单进行校验
  await childComponentsRefs.value.map((child) => {
    checkForm(child)
  })
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []//每次请求完要清空数组
    if (allTrue) {
      //启用验证码
      if (currentData.value && currentData.value.qysqyzm === '是') {
        dialogCaptchaVisible.value = true;
      } else {
        // 执行后续操作
        save()
      }
    } else {
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
}

/** 提交 */
const save = () => {
  let resData = proFormGroup.value;
  let result = {};
  resData.forEach(res => {
    const value = res.values;
    Object.keys(value).forEach((key) => {
      const resValue = value[key];
      if (resValue) result[key] = resValue;
    })
  })
  Object.keys(result).forEach((key) => {
    //处理type=regions类型，省市区+详细地址逻辑处理
    if (key.includes('_regionsDetail')) {
      let keyArray = key.split('_')
      result[keyArray[0]] = [result[keyArray[0]].join(), result[key]].join(",")
    }
  })
  let delFileIdData = proFormDelFile.value;
  let resultFile = {};
  delFileIdData.forEach(res => {
    resultFile[res.prop] = res.values;
  })
  if (resultFile) result['deleteFileIds'] = JSON.stringify(resultFile)
  result.type = props.routeType;
  result.projectId = props.currentXmId;
  result.sfzc = setSfzc.value;//是否暂存
  if (currentData.value.enType === 'edit') result.id = currentData.value.id
  if (currentData.value.applicationId) result.id = currentData.value.applicationId
  if (setCaptchaData.value) {
    result = Object.assign(result, setCaptchaData.value)
  }
  // let data = toFormData({...result})
  console.log(data)
  loading.value = true;
  operationApply(data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emits('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};


/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  // proFormRef.value = data.proFormRef;
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
};

/** 存放删除的图片ID，附件ID*/
const proFormDelFile = ref([]);

const onDoneDeleteFile = (data) => {
  let isExist = proFormDelFile.value.filter(obj => obj.prop === data.prop)
  if (isExist.length > 0) {
    proFormDelFile.value.filter(obj => {
      if (obj.prop === data.prop) obj.values = data.values;
    })
  } else {
    proFormDelFile.value.push(data)
  }
};


/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.currentXmData) currentData.value = props.currentXmData;
      if (currentData.value && currentData.value.enType === 'add') {
        console.log(1)
        queryFormApplyApplicationInfo()
      } else {
        console.log(2)
        query();
      }
    }
  }
);

</script>

<script>
export default {
  name: 'ZIZHUDETAILSFORM'
};
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}

.info-tags .el-tag {
  margin: 0 12px 8px 0;
}
</style>
