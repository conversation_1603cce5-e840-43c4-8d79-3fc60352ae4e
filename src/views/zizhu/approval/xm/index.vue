<template>
  <ele-page hide-footer  flex-table>
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     highlight-current-row
                     tooltip-effect="light"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #xmmc="{ row }">
          <el-link type="primary" underline="never"
                   @click.stop="openProjectInfo(row)">
            {{ row.xmmc }}
          </el-link>
        </template>
        <template #workflow="{ row }">
          <el-link type="primary" underline="never"
                   @click.stop="handleShowWorkFlow(row)">
            查看
          </el-link>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handleShowApprove(row)">
            办理
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <ProjectInfo v-model="showProjectInfo" :currentData="currentData"/>

    <Workflow v-model="showSorkFlow" :pageHeight="pageHeight"
              :workflowId="workflowId"/>
  </ele-page>
</template>

<script setup>
import {onMounted, ref, unref,} from 'vue';
import {getWorkflowByProjectId, queryPageProjectInfo,} from '../api/index.js';

import {useRouter} from "vue-router";
import Workflow from "@/views/dingding-flow/details/drawerflow.vue";
import { ElMessage as EleMessage} from "element-plus";
import ProjectInfo from "@/views/zizhu/components/projectInfo.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

let workflowId = null
let currentXmId = null
let routeType = null

routeType = routeType ? routeType : path.split("/")[4];

/** 表格实例 */
const tableRef = ref(null);

/** 是否显示导入弹窗 */
const showSorkFlow = ref(false);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'xmmc',
    slot: 'xmmc',
    label: '项目名称',
    minWidth: 110
  },
  {
    prop: 'sqsj',
    label: '可申请时间',
    formatter: (row) => {
      return (row.sqkssj && row.sqjzsj) ? row.sqkssj + '至' + row.sqjzsj : '无限制'
    },
  },
  {
    prop: 'ksqts',
    label: '可申请次数',
  },
  {
    prop: 'mrksqts',
    label: '每日可申请次数',
  },
  {
    prop: 'applyCount',
    label: '已申请记录条数',
  },
  // {
  //   prop: 'sqsj',
  //   label: '审核时间',
  //   slot: 'status'
  // },
  {
    prop: 'workflow',
    label: '审核流程',
    slot: 'workflow'
  },
  {
    prop: 'sqsj',
    label: '业务汇总',
    slot: 'status'
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 150,
    slot: 'action'
  }
]);

/** 列表选中数据 */
const selections = ref([]);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPageProjectInfo(routeType, {...where, ...orders, ...filters, page, limit})
};

const handleShowWorkFlow = (row) => {
  currentXmId = row.id;
  queryWorkflowByProjectId()
}

const showProjectInfo = ref(false);
const currentData = ref({});

const openProjectInfo = (row) => {
  showProjectInfo.value = true;
  currentData.value = row
  console.log(row)
}
const queryWorkflowByProjectId = () => {
  getWorkflowByProjectId(currentXmId).then((data) => {
    if (data) {
      workflowId = data.id;
      showSorkFlow.value = true;
      // assignFields({...initModel.value, ...data,});
      // currentProjectData.value = data;
      // addPageTab({
      //   title: `${data.xmmc}-编辑`,
      //   key: `/zizhu/details/${routeType}/${data.id}`,
      //   closable: true,
      //   meta: {icon: 'LinkOutlined'}
      // });
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  })
}

/** 打开项目审核列表 */
const handleShowApprove = (row) => {
  push({
    path: `/zizhu/approval/approve/${routeType}/${row.id}`
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};
</script>

<script>
export default {
  name: 'ZUZHUSHXMINDEX'
};
</script>
<style lang="scss" scoped>
.list-item-footer {
  display: flex;
  align-items: center;

  .list-item-tool {
    flex: 1;
    display: flex;
    justify-content: center;
    transition: color 0.2s;
    box-sizing: border-box;
    font-size: 12px;
    padding: 6px 0;
    cursor: pointer;

    .list-item-tool-trigger {
      width: 100%;
      padding: 16px 0;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      outline: none;
    }

    &:hover {
      color: var(--el-color-primary);

      .list-item-tool-trigger {
        color: var(--el-color-primary);
      }
    }
  }
}
</style>
