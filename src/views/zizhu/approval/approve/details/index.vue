<!--审核详情页-->
<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', borderWidth: '0',  width: '100%' ,}">
      <div ref="containerRef" :style="{height: (pageHeight+10)+'px',overflow:'auto',background:'#ffffff'}">
        <UserCard :config="config" :routeType="routeType" :leftData="leftData"/>
      </div>
      <template #body>
        <template v-for=" (group, index) in rightData">
          <table-preview v-if="group.listFlag==='是'"
                         :key="'formKey'+index"
                         :labelWidth="config.labelWidth"
                         :grid="config.grid"
                         :routeType="routeType"
                         :currentGroup="group"/>
          <form-preview v-else :key="'formKey'+index"
                        :labelWidth="config.labelWidth"
                        :routeType="routeType"
                        :currentData="group.currentData"
                        :currentGroup="group"/>
        </template>
        <ele-card v-if="enterType!=='preview'" header="审核信息" :body-style="{  height:'auto' }">
          approvalForm=={{ approvalForm }}<br/>
          <el-form ref="formRef"
                   :model="approvalForm"
                   :rules="approvalRules"
                   label-width="110px"
                   @submit.prevent="">
            <el-form-item label="审核状态" prop="result">
              <el-radio-group v-model="approvalForm.result" @change="onChange">
                <template v-for="item in approvalStatus"
                          :key="item.diyname">
                  <el-radio :value="item.name"
                            :label="item.label">
                    {{ item.label }}
                  </el-radio>
                </template>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="approvalForm.result==='退回'" label="退回规则" prop="returnWay">
              <el-radio-group v-model="approvalForm.returnWay">
                <template v-for="item in approvalreturnWay"
                          :key="item.name">
                  <el-radio :value="item.name"
                            :label="item.label">
                    {{ item.label }}
                  </el-radio>
                  <br/>
                </template>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <!-- approval -->
          <form-preview :key="'formKey'+index"
                        labelWidth="109px"
                        :routeType="routeType"
                        :currentGroup="approvalData"
                        @onDoneGroup="onDoneGroup"/>
        </ele-card>
        <ele-card header="审核记录"
                  :body-style="{  height:'auto' ,padding: '10px!important'}">
          <div class="time-line">
            <el-timeline :reverse="true">
              <el-timeline-item v-for="(activity, index) in activities"
                                :key="index"
                                :icon="activity.icon"
                                :type="activity.type"
                                :color="activity.color"
                                :size="activity.size"
                                :hollow="activity.hollow">
                <div class="ml10">
                  <div class="list-title">{{ activity.title }}</div>
                  <div class="list-company" :style="{color:activity.color}">节点状态:{{ activity.name }}</div>
                  <template v-if="activity.reviewType==='会签'">
                    <ele-check-card :items="activity.approvalNodeRecords"
                                    :item-style="{ width:'45%', marginRight:'10px',padding: '8px 12px', display: 'flex' }"
                                    style="display: flex; flex-wrap: wrap">
                      <template #item="{ item }">
                        <div style="flex: 1; overflow: hidden;">
                          <ele-text size="md">审核人:{{ item.realName }}</ele-text>
                          <div class="list-company1">{{ item.result }}</div>
                          <template
                            v-for="formFiled in activity.nodeForms.filter(node => node.nodeApproverId===item.nodeApproverId)">
                            {{ formFiled.fieldZh }}:{{ item[formFiled.fieldEn] }}<br/>
                          </template>
                          <ele-text size="sm">操作时间:{{ item.createTime }}</ele-text>
                        </div>
                      </template>
                    </ele-check-card>
                  </template>
                  <template v-else>
                    <template v-for="(item,index) in activity.approvalNodeRecords">
                      <template v-if="index===0">
                        <ele-text size="sm">审核人:{{ item.realName }}</ele-text>
                        <ele-text size="sm">审核状态:{{ item.result }}</ele-text>
                        <ele-text size="sm" v-if="item.result==='退回'" :style="{color:activity.color}">退回规则:{{
                            approvalreturnWay.filter(way => way.name === item.returnWay)[0].label
                          }}<br/>
                        </ele-text>
                        <ele-text
                          v-for="formFiled in activity.nodeForms.filter(node => node.nodeApproverId===item.nodeApproverId)">
                          <template v-if="formFiled.controlType==='imageUpload'">
                            {{ formFiled.fieldZh }}:
                            <div class="info-user-avatar">
                              <el-avatar :size="95">
                                <img v-if="item[formFiled.fieldEn] "
                                     :src="'/api/file/inline/'+ JSON.parse(item[formFiled.fieldEn] )[0].id"/>
                              </el-avatar>
                            </div>
                          </template>
                          <template v-else>
                            {{ formFiled.fieldZh }}:{{ item[formFiled.fieldEn] }}
                          </template>
                        </ele-text>
                        <ele-text size="sm">操作时间:{{ item.createTime }}</ele-text>
                      </template>
                    </template>
                  </template>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </ele-card>
      </template>
    </ele-split-panel>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="!approvalTimeIsCorrect" type="danger" :icon="CloseCircleOutlined">
        <span style="margin-left: 5px;">暂时不能审核，不在审核时间范围内！</span>
      </ele-text>
      <template #extra>
        <el-button size="small" @click="onBack">返回到列表</el-button>
        <el-button v-if="approvalTimeIsCorrect&&enterType!=='preview'" plain size="small" type="primary"
                   :loading="loading"
                   @click="onSubmit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
import {ref, reactive, unref} from 'vue';
import {ElMessage as EleMessage} from 'element-plus';
import {getFormGroup} from '@/views/zizhu/api/form-group-index.js';
import TablePreview from '@/views/personInfo/st/components/table-preview.vue';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import {comApproveStatus, compare, groupArr, toFormData} from '@/utils/common_bak2.js';
import {useRouter} from 'vue-router';
import {CloseCircleOutlined} from '@/components/icons';
import {
  approvalOperation,
  getApprovalApplicationInfo,
  getApprovalNodesBySqId,
  getNodeState,
} from '@/views/zizhu/approval/api/index.js';
import UserCard from '../../../components/user-card.vue';
import {usePageTab} from '@/utils/use-page-tab.js';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();
const emits = defineEmits(['done']);
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
let enterType = query?.enterType ?? '';
let currentResult = query?.currentResult ?? '';
let pathArray = path.split('/');
console.log(pathArray);
let routeType = pathArray[5];
let currentProjectId = pathArray[6];
let currentSqId = pathArray[7];
let currentNodeId = pathArray[8];
let oldNodeId = pathArray[8];

/** 数据 */
const data = ref([]);
const leftData = ref([]);
const rightData = ref([]);
const approvalApplicationInfo = ref(null);

const approvalTimeIsCorrect = ref(true);

const activities = ref([]);

/** 审核信息*/
const approvalData = ref(null);
const approvalStatus = ref(null);
const approvalreturnWay = ref([
  {
    label: '退回到上一节点',
    name: 'PRE_NODE',
  }, {
    label: '退回到申请人',
    name: 'APPLICANT',
  }]);

const approvalForm = ref({
  projectId: currentProjectId,
  id: [currentSqId],
  nodeId: currentNodeId,
  year: '',
  result: '',
  returnWay: '',
});

/** 表单验证规则 */
const approvalRules = reactive({
  result: [
    {
      required: true,
      message: '请选择审核状态',
      type: 'string',
      trigger: 'blur',
    },
  ],
  returnWay: [
    {
      required: true,
      message: '请选择退回规则',
      type: 'string',
      trigger: 'blur',
    },
  ],
});

const onChange = (newValue) => {
  if (newValue) {
    approvalForm.value.returnWay = '';
  }
};
/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId);
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    });
  } else {
    proFormGroup.value.push(data);
  }
};

/** 提交状态 */
const loading = ref(false);
/** 表单配置 */
const config = reactive({
  labelWidth: '110px',
  grid: 1,
});

/** 查询 */
const queryFormGroup = () => {
  loading.value = true;
  getFormGroup({
    projectId: currentProjectId,
    type: routeType,
  }).then((list) => {
    loading.value = false;
    let xgh = approvalApplicationInfo.value.xgh;
    let userType = approvalApplicationInfo.value.userType;
    let year = approvalApplicationInfo.value.year;
    let leftArray = [];
    if (list) {
      list.forEach((item, index) => {
        item.enType = 'sqpreview';
        item.xgh = xgh;
        item.userType = userType;
        item.sqId = currentSqId;
        item.year = year;
        if (item.infoType === 'base') {
          item.sort = index + 1;
          item.span = 24;
          item.enType = 'preview';
          item.title = item.groupName;
          leftArray.push(item);
        } else {
          item.title = item.groupName;
          item.sort = item.dicGroupId ? index + 1 : index + 10;
          if (item.infoType === 'apply') {
            item.currentData = approvalApplicationInfo.value;
            rightData.value.push(item);
          } else {
            if (item.dicGroupId) {
              rightData.value.push(item);
            } else {
              item.currentData = approvalApplicationInfo.value;
              rightData.value.push(item);
            }
          }
        }
      });
      leftArray = leftArray.sort(compare('sort'));
      rightData.value = rightData.value.sort(compare('sort'));
      leftData.value = groupArr(leftArray, 'infoType');
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 查询tabs */
const queryNodeState = () => {
  getNodeState({
    projectId: currentProjectId,
    year: approvalApplicationInfo.value.year,
    nodeId: currentNodeId,
  }).then((data) => {
    let baseItmes = comApproveStatus();
    baseItmes.forEach(item => {
      item.label = data[0][item.diyname];
      item.name = data[0][item.prename];
    });
    approvalStatus.value = baseItmes;
    approvalTimeIsCorrect.value = data[0].approvalTimeIsCorrect;
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

const queryApprovalNodesBySqId = () => {
  activities.value = []; // 清空原有数据
  getApprovalNodesBySqId(currentSqId).then((data) => {
    if (data.length > 0) {
      data.forEach(item => {
        let newObj = statusColor().filter(obj => obj.label === item.approvalNode.result);
        let obj = {
          title: item.approvalNode.nodeName,
          name: item.approvalNode.result,
          content: '',
          timestamp: item.approvalNode.updateTime,
          size: 'large',
          ...newObj[0],
          ...item,
        };
        // obj.icon = item.reviewType === '会签' ? 'Stamp' : 'CircleCloseFilled';
        activities.value.push(obj);
      });
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

const queryApprovalApplicationInfo = () => {
  loading.value = true;
  getApprovalApplicationInfo(currentSqId).then((list) => {
    loading.value = false;
    if (list) {
      approvalApplicationInfo.value = list;
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    queryFormGroup();
    queryNodeState();
    queryApprovalNodesBySqId();
    approvalData.value = {
      groupName: '审核信息',
      title: '',
      id: currentSqId,
      sqId: currentSqId,
      nodeId: currentNodeId,
      infoType: 'approval',
      listFlag: '否',
      projectId: currentProjectId,
      sort: '',
      type: routeType,
      userType: approvalApplicationInfo.value.userType,
      xgh: approvalApplicationInfo.value.xgh,
      year: approvalApplicationInfo.value.year,
    };
    approvalForm.value.year = approvalApplicationInfo.value.year;
  });
};

// 处理下级审批逻辑
// 处理下级审批逻辑（不更新路由参数）
const handleNextApproval = (currentNodeId) => {
  if (currentNodeId) {
    // 更新当前节点ID（仅内部状态，不修改路由）
    approvalForm.value.nodeId = currentNodeId;

    // 重新获取审核表单和记录
    activities.value = []; // 清空原有数据
    queryNodeState();
    queryApprovalNodesBySqId();
    approvalData.value = {...approvalData.value, nodeId: currentNodeId};

    // 重置表单
    approvalForm.value.result = '';
    approvalForm.value.returnWay = '';
    proFormGroup.value = []; // 清空表单字段

    EleMessage.success('已加载下级审批节点数据');
  }
};

const onSubmit = () => {
  let resData = proFormGroup.value;
  if (!approvalForm.value.result) {
    EleMessage.error('请选择审核状态');
    return;
  }

  let newObj = Object.assign(approvalForm.value, resData.length > 0 ? resData[0].values : {});
  loading.value = true;
  let data = toFormData({...newObj});

  approvalOperation(data).then((rData) => {
    loading.value = false;
    EleMessage.success('操作成功');
    // 根据返回数据判断是否有下级审批
    if (rData) {
      // 有下级审批，
      currentNodeId = rData;
      handleNextApproval(rData);
    } else {
      // 没有下级审批，返回列表
      onBack();
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};
/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/zizhu/approval/approve/' + routeType + '/' + currentProjectId,
    query: {currentNodeId: oldNodeId, currentResult: currentResult},
  });
};

queryApprovalApplicationInfo();
</script>

<script>
export default {
  name: 'ZIZHUDETAILSFORM',
};
</script>

<style scoped>
.ml10 {
  margin-left: 10px;
}

.list-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.list-company {
  font-size: 14px;
  margin-bottom: 8px;
}

.list-company1 {
  font-size: 14px;
  margin: 5px 0;
  color: #666;
}

.list-desc {
  font-size: 13px;
  color: #999;
  margin: 5px 0;
}

.info-user-avatar {
  margin: 5px 0;
}

.time-line {
  padding: 10px;
}
</style>
