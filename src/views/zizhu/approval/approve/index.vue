<!--资助表单申请-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <ele-tabs type="indicator"
                  size="small"
                  v-model="activeName"
                  :items="tabsItems">
          <template #label="{ item, label}">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <el-button v-if="activeName&&nodeStateArray&&nodeStateArray.length>1" type="primary" size="small"
                   class="ele-btn-icon" plain
                   @click="dialogFormVisible=true"> 切换审核的节点信息
        </el-button>
      </template>
      <Search v-if="activeName" :searchFieldData="searchFieldData"
              @search="reloadData"
              @handleClick="handleClickEvent"/>
      <!-- 表格 -->
      <ele-pro-table v-if="activeName" ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :toolbar="false"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     :footer-style="{ paddingBottom: '3px' }"
                     highlight-current-row>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="openApprovalDetail(row,'')">
            审批
          </el-link>
          <el-divider v-if="activeName!=='待审批'" direction="vertical"/>
          <el-link v-if="activeName!=='待审批'" type="primary" underline="never"
                   @click="openApprovalDetail(row,'preview')">
            查看
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 多审核节点选择弹框 -->
    <el-dialog v-model="dialogFormVisible" title="系统提示" width="500"
               :show-close="false"
               :close-on-click-modal="false"
               draggable
               align-center>
      <ele-text size="md">你当前有多个审核节点，请选择你要审核的节点信息：<br/></ele-text>
      <div style="margin-left: -8px;">
        <ele-check-card v-model="checkedNodeId"
                        :items="nodeStateArray"
                        :item-style="{ margin: '8px', padding: '8px 12px', display: 'flex' }"
                        style="display: flex; flex-wrap: wrap">
          <template #item="{ item }">
            <el-icon :size="22" style="margin-right: 8px;">
              <SetUp/>
            </el-icon>
            {{ item.nodeName }}
          </template>
        </ele-check-card>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" plain @click="handleDialogSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogPlspVisible" title="批量审批" width="500"
               :close-on-click-modal="false"
               draggable
               align-center>
      <div style="margin-left: -8px;">
        <approval-status ref="childFormRef"
                         :currentProjectId="currentXmId"
                         :currentNodeId="checkedNodeId"
                         :currentProjectData="currentProjectData"/>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogPlspVisible = false">关闭</el-button>
          <el-button type="primary" plain @click="handleBatchOperation">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <DiyListSearch v-model="showPerSelector"
                   :diyHearder="diyHearder"
                   :currentDiyType="currentDiyType"
                   :RandomString="RandomString"
                   :currentXmId="currentXmId"
                   :activeName="activeName"
                   :currentNodeId="checkedNodeId"
                   :currentProjectData="currentProjectData"
                   @done="refeshApprovel"/>

  </ele-page>
</template>

<script setup>
import {computed, reactive, ref, unref, watch} from 'vue';
import {
  queryPageProjectInfo,
  queryPageApprovalList,
  getNodeState,
  getApprovalCustomField, approvalBatchOperation, approvalOperation,
} from '../api/index.js';
import Search from '../components/search.vue';
import DiyListSearch from '../components/diyListSearch.vue';
import {useRouter} from 'vue-router';
import {comApproveStatus, generateRandomString, groupArr, insertAtIndex, toFormData} from '@/utils/common_bak2.js';
import {ElMessage as EleMessage} from 'element-plus';
import {getFormProjectById, getProjectTemplateCheck} from '@/views/zizhu/api/index.js';
import {DeleteOutlined, DownloadOutlined} from '@/components/icons/index.js';
import {usePageTab} from '@/utils/use-page-tab.js';
import {ElMessageBox} from 'element-plus';
import {getToken} from '@/utils/token-util.js';
import ApprovalStatus from '@/views/zizhu/approval/components/approvalStatus.vue';

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
let pArray = path.split('/');
console.log(pArray);
const routeType = pArray[4];
const currentXmId = pArray[5];

let checkedNode = ref(null);
let checkedNodeId = ref(null);

/** 标签页选中 */
const activeName = ref(null);
const tabsItems = ref([]);

let backNodeId = query?.currentNodeId ?? null;
checkedNodeId.value = backNodeId;
activeName.value = query?.currentResult ?? null;

/** 表格实例 */
const tableRef = ref(null);
const columns = ref([]);

/** 请求状态 */
const loading = ref(true);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);
/** =====人员选择器=====start=*/
const perSelectedData = ref([]);
const showPerSelector = ref(false);
const diyHearder = ref(null);
const currentDiyType = ref(null);
let RandomString = '';

let diyListSearchRandomBack = '';
let activeNameBack = '';
const refeshApprovel = (data) => {
  console.log('refeshApprovel===', data, routeType, currentXmId, currentProjectData.value);
  diyListSearchRandomBack = data.diyListSearchRandom;
  checkedNodeId.value = data.currentNodeId;
  activeNameBack = data.activeName;
  if (diyListSearchRandomBack) {
    queryNodeState();
    queryApprovalCustomField();
  }
};

const openPerSelector = (mark) => {
  RandomString = generateRandomString(10);
  currentDiyType.value = mark;
  diyHearder.value = mark === '列表' ? '自定义列设置' : '自定义查询条件设置';
  showPerSelector.value = true;
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  if (activeName.value === 'sqjl') {// 我的申请记录
    return queryPageProjectInfo(routeType, {
      ...where, ...orders, ...filters, page, limit, type: routeType,
    });
  } else {
    return queryPageApprovalList(activeName.value, {
      ...where, ...orders, ...filters,
      page,
      limit,
      type: routeType,
      result: activeName.value,
      projectId: currentXmId,
      nodeId: checkedNode.value?.nodeId,
    });
  }
};

/** 导出前先判断下是否有模版在下载 */
const exportBas = (templateType) => {
  loading.value = true;
  getProjectTemplateCheck({
    module: currentProjectData.value.id,
    templateType: templateType,
  }).then(data => {
    if (data) {
      let type = templateType === 'sqb' ? '申请表' : '汇总表';
      const rows = selections.value;
      let confirmMsg = '';
      if (!rows.length) {
        confirmMsg = '您确定要批量导出' + currentProjectData.value.xmmc + type;
      } else {
        confirmMsg = '您确定要导出' + rows.map((d) => d.userInfo.xm).join(', ') + currentProjectData.value.xmmc + type;
      }
      ElMessageBox.confirm(confirmMsg, '系统提示',
        {type: 'warning', draggable: true},
      ).then(() => {
        setTimeout(() => {
          // templateType
          window.location.href = BASE_URL + 'api/form/approval/' + templateType + '/exportByTemplate?access_token=' +
            accessToken
            + '&module=' + currentProjectData.value.id
            + '&projectId=' + currentProjectData.value.id
            + '&year=' + currentProjectData.value.year
            + '&result=' + activeName.value
            + '&nodeId=' + checkedNode.value.nodeId
            + '&id=' + rows.map((d) => d.id);
          loading.value = false;
        }, 600);
      }).catch(() => {
      });
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error('对不起，你要导出的模版文件不存在！');
  });
};

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = ref({});

/** 搜索 */
const reloadData = (where) => {
  lastWhere.value = where ? where : {};
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 用户信息 */
const nodeStateArray = ref();
/** 项目信息*/
const currentProjectData = ref();

const queryFormProjectById = () => {
  getFormProjectById(currentXmId).then((data) => {
    if (data) {
      currentProjectData.value = data;
    }
    loading.value = false;

    // 初始化时保留返回的状态
    backNodeId = query?.currentNodeId || null;
    activeNameBack = query?.currentResult || null;

    setPageTabTitle(currentProjectData.value.xmmc + '审核');
    queryNodeState();
    queryApprovalCustomField();
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
    setPageTabTitle(currentProjectData.value.xmmc + '审核');
    queryNodeState();
    queryApprovalCustomField();
  });
};

const searchFieldData = ref([]);
const queryApprovalCustomField = () => {
  searchFieldData.value = [];
  columns.value = [];
  getApprovalCustomField(currentXmId).then((data) => {
    if (data) {
      let rData = groupArr(data, 'customType');
      console.log(rData)
      rData.forEach((r) => {
        if (r.type === '列表') {
          columns.value = [
            {
              type: 'selection',
              columnKey: 'selection',
              width: 45,
              align: 'center',
              fixed: 'left',
            },
          ];
          r.list.forEach((rlist) => {
            let obj = {
              // prop: rlist.infoType === 'base' ? 'userInfo.' + rlist.fieldEn : rlist.fieldEn,
              prop: rlist.infoType === 'base' ? 'userInfo.' + rlist.fieldEn : rlist.fieldEn,
              label: rlist.fieldZh,
            };
            columns.value.push(obj);
          });

          columns.value.push(
            {
              columnKey: 'action',
              label: '操作',
              slot: 'action',
              fixed: 'right',
            },
          );
        } else {
          searchFieldData.value = r.list;
        }
      });
    }
    loading.value = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
};

const form = ref({});
const dialogFormVisible = ref(false);

const dialogPlspVisible = ref(false);

/** 查询tabs */

const queryNodeState = () => {
  getNodeState({projectId: currentXmId, year: currentProjectData.value.year}).then((data) => {
    if (data) {
      data.forEach(item => {
        item.value = item.nodeId;
      });
      nodeStateArray.value = data ?? [];

      // 优先使用返回的currentNodeId，其次使用路由参数中的，最后使用第一个节点
      checkedNodeId.value = backNodeId || query?.currentNodeId || data[0]?.nodeId;

      // 优先使用返回的currentResult，其次使用路由参数中的
      const resultToUse = query?.currentResult || backNodeId;

      if (data.length > 1) {
        if (checkedNodeId.value) {
          checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
          handleDialogSubmit(resultToUse); // 传入要使用的result
        } else {
          dialogFormVisible.value = true;
        }
      } else {
        checkedNodeId.value = data[0].nodeId;
        handleDialogSubmit(resultToUse); // 传入要使用的result
      }
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};


watch(
  activeName,
  (newVal) => {
    if (newVal) {
      console.log(newVal)
      reloadData();
    }
  },
  {immediate: true},
);

const handleClickEvent = (type) => {
  if (['sqb', 'hzb'].includes(type)) {
    exportBas(type);
  } else if (['列表', '查询'].includes(type)) {
    openPerSelector(type);
  } else if (type === 'plsp') {
    dialogPlspVisible.value = true;
  }
};

/** 打开审核详情页 */
const openApprovalDetail = (row, mark) => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: `/zizhu/approval/approve/details/${routeType}/${row.projectId}/${row.id}/${checkedNode.value.nodeId}`,
    query: {
      enterType: mark,
      currentResult: activeName.value,
    },
  });
};

const childFormRef = ref(null);
const handleBatchOperation = async () => {
  try {
    // 调用子组件的验证方法
    await childFormRef.value.validate();
    // 获取子组件的表单数据
    // console.log(childFormRef.value.getFormData?.()); // 或调用方法
    // 提交逻辑...
    loading.value = true;
    let data = Object.assign({id: selections.value?.map((d) => d.id).join(',')}, lastWhere.value,
      childFormRef.value.approvalForm);
    console.log(JSON.stringify(data));
    let rData = toFormData({...data});
    approvalBatchOperation(rData).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      reloadData();
    });
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
const handleDialogSubmit = (presetResult = null) => {
  if (!checkedNodeId.value) {
    EleMessage.error('请选择你要审核的节点信息');
    return false;
  } else {
    checkedNode.value = nodeStateArray.value.find(item => item.nodeId === checkedNodeId.value);
    let baseItmes = comApproveStatus();
    baseItmes.forEach(item => {
      item.label = checkedNode.value[item.diyname];
      item.name = checkedNode.value[item.prename];
    });
    insertAtIndex(baseItmes, {
      label: '待审批',
      name: '待审批',
    }, 0);

    // 优先使用预设的结果，其次使用activeNameBack，最后使用第一个标签
    activeName.value = presetResult || activeNameBack || baseItmes[0].name;

    tabsItems.value = baseItmes;
    dialogFormVisible.value = false;
    reloadData();
    setPageTab({
      fullPath: '/zizhu/approval/approve/' + routeType + '/' + currentXmId,
      query: {
        currentNodeId: checkedNodeId.value,
        currentResult: activeName.value
      }
    });
  }
};

if (currentXmId) queryFormProjectById();
</script>

<script>
export default {
  name: 'APPROVEINDEX',
};
</script>

<style>

.el-radio-group {
  display: unset !important;
}

</style>
