<!--审核详情页-->
<template>
  <ele-page hide-footer flex-table>
    <ele-split-panel ref="splitRef"
                     space="6px"
                     size="20%"
                     :allow-collapse="true"
                     :resizable="true"
                     :responsive="true"
                     flex-table
                     :custom-style="{border: 'none', borderWidth: '0',  width: '100%' ,}">
      <div ref="containerRef" :style="{height: pageHeight+'px',overflow:'auto',background:'#ffffff'}">
        <UserCard :config="config" :routeType="routeType" :leftData="leftData"/>
      </div>
      <template #body>
        <template v-for=" (group, index) in rightData">
          <table-preview v-if="group.listFlag==='是'"
                         :key="'formKey'+index"
                         :labelWidth="config.labelWidth"
                         :grid="config.grid"
                         :routeType="routeType"
                         :currentGroup="group"/>
          <form-preview v-else :key="'formKey'+index"
                        :routeType="routeType"
                        :currentGroup="group"/>
        </template>
        <ele-card header="审核信息"
                  :body-style="{  height:'auto' }">
          <el-form ref="formRef"
                   :model="approvalForm"
                   :rules="approvalRules"
                   label-width="110px"
                   @submit.prevent="">
            <el-form-item label="审核状态" prop="result">
              <el-radio-group v-model="approvalForm.result" @change="onChange">>
                <template v-for="item in approvalStatus"
                          :key="item.diyname">
                  <el-radio :value="item.name"
                            :label="item.label">
                    {{ item.label }}
                  </el-radio>
                  <br/>
                </template>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="approvalForm.result==='退回'" label="退回规则" prop="returnWay">
              <el-radio-group v-model="approvalForm.returnWay">
                <template v-for="item in approvalreturnWay"
                          :key="item.name">
                  <el-radio :value="item.name"
                            :label="item.label">
                    {{ item.label }}
                  </el-radio>
                  <br/>
                </template>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <form-preview :key="'formKey'+index"
                        labelWidth="109px"
                        :routeType="routeType"
                        :currentGroup="approvalData"
                        @onDoneGroup="onDoneGroup"/>
        </ele-card>
        <ele-card header="审核记录"
                  :body-style="{  height:'auto' }">

          <div class="time-line">
            <el-timeline :reverse="true">
              <el-timeline-item v-for="(activity, index) in activities"
                                :key="index"
                                :icon="activity.icon"
                                :type="activity.type"
                                :color="activity.color"
                                :size="activity.size"
                                :hollow="activity.hollow"
                                :timestamp="activity.timestamp">
                <div class="ml10">
                  <div class="list-title">{{ activity.title }}</div>
                  <div class="list-company">{{ activity.name }}</div>
                  <div class="list-desc">{{ activity.content }}</div>
                </div>
              </el-timeline-item>
              <el-timeline-item/>
            </el-timeline>
          </div>
        </ele-card>
      </template>
    </ele-split-panel>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="!approvalTimeIsCorrect" type="danger" :icon="CloseCircleOutlined">
        <span style="margin-left: 5px;">暂时不能审核，不在审核时间范围内！</span>
      </ele-text>
      <template #extra>
        <!--          <el-button size="small" @click="onBack">关闭</el-button>-->
        <el-button v-if="approvalTimeIsCorrect" size="small" type="primary" plain :loading="loading" @click="onSubmit">
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
import {ref, onMounted, reactive, unref,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {getFormGroup,} from "@/views/zizhu/api/form-group-index.js";
import TablePreview from "@/views/personInfo/st/components/table-preview.vue";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {comApproveStatus, compare, groupArr, toFormData} from '@/utils/common_bak2.js';
import {useRouter} from "vue-router";
import {MoreFilled,} from '@element-plus/icons-vue'
import {CloseCircleOutlined,} from '@/components/icons';
import {
  approvalOperation,
  getApprovalApplicationInfo,
  getApprovalNodesBySqId,
  getNodeState
} from "@/views/zizhu/approval/api/index.js";
import UserCard from "@/views/zizhu/components/user-card.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emits = defineEmits(['done']);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path,} = unref(currentRoute);
let pathArray = path.split("/");
console.log(pathArray)
let routeType = pathArray[5]
let currentProjectId = pathArray[6]
let currentSqId = pathArray[7]
let currentNodeId = pathArray[8]

/** 数据 */
const data = ref([]);
const leftData = ref([]);
const rightData = ref([]);
const approvalApplicationInfo = ref(null);

const approvalTimeIsCorrect = ref(true);

/** 审核信息*/
const approvalData = ref(null);
const approvalStatus = ref(null);
const approvalreturnWay = ref([{
  label: '退回到上一节点',
  name: 1,
}, {
  label: '退回到申请人',
  name: 0,
}]);

const approvalForm = ref({
  projectId: currentProjectId,
  id: [currentSqId],
  nodeId: currentNodeId,
  year: '',
  result: '',
  returnWay: '',
});

/** 表单验证规则 */
const approvalRules = reactive({
  result: [
    {
      required: true,
      message: '请选择审核状态',
      type: 'string',
      trigger: 'blur'
    }
  ],
  returnWay: [
    {
      required: true,
      message: '请选择退回规则',
      type: 'string',
      trigger: 'blur'
    }
  ],
})


/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
};

const onChange = (newValue) => {
  if (newValue) {
    approvalForm.value.returnWay = '';
  }
}
/** 提交状态 */
const loading = ref(false);
/** 表单配置 */
const config = reactive({
  labelWidth: 'auto',
  grid: 1
});

/** 查询 */
const query = () => {
  loading.value = true;
  getFormGroup({
    projectId: currentProjectId,
    type: routeType,
  }).then((list) => {
    loading.value = false;
    let xgh = approvalApplicationInfo.value.xgh;
    let userType = approvalApplicationInfo.value.userType;
    let year = approvalApplicationInfo.value.year;

    let leftArray = []
    if (list) {
      list.forEach((item, index) => {
        item.xgh = xgh
        item.userType = userType
        item.sqId = currentSqId
        item.year = year
        // item.title = item.groupName
        if (item.infoType === 'base') {
          item.sort = index + 1
          item.span = 24
          leftArray.push(item)
        } else {
          item.title = item.groupName
          item.sort = item.dicGroupId ? index + 1 : index + 10
          if (item.infoType === 'apply') {
            item.currentData = approvalApplicationInfo.value
            rightData.value.push(item)
          } else {
            if (item.dicGroupId) {
              // leftArray.push(item)
              rightData.value.push(item)
            } else {
              item.currentData = approvalApplicationInfo.value
              rightData.value.push(item)
            }
          }
        }
      })
      // list = list.sort(compare('sort'))
      leftArray = leftArray.sort(compare('sort'))
      rightData.value = rightData.value.sort(compare('sort'))


      leftData.value = groupArr(leftArray, 'infoType');
      // list = list.concat(approvalData)
    }
    // data.value = list ?? [];
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 查询tabs */
const queryNodeState = () => {
  getNodeState({
    projectId: currentProjectId,
    year: approvalApplicationInfo.value.year,
    nodeId: currentNodeId
  }).then((data) => {
    let baseItmes = comApproveStatus()
    baseItmes.forEach(item => {
      item.label = data[0][item.diyname]
      item.name = data[0][item.prename]
    })
    approvalStatus.value = baseItmes

    approvalTimeIsCorrect.value = data[0].approvalTimeIsCorrect;

  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

const queryApprovalNodesBySqId = () => {
  getApprovalNodesBySqId(currentSqId).then((data) => {
    if (data.length > 0) {
      data.forEach(item => {
        let obj = {
          title: item.approvalNode.nodeName,
          name: item.approvalNode.result,
          content: '',
          timestamp: item.approvalNode.createTime,
          size: 'large',
          type: 'primary',
          icon: MoreFilled,
        }
        activities.value.push(obj)
      })
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {

  });
};

const queryApprovalApplicationInfo = () => {
  loading.value = true;
  getApprovalApplicationInfo(currentSqId).then((list) => {
    loading.value = false;
    if (list) {
      approvalApplicationInfo.value = list
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    query();
    queryNodeState();
    queryApprovalNodesBySqId();
    approvalData.value = {
      groupName: "审核信息",
      title: "",
      id: currentSqId,
      sqId: currentSqId,
      nodeId: currentNodeId,
      infoType: "approval",
      listFlag: "否",
      projectId: currentProjectId,
      sort: '',
      type: routeType,
      userType: approvalApplicationInfo.value.userType,
      xgh: approvalApplicationInfo.value.xgh,
      year: approvalApplicationInfo.value.year,
    }
    approvalForm.value.year = approvalApplicationInfo.value.year
  });
};

const onSubmit = () => {
  let resData = proFormGroup.value;
  let newObj = Object.assign(approvalForm.value, resData[0].values,);
  console.log(newObj)
  loading.value = true;
  let data = toFormData({...newObj})
  approvalOperation(data).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const activities = ref([])

queryApprovalApplicationInfo()

</script>

<script>
export default {
  name: 'ZIZHUDETAILSFORM'
};
</script>

<style lang="scss" scoped>
.user-wrapper {
  display: flex;

  .user-side {
    width: 320px;
    margin: 0 16px 0 0;
    flex-shrink: 0;
  }

  .user-body {
    flex: 1;
  }
}

@media screen and (max-width: 928px) {
  .user-wrapper .user-side {
    width: 240px;
  }
}

@media screen and (max-width: 768px) {
  .user-wrapper {
    display: block;

    .user-side {
      width: auto;
      margin: 0 0 16px 0;
    }
  }
}

.el-collapse {
  border-bottom: unset !important;
}

.el-collapse-item {
  padding-left: 12px !important;
}

</style>
<style lang="scss" scoped>
.time-line {
  //margin-top: 50px;
  //margin-left: 200px;
}

.list-title {
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #181b1e;
}

.list-company {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #2991ff;
  margin-top: 15px;
  margin-bottom: 15px;
}

.list-desc {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #596878;
}

//
////左侧时间
//.time {
//  color: #409eff;
//  position: absolute;
//  left: -94px;
//  top: 1px;
//
//  .year {
//    font-size: 18px;
//    font-family: PingFangSC-Regular, PingFang SC;
//    font-weight: 400;
//    color: #20354a;
//  }
//
//  .day {
//    font-size: 14px;
//    font-family: PingFangSC-Regular, PingFang SC;
//    font-weight: 400;
//    color: #596878;
//    text-align: center;
//    margin-top: 10px;
//  }
//}

</style>
