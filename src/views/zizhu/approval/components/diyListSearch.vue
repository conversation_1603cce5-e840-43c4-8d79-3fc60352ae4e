<template>
  <ele-drawer size="66%"
              :title="diyHearder"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' ,marginTop:0}"
              @update:modelValue="updateModelValue">
    <div class="treeTransfer" :style="{ height: pageHeight+'px',}">
      <SetPerSelector paramMode="zzshzdylzdycx"
                      :perSelectedData="perSelectedData"
                      :RandomString="RandomString"
                      :currentDiyType="currentDiyType"
                      :currentXmId="currentXmId"
                      :userType="currentProjectData?.userType"
                      @done="onDoneSelector"/>
    </div>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button size="small" plain type="primary" :loading="loading" @click="onSubmit">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import SetPerSelector from "@/components/PerSelector/components/set-per-selector.vue";
import {ref,} from "vue";
import {generateRandomString,} from '@/utils/common_bak2.js';
import { ElMessage as EleMessage} from "element-plus";
import {setApprovalCustomField} from "@/views/zizhu/approval/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
// 定义emit
const emits = defineEmits(['done', 'update:modelValue'])

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  diyHearder: String,
  RandomString: String,
  currentXmId: String,
  currentDiyType: String,
  activeName: String,
  currentNodeId: String,
  currentProjectData: Object,
});
/** 提交状态 */
const loading = ref(false);
/** =====人员选择器=====start=*/
const perSelectedData = ref([]);

const onDoneSelector = (data) => {
  perSelectedData.value = data;
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

const onSubmit = () => {
  let setInfoData = perSelectedData.value
  let newFromArray = []
  setInfoData.forEach((item) => {
    let form = {
      // id: item.id,
      fieldEn: item.fieldEn,
      fieldZh: item.fieldZh,
      projectId: item.projectId,
      sort: item.sort,
      type: item.type,
      infoType: item.infoType,
      customType: item.customType
    }
    newFromArray.push(form)
  })
  save(newFromArray)
}

const save = (form) => {
  console.log("=====save=======", JSON.stringify(form))
  loading.value = true;
  setApprovalCustomField(props.currentXmId, form).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emits('done', {
      activeName: props.activeName,
      currentNodeId: props.currentNodeId,
      diyListSearchRandom: generateRandomString(10)
    });
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

</script>

<style scoped lang="scss">

</style>
