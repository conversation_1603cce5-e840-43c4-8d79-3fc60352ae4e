<template>
  <el-form ref="formRef"
           :model="approvalForm"
           :rules="approvalRules"
           label-width="110px"
           @submit.prevent="">
    <el-form-item label="审核状态" prop="result">
      <el-radio-group v-model="approvalForm.result" @change="onChange">>
        <template v-for="item in approvalStatus"
                  :key="item.diyname">
          <el-radio :value="item.name"
                    :label="item.label">
            {{ item.label }}
          </el-radio>
        </template>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="approvalForm.result==='退回'" label="退回规则" prop="returnWay">
      <el-radio-group v-model="approvalForm.returnWay">
        <template v-for="item in approvalreturnWay"
                  :key="item.name">
          <el-radio :value="item.name"
                    :label="item.label">
            {{ item.label }}
          </el-radio>
          <br/>
        </template>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>
<script setup>

import {reactive, ref, watch} from "vue";
import {getNodeState} from "@/views/zizhu/approval/api/index.js";
import {comApproveStatus} from '@/utils/common_bak2.js';
import { ElMessage as EleMessage} from "element-plus";

const props = defineProps({
  currentProjectId: String,
  currentNodeId: String,
  currentSqId: String,
  currentProjectData: Object,
});


const formRef = ref(null);

const approvalForm = ref({
  projectId: props.currentProjectId,
  // id: [props.currentSqId],
  nodeId: props.currentNodeId,
  year: '',
  result: '',
  returnWay: '',
});

const approvalStatus = ref(null);

const approvalreturnWay = ref([{
  label: '退回到上一节点',
  name: 'PRE_NODE',
}, {
  label: '退回到申请人',
  name: 'APPLICANT',
}]);
/** 表单验证规则 */
const approvalRules = reactive({
  result: [
    {
      required: true,
      message: '请选择审核状态',
      type: 'string',
      trigger: 'blur'
    }
  ],
  returnWay: [
    {
      required: true,
      message: '请选择退回规则',
      type: 'string',
      trigger: 'blur'
    }
  ],
})

const onChange = (newValue) => {
  if (newValue) {
    approvalForm.value.returnWay = '';
  }
}

/** 查询tabs */
const queryNodeState = () => {
  getNodeState({
    projectId: props.currentProjectId,
    year: props.currentProjectData.year,
    nodeId: props.currentNodeId
  }).then((data) => {
    let baseItmes = comApproveStatus()
    baseItmes.forEach(item => {
      item.label = data[0][item.diyname]
      item.name = data[0][item.prename]
    })
    approvalStatus.value = baseItmes
    // approvalTimeIsCorrect.value = data[0].approvalTimeIsCorrect;
  }).catch((e) => {
    EleMessage.error(e.message);
  }).finally(() => {
  });
};

watch(
  () => props.currentNodeId,
  (currentNodeId) => {
    if (currentNodeId) {
      queryNodeState()
    }
  },
  {immediate: true}
);

// 子组件中
// const emit = defineEmits(["update-form"]);
// watch(approvalForm.value, (newVal) => {
//   emit("update-form", newVal);
// }, { deep: true });

// 暴露 approvalForm 和表单验证方法
defineExpose({
  approvalForm,
  getFormData: () => approvalForm.value, // 或者暴露一个获取数据的方法
  validate: () => formRef.value.validate(), // 如果需要验证表单
  resetForm: () => formRef.value.resetFields(), // 如果需要重置表单
});
</script>
<style scoped lang="scss">

</style>
