/**
 * 表单审核端管理 控制器
 * */
import request from '@/utils/request.js';

/**
 * 分页查询表单项目记录（权限标识：form:'+(#param.type)+':approval:list）
 */
export async function queryPageProjectInfo(type, params) {
  const res = await request.get('/form/approval/' + type + '/pageProjectInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询申请审核列表
 */
export async function queryPageApprovalList(type, params) {
  const res = await request.get('/form/approval/' + type + '/pageApprovalList', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请记录ID或项目ID,年份查询节点自定义状态
 */
export async function getNodeState(params) {
  const res = await request.get('/form/approval/nodeState', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请ID查询审核记录
 * @param sqId
 * @returns {Promise<*>}
 */
export async function getApprovalNodesBySqId(sqId) {
  const res = await request.get('/form/approval/' + sqId + '/approvalNodes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询工作流
 * @param projectId
 * @returns {Promise<*>}
 */
export async function getWorkflowByProjectId(projectId) {
  const res = await request.get('/form/approval/' + projectId + '/workflow');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请ID查询申请信息
 * @param id
 */
export async function getApprovalApplicationInfo(id) {
  const res = await request.get('/form/approval/' + id + '/applicationInfo');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 自定义列表/查询条件字段查询
 * @param projectId
 * @param customType list=自定义列表，query=查询条件
 * @returns {Promise<*>}
 */
export async function getApprovalCustomField(projectId, params) {
  const res = await request.get('form/approval/' + projectId + '/customField', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function setApprovalCustomField(projectId, data) {
  const res = await request.post('form/approval/' + projectId + '/customField', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据申请信息查询当前用户审核表单字段
 *
 * @param applicationId        申请记录ID
 * @param projectId 项目ID
 * @param year      年份
 * @return
 */
export async function getApprovalNodeFormFieldList(params) {
  const res = await request.get('/form/approval/nodeFormFieldList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 单个或批量审核操作（权限标识：form:approval:operation）
 */
export async function approvalOperation(data) {
  const res = await request.post('/form/approval/operation', data);
  if (res.data.code === 0) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量审批
 * @param data
 * @returns {Promise<*>}
 */
export async function approvalBatchOperation(data) {
  const res = await request.post('/evaluate/approval/batchOperation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除限制条件（权限标识：condition:tempCondition:remove）
 */
export async function removes(data) {
  const res = await request.post('/condition/temp-condition/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
