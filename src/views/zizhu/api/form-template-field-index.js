/**表单模板配置*/
import request from '@/utils/request.js';

/**
 * 可配置字段查询不分页
 */
export async function getFormTemplateField(params) {
    const res = await request.get('/form/form-template-field', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**可配置字段列表*/
export async function getFormTemplateFieldList(params) {
    const res = await request.get('/form/form-template-field/fieldList', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/form/form-template-field/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFormTemplateFieldById(id) {
    const res = await request.post('/form/form-template-field/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/form/form-template-field/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/form/form-template-field/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
