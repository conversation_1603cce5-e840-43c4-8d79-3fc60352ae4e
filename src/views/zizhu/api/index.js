import request from '@/utils/request.js';

/**
 * 导入申请表/汇总表
 */
export async function importData(file, projectId, templateFile) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('module', projectId);
  formData.append('templateType', templateFile);
  const res = await request.post('/file/template', formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * templateCheck 检查申请表/汇总表是否有模版存在
 */
export async function getProjectTemplateCheck(params) {
  const res = await request.post('/file/template-check', params);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导入历史数据
 */
export async function importDataLssj(file, projectId) {
  const formData = new FormData();
  formData.append('file', file);
  // formData.append('codeType', codeType);
  const res = await request.post('/form/form-project/' + projectId + '/dataImport', formData);
  if (res.data.code === 0 || res.data.code === 2) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 项目申请表单发布
 */
export async function projectPublish(id) {
  const res = await request.post('/form/form-project/' + id + '/publish');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询工作流
 */
export async function getProjectWorkFlow(id) {
  const res = await request.get('/form/form-project/' + id + '/workflow');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 项目工作流添加或修改
 */
export async function operationWorkflow(id, data) {
  const res = await request.post('/form/form-project/' + id + '/workflowEdit', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getFormProject(params) {
  const res = await request.get('/form/form-project', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/form/form-project/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFormProjectById(id) {
  const res = await request.get('/form/form-project/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/form/form-project/operation', data);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 审核学院添加的项目
 * @param data
 * @returns {Promise<*>}
 */
export async function projectApprove(data) {
  const res = await request.post('/form/form-project/approve', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/form/form-project/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
