<!--限制条件库-->
<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            size="small"
            class="ele-btn-icon"
            @click="openEdit(null, 'add')"
          >
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove(row)">
            删除
          </el-button>
        </template>
        <template #conditionName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.conditionName }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #action="{ row }">
          <!--          <el-divider direction="vertical"/>-->
          <el-link type="primary" underline="never" @click="openFiled(row)">
            限制条件维护
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <Field v-model="showField" :currentData="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import { PlusOutlined } from '@/components/icons';
  import { queryPage, removes } from '../api';
  import { useRouter } from 'vue-router';
  import Edit from '../components/edit.vue';
  import Field from '../field/index.vue';
  import { comCondition } from '@/utils/common_bak2.js';

  // const {currentRoute, push} = useRouter();
  // const {params, path} = unref(currentRoute);
  // const routeType = path.split("/")[4];
  // console.log(1)

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'conditionKey',
      label: '英文名'
    },
    {
      prop: 'conditionName',
      slot: 'conditionName',
      label: '限制条件名称'
    },
    {
      prop: 'enable',
      label: '是否启用'
    },
    {
      prop: 'condition',
      label: '条件',
      formatter: (row) => {
        let value = row && row.condition ? row.condition.split(',') : [];
        let baseData = comCondition();
        if (value.length > 0) {
          return baseData
            .filter((item) => value.includes(item.value))
            .map((item) => item.label)
            .join(',');
        }
      }
    },
    {
      prop: 'module',
      label: '业务模块'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);
  const showField = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  const openFiled = (row) => {
    current.value = row ?? null;
    showField.value = true;
    // push({
    //   path: '/zizhu/condition/field/' + row.id,
    // })
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    NAME: 'CONDITIONSET'
  };
</script>
