<!--限制条件参数设置-->
<template>
  <ele-drawer size="40%"
              :title="'['+(currentCondition&&currentCondition.conditionName)+'] 设置'"
              :append-to-body="true"
              style="max-width: 100%"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <form-preview :ref="setChildComponentRef"
                  label-position="top"
                  :labelWidth="'auto'"
                  enType="conditionSet"
                  :currentData="currentData"
                  :currentGroup="currentCondition"
                  @onDoneGroup="onDoneGroup"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {reactive, ref,} from 'vue';
import {operation,} from '../api/form-restrict-index.js'
import {comCondition,} from '@/utils/common_bak2.js';
import { ElMessage as EleMessage} from "element-plus";
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  currentCondition: Object,
  currentData: Object,
  projectId: String,
});


/** 表格数据源 */
const datasource = ref([]);

/** 表单配置 */
const config = reactive({
  labelWidth: 120,
  grid: 1
});


/** 表格列配置 */
const columns = ref([
    {
      prop: 'conditionName',
      label: '限制条件名称',
    },
    {
      prop: 'condition',
      label: '条件',
      type: 'condition',
    },
    {
      prop: 'conditionValue',
      label: '限制项',
      type: 'conditionValue',
    },
    {
      prop: 'description',
      label: '说明',
    },
  ]
);

/** 提交状态 */
const loading = ref(false);
/** 表单数据 */
const form = reactive({
  users: []
});

/** 更新值 */
const updateValue = (index, prop, value) => {
  datasource.value[index][prop] = value;
};

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
};

const childComponentsRefs = ref([]);

// 设置子组件的引用
const setChildComponentRef = (el) => {
  if (el) {
    childComponentsRefs.value.push(el);
  }
}

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  let result = new Promise((resolve, reject) => {
    formChild.proFormRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}

const onSubmit = async () => {
  // 遍历表单数组，依次对每个表单进行校验
  childComponentsRefs.value.map((child) => {
    checkForm(child)
  })
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []//每次请求完要清空数组
    if (allTrue) {
      // 执行后续操作
      save()
    } else {
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
}

const save = () => {
  const data = proFormGroup.value
  let objData = {
    conditionId: props.currentCondition.id,
    projectId: props.projectId,
    conditionValue: JSON.stringify(data[0].values)
  }
  if (props.currentData) objData.id = props.currentData.id;
  loading.value = true;
  operation(objData).then((msg) => {
    loading.value = false;
    if (msg) EleMessage.success("限制条件设置成功");
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};


/** 更新modelValue */
const updateModelValue = (value) => {
  childComponentsRefs.value = [];
  emit('update:modelValue', value);
};

</script>

<style lang="scss" scoped>
</style>
