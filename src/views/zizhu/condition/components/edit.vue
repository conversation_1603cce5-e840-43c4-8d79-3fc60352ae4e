<template>
  <ele-drawer :size="520"
              :title="isUpdate ? '修改信息' : '新建信息'"
              :append-to-body="true"
              style="max-width: 100%"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              :title-style="{ fontSize: '16px' }"
              @update:modelValue="updateModelValue">
    <pro-form ref="formRef"
              size="small"
              :model="form"
              :items="formItems"
              label-width="auto"
              :labelPosition="'top'"
              @updateValue="setFieldValue"/>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small"
                 type="primary"
                 plain
                 :loading="loading"
                 @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import ProForm from '@/components/ProForm/index.vue';
import {operation} from '../api/index.js';
import { ElMessage as EleMessage } from 'element-plus';
import {
  comCondition,
  insertAtIndex,
} from '@/utils/common_bak2.js';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  conditionKey: '',
  conditionName: '',
  module: '',
  condition: [],
  description: '',
  sort: '',
  enable: '是'
});

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

/** 提交 */
const onSubmit = () => {
  console.log('form:', JSON.stringify(form));
  formRef.value?.validate?.((valid, obj) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    // 安全处理 condition
    const conditionValue = Array.isArray(form.condition)
      ? form.condition.join(',')
      : (form.condition || '');

    const data = {
      ...form,
      condition: conditionValue
    };

    operation(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const formItems = ref([]);
/** 表单项 */
const baseItems = ref([
  {prop: 'conditionKey', label: '英文名', type: 'input', required: true},
  {
    prop: 'conditionName',
    label: '限制条件名称',
    type: 'input',
    required: true
  },
  {prop: 'module', label: '业务模块', type: 'input'},
  {
    prop: 'condition',
    label: '条件',
    type: 'select',
    options: comCondition()
  },
  {prop: 'description', label: '说明', type: 'textarea'},
  // {
  //   prop: 'paramMode', label: '功能业务类型',
  //   type: 'dictSelect',
  //   typeKey: 'select',
  //   props: {
  //     code: 'personnelSelector',
  //     dicQueryParams: {
  //       "valueField": 'code'
  //     },
  //   },
  // },
  //
  {
    prop: 'enable',
    label: '是否启用',
    type: 'switch',
    required: true
  }
]);

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      formItems.value = [...baseItems.value];
      console.log(props.data);
      if (props.data) {
        assignFields({
          ...props.data,
          condition: props.data.condition
            ? props.data.condition.split(',')
            : []
        });
        isUpdate.value = true;
        insertAtIndex(
          formItems.value,
          {
            prop: 'sort',
            label: '排序',
            type: 'inputNumber',
            required: true
          },
          4
        );
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);
</script>
