<!--限制条件字段配置列表-->
<template>
  <ele-drawer size="78%"
              :title="'['+(currentData&&currentData.conditionName)+'] 字段维护'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px 8px!important' ,marginTop:0}"
              @update:modelValue="updateModelValue">

    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <TablePreview v-if="currentData" :ref="el => getRiskSpreadRef(el, 0)"
                    dataName=""
                    approverType="condition"
                    :approverId="currentData.id"
                    @onDoneGroup="handleDoneGroup"/>
    </ele-card>
    <template #footer>
      <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="updateModelValue(false)">返回</el-button>
      <el-button size="small" type="primary" plain @click="saveConditionField">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {computed, reactive, ref, unref,} from 'vue';
import {operation} from '../api/field-index.js';
import TablePreview from "@/components/DrawFlow/drawer/table-preview.vue";
import {generateRandomString} from '@/utils/common_bak2.js';
import {CloseCircleOutlined} from "@/components/icons/index.js";
import { ElMessage as EleMessage} from "element-plus";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  currentData: Object
});


const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};

const RandomString = computed(() => {
  return props.currentData ? generateRandomString(10) : ""
})

/** 存放form提交字段*/
const proFormData = ref(null);

const handleDoneGroup = async (data) => {
  proFormData.value = data;
};

const saveConditionField = async () => {
  await riskSpreadRefList.value?.forEach((child) => {
    checkForm(child)
  });
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []//每次请求完要清空数组
    if (allTrue) {
      // 执行后续操作
      save()
    } else {
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
}

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  validMsg.value = null;
  let result = new Promise((resolve, reject) => {
    formChild.formRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}

/** 请求状态 */
const loading = ref(true);

/** 提交 */
const save = () => {
  loading.value = true;
  let arrData = proFormData.value
  operation(arrData.workflowNodeForm).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false)
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};


</script>

<script>
export default {
  NAME: 'CONDITIONFIELDSET'
};
</script>

