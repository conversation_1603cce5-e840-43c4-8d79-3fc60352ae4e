/**
 * 表单限制条件控制器
 * */
import request from '@/utils/request.js';

/**
 * 查询表单限制条件（权限标识：form:formRestrict:list）
 */
export async function getFormTestrict(params) {
    const res = await request.get('/form/form-restrict', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询表单限制条件（权限标识：form:formRestrict:list）
 */
export async function queryPage(params) {
    const res = await request.get('/form/form-restrict/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询表单限制条件（权限标识：form:formRestrict:list）
 */
export async function getFormTestrictById(id) {
    const res = await request.get('/form/form-restrict/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改表单限制条件（权限标识：form:formRestrict:operation）
 */
export async function operation(data) {
    const res = await request.post('/form/form-restrict/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除表单限制条件（权限标识：form:formRestrict:remove）
 */
export async function removes(data) {
    const res = await request.post('/form/form-restrict/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
