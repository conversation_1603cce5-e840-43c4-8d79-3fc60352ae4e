/**
 * 限制条件控制器
 * */
import request from '@/utils/request.js';

/**
 * 查询全部限制条件（权限标识：condition:tempCondition:list）
 */
export async function queryCondition(params) {
    const res = await request.get('/condition/temp-condition', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询限制条件（权限标识：condition:tempCondition:list）
 */
export async function queryPage(params) {
    const res = await request.get('/condition/temp-condition/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询限制条件（权限标识：condition:tempCondition:list）
 */
export async function getConditionById(id) {
    const res = await request.get('/condition/temp-condition/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改限制条件（权限标识：condition:tempCondition:operation）
 */
export async function operation(data) {
    const res = await request.post('/condition/temp-condition/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除限制条件（权限标识：condition:tempCondition:remove）
 */
export async function removes(data) {
    const res = await request.post('/condition/temp-condition/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
