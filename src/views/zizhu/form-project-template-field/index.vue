<!--扩展字段设置-->
<template>
<!--  <ele-page flex-table :style="{padding:!enterType?'8px 8px 0 8px !important':'unset!important'}">-->

  <ele-page flex-table style="padding: 0!important;">
<!--    <ele-card :body-style="{height: (pageHeight-42)+'px',overflow:'auto',-->
    <!-- 搜索表单 -->
    <ele-card :body-style="{ padding: '0 8px 0 8px!important',height: (pageHeight+2)+'px',overflow:'auto'}">
      <!-- 表格 -->
      <el-form ref="formRef"
               label-width="auto"
               size="small"
               @submit.prevent="">
        <TablePreview :ref="el => getRiskSpreadRef(el, 0)"
                      approverType="projectTemplateField"
                      :currentProject="newProjectData"
                      :RandomString="RandomString"
                      :approverData="formProjectTemplateField"
                      @onDoneGroup="handleDoneGroup"/>
        <!--      :currentNode="current"-->
        <!--      :approverId="current.id"-->
        <!--      :RandomString="RandomStringfield"-->
        <!--      :approverData="proFormData"-->
        <!--      @onDoneGroup="handleDoneGroup"-->
        <!--      @child-event="parentMethod"-->
        <!--      @FieldLinkEvent="handleFieldLinkEvent"-->
      </el-form>
    </ele-card>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <ele-text v-if="validMsg" style="float: right;padding-right: 10px;" type="danger"
                :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <template #extra>
        <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
          保存
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
import {computed,  reactive, ref, unref, watch} from 'vue';
import {useRouter} from "vue-router";
import {getToken} from "@/utils/token-util";
import {getCurrentRole} from "@/utils/current-role-util";
import {usePageTab} from "@/utils/use-page-tab.js";
import TablePreview from "@/components/DrawFlow/drawer/table-preview.vue";
import {CloseCircleOutlined} from "@/components/icons/index.js";
import {operation, getFormProjectTemplateField} from "./api/index.js";
import {generateRandomString} from '@/utils/common_bak2.js';
import {getFormProjectById} from "@/views/zizhu/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import { ElMessage as EleMessage} from "element-plus";
import {storeToRefs} from "pinia";

const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();

const props = defineProps({
  /** 弹窗是否打开 */
  currentProject: {
    type: Object,
    default: null,
  },
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
console.log(params, path, query)
// const userType = path.split("/")[3];
const userType = ref(null)
const currentXmId = params.xmId;
let enterType = query?.enterType
let routeType = path.split("/")[3];

/** 项目信息*/
const newProjectData = ref(null);

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};

/** 表单资助申请字段 */
const formData = ref(null);
const handleDoneGroup = (data,) => {
  formData.value = data ?? []
};

/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  validMsg.value = null;
  let result = new Promise((resolve, reject) => {
    formChild.formRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}

const onSubmit = async () => {
  if (formData.value && formData.value.length > 0) {
    await riskSpreadRefList.value?.forEach((child) => {
      checkForm(child)
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []//每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save()
      } else {
        validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  } else {
    EleMessage.error("请设置项目扩展字段信息")
    return false
  }
}

/** 提交 */
const save = () => {
  loading.value = true;
  operation(formData.value).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

const formProjectTemplateField = ref()
const QyeryFormProjectTemplateField = () => {
  getFormProjectTemplateField({projectId: newProjectData.value.id, year: newProjectData.value.year}).then((list) => {
    if (list) {
      list.forEach(e => {
        e.isEdit = true;
      })
      formProjectTemplateField.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

const RandomString = computed(() => {
  return formProjectTemplateField.value ? generateRandomString(10) : ""
})

const queryFormProjectById = () => {
  getFormProjectById(currentXmId).then((data) => {
    if (data) {
      newProjectData.value = data;
    }
    loading.value = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
}

if (props.currentProject) {
  newProjectData.value = props.currentProject
} else {
  queryFormProjectById()
}

watch(
  () => newProjectData.value,
  (value) => {
    console.log(1)
    if (value) QyeryFormProjectTemplateField()
  },
  {immediate: true}
)

</script>

<script>
export default {
  name: 'FORMPROJECTTEMPLATEFIELD'
};
</script>


<style scoped>
.el-link {
  font-weight: unset !important;
}
</style>
