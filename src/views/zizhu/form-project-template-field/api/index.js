import request from '@/utils/request.js';

/**
 * 查询不分页
 */
export async function getFormProjectTemplateField(params) {
  const res = await request.get('/form/form-project-template-field', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/form/form-project-template-field/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getFormGroupById(id) {
  const res = await request.post('/form/form-project-template-field/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/form/form-project-template-field/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removeFormProjectTemplateField(data) {
  const res = await request.post('/form/form-project-template-field/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
