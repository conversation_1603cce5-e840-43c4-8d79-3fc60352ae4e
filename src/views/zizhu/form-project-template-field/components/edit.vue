<!-- 单个编辑 -->
<template>
  <ele-drawer size="41%"
              :title="'限制名额设置'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="formData"
             label-width="auto"
             @submit.prevent="">
      <el-row>
        <el-col :span="12">
          <el-form-item label="培养层次">
            <dict-data placeholder="请选择培养层次"
                       code="pycc"
                       :model-value="formData['pyccid']"
                       @update:modelValue="(value) => updateValue('pyccid',value)"/>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="年级">
            <dict-data placeholder="请选择年级"
                       code="nj"
                       :model-value="formData['njid']"
                       @update:modelValue="(value) => updateValue('njid',value)"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="院系">
        <!--        :isColProps="true"-->
        <dict-data placeholder="请选择院系"
                   code="xymc"
                   :model-value="formData['xyid']"
                   @update:modelValue="(value) => updateValue('xyid',value)"/>
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="专业">
            <!--            <dict-data placeholder="请选择院系"-->
            <!--                       code="xymc"-->
            <!--                       :model-value="formData['zyid']"-->
            <!--                       @update:modelValue="(value) => updateValue('xyid',value)"/>-->
            <ele-table-select :disabled="!formData['xyid']"
                              size="small"
                              clearable
                              placeholder="请选择专业"
                              value-key="id"
                              label-key="name"
                              v-model="formData['zyid']"
                              :table-props="tablePropsZymc"
                              :cache-data="cacheDataZymc"
                              @select="onSelectZy"
                              :popper-width="520">

              <!--              @clear="onClearZy"-->
              <!--              @removeTag="onRemoveTagZy"-->
              <!--              @select="onSelectZy"-->
            </ele-table-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班级" prop="sort">
            <ele-table-select :disabled="!formData['zyid']"
                              size="small"
                              clearable
                              placeholder="请选择班级"
                              value-key="id"
                              label-key="name"
                              v-model="formData['bjid']"
                              :table-props="tablePropsBjmc"
                              :cache-data="cacheDataBj"
                              :popper-width="680">
              <!--              :maxTagCount="4"-->
              <!--              -->
              <!--              @clear="onClearBj"-->
              <!--              @removeTag="onRemoveTagBj"-->
              <!--              @select="onSelectBj"-->
            </ele-table-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="类型">
            <el-radio-group v-model="formData.quotaType">
              <el-radio value="名额">名额</el-radio>
              <el-radio value="比例">比例</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="名额/比例">
            <el-input-number :min="0"
                             :max="9999"
                             v-model="formData.quota"
                             placeholder="请输入名额或百分比例"
                             controls-position="right"
                             class="ele-fluid"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" size="small" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref, computed} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation} from '../api/index.js'
import {useRouter} from "vue-router";
import {getCodeZyb, queryPage as queryZyb} from "@/views/base-code/zyb/api/index.js";
import {getCodeBjb, queryPage as queryBjb} from "@/views/base-code/bjb/api/index.js";
import {getUserDataScope} from "@/views/jsglfw/fdydbgl/api/index.js";

const emit = defineEmits(['done', 'update:modelValue']);

const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
console.log(params, path, query)
// const userType = path.split("/")[3];
const userType = ref(null)
const currentXmId = params.xmId;
let enterType = query?.enterType
let routeType = path.split("/")[3];

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  currentProject: Object//项目信息
});

const formData = ref({
  projectId: currentXmId,
  year: props.currentProject?.year,
  quotaType: '0',
  quota: 1,

});
/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);


/** 专业缓存数据 */
const cacheDataZymc = ref([]);

const tablePropsZymc = reactive({
  datasource: [],
  virtual: true,
  height: 288,
  columns: [
    {
      prop: 'code',
      label: '代码',
      minWidth: 110
    },
    {
      prop: 'name',
      label: '专业名称',
      minWidth: 150,
    },
    {
      prop: 'xymc',
      label: '所属院系',
      minWidth: 150,
    },
  ],
  showOverflowTooltip: true,
  highlightCurrentRow: true,
  toolbar: false,
  pagination: {
    layout: 'total, prev, pager, next, sizes',
    teleported: false,
  },
  rowStyle: {cursor: 'pointer'},
  // rowClickChecked: true
});
const cacheDataBj = ref([]);

/** 班级缓存数据 */
const tablePropsBjmc = reactive({
  datasource: [],
  virtual: true,
  height: 288,
  columns: [
    {
      prop: 'name',
      label: '班级名称',
      minWidth: 150,
    },
    {
      prop: 'xymc',
      label: '所属院系',
      minWidth: 150,
    },
    {
      prop: 'zymc',
      label: '专业',
      minWidth: 150,
    },
    {
      prop: 'njmc',
      label: '年级',
      minWidth: 70,
    },
  ],
  showOverflowTooltip: true,
  highlightCurrentRow: true,
  toolbar: false,
  pagination: {
    layout: 'total, prev, pager, next, sizes',
    teleported: false,
  },
  rowStyle: {cursor: 'pointer'},
  // rowClickChecked: true
});

watch(() => formData.value.xyid,
  (xyIds) => {
    if (xyIds) {
      queryCodeZyb()
    } else {
      cacheDataZymc.value = [];
      cacheDataBj.value = []
      formData.value.zyid = null
      formData.value.bjid = null
    }
    /** 查询院系下的专业数据 */
    tablePropsZymc.datasource = ({page, limit, where, orders}) => {
      return queryZyb({...where, ...orders, page, limit, xyid: xyIds});
    };
  },
)

watch(() => formData.value['zyid'],
  (zyIds) => {
    if (zyIds) {
      queryCodeBjb()
    } else {
      cacheDataBj.value = []
      formData.value.zyid = null
      formData.value.bjid = null
    }
    /** 查询表格数据 */
    tablePropsBjmc.datasource = ({page, limit, where, orders}) => {
      return queryBjb({
        ...where,
        ...orders,
        page,
        limit,
        zyid: zyIds,
        njid: formData.value?.njid ?? '',
      });
    };
  },
)

const queryCodeBjb = () => {
  if (formData.value['bjid']) {
    cacheDataBj.value = []
    getCodeBjb({
      id: formData.value['bjid'],
      njid: formData.value?.njid ?? '',
    }).then((list) => {
      if (list) {
        cacheDataBj.value = list
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    }).finally(() => {
    });
  }
}

/** 根据zyid查询所属xyid */
const queryCodeZyb = () => {
  if (formData.value['zyid']) {
    cacheDataZymc.value = []
    getCodeZyb({id: formData.value['zyid']}).then((list) => {
      if (list.length > 0) {
        cacheDataZymc.value = list
      }
    }).catch((e) => {
      EleMessage.error(e.message);
    }).finally(() => {
    });
  }
}

/* 当用户手动勾选数据行的 Checkbox 时触发的事件	 */
const onSelectZy = () => {
  cacheDataBj.value = []
  formData.value.bjid = null
};
/** 更新值 */
const updateValue = (prop, value) => {
  if (prop === 'xyid') {
    cacheDataZymc.value = [];
    cacheDataBj.value = []
    formData.value.zyid = null
    formData.value.bjid = null
  }
  formData.value[prop] = value
}

/** 保存编辑 */
const save = () => {
  let resultTrue = false;
  Object.keys(formData.value).forEach((key) => {
    const value = formData.value[key];
    if (['pyccid', 'njid', 'xyid', 'zyid', 'bjid'].includes(key)) {
      if (value) {
        resultTrue = true
        return false
      }
    }
  })
  if (!resultTrue) {
    EleMessage.error('请至少选择一项设置条件！');
    return false;
  }
  loading.value = true;
  console.log(JSON.stringify(formData.value))
  operation(formData.value).then((msg) => {
    loading.value = false;
    EleMessage.success(msg);
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.currentProject) {
        console.log(props.currentProject)
        formData.value.year = props.currentProject.year
      }

      if (props.data) {
        console.log(props.data)
        isUpdate.value = true;
        formData.value = props.data
        cacheDataZymc.value = [{id: props.data.zyid, name: props.data.zymc}];
        cacheDataBj.value = [{id: props.data.bjid, name: props.data.bjmc}];
      } else {
        isUpdate.value = false;
      }
    } else {
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
