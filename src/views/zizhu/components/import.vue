<!-- 院系导入弹窗 -->
<template>
  <ele-modal :width="460"
             :close-on-click-modal="false"
             :title="'上传'+(currentType==='sqb'?'申请表':'汇总表')"
             :model-value="modelValue"
             @update:modelValue="updateModelValue">
    <div v-loading="loading" class="user-import-upload">
      <el-upload drag
                 action=""
                 accept=".xls,.xlsx"
                 :show-upload-list="false"
                 :before-upload="doUpload">
        <ele-text type="primary"
                  :icon="CloudUploadOutlined"
                  :icon-props="{ size: 52 }"
                  style="margin-bottom: 10px"/>
        <ele-text type="placeholder">将文件拖到此处, 或点击上传</ele-text>
      </el-upload>
    </div>
    <div style="display: flex; align-items: center; justify-content: center">
      <span style="padding-right: 8px">只能上传 xls、xlsx 文件,</span>
      <el-link type="primary"
               style="font-size: 20px"
               underline="never"
               @click="handleDownLoad"
               :download="(currentType==='sqb'?'申请表':'汇总表')+'模板.xlsx'">
        点击下载模板
      </el-link>
    </div>
  </ele-modal>
</template>

<script setup>
import {ref, unref} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from 'element-plus';
import {CloudUploadOutlined} from '@/components/icons';
import {importData, getProjectTemplateCheck} from '../api/index';

import {getToken,} from '@/utils/token-util';
import {getCurrentRole,} from '@/utils/current-role-util';
import {useRouter} from "vue-router";
import {FILE_IMPORTERROREXCEL} from '@/config/setting';

const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

const emit = defineEmits(['done', 'update:modelValue']);

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const routeType = path.split("/")[3];
const props = defineProps({
  /** 是否打开弹窗 */
  modelValue: Boolean,
  currentProject: Object,
  currentType: String,
});

/** 导入请求状态 */
const loading = ref(false);

/** 下载模版 */
const handleDownLoad = () => {
  getProjectTemplateCheck({
    module: props.currentProject.id,
    templateType: props.currentType
  }).then(data => {
    if (data) {
      window.location.href = BASE_URL + 'api/file/template?module=' + props.currentProject.id + '&templateType=' + props.currentType + '&access_token=' + accessToken;
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

/** 上传 */
const doUpload = (file) => {
  if (
      ![
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ].includes(file.type)
  ) {
    EleMessage.error('只能选择 excel 文件');
    return false;
  }
  if (file.size / 1024 / 1024 > 10) {
    EleMessage.error('大小不能超过 10MB');
    return false;
  }
  loading.value = true;
  importData(file, props.currentProject.id, props.currentType).then((data) => {
    loading.value = false;
    if (data.code === 0) {
      EleMessage.success(data.message);
    } else if (data.code === 2) {
      // EleMessage.success(data.message);
      open(data)
    }
    updateModelValue(false);
    emit('done');

  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
  return false;
};
const open = (data) => {
  ElMessageBox.confirm(
      data.message,
      '导入提示',
      {
        confirmButtonText: '下载',
        cancelButtonText: '关闭',
        type: 'warning',
      }
  ).then(() => {
    let downUrl = BASE_URL + FILE_IMPORTERROREXCEL + '?id=' + data.data + '&access_token=' + accessToken ;
    window.location.href = downUrl
  }).catch(() => {
    EleMessage({
      type: 'info',
      message: 'Delete canceled',
    })
  })
}
/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
</script>

<style lang="scss" scoped>
.user-import-upload {
  margin-bottom: 12px;

  :deep(.el-upload > .el-upload-dragger) {
    padding: 0;
    height: 168px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: (border-color 0.2s, background-color 0.2s);

    &:not(.is-dragover) {
      background: var(--el-fill-color-light);
    }
  }

  :deep(.el-upload-list) {
    display: none;
  }

  :deep(.el-icon > svg) {
    stroke-width: 3;
  }
}
</style>
