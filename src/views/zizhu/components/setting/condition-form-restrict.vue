<!--资助申请限制条件设置-->
<template>
  <ele-card header="申请限制条件设置"
            :style="{marginBottom:'0px!important'}"
            :body-style="{height: (pageHeight+15)+'px',overflow:'auto',padding: '10px 8px !important'}">
    <el-form ref="formRef" :model="datasource" label-width="0px" @submit.prevent="" size="small">
      <div style="overflow: auto">
        <ele-table size="small" style="min-width: 580px; table-layout: fixed;text-align: left" border>
          <thead>
          <tr>
            <template v-for="header in columns">
              <th :style="{ 'width': header.width+'px'}">{{ header.label }}</th>
            </template>
          </tr>
          </thead>
          <tbody>
          <tr v-for="item in datasource" :key="item.prop">
            <template v-for="(row, rindex) in columns" :key="rindex">
              <td>
                <el-form-item v-if="row.type==='condition'"
                              label=""
                              class="form-error-popper"
                              style="margin-bottom: 0 !important">
                  <div class="editable-cell-text">{{ getOptions(item[row.prop]) }}</div>
                </el-form-item>
                <el-form-item v-else-if="row.type==='conditionValue'"
                              label=""
                              class="form-error-popper"
                              style="margin-bottom: 0 !important">
                  <el-link type="primary" underline="never" @click="openFieldIndex(item)">
                    限制
                  </el-link>
                  <template v-if="setConditionValue(item['conditionKey'])">
                    <el-divider direction="vertical"/>
                    <el-link type="primary" underline="never"
                             @click="removeFieldIndex(item)">
                      取消
                    </el-link>
                  </template>
                </el-form-item>
                <div v-else class="editable-cell-text">
                  <ele-ellipsis :tooltip="{
                      original: false,
                      popperStyle: {
                        width: '320px',
                        background: '#ffffff',
                        color: '#2a2a2a',
                        maxWidth: 'calc(100vw - 32px)'
                      }
                    }">
                    {{ item[row.prop] }}
                  </ele-ellipsis>
                </div>
              </td>
            </template>
          </tr>
          <tr v-if="!datasource || !datasource.length">
            <td :colspan="columns.length" style="text-align: center">
              <ele-text style="padding: 4px 0" type="secondary">
                暂无数据
              </ele-text>
            </td>
          </tr>
          </tbody>
        </ele-table>
      </div>
    </el-form>
  </ele-card>
  <FieldIndex v-if="currentProject" v-model="showFieldIndex" :currentCondition="currentCondition"
              :currentData="currentData"
              :projectId="currentProject?.id"
              @done="reLoad"/>
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import {queryCondition} from "@/views/zizhu/condition/api/index.js";
import {getFormTestrict, removes} from "@/views/zizhu/condition/api/form-restrict-index.js";
import {comCondition} from '@/utils/common_bak2.js';
import FieldIndex from "@/views/zizhu/condition/form-restrict/field-index.vue";
import {ElLoading, ElMessageBox,ElMessage as EleMessage} from "element-plus";

const props = defineProps({
  /** 修改回显的数据 */
  currentProject: Object,
  pageHeight: Number,
});

const currentData = ref([]);
let currentCondition = null
const showFieldIndex = ref(false);
const openFieldIndex = (row) => {
  currentCondition = row;
  currentCondition.title = '';
  currentCondition.infoType = 'conditionSet';
  currentData.value = setConditionValue(row.conditionKey)
  showFieldIndex.value = true
};

/** 删除 */
const removeFieldIndex = async (row) => {
  const loading = ElLoading.service({
    lock: true,
    text: '请求中...',
    background: 'rgba(0, 0, 0, 0.1)'
  });
  try {
    const data = formTestrict.value.find(ft => ft.conditionKey === row.conditionKey);
    await ElMessageBox.confirm(
      `确定要取消"${data.conditionName}"吗?`,
      '系统提示',
      {type: 'warning', draggable: true}
    );


    await removes([data.id]);
    EleMessage.success("限制条件取消成功");

    // 重新加载数据
    await queryFormTestrict();
    // 创建新数组触发响应式更新
    formTestrict.value = [...formTestrict.value];

  } catch (e) {
    if (e !== 'cancel') {
      EleMessage.error(e.message || '操作失败');
    }
  } finally {
    loading.close();
  }
};
// const removeFieldIndex = (row) => {
//   let data = formTestrict.value.find(ft => ft.conditionKey === row.conditionKey)
//   const rows = [data];
//   ElMessageBox.confirm(
//     '确定要取消“' + rows.map((d) => d.conditionName).join(', ') + '”吗?',
//     '系统提示',
//     {type: 'warning', draggable: true}
//   ).then(() => {
//      const loading = ElLoading.service({
//       lock: true,
//       text: '请求中...',
//       background: 'rgba(0, 0, 0, 0.1)'
//     });
//     removes(rows.map((d) => d.id)).then((msg) => {
//       loading.close();
//       if (msg) EleMessage.success("限制条件取消成功");
//       reLoad()
//     }).catch((e) => {
//       loading.close();
//       EleMessage.error(e.message);
//     });
//   }).catch(() => {
//   });
// };

const reLoad = async () => {
  await getDataSource()
  await queryFormTestrict()
}
/** 表格数据源 */
const datasource = ref([]);

/** 表格列配置 */
const columns = ref([
    {
      prop: 'conditionName',
      label: '限制条件名称',
    },
    {
      prop: 'condition',
      label: '条件',
      type: 'condition',
    },
    {
      prop: 'conditionValue',
      label: '限制项',
      type: 'conditionValue',
    },
    {
      prop: 'description',
      label: '说明',
    },
  ]
);

/** 提交状态 */
const loading = ref(false);
/** 表单数据 */
const form = reactive({
  users: []
});

const getDataSource = async () => {
  await queryCondition({enable: '是',}).then((arr) => {
    if (arr) {
      arr.forEach(item => {
        item.condition = item.condition ? item.condition.split(',') : []
        item.title = item.conditionName
      })
      datasource.value = arr
    }
  });
}

const formTestrict = ref([])
const queryFormTestrict = () => {
  getFormTestrict({projectId: props.currentProject.id}).then((list) => {
    if (list) formTestrict.value = list;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 更新condition */
const getOptions = (value) => {
  let baseData = comCondition()
  if (value.length > 0) {
    return baseData.filter(item => value.includes(item.value)).map(item => item.label).join(',')
  }
};

/** 更新conditionValue */
const setConditionValue = (conditionKey) => {
  if (formTestrict.value.length > 0) {
    let newVal = formTestrict.value.find(ft => ft.conditionKey === conditionKey)
    return newVal ? newVal : null
  }
  return null
};

watch(
  () => props.currentProject,
  (currentProject) => {
    if (currentProject) {
      getDataSource();
      queryFormTestrict()
    }
  },
  {
    immediate: true,
  }
);

</script>

<style scoped lang="scss">

</style>
