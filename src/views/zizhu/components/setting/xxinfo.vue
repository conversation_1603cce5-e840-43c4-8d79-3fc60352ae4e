<template>
  <div :style="{height:(pageHeight+10)+'px',overflow:'auto' }">
    <pro-form ref="formRef"
              :model="form"
              :items="formItems"
              :rules="rules"
              size="small"
              labelWidth="auto"
              @updateValue="setFieldValue">
      <!-- 前置项目选择器 -->
      <template #qzxmid="{ item, model, updateValue }">
        <dict-data
          placeholder="请选择前置项目"
          code="qzxmData"
          type="multipleSelect"
          :refresh="RandomString"
          :dicQueryParams="getQzxmParams()"
          v-model="form[item.prop]"
          @change="handleQzxmChange"
        />
      </template>

      <!-- 互斥项目选择器 -->
      <template #hcxmid="{ item, model, updateValue }">
        <dict-data
          placeholder="请选择互斥项目"
          code="hcxmData"
          type="multipleSelect"
          :refresh="RandomString"
          :dicQueryParams="getHcxmParams()"
          v-model="form[item.prop]"
          @change="handleHcxmChange"
        />
      </template>
    </pro-form>
  </div>
  <!-- 底部工具栏 -->
  <ele-bottom-bar>
    <ele-text style="float: right;padding-right: 10px;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
      <span>{{ validMsg }}</span>
    </ele-text>
    <template #extra>
      <el-button size="small" @click="onBack">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-bottom-bar>
</template>
<script setup>

import {ElMessage as EleMessage} from 'element-plus';
import {CloseCircleOutlined} from '@/components/icons/index.js';
import ProForm from '@/components/ProForm/index.vue';
import {onMounted, reactive, ref, unref} from 'vue';
import {arrayTypes, regionsArrayTypes, selectTypes, stringTypes, uploadTypes} from '@/components/ProForm/util.js';
import {useFormData} from '@/utils/use-form-data.js';
import {getFormProjectById, operation} from '@/views/zizhu/api/index.js';
import {usePageTab} from '@/utils/use-page-tab.js';
import {useThemeStore} from '@/store/modules/theme.js';
import {useRouter} from 'vue-router';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {generateRandomString} from '@/utils/common_bak2.js';

const emits = defineEmits(['done']);
const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();
const themeStore = useThemeStore();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {params, path, query} = unref(currentRoute);
console.log(params, path, query);
let enterType = query?.enterType;
/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
const currentProjectData = ref(null);

let routeType = path.split('/')[3];
let currentXmId = params?.xmId;
/** 表单项 */
const formItems = ref([
  {
    label: '基本信息',
    prop: 'senderInfo',
    type: 'card',
    props: {
      bordered: false,
      bodyStyle: {
        padding: '16px 0 0 0 !important',
      },
      style: {
        padding: '0px 5px 0 5px !important',
      },
    },
    grid: {
      span: 24,
    },
    children: [
      {
        prop: 'xmmc',
        label: '项目名称',
        type: 'input',
        required: true,
        showFlag: '是',
        selfModifyFlag: '是',
        colProps: {span: 8},
      },
      {
        prop: 'sqkssj',
        label: '申请开始时间',
        type: 'date',
        required: true,
        showFlag: '是',
        selfModifyFlag: '是',
        colProps: {span: 8},
      },
      {
        prop: 'sqjzsj',
        label: '申请结束时间',
        type: 'date',
        required: true,
        showFlag: '是',
        selfModifyFlag: '是',
        colProps: {span: 8},
      },
      {
        prop: 'sqnum', label: '允许申请条数', type: 'inputNumber',

        colProps: {span: 8},
      },
      {
        prop: 'xmfl', label: '项目分类', type: 'input',
        colProps: {span: 8},
      },
      {
        prop: 'jxzsdm', label: '资助项目代码', type: 'input',
        colProps: {span: 8},
      },
      {
        prop: 'sfxtsczzmd', label: '系统生成资助名单', type: 'input',
        colProps: {span: 8},
      },
      {
        prop: 'sqbdcgs', label: '申请表导出格式', type: 'select',
        options: [
          {label: 'excel', value: 'excel'},
          {label: 'pdf', value: 'pdf'},
        ],
        colProps: {span: 8},
      },
      {
        prop: 'xmtjType', label: '统计类型', type: 'input',
        colProps: {span: 8},
      },
      // {
      //   prop: 'sqanmc',
      //   label: '申请按钮名称',
      //   type: 'input',
      //   // required: true, showFlag: '是', selfModifyFlag: '是',
      //   colProps: {span: 8},
      // },
    ],
  },
  {
    label: '参数设置',
    prop: 'csszInfo',
    type: 'card',
    props: {
      bordered: false,
      bodyStyle: {
        padding: '16px 0 0 0 !important',
      },
      style: {
        padding: '0px 5px 0 5px !important',
      },
    },
    grid: {
      span: 24,
    },
    children: [
      {
        prop: 'sfxs',
        label: '是否显示',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxszc',
        label: '是否显示暂存',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'qysqyzm',
        label: '是否启用申请验证码',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'fyspjg',
        label: '复用审核信息',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfkxgsqxx',
        label: '是否可修改申请信息',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      // {
      //   prop: 'sfxszs',
      //   label: '是否显示证书',
      //   type: 'switch',
      //   required: true, showFlag: '是', selfModifyFlag: '是',
      //   colProps: {span: 6}
      // },
      {
        prop: 'sfyd',
        label: '审核学籍异动前数据',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxsxqbxz',
        label: '显示申请表下载按钮',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxshzbxz',
        label: '显示汇总表下载按钮',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      // {
      //   prop: 'sfxspybxz',
      //   label: '显示评议表下载按钮',
      //   type: 'radioButton',
      //   options: sfOption,
      //   required: true,
      //   colProps: {span: 6}
      // },
      {
        prop: 'sfxspldcfj',
        label: '显示批量导出申请附件按钮',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxsgs',
        label: '显示公示按钮',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sftqdtsj',
        label: '是否提取当天数据',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxstj',
        label: '是否显示统计',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfdw',
        label: '是否定位',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfxsdw',
        label: '是否显示定位',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfworkflow',
        label: '是否根据流程查询',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfjskj',
        label: '是否教师可见',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sfsqxz',
        label: '申请后即可下载',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      // {
      //   prop: 'daySqnum',
      //   label: '每日可申请条数',
      //   type: 'switch',
      //   required: true, showFlag: '是', selfModifyFlag: '是',
      //   colProps: {span: 6},
      // },
      {
        prop: 'sftsxx',
        label: '是否推送消息',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
      {
        prop: 'sftqzhsqxx',
        label: '是否提取最后申请信息',
        type: 'switch',
        required: true, showFlag: '是', selfModifyFlag: '是',
        colProps: {span: 6},
      },
    ],
  },
  {
    label: '前置项目设置',
    prop: 'qzxmszInfo',
    type: 'card',
    props: {
      bordered: false,
      bodyStyle: {
        padding: '16px 0 0 0 !important',
      },
      style: {
        padding: '0px 5px 0 5px !important',
      },
    },
    grid: {
      span: 24,
    }, children: [
      {
        prop: 'qzxmid', label: '前置项目',
        type: 'qzxmid',
        // type: 'dictSelect',
        // typeKey: 'select',
        // props: {
        //   code: 'ksPaperData',
        //   dicQueryParams: {
        //     dictFieldUrl: '/form/form-project',
        //   }
        // },
        colProps: {span: 24},
      },
      {
        prop: 'hcxmid', label: '互斥项目',
        type: 'hcxmid',
        colProps: {span: 24},
      },
      {
        prop: 'qzxmgx', label: '前置项目关系', type: 'select',
        options: [
          {label: '并', value: 'AND'},
          {label: '或', value: 'OR'}],
        colProps: {span: 8},
      },
      {
        prop: 'hcxm', label: '包含条数', type: 'input',
        colProps: {span: 8},
      },
      {
        prop: 'qzqzxm', label: '强制包含前置项目', type: 'input',
        colProps: {span: 8},
      }],
  },
  {
    label: '项目简介',
    prop: 'xmjjInfo',
    type: 'card',
    props: {
      bordered: false,
      bodyStyle: {
        padding: '16px 0 0 0 !important',
      },
      style: {
        padding: '0px 5px 0 5px !important',
      },
    },
    grid: {
      span: 24,
    }, children: [
      {
        prop: 'xmjj', label: '简介', type: 'textarea',
        colProps: {span: 24},
      },
      {
        prop: 'ydxz', label: '阅读须知', type: 'textarea',
        colProps: {span: 24},
      }],
  },
  // {
  //   key: 'f0kglcmb5dcoh1i7',
  //   prop: 'senderInfo',
  //   type: 'card',
  //   props: {
  //     header: '扩展信息',
  //     bordered: false,
  //     collapsable: 'header',
  //     bodyStyle: {
  //       padding: '16px 0 0 0 !important',
  //     },
  //     headerStyle: {
  //       cursor: 'pointer',
  //       userSelect: 'none',
  //     },
  //     style: {
  //       padding: '0px 5px 0 5px !important',
  //       // marginBottom: '16px'
  //     },
  //   },
  //   grid: {
  //     span: 24,
  //   },
  //   // rowProps: {
  //   //   gutter: 10,
  //   //   style: {
  //   //     marginTop: '10px',
  //   //     padding: '0px 5px 0 5px !important',
  //   //   }
  //   // },
  //   children: [
  //     {
  //       prop: 'xmje', label: '项目金额', type: 'inputNumber',
  //       colProps: {span: 8},
  //     },
  //     {
  //       prop: 'xmme', label: '项目名额', type: 'input',
  //       colProps: {span: 8},
  //     },
  //     {
  //       prop: 'zjly', label: '资金来源', type: 'input',
  //       colProps: {span: 8},
  //     },
  //
  //   ],
  // },
]);

/** 表单验证规则 */
const rules = reactive({});

/** 获取表单字段 */
const initModel = ref([]);

// 新增方法：获取前置项目查询参数
const getQzxmParams = () => {
  const excludeIds = [...(form.hcxmid || [])];
  if (currentXmId) {
    excludeIds.push(currentXmId);
  }
  return {
    dictFieldUrl: '/form/form-project',
    valueField: 'id',
    textField: 'xmmc',
    params: {
      type: routeType,
      idNotIn: excludeIds.length > 0 ? excludeIds.join(',') : undefined,
    },
  };
};

// 新增方法：获取互斥项目查询参数
const getHcxmParams = () => {
  const excludeIds = [...(form.qzxmid || [])];
  if (currentXmId) {
    excludeIds.push(currentXmId);
  }
  return {
    dictFieldUrl: '/form/form-project',
    valueField: 'id',
    textField: 'xmmc',
    params: {
      type: routeType,
      idNotIn: excludeIds.length > 0 ? excludeIds.join(',') : undefined,
    },
  };
};
// 添加这两个处理函数
const handleQzxmChange = (val) => {
  // if (val?.length) {
  //   // 如果选择了前置项目，清空互斥项目
  //   form.hcxmid = [];
  // }
  // 重新生成随机字符串刷新组件
  RandomString.value = generateRandomString(10);
};

const handleHcxmChange = (val) => {
  // if (val?.length) {
  //   // 如果选择了互斥项目，清空前置项目
  //   form.qzxmid = [];
  // }
  // 重新生成随机字符串刷新组件
  RandomString.value = generateRandomString(10);
};

let fieldFormResult = {};
if (formItems.value) {
  formItems.value.forEach(item => {
    if (item.prop !== 'titleText1' && item.prop !== 'collapse1') {
      if (item.prop === 'csszInfo') {
        item.children.forEach(child => {
          fieldFormResult[child.prop] = '是';
        });
      } else {
        item.children.forEach(child => {
          if (child.type) {
            if (uploadTypes.includes(child.type) || regionsArrayTypes.includes(child.type) ||
              arrayTypes.includes(child.type)) {
              fieldFormResult[child.prop] = [];
            }
            if (stringTypes.includes(child.type) || selectTypes.includes(child.type)) {
              fieldFormResult[child.prop] = null;
            }
            if (child.type === 'switch') {
              fieldFormResult[child.prop] = '是';
            }
            if (child.type === 'inputNumber') {
              fieldFormResult[child.prop] = 1;
            }
          }
        });
      }
    }
  });
  initModel.value = fieldFormResult;
}

/** 提交状态 */
const loading = ref(false);
/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData(initModel.value);

/** 表单实例 */
const formRef = ref(null);

/** 提交 */
const onSubmit = () => {
  formRef.value?.validate?.((valid, obj) => {
    if (!valid) {
      const errors = obj ? Object.keys(obj).length : 0;
      validMsg.value = ` 共有 ${errors} 项校验不通过`;
      return;
    }
    validMsg.value = '';
    loading.value = true;
    const newObj = {...form, id: currentXmId, type: routeType};
    operation(newObj).then((data) => {
      if (data) {
        currentProjectData.value = data;
        currentXmId = data.id;
      }
      loading.value = false;
      EleMessage.success('操作成功');
      // updateModelValue(false);

      if (enterType && enterType === 'details-form') {
        emits('done');
      } else {
        removePageTab({key: getRouteTabKey()});
        push({
          path: `/zizhu/details/${routeType}/${currentXmId}`,
          query: {enterType: 'details-form'},
        });
      }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    }).finally(() => {
      loading.value = false;
      // reloadPageTab()
      // 例如修改用户管理的页签标题和不可关闭, 需要已经打开过用户管理
      // reloadPageTab();
    });
  });
};

const queryFormProjectById = () => {
  getFormProjectById(currentXmId).then((data) => {
    if (data) {
      assignFields({...initModel.value, ...data});
      currentProjectData.value = data;
      // addPageTab({
      //   title: `${data.xmmc}-编辑`,
      //   key: `/zizhu/details/${routeType}/${data.id}`,
      //   closable: true,
      //   // meta: {icon: 'LinkOutlined'}
      // });
    }
    loading.value = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
};
/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push('/zizhu/info/' + routeType);
};

let RandomString = ref('');
onMounted(() => {
  RandomString.value = generateRandomString(10);
  if (currentXmId) {
    queryFormProjectById();
  } else {
    setPageTabTitle('新建项目');
  }
});

</script>

<style scoped lang="scss">

</style>
