<template>
  <ele-card>
    <div class="info-user">
      <div class="info-user-avatar" @click="openStudentDetailsInfo">
        <template v-if="currentData">
          <el-avatar :size="66"
                     :src="currentData&&currentData.xgh?('/api/personInfo/' + currentData.xgh + '/photo?access_token='+accessToken):''">
            {{ currentData?.xm?.charAt(0) || '' }}
          </el-avatar>
        </template>
<!--        <template v-else>-->
<!--          <el-avatar v-if="currentData.xb==='男'" :size="64"-->
<!--                     src="/male.png"-->
<!--                     :alt="currentData.xb"-->
<!--                     class="icon-svg"/>-->
<!--          <el-avatar v-else-if="currentData.xb==='女'" :size="64"-->
<!--                     src="/female.png"-->
<!--                     :alt="currentData.xb"-->
<!--                     class="icon-svg"/>-->
<!--        </template>-->
      </div>
      <ele-text type="placeholder" style="margin-top: 5px">
        点击头像查看学生详细信息
      </ele-text>
    </div>
    <div style="margin: 10px;">
      <form-preview
        label-width="auto"
        :routeType="routeType"
        :currentGroup="currentGroup"
        @onDoneGroup="onDoneGroup"
      />
    </div>
  </ele-card>
</template>

<script setup>
import {ref, computed} from 'vue';
import {useRouter} from 'vue-router';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';
import {getToken} from '@/utils/token-util.js';

// 调试模式
const debug = ref(true);

// Props
const props = defineProps({
  config: Object,
  routeType: String,
  leftData: Array,
});

// Emits
const emit = defineEmits(['done']);
const {push} = useRouter();

const imageLoaded = ref(false);

// 当前分组（基础信息）
const currentGroup = computed(() => props.leftData?.[0]?.list?.[0] || null);
// 当前数据
const currentData = ref(null);

const accessToken = getToken();
// 表单分组结果
const proFormGroup = ref([]);

// 打开学生详情
const openStudentDetailsInfo = () => {
  const xgh = currentData.value?.xgh || proFormGroup.value[0]?.values?.xgh;
  if (xgh) {
    push({
      path: `/personInfo/st/details/student/${xgh}`,
      query: {enterType: 'preview'},
    });
  }
};

// 表单回填
const onDoneGroup = (data) => {
  console.log('收到表单数据:', data);

  const existingIndex = proFormGroup.value.findIndex(
    item => item.groupId === data.groupId,
  );

  if (existingIndex >= 0) {
    proFormGroup.value[existingIndex].values = data.values;
  } else {
    proFormGroup.value.push(data);
  }

  // 确保总是使用最新的数据
  if (proFormGroup.value.length > 0) {
    currentData.value = {...proFormGroup.value[0].values};
    console.log('更新currentData:', currentData.value);
  }
};
</script>

<style lang="scss" scoped>
.info-user {
  box-sizing: border-box;
  text-align: center;

  .info-user-avatar {
    display: inline-block;
    position: relative;
    cursor: pointer;
    line-height: 0;

    .avatar-error,
    .avatar-loading {
      width: 90px;
      height: 90px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
      color: #999;

      .el-icon {
        font-size: 36px;
      }
    }

    .avatar-loading {
      .el-icon.is-loading {
        animation: rotating 2s linear infinite;
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.info-tags .el-tag {
  margin: 0 12px 8px 0;
}

.debug-info {
  padding: 10px;
  background: #f5f5f5;
  margin: 10px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;

  p {
    margin: 5px 0;
  }
}
</style>
