<template>
  <ele-card>
    <div class="info-user">
      <div class="info-user-avatar" @click="openStudentDtailsInfo">
        <el-avatar :size="90">
          <el-image style="width: 90px; height: 90px" v-if="imageLoaded"
                    fit="cover"
                    :src="'/api/file/inline/'+ JSON.parse(currentData.photo)[0].id"/>
        </el-avatar>
      </div>
      <ele-text type="placeholder" style="margin-top: 5px">
        点击头像查看学生详细信息
      </ele-text>
    </div>
    <div style="margin: 10px;">
      <form-preview label-width="auto"
                    :routeType="routeType"
                    :currentGroup="currentGroup"
                    @onDoneGroup="onDoneGroup"/>
    </div>
    <!--    <el-divider border-style="dashed" style="margin: 0"/>-->
    <!--    <ele-text size="md" style="margin-top: 16px;margin-left: 20px;">多条信息</ele-text>-->
    <!--    <div class="info-tags" style="margin-top: 12px;margin-left: 20px;">-->
    <!--      <el-tag v-for="group in currentGroupBottom" type="info" size="small" :disable-transitions="true">-->
    <!--        {{ group.title }}-->
    <!--      </el-tag>-->
    <!--    </div>-->
  </ele-card>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import FormPreview from "@/views/personInfo/st/components/form-preview.vue";
import {getPersonInfo} from "@/views/personInfo/st/api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import {useRouter} from "vue-router";

const {push} = useRouter();
const props = defineProps({
  config: Object,
  routeType: String,
  leftData: Array,
});

const emit = defineEmits(['done']);

/** 请求状态 */
const loading = ref(true);
const imageLoaded = ref(false);
const currentData = ref(null);

const currentGroup = computed(() => {
  if (props.leftData && props.leftData.length > 0) {
    return props.leftData[0].list[0];
  }
  return null;
})

const currentGroupBottom = computed(() => {
  if (props.leftData && props.leftData.length > 0) {
    return props.leftData[1].list;
  }
  return null;
})

/** 是否显示裁剪弹窗 */
const visible = ref(false);

/** 打开学生档案 */
const openStudentDtailsInfo = () => {
  push({
    path: '/personInfo/st/details/student/' + proFormGroup.value[0]?.values.xgh,
    query: {enterType: 'preview'}
  })
  // push('/personInfo/st/personal/' + currentGroup.value.userType + '/' + proFormGroup.value[0]?.values.xgh)
};

/** 存放form提交字段*/
const proFormGroup = ref([]);
const onDoneGroup = (data) => {
  let isExist = proFormGroup.value.filter(obj => obj.groupId === data.groupId)
  if (isExist.length > 0) {
    proFormGroup.value.filter(obj => {
      if (obj.groupId === data.groupId) obj.values = data.values;
    })
  } else {
    proFormGroup.value.push(data)
  }
};

/** 根据学工号查询 */
const queryPersonInfo = async () => {
  loading.value = true;
  await getPersonInfo(currentGroup.value.userType, proFormGroup.value[0]?.values.xgh).then((list) => {
    loading.value = false;
    list['avatar'] = null
    currentData.value = list;
    // 图片加载完成
    imageLoaded.value = true;
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
watch(
  () => proFormGroup.value[0]?.values?.xgh,
  (newVal) => {
    if (newVal && currentGroup.value?.userType) {
      queryPersonInfo();
    }
  }
);
watch(
  () => proFormGroup.value.length,
  () => {
    if (proFormGroup.value[0]?.values?.xgh && currentGroup.value?.userType) {
      queryPersonInfo();
    }
  }
);
watch(
  () => props.leftData,
  (newVal) => {
    if (newVal?.values.xgh && currentGroup.value.userType) {
      queryPersonInfo()
    }
  }
);

</script>

<style lang="scss" scoped>
.info-user {
  box-sizing: border-box;
  text-align: center;

  .info-user-avatar {
    display: inline-block;
    position: relative;
    cursor: pointer;
    line-height: 0;
  }
}

.info-tags .el-tag {
  margin: 0 12px 8px 0;
}
</style>
