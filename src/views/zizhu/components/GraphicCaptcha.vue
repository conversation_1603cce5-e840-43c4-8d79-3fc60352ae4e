<template>
  <!-- 图形验证码展示 -->
  <el-form ref="formRef"
           size="large"
           :model="form">
    <el-form-item prop="username">
      <el-input clearable
                size="small"
                v-model="form.code"
                placeholder="请输入验证码"
                :prefix-icon="ProtectOutlined"
                @blur="OnUpdateCaptchaCode">
        <template #append>
          <img
            v-if="captcha"
            :src="captcha"
            @click="changeCaptcha"
            style="height: 100%; width: 100%; display: block; object-fit: cover; cursor: pointer;"
          />
        </template>
      </el-input>
    </el-form-item>
  </el-form>
</template>

<script setup>
import {reactive, ref} from 'vue';
import {ProtectOutlined} from "@/components/icons/index.js";
import {getCaptcha} from "@/api/login/index.js";
import { ElMessage as EleMessage} from "element-plus";

const emits = defineEmits(['updateCaptchaCode']);
/** 表单数据 */
const form = reactive({
  code: '',
  cid: '',
});
/** 图形验证码 */
const captcha = ref('');

const OnUpdateCaptchaCode = () => {
  // ✅ 深拷贝 form 数据，避免被后续清空影响
  const copy = JSON.parse(JSON.stringify(form));
  emits('updateCaptchaCode', copy);
  // form.code = null;
  // form.cid = null;
};
/** 获取图形验证码 */
const changeCaptcha = () => {
  getCaptcha().then((data) => {
    captcha.value = data.base64;
    form.cid = data.cid;
    // // 实际项目后端一般会返回验证码的key而不是直接返回验证码的内容, 登录用key去验证, 可以根据自己后端接口修改
    // text.value = data.base64;
    // // // 自动回填验证码, 实际项目去掉
    // form.code = data.base64;
    // formRef.value?.clearValidate?.();
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

changeCaptcha();
</script>
<style lang="less" scoped>
:deep(.el-input-group__append) {
  padding: 0 !important;
  overflow: hidden;
}

:deep(.el-input-group__append img) {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}
</style>
