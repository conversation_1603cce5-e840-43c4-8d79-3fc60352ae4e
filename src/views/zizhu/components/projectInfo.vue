<template>
  <ele-drawer size="48%"
              :title="'['+(currentData&&currentData.xmmc)+'] 信息查看'"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '5px 8px!important' ,marginTop:0}"
              @update:modelValue="updateModelValue">
    <pro-form ref="formRef"
              :model="form"
              :items="formItems"
              :grid="{ span: 24 }"
              size="small"
              labelWidth="auto">
    </pro-form>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-drawer>
</template>
<script setup>

import ProForm from "@/components/ProForm/index.vue";
import { reactive, ref, unref, watch} from "vue";
import {useFormData} from "@/utils/use-form-data.js";
import {getFormProjectById, operation} from "@/views/zizhu/api/index.js";
import {usePageTab} from "@/utils/use-page-tab.js";
import {useThemeStore} from "@/store/modules/theme.js";
import {useRouter} from "vue-router";
import {getFormProjectTemplateField} from "@/views/zizhu/form-project-template-field/api/index.js";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  currentData: Object
});

const {removePageTab, getRouteTabKey, addPageTab, setPageTabTitle} = usePageTab();
const themeStore = useThemeStore();
const {currentRoute, push,} = useRouter();
const {params, path, query} = unref(currentRoute);
console.log(params, path, query)
let enterType = query?.enterType
/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
const currentProjectData = ref(null);

let userType = null
let routeType = path.split("/")[3];
let currentXmId = params?.xmId
/** 表单项 */
const formItems = ref([
  {
    prop: 'xmmc',
    label: '项目名称',
    type: 'input',
    showFlag: 'readonly',
    selfModifyFlag: '是',
    colProps: {span: 24}
  },
  {
    prop: 'sqkssj',
    label: '申请开始时间',
    type: 'date',
    showFlag: 'readonly',
    selfModifyFlag: '是',
    colProps: {span: 12}
  },
  {
    prop: 'sqjzsj',
    label: '申请结束时间',
    type: 'date',
    showFlag: 'readonly',
    selfModifyFlag: '是',
    colProps: {span: 12}
  },
  {
    prop: 'bz', label: '简介', type: 'textarea',
    showFlag: 'readonly',
    colProps: {span: 24}
  },
  {
    prop: 'ydxy', label: '阅读须知', type: 'textarea',
    showFlag: 'readonly',
    colProps: {span: 24}
  },
]);

/** 表单验证规则 */
const rules = reactive({})

/** 获取表单字段 */
const initModel = ref([]);

let fieldFormResult = {}
// if (formItems.value) {
//   formItems.value.forEach(item => {
//     if (item.prop !== 'titleText1' && item.prop !== 'collapse1') {
//       if (item.prop === 'csszInfo') {
//         item.children.forEach(child => {
//           fieldFormResult[child.prop] = '否';
//         })
//       } else {
//         item.children.forEach(child => {
//           if (child.type) {
//             if (uploadTypes.includes(child.type) || regionsArrayTypes.includes(child.type) || arrayTypes.includes(child.type)) {
//               fieldFormResult[child.prop] = [];
//             }
//             if (stringTypes.includes(child.type) || selectTypes.includes(child.type)) {
//               fieldFormResult[child.prop] = null;
//             }
//             if (child.type === 'switch') {
//               fieldFormResult[child.prop] = '否';
//             }
//           }
//         })
//       }
//     }
//   });
//   initModel.value = fieldFormResult;
// }

/** 提交状态 */
const loading = ref(false);
/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: '',
  xmmc: '',
  sqkssj: '',
  sqjzsj: '',
  bz: '',
  ydxy: '',
});


/** 表单实例 */
const formRef = ref(null);


const queryFormProjectById = () => {
  getFormProjectById(currentXmId).then((data) => {
    if (data) {
      assignFields({...initModel.value, ...data,});
      currentProjectData.value = data;
      // addPageTab({
      //   title: `${data.xmmc}-编辑`,
      //   key: `/zizhu/details/${routeType}/${data.id}`,
      //   closable: true,
      //   // meta: {icon: 'LinkOutlined'}
      // });
    }
    loading.value = false;
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
  });
}

/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};


// const formProjectTemplateField = ref()
const QyeryFormProjectTemplateField = () => {
  getFormProjectTemplateField({projectId: props.currentData.id, year: props.currentData.year}).then((list) => {
    if (list) {
      list.forEach(e => {
        form[e.fieldEn] = e["defaultVal"]
        formItems.value.push({
          "prop": e.fieldEn,
          "label": e.fieldZh,
          "type": "input",
          "showFlag": "readonly",
          "colProps": {"span": 24}
        },)
      })
      // formProjectTemplateField.value = list
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.currentData) {
        QyeryFormProjectTemplateField()
        console.log("11==", props.currentData)
        assignFields({
          ...props.currentData,
        });
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  },
);
</script>

<style scoped lang="scss">

</style>
