<template>
  <ele-card>
    <div class="info-user">
      <div class="info-user-avatar" @click="openStudentDetailsInfo">
        <el-avatar :size="90"
                   v-show="currentData">
          <el-image
            style="width: 90px; height: 90px"
            fit="cover"
            :src="'/api/personInfo/'+currentData.xgh+'/photo'"
          />

          <!--          :src="'/api/file/inline/' + JSON.parse(currentData.photo)?.[0]?.id"-->
        </el-avatar>
      </div>
      <ele-text type="placeholder" style="margin-top: 5px">
        点击头像查看学生详细信息
      </ele-text>
    </div>

    <div style="margin: 10px;">
      <form-preview
        label-width="auto"
        :routeType="routeType"
        :currentGroup="currentGroup"
        @onDoneGroup="onDoneGroup"
      />
    </div>
  </ele-card>
</template>

<script setup>
import {ref, computed, watch} from 'vue';
import {useRouter} from 'vue-router';
import FormPreview from '@/views/personInfo/st/components/form-preview.vue';

// Props
const props = defineProps({
  config: Object,
  routeType: String,
  leftData: Array,
});

// Emits
const emit = defineEmits(['done']);
const {push} = useRouter();

// 当前分组（基础信息）
const currentGroup = computed(() => props.leftData?.[0]?.list?.[0] || null);
// 当前数据
const currentData = ref(null);
const imageLoaded = ref(false);

// 表单分组结果
const proFormGroup = ref([]);

// 打开学生详情
const openStudentDetailsInfo = () => {
  const xgh = proFormGroup.value[0]?.values?.xgh;
  if (xgh) {
    push({
      path: `/personInfo/st/details/student/${xgh}`,
      query: {enterType: 'preview'},
    });
  }
};

// 表单回填
const onDoneGroup = (data) => {
  const existing = proFormGroup.value.find(item => item.groupId === data.groupId);
  if (existing) {
    existing.values = data.values;
  } else {
    proFormGroup.value.push(data);
  }
  console.log('表单回填==', proFormGroup.value);
  if(proFormGroup.value){
    currentData.value = proFormGroup.value[0].values;
    console.log('currentData.value==', currentData.value);
  }
};

</script>

<style lang="scss" scoped>
.info-user {
  box-sizing: border-box;
  text-align: center;

  .info-user-avatar {
    display: inline-block;
    position: relative;
    cursor: pointer;
    line-height: 0;
  }
}

.info-tags .el-tag {
  margin: 0 12px 8px 0;
}
</style>
