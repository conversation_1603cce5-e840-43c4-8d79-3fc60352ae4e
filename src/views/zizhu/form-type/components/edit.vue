<!-- 字段组编辑弹窗 -->
<template>
  <ele-drawer :size="430"
              :title="isUpdate ? '修改 ['+data.name+']' : '添加表单业务类型'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             size="small"
             :model="form"
             :rules="rules"
             label-width="auto"
             label-position="top"
             @submit.prevent="">
      <el-form-item label="业务名称" prop="name">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.name"
                  placeholder="请输入业务名称"/>
      </el-form-item>
      <el-form-item label="业务类型" prop="type">
        <el-input clearable
                  :maxlength="20"
                  :readonly="isUpdate"
                  v-model="form.type"
                  placeholder="请输入业务类型"/>
      </el-form-item>
      <el-form-item label="年份">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.year"
                  placeholder="请输入年份"/>
      </el-form-item>
      <el-form-item label="用户类型">
        <el-radio-group v-model="form.userType">
          <el-radio label="student">学生</el-radio>
          <el-radio label="teacher">教师</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="isUpdate" label="排序号" prop="sort">
        <el-input-number :min="0"
                         :max="9999"
                         v-model="form.sort"
                         placeholder="请输入排序号"
                         controls-position="right"
                         class="ele-fluid"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" size="small" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, unref} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data';
import {operation} from '../api/index.js'
import {useRouter} from "vue-router";

const emit = defineEmits(['done', 'update:modelValue']);

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  name: '',
  type: '',
  userType: 'student',
  year: '',
  sort: void 0,
});

/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入业务名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  type: [
    {
      required: true,
      message: '请输入业务类型',
      type: 'string',
      trigger: 'blur'
    }
  ],
});

/** 保存编辑 */
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields(props.data);
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);
</script>
