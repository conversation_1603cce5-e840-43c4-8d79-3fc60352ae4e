<!-- 试卷试题详情页 逐题模式-->
<template>
  <ele-card :body-style="{ padding: '7px 8px 6px 8px', height: (pageHeight+54)+'px', overflow:'auto'}">
    <div class="list-item-body">
      <div style="flex: 1">
        <template v-if="currentQuestion" :key="currentQuestion.id">
          <div style="margin-top: 16px; display: flex; align-items: center">
            <ele-text size="xxl" style="flex: 1; padding-left: 1px">
              {{ currentQuestion.sort + 1 }}、 [{{ currentQuestion.sttype }}] {{ currentQuestion.name }}
              ({{ currentQuestion.score }}分)
            </ele-text>
          </div>
          <div style="margin-top: 12px;">
            <el-input v-if="currentQuestion.sttype==='问答题'"
                      style="margin-bottom: 10px;"
                      :rows="6"
                      type="textarea"
                      show-word-limit
                      maxlength="255"
                      placeholder="请输入答案"
                      v-model="answersList[currentIndex]['result']"
                      :readonly="['perview','perviewPaper','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"/>
            <el-radio-group v-else-if="currentQuestion.sttype==='判断题'"
                            :disabled="['perview','perviewPaper','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                            v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in ['对','错']" :key="qoption">
                <el-radio :value="qoption">
                  {{ qoption }}
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <el-radio-group v-else-if="currentQuestion.sttype==='单选题'"
                            :disabled="['perview','perviewPaper','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                            v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in currentQuestion.examPaperQuestionsOptionsList"
                        :key="qoption.code">
                <el-radio size="large" :value="qoption.code">
                  <ele-text size="md">{{ qoption.code }}、{{ qoption.name }}</ele-text>
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <el-checkbox-group v-else-if="currentQuestion.sttype==='多选题'"
                               :disabled="['perview','perviewPaper','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                               v-model="answersList[currentIndex]['result']">
              <template v-for="qoption in currentQuestion.examPaperQuestionsOptionsList"
                        :key="qoption.code">
                <el-checkbox size="large" :value="qoption.code">
                  <ele-text size="md">{{ qoption.code }}、{{ qoption.name }}</ele-text>
                </el-checkbox>
                <br/>
              </template>
            </el-checkbox-group>
          </div>
        </template>
      </div>
    </div>
    <el-divider style="margin: 0; opacity: 0.6"/>
    <ele-alert v-if="['perview','perviewAlreadyMarked'].includes(enterType)" style="margin:10px;"
               :closable="false"
               show-icon
               :type="checkAnswer(currentIndex)?'success':'error'">
      <ele-text size="lg" style="margin-bottom: 6px">答题解析</ele-text>
      <ele-text size="md" style="margin-bottom: 6px">
        考生得分：{{ checkAnswer(currentIndex) ? currentQuestion.score : 0 }}分<br/>
        考生答案：{{ answersList[currentIndex]['showResult'] }}<br/>
        正确答案：{{ currentQuestion.answer }}<br/>
        答案解析：{{ currentQuestion.analysis }}<br/>
      </ele-text>
    </ele-alert>
    <div style="margin: 20px; ">
      <el-button v-if="currentIndex!==0" type="primary" plain size="small" @click="handleClickUp">上一题</el-button>
      <el-button v-if="currentIndex !== total" type="primary" plain size="small" @click="handleClickNext">
        下一题
      </el-button>
    </div>
  </ele-card>
</template>
<script setup>
import {computed, onMounted, ref, watch,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {inject} from 'vue';
import {operation} from "@/views/exam/my-exam/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  ksId: String,//考试ID
  ksms: String,//考试模式
  paperViewData: Object,
  enterType: String,
});

// 使用 inject 函数接收提供的数据
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');
const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

defineOptions({name: 'EXAMCARDINFO'});

const loading = ref(false);
const cacheExamPaperData = ref([]);
const total = ref(0);
const currentIndex = ref(0);
const answersList = ref([])

const currentQuestion = computed(() => {
  currentIndex.value = sharedData.value || sharedData.value === 0 ? sharedData.value : currentIndex.value;
  let question = {}
  if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
    question = cacheExamPaperData.value[currentIndex.value]
    if (question) sharedData.value = null;//清空右侧点击的试题编号
  }
  return question;
});

const handleClickUp = () => {
  if (currentIndex.value !== 0) {
    currentIndex.value--;
  }
};

/** 提交 */
const handleClickNext = () => {
  console.log(currentIndex.value === total.value)
  if (currentIndex.value === total.value) {
  } else {
    emit('updateAnswerVal', answersList.value);
    sharedPaperData.value = answersList.value
    if (currentIndex.value < total.value) {
      currentIndex.value++;
    }
  }
};

watch(
  () => props.ksId,
  (ksId) => {
    if (ksId) {
      if (props.paperViewData) {
        cacheExamPaperData.value = props.paperViewData?.examPaperQuestionsVOList
        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              sttype: cacheExam.sttype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
            }
            let result = ""
            if (cacheExam.sttype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
);

const checkAnswer = (currentIndex) => {
  console.log(currentIndex)
  console.log(answersList.value)
  let isCorrect = false;
  let currentQAnswer = answersList.value[currentIndex]
  console.log(currentQAnswer)
  if (currentQAnswer) {
    if (currentQAnswer.sttype === "多选题") {
      //学生答案
      let currentSelected = currentQAnswer.result
      //正确答案
      let correctOptions = currentQuestion.value?.answer.split(',') ?? [];
      if (currentSelected.length > 0 && correctOptions.length > 0) {
        // 判断用户选择的选项是否全部包含在正确答案中
        isCorrect = currentSelected.every(option => correctOptions.includes(option));
      }
    } else {
      if (currentQAnswer.result === currentQuestion.value.answer) isCorrect = true
    }
  }
  return isCorrect
}

</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 0 24px;
  //min-height: 280px;
}

.el-radio-group {
  display: unset !important;
}

.el-checkbox.is-bordered.el-checkbox--large .el-checkbox__label {
  font-size: 14px !important;
}
</style>
