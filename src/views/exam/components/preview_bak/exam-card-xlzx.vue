<!-- 试卷试题详情页 -->
<template>
  <ele-card :body-style="{ padding: '7px 8px 5px 8px!important', height: (pageHeight+10)+'px', overflow:'auto'}">
    <div v-for="(item,index) in cacheExamPaperData" :key="index">
      <div class="list-item-body" :id="item.id">
        <div style="flex: 1">
          <ele-text size="xxl"> {{ index + 1 }}、{{ item.name }} [{{ item.qtype }}]</ele-text>
          <div style="margin-top: 12px">
            <template v-if="item.qtype==='问答题'">
              <el-input :rows="6"
                        type="textarea"
                        show-word-limit
                        maxlength="255"
                        v-model="answersList[item.sort-1]['result']"
                        :disabled="readonlyVal"
                        @blur="onUpdateOptionValue"
                        placeholder="请输入答案"/>
            </template>
            <template v-else-if="item.qtype==='填空题'">
              <el-input :rows="6"
                        type="textarea"
                        show-word-limit
                        maxlength="255"
                        v-model="answersList[item.sort-1]['result']"
                        :disabled="readonlyVal"
                        @blur="onUpdateOptionValue"
                        placeholder="请输入"/>
            </template>
            <template v-else-if="item.qtype==='上传题'">
              <file-upload :limit="8" :modelValue="answersList[item.sort-1]['result']"
                           :contentId="answersList[item.sort-1]['contentId']"
                           :readonly="readonlyVal"
                           @update:modelValue="updateFileValue($event,item.sort-1)" list-type="image"/>
            </template>
            <template v-else-if="item.qtype==='评分题'">
              <el-rate v-model="answersList[item.sort-1]['result']" allow-half :max="item.maxScore"
                       size="large"
                       :disabled="readonlyVal"/>
            </template>
            <el-radio-group v-else-if="item.qtype==='判断题'"
                            :disabled="readonlyVal"
                            @change="onUpdateOptionValue"
                            v-model="answersList[item.sort-1]['result']">
              <template v-for="qoption in ['对','错']" :key="qoption">
                <el-radio :value="qoption" :label="qoption">
                  {{ qoption }}
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <el-radio-group v-else-if="item.qtype==='单选题'"
                            @change="onUpdateOptionValue($event,item.sort-1,item.surveyPquestionsOptionsList)"
                            :disabled="readonlyVal"
                            v-model="answersList[item.sort-1]['result']">
              <template v-for="qoption in item.surveyPquestionsOptionsList"
                        :key="qoption.code">
                <el-radio :value="qoption.code" :label="qoption.code">
                  {{ qoption.code }}、{{ qoption.name }}
                  <!--                    <span v-if="baseData&&baseData.sfjf==='是'">-->
                  <!--                      ({{ qoption?.score ?? 0 }}分)</span>-->
                </el-radio>
                <br/>
              </template>
            </el-radio-group>
            <el-checkbox-group v-else-if="item.qtype==='多选题'"
                               @change="onUpdateOptionValue($event,item.sort-1,item.surveyPquestionsOptionsList)"
                               :disabled="readonlyVal"
                               v-model="answersList[item.sort-1]['result']">
              <template v-for="qoption in item.surveyPquestionsOptionsList"
                        :key="qoption.code">
                <el-checkbox :value="qoption.code" :label="qoption.code">
                  {{ qoption.code }}、{{ qoption.name }}
                  <span v-if="paperViewData&&paperViewData.sfjf==='是'">
                      ({{ qoption?.score ?? 0 }}分)</span>
                </el-checkbox>
                <br/>
              </template>
            </el-checkbox-group>
          </div>
          <el-divider style="margin: 0; opacity: 0.6"/>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import {computed, inject, onMounted, ref, watch} from 'vue';
import FileUpload from "@/components/FileUpload/index.vue";
import {isImageFile} from "@/utils/common_bak2.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const BASE_URL = import.meta.env.BASE_URL;

const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  paperId: String,//试卷ID
  ksms: String,//考试模式
  paperViewData: Object,
  enterType: String,
  currentPageHeight: String,
});

const sharedPaperData = inject('sharedPaperData');

defineOptions({name: 'EXAMCARD'});

/** 请求状态 */
const loading = ref(false);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const cacheExamPaperData = ref([]);
const total = ref(0);
const answersList = ref([])
/** 列表数据 */
const data = ref([])

const readonlyVal = computed(() => {
  return ['perviewPaperXlzx'].includes(props.enterType);
});

const updateFileValue = (data, index) => {
  answersList.value[index]['resultFiles'] = data
  emit('updateAnswerVal', answersList.value);
}

const onUpdateOptionValue = (data, index, surveyPquestionsOptionsList) => {
  if (data && surveyPquestionsOptionsList) {
    if (typeof data === 'string') {
      answersList.value[index]['resultId'] = surveyPquestionsOptionsList.filter(e => e.code === data)[0].id
    } else if (typeof data === 'object') {
      let rdata = []
      surveyPquestionsOptionsList.forEach(e => {
        data.forEach(code => {
          if (e.code === code) rdata.push(e.id)
        })
      })
      answersList.value[index]['resultId'] = rdata.join()
    }
  }
  console.log("answersList===", answersList.value)
  sharedPaperData.value = answersList.value
  emit('updateAnswerVal', answersList.value);
}

const checkAnswer = (question) => {
  let isCorrect = false;
  if (question.qtype === "多选题") {
    //学生答案
    let currentSelected = question.result ? question.result.split(',') : [];
    //正确答案
    let correctOptions = question.answer ? question.answer.split(',') : [];
    if (currentSelected.length > 0 && correctOptions.length > 0) {
      // 判断用户选择的选项是否全部包含在正确答案中
      isCorrect = currentSelected.every(option => correctOptions.includes(option));
    }
  } else {
    if (question.result === question.answer) isCorrect = true
  }
  return isCorrect
}
/**
 * 试卷预览
 */
watch(
  () => props.paperId,
  (paperId) => {
    if (paperId) {
      if (props.paperViewData) {
        console.log(props.paperViewData)
        cacheExamPaperData.value = props.paperViewData?.surveyPaperQuestionsVOList
        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              qtype: cacheExam.qtype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
              manualScoring: "",
            }
            let result = ""
            if (cacheExam.qtype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else if (cacheExam.qtype === "上传题") {
              let uploadFiles = cacheExam.result ? JSON.parse(cacheExam.result) : []
              let oldFiles = [];
              uploadFiles.map((d, i) => {
                let newObj = {
                  key: d.id,
                  name: d.originalFilename,
                  contentType: d.contentType,
                  fileUrl: BASE_URL + 'api/file/inline/' + d.id,
                  status: 'done'
                }
                let mark = isImageFile(newObj)
                newObj.isImageFile = mark;
                if (mark) newObj.url = newObj.fileUrl
                oldFiles.push(newObj);
              })
              result = oldFiles
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
)
</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 5px 15px;
}

.el-radio-group {
  display: unset !important;
}

:deep(.el-radio .el-radio__label,) {
  font-size: 14px !important;
}

:deep(.el-checkbox .el-checkbox__label) {
  font-size: 14px !important;
}

</style>
