<!-- 试卷试题详情页 整卷模式-->
<template>
  <ele-card :body-style="{padding: '5px!important',height:(pageHeight+10)+'px',overflow:'auto' }">
    <div v-for="(item,index) in data" :key="index">
      <div class="list-item-body">
        <div style="flex: 1">
          <ele-text size="xxl">{{ item.name }} （共{{ item?.qsum ?? 0 }}题，共{{ item?.totalScore ?? 0 }}分）</ele-text>
          <template v-for="(question,qindex) in item.examPaperQuestionsVOList" :key="question.id">
            <div :id="question.id" style="margin-top: 16px; display: flex; align-items: center">
              <ele-text size="lg" style="flex: 1; padding-left: 1px">
                {{ question.sort + 1 }}、[{{ question.sttype }}] {{ question.name }} ({{ question.score }}分)
              </ele-text>
            </div>
            <div style="margin-top: 12px">
              <template v-if="question.sttype==='问答题'">
                <el-input :rows="6"
                          type="textarea"
                          show-word-limit
                          maxlength="255"
                          v-model="answersList[question.sort]['result']"
                          :disabled="['perview','perviewPaper','toGradingPapers','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                          @blur="onUpdateOptionValue"
                          placeholder="请输入答案"/>
                <el-row v-if="enterType==='toGradingPapers'" style="margin: 10px 0 10px 0;">
                  <ele-text size="lg">人工评分&nbsp;&nbsp;
                    <el-input-number v-model="answersList[question.sort]['manualScoring']"
                                     :min="0"
                                     :max="answersList[question.sort]['point']"
                                     size="small"
                                     controls-position="right"/>
                  </ele-text>
                </el-row>
              </template>
              <el-radio-group v-else-if="question.sttype==='判断题'"
                              :disabled="['perview','perviewPaper','toGradingPapers','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                              @change="onUpdateOptionValue"
                              v-model="answersList[question.sort]['result']">
                <template v-for="qoption in ['对','错']" :key="qoption">
                  <el-radio :value="qoption">
                    {{ qoption }}
                  </el-radio>
                  <br/>
                </template>
              </el-radio-group>
              <el-radio-group v-else-if="question.sttype==='单选题'" @change="onUpdateOptionValue"
                              :disabled="['perview','perviewPaper','toGradingPapers','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                              v-model="answersList[question.sort]['result']">
                <template v-for="qoption in question.examPaperQuestionsOptionsList"
                          :key="qoption.code">
                  <el-radio :value="qoption.code">
                    {{ qoption.code }}、{{ qoption.name }}
                  </el-radio>
                  <br/>
                </template>
              </el-radio-group>
              <el-checkbox-group v-else-if="question.sttype==='多选题'" @change="onUpdateOptionValue"
                                 :disabled="['perview','perviewPaper','toGradingPapers','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
                                 v-model="answersList[question.sort]['result']">
                <template v-for="qoption in question.examPaperQuestionsOptionsList"
                          :key="qoption.code">
                  <el-checkbox :value="qoption.code">
                    {{ qoption.code }}、{{ qoption.name }}
                  </el-checkbox>
                  <br/>
                </template>
              </el-checkbox-group>
            </div>
            <el-divider style="margin: 0; opacity: 0.6"/>
            <ele-alert v-if="['perview','perviewAlreadyMarked','toGradingPapers'].includes(enterType)"
                       style="margin-top:10px;"
                       :closable="false"
                       :show-icon="question.sttype==='问答题'?false:true"
                       :type="checkAnswer(question)?'success':'error'">
              <ele-text size="lg" style="margin-bottom: 6px">答题解析</ele-text>
              <ele-text size="md" style="margin-bottom: 6px">
                考生得分：{{
                  checkAnswer(question) ? question.score : 0
                }}分<br/>
                考生答案：{{ question.result }}<br/>
                正确答案：{{ question.answer }}<br/>
                答案解析：{{ question.analysis }}<br/>
              </ele-text>
            </ele-alert>
          </template>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import {inject, ref, watch} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
const BASE_URL = import.meta.env.BASE_URL;

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  paperId: String,//试卷ID
  ksId: String,//考试ID
  ksms: String,//考试模式
  paperViewData: Object,
  enterType: String,
});

const sharedPaperData = inject('sharedPaperData');

defineOptions({name: 'EXAMCARD'});

/** 请求状态 */
const loading = ref(false);

const cacheExamPaperData = ref([]);
const total = ref(0);
const currentIndex = ref(0);
const answersList = ref([])
/** 列表数据 */
const data = ref([])

const onUpdateOptionValue = () => {
  sharedPaperData.value = answersList.value
  emit('updateAnswerVal', answersList.value);
}

const checkAnswer = (question) => {
  let isCorrect = false;
  if (question.sttype === "多选题") {
    //学生答案
    let currentSelected = question.result ? question.result.split(',') : [];
    //正确答案
    let correctOptions = question.answer ? question.answer.split(',') : [];
    if (currentSelected.length > 0 && correctOptions.length > 0) {
      // 判断用户选择的选项是否全部包含在正确答案中
      isCorrect = currentSelected.every(option => correctOptions.includes(option));
    }
  } else {
    if (question.result === question.answer) isCorrect = true
  }
  return isCorrect
}
/**
 * 试卷预览
 */
watch(
  () => props.paperId,
  (paperId) => {
    if (paperId) {
      if (props.paperViewData) {
        data.value = props.paperViewData.questionsGroupList;
        cacheExamPaperData.value = props.paperViewData?.examPaperQuestionsVOList
        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              sttype: cacheExam.sttype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
              manualScoring: "",
            }
            let result = ""
            if (cacheExam.sttype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
)

/**
 * 考试试卷预览
 */
watch(
  () => props.ksId,
  (ksId) => {
    if (ksId) {
      if (props.paperViewData) {
        console.log(props.paperViewData)
        data.value = props.paperViewData.questionsGroupList;
        cacheExamPaperData.value = props.paperViewData?.examPaperQuestionsVOList
        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              sttype: cacheExam.sttype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
              manualScoring: "",
              examInfoId: "",
            }
            let result = ""
            if (cacheExam.sttype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
);


</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 0 20px;
}

.el-radio-group {
  display: unset !important;
}

:deep .el-radio .el-radio__label, {
  font-size: 14px !important;
}

:deep .el-checkbox .el-checkbox__label {
  font-size: 14px !important;
}

</style>
