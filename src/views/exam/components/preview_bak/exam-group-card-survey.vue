<!-- 试卷试题组 -->
<template>
  <ele-card :border="true" :body-style="{padding: '5px!important',height: (pageHeight+10)+'px', overflow:'auto'}"
            :header-style="{ 'text-align':'center' }">
    <el-row v-for="(group, index) in groupList"
            :key="group.id"
            class="group-container">
      <el-col :span="24">
        <div class="group-header">
          <ele-text size="lg" style="white-space: pre-line;">
            {{ group.name }}
            <span class="group-info">
          (共{{ group.qsum || 0 }}题)
        </span>
          </ele-text>
        </div>
      </el-col>
      <el-col :span="24">
        <el-row :gutter="8"
                class="tag-container">
          <el-col v-for="item in group.surveyPquestionsVOList"
                  :key="item.id"
                  :xs="4"
                  :sm="4"
                  :md="4">
            <el-tag @click="changeElTag(item)"
                    :type="changeTagtype(item)"
                    effect="plain"
                    size="default"
                    class="question-tag">
              {{ item.sort }}
            </el-tag>
          </el-col>
        </el-row>
      </el-col>
      <el-divider style="margin: 0; opacity: 0.6"/>
    </el-row>
  </ele-card>
</template>

<script setup>
import {nextTick,  ref, watch} from "vue";
import {inject} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emit = defineEmits(['updateNoAnswerNumber', 'updateUpTimeVal', 'autoSumbit']);
/**
 * 使用 inject 函数接收提供的数据
 * @type {string}
 */
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');
const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const props = defineProps({
  title: String,
  // uptime: String,
  // effectiveTime: Number,
  // dtms: String,
  enterType: String,
  // paperAnswerData: Object,
  paperViewData: Object,//试卷数据
});

const paperAnswerData = ref(null);
const groupList = ref([])

watch(
  () => props.enterType,
  (enterType) => {
    nextTick(() => {
      if (props.paperViewData) {
        groupList.value = props.paperViewData.questionsGroupList;
        if (enterType === "perviewPaper") {//管理端-问卷预览
          // groupList.value = props.paperAnswerData.questionsGroupList;
        } else {
          paperAnswerData.value = props.paperViewData.surveyPaperQuestionsVOList;
        }
      }
    })
  },
  {immediate: true}
)

/**
 * 根据右侧试题答题情况，格式化tag类型
 * @param item
 * @returns {string}
 */
const changeTagtype = (item) => {
  let type = "info"
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      let checkedData = sharedPaperData.value?.filter?.((d) => d.contentId === item.id)[0]
      if (checkedData) {
        if (item.qtype === "多选题") {
          type = checkedData['result'].length > 0 ? "primary" : "info"
        } else if (item.qtype === "上传题") {
          type = checkedData['resultFiles'].length > 0 ? "primary" : "info"
        } else {
          type = checkedData['result'] ? "primary" : "info"
        }
      }
      // if (props.enterType === 'perviewAlreadyMarked') type = "primary";

    }
  }
  return type
}

const handleAutoSumbit = (data) => {
  emit('updateUpTimeVal', sharedPaperUpTimeData.value);
  emit('autoSumbit', data);
}
const handleCheckedAnswer = () => {
  let answered = 0
  let noAnswered = props.paperViewData.examPaperVO.examPaperQuestionsVOList.length;
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      sharedPaperData.value.forEach(chk => {
        if (chk.qtype === "多选题") {
          const isArrayEmpty = (arr) => arr.length === 0;
          if (!isArrayEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        } else {
          const isStringEmpty = (str) => !str || str.trim() === '';
          if (!isStringEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        }
      })
    }
  }
  emit('updateNoAnswerNumber', noAnswered);
  emit('updateUpTimeVal', sharedPaperUpTimeData.value);
  return [answered, noAnswered]
}

const changeElTag = (tag) => {
  if (props.paperViewData.dtms === '逐题模式') {
    sharedData.value = tag.sort;
  } else {
    /**
     * 锚点滚动，整卷模式查看
     * 在不使用<a>标签的情况下实现锚点滚动，使用JavaScript来更改窗口的滚动位置。
     * 实现每个<div>设定了唯一的id，并且通过点击按钮触发scrollToDiv函数，该函数接受一个参数divId，
     * 根据这个id查找对应的元素，并使用scrollIntoView方法平滑滚动到该元素的位置
     * @type {HTMLElement}
     */
    const element = document.getElementById(tag.id);
    if (element) {
      element.scrollIntoView({behavior: 'smooth'});
    }
  }
}

</script>

<style>
.custom-name {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-left: 10px;
}
</style>
