<template>
  <ele-card :body-style="{ padding: '0 8px 10px 8px!important'}">
    <div style="text-align: center">
      <el-progress
        type="circle"
        :percentage="safePercentage"
        :format="formatTime"
      >
        <template #default>
          <span class="percentage-value">{{ formatTime() }}</span>
          <span class="percentage-label">{{ uptime }}分钟</span>
        </template>
      </el-progress>
    </div>
  </ele-card>
</template>

<script setup>
import {ref, computed, onMounted, onBeforeUnmount, inject} from 'vue';
import MomentWrap from "@/utils/momentWrap.js";

const emit = defineEmits(['autoSumbit', 'updateUpTimeVal']);

const props = defineProps({
  title: String,
  baseData: Object,
});

const uptime = ref(0);
const totalTime = ref(0); // 总时长（秒）
const currentTime = ref(0); // 当前已用时（秒）
const timer = ref(null);
const effectiveTime = ref(0);

// 保护性计算，避免 NaN
const safePercentage = computed(() => {
  if (totalTime.value <= 0) return 0;
  return Math.min(100, Math.floor((currentTime.value / totalTime.value) * 100));
});

const formatTime = () => {
  const remainingTime = totalTime.value - currentTime.value;
  const hours = Math.floor(remainingTime / 3600);
  const minutes = Math.floor((remainingTime % 3600) / 60);
  const seconds = remainingTime % 60;
  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

onMounted(() => {
  uptime.value = props.baseData.uptime ?? 0;
  if (uptime.value) {
    totalTime.value = uptime.value * 60;
    effectiveTime.value = MomentWrap.commonDiffSeconds(new Date(), props.baseData.jssj);
    if (effectiveTime.value < totalTime.value) {
      totalTime.value = effectiveTime.value;
    }

    timer.value = setInterval(() => {
      if (currentTime.value >= totalTime.value) {
        emit('autoSumbit', '考试倒计时结束,系统自动交卷');
        emit('updateUpTimeVal', currentTime.value);
        clearInterval(timer.value);
      } else {
        currentTime.value++;
        emit('updateUpTimeVal', currentTime.value);
      }
    }, 1000);
  }
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});
</script>

<style scoped>
.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 16px;
}

.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}

.demo-progress .el-progress--line {
  margin-bottom: 15px;
  max-width: 600px;
}

.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>
