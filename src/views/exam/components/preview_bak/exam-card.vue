<template>
  <ele-card :body-style="{padding: '5px', height: `${pageHeight + 10}px`, overflow: 'auto'}">
    <template v-for="(group, groupIndex) in questionGroups" :key="groupIndex">
      <div class="question-group">
        <div class="group-header">
          <ele-text size="xxl">
            {{ group.name }} （共{{ group.qsum || 0 }}题，共{{ group.totalScore || 0 }}分）
          </ele-text>
        </div>

        <template v-for="(question, qIndex) in group.examPaperQuestionsVOList" :key="question.id">
          <question-item :question="question"
                         :enter-type="enterType"
                         :answer="getAnswer(question)"
                         @update-answer="handleAnswerUpdate"/>
        </template>
      </div>
    </template>
  </ele-card>
</template>

<script setup>
import {ref, watch, computed, inject} from 'vue';
import {useUserStore} from "@/store/modules/user";
import {storeToRefs} from "pinia";
import QuestionItem from '../cardInfo/QuestionItem.vue';

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const emit = defineEmits(['updateAnswerVal']);

const props = defineProps({
  title: String,
  paperId: String,
  ksId: String,
  ksms: String,
  paperViewData: Object,
  enterType: String,
});

// 注入的共享数据  修改注入，提供默认值
const sharedPaperData = inject('sharedPaperData', ref([]));

// 响应式数据
const answersList = ref([]);
const questionGroups = ref([]);

// 计算属性：判断是否为只读模式
const isReadonly = computed(() =>
  ['perview', 'perviewPaper', 'toGradingPapers', 'perviewExamPaper', 'perviewPendingGrading', 'perviewAlreadyMarked']
    .includes(props.enterType)
);

// 初始化问题数据
const initQuestions = (questions) => {
  answersList.value = questions.map(question => ({
    contentId: question.id,
    sttype: question.sttype,
    point: question?.score ?? 0,
    answer: question?.answer ?? "",
    analysis: question?.analysis ?? "",
    sort: question?.sort ?? "",
    gid: question?.gid ?? "",
    showResult: question.result,
    manualScoring: "",
    examInfoId: "",
    result: question.sttype === "多选题"
      ? question.result ? question.result.split(',') : []
      : question?.result ?? ""
  }));
  // 安全设置值
  if (sharedPaperData) {
    sharedPaperData.value = answersList.value;
  }
  emit('updateAnswerVal', answersList.value);
};

// 获取答案
const getAnswer = (question) => {
  return answersList.value.find(answer => answer.contentId === question.id);
};

// 处理答案更新
const handleAnswerUpdate = (updatedAnswer) => {
  const index = answersList.value.findIndex(a => a.contentId === updatedAnswer.contentId);
  if (index !== -1) {
    answersList.value[index] = updatedAnswer;
    if (sharedPaperData) {
      sharedPaperData.value = answersList.value;
    }
    console.log("sharedPaperData.value =", sharedPaperData.value)
    emit('updateAnswerVal', answersList.value);
  }
};

// 监听试卷数据变化
watch(
  () => props.paperViewData,
  (newData) => {
    if (newData?.questionsGroupList) { // 添加可选链
      questionGroups.value = newData.questionsGroupList;
      if (newData.examPaperQuestionsVOList) {
        initQuestions(newData.examPaperQuestionsVOList);
      }
    }
  },
  {immediate: true, deep: true}
);
</script>

<style lang="scss" scoped>
.question-group {
  margin-bottom: 20px;
}

.group-header {
  padding: 10px 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .group-header {
    padding: 10px;
  }
}
</style>
