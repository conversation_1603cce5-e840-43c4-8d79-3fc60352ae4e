<!-- 试卷试题组 -->
<template>
  <ele-card :body-style="{ padding: '7px 8px 5px 8px!important', height: (pageHeight+10)+'px', overflow:'auto'}">
    <el-row v-if="enterType==='toAnswerXlzx'">
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag size="small"
                style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="primary">
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        已答&nbsp;{{ handleCheckedAnswer()[0] }}
      </el-col>
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag size="small"
                style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="info">
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        未答&nbsp;{{ handleCheckedAnswer()[1] }}
      </el-col>
    </el-row>

    <div class="tag-grid">
      <el-tag v-for="(item, i) in paperAnswerData"
              @click="changeElTag(item)"
              :type="changeTagtype(item)"
              effect="plain"
              size="default"
              :key="item.id"
              class="tag-item">
        {{ item.sort }}
      </el-tag>
    </div>
  </ele-card>
</template>

<script setup>
import {nextTick,  ref, watch} from "vue";
import {inject} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const emit = defineEmits(['updateNoAnswerNumber', 'updateUpTimeVal', 'autoSumbit']);
/**
 * 使用 inject 函数接收提供的数据
 * @type {string}
 */
const sharedPaperData = inject('sharedPaperData');
const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const props = defineProps({
  title: String,
  enterType: String,
  paperViewData: Object,//试卷数据
});

const paperAnswerData = ref([]);

const isStringEmpty = (str) => {
  return typeof str !== 'string' || str.trim() === '';
}
const handleCheckedAnswer = () => {
  let answered = 0
  let noAnswered = paperAnswerData.value.length;
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      sharedPaperData.value.forEach(chk => {
        if (chk.sttype === "多选题") {
          const isArrayEmpty = (arr) => arr.length === 0;
          if (!isArrayEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        } else {
          if (!isStringEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        }
      })
    }
  }
  // emit('updateNoAnswerNumber', noAnswered);
  // emit('updateUpTimeVal', sharedPaperUpTimeData.value);
  return [answered, noAnswered]
}

/**
 * 根据右侧试题答题情况，格式化tag类型
 * @param item
 * @returns {string}
 */
const changeTagtype = (item) => {
  let type = "info"
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      let checkedData = sharedPaperData.value?.filter?.((d) => d.contentId === item.id)[0]
      if (checkedData) {
        if (item.qtype === "多选题") {
          type = checkedData['result'] && checkedData['result'].length > 0 ? "primary" : "info"
        } else if (item.qtype === "上传题") {
          type = checkedData['resultFiles'] && checkedData['resultFiles'].length > 0 ? "primary" : "info"
        } else {
          type = checkedData['result'] && checkedData['result'] ? "primary" : "info"
        }
      }

    }
  }
  return type
}

/**
 * 延迟滚动，确保 DOM 渲染完毕再滚动
 * @param tag
 */
const changeElTag = (tag) => {
  // 使用 nextTick 确保 DOM 更新完成
  /**
   * 锚点滚动，整卷模式查看
   * 在不使用<a>标签的情况下实现锚点滚动，使用JavaScript来更改窗口的滚动位置。
   * 实现每个<div>设定了唯一的id，并且通过点击按钮触发scrollToDiv函数，该函数接受一个参数divId，
   * 根据这个id查找对应的元素，并使用scrollIntoView方法平滑滚动到该元素的位置
   * @type {HTMLElement}
   */
  nextTick(() => {
    const element = document.getElementById(tag.id);
    if (element) {
      element.scrollIntoView({behavior: 'smooth', block: 'start'});
    } else {
      console.warn(`Element with id ${tag.id} not found.`);
    }
  });
};

watch(
  () => props.enterType,
  (enterType) => {
    nextTick(() => {
      if (enterType && props.paperViewData) {
        paperAnswerData.value = Array.isArray(props.paperViewData?.surveyPaperQuestionsVOList)
          ? props.paperViewData.surveyPaperQuestionsVOList
          : [];
      }
    })
  },
  {immediate: true}
)
</script>
<style scoped>
.tag-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr); /* 每行 6 个 */
  gap: 10px 8px; /* 行间距、列间距 */
  margin-top: 5px;
  padding: 0 2px;
}

.tag-item {
  cursor: pointer;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}
</style>
