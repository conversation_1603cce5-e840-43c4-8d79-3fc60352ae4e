<!-- 试卷试题组 -->
<template>
  <ele-card :border="true" :body-style="{padding: '5px!important',height: (pageHeight+10)+'px', overflow:'auto'}"
            :header-style="{ 'text-align':'center' }">
    <template v-if="baseData?.uptime">
      <goal-card v-if="enterType==='toAnswer'" :baseData="baseData" @autoSumbit="handleAutoSumbit"
                 @updateUpTimeVal="handleUpdateUpTimeVal"/>
      <el-divider v-if="enterType==='toAnswer'" style="margin: 0; opacity: 0.6"/>
      <template v-if="['perviewAlreadyMarked','perview'].includes(enterType)">
        <el-row style="margin-bottom: 5px;">
          <el-col :md="12" :sm="12" :xs="24">
            <div style="display: flex; padding: 0 10px 0 10px; min-height: 22px">
              <div style="flex: 1;  min-width: 0;">
                <ele-text size="lg">{{ paperAnswerData.score }}</ele-text>
                <ele-text size="sm">考试成绩</ele-text>
              </div>
            </div>
          </el-col>
          <el-col :md="12" :sm="12" :xs="24">
            <div style="display: flex; padding: 0 10px 0 10px; min-height: 22px">
              <div style="flex: 1;  min-width: 0;">
                <ele-text size="lg">{{ paperAnswerData.sfhg }}</ele-text>
                <ele-text size="sm">是否合格</ele-text>
              </div>
            </div>
          </el-col>
          <el-col :md="12" :sm="12" :xs="24">
            <div style="display: flex; padding: 0 10px 0 10px; min-height: 22px">
              <div style="flex: 1;  min-width: 0;">
                <ele-text size="lg">{{ paperAnswerData.kscs }}</ele-text>
                <ele-text size="sm">考试次数</ele-text>
              </div>
            </div>
          </el-col>
          <el-col :md="12" :sm="12" :xs="24">
            <div style="display: flex; padding: 0 10px 0 10px; min-height: 22px">
              <div style="flex: 1;  min-width: 0;">
                <ele-text size="lg">{{ paperAnswerData.ksys }}</ele-text>
                <ele-text size="sm">考试用时</ele-text>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-divider style="margin: 0; opacity: 0.6"/>
      </template>
      <el-divider v-if="enterType==='perview'" style="margin: 0; opacity: 0.6"/>
      <el-row>
        <el-col :md="12" :sm="12" :xs="24">
          <el-tag size="small"
                  style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="primary">
            &nbsp;&nbsp;&nbsp;
          </el-tag>
          已答&nbsp;{{ handleCheckedAnswer()[0] }}
        </el-col>
        <el-col :md="12" :sm="12" :xs="24">
          <el-tag size="small"
                  style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="info">
            &nbsp;&nbsp;&nbsp;
          </el-tag>
          未答&nbsp;{{ handleCheckedAnswer()[1] }}
        </el-col>
        <template v-if="['perviewAlreadyMarked',].includes(enterType)&&baseData.examAnswerInfo">
          <el-col :md="12" :sm="12" :xs="24">
            <el-tag size="small"
                    style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="success">
              &nbsp;&nbsp;&nbsp;
            </el-tag>
            正确&nbsp;{{ baseData.examAnswerInfo['ddt'] }}
          </el-col>
          <el-col :md="12" :sm="12" :xs="24">
            <el-tag size="small"
                    style="margin:9px 10px 10px 10px;cursor: pointer;" effect="plain" type="danger">
              &nbsp;&nbsp;&nbsp;
            </el-tag>
            错误&nbsp;{{ baseData.examAnswerInfo['dcs'] }}
          </el-col>
        </template>
      </el-row>
      <el-divider style="margin: 0; opacity: 0.6"/>
    </template>
    <el-row v-for="(group, index) in groupList"
            :key="group.id"
            class="group-container">
      <el-col :span="24">
        <div class="group-header">
          <ele-text size="lg" style="white-space: pre-line;">
            {{ group.name }}
            <span class="group-info">
          (共{{ group.qsum || 0 }}题，共{{ group.totalScore || 0 }}分)
        </span>
          </ele-text>
        </div>
      </el-col>
      <el-col :span="24">
        <el-row :gutter="8"
                class="tag-container">
          <el-col v-for="item in group.examPaperQuestionsVOList"
                  :key="item.id"
                  :xs="4"
                  :sm="4"
                  :md="4">
            <el-tag @click="changeElTag(item)"
                    :type="changeTagtype(item)"
                    effect="plain"
                    size="default"
                    class="question-tag">
              {{ item.sort + 1 }}
            </el-tag>
          </el-col>
        </el-row>
      </el-col>
      <el-divider style="margin: 0; opacity: 0.6"/>
    </el-row>

  </ele-card>
</template>

<script setup>
import {computed,  ref, watch} from "vue";
import {inject} from 'vue';
import GoalCard from "@/views/exam/components/preview/goal-card.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const emit = defineEmits(['updateNoAnswerNumber', 'updateUpTimeVal', 'autoSumbit']);
/**
 * 使用 inject 函数接收提供的数据
 * @type {string}
 */
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');
// const sharedPaperUpTimeData = inject('sharedPaperUpTimeData');

const props = defineProps({
  title: String,
  // uptime: String,
  // effectiveTime: Number,
  // dtms: String,
  enterType: String,
  // paperAnswerData: Object,
  baseData: Object,//试卷数据
});

const paperAnswerData = ref(null);
const groupList = ref([])

// 计算属性：根据enterType确定组列表数据
const normalizedGroupList = computed(() => {
  if (!props.baseData) return [];

  return props.enterType === "perviewPaper"
    ? props.baseData?.questionsGroupList
    : props.baseData.examPaperVO?.questionsGroupList;
});

watch(
  () => [props.enterType, props.baseData],
  () => {
    groupList.value = normalizedGroupList.value;
    if (props.enterType !== "perviewPaper") {
      paperAnswerData.value = props.baseData?.examAnswerInfo;
    }
  },
  {immediate: true}
);
/**
 * 根据右侧试题答题情况，格式化tag类型
 * @param item
 * @returns {string}
 */
const changeTagtype = (item) => {
  let type = "info"
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      let checkedData = sharedPaperData.value?.filter?.((d) => d.contentId === item.id)[0]
      if (checkedData) {
        if (item.sttype === "多选题") {
          type = checkedData['result'].length > 0 ? "primary" : "info"
        } else {
          type = checkedData['result'] ? "primary" : "info"
        }
      }
      if (props.enterType === 'perviewAlreadyMarked') type = item.resultCorrect ? "success" : "danger";

    }
  }
  // return isSelected(item) ? 'success' : type
  return type
}

const handleAutoSumbit = (data) => {
  emit('autoSumbit', data);
}
const handleUpdateUpTimeVal = (data) => {
  emit('updateUpTimeVal', data);
}
const handleCheckedAnswer = () => {
  let answered = 0
  let noAnswered = props.baseData.examPaperVO.examPaperQuestionsVOList.length;
  if (sharedPaperData !== undefined) {
    if (sharedPaperData.value) {
      sharedPaperData.value.forEach(chk => {
        if (chk.sttype === "多选题") {
          const isArrayEmpty = (arr) => arr.length === 0;
          if (!isArrayEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        } else {
          const isStringEmpty = (str) => !str || str.trim() === '';
          if (!isStringEmpty(chk.result)) {
            answered++;
            noAnswered--;
          }
        }
      })
    }
  }
  emit('updateNoAnswerNumber', noAnswered);
  // emit('updateUpTimeVal', sharedPaperUpTimeData.value);
  return [answered, noAnswered]
}

// const selectedTags = ref([]); // 存储选中的 tag ID

const changeElTag = (tag) => {
  if (props.baseData.dtms === '逐题模式') {
    sharedData.value = tag.sort;
    /**
     * 锚点滚动，整卷模式查看
     * 在不使用<a>标签的情况下实现锚点滚动，使用JavaScript来更改窗口的滚动位置。
     * 实现每个<div>设定了唯一的id，并且通过点击按钮触发scrollToDiv函数，该函数接受一个参数divId，
     * 根据这个id查找对应的元素，并使用scrollIntoView方法平滑滚动到该元素的位置
     * @type {HTMLElement}
     */
    const element = document.getElementById(tag.id);
    if (element) {
      element.scrollIntoView({behavior: 'smooth'});
    }
  }
  // const index = selectedTags.value.indexOf(tag.id);
  // if (index === -1) {
  //   selectedTags.value.push(tag.id); // 选中
  // } else {
  //   selectedTags.value.splice(index, 1); // 取消选中
  // }
}
// const isSelected = (item) => selectedTags.value.includes(item.id);
//
//
// const props = defineProps({
//   group: Object,
//   enterType: String,
//   dtms: String
// });
// const emit = defineEmits(['question-click']);
// 获取标签类型
// const getTagType = (item) => {
//   if (props.enterType === 'perviewAlreadyMarked') {
//     return item.resultCorrect ? "success" : "danger";
//   }
//
//   const hasAnswer = item.sttype === "多选题"
//     ? item.result?.length > 0
//     : Boolean(item.result);
//
//   return hasAnswer ? "primary" : "info";
// };
</script>

<style>
.group-container {
  padding: 0 0 10px 0;
}

.tag-container {
  margin-top: 5px;
}

.question-tag {
  cursor: pointer;
  margin-bottom: 8px; /* 垂直间距 */
  /*width: 100%;*/
  text-align: center;
  transition: all 0.3s;

  /* 保证方形样式 */
  width: 30px;
  height: 30px;
  line-height: 30px;
  padding: 0 5px;
}

/* 鼠标悬停效果 */
.question-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>


<style scoped>
.question-group {
  margin-bottom: 10px;
}

.group-header {
  padding: 5px 10px;
}

.group-info {
  font-size: 0.8em;
  color: #888;
}

.question-tags {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  padding: 5px 10px;
}

.question-tag {
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
  line-height: 38px;
}

.question-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .question-tags {
    grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
  }

  .question-tag {
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
  }
}
</style>
