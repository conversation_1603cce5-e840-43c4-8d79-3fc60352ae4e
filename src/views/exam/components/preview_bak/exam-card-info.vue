<template>
  <ele-card :body-style="{ padding: '7px 8px 6px 8px', height: (pageHeight+54)+'px', overflow:'auto'}">
    <question-item v-if="currentQuestion"
                   :question="currentQuestion"
                   :enter-type="enterType"
                   :answer="getAnswer(currentQuestion)"
                   @update-answer="handleAnswerUpdate"/>
    <div style="margin: 20px; ">
      <el-button v-if="currentIndex !== 0" type="primary" plain size="small" @click="handleClickUp">上一题</el-button>
      <el-button v-if="currentIndex !== total" type="primary" plain size="small" @click="handleClickNext">
        下一题
      </el-button>
    </div>
  </ele-card>
</template>

<script setup>
import {computed, onMounted, ref, watch,} from 'vue';
import {inject} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import QuestionItem from "@/views/exam/components/cardInfo/QuestionItem.vue";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  ksId: String,//考试ID
  ksms: String,//考试模式
  paperViewData: Object,
  enterType: String,
});

// 使用 inject 函数接收提供的数据
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');

defineOptions({name: 'EXAMCARDINFO'});

// 获取答案
const getAnswer = (question) => {
  return answersList.value.find(answer => answer.contentId === question.id);
};

// 处理答案更新
const handleAnswerUpdate = (updatedAnswer) => {
  const index = answersList.value.findIndex(a => a.contentId === updatedAnswer.contentId);
  if (index !== -1) {
    answersList.value[index] = updatedAnswer;
    sharedPaperData.value = answersList.value;
    emit('updateAnswerVal', answersList.value);
  }
};

const loading = ref(false);
const cacheExamPaperData = ref([]);
const total = ref(0);
const currentIndex = ref(0);  // 默认从第一题开始
const answersList = ref([]);

// 计算当前问题
const currentQuestion = computed(() => {
  // 先保护 currentIndex
  const index = sharedData?.value ?? currentIndex?.value ?? 0;  // 确保 `index` 至少为 0
  // 如果还没加载题目，直接返回 null
  if (!cacheExamPaperData.value || cacheExamPaperData.value.length === 0) {
    return null;
  }
  // 防止 index 超出题目数量
  if (index < 0 || index >= cacheExamPaperData.value.length) {
    return null;
  }
  // 取到正确的题目
  const question = cacheExamPaperData.value[index];
  // 如果是通过 sharedData 跳题，跳完要清空 sharedData
  if (sharedData.value !== null && sharedData.value !== undefined) {
    currentIndex.value = sharedData.value;
    sharedData.value = null;
  }
  return question;
});

const handleClickUp = () => {
  if (currentIndex.value !== 0) {
    currentIndex.value--;
  }
};

/** 提交 */
const handleClickNext = () => {
  if (currentIndex.value === total.value) {
    // 到达最后一题后可以做一些处理（例如提交答卷）
  } else {
    emit('updateAnswerVal', answersList.value);
    sharedPaperData.value = answersList.value
    if (currentIndex.value < total.value) {
      currentIndex.value++;
    }
  }
};

onMounted(() => {
  if (props.ksId && props.paperViewData) {
    initPaperData();
  }
});

// 封装成方法，watch 和 mounted 都调用
const initPaperData = () => {
  cacheExamPaperData.value = props.paperViewData?.examPaperQuestionsVOList || [];
  if (cacheExamPaperData.value.length > 0) {
    answersList.value = cacheExamPaperData.value.map(cacheExam => {
      let result = cacheExam.sttype === "多选题"
        ? (cacheExam.result ? cacheExam.result.split(',') : [])
        : (cacheExam.result ?? "");
      return {
        contentId: cacheExam.id,
        sttype: cacheExam.sttype,
        point: cacheExam?.score ?? 0,
        answer: cacheExam?.answer ?? "",
        analysis: cacheExam?.analysis ?? "",
        sort: cacheExam?.sort ?? "",
        gid: cacheExam?.gid ?? "",
        showResult: cacheExam.result,
        result: result,
      };
    });
    total.value = cacheExamPaperData.value.length - 1;
    sharedPaperData.value = answersList.value;
    emit('updateAnswerVal', answersList.value);
  }
};

watch(
  () => props.ksId,
  (ksId) => {
    if (ksId && props.paperViewData) {
      initPaperData();
    }
  },
  {immediate: true}
);
</script>
