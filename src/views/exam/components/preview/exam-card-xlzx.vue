<!-- 试卷试题详情页 -->
<template>
  <ele-card :body-style="{ padding: '7px 8px 5px 8px!important', height:computedHeight, overflow:'auto'}">
    <div v-for="(item,index) in cacheExamPaperData" :key="index">
      <question-item :question="item"
                     :enter-type="enterType"
                     :answer="getAnswer(item)"
                     @update-answer="handleAnswerUpdate"/>
    </div>
  </ele-card>
</template>

<script setup>
import {computed, inject, onMounted, ref, watch} from 'vue';
import FileUpload from "@/components/FileUpload/index.vue";
import {isImageFile} from "@/utils/common_bak2.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import QuestionItem from "@/views/exam/components/cardInfo/QuestionItem.vue";

const BASE_URL = import.meta.env.BASE_URL;

const emit = defineEmits(['updateAnswerVal']);
const props = defineProps({
  title: String,
  paperId: String,//试卷ID
  ksms: String,//考试模式
  paperViewData: Object,
  enterType: String,
  currentPageHeight: String,
});

const sharedPaperData = inject('sharedPaperData');

defineOptions({name: 'EXAMCARD'});

/** 请求状态 */
const loading = ref(false);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const computedHeight = computed(() => {
  return (pageHeight.value + (props.enterType?.includes('toAnswer') ? -5 : 50)) + 'px';
});

const cacheExamPaperData = ref([]);
const total = ref(0);
const answersList = ref([])
/** 列表数据 */
const data = ref([])


// 获取答案
const getAnswer = (question) => {
  return answersList.value.find(answer => answer.contentId === question.id);
};

// 处理答案更新
const handleAnswerUpdate = (updatedAnswer) => {
  const index = answersList.value.findIndex(a => a.contentId === updatedAnswer.contentId);
  if (index !== -1) {
    answersList.value[index] = updatedAnswer;
    sharedPaperData.value = answersList.value;
    emit('updateAnswerVal', answersList.value);
  }
};


const readonlyVal = computed(() => {
  return ['perviewPaperXlzx'].includes(props.enterType);
});

/**
 * 试卷预览
 */
watch(
  () => props.paperId,
  (paperId) => {
    if (paperId) {
      if (props.paperViewData) {
        // 处理数据，添加 stype 字段
        cacheExamPaperData.value = props.paperViewData?.surveyPaperQuestionsVOList.map(question => ({
          ...question,
          sttype: question.qtype,
          sort: question.sort - 1,
          examPaperQuestionsOptionsList: question.surveyPquestionsOptionsList
        }));

        if (cacheExamPaperData.value && cacheExamPaperData.value.length > 0) {
          cacheExamPaperData.value.forEach(cacheExam => {
            let obj = {
              contentId: cacheExam.id,
              qtype: cacheExam.qtype,
              sttype: cacheExam.qtype,
              point: cacheExam?.score ?? 0,
              answer: cacheExam?.answer ?? "",
              analysis: cacheExam?.analysis ?? "",
              sort: cacheExam?.sort ?? "",
              gid: cacheExam?.gid ?? "",
              showResult: cacheExam.result,
              manualScoring: "",
            }
            let result = ""
            if (cacheExam.qtype === "多选题") {
              result = cacheExam.result ? cacheExam.result.split(',') : []
            } else if (cacheExam.qtype === "上传题") {
              let uploadFiles = cacheExam.result ? JSON.parse(cacheExam.result) : []
              let oldFiles = [];
              uploadFiles.map((d, i) => {
                let newObj = {
                  key: d.id,
                  name: d.originalFilename,
                  contentType: d.contentType,
                  fileUrl: BASE_URL + 'api/file/inline/' + d.id,
                  status: 'done'
                }
                let mark = isImageFile(newObj)
                newObj.isImageFile = mark;
                if (mark) newObj.url = newObj.fileUrl
                oldFiles.push(newObj);
              })
              result = oldFiles
            } else {
              result = cacheExam?.result ?? "";
            }
            obj.result = result;
            answersList.value.push(obj)
          })
          total.value = cacheExamPaperData.value.length - 1
          sharedPaperData.value = answersList.value
          emit('updateAnswerVal', answersList.value);
        }
      }
    }
  },
  {immediate: true}
)
</script>

<style lang="scss" scoped>
.list-item-body {
  display: flex;
  padding: 5px 15px;
}

.el-radio-group {
  display: unset !important;
}

:deep(.el-radio .el-radio__label,) {
  font-size: 14px !important;
}

:deep(.el-checkbox .el-checkbox__label) {
  font-size: 14px !important;
}

</style>
