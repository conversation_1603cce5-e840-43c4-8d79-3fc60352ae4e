<template>
  <ele-card
    :body-style="{
      padding: '0 8px 10px 8px!important',
      height: computedHeight,
      overflow: 'auto'
    }"
  >
    <template v-for="(group, groupIndex) in questionGroups" :key="groupIndex">
      <div class="question-group">
        <div class="group-header">
          <ele-text size="md">
            {{ group.name }} （共{{ group.qsum || 0 }}题）
          </ele-text>
        </div>
        <template
          v-for="(question, qIndex) in group.surveyPquestionsVOList"
          :key="qIndex"
        >
          <question-item
            :question="question"
            :enter-type="enterType"
            :answer="getAnswer(question)"
            @update-answer="handleAnswerUpdate"
          />
        </template>
      </div>
    </template>
  </ele-card>
</template>

<script setup>
  import { ref, watch, inject, computed } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import QuestionItem from '@/views/exam/components/cardInfo/QuestionItem.vue';
  import Exam<PERSON>erson from './exam-person.vue';
  import { options } from 'less';

  const BASE_URL = import.meta.env.BASE_URL;

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const computedHeight = computed(() => {
    return (
      pageHeight.value +
      (props.enterType?.includes('toAnswer') ? -5 : 50) +
      'px'
    );
  });
  const emit = defineEmits(['updateAnswerVal']);

  const props = defineProps({
    title: String,
    paperId: String,
    ksms: String,
    paperViewData: Object,
    enterType: String,
    currentPageHeight: String
  });

  // 注入的共享数据
  const sharedPaperData = inject('sharedPaperData', ref([]));

  // 响应式数据
  const answersList = ref([]);
  const questionGroups = ref([]);

  /**
   * 初始化问题数据
   * @param {Array} questions - 问题列表
   */
  const initQuestions = (questions) => {
    answersList.value = questions.map((question) => {
      const baseQuestion = {
        sort: question.sort - 1,
        contentId: question.id,
        sttype: question.qtype, // 使用 qtype 作为 sttype
        point: question?.score ?? 0,
        showResult: question.result,
        manualScoring: '',
        examInfoId: '',
        ...question,
        id: ''
      };

      // 根据不同类型处理结果
      if (question.qtype === '多选题') {
        baseQuestion.result = question.result ? question.result.split(',') : [];
      } else if (question.qtype === '上传题') {
        console.log('上传题 :>> ', question, question.result);
        const uploadFiles = question.result ? JSON.parse(question.result) : [];
        baseQuestion.result = uploadFiles.map((file) => ({
          key: file.id,
          name: file.originalFilename,
          contentType: file.contentType,
          fileUrl: `${BASE_URL}api/file/inline/${file.id}`,
          status: 'done',
          isImageFile: isImageFile(file.contentType),
          ...(isImageFile(file.contentType) && {
            url: `${BASE_URL}api/file/inline/${file.id}`
          })
        }));
      } else {
        baseQuestion.result = question?.result ?? '';
      }

      return baseQuestion;
    });

    // 更新共享数据
    sharedPaperData.value = answersList.value;
    emit('updateAnswerVal', answersList.value);
  };

  /**
   * 检查文件是否为图片
   * @param {Object} file - 文件对象
   * @returns {Boolean}
   */
  const isImageFile = (file) => {
    const imageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    return imageTypes.includes(file.contentType);
  };

  // 获取答案
  const getAnswer = (question) => {
    return answersList.value.find((answer) => answer.contentId === question.id);
  };

  // 处理答案更新
  const handleAnswerUpdate = (updatedAnswer) => {
    const index = answersList.value.findIndex(
      (a) => a.contentId === updatedAnswer.contentId
    );
    if (index !== -1) {
      getResultId(updatedAnswer);
      answersList.value[index] = updatedAnswer;
      sharedPaperData.value = answersList.value;
      emit('updateAnswerVal', answersList.value);
    }
  };
  //根据选项获取选项对应的id
  const getResultId = (updatedAnswer) => {
    let optionsList = updatedAnswer.surveyPquestionsOptionsList;
    let result = updatedAnswer.result;
    if (updatedAnswer.sttype === '多选题') {
      let resultId = [];
      optionsList.forEach((option) => {
        if (result.includes(option.code)) {
          resultId.push(option.id);
        }
      });
      updatedAnswer.resultId = resultId;
    } else if (updatedAnswer.sttype === '单选题') {
      let resultId = '';
      let res = optionsList.find((option) => option.code === result);
      if (res) resultId = res.id;
      updatedAnswer.resultId = resultId;
    }
  };

  // 处理被测评人更新
  // const handleUpdatePerson = (updatePerson) => {
  //   console.log('handleUpdatePerson :>> ', updatePerson);
  //   emit('updatePerson', updatePerson);
  // };

  // 监听试卷数据变化
  watch(
    () => props.paperViewData,
    (newData) => {
      if (newData?.questionsGroupList) {
        // 处理数据，添加 stype 字段
        questionGroups.value = newData.questionsGroupList.map((group) => ({
          ...group,
          surveyPquestionsVOList:
            group.surveyPquestionsVOList?.map((question) => ({
              ...question,
              sttype: question.qtype,
              sort: question.sort - 1,
              examPaperQuestionsOptionsList:
                question.surveyPquestionsOptionsList
            })) || []
        }));

        // 如果有 surveyPaperQuestionsVOList 则初始化问题
        if (newData.surveyPaperQuestionsVOList) {
          initQuestions(
            newData.surveyPaperQuestionsVOList.map((q) => ({
              ...q,
              sttype: q.qtype
            }))
          );
        }
      }
    },
    { immediate: true, deep: true }
  );
</script>

<style lang="scss" scoped>
  .question-group {
    margin-bottom: 20px;
  }

  .group-header {
    padding: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-radio-group) {
    display: unset !important;
  }

  :deep(.el-radio .el-radio__label),
  :deep(.el-checkbox .el-checkbox__label) {
    font-size: 14px !important;
  }

  @media (max-width: 768px) {
    .group-header {
      padding: 10px;
    }
  }
</style>
