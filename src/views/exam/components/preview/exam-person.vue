<template>
  <el-form
    ref="formRef"
    size="small"
    :model="form"
    :rules="rules"
    labelWidth="96"
  >
    <el-row :gutter="4">
      <template v-for="item in formItems" :key="item.id">
        <el-col :span="item.colProps">
          <el-form-item
            v-if="item.prop === 'bcpr'"
            :label="item.label"
            :prop="item.prop"
          >
            <dict-data
              :placeholder="'请选择' + item.label"
              code="bcpr"
              :type="item.type"
              :dicQueryParams="{
                dictFieldUrl: '/teacherEvaluate/fdycp-answerer/testeeUserList',
                valueField: 'xgh',
                textField: 'xm',
                params: { surveyId: paperId }
              }"
              :model-value="form[item.prop]"
              @update:modelValue="
                (value) => updateFormValue(item, item.prop, value)
              "
            />
          </el-form-item>
          <ProFormItem
            v-else
            :item="item"
            :model="form"
            @updateItemValue="
              (prop, value) => updateFormValue(item, prop, value)
            "
          >
            <template
              v-for="name in Object.keys($slots).filter(
                (k) =>
                  !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
              )"
              #[name]="slotProps"
            >
              <slot :name="name" v-bind="slotProps || {}"></slot>
            </template>
          </ProFormItem>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>

<script setup>
  import { ref, reactive, defineProps, watch, defineEmits } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data.js';

  const props = defineProps({
    paperId: {
      type: String,
      required: true
    }
  });
  const emit = defineEmits(['update-person']);

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  const formRef = ref(null);
  const formItems = ref([
    {
      prop: 'bcpr',
      label: '被测评人',
      type: 'radioButton'
    }
  ]);
  /** 表单验证规则 */
  const rules = reactive({
    bcpr: [
      {
        required: true,
        message: '请选择被测评人',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    bcpr: ''
  });
  const updateFormValue = (item, prop, newVal) => {
    console.log('item, prop, newVal :>> ', item, prop, newVal);
    form[item.prop] = newVal;
    if (prop === 'bcpr') {
      let fdycpzbValues = dicts.value['bcpr'].find(
        (item) => item.id === newVal
      );
      if (fdycpzbValues) {
        let resultObj = { ...fdycpzbValues };
        delete resultObj.id;
        setFieldValue('item', {
          ...resultObj
        });
      }
      console.log('form :>> ', form);
      emit('update-person', form);
    }
  };
  watch(
    () => props.paperId,
    (newVal) => {
      console.log('22222 :>> ', newVal);
    }
  );
</script>
<style lang="scss" scoped>
  .el-form-item--small .el-form-item__label {
    font-size: 16px;
  }
</style>
