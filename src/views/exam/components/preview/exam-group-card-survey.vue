<!-- 试卷试题组 -->
<template>
  <ele-card :border="true" :body-style="{padding: '5px!important',height:computedHeight, overflow:'auto'}"
            :header-style="{ 'text-align':'center' }">
    <QuestionGroupList :paper-data="paperViewData"
                       :answer-data="sharedPaperData"
                       :exam-answer-info="paperViewData.examAnswerInfo"
                       :mode="enterType"
                       :answer-mode="paperViewData.dtms === '逐题模式' ? 'oneByOne' : 'whole'"
                       @question-click="handleQuestionClick"
                       @update-stats="handleStatsUpdate"/>
  </ele-card>
</template>

<script setup>
import {computed, ref,} from "vue";
import {inject} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import QuestionGroupList from "@/views/exam/components/cardInfo/QuestionGroupList.vue";

const emit = defineEmits(['updateNoAnswerNumber', 'updateUpTimeVal']);
/**
 * 使用 inject 函数接收提供的数据
 * @type {string}
 */
const sharedData = inject('sharedData');
const sharedPaperData = inject('sharedPaperData');

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const computedHeight = computed(() => {
  return (pageHeight.value + (props.enterType?.includes('toAnswer') ? -5 : 50)) + 'px';
});

const props = defineProps({
  title: String,
  enterType: String,
  paperViewData: Object,//试卷数据
});

const paperAnswerData = ref(null);

const handleQuestionClick = (question) => {
  console.log(props.paperViewData)
  if (props.paperViewData.dtms === '逐题模式') {
    // 处理逐题模式点击
    sharedData.value = question.sort;
  }
};

const handleStatsUpdate = ({answered, unanswered}) => {
  // 更新未答题数
  emit('updateNoAnswerNumber', unanswered);
};


</script>
