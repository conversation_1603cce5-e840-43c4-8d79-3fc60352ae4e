<!-- 试卷试题组 -->
<template>
  <ele-card
    :body-style="{
      padding: '7px 8px 5px 8px!important',
      height: computedHeight,
      overflow: 'auto'
    }"
  >
    <!-- enterType==={{enterType}}<br/> -->
    <el-row>
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag
          size="small"
          style="margin: 9px 10px 10px 10px"
          effect="plain"
          type="primary"
        >
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        已答&nbsp;{{ handleCheckedAnswer()[0] }}
      </el-col>
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag
          size="small"
          style="margin: 9px 10px 10px 10px"
          effect="plain"
          type="info"
        >
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        未答&nbsp;{{ handleCheckedAnswer()[1] }}
      </el-col>
    </el-row>
    <el-divider style="margin: 5px 0; opacity: 0.6" />
    <QuestionsGrid
      :questions="paperAnswerData"
      :answer-data="sharedPaperData"
      :mode="enterType"
      :normalizedPaperData="paperAnswerData"
      @question-click="handleQuestionClick"
    />
  </ele-card>
</template>

<script setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import { inject } from 'vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import QuestionsGrid from '@/views/exam/components/cardInfo/QuestionsGrid.vue';

  const emit = defineEmits([
    'updateNoAnswerNumber',
    'updateUpTimeVal',
    'autoSumbit'
  ]);
  /**
   * 使用 inject 函数接收提供的数据
   * @type {string}
   */
  const sharedPaperData = inject('sharedPaperData');

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const props = defineProps({
    title: String,
    enterType: String,
    paperViewData: Object //试卷数据
  });

  const computedHeight = computed(() => {
    return (
      pageHeight.value +
      (props.enterType?.includes('toAnswer') ? -5 : 50) +
      'px'
    );
  });

  const paperAnswerData = ref([]);

  // 处理题目点击
  const handleQuestionClick = (question) => {
    nextTick(() => {
      const element = document.getElementById(question.id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });
  };

  const isStringEmpty = (str) => {
    return typeof str !== 'string' || str.trim() === '';
  };

  const handleCheckedAnswer = () => {
    let answered = 0;
    let noAnswered = paperAnswerData.value.length;

    if (!sharedPaperData.value) {
      return [answered, noAnswered];
    }

    sharedPaperData.value.forEach((chk) => {
      let isAnswered = false;
      switch (chk.sttype) {
        case '多选题':
          isAnswered = Array.isArray(chk.result) && chk.result.length > 0;
          break;
        case '上传题':
          // 上传题的特殊处理 - 检查是否有有效文件
          isAnswered =
            Array.isArray(chk.result) &&
            chk.result.some(
              (file) => file && (file.key || file.name) // 根据实际数据结构调整
            );
          break;
        default:
          // 其他题型（单选题、填空题等）
          isAnswered = !isStringEmpty(chk.result);
      }

      if (isAnswered) {
        answered++;
        noAnswered--;
      }
    });

    emit('updateNoAnswerNumber', noAnswered);
    return [answered, noAnswered];
  };

  watch(
    () => props.enterType,
    (enterType) => {
      nextTick(() => {
        if (enterType && props.paperViewData) {
          paperAnswerData.value = Array.isArray(
            props.paperViewData?.surveyPaperQuestionsVOList
          )
            ? props.paperViewData.surveyPaperQuestionsVOList
            : [];
        }
      });
    },
    { immediate: true }
  );
</script>
