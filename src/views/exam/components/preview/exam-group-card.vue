<template>
  <!-- 试卷试题组 -->
  <ele-card
    :border="true"
    :body-style="{
      padding: '5px!important',
      height: computedHeight,
      overflow: 'auto'
    }"
    :header-style="{ 'text-align': 'center' }"
  >
    <template v-if="baseData?.uptime">
      <goal-card
        v-if="enterType === 'toAnswer'"
        :baseData="baseData"
        @autoSumbit="handleAutoSumbit"
        @updateUpTimeVal="handleUpdateUpTimeVal"
      />
      <el-divider
        v-if="enterType === 'toAnswer'"
        style="margin: 0; opacity: 0.6"
      />

      <template v-if="['perviewAlreadyMarked', 'perview'].includes(enterType)">
        <el-row style="margin-bottom: 5px">
          <el-col
            :md="12"
            :sm="12"
            :xs="24"
            v-for="(item, index) in statsList"
            :key="index"
          >
            <div style="display: flex; padding: 0 10px; min-height: 22px">
              <div style="flex: 1; min-width: 0">
                <ele-text size="lg">{{ item.value }}</ele-text>
                <ele-text size="sm">{{ item.label }}</ele-text>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-divider style="margin: 0; opacity: 0.6" />
      </template>

      <el-divider
        v-if="enterType === 'perview'"
        style="margin: 0; opacity: 0.6"
      />
    </template>

    <!-- enterType=={{ enterType }} -->

    <QuestionGroupList
      :paper-data="paperViewData"
      :answer-data="sharedPaperData"
      :exam-answer-info="paperViewData.examAnswerInfo"
      :mode="enterType"
      :answer-mode="paperViewData.dtms === '逐题模式' ? 'oneByOne' : 'whole'"
      @question-click="handleQuestionClick"
      @update-stats="handleStatsUpdate"
    />
  </ele-card>
</template>

<script setup>
  import { ref, watch, computed, inject } from 'vue';
  import GoalCard from '@/views/exam/components/preview/goal-card.vue';
  import QuestionGroupList from '@/views/exam/components/cardInfo/QuestionGroupList.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const emit = defineEmits([
    'updateNoAnswerNumber',
    'updateUpTimeVal',
    'autoSumbit'
  ]);

  const sharedData = inject('sharedData');
  const sharedPaperData = inject('sharedPaperData');

  const props = defineProps({
    title: String,
    enterType: String,
    baseData: Object
  });

  const paperViewData = ref(null);
  const paperAnswerData = ref(null);

  const computedHeight = computed(() => {
    return (
      pageHeight.value +
      (props.enterType?.includes('toAnswer') ? -5 : 50) +
      'px'
    );
  });

  const statsList = computed(() => [
    { label: '考试成绩', value: paperAnswerData.value?.score },
    { label: '是否合格', value: paperAnswerData.value?.sfhg },
    { label: '考试次数', value: paperAnswerData.value?.kscs },
    { label: '考试用时', value: paperAnswerData.value?.ksys }
  ]);

  watch(
    () => [props.enterType, props.baseData],
    () => {
      paperViewData.value = props.baseData;
      paperAnswerData.value = props.baseData?.examAnswerInfo;
    },
    { immediate: true }
  );

  const handleQuestionClick = (question) => {
    if (paperViewData.value?.dtms === '逐题模式') {
      sharedData.value = question.sort;
    }
  };

  const handleStatsUpdate = ({ answered, unanswered }) => {
    emit('updateNoAnswerNumber', unanswered);
  };

  const handleAutoSumbit = (data) => {
    emit('autoSumbit', data);
  };

  const handleUpdateUpTimeVal = (data) => {
    emit('updateUpTimeVal', data);
  };
</script>
