<template>
  <ProviderComponent>
    <!-- 骨架屏部分 -->
    <el-row v-if="loading" ref="wrapRef" :gutter="8">
      <el-col
        v-for="item in viewConfig.cards"
        :key="item.name"
        :md="item.md"
        :sm="item.sm"
        :xs="item.xs"
      >
        <!-- 骨架屏：可以自定义自己喜欢的骨架样式 -->
        <el-skeleton :rows="6" animated>
          <template #template>
            <el-skeleton-item variant="text" :width="'100%'" />
            <el-skeleton-item variant="text" :width="'80%'" />
            <el-skeleton-item variant="text" :width="'60%'" />
          </template>
        </el-skeleton>
      </el-col>
    </el-row>
    <!-- 数据加载完成后显示的内容 -->
    <ele-split-panel
      v-if="!loading && paperViewData && paperData"
      ref="splitRef"
      space="8px"
      size="320px"
      :min-size="320"
      :max-size="320"
      :allow-collapse="true"
      :resizable="true"
      :responsive="true"
      flex-table
      :custom-style="{ border: 'none', borderWidth: '0', width: '100%' }"
      :body-style="{ height: computedHeight + 'px', overflow: 'auto' }"
      :style="{ height: '100%', overflow: 'visible' }"
    >
      <!-- 左侧题号面板 -->
      <component
        :is="viewComponents[viewConfig.cards[0].name]"
        :ksId="ksId"
        :paperId="paperId"
        :title="viewConfig.cards[0].title"
        :ksms="paperData?.ksms"
        :enterType="enterType"
        :baseData="paperData"
        :paperViewData="paperViewData"
        @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
        @autoSumbit="handleAutoSubmit"
        @updateUpTimeVal="handleUpdateUpTimeVal"
        @updateAnswerVal="handleUpdateAnswerVal"
      />
      <template #body>
        <!-- 右侧题目内容 -->
        <!-- 右侧题目内容=={{ viewConfig.cards[1].name }} -->
        <component
          :is="viewComponents[viewConfig.cards[1].name]"
          :ksId="ksId"
          :paperId="paperId"
          :title="viewConfig.cards[1].title"
          :ksms="paperData?.ksms"
          :enterType="enterType"
          :baseData="paperData"
          :paperViewData="paperViewData"
          @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
          @autoSumbit="handleAutoSubmit"
          @updateUpTimeVal="handleUpdateUpTimeVal"
          @updateAnswerVal="handleUpdateAnswerVal"
        />
      </template>
    </ele-split-panel>
  </ProviderComponent>
</template>

<script setup>
  import { computed, ref, watch } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  // Components
  import ProviderComponent from '@/views/exam/my-exam/answer/components/ProviderComponent.vue';
  import ExamCardInfo from '@/views/exam/components/preview/exam-card-info.vue';
  import ExamCard from '@/views/exam/components/preview/exam-card.vue';
  import GoalCard from '@/views/exam/components/preview/goal-card.vue';
  import ExamGroupCard from '@/views/exam/components/preview/exam-group-card.vue';
  import ExamCardXlzx from '@/views/exam/components/preview/exam-card-xlzx.vue';
  import ExamGroupCardXlzx from '@/views/exam/components/preview/exam-group-card-xlzx.vue';
  import ExamCardSurvey from '@/views/exam/components/preview/exam-card-survey.vue';
  import ExamCardFdycp from '@/views/exam/components/preview/exam-card-fdycp.vue';
  import ExamGroupCardSurvey from '@/views/exam/components/preview/exam-group-card-survey.vue';
  import ExamGroupCardFdycp from '@/views/exam/components/preview/exam-group-card-fdycp.vue';
  import {
    ElSkeleton,
    ElSkeletonItem,
    ElMessage as EleMessage
  } from 'element-plus'; // 骨架屏组件

  // APIs
  import { getExamVO } from '@/views/exam/exam-management/api/index.js';
  import { getExamPaperById } from '@/views/exam/test-paper/api/index.js';
  import {
    getSurveyManageAnswer,
    getSurveyManageById
  } from '@/views/mentalHealth/test-paper/api/index.js';
  import {
    getSurveyById,
    getSurveyVO
  } from '@/views/survey/test-paper/api/index.js';
  import { getSurveyAnswer } from '@/views/mentalHealth/my-exam/api/index.js';
  import { getSurveyAnswerInfo } from '@/views/survey/my-exam/api/index.js';
  import { getFdycpAnswerInfo } from '@/views/survey/teacher-answer/api/index.js';
  import { getFdycpResultSurveyAnswer } from '@/views/survey/teacher-result/api/index.js';

  defineOptions({
    name: 'ComPaperPreview'
  });

  const emit = defineEmits([
    'done',
    'autoSumbit',
    'currentPaperData',
    'updateUpTimeVal',
    'updateAnswerVal',
    'updateNoAnswerNumber',
    'update:modelValue'
  ]);

  const props = defineProps({
    modelValue: { type: Boolean, default: false },
    ksId: { type: String, default: '' },
    paperId: { type: String, default: '' },
    bcpr: { type: String, default: '' }, // 被测评人（辅导员测评使用）
    surveyItemId: { type: String, default: '' }, // 答卷id（辅导员测评使用）
    RandomString: { type: String, default: '' },
    enterType: {
      type: String,
      validator: (value) =>
        [
          'perview',
          'toAnswer',
          'perviewPaper',
          'perviewExamPaper',
          'toGradingPapers',
          'perviewPendingGrading',
          'perviewAlreadyMarked',
          'perviewPaperXlzx',
          'toAnswerXlzx',
          'toPerviewXlzx',
          'perviewAnswerPaperXlzx',
          'perviewPaperServey',
          'perviewPaperFdycp',
          'toAnswerServey',
          'toAnswerFdycp'
        ].includes(value)
    },
    perviewParams: { type: Object, default: () => ({}) }
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const computedHeight = computed(() => {
    return (
      pageHeight.value + (props.enterType?.includes('toAnswer') ? 0 : 56) + 'px'
    );
  });

  // 组件映射
  const viewComponents = {
    'exam-group-card': ExamGroupCard,
    'exam-card-info': ExamCardInfo,
    'goal-card': GoalCard,
    'exam-card': ExamCard,
    'exam-card-xlzx': ExamCardXlzx,
    'exam-group-card-xlzx': ExamGroupCardXlzx,
    'exam-card-survey': ExamCardSurvey,
    'exam-card-fdycp': ExamCardFdycp,
    'exam-group-card-survey': ExamGroupCardSurvey,
    'exam-group-card-fdycp': ExamGroupCardFdycp
  };

  // 响应式数据
  const loading = ref(false);
  const paperData = ref(null);
  const paperViewData = ref(null);
  const wrapRef = ref(null);

  // 计算属性：视图配置
  const viewConfig = computed(() => {
    const configs = {
      // 心理咨询相关配置
      xlzx: {
        cards: [
          {
            name: 'exam-group-card-xlzx',
            title: '答题卡',
            md: 8,
            sm: 24,
            xs: 24
          },
          { name: 'exam-card-xlzx', title: '题', md: 16, sm: 24, xs: 24 }
        ],
        fetchMethod: fetchSurveyManageData
      },
      // 问卷调查相关配置
      survey: {
        cards: [
          {
            name: 'exam-group-card-survey',
            title: '答题卡',
            md: 8,
            sm: 24,
            xs: 24
          },
          { name: 'exam-card-survey', title: '题', md: 16, sm: 24, xs: 24 }
        ],
        fetchMethod: fetchSurveyData
      },
      // 辅导员测评相关配置
      fdycp: {
        cards: [
          {
            name: 'exam-group-card-fdycp',
            title: '答题卡',
            md: 8,
            sm: 24,
            xs: 24
          },
          { name: 'exam-card-fdycp', title: '题', md: 16, sm: 24, xs: 24 }
        ],
        fetchMethod: fetchSurveyData
      },
      // 考试相关配置 - 逐题模式
      examQuestionByQuestion: {
        cards: [
          { name: 'exam-group-card', title: '答题卡', md: 6, sm: 12, xs: 24 },
          { name: 'exam-card-info', title: '题', md: 18, sm: 12, xs: 24 }
        ],
        fetchMethod: fetchExamData
      },
      // 考试相关配置 - 整卷模式
      examFullPaper: {
        cards: [
          { name: 'exam-group-card', title: '答题卡', md: 6, sm: 12, xs: 24 },
          { name: 'exam-card', title: '题', md: 18, sm: 12, xs: 24 }
        ],
        fetchMethod: fetchExamData
      },
      // 默认配置
      default: {
        cards: [
          { name: 'exam-group-card', title: '答题卡', md: 8, sm: 24, xs: 24 },
          { name: 'exam-card', title: '题', md: 16, sm: 24, xs: 24 }
        ],
        fetchMethod: fetchExamPaperData
      }
    };

    // 根据enterType返回对应配置
    if (props.enterType.includes('Xlzx')) return configs.xlzx;
    if (props.enterType.includes('Servey')) return configs.survey;
    if (props.enterType.includes('Fdycp')) return configs.fdycp;
    if (
      props.enterType === 'toGradingPapers' ||
      paperData.value?.dtms === '逐题模式'
    ) {
      return configs.examQuestionByQuestion;
    }
    if (
      props.enterType === 'perviewPaper' ||
      props.enterType === 'perviewExamPaper'
    ) {
      return configs.default;
    }
    return configs.examFullPaper;
  });

  // 方法：更新modelValue
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  const handleUpdateNoAnswerNumber = (val) => {
    emit('updateNoAnswerNumber', val);
  };

  const handleUpdateUpTimeVal = (val) => {
    emit('updateUpTimeVal', val);
  };

  const handleUpdateAnswerVal = (val) => {
    emit('updateAnswerVal', val);
  };

  // 方法：处理自动提交
  const handleAutoSubmit = () => {
    emit('autoSumbit');
  };
  // 数据获取方法
  const fetchExamData = async () => {
    const queryData = [
      'perview',
      'toGradingPapers',
      'perviewPendingGrading',
      'perviewAlreadyMarked'
    ].includes(props.enterType)
      ? props.perviewParams
      : { examId: props.ksId };
    const data = await getExamVO(queryData);
    paperData.value = data;
    paperViewData.value = data?.examPaperVO ?? {};
  };

  const fetchExamPaperData = async () => {
    const data = await getExamPaperById(props.paperId);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  /** 心理咨询-管理端 试卷预览查询试卷试题详情*/
  const fetchSurveyManageData = async () => {
    const data = await getSurveyManageById(props.paperId);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  /** 心理咨询-管理端 试卷预览查询试卷试题详情（带答案）*/
  const fetchSurveyManageAnswerData = async () => {
    const data = await getSurveyManageAnswer(props.paperId);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  const fetchSurveyData = async () => {
    const data = await getSurveyById(props.paperId);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  const fetchSurveyVOData = async () => {
    const data = await getSurveyVO(props.perviewParams);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  /**学生查看自己的答题详情-wjdc*/
  const fetchSurveyAnswerInfo = async () => {
    const data = await getSurveyAnswerInfo({
      sid: props.paperId
    });
    paperData.value = data;
    paperViewData.value = data ?? {};
  };
  /**答卷人查看自己的答题详情-fdycp*/
  const fetchFdycpSurveyAnswerInfo = async () => {
    const data = await getFdycpAnswerInfo({
      bcpr: props.bcpr, // 被测评人
      surveyItemId: props.surveyItemId // 答卷id
    });
    paperData.value = data;
    paperViewData.value = data ?? {};
  };
  /**管理端 辅导员测评结果-查看答题详情-fdycp*/
  const fetchFdycpResultSurveyAnswer = async () => {
    const data = await getFdycpResultSurveyAnswer(props.perviewParams);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };
  /**
   * 答卷人查看自己的答题记录详情-xlzx
   * @returns {Promise<void>}
   */
  const fetchSurveyAnswerData = async () => {
    const data = await getSurveyAnswer(props.perviewParams);
    paperData.value = data;
    paperViewData.value = data ?? {};
  };

  // 统一数据获取入口
  const fetchData = async () => {
    try {
      loading.value = true;
      paperData.value = null;
      paperViewData.value = null;
      console.log(props.enterType);
      if (!props.enterType) return;
      if (
        [
          'perviewExamPaper',
          'perview',
          'toGradingPapers',
          'perviewPendingGrading',
          'perviewAlreadyMarked',
          'toAnswer'
        ].includes(props.enterType)
      ) {
        if (props.ksId) await fetchExamData();
      } else if (props.enterType === 'perviewPaper') {
        // if (props.ksId) await fetchExamData()
        if (props.paperId) await fetchExamPaperData();
      } else if (
        ['toAnswerXlzx', 'toPerviewXlzx', 'perviewPaperXlzx'].includes(
          props.enterType
        )
      ) {
        await fetchSurveyManageData();
      } else if (
        [
          'perviewPaperServey',
          'perviewPaperFdycp',
          'toAnswerServey',
          'toAnswerFdycp'
        ].includes(props.enterType)
      ) {
        await fetchSurveyData();
      } else if (['perviewAlreadyMarkedServey'].includes(props.enterType)) {
        await fetchSurveyVOData();
      } else if (
        ['perviewAlreadyMarkedFdycpResult'].includes(props.enterType)
      ) {
        await fetchFdycpResultSurveyAnswer();
      } else if (['toAnswerdDetailServey'].includes(props.enterType)) {
        await fetchSurveyAnswerInfo();
      } else if (['toAnswerdDetailFdycp'].includes(props.enterType)) {
        await fetchFdycpSurveyAnswerInfo();
      } else if (['perviewAnswerPaperXlzx'].includes(props.enterType)) {
        await fetchSurveyAnswerData();
      }
    } catch (error) {
      EleMessage.error(error.message);
    } finally {
      loading.value = false;
    }
  };

  // 监听RandomString变化触发数据获取
  watch(() => props.RandomString, fetchData, { immediate: true });

  watch(
    paperData,
    (newVal) => {
      if (newVal) {
        emit('currentPaperData', newVal);
      }
    },
    { immediate: true }
  );
</script>
