<!--试卷预览-->
<template>
  <ele-drawer
    size="66%"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{
      padding: '0 8px!important',
      background: '#f0f2f5',
      marginTop: 0,
      height: 'auto'
    }"
    @update:modelValue="updateModelValue"
  >
    <template #header="{ close, titleId, titleClass }">
      <span :id="titleId" :class="titleClass" v-if="paperData">
        <!-- {{ enterType }}&&&& -->
        {{ paperData?.ksmc ?? paperData.name }}
        <span v-if="enterType === 'perviewPaper'"
          >预览 [共{{ paperData?.subjectSum ?? 0 }}题，共{{
            paperData?.score ?? 0
          }}分]</span
        >
        <template v-else>
          <span
            v-if="
              [
                'perviewPaperServey',
                'perviewPaperFdycp',
                'perviewAnswerPaperXlzx',
                'toAnswerXlzx',
                'toPerviewXlzx'
              ].includes(enterType)
            "
          >
            {{ enterType === 'toAnswerXlzx' ? '预览' : '答题详情查看' }}[共{{
              paperData.subjectSum ?? 0
            }}题]</span
          >
          <span v-else
            >[试卷共{{ paperData.examPaperVO?.subjectSum ?? 0 }}题，共{{
              paperData.examPaperVO?.score ?? 0
            }}分]</span
          >
        </template>
      </span>
    </template>
    <ele-page style="padding: 8px 0 0 8px !important">
      <ComPreviewComponent
        :ksId="ksId"
        :paperId="paperId"
        :bcpr="bcpr"
        :surveyItemId="surveyItemId"
        :RandomString="RandomString"
        :enterType="enterType"
        :routeType="routeType"
        :perviewParams="perviewParams"
        @currentPaperData="handleCurrentPaperData"
        @updateAnswerVal="handleUpdateAnswerVal"
      />
    </ele-page>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">
        关闭
      </el-button>
      <el-button
        v-if="enterType === 'toGradingPapers'"
        type="primary"
        size="small"
        plain
        @click="handleSumbitGrading"
      >
        提交阅卷
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import ComPreviewComponent from '@/views/exam/components/comPreviewComponent.vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ref } from 'vue';
  import { setPageExamAnswerScoring } from '@/views/exam/exam-management/api/index-kscj.js';
  import { ElMessage as EleMessage } from 'element-plus';

  defineOptions({
    name: 'PaperPreview'
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    RandomString: String,
    ksId: String,
    bcpr: String,
    surveyItemId: String,
    paperId: String,
    enterType: String, //perview：答题详情预览 toAnswer:去答题 perviewPaper:试卷预览  perviewExamPaper:考试试卷预览
    routeType: String, // wjdc：问卷调查 fdycp：辅导员测评
    perviewParams: Object //预览查询参数
  });

  // /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 加载状态 */
  const loading = ref(false);
  const answerVals = ref([]);
  const handleUpdateAnswerVal = (value) => {
    answerVals.value = [];
    answerVals.value = value;
  };
  const paperData = ref(null);
  const handleCurrentPaperData = (value) => {
    paperData.value = value;
    console.log(value);
  };
  const handleSumbitGrading = () => {
    ElMessageBox.confirm(`你确定提交阅卷信息111？`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const processedAnswers = answerVals.value.map((answer) => {
          if (answer.sttype === '多选题') {
            return {
              ...answer,
              result: answer.result?.length > 0 ? answer.result.join(',') : ''
            };
          }
          return answer;
        });
        const result = {
          ...props.perviewParams,
          answers: processedAnswers
        };
        loading.value = true;
        setPageExamAnswerScoring(result)
          .then((msg) => {
            loading.value = false;
            EleMessage.success(msg);
            updateModelValue(false);
            emit('done');
          })
          .catch((e) => {
            loading.value = false;
          });
      })
      .catch(() => {
        // 用户取消操作
        console.log('用户取消了操作');
      });
  };
</script>
