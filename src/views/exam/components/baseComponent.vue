<!--试卷预览-->
<template>
  <ele-page hide-footer flex-table style="padding: 0 !important">
    <ComPreviewComponent
      :ksId="ksId"
      :paperId="paperId"
      :RandomString="RandomString"
      :enterType="enterType"
      @autoSumbit="handleAutoSubmit"
      @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
      @updateAnswerVal="handleUpdateAnswerVal"
      @updateUpTimeVal="handleUpdateUpTimeVal"
    />
    <ele-bottom-bar>
      <template #extra>
        <el-button type="primary" plain size="small" @click="handleSubmit">
          <ele-dot
            :ripple="true"
            text=" "
            size="16px"
            style="cursor: pointer"
          />
          {{ enterType === 'toGradingPapers' ? '提交阅卷' : '交 卷' }}
        </el-button>
      </template>
    </ele-bottom-bar>
  </ele-page>
</template>

<script setup>
  import { ElMessage as EleMessage } from 'element-plus';
  import { ref } from 'vue';
  import { operation } from '@/views/exam/my-exam/api/index.js';
  import { ElMessageBox } from 'element-plus';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import ComPreviewComponent from '@/views/exam/components/comPreviewComponent.vue';
  import { toFormDataWj } from '@/utils/common_bak2.js';
  import { surveyAnswerOperation } from '@/views/survey/my-exam/api/index-answer.js';
  import { operationFdycpAnswerer } from '@/views/survey/teacher-answer/api/index.js';
  import { xljkSurveyOperation } from '@/views/mentalHealth/my-exam/api/index.js';

  defineOptions({
    name: 'BasePaperPreviewComponent'
  });

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const emit = defineEmits(['doneRefesh']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    RandomString: String,
    ksId: String,
    paperId: String,
    bcpr: String, // 被测评人学工号(辅导员测评使用)
    surveyItemId: String, // 问卷id(辅导员测评使用)
    enterType: String, //perview：答题详情预览 toAnswer:去答题 perviewPaper:试卷预览  perviewExamPaper:考试试卷预览
    perviewParams: Object //预览查询参数
  });

  const paperData = ref(null);
  const paperViewData = ref(null);

  /** 容器 */
  const wrapRef = ref(null);

  const upTimeVal = ref(null);
  const handleUpdateUpTimeVal = (value) => {
    upTimeVal.value = value;
  };
  const answerVals = ref(null);
  const handleUpdateAnswerVal = (value) => {
    answerVals.value = value;
  };

  const upNoAnswerNumber = ref(null);
  const handleUpdateNoAnswerNumber = (value) => {
    upNoAnswerNumber.value = value;
  };
  /** 加载状态 */
  const loading = ref(false);

  // 方法：准备提交数据
  const prepareSubmitData = () => {
    const processedAnswers = answerVals.value.map((answer) => {
      if (answer.sttype === '多选题') {
        return {
          ...answer,
          result: answer.result?.length > 0 ? answer.result.join(',') : '',
          resultId: answer.resultId?.length > 0 ? answer.resultId.join(',') : ''
        };
      } else if (answer.sttype === '上传题') {
        return {
          ...answer,
          result: null
        };
      }
      return answer;
    });

    return {
      examId: props.ksId,
      paperId: props.paperId,
      sid: props.paperId,
      xsid: props.ksId, //心理咨询
      ...(props.enterType === 'toGradingPapers' && {
        examInfoId: props.ksId
      }),
      ...(props.enterType !== 'toGradingPapers' && {
        usedTime: upTimeVal.value
      }),
      bcpr: props.bcpr,
      surveyItemId: props.surveyItemId,
      answers: processedAnswers
    };
  };

  // 方法：提交数据
  const submitData = async (data, type) => {
    console.log('data, type :>> ', data, type);
    try {
      loading.value = true;
      let response;
      // if (type === 'grading') {
      //   response = await setPageExamAnswerScoring(data);
      // } else

      console.log('answerVals.value ===', answerVals.value);
      // let newObj = toFormDataWj({ ...data });
      // console.log('转换后', newObj);
      // for (const [key, value] of newObj) {
      //   console.log(key, value);
      // }
      // return false;
      if (type === 'toAnswerServey') {
        // 问卷调查
        let newObj = toFormDataWj({ ...data });
        response = await surveyAnswerOperation(newObj);
      } else if (type === 'toAnswerFdycp') {
        // 辅导员测评
        let newObj = toFormDataWj({ ...data });
        response = await operationFdycpAnswerer(newObj);
      } else if (type === 'toAnswerXlzx') {
        // 心理咨询
        let newObj = toFormDataWj({ ...data });
        response = await xljkSurveyOperation(newObj);
      } else {
        response = await operation(data);
      }
      if (response)
        EleMessage.success(type === 'grading' ? '阅卷成功' : '交卷成功');
      // 确保触发刷新事件
      emit('doneRefesh', true); // 添加成功标志参数
    } catch (error) {
      EleMessage.error(error.message);
      emit('doneRefesh', false); // 添加失败标志参数
    } finally {
      loading.value = false;
    }
  };

  // 方法：处理自动提交
  const handleAutoSubmit = () => {
    // emit('autoSumbit', value);
    const data = prepareSubmitData();
    submitData(data, 'answer');
  };

  // 方法：处理提交
  const handleSubmit = () => {
    // type = 'toAnswer'
    const messages = {
      toAnswer: {
        confirm:
          upNoAnswerNumber.value > 0
            ? `你确定要交卷！你还有"${upNoAnswerNumber.value}"题没有答?`
            : '你确定要交卷！'
      },
      toAnswerServey: {
        confirm:
          upNoAnswerNumber.value > 0
            ? `你确定要交卷！你还有"${upNoAnswerNumber.value}"题没有答?`
            : '你确定要交卷！'
      },
      toAnswerFdycp: {
        confirm:
          upNoAnswerNumber.value > 0
            ? `你确定要交卷！你还有"${upNoAnswerNumber.value}"题没有答?` +
              props.enterType
            : '你确定要交卷！'
      },
      toAnswerXlzx: {
        confirm:
          upNoAnswerNumber.value > 0
            ? `你还有"${upNoAnswerNumber.value}"题没有答，无法交卷！`
            : '你确定要交卷！',
        confirmButtonText: upNoAnswerNumber.value > 0 ? '知道了' : '确定'
      },
      grading: {
        confirm: '你确定提交阅卷信息？'
      }
    };
    ElMessageBox.confirm(messages[props.enterType].confirm, '系统提示', {
      confirmButtonText: messages[props.enterType]?.confirmButtonText || '确定',
      type: 'warning',
      draggable: true
    })
      .then(() => {
        // 心理咨询的答卷需要全部答题才可以提交
        if (props.enterType === 'toAnswerXlzx' && upNoAnswerNumber.value > 0) {
          return;
        }
        const data = prepareSubmitData();
        submitData(data, props.enterType);
      })
      .catch((e) => {
        console.log(e);
      });
  };
</script>

<style>
  .span-wrap {
    display: inline-block;
    white-space: pre-wrap; /* 保留空白符序列，但是当到达容器的末端时进行换行 */
  }
</style>
