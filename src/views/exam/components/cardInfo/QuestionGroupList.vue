<template>
  <ele-card :border="true"
            :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
            :header-style="{ 'text-align':'center' }">
    <!-- 答题统计区域 -->
    <el-row>
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag size="small" style="margin:9px 10px 10px 10px;" effect="plain" type="primary">
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        已答 {{ answerStats.answered }}
      </el-col>
      <el-col :md="12" :sm="12" :xs="24">
        <el-tag size="small" style="margin:9px 10px 10px 10px;" effect="plain" type="info">
          &nbsp;&nbsp;&nbsp;
        </el-tag>
        未答 {{ answerStats.unanswered }}
      </el-col>
      <template v-if="showCorrectStats && examAnswerInfo">
        <el-col :md="12" :sm="12" :xs="24">
          <el-tag size="small" style="margin:9px 10px 10px 10px;" effect="plain" type="success">
            &nbsp;&nbsp;&nbsp;
          </el-tag>
          正确 {{ examAnswerInfo.ddt || 0 }}
        </el-col>
        <el-col :md="12" :sm="12" :xs="24">
          <el-tag size="small" style="margin:9px 10px 10px 10px;" effect="plain" type="danger">
            &nbsp;&nbsp;&nbsp;
          </el-tag>
          错误 {{ examAnswerInfo.dcs || 0 }}
        </el-col>
      </template>
    </el-row>
    <el-divider style="margin: 5px 0; opacity: 0.6"/>
    <!-- 分组列表 -->
    <div v-for="(group, index) in normalizedGroups" :key="group.id" class="group-container">
      <div class="group-header">
        <ele-text size="md" style="white-space: pre-line;">
          {{ group.name }}
          <span class="group-info">
            (共{{ group.qsum || 0 }}题)
          </span>
        </ele-text>
      </div>
      <!-- 使用独立的 QuestionsGrid 组件 -->
      <QuestionsGrid :questions="group.questions"
                     :answer-data="answerData"
                     :mode="mode"
                     :normalizedPaperData="paperData"
                     @question-click="handleQuestionClick"/>

      <el-divider v-if="index < normalizedGroups.length - 1"
                  style="margin: 5px 0; opacity: 0.6"/>

    </div>
  </ele-card>
</template>

<script setup>
import {computed, nextTick} from "vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import QuestionsGrid from "@/views/exam/components/cardInfo/QuestionsGrid.vue";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const emit = defineEmits(['question-click', 'update-stats']);

const props = defineProps({
  // 试卷数据
  paperData: {
    type: Object,
    required: true
  },
  // 答题数据
  answerData: {
    type: [Array, Object],
    default: () => ({})
  },
  // 考试答案信息
  examAnswerInfo: {
    type: Object,
    default: () => ({})
  },
  // 当前模式
  mode: {
    type: String,
    default: 'toAnswer',
    validator: (value) => ['toAnswer', 'perview', 'perviewAlreadyMarked', 'perviewPaper'].includes(value)
  },
  // 答题模式
  answerMode: {
    type: String,
    default: 'whole',
    validator: (value) => ['whole', 'oneByOne'].includes(value)
  }
});

// 规范化分组数据
const normalizedGroups = computed(() => {
  if (!props.paperData) return [];

  // 支持多种数据结构
  const groups = props.paperData.questionsGroupList ||
    props.paperData.examPaperVO?.questionsGroupList ||
    [];

  return groups.map(group => ({
    ...group,
    // 支持多种问题列表字段名
    questions: group.surveyPquestionsVOList ||
      group.examPaperQuestionsVOList ||
      []
  }));
});


// 获取所有问题
const allQuestions = computed(() => {
  return normalizedGroups.value.flatMap(group => group.questions);
});

// 答题统计
const answerStats = computed(() => {
  let answered = 0;
  let unanswered = allQuestions.value.length;

  allQuestions.value.forEach(question => {
    const answer = getAnswerForQuestion(question.id);
    if (answer) {
      if (question.qtype === "多选题" || question.sttype === "多选题") {
        if (answer.result?.length > 0) {
          answered++;
          unanswered--;
        }
      } else if (question.qtype === "上传题") {
        if (answer.resultFiles?.length > 0) {
          answered++;
          unanswered--;
        }
      } else {
        // 处理字符串答案
        const answerStr = String(answer.result || '').trim();
        if (answerStr !== '') {
          answered++;
          unanswered--;
        }
      }
    }
  });

  emit('update-stats', {answered, unanswered});

  return {answered, unanswered};
});

// 正确/错误统计
const showCorrectStats = computed(() => props.mode === 'perviewAlreadyMarked');

// 获取答题数据
const getAnswerForQuestion = (questionId) => {
  if (Array.isArray(props.answerData)) {
    return props.answerData.find(a => a.contentId === questionId);
  }
  return props.answerData[questionId] || null;
};


// 处理题目点击
const handleQuestionClick = (question) => {
  emit('question-click', question);
  if (props.answerMode === 'whole') {
    nextTick(() => {
      const element = document.getElementById(question.id);
      if (element) {
        element.scrollIntoView({behavior: 'smooth', block: 'start'});
      }
    });
  }
};
</script>

<style scoped>
.group-container {
  padding: 0 0 10px 0;
}

.group-header {
  padding: 5px 3px;
}

.group-info {
  font-size: 0.8em;
  color: #888;
}
</style>
