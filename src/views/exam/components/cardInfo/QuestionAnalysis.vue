<template>
  <ele-alert
    :closable="false"
    :show-icon="question.sttype !== '问答题'"
    :type="isAnswerCorrect ? 'success' : 'error'"
    class="analysis-alert"
  >
    <ele-text size="lg" class="analysis-title">答题解析</ele-text>
    <div class="analysis-content">
      <ele-text size="md">考生得分：{{ isAnswerCorrect ? question.score : 0 }}分</ele-text>
      <ele-text size="md">考生答案：{{ question.result }}</ele-text>
      <ele-text size="md">正确答案：{{ question.answer }}</ele-text>
      <ele-text size="md">答案解析：{{ question.analysis }}</ele-text>
    </div>
  </ele-alert>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  question: Object,
  answer: Object
});

const isAnswerCorrect = computed(() => {
  const { question } = props;
  if (question.sttype === "多选题") {
    const currentSelected = question.result ? question.result.split(',') : [];
    const correctOptions = question.answer ? question.answer.split(',') : [];
    return currentSelected.length > 0 && correctOptions.length > 0 &&
      currentSelected.every(option => correctOptions.includes(option));
  }
  return question.result === question.answer;
});
</script>

<style scoped>
.analysis-alert {
  margin-top: 10px;
}

.analysis-title {
  display: block;
  margin-bottom: 6px;
}

.analysis-content {
  line-height: 1.6;
}

.analysis-content > * {
  display: block;
  margin-bottom: 4px;
}
</style>
