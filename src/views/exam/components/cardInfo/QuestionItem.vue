<template>
  <div :id="question.id" class="question-container">
    <!-- Question Header -->
    <div class="question-header">
      <ele-text size="md">
        {{ question.sort + 1 }}、[{{ question.sttype }}] {{ question.name }}
        {{ question.score ? '(' + question.score + '分)' : '' }}
      </ele-text>
    </div>

    <!-- Question Content -->
    <div class="question-content">
      <!-- Essay Question -->
      <template
        v-if="question.sttype === '问答题' || question.sttype === '填空题'"
      >
        <el-input
          :rows="6"
          type="textarea"
          show-word-limit
          maxlength="255"
          v-model="localAnswer.result"
          :disabled="isReadonly"
          @blur="$emit('update-answer', localAnswer)"
          placeholder="请输入答案"
          class="essay-input"
        />
        <el-row v-if="enterType === 'toGradingPapers'" class="grading-row">
          <ele-text size="lg"
            >人工评分&nbsp;&nbsp;
            <el-input-number
              v-model="localAnswer.manualScoring"
              :min="0"
              :max="localAnswer.point"
              size="small"
              controls-position="right"
              @change="$emit('update-answer', localAnswer)"
            />
          </ele-text>
        </el-row>
      </template>
      <template v-else-if="question.sttype === '上传题'">
        <file-upload
          :limit="8"
          v-model="localAnswer.result"
          :readonly="isReadonly"
          @update:modelValue="updateFileValue($event, localAnswer)"
          list-type="image"
        />
        <!--
                     @update:modelValue="$emit('update-answer', localAnswer)"    -->
      </template>
      <template v-else-if="question.sttype === '评分题'">
        <el-rate
          v-model="localAnswer.result"
          allow-half
          :max="localAnswer.maxScore"
          size="large"
          @change="$emit('update-answer', localAnswer)"
          :disabled="isReadonly"
        />
      </template>
      <!-- True/False Question -->
      <div v-else-if="question.sttype === '判断题'" class="option-container">
        <el-radio
          v-for="option in ['对', '错']"
          :key="option"
          :label="option"
          v-model="localAnswer.result"
          :disabled="isReadonly"
          @change="$emit('update-answer', localAnswer)"
          class="option-item"
        >
          {{ option }}
        </el-radio>
      </div>

      <!-- Single Choice Question -->
      <div v-else-if="question.sttype === '单选题'" class="option-container">
        <el-radio
          v-for="option in question.examPaperQuestionsOptionsList"
          :key="option.code"
          :label="option.code"
          v-model="localAnswer.result"
          :disabled="isReadonly"
          @change="$emit('update-answer', localAnswer)"
          class="option-item"
        >
          <span class="option-code">{{ option.code }}、</span>
          <span class="option-text">{{ option.name }}</span>
        </el-radio>
      </div>
      <!-- Multiple Choice Question -->
      <div v-else-if="question.sttype === '多选题'" class="option-container">
        <el-checkbox-group
          v-model="localAnswer.result"
          :disabled="isReadonly"
          @change="$emit('update-answer', localAnswer)"
          class="option-container"
        >
          <el-checkbox
            v-for="option in question.examPaperQuestionsOptionsList"
            :key="option.code"
            :label="option.code"
            :value="option.code"
            class="option-item"
          >
            <span class="option-code">{{ option.code }}、</span>
            <span class="option-text">{{ option.name }}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <!--    <el-divider />-->
    <el-divider style="margin: 10px 0 10px 0; opacity: 0.6" />

    <!-- Answer Analysis 答题解析 -->
    <question-analysis
      v-if="showAnalysis"
      :question="question"
      :answer="localAnswer"
    />
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';
  import QuestionAnalysis from './QuestionAnalysis.vue';
  import FileUpload from '@/components/FileUpload/index.vue';

  const props = defineProps({
    question: {
      type: Object,
      required: true
    },
    enterType: {
      type: String,
      required: true
    },
    answer: {
      type: Object,
      required: true,
      default: () => ({
        contentId: '',
        sttype: '',
        point: 0,
        answer: '',
        analysis: '',
        sort: '',
        gid: '',
        showResult: '',
        manualScoring: '',
        examInfoId: '',
        result: '' // 这里 result 可以是 String 或 Array
      })
    }
  });

  const emit = defineEmits(['update-answer']);

  const isReadonly = computed(() =>
    [
      'perview',
      'perviewPaperServey',
      'perviewPaperFdycp',
      'toAnswerdDetailServey',
      'toAnswerdDetailFdycp',
      'perviewAlreadyMarkedServey',
      'perviewAlreadyMarkedFdycpResult',
      'perviewPaper',
      'toGradingPapers',
      'perviewExamPaper',
      'perviewPendingGrading'
    ].includes(props.enterType)
  );
  /**
   * 是否显示答题解析
   * perviewAlreadyMarkedServey: 问卷调查、辅导员综合测评不需要显示答题解析
   */
  const showAnalysis = computed(() =>
    ['perview', 'toGradingPapers'].includes(props.enterType)
  );

  const localAnswer = ref({ ...props.answer });

  const updateFileValue = ($event, localAnswer) => {
    // 上传题特殊处理，提交时答题的文件对象数组是resultFiles
    if (localAnswer.sttype === '上传题') {
      localAnswer.resultFiles = localAnswer.result;
    }
    emit('update-answer', localAnswer);
  };

  /**
   * 监听 props.answer，同步更新 localAnswer
   * 父组件更新了 answer，localAnswer 自动同步
   */
  watch(
    () => props.answer,
    (newAnswer) => {
      localAnswer.value = { ...newAnswer };
    },
    { deep: true }
  );
</script>

<style scoped>
  .question-container {
    margin: 0 10px 0;
    //padding-bottom: 10px;
  }

  .question-header {
    margin-bottom: 12px;
  }

  .question-content {
    margin-top: 12px;
  }

  /* Option container styling */
  .option-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .option-item {
    margin: 0 !important;
    padding: 4px 0;
    display: flex;
    align-items: center;
    width: 100%;
  }

  /* Remove default Element Plus margins */
  .option-item :deep(.el-radio__label),
  .option-item :deep(.el-checkbox__label) {
    display: flex;
    align-items: flex-start;
    margin-left: 8px;
  }

  .option-code {
    font-weight: bold;
    margin-right: 4px;
    flex-shrink: 0;
  }

  .option-text {
    white-space: normal;
    word-break: break-word;
    text-align: left;
  }

  .essay-input {
    margin-bottom: 16px;
    width: 100%;
  }

  .grading-row {
    margin: 16px 0;
    display: flex;
    align-items: center;
  }

  @media (max-width: 768px) {
    .question-container {
      margin: 16px 10px 0;
    }

    .option-container {
      gap: 6px;
    }
  }
</style>
