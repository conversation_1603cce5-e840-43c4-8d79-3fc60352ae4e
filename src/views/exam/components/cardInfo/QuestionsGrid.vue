<!--专注于题目标签的渲染和交互-->
<template>
  <div class="questions-grid">
    <el-tag v-for="(question, index) in questions"
            :key="question.id"
            @click="handleClick(question)"
            :type="getTagType(question)"
            effect="plain"
            size="default"
            class="question-tag">
      {{ getDisplayNumber(question, index) }}
    </el-tag>
  </div>
</template>

<script setup>
import {computed} from 'vue';

const props = defineProps({
  questions: {//当前分组的问题列表
    type: Array,
    required: true,
    default: () => []
  },
  answerData: {//答题数据
    type: [Array, Object],
    default: () => ({})
  },
  normalizedPaperData: {//试卷数据
    type: Object,
    required: true
  },
  mode: {//当前模式
    type: String,
    default: 'toAnswer'
  },
});

const emit = defineEmits(['question-click']);

// 检测是否0-based排序
/**
 * 自动检测排序模式
 * 如果有0，则认为0-based默认+1，否则认为1-based直接显示
 * @type {ComputedRef<unknown>}
 */
const isZeroBased = computed(() => {
  if (!props.normalizedPaperData) return [];
  // 支持多种数据结构
  const allQueryionsVoList = props.normalizedPaperData.surveyPaperQuestionsVOList ||
    props.normalizedPaperData.examPaperVO?.examPaperQuestionsVOList ||
    [];

  const allSorts = allQueryionsVoList.map(q => q.sort)
    .filter(sort => typeof sort === 'number');

  return allSorts.length > 0 &&
    allSorts.some(s => s === 0) &&
    Math.min(...allSorts) === 0;
});

// 获取显示的题号
const getDisplayNumber = (question, index) => {
  if (typeof question.sort === 'number') {
    return isZeroBased.value ? question.sort + 1 : question.sort;
  }
  if (typeof question.sortIndex === 'number') {
    return question.sortIndex + 1;
  }
  return index + 1;
};

// 获取标签类型
const getTagType = (question) => {
  if (props.mode === 'perviewAlreadyMarked') {
    return question.resultCorrect ? "success" : "danger";
  }

  const answer = getAnswerForQuestion(question.id);
  if (!answer) return "info";

  if (question.qtype === "多选题" || question.sttype === "多选题") {
    return answer.result?.length > 0 ? "primary" : "info";
  }

  if (question.qtype === "上传题") {
    return answer.resultFiles?.length > 0 ? "primary" : "info";
  }

  return answer.result ? "primary" : "info";
};

// 获取答题数据
const getAnswerForQuestion = (questionId) => {
  if (Array.isArray(props.answerData)) {
    return props.answerData.find(a => a.contentId === questionId);
  }
  return props.answerData[questionId] || null;
};

// 处理题目点击
const handleClick = (question) => {
  emit('question-click', question);
};
</script>

<style scoped>
.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: 8px;
  padding: 5px 0;
  margin-top: 5px;
}

.question-tag {
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
  line-height: 30px;
  padding: 0;
  margin: 0 auto;
}

.question-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .questions-grid {
    grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
  }

  .question-tag {
    width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
  }
}
</style>
