<!-- 搜索表单 -->
<template>
  <!--  <ele-card :body-style="{ padding: '10px !important',marginBottom:'-18px' }">-->
  <el-form size="small" @keyup.enter="search" @submit.prevent=""
  label-position="right" label-width="86px">
    <el-row :gutter="8">
      <el-col :md="8" :sm="12" :xs="24">
        <el-form-item label="试题分类">
          <dict-data code="bankData"
                     :dicQueryParams="{
                        dictFieldUrl:'/exam/exam-questions-class',
                        getValType:'id'}"
                     v-model="form.cid" placeholder="请选择试题分类"/>
        </el-form-item>
      </el-col>
      <el-col :md="10" :sm="12" :xs="24">
        <el-form-item label="试题类型">
          <dict-data code="sttype" type="radioButton"
                     :dicQueryParams="{getValType:'name'}"
                     v-model="form.sttype" placeholder="请选择试题类型"/>
        </el-form-item>
      </el-col>
      <el-col :md="6" :sm="12" :xs="24">
        <el-form-item label-width="16px">
          <el-button type="primary" size="small" plain @click="search">查询</el-button>
          <el-button @click="reset" size="small">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!--  </ele-card>-->
</template>

<script setup>
import {useFormData} from '@/utils/use-form-data.js';
import {PlusOutlined} from "@/components/icons/index.js";
import {ref} from "vue";

const emit = defineEmits(['search', 'handleClick']);

const props = defineProps({
  userType: String,
});

/** 表单数据 */
const [form, resetFields] = useFormData({
  cid: '',
  sttype: '',
});

/** 搜索 */
const search = () => {
  console.log(form)
  emit('search', {...form});
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
/** 打开组group编辑弹窗 */
const handleCilck = (row) => {
  emit('handleClick', row.value);
};

</script>
