<!-- 编辑弹窗 -->
<template>
  <ele-drawer size="45%"
              :title="((isUpdate ? '修改' : '添加')+'['+dictName+']')+'试题'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             label-width="auto"
             label-position="top"
             size="small"
             @submit.prevent="">
      <el-form-item label="试题名称" prop="name">
        <el-input clearable
                  v-model="form.name"
                  placeholder="请输入试题名称"/>
      </el-form-item>
      <el-form-item label="试题类型" prop="sttype">
        <dict-data code="sttype" type="radioButton"
                   :dicQueryParams="{getValType:'name'}"
                   v-model="form.sttype" placeholder="请选择试题类型"/>
      </el-form-item>
      <el-form-item label="" v-if="form.sttype&&['单选题','多选题'].includes(form.sttype)">
        <TablePreview :ref="el => getRiskSpreadRef(el, 0)"
                      :approverId="data?data.id:''"
                      dataName="选项设置"
                      :approverType="'questionSel_'+form.sttype"
                      @onDoneGroup="handleDoneGroup"/>
      </el-form-item>
      <el-form-item label="答案" prop="answer" v-if="form.sttype">
        <el-input v-if="form.sttype==='问答题'" :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.answer"
                  placeholder="请输入答案"/>
        <el-radio-group v-else-if="form.sttype==='判断题'" v-model="form.answer">
          <el-radio-button v-for="item in ['对','错']"
                           :key="item"
                           :value="item"
                           :label="item">
            {{ item }}
          </el-radio-button>
        </el-radio-group>
        <el-radio-group v-else-if="form.sttype==='单选题'" v-model="form.answer">
          <el-radio-button v-for="item in formData"
                           :key="item.code"
                           :value="item.code"
                           :label="item.code">
            {{ item.code }}
          </el-radio-button>
        </el-radio-group>
        <el-checkbox-group v-else-if="form.sttype==='多选题'" v-model="form.answer">
          <el-checkbox v-for="item in formData"
                       border style="margin-bottom: 8px;"
                       :key="item.code"
                       :value="item.code"
                       :label="item.code">
            {{ item.code }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="解析">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.analysis"
                  placeholder="请输入解析"/>
      </el-form-item>
      <!--      <el-form-item label="排序号" prop="sort">-->
      <!--        <el-input-number :min="0"-->
      <!--                         :max="99999"-->
      <!--                         v-model="form.sort"-->
      <!--                         placeholder="请输入排序号"-->
      <!--                         controls-position="right"-->
      <!--                         class="ele-fluid"/>-->
      <!--      </el-form-item>-->
    </el-form>
    <template #footer>
      <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="handleSubmit">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch} from 'vue';
import {ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../../api/index.js';
import TablePreview from "@/components/DrawFlow/drawer/table-preview.vue";
import {CloseCircleOutlined} from "@/components/icons/index.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,

  codeType: String,
  dictName: String,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  sttype: '',
  name: '',
  sort: void 0,
  answer: [],
  analysis: '',
  cid: '',
  examQuestionsOptionsList: null,
});

/** 表单验证规则 */
const rules = reactive({
  sttype: [
    {
      required: true,
      message: '请选择试题类型',
      type: 'string',
      trigger: 'blur'
    },
  ],
  name: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
});


const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};

const formData = ref(null);
const handleDoneGroup = async (data) => {
  formData.value = data;
};


/** 表单验证失败提示信息 */
const validMsg = ref('');
const validMsgCount = ref(0);
/** 存放子组件的数组 */
let resultArr = reactive([])
/** 用来创建 Promise 实例，为多个组件校验使用 */
const checkForm = (formChild) => {
  validMsg.value = null;
  let result = new Promise((resolve, reject) => {
    formChild.formRef?.validate((valid, obj) => {
      if (valid) {
        resolve(true)
      } else {
        const errors = obj ? Object.keys(obj).length : 0;
        validMsgCount.value += errors
        resolve(false)
        reject()
      }
    })
  })
  resultArr.push(result)
}


const handleSubmit = async () => {
  await riskSpreadRefList.value?.forEach((child) => {
    checkForm(child)
  });
  try {
    const results = await Promise.all(resultArr);
    // 检查所有结果是否为true
    const allTrue = results.every(Boolean);
    resultArr = []//每次请求完要清空数组
    if (allTrue) {
      // 执行后续操作
      save()
    } else {
      validMsg.value = `选项设置共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  } catch (error) {
    console.error('有异步操作失败:', error);
  }
}


/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};


/**
 * 添加类型转换方法
 * 自动处理字符串和数组的转换
 * @param sttype
 * @param answer
 * @returns {string[]|*[]|string}
 */
const adaptAnswer = (sttype, answer) => {
  if (!answer) {
    return sttype === '多选题' ? [] : '';
  }
  if (sttype === '多选题') {
    return typeof answer === 'string' ? answer.split(',') : (Array.isArray(answer) ? answer : []);
  }
  return typeof answer === 'string' ? answer : (Array.isArray(answer) ? answer[0] || '' : '');
};

// 修改watch监听
watch(
  () => form.sttype,
  (newType, oldType) => {
    // 当类型变化时重置答案格式
    if (newType !== oldType) {
      form.answer = adaptAnswer(newType, form.answer);
    }
  }
);

// 修改保存方法
const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) return;

    loading.value = true;
    const data = {
      ...form,
      cid: props.codeType,
      examQuestionsOptionsList: formData.value,
      answer: form.sttype === '多选题' ? (Array.isArray(form.answer) ? form.answer.join(',') : '') : form.answer
    };

    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

// 修改初始化逻辑
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields({
          ...props.data,
          answer: adaptAnswer(props.data.sttype, props.data.answer)
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
        // 初始化时根据当前类型设置answer格式
        form.answer = adaptAnswer(form.sttype, form.answer);
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
    }
  }
);
</script>
