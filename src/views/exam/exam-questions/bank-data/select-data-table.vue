<template>
  <!--  <ele-page flex-table :style="{height: pageHeight+'px',overflow:'auto'}">-->
  <!--    -->
  <ele-card flex-table
            :body-style="{ padding: '0 5px 0 5px!important', height:(pageHeight+6)+'px' }">
    <!-- 表格 -->
    <template #header>
      <SelectDataTableSearch @search="reload"/>
    </template>
    <ele-pro-table ref="tableRef"
                   flex-table
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   @update:selectedRowKeys="handleReserveSelectionChange"
                   v-model:current="current"
                   highlight-current-row
                   tooltip-effect="light"
                   cache-key="SelectBankDataTable"
                   :footer-style="{ paddingBottom: '3px' }"
                   :table-style="{  height: (pageHeight+90)+'px', overflow: 'auto' }">
      <template #toolbar>
        <div style="display: flex; align-items: center">
          <ele-text strong>批量设置分数</ele-text>
          <el-input size="small" style="width: 260px; margin-left: 15px;height: 26px;"
                    type="number"
                    v-model="score"
                    :placeholder="'请输入你要批量更新的试题分值'">
            <!--            <template #append>-->
            <!--              <el-button size="small"-->
            <!--                         type="primary"-->
            <!--                         plain-->
            <!--                         class="ele-btn-icon"-->
            <!--                         @click="saveExamPaperQuestionsScores">更新-->
            <!--              </el-button>-->
            <!--            </template>-->
          </el-input>
        </div>
      </template>
    </ele-pro-table>
  </ele-card>
  <!--  </ele-page>-->
</template>

<script setup>
import {ref, watch, computed, onMounted,} from 'vue';
import SelectDataTableSearch from './components/select-data-table-search.vue';
import {queryQuestionsNotInPaper,} from '../api/index.js';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const props = defineProps({
  currentPId: String,
  pageType: String,
});
const emit = defineEmits(['selectDone', 'setGroupScore']);
const userStore = useUserStore();
const {pageHeight, dicts} = storeToRefs(userStore);
console.log(dicts)
/** 表格实例 */
const tableRef = ref(null);
const score = ref(null);

/** 表格列配置 */
const columns = computed(() => {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true,
    },
    {
      prop: 'sttype',
      label: '题型',
      width: 90
    },
    {
      prop: 'cid',
      label: '试题分类',
      formatter: (row) => {
        let dictsData = dicts.value['bankData'];
        let existData = dictsData.filter(obj => obj.dictDataCode === row.cid)
        return existData.length > 0 ? existData[0].dictDataName : '';
      },
    },
    {
      prop: 'name',
      label: '试题名称',
      headerSlot: 'nameHeader',
      slot: 'name'
    },
    {
      prop: 'answer',
      label: '答案',
      width: 90,
    },
    {
      prop: 'analysis',
      label: '答案解析',
    },
  ]
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryQuestionsNotInPaper({
    ...where, ...orders, ...filters,
    page,
    limit,
    pid: props.currentPId
  });
};

/** 刷新表格 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 翻页保留多选改变 */
const handleReserveSelectionChange = (data) => {
  emit('selectDone', data);
};
watch(() => score.value, (val) => {
  if (val) emit('setGroupScore', {score: val});
})

</script>
