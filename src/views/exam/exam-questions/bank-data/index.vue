<template>
  <ele-card :body-style="{padding: '5px!important',height:(pageHeight+6)+'px' }">
    <template #header>
      <ele-text style="padding: 2px;">{{ dictName}}</ele-text>
    </template>
    <ele-pro-table ref="tableRef"
                   flex-table
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :border="true"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   v-model:current="current"
                   highlight-current-row
                   tooltip-effect="light"
                   cache-key="BankDataTable"
                   :footer-style="{ paddingBottom: '3px' }"
                   :table-style="{  height: (pageHeight-90)+'px', overflow: 'auto' }">
      <template #toolbar>
        <el-button size="small"
                   class="ele-btn-icon"
                   @click="openEdit()">
          新建
        </el-button>
        <el-button size="small"
                   class="ele-btn-icon"
                   @click="remove()">
          删除
        </el-button>
        <el-button class="ele-btn-icon" size="small"
                   @click="openImport">
          导入
        </el-button>
      </template>
      <template #name="{ row }">
        <el-link type="primary" underline="never" @click="openEdit(row)">
          {{ row.name }}
        </el-link>
      </template>
      <!-- 表头查询 -->
      <template #nameHeader="{ column }">
        <div style="display: flex; align-items: center">
          <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
            {{ column.label }}
          </div>
          <name-filter @search="onNameFilter"/>
        </div>
      </template>
    </ele-pro-table>
  </ele-card>
  <DataEdit v-model="showEdit"
            :data="current"
            :codeType="codeType"
            :dictName="dictName"
            @done="reload"/>
  <!-- 导入弹窗 -->
  <Import v-model="showImport" @done="reload"
          :codeType="codeType"
          :dictName="dictName+'类型试题'"/>
</template>

<script setup>
import {ref, watch, computed, reactive} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {PlusOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined} from '@/components/icons/index.js';
import DataEdit from './components/edit.vue';
import nameFilter from './components/name-filter.vue';
import {queryPage, removes} from '../api/index.js';
import {usePermission} from '@/utils/use-permission.js';
import {getToken} from "@/utils/token-util.js";
import {getCurrentRole} from "@/utils/current-role-util.js";
import Import from "@/views/exam/exam-questions/bank-data/components/import.vue";
import NameFilter from "@/views/exam/my-exam/components/name-filter.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const {hasPermission,} = usePermission();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const props = defineProps({
  /** 字典id */
  codeType: String,
  dictName: String,
  pageType: String,//selectBank：标识从试卷过来，选择试题
});
const BASE_URL = import.meta.env.BASE_URL;
const accessToken = getToken();
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};

/** 搜索栏实例 */
const searchRef = ref(null);

/** 表格实例 */
const tableRef = ref(null);


/** 是否显示用户导入弹窗 */
const showImport = ref(false);

/** 打开导入弹窗 */
const openImport = () => {
  showImport.value = true;
};

/** 导出excel */
const exportBas = () => {
  window.location.href = BASE_URL + 'api/code/codeCommon/exportData?access_token='
    + accessToken + '&codeType=' + props.codeType
};
/** 表格列配置 */
const columns = computed(() => {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'sttype',
      label: '题型',
      width: 90
    },
    {
      prop: 'name',
      label: '试题名称',
      sortable: 'custom',
      headerSlot: 'nameHeader',
      slot: 'name',
      minWidth: 230
    },
    {
      prop: 'answer',
      label: '答案',
      sortable: 'custom'
    },
    {
      prop: 'analysis',
      label: '答案解析',
    },
    {
      prop: 'createDate',
      label: '创建时间',
    },
  ]
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({
    ...where, ...orders, ...filters,
    page,
    limit,
    cid: props.codeType
  });
};

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 刷新表格 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};


/** 翻页保留多选改变 */
const handleReserveSelectionChange = () => {
  columns.value[0].reserveSelection = reserveSelection.value;
};


// 监听字典id变化
watch(
  () => props.codeType,
  () => {
    searchRef.value?.resetFields?.();
    reload({});
  }
);
</script>
