<template>
  <pro-form :model="form"
            :items="formItems"
            :label-width="auto"
            :footer="true"
            :footer-props="{ labelWidth: '0px' }"
            :footer-style="{ justifyContent: 'center' }"
            :style="{ padding: '28px 26px 16px 26px' }"
            @updateValue="setFieldValue"
            @submit="handleSubmit"
            @reset="handleReset"/>
</template>

<script setup>
import {ref} from 'vue';
import {useFormData} from '@/utils/use-form-data.js';
import ProForm from '@/components/ProForm/index.vue';

/** 表单数据 */
const [form, resetFields, _assignFields, setFieldValue] = useFormData({
  plan: [],
  name: '',
  family: void 0,
  attendance: void 0
});

/** 提交 */
const handleSubmit = (form) => {
  console.log(form);
};

/** 重置 */
const handleReset = () => {
  resetFields();
};

/** 表单项 */
const formItems = ref([
  {
    label: '部门团建活动方案投票',
    prop: 'title',
    type: 'label',
    props: {
      size: 'xl',
      strong: true,
      style: {
        textAlign: 'center',
        marginBottom: '18px'
      }
    },
    key: 'item-0001'
  },
  {
    label: '为了感谢各位员工最近对公司发展做出的辛苦付出，也为了增强部门同事之间的凝聚力，开阔员工的视野，让大家在繁忙的工作中放松心情，以更饱满的精神状态迎接新一个月的工作，本部门计划于 06 月 01 日组织团建活动，现在开始征集部门团建活动方案的投票啦~ 请各位同事积极参与，认真填写。',
    prop: 'description',
    type: 'label',
    props: {
      style: {
        textIndent: '2em',
        lineHeight: '28px',
        marginBottom: '28px'
      }
    },
    key: 'item-0002'
  },
  {
    label: '（01）请选择你最喜欢的活动方案(可多选)：',
    prop: 'plan',
    itemProps: {
      rules: [
        {
          required: true,
          message: '请选择你最喜欢的活动方案'
        }
      ],
      style: {
        marginBottom: '36px'
      }
    },
    type: 'checkbox',
    props: {
      style: {
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        paddingLeft: '22px'
      }
    },
    options: [
      {
        label: '方案一 龙湾度假村一日游',
        value: 1
      },
      {
        label: '方案二 欢乐谷一日游',
        value: 2
      },
      {
        label: '方案三 世界城吃饭 + 密室逃脱',
        value: 3
      },
      {
        label: '方案四 大悦城自助餐 + KTV',
        value: 4
      }
    ],
    key: 'item-0003'
  },
  {
    label: '（02）您的姓名：',
    prop: 'name',
    itemProps: {
      rules: [
        {
          required: true,
          message: '请输入您的姓名'
        }
      ],
      style: {
        marginBottom: '36px'
      }
    },
    type: 'input',
    props: {
      placeholder: '请输入您的姓名',
      style: {
        flex: 1,
        margin: '4px 0 8px 22px',
        maxWidth: '320px'
      }
    },
    key: 'item-0004'
  },
  {
    label: '（03）是否携带家属(选是会继续选择携带数量)：',
    prop: 'family',
    itemProps: {
      rules: [
        {
          required: true,
          message: '请选择是否携带家属'
        }
      ],
      style: {
        marginBottom: '36px'
      }
    },
    type: 'radio',
    props: {
      style: {
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        paddingLeft: '22px'
      }
    },
    options: [
      {
        label: '是',
        value: 1
      },
      {
        label: '否',
        value: 0
      }
    ],
    key: 'item-0005'
  },
  {
    label: '（04）携带家属数量：',
    prop: 'attendance',
    itemProps: {
      rules: [
        {
          required: true,
          message: '请选择携带家属数量'
        }
      ],
      style: {
        marginBottom: '36px'
      }
    },
    type: 'radio',
    props: {
      style: {
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'stretch',
        paddingLeft: '22px'
      }
    },
    options: [
      {
        label: '携带 1 人',
        value: 1
      },
      {
        label: '携带 2 人',
        value: 2
      },
      {
        label: '携带 3 人',
        value: 3
      }
    ],
    vIf: 'form.family == 1',
    key: 'item-0006'
  }
]);
</script>
