<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #action="{ row }">
          <template v-if="row.status === '已发卷'&&row.kscsIsCorrect&&row.timeIsCorrect">
            <el-link type="primary" underline="never" @click="handlePaperPerview(row)">
              {{ row.answered ? '重新考试' : '在线考试' }}
            </el-link>
            <el-divider v-if="row.answered" direction="vertical"/>
          </template>
          <template v-if="row.answered">
            <el-link type="primary" underline="never" @click="handlePaperAnswerList(row)">
              答题记录
            </el-link>
          </template>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter"/>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview v-model="showPaperPerview" :ksId="ksid" @done="reload" enterType="toAnswer"/>
  </ele-page>
</template>

<script setup>
import {onMounted, inject, reactive, ref, unref, watch,} from 'vue';
import NameFilter from './components/name-filter.vue';
import {queryPage,} from './api/index.js';
import {useRoute, useRouter} from "vue-router";
import PaperPerview from "../components/paper-preview.vue"
import {usePageTab} from "@/utils/use-page-tab.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {generateRandomString} from '@/utils/common_bak2.js';


const {removePageTab, getRouteTabKey, setPageTab,} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
let pathArray = path.split("/");
console.log(pathArray, query)

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let ksid = ref(null);

/** 加载状态 */
const loading = ref(false);

/** 试卷答题 */
const handlePaperPerview = (row) => {
  ksid = row.id;
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/exam/my-exam/answer/' + ksid + '/' + row.pid,
    query: {currentKsmcc: row.ksmc}
  })
}

/** 答题记录列表*/
const handlePaperAnswerList = (row) => {
  removePageTab({key: getRouteTabKey()});
  ksid = row.id;
  push({
    path: '/exam/my-exam/details/' + ksid,
    query: {currentKsmcc: row.ksmc}
  })
}

/** 是否显示导入弹窗 */
const showImport = ref(false);
/** 表格列配置 */
const columns = ref([
  {
    prop: 'ksmc',
    label: '试卷名称',
    headerSlot: 'nameHeader',
    minWidth: 110
  },
  {
    prop: 'ksms',
    label: '试卷模式',
  },
  {
    prop: 'kssjfw',
    label: '考试时间',
    minWidth: 220,
    formatter: (row) => {
      return (row.kssj && row.jssj) ? row.kssj + '至' + row.jssj : ''
    },
  },
  {
    prop: 'uptime',
    label: '考试时长(分)',
    slot: 'status'
  },
  {
    prop: 'cs',
    label: '已考次数/考试次数',
    formatter: (row) => {
      return row.ykcs + "/" + row.ckcs
    },

  },
  // {
  //   prop: 'paperBz',
  //   label: '总分',
  //   slot: 'status'
  // },
  {
    prop: 'hgx',
    label: '合格线',
    slot: 'status'
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 150,
    slot: 'action'
  }
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit,})
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

// 子组件
const refreshEvent = inject('refreshEvent', {refreshFlag: ref(false)});

// 监听 refreshFlag
watch(
  () => refreshEvent.refreshFlag.value,
  (val) => {
    if (val) {
      reload();
      refreshEvent.refreshFlag.value = false; // 重置
    }
  }
);

// RandomString
</script>

<script>
export default {
  name: 'MYEXAMINDEX'
};
</script>
