<template>
  <ele-page hide-footer flex-table>
    <BaseComponent :ksId="ksId"
                   :paperId="paperId"
                   :RandomString="RandomString"
                   enterType="toAnswer"
                   @doneRefesh="handleDoneRefesh"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, watch, inject} from 'vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {useRoute, useRouter} from "vue-router";
import BaseComponent from "@/views/exam/components/baseComponent.vue";
import {generateRandomString} from '@/utils/common_bak2.js';
import {usePageTab} from "@/utils/use-page-tab.js";

defineOptions({
  name: 'PaperAnswerPreview',
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

const route = useRoute();
const {currentRoute, push} = useRouter();
const {path, query} = route; // Directly access 'route' without unref

let pathArray = path.split("/");
const ksId = computed(() => pathArray[4]);  // Make ksId reactive
const paperId = computed(() => pathArray[5]);
const RandomString = ref('');

console.log(pathArray, ksId.value, query);

const {triggerRefresh} = inject('refreshEvent');

const handleDoneRefesh = () => {
  triggerRefresh();
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/exam/my-exam',
  });
};

// Watch ksId to handle updates
watch(ksId, (value) => {
  console.log("ksId updated:", value);
  if (value) {
    setPageTabTitle(query.currentKsmcc + '答卷');
    RandomString.value = generateRandomString(10);
  }
});

// Watch query.currentKsmcc to handle updates
watch(
  () => query.currentKsmcc,
  (value) => {
    console.log("currentKsmcc updated:", value);
    if (value) {
      setPageTabTitle(value + ' 答卷');
    }
  },
  {immediate: true}
);
</script>
