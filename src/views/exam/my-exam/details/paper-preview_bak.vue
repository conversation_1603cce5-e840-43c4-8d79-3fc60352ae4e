<!--试卷预览-->
<template>
  <ele-drawer size="75%"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="false"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ padding: '0px 0px 5px 0px !important' ,background: 'rgb(236 236 236)'}"
              @update:modelValue="updateModelValue">
    <!--    :body-style="{padding: '0px!important',background: 'rgb(236 236 236)', height: (pageHeight-20)+'px',overflow:'auto'}"-->
    <template #header="{ close, titleId, titleClass }">
      <span :id="titleId" :class="titleClass" v-if="paperData">
        {{ paperData?.ksmc ?? paperData.name + '预览' }}
       <span v-if="enterType==='perviewPaper'">[试卷共{{ paperData?.subjectSum ?? 0 }}题，共{{
           paperData?.score ?? 0
         }}分]</span>
       <span v-else>[试卷共{{ paperData.examPaperVO?.subjectSum ?? 0 }}题，共{{
           paperData.examPaperVO?.score ?? 0
         }}分]</span>
    </span>
    </template>
    <div v-if="cardData.length>0" :style="{height: (pageHeight-4)+'px', overflow:'auto'}">
      <ProviderComponent>
        <el-row ref="wrapRef" :gutter="8">
          <el-col v-for="item in cardData"
                  :key="item.name"
                  :md="item.md"
                  :sm="item.sm"
                  :xs="item.xs">
            <template v-if="paperViewData">
              <component v-if="item.name==='exam-group-card'"
                         :is="item.name"
                         :ksId="ksId"
                         :paperId="paperId"
                         :title="item.title"
                         :enterType="enterType"
                         :baseData="paperData"
                         @updateNoAnswerNumber="handleUpdateNoAnswerNumber"
                         @autoSumbit="handleUpdateAutoSumbit"
                         @updateUpTimeVal="handleUpdateUpTimeVal"/>
              <component v-else :is="item.name"
                         :ksId="ksId"
                         :paperId="paperId"
                         :title="item.title"
                         :ksms="paperData?.ksms"
                         :enterType="enterType"
                         :baseData="paperViewData"
                         @updateAnswerVal="handleUpdateAnswerVal"/>
            </template>
          </el-col>
        </el-row>
      </ProviderComponent>
    </div>
    <!-- 底部工具栏 -->
    <template #footer>
      <el-button
        v-if="['perview','perviewPaper','perviewExamPaper','perviewPendingGrading','perviewAlreadyMarked'].includes(enterType)"
        size="small"
        @click="updateModelValue(false)">
        关闭
      </el-button>
      <el-button v-else-if="enterType==='toGradingPapers'" type="primary" size="small" plain
                 @click="handleSumbitGrading">
        提交阅卷
      </el-button>
      <el-button v-else type="primary" plain size="small" @click="handleSumbitAnswer">
        <ele-dot :ripple="true" text=" " size="16px" style="cursor: pointer;"/>
        交 卷
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {onMounted, ref, watch} from 'vue';
import ProviderComponent from '../../components/preview/ProviderComponent.vue';
import ExamCardInfo from '../../components/preview/exam-card-info.vue';
import ExamCard from '../../components/preview/exam-card.vue';
import GoalCard from '../../components/preview/goal-card.vue';
import ExamGroupCard from '../../components/preview/exam-group-card.vue';
import {getExamVO} from "@/views/exam/exam-management/api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import {operation} from "@/views/exam/my-exam/api/index.js";
import {ElMessageBox} from "element-plus";
import {setPageExamAnswerScoring} from "@/views/exam/exam-management/api/index-kscj.js";
import {getExamPaperById} from "@/views/exam/test-paper/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

defineOptions({
  name: 'PaperPreview',
  components: {
    ExamGroupCard,
    ExamCardInfo,
    GoalCard,
    ExamCard,
  }
});
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  ksId: String,
  paperId: String,
  enterType: String,//perview：答题详情预览 toAnswer:去答题 perviewPaper:试卷预览  perviewExamPaper:考试试卷预览
  perviewParams: Object,//预览查询参数
});

const paperData = ref(null);
// const paperAnswerData = ref(null);
const paperViewData = ref(null);

/** 容器 */
const wrapRef = ref(null);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const cardData = ref([
  // {"name": "goal-card", "title": "考试信息", "md": 4, "sm": 24, "xs": 24},
]);
const upTimeVal = ref(null);
const handleUpdateUpTimeVal = (value) => {
  upTimeVal.value = value;
};
const upNoAnswerNumber = ref(null);
const handleUpdateNoAnswerNumber = (value) => {
  upNoAnswerNumber.value = value;
};
/** 加载状态 */
const loading = ref(false);

/** 查询试卷试题详情 */
const queryExamVO = () => {
  cardData.value = [];
  // paperAnswerData.value = null;
  paperData.value = null;
  paperViewData.value = null;
  loading.value = true;
  let queryData = {}
  if (['perview', 'toGradingPapers', 'perviewPendingGrading', 'perviewAlreadyMarked'].includes(props.enterType)) {
    queryData = props.perviewParams;
  } else {
    queryData = {examId: props.ksId}
  }
  getExamVO(queryData).then((list) => {
    loading.value = false;
    if (list) {
      if (props.enterType === "toGradingPapers") {
        list.dtms = "整卷模式"
      }
      paperData.value = list;
      // paperAnswerData.value = list?.examAnswerInfo ?? {};
      paperViewData.value = list?.examPaperVO ?? {}
      if (list.dtms === "逐题模式") {
        cardData.value.push(
          {"name": "exam-group-card", "title": "答题卡", "md": 8, "sm": 24, "xs": 24},
          {"name": "exam-card-info", "title": "题", "md": 16, "sm": 24, "xs": 24},
        )
      } else {
        cardData.value.push(
          {"name": "exam-group-card", "title": "答题卡", "md": 8, "sm": 24, "xs": 24},
          {"name": "exam-card", "title": "题", "md": 16, "sm": 24, "xs": 24},
        )
      }
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    loading.value = false;
    // console.log(MomentWrap.commonDiffSeconds(new Date(), paperData.value.jssj))

  });
}

const answerVals = ref([]);
const handleUpdateAnswerVal = (value) => {
  answerVals.value = []
  answerVals.value = value
}

const handleSumbitGrading = () => {
  ElMessageBox.confirm(`你确定提交阅卷信息？`, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    if (answerVals.value.length > 0) {
      answerVals.value.forEach(chk => {
        if (chk.sttype === "多选题") chk.result = chk.result && chk.result.length > 0 ? chk.result.join(',') : ''
      })
    }
    const result = {
      examId: props.ksId,
      paperId: paperData.value.pid,
      examInfoId: paperData.value.examAnswerInfo.id,
      // usedTime: upTimeVal.value,
      answers: answerVals.value,
    };
    console.log(JSON.stringify(result))
    loading.value = true;
    setPageExamAnswerScoring(result).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

const handleSumbitAnswer = () => {
  let confirmMsg = `你确定要交卷！`
  if (upNoAnswerNumber.value > 0) confirmMsg = `你确定要交卷！你还有“${upNoAnswerNumber.value}”题没有答?`
  let totalTime = paperData.value.uptime * 60;// 总时长（秒）
  if (upTimeVal.value < totalTime) {
    confirmMsg = `考试时间还没到，你确定要交卷！`
  }
  ElMessageBox.confirm(confirmMsg, '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    save()
  }).catch(() => {
  });
};

const save = () => {
  if (answerVals.value.length > 0) {
    answerVals.value.forEach(chk => {
      if (chk.sttype === "多选题") chk.result = chk.result && chk.result.length > 0 ? chk.result.join(',') : ''
    })
  }
  const result = {
    examId: props.ksId,
    paperId: paperData.value.pid,
    usedTime: upTimeVal.value,
    answers: answerVals.value,
  };
  console.log(JSON.stringify(result))
  loading.value = true;
  operation(result).then((msg) => {
    loading.value = false;
    EleMessage.success("交卷成功");
    updateModelValue(false);
    emit('done');
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
}

/**
 * 倒计时结束,系统自动交卷
 */
const handleUpdateAutoSumbit = () => {
  save()
};

/** 试卷预览 查询试卷试题详情*/
const handlePaperPerview = () => {
  cardData.value = [];
  paperViewData.value = null;
  loading.value = true;
  getExamPaperById(props.paperId).then((list) => {
    cardData.value.push(
      {"name": "exam-group-card", "title": "答题卡", "md": 8, "sm": 12, "xs": 24},
      {"name": "exam-card", "title": "题", "md": 16, "sm": 12, "xs": 24},
    )
    loading.value = false;
    paperData.value = list;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    // showPaperPerview.value = true;
  });
}

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.ksId) {
        queryExamVO()
      }
      if (props.paperId) {
        handlePaperPerview()
      }
    }
  }
);

</script>

<style>
.span-wrap {
  display: inline-block;
  white-space: pre-wrap; /* 保留空白符序列，但是当到达容器的末端时进行换行 */
}
</style>
