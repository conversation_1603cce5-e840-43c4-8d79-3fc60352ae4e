/**试卷试题*/
import request from '@/utils/request';

/**
 * 固定选题，保存试卷试题和试题选项（权限标识：exam:examPaperQuestions:operation）
 */
export async function saveExamPaperQuestions(data) {
    const res = await request.post('/exam/exam-paper-questions/saveExamPaperQuestions', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 试卷考试：创建试卷，固定试题模式，从题库中选择试题时，增加一个批量给分功能；
 * pid"/"gid"/"score"
 * @param data
 * @returns {Promise<*>}
 */
export async function setExamPaperQuestionsScores(data) {
    const res = await request.post('/exam/exam-paper-questions/setExamPaperQuestionsScores', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 固定选题，试卷维护试题,从题库选题（权限标识：exam:examPaperQuestions:operation）
 */
export async function saveExamPaperQuestionsByQuestions(data) {
    const res = await request.post('/exam/exam-paper-questions/saveExamPaperQuestionsByQuestions', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 随机抽题，试卷维护试题,保存随机抽题规则（权限标识：exam:examPaperQuestions:operation）
 */
export async function saveExamPaperQuestionsRules(data) {
    const res = await request.post('/exam/exam-paper-questions/saveExamPaperQuestionsRules', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询试卷试题（权限标识：exam:examPaperQuestions:list）
 */
export async function getPageExamPaperQuestions(params) {
    const res = await request.get('/exam/exam-paper-questions/page', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 不分页查询试卷试题（权限标识：exam:examPaperQuestions:list）
 */
export async function getExamPaperQuestions(params) {
    const res = await request.get('/exam/exam-paper-questions', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询试卷试题（权限标识：exam:examPaperQuestions:list）
 */
export async function getExamPaperQuestionsById(id) {
    const res = await request.get('/exam/exam-paper-questions/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除试卷试题规则（权限标识：exam:examPaperQuestions:remove）
 */
export async function removesExamPaperQuestionsRules(data) {
    const res = await request.post('/exam/exam-paper-questions/removeExamPaperQuestionsRules', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/exam/exam-paper-questions/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
