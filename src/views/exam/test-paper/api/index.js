import request from '@/utils/request';

/**
 * 查询不分页
 */
export async function getExamPaper(params) {
    const res = await request.get('/exam/exam-paper', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/exam/exam-paper/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询试卷信息
 */
export async function getExamPaperById(id) {
    const res = await request.get('/exam/exam-paper/' + id);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/exam/exam-paper/operation', data);
    if (res.data.code === 0&&res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/exam/exam-paper/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
