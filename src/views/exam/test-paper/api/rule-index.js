/**
 * 试卷试题抽题规则控制器
 */
import request from '@/utils/request.js';

/**
 * 查询不分页
 * 查询全部试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:list）
 */
export async function getExamPaperQuestionRule(params) {
    const res = await request.get('/exam/exam-paper-questions-rule', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:list）
 */
export async function queryPage(params) {
    const res = await request.get('/exam/exam-paper-questions-rule/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 根据id查询
 */
export async function getExamPaperQuestionRuleById(id) {
    const res = await request.post('/exam/exam-paper-questions-rule/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:operation）
 */
export async function operationQuestionsRule(data) {
    const res = await request.post('/exam/exam-paper-questions-rule/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除试卷试题抽题规则（权限标识：exam:examPaperQuestionsRule:remove）
 */
export async function removesQuestionsRule(data) {
    const res = await request.post('/exam/exam-paper-questions-rule/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
