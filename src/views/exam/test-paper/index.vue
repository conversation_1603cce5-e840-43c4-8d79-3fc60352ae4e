<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     highlight-current-row
                     tooltip-effect="light"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="查看试卷基本信息" placement="left" effect="light">
            <el-link type="primary"
                     underline="never"
                     @click="openEdit(row,true)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handlePaperPerview(row)">
            预览
          </el-link>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="openEdit(row)">
            编辑
          </el-link>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="openStep2Edit(row)">
            试卷设计
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter"/>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <PaperPerview v-model="showPaperPerview"
                  :paperId="pid"
                  :RandomString="RandomString"
                  enterType="perviewPaper"
                  @done="reload"/>
    <Step1Edit v-model="showStep1Edit"
               :paperId="pid"
               :readonlyVal="readonlyVal"
               :data="current"
               @done="handleDonePaper"/>
    <Step2Edit v-model="showStep2Edit" :routeType="routeType" :paperId="Step2Pid" :testPaperData="Step2PData"
               :RandomString="RandomString"
               @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import NameFilter from './components/name-filter.vue';
import {queryPage, removes} from './api/index.js';
import {useRouter} from "vue-router";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import {usePageTab} from "@/utils/use-page-tab.js";
import Step1Edit from "@/views/exam/test-paper/details/components/step1-edit.vue";
import Step2Edit from "@/views/exam/test-paper/details/components/step2-edit.vue";
import {generateRandomString} from '@/utils/common_bak2.js';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
let routeType = path.split("/")[3];
let currentProject = null

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
const RandomString = ref(null)

const pid = ref(null)
/** 加载状态 */
const loading = ref(false);
/** 试卷预览 */
const handlePaperPerview = (row) => {
  RandomString.value = generateRandomString(10)
  pid.value = row.id;
  showPaperPerview.value = true;
}

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'name',
    slot: 'name',
    label: '试卷名称',
    headerSlot: 'nameHeader',
  },
  {
    prop: 'sjms',
    label: '试卷模式',
  },
  {
    prop: 'createDate',
    label: '创建时间',
  },
  {
    prop: 'paperBz',
    label: '试卷说明',
    slot: 'status'
  },
  {
    columnKey: 'action',
    label: '操作',
    slot: 'action'
  }
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit, type: routeType})
};

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showStep1Edit = ref(false);
const showStep2Edit = ref(false);

const Step2Pid = ref(null)
const Step2PData = ref(null)
const readonlyVal = ref(false);

/** 打开编辑弹窗 */
const openEdit = (row, readonly) => {
  readonlyVal.value = false;
  if (readonly) readonlyVal.value = readonly;
  pid.value = row?.id;
  current.value = row ?? null;
  showStep1Edit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

/**
 * 试卷设计
 * @param row
 */
const openStep2Edit = (row) => {
  removePageTab({key: getRouteTabKey()});
  push({
    // path: '/onlineExams/exam-management/details/' + row.id,
    // + row.id
    path: '/exam/test-paper/design',
    query: {paperId: row.id, paperName: row.name}
  })

  // RandomString.value = generateRandomString(10)
  // Step2PData.value = row;
  // Step2Pid.value = row.id;
  // showStep2Edit.value = true;
};

const handleDonePaper = (data) => {
  if (data && data.submitType === 'design') {//保存并进入试卷设计
    // RandomString.value = generateRandomString(10)
    // Step2PData.value = data;
    // Step2Pid.value = data.id;
    // showStep2Edit.value = true;
    openStep2Edit(data)
  } else {
    reload()
  }
};

</script>

<script>
export default {
  name: 'TESTPAPERINDEX'
};
</script>
