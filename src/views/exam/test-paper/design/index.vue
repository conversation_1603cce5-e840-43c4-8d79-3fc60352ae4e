<template>
  <ele-page hide-footer flex-table>
    <el-row :gutter="8">
      <el-col :md="5" :sm="12" :xs="24">
        <ele-card header="试卷组管理" :body-style="{ padding: '5px 0!important' }">
          <template #extra>
            <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
              <IconPark name="return" size="18" strokeWidth="3"/>
              <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
                返回
              </ele-text>
            </div>
            <div style="display: flex; align-items: center;margin-right: 5px;" @click="handlePaperPerview()">
              <IconPark name="view-grid-detail" size="18" strokeWidth="3"/>
              <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
                预览
              </ele-text>
            </div>
            <div style="display: flex; align-items: center" @click="openEdit()">
              <IconPark name="add-one" size="18" strokeWidth="3"/>
              <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
                新建组
              </ele-text>
            </div>
          </template>
          <div style="padding: 5px 5px 8px">
            <el-input clearable size="small"
                      :maxlength="20"
                      v-model="keywords"
                      placeholder="输入名称搜索"
                      :prefix-icon="SearchOutlined"/>
          </div>
          <div :style="{height: (pageHeight-40)+'px', overflow:'auto'}">
            <ele-loading :loading="loading">
              <el-tree ref="treeRef"
                       :data="data"
                       highlight-current
                       :indent="20"
                       node-key="id"
                       :props="{ label: 'title' }"
                       :expand-on-click-node="false"
                       :default-expand-all="true"
                       :filter-node-method="filterNode"
                       :style="{
                              '--ele-tree-item-height': '60px',
                              '--ele-tree-expand-padding': 0,
                              '--ele-tree-expand-margin': 0
                            }"
                       @node-click="handleNodeClick">
                <template #default="{ data: d }">
                  <div class="el-tree-node__label">
                    <span class="custom-tree-node" :class="{ selected: selectedKey === d.id }">
                      <!-- 核心修改：使用 flex 布局 -->
                      <span class="text-container">
                        <span class="span-wrap">
                          {{ d.name }}
                        </span>
                        <ele-text type="placeholder" class="text-info">
                          共{{ d?.qsum ?? 0 }}题，共{{ d?.totalScore ?? 0 }}分
                        </ele-text>
                      </span>
                      <!-- 图标容器 -->
                     <span v-if="selectedKey===d.id" class="icon-container">
                         <ele-tooltip style="cursor: pointer;" effect="light" content="编辑">
                            <a @click="openEdit(d)">
                               <IconPark name="edit-two" size="18" strokeWidth="3" color="#d0021b"/>
                            </a>
                        </ele-tooltip>
                         <ele-tooltip style="cursor: pointer;" effect="light" content="删除">
                            <a @click="remove(d)">
                              <IconPark style="margin-left: 10px;" name="delete" size="18" strokeWidth="3"
                                        color="#d0021b"/>
                            </a>
                         </ele-tooltip>
                      </span>
                    </span>
                  </div>
                </template>
              </el-tree>
            </ele-loading>
          </div>
        </ele-card>
      </el-col>
      <!-- 中间编辑区，动态调整宽度 -->
      <el-col :md="19" :sm="12" :xs="24">
        <BankData v-if="paperData?.sjms==='固定选题' && current && current.id"
                  :currentPId="pid"
                  :currentGId="current.id"
                  :dictName="current.name"
                  :RandomString="RandomString"
                  @doneRefreshGroup="handleDoneRefreshGroup"/>
        <TablePreview v-else-if="paperData?.sjms==='随机抽题' && current && current.id"
                      :approverId="current?.id"
                      :currentNode="current"
                      dataName="抽题规则设置"
                      :approverType="'randomTopicSelection_'+current?.id"
                      @doneRefreshGroup="handleDoneRefreshGroup"/>
      </el-col>
    </el-row>
    <Edit v-model="showEdit" :data="editData" :paperId="pid" @done="reload"/>
    <PaperPerview v-model="showPaperPerview" :paperId="pid" enterType="perviewPaper"/>
    <FloatButton :needType="['help']" @floatClickEvent="handleFloatClickEvent"/>
    <help v-model="helpVisible" :paperData="paperData"/>
  </ele-page>
</template>

<script setup>
import {ref, watch, nextTick, onMounted, unref} from 'vue';
import {FormOutlined, SearchOutlined} from "@/components/icons/index.js";
import TablePreview from "@/components/DrawFlow/drawer/table-preview.vue";
import BankData from "../bank-data/index.vue";
import {getExamPaperQgroup, removes} from "../api/qgroup-index.js";
import Edit from "../components/edit.vue";
import {ElLoading, ElMessageBox} from "element-plus";
import {getExamPaperById} from "../api/index.js";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import {generateRandomString} from '@/utils/common_bak2.js';
import FloatButton from "@/components/FloatButton/index.vue";
import Help from "@/views/exam/test-paper/details/components/help.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import IconPark from "@/components/IconPark/index.vue";
import EditInfo from "@/views/evaluate/evaluate-config/components/edit-info.vue";
import {useRoute, useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const emit = defineEmits(['done', 'back']);

// const props = defineProps({
//   /** 弹窗是否打开 */
//   modelValue: Boolean,
//   paperId: String,
//   RandomString: String,
//   testPaperData: Object
// });

const route = useRoute();
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
console.log(query)
let paperName = query?.paperName ?? ''
let pid = query?.paperId ?? '';
const RandomString = ref('')

const showPaperPerview = ref(false);
/** 是否是修改 */
const isUpdate = ref(false);
// let pid = null;
/** 提交状态 */
const loading = ref(false);

let newGroupId = null;
/** 表单实例 */
const formRef = ref(null);
/** 分割面板组件 */
const splitRef = ref(null);
/** 树组件 */
const treeRef = ref(null);

/** 机构搜索关键字 */
const keywords = ref('');

/** 是否显示编辑弹窗 */
const showEdit = ref(false);
const helpVisible = ref(false);

const paperData = ref(null);

/** 编辑回显数据 */
const editData = ref(null);
/** 树形数据 */
const data = ref([]);
/** 选中数据 */
const current = ref(null);

const handleFloatClickEvent = (data) => {
  if (data === 'help') {
    helpVisible.value = true;
  }
};
/** 打开编辑弹窗 */
const openEdit = (row) => {
  pid = row?.pid ?? pid;
  editData.value = row ?? null;
  showEdit.value = true;
};

/** 上一步 */
const back = () => {
  emit('back');
};

/** 删除组 */
const remove = (data) => {
  const rows = [data];
  if (!rows) {
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?该组下面的所有试题系统会一并删除！',
    {
      type: 'warning',
      draggable: true
    }).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes({
      id: rows.map((d) => d.id).join(),
      pid: pid
    }).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload()
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

// const RandomString = ref(null)

/** 刷新组 */
const reload = (data) => {
  newGroupId = data?.id;
  RandomString.value = generateRandomString(10)
  queryExamPaperById()
  queryExamPaperQgroup()
}

/**试卷试题编辑刷新组数据*/
const handleDoneRefreshGroup = (data) => {
  pid = data?.pid;
  newGroupId = data?.gid;
  queryExamPaperById()
  queryExamPaperQgroup();
}

/** 查询 */
const queryExamPaperById = () => {
  if (pid) {
    loading.value = true;
    getExamPaperById(pid).then((list) => {
      loading.value = false;
      if (list) {
        paperData.value = list ?? [];
        title.value = paperData.value.name + " [" + paperData.value.sjms + "]"
          + " (试卷共" + (paperData.value?.subjectSum ?? 0) + "题，共"
          + (paperData.value?.score ?? 0) + "分" + ")"
      }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }
};

/** 查询试卷试题组 */
const queryExamPaperQgroup = () => {
  loading.value = true;
  getExamPaperQgroup({pid: pid}).then((list) => {
    loading.value = false;
    data.value = list ?? [];
    let refreshData = data.value[0];
    if (newGroupId) {//选中左侧编辑的组数据
      refreshData = data.value.find(item => item.id === newGroupId)
    }
    nextTick(() => {
      handleNodeClick(refreshData);
    });
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  });
};

const selectedKey = ref(null);
/** 选择数据 */
const handleNodeClick = (row) => {
  console.log(':row===', row)
  if (row && row.id) {
    current.value = row;
    treeRef["value"]?.setCurrentKey?.(row.id);
    // 监听选中状态
    selectedKey.value = row.id
  } else {
    current.value = null;
  }
};

/** 树过滤方法 */
const filterNode = (value, data) => {
  if (value) {
    return !!(data.name && data.name.includes(value));
  }
  return true;
};

/** 树过滤 */
watch(keywords, (value) => {
  treeRef["value"]?.filter?.(value);
});

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const title = ref(null);

if (pid) {
  RandomString.value = generateRandomString(10)
  setPageTabTitle(paperName + "问卷设计")
  queryExamPaperById()
  /** 获取试卷组数据 */
  queryExamPaperQgroup()
}

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/exam/test-paper',
    // query: {currentNodeId: currentNodeId}
  })
};

/** 试卷预览 */
const handlePaperPerview = () => {
  // pid.value = row.id;
  showPaperPerview.value = true;
}

// watch(
//   () => props.modelValue,
//   (modelValue) => {
//     if (modelValue) {
//       if (props.testPaperData) {
//         paperData.value = props.testPaperData
//         title.value = paperData.value.name + " [" + paperData.value.sjms + "]"
//           + " (试卷共" + (paperData.value?.subjectSum ?? 0) + "题，共"
//           + (paperData.value?.score ?? 0) + "分" + ")"
//       }
//       if (props.RandomString) {
//         pid = props.paperId;
//         /** 获取试卷组数据 */
//         queryExamPaperQgroup()
//       }
//     } else {
//       formRef.value?.clearValidate?.();
//     }
//   }
// );

</script>
