<template>
  <ele-drawer title="使用帮助"
              :append-to-body="true"
              :size="400"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <ele-alert style="margin-bottom:5px;" :closable="false">
      <ele-text size="lg" style="margin-bottom: 6px">试卷试题组及试题维护说明</ele-text>
      <ele-text size="md" style="margin-bottom: 6px">
        1、创建试题组
      </ele-text>
      <ele-text style="margin-bottom: 6px">
        页面左侧点击
        <el-button type="" size="small" plain>
          新建组
        </el-button>
        按钮，创建试题组。<br/>
        试题组标题右侧，编辑按钮
        <el-icon>
          <FormOutlined/>
        </el-icon>
        点击编辑，编辑该试题组信息。<br/>
        试题组标题右侧，删除按钮
        <el-icon>
          <Delete/>
        </el-icon>
        点击删除，删除该试题组信息。<br/>
      </ele-text>
      <template v-if="paperData?.sjms==='固定选题'">
        <ele-text size="md" style="margin-bottom: 6px">2、添加【{{ paperData?.sjms }}】试卷试题</ele-text>
        <ele-text style="margin-bottom: 6px">
          1）、您可以点击
          <el-button type="" size="small" plain>
            新建试题
          </el-button>
          按钮，添加试题信息。<br/>
          2）、您可以点击
          <el-button type="" size="small" plain>
            从题库中选择试题
          </el-button>
          按钮，选择题库已有试题，添加试题信息。<br/>
          3）、你可以点击
          <el-button type="" size="small" plain>
            删除
          </el-button>
          按钮，删除试题信息。<br/>
          4）、右侧试题列表，试题名称标题列，标蓝列您可以点击编辑该试题信息。<br/>
          5）、右侧试题列表，试题分数列，您可以直接编辑该试题分数，系统会自动更新到试题组信息及试卷信息上。<br/>
        </ele-text>
      </template>
      <template v-if="paperData?.sjms==='随机抽题'">
        <ele-text size="md" style="margin-bottom: 6px">2、【{{ paperData?.sjms }}】规则设置</ele-text>
        <ele-text style="margin-bottom: 6px">
          1）、您可以点击
          <el-button type="" size="small" plain>
            新建
          </el-button>
          按钮，添加随机抽题规则。<br/>
          <!--              2）、您可以点击-->
          <!--              <el-button type="" size="small" :icon="PlusOutlined">-->
          <!--                从题库中选择试题-->
          <!--              </el-button>-->
          <!--              按钮，选择题库已有试题，添加试题信息。<br/>-->
        </ele-text>
      </template>
    </ele-alert>
  </ele-drawer>
</template>

<script setup>
import {FormOutlined} from "@/components/icons/index.js";

const emit = defineEmits([ 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  paperData: Object
});

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};
</script>
