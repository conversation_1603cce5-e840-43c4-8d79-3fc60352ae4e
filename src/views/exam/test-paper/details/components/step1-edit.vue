<template>
  <ele-drawer size="50%"
              :title="readonlyVal?'试卷详情':(isUpdate?'编辑试卷':'新建试卷')"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-position="top"
             label-width="auto"
             @submit.prevent="">
      <!-- 优化后的试卷模式选择 -->
      <el-form-item label="试卷模式" prop="sjms">
        <div class="mode-container">
          <ele-check-card v-model="form.sjms"
                          :items="itemsType"
                          @change="changeSjms"
                          :disabled="readonlyVal"
                          class="mode-options">
            <template #item="{ item }">
              <div class="mode-option">
                <div class="mode-content">
                  <ele-text class="mode-title">{{ item.name }}</ele-text>
                  <ele-text class="mode-desc" type="placeholder">
                    {{ item.description }}
                  </ele-text>
                </div>
              </div>
            </template>
          </ele-check-card>
        </div>
      </el-form-item>
<!--      <el-form-item label="试卷模式" prop="sjms">-->
<!--        <ele-check-card v-model="form.sjms" :items="itemsType" @change="changeSjms"-->
<!--                        :item-style="{ padding: '6px', display: 'flex',marginRight:'6px' }"-->
<!--                        :disabled="readonlyVal"-->
<!--                        style="display: flex;">-->
<!--          <template #item="{ item }">-->
<!--            <div style="display: flex;">-->
<!--              <div style="flex: 1; overflow: hidden; padding-left: 2px;width: 240px;">-->
<!--                <ele-text size="md" style="font-weight:600;font-size: 13px!important;">{{ item.name }}</ele-text>-->
<!--                <ele-text size="sm" type="placeholder" style="line-height: 16px!important;">-->
<!--                  {{ item.description }}-->
<!--                </ele-text>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </ele-check-card>-->
<!--      </el-form-item>-->
      <el-form-item label="试卷名称" prop="name">
        <el-input clearable
                  :maxlength="20"
                  :readonly="readonlyVal"
                  v-model="form.name"
                  placeholder="请输入试卷名称"/>
      </el-form-item>
      <el-form-item label="试卷说明">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  :readonly="readonlyVal"
                  v-model="form.paperBz"
                  placeholder="请输入试卷说明"/>
      </el-form-item>
    </el-form>
    <!-- 底部工具栏 -->
    <template #footer>
      <template v-if="readonlyVal">
        <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
      </template>
      <template v-else>
        <el-button size="small" @click="updateModelValue(false)">取消</el-button>
        <el-button size="small" type="primary" @click="submit('design')" plain>保存并进入试卷设计
        </el-button>
        <el-button size="small" type="primary" plain :loading="loading" @click="submit">
          保存
        </el-button>
      </template>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, h, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {getExamPaperById, operation} from "@/views/exam/test-paper/api/index.js";
import { ElMessage as EleMessage} from "element-plus";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  paperId: String,
  readonlyVal: Boolean,//是否只读
  /** 修改回显的数据 */
  data: Object
});

/** 提交状态 */
const loading = ref(false);

/** 是否是修改 */
const isUpdate = ref(false);
/** 表单实例 */
const formRef = ref(null);

// 试卷数据
const paperData = ref([]);

const itemsType = ref([
  {
    description: "通过从题库自助选题生成的试卷，试题内容是相同的",
    name: "固定选题",
    value: "固定选题",
  },
  {
    description: "依据设定的规则，随机自动抽取指定数量的试题生成试卷",
    name: "随机抽题",
    value: "随机抽题",
  }])
;
/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  id: void 0,
  name: '',
  subjectSum: '',
  score: '',
  sjms: '',
  paperBz: '',
});


const changeSjms = (value) => {
  if (value) {
    form.sjms = value;
  }
};

/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  sjms: [
    {
      required: true,
      message: '请选择试卷模式',
      type: 'string',
      trigger: 'blur'
    }
  ]
});

/** 步骤一提交 */
const submit = (type) => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    operation(form).then((data) => {
      loading.value = false;
      if (data) {
        EleMessage.success(isUpdate.value ? "试卷维护成功" : "试卷创建成功");
        updateModelValue(false)
        data.submitType = type
        emit('done', data);
      }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 查询 */
const query = () => {
  if (props.paperId && props.paperId !== 'ad') {
    loading.value = true;
    getExamPaperById(props.paperId).then((list) => {
      loading.value = false;
      paperData.value = list ?? [];
      if (paperData.value) {
        assignFields({
          ...paperData.value,
        });
      }
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  }
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      if (props.data) {
        assignFields({
          ...props.data,
          answer: props.data.sttype === '多选题' ? props.data.answer.split(',') : props.data.answer
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);

query();
</script>
