<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
      </template>
      <template #extra>
        <el-button size="small" type="primary" plain @click="handlePaperPerview">
          预览
        </el-button>
      </template>
      <div style="width:99%;margin: 10px auto; padding: 10px 0 16px 0">
        <ele-steps :active="active"
                   finish-status="success"
                   :items="[
          { title: '第一步', description: '维护试卷基本信息' },
          { title: '第二步', description: '维护试卷试题组及试题' },
        ]"/>
        <step1-edit v-if="active === 0" @done="handleDone" :paperId="pid"/>
        <step2-edit v-if="active === 1" @back="handleBack" :paperId="pid" :RandomString="RandomString"/>
      </div>
    </ele-card>
    <PaperPerview v-model="showPaperPerview"
                  enterType="perviewPaper" :paperId="pid"/>
  </ele-page>
</template>

<script setup>
import {ref, unref,} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useRoute, useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {getExamPaperById} from "@/views/exam/test-paper/api/index.js";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import Step1Edit from './components/step1-edit.vue';
import Step2Edit from './components/step2-edit.vue';
import {generateRandomString} from '@/utils/common_bak2.js';
import IconPark from "@/components/IconPark/index.vue";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

const route = useRoute();
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
let paperName = query?.paperName ?? ''
let pid = route.params?.id ?? '';
let isUpdate = false;

/** 选中步骤 */
const active = ref(0);
let RandomString = ref('')

if (pid && pid !== 'ad') {
  isUpdate = true;
  RandomString.value = generateRandomString(10)
  active.value = 1;
  setPageTabTitle(paperName + "编辑信息")
} else {
  setPageTabTitle("新建试卷信息")
}

/** 加载状态 */
const loading = ref(false);

const showPaperPerview = ref(false);
const paperViewData = ref(null);

/** 试卷预览 查询试卷试题详情*/
const handlePaperPerview = () => {
  loading.value = true;
  getExamPaperById(pid).then((list) => {
    loading.value = false;
    paperViewData.value = list ?? {};
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    showPaperPerview.value = true;
  });
}

/** 表单编辑完成事件 */
const handleDone = (data) => {
  RandomString.value = generateRandomString(10)
  pid = data.id
  active.value = 1;
};

/** 上一步 */
const handleBack = () => {
  active.value = 0;
};

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/exam/test-paper',
    // query: {currentNodeId: currentNodeId}
  })
};

</script>
<script>
export default {
  name: 'TESTPAPERDETAILS'
};
</script>
