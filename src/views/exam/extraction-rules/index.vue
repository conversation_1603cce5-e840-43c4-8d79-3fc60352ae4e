<template>
  <ele-page hide-footer  flex-table>
    <search @search="reload" @handleClick="handleClickEvent"/>
    <ele-card style="padding: 0px 10px;">
    <el-row :gutter="10">
      <el-col v-for="(item,index) in rDataList"
              :key="item.id"
              :lg="6" :md="8" :sm="12" :xs="24">
        <ele-card :body-style="{ padding: '0px!important'}" class="ele-card-cus"
                  shadow="hover">
          <div style="padding: 15px">
            <div style="display: flex; align-items: center">
              <!--              <el-avatar :size="40" :src="item.cover" style="flex-shrink: 0"/>-->
              <ele-text size="md" style="flex: 1;">
                <span>
                    <el-avatar :size="20" shape="square" style="background-color: #1677ff">
                      {{ index + 1 }}
                    </el-avatar>
                </span>
                &nbsp;{{ item.name }}
              </ele-text>
            </div>
            <div style="margin-top: 16px;  align-items: center">
              <!--                <el-avatar :src="item.avatar" :size="24" />-->
              <ele-text type="secondary" style="flex: 1; padding-left: 6px;">
                发布于 {{ item.createTime }}
              </ele-text>
            </div>
          </div>
          <el-divider style="margin: 0; opacity: 0.48"/>
          <div class="list-item-footer">
            <ele-text type="secondary" class="list-item-tool" @click="openEdit(item)">
              编辑
            </ele-text>
            <el-divider direction="vertical" style="margin: 0; opacity: 0.8"/>
            <ele-text type="secondary" class="list-item-tool" @click="remove(item)">
              删除
            </ele-text>
          </div>
        </ele-card>
      </el-col>
    </el-row>
    <el-pagination @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page="currentPage"
                   :page-sizes="[20,40,60, 80, 100, 200]"
                   :page-size="pageSize"
                   :total="total"
                   :small="true"
                   :background="true"
                   layout="total, sizes, prev, pager, next, jumper"
                   :style="{
                    marginTop:'8px',
                    justifyContent: 'center',
                    '--ele-pagination-hover-bg': 'transparent',
                    '--ele-pagination-hover-color': 'var(--el-color-primary)'
                  }">
    </el-pagination>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {computed, onMounted, reactive, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {queryPage, removes} from '@/views/exam/exam-management/api/index.js';

import {useRouter} from "vue-router";
import {useDictData} from "@/utils/use-dict-data.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {ElPagination} from "element-plus";
import {usePageTab} from '@/utils/use-page-tab.js';
import {mapMutations, mapState} from "@/plugins/lib.js";
import search from '../components/search.vue';

let {setSharedWorkFlowData} = mapMutations();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const routeType = path.split("/")[4];

const total = ref(null); // 假设总数据量为100
const pageSize = ref(20); // 每页显示10条数据
const currentPage = ref(1); // 当前页码
const queryParams = ref({});
const rDataList = ref([]);

// 计算属性，根据当前页码和每页显示条数获取当前页的数据
const queryPageList = () => {
  let obj = {page: currentPage.value, limit: pageSize.value, type: routeType}
  let newObj = Object.assign(obj, queryParams.value);
  queryPage(newObj).then((resData) => {
    total.value = resData.count
    let resList = resData.list;
    if (resList.length > 0) {
      rDataList.value = resList;
    } else {
      rDataList.value = [];
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

watch(
    currentPage,
    (newVal) => {
      if (newVal) queryPageList()
    },
    {immediate: true}
)

watch(
    pageSize,
    (newVal) => {
      if (newVal) queryPageList()
    },
    {immediate: true}
)

// 分页大小改变时的回调
const handleSizeChange = (val) => {
  pageSize.value = val;
}

// 当前页改变时的回调
const handleCurrentChange = (val) => {
  currentPage.value = val;
}

/** 搜索 */
const reload = (data) => {
  console.log(data)
  queryParams.value = data
  queryPageList()
};

const handleClickEvent = (type) => {
  console.log(type)
  if (type === 'add') openEdit()
}
/** 打开组group编辑弹窗 */
const openEdit = (row) => {
  let json = {
    workflowId: row?.id,
    workflowName: row?.name + '-',
  }
  setSharedWorkFlowData(json)
  push({
    path: '/dingding-flow/details',
  })
};


/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      delReload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'ZuzhuIndex'
};
</script>
<style lang="scss" scoped>
.ele-card-cus {
  border:1px solid #e8e8e8;
  border-radius:3px !important;
  margin-bottom:10px !important;
}
.list-item-footer {
  display: flex;
  align-items: center;

  .list-item-tool {
    flex: 1;
    display: flex;
    justify-content: center;
    transition: color 0.2s;
    box-sizing: border-box;
    font-size: 12px;
    padding: 6px 0;
    cursor: pointer;

    .list-item-tool-trigger {
      width: 100%;
      padding: 16px 0;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      outline: none;
    }

    &:hover {
      color: var(--el-color-primary);

      .list-item-tool-trigger {
        color: var(--el-color-primary);
      }
    }
  }
}
</style>
