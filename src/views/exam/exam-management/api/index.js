import request from '@/utils/request.js';

/**
 * 查询全部在线考试（权限标识：exam:exam:list）
 */
export async function getExam(params) {
    const res = await request.get('/exam/exam/', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询在线考试（权限标识：exam:exam:list）
 */
export async function queryPage(params) {
    const res = await request.get('/exam/exam/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询在线考试（权限标识：exam:exam:list）
 */
export async function getExamInfo(id) {
    const res = await request.get('/exam/exam/' + id);
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 获取考试试题详情（权限标识：exam:exam:list）
 */
export async function getExamVO(params) {
    const res = await request.get('/exam/exam/getExamVO' ,{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 获取考试详情（权限标识：exam:exam:list）
 * 不分组
 */
export async function getExamPaperQuestionsVOList(params) {
    const res = await request.get('/exam/exam/examPaperQuestionsVOList' ,{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}
/**
 * 检查发卷情况（权限标识：exam:exam:operation）
 */
export async function getExamCheckFjqk(params) {
    const res = await request.get('/exam/exam/checkFjqk' ,{params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 发卷（权限标识：exam:exam:operation）
 */
export async function sendPaper(data) {
    const res = await request.post('/exam/exam/sendPaper', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 结束考试（权限标识：exam:exam:operation）
 */
export async function endExam(data) {
    const res = await request.post('/exam/exam/endExam', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改在线考试（权限标识：exam:exam:operation）
 */
export async function operation(data) {
    const res = await request.post('/exam/exam/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除在线考试（权限标识：exam:exam:remove）
 */
export async function removes(data) {
    const res = await request.post('/exam/exam/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
