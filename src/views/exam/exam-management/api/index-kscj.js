import request from '@/utils/request.js';

/**
 * 分页查询应考/缺考名单（权限标识：exam:exam:list）
 */
export async function getPageExamPersonnelList(params) {
  const res = await request.get('/exam/exam/pageExamPersonnelList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 人工阅卷打分（权限标识：exam:examAnswer:operation）
 */
export async function setPageExamAnswerScoring(params) {
  const res = await request.post('/exam/exam-answer/scoring', params);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 考试成绩排名
 * cjpmfs  成绩排名方式字段 （递增  并列）
 */
export async function getPageExamRankingList(params) {
  const res = await request.get('/exam/exam/pageExamRankingList', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
