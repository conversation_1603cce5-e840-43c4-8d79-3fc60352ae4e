<template>
  <ele-page hide-footer  flex-table>
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit('add')"> 新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handleExamPaperPerview(row)">
            试卷预览
          </el-link>
          <template v-if="row.status === '未发卷' || row.status === '未开始'">
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openEdit('edit',row)">
              编辑
            </el-link>
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="handleSendPaper(row)">
              发卷
            </el-link>
          </template>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="handleToKsCj(row)">
            考试成绩
          </el-link>
        </template>
        <template #ksmc="{ row }">
          <ele-tooltip content="查看考试信息" effect="light" placement="left">
            <el-link type="primary" underline="never" @click="openEdit('preview',row)">
              {{ row.ksmc }}
            </el-link>
          </ele-tooltip>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter"/>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <Edit v-model="showEdit" :enterType="enterType" :data="currentData" @done="reload"/>
    <PaperPerview v-model="showPaperPerview"
                  :ksId="ksid"
                  :RandomString="RandomString"
                  enterType="perviewExamPaper"
                  @done="reload"/>
  </ele-page>
</template>

<script setup>
import {computed, onMounted, reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import NameFilter from './components/name-filter.vue';
import Edit from './components/edit.vue';
import {queryPage, removes, sendPaper} from './api/index.js';
import {useRouter} from "vue-router";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {useDictData} from "@/utils/use-dict-data.js";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import {usePageTab} from "@/utils/use-page-tab.js";
import right from "@icon-park/vue-next/lib/icons/Right.js";
import {generateRandomString} from '@/utils/common_bak2.js';

const {removePageTab, getRouteTabKey, setPageTab,} = usePageTab();

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const userStore = useUserStore();
useDictData(['ksPaperData'], {dictFieldUrl: '/exam/exam-paper',});
const {pageHeight, dicts} = storeToRefs(userStore);

/** 加载状态 */
const loading = ref(false);

let enterType = null
let currentData = null
let routeType = null
routeType = routeType ? routeType : path.split("/")[3];

/** 表格实例 */
const tableRef = ref(null);
const showEdit = ref(false);

/** 新建编辑 */
const openEdit = (type, row) => {
  enterType = type;
  currentData = row ?? null;
  showEdit.value = true;
};

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'ksmc',
    label: '考试名称',
    headerSlot: 'nameHeader',
    slot: 'ksmc',
    minWidth: 110
  },
  {
    prop: 'rgyj',
    label: '人工阅卷',
  },
  {
    prop: 'ksms',
    label: '考试模式',
  },
  {
    prop: 'dtms',
    label: '答题模式',
  },
  {
    prop: 'pname',
    label: '试卷名称',
    formatter: (row) => {
      let dictsData = dicts.value['ksPaperData'];
      let existData = dictsData.filter(obj => obj.dictDataCode === row.pid)
      return existData.length > 0 ? existData[0].dictDataName : '';
    },
  },
  {
    prop: 'ckcs',
    label: '考试次数',
  },
  {
    prop: 'uptime',
    label: '考试时长',
  },
  {
    prop: 'kssjfw',
    label: '考试时间',
    minWidth: 240,
    formatter: (row) => {
      return (row.kssj && row.jssj) ? row.kssj + '至' + row.jssj : ''
    },
  },
  {
    prop: 'sfzdry',
    label: '指定名单',
  },
  {
    prop: 'status',
    label: '考试状态',
  },
  {
    columnKey: 'action',
    label: '操作',
    slot: 'action',
    minWidth: 150,
    fixed: 'right'
  }
]);


const showPaperPerview = ref(false);
const RandomString = ref(null)
let ksid = ref(null);

/** 考试试卷预览 */
const handleExamPaperPerview = (row) => {
  RandomString.value = generateRandomString(10)
  ksid = row.id;
  showPaperPerview.value = true;
}

const handleToKsCj = (row) => {
  removePageTab({key: getRouteTabKey()});
  push({
    // path: '/onlineExams/exam-management/details/' + row.id,
    path: '/exam/exam-management/details/' + row.id,
    query: {currentKsmcc: row.ksmc}
  })
}

/** 发卷 */
const handleSendPaper = (row) => {
  ElMessageBox.confirm(
    '确定要发卷“' + row.ksmc + '”吗? 考试发卷后，您不可以在对该考试进行编辑操作，学生可在考试时间内参加该考试。',
    '温馨提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    sendPaper(row.id).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
}

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      ksmc: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit, type: routeType})
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.ksmc).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

</script>

<script>
export default {
  name: 'EXAMMANAGEMENTDETAILS'
};
</script>
