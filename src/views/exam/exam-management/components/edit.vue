<template>
  <ele-drawer size="50%"
              :title="cardTitle"
              :append-to-body="true"
              style="max-width: 100%;"
              :destroy-on-close="true"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <pro-form ref="formRef"
              size="small"
              :model="form"
              :items="formItems"
              :rules="rules"
              :labelWidth="'auto'"
              :grid="{ span: 24 }"
              @updateValue="setFieldValue">
      <template #ksms="{ item, model, updateValue }">
        <div class="mode-container">
          <ele-check-card v-model="model[item.prop]"
                          :items="ksmsOptions"
                          :disabled="enterType==='preview'"
                          class="mode-options">
            <template #item="{ item }">
              <div class="mode-option">
                <div class="mode-content">
                  <ele-text class="mode-title">{{ item.name }}</ele-text>
                  <ele-text class="mode-desc" type="placeholder">
                    {{ item.description }}
                  </ele-text>
                </div>
              </div>
            </template>
          </ele-check-card>
        </div>
      </template>
      <template #dtms="{ item, model, updateValue }">
        <div class="mode-container">
          <ele-check-card v-model="model[item.prop]"
                          :items="dtmsOptions"
                          :disabled="enterType==='preview'"
                          class="mode-options">
            <template #item="{ item }">
              <div class="mode-option">
                <div class="mode-content">
                  <ele-text class="mode-title">{{ item.name }}</ele-text>
                  <ele-text class="mode-desc" type="placeholder">
                    {{ item.description }}
                  </ele-text>
                </div>
              </div>
            </template>
          </ele-check-card>
        </div>
      </template>
      <template #sfzdry="{ item, model, updateValue }">
        <div v-if="enterType!=='preview'" style="margin-left: 1px;cursor: pointer;">
          <ele-text type="primary" style="float: left;" @click="openPerSelector()">
            <el-icon>
              <CirclePlus/>
            </el-icon>
            选择在线考试名单
          </ele-text>&nbsp;
        </div>
        <el-input :rows="6"
                  type="textarea"
                  readonly
                  v-model="qddxData"
                  placeholder="请选择在线考试名单"/>
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" plain @click="updateModelValue(false)">{{ enterType === 'preview' ? '关闭' : '取消' }}
      </el-button>
      <el-button v-if="enterType !== 'preview'" plain size="small" type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
  <PerSelector v-model="showPerSelector"
               ref="treeTransferRef"
               node-key="id"
               paramMode="base"
               :perSelectedData="perSelectedData"
               @done="onDoneSelector"/>
</template>

<script setup>
import {computed, reactive, ref, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import ProForm from '@/components/ProForm/index.vue';
import {getExamInfo, operation} from "../api/index.js";
import {ElMessage as EleMessage} from "element-plus";
import {comCondition, insertAtIndex, toFormData, transformParams} from '@/utils/common_bak2.js';
import PerSelector from "@/components/PerSelector/index.vue";
import {getCheckinItemById} from "@/views/checkin/item/api/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import $func from "@/plugins/preload.js";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  enterType: String,//perview：考试详情预览 add:添加考试 edit:编辑考试
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  ksms: '',
  ksmc: '',
  pid: '',
  pname: '',
  kssjfw: [],
  kssj: '',
  jssj: '',
  uptime: null,
  // ksfl: '',
  ckcs: null,
  sfzdry: '',
  hgx: null,
  sfxsda: '',
  rgyj: '',
  dtms: '',
  cjpmfs: '',
  bz: '',
  selectorDatas: [],
});

const ksmsOptions = ref([{
  value: '考试模式',
  name: '考试模式',
  description: '限定答题次数，且记录成绩，请认真答题',
  col: {md: 12, sm: 12, xs: 24, style: {marginBottom: '5px'}}
}, {
  value: '学习模式',
  name: '学习模式',
  description: '不记录成绩，可以多次答题，当作练习',
  col: {md: 12, sm: 12, xs: 24, style: {marginBottom: '5px'}}
},])

const dtmsOptions = ref([{
  value: '整卷模式',
  name: '整卷模式',
  description: '在一页上显示所有试题，通过滑动滚动条浏览',
  col: {md: 12, sm: 12, xs: 24, style: {marginBottom: '5px'}}
}, {
  value: '逐题模式',
  name: '逐题模式',
  description: '一页只显示一个试题，点击上一题/下一题按钮来切换试题',
  col: {md: 12, sm: 12, xs: 24, style: {marginBottom: '5px'}}
},])

/** 表单实例 */
const formRef = ref(null);

/** 提交状态 */
const loading = ref(false);

const showPerSelector = ref(false);
const perSelectedData = ref([]);

let cardTitle = computed(() => {
  let title = null;
  if (props.enterType === "preview") {
    title = "预览考试信息"
  } else if (props.enterType === "add") {
    title = "新建考试信息"
  } else if (props.enterType === "edit") {
    title = "修改考试信息"
  }
  return title
})

/** 表单验证规则 */
const rules = reactive({
  ksms: [
    {
      required: true,
      message: '请选择考试模式',
      trigger: 'change'
    }
  ],
  pid: [
    {
      required: true,
      message: '请选择试卷',
      trigger: 'change'
    }
  ],
  dtms: [
    {
      required: true,
      message: '请选择答题模式',
      trigger: 'change'
    }
  ],
  ksmc: [
    {
      required: true,
      message: '请输入考试名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  kssjfw: [
    {
      required: true,
      message: '请输入考试时间',
      type: 'array',
      trigger: 'blur'
    }
  ],
  uptime: [
    {
      required: true,
      message: '请输入考试时长',
      type: 'number',
      trigger: 'blur'
    }
  ],
  ckcs: [
    {
      required: true,
      message: '请输入考试次数',
      type: 'number',
      trigger: 'blur'
    }
  ],
  sfzdry: [
    {
      required: true,
      message: '选择在线考试名单',
      type: 'string',
      trigger: 'blur'
    }
  ],
  cjpmfs: [
    {
      required: true,
      message: '选择排名方式',
      type: 'string',
      trigger: 'blur'
    }
  ],
})

/** 提交 */
const onSubmit = () => {
  if (loading.value) return; // ⭐ 防止快速连点
  loading.value = true;       // ⭐ 第一时间就设置 loading
  formRef.value?.validate?.((valid, obj) => {
    if (!valid) {
      loading.value = false; // 验证不通过也要取消 loading
      return;
    }
    let data = {...form, kssj: form.kssjfw[0], jssj: form.kssjfw[1]};
    operation(data).then((msg) => {
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      EleMessage.error(e.message);
    }).finally(() => {
      loading.value = false; // 不管成功失败都关掉 loading
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

/** 表单项 */
const formItems = computed(() => {
  let baseFileds = [
    {prop: 'ksmc', label: '考试名称', type: 'input', required: true},
    // {prop: 'ksfl', label: '考试分类', type: 'ksfl', required: true,},
    {prop: 'ksms', label: '考试模式', type: 'ksms', required: true},
    {prop: 'dtms', label: '答题模式', type: 'dtms', required: true},
    {
      prop: 'pid', label: '选择试卷', required: true,
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'ksPaperData',
        dicQueryParams: {
          dictFieldUrl: '/exam/exam-paper',
        }
      }, colProps: {span: 12}
    },
    {prop: 'kssjfw', label: '考试时间', type: 'datetimerange', required: true, colProps: {span: 12}},
    {prop: 'uptime', label: '考试时长', type: 'inputNumber', required: true, colProps: {span: 12}},
    {prop: 'ckcs', label: '考试次数', type: 'inputNumber', required: true, colProps: {span: 12}},
    {prop: 'sfzdry', label: '指定名单', type: 'sfzdry', required: true},
    {
      prop: 'cjpmfs', label: '排名方式',
      type: 'radioButton',
      options: [{label: '并列', value: '并列'},
        {label: '递增', value: '递增'},
      ], required: true, colProps: {span: 8}
    },
    {prop: 'hgx', label: '合格线', type: 'inputNumber', colProps: {span: 8}},
    {prop: 'sfxsda', label: '显示答案', type: 'switch', colProps: {span: 4}},
    {prop: 'rgyj', label: '人工阅卷', type: 'switch', colProps: {span: 4}},
    {prop: 'bz', label: '考试说明', type: 'textarea'},
  ]
  baseFileds.forEach(filed => {
    if (props.enterType === 'preview') {
      if (filed.type === "dictSelect") {
        filed.props.disabled = props.enterType === 'preview';
        filed.props.readonly = props.enterType === 'preview';
      } else if (filed.type === "switch" || filed.type === "radioButton") {
        filed['props'] = {
          "readonly": props.enterType === 'preview',
          "disabled": props.enterType === 'preview',
        }
      } else {
        filed['props'] = {
          "readonly": props.enterType === 'preview',
          // "disabled": props.enterType === 'preview',
        }
      }
    }
  })
  return baseFileds;
});

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);

/** 查询字段更新值 */
// const updateValuefield = (item, e) => {
//   form[item.prop] = e;
//   /** 加载数据URL 更新ksPaperData*/
//   let dicUrlData = dicts.value['ksPaperData'];
//   if (dicUrlData) {
//     dicUrlData.forEach((item) => {
//       if (item.dictDataCode === e) {
//         form.pid = item.dictDataCode;
//         form.pname = item.dictDataName;
//       }
//     });
//   }
// }

const openPerSelector = () => {
  showPerSelector.value = true;
};

const onDoneSelector = (data) => {
  perSelectedData.value = data;
  form.sfzdry = '是'
  form.selectorDatas = data
};

const qddxData = computed(() => {
  let allDataCode = []
  if (perSelectedData.value.length > 0) {
    perSelectedData.value.forEach(e => {
      allDataCode.push(e?.dictDataName ?? e?.name ?? '')
    })
  }
  return allDataCode.join(',');
});

const queryExamInfo = () => {
  getExamInfo(props.data.id).then((resData) => {
    if (resData) {
      if (resData.kssj && resData.jssj) {
        resData.kssjfw = [resData.kssj, resData.jssj]
      }
      if (resData.selectorDatas && resData.selectorDatas.length > 0) {
        resData.sfzdry = '是'
        perSelectedData.value = resData.selectorDatas
      }
      assignFields({
        ...resData,
        // ksPaper: resData.pid,
      });
      isUpdate.value = true;
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}
watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      perSelectedData.value = [];
      if (props.data) {
        queryExamInfo()
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  },
);
</script>
