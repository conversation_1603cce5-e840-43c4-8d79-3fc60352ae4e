<template>
  <ele-page hide-footer  flex-table>
    <!-- 表格 -->
    <ele-pro-table ref="tableRef"
                   row-key="xgh"
                   :columns="columns"
                   :datasource="datasource"
                   :toolbar="false"
                   :border="true"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   tooltip-effect="light"
                   highlight-current-row
                   :footer-style="{ paddingBottom: '3px' }"
                   style="padding-bottom: 0">
      <template #action="{ row }">
        <!--          <el-link type="primary" underline="never" @click="handleExamPaperPerview(row)">-->
        <!--            预览-->
        <!--          </el-link>-->
        <!--          <el-divider direction="vertical"/>-->
        <template v-if="row.fjqk === '未发卷'">
          <el-link type="primary" underline="never" @click="handleSendPaper(row)">
            发卷
          </el-link>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="openEdit(row)">
            编辑
          </el-link>
        </template>
        <!--          <el-divider direction="vertical"/>-->
        <!--          <el-link type="primary" underline="never" @click="remove(row)">-->
        <!--            删除-->
        <!--          </el-link>-->
      </template>
      <template #ksmc="{ row }">
        <el-link type="primary" underline="never" @click="handleExamPaperPerview(row)">
          {{ row.ksmc }}
        </el-link>
      </template>
      <!-- 用户名表头 -->
      <template #nameHeader="{ column }">
        <div style="display: flex; align-items: center">
          <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
            {{ column.label }}
          </div>
          <name-filter @search="onNameFilter"/>
        </div>
      </template>
    </ele-pro-table>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {useRouter} from "vue-router";
import {getPageExamPersonnelList} from "../../api/index-kscj.js"
import NameFilter from "@/views/exam/exam-management/components/name-filter.vue";

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const props = defineProps({
  ksId: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
});

let mdlx = null;//名单类型

/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'xgh',
    label: '学号',
  },
  {
    prop: 'xm',
    label: '姓名',
    minWidth: 110
  },
  {
    prop: 'pyccmc',
    label: '培养层次',
  },
  {
    prop: 'xqmc',
    label: '校区',
  },
  {
    prop: 'xymc',
    label: '院系',
  },
  {
    prop: 'zymc',
    label: '专业',
  },
  {
    prop: 'njmc',
    label: '年级',
  },
  {
    prop: 'bjmc',
    label: '班级',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getPageExamPersonnelList({
    ...where, ...orders, ...filters, page, limit,
    id: props.ksId, mdlx: mdlx
  })
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.xmmc).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

watch(() => props.enterType, (val) => {
  if (val) {
    mdlx = props.tabs.filter(tab => tab.name === val)[0].enterType
    reload()
  }
}, {immediate: true})

</script>

<script>
export default {
  name: 'KAOSHIMINGDAN'
};
</script>
