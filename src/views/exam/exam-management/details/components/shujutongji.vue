<template>
  <ele-page hide-footer  flex-table>
    <TongjiCard :ksId="ksId"/>
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <!--    <ele-card flex-table-->
    <!--              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden',marginTop:'6px' }">-->
    <!-- 表格 -->
    <ele-pro-table ref="tableRef"
                   flex-table
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :toolbar="false"
                   :border="true"
                   tooltip-effect="light"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   highlight-current-row
                   :footer-style="{ paddingBottom: '3px' }"
                   style="padding-bottom: 0">
      <template #action="{ row }">
        <el-link type="primary" underline="never" @click="handleGradingPapers(row)">
          阅卷
        </el-link>
      </template>
    </ele-pro-table>
    <!--    </ele-card>-->
    <PaperPerview v-model="showPaperPerview" :perviewParams="perviewParams" :ksId="ksid" @done="reload"
                  enterType="toGradingPapers"/>
  </ele-page>
</template>

<script setup>
import {computed, onMounted, reactive, ref, unref, watch,} from 'vue';
import {getExamStatisticsGroup, queryExamAnswerInfoPage} from "@/views/exam/my-exam/api/index-answer.js";
import {useRouter} from "vue-router";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import TongjiCard from "@/views/exam/exam-management/details/components/tongji-card.vue";

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const props = defineProps({
  ksId: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
});


let status = null;//状态

/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let ksid = ref(null);
let perviewParams = ref({});

/** 阅卷 */
const handleGradingPapers = (row) => {
  ksid = row.examId;
  perviewParams.value = {
    id: row.id,
    examId: row.examId
  }
  showPaperPerview.value = true;
}

const columns = ref([])

/** 表格列配置 */
const baseComColumns = ref([
  {
    prop: 'xymc',
    label: '学院',
  },
  {
    prop: 'shouldAttendCount',
    label: '应考人数',
  },
  {
    prop: 'actualAttendCount',
    label: '实际参考人数',
  },
  {
    prop: 'absentCount',
    label: '缺考人数',
  },
  {
    prop: 'passCount',
    label: '及格人数',
  },
  {
    prop: 'failCount',
    label: '不及格人数',
  },
  {
    prop: 'passRate',
    label: '及格率',
  },
  {
    prop: 'highestScore',
    label: '最高分',
  },
  {
    prop: 'lowestScore',
    label: '最低分',
  },
  {
    prop: 'averageScore',
    label: '平均分',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getExamStatisticsGroup({
    ...where, ...orders, ...filters, page, limit,
    examId: props.ksId,
    status: status,
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

watch(() => props.enterType, (val) => {
  if (val) {
    columns.value = baseComColumns.value
    reload()
  }
}, {immediate: true})

</script>

<script>
export default {
  name: 'SHJUTONGJI'
};
</script>
