<template>
  <ele-page hide-footer  flex-table>
    <!-- 表格 -->
    <ele-pro-table ref="tableRef"
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :toolbar="false"
                   :border="true"
                   tooltip-effect="light"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   highlight-current-row
                   :footer-style="{ paddingBottom: '5px'}"
                   style="padding-bottom: 0">
      <template #xm="{ row }">
        <ele-tooltip v-if="enterType==='kaoshengchengji'" content="查看学生考试详情" placement="left" effect="light">
          <el-link type="primary"
                   underline="never"
                   @click="handleGradingPapers(row,'perviewAlreadyMarked')">
            {{ row.userInfo.xm }}
          </el-link>
        </ele-tooltip>
        <ele-tooltip v-else-if="enterType==='rengongyuejuan'" content="点我去阅卷" placement="left" effect="light">
          <el-link type="primary"
                   underline="never"
                   @click="handleGradingPapers(row,'toGradingPapers')">
            {{ row.userInfo.xm }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
    <PaperPerview v-model="showPaperPerview"
                  :perviewParams="perviewParams"
                  :RandomString="RandomString"
                  :ksId="ksid"
                  :enterType="PaperPerviewType"
                  @done="reload"/>
  </ele-page>
</template>

<script setup>
import { reactive, ref, unref, watch,} from 'vue';
import {queryExamAnswerInfoPage} from "@/views/exam/my-exam/api/index-answer.js";
import {useRouter} from "vue-router";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import {generateRandomString, } from '@/utils/common_bak2.js';

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const props = defineProps({
  ksId: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
});

let status = null;//状态

/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let ksid = ref(null);
let perviewParams = ref({});

const PaperPerviewType = ref(null);
const RandomString = ref(null)
/** 阅卷 */
const handleGradingPapers = (row, type) => {
  RandomString.value = generateRandomString(10)
  PaperPerviewType.value = type;
  ksid = row.examId;
  perviewParams.value = {
    id: row.id,
    examId: row.examId,
    paperId: row.paperId,
    examInfoId: row.id,
  }
  showPaperPerview.value = true;
}

const columns = ref([])

/** 表格列配置 */
const baseComColumns = ref([
  {
    prop: 'userInfo.xgh',
    label: '学号',
  },
  {
    prop: 'userInfo.xm',
    label: '姓名',
    minWidth: 110,
    slot: 'xm',
  },
  {
    prop: 'userInfo.xymc',
    label: '院系',
  },
  {
    prop: 'userInfo.zymc',
    label: '专业',
  },
  {
    prop: 'userInfo.njmc',
    label: '年级',
  },
  {
    prop: 'userInfo.bjmc',
    label: '班级',
  },
  {
    prop: 'createDate',
    label: '答题时间',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryExamAnswerInfoPage({
    ...where, ...orders, ...filters, page, limit,
    examId: props.ksId,
    status: status,
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

watch(() => props.enterType, (val) => {
  if (val) {
    columns.value = []
    if (val === "rengongyuejuan") {
      columns.value = [...baseComColumns.value,]
    } else {
      columns.value = [...baseComColumns.value, ...[{
        prop: 'score',
        label: '考试成绩',
      }, {
        prop: 'ksys',
        label: '考试用时',
      }]]
    }
    let newVal = props.tabs.filter(tab => tab.name === val)[0].enterType
    status = newVal === "成绩" ? "" : newVal
    reload()
  }
}, {immediate: true})

</script>

<script>
export default {
  name: 'CHENGJI'
};
</script>
