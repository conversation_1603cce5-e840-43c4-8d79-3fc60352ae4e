<template>
    <ele-page hide-footer  flex-table>
  <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
  <!--  <ele-card flex-table-->
  <!--            :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">-->
  <!-- 表格 -->
  <ele-pro-table ref="tableRef"
                 row-key="xgh"
                 :columns="columns"
                 :datasource="datasource"
                 :border="true"
                 :toolbar="false"
                 :show-overflow-tooltip="true"
                 v-model:selections="selections"
                 tooltip-effect="light"
                 highlight-current-row
                 :footer-style="{ paddingBottom: '3px' }"
                 style="padding-bottom: 0">
  </ele-pro-table>
  <!--  </ele-card>-->
    </ele-page>
</template>

<script setup>
import {ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {useRouter} from "vue-router";
import {getPageExamRankingList} from "../../api/index-kscj.js"

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

const props = defineProps({
  ksId: String,
  enterType: String,//tab类型
  tabs: Array,//tabs
});

let mdlx = null;//名单类型

/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    prop: 'userInfo.xgh',
    label: '学号',
  },
  {
    prop: 'userInfo.xm',
    label: '姓名',
    minWidth: 110
  },
  {
    prop: 'rank',
    label: '排名',
  },
  {
    prop: 'userInfo.xqmc',
    label: '校区',
  },
  {
    prop: 'userInfo.xymc',
    label: '院系',
  },
  {
    prop: 'userInfo.zymc',
    label: '专业',
  },
  {
    prop: 'userInfo.njmc',
    label: '年级',
  },
  {
    prop: 'userInfo.bjmc',
    label: '班级',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getPageExamRankingList({
    ...where, ...orders, ...filters, page, limit,
    examId: props.ksId
  })
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.xmmc).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

watch(() => props.enterType, (val) => {
  if (val) {
    mdlx = props.tabs.filter(tab => tab.name === val)[0].enterType
    reload()
  }
}, {immediate: true})

</script>

<script>
export default {
  name: 'CHENGJIPAIMING'
};
</script>
