<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table :body-style="{marginTop:'-15px'}">
      <template #header>
        <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
          <IconPark name="return" size="18" strokeWidth="3"/>
          <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
            返回
          </ele-text>
        </div>
      </template>
      <template #extra>
        <ele-tabs type="tag"
                  size="small"
                  v-model="activeName"
                  :items="tabsItems">
          <template #label="{ item, label}">
            <span>{{ label }}</span>
          </template>
        </ele-tabs>
      </template>
      <Chengji v-if="['rengongyuejuan','kaoshengchengji'].includes(activeName)" :ksId="ksId"
               :enterType="activeName" :tabs="tabsItems"/>
      <Kaoshimingdan v-else-if="['quekaomingdan','yingkaomingdan'].includes(activeName)" :ksId="ksId"
                     :enterType="activeName" :tabs="tabsItems"/>
      <Shujutongji v-else-if="['shujutongji'].includes(activeName)" :ksId="ksId"
                   :enterType="activeName" :tabs="tabsItems"/>
      <Chengjipaiming v-else :ksId="ksId"
                      :enterType="activeName" :tabs="tabsItems"/>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {onMounted, ref, unref,} from 'vue';
import {useRouter} from "vue-router";
import Chengji from "@/views/exam/exam-management/details/components/chengji.vue";
import Chengjipaiming from "@/views/exam/exam-management/details/components/chengjipaiming.vue";
import Kaoshimingdan from "@/views/exam/exam-management/details/components/kaoshimingdan.vue";
import Shujutongji from "@/views/exam/exam-management/details/components/shujutongji.vue";
import {usePageTab} from "@/utils/use-page-tab.js";
import IconPark from "@/components/IconPark/index.vue";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();

const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
// currentKsmcc
let ksId = path.split("/")[4];
if (query.currentKsmcc) setPageTabTitle(query.currentKsmcc + '考试成绩')
/** 加载状态 */
const loading = ref(false);

let currentData = null

/** 表格实例 */
const tableRef = ref(null);

/** 标签页选中 */
const activeName = ref('kaoshengchengji');
const tabsItems = ref([{
  label: '考生成绩',
  name: 'kaoshengchengji',
  enterType: '成绩',
}, {
  label: '人工阅卷',
  name: 'rengongyuejuan',
  enterType: '待阅卷',
}, {
  label: '缺考名单',
  name: 'quekaomingdan',
  enterType: '缺考',
}, {
  label: '应考名单',
  name: 'yingkaomingdan',
  enterType: '应考',
}, {
  label: '考试排名',
  name: 'chengjipaiming',
}, {
  label: '数据统计',
  name: 'shujutongji',
},])

/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/exam/exam-management',
  })
};
</script>

<script>
export default {
  name: 'EXAMMANAGEMENTDETAILSINDEX'
};
</script>
