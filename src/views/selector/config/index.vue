<template>
  <ele-page hide-footer  flex-table>
    <search @search="reload"/>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     highlight-current-row
                     tooltip-effect="light"
                     cache-key="sphfwDictionaryGroup"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #title="{ row }">
          <ele-tooltip content="去编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.title }}
            </el-link>
          </ele-tooltip>
        </template>
        <!--        <template #action="{ row }">-->
        <!--          <el-link type="primary" underline="never" @click="openEdit(row)">-->
        <!--            修改-->
        <!--          </el-link>-->
        <!--          <el-divider direction="vertical"/>-->
        <!--          <el-link type="primary" underline="never" @click="remove(row)">-->
        <!--            删除-->
        <!--          </el-link>-->
        <!--        </template>-->
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter"/>
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {PlusOutlined, DeleteOutlined, ClusterOutlined} from '@/components/icons/index.js';
import Edit from './components/edit.vue';
import search from './components/search.vue';
import {queryPage, removes} from './api';

import {useRouter} from "vue-router";
import {transformDicDataName} from '@/utils/common_bak2.js';
import {useDictData} from "@/utils/use-dict-data.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import NameFilter from "@/views/exam/my-exam/components/name-filter.vue";

const {currentRoute, push} = useRouter();
const {params, path} = unref(currentRoute);
const userType = path.split("/")[4];

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
let dicCodes = ['listRoles', 'personnelSelector']
dicCodes.forEach(code => {
  if (dicts.value[code]) {
    return false
  } else {
    let params = {}
    if (userType) params.userType = userType;
    if (code === 'personnelSelector') params.valueField = 'code'
    useDictData([code], params);
  }
})

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      fixed: 'left'
    },
    {
      prop: 'title',
      slot: 'title',
      label: '显示名称',
    },
    {
      prop: 'key',
      label: '选项代码',
    },
    {
      prop: 'name',
      label: '选项名称',
      headerSlot: 'nameHeader',
    },
    {
      prop: 'paramMode',
      label: '功能业务类型',
      formatter: (row) => {
        let newArray = row.paramMode ? [row.paramMode] : []
        let rData = transformDicDataName(dicts.value['personnelSelector'], newArray,)
        return row.paramMode ? rData + '  ' + row.paramMode : '';
      },
    },
    {
      prop: 'url',
      label: '数据接口',
    },
    {
      prop: 'columns',
      label: '字段配置',
    },
    {
      prop: 'role',
      label: '授权角色',
      formatter: (row) => {
        let newArray = row.role ? row.role : []
        return transformDicDataName(dicts.value['listRoles'], newArray,);
      },
    },
    {
      prop: 'sort',
      label: '排序',
    },
    {
      prop: 'status',
      label: '状态',
    },
    {
      prop: 'userType',
      label: '用户类别',
    },
  ]
);

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit});
};
/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      groupName: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef.value?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef.value?.reload?.();
  }
};

/** 打开组group编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.paramName).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

</script>

<script>
export default {
  name: 'SelectorConfig'
};
</script>


<style scoped>
.el-link {
  font-weight: unset !important;
}
</style>
