<template>
  <ele-page hide-footer flex-table :style="{paddingTop:'8px!important'}">
    <ele-card :body-style="{ padding: '5px 0!important', overflow: 'hidden'}">
      <template #header>
        <span>
           <IconPark name="help" size="18" strokeWidth="3"/>点击保存按钮系统自动检查必填项（标 <span style="color: red">*</span> 字段）和格式合法性</span>
      </template>
      <template #extra>
        <ele-text style="float: left;" v-if="validMsg" type="danger" :icon="CloseCircleOutlined">
          <span>{{ validMsg }}</span>
        </ele-text>
        <template v-if="currentEnterType!=='preview'">
          <el-button plain size="small" type="primary" :loading="loading" @click="submitInfo">
            保存
          </el-button>
        </template>
      </template>

      <div :style="{overflow:'auto'}">
        <el-form ref="formRef"
                 size="small"
                 :model="form"
                 label-position="top"
                 style="margin: 10px;">
          <el-row :gutter="8">
            <template v-for="item in formItems">
              <el-col :span="item.colProps.span" v-if="item.showFlag==='是'&&item.selfModifyFlag==='是'">
                <template v-if="item.prop==='zqtys'">
                  <el-form-item :label="item.label" required>
                    <tinymce-editor ref="vditorRef"
                                    :key="'editor_' + editorHeight"
                                    :init="{
                                      ...item.init,
                                      height: editorHeight
                                    }"
                                    v-model="form[item.prop]"/>
                  </el-form-item>
                </template>
                <ProFormItem v-else :item="item"
                             :model="form"
                             @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </ProFormItem>
              </el-col>
              <el-col :span="item.colProps.span" v-else class="border-only-bottom">
                <ProFormItem :item="item"
                             :model="form"
                             @updateItemValue="(prop,value) => updateFormValue(item,prop, value)">
                  <template
                    v-for="name in Object.keys($slots).filter( (k) =>!['default', 'footer', 'topExtra', 'bottomExtra'].includes(k))"
                    #[name]="slotProps">
                    <slot :name="name" v-bind="slotProps || {}"></slot>
                  </template>
                </ProFormItem>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
    </ele-card>
  </ele-page>
</template>

<script setup>
import {computed, nextTick, onMounted, ref, onBeforeUnmount, watch} from 'vue';
import {useFormData} from '@/utils/use-form-data';
import {
  getXljkParam,
  operation
} from "./api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import ProFormItem from "@/components/ProForm/components/pro-form-item.vue";
import {CloseCircleOutlined} from "@/components/icons/index.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import TinymceEditor from "@/components/TinymceEditor/index.vue";
// 使用防抖函数防止频繁触发
import {debounce} from 'lodash-es';
import Vditor from "vditor";
import IconPark from "@/components/IconPark/index.vue";

const emit = defineEmits(['done']);

const props = defineProps({
  /** 修改回显的数据 */
  data: Object,
  currentId: String,
  RandomString: String,
  routeType: String,
  enterType: String,//preview：详情 add:添加 edit:编辑
});

// 使用ref存储编辑器高度
const editorHeight = ref(300); // 默认高度

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const validMsg = ref(null);
const validMsgCount = ref(0);

const currentEnterType = ref(null);
currentEnterType.value = props.enterType;
/** 编辑器 */
const vditorRef = ref(null);
/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  sycs: void 0,
  nzdyycs: void 0,
  zqtys: '',
});

/** 表单实例 */
const formRef = ref(null);

// 监听窗口变化
const handleResize = debounce(() => {
  if (!isUpdatingHeight) {
    updateEditorHeight();
  }
}, 100);

/** 是否是修改 */
const isUpdate = ref(false);
/** 提交状态 */
const loading = ref(false);

const baseFileds = ref([])

const formItems = computed(() => {
  baseFileds.value.forEach(filed => {
    filed.showFlag = currentEnterType.value === 'preview' ? 'readonly' : '是'
    if (filed.type === "dictSelect") {
      filed.props.disabled = currentEnterType.value === 'preview';
      filed.props.readonly = currentEnterType.value === 'preview';
    } else if (filed.type === "switch" || filed.type === "radioButton") {
      filed['props'] = {
        "readonly": currentEnterType.value === 'preview',
        "disabled": currentEnterType.value === 'preview',
      }
    } else {
      filed['props'] = {
        "readonly": currentEnterType.value === 'preview',
      }
    }
  })
  return baseFileds.value;
})

const updateFormValue = (item, prop, newVal) => {
  form[item.prop] = newVal;
}

/** 提交 */
const submitInfo = () => {
  formRef["value"]?.validate?.((valid, obj) => {
    if (valid) {
      loading.value = true;
      let rData = Object.assign({}, form)
      if (rData.pyccid) rData.pyccid = rData.pyccid.join(',')
      if (rData.xyid) rData.xyid = rData.xyid.join(',')
      if (rData.njid) rData.njid = rData.njid.join(',')
      operation(rData).then((msg) => {
        loading.value = false;
        if (msg) {
          EleMessage.success(msg);
          emit('done');
        }
      }).catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      }).finally(() => {
      });
    } else {
      const errors = obj ? Object.keys(obj).length : 0;
      validMsgCount.value += errors;
      validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
      validMsgCount.value = 0;
    }
  });
};

/** 已设置参数信息 */
const bindXljkParam = ref(null);

const queryXljkParam = async () => {
  loading.value = true;
  let newObj = {}
  getXljkParam(newObj).then((rData) => {
    loading.value = false;
    if (rData.length > 0) {
      nextTick(() => {
        bindXljkParam.value = rData.length > 0 ? rData[0] : null;
        form.id = bindXljkParam.value.id
        form.sycs = bindXljkParam.value.sycs
        form.nzdyycs = bindXljkParam.value.nzdyycs
        form.zqtys = bindXljkParam.value.zqtys
      });
    }
  });
}
// 使用标志位防止递归
let isUpdatingHeight = false;
const updateEditorHeight = () => {
  if (isUpdatingHeight) return;
  isUpdatingHeight = true;

  let height = pageHeight.value - 128;
  const newHeight = Math.max(height, 300);

  if (editorHeight.value !== newHeight) {
    editorHeight.value = newHeight;
    // 直接操作编辑器实例设置高度
    if (vditorRef.value?.editor) {
      const container = vditorRef.value.editor.getContainer();
      const contentArea = vditorRef.value.editor.getContentAreaContainer();

      if (container.style.height !== `${newHeight}px`) {
        container.style.height = `${newHeight}px`;
      }
      if (contentArea.style.height !== `${newHeight - 40}px`) {
        contentArea.style.height = `${newHeight - 40}px`;
      }
    }
  }

  isUpdatingHeight = false;
};
const styles = computed(() => ({
  '--editor-height': `${editorHeight.value}px`,
  '--editor-content-height': `${editorHeight.value - 40}px`
}));

// 编辑器配置
const editorOptions = {
  theme: 'snow', // 主题：snow（白色简洁） or bubble（气泡简洁）
  modules: {
    toolbar: [
      [{header: [1, 2, false]}],
      ['bold', 'italic', 'underline'],
      ['image', 'code-block']
    ]
  }
}

const content = ref('');
onMounted(async () => {
  window.addEventListener('resize', handleResize);
  // 1. 先初始化表单字段
  baseFileds.value = [
    {
      prop: 'sycs', label: '爽约次数', type: 'inputNumber', required: true,
      showFlag: '是', selfModifyFlag: '是', colProps: {span: 12},
    },
    {
      prop: 'nzdyycs', label: '最多预约次数（自然年）', type: 'inputNumber', required: true,
      showFlag: '是', selfModifyFlag: '是', colProps: {span: 12},
    },
    {
      prop: 'zqtys', label: '知情同意书', type: 'editor', required: true,
      init: {
        height: editorHeight.value,
        setup: (editor) => {
          editor.on('init', () => {
            requestAnimationFrame(() => {
              updateEditorHeight();
            });
          });
        }
      },
      showFlag: '是', selfModifyFlag: '是', colProps: {span: 24},
    },
  ];

  // 2. 使用requestAnimationFrame确保DOM就绪
  requestAnimationFrame(() => {
    updateEditorHeight();
  });

  // 3. 获取数据
  await queryXljkParam();

  // 4. 使用setTimeout确保完全加载
  setTimeout(() => {
    updateEditorHeight();
  }, 300);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

// 监听所有可能影响高度的变化
watch([pageHeight, currentEnterType], () => {
  if (!isUpdatingHeight) {
    updateEditorHeight();
  }
}, {
  immediate: true,
  flush: 'post' // 在 DOM 更新后执行
});

</script>
<style scoped>
@import "@/css/border-only-bottom.css";
/* Quill 编辑器自带的主题样式 */
@import "quill/dist/quill.snow.css";

/* 确保编辑器容器正确显示 */
:deep .tox-tinymce {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: height 0.3s ease;
}

:deep .el-form-item__content {
  display: block !important;
}

</style>
