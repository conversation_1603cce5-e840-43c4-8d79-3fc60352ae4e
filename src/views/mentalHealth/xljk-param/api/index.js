import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/mentalHealth/xljk-param/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkParam(params) {
  const res = await request.get('/mentalHealth/xljk-param', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkParamInfo(id) {
  const res = await request.get('/mentalHealth/xljk-param/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-param/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-param/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
