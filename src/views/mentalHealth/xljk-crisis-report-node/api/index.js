import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryPageXljkCrisisReportNode(params) {
  const res = await request.get('/mentalHealth/xljk-crisis-report-node/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkCrisisReportNode(params) {
  const res = await request.get('/mentalHealth/xljk-crisis-report-node', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkCrisisReportNodeInfo(id) {
  const res = await request.get('/mentalHealth/xljk-crisis-report-node/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-crisis-report-node/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-crisis-report-node/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
