<!-- 添加或修改心理危机上报审核流程 -->
<template>
  <ele-drawer size="43%"
              :title="isUpdate ? '修改' : '添加'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-position="top"
             label-width="100px"
             @submit.prevent="">
      <el-form-item label="审核节点角色" prop="roleName">
        <dict-data placeholder="请选择审核节点角色"
                   v-model="form.roleName"
                   code="listRoles"
                   type="select"/>
      </el-form-item>
      <el-form-item label="审核节点顺序" prop="sortNumber">
        <el-input-number :min="0"
                         :max="99999"
                         v-model="form.sortNumber"
                         placeholder="请输入审核节点顺序"
                         controls-position="right"
                         class="ele-fluid"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, computed} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../api/index.js';
import {CirclePlus} from "@element-plus/icons-vue";
import PerSelector from "@/components/PerSelector/index.vue";
import FileUpload from '@/components/FileUpload/index.vue';
import {isImageFile, toFormData, toFormDataWj} from '@/utils/common_bak2.js';
import {getPersonIdNumber} from "@/views/personInfo/st/api/index.js";

const BASE_URL = import.meta.env.BASE_URL;
const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  /** 试卷ID*/
  paperId: String,
  routeType: String,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  sortNumber: void 0,
  roleName: '',
});

/** 表单验证规则 */
const rules = reactive({
  roleName: [
    {
      required: true,
      message: '请选择审核节点角色',
      type: 'string',
      trigger: 'change'
    }
  ],
  sortNumber: [
    {
      required: true,
      message: '请输入审核节点顺序',
      type: 'number',
      trigger: 'blur'
    }
  ]
});

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    console.log(JSON.stringify(form))
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      resetFields();
      if (props.data) {
        assignFields({
          ...props.data,
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
