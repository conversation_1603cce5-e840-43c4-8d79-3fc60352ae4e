<template>
  <el-dialog v-model="dialogVisible"
             :title="dialogTitle"
             @close="closeDialog()"
             draggable
             overflow
             destroy-on-close
             :close-on-click-modal="false"
             width="660px">
    <el-form ref="formRef"
             :model="form"
             size="small"
             label-width="99px"
             @submit.prevent="">
      <template v-if="currentRoleScope!=='咨询师'">
        <el-form-item label="咨询师">
          <dict-data placeholder="请选择咨询师"
                     code="consultantList"
                     type="multipleSelect"
                     :dicQueryParams="{
                            dictFieldUrl: '/mentalHealth/xljk-counselor-schedule/consultantList',
                            valueField: 'xgh',
                            textField: 'userInfo.xm',
                            valueType:'Object'
                         }"
                     v-model="form.consultantsInfo"/>
        </el-form-item>
      </template>
      <!-- 周视图 -->
      <template v-if="currentView === 'week'">
        <el-form-item label="选择星期" prop="xqmcs"
                      :rules="[{ required: true, message: '请选择星期', trigger: 'change' }]">
          <el-checkbox-group v-model="form.xqmcs"
                             placeholder="请选择星期"
                             style="width: 100%">
            <el-checkbox v-for="day in weekDays"
                         :key="day.dateStr"
                         :label="`${day.week}`"
                         :value="day.week">
              {{ day.week }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </template>
      <!-- 月视图 -->
      <template v-if="currentView === 'month'">
        <el-form-item label="选择日期" prop="selectedDates"
                      :rules="[{ required: true, message: '请选择日期', trigger: 'change',validator: validateDates }]">
          <el-date-picker v-model="form.selectedDates"
                          type="dates"
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          placeholder="选择多个日期（可跨月）"
                          style="width: 100%"/>
          <!--            :picker-options="pickerOptions"-->
          <!--            @change="handleDateChange"-->
        </el-form-item>
      </template>
      <el-form-item label="开始时间" prop="kssjStr"
                    :rules="[{ required: true, message: '请选择开始时间', trigger: 'change' }]">
        <el-time-select v-model="form.kssjStr"
                        start="09:00"
                        step="00:30"
                        end="23:00"
                        placeholder="选择开始时间"
                        style="width: 100%"
                        @change="handleStartTimeChange">
        </el-time-select>
      </el-form-item>
      <el-form-item label="结束时间" prop="jssjStr"
                    :rules="[{
                required: true,
                message: '请选择结束时间',
                trigger: 'change',
                validator: validateEndTime
              }]">
        <el-time-select v-model="form.jssjStr"
                        :picker-options="timeOptions"
                        start="09:00"
                        step="00:30"
                        end="23:00"
                        :min-time="form.kssjStr"
                        placeholder="选择结束时间"
                        style="width: 100%"
                        :disabled="!form.kssjStr">
        </el-time-select>
      </el-form-item>
      <el-form-item label="可预约人数" prop="kyyrs"
                    :rules="[{
                required: true,
                message: '请输入可预约人数',
                trigger: 'change',
                validator: validateKyyrs
              }]">
        <el-input-number v-model="form.kyyrs" style="width: 100%"/>
      </el-form-item>
      <el-form-item label="预约窗口期" v-if="form.pblx==='固定周期'">
        <el-date-picker v-model="form.yyckq"
                        type="date"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        placeholder="选择固定周期班次有效截止日期"
                        style="width: 100%"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="closeDialog()">取消</el-button>
      <el-button type="danger" size="small" @click="deleteEvent" v-if="form.id">删除</el-button>
      <el-button type="primary" size="small" @click="saveEvent">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup>

import {computed, reactive, ref, watch} from "vue";
import {compareTime} from "@/utils/dayJs.js";
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from "element-plus";
import {operation, removes} from "@/views/mentalHealth/xljk-counselor-schedule/api/index.js";
import {useFormData} from "@/utils/use-form-data.js";

const emits = defineEmits(['done', 'update:visible']);
const props = defineProps({
  /** 弹窗是否打开 */
  visible: Boolean,
  /** 修改回显的数据 */
  data: Object,
  // timeOptions: Object,
  dialogTitle: String,
  setCurrentView: String,
  currentRoleScope: String,
  weekDays: Array,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

// 当前视图模式
const currentView = ref(null) // 'day', 'week' 或 'month'

const timeOptions = reactive({
  start: '09:00',   // 开始时间
  step: '01:00',    // 时间间隔
  end: '23:00',     // 结束时间
})

// const currentPblx = computed(() => {
//   if (currentView.value === 'week') {
//     return '固定周期'
//   } else {
//     return '固定日期'
//   }
// })

/** 表单数据 */
const [form, resetFields, setFieldValue, assignFields] = useFormData({
  id: null,
  selectedDates: [],
  consultants: [], // 会自动接收[对象数组, 值数组]
  consultantsInfo: [], // 如果需要可以单独存储ID数组
  xqmcs: [],
  kssjStr: '',
  jssjStr: '',
  kyyrs: 0,
  // pblx: currentPblx.value,
  pblx: '',
  yyckq: '',
  xqmc: '',//星期名称
  gzrq: '',//工作日期
  gzrqStr: '',//工作日期字符串
});

let dialogVisible = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
})
const closeDialog = () => {
  resetFields()
  emits('done', currentView.value)
  emits('update:visible', false)
}

// 自定义验证函数
const validateDates = (rule, value, callback) => {
  if (!value || value.length === 0) {
    callback(new Error('请至少选择一个日期'))
    // } else if (value.length > 5) { // 示例：限制最多选择5个日期
    //   callback(new Error('最多只能选择5个日期'))
  } else {
    callback()
  }
}
// 开始时间变化时的处理
const handleStartTimeChange = (val) => {
  // 如果结束时间早于新的开始时间，则清空结束时间
  if (form.jssjStr && compareTime(form.jssjStr, val) <= 0) {
    form.jssjStr = ''
  }
}

// 自定义结束时间验证规则
const validateEndTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请选择结束时间'))
  } else if (!form.kssjStr) {
    callback(new Error('请先选择开始时间'))
  } else if (compareTime(value, form.kssjStr) <= 0) {
    callback(new Error(`结束时间必须晚于 ${form.kssjStr}`))
  } else {
    callback()
  }
}
const validateKyyrs = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入可预约人数'))
  } else {
    callback()
  }
}

/** 删除 */
const deleteEvent = () => {
  const rows = form == null ? form : [form];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.gzrq).join(', ') + '”的排班信息吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      // switchView(currentView.value)
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    }).finally(() => {
      // cancleEvent(false)
      closeDialog()
    });
  }).catch(() => {
  });
};

const validateForm = () => {
  if (currentView.value === 'week') {
    if (!form.xqmcs) {
      EleMessage.error('请选择星期')
      return false
    }
  }
  if (currentView.value === 'month') {
    if (!form.selectedDates) {
      EleMessage.error('请选择日期')
      return false
    }
  }
  if (!form.kssjStr) {
    EleMessage.error('请选择开始时间')
    return false
  }
  if (!form.jssjStr) {
    EleMessage.error('请选择结束时间')
    return false
  }
  return true
}

// 保存事件
const saveEvent = () => {
  // 验证表单
  if (!validateForm()) return
  // 添加新事件
  console.log(form)
  if (currentView.value === 'week') form.xqmc = form?.xqmcs.join(',') ?? '';
  if (currentView.value === 'month') form.gzrqStr = form.selectedDates.join(',');
  if (form.consultants.length > 0) form.zxsxx = form.consultants.join(',');
  loading.value = true;
  operation(form).then((res) => {
    if (res) {
      loading.value = false;
      closeDialog()
      EleMessage.success('操作成功');
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    emits('done', currentView.value)
  });
}

watch(() => form.consultantsInfo, (newVal) => {
  form.consultants = (newVal || []).map(item => item.xgh + '|' + item.userInfo.xm)
}, {immediate: true, deep: true})

watch(
  () => props.visible,
  (val) => {
    if (val) {
      currentView.value = props.setCurrentView
      console.log(props.data)
      if (props.data) {
        // assignFields(props.data);
        setFieldValue({
          ...props.data,
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
<style scoped lang="scss">

</style>
