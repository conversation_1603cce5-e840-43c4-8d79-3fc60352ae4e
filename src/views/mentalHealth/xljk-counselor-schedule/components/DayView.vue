<template>
  <div class="day-view-container">
    <ele-table size="small" border style="min-width: 600px; table-layout: fixed;">
      <thead style="display: block; background: #f5f7fa;">
      <tr style="display: flex;">
        <th style="width: 100px; flex-shrink: 0; height: 50px; line-height: 50px;">时间</th>
        <th style="flex-grow: 1; height: 50px; line-height: 50px;">{{ currentDayText }}</th>
      </tr>
      </thead>
      <tbody :style="{height: (pageHeight-90)+'px', overflow:'auto'}">
      <tr v-for="(timeSlot, index) in dayTimeSlots" :key="index" style="display: flex; min-height: 56px;">
        <td class="time-label"
            style="width: 100px; flex-shrink: 0; display: flex; align-items: center; justify-content: center; border-right: 1px solid #ebeef5;">
          {{ timeSlot.time }}
          <div v-if="getEventsForTimeSlot(timeSlot).length>=5" style="float: right;padding-left: 5px"
               @click.stop="handleScheduleList(timeSlot.time)">
            <ele-tooltip content="点击查看更多排序信息" placement="left" effect="light">
              <IconPark name="more-app" size="12" strokeWidth="5"/>
            </ele-tooltip>
          </div>
        </td>
        <td style="flex-grow: 1; position: relative; padding: 2px;"
            @click="handleCellClick(timeSlot.time)">
          <div v-if="events.length > 0">
            <ele-check-card :items="getEventsForTimeSlot(timeSlot)"
                            :row="{ gutter: 6 }" style="margin: 3px;">
              <template #item="{ item }">
                <div style="display: flex; padding: 3px; min-height: 28px"
                     @click.stop="emit('edit-event', item)">
                  <div style="flex: 1; min-width: 0;">
                    <ele-text size="sm">{{ currentRoleScope !== '咨询师' ? item.xm : '' }}</ele-text>
                    <ele-text :size="currentRoleScope !== '咨询师' ? 'sm' : 'md' "
                              :type="currentRoleScope !== '咨询师' ? 'placeholder' : '' ">
                      可接待: {{ item.kyyrs }}人
                    </ele-text>
                    <div class="event-time" v-if="currentRoleScope === '咨询师'">
                      {{ item.kssjStr }} -{{ item.jssjStr }}
                    </div>
                  </div>
                  <!--                  <IconPark name="mark" size="20" strokeWidth="3"/>-->
                </div>
              </template>
            </ele-check-card>
          </div>
        </td>
      </tr>
      </tbody>
    </ele-table>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import IconPark from "@/components/IconPark/index.vue";
import {dayJsFormatDate, isTimeInRange} from "@/utils/dayJs.js";
import {generateRandomString} from "@/utils/common_bak2.js";
import dayjs from "dayjs";
import {isSameDay} from "@/views/mentalHealth/utils/index.js";

const props = defineProps({
  events: {
    type: Array,
    default: () => []
  },
  currentDate: {
    type: Date,
    required: true
  },
  pageHeight: {
    type: Number,
    default: 0 // 设置默认值，避免传入 null 报错
  },
  currentRoleScope: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['cell-click', 'edit-event', 'show-schedule']);

// 生成一天的时间段 (9:00 - 23:00)，半小时为单位
const dayTimeSlots = computed(() => {
  const slots = [];
  const startHour = 9;
  const endHour = 23;

  for (let hour = startHour; hour <= endHour; hour++) {
    slots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      hour,
      minute: 0
    });

    if (hour < endHour) {
      slots.push({
        time: `${hour.toString().padStart(2, '0')}:30`,
        hour,
        minute: 30
      });
    }
  }
  return slots;
});

const currentDayText = computed(() => {
  return dayJsFormatDate(props.currentDate, 'YYYY-MM-DD dddd');
});

const getEventsForTimeSlot = (timeSlot) => {
  const currentDateStr = dayJsFormatDate(props.currentDate);
  const slotStart = new Date(`${currentDateStr} ${timeSlot.time}`);
  return props.events.filter(event => {
    if (!event.gzrq || !event.kssjStr || !event.jssjStr) return false;

    try {
      const eventDateStr = event.gzrq;
      const eventStart = new Date(`${eventDateStr} ${event.kssjStr}`);
      const eventEnd = new Date(`${eventDateStr} ${event.jssjStr}`);

      if (!isSameDay(eventStart, slotStart)) return false;
      return isTimeInRange(slotStart, eventStart, eventEnd);
    } catch (e) {
      console.error('事件时间解析错误:', e);
      return false;
    }
  }).map(event => ({
    ...event,
    col: {md: 3, sm: 12, xs: 24, style: {marginBottom: '1px'}}
  }));
};


const handleCellClick = (time) => {
  emit('cell-click', time);
};

const handleScheduleList = (time) => {
  emit('show-schedule', {
    time: time,
    date: props.currentDate
  });
};
</script>

<style lang="scss" scoped>
@import "@/views/mentalHealth/css/index.css";
</style>


