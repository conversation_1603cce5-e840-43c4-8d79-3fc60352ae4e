<template>
  <div class="week-view-container">
    <ele-table size="small" border style="min-width: 600px; table-layout: fixed;">
      <thead style="display: block; background: #f5f7fa;">
      <tr style="display: flex;">
        <th style="width: 100px; flex-shrink: 0;height: 50px;">时间</th>
        <th v-for="day in weekDays" :key="day.dateStr" style="flex-grow: 1;height: 50px;">
          {{ day.week }}<br/>{{ day.displayDate }}
        </th>
      </tr>
      </thead>
      <tbody :style="{height: (pageHeight-90)+'px', overflow:'auto'}">
      <tr v-for="(timeSlot, timeIndex) in timeSlots" :key="timeSlot.time" style="display: flex;">
        <td class="time-label" style="width: 100px; flex-shrink: 0;">{{ timeSlot.time }}</td>
        <td v-for="(day, dayIndex) in weekDays"
            :key="day.dateStr"
            style="flex-grow: 1; min-height: 75px; position: relative;cursor: pointer"
            @click="handleCellClick(timeSlot.time, day.date, getEventsForDayAndTime(day.date, timeSlot)[1])">
          <template v-if="getEventsForDayAndTime(day.date, timeSlot)[1]==='group'">
            <ele-check-card :items="getEventsForDayAndTime(day.date, timeSlot)[0]" :row="{ gutter: 6 }"
                            style="margin: 3px;"
                            class="day-event">
              <template #item="{ item }">
                <div style="display: flex; padding: 3px; min-height: 28px">
                  <div style="flex: 1; min-width: 0;">
                    <ele-text size="sm">{{ currentRoleScope !== '咨询师' ? item.lable : '' }}</ele-text>
                    <ele-text size="sm"
                              :type="currentRoleScope !== '咨询师' ? 'placeholder' : '' ">
                      可接待: {{ item.kyyrs }}人
                    </ele-text>
                  </div>
                  <ele-tooltip content="点击查看更多排序信息" placement="left" effect="light">
                    <IconPark name="more-app" size="12" strokeWidth="5"/>
                  </ele-tooltip>
                </div>
              </template>
            </ele-check-card>
          </template>
          <template v-else>
            <ele-check-card :items="getEventsForDayAndTime(day.date, timeSlot)[0]" :row="{ gutter: 6 }"
                            style="margin: 3px;"
                            class="day-event">
              <template #item="{ item }">
                <div style="display: flex; padding: 3px; min-height: 28px"
                     @click.stop="emit('edit-event', item)">
                  <div style="flex: 1; min-width: 0;">
                    <ele-text size="sm">{{ currentRoleScope !== '咨询师' ? item.xm : '' }}</ele-text>
                    <ele-text size="sm"
                              :type="currentRoleScope !== '咨询师' ? 'placeholder' : '' ">
                      可接待: {{ item.kyyrs }}人
                    </ele-text>
                    <ele-text size="sm" type="placeholder" v-if="currentRoleScope === '咨询师'">
                      {{ item.kssjStr }}-{{ item.jssjStr }}
                    </ele-text>
                  </div>
<!--                  <IconPark name="mark" size="20" strokeWidth="3"/>-->
                </div>
              </template>
            </ele-check-card>
          </template>
        </td>
      </tr>
      </tbody>
    </ele-table>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import IconPark from "@/components/IconPark/index.vue";
import {dayJsFormatDate, isTimeInRange} from "@/utils/dayJs.js";
import {groupArr} from "@/utils/common_bak2.js";
import dayjs from "dayjs";
import {generateWeekDays, isSameDay} from "@/views/mentalHealth/utils/index.js";

const props = defineProps({
  events: {
    type: Array,
    default: () => []
  },
  currentDate: {
    type: Date,
    required: true
  },
  pageHeight: {
    type: Number,
    default: 0 // 设置默认值，避免传入 null 报错
  },
  currentRoleScope: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['cell-click', 'edit-event', 'show-schedule']);

// 生成一周日期（周一到周日）
const weekDays = computed(() => generateWeekDays(props.currentDate))

// 生成时间区间数据（带7天数据），半小时为单位
const timeSlots = computed(() => {
  const slots = [];
  let current = new Date();
  current.setHours(9, 0, 0, 0); // 开始时间 9:00
  const jssj = new Date();
  jssj.setHours(23, 0, 0, 0); // 结束时间 23:00

  while (current <= jssj) {
    slots.push({
      time: current.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }),
      hour: current.getHours(),
      minute: current.getMinutes()
    });

    const nextTime = new Date(current.getTime() + 30 * 60 * 1000);
    if (nextTime <= jssj) {
      slots.push({
        time: nextTime.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }),
        hour: nextTime.getHours(),
        minute: nextTime.getMinutes()
      });
    }

    current = new Date(current.getTime() + 60 * 60 * 1000); // 1小时间隔
  }
  return slots;
});

const getEventsForDayAndTime = (date, timeSlot) => {
  const currentDateStr = dayJsFormatDate(date);
  const slotStart = new Date(`${currentDateStr} ${timeSlot.time}`);
  let eventsData = props.events.filter(event => {
    if (!event.gzrq || !event.kssjStr || !event.jssjStr) return false;

    try {
      const eventDateStr = event.gzrq;
      const eventStart = new Date(`${eventDateStr} ${event.kssjStr}`);
      const eventEnd = new Date(`${eventDateStr} ${event.jssjStr}`);

      if (!isSameDay(eventStart, slotStart)) return false;
      return isTimeInRange(slotStart, eventStart, eventEnd);
    } catch (e) {
      console.error('事件时间解析错误:', e);
      return false;
    }
  });

  let newData = groupArr(eventsData, 'xlzxEvent');
  let eventsRData = [];
  let mark = "";

  if (newData.length > 2) {
    mark = 'group';
    eventsRData = [{
      lable: newData.length + '位咨询师在班',
      kyyrs: newData.reduce((sum, consultant) => sum + consultant.totalCapacity, 0),
    }];
  } else {
    eventsRData = newData.length > 0 ? newData[0].list : [];
  }

  return [eventsRData, mark];
};

const handleCellClick = (time, date, eventSize) => {
  if (eventSize === 'group') {
    emit('show-schedule', {
      time: time,
      date: date
    });
  } else {
    emit('cell-click', {time, date});
  }
};
</script>

<style lang="scss" scoped>
@import "@/views/mentalHealth/css/index.css";
</style>

