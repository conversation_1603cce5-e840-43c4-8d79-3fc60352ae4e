<template>
  <div class="month-view-container">
    <div class="month-header">
      <div class="weekdays-header">
        <div v-for="day in baseWeekDays()" :key="day" class="weekday">
          {{ day }}
        </div>
      </div>
    </div>
    <div class="month-body" :style="{height: `calc(${pageHeight}px - 130px)`}">
      <div v-for="(week, weekIndex) in monthWeeks" :key="weekIndex" class="week-row">
        <div v-for="(day, dayIndex) in week"
             :key="day.dateStr"
             class="day-cell"
             :class="{
               'current-month': day.isCurrentMonth,
               'today': day.isToday,
               'weekend': dayIndex === 5 || dayIndex === 6,
               'holiday': day.isHoliday
             }"
             @click="handleCellClick(day.date, day.events.length)">
          <div class="day-header">
            <div class="day-header-content">
              <span v-if="day.holiday" class="holiday-text">
                {{ day.holiday }}
              </span>
              <span class="day-number" :class="{ 'today': day.isToday }">
                {{ dayJsFormatDate(day.date, 'DD') }}
              </span>
            </div>
          </div>
          <div class="day-events">
            <template v-if="day.events.length>0">
              <ele-check-card :items="day.events"
                              style="margin: 3px;"
                              class="month-event">
                <template #item="{ item }">
                  <div style="display: flex; padding: 3px; min-height: 28px">
                    <div style="flex: 1; min-width: 0;">
                      <ele-text size="sm">{{ currentRoleScope !== '咨询师' ? item.lable : '' }}</ele-text>
                      <ele-text size="sm"
                                :type="currentRoleScope !== '咨询师' ? 'placeholder' : '' ">
                        可接待: {{ item.kyyrs }}人
                      </ele-text>
                    </div>
                    <ele-tooltip content="点击查看更多排序信息" placement="left" effect="light">
                      <IconPark name="more-app" size="12" strokeWidth="5"/>
                    </ele-tooltip>
<!--                    <IconPark name="two-triangles" size="20" strokeWidth="3"/>-->
                  </div>
                </template>
              </ele-check-card>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import IconPark from "@/components/IconPark/index.vue";
import {dayJsFormatDate} from "@/utils/dayJs.js";
import {generateAllHolidays} from "@/utils/holiday/index.js";
import {baseWeekDays, chunkArray, createDayObject,} from "@/views/mentalHealth/utils/index.js";

const props = defineProps({
  events: {
    type: Array,
    default: () => []
  },
  currentDate: {
    type: Date,
    required: true
  },
  pageHeight: {
    type: Number,
    default: 0 // 设置默认值，避免传入 null 报错
  },
  currentRoleScope: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['cell-click', 'show-schedule']);

const holidayMap = computed(() => {
  const year = props.currentDate.getFullYear();
  const map = {};
  const holidays = generateAllHolidays(year);
  holidays.forEach(item => {
    map[item.date] = {
      type: item.type,
      label: item.name,
      isHoliday: item.isHoliday
    };
  });
  return map;
});

const monthWeeks = computed(() => {
  const year = props.currentDate.getFullYear();
  const month = props.currentDate.getMonth();

  // 当月第一天和最后一天
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  // 计算需要显示的上个月天数（调整为周一开始）
  const firstDayOfWeek = firstDay.getDay();
  const prevMonthDays = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

  // 计算需要显示的下个月天数
  const totalCells = 42; // 6周×7天
  const currentMonthDays = lastDay.getDate();
  const nextMonthDays = totalCells - currentMonthDays - prevMonthDays;

  const days = [];

  // 1. 添加上个月的日期
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  for (let i = prevMonthDays; i > 0; i--) {
    const dayDate = new Date(year, month - 1, prevMonthLastDay - i + 1);
    days.push(createDayObject(dayDate, false, holidayMap.value, props.events));
  }

  // 2. 添加当月的日期
  for (let i = 1; i <= currentMonthDays; i++) {
    const dayDate = new Date(year, month, i);
    days.push(createDayObject(dayDate, true, holidayMap.value, props.events));
  }

  // 3. 添加下个月的日期
  for (let i = 1; i <= nextMonthDays; i++) {
    const dayDate = new Date(year, month + 1, i);
    days.push(createDayObject(dayDate, false, holidayMap.value, props.events));
  }
  // 按周分组
  return chunkArray(days, 7);
});


const handleCellClick = (date, eventSize) => {
  if (eventSize === 0) {
    emit('cell-click', date);
  } else {
    emit('show-schedule', {
      date: date
    });
  }
};
</script>

<style lang="scss" scoped>
@import "@/views/mentalHealth/css/index.css";
</style>

