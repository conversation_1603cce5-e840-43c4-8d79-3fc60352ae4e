<!--多咨询师排班列表-->
<template>
  <ele-drawer size="60%"
              title="咨询师排班信息"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="true"
              style="max-width: 100%"
              :model-value="modelValue"
              @close="updateModelValue"
              @update:modelValue="updateModelValue">
    <!-- 表格 -->
    <ele-pro-table ref="tableRef"
                   flex-table
                   row-key="id"
                   :columns="columns"
                   :datasource="datasource"
                   :span-method="spanMethod"
                   :border="true"
                   :show-overflow-tooltip="true"
                   v-model:selections="selections"
                   tooltip-effect="light"
                   highlight-current-row
                   :pagination="{
                      pageSize: 80,
                      layout: 'total, prev, pager, next, sizes, jumper',
                      autoAmend: false
                  }"
                   style="padding-bottom: 0"
                   :table-style="{  height: (pageHeight+10)+'px', overflow: 'auto' ,textAlign: 'center'}">
      <template #toolbar>
        <el-button size="small"
                   plain
                   class="ele-btn-icon"
                   :disabled="!selections.length || isLoading"
                   @click="remove()"> 删除
        </el-button>
      </template>
      <template #xm="{ row }">
        <ele-tooltip content="点击查看咨询师信息" placement="left" effect="light">
          <el-link type="primary"
                   underline="never"
                   @click="showConsultantDetail(row)">
            {{ row.xm }}
            <IconPark name="avatar" size="16" strokeWidth="3"/>
          </el-link>
        </ele-tooltip>
      </template>

      <template #kssjStr="{ row }">
        <ele-tooltip content="编辑" placement="left" effect="light">
          <el-link type="primary"
                   underline="never"
                   @click="editEvent(row)">
            {{ row.kssjStr }}
          </el-link>
        </ele-tooltip>
      </template>
    </ele-pro-table>
    <!-- 添加/编辑事件对话框 -->
    <schedule-dialog v-model:visible="dialogVisible"
                     :dialogTitle="dialogTitle"
                     :setCurrentView="currentView"
                     :currentRoleScope="currentRoleScope"
                     :weekDays="weekDays"
                     :data="currentData"
                     @done="reload"/>

    <consultant-detail-dialog v-model:visible="consultantDialogVisible" :data="currentConsultant"/>
  </ele-drawer>
</template>

<script setup>
import {ref, unref, watch,} from 'vue';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {ElLoading, ElMessage as EleMessage} from "element-plus";
import {ElMessageBox} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {
  removes,
  getXljkCounselorSchedulePage
} from "@/views/mentalHealth/xljk-counselor-schedule/api/index.js";
import ScheduleDialog from "@/views/mentalHealth/xljk-counselor-schedule/components/ScheduleDialog.vue";
import IconPark from "@/components/IconPark/index.vue";
import ConsultantDetailDialog from "@/views/mentalHealth/xljk-appointment-info/components/consultant-detail-dialog.vue";
import RecordIndex from "@/views/mentalHealth/xljk-appointment-info/record/index.vue";
import {getXljkConsultant} from "@/views/mentalHealth/xljk-consultant/api/index.js";

const emits = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  queryDateTime: Object,
  RandomString: String,
  setCurrentView: String,
  currentRoleScope: String,
  weekDays: Array,
});

const isLoading = ref(false);
// 当前视图模式
const currentView = ref(null) // 'day', 'week' 或 'month'
const currentData = ref(null)
const {removePageTab, getRouteTabKey, setPageTab,} = usePageTab();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

/** 表格实例 */
const tableRef = ref(null);

/** 加载状态 */
const loading = ref(false);

// 事件对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('编辑排班信息')


const consultantDialogVisible = ref(false);
const currentConsultant = ref({});
// 添加显示咨询师详情的方法
const showConsultantDetail = (consultant) => {
  //加载数据
  getXljkConsultant({xgh: consultant.xgh}).then((list) => {
    if (list) {
      console.log(list)
      currentConsultant.value = list[0]
      consultantDialogVisible.value = true
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 删除 */
const remove = (row) => {
  if (isLoading.value) return;
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.xm + '' + d.kssjStr + '至' + d.jssjStr).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'xm',
    slot: 'xm',
    columnKey: 'xm',
    label: '咨询师',
    minWidth: 110
  },
  {
    prop: 'gzrq',
    label: '工作日期',
  },
  {
    prop: 'kssjStr',
    slot: 'kssjStr',
    label: '开始时间',
  },
  {
    prop: 'jssjStr',
    label: '结束时间',
  },
  {
    prop: 'kyyrs',
    label: '可接待人数',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 预处理数据（排序 + 计算合并行数） */
const processData = (data) => {
  // 1. 按姓名排序
  const sortedData = [...data].sort((a, b) => a.xm.localeCompare(b.xm));
  // 2. 计算合并行数
  const nameMap = {};
  sortedData.forEach(item => {
    nameMap[item.xm] = (nameMap[item.xm] || 0) + 1;
  });
  // 3. 标记需要合并的行
  sortedData.forEach((item, index) => {
    if (index === 0 || item.xm !== sortedData[index - 1].xm) {
      item.xmRowSpan = nameMap[item.xm]; // 第一行设置合并行数
    } else {
      item.xmRowSpan = 0; // 后续相同姓名的行不显示
    }
  });
  return sortedData;
};

/** 表格数据源 */
const datasource = async ({page, limit = 80, where, orders}) => {
  const res = await getXljkCounselorSchedulePage({
    ...where, ...orders,
    page,
    limit,
    ...props?.queryDateTime
  });
  return {
    list: processData(res.list), // 处理数据
    count: res.count
  };
};

/** 合并表格单元格 */
const spanMethod = ({row, column}) => {
  if (column.property === 'xm') {
    return {
      rowspan: row.xmRowSpan, // 动态合并行数
      colspan: row.xmRowSpan > 0 ? 1 : 0 // 隐藏被合并的行
    };
  }
  return [1, 1]; // 其他列不合并
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

// 编辑事件
const editEvent = (event) => {
  dialogTitle.value = '编辑排班信息3'
  if (currentView.value === 'month') {
    event.selectedDates = [event?.gzrq];
  }
  if (currentView.value === 'week') {
    event.xqmcs = [event?.xqmc];
  }
  if (props.currentRoleScope !== '咨询师' && event.xgh) {
    event.consultantsInfo = [{
      "dictDataCode": event.xgh,
      "id": "e6a750b9ef8dedb31c51a1ce73059787",
      "xgh": event.xgh,
      "userInfo": {
        "xgh": event.xgh,
        "xm": event.xm,
      }
    }]
    event.consultants = [event.xgh + '|' + event.xm]
  }

  currentData.value = event;
  dialogVisible.value = true
}
/** 更新modelValue */
const updateModelValue = (value) => {
  emits('done', currentView.value)
  emits('update:modelValue', value);
};

watch(
  () => props.RandomString,
  (RandomString) => {
    if (RandomString) {
      currentView.value = props.setCurrentView
      reload()
    }
  },
  {immediate: true}
)
</script>

<script>
export default {
  name: 'SCHEDULEINDEX'
};
</script>
