import request from '@/utils/request';

/**
 * 咨询师列表
 */
export async function getConsultantList(params) {
  const res = await request.get('/mentalHealth/xljk-counselor-schedule/consultantList', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function getXljkCounselorSchedulePage(params) {
  const res = await request.get('/mentalHealth/xljk-counselor-schedule/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询全部心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
 * @param params
 * @returns {Promise<*>}
 */
export async function getXljkCounselorSchedule(params) {
  const res = await request.get('/mentalHealth/xljk-counselor-schedule', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询心理健康咨询师排班信息（权限标识：mentalHealth:xljkCounselorSchedule:list）
 * @param id
 * @returns {Promise<*>}
 */
export async function getXljkCounselorScheduleInfo(id) {
  const res = await request.get('/mentalHealth/xljk-counselor-schedule/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-counselor-schedule/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-counselor-schedule/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
