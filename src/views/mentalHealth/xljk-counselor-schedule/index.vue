<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="{ padding: '5px 0!important' }">
      <div style="margin: 10px;">
        <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
          <div>
            <el-button-group>
              <el-button @click="switchView('day')" :type="currentView === 'day' ? 'primary' : ''">日视图</el-button>
              <el-button @click="switchView('week')" :type="currentView === 'week' ? 'primary' : ''">周视图</el-button>
              <el-button @click="switchView('month')" :type="currentView === 'month' ? 'primary' : ''">月视图
              </el-button>
            </el-button-group>
            <el-button-group style="margin-left: 15px;">
              <el-button @click="prevPeriod">
                <el-icon>
                  <ArrowLeft/>
                </el-icon>
              </el-button>
              <el-button @click="today">今天</el-button>
              <el-button @click="nextPeriod">
                <el-icon>
                  <ArrowRight/>
                </el-icon>
              </el-button>
            </el-button-group>
            <span style="margin-left: 15px; font-weight: bold;">
                  {{
                currentView === 'week' ? (currentPeriodText[0] + ' 至 ' + currentPeriodText[1]) : currentPeriodText[0]
              }}
                </span>
          </div>
          <el-button @click="addEvent" type="primary">
            <el-icon size="16" style="padding-right: 5px;">
              <Plus/>
            </el-icon>
            添加事件
          </el-button>
        </div>
        <!-- 动态加载视图组件 -->
        <component :is="currentViewComponent"
                   :events="events"
                   :current-date="currentDate"
                   :page-height="pageHeight ?? 0"
                   :current-role-scope="currentRoleScope"
                   @cell-click="handleViewCellClick"
                   @edit-event="editEvent"
                   @show-schedule="handleScheduleList"/>
      </div>
    </ele-card>

    <!-- 多咨询师排班列表信息 -->
    <schedule-index v-model="showSchedule"
                    :queryDateTime="queryDateTime"
                    :setCurrentView="currentView"
                    :currentRoleScope="currentRoleScope"
                    :weekDays="weekDays"
                    :RandomString="RandomString"
                    @done="switchView"/>
    <!--添加排班信息-->
    <schedule-dialog v-model:visible="dialogVisible"
                     :dialogTitle="dialogTitle"
                     :setCurrentView="currentView"
                     :currentRoleScope="currentRoleScope"
                     :weekDays="weekDays"
                     :data="form"
                     @done="switchView"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue';
import {ArrowLeft, ArrowRight, Plus} from '@element-plus/icons-vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import { ElMessage as EleMessage} from "element-plus";
import {getXljkCounselorSchedule} from "@/views/mentalHealth/xljk-counselor-schedule/api/index.js";
import {dayJsFormatDate} from "@/utils/dayJs.js";
import {getCurrentRole} from "@/utils/current-role-util.js";
import {generateRandomString} from '@/utils/common_bak2.js';
import scheduleIndex from "./schedule/index.vue";
import ScheduleDialog from "@/views/mentalHealth/xljk-counselor-schedule/components/ScheduleDialog.vue";

// 导入视图组件
import DayView from './components/DayView.vue';
import WeekView from './components/WeekView.vue';
import MonthView from './components/MonthView.vue';
import {generateWeekDays} from "@/views/mentalHealth/utils/index.js";

const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
const currentRoleScope = ref(null);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

// 当前视图模式
const currentView = ref('day');
const viewComponents = {
  day: DayView,
  week: WeekView,
  month: MonthView
};

const currentViewComponent = computed(() => viewComponents[currentView.value]);

// 当前显示的日期范围
const currentDate = ref(new Date());
const events = ref([]);
const queryDateTime = ref({});
const RandomString = ref(null);
const showSchedule = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref('添加排班信息');
const form = ref({});

// 计算属性
const currentPeriodText = computed(() => {
  if (currentView.value === 'day') {
    return [dayJsFormatDate(currentDate.value)];
  } else if (currentView.value === 'week') {
    const start = weekDays.value[0].date;
    const end = weekDays.value[6].date;
    return [dayJsFormatDate(start), dayJsFormatDate(end)];
  } else {
    const date = new Date(currentDate.value);
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    return [dayJsFormatDate(date, 'YYYY-MM'), dayJsFormatDate(firstDay), dayJsFormatDate(lastDay)];
  }
});

//生成一周日期（周一到周日）
const weekDays = computed(() => generateWeekDays(currentDate.value))

const currentPblx = computed(() => {
  return currentView.value === 'week' ? '固定周期' : '固定日期';
});

// 方法
const switchView = (viewType) => {
  form.value = {};
  currentView.value = viewType;
  switch (viewType) {
    case 'day':
      loadDayEvents();
      break;
    case 'week':
      loadWeekEvents();
      break;
    case 'month':
      loadMonthEvents();
      break;
  }
};

const loadDayEvents = () => {
  const today = dayJsFormatDate(currentDate.value);
  queryXljkCounselorSchedule({
    startDate: today,
    endDate: today
  });
};

const loadWeekEvents = () => {
  queryXljkCounselorSchedule({
    startDate: currentPeriodText.value[0],
    endDate: currentPeriodText.value[1]
  });
};

const loadMonthEvents = () => {
  queryXljkCounselorSchedule({
    startDate: currentPeriodText.value[1],
    endDate: currentPeriodText.value[2]
  });
};

const queryXljkCounselorSchedule = (object) => {
  getXljkCounselorSchedule(object).then((list) => {
    events.value = list || [];
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

const prevPeriod = () => {
  if (currentView.value === 'day') {
    currentDate.value = new Date(currentDate.value.getTime() - 24 * 60 * 60 * 1000);
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() - 7 * 24 * 60 * 60 * 1000);
  } else {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1);
  }
};

const nextPeriod = () => {
  if (currentView.value === 'day') {
    currentDate.value = new Date(currentDate.value.getTime() + 24 * 60 * 60 * 1000);
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() + 7 * 24 * 60 * 60 * 1000);
  } else {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1);
  }
};

const today = () => {
  currentDate.value = new Date();
};

const handleViewCellClick = (data) => {
  if (currentView.value === 'day') {
    handleDayCellClick(data);
  } else if (currentView.value === 'week') {
    handleWeekCellClick(data.time, data.date);
  } else {
    handleMonthCellClick(data);
  }
};

const handleDayCellClick = (time) => {
  const [hour, minute] = time.split(':').map(Number);
  const start = new Date(currentDate.value);
  start.setHours(hour, minute, 0, 0);
  const end = new Date(start.getTime() + 30 * 60 * 1000);
  addEventWithTime(start, end);
};

const handleWeekCellClick = (time, date) => {
  const [hour, minute] = time.split(':').map(Number);
  const start = new Date(date);
  start.setHours(hour, minute, 0, 0);
  const end = new Date(start.getTime() + 30 * 60 * 1000);
  addEventWithTime(start, end);
};

const handleMonthCellClick = (date) => {
  const start = new Date(date);
  start.setHours(9, 0, 0, 0);
  const end = new Date(start.getTime() + 30 * 60 * 1000);
  addEventWithTime(start, end);
};

const handleScheduleList = ({time, date}) => {
  queryDateTime.value = {};
  if (currentView.value === 'month') {
    queryDateTime.value.startDate = dayJsFormatDate(date, 'YYYY-MM-DD');
    queryDateTime.value.endDate = dayJsFormatDate(date, 'YYYY-MM-DD');
  } else {
    const [hour, minute] = time.split(':').map(Number)
    const start = new Date(date)
    start.setHours(hour, minute, 0, 0)
    let newTime = dayJsFormatDate(start, 'YYYY-MM-DD HH:mm')
    queryDateTime.value.time = currentView.value === 'week' ? newTime : dayJsFormatDate(currentDate.value) + ' ' + time;
  }

  RandomString.value = generateRandomString(10);
  showSchedule.value = true;
};

const addEventWithTime = (start, end) => {
  dialogTitle.value = '添加排班信息';
  form.value = {
    id: null,
    kssjStr: dayJsFormatDate(start, 'HH:mm'),
    jssjStr: dayJsFormatDate(end, 'HH:mm'),
    gzrqStr: currentView.value === 'day' ? dayJsFormatDate(currentDate.value) : null,
    xqmcs: currentView.value === 'week' ? [dayJsFormatDate(start, 'dddd')] : [],
    selectedDates: currentView.value === 'month' ? [dayJsFormatDate(start)] : [],
    pblx: currentPblx.value,
    color: '#409eff'
  };
  dialogVisible.value = true;
};

const addEvent = () => {
  const now = new Date();
  const start = new Date(now);
  start.setHours(now.getHours(), 0, 0, 0);
  const end = new Date(start.getTime() + 30 * 60 * 1000);
  addEventWithTime(start, end);
};

const editEvent = (event) => {
  dialogTitle.value = '编辑排班信息';

  if (currentView.value === 'month') {
    event.selectedDates = [event?.gzrq];
  }
  if (currentView.value === 'week') {
    event.xqmcs = [event?.xqmc];
  }
  if (currentRoleScope.value !== '咨询师' && event.xgh) {
    event.consultantsInfo = [{
      "dictDataCode": event.xgh,
      "id": "e6a750b9ef8dedb31c51a1ce73059787",
      "xgh": event.xgh,
      "xm": event.xm,
      "userInfo": {
        "xgh": event.xgh,
        "xm": event.xm,
      }
    }];
    event.consultants = [event.xgh + '|' + event.xm];
  }

  form.value = event;
  dialogVisible.value = true;
};

watch(() => currentDate.value, (val) => {
  if (val) {
    switchView(currentView.value);
  }
}, {immediate: true});

onMounted(() => {
  switchView('day');
  if (currentRoles) currentRoleScope.value = currentRoles.roleScope;
});
</script>
