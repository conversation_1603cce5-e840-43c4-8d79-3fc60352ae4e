<template>
  <div class="demo-img-grid">
    <el-image v-for="(item, index) in images"
              :key="item"
              fit="cover"
              :src="item.fileUrl"
              :style="{
                    width: '144px',
                    height: '144px',
                    cursor: 'zoom-in'
                  }"
              @click="openPreview(index)"/>
  </div>
</template>

<script setup>
const props = defineProps({
  images: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['preview']);

const openPreview = (index) => {
  emit('preview', props.images, index);
};
</script>

<style lang="scss" scoped>
.demo-img-grid {
  display: grid;
  grid-gap: 3px 3px;
  grid-template-columns: repeat(4, 1fr);
  height: 100%;
  padding: 4px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);

  :deep(.el-image) {
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s;

    &:hover {
      filter: brightness(0.8);
    }
  }
}
</style>
