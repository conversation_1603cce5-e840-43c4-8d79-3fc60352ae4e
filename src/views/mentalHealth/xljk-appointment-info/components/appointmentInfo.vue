<template>
  <ele-card flex-table
            :header="dialogTitle"
            :header-style="{borderBottom: 'unset !important' }"
            :body-style="{ padding: '5px 0!important' }">
    <div class="psychology-appointment  compact-view">
      <!-- 学生基本信息区 -->
      <appoint-person-info/>
      <!-- 两栏布局 -->
      <div class="main-columns">
        <!-- 左侧列 -->
        <div class="left-column">
          <!-- 新预约区 -->
          <appointment :LastAppointmentInfo="LastAppointmentInfo"
                       :RandomString="RandomString"
                       :loadParams="loadParams"
                       :currentRoleScope="currentRoleScope"
                       @done="onReturnIndex"/>
          <!-- 温馨提醒区 -->
          <reminder/>
        </div>
        <!-- 右侧列 -->
        <div class="right-column">
          <!-- 历史预约记录区 -->
          <history :LastAppointmentInfo="LastAppointmentInfo" :RandomString="RandomString"/>
        </div>
      </div>
    </div>
  </ele-card>
</template>

<script setup>
import Reminder from "@/views/mentalHealth/xljk-appointment-info/components/childs/reminder.vue";
import Appointment from "@/views/mentalHealth/xljk-appointment-info/components/childs/appointment.vue";
import History from "@/views/mentalHealth/xljk-appointment-info/components/childs/history.vue";
import AppointPersonInfo from "@/views/mentalHealth/xljk-appointment-info/components/childs/appointPersonInfo.vue";
import {ref, watch} from "vue";

const emits = defineEmits(['done']);
const props = defineProps({
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  dialogTitle: String,
  currentRoleScope: String,
});

const loadParams = ref({})

const onReturnIndex = () => {
  emits('done')
}


watch(() => props.RandomString, (val) => {
  if (val) {
    if (props.LastAppointmentInfo) {
      loadParams.value = {
        xgh: props.LastAppointmentInfo.schedule.xgh
      };
    }
  }
}, {immediate: true, deep: true})

</script>

<style scoped>
/*.psychology-appointment {
  margin: 0 auto;
  padding: 16px; !* 减少外层padding *!
}

!* 紧凑版整体样式 *!
.compact-view {
  margin: 0 auto;
  padding: 1px;
}*/

/* 卡片间距优化 */
.el-card {
  margin-bottom: 16px !important; /* 统一减少卡片间距 */
  border-radius: 12px;
}


/* 高亮文本优化 */
.highlight {
  color: #FF9F43; /* 更温暖的橙色 */
  font-weight: 600;
}

/* 时间线优化 */
:deep(.el-timeline-item__node) {
  background-color: #FF9F43;
}

/* 表单标签优化 */
:deep(.el-form-item__label) {
  color: #666;
  font-weight: 500;
}

.mt-3 {
  margin-top: 12px;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 14px;
}

:deep(.el-alert__title) {
  font-weight: bold;
}

:deep(.el-timeline-item) {
  padding-bottom: 8px !important;
}

/* 表单标签优化 */
:deep(.el-form-item__label) {
  color: #666;
}

:deep(.el-card__header) {
  padding: 10px 16px !important;
  font-size: 14px;
}

/* 两栏布局 */
.main-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 小屏幕响应式 */
@media (max-width: 768px) {
  .main-columns {
    grid-template-columns: 1fr;
  }
}

:deep(.el-card__body ) {
  padding: 10px !important;
}

</style>
