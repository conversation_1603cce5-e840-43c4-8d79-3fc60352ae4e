<template>
  <ele-card flex-table :body-style="{ padding: '5px 0!important' }">
    <div style="margin: 10px;">
      <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <el-button-group>
            <el-button @click="switchView('day')" :type="currentView === 'day' ? 'primary' : ''">日视图
            </el-button>
            <el-button @click="switchView('week')" :type="currentView === 'week' ? 'primary' : ''">周视图
            </el-button>
            <el-button @click="switchView('month')" :type="currentView === 'month' ? 'primary' : ''">月视图
            </el-button>
          </el-button-group>
          <el-button-group style="margin-left: 15px;">
            <el-button @click="prevPeriod">
              <el-icon>
                <ArrowLeft/>
              </el-icon>
            </el-button>
            <el-button @click="today">今天</el-button>
            <el-button @click="nextPeriod">
              <el-icon>
                <ArrowRight/>
              </el-icon>
            </el-button>
          </el-button-group>
          <span style="margin-left: 15px; font-weight: bold;">{{
              currentView === 'week' ? (currentPeriodText[0] + ' 至 ' + currentPeriodText[1]) : currentPeriodText[0]
            }}</span>
        </div>
        <el-button @click="addEvent" type="primary">
          <el-icon size="16" style="padding-right: 5px;">
            <Plus/>
          </el-icon>
          我要预约
        </el-button>
      </div>

      <!-- 动态加载视图组件 -->
      <component :is="currentViewComponent"
                 :consultant-data="XljkConsultantData"
                 :events="events"
                 :weekDays="weekDays"
                 :current-date="currentDate"
                 :page-height="pageHeight"
                 :current-role-scope="currentRoleScope"
                 @show-consultant-detail="showConsultantDetail"
                 @cell-click="handleViewCellClick"/>
    </div>
  </ele-card>
  <appointment-dialog v-model:visible="dialogVisible"
                      :dialogTitle="dialogTitle"
                      :setCurrentView="currentView"
                      :currentRoleScope="currentRoleScope"
                      :currentZxsxx="currentZxsxx"
                      @done="onReturnIndex"/>
  <RecordIndex v-model="recordVisible"/>
  <consultant-detail-dialog v-model:visible="consultantDialogVisible" :data="currentConsultant"/>
</template>

<script setup>
import {ref, computed, onMounted, watch} from 'vue';
import {ArrowLeft, ArrowRight, Plus} from '@element-plus/icons-vue';
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {dayJsFormatDate} from "@/utils/dayJs.js";
import {getCurrentRole} from "@/utils/current-role-util.js";
import {getXljkConsultant} from "@/views/mentalHealth/xljk-consultant/api/index.js";
import {getCounselorScheduleList} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import AppointmentDialog from "@/views/mentalHealth/xljk-appointment-info/components/appointment-dialog.vue";
import RecordIndex from "../record/index.vue";
import ConsultantDetailDialog from './consultant-detail-dialog.vue';

// 导入视图组件
import DayView from '../../components/DayView.vue';
import WeekView from '../../components/WeekView.vue';
import MonthView from '../../components/MonthView.vue';
import {generateWeekDays} from "@/views/mentalHealth/utils/index.js";

const emits = defineEmits(['done']);
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
const currentRoleScope = ref(null);
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

// 当前视图模式
const currentView = ref('day');
const viewComponents = {
  day: DayView,
  week: WeekView,
  month: MonthView
};

const form = ref({})
const loadParams = ref({});
const weekDays = ref([])

// 当前周期文本
const currentPeriodText = computed(() => {
  if (currentView.value === 'day') {
    const date = currentDate.value
    return [dayJsFormatDate(date)]
  } else if (currentView.value === 'week') {
    const start = weekDays.value[0].date
    const end = weekDays.value[6].date
    return [dayJsFormatDate(start), dayJsFormatDate(end)]
  } else {
    const date = new Date(currentDate.value)
    const year = date.getFullYear()
    const month = date.getMonth()
    // 当月第一天
    const firstDay = new Date(year, month, 1)
    // 当月最后一天
    const lastDay = new Date(year, month + 1, 0)
    return [dayJsFormatDate(date, 'YYYY-MM'), dayJsFormatDate(firstDay), dayJsFormatDate(lastDay)]
  }
})
// 其他逻辑保持不变...
const currentDate = ref(new Date());
const events = ref([]);
const XljkConsultantData = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref('添加排班信息');
const recordVisible = ref(false);
const consultantDialogVisible = ref(false);
const currentConsultant = ref({});
const currentZxsxx = ref({});

// 计算当前视图组件
const currentViewComponent = computed(() => viewComponents[currentView.value]);

// 处理视图单元格点击
const handleViewCellClick = (data) => {
  currentZxsxx.value = null
  if (currentView.value === 'day') {
    handleDayCellClick(data);
  } else if (currentView.value === 'week') {
    handleWeekCellClick(data.date, data.zxsxx);
  } else {
    handleMonthCellClick(data);
  }
};

// 切换视图
const switchView = (viewType) => {
  form.value = {};
  currentView.value = viewType
  // 根据视图类型加载不同数据
  switch (viewType) {
    case 'day':
      const today = dayJsFormatDate(currentDate.value);
      loadParams.value = {
        ...loadParams.value,
        startDate: today,
        endDate: today
      };
      break;
    case 'week':
      //生成一周日期（周一到周日）
      weekDays.value = generateWeekDays(currentDate.value)

      loadParams.value = {
        ...loadParams.value,
        startDate: currentPeriodText.value[0],
        endDate: currentPeriodText.value[1]
      };
      break;
    case 'month':
      loadParams.value = {
        ...loadParams.value,
        startDate: currentPeriodText.value[1],
        endDate: currentPeriodText.value[2]
      };
      break;
  }
  queryXljkCounselorSchedule()
}

const queryXljkCounselorSchedule = async () => {
  if (!loadParams.value?.startDate || !loadParams.value?.endDate) {
    return;
  }
  try {
    const list = await getCounselorScheduleList(loadParams.value);
    if (list) {
      events.value = list;
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}


// 上一周期
const prevPeriod = () => {
  if (currentView.value === 'day') {
    currentDate.value = new Date(currentDate.value.getTime() - 24 * 60 * 60 * 1000)
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() - 7 * 24 * 60 * 60 * 1000)
  } else {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
  }
}

// 下一周期
const nextPeriod = () => {
  if (currentView.value === 'day') {
    currentDate.value = new Date(currentDate.value.getTime() + 24 * 60 * 60 * 1000)
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() + 7 * 24 * 60 * 60 * 1000)
  } else {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
  }
}

// 回到今天
const today = () => {
  currentDate.value = new Date()
}

// 添加事件
const addEvent = () => {
  currentZxsxx.value = null
  form.value = {};
  if (currentView.value === 'week') {
    addEventWithTime(currentPeriodText.value[0], currentPeriodText.value[1])
  } else if (currentView.value === 'month') {
    addEventWithTime(currentPeriodText.value[1], currentPeriodText.value[2])
  } else {
    addEventWithTime(currentPeriodText.value[0], currentPeriodText.value[0])
  }
}

// 处理日视图单元格点击
const handleDayCellClick = (zxsxx) => {
  currentZxsxx.value = zxsxx
  const start = new Date(currentDate.value)
  const end = new Date(start.getTime() + 60 * 60 * 1000) // 默认1小时
  addEventWithTime(start, end)
}

// 处理周视图单元格点击
const handleWeekCellClick = (date, zxsxx) => {
  currentZxsxx.value = zxsxx
  const start = new Date(date)
  const end = new Date(start.getTime() + 60 * 60 * 1000) // 默认1小时
  addEventWithTime(start, end)
}

// 处理月视图单元格点击
const handleMonthCellClick = (date) => {
  // 设置默认时间为当天9:00-10:00
  const start = new Date(date)
  const end = new Date(start)
  addEventWithTime(start, end)
}

// 添加带时间的事件
const addEventWithTime = (start, end) => {
  dialogTitle.value = '预约咨询师'
  form.value.kssjStr = dayJsFormatDate(start)
  form.value.jssjStr = dayJsFormatDate(end)
  currentZxsxx.value = {
    ...currentZxsxx.value,
    ...form.value
  }
  dialogVisible.value = true
}

const queryXljkConsultant = () => {
  //加载数据
  getXljkConsultant().then((list) => {
    if (list) XljkConsultantData.value = list
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

// 添加显示咨询师详情的方法
const showConsultantDetail = (consultant) => {
  currentConsultant.value = consultant
  consultantDialogVisible.value = true
}

const onReturnIndex = () => {
  emits('done')
}

watch(() => currentDate.value, (val) => {
  if (val) {
    switchView(currentView.value)
  }
}, {immediate: true})


onMounted(() => {
  queryXljkConsultant();
  switchView('day');
  if (currentRoles) currentRoleScope.value = currentRoles.roleScope;
});
</script>
