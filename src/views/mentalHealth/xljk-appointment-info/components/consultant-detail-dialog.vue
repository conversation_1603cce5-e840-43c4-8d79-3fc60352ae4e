<!--咨询师详情信息-->
<template>
  <el-dialog v-model="visibleDialog"
             :close-on-click-modal="false"
             draggable
             align-center
             :title="'咨询师'+currentData.xm +'的详细信息'"
             width="660px"
             class="consultant-dialog">
    <div class="consultant-detail-card">
      <!-- 顶部信息区域 -->
      <div class="top-info">
        <el-avatar :size="72"
                   :src="currentData.userInfo&&currentData.userInfo.photo?('/api/file/inline/' + JSON.parse(currentData.userInfo.photo)[0].id):''">
          {{ currentData.xm.charAt(0) }}
        </el-avatar>
        <div class="top-meta">
          <div class="name-row">
            <span class="name">{{ currentData.xm }}</span>
            <el-tag size="small" type="success" placement="left" effect="light">{{ currentData.zxslb }}</el-tag>
          </div>
          <div class="basic-info">
          <span class="info-item">
            <el-icon><User/></el-icon> {{ currentData.userInfo?.xb || '未填写' }}
          </span>
            <span class="info-item">
            <el-icon><Phone/></el-icon> {{ currentData.userInfo?.sjh || '未填写' }}
          </span>
          </div>
        </div>
        <!--        <el-button plain  size="small" type="primary" @click="handleAppoint">预约</el-button>-->
      </div>
      <el-divider/>
      <!-- 内容模块 -->
      <div class="section" v-if="currentData.scly">
        <div class="section-title">
          <IconPark name="degree-hat" size="18" strokeWidth="3"/>
          擅长领域
        </div>
        <div class="section-content">{{ currentData.scly }}</div>
      </div>

      <div class="section" v-if="currentData.cyjy">
        <div class="section-title">
          <IconPark name="time" size="18" strokeWidth="3"/>
          从业经验
        </div>
        <div class="section-content">{{ currentData.cyjy }}</div>
      </div>

      <div class="section" v-if="currentData.zzzs&&currentData.zzzs.length>0">
        <div class="section-title">
          <IconPark name="certificate" size="18" strokeWidth="3"/>
          资格证书
        </div>
        <div class="section-content">
          <img-grid :images="currentData.zzzs || []" @preview="openPreview"/>
        </div>
      </div>

      <div class="section">
        <div class="section-title">
          <IconPark name="like" size="18" strokeWidth="3"/>
          心灵寄语
        </div>
        <!--        muted-->
        <div class="section-content ">
          {{ currentData.xljy || '未填写心灵寄语' }}
        </div>
      </div>
    </div>
  </el-dialog>

  <ele-image-viewer v-model="showImageViewer"
                    :urlList="viewerImages"
                    :initialIndex="viewerIndex"
                    :infinite="false"/>
</template>

<script setup>
import {
  User,
  Phone,
  Briefcase,
  Document,
  Postcard,
  ChatLineRound
} from '@element-plus/icons-vue'
import {computed, ref, watch} from "vue";
import {isImageFile} from "@/utils/common_bak2.js";
import ImgGrid from "@/views/mentalHealth/xljk-appointment-info/components/img-grid.vue";
import IconPark from "@/components/IconPark/index.vue";

const BASE_URL = import.meta.env.BASE_URL;

let emits = defineEmits(['update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    required: true
  }
})

let visibleDialog = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
});

const closeDialog = () => {
  emits('update:visible', false)
}

// const visible = defineModel('visible')
const currentData = ref({})

/** 是否打开图片预览 */
const showImageViewer = ref(false);

/** 图片预览数据 */
const viewerImages = ref([]);

/** 图片预览初始位置 */
const viewerIndex = ref(0);

/** 打开图片预览 */
const openPreview = (images, index) => {
  console.log(images)
  viewerImages.value = images.map((d) => {
    let newArray = []
    let mark = isImageFile(d)
    d.isImageFile = mark;
    if (mark) {
      d.url = d.fileUrl
      newArray.push(d.fileUrl)
    }
    return newArray;
  })
  // viewerImages.value = images;
  viewerIndex.value = index;
  showImageViewer.value = true;
};

watch(
  () => props.visible,
  (modelValue) => {
    console.log(modelValue)
    if (modelValue && props.data) {
      console.log(props.data)
      const oldFiles = props.data.zzzs
        ? JSON.parse(props.data.zzzs).map((d, i) => {
          let newObj = {
            key: d.id,
            name: d.originalFilename,
            contentType: d.contentType,
            fileUrl: BASE_URL + 'api/file/inline/' + d.id,
            status: 'done'
          }
          let mark = isImageFile(newObj)
          newObj.isImageFile = mark;
          if (mark) newObj.url = newObj.fileUrl
          return newObj;
        }) : [];

      const roleIdArray = props.data.userInfo.roleId?.split(',') ?? [];

      currentData.value = {
        ...props.data,
        zzzs: oldFiles,
        userInfo: {
          xgh: props.data.userInfo.xgh,
          xm: props.data.userInfo.xm,
          sjh: props.data.userInfo.sjh,
          xb: props.data.userInfo.xb,
          zjhm: props.data.userInfo.zjhm,
          photo: props.data.userInfo.photo,
          roleId: props.data.userInfo.roleId,
          roleIdArray: roleIdArray
        }
      }
    }
  }
);
</script>

<style scoped>

.consultant-dialog {
  --section-gap: 16px;
  --item-gap: 10px;
  --border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.consultant-detail-card {
  padding: 10px;
  background-color: #fff;
  border-radius: 12px;
  //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

.top-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.top-meta {
  flex: 1;
}

.name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.basic-info {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #666;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.section {
  margin-bottom: 16px;
}

.section-title {
  font-weight: 600;
  color: #222;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 14px;
}

.section-content {
  padding-left: 2px;
  color: #444;
  word-break: break-word;
}

.section-content.muted {
  color: #999;
  font-style: italic;
}
</style>
