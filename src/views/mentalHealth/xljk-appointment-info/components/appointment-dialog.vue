<!--预约咨询师-->
<template>
  <ele-drawer size="64%"
              :title="dialogTitle"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="false"
              style="max-width: 100%"
              :model-value="dialogVisible"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="closeDialog()">
    <div class="psychology-appointment  compact-view">
      <!-- 学生基本信息区 -->
      <appoint-person-info/>
      <!-- 两栏布局 -->
      <div class="main-columns">
        <!-- 左侧列 -->
        <div class="left-column">
          <!-- 新预约区 -->
          <appointment :LastAppointmentInfo="LastAppointmentInfo"
                       :RandomString="RandomString"
                       :loadParams="loadParams"
                       :currentRoleScope="currentRoleScope" @done="closeDialog"/>
          <!-- 温馨提醒区 -->
          <reminder/>
        </div>
        <!-- 右侧列 -->
        <div class="right-column">
          <!-- 历史预约记录区 -->
          <history :LastAppointmentInfo="LastAppointmentInfo" :RandomString="RandomString"/>
        </div>
      </div>
    </div>
  </ele-drawer>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {useUserStore} from "@/store/modules/user.js";
import Reminder from "@/views/mentalHealth/xljk-appointment-info/components/childs/reminder.vue";
import Appointment from "@/views/mentalHealth/xljk-appointment-info/components/childs/appointment.vue";
import AppointPersonInfo from "@/views/mentalHealth/xljk-appointment-info/components/childs/appointPersonInfo.vue";
import History from "@/views/mentalHealth/xljk-appointment-info/components/childs/history.vue";
import {generateRandomString} from "@/utils/common_bak2.js";

const userStore = useUserStore();

const emits = defineEmits(['done', 'update:visible']);
const props = defineProps({
  /** 弹窗是否打开 */
  visible: Boolean,
  /** 咨询师信息数据 */
  currentZxsxx: Object,
  setCurrentView: String,
  LastAppointmentInfo: Object,
  dialogTitle: String,
  currentRoleScope: String,
});

const loadParams = ref({})
let RandomString = ref('')

let dialogVisible = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
})

const closeDialog = () => {
  loadParams.value = {}
  emits('update:visible', false)
  emits('done')
}

watch(
  () => props.visible,
  (val) => {
    if (val) {
      if (props.currentZxsxx) {
        loadParams.value = {
          startDate: props.currentZxsxx.kssjStr,
          endDate: props.currentZxsxx.jssjStr,
          xgh: props.currentZxsxx?.xgh
        };
        RandomString.value = generateRandomString(10)
      }
    }
  }
);
</script>

<style scoped>
.psychology-appointment {
  margin: 0 auto;
  padding: 16px; /* 减少外层padding */
}

/* 卡片间距优化 */
.el-card {
  margin-bottom: 16px !important; /* 统一减少卡片间距 */
  border-radius: 12px;
}

.reminder-card {
  transition: all 0.3s ease;
  margin-bottom: 0 !important; /* 去除卡片间默认间距 */
}

.reminder-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08) !important;
}

.reminder-content {
  display: flex;
  gap: 2px;
  padding: 8px 0;
}

.reminder-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 4px;
}

.reminder-card h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.reminder-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* 高亮文本优化 */
.highlight {
  color: #FF9F43; /* 更温暖的橙色 */
  font-weight: 600;
}

/* 时间线优化 */
:deep(.el-timeline-item__node) {
  background-color: #FF9F43;
}

/* 表单标签优化 */
:deep(.el-form-item__label) {
  color: #666;
  font-weight: 500;
}

.history-section,
.new-appointment,
.reminder-section {
  margin-bottom: 10px;
}

.counselor-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-3 {
  margin-top: 12px;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 14px;
}

:deep(.el-alert__title) {
  font-weight: bold;
}

:deep(.el-timeline-item) {
  padding-bottom: 8px !important;
}

/* 表单标签优化 */
:deep(.el-form-item__label) {
  color: #666;
}

/* 紧凑版整体样式 */
.compact-view {
  margin: 0 auto;
  padding: 1px;
}

:deep(.el-card__header) {
  padding: 10px 16px !important;
  font-size: 14px;
}

/* 紧凑卡片样式 */
.compact-card {
  margin-bottom: 10px !important;
  border-radius: 8px;
}

/* 两栏布局 */
.main-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 小屏幕响应式 */
@media (max-width: 768px) {
  .main-columns {
    grid-template-columns: 1fr;
  }
}

:deep(.el-card__body ) {
  padding: 10px !important;
}

</style>
