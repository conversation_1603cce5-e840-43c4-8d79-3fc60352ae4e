<template>
  <el-card class="new-appointment compact-card" style="margin-bottom: 8px!important;">
    <template #header>
      <div class="card-header">
        <div class="section-header">
          <el-icon color="#52C41A">
            <Star/>
          </el-icon>
          <span style="color: #52C41A">预约新的咨询</span>
        </div>
        <div class="header-actions">
          <el-button v-if="form.status && form.status !== '已确认'"
                     plain
                     size="small"
                     @click="xljkUpdateStatusEvent"
                     :loading="submitting">
            取消预约
          </el-button>
          <el-button type="primary"
                     plain
                     size="small"
                     @click="saveEvent"
                     :loading="submitting">
            提交预约
          </el-button>
        </div>
      </div>
    </template>
    <el-form ref="formRef"
             :model="form"
             size="small"
             label-position="top"
             @submit.prevent="">
      <el-row :gutter="6">
        <el-col :md="12">
          <template v-if="currentRoleScope!=='咨询师'">
            <el-form-item label="咨询师" prop="zxs"
                          :rules="[{
                required: true,
                message: '请选择咨询师',
                trigger: 'change',
                validator: validateZxs
              }]">
              <el-select clearable
                         v-model="form.zxs"
                         placeholder="请选择咨询师"
                         class="ele-fluid">
                <el-option v-for="field in currentConsultantData" :value="field.type"
                           @click="selectChangeZxs(field)">
                  {{ field.type }}
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-col>
        <el-col :md="12">
          <el-form-item label="咨询时间" prop="counselorScheduleId"
                        :rules="[{ required: true, message: '请选择咨询时间', trigger: 'change' }]">
            <el-select v-model="form.counselorScheduleId"
                       placeholder="请选择咨询时间"
                       clearable>
              <el-option v-for="time in sortByDateAndTime(selectedZxs.list, {
                              dateField: 'gzrq',
                              startTimeField: 'kssjStr',
                              endTimeField: 'jssjStr'
                              })"
                         :key="time.id"
                         :value="time.id"
                         :label="`${time.gzrq} ${time.kssjStr}-${time.jssjStr} [${time.kyyrs}]`">
                <template #default>
                  {{ `${time.gzrq + ' ' + time.kssjStr + '-' + time.jssjStr + ' [' + time.kyyrs + ']'}` }}
                </template>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :md="12">
          <el-form-item label="是/否紧急情况" prop="sfjjqk"
                        :rules="[{
                required: true,
                message: '请选择是/否紧急情况',
                trigger: 'change',
                validator: validateSfjjqk
              }]">
            <el-radio-group v-model="form.sfjjqk">
              <el-radio-button value="是">是</el-radio-button>
              <el-radio-button value="否">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :md="12">
          <el-form-item label="问题类型" prop="zxwtfl"
                        :rules="[{
                required: true,
                message: '请选择咨询问题类型',
                trigger: 'change',
                validator: validateZxwtfl
              }]">
            <dict-data placeholder="请选择咨询问题类型"
                       code="xlzxlxfl"
                       :dicQueryParams="{
                              getValType:'name',
                            }"
                       v-model="form.zxwtfl"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="6">
        <el-col :md="12">
          <el-form-item label="紧急联系人">
            <el-input v-model="form.jjlxr" style="width: 100%"/>
          </el-form-item>
        </el-col>
        <el-col :md="12">
          <el-form-item label="紧急联系方式">
            <el-input v-model="form.jjlxrlxfs" style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="问题描述">
        <el-input
          placeholder="请输入主要问题或困扰..."
          :rows="3"
          type="textarea"
          show-word-limit
          maxlength="255" v-model="form.wtjs" style="width: 100%"/>
      </el-form-item>
      <el-form-item label="" prop="sftyyszc"
                    :rules="[{
                required: true,
                message: '请勾选隐私政策',
                trigger: 'change',
                validator: validateSftyyszc
              }]">
        <el-checkbox v-model="form.sftyyszc"
                     style="width: 100%;font-weight: unset!important;">
          我已阅读并同意<span class="highlight" @click="showSftyyszcHtml">《心理咨询隐私政策》</span>
        </el-checkbox>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import {ref, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from "@/utils/use-form-data.js";
import {
  getCounselorScheduleList,
  getInformedConsent,
  operation,
  xljkUpdateStatus
} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import {groupArr, sortByDateAndTime} from "@/utils/common_bak2.js";
import {
  Star,
} from '@element-plus/icons-vue'

const emits = defineEmits(['done']);
const props = defineProps({
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  /** 咨询师查询信息 */
  loadParams: Object,
  currentRoleScope: String,
});

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);
/** 表单数据 */
const [form, resetFields, setFieldValue, assignFields] = useFormData({
  id: null,
  counselorScheduleId: '',//咨询师排班信息ID
  zxwtfl: '',
  wtjs: '',
  zxmd: '',
  jjlxr: '',
  jjlxrlxfs: '',
  sftyyszc: '',
  zxs: '',
  status: '',
});

/** 获取编辑器内容 */
const showSftyyszcHtml = () => {
  getInformedConsent().then((data) => {
    if (data) {
      ElMessageBox.alert(data, '心理咨询隐私政策', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '知道了',
        closeOnClickModal: true,
        center: true,
        callback: () => {
        },
        draggable: true,
        customClass: 'custom-alert-box'
      });
    }
  })
};

// 事件数据
const events = ref([])
const currentConsultantData = ref([])
const selectedZxs = ref({});

/** 更新选中数据 */
const selectChangeZxs = (data) => {
  selectedZxs.value = data;
}

const queryXljkCounselorSchedule = async () => {
  try {
    console.log(props.loadParams)
    const list = await getCounselorScheduleList(props.loadParams);
    console.log(list)
    // const list = await getCounselorScheduleList({xgh: props.LastAppointmentInfo.schedule.xgh});
    if (list) {
      // 按开始时间升序排序
      events.value = list.sort((a, b) => a.kssjStr.localeCompare(b.kssjStr));
      currentConsultantData.value = groupArr(events.value, 'xlzxEvent');
      form.zxs = null;
      selectedZxs.value = {};
      if (currentConsultantData.value.length === 1) {
        selectedZxs.value = currentConsultantData.value[0];
        form.zxs = currentConsultantData.value[0].type
      }
      console.log(currentConsultantData.value)
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}


const validateZxwtfl = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请选择咨询问题分类'))
  } else {
    callback()
  }
}
const validateSfjjqk = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请选择是/否紧急情况'))
  } else {
    callback()
  }
}
const validateZxs = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请选择咨询师'))
  } else {
    callback()
  }
}

const validateWtjs = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入主要问题或困扰'))
  } else {
    callback()
  }
}

const validateSftyyszc = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请勾选隐私政策'))
  } else {
    callback()
  }
}

const validateForm = () => {
  if (!form.counselorScheduleId) {
    EleMessage.error('请选择咨询时间')
    return false
  }
  return true
}

// 提交状态
const submitting = ref(false)
// `<i class="el-icon-warning" style="color:#E6A23C;margin-right:6px;"></i>取消预约`,
// 取消预约
const xljkUpdateStatusEvent = () => {
  ElMessageBox.prompt(
    '取消原因：',
    '您确定要取消本次心理咨询预约吗？',
    {
      dangerouslyUseHTMLString: true, // 允许 HTML
      confirmButtonText: '确认取消',
      cancelButtonText: '我再想想',
      inputPlaceholder: '取消原因（可不填）',
      draggable: true,
      // type: 'warning',
      // 移除 inputValidator 和 inputErrorMessage 即可不校验
    }
  ).then(({value}) => {
    submitting.value = true
    xljkUpdateStatus({
      appointmentInfoId: props.LastAppointmentInfo.id,
      status: '已取消',
      remark: value || '', // 用户可能不填写，这里默认空字符串
    }).then((msg) => {
      submitting.value = false
      ElMessage.success(msg);
      // reload();
    }).catch((e) => {
      submitting.value = false
      ElMessage.error(e.message);
    });
  }).catch(() => {
    // 用户点击取消或关闭弹窗
  });
};

// 保存事件
const saveEvent = async () => {
  // 验证表单
  // 首先验证整个表单
  await formRef.value.validate();
  if (!validateForm()) return
  // 添加新事件
  form.sftyyszc = form.sftyyszc ? '是' : '否'
  console.log(JSON.stringify(form))
  // if (form.consultants.length > 0) form.counselorScheduleId = form.consultants.join(',');
  loading.value = true;
  operation(form).then((res) => {
    if (res) {
      loading.value = false;
      emits('done')
      EleMessage.success('操作成功');
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    emits('done')
  });
}

watch(() => props.RandomString, (val) => {
  if (val) {
    queryXljkCounselorSchedule()
    // resetFields();
    console.log(props.LastAppointmentInfo)
    if (props.LastAppointmentInfo) {
      if (props.LastAppointmentInfo.status === '预约中') {
        setFieldValue({
          ...props.LastAppointmentInfo,
          sftyyszc: true
        });
      }
    }
  } else {
    resetFields();
    formRef["value"]?.clearValidate?.();
  }
}, {immediate: true, deep: true})

</script>

<style lang="scss" scoped>

//.new-appointment {
//  margin-bottom: 8px !important;
//}

.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 表单区优化 */
.new-appointment {
  :deep(.el-form-item) {
    margin-bottom: 10px;

    .el-form-item__label {
      font-size: 13px;
      padding-bottom: 4px;
    }
  }

  :deep(.el-radio) {
    margin-right: 8px;
  }
}

/* 高亮文本优化 */
.highlight {
  color: #FF9F43; /* 更温暖的橙色 */
  font-weight: 600;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-header {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    font-size: 14px;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

</style>
