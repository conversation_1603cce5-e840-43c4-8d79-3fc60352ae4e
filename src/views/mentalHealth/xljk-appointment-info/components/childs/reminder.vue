<template>
  <!-- 温馨提醒区 -->
  <el-card class="reminder-section compact-card">
    <template #header>
      <div class="section-header">
        <el-icon color="#FF9F43">
          <Bell/>
        </el-icon>
        <span style="color: #FF9F43">心灵小贴士</span>
      </div>
    </template>
    <!-- 重新设计的提醒卡片，使用更温暖的语言和更柔和的视觉 -->
    <div class="reminder-grid">
      <!-- 保密原则 -->
      <el-card shadow="never" class="reminder-card" style="border-left: 4px solid #67C23A">
        <div class="reminder-content">
          <el-icon class="reminder-icon" color="#67C23A">
            <Lock/>
          </el-icon>
          <div>
            <h4>你的隐私安全</h4>
            <p>
              请放心，咨询室是一个绝对安全的空间。根据心理咨询伦理准则，你的所有谈话内容都将被严格保密，我们承诺保护你的隐私。</p>
          </div>
        </div>
      </el-card>
      <!-- 勇气肯定 -->
      <el-card shadow="never" class="reminder-card" style="border-left: 4px solid #409EFF">
        <div class="reminder-content">
          <el-icon class="reminder-icon" color="#409EFF">
            <StarFilled/>
          </el-icon>
          <div>
            <h4>勇敢的第一步</h4>
            <p>
              能够主动寻求帮助是非常值得肯定的行为！这显示了你对自己心理健康的重视和改变的勇气，这本身就是一种成长。</p>
          </div>
        </div>
      </el-card>
      <!-- 咨询准备 -->
      <el-card shadow="never" class="reminder-card" style="border-left: 4px solid #E6A23C">
        <div class="reminder-content">
          <el-icon class="reminder-icon" color="#E6A23C">
            <Clock/>
          </el-icon>
          <div>
            <h4>咨询小准备</h4>
            <p>
              建议提前5分钟到达，找一个安静舒适的环境。可以提前想想你想讨论的话题，但不需要准备得太完美，自然表达就好。</p>
          </div>
        </div>
      </el-card>
      <!-- 成长过程 -->
      <el-card shadow="never" class="reminder-card" style="border-left: 4px solid #F56C6C">
        <div class="reminder-content">
          <el-icon class="reminder-icon" color="#F56C6C">
            <Opportunity/>
          </el-icon>
          <div>
            <h4>成长需要时间</h4>
            <p>
              心理成长就像照顾一株植物，需要耐心和持续的关注。不要对自己要求太高，每一次咨询都是向更好的自己迈进了一步。</p>
          </div>
        </div>
      </el-card>
    </div>
  </el-card>
</template>

<script setup>
import {Clock, Bell, Opportunity, Lock, StarFilled,} from '@element-plus/icons-vue'

</script>

<style lang="scss" scoped>
/* 紧凑卡片样式 */
.compact-card {
  margin-bottom: 10px !important;
  border-radius: 8px;
}
.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 卡片间距优化 */
.el-card {
  margin-bottom: 16px !important; /* 统一减少卡片间距 */
  border-radius: 12px;
}

.reminder-section {
  margin-bottom: 10px;
}

/* 温馨提醒网格布局 */
.reminder-section {
  .reminder-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 10px;
  }

  .reminder-card {
    transition: all 0.3s ease;
    margin-bottom: 0 !important; /* 去除卡片间默认间距 */
  }

  .reminder-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08) !important;
  }
  .reminder-content {
    display: flex;
    gap: 2px;
    padding: 8px 0;
  }

  .reminder-icon {
    font-size: 24px;
    flex-shrink: 0;
    margin-top: 4px;
  }

  .reminder-card h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-weight: 600;
  }

  .reminder-card p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.6;
  }

  .reminder-card {
    //padding: 10px;

    h4 {
      font-size: 14px;
      margin-bottom: 6px;
    }

    p {
      font-size: 12px;
      line-height: 1.5;
    }

    .reminder-icon {
      font-size: 20px;
    }
  }
}

</style>


.section-header {
display: flex;
align-items: center;
gap: 6px;
}

.section-header {
display: flex;
align-items: center;
gap: 6px;
}
