<template>
  <el-card class="history-section compact-card" style="margin-bottom: 3px!important;">
    <template #header>
      <div class="section-header">
        <el-icon>
          <Calendar/>
        </el-icon>
        <span>你的咨询历程</span>
      </div>
    </template>

    <el-card shadow="never" class="summary-card">
      <p>你已完成 <span class="highlight">{{ completedCount }}</span> 次咨询，你一共有 <span
        class="highlight">{{ completedHistoryCount }}</span> 次预约
      </p>
      <p>最近一次: {{ lastAppointmentDate || '暂无' }}</p>
    </el-card>

    <!-- 咨询记录时间轴 -->
    <compact-timeline :LastAppointmentInfo="LastAppointmentInfo"
                      :RandomString="newRandomString"
                      enterType="stuAppoint"
                      @doneAppointmentHistory="handleDoneAppointmentHistory"/>
  </el-card>
</template>

<script setup>
import {ref, computed, onMounted, reactive, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {compareTime} from "@/utils/dayJs.js";
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from "@/utils/use-form-data.js";
import {
  getCounselorScheduleList,
  getInformedConsent, getMyAppointmentInfo, getXljkAppointment,
  operation,
  removes
} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import {generateRandomString, groupArr, sortByDateAndTime} from "@/utils/common_bak2.js";
import IconPark from "@/components/IconPark/index.vue";
import {
  UserFilled,
  School,
  Message,
  Iphone,
  Calendar,
  User,
  Clock,
  Folder,
  Document,
  Star,
  Bell, Opportunity, Sunny
} from '@element-plus/icons-vue'
import CompactTimeline from "@/views/mentalHealth/xljk-appointment-info/manager/components/compactTimeline.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const props = defineProps({
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  enterType: String,//manager-咨询师管理预约记录入口
});

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
// 方法
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}

// 咨询历史数据
const appointmentHistory = ref([])
const handleDoneAppointmentHistory = (data) => {
  console.log(data)
  appointmentHistory.value = data
}

// 计算属性
const completedCount = computed(() => {
  return appointmentHistory.value.filter(item => item.status === '已完成').length
})

const completedHistoryCount = computed(() => {
  return appointmentHistory.value.length
})

const lastAppointmentDate = computed(() => {
  if (appointmentHistory.value.length === 0) return null
  const last = [...appointmentHistory.value].sort((a, b) => new Date(b.gzrq) - new Date(a.gzrq))[0]
  return formatDate(last.gzrq)
})

const newRandomString = ref(null)
watch(() => props.RandomString, (val) => {
  if (val) {
    newRandomString.value = generateRandomString(10)
  }
}, {immediate: true, deep: true})

</script>

<style lang="scss" scoped>

.history-section {
  margin-bottom: 10px;
}

/* 紧凑卡片样式 */
.compact-card {
  margin-bottom: 10px !important;
  border-radius: 8px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.summary-card {
  margin-bottom: 20px;
  background-color: #f5f7fa;
}

/* 高亮文本优化 */
.highlight {
  color: #FF9F43; /* 更温暖的橙色 */
  font-weight: 600;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

</style>
