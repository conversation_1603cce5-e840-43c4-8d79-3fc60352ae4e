<template>
  <!-- 学生基本信息区 -->
  <el-card class="student-info compact-card">
    <el-row :gutter="10" align="middle">
      <!-- 左侧头像和学生信息 -->
      <el-col :span="14">
        <div class="info-content">
          <el-avatar :size="66"
                     :src="info.photo?('/api/file/inline/' + JSON.parse(info.photo)[0].id):''">
           {{ info?.xm?.charAt(0) || '' }}
          </el-avatar>
          <div class="details">
            <div class="student-info-container">
              <div class="student-name-row">
                <h3 class="student-name">你好，<span class="highlight">{{ info.xm }}</span></h3>
                <div class="student-meta">
                  <span class="student-id">{{ info.xgh }}</span>
                </div>
              </div>

              <div class="student-contact-row">
                <div class="contact-item">
                  <el-icon>
                    <Iphone/>
                  </el-icon>
                  <span>{{ info.sjh || '未提供电话' }}</span>
                </div>
                <div class="contact-item">
                  <span>{{ info.xymc }} {{ info.zymc }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 右侧心情标记 -->
      <el-col :span="10">
        <div class="right-panel">
          <!-- 心情标记 -->
          <div class="mood-section">
            <h3 class="section-title">
              <el-icon>
                <Sunny/>
              </el-icon>
              今日心情
            </h3>
            <div class="mood-selector">
              <div class="mood-options">
                <div
                  v-for="mood in moods"
                  :key="mood.value"
                  class="mood-option"
                  :class="{ 'active': currentMood === mood.value }"
                  @click="selectMood(mood.value)">
                  <div class="mood-icon">
                    <IconPark :name="mood.icon" :color="mood.color" size="24"
                              strokeWidth="3"/>
                  </div>
                  <span class="mood-label">{{ mood.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
  <!-- 学生信息编辑对话框 -->
  <el-dialog v-model="editDialogVisible"
             title="编辑个人信息"
             width="500px">
    <el-form ref="studentForm"
             :model="studentForm"
             :rules="studentRules"
             label-width="100px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="studentForm.name"/>
      </el-form-item>

      <el-form-item label="学号">
        <el-input v-model="studentForm.studentId" disabled/>
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model="studentForm.email"/>
      </el-form-item>

      <el-form-item label="电话" prop="phone">
        <el-input v-model="studentForm.phone"/>
      </el-form-item>

      <el-form-item label="头像">
        <el-upload class="avatar-uploader"
                   action="/api/upload"
                   :show-file-list="false"
                   :on-success="handleAvatarSuccess"
                   :before-upload="beforeAvatarUpload">
          <img v-if="studentForm.avatar" :src="studentForm.avatar" class="avatar"/>
          <el-icon v-else class="avatar-uploader-icon">
            <Plus/>
          </el-icon>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="editDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="saveStudentInfo">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {ref, computed, onMounted, reactive, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {compareTime} from "@/utils/dayJs.js";
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from "@/utils/use-form-data.js";
import {
  getCounselorScheduleList,
  getInformedConsent,
  operation,
  removes
} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import {groupArr, sortByDateAndTime} from "@/utils/common_bak2.js";
import IconPark from "@/components/IconPark/index.vue";
import {
  UserFilled,
  School,
  Message,
  Iphone,
  Calendar,
  User,
  Clock,
  Folder,
  Document,
  Star,
  Bell, Opportunity, Sunny
} from '@element-plus/icons-vue'
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {info} = storeToRefs(userStore);
console.log("info====", info.value)
// 学生信息
const studentInfo = ref({
  name: '张同学',
  studentId: '20231001',
  email: '<EMAIL>',
  phone: '138****1234'
})

const editDialogVisible = ref(false)
const studentForm = ref({})
const studentFormRef = ref(null)
// 打开编辑对话框
const editStudentInfo = () => {
  studentForm.value = JSON.parse(JSON.stringify(studentInfo.value))
  editDialogVisible.value = true
}

const currentMood = ref('')

// 方法部分修改
const selectMood = (mood) => {
  currentMood.value = mood;
  saveMood();
}
// 在setup()中添加
const moods = ref([
  {value: 'happy', label: '开心', icon: 'grinning-face-with-open-mouth', color: '#F56C6C'},
  {value: 'good', label: '不错', icon: 'smiling-face', color: '#E6A23C'},
  {value: 'normal', label: '一般', icon: 'neutral-face', color: '#409EFF'},
  {value: 'bad', label: '不太好', icon: 'emotion-unhappy', color: '#909399'},
  {value: 'sad', label: '难过', icon: 'loudly-crying-face', color: '#67C23A'}
])
// 从本地存储获取心情
const loadMood = () => {
  const today = new Date().toDateString()
  const savedMood = localStorage.getItem('dailyMood')
  if (savedMood) {
    const {date, mood} = JSON.parse(savedMood)
    if (date === today) {
      currentMood.value = mood
    }
  }
}

// 保存心情
const saveMood = () => {
  const today = new Date().toDateString()
  const moodData = {
    date: today,
    mood: currentMood.value
  }
  localStorage.setItem('dailyMood', JSON.stringify(moodData))
  ElMessage.success('心情记录已保存')
}

// 学生信息验证规则
const studentRules = {
  name: [
    {required: true, message: '请输入姓名', trigger: 'blur'},
    {min: 2, max: 10, message: '长度在2到10个字符', trigger: 'blur'}
  ],
  email: [
    {required: true, message: '请输入邮箱', trigger: 'blur'},
    {type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur'}
  ],
  phone: [
    {required: true, message: '请输入电话', trigger: 'blur'},
    {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur'}
  ]
}

// 保存学生信息
const saveStudentInfo = () => {
  studentFormRef.value.validate((valid) => {
    if (valid) {
      // 模拟API请求
      setTimeout(() => {
        studentInfo.value = JSON.parse(JSON.stringify(studentForm.value))
        editDialogVisible.value = false
        ElMessage.success('个人信息更新成功')
      }, 500)
    }
  })
}

// 头像上传处理
const handleAvatarSuccess = (response) => {
  studentForm.value.avatar = response.url
  ElMessage.success('头像上传成功')
}

const beforeAvatarUpload = (file) => {
  const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPGorPNG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }

  return isJPGorPNG && isLt2M
}


// 初始化加载心情
onMounted(() => {
  loadMood()
})
</script>

<style lang="scss" scoped>

/* 紧凑卡片样式 */
.compact-card {
  margin-bottom: 10px !important;
  border-radius: 8px;
}

/* 学生信息区优化 */
.student-info {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  margin-bottom: 20px;
}

.info-content {
  display: flex;
  align-items: center;
  gap: 2px;
}

.details {
  flex: 1;
}

.highlight {
  color: #409EFF;
  font-weight: bold;
}


/* 学生信息编辑样式 */
.name-edit {
  display: flex;
  align-items: center;
  gap: 2px;
}

.edit-btn {
  padding: 0;
  height: auto;
}

.edit-btn .el-icon {
  margin-right: 4px;
}

/* 头像上传样式 */
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}


/* 学生卡片整体样式 */
.student-info {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: none;
  border-radius: 12px;
  //box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  transition: all 0.3s ease;
  overflow: hidden;

  //&:hover {
  //  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  //  transform: translateY(-2px);
  //}

  .el-row {
    align-items: stretch;
    margin: 0 !important;
  }

  :deep(.el-card__body) {
    padding: 0;
  }
}


.summary-card {
  margin-bottom: 20px;
  background-color: #f5f7fa;
}

/* 学生信息左侧区域 */
.info-content {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  padding: 20px;

  .avatar {
    flex-shrink: 0;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .details {
    flex: 1;

    .name-edit {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      gap: 10px;

      .student-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        transition: all 0.3s ease;

        .highlight {
          color: #5B8FF9;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #5B8FF9, transparent);
            opacity: 0.5;
          }
        }
      }

      .edit-btn {
        padding: 4px 8px;
        color: #718096;
        transition: all 0.3s;
        border-radius: 4px;
        background: rgba(113, 128, 150, 0.1);

        &:hover {
          color: #5B8FF9;
          background: rgba(91, 143, 249, 0.1);
          transform: translateY(-1px);
        }

        .el-icon {
          vertical-align: middle;
          margin-right: 4px;
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      //gap: 12px 16px;
      gap: 2px;

      p {
        margin: 0;
        font-size: 13px;
        color: #4a5568;
        display: flex;
        align-items: center;
        gap: 3px;
        line-height: 1.6;
        padding: 6px 0;

        .el-icon {
          color: #7c8ca5;
          font-size: 16px;
          flex-shrink: 0;
          transition: all 0.3s;
        }

        &:hover {
          .el-icon {
            color: #5B8FF9;
            transform: scale(1.1);
          }
        }
      }
    }
  }
}

/* 心情标记区域 */
.right-panel {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  padding: 0 10px;
  background: rgba(255, 255, 255, 0.8);
  border-left: 1px dashed #e2e8f0;

  .mood-section {
    width: 100%;

    .section-title {
      margin: 0 0 12px 0;
      font-size: 15px;
      color: #4a5568;
      display: flex;
      align-items: center;
      gap: 6px;

      .el-icon {
        color: #F6AD55;
        font-size: 18px;
      }
    }

    .mood-selector {
      width: 100%;
      overflow: hidden;
      margin-top: 10px;

      .mood-options {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        justify-content: space-between;
      }

      .mood-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.3s ease;
        flex: 1;
        min-width: 0;

        &:hover {
          background-color: rgba(91, 143, 249, 0.08);
        }

        &.active {
          background-color: rgba(91, 143, 249, 0.15);
        }

        .mood-icon {
          margin-bottom: 4px;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }

        .mood-label {
          font-size: 12px;
          color: #666;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .student-info {
    .el-col {
      &:first-child {
        border-bottom: 1px dashed #e2e8f0;
      }

      &:last-child {
        border-left: none;
        padding-top: 16px;
      }
    }

    .right-panel {
      border-left: none;
      border-top: 1px dashed #e2e8f0;
    }
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .right-panel {
    .mood-section {
      .mood-selector {
        .mood-options {
          gap: 6px;
        }
      }
    }
  }
}


@media (max-width: 768px) {
  .right-panel {
    .mood-section {
      .mood-selector {
        .mood-options {
          gap: 4px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .right-panel {
    padding: 12px;

    .mood-section {
      .section-title {
        font-size: 14px;
        margin-bottom: 10px;
      }

      .mood-selector {
        .mood-options {
          gap: 4px;
        }

        .mood-option {
          padding: 6px 2px;

          .mood-icon {
            transform: scale(0.9);
          }

          .mood-label {
            font-size: 11px;
          }
        }
      }
    }
  }
}

.student-info-container {
  flex: 1;
  min-width: 0;

  .student-name-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;

    .student-name {
      margin: 0;
      font-size: 18px;
      color: #1e293b;
    }

    .student-meta {
      .student-id {
        font-size: 13px;
        color: #64748b;
      }
    }
  }

  .student-contact-row {
    margin-top: 18px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 13px;
    color: #475569;

    .contact-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        color: #94a3b8;
        font-size: 14px;
      }
    }
  }
}
</style>
