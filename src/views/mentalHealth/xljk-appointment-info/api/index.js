import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getCounselorScheduleList(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/counselorScheduleList', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 我的正式咨询预约记录
 * @param params
 * @returns {Promise<*>}
 */
export async function getMyAppointmentInfo(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/myAppointmentInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 心理咨询预约知情同意书
 * @param params
 * @returns {Promise<*>}
 */
export async function getInformedConsent() {
  const res = await request.get('/mentalHealth/xljk-param/informedConsent');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 我的最近一条预约记录
 * @returns {Promise<*>}
 */
export async function getMyLastAppointmentInfo() {
  const res = await request.get('/mentalHealth/xljk-appointment-info/myLastAppointmentInfo');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 我的预约记录
 * @param params
 * @returns {Promise<*>}
 */
export async function queryMyAppointmentInfo(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/myAppointmentInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getPageDataByUrl(dictionaryFieldUrl, params) {
  const res = await request.get(dictionaryFieldUrl, {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 管理端查询预约记录
 * @param params
 * @returns {Promise<*>}
 */
export async function getAppointmentInfoPage(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询正式咨询预约者记录（权限标识：mentalHealth:xljkAppointmentInfo:list）
 * 根据人分组
 * @param params
 * @returns {Promise<*>}
 */
export async function getAppointmentInfoPagePerson(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/pagePerson', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkAppointment(params) {
  const res = await request.get('/mentalHealth/xljk-appointment-info', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkAppointmentInfo(id) {
  const res = await request.get('/mentalHealth/xljk-appointment-info/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-appointment-info/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 预约记录状态变更
 * @param data
 * @returns {Promise<*>}
 */
export async function xljkUpdateStatus(data) {
  const res = await request.post('/mentalHealth/xljk-appointment-info/updateStatus', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-appointment-info/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
