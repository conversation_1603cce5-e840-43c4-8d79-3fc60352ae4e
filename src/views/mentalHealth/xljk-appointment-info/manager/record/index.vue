<template>
  <ele-page hide-footer flex-table :style="{paddingTop:'8px!important'}">
    <ele-card :style="{marginBottom:'0px!important'}"
              :body-style="{ padding: '13px 5px 0 5px !important' }">
      <!-- 操作按钮区域 -->
      <div style="margin-bottom: 12px; display: flex; justify-content: flex-start; gap: 8px;">
        <el-button type="primary" size="small" @click="handleAdd">添加回访记录</el-button>

        <!--    <el-button type="success" size="small" @click="handleImport">批量导入</el-button>-->
        <!--    <el-button type="info" size="small" @click="handleExport">导出数据</el-button>-->
      </div>
    </ele-card>
    <record-list :RandomString="RandomString" enterType="makedSure"/>

    <FollowUpEdit v-model="showFollowUpEdit" :RandomString="RandomString"/>
  </ele-page>
</template>

<script setup>
import {onMounted, ref,} from 'vue';
import RecordList from "@/views/mentalHealth/xljk-appointment-info/manager/components/RecordList.vue";
import {generateRandomString} from "@/utils/common_bak2.js";
import FollowUpEdit from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/components/edit.vue";

const RandomString = ref(null)
const showFollowUpEdit = ref(false)
const handleAdd = () => {
  showFollowUpEdit.value = true;
  RandomString.value = generateRandomString(10)
}

onMounted(() => {
  RandomString.value = generateRandomString(10)
})

</script>

<script>
export default {
  name: 'MANAGERRECORDLIST'
};
</script>
