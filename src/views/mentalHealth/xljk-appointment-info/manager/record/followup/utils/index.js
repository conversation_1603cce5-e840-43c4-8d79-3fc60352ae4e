export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      label: '被回访人',
      type: 'ZxrPersonInfo',
      // typeKey: 'select',
      // minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      // props: {
      //   code: "ZxrPersonInfo",
      //   filterable: true,
      //   dicQueryParams: {
      //     dictFieldUrl: "/mentalHealth/xljk-appointment-info/pagePerson",
      //     valueField: "xgh",
      //     textField: "xm"
      //   }
      // }
    },
    {
      prop: 'hfsj',
      label: '回访时间',
      type: 'datetime',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: {span: 24}
    },
    {
      prop: 'hffs',
      label: '回访方式',
      type: 'dictSelect',
      typeKey: 'select',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      props: {
        code: "xlzxhffs",
        filterable: true,
        dicQueryParams: {
          getValType: 'name'
        },
      }
    },
    {
      prop: 'ydsrgtqk',
      label: '与当事人沟通情况',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
  ]
}
