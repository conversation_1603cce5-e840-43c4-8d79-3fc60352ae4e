<template>
  <ele-drawer size="50%"
              title="回访历史记录"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <!-- 回访历史记录时间轴 -->
    <el-timeline class="compact-timeline">
      <el-timeline-item
        v-for="(interview, index) in interviewRecords"
        :key="interview.id"
        :timestamp="formatFullDate(interview.hfsj)"
        placement="top">
        <el-card class="timeline-card">
          <div class="record-header">
            <div class="record-title">
              <span>回访方式: {{ interview.hffs }}</span>
            </div>
            <div class="status-with-icon status-danger" @click="handleDelete(interview)">
              <el-icon :size="14">
                <Delete/>
              </el-icon>
              <span>删除</span>
            </div>
          </div>
          <div class="record-basic">
            <div class="basic-item">
              <span>回访人: {{ interview.czr }}</span>
            </div>
            <div class="basic-item">
              <span>与当事人沟通情况: {{ interview.ydsrgtqk || '未填写' }}</span>
            </div>
            <!--          <div class="basic-item full-width">-->
            <!--            <div class="text-content">问题描述: {{ interview.wtjs || '未填写问题描述' }}</div>-->
            <!--          </div>-->
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </ele-drawer>
</template>
<script setup>

import {ref, watch} from "vue";
import {getXljkFollowupRecord} from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/api/index.js";
import {Delete} from "@element-plus/icons-vue";
import {removes} from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/api/index.js";
import {ElMessageBox} from "element-plus/es";
import {ElLoading} from "element-plus";

const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  enterType: String,//manager-咨询师管理预约记录入口
});

const currentEnterType = ref(null)
const formatFullDate = (dateStr) => {
  const date = new Date(dateStr)
  const week = ['日', '一', '二', '三', '四', '五', '六']
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 周${week[date.getDay()]}`
}


/**
 * 访谈记录
 */
const interviewRecords = ref([])

const queryXljkFollowupRecord = async () => {
  try {
    const list = await getXljkFollowupRecord({xgh: props.LastAppointmentInfo.xgh});
    if (list) {
      // list.map(item => {
      //   item.counselor = item.schedule.xm
      //   item.gzrq = item.schedule.gzrq
      //   item.gzrqStr = item.schedule.kssjStr + '-' + item.schedule.jssjStr
      // })
      interviewRecords.value = list
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}

const handleDelete = (item) => {
  console.log(item)
  const row = item;
  if (!row) {
    return;
  }
  ElMessageBox.confirm(
    `确定要删除“${formatFullDate(row.hfsj)}”的回访记录吗?`,
    {
      type: 'warning',
      draggable: true
    }).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes([row.id]).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      queryXljkFollowupRecord()
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
}

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(() => props.RandomString, (val) => {
  if (val) {
    currentEnterType.value = props.enterType;
    console.log(props.LastAppointmentInfo)
    if (props.LastAppointmentInfo) {
      queryXljkFollowupRecord();
    }
  }
}, {immediate: true, deep: true})

</script>

<style scoped lang="scss">

/* 时间轴样式 */
.compact-timeline {
  padding-left: 0;
  margin: 0;

  :deep(.el-timeline-item__node ) {
    background-color: #FF9F43;
  }

  :deep(.el-timeline-item) {
    padding-bottom: 1px !important;

    &:last-child {
      padding-bottom: 0;
    }

    &.has-risk {
      :deep(.el-timeline-item__node) {
        background-color: #f56c6c;
        border-color: #f56c6c;
      }
    }
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 14px;
    color: #475569;
    padding-bottom: 6px;
  }
}

/* 记录卡片样式 */
.timeline-card {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  margin-bottom: 4px;

  &.risk-highlight {
    border-left: 3px solid #f56c6c;
  }

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .record-title {
      .record-index {
        font-size: 15px;
        color: #1e293b;
        margin-right: 12px;
      }

      .record-time {
        font-size: 13px;
        color: #64748b;
      }
    }

    .el-tag {
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }

  .record-basic {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 13px;

    .basic-item {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      color: #475569;
      line-height: 1.5;

      &.full-width {
        width: 100%;
      }

      .el-icon {
        color: #94a3b8;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .text-content {
        flex: 1;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .record-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    //border-top: 1px dashed #e2e8f0;
  }

  .record-actions {
    .el-button {
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}


.status-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  cursor: pointer;

  &.status-info {
    color: var(--el-color-info);
  }

  &.status-primary {
    color: var(--el-color-primary);
  }

  &.status-success {
    color: var(--el-color-success);
  }

  &.status-warning {
    color: var(--el-color-warning);
  }

  &.status-danger {
    color: var(--el-color-danger);
  }
}
</style>
