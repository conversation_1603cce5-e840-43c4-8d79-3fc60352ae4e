<!-- 添加回访记录 -->
<template>
  <ele-drawer size="50%"
              title="添加回访记录"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <pro-form ref="formRef"
              size="small"
              :model="form"
              :items="formItems"
              :grid="{ span: 24 }"
              labelWidth="auto"
              label-position="top"
              @updateValue="setFieldValue">
      <template #ZxrPersonInfo="{ item, model, updateValue }">
        <ele-text v-if="isUpdate" readonly>
          {{ form.xm }}
        </ele-text>
        <TableMultiple v-else :RandomString="RandomString"
                       :value="model[item.prop]"
                       @change="val => model[item.prop] = val"/>
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from "../api/index.js";
import {comColumns} from "../utils/index.js";
import {generateForm} from '@/utils/common_bak2.js';
import ProForm from "@/components/ProForm/index.vue";
import TableMultiple
  from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/components/table-multiple.vue";

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  RandomString: String,
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
});


/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
});

const formItems = ref([])

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    let data = {...form}
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      let baseColumns = comColumns() || [];
      formItems.value = baseColumns.filter(item => item.prop && !item.disabled);
      const formInitData = generateForm(formItems.value)
      if (props.LastAppointmentInfo) {
        assignFields({...formInitData, xgh: props.LastAppointmentInfo.xgh, xm: props.LastAppointmentInfo.xm});
        isUpdate.value = true;
      } else {
        resetFields(formInitData); // 一次性reset新的字段
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formItems.value = [];
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
<style>
.el-date-editor {
  width: 100% !important;
}
</style>
