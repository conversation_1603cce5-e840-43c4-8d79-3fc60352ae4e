<template>
  <ele-page hide-footer flex-table class="appointment-layout">
    <div class="main-container">
      <!-- 左侧预约列表 -->
      <div class="left-panel">
        <div class="search-filter">
          <el-input v-model="searchQuery" placeholder="搜索学生姓名/学号"
                    clearable
                    @clear="handleSearchClear"
                    @keyup.enter="handleSearch">
            <template #append>
              <el-button :icon="Search" @click="handleSearch" />
            </template>
          </el-input>
          <el-date-picker v-model="dateRange"
                          type="date"
                          @change="handleDateChange"
                          class="date-picker" />
        </div>
        <el-scrollbar class="scrollbar-container">
          <scrollbar-index :enterTab="activeTab"
                           :RandomString="RandomString"
                           @update:activeAppointment="doUpdateActiveAppointment" />
        </el-scrollbar>
      </div>

      <!-- 右侧详情区域 -->
      <div class="right-panel">
        <div v-if="activeAppointment" class="detail-container">
          <history-base :LastAppointmentInfo="activeAppointment"
                        :RandomString="RandomString"
                        :SetPageHeight="(pageHeight-150)"
                        enterType="manager" />
        </div>
        <div v-else class="empty-state">
          <el-empty description="请从左侧选择预约记录查看详情" />
        </div>
      </div>
    </div>
  </ele-page>
</template>

<script setup>
import {ref, onMounted,} from 'vue'
import {Search} from '@element-plus/icons-vue'
import {useUserStore} from "@/store/modules/user.js"
import {storeToRefs} from "pinia"
import {generateRandomString,} from "@/utils/common_bak2.js";
import ScrollbarIndex from "@/views/mentalHealth/xljk-appointment-info/manager/components/scrollbarIndex.vue";
import HistoryBase from "@/views/mentalHealth/xljk-appointment-info/manager/components/historyBase.vue";

const userStore = useUserStore()
const {pageHeight} = storeToRefs(userStore)

const activeAppointment = ref(null)

const RandomString = ref(null)
const handleTabChange = (tabName) => {
  if (tabName) {
    activeTab.value = tabName
    RandomString.value = generateRandomString(10)
  }
}

const doUpdateActiveAppointment = (data) => {
  activeAppointment.value = data;
}

// 状态管理
const activeTab = ref()

const tableTabs = ref([{
    name: 'pending',
    label: '待确认',
  },
    {
      name: 'makedSure',
      label: '咨询管理',
    }]
)

const searchQuery = ref('')
const dateRange = ref([])
const loading = ref(false)

const handleSearchClear = () => {
  searchQuery.value = ''
}

// 搜索和日期变化时重置
const handleSearch = () => {
  resetLoad()
}

const handleDateChange = () => {
  resetLoad()
}

const resetLoad = () => {

}
onMounted(() => {
  handleTabChange('pending')
})

</script>

<style scoped lang="scss">
.appointment-layout {
  height: 100%;
  display: flex;
  flex-direction: column;

  .main-container {
    flex: 1;
    display: grid;
    grid-template-columns: 5fr 19fr;
    gap: 8px;
    height: 100%;
    overflow: hidden;
  }

  .left-panel {
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    padding: 12px;
    overflow: hidden;

    .search-filter {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 6px;

      .date-picker {
        width: 100%;
      }

      :deep(.el-input),
      :deep(.el-date-editor) {
        width: 100%;
      }
    }

    .scrollbar-container {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;

      &::-webkit-scrollbar {
        width: 0;
      }
    }
  }

  .right-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .detail-container {
      flex: 1;
      overflow-y: auto;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
      padding: 10px;

      &::-webkit-scrollbar {
        width: 0;
      }
    }

    .empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    }
  }
}
</style>
