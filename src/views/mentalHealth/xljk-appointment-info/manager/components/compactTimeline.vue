<template>
  <!-- 咨询记录时间轴 -->
  <el-timeline class="compact-timeline" :style="{height:currentPageHeight+'px',overflow:'auto'}">
    <el-timeline-item
      v-for="(record, index) in appointmentHistory"
      :key="record.id"
      :timestamp="formatFullDate(record.gzrq)"
      placement="top"
      :class="{'has-risk': hasRisk(record)}">
      <el-card class="timeline-card" :class="{'risk-highlight': hasRisk(record)}">
        <div class="record-header">
          <div class="record-title">
            <span class="record-index"><el-tag type="info">第{{
                appointmentHistory.length - index
              }}次咨询</el-tag></span>
            <span class="record-time">{{ record.gzrqStr }}</span>
          </div>
          <div class="status-with-icon" :class="`status-${getStatusType(record.status)}`">
            <el-icon :size="14">
              <component :is="getStatusIcon(record.status)"/>
            </el-icon>
            <span>{{ record.status }}</span>
          </div>
        </div>

        <!-- 咨询基本信息 -->
        <div class="record-basic">
          <div class="basic-item">
            <el-icon>
              <User/>
            </el-icon>
            <span>咨询师: {{ record.counselor }}</span>
          </div>
          <div class="basic-item">
            <el-icon>
              <Folder/>
            </el-icon>
            <span>问题类型: {{ record.zxwtfl || '未填写' }}</span>
          </div>
          <div class="basic-item full-width">
            <el-icon>
              <Document/>
            </el-icon>
            <div class="text-content">问题描述: {{ record.wtjs || '未填写问题描述' }}</div>
          </div>
        </div>

        <!-- 访谈记录部分 - 完全展开 -->
        <div v-if="matchInterviewRecords(record.id).length > 0" class="interview-section">
          <div class="section-title">访谈记录</div>
          <div v-for="(interview, i) in matchInterviewRecords(record.id)" :key="i" class="interview-item">
            <div class="interview-header">
              <div class="interview-title">
                <span class="interview-date">{{ formatDateTime(interview.updateTime) }}</span>
                <div class="interview-risk-tags">
                  <el-tag v-if="interview.sfczzszsfx === '是'" size="small" type="danger">自伤风险</el-tag>
                  <el-tag v-if="interview.sfczshtrfx === '是'" size="small" type="warning">伤人风险</el-tag>
                  <el-tag v-if="interview.sfxygj === '是'" size="small" type="info">需跟进</el-tag>
                </div>
              </div>
            </div>
            <div class="interview-content">
              <div class="interview-row">
                <div class="interview-label">主诉问题:</div>
                <div class="interview-text">{{ interview.zswt }}</div>
              </div>
              <div class="interview-row" v-if="interview.gcypg">
                <div class="interview-label">观察评估:</div>
                <div class="interview-text">{{ interview.gcypg }}</div>
              </div>
              <div class="interview-row" v-if="interview.cydzxffhjs">
                <div class="interview-label">咨询方法:</div>
                <div class="interview-text">{{ interview.cydzxffhjs }}</div>
              </div>
              <div class="interview-row" v-if="interview.zxgc">
                <div class="interview-label">咨询过程:</div>
                <div class="interview-text">{{ interview.zxgc }}</div>
              </div>
              <div class="interview-row" v-if="interview.zjybz">
                <div class="interview-label">总结备注:</div>
                <div class="interview-text">{{ interview.zjybz }}</div>
              </div>
              <div class="interview-row" v-if="interview.bycs">
                <div class="interview-label">必要措施:</div>
                <div class="interview-text">{{ interview.bycs }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="enterType!=='stuAppoint'" class="record-actions">
          <el-button v-if="record.status === '预约中'"
                     size="small"
                     type="danger"
                     plain
                     @click="handleCancel(record)">
            取消预约
          </el-button>
          <el-button v-if="record.status === '预约中'"
                     size="small"
                     type="success"
                     plain
                     @click="handleConfirm(record)">
            确认预约
          </el-button>
          <el-button v-if="record.status === '已确认'"
                     plain
                     type="primary"
                     size="small"
                     @click="handleConsultationDialog(record)">
            添加记录
          </el-button>
        </div>
      </el-card>
    </el-timeline-item>
  </el-timeline>

  <!-- 访谈记录弹窗 -->
  <xljk-counseling-record v-model:visible="dialogVisible"
                          :RandomString="newRandomString"
                          :enterType="enterType"
                          :LastAppointmentInfo="activeConsultationData"
                          @done="handleComplete"/>
</template>
<script setup>

import {Document, Folder, User} from "@element-plus/icons-vue";
import { ref, watch} from "vue";
import {
  getMyAppointmentInfo,
  getXljkAppointment,
  xljkUpdateStatus
} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import { ElMessage as EleMessage} from "element-plus";
import {getCounselingRecord} from "@/views/mentalHealth/xljk-counseling-record/api/index.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {generateRandomString} from "@/utils/common_bak2.js";
import XljkCounselingRecord from "@/views/mentalHealth/xljk-counseling-record/index.vue";

const emits = defineEmits(['doneAppointmentHistory']);
const props = defineProps({
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  currentPageHeight: Number,
  enterType: String,//manager-咨询师管理预约记录入口
});

const currentEnterType = ref(null)
const formatFullDate = (dateStr) => {
  const date = new Date(dateStr)
  const week = ['日', '一', '二', '三', '四', '五', '六']
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 周${week[date.getDay()]}`
}

const hasRisk = (record) => {
  const interviews = matchInterviewRecords(record.id)
  return interviews.some(i => i.sfczzszsfx === '是' || i.sfczshtrfx === '是')
}

const getStatusIcon = (status) => {
  switch (status) {
    case '预约中':
      return 'Clock';
    case '已确认':
      return 'CircleCheck';
    case '已完成':
      return 'SuccessFilled';
    case '已取消':
      return 'CloseBold';
    default:
      return 'InfoFilled';
  }
}

const getStatusType = (status) => {
  switch (status) {
    case '预约中':
      return 'info';       // 蓝色
    case '已确认':
      return 'primary';    // 紫色
    case '已完成':
      return 'success';    // 绿色
    case '已取消':
      return 'warning';    // 橙色
    default:
      return '';
  }
};

/**
 * 根据预约信息ID，获取对应的访谈记录
 * @param appointmentInfoId
 * @returns {never[]|*[]}
 */
const matchInterviewRecords = (appointmentInfoId) => {
  if (!Array.isArray(interviewRecords.value)) return [];
  return interviewRecords.value.filter(item => item.appointmentInfoId === appointmentInfoId);
};

// 咨询历史数据
const appointmentHistory = ref([])

/**
 * 访谈记录
 */
const interviewRecords = ref([])
const queryCounselingRecord = async () => {
  try {
    const list = await getCounselingRecord({'appointmentInfo.xgh': props.LastAppointmentInfo.xgh});
    if (list) {
      interviewRecords.value = list
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}

const queryXljkAppointment = async () => {
  try {
    const list = await getXljkAppointment({xgh: props.LastAppointmentInfo.xgh});
    if (list) {
      list.map(item => {
        item.counselor = item.schedule.xm
        item.gzrq = item.schedule.gzrq
        item.gzrqStr = item.schedule.kssjStr + '-' + item.schedule.jssjStr
      })
      appointmentHistory.value = list
      emits('doneAppointmentHistory', appointmentHistory.value)
      await queryCounselingRecord()
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}

const handleComplete = (enterType) => {
  currentEnterType.value = enterType;
  refreshData()
};

/**
 * 预约记录
 */
const queryMyAppointmentInfo = async () => {
  try {
    const list = await getMyAppointmentInfo();
    if (list) {
      list.map(item => {
        item.counselor = item.schedule.xm
        item.gzrq = item.schedule.gzrq
        item.gzrqStr = item.schedule.kssjStr + '-' + item.schedule.jssjStr
      })
      appointmentHistory.value = list
      emits('doneAppointmentHistory', appointmentHistory.value)
      await queryCounselingRecord()
    }
  } catch (e) {
    EleMessage.error(e.message || 'Failed to load schedule data');
  }
}

const handleCancel = (record) => {
  ElMessageBox.confirm('确定要取消此预约吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    xljkUpdateStatus(
      {
        appointmentInfoId: record.id,
        status: '已取消',
      }
    ).then(() => {
      ElMessage.success('预约取消成功');
      refreshData();
    });
  }).catch(() => {
    ElMessage.info('已取消操作');
  });
};

const handleConfirm = (record) => {
  ElMessageBox.confirm('确定要确认此预约吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 调用确认预约的API
    xljkUpdateStatus(
      {
        appointmentInfoId: record.id,
        status: '已确认',
      }
    ).then(() => {
      ElMessage.success('预约确认成功');
      refreshData();
    });
  }).catch(() => {
    ElMessage.info('已取消操作');
  });
};

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

const refreshData = () => {
  if (currentEnterType.value === 'manager') {
    queryXljkAppointment();
  } else {
    queryMyAppointmentInfo();
  }
};

const dialogVisible = ref(false)
const activeConsultationData = ref({})
const newRandomString = ref(null)
const handleConsultationDialog = (record) => {
  activeConsultationData.value = record
  // 跳转到详情页面或打开详情弹窗
  console.log('查看详情:', record);
  // emit('updateConsultationDialog', record);
  newRandomString.value = generateRandomString(10);
  dialogVisible.value = true;
  // 这里可以根据实际需求实现详情查看逻辑
};

watch(() => props.RandomString, (val) => {
  if (val) {
    currentEnterType.value = props.enterType;
    if (props.LastAppointmentInfo) {
      if (currentEnterType.value === 'manager') {
        queryXljkAppointment();
      } else {
        queryMyAppointmentInfo();

      }
    }
  }
}, {immediate: true, deep: true})

</script>

<style scoped lang="scss">

/* 时间轴样式 */
.compact-timeline {
  padding-left: 0;
  margin: 0;

  :deep(.el-timeline-item__node ) {
    background-color: #FF9F43;
  }

  :deep(.el-timeline-item) {
    padding-bottom: 1px !important;

    &:last-child {
      padding-bottom: 0;
    }

    &.has-risk {
      :deep(.el-timeline-item__node) {
        background-color: #f56c6c;
        border-color: #f56c6c;
      }
    }
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 14px;
    color: #475569;
    padding-bottom: 6px;
  }
}

/* 记录卡片样式 */
.timeline-card {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  margin-bottom: 4px;

  &.risk-highlight {
    border-left: 3px solid #f56c6c;
  }

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .record-title {
      .record-index {
        font-size: 15px;
        color: #1e293b;
        margin-right: 12px;
      }

      .record-time {
        font-size: 13px;
        color: #64748b;
      }
    }

    .el-tag {
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }

  .record-basic {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 13px;

    .basic-item {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      color: #475569;
      line-height: 1.5;

      &.full-width {
        width: 100%;
      }

      .el-icon {
        color: #94a3b8;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .text-content {
        flex: 1;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .record-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    //border-top: 1px dashed #e2e8f0;
  }

  .record-actions {
    .el-button {
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 访谈记录部分 */
.interview-section {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #e2e8f0;

  .section-title {
    font-size: 14px;
    color: #475569;
    margin-bottom: 12px;
  }

  .interview-item {
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;

    &:last-child {
      margin-bottom: 0;
    }

    .interview-header {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #e2e8f0;

      .interview-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .interview-date {
          font-size: 13px;
          color: #475569;
        }

        .interview-risk-tags {
          display: flex;
          gap: 6px;
        }
      }
    }

    .interview-content {
      .interview-row {
        display: flex;
        margin-bottom: 8px;
        font-size: 13px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }

        .interview-label {
          width: 70px;
          color: #64748b;
          flex-shrink: 0;
        }

        .interview-text {
          flex: 1;
          color: #475569;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .interview-content .interview-row {
    flex-direction: column;
    gap: 2px;

    .interview-label {
      width: auto;
    }
  }
}

.status-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  cursor: pointer;

  &.status-info {
    color: var(--el-color-info);
  }

  &.status-primary {
    color: var(--el-color-primary);
  }

  &.status-success {
    color: var(--el-color-success);
  }

  &.status-warning {
    color: var(--el-color-warning);
  }

  &.status-danger {
    color: var(--el-color-danger);
  }
}

</style>
