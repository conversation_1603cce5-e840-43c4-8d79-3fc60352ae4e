<template>
  <el-scrollbar
    :height="pageHeight - 160"
    @scroll="handleScroll"
    ref="scrollbarRef">
    <div class="appointment-cards">
      <ele-card v-for="appointment in appointmentsPending"
                :key="appointment.id"
                shadow="hover"
                :class="{ 'active-card':enterTab==='pending'?( activeAppointment?.id === appointment.id ):( activeAppointment?.xgh === appointment.xgh)}"
                :body-style="{padding: '1px!important'}"
                @click="selectAppointment(appointment)">
        <div class="card-content-wrapper">
          <!-- 状态标签区域 - 现在放在卡片内容顶部 -->
          <div class="status-tags">
            <!-- 紧急状态 -->
            <el-tag v-if="enterTab==='pending'&&appointment.sfjjqk === '是'"
                    type="danger"
                    size="small"
                    class="status-tag">
              紧急
            </el-tag>
            <el-tag v-else-if="enterTab==='makedSure'"
                    type="danger"
                    size="small"
                    class="status-tag">
              {{ appointment.total }} 次
            </el-tag>
          </div>
          <div class="appointment-card-content">
            <div class="student-basic">
              <span class="student-name">{{ appointment.xm }}</span>
              <span class="student-id">{{ appointment.xgh }}</span>
            </div>
            <div class="appointment-time">
                          <span v-if="enterTab==='pending'">{{ appointment.schedule?.gzrq }} {{
                              appointment.schedule?.kssjStr
                            }}-{{ appointment.schedule?.jssjStr }}</span>
              <span v-else>最近一次：{{ appointment.createTime }}</span>
            </div>
          </div>
        </div>
      </ele-card>
      <!-- 加载状态提示 -->
      <div v-if="loading" class="loading-more">
        <el-icon class="is-loading">
          <Loading/>
        </el-icon>
        <span>加载中...</span>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="noMoreData && appointmentsPending.length > 0" class="no-more-data">
        <span>没有更多数据了</span>
      </div>

      <!-- 初始无数据提示 -->
      <div v-if="!loading && appointmentsPending.length === 0" class="empty-data">
        <el-empty description="暂无预约数据"/>
      </div>
    </div>
  </el-scrollbar>
</template>
<script setup>

import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {nextTick, onBeforeUnmount, onMounted, ref, watch} from "vue";
import {getPageDataByUrl} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import { ElMessage as EleMessage} from "element-plus";

const emit = defineEmits(['update:activeAppointment']);
const userStore = useUserStore()
const {pageHeight} = storeToRefs(userStore)

const props = defineProps({
  enterTab: String,
  RandomString: String,
});

// 新增 scrollbar 引用
const scrollbarRef = ref(null)
const loadDataUrl = ref(null)

// 状态管理
const totalCount = ref(0) // 新增总条数
const activeAppointment = ref(null)
const dateRange = ref([])
const loading = ref(false)
const noMoreData = ref(false)
const currentPage = ref(1)
const pageSize = 20
const appointmentsPending = ref([])

// 方法
const selectAppointment = (appointment) => {
  activeAppointment.value = appointment
  emit('update:activeAppointment', activeAppointment.value)
}

// 初始化数据
const initLoad = async () => {
  loading.value = true;
  try {
    let newObject = {
      page: currentPage.value,
      limit: pageSize
    }
    if (props.enterTab === 'pending') newObject.status = '预约中'
    const result = await getPageDataByUrl(loadDataUrl.value, newObject);
    appointmentsPending.value = result.list || [];
    totalCount.value = result.count || 0;

    noMoreData.value = appointmentsPending.value.length >= totalCount.value;

    if (appointmentsPending.value.length > 0) {
      selectAppointment(appointmentsPending.value[0]);
    }

    await checkLoadMore();
  } catch (e) {
    EleMessage.error(e.message || '加载失败');
  } finally {
    loading.value = false;
  }
};

// 重置加载状态
const resetLoad = () => {
  currentPage.value = 0
  noMoreData.value = false
  totalCount.value = 0
  appointmentsPending.value = []
  initLoad()
}

// 检查是否需要立即加载更多数据
const checkLoadMore = async () => {
  await nextTick();
  if (!scrollbarRef.value) return;
  // 使用 requestAnimationFrame 确保在浏览器下一次重绘前执行
  requestAnimationFrame(async () => {
    const wrap = scrollbarRef.value.$el.querySelector('.el-scrollbar__wrap');
    if (!wrap) return;
    // 添加调试信息
    console.log('ScrollHeight:', wrap.scrollHeight, 'ClientHeight:', wrap.clientHeight);
    // 增加 2px 容差防止小数计算问题
    const hasSpace = wrap.scrollHeight <= wrap.clientHeight + 2;
    if (hasSpace && !noMoreData.value && !loading.value) {
      await loadMoreAppointments();
      await checkLoadMore(); // 递归检查直到填满
    }
  });
};

// 滚动处理
const handleScroll = async ({scrollTop}) => {
  if (!scrollbarRef.value || loading.value || noMoreData.value) return;
  const wrap = scrollbarRef.value.$el.querySelector('.el-scrollbar__wrap');
  const threshold = 50; // 距离底部50px触发加载
  // 使用更精确的计算方式
  // 判断是否滚动到底部（距离底部 < 50px 时加载更多）
  const remainingSpace = wrap.scrollHeight - (scrollTop + wrap.clientHeight);
  if (remainingSpace < threshold) {
    await loadMoreAppointments();
  }
};

// 加载更多方法
const loadMoreAppointments = async () => {
  if (noMoreData.value || loading.value) return;
  loading.value = true;
  try {
    const nextPage = currentPage.value + 1;
    let newObject = {
      page: nextPage,
      limit: pageSize
    }
    if (props.enterTab === 'pending') newObject.status = '预约中'
    const result = await getPageDataByUrl(loadDataUrl.value, newObject);
    if (result.list?.length) {
      // 使用 concat 保持响应式
      appointmentsPending.value = appointmentsPending.value.concat(result.list);
      currentPage.value = nextPage;
      totalCount.value = result.count || 0;
    }
    // 判断是否还有更多数据
    noMoreData.value = appointmentsPending.value.length >= totalCount.value;
    // 等待 DOM 更新后检查
    await nextTick();
    await checkLoadMore();
  } catch (e) {
    EleMessage.error(e.message || '加载失败');
  } finally {
    loading.value = false;
  }
};

const observer = ref(null)

watch(() => props.RandomString, async (val) => {
  if (val) {
    loadDataUrl.value = props.enterTab === 'pending' ? '/mentalHealth/xljk-appointment-info/page' : '/mentalHealth/xljk-appointment-info/pagePerson';
    if (loadDataUrl.value) await initLoad();
  }
}, {immediate: true, deep: true})

onMounted(async () => {
  if (loadDataUrl.value) {
    await initLoad();
    // 添加 resize 监听
    observer.value = new ResizeObserver(async () => {
      await checkLoadMore();
    });
    if (scrollbarRef.value?.$el) {
      observer.value.observe(scrollbarRef.value.$el);
    }
  }
});

onBeforeUnmount(() => {
  if (observer.value) observer.value.disconnect();
});

</script>
<style scoped lang="scss">

.appointment-cards {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

/* 左侧预约卡片样式 */
.appointment-card-content {
  /* padding: 4px 10px;*/
  position: relative;
}

.appointment-time {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

.urgency-tag {
  position: absolute;
  top: 8px;
  right: 8px;
}

.active-card {
  border: 1px solid var(--el-color-primary);
  background-color: rgba(64, 158, 255, 0.05);
}

/* 右侧详情区域样式 */
.summary-card {
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px !important;
  }
}

.appointment-status {
  margin-left: auto;
}

.action-buttons {
  margin-left: 20px;
}

.status-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
  min-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 10px;

  .el-button {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 6px;
    }
  }
}

/* 新增加载状态样式 */
.loading-more, .no-more-data, .empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  color: var(--el-text-color-secondary);

  .el-icon {
    margin-right: 8px;
    animation: rotating 2s linear infinite;
  }
}

.no-more-data {
  padding: 12px;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  margin: 8px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/*新增状态标签样式*/
.status-tags {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.status-tag {
  margin-left: 4px;
  height: 22px;
  line-height: 22px;
}

/* 卡片内容容器 */
.card-content-wrapper {
  position: relative;
  padding: 8px;
}

/* 状态标签样式 */
.status-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.status-tag {
  height: 22px;
  line-height: 22px;
}

/* 学生基本信息 */
.student-basic {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.student-name {
  /*font-weight: bold;*/
  font-size: 14px;
}

.student-id {
  font-size: 12px;
  color: #909399;
}

/* 预约时间 */
.appointment-time {
  font-size: 12px;
  color: #606266;
}

</style>
