<template>
  <ele-drawer size="64%"
              style="max-width: 100%"
              :title="LastAppointmentInfo.xm+'的咨询历程'"
              :append-to-body="false"
              :model-value="dialogVisible"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="closeDialog()">
    <div class="psychology-appointment  compact-view">
      <history-base :LastAppointmentInfo="LastAppointmentInfo" :RandomString="RandomString"
                    enterType="manager"/>
    </div>
  </ele-drawer>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {generateRandomString,} from "@/utils/common_bak2.js";
import HistoryBase from "@/views/mentalHealth/xljk-appointment-info/manager/components/historyBase.vue";

const emits = defineEmits(['done', 'update:visible']);
const props = defineProps({
  /** 弹窗是否打开 */
  visible: Boolean,
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  enterType: String,
});

const RandomString = ref(null)
let dialogVisible = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
})

const closeDialog = () => {
  emits('update:visible', false)
  emits('done')
}


watch(
  () => props.visible,
  (val) => {
    if (val) {
      RandomString.value = generateRandomString(10)
    }
  }
);
</script>

<style lang="scss">

/* 基础样式 */
.history-section {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  :deep(.el-card__header) {
    padding: 16px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }
}

/* 紧凑型学生头部信息 */
.student-header {
  display: flex;
  align-items: center;
  gap: 16px;

  .student-avatar-container {
    flex-shrink: 0;
  }

  .student-info-container {
    flex: 1;
    min-width: 0;

    .student-name-row {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 6px;

      .student-name {
        margin: 0;
        font-size: 18px;
        color: #1e293b;
      }

      .student-meta {
        .student-id {
          font-size: 13px;
          color: #64748b;
        }
      }
    }

    .student-contact-row {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 13px;
      color: #475569;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .el-icon {
          color: #94a3b8;
          font-size: 14px;
        }
      }
    }
  }

  .consult-stats {
    flex-shrink: 0;
    display: flex;
    gap: 16px;

    .stat-item {
      text-align: center;
      min-width: 80px;
      padding: 12px 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stat-value {
        font-size: 26px;
        font-weight: 600;
        color: #3b82f6;
        margin-bottom: 4px;
        line-height: 1.2;
      }

      .stat-label {
        font-size: 13px;
        color: #64748b;
        letter-spacing: 0.5px;
      }
    }
  }


  /* .consult-stats {
     flex-shrink: 0;
     display: flex;
     gap: 16px;
     padding: 8px 12px;
     background-color: #f1f5f9;
     border-radius: 6px;

     .stat-item {
       text-align: center;
       min-width: 50px;

       .stat-value {
         font-size: 16px;
         color: #3b82f6;
         margin-bottom: 2px;
       }

       .stat-label {
         font-size: 12px;
         color: #64748b;
       }
     }
   }*/

}


/* 响应式调整 */
@media (max-width: 768px) {
  .student-header {
    flex-wrap: wrap;
    gap: 12px;

    .consult-stats {
      width: 100%;
      justify-content: space-around;
    }
  }
}
</style>
