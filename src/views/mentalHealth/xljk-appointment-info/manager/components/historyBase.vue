<template>
  <div class="psychology-appointment  compact-view">
    <el-card class="history-section compact-card">
      <!-- 头部 - 学生基本信息 -->
      <div class="student-header">
        <div class="student-avatar-container">
          <el-avatar :size="66"
                     :src="LastAppointmentInfo.photo?('/api/file/inline/' + JSON.parse(LastAppointmentInfo.photo)[0].id):''">
            {{ LastAppointmentInfo.xm.charAt(0) }}
          </el-avatar>
        </div>

        <div class="student-info-container">
          <div class="student-name-row">
            <h3 class="student-name">{{ LastAppointmentInfo.xm }}</h3>
            <div class="student-meta">
              <span class="student-id">{{ LastAppointmentInfo.xgh }}</span>
            </div>
          </div>

          <div class="student-contact-row">
            <div class="contact-item">
              <el-icon>
                <Iphone/>
              </el-icon>
              <span>{{ LastAppointmentInfo.sjh || '未提供电话' }}</span>
            </div>
            <div class="contact-item">
              <span>{{ LastAppointmentInfo.xymc }} {{ LastAppointmentInfo.zymc }}</span>
            </div>
          </div>
          <div class="student-contact-row" v-if="completedCount>0"
               style="color:var(--el-color-primary);cursor: pointer;">
            <div class="contact-item" @click="handleFollowUp">
              <el-icon style="color:var(--el-color-primary);">
                <DocumentAdd/>
              </el-icon>
              <span>添加回访记录</span>
            </div>
            <div class="contact-item" @click="handleHistoryTimeline">
              <el-icon style="color:var(--el-color-primary);">
                <Collection/>
              </el-icon>
              <span>历史回访记录</span>
            </div>
          </div>
        </div>

        <div class="consult-stats">
          <div class="stat-item">
            <div class="stat-value">{{ completedCount }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ completedHistoryCount }}</div>
            <div class="stat-label">总预约</div>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 咨询记录时间轴 -->
    <compact-timeline :LastAppointmentInfo="LastAppointmentInfo" :RandomString="RandomString"
                      enterType="manager"
                      :currentPageHeight="currentPageHeight"
                      @doneAppointmentHistory="handleDoneAppointmentHistory"/>

    <!-- 添加回访记录-->
    <FollowUpEdit v-model="showFollowUpEdit" :RandomString="currentRandomString"
                  :LastAppointmentInfo="LastAppointmentInfo"/>
    <!--    回访历史记录-->
    <HistoryTimeline v-model="showHistoryTimeline" :LastAppointmentInfo="LastAppointmentInfo"
                     :RandomString="currentRandomString"/>
  </div>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {generateRandomString,} from "@/utils/common_bak2.js";
import {Collection, DocumentAdd, InfoFilled, Iphone} from "@element-plus/icons-vue";
import CompactTimeline from "@/views/mentalHealth/xljk-appointment-info/manager/components/compactTimeline.vue";
import IconPark from "@/components/IconPark/index.vue";
import FollowUpEdit from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/components/edit.vue";
import HistoryTimeline
  from "@/views/mentalHealth/xljk-appointment-info/manager/record/followup/components/historyTimeline.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const props = defineProps({
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  RandomString: String,
  enterType: String,
  SetPageHeight: Number,
});


const appointmentHistory = ref([])
const handleDoneAppointmentHistory = (data) => {
  appointmentHistory.value = data
}
// 计算属性
const completedCount = computed(() => {
  return appointmentHistory.value.filter(item => item.status === '已完成').length
})

const currentPageHeight = computed(() => {
  return props?.SetPageHeight??pageHeight.value-60;
})

const completedHistoryCount = computed(() => {
  return appointmentHistory.value.length
})

const currentRandomString = ref(null)

const showFollowUpEdit = ref(false)
const handleFollowUp = () => {
  showFollowUpEdit.value = true;
  currentRandomString.value = generateRandomString(10)
}

const showHistoryTimeline = ref(false)
const handleHistoryTimeline = () => {
  showHistoryTimeline.value = true;
  currentRandomString.value = generateRandomString(10)
}

</script>

<style lang="scss">

/* 基础样式 */
.history-section {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  :deep(.el-card__header) {
    padding: 16px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }
}

/* 紧凑型学生头部信息 */
.student-header {
  display: flex;
  align-items: center;
  gap: 16px;

  .student-avatar-container {
    flex-shrink: 0;
  }

  .student-info-container {
    flex: 1;
    min-width: 0;

    .student-name-row {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 6px;

      .student-name {
        margin: 0;
        font-size: 18px;
        color: #1e293b;
      }

      .student-meta {
        .student-id {
          font-size: 13px;
          color: #64748b;
        }
      }
    }

    .student-contact-row {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 13px;
      color: #475569;
      margin-bottom: 3px;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .el-icon {
          color: #94a3b8;
          font-size: 14px;
        }
      }
    }
  }

  .consult-stats {
    flex-shrink: 0;
    display: flex;
    gap: 16px;

    .stat-item {
      text-align: center;
      min-width: 80px;
      padding: 12px 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stat-value {
        font-size: 26px;
        font-weight: 600;
        color: #3b82f6;
        margin-bottom: 4px;
        line-height: 1.2;
      }

      .stat-label {
        font-size: 13px;
        color: #64748b;
        letter-spacing: 0.5px;
      }
    }
  }


  /* .consult-stats {
     flex-shrink: 0;
     display: flex;
     gap: 16px;
     padding: 8px 12px;
     background-color: #f1f5f9;
     border-radius: 6px;

     .stat-item {
       text-align: center;
       min-width: 50px;

       .stat-value {
         font-size: 16px;
         color: #3b82f6;
         margin-bottom: 2px;
       }

       .stat-label {
         font-size: 12px;
         color: #64748b;
       }
     }
   }*/

}


/* 响应式调整 */
@media (max-width: 768px) {
  .student-header {
    flex-wrap: wrap;
    gap: 12px;

    .consult-stats {
      width: 100%;
      justify-content: space-around;
    }
  }
}
</style>
