<template>
  <search style="margin-bottom: 10px;" ref="searchRef" @search="reload"/>
  <el-row :gutter="8">
    <el-col
      v-for="appointment in appointmentsPending"
      :key="appointment.id"
      :lg="4"
      :md="6"
      :sm="8"
      :xs="12"
      @click="handleHistory(appointment)">
      <ele-card
        shadow="hover"
        :body-style="{ padding: '8px 0 0 0 !important' }"
        class="appointment-card">
        <div class="avatar-container">
          <el-avatar :size="88"
                     class="user-avatar"
                     :src="appointment.photo?('/api/file/inline/' + JSON.parse(appointment.photo)[0].id):''">
            {{ appointment.xm.charAt(0) }}
          </el-avatar>
        </div>
        <div class="info-container">
          <div class="name-section">
            <ele-text size="md" bold class="user-name">  <!-- Changed from lg to md -->
              {{ appointment.xm }}
            </ele-text>
            <ele-text type="info" class="user-id">
              {{ appointment.xgh }}
            </ele-text>
          </div>

          <div class="college-section">
            <el-icon size="14">
              <School/>
            </el-icon>
            <ele-text type="placeholder" class="college-name">
              {{ appointment.xymc }}
            </ele-text>
          </div>
          <div class="college-section">
            <ele-text type="placeholder" class="college-name">
              {{ appointment.createTime }}
            </ele-text>
          </div>
          <div class="status-tag">
            <el-tag type="danger" size="small">{{ appointment.total }}次</el-tag>
          </div>
        </div>
      </ele-card>
    </el-col>
  </el-row>
  <history-drawer v-model:visible="dialogVisible"
                  :enterType="enterType"
                  :LastAppointmentInfo="LastAppointmentInfo"/>
</template>

<script setup>
import {reactive, ref, unref, watch,} from 'vue';
import {getAppointmentInfoPagePerson,} from '../../api/index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import { ElMessage as EleMessage} from "element-plus";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import HistoryDrawer from "@/views/mentalHealth/xljk-appointment-info/manager/components/historyDrawer.vue";
import Search from "@/views/mentalHealth/xljk-appointment-info/manager/components/search.vue";

const {removePageTab, getRouteTabKey, setPageTab,} = usePageTab();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
const BASE_URL = import.meta.env.BASE_URL;

const props = defineProps({
  RandomString: String,
  enterType: String,
});

/** 搜索栏实例 */
const searchRef = ref(null);
/** 加载状态 */
const loading = ref(false);
const totalCount = ref(0) // 新增总条数
const dateRange = ref([])
const noMoreData = ref(false)
const currentPage = ref(1)
const pageSize = 20
const appointmentsPending = ref([])

const dialogVisible = ref(false)
const LastAppointmentInfo = ref({})
const handleHistory = (appointment) => {
  dialogVisible.value = true
  LastAppointmentInfo.value = appointment
}

const prefixKeys = (obj, prefix = 'userInfo.') => {
  const result = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[`${prefix}${key}`] = obj[key];
    }
  }
  return result;
}
// 初始化数据
const initLoad = async () => {
  loading.value = true;
  try {
    let newObject = {
      'appointmentInfo.page': currentPage.value,
      'appointmentInfo.limit': pageSize
    }
    if (lastWhere.value) {
      newObject = {
        ...newObject,
        ...prefixKeys(lastWhere.value)
      }
    }
    const result = await getAppointmentInfoPagePerson(newObject);
    appointmentsPending.value = result.list || [];
    totalCount.value = result.count || 0;
    noMoreData.value = appointmentsPending.value.length >= totalCount.value;
  } catch (e) {
    EleMessage.error(e.message || '加载失败');
  } finally {
    loading.value = false;
  }
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 搜索 */
const reload = async (where) => {
  lastWhere.value = where
  if (where) {
    await initLoad();
  }
};

watch(() => props.RandomString, async (val) => {
  console.log(val)
  if (val) {
    await initLoad();
  }
}, {immediate: true, deep: true})


</script>

<script>
export default {
  name: 'RECORDLIST'
};
</script>

<style lang="scss" scoped>
/* Compact card styling */
.appointment-card {
  margin-top: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
}

.appointment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Compact avatar container */
.avatar-container {
  display: flex;
  justify-content: center;
  padding: 10px 0px 10px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ed 100%);
}

.user-avatar {
  border: 2px solid white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  background-color: var(--el-color-primary);
  color: white;
  font-weight: bold;
}

/* Compact info area */
.info-container {
  padding: 6px 12px 10px;
  text-align: center;
  position: relative;
}

.name-section {
  margin-bottom: 8px;
}

.user-name {
  display: block;
  color: #333;
  margin-bottom: 2px;
  font-size: 14px;
}

.user-id {
  display: block;
  font-size: 12px;
}

/* College info */
.college-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 6px;
  color: #666;
}

.college-name {
  font-size: 12px;
}

/* Status tag */
.status-tag {
  position: absolute;
  top: -10px;
  right: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .avatar-container {
    padding: 12px 0 8px;
  }

  .info-container {
    padding: 8px 12px 12px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }
}
</style>
