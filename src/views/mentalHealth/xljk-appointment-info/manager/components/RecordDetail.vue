<template>
  <div class="record-detail">
    <el-descriptions title="基本信息" :column="2" border>
      <el-descriptions-item label="记录编号">{{ record.id }}</el-descriptions-item>
      <el-descriptions-item label="咨询日期">{{ record.date }}</el-descriptions-item>
      <el-descriptions-item label="咨询时段">{{ record.timeSlot }}</el-descriptions-item>
      <el-descriptions-item label="咨询主题">{{ record.topic }}</el-descriptions-item>
      <el-descriptions-item label="咨询师">{{ record.consultant }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="getStatusType(record.status)">
          {{ record.status }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>

    <el-divider />

    <h3>主要问题</h3>
    <div class="detail-content">
      {{ record.details.mainIssues }}
    </div>

    <h3>咨询过程</h3>
    <div class="detail-content">
      {{ record.details.process }}
    </div>

    <h3>评估与建议</h3>
    <el-descriptions :column="1" border>
      <el-descriptions-item label="初步评估">
        {{ getAssessmentLabel(record.details.assessment) }}
      </el-descriptions-item>
      <el-descriptions-item label="建议与计划">
        {{ record.details.recommendations }}
      </el-descriptions-item>
      <el-descriptions-item label="跟进计划" v-if="record.details.followUpRequired">
        计划跟进日期: {{ record.details.followUpDate }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>

const props = defineProps({
  record: {
    type: Object,
    required: true
  }
})

const getStatusType = (status) => {
  switch(status) {
    case '已完成': return 'success'
    case '已取消': return 'info'
    default: return ''
  }
}

const getAssessmentLabel = (value) => {
  switch(value) {
    case 'mild': return '轻度困扰'
    case 'moderate': return '中度困扰'
    case 'severe': return '严重困扰'
    default: return value
  }
}
</script>

<style scoped>
.record-detail {
  padding: 10px;
}

h3 {
  margin: 20px 0 10px;
  color: #303133;
}

.detail-content {
  padding: 10px 15px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>
