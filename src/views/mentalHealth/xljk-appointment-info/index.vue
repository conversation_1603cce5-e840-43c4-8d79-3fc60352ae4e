<template>
  <ele-page hide-footer flex-table>
    <appointment-info v-if="LastAppointmentInfo" :LastAppointmentInfo="LastAppointmentInfo"
                      :RandomString="RandomString" @done="queryMyLastAppointmentInfo"/>
    <appointment-views v-else @done="queryMyLastAppointmentInfo"/>
  </ele-page>
</template>

<script setup>
import {ref, onMounted,} from 'vue'
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import { ElMessage as EleMessage} from "element-plus";
import {getCurrentRole} from "@/utils/current-role-util.js";
import {getMyLastAppointmentInfo} from "./api/index.js";
import AppointmentViews from "@/views/mentalHealth/xljk-appointment-info/components/appointmentViews.vue";
import AppointmentInfo from "@/views/mentalHealth/xljk-appointment-info/components/appointmentInfo.vue";
import {generateRandomString} from '@/utils/common_bak2.js';

const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

let RandomString = ref('')

const LastAppointmentInfo = ref(null)
const queryMyLastAppointmentInfo = () => {
  //加载数据
  getMyLastAppointmentInfo().then((list) => {
    if (list) {
      list.zxs = list.schedule.xm || ''
      LastAppointmentInfo.value = list
      RandomString.value = generateRandomString(10)
    }
    console.log(list)
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

onMounted(() => {
  queryMyLastAppointmentInfo()
})
</script>

<style scoped>
</style>
