import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getXljkSurvey(params) {
    const res = await request.get('/mentalHealth/xljk-survey', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

export async function queryXljkSurveyPage(params) {
    const res = await request.get('/mentalHealth/xljk-survey/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 获取答题记录xsid
 * @param params
 * @returns {Promise<*>}
 */
export async function getPageAnswerInfo(params) {
  const res = await request.get('/mentalHealth/xljk-survey/pageAnswerInfo', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 答卷人查看自己的答题记录详情
 */
export async function getSurveyAnswer(params) {
  const res = await request.get('/mentalHealth/xljk-survey/surveyAnswer', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 获取测评结果
 */
export async function getXljkSurveyReport(answerInfoId) {
  const res = await request.get('/mentalHealth/xljk-survey/surveyReport/' + answerInfoId);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getXljkSurveyById(id) {
  const res = await request.get('/mentalHealth/xljk-survey/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 答题
 */
export async function xljkSurveyOperation(data) {
    const res = await request.post('/mentalHealth/xljk-survey/answerOperation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
