<!--咨询师详情信息-->
<template>
  <el-dialog v-model="visibleDialog"
             :close-on-click-modal="false"
             draggable
             center
             title="心理测评结果"
             width="700px">
    <div class="consultant-detail-card">
      <!-- 顶部信息区域 -->
      <div class="top-info" v-if="currentData.userInfo">
        <el-avatar :size="72"
                   :src="currentData.userInfo.photo?('/api/file/inline/' + JSON.parse(currentData.userInfo.photo)[0].id):''">
          {{ currentData.userInfo.xm.charAt(0) }}
        </el-avatar>
        <div class="top-meta">
          <div class="name-row">
            <span class="name">{{ currentData?.userInfo.xm }}</span>
            <el-tag size="small" type="success" placement="left" effect="light">{{ currentData?.userInfo.xgh }}</el-tag>
            <el-tag v-if="currentData?.answerInfo?.score" size="small" type="warning" effect="light"
                    style="margin-left: 8px;">
              总分: {{ currentData?.answerInfo?.score }}
            </el-tag>
          </div>
          <div class="basic-info">
            <span class="info-item">
            <IconPark name="tips-one" size="18" strokeWidth="3"/>
               {{ currentData?.answerInfo?.testResult }}
            </span>
          </div>
        </div>
      </div>
      <el-divider/>

      <!-- 内容模块 -->
      <div class="result-container" v-if="currentData.reportList&&currentData.reportList.length>0">
        <div class="result-section" v-for="(item, index) in currentData.reportList" :key="index">
          <div class="section-title">
            <IconPark name="degree-hat" size="18" strokeWidth="3"/>
            <span class="title-text">{{ item.type }}</span>
          </div>
          <div class="tags-container">
            <el-tag
              v-for="(ilist, idx) in item.list"
              :key="idx"
              class="result-tag"
              :type="getTagType(ilist.val)"
              plain
              size="large">
              {{ ilist.itemName }}: {{ ilist.val }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import {computed, ref, watch} from "vue";
import {isImageFile} from "@/utils/common_bak2.js";
import IconPark from "@/components/IconPark/index.vue";
import { ElMessage as EleMessage} from "element-plus";
import {getXljkSurveyReport} from "@/views/mentalHealth/my-exam/api/index.js";
import {groupArr} from '@/utils/common_bak2.js';

const BASE_URL = import.meta.env.BASE_URL;

let emits = defineEmits(['update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  answerInfoId: {
    type: String,
    required: true
  }
})

let visibleDialog = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
});

const currentData = ref({})

// 根据值返回不同的tag类型
const getTagType = (val) => {
  if (typeof val === 'string') {
    if (val.includes('高风险') || val.includes('高')) return 'danger';
    if (val.includes('中风险') || val.includes('中')) return 'warning';
    if (val.includes('低风险') || val.includes('低')) return 'success';
  }
  return 'info';
}

const querySurveyReport = () => {
  getXljkSurveyReport(props.answerInfoId).then((data) => {
    if (data) {
      const tableDataGroup = data.reportList.length > 0 ? groupArr(data.reportList, 'type') : []
      currentData.value = data
      currentData.value.reportList = tableDataGroup
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

const closeDialog = () => {
  emits('update:visible', false)
}

watch(
  () => props.visible,
  (modelValue) => {
    if (modelValue && props.answerInfoId) {
      querySurveyReport()
    }
  }
);
</script>

<style scoped lang="scss">
.consultant-detail-card {
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

.top-info {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 12px;
}

.top-meta {
  flex: 1;
}

.name-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;

  .name {
    font-size: 20px;
  }
}

.basic-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.result-container {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 8px;
}

.result-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #222;

  .title-text {
    margin-top: 2px;
    font-size: 16px;
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-tag {
  margin: 4px;
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-2px);
    transition: transform 0.2s;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
