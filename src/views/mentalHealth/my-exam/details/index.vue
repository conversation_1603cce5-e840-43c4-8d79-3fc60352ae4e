<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <div style="display: flex; align-items: center;margin-right: 5px;" @click="onBack()">
            <IconPark name="return" size="18" strokeWidth="3"/>
            <ele-text style="padding: 2px;cursor: pointer;" type="secondary" size="sm">
              返回
            </ele-text>
          </div>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handlePaperAnswerList(row)">
            答题详情
          </el-link>
          <el-divider direction="vertical"/>
          <el-link type="primary" underline="never" @click="handleSurveyReport(row)">
            测评结果
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <PaperPerview v-model="showPaperPerview"
                  :paperId="sid"
                  :ksId="xsid"
                  enterType="perviewAnswerPaperXlzx"
                  :RandomString="RandomString"
                  :perviewParams="perviewParams"
                  @done="reload"/>

    <record-detail-dialog v-model:visible="showSurveyReportDialogVisible" :answerInfoId="answerInfoId"/>
  </ele-page>
</template>

<script setup>
import {ref, unref, watch,} from 'vue';
import {useRouter} from "vue-router";
import {generateRandomString,} from "@/utils/common_bak2.js";
import {usePageTab} from "@/utils/use-page-tab.js";
import IconPark from "@/components/IconPark/index.vue";
import {getPageAnswerInfo,} from "../api/index.js";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import RecordDetailDialog from "@/views/mentalHealth/my-exam/components/record-detail-dialog.vue";

const {removePageTab, getRouteTabKey, setPageTab, setPageTabTitle} = usePageTab();
const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
if (query.currentKsmcc) setPageTabTitle(query.currentKsmcc + '答题记录')

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let sid = ref(null);
let xsid = ref(null);
let perviewParams = ref(null);
const RandomString = ref(null)

/**
 * 页面初始化数据提取封装
 * @returns {{examId: string}}
 */
const getPathParams = () => {
  const segments = unref(currentRoute).path.split('/');
  console.log(segments)
  return {
    examId: segments[4]
  };
};

const {examId} = getPathParams();

/** 加载状态 */
const loading = ref(false);

/** 答题详情 */
const handlePaperAnswerList = (row) => {
  RandomString.value = generateRandomString(10)
  sid.value = row.sid;//问卷id
  xsid.value = row.xsid;//心理问卷ID
  perviewParams.value = {
    id: row.id,//答题ID
    sid: row.sid,//问卷ID
    xsid: row.xsid//心理问卷ID
  }
  showPaperPerview.value = true;
}
const answerInfoId = ref(null)
const showSurveyReportDialogVisible = ref(false)
/** 测评结果 */
const handleSurveyReport = (row) => {
  answerInfoId.value = row.id
  showSurveyReportDialogVisible.value = true
  // getXljkSurveyReport(row.id).then((msg) => {
  //   console.log(msg)
  // }).catch((e) => {
  //   EleMessage.error(e.message);
  // });
}

/** 表格列配置 */
const initColumns = () => ([
  {prop: 'createDate', label: '答题提交时间'},
  {prop: 'xm', label: '答题人'},
  {prop: 'xgh', label: '学工号'},
  {
    columnKey: 'action',
    label: '操作',
    width: 150,
    slot: 'action'
  }
]);

const columns = ref(initColumns());

/** 列表选中数据 */
const selections = ref([]);

/** 表格数据源 */
// const datasource = ({page, limit, where, orders, filters}) => {
//   return getPageAnswerInfo({...where, ...orders, ...filters, page, limit, xsid: examId})
// };
const datasource = async ({page, limit, where, orders, filters}) => {
  loading.value = true;
  try {
    return await getPageAnswerInfo({
      ...where, ...orders, ...filters,
      page, limit, xsid: examId
    });
  } finally {
    loading.value = false;
  }
};
/** 返回 */
const onBack = () => {
  removePageTab({key: getRouteTabKey()});
  push({
    path: '/mentalHealth/my-exam/xljkjy',
  })
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

//弹窗关闭自动刷新
watch(showPaperPerview, (val) => {
  if (!val) {
    reload();
  }
});
</script>

<script>
export default {
  name: 'XLZXMYEXAMDETAILSINDEX'
};
</script>
