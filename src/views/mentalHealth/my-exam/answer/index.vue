<!-- 试卷预览 -->
<template>
  <ele-page hide-footer flex-table>
    <BaseComponent :ksId="ksId"
                   :paperId="paperId"
                   :RandomString="randomString"
                   enterType="toAnswerXlzx"
                   @doneRefesh="handleDoneRefresh"/>
  </ele-page>
</template>

<script setup>
import {inject, ref, onMounted, unref} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import BaseComponent from '@/views/exam/components/baseComponent.vue';
import {generateRandomString} from '@/utils/common_bak2.js';

// 组件名
defineOptions({
  name: 'XLZXMYEXAMANSWER',
});

// 路由、标签页
const route = useRoute();
const router = useRouter();
const {removePageTab, getRouteTabKey, setPageTabTitle} = usePageTab();

const {currentRoute, push} = useRouter();
const {path, query} = unref(currentRoute);
if (query.currentKsmcc) setPageTabTitle(query.currentKsmcc + '答卷')
/**
 * 页面初始化数据提取封装
 * @returns {{examId: string, userType: string, routeType: string}}
 */
const getPathParams = () => {
  const segments = unref(currentRoute).path.split('/');
  console.log(segments)
  return {
    ksId: segments[4],
    paperId: segments[5],
  };
};

const {ksId, paperId,} = getPathParams();
// 随机串
const randomString = ref('');
// 注入 refresh，带容错
const triggerRefresh = inject('refreshEvent')?.triggerRefresh || (() => {
});

// 提交完成后的回调
const handleDoneRefresh = () => {
  triggerRefresh();
  removePageTab({key: getRouteTabKey()});
  router.push({path: '/mentalHealth/my-exam/xljkjy'});
};

// 初始化
onMounted(() => {
  if (ksId) {
    randomString.value = generateRandomString(10);
  }
});

</script>
