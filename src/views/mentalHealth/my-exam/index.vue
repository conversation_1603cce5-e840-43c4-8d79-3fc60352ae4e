<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #name="{ row }">
          <ele-tooltip
            content="点我预览问卷详情"
            placement="left"
            effect="light"
          >
            <el-link
              type="primary"
              underline="never"
              @click="handlePaperPerview(row)"
            >
              {{ row.survey.name }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #surveyStat="{ row }">
          <template v-if="row.testNum > 0">
            <ele-tooltip content="查看测评记录" placement="left" effect="light">
              <el-link
                type="primary"
                underline="never"
                @click="handlePaperAnswerList(row)"
              >
                {{ row.testNum }}
              </el-link>
            </ele-tooltip>
            / {{ row?.kdtcs ?? '不限制' }}
          </template>
          <template v-else>
            {{ row.testNum }} / {{ row?.kdtcs ?? '不限制' }}
          </template>
        </template>
        <template #status="{ row }">
          <el-switch
            v-model="row.status"
            size="small"
            inline-prompt
            active-text="启用"
            active-value="启用"
            inactive-text="结束"
            inactive-value="结束"
            @change="handleChangeState($event, row)"
          />
        </template>
        <template #action="{ row }">
          <el-link
            v-if="row?.kdtcs === null || row?.testNum < row?.kdtcs"
            type="primary"
            underline="never"
            @click="handlePaperToAnswer(row)"
          >
            答卷
          </el-link>
          <el-link
            v-if="row?.testNum === row?.kdtcs"
            type="primary"
            underline="never"
            @click="handlePaperAnswerList(row)"
          >
            答题记录
          </el-link>
          <el-divider v-if="row.status === '结束'" direction="vertical" />
          <el-link
            v-if="row.status === '结束'"
            type="primary"
            underline="never"
            @click="openDataTongJi(row)"
          >
            数据统计
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <PaperPerview
      v-model="showPaperPerview"
      :paperId="pid"
      :pxsid="pxsid"
      enterType="toPerviewXlzx"
      :RandomString="RandomString"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { queryXljkSurveyPage } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { generateRandomString } from '@/utils/common_bak2.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { comColumns } from '../test-paper/utils/index.js';
  import { useDictData } from '@/utils/use-dict-data.js';
  import PaperPerview from '@/views/exam/components/paper-preview.vue';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  let currentProject = null;
  let pathArray = path.split('/');
  console.log(pathArray);
  let routeType = pathArray[3];
  // 已缓存的字典
  const { dicts } = storeToRefs(userStore);

  /** 表格实例 */
  const tableRef = ref(null);
  const showPaperPerview = ref(false);

  const pid = ref(null);
  const pxsid = ref(null);
  /** 当前编辑数据 */
  const current = ref(null);

  const RandomString = ref(null);

  /** 加载状态 */
  const loading = ref(false);
  /** 试卷预览 */
  const handlePaperPerview = (row) => {
    RandomString.value = generateRandomString(10);
    pid.value = row.sid;
    pxsid.value = row.id;
    showPaperPerview.value = true;
  };

  /** 答题记录列表*/
  const handlePaperAnswerList = (row) => {
    removePageTab({ key: getRouteTabKey() });
    pid.value = row.sid;
    pxsid.value = row.id;
    push({
      path: '/mentalHealth/my-exam/details/' + pxsid.value,
      query: { currentKsmcc: row.survey.name }
    });
  };

  /**
   * 去答卷
   */
  const handlePaperToAnswer = (row) => {
    pid.value = row.sid;
    pxsid.value = row.id;
    removePageTab({ key: getRouteTabKey() });
    push({
      path: '/mentalHealth/my-exam/answer/' + pxsid.value + '/' + pid.value,
      query: { currentKsmcc: row.survey.name }
    });
  };

  const handleChangeState = (value, row) => {
    console.log(value, row);
  };

  /** 表格列配置 */
  const columns = ref([]);

  /** 列表选中数据 */
  const selections = ref([]);
  const readonlyVal = ref(false);

  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    let whereData = {
      ...where,
      ...orders,
      ...filters,
      'param.page': page,
      'param.limit': limit
      // 'approvalNode.nodeId': currentNodeId.value,
    };
    lastWhere.value = whereData;
    return queryXljkSurveyPage(whereData);
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };

  const initColumns = (dictData) => {
    const baseColumns = comColumns(routeType, dictData);
    const visibleColumns = baseColumns.filter((col) => col.hideFlag !== 'list');
    const customColumns = [
      {
        prop: 'surveyStat',
        label: '已测评/可测评',
        slot: 'surveyStat'
      },
      {
        columnKey: 'action',
        label: '操作',
        slot: 'action',
        minWidth: 40
      }
    ];

    columns.value = [...visibleColumns, ...customColumns];
  };

  const checkAndInitColumns = () => {
    const xlzxDict = dicts.value['xlzxSurveyGid'];

    if (xlzxDict && xlzxDict.length) {
      initColumns(xlzxDict);
    } else {
      useDictData(['xlzxSurveyGid'], {
        dictFieldUrl: '/survey/survey-questions-calss',
        valueField: 'id',
        textField: 'name',
        params: { type: routeType }
      });
    }
  };

  watch(() => dicts.value['xlzxSurveyGid'], checkAndInitColumns, {
    immediate: true
  });
</script>

<script>
  export default {
    name: 'XLZXTESTPAPERINDEX'
  };
</script>
