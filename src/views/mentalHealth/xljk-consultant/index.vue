<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #userInfo.xm="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.userInfo.xm }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #userInfo.zjhm="{ row }">
          <ele-tooltip v-if="row.userInfo.zjhm" content="查看完整证件号码" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="handlePersonIdNumber(row)">
              <IconPark name="id-card-h" size="18" strokeWidth="3"/>
            </el-link>
          </ele-tooltip>
        </template>
        <template #zzzs="{ row }">
          <ele-tooltip v-if="row.zzzs" content="查看资质证书" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openImageViewer(row)">
              <IconPark name="certificate" size="18" strokeWidth="3"/>
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload"/>

    <EleImageViewer v-model="showImageViewer"
                    :urlList="urlList"
                    :infinite="infinite"
                    :hideOnClickModal="hideOnClickModal"
                    :closeOnPressEscape="closeOnPressEscape"
                    :initialIndex="initialIndex"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref} from 'vue';
import {queryPage, removes} from './api/index.js';
import {useRouter} from 'vue-router';
import {usePageTab} from '@/utils/use-page-tab.js';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import {ElMessageBox} from 'element-plus';
import Edit from './components/edit.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import IconPark from '@/components/IconPark/index.vue';
import {isImageFile} from '@/utils/common_bak2.js';
import {getPersonIdNumber} from '@/views/personInfo/st/api/index.js';
import {useMessageBox} from 'ele-admin-plus/es';

const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);
const BASE_URL = import.meta.env.BASE_URL;

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);
let ksid = ref(null);

/** 加载状态 */
const loading = ref(false);

/** 试卷答题 */
const handlePaperPerview = (row) => {
  ksid = row.id;
  showPaperPerview.value = true;
};

/** 答题记录列表*/
const handlePaperAnswerList = (row) => {
  removePageTab({key: getRouteTabKey()});
  ksid = row.id;
  push({
    path: '/exam/my-exam/details/' + ksid,
    query: {currentKsmcc: row.ksmc},
  });
};

/** 是否显示表单弹窗 */
const showEdit = ref(false);

/** 编辑回显数据 */
const editData = ref(null);
/** 打开编辑弹窗 */
const openEdit = (row) => {
  editData.value = row ?? null;
  showEdit.value = true;
};

const messageBox = useMessageBox();
const inner = ref(false);

const handlePersonIdNumber = (row) => {
  const loading = ElLoading.service({
    lock: true,
    text: '请求中...',
    background: 'rgba(0, 0, 0, 0.1)',
  });
  // editData.value = row ?? null;
  // showEdit.value = true;
  getPersonIdNumber(row.userInfo.xgh).then((res) => {
    loading.close();
    if (res) {
      messageBox.alert(res, '查看完整证件号码', {
        inner: inner.value,
        draggable: true,
      }).then(() => {
        console.log('点击了确定');
      }).catch(() => {
      });
    }
  }).catch((e) => {
    loading.close();
    EleMessage.error(e.message);
  });
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.userInfo.xm).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true},
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)',
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 是否显示导入弹窗 */
const showImport = ref(false);
/** 表格列配置 */
const columns = ref([
  {
    type: 'selection',
    columnKey: 'selection',
    width: 45,
    align: 'center',
    fixed: 'left',
  },
  {
    prop: 'userInfo.xgh',
    label: '账号',
  },
  {
    prop: 'userInfo.xm',
    slot: 'userInfo.xm',
    label: '姓名',
  },
  {
    prop: 'userInfo.sjh',
    label: '手机号',
  },
  {
    prop: 'userInfo.zjhm',
    slot: 'userInfo.zjhm',
    label: '证件号码',
  },
  {
    prop: 'zxslb',
    label: '咨询师类别',
  },
  {
    prop: 'scly',
    label: '擅长领域',
  },
  {
    prop: 'cyjy',
    label: '从业经验',
  },
  {
    prop: 'zzzs',
    slot: 'zzzs',
    label: '资质证书',
  },
  {
    prop: 'xljy',
    label: '心灵寄语',
  },
]);

/** 列表选中数据 */
const selections = ref([]);

/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value,
    });
  } else {
    reload(lastWhere);
  }
};
/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return queryPage({...where, ...orders, ...filters, page, limit});
};

/** 是否打开图片预览组件 */
const showImageViewer = ref(false);

/** 点击遮罩关闭 */
const hideOnClickModal = ref(false);

/** 返回键关闭 */
const closeOnPressEscape = ref(true);

/** 初始图片索引 */
const initialIndex = ref(0);
/** 循环预览 */
const infinite = ref(false);
/** 图片列表 */
const urlList = ref([]);

/** 打开图片预览组件 */
const openImageViewer = (row) => {
  urlList.value = row.zzzs
    ? JSON.parse(row.zzzs).map((d, i) => {
      let newArray = [];
      let newObj = {
        key: d.id,
        name: d.originalFilename,
        contentType: d.contentType,
        fileUrl: BASE_URL + 'api/file/inline/' + d.id,
        status: 'done',
      };
      let mark = isImageFile(newObj);
      newObj.isImageFile = mark;
      if (mark) {
        newObj.url = newObj.fileUrl;
        newArray.push(newObj.fileUrl);
      }
      return newArray;
    }) : [];
  console.log(urlList.value);
  showImageViewer.value = true;
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef['value']?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef['value']?.reload?.();
  }
};

</script>

<script>
export default {
  name: 'CONSULTANTINDEX',
};
</script>
