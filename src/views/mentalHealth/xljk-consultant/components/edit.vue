<!-- 咨询师编辑弹窗 -->
<template>
  <ele-drawer size="50%"
              :title="isUpdate ? '修改咨询师 ['+data.userInfo.xm+']' : '添加咨询师'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-width="100px"
             @submit.prevent="">
      <el-form-item label="咨询师类别" prop="zxslb">
        <el-radio-group v-model="form.zxslb">
          <el-radio-button value="专职">专职</el-radio-button>
          <el-radio-button value="校内兼职">校内兼职</el-radio-button>
          <el-radio-button value="外聘">外聘</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="!isUpdate" label="咨询师" prop="zxsly">
        <el-radio-group v-model="form.zxsly">
          <el-radio-button value="xtxz">系统选择</el-radio-button>
          <el-radio-button value="sdwh">手动维护</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.zxsly==='xtxz'" :label="isUpdate?'咨询师':' '" prop="selectorDatas">
        <div v-if="!isUpdate" style="margin-bottom: 6px;cursor: pointer;" @click="openPerSelector()">
          <ele-text type="primary" style="float: left;" @click="openPerSelector()">
            <el-icon>
              <CirclePlus/>
            </el-icon>
            选择咨询师
          </ele-text>
        </div>
        <el-input readonly
                  v-model="qddxData"
                  placeholder="请选择咨询师"/>
      </el-form-item>
      <el-form-item label="头像">
        <file-upload :limit="1" v-model="form.userInfo.photo" @change="changeDelIds" listType="image"/>
      </el-form-item>
      <template v-if="form.zxsly==='sdwh'">
        <el-form-item label="咨询师账号" prop="xgh">
          <el-input clearable
                    :readonly="isUpdate"
                    :maxlength="20"
                    v-model="form['userInfo']['xgh']"
                    placeholder="请输入咨询师账号"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="咨询师姓名" prop="xm">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.userInfo.xm"
                    placeholder="请输入咨询师姓名"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="咨询师性别" prop="xb">
          <el-radio-group v-model="form.userInfo.xb">
            <el-radio-button value="男">男</el-radio-button>
            <el-radio-button value="女">女</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="咨询师手机号" prop="sjh">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.userInfo.sjh"
                    placeholder="请输入咨询师手机号"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
        <el-form-item label="咨询师证件号码" prop="zjhm">
          <el-input clearable
                    :maxlength="20"
                    v-model="form.userInfo.zjhm"
                    placeholder="请输入咨询师证件号码"
                    controls-position="right"
                    class="ele-fluid"/>
        </el-form-item>
      </template>
      <el-form-item label="授权角色" prop="roleId">
        <dict-data placeholder="请选择角色"
                   code="xljkRoleZxs"
                   type="multipleSelect"
                   :dicQueryParams="{
                            dictFieldUrl: '/system/role',
                            valueField: 'id',
                            textField: 'name',
                            getValType: 'id',
                            params:{roleScope:'咨询师'}
                         }"
                   :model-value="form.userInfo.roleIdArray"
                   @update:modelValue="(value) => updateValue('roleId',value)"/>
      </el-form-item>
      <el-form-item label="擅长领域" prop="scly">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.scly"
                  placeholder="请输入擅长领域"/>
      </el-form-item>
      <el-form-item label="从业经验" prop="cyjy">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.cyjy"
                  placeholder="请输入从业经验"/>
      </el-form-item>
      <el-form-item label="资质证书" prop="zzzs">
        <file-upload :limit="8" v-model="form.zzzs" @change="changeDelIds" listType="image"/>
      </el-form-item>
      <el-form-item label="心灵寄语">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.xljy"
                  placeholder="请输入心灵寄语"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
    <PerSelector v-model="showPerSelector"
                 ref="treeTransferRef"
                 node-key="id"
                 header="请选择心理咨询师"
                 paramMode="xljk-consultant"
                 selectMaxNubmer="1"
                 :perSelectedData="perSelectedData"
                 @done="onDoneSelector"/>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, computed} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../api/index.js';
import {CirclePlus} from "@element-plus/icons-vue";
import PerSelector from "@/components/PerSelector/index.vue";
import FileUpload from '@/components/FileUpload/index.vue';
import {isImageFile,  toFormDataWj} from "@/utils/common_bak2.js";
import {getPersonIdNumber} from "@/views/personInfo/st/api/index.js";

const BASE_URL = import.meta.env.BASE_URL;
const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  /** 试卷ID*/
  paperId: String,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  zxslb: '专职',
  zxsly: 'xtxz',
  scly: '',
  sort: void 0,
  cyjy: '',
  zzzs: '',
  xljy: '',
  userInfo: {
    xgh: '',
    xm: '',
    sjh: '',
    zjhm: '',
    xb: '',
    photo: '',
    roleId: '',
    roleIdArray: [],
  }
});

/** 更新值 */
const updateValue = (prop, value) => {
  form["userInfo"][prop + 'Array'] = value;
  form["userInfo"][prop] = form["userInfo"][prop + 'Array'].join()
}

/** =====人员选择器=====start=*/
const perSelectedData = ref([]);
const showPerSelector = ref(false)

const onDoneSelector = (data) => {
  console.log(data)
  perSelectedData.value = data;
};

const openPerSelector = () => {
  showPerSelector.value = true;
};

const qddxData = computed(() => {
  let allDataCode = []
  if (perSelectedData.value.length > 0) {
    perSelectedData.value.forEach(e => {
      // form.xgh = e.dictDataCode
      // form.xm = e.dictDataName
      form.userInfo = {
        xgh: e.dictDataCode,
        xm: e.dictDataName,
        xb: e.xb,
        sjh: e.sjh,
        zjhm: e.zjhm,
        photo: e.photo,
        roleId: e.roleId,
        roleIdArray: e.roleId?.split(',') ?? []
      }
      allDataCode.push(e.dictDataName ? e.dictDataName : e.name)
    })
  }
  return allDataCode.join(',');
});


/** 表单验证规则 */
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  sort: [
    {
      message: '请输入排序号',
      type: 'number',
      trigger: 'blur'
    }
  ]
});

/** 要删除的附件ID，字符串逗号分割*/
const deleteFileIds = ref(null);
/** changeDelIds */
const changeDelIds = (value) => {
  deleteFileIds.value = value
};

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    console.log(JSON.stringify(form))
    let newDeleteFileIds = ""
    const fileArrayKeys = ['zzzs', 'userInfo.photo'];
    if (deleteFileIds.value && deleteFileIds.value.length > 0) newDeleteFileIds = JSON.stringify({zzzs: deleteFileIds.value})
    // let data = toFormData({...form, deleteFileIds: newDeleteFileIds}, fileArrayKeys)
    let data = toFormDataWj({...form, deleteFileIds: newDeleteFileIds})
    // let data = toFormData({...result})
    console.log(JSON.stringify(data))
    operation(data).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done', isUpdate.value, data);
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

const handlePersonIdNumber = () => {
  getPersonIdNumber(props.data.userInfo.xgh).then((res) => {
    if (res) {
      form.userInfo.zjhm = res;
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      resetFields();
      perSelectedData.value = []
      if (props.data) {
        console.log(props.data)
        const oldFiles = props.data.zzzs
          ? JSON.parse(props.data.zzzs).map((d, i) => {
            let newObj = {
              key: d.id,
              name: d.originalFilename,
              contentType: d.contentType,
              fileUrl: BASE_URL + 'api/file/inline/' + d.id,
              status: 'done'
            }
            let mark = isImageFile(newObj)
            newObj.isImageFile = mark;
            if (mark) newObj.url = newObj.fileUrl
            return newObj;
          }) : [];


        const oldPhoto = props.data.userInfo && props.data.userInfo.photo
          ? JSON.parse(props.data.userInfo.photo).map((d, i) => {
            let newObj = {
              key: d.id,
              name: d.originalFilename,
              contentType: d.contentType,
              fileUrl: BASE_URL + 'api/file/inline/' + d.id,
              status: 'done'
            }
            let mark = isImageFile(newObj)
            newObj.isImageFile = mark;
            if (mark) newObj.url = newObj.fileUrl
            return newObj;
          }) : [];


        if (props.data.userInfo.zjhm) handlePersonIdNumber()
        const roleIdArray = props.data.userInfo.roleId?.split(',') ?? [];

        perSelectedData.value = [{
          configId: "dbdf65be75416ec85397e6706f202db4",
          configKey: "xgh",
          configName: props.data.userInfo.userType,
          configTitle: props.data.userInfo.userType === 'teacher' ? "教师" : '学生',
          dictDataCode: props.data.userInfo.xgh,
          dictDataName: props.data.userInfo.xm,
          name: props.data.userInfo.xm,
          userType: props.data.userInfo.userType,
          value: props.data.userInfo.xgh,
          ...props.data.userInfo,
          roleId: props.data.userInfo.roleId,
          photo: oldPhoto,
          roleIdArray: roleIdArray,
        }]

        assignFields({
          ...props.data,
          zzzs: oldFiles,
          userInfo: {
            xgh: props.data.userInfo.xgh,
            xm: props.data.userInfo.xm,
            sjh: props.data.userInfo.sjh,
            xb: props.data.userInfo.xb,
            zjhm: props.data.userInfo.zjhm,
            photo: oldPhoto,
            roleId: props.data.userInfo.roleId,
            roleIdArray: roleIdArray
          }
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
