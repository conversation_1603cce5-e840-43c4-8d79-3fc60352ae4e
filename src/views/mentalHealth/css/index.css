
.time-label {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #606266;
}

.not-current-month {
  background-color: #f9f9f9;
  color: #ccc;
}

.today {
  background-color: #f0f9eb;
}

/* 月视图事件样式 */
.month-event {
  /*background-color: #409eff;*/
  /*color: white;*/
  border-radius: 3px;
  padding: 2px 5px;
  margin-bottom: 2px;
  font-size: 12px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-time {
  font-size: 11px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

th {
  background-color: #f5f7fa;
  padding: 12px;
  text-align: center;
  line-height: 1.4;
}

td {
  position: relative;
}

/* 日视图容器 */
.day-view-container {
  /*border: 1px solid #ebeef5;*/
  border-radius: 4px;
  overflow: hidden;
}

/* 表格特殊处理 */
.ele-table {
  display: flex;
  flex-direction: column;
}

/* 时间标签样式 */
.time-label {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #606266;
  text-align: center;
  padding: 8px;
}

/* 事件卡片样式 */
.day-event {
  position: absolute;
  left: 2px;
  right: 2px;
  top: 2px;
  bottom: 2px;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  overflow: hidden;
  /*background-color: #409eff;*/
  /*color: white;*/
  box-sizing: border-box;
}

.day-event:hover {
  opacity: 0.9;
}

.event-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event-time {
  font-size: 11px;
  opacity: 0.9;
}

/* 滚动条美化 */
tbody::-webkit-scrollbar {
  width: 8px;
}

tbody::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 4px;
}

tbody::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* 月视图容器 */
.month-view-container {
  /*border: 1px solid #ebeef5;*/
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 月视图头部 */
.month-header {
  background: #f5f7fa;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

/* 星期标题 */
.weekdays-header {
  display: flex;
  background: #f5f7fa;
}

.weekday {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  color: var(--el-text-color-primary);
  font-weight: bold;
  font-size: var(--el-font-size-extra-small);
}

/* 月视图主体 */
.month-body {
  overflow-y: auto;
  background: white;
}

/* 周行 */
.week-row {
  display: flex;
  min-height: 100px;
  border-bottom: 1px solid #ebeef5;
}

/* 日单元格 */
.day-cell {
  flex: 1;
  padding: 4px;
  border-right: 1px solid #ebeef5;
  position: relative;
}

.day-cell:last-child {
  border-right: none;
}

.day-cell.current-month {
  background: white;
}

.day-cell:not(.current-month) {
  background: #fafafa;
  color: #c0c4cc;
}

/* 周末样式 */
.day-cell.weekend {
  background-color: #f9f9f9;
}

.day-cell.weekend:not(.current-month) {
  background-color: #f5f5f5;
}

/* 今日样式 */
.day-cell.today {
  background-color: #f0f9eb;
}

/* 日头部 */
.day-header {
  text-align: right;
  margin-bottom: 4px;
}

.day-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  font-size: 14px;
}

.today-mark {
  background-color: #409eff;
  color: white;
  font-weight: bold;
}

/* 日事件区域 */
.day-events {
  min-height: 60px;
}

/* 月视图事件样式 */
.month-event {
  border-radius: 4px;
  padding: 2px 4px;
  margin-bottom: 2px;
  font-size: 12px;
  cursor: pointer;
  overflow: hidden;
  /*color: white;*/
  white-space: nowrap;
  text-overflow: ellipsis;
}

.month-event:hover {
  opacity: 0.9;
}

.event-title {
  font-weight: 500;
}

.event-time {
  font-size: 11px;
  opacity: 0.8;
}

/* 滚动条美化 */
.month-body::-webkit-scrollbar {
  width: 6px;
}

.month-body::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
}

.month-body::-webkit-scrollbar-track {
  background-color: rgba(144, 147, 153, 0.1);
}

/* 添加点击效果 */
.day-cell {
  cursor: pointer;
  transition: background-color 0.2s;
}

.day-cell:hover {
  background-color: #f5f7fa !important;
}

.day-cell:not(.current-month) {
  pointer-events: none; /* 禁止点击非当月日期 */
}

/* 调整事件卡片点击区域 */
.month-event {
  pointer-events: auto; /* 确保事件卡片可以点击 */
}

/* 样式保持不变，只调整日期数字的样式 */
.day-number {
  display: inline-block;
  padding: 2px 4px;
  font-size: 14px;
  /* 移除圆形样式 */
  border-radius: 0;
  width: auto;
  height: auto;
  line-height: normal;
}

.today-mark {
  background-color: #409eff;
  color: white;
  font-weight: bold;
  padding: 2px 4px;
  border-radius: 4px;
}

/* 当天日期数字样式 */
.day-number.today {
  color: white;
  background-color: var(--el-color-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

/* 普通日期数字样式 */
.day-number {
  font-weight: bold;
  margin-left: auto;
}

/* 节假日文字样式 */
.holiday-text {
  font-size: 12px;
  color: #f56c6c; /* 红色突出节假日 */
  float: left;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 4px;
}

/* 单元格基础样式保持不变 */
.day-cell {
  position: relative;
  min-height: 80px;
  padding: 2px;
}
/* 节假日样式 */
.day-cell.holiday {
  background-color: #fff8e6; /* 浅黄色背景 */
}

.day-cell.weekend {
  background-color: #f9f9f9; /* 浅灰色背景 */
}

.holiday-text {
  font-size: 12px;
  color: #f56c6c;
  font-weight: bold;
}

/* 节气样式 */
.solar-term {
  font-size: 12px;
  color: #67c23a;
}
