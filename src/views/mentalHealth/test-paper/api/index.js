import request from '@/utils/request';

/**
 * 查询不分页
 */
export async function getSurveyManage(params) {
  const res = await request.get('/mentalHealth/xljk-survey-manage', {params});
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function querySurveyManagePage(params) {
  const res = await request.get('/mentalHealth/xljk-survey-manage/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}


/**
 * 心理咨询- 试卷预览查询试卷试题详情（带答案的）
 */
export async function getSurveyManageAnswer(params) {
  const res = await request.get('/mentalHealth/xljk-survey-manage/surveyAnswer', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getSurveyManageById(id) {
  const res = await request.get('/mentalHealth/xljk-survey-manage/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-survey-manage/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-survey-manage/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
