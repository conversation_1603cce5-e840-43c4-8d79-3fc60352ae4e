<template>
  <ele-drawer
    size="43%"
    :title="readonlyVal ? '问卷详情' : isUpdate ? '编辑问卷' : '新建问卷'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="formItems"
      :grid="{ span: 24 }"
      labelWidth="auto"
      label-position="top"
      @updateValue="setFieldValue"
    >
      <template #selectorData="{ item, model, updateValue }">
        <div
          style="margin-bottom: 6px; cursor: pointer"
          @click="openPerSelector()"
        >
          <ele-text
            type="primary"
            style="float: left"
            @click="openPerSelector()"
          >
            <el-icon>
              <CirclePlus />
            </el-icon>
            选择测评对象 </ele-text
          >&nbsp;
        </div>
        <el-input
          :rows="6"
          type="textarea"
          readonly
          v-model="qddxData"
          placeholder="请选择测评对象"
        />
      </template>
    </pro-form>

    <!-- 底部工具栏 -->
    <template #footer>
      <template v-if="readonlyVal">
        <el-button size="small" @click="updateModelValue(false)"
          >关闭</el-button
        >
      </template>
      <template v-else>
        <el-button size="small" @click="updateModelValue(false)"
          >取消</el-button
        >
        <!--        <el-button size="small" type="primary" @click="submit('design')" plain>保存并进入问卷设计-->
        <!--        </el-button>-->
        <el-button
          size="small"
          type="primary"
          plain
          :loading="loading"
          @click="submit"
        >
          保存
        </el-button>
      </template>
    </template>
  </ele-drawer>
  <PerSelector
    v-model="showPerSelector"
    ref="treeTransferRef"
    node-key="id"
    paramMode="base"
    :perSelectedData="perSelectedData"
    @done="onDoneSelector"
  />
</template>

<script setup>
  import { ref, watch, computed, unref } from 'vue';
  import { useFormData } from '@/utils/use-form-data.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import ProForm from '@/components/ProForm/index.vue';
  import { operation } from '../api/index.js';
  import PerSelector from '@/components/PerSelector/index.vue';
  import { comColumns } from '../utils/index.js';
  import { generateForm } from '@/utils/common_bak2.js';
  import { CirclePlus } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    paperId: String,
    routeType: String,
    readonlyVal: Boolean, //是否只读
    /** 修改回显的数据 */
    data: Object
  });
  //
  const { currentRoute, push } = useRouter();
  const { params, path, meta } = unref(currentRoute);
  let pathArray = path.split('/');
  let routeType = pathArray[3];

  /** 提交状态 */
  const loading = ref(false);

  /** 是否是修改 */
  const isUpdate = ref(false);
  /** 表单实例 */
  const formRef = ref(null);

  // 试卷数据
  const paperData = ref([]);

  const showPerSelector = ref(false);
  const perSelectedData = ref([]);
  const openPerSelector = () => {
    showPerSelector.value = true;
  };

  const onDoneSelector = (data) => {
    perSelectedData.value = data;
    form.sfzdry = '是';
    form.selectorData = data;
    console.log('onDoneSelector form :>> ', form);
  };

  const qddxData = computed(() => {
    let allDataCode = [];
    if (perSelectedData.value.length > 0) {
      perSelectedData.value.forEach((e) => {
        allDataCode.push(e.dictDataName ? e.dictDataName : e.name);
      });
    }
    return allDataCode.join(',');
  });

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0
  });

  const formItems = ref([]);

  /** 步骤一提交 */
  const submit = () => {
    console.log('submit form :>> ', form);
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      form.survey.name = form.surveyName;
      loading.value = true;
      operation(form)
        .then((data) => {
          loading.value = false;
          if (data) {
            EleMessage.success(
              isUpdate.value ? '问卷维护成功' : '问卷创建成功'
            );
            updateModelValue(false);
          }
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('done');
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        perSelectedData.value = [];

        let baseColumns = comColumns(routeType) || [];
        baseColumns = baseColumns.concat([
          {
            prop: 'selectorData',
            slot: 'selectorData',
            type: 'selectorData',
            label: '测评对象'
          }
        ]);
        baseColumns.splice(1, 1, {
          prop: 'surveyName',
          slot: 'surveyName',
          label: '问卷名称',
          type: 'input'
        });
        formItems.value = baseColumns.filter(
          (item) => item.prop && !item.disabled
        );
        const formInitData = generateForm(formItems.value);
        if (props.data) {
          perSelectedData.value = props.data.selectorData;
          assignFields({
            ...formInitData,
            ...props.data,
            surveyName: props.data?.survey?.name
          });
          isUpdate.value = true;
        } else {
          assignFields({
            ...formInitData,
            survey: {}
          });
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
