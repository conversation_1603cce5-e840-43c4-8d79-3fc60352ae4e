<template>
  <ele-page hide-footer  flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     highlight-current-row
                     tooltip-effect="light"
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="openEdit()"> 新建
          </el-button>
          <el-button size="small"
                     plain
                     class="ele-btn-icon"
                     @click="remove()"> 删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="点我查看问卷详情" placement="left" effect="light">
            <el-link type="primary"
                     underline="never"
                     @click="openEdit(row)">
              {{ row.survey.name }}
            </el-link>
          </ele-tooltip>
        </template>
        <template #status="{ row }">
          <el-switch v-model="row.status"
                     size="small"
                     inline-prompt
                     active-text="启用"
                     active-value="启用"
                     inactive-text="结束"
                     inactive-value="结束"
                     @change="handleChangeState($event,row)"/>
        </template>
        <template #action="{ row }">
          <el-link type="primary" underline="never" @click="handlePaperPerview(row)">
            预览
          </el-link>
          <template v-if="row.status==='启用'&&!row.answered">
            <el-divider direction="vertical"/>
            <el-link type="primary" underline="never" @click="openEdit(row)">
              编辑
            </el-link>
          </template>
          <el-divider v-if="row.status==='结束'" direction="vertical"/>
          <el-link v-if="row.status==='结束'" type="primary" underline="never" @click="openDataTongJi(row)">
            数据统计
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <Step1Edit v-model="showStep1Edit" :paperId="pid" :readonlyVal="readonlyVal" :data="current"
               :routeType="routeType"
               @done="handleDonePaper"/>

    <PaperPerview v-model="showPaperPerview" :paperId="pid" enterType="perviewPaperXlzx"
                  :RandomString="RandomString" @done="reload"/>
  </ele-page>
</template>

<script setup>
import {reactive, ref, unref, watch,} from 'vue';
import {ElMessageBox} from 'element-plus/es';
import { ElMessage as EleMessage} from "element-plus";
import {querySurveyManagePage, removes} from './api/index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import Step1Edit from "./components/step1-edit.vue"
import {generateRandomString} from "@/utils/common_bak2.js";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {comColumns} from "./utils/index.js";
import {useDictData} from "@/utils/use-dict-data.js";
import PaperPerview from "@/views/exam/components/paper-preview.vue";
import {ElLoading} from "element-plus";

const {removePageTab, getRouteTabKey, setPageTab} = usePageTab();

const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);
const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

let currentProject = null
let pathArray = path.split("/")
console.log(pathArray)
let routeType = pathArray[3];
// 已缓存的字典
const {dicts} = storeToRefs(userStore);

/** 表格实例 */
const tableRef = ref(null);
const showPaperPerview = ref(false);

const pid = ref(null)
/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showStep1Edit = ref(false);
const RandomString = ref(null)

/** 加载状态 */
const loading = ref(false);
/** 试卷预览 */
const handlePaperPerview = (row) => {
  RandomString.value = generateRandomString(10)
  pid.value = row.sid;
  showPaperPerview.value = true;
}

const handleChangeState = (value, row) => {
  console.log(value, row)
}

/** 表格列配置 */
const columns = ref([]);

/** 列表选中数据 */
const selections = ref([]);
const readonlyVal = ref(false);

/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  let whereData = {
    ...where, ...orders, ...filters,
    'param.page': page,
    'param.limit': limit,
    // 'approvalNode.nodeId': currentNodeId.value,
  }
  return querySurveyManagePage(whereData);
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  readonlyVal.value = false;
  pid.value = row?.id;
  current.value = row ?? null;
  showStep1Edit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.survey.name).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
     const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

const handleDonePaper = () => {
  reload()
};

watch(
  () => dicts.value['xlzxSurveyGid'],
  (val) => {
    if (val) {
      console.log('字典加载完成：', val);

      columns.value = comColumns(routeType, val).concat([
        {
          prop: 'survey.status',
          label: '状态',
        }, {
          columnKey: 'action',
          label: '操作',
          slot: 'action',
          minWidth: 120
        }]);
    } else {
      useDictData(['xlzxSurveyGid'], {
        dictFieldUrl: "/survey/survey-questions-calss",
        valueField: "id",
        textField: "name",
        params: {type: routeType}
      });
    }
  },
  {immediate: true}
);
</script>

<script>
export default {
  name: 'XLZXTESTPAPERINDEX'
};
</script>
