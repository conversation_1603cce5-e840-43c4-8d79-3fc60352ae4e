export function comColumns(routeType, dictsValueData) {
  console.log('路径：', routeType);
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'survey.name',
      slot: 'name',
      label: '问卷名称',
      type: 'input',
    },
    {
      prop: 'gid',
      label: '测评问卷',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: "xlzxSurveyGid",
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: "/survey/survey-questions-calss",
          valueField: "id",
          textField: "name",
          params: {type: routeType}
        }
      },
      formatter: (row) => {
        let existData = dictsValueData.filter(obj => obj.dictDataCode === row.gid)
        return existData.length > 0 ? existData[0].dictDataName : '';
      },
    },
    {
      prop: 'lblb',
      label: '量表类别',
      showFlag: '是',
      selfModifyFlag: '是',
      type: 'radioButton',
      options: [
        {label: '日常测评', value: '日常测评'},
        {label: '普查量表', value: '普查量表'},
      ],
      required: true,
    },
    {
      prop: 'pclx',
      label: '普查类型',
      type: 'input',
    },
    {
      prop: 'kdtcs',
      label: '可答题次数',
      type: 'inputNumber',
      hideFlag: 'list',
      colProps: {span: 12}
    },
    {
      prop: 'sfyxckcpjg',
      label: '是/否允许查看测评结果',
      type: 'switch',
      colProps: {span: 12}
    },
  ]
}
