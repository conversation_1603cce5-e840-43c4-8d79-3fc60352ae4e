export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      slot: 'ZxrPersonInfo',
      type: 'ZxrPersonInfo',
      label: '被上报人',
      typeKey: 'select',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      tip: '请选择或输入被上报人信息',
      colProps: {span: 12},
    },

    {
      prop: 'pyccmc',
      label: '培养层次',
      disabled: true,
    },
    {
      prop: 'xymc',
      label: '院系',
      disabled: true,
    },
    {
      prop: 'zymc',
      label: '专业',
      disabled: true,
    },
    {
      prop: 'njmc',
      label: '年级',
      disabled: true,
    },
    {
      prop: 'bjmc',
      label: '班级',
      disabled: true,
    },
    {
      prop: 'wjlx',
      label: '危机类型',
      type: 'dictSelect',
      typeKey: 'select',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: {span: 12},
      props: {
        code: "xlzxwjlx",
        filterable: true,
        dicQueryParams: {
          getValType: 'name'
        },
      }
    },
    {
      prop: 'wjfssj',
      label: '危机发生时间',
      type: 'datetime',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: {span: 12},
    },
    {
      prop: 'wjfsdd',
      label: '危机发生地点',
      type: 'input',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      colProps: {span: 12},
    },
    {
      prop: 'wjsjms',
      label: '危机事件/问题描述',
      type: 'textarea',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
  ]
}
