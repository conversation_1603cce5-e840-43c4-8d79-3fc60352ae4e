<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table
              :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0">
        <template #toolbar>
          <ele-tabs type="plain"
                    size="small"
                    v-model="activeName"
                    :items="tabsItems">
            <template #label="{ item, label}">
              <span>{{ label }}</span>
            </template>
          </ele-tabs>
        </template>
        <template #ZxrPersonInfo="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="openEdit(row)">
              {{ row.xm + '-' + row.xgh }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" enterType="preview" @done="reload" :RandomString="RandomString"/>
  </ele-page>
</template>

<script setup>
import {computed, reactive, ref, unref, watch,} from 'vue';
import {getXljkCrisisReportPageApprovalList, removes} from '../api/index.js';
import {useRouter} from "vue-router";
import {usePageTab} from "@/utils/use-page-tab.js";
import {ElMessageBox, ElMessage as EleMessage, ElLoading} from "element-plus";
import Edit from "../components/edit.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";
import {comColumns} from "../utils/index.js";
import {generateRandomString} from "@/utils/common_bak2.js";

const {removePageTab, getRouteTabKey, setPageTab,} = usePageTab();
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const {currentRoute, push} = useRouter();
const {path} = unref(currentRoute);

/** 表格实例 */
const tableRef = ref(null);

/** 加载状态 */
const loading = ref(false);

const RandomString = ref(null)
/** 是否显示表单弹窗 */
const showEdit = ref(false);
const activeName = ref("待审批");

const tabsItems = computed(() => {
  return [
    {
      label: '待审批',
      name: '待审批',
      disabled: false,
      sort: 1
    },
    {
      label: '已通过',
      name: '已通过',
      sort: 2
    },
    {
      label: '不通过',
      name: '不通过',
      sort: 2
    },
  ]
})

/** 编辑回显数据 */
const editData = ref(null);
/** 打开编辑弹窗 */
const openEdit = (row,) => {
  RandomString.value = generateRandomString(10)
  editData.value = row ?? null;
  showEdit.value = true;
};

/** 删除单个 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除“' + rows.map((d) => d.xm).join(', ') + '”吗?',
    '系统提示',
    {type: 'warning', draggable: true}
  ).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: '请求中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success('删除成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {
  });
};

/** 是否显示导入弹窗 */
const showImport = ref(false);
/** 表格列配置 */
const columns = ref([]);
columns.value = comColumns()

/** 列表选中数据 */
const selections = ref([]);
/** 用户名筛选值 */
const nameFilterValue = ref('');

/** 用户名筛选事件 */
const onNameFilter = (name) => {
  nameFilterValue.value = name;
  doReload();
};

/** 表格搜索参数 */
const lastWhere = reactive({});
/** 表格搜索 */
const doReload = () => {
  if (nameFilterValue.value) {
    reload({
      ...lastWhere,
      name: nameFilterValue.value
    });
  } else {
    reload(lastWhere);
  }
};
/** 表格数据源 */
const datasource = ({page, limit, where, orders, filters}) => {
  return getXljkCrisisReportPageApprovalList({
    ...where, ...orders, ...filters,
    'report.page': page,
    'report.limit': limit,
    'approvalNode.result': activeName.value
  })
};


/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    tableRef["value"]?.reload?.({page: 1, where});
  } else {
    //编辑提交table不全局刷新
    tableRef["value"]?.reload?.();
  }
};

watch(
  activeName,
  (newVal) => {
    if (newVal) reload()
  },
  {immediate: true}
)

</script>

<script>
export default {
  name: 'XLJKCRISISREPORT'
};
</script>
