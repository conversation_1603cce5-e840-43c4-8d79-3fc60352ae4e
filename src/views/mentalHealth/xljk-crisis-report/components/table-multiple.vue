<template>
  <ele-table-select size="small"
                    filterable
                    clearable
                    placeholder="请选择被上报人(支持根据姓名/学工号搜索)"
                    value-key="xgh"
                    label-key="xm"
                    v-model="internalValue"
                    :table-props="tableProps"
                    @filterChange="handleFilterChange"
                    @visibleChange="handleVisibleChange"
                    :popper-width="580"
                    :popper-options="{ strategy: 'fixed' }"
                    @change="updateSelection">
  </ele-table-select>
</template>

<script setup>
import {watch, ref, reactive} from 'vue';
import {getAppointmentInfoPagePerson} from "@/views/mentalHealth/xljk-appointment-info/api/index.js";
import {queryPage} from "@/views/personInfo/st/api/index.js";

const emit = defineEmits(['change']);
const props = defineProps({
  value: String,
  RandomString: String,
});

const internalValue = ref();

/** 双向绑定外部值 */
watch(() => props.value, (val) => {
  internalValue.value = val;
}, {immediate: true, deep: true});

/** 更新并通知父组件 */
const updateSelection = (val) => {
  internalValue.value = val;
  emit('change', val);
};

let allData = [];

/** 表格配置 */
const tableProps = reactive({
  datasource: [],
  columns: [
    {
      type: 'index',
      columnKey: 'index',
      width: 48,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'xm',
      label: '姓名',
      width: 190
    },
    {
      prop: 'xgh',
      label: '学工号',
    },
    {
      prop: 'xymc',
      label: '学院名称',
    },
    {
      prop: 'zymc',
      label: '专业名称',
    },
    {
      prop: 'njmc',
      label: '年级名称',
    },
    {
      prop: 'bjmc',
      label: '班级名称',
    },
  ],
  showOverflowTooltip: true,
  toolbar: false,
  pagination: {
    pageSize: 6,
    layout: 'total, prev, pager, next, jumper',
    style: {padding: '0px'}
  },
  rowClickChecked: true
});

/** 加载状态 */
const loading = ref(false);

// const prefixKeys = (obj, prefix = 'userInfo.') => {
//   const result = {};
//   for (const key in obj) {
//     if (obj.hasOwnProperty(key)) {
//       result[`${prefix}${key}`] = obj[key];
//     }
//   }
//   return result;
// }

/** 表格搜索参数 */
const lastWhere = reactive({});
const currentPage = ref(1)
const pageSize = 20
// 初始化数据
const initLoad = async () => {
  loading.value = true;
  try {
    let newObject = {
      'page': currentPage.value,
      'limit': pageSize,
    }
    // if (lastWhere.value) {
    //   newObject = {
    //     ...newObject,
    //     // ...prefixKeys(lastWhere.value)
    //   }
    // }
    const result = await queryPage('student', newObject);
    tableProps.datasource = result.list || [];
    allData = result.list || [];
  } catch (e) {
    EleMessage.error(e.message || '加载失败');
  } finally {
    loading.value = false;
  }
};


/** 筛选输入框值改变事件 */
const handleFilterChange = (keyword) => {
  tableProps.datasource = allData.filter((d) => {
    return (
      d.xm?.includes?.(keyword) || d.xgh?.includes?.(keyword)
    );
  });
};

/** 下拉框展开状态改变事件 */
const handleVisibleChange = (visible) => {
  if (visible) {
    tableProps.datasource = allData;
  }
};

watch(() => props.RandomString, async (val) => {
  console.log(val)
  if (val) {
    await initLoad();
  }
}, {immediate: true, deep: true})

</script>
