<template>
  <ele-drawer size="50%"
              :title="isUpdate ? '修改危机上报 [' + data.xm + ']' : '添加危机上报'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px' }"
              @update:modelValue="updateModelValue">
    <div :class="{ 'border-only-bottom-mode': enterType === 'preview' }">
      <pro-form ref="formRef"
                size="small"
                :model="form"
                :items="mergedFormItems"
                :grid="{ span: 24 }"
                labelWidth="auto"
                label-position="top"
                @updateValue="setFieldValue">
        <template #ZxrPersonInfo="{ item, model, updateValue }">
          <el-input v-if="isUpdate || enterType === 'preview'"
                    v-model="form.xm"
                    :readonly="true"/>
          <TableMultiple v-else
                         :RandomString="RandomString"
                         placeholder="请选择或输入被上报人"
                         @change="handleChangeVal"/>
        </template>
      </pro-form>
    </div>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small"
                 type="primary"
                 plain
                 :loading="loading"
                 @click="saveApprove"
                 v-if="enterType === 'preview'">
        提交
      </el-button>
      <el-button v-else size="small"
                 type="primary"
                 plain
                 :loading="loading"
                 @click="save"
                 :disabled="enterType === 'preview'">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {ElMessage as EleMessage} from 'element-plus'
import {useFormData} from '@/utils/use-form-data.js'
import {approve, operation} from '../api/index.js'
import {comColumns} from '../utils/index.js'
import ProForm from '@/components/ProForm/index.vue'
import {generateForm} from '@/utils/common_bak2.js'
import TableMultiple from './table-multiple.vue'

const emit = defineEmits(['done', 'update:modelValue'])

const props = defineProps({
  modelValue: Boolean,
  data: Object,
  paperId: String,
  RandomString: String,
  enterType: String // preview：详情预览
})

const isUpdate = ref(false)
const loading = ref(false)
const formRef = ref(null)
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0
})

const formItems = ref([])

const approvalItems = ref([
  {
    prop: 'result',
    label: '审核结果',
    type: 'radioButton',
    options: [
      {label: '待审批', value: '待审批'},
      {label: '已通过', value: '已通过'},
      {label: '不通过', value: '不通过'},
    ],
    showFlag: '是',
    selfModifyFlag: '是',
    required: true,
    colProps: {span: 12},
  },
  {
    prop: 'remark',
    label: '审核意见',
    type: 'textarea',
    showFlag: '是',
    selfModifyFlag: '是',
    required: true
  }
])

// 计算属性：合并表单项
const mergedFormItems = computed(() => {
  let items = [...formItems.value]

  // 如果是预览模式，添加审批项
  if (props.enterType === 'preview') {
    items = [...items, ...approvalItems.value]
  }

  return items
})

const save = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    const data = {...form}
    operation(data).then((msg) => {
      loading.value = false
      EleMessage.success(msg)
      updateModelValue(false)
      emit('done', isUpdate.value, data)
    }).catch((e) => {
      loading.value = false
      EleMessage.error(e.message)
    })
  })
}


const saveApprove = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return
    }
    loading.value = true
    const data = {...form}
    approve(data).then((msg) => {
      loading.value = false
      EleMessage.success(msg)
      updateModelValue(false)
      emit('done', isUpdate.value, data)
    }).catch((e) => {
      loading.value = false
      EleMessage.error(e.message)
    })
  })
}

const handleChangeVal = (value) => {
  form.xgh = value
}

const updateModelValue = (value) => {
  emit('update:modelValue', value)
}

// 初始化字段属性
const initFieldProps = (field) => {
  if (!field.props) {
    field.props = {}
  }
  if (props.enterType === 'preview') {
    field.showFlag = "readonly"
    field.props.readonly = true
    if (['select', 'switch', 'radioButton'].includes(field.type)) {
      field.props.disabled = true
    }
  }
  return field
}

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      let baseColumns = comColumns() || []
      formItems.value = baseColumns
        .filter((item) => item.prop && !item.disabled)
        .map(initFieldProps)

      const formInitData = generateForm(formItems.value)
      if (props.data) {
        assignFields({...formInitData, ...props.data, crisisReportId: props.data.id})
        isUpdate.value = true
      } else {
        resetFields(formInitData)
        isUpdate.value = false
      }
    } else {
      resetFields()
      formRef.value?.clearValidate?.()
    }
  }
)
</script>

<style lang="scss" scoped>
@import "@/css/border-only-bottom.css";
</style>
