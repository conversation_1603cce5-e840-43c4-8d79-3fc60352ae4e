import request from '@/utils/request';

export async function getXljkCrisisReport(params) {
  const res = await request.get('/mentalHealth/xljk-crisis-report', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function getXljkCrisisReportPage(params) {
  const res = await request.get('/mentalHealth/xljk-crisis-report/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询心理危机上报审批列表（权限标识：mentalHealth:xljkCrisisReport:approvalList）
 * @param params
 * @returns {Promise<*>}
 */
export async function getXljkCrisisReportPageApprovalList(params) {
  const res = await request.get('/mentalHealth/xljk-crisis-report/pageApprovalList', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkCrisisReportInfo(id) {
  const res = await request.get('/mentalHealth/xljk-crisis-report/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改心理危机上报审核（权限标识：mentalHealth:xljkCrisisReport:approve）
 * @param data
 * @returns {Promise<*>}
 */
export async function approve(data) {
  const res = await request.post('/mentalHealth/xljk-crisis-report/approve', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-crisis-report/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-crisis-report/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
