import request from '@/utils/request';

/**
 * 分页查询
 */
export async function queryPageInterpretation(params) {
  const res = await request.get('/mentalHealth/xljk-interpretation/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkInterpretation(params) {
  const res = await request.get('/mentalHealth/xljk-interpretation', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getXljkInterpretationInfo(id) {
  const res = await request.get('/mentalHealth/xljk-interpretation/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-interpretation/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-interpretation/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
