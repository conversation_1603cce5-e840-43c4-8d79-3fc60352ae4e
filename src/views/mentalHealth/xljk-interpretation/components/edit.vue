<!-- 结果解释编辑 -->
<template>
  <ele-drawer size="43%"
              :title="isUpdate ? '修改'+form.itemName : '添加'"
              :append-to-body="true"
              style="max-width: 100%"
              :model-value="modelValue"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="updateModelValue">
    <el-form ref="formRef"
             :model="form"
             :rules="rules"
             size="small"
             label-position="top"
             label-width="100px"
             @submit.prevent="">
      <el-form-item label="指标名称" prop="itemName">
        <el-input clearable
                  :maxlength="20"
                  v-model="form.itemName"
                  placeholder="请输入指标名称"
                  controls-position="right"
                  class="ele-fluid"/>
      </el-form-item>
      <el-form-item label="测评问卷" prop="gid">
                <dict-data placeholder="请选择入测评问卷"
                           v-model="form.gid"
                           code="xlzxSurveyGid"
                           type="select"
                           :dicQueryParams="{
                              dictFieldUrl:'/survey/survey-questions-calss',
                              valueField: 'id',
                              textField:'name',
                              params: {type: routeType}
                            }"/>
      </el-form-item>
      <el-row :gutter="4">
        <el-col :span="12">
          <el-form-item label="最小值" prop="minScore">
            <el-col :span="12">
              <el-input-number :min="0"
                               :max="99999"
                               v-model="form.minScore"
                               placeholder="请输入最小值"
                               controls-position="right"
                               class="ele-fluid"/>
            </el-col>
            <el-col :span="12">
              <el-select clearable
                         v-model="form.minScoreOperator"
                         placeholder="请选择最小值运算符号"
                         class="ele-fluid">
                <el-option value="小于">小于</el-option>
                <el-option value="小于等于">小于等于</el-option>
                <el-option value="大于">大于</el-option>
                <el-option value="大于等于">大于等于</el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大值" prop="maxScore">
            <el-col :span="12">
              <el-input-number :min="0"
                               :max="99999"
                               v-model="form.maxScore"
                               placeholder="请输入最大值"
                               controls-position="right"
                               class="ele-fluid"/>
            </el-col>
            <el-col :span="12">
              <el-select clearable
                         v-model="form.maxScoreOperator"
                         placeholder="请选择最大值运算符号"
                         class="ele-fluid">
                <el-option value="小于">小于</el-option>
                <el-option value="小于等于">小于等于</el-option>
                <el-option value="大于">大于</el-option>
                <el-option value="大于等于">大于等于</el-option>
              </el-select>
            </el-col>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="分数解释">
        <el-input :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  v-model="form.remark"
                  placeholder="请输入分数解释"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, reactive, watch, computed} from 'vue';
import { ElMessage as EleMessage} from "element-plus";
import {useFormData} from '@/utils/use-form-data.js';
import {operation} from '../api/index.js';
import {CirclePlus} from "@element-plus/icons-vue";
import PerSelector from "@/components/PerSelector/index.vue";
import FileUpload from '@/components/FileUpload/index.vue';
import {isImageFile, toFormData, toFormDataWj} from '@/utils/common_bak2.js';
import {getPersonIdNumber} from "@/views/personInfo/st/api/index.js";

const BASE_URL = import.meta.env.BASE_URL;
const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object,
  /** 试卷ID*/
  paperId: String,
  routeType: String,
});

/** 是否是修改 */
const isUpdate = ref(false);

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields, setFieldValue] = useFormData({
  id: void 0,
  sort: void 0,
  itemName: '',
  remark: '',
  gid: '',
  minScore: null,
  minScoreOperator: '大于等于',
  maxScore: null,
  maxScoreOperator: '小于等于',

});

/** 表单验证规则 */
const rules = reactive({
  itemName: [
    {
      required: true,
      message: '请输入指标名称',
      type: 'string',
      trigger: 'blur'
    }
  ],
  gid: [
    {
      required: true,
      message: '请选择测评问卷',
      type: 'string',
      trigger: 'change'
    }
  ]
});

/** 保存编辑 */
const save = () => {
  formRef["value"]?.validate?.((valid) => {
    if (!valid) {
      return;
    }
    loading.value = true;
    console.log(JSON.stringify(form))
    operation(form).then((msg) => {
      loading.value = false;
      EleMessage.success(msg);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  (modelValue) => {
    if (modelValue) {
      resetFields();
      if (props.data) {
        assignFields({
          ...props.data,
        });
        isUpdate.value = true;
      } else {
        isUpdate.value = false;
      }
    } else {
      resetFields();
      formRef["value"]?.clearValidate?.();
    }
  }
);
</script>
