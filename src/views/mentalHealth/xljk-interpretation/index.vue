<template>
  <ele-page hide-footer flex-table>
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }">
      <ele-pro-table ref="tableRef"
                     row-key="id"
                     :columns="columns"
                     :datasource="datasource"
                     :border="true"
                     :show-overflow-tooltip="true"
                     v-model:selections="selections"
                     tooltip-effect="light"
                     highlight-current-row
                     :footer-style="{ paddingBottom: '3px' }"
                     style="padding-bottom: 0"
                     v-loading="isLoading">
        <template #toolbar>
          <el-button size="small" plain class="ele-btn-icon" @click="handleEdit" :disabled="isLoading">
            新建
          </el-button>
          <el-button size="small" plain class="ele-btn-icon" @click="remove(row)"
                     :disabled="!selections.length || isLoading">
            删除
          </el-button>
        </template>
        <template #itemName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never"
                     @click="handleEdit(row)"
                     :disabled="isLoading">
              {{ row.itemName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" :route-type="currentRouteType" @done="reload" :key="editComponentKey"/>
  </ele-page>
</template>

<script setup>
import {ref, computed, watch, nextTick, onMounted, onUnmounted, onActivated} from 'vue';
import {useRoute, useRouter, onBeforeRouteLeave} from 'vue-router';
import {ElLoading, ElMessage as EleMessage} from 'element-plus';
import {ElMessageBox} from 'element-plus';
import {queryPageInterpretation, removes} from './api/index.js';
import Edit from './components/edit.vue';
import {useUserStore} from '@/store/modules/user.js';
import {storeToRefs} from 'pinia';
import {useDictData} from '@/utils/use-dict-data.js';

const userStore = useUserStore();
const {dicts} = storeToRefs(userStore);
const route = useRoute();
const router = useRouter();

const tableRef = ref(null);
const showEdit = ref(false);
const editData = ref(null);
const selections = ref([]);
const columns = ref([]);
const isLoading = ref(false);
const editComponentKey = ref(0);
const pageError = ref(null);

let abortController = null;
const isInited = ref(false);

const currentRouteType = computed(() => route.path.split('/')[3] || '');


const initColumns = (dictsValueData = []) => [
  {type: 'selection', columnKey: 'selection', width: 45, align: 'center', fixed: 'left'},
  {
    prop: 'itemName',
    slot: 'itemName',
    label: '指标名称',
    minWidth: 120
  },
  {
    prop: 'gid',
    label: '测评问卷',
    minWidth: 150,
    formatter: (row) => {
      const existData = dictsValueData.filter(obj => obj.dictDataCode === row.gid);
      return existData.length > 0 ? existData[0].dictDataName : '--';
    },
  },
  {
    prop: 'minScore',
    label: '最小值',
    width: 100
  },
  {
    prop: 'minScoreOperator',
    label: '最小值运算符号',
    width: 120
  },
  {
    prop: 'maxScore',
    label: '最大值',
    width: 100
  },
  {
    prop: 'maxScoreOperator',
    label: '最大值运算符号',
    width: 120
  },
  {
    prop: 'remark',
    label: '分数解释',
    minWidth: 200
  },
];

const abortAllRequests = () => {
  try {
    abortController?.abort?.();
  } catch (e) {
    console.warn('取消请求失败:', e);
  } finally {
    abortController = null;
  }
};

const resetComponentState = () => {
  showEdit.value = false;
  editData.value = null;
  selections.value = [];
  columns.value = [];
  isLoading.value = false;
  pageError.value = null;
  editComponentKey.value++;
};

const doInit = async () => {
  if (isInited.value) return;
  isInited.value = true;

  abortAllRequests();
  abortController = new AbortController();
  resetComponentState();
  isLoading.value = true;

  try {
    if (!dicts.value['xlzxSurveyGid']) {
      await useDictData(['xlzxSurveyGid'], {
        dictFieldUrl: "/survey/survey-questions-calss",
        valueField: "id",
        textField: "name",
        params: {type: currentRouteType.value},
        signal: abortController.signal
      });
    }

    columns.value = initColumns(dicts.value['xlzxSurveyGid'] || []);
    await nextTick();
    await tableRef.value?.reload?.({signal: abortController.signal});
  } catch (error) {
    if (error.name !== 'AbortError') {
      pageError.value = error;
      EleMessage.error('数据加载失败: ' + error.message);
    }
  } finally {
    isLoading.value = false;
  }
};


const handleEdit = (row = null) => {
  if (isLoading.value) return;
  editData.value = row ? {...row} : {minScore: 0, maxScore: 100};
  showEdit.value = true;
};

const remove = async (row = null) => {
  if (isLoading.value) return;

  const rows = row ? [row] : selections.value;
  if (!rows.length) return EleMessage.error('请至少选择一条数据');
  console.log(rows, row)
  try {
    await ElMessageBox.confirm(`确定要删除 "${rows.map(d => d.itemName).join(', ')}" 吗?`, '系统提示', {
      type: 'warning',
      draggable: true
    });

    const loading = ElLoading.service({
      lock: true,
      text: '删除中...',
      background: 'rgba(0, 0, 0, 0.1)'
    });

    await removes(rows.map(d => d.id));
    loading.close();
    EleMessage.success('删除成功');
    await reload();
  } catch (error) {
    if (error !== 'cancel') {
      EleMessage.error(error.message || '删除失败');
    }
  }
};

const datasource = async ({page, limit, where, orders, filters}) => {
  try {
    const signal = new AbortController().signal;
    return await queryPageInterpretation({page, limit, ...where, ...orders, ...filters}, {signal});
  } catch (error) {
    if (error.name !== 'AbortError') throw error;
    return {list: [], total: 0};
  }
};

const reload = async (where) => {
  try {
    selections.value = [];
    await nextTick();
    const params = where ? {page: 1, where} : {};
    await tableRef.value?.reload?.(params);
  } catch (error) {
    EleMessage.error('刷新数据失败: ' + error.message);
  }
};

watch(
  () => route.fullPath,
  (newVal, oldVal) => {
    if (newVal !== oldVal && route.path.split('/')[3] !== (oldVal?.split('/')[3])) {
      isInited.value = false;
      doInit();
    }
  },
  {immediate: true}
);

onMounted(() => {
  isInited.value = false;
  doInit();
});

onActivated(() => {
  isInited.value = false;
  doInit();
});

onUnmounted(() => {
  abortAllRequests();
  resetComponentState();
  tableRef.value = null;
});

onBeforeRouteLeave(() => {
  abortAllRequests();
  resetComponentState();
});
</script>

<style scoped>
</style>
