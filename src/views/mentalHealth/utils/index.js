import dayjs from "dayjs";
import {dayJsFormatDate} from "@/utils/dayJs.js";
import {groupArr} from "@/utils/common_bak2.js";

export function baseWeekDays() {
  return ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
}

/**
 * 生成一周日期（周一到周日）
 * @param date
 * @returns {[]}
 */
export function generateWeekDays(date = new Date()) {
  const days = []
  const dayOfWeek = date.getDay() // 0=周日
  const monday = new Date(date)
  monday.setDate(date.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
  for (let i = 0; i < 7; i++) {
    const d = new Date(monday)
    d.setDate(monday.getDate() + i)
    days.push({
      date: d,
      dateStr: d.toISOString().split('T')[0],
      week: baseWeekDays()[i],
      displayDate: `${d.getMonth() + 1}/${d.getDate()}`
    })
  }
  return days
}

/**
 * 两个日期判断是否是同一天
 * @param date1
 * @param date2
 * @returns {boolean}
 */
export function isSameDay(date1, date2) {
  return dayjs(date1).isSame(dayjs(date2), 'day');
}

/**
 * 初始化日期对象
 * @param date
 * @param isCurrentMonth
 * @param holidayMap
 * @param events
 * @returns {{date, dateStr: string, isToday: boolean, isHoliday: (boolean|*), isCurrentMonth, holiday: (*|string), events: ([{kyyrs: *, lable: string}]|[])}}
 */
export function createDayObject(date, isCurrentMonth, holidayMap, events) {
  const dateKey = dayJsFormatDate(date);
  const holidayInfo = holidayMap[dateKey];
  return {
    date,
    dateStr: dayjs(date).format('YYYY-MM-DD'),
    isCurrentMonth,
    isToday: isSameDay(date, new Date()),
    isHoliday: holidayInfo?.isHoliday || false,
    holiday: holidayInfo?.label || '',
    events: getEventsForDay(date, events)
  };
}

/**
 * 按周分组
 * @param arr
 * @param size
 * @returns {[]}
 */
export function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
};


const getEventsForDay = (date, events) => {
  let eventsData = events.filter(event => isSameDay(new Date(event.gzrq), date));
  let newData = groupArr(eventsData, 'xlzxEvent');
  return newData.length > 0 ? [{
    lable: newData.length + '位咨询师在班',
    kyyrs: declaredTotalMonth(newData),
  }] : [];
};

const declaredTotalMonth = (data) => {
  return data.reduce((sum, consultant) => sum + consultant.totalCapacity, 0);
};

