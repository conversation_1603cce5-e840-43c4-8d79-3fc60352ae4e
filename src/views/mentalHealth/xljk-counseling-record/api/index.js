import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getCounselingRecordPage(params) {
  const res = await request.get('/mentalHealth/xljk-counseling-record/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getCounselingRecord(params) {
  const res = await request.get('/mentalHealth/xljk-counseling-record', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function getCounselingRecordInfo(id) {
  const res = await request.get('/mentalHealth/xljk-counseling-record/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 编辑
 */
export async function operation(data) {
  const res = await request.post('/mentalHealth/xljk-counseling-record/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 预约记录状态变更
 * @param data
 * @returns {Promise<*>}
 */
export async function xljkUpdateStatus(data) {
  const res = await request.post('/mentalHealth/xljk-counseling-record/updateStatus', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/mentalHealth/xljk-appointment-info/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
