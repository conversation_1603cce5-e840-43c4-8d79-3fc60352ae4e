<template>
  <ele-drawer size="50%"
              style="max-width: 100%"
              title="咨询记录维护"
              :append-to-body="false"
              :model-value="dialogVisible"
              :body-style="{ paddingBottom: '8px'}"
              @update:modelValue="closeDialog()">
    <el-form ref="formRef"
             :model="form"
             label-position="top"
             size="small">
      <el-form-item label="主诉问题" prop="zswt"
                    :rules="[{
                required: true,
                message: '请填写主诉问题',
                trigger: 'change',
                validator: validateZswt
              }]">
        <el-input v-model="form.zswt"
                  :rows="5"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  placeholder="来访者自述的主要问题；问题的持续时间、触发因素；对生活的影响程度。"/>
        <!--        如：学业压力（考试焦虑、学习动力不足）；人际关系（同伴冲突、师生关系、校园欺凌）；家庭问题（亲子矛盾、离异家庭影响）；行为表现（注意力不集中、逃课、自伤行为）；情绪问题（抑郁、焦虑、躯体化症状）。-->
      </el-form-item>
      <el-form-item label="观察与评估">
        <el-input v-model="form.gcypg"
                  :rows="6"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  placeholder="情绪状态（如焦虑、抑郁、愤怒等级）；行为表现（肢体语言、表情、语言流畅度等）；认知模式（是否存在不合理信念）；风险评估（自杀/自伤/伤人倾向等，需标注等级）。"/>
      </el-form-item>
      <el-form-item label="采用的咨询方法和技术">
        <el-input v-model="form.cydzxffhjs"
                  :rows="3"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  placeholder="采用的咨询方法和技术（如CBT、焦点解决、正念等）"/>
      </el-form-item>
      <el-form-item label="咨询过程">
        <el-input v-model="form.zxgc"
                  :rows="5"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  placeholder="来访者的反应与互动；关键对话摘要（需匿名化处理敏感信息）。"/>
      </el-form-item>
      <el-form-item label="总结与备注">
        <el-input v-model="form.zjybz"
                  :rows="5"
                  type="textarea"
                  show-word-limit
                  maxlength="255"
                  placeholder="填写总结与备注"/>
      </el-form-item>
      <el-row :gutter="8">
        <el-col :md="7">
          <el-form-item label="是否存在自杀/自伤风险">
            <el-switch v-model="form.sfczzszsfx"
                       inline-prompt
                       active-text="是"
                       active-value="是"
                       inactive-text="否"
                       inactive-value="否"/>
          </el-form-item>
        </el-col>
        <el-col :md="7">
          <el-form-item label="是否存在伤害他人风险">
            <el-switch v-model="form.sfczshtrfx"
                       inline-prompt
                       active-text="是"
                       active-value="是"
                       inactive-text="否"
                       inactive-value="否"/>
          </el-form-item>
        </el-col>
        <el-col :md="10">
          <el-form-item label="是否需要跟进">
            <el-switch v-model="form.sfxygj"
                       inline-prompt
                       active-text="是"
                       active-value="是"
                       inactive-text="否"
                       inactive-value="否"/>
            <el-date-picker v-if="form.sfxygj==='是'"
                            v-model="form.jzrq"
                            type="date"
                            placeholder="选择跟进日期"
                            style="margin-left: 20px;"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="必要措施" prop="actions">
        <!-- 附加措施 -->
        <el-checkbox-group v-model="form.bycsStr">
          <!--          <el-checkbox value="crisisIntervention">启动危机干预流程</el-checkbox>-->
          <el-checkbox value="列入重点关注学生名单">列入重点关注学生名单</el-checkbox>
          <el-checkbox value="通知家长">通知家长</el-checkbox>
          <el-checkbox value="通知学校相关部门">通知学校相关部门</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button size="small" @click="dialogVisible=false">取消</el-button>
      <el-button size="small" type="primary"
                 :loading="loading" @click="saveConsultationRecord">保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import {ref, computed, watch,} from 'vue'
import {ElMessage} from "element-plus";
import {useFormData} from "@/utils/use-form-data.js";
import {operation} from "./api/index.js";
import { ElMessage as EleMessage} from "element-plus";

const emits = defineEmits(['done', 'update:visible']);
const props = defineProps({
  /** 弹窗是否打开 */
  visible: Boolean,
  RandomString: String,
  /** 最后一次预约信息 */
  LastAppointmentInfo: Object,
  enterType: String,//manager-咨询师管理预约记录入口
});

const RandomString = ref(null)
let dialogVisible = computed({
  get() {
    return props.visible
  },
  set() {
    closeDialog()
  }
})

const closeDialog = () => {
  emits('update:visible', false)
  emits('done', props.enterType)
}

/** 提交状态 */
const loading = ref(false);

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, setFieldValue, assignFields] = useFormData({
  id: null,
  appointmentInfoId: '',//预约记录ID
  zswt: '',
  gcypg: '',
  cydzxffhjs: '',
  zxgc: '',
  zjybz: '',
  sfczzszsfx: '否',
  sfczshtrfx: '否',
  bycsStr: [],//必要措施
  bycs: '',//必要措施
  sfxygj: '否',
  jzrq: '',
});

const validateZswt = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请填写主诉问题'))
  } else {
    callback()
  }
}

const saveConsultationRecord = async () => {
  // 首先验证整个表单
  await formRef.value.validate();
  if (form.bycsStr.length > 0) form.bycs = form.bycsStr.join(',')
  console.log(JSON.stringify(form))
  loading.value = true;
  operation(form).then((res) => {
    if (res) {
      loading.value = false;
      ElMessage.success('访谈记录已保存')
      closeDialog()
    }
  }).catch((e) => {
    loading.value = false;
    EleMessage.error(e.message);
  }).finally(() => {
    closeDialog()
  });
}

watch(() => props.RandomString, (val) => {
  if (val) {
    if (props.LastAppointmentInfo) {
      //预约记录ID
      setFieldValue({'appointmentInfoId': props.LastAppointmentInfo?.id});
    }
  }
}, {immediate: true, deep: true})

</script>

<style lang="scss" scoped>
.radio-vertical {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 两列 */
  gap: 12px 16px;
}

.radio-vertical :deep(.el-radio) {
  min-width: 160px;
  margin: 0; /* 去掉默认右边距 */
  white-space: normal; /* 允许内容换行 */
}

.radio-vertical :deep(.el-checkbox) {
  min-width: 133px;
}
</style>
