<!-- 统计卡片 -->
<template>
  <ele-check-card :items="cardOptData2" :row="{ gutter: 6 }" style="margin: 3px;">
    <template #item="{ item }">
      <div style="flex: 1; padding: 10px;min-height: 70px">
        <div style="display: flex; align-items: center">
          <ele-text type="secondary" style="flex: 1">{{ item.title }}</ele-text>
          <ele-tooltip v-if="item.content" :content="item.content" placement="top" :offset="6" effect="light">
            <IconPark :name="item.icon" size="20" strokeWidth="3"/>
          </ele-tooltip>
        </div>
        <ele-text style="font-size: 36px;">{{ item.countVal }}</ele-text>
      </div>
    </template>
  </ele-check-card>
</template>

<script setup>
import {computed} from 'vue';
import {
  QuestionCircleOutlined,
} from '@/components/icons/index.js';
import IconPark from "@/components/IconPark/index.vue";

let cardOptData2 = computed(() => {
  let baseData = [
    {
      id: 6,
      title: '待审批数',
      content: '统计说明。',
      countVal: '1,846',
      icon: 'help',
      isDisplay: true,
      onClick: () => {
      }
    },
    {
      id: 3,
      title: '审核已通过数',
      content: '',
      countVal: '8,846',
      icon: 'file-settings',
      isDisplay: true,
      onClick: () => {
      }
    },
    {
      id: 2,
      title: '应测评人数',
      content: '',
      countVal: '14,846',
      icon: 'setting-config',
      isDisplay: true,
      onClick: () => {
      }
    },
    {
      id: 2,
      title: '未测评人数',
      content: '',
      countVal: '746',
      icon: 'setting-config',
      isDisplay: true,
      onClick: () => {
      }
    },
  ]

  return baseData.map((d) => {
    return {
      ...d,
      col: {md: 6, sm: 12, xs: 24, style: {marginBottom: '6px',}}
    };
  });
})
</script>

<style lang="scss" scoped>
.statistics-card {
  :deep(.ele-card-body) {
    cursor: pointer;
    padding: 16px !important;
  }

  :deep(.el-divider) {
    margin: 12px 0;
    opacity: 0.6;
  }

  .statistics-header {
    display: flex;
    align-items: center;

    .statistics-header-text {
      flex: 1;
    }

    .statistics-header-tip {
      font-size: 15px;
      cursor: help;
    }
  }

  .statistics-value {
    margin-top: 4px;
  }

  .statistics-body {
    height: 36px;
    display: flex;
    padding-top: 18px;
    box-sizing: border-box;
  }

  .statistics-trend-text {
    display: flex;
    align-items: center;
    white-space: nowrap;
    word-break: break-all;
    overflow: hidden;

    .el-icon {
      font-size: 16px;
      margin-left: 4px;
    }

    & + .statistics-trend-text {
      margin-left: 14px;
    }
  }

  .statistics-footer {
    display: flex;
    align-items: center;
  }
}
</style>
