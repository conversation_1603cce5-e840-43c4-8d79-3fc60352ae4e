<template>
  <div class="day-view-container">
    <ele-table size="small" border style="min-width: 600px; table-layout: fixed;">
      <thead style="display: block; background: #f5f7fa;">
      <tr style="display: flex;">
        <th style="width: 100px; flex-shrink: 0; height: 50px; line-height: 50px;">
          咨询师
        </th>
        <th style="flex-grow: 1; height: 50px; line-height: 50px;">{{ currentDayText }}</th>
      </tr>
      </thead>
      <tbody :style="{height: (pageHeight-90)+'px', overflow:'auto'}">
      <tr v-for="(tslot, index) in consultantData" :key="index" style="display: flex; min-height: 56px;">
        <td class="time-label"
            style="width: 100px; flex-shrink: 0; display: flex; align-items: center; justify-content: center; border-right: 1px solid #ebeef5;">
          <el-tooltip content="点击查看咨询师信息" effect="light" placement="right">
            <div class="consultant-name" @click="showConsultantDetail(tslot)">
              {{ tslot.xm }}
              <IconPark name="avatar" size="16" strokeWidth="3"/>
            </div>
          </el-tooltip>
        </td>
        <td style="flex-grow: 1; position: relative; padding: 2px;"
            @click="handleCellClick(tslot)">
          <div v-if="events.length > 0">
            <ele-check-card :items="getEventsForTimeSlot(tslot)"
                            :row="{ gutter: 6 }" style="margin: 3px;">
              <template #item="{ item }">
                <div style="display: flex; padding: 3px; min-height: 28px">
                  <div style="flex: 1; min-width: 0;">
                    <ele-text size="md">可接待: {{ item.kyyrs }}人</ele-text>
                  </div>
                  <IconPark name="other" size="20" strokeWidth="3"/>
                </div>
              </template>
            </ele-check-card>
          </div>
        </td>
      </tr>
      </tbody>
    </ele-table>
  </div>
</template>

<script setup>
import {computed,} from 'vue';
import IconPark from "@/components/IconPark/index.vue";
import {dayJsFormatDate} from "@/utils/dayJs.js";
import {groupArr} from "@/utils/common_bak2.js";

const props = defineProps({
  /** 咨询师数据 */
  consultantData: {
    type: Array,
    required: true
  },
  events: {
    type: Array,
    default: () => []
  },
  currentDate: {
    type: Date,
    required: true
  },
  pageHeight: {
    type: Number,
    required: true
  },
  currentRoleScope: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['showConsultantDetail', 'cellClick']);

const currentDayText = computed(() => {
  return dayJsFormatDate(props.currentDate, 'YYYY-MM-DD dddd');
});

const getEventsForTimeSlot = (data) => {
  let eventsData = props.events.filter(event => event.xm === data.xm);
  let newData = groupArr(eventsData, 'xlzxEvent');
  return newData.length > 0 ? [{
    kyyrs: declaredTotalMonth(newData),
    col: {md: 4, sm: 12, xs: 12, style: {marginBottom: '1px'}}
  }] : [];
};

const declaredTotalMonth = (data) => {
  return data.reduce((sum, consultant) => sum + consultant.totalCapacity, 0);
};

const showConsultantDetail = (consultant) => {
  emit('showConsultantDetail', consultant);
};

const handleCellClick = (zxsxx) => {
  emit('cellClick', zxsxx);
}

</script>
<style lang="scss" scoped>
@import "@/views/mentalHealth/css/index.css";
</style>

