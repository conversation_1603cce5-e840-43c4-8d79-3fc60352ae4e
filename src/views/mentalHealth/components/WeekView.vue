<template>
  <div class="week-view-container">
    <ele-table size="small" border style="min-width: 600px; table-layout: fixed;">
      <thead style="display: block;">
      <tr style="display: flex;">
        <th style="width: 100px; flex-shrink: 0;height: 50px;">咨询师</th>
        <th v-for="day in weekDays" :key="day.dateStr" style="flex-grow: 1;height: 50px;">
          {{ day.week }}<br/>{{ day.displayDate }}
        </th>
      </tr>
      </thead>
      <tbody :style="{height: (pageHeight-100)+'px', overflow:'auto'}">
      <tr v-for="(tslot, index) in consultantData" :key="index" style="display: flex; min-height: 56px;">
        <td class="time-label"
            style="width: 100px; flex-shrink: 0; display: flex; align-items: center; justify-content: center; border-right: 1px solid #ebeef5;">
          <el-tooltip content="点击查看咨询师信息" effect="light" placement="right">
            <div class="consultant-name" @click="showConsultantDetail(tslot)">
              {{ tslot.xm }}
              <IconPark name="avatar" size="16" strokeWidth="3"/>
            </div>
          </el-tooltip>
        </td>

        <td v-for="(day, dayIndex) in weekDays"
            :key="day.dateStr"
            style="flex-grow: 1; min-height: 75px; position: relative;cursor: pointer">
          <ele-check-card :items="getEventsForDayAndTime(day.date, tslot.xm)" :row="{ gutter: 6 }"
                          style="margin: 3px;"
                          class="day-event">
            <template #item="{ item }">
              <div style="display: flex; padding: 3px; min-height: 28px"
                   @click="handleCellClick(day.date, tslot)">
                <div style="flex: 1; min-width: 0;">
                  <ele-text size="md">可接待: {{ item.kyyrs }}人</ele-text>
                </div>
                <IconPark name="other" size="20" strokeWidth="3"/>
              </div>
            </template>
          </ele-check-card>
        </td>
      </tr>
      </tbody>
    </ele-table>
  </div>
</template>

<script setup>
import IconPark from "@/components/IconPark/index.vue";
import { dayJsFormatDate, isTimeInRange } from "@/utils/dayJs.js";
import { groupArr } from "@/utils/common_bak2.js";
import dayjs from "dayjs";
import {isSameDay} from "@/views/mentalHealth/utils/index.js";

const props = defineProps({
  weekDays: {
    type: Array,
    required: true
  },
  consultantData: {
    type: Array,
    required: true
  },
  events: {
    type: Array,
    default: () => []
  },
  currentDate: {
    type: Date,
    required: true
  },
  pageHeight: {
    type: Number,
    required: true
  },
  currentRoleScope: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['showConsultantDetail', 'cellClick']);

const getEventsForDayAndTime = (date, xm) => {
  const currentDateStr = dayJsFormatDate(date);
  const slotStart = new Date(`${currentDateStr}`);
  let eventsData = props.events.filter(event => {
    try {
      const eventDateStr = event.gzrq;
      const eventStart = new Date(`${eventDateStr}`);
      const eventEnd = new Date(`${eventDateStr}`);
      if (!isSameDay(eventStart, slotStart)) {
        return false;
      }
      return isTimeInRange(slotStart, eventStart, eventEnd) && event.xm === xm;
    } catch (e) {
      console.error('事件时间解析错误:', e);
      return false;
    }
  });
  let newData = groupArr(eventsData, 'xlzxEvent');
  return newData.length > 0 ? [{
    kyyrs: declaredTotalMonth(newData),
  }] : [];
};


const declaredTotalMonth = (data) => {
  return data.reduce((sum, consultant) => sum + consultant.totalCapacity, 0);
};

const showConsultantDetail = (consultant) => {
  emit('showConsultantDetail', consultant);
};

const handleCellClick = (date, zxsxx) => {
  emit('cellClick', { date, zxsxx });
};
</script>

<style lang="scss" scoped>
@import "@/views/mentalHealth/css/index.css";
</style>

