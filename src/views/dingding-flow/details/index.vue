<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '6px!important', overflow: 'hidden' }"
    >
      <el-form
        :model="form"
        label-width="100px"
        style="padding: 10px 10px 0 10px; flex-shrink: 0"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="系统模块">
              <el-select
                v-model="form.moduleCode"
                placeholder="请选择系统模块"
                @change="handleModuleChange"
                style="width: 100%"
              >
                <el-option
                  v-for="item in sysModuleList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="8">
            <el-form-item label="年份">
              <el-input v-model="form.year" disabled />
            </el-form-item>
          </el-col>-->
        </el-row>
      </el-form>
      <section
        class="dingflow-design"
        :style="{ height: '100%', overflow: 'auto' }"
      >
        <div class="zoom">
          <div
            class="zoom-out"
            :class="nowVal === 50 && 'disabled'"
            @click="zoomSize(1)"
          ></div>
          <span>{{ nowVal }}%</span>
          <div
            class="zoom-in"
            :class="nowVal === 300 && 'disabled'"
            @click="zoomSize(2)"
          ></div>
        </div>
        <div class="box-scale" :style="`transform: scale(${nowVal / 100});`">
          <nodeWrap
            v-model:nodeConfig="nodeConfig"
            v-model:flowPermission="flowPermission"
          />
          <div class="end-node">
            <div class="end-node-circle"></div>
            <div class="end-node-text">流程结束</div>
          </div>
        </div>
      </section>
    </ele-card>
    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <template #extra>
        <el-button size="small" @click="onBack">取消</el-button>
        <el-button
          size="small"
          type="primary"
          plain
          :loading="loading"
          @click="saveSet"
        >
          提交
        </el-button>
      </template>
    </ele-bottom-bar>
    <!-- 审批人设置-->
    <approverDrawer />
    <!-- 条件设置-->
    <conditionDrawer />
  </ele-page>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import { useRouter } from 'vue-router';
  import { getWorkFlowData } from '@/plugins/api.js';
  import { mapMutations } from '@/plugins/lib.js';
  import approverDrawer from '@/components/DrawFlow/drawer/approverDrawer.vue';
  import conditionDrawer from '@/components/DrawFlow/drawer/conditionDrawer.vue';
  import { getWorkFlowInfo, operation } from '../api/index.js';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useUserStore } from '@/store/modules/user.js';
  import { list as listSysModule } from '../../system/module-setup/api/index.js';

  const userStore = useUserStore();

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTab, reloadPageTab } =
    usePageTab();

  const { setTableId, setIsTried } = mapMutations();

  let workflowId = null;
  let workflowName = null;
  let storageData = localStorage.getItem('WORKFLOW');
  let params = storageData ? JSON.parse(storageData) : null;
  workflowId = params?.workflowId;
  workflowName = params?.workflowName;

  const tipList = ref([]);
  const tipVisible = ref(false);
  const nowVal = ref(100);
  const processConfig = ref({});
  const nodeConfig = ref({});
  const flowPermission = ref([]);
  const setNewConfigData = ref({});

  const form = ref({
    projectId: '',
    moduleCode: '',
    year: ''
  });
  const sysModuleList = ref([]);

  const handleModuleChange = (selectedCode) => {
    const selected = sysModuleList.value.find(
      (item) => item.code === selectedCode
    );
    if (selected) {
      form.value.year = selected.year;
    } else {
      form.value.year = '';
    }
  };

  onMounted(async () => {
    listSysModule({ sfgllc: '是' }).then((data) => {
      sysModuleList.value = data || [];
    });
    console.log('workflowId===', workflowId);
    if (workflowId) {
      setPageTab({
        key: getRouteTabKey(),
        title: workflowName + '流程设置'
      });

      getWorkFlowInfo(workflowId)
        .then((list) => {
          // console.log(JSON.stringify(list))
          if (list) {
            // form.value.projectId = list.projectId;
            form.value.moduleCode = list.moduleCode;
            form.value.year = list.year;
            setNewConfigData.value = {
              tableId: list.id,
              nodeConfig: {
                nodeName: list.name,
                type: -1,
                settype: '',
                conditionDetail: [],
                nodeUserList: null,
                childNode: list.workflowNodes,
                conditionNodes: []
              }
            };
            processConfig.value = setNewConfigData.value;
            setTableId(list.id);
            nodeConfig.value = setNewConfigData.value.nodeConfig;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else {
      let { data } = await getWorkFlowData();
      processConfig.value = data;
      let { nodeConfig: nodes, tableId } = data;

      nodeConfig.value = nodes;
      setTableId(tableId);
    }
  });

  const reErr = ({ childNode }) => {
    if (childNode) {
      let { type, error, nodeName, conditionNodes } = childNode;
      console.log(type, error, nodeName, conditionNodes);
      if (type === -1 || type === 0) {
        if (error) {
          tipList.value.push({
            name: nodeName,
            type: ['', '审核人', '抄送人'][type + 1]
          });
        }
        reErr(childNode);
      } else if (type === 1) {
        reErr(childNode);
      } else if (type === 2) {
        reErr(childNode);
        for (var i = 0; i < conditionNodes.length; i++) {
          if (conditionNodes[i].error) {
            tipList.value.push({
              name: conditionNodes[i].nodeName,
              type: '条件'
            });
          }
          reErr(conditionNodes[i]);
        }
      }
    } else {
      childNode = null;
    }
  };

  /** 提交状态 */
  const loading = ref(false);
  const saveSet = async () => {
    setIsTried(true);
    tipList.value = [];
    reErr(nodeConfig.value);
    if (tipList.value.length !== 0) {
      tipVisible.value = true;
    } else {
      processConfig.value.flowPermission = flowPermission.value;
      let FlowData = processConfig.value;
      let workflowData = {
        id: workflowId,
        name: FlowData.nodeConfig.nodeName, //流程名称
        // projectId: form.value.projectId,//项目ID
        moduleCode: form.value.moduleCode, //模块编码
        year: form.value.year, //年份
        workflowNodes: FlowData.nodeConfig.childNode
      };
      console.log('workflowData===', JSON.stringify(workflowData));
      loading.value = true;
      operation(workflowData)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          onBack();
          // emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  const zoomSize = (type) => {
    if (type === 1) {
      if (nowVal.value === 50) {
        return;
      }
      nowVal.value -= 10;
    } else {
      if (nowVal.value === 300) {
        return;
      }
      nowVal.value += 10;
    }
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    reloadPageTab({ fullPath: '/dingding-flow' });
    // push('/dingding-flow');
  };
</script>

<style>
  @import '../../../css/workflow.css';

  .error-modal-list {
    width: 455px;
  }
</style>
