<template>
  <ele-drawer size="84%"
              title="流程查看"
              :append-to-body="true"
              :destroy-on-close="true"
              :close-on-click-modal="false"
              style="max-width: 100%;"
              :body-style="{ padding: 'unset!important'}"
              :model-value="modelValue"
              @update:modelValue="updateModelValue">
    <Workflow :workflowId="workflowId" :pageHeight="pageHeight" :workFlowReadonly="true"/>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
import Workflow from "@/views/dingding-flow/components/workflow.vue";
import {useUserStore} from "@/store/modules/user.js";
import {storeToRefs} from "pinia";


const props = defineProps({
  /** 页面高度 */
  pageHeight: Number,
  workflowId: String,
  modelValue: Boolean // ✅ 这里是关键，添加这一行

});
const userStore = useUserStore();
const {pageHeight} = storeToRefs(userStore);

const emit = defineEmits(['done', 'update:modelValue']);

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

</script>
<style>
@import "../../../css/workflow.css";

.error-modal-list {
  width: 455px;
}
</style>
