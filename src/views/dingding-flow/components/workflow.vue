<template>
  <!--  <ele-page :style="{padding:'8px!important',overflow: 'hidden'}">-->
  <div :style="{height: (pageHeight)+'px', overflow:'auto'}">
    <section class="dingflow-design">
      <div class="zoom">
        <div class="zoom-out" :class="nowVal === 50 && 'disabled'" @click="zoomSize(1)"></div>
        <span>{{ nowVal }}%</span>
        <div class="zoom-in" :class="nowVal === 300 && 'disabled'" @click="zoomSize(2)"></div>
      </div>
      <div class="box-scale" :style="`transform: scale(${ nowVal / 100});`">
        <nodeWrap v-model:nodeConfig="nodeConfig" v-model:flowPermission="flowPermission"
                  :workFlowReadonly="workFlowReadonly"/>
        <div class="end-node">
          <div class="end-node-circle"></div>
          <div class="end-node-text">流程结束</div>
        </div>
      </div>
    </section>
    <!-- 审批人设置-->
    <approverDrawer :directorMaxLevel="directorMaxLevel" :currentProject="currentProject"
                    @update:nodeConfig="updateNodeConfig"/>
    <!--      &lt;!&ndash; 抄送人设置 &ndash;&gt;-->
    <!--      <copyerDrawer/>-->
    <!-- 条件设置-->
    <conditionDrawer/>
  </div>
  <!--  </ele-page>-->
</template>

<script setup>
import {ref, onMounted, watch} from 'vue';
import {getWorkFlowData} from '@/plugins/api.js';
import {mapMutations} from '@/plugins/lib.js';
import {getWorkFlowInfo} from '@/views/dingding-flow/api/index.js';
import approverDrawer from '@/components/DrawFlow/drawer/approverDrawer.vue';
import conditionDrawer from '@/components/DrawFlow/drawer/conditionDrawer.vue';

const {setTableId, setIsTried} = mapMutations();
let emits = defineEmits(['done', 'update:modelValue', 'update:flowPermission', 'update:nodeConfig']);

const props = defineProps({
  /** 页面高度 */
  pageHeight: Number,
  workflowId: String,
  currentProject: Object,
  workFlowReadonly: {
    type: Boolean,
    default: false,
  },
});

const tipList = ref([]);
const tipVisible = ref(false);
const nowVal = ref(100);
const processConfig = ref({});
const nodeConfig = ref({});
const workFlowDef = ref({});
const flowPermission = ref([]);
const directorMaxLevel = ref(0);
const setNewConfigData = ref({});
onMounted(async () => {
  console.log('workflowId===', props.workflowId);
  if (props.workflowId) {
    if (props.workflowId === 'workFlowSelected') {
      queryWorkFlowDataInit();
    } else {
      getWorkFlowInfoById(props.workflowId);
    }
  } else {
    queryWorkFlowDataInit();
  }
});
/**
 * 根据流程ID获取流程模版
 * @param workflowId
 */
const getWorkFlowInfoById = (workflowId) => {
  getWorkFlowInfo(workflowId).then((list) => {
    if (list) {
      setNewConfigData.value = {
        'tableId': list.id,
        'nodeConfig': {
          'nodeName': list.name,
          'type': -1,
          'settype': '',
          'conditionDetail': [],
          'nodeUserList': null,
          'childNode': list.workflowNodes,
          // "childNode": {
          //   "nodeName": "",
          //   "error": true,
          //   "type": 1,
          //   "settype": 2,
          //   "childNode": {},
          //   "nodeUserList": null
          // },
          'conditionNodes': [],
        },
      };
      processConfig.value = setNewConfigData.value;
      setTableId(list.id);
      nodeConfig.value = setNewConfigData.value.nodeConfig;
      emits('update:nodeConfig', processConfig.value);
    }
  }).catch((e) => {
    EleMessage.error(e.message);
  });
};
/**
 * 获取初始化的流程模版
 * @returns {Promise<void>}
 */
const queryWorkFlowDataInit = async () => {
  let {data} = await getWorkFlowData();
  processConfig.value = data;
  let {
    nodeConfig: nodes,
    tableId,
  } = data;

  nodeConfig.value = nodes;
  emits('update:nodeConfig', processConfig.value);
  setTableId(tableId);
};

const updateNodeConfig = async (data) => {
  // console.log("updateNodeConfig============1========")
  // console.log(data)
  // console.log(processConfig.value)
  // console.log("updateNodeConfig============2========")
  emits('update:nodeConfig', processConfig.value);
};

const zoomSize = (type) => {
  if (type === 1) {
    if (nowVal.value === 50) {
      return;
    }
    nowVal.value -= 10;
  } else {
    if (nowVal.value === 300) {
      return;
    }
    nowVal.value += 10;
  }
};
/** 更新modelValue */
const updateModelValue = (value) => {
  emits('update:modelValue', value);
};

watch(() => props.workflowId, (workflowId) => {
    if (workflowId) {
      if (workflowId === 'workFlowSelected') {
        queryWorkFlowDataInit();
      } else {
        getWorkFlowInfoById(workflowId);
      }
    }
  },
  {immediate: true},
);


</script>
<style>
@import "../../../css/workflow.css";

.error-modal-list {
  width: 455px;
}
</style>
