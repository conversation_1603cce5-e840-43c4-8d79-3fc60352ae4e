export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'name',
      slot: 'name',
      label: '字段名',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    // {
    //   prop: 'requestType',
    //   label: '申请类型',
    //   minWidth: 110,
    //   type: 'dictSelect',
    //   typeKey: 'select',
    //   props: {
    //     code: 'cancellationMode',
    //     filterable: true,
    //     dicQueryParams: {
    //       dictFieldUrl: 'leave/common/optionalRequestType',
    //       valueField: 'id',
    //       textField: 'text'
    //     }
    //   },
    //   showFlag: '是',
    //   selfModifyFlag: '是',
    //   required: true
    // },
    {
      prop: 'typeText',
      slot: 'typeText',
      label: '字段类型',
      type: 'typeText',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    },
    {
      prop: 'codeTypeText',
      slot: 'codeTypeText',
      label: '加载数据类型',
      type: 'codeTypeText',
      showFlag: '是',
      selfModifyFlag: '是',
      required: false,
      disabled: true
    },
    {
      prop: 'options',
      label: '可选项',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: false,
      disabled: true
    },
    {
      prop: 'required',
      label: '是否必填',
      type: 'switch',
      showFlag: '是',
      selfModifyFlag: '是',
      required: true
    }
  ];
}
