<template>
  <ele-drawer
    size="60%"
    :title="requestTypeText + '流程管理'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-page hide-footer flex-table>
      <ele-card
        flex-table
        :body-style="{
          padding: '0 8px 10px 8px!important',
          overflow: 'hidden'
        }"
      >
        <el-row>
          <el-button
            size="small"
            class="ele-btn-icon"
            @click="openEdit()"
            style="margin-bottom: 10px"
          >
            新建节点
          </el-button>
          <!-- {{ itemList.length }} ==== {{ itemList }} -->
          <ele-table size="small" style="table-layout: fixed; text-align: left">
            <colgroup>
              <col width="20" />
              <col width="78" />
              <col width="78" />
              <col width="30" />
              <col width="30" />
              <col width="40" />
            </colgroup>
            <thead>
              <tr>
                <th></th>
                <th>节点名称</th>
                <th>角色</th>
                <!-- <th>排序</th> -->
                <th>条件启用</th>
                <th>操作</th>
              </tr>
            </thead>
            <vue-draggable
              tag="tbody"
              item-key="id"
              v-model="itemList"
              handle=".sort-handle"
              :animation="300"
              :set-data="() => void 0"
            >
              <!-- @update="updateSort" -->
              <template #item="{ element }">
                <tr>
                  <td style="text-align: center">
                    <ele-text
                      :icon="Rank"
                      class="sort-handle"
                      style="cursor: move"
                    />
                  </td>
                  <td>{{ element.name }}</td>
                  <td>{{ getRolesName(element) }}</td>
                  <!-- <td>
                    {{ element.sortOrder }}
                  </td> -->
                  <td>
                    {{ element.conditionEnabled }}
                  </td>
                  <td>
                    <el-link
                      type="primary"
                      underline="never"
                      @click="openEdit(element)"
                    >
                      修改
                    </el-link>
                    <el-divider direction="vertical" />
                    <el-link
                      type="primary"
                      underline="never"
                      @click="remove(element)"
                    >
                      删除
                    </el-link>
                  </td>
                </tr>
              </template>
            </vue-draggable>
          </ele-table>
        </el-row>
      </ele-card>
      <edit
        v-model="showEdit"
        :data="editData"
        :currentProjectId="currentProjectId"
        :requestTypeId="requestTypeId"
        @done="handleAdd"
      />
    </ele-page>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import {
    getLeaveApprovalRuleNodeList,
    removes,
    operation
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import {
    ElMessageBox,
    ElMessage as EleMessage,
    ElLoading
  } from 'element-plus';
  import Edit from './components/edit.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import VueDraggable from 'vuedraggable';
  import { Rank } from '@element-plus/icons-vue';

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 请假类型ID*/
    currentProjectId: String,
    /** 申请类型Id  */
    requestTypeId: String,
    /** 申请类型text  */
    requestTypeText: String
  });

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const emit = defineEmits(['done']);
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 是否显示表单弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };
  const itemList = ref([]);

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        itemList.value = itemList.value.filter(
          (i) => i.sortOrder !== row.sortOrder
        );
        console.log('确定 :>> ', itemList.value);
      })
      .catch(() => {
        console.log('取消 :>> ');
      });
  };

  /** 列表选中数据 */
  const selections = ref([]);
  const getRolesName = (item) => {
    console.log('item :>> ', item);
    let roles = item.roles || [];
    return roles.length > 0 ? roles.map((i) => i.text).join(',') : '';
  };

  /** 表格数据源 */
  const getDetailInfo = async () => {
    const res = await getLeaveApprovalRuleNodeList({
      projectId: props.currentProjectId,
      requestType: props.requestTypeId
    });
    const list = [].concat(...res);
    list.map((i) => {
      i.conditions.map((c) => {
        c.fieldId = c?.field?.id;
        c.actionId = c?.action?.id;
        c.operatorId = c?.operator?.id;
      });
    });
    itemList.value = list;
    console.log('res', res, itemList.value);
  };

  const handleAdd = (item) => {
    if (item.id) {
      const index = itemList.value.findIndex((i) => i.id == item.id);
      itemList.value.splice(index, 1, item);
    } else {
      itemList.value.push(item);
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const save = () => {
    let stepsList = [];
    itemList.value.map((item) => {
      item.conditions.map((c) => {
        c.operator = {
          id: c.operatorId
        };
        c.field = {
          id: c.fieldId
        };
        c.action = {
          id: c.actionId
        };
      });
      stepsList.push([item]);
    });
    let data = {
      nodeGroups: stepsList,
      projectId: props.currentProjectId,
      requestType: props.requestTypeId
    };
    console.log('data', data);
    loading.value = true;
    operation(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        getDetailInfo();
      } else {
        itemList.value = [];
      }
    }
  );
  watch(
    () => itemList.value,
    (itemList) => {
      console.log('itemList变化了', itemList);
      if (itemList.length > 0) {
        itemList.forEach((item, index) => {
          item.sortOrder = index + 1;
        });
      }
    },
    { deep: true } // 深度监听，可以监测数组内对象的变化
  );
</script>

<script>
  export default {
    name: 'APPROVALRULENODE'
  };
</script>
