<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    size="55%"
    :title="isUpdate ? '修改 [' + data.name + ']' : '添加'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="formItems"
      :grid="{ span: 24 }"
      labelWidth="auto"
      label-position="top"
      @updateValue="setFieldValue"
    >
      <template #conditions="{ item, model, updateValue }">
        <TablePreview
          v-if="modelValue"
          :ref="(el) => getRiskSpreadRef(el, 0)"
          :approverData="model[item.prop]"
          :projectId="currentProjectId"
          :requestType="requestTypeId"
          dataName="条件设置"
          approverType="leaveProcessConditions"
          :RandomString="RandomString"
          @onDoneGroup="handleDoneGroup"
        />
      </template>
      <template #roles="{ item, model, updateValue }">
        <dragsort-list v-model="model[item.prop]" />
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operation } from '../api/index.js';
  import { comColumns } from '../utils/index.js';
  import ProForm from '@/components/ProForm/index.vue';
  import { generateForm, generateRandomString } from '@/utils/common_bak2.js';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
  import DragsortList from '@/views/system/mobile/menu/components/dragsort-list.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 请假类型ID*/
    currentProjectId: String,
    /** 申请类型Id  */
    requestTypeId: String
  });
  let RandomString = ref('');

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0
  });

  const formItems = ref([]);
  const riskSpreadRefList = ref([]);
  const getRiskSpreadRef = (el, index) => {
    if (el) {
      riskSpreadRefList.value[index] = el;
    }
  };
  const formData = ref(null);
  const handleDoneGroup = async (data) => {
    formData.value = data;
    console.log('formData', formData.value);
  };
  /** 保存编辑 */
  const save = () => {
    formRef['value']?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // loading.value = true;
      let data = { ...form, conditions: formData.value };
      // let data = toFormData({...result})
      // data.operator = {
      //   id: 'EQUAL',
      //   text: '等于'
      // };
      console.log('保存', JSON.stringify(data));
      updateModelValue(false);
      // emit('done', isUpdate.value, data);
      emit('done', data);
      // resetFields();
      // formRef['value']?.clearValidate?.();
      // operation(data)
      //   .then((msg) => {
      //     loading.value = false;
      //     EleMessage.success(msg);
      //     updateModelValue(false);
      //     emit('done', isUpdate.value, data);
      //   })
      //   .catch((e) => {
      //     loading.value = false;
      //     EleMessage.error(e.message);
      //   });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => [props.modelValue, props.projectId, props.requestTypeId],
    ([modelValue, projectId, requestTypeId]) => {
      console.log('watch  modelValue:>> ', modelValue, requestTypeId);
      if (modelValue || projectId || requestTypeId) {
        formItems.value = comColumns() || [];
        if (requestTypeId === 'CANCELLATION') {
          formItems.value = formItems.value.filter(
            (item) => item.prop !== 'minDays'
          );
        }
        const formInitData = generateForm(formItems.value);
        console.log(props.data);
        if (props.data) {
          assignFields({ ...formInitData, ...props.data });
          RandomString.value = generateRandomString(10);
          console.log('RandomString.value :>> ', RandomString.value);
          isUpdate.value = true;
          console.log('isUpdate :>> ', true);
        } else {
          console.log('isUpdate :>> ', false);
          resetFields(formInitData); // 一次性reset新的字段
          isUpdate.value = false;
        }
      } else {
        formData.value = null;
        RandomString.value = '';
        resetFields();
        formRef['value']?.clearValidate?.();
      }
    }
  );
</script>
