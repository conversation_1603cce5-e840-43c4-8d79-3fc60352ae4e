import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getLeaveApprovalRuleNodeList(params) {
  const res = await request.get('/leave/project/approval_rule_node/list', {
    params
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post(
    '/leave/project/approval_rule_node/operation',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post(
    '/leave/project/approval_rule_node/remove',
    data
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 条件的可选字段
 */
export async function optionalConditionField(params) {
  const res = await request.get(
    '/leave/project/approval_rule_node/optionalConditionField',
    { params }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 条件的可选策略
 */
export async function optionalConditionAction(params) {
  const res = await request.get(
    '/leave/project/approval_rule_node/optionalConditionAction',
    { params }
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
