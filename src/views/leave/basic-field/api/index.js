import request from '@/utils/request';

/**
 * 分页查询
 */
export async function getLeaveBasicFieldPage(params) {
  const res = await request.get('/leave/basic_field/page', {params});
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询
 */
export async function getLeaveBasicFieldInfo(id) {
  const res = await request.get('/leave/basic_field/' + id);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

export async function operation(data) {
  const res = await request.post('/leave/basic_field/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/leave/basic_field/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
