export function comColumns() {
  return [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
    },
    {
      prop: 'fieldZh',
      slot: 'fieldZh',
      label: '字段',
      type: 'fieldZh',
      // typeKey: 'select',
      // props: {
      //   code: 'cancellationMode',
      //   filterable: true,
      //   dicQueryParams: {
      //     dictFieldUrl: 'leave/basic_field/optionalDictionaryField',
      //     valueField: "fieldEn",
      //     textField: "fieldZh",
      //   },
      // },
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
    },
    {
      prop: 'fieldEn',
      label: '字段英文名',
      type: 'input',
      minWidth: 110,
      showFlag: '是',
      selfModifyFlag: '是',
      required: true,
      disabled: true,
    },
    {
      prop: 'alias',
      label: '显示名',
      type: 'input',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true,
      colProps: {span: 12},
    },
    {
      prop: 'display',
      label: '是否显示',
      type: 'switch',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true,
      colProps: {span: 6},
    },
    {
      prop: 'condition',
      label: '是否用于条件判断',
      type: 'switch',
      showFlag: '否',
      selfModifyFlag: '否',
      required: true,
      colProps: {span: 6},
    },
  ];
}
