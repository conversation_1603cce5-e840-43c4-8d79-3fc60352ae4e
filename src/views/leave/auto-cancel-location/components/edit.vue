<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    size="50%"
    :title="isUpdate ? '修改 [' + data.name + ']' : '添加'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <!-- <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="formItems"
      :grid="{ span: 24 }"
      labelWidth="auto"
      label-position="top"
      @updateValue="setFieldValue"
    /> -->
    <div class="address-library-section">
      <!-- {{ selectedAddressInfo }} -->
      <el-form
        ref="formRef"
        @submit.prevent=""
        size="small"
        label-width="84px"
        style="margin: 3px 6px 0"
        :model="formAddress"
        :rules="rulesFormRef"
      >
        <el-form-item label="选择模式">
          <el-radio-group v-model="formAddress.mode">
            <el-radio-button value="PresetAddress"
              >预设地址库选取</el-radio-button
            >
            <el-radio-button value="Map">地图选取</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formAddress.mode === 'PresetAddress'"
          label="地址范围"
          label-width="84px"
          prop="presetAddressId"
        >
          <!-- multiple -->
          <ele-table-select
            size="small"
            clearable
            placeholder="从地址库选择"
            value-key="id"
            label-key="name"
            v-model="formAddress.presetAddressId"
            :table-props="tableProps"
            :popper-width="550"
          />
          <!-- :cache-data="cachedAddressData" -->
        </el-form-item>
      </el-form>
    </div>
    <ele-card
      :body-style="{ padding: '0!important', height: 'auto' }"
      v-if="formAddress.mode === 'Map'"
    >
      <MapInit
        ref="child"
        :pageHeight="pageHeight - 140"
        :moduleParam="moduleParam"
        :onlyPoi="true"
        @updateAddress="handleUpdateAddress"
      />
    </ele-card>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="save"
      >
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch, reactive, computed } from 'vue';
  import { ElMessage as EleMessage } from 'element-plus';
  import { ElMessageBox } from 'element-plus/es';
  import { useFormData } from '@/utils/use-form-data.js';
  import { operation, selectablePresetAddressPage } from '../api/index.js';
  import MapInit from '@/views/checkin/address/map/index.vue';
  import { comColumns } from '../utils/index.js';
  import ProForm from '@/components/ProForm/index.vue';
  import { generateForm } from '@/utils/common_bak2.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 请假类型ID*/
    projectId: String
  });

  // 地址库表格配置
  const tableProps = reactive({
    datasource: [],
    columns: [
      // {
      //   type: 'selection',
      //   width: 45,
      //   fixed: 'left',
      //   reserveSelection: true
      // },
      {
        prop: 'name',
        label: '签到位置',
        width: 190
      },
      {
        prop: 'type',
        label: '类型',
        formatter: (row) => {
          const types = {
            poi: '圆形',
            Polygon: '多边形',
            Rectangle: '矩形'
          };
          return types[row.type] || row.type;
        }
      },
      {
        prop: 'fwbj',
        label: '范围半径(米)',
        sortable: true
      }
    ],
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper'
    }
  });
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const formAddress = reactive({
    mode: 'PresetAddress', // 默认从预设地址库选取
    presetAddressId: void 0 // 预设地址id
  });
  /** 表单验证规则 */
  const rulesFormRef = reactive({
    presetAddressId: [
      {
        required: true,
        message: '请选择地址范围',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });
  const child = ref(null);
  /** 表单数据 */
  // const [form, resetFields, assignFields, setFieldValue] = useFormData({
  //   id: void 0
  // });

  const formItems = ref([]);
  /** 保存编辑 */
  const save = () => {
    console.log('save :>> ', formAddress, selectedAddressInfo.value);
    let data = {};
    if (formAddress.mode === 'PresetAddress') {
      formRef['value']?.validate?.((valid) => {
        if (!valid) {
          return;
        }
        data = {
          projectId: props.projectId,
          name: selectedAddressInfo.value.name,
          longitude: selectedAddressInfo.value.jwdzb
            ? selectedAddressInfo.value.jwdzb.split('#')[0]
            : '',
          latitude: selectedAddressInfo.value.jwdzb
            ? selectedAddressInfo.value.jwdzb.split('#')[1]
            : '',
          radius: selectedAddressInfo.value.fwbj,
          detail: selectedAddressInfo.value.bz,
          presetAddressId: selectedAddressInfo.value.id,
          syncToPresetAddress: '否'
        };
        submit(data);
      });
    } else if (formAddress.mode === 'Map') {
      console.log('child.value.getMapAddress()', child.value.getMapAddress());
      if (
        child.value &&
        child.value.getMapAddress() &&
        child.value.getMapAddress().jwdzb
      ) {
        const mapAddress = child.value.getMapAddress();
        data = {
          projectId: props.projectId,
          name: mapAddress.name,
          longitude: mapAddress.jwdzb ? mapAddress.jwdzb.split('#')[0] : '',
          latitude: mapAddress.jwdzb ? mapAddress.jwdzb.split('#')[1] : '',
          radius: mapAddress.fwbj,
          detail: mapAddress.bz
          // type: mapAddress.type
        };
        ElMessageBox.confirm('是否将该地址同步到地址库？', '确认操作', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        })
          .then(() => {
            data.syncToPresetAddress = '是';
            submit(data);
          })
          .catch(() => {
            data.syncToPresetAddress = '否';
            submit(data);
          });
      } else {
        EleMessage.error('请从地图选取地址');
        return;
      }
    }
  };
  const submit = async (data) => {
    console.log('data', JSON.stringify(data));
    loading.value = true;
    operation(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done', isUpdate.value, data);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  // 初始化加载地址库数据
  const loadAddressData = async () => {
    let data = {
      type: 'poi'
    };
    try {
      let datasource = await selectablePresetAddressPage(data);
      tableProps.datasource = datasource.list;
      // emit('update:isAddAddress', false);
    } catch (e) {
      console.error('加载地址数据失败:', e);
    }
  };
  const selectedAddressInfo = computed(() => {
    return tableProps.datasource.find(
      (item) => item.id === formAddress.presetAddressId
    );
  });
  const resetFields = () => {
    formAddress.mode = 'PresetAddress'; // 默认从预设地址库选取
    formAddress.presetAddressId = void 0; // 预设地址id
  };
  watch(
    () => props.modelValue,
    (modelValue) => {
      loadAddressData();
      if (modelValue) {
        formItems.value = comColumns() || [];
        resetFields();
        formRef['value']?.clearValidate?.();
      } else {
        resetFields();
      }
    }
  );
</script>
