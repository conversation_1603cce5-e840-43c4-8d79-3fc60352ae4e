<!-- 切换角色弹框 -->
<template>
  <ele-modal
    :width="560"
    title="切换角色"
    :append-to-body="true"
    :model-value="modelValue"
    :body-style="{ padding: '15px !important' }"
    @update:modelValue="updateModelValue"
  >
    <ele-check-card
      :items="data"
      :item-style="{ width: '47%', margin: '6px 6px 6px 1px', padding: '10px' }"
      style="display: flex; flex-wrap: wrap"
    >
      <template #item="{ item }">
        <div style="display: flex" @click="changeRoleClick(item)">
          <div style="flex: 1; overflow: hidden; padding-left: 2px">
            <ele-text
              size="md"
              style="font-weight: 600; font-size: 13px !important; width: 100%"
              :class="currentRole?.id === item.id ? 'active-role' : ''"
            >
              <el-button
                type="primary"
                plain
                style="margin-right: 6px; width: auto"
                size="small"
                :disable-transitions="true"
              >
                {{ item.roleScope }}
              </el-button>
              {{ item.name }}
            </ele-text>
            <ele-text
              size="sm"
              type="placeholder"
              style="
                line-height: 16px !important;
                padding-top: 5px;
                width: auto;
              "
            >
              {{ item.remark }}
            </ele-text>
          </div>
        </div>
      </template>
    </ele-check-card>
  </ele-modal>
</template>

<script setup>
  import {
    setCurrentRole,
    setIsChangeRoleClick,
    getCurrentRole
  } from '@/utils/current-role-util';
  import { switchRole } from '@/api/layout';
  import { ElMessage as EleMessage } from 'element-plus';
  import { useRouter } from 'vue-router';

  const { push, currentRoute } = useRouter();
  /** 当前用户已选中的角色*/
  const currentRole = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
  console.log('currentRoles :>> ', currentRole);
  const emit = defineEmits(['update:modelValue']);
  const props = defineProps({
    modelValue: Boolean,
    /** 角色数据 */
    data: Array
  });

  /** 用户切换角色 */
  const changeRoleClick = (role) => {
    /** 获取角色数据 */
    switchRole(role.id)
      .then(() => {
        let newObj = {
          realName: null,
          roleId: role.id,
          roleName: role.name
        };
        setCurrentRole(newObj);
        updateModelValue(false);
        setIsChangeRoleClick(true);
        //当角色切换完成后，返回首页并重新加载
        location.reload();
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
</script>
<style lang="scss" scoped>
  .active-role {
    color: var(--el-color-primary);
  }
</style>
