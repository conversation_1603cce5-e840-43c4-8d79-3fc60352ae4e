<!-- 用户信息 -->
<template>
  <ele-dropdown :items="downItems"
                :icon-props="{ size: 15 }"
                :popper-options="{
                      modifiers: [{ name: 'offset', options: { offset: [0, 5] } }]
                   }"
                @command="onUserDropClick">
    <div class="header-avatar">
      <el-avatar :size="25"
                 :src="loginUser.avatar"
                 :icon="loginUser.avatar ? void 0 : UserOutlined"
                 style="transform: translateY(-1px)"/>
      <div class="hidden-sm-and-down"
           style="margin-left: 5px; line-height: 1.5">
        {{ loginUser.xm }} - <span>[{{ currentRoles.name }}]</span>
      </div>
      <el-icon :size="13" style="margin: 0 -4px 0 2px">
        <ArrowDown/>
      </el-icon>
    </div>
  </ele-dropdown>
  <!-- 修改密码弹窗 -->
  <password-modal v-model="passwordVisible"/>
  <!--切换角色弹框-->
  <roles-modal v-model="rolesVisible" :data="loginRoles"/>
</template>

<script setup>
import {computed, ref, unref,shallowRef} from 'vue';
import {useRouter} from 'vue-router';
import {useI18n} from 'vue-i18n';
import {ElMessageBox} from 'element-plus/es';
import {
  ArrowDown,
  UserOutlined,
  SwapOutlined,
  LockOutlined,
  LogoutOutlined
} from '@/components/icons';
import {useUserStore} from '@/store/modules/user';
import {logout} from '@/utils/common_bak2.js';
import {getCurrentRole} from '@/utils/current-role-util';
import PasswordModal from './password-modal.vue';
import RolesModal from './roles-modal.vue';
import {checkSwitchRole} from '@/api/layout';
import { ElMessage as EleMessage} from "element-plus";

const {t} = useI18n();
const {push, currentRoute} = useRouter();
const userStore = useUserStore();

const downItems = shallowRef(
  [{
    title: '选择角色',
    command: 'roles',
    icon: SwapOutlined
  }, {
    title: '个人信息',
    command: 'person',
    icon: UserOutlined
    // },  {
    //     title: t('layout.header.profile'),
    //     command: 'profile',
    //     icon: UserOutlined
  }, {
    title: t('layout.header.password'),
    command: 'password',
    icon: LockOutlined,
    iconStyle: {transform: 'translateY(-1px)'}
  }, {
    title: t('layout.header.logout'),
    command: 'logout',
    icon: LogoutOutlined,
    divided: true
  }]
)
/** 是否显示修改密码弹窗 */
const passwordVisible = ref(false);
/** 是否显示切换角色弹框*/
const rolesVisible = ref(false);

/** 当前用户信息 */
const loginUser = computed(() => userStore.info ?? {});
/** 当前用户角色*/
const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
/** 当前用户多角色*/
const loginRoles = ref([]);
// const loginRoles = computed(() => userStore.roles ?? {});

const checkSwitchRoleQuery = () => {
  /** 获取角色数据 */
  checkSwitchRole().then((res) => {
    userStore.setRoles(res)
    loginRoles.value = res
  }).catch((e) => {
    EleMessage.error(e.message);
  });
}

/** 用户信息下拉点击 */
const onUserDropClick = (command) => {
  if (command === 'password') {
    passwordVisible.value = true;
  } else if (command === 'roles') {
    checkSwitchRoleQuery();
    rolesVisible.value = true;
  } else if (command === 'person') {
    let routerType = currentRoles.roleName === '学生' ? 'student' : 'teacher';
    push('/personInfo/st/details/' + routerType + '/' + loginUser.value.xgh);
  } else if (command === 'profile') {
    push('/user/profile');
  } else if (command === 'logout') {
    // 退出登录
    ElMessageBox.confirm(
      t('layout.logout.message'),
      t('layout.logout.title'),
      {type: 'warning', draggable: true}
    ).then(() => {
      logout(false, unref(currentRoute).fullPath);
    }).catch(() => {
    });
  }
};
</script>

<style lang="scss" scoped>
.header-avatar {
  display: flex;
  align-items: center;
  position: relative;
  height: 100%;
  outline: none;
}
</style>
