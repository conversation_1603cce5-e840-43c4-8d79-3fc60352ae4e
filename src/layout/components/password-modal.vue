<template>
  <ele-modal
    :width="450"
    title="修改密码"
    :close-on-click-modal="false"
    :append-to-body="true"
    :model-value="modelValue"
    @update:model-value="updateModelValue"
    @closed="onCancel"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="small"
      label-width="110px"
      @submit.prevent=""
    >
      <el-form-item label="当前密码" prop="oldPassword">
        <el-input
          v-model="form.oldPassword"
          show-password
          clearable
          type="password"
          :maxlength="20"
          placeholder="请输入当前密码"
          @keyup.enter="onOk"
        />
      </el-form-item>

      <el-form-item label="新密码" prop="password">
        <el-input
          v-model="form.password"
          show-password
          clearable
          type="password"
          :maxlength="20"
          placeholder="请输入6-18位新密码"
          @keyup.enter="onOk"
        />
        <div class="password-tips">
          <span :class="{ active: passwordStrength.length }">长度6-18位</span>
          <span :class="{ active: passwordStrength.letter }">包含字母</span>
          <span :class="{ active: passwordStrength.number }">包含数字</span>
        </div>
      </el-form-item>

      <el-form-item label="确认新密码" prop="password2">
        <el-input
          v-model="form.password2"
          show-password
          clearable
          type="password"
          :maxlength="20"
          placeholder="请再次输入新密码"
          @keyup.enter="onOk"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button
        size="small" @click="updateModelValue(false)">取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="onOk"
        :disabled="!formValid"
      >
        确认修改
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
import {ref, computed, watch, nextTick} from 'vue';
import {ElMessage} from 'element-plus';
import {updatePassword} from '@/api/layout';
import {useFormData} from '@/utils/use-form-data.js';

const emit = defineEmits(['update:modelValue']);

const props = defineProps({
  modelValue: Boolean,
});

// 表单数据初始化
const form = ref({
  oldPassword: '',
  password: '',
  password2: '',
});

// 密码强度计算
const passwordStrength = computed(() => {
  const pwd = form.value.password || '';
  return {
    length: pwd.length >= 6 && pwd.length <= 18,
    complexity: /[a-zA-Z]/.test(pwd) && /[0-9]/.test(pwd),
  };
});

// 表单验证规则
const rules = {
  oldPassword: [
    {required: true, message: '请输入当前密码', trigger: 'blur'},
  ],
  password: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {
      min: 6,
      max: 18,
      message: '密码长度需在6-18位之间',
      trigger: 'blur',
    },
    {
      validator: (_, value, callback) => {
        if (!value || !/[a-zA-Z]/.test(value) || !/[0-9]/.test(value)) {
          callback(new Error('密码需包含字母和数字'));
        } else {
          callback();
        }
      },
      trigger: 'blur',
    },
  ],
  password2: [
    {required: true, message: '请确认新密码', trigger: 'blur'},
    {
      validator: (_, value, callback) => {
        if (value !== form.value.password) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: ['blur', 'change'],
    },
  ],
};

const formRef = ref(null);
const loading = ref(false);

// 表单有效性检查
const formValid = computed(() => {
  return (
    form.value.oldPassword &&
    form.value.password &&
    form.value.password2 &&
    form.value.password === form.value.password2 &&
    passwordStrength.value.length
  );
});

// 提交方法
const onOk = async () => {
  try {
    if (!formRef.value) return;

    // 验证表单
    await formRef.value.validate();

    loading.value = true;
    const {data: message} = await updatePassword({
      oldPassword: encrypt(form.value.oldPassword),
      newPassword: encrypt(form.value.password),
    });

    ElMessage.success(message || '密码修改成功');
    updateModelValue(false);
  } catch (error) {
    if (error?.errors) return; // 忽略表单验证错误
    ElMessage.error(error.message || '密码修改失败');
  } finally {
    loading.value = false;
  }
};

const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

// 重置表单
const onCancel = () => {
  form.value = {oldPassword: '', password: '', password2: ''};
  formRef.value?.resetFields?.();
};

// 加载加密脚本
const insertScript = () => {
  return new Promise((resolve, reject) => {
    if (window.encrypt) {
      return resolve();
    }
    const scriptElement = document.createElement('script');
    scriptElement.type = 'text/javascript';
    scriptElement.async = true;
    scriptElement.src = window.location.origin + '/api/sec_js';

    scriptElement.onload = () => {
      if (window.encrypt) {
        resolve();
      } else {
        reject(new Error('加密工具未正确加载'));
      }
    };

    scriptElement.onerror = () => {
      reject(new Error('加载加密脚本失败'));
    };

    document.head.appendChild(scriptElement);
  });
};

// 监听弹窗打开状态
watch(() => props.modelValue, (val) => {
  if (val) {
    insertScript();
    nextTick(() => {
      form.value = {oldPassword: '', password: '', password2: ''};
      formRef.value?.clearValidate?.();
    });
  }
});
insertScript();
</script>

<style scoped>
.password-tips {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: flex;
  gap: 8px;
}

.password-tips span {
  position: relative;
  padding-left: 16px;
}

.password-tips span::before {
  content: "";
  position: absolute;
  left: 4px;
  top: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ddd;
}

.password-tips span.active {
  color: #67c23a;
}

.password-tips span.active::before {
  background: #67c23a;
}

:deep(.el-form-item__label) {
  color: #666;
}
</style>
