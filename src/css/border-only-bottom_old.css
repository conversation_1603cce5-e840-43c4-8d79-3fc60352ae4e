.border-only-bottom {
  /* 通用输入框样式 */
  :deep(.el-input .el-input__wrapper),
  :deep(.el-textarea .el-textarea__inner),
  :deep(.el-input__inner),
  :deep(.el-select .el-select__wrapper),
  :deep(.el-date-editor.el-range-editor.el-input__wrapper) {
    box-shadow: none !important;
    border: none !important;
    border-bottom: 1px solid #dcdfe6 !important;
    background: transparent !important;
    border-radius: 0 !important;
    color: #222 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  /* 禁用状态下的选择框 */
  :deep(.el-select .el-select__wrapper.is-disabled) {
    background-color: transparent !important;
    .el-select__placeholder,
    .el-select__selected-item {
      color: #222 !important;
      -webkit-text-fill-color: #222 !important;
    }
  }

  /* 隐藏所有后缀图标 */
  :deep(.el-input__suffix),
  :deep(.el-select__suffix),
  :deep(.el-cascader__suffix) {
    display: none !important;
  }

  /* 表单标签样式 */
  :deep(.el-form-item__label) {
    pointer-events: none !important;
    color: #666 !important;
  }

  /* 特殊处理 radio button 组 */
  :deep(.el-radio-button__original-radio:disabled + .el-radio-button__inner) {
    color: #222 !important;
    border-color: transparent !important;
    background: transparent !important;
  }

  /* 文本域样式 */
  :deep(.el-textarea .el-textarea__inner) {
    resize: none !important;
  }

  /* 级联选择器样式 */
  :deep(.el-cascader .el-input__wrapper) {
    padding-left: 0 !important;
  }
}
