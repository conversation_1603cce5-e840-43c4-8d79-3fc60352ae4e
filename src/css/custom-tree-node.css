/* ===== 树组件容器优化 ===== */
.el-tree {
  padding: 0 3px !important;
  background-color: transparent;
}

/* ===== 节点行容器（原生 el-tree 样式修正） ===== */
.el-tree-node__content {
  font-size: 13px !important;
  height: auto !important;
  min-height: 32px;
  align-items: flex-start;
  padding: 2px 2px; /* 修正间距防止边缘阴影 */
  border-radius: 4px;
  transition: background-color 0.2s ease-in-out;
  box-shadow: none !important; /* 移除阴影 */
}

/* ===== label 默认布局修复 ===== */
.el-tree-node__label {
  display: flex !important;
  flex: 1 1 auto;
  align-items: stretch;
  overflow: hidden;
}

/* ===== 自定义节点整体结构 ===== */
.custom-tree-node {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: flex-start;
  overflow: hidden;
  padding-right: 0 !important;
  position: relative; /* 为了后续使用 ::before 做标记条 */
}

/* ===== 文字部分容器 ===== */
.text-container {
  margin: 8px 0;
  flex: 1;
  overflow: hidden;
}

/* ===== 文本内容：最多一行省略显示 ===== */
.span-wrap {
  white-space: normal;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.8;
}

/* ===== 图标容器 ===== */
.icon-container {
  display: flex;
  align-items: center;
  margin-left: 8px;
  flex-shrink: 0;
  padding-top: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* ===== 选中或悬停时显示图标 ===== */
.custom-tree-node:hover .icon-container,
.custom-tree-node.selected .icon-container {
  opacity: 1;
}

/* ===== 辅助标签容器（如 badge） ===== */
.completion-badge {
  color: var(--primary-color);
  font-size: 12px;
  border-radius: 10px;
  margin-right: 8px;
  font-weight: 500;
  margin-left: clamp(8px, 2vw, 12px);
  flex-shrink: 0;
  padding-top: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-tree-node:hover .completion-badge,
.custom-tree-node.selected .completion-badge {
  opacity: 1;
}

/* 防止阴影/闪动 */
.el-tree-node__content:hover,
.el-tree-node.is-current > .el-tree-node__content {
  box-shadow: none !important;
}

/* ===== 选中状态的背景和边框 ===== */
.custom-tree-node.selected {
  background-color: var(--el-color-primary-light-9, #f0faff);
}

/* ===== 选中状态文字加粗，改变颜色 ===== */
.custom-tree-node.selected .span-wrap {
  font-weight: 600;
  color: var(--el-color-primary, #409EFF);
}


/* ===== 可选：动画过渡统一处理 ===== */
.el-col {
  transition: all 0.3s ease-in-out;
}
