.border-only-bottom {
  :deep(.el-input .el-input__wrapper),
  :deep(.el-textarea .el-textarea__inner),
  :deep(.el-input__inner),
  :deep(.el-select .el-select__wrapper),
  :deep(.el-date-editor.el-range-editor.el-input__wrapper) {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    color: #222;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  :deep(.el-form-item__label) {
    pointer-events: none;
  }

  :deep(.el-select__wrapper.is-disabled .el-select__placeholder) {
    color: #222;
    -webkit-text-fill-color: unset;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  :deep(.el-select .el-select__suffix) {
    display: none;
  }

  :deep(.el-select .el-select__wrapper) {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  :deep(.el-input .el-input__wrapper .el-input__inner) {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    color: #222;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  :deep(.el-select .el-input__inner),
  :deep(.el-cascader .el-input__wrapper) {
    box-shadow: none;
    border: unset !important;
    border-radius: 0;
    color: blue;
    background: unset !important;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  :deep(.ele-fluid.el-date-editor.el-input__wrapper) {
    box-shadow: none;
    border: unset !important;
    border-radius: 0;
  }

  :deep(.el-cascader .el-input__wrapper .el-input__suffix) {
    display: none;
  }

  :deep(.el-input .el-input__wrapper .el-input__suffix) {
    display: none;
  }

  :deep(.el-input .el-input__suffix .el-input__clear) {
    display: none;
  }

  :deep(.el-select .el-select__wrapper.is-disabled) {
    background-color: #fff !important;
  }

  :deep(.el-tag--light.el-tag--info) {
    background-color: #fff !important;
  }
}
