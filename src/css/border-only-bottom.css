:deep(.border-only-bottom) {
  .el-input .el-input__wrapper,
  .el-textarea .el-textarea__inner,
  .el-input__inner,
  .el-select .el-select__wrapper,
  .el-date-editor.el-range-editor.el-input__wrapper {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    color: #222;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  .el-form-item__label {
    pointer-events: none;
  }

  .el-select__wrapper.is-disabled .el-select__placeholder {
    color: #222;
    -webkit-text-fill-color: unset;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  .el-select .el-select__suffix {
    display: none;
  }

  .el-select .el-select__wrapper {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  .el-input .el-input__wrapper .el-input__inner {
    box-shadow: none;
    border: unset !important;
    background: unset !important;
    border-radius: 0;
    color: #222;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  .el-input .el-input__suffix,
  .el-input .el-input__suffix .el-input__clear,
  .el-cascader .el-input__wrapper .el-input__suffix {
    box-shadow: none;
    border: unset !important;
    border-radius: 0;
    color: blue;
    background: unset !important;
    cursor: not-allowed;
    pointer-events: none;
    user-modify: read-only;
  }

  .ele-fluid.el-date-editor.el-input__wrapper {
    box-shadow: none;
    border: unset !important;
    border-radius: 0;
  }

  .el-cascader .el-input__wrapper .el-input__suffix {
    display: none;
  }

  .el-input .el-input__wrapper .el-input__suffix {
    display: none;
  }

  .el-input-number.is-controls-right .el-input-number__decrease {
    display: none;
  }

  .el-input-number.is-controls-right .el-input-number__increase {
    display: none;
  }

  .el-input .el-input__suffix .el-input__clear {
    display: none;
  }

  .el-select .el-select__wrapper.is-disabled {
    background-color: #fff !important;
  }

  .el-tag--light.el-tag--info {
    background-color: #fff !important;
  }
}
