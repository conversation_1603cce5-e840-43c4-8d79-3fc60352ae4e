{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?0f54", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?22b5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?9cdc", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?d2f0", "uni-app:///components/mp-html/audio/audio.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?d801", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/components/mp-html/audio/audio.vue?ddd5"], "names": ["data", "error", "playing", "time", "value", "props", "aid", "name", "author", "poster", "autoplay", "controls", "loop", "src", "watch", "mounted", "context", "<PERSON><PERSON><PERSON><PERSON>", "onPageShow", "methods", "setSrc", "play", "target", "id", "pause", "seek", "_buttonTap", "_seeking", "_seeked"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBp4B;AACA;AACA;AACA;AAAA,eACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;EACA;;EACAC;IACAD;MACA;IACA;EACA;EACAE;IAAA;IACA;IACA;MACA;MACA;IACA;IACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;;IACA;MACA;QACA;MACA;IACA;IACAC;IACA;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA,oCACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClIA;AAAA;AAAA;AAAA;AAA+uC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/mp-html/audio/audio.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./audio.vue?vue&type=template&id=7c140678&\"\nvar renderjs\nimport script from \"./audio.vue?vue&type=script&lang=js&\"\nexport * from \"./audio.vue?vue&type=script&lang=js&\"\nimport style0 from \"./audio.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/mp-html/audio/audio.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio.vue?vue&type=template&id=7c140678&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"controls\" class=\"_contain\">\r\n    <!-- 海报和按钮 -->\r\n\t\t<view class=\"_poster\" :style=\"'background-image:url('+poster+')'\">\r\n\t\t\t<view class=\"_button\" @tap=\"_buttonTap\">\r\n\t\t\t\t<view :class=\"playing?'_pause':'_play'\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n    <!-- 曲名和作者 -->\r\n\t\t<view class=\"_title\">\r\n\t\t\t<view class=\"_name\">{{name||'未知音频'}}</view>\r\n\t\t\t<view class=\"_author\">{{author||'未知作者'}}</view>\r\n\t\t</view>\r\n    <!-- 进度条 -->\r\n    <slider class=\"_slider\" activeColor=\"#585959\" block-size=\"12\" handle-size=\"12\" :disabled=\"error\" :value=\"value\" @changing=\"_seeking\" @change=\"_seeked\" />\r\n    <!--播放时间-->\r\n\t\t<view class=\"_time\">{{time||'00:00'}}</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * @fileoverview audio 组件\r\n */\r\nconst context = require('./context')\r\nexport default {\r\n  data () {\r\n    return {\r\n      error: false,\r\n      playing: false,\r\n      time: '00:00',\r\n      value: 0\r\n    }\r\n  },\r\n  props: {\r\n    aid: String,\r\n    name: String, // 音乐名\r\n    author: String, // 作者\r\n    poster: String, // 海报图片地址\r\n    autoplay: [Boolean, String], // 是否自动播放\r\n    controls: [Boolean, String], // 是否显示控件\r\n    loop: [Boolean, String], // 是否循环播放\r\n    src: String // 源地址\r\n  },\r\n  watch: {\r\n    src (src) {\r\n      this.setSrc(src)\r\n    }\r\n  },\r\n  mounted () {\r\n    this._ctx = uni.createInnerAudioContext()\r\n    this._ctx.onError((err) => {\r\n      this.error = true\r\n      this.$emit('error', err)\r\n    })\r\n    this._ctx.onTimeUpdate(() => {\r\n      const time = this._ctx.currentTime\r\n      const min = parseInt(time / 60)\r\n      const sec = Math.ceil(time % 60)\r\n      this.time = (min > 9 ? min : '0' + min) + ':' + (sec > 9 ? sec : '0' + sec)\r\n      if (!this.lastTime) {\r\n        this.value = time / this._ctx.duration * 100 // 不在拖动状态下\r\n      }\r\n    })\r\n    this._ctx.onEnded(() => {\r\n      if (!this.loop) {\r\n        this.playing = false\r\n      }\r\n    })\r\n    context.set(this.aid, this)\r\n    this.setSrc(this.src)\r\n  },\r\n  beforeDestroy () {\r\n    this._ctx.destroy()\r\n    context.remove(this.properties.audioId)\r\n  },\r\n  onPageShow () {\r\n    if (this.playing && this._ctx.paused) {\r\n      this._ctx.play()\r\n    }\r\n  },\r\n  methods: {\r\n    // 设置源\r\n    setSrc (src) {\r\n      this._ctx.autoplay = this.autoplay\r\n      this._ctx.loop = this.loop\r\n      this._ctx.src = src\r\n      if (this.autoplay && !this.playing) {\r\n        this.playing = true\r\n      }\r\n    },\r\n    // 播放\r\n    play () {\r\n      this._ctx.play()\r\n      this.playing = true\r\n      this.$emit('play', {\r\n        target: {\r\n          id: this.aid\r\n        }\r\n      })\r\n    },\r\n    // 暂停\r\n    pause () {\r\n      this._ctx.pause()\r\n      this.playing = false\r\n      this.$emit('pause')\r\n    },\r\n    // 移动进度条\r\n    seek (sec) {\r\n      this._ctx.seek(sec)\r\n    },\r\n    // 内部方法\r\n    _buttonTap () {\r\n      if (this.playing) this.pause()\r\n      else this.play()\r\n    },\r\n    _seeking (e) {\r\n      // 避免过于频繁 setData\r\n      if (e.timeStamp - this.lastTime < 200) return\r\n      const time = Math.round(e.detail.value / 100 * this._ctx.duration)\r\n      const min = parseInt(time / 60)\r\n      const sec = time % 60\r\n      this.time = (min > 9 ? min : '0' + min) + ':' + (sec > 9 ? sec : '0' + sec)\r\n      this.lastTime = e.timeStamp\r\n    },\r\n    _seeked (e) {\r\n      this.seek(e.detail.value / 100 * this._ctx.duration)\r\n      this.lastTime = undefined\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 顶层容器 */\r\n._contain {\r\n  position: relative;\r\n  display: inline-flex;\r\n  width: 290px;\r\n  background-color: #fcfcfc;\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 2px;\r\n}\r\n\r\n/* 播放、暂停按钮 */\r\n._button {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 20px;\r\n  height: 20px;\r\n  overflow: hidden;\r\n  background-color: rgb(0, 0, 0, 0.2);\r\n  border: 1px solid white;\r\n  border-radius: 50%;\r\n  opacity: 0.9;\r\n}\r\n\r\n._play {\r\n  margin-left: 2px;\r\n  border-top: 4px solid transparent;\r\n  border-bottom: 4px solid transparent;\r\n  border-left: 8px solid white;\r\n}\r\n\r\n._pause {\r\n  width: 8px;\r\n  height: 8px;\r\n  background-color: white;\r\n}\r\n\r\n/* 海报 */\r\n._poster {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 70px;\r\n  height: 70px;\r\n  background-color: #e6e6e6;\r\n  background-size: contain;\r\n}\r\n\r\n/* 标题栏 */\r\n._title {\r\n  flex: 1;\r\n  margin: 4px 0 0 14px;\r\n  text-align: left;\r\n}\r\n\r\n._author {\r\n  width: 45px;\r\n  font-size: 12px;\r\n  color: #888;\r\n}\r\n\r\n._name {\r\n  width: 140px;\r\n  font-size: 15px;\r\n  line-height: 39px;\r\n}\r\n\r\n._author,\r\n._name {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 进度条 */\r\n._slider {\r\n  position: absolute;\r\n  right: 16px;\r\n  bottom: 8px;\r\n  width: 140px;\r\n  margin: 0;\r\n}\r\n\r\n/* 播放时间 */\r\n._time {\r\n  margin: 7px 14px 0 0;\r\n  font-size: 12px;\r\n  color: #888;\r\n}\r\n\r\n/* 响应式布局，大屏幕用更大的尺寸 */\r\n@media (min-width: 400px) {\r\n  ._contain {\r\n    width: 380px;\r\n  }\r\n\r\n  ._button {\r\n    width: 26px;\r\n    height: 26px;\r\n  }\r\n\r\n  ._poster {\r\n    width: 90px;\r\n    height: 90px;\r\n  }\r\n\r\n  ._author {\r\n    width: 60px;\r\n    font-size: 15px;\r\n  }\r\n\r\n  ._name {\r\n    width: 180px;\r\n    font-size: 19px;\r\n    line-height: 55px;\r\n  }\r\n\r\n  ._slider {\r\n    right: 20px;\r\n    bottom: 10px;\r\n    width: 180px;\r\n  }\r\n\r\n  ._time {\r\n    font-size: 15px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./audio.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191710\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}