{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?c68a", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?3b4b", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?09ad", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?aee0", "uni-app:///ui/components/ui-fixed/ui-fixed.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?07dc", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-fixed/ui-fixed.vue?1617"], "names": ["name", "data", "content", "fixed", "props", "noNav", "type", "default", "noSafe", "bottom", "bg", "val", "width", "opacity", "opacityVal", "index", "placeholder", "sticky", "noFixed", "ui", "clickTo", "created", "uni", "watch", "sys_scrollTop", "handler", "immediate", "mounted", "destroyed", "methods", "_computedQuery", "in", "select", "boundingClientRect", "console", "exec", "_setFixed", "_toTop", "scrollTop", "duration"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,o0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiCv4B;EACAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;EACA;EACAc;IAAA;IACA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;IACA;IACAN;MACAO;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACAN;EACA;EACAO;IACAC;MAAA;MACAR,0BACAS,SACAC,8BACAC;QACA;UACA;UACA;UACA;UACA;YACA;UACA;QACA;UACAC;QACA;MACA,GACAC;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAf;UACAgB;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrKA;AAAA;AAAA;AAAA;AAAsnD,CAAgB,29CAAG,EAAC,C;;;;;;;;;;;ACA1oD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-fixed/ui-fixed.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-fixed.vue?vue&type=template&id=4546e8d3&\"\nvar renderjs\nimport script from \"./ui-fixed.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-fixed.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-fixed.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-fixed/ui-fixed.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-fixed.vue?vue&type=template&id=4546e8d3&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    {\n      zIndex: _vm.index + _vm.sys_layer - 1,\n    },\n    _vm.bg == \"bg-img\"\n      ? {\n          backgroundImage: \"url(\" + _vm.img + \")\",\n        }\n      : {},\n    _vm.opacity\n      ? {\n          opacity: _vm.opacityVal,\n        }\n      : \"\",\n  ])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-fixed.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-fixed.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ui-fixed\">\r\n\t\t<view\r\n\t\t\tclass=\"ui-fixed-box\"\r\n\t\t\t:id=\"'fixed-' + _uid\"\r\n\t\t\t:class=\"[{ fixed: fixed }]\"\r\n\t\t\t:style=\"[\r\n\t\t\t\t{\r\n\t\t\t\t\tleft: sticky ? 'auto' : '0px',\r\n\t\t\t\t\ttop: fixed && !bottom ? (noNav ? val : val + sys_navBar) + 'px' : 'auto',\r\n\t\t\t\t\tbottom: fixed && bottom ? val + 'px' : 'auto',\r\n\t\t\t\t\tzIndex: index + sys_layer\r\n\t\t\t\t}\r\n\t\t\t]\"\r\n\t\t>\r\n\t\t\t<view class=\"ui-fixed-content\" @tap=\"_toTop\" :style=\"[{ zIndex: index + sys_layer }]\"><slot /></view>\r\n\t\t\t<view class=\"ui-fixed-bottom\" :class=\"[bg]\" v-if=\"bottom && !noSafe\"></view>\r\n\t\t\t<view\r\n\t\t\t\tclass=\"ui-fixed-bg\"\r\n\t\t\t\t:class=\"[ui,bg]\"\r\n\t\t\t\t:style=\"[{ zIndex: index + sys_layer - 1 }, bg == 'bg-img' ? { backgroundImage: 'url(' + img + ')' } : {}, opacity ? { opacity: opacityVal } : '']\"\r\n\t\t\t></view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"skeleton\"\r\n\t\t\t:id=\"'skeleton-' + _uid\"\r\n\t\t\t:style=\"[{ height: content.height + 'px', width: width + 'px' }]\"\r\n\t\t\tv-if=\"sticky ? fixed : placeholder && fixed\"\r\n\t\t></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: 'UiFixed',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcontent: {},\r\n\t\t\tfixed: true\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tnoNav: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tnoSafe: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tbottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tbg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'bg-none'\r\n\t\t},\r\n\t\tval: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\twidth: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\topacity: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\topacityVal: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tindex: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tplaceholder: {\r\n\t\t\ttype: [Boolean],\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tsticky: {\r\n\t\t\ttype: [Boolean],\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tnoFixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tui: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tclickTo: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tif (this.sticky) {\r\n\t\t\tthis.fixed = false;\r\n\t\t}\r\n\t\tuni.$on('_scrollTop_' + this.$root._uid, e => {\r\n\t\t\tthis.sys_scrollTop = e;\r\n\t\t});\r\n\t},\r\n\twatch: {\r\n\t\tsys_scrollTop(newValue, oldValue) {\r\n\t\t\tif (this.sticky) {\r\n\t\t\t\tthis._setFixed(newValue);\r\n\t\t\t}\r\n\t\t},\r\n\t\tnoFixed: {\r\n\t\t\thandler(val) {\r\n\t\t\t\tif (val) {\r\n\t\t\t\t\tthis.fixed = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$nextTick(function() {\r\n\t\t\tthis._computedQuery();\t\t\t\r\n\t\t});\r\n\t},\r\n\tdestroyed() {\r\n\t\tuni.$off('_scrollTop_' + this.$root._uid);\r\n\t},\r\n\tmethods: {\r\n\t\t_computedQuery() {\r\n\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t.in(this)\r\n\t\t\t\t.select('#fixed-' + this._uid)\r\n\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\tif (data != null) {\t\t\t\n\t\t\t\t\t\tthis.content = data;\n\t\t\t\t\t\tthis.$emit('getHeight', data.height);\n\t\t\t\t\t\tthis.$emit('update:height', data.height);\r\n\t\t\t\t\t\tif (this.sticky) {\r\n\t\t\t\t\t\t\tthis._setFixed(this.sys_scrollTop);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('fixed-' + this._uid + ' data error');\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.exec();\r\n\t\t},\r\n\t\t_setFixed(value) {\r\n\t\t\tif (this.bottom) {\r\n\t\t\t\tthis.fixed = value >= this.content.bottom - uni.getSystemInfoSync().windowHeight + this.content.height + this.val;\r\n\t\t\t} else {\r\n\t\t\t\tthis.fixed = value >= this.content.top - (this.noNav ? this.val : this.val + this.sys_navBar);\r\n\t\t\t}\r\n\t\t},\r\n\t\t_toTop() {\r\n\t\t\tif (this.clickTo) {\r\n\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\tscrollTop: this.content.top,\r\n\t\t\t\t\tduration: 100\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ui-fixed {\r\n\t.ui-fixed-box {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\t&.fixed {\r\n\t\t\tposition: fixed;\r\n\t\t}\r\n\t\t.ui-fixed-content {\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\t\t.ui-fixed-bg {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\ttop: 0;\r\n\t\t\tz-index: 1;\r\n\t\t\tpointer-events: none;\r\n\t\t}\n\t\t.ui-fixed-bottom{\n\t\t\t height: calc(constant(safe-area-inset-bottom) / 5 * 3);\n\t\t\t height: calc(env(safe-area-inset-bottom) / 5 * 3);\n\t\t}\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-fixed.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-fixed.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191874\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}