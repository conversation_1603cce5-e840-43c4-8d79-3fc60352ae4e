{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?8130", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?81d3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?ccb1", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?4f05", "uni-app:///ui/components/ui-swiper/ui-swiper.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?efb7", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-swiper/ui-swiper.vue?9b93"], "names": ["name", "data", "cur", "props", "circular", "type", "default", "autoplay", "interval", "duration", "info", "ui", "mode", "dotStyle", "dotCur", "bg", "height", "imgHeight", "methods", "redictAppDetail", "e", "appid", "url", "path", "wx", "appId", "swiper<PERSON><PERSON>e"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAo3B,CAAgB,q0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCwBx4B;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;QACA;MACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACAC;MACA,4BAKAC;QAJAf;QACAgB;QACAC;QACAC;MAGA;QAAA;QACAC;UACAF;QACA;MACA;MACA;QAAA;QACAA;QACAE;UACAF;QACA;MACA;MACA;QAAA;QACAE;UACAC;UACAF;QACA;MACA;IACA;IACAG;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAAunD,CAAgB,49CAAG,EAAC,C;;;;;;;;;;;ACA3oD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-swiper/ui-swiper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-swiper.vue?vue&type=template&id=29cc0152&\"\nvar renderjs\nimport script from \"./ui-swiper.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-swiper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-swiper.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-swiper/ui-swiper.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-swiper.vue?vue&type=template&id=29cc0152&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dotStyle == \"tag\" ? _vm.info.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-swiper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-swiper.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"ui-swiper\" :class=\"[mode,bg,ui]\">\r\n\t\t\t<swiper :circular=\"circular\" :autoplay=\"true\" :interval=\"interval\" duration=\"500\" :style=\"{height: height}\"\r\n\t\t\t\t@change=\"swiperChange\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in info\" :key=\"index\" :class=\"{ cur: cur == index }\" :data-item=\"item\" v-if=\"item.enable=='yes'\" :id=\"item.id\" :index=\"index\" :data-type=\"item.type\" :data-appid=\"item.appid\" :data-path=\"item.path\" :data-url=\"item.url\" @tap=\"redictAppDetail\">\r\n\t\t\t\t\t<view class=\"ui-swiper-main radius\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" :style=\"{height: imgHeight}\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<view class=\"ui-swiper-dot\" :class=\"dotStyle\" v-if=\"dotStyle != 'tag'\">\r\n\t\t\t\t<view v-for=\"(item, index) in info\" :key=\"index\" :class=\"[cur == index ? 'cur' : '', dotCur]\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ui-swiper-dot\" :class=\"dotStyle\" v-if=\"dotStyle == 'tag'\">\r\n\t\t\t\t<view class=\"ui-tag radius\" :class=\"[dotCur]\">\r\n\t\t\t\t\t<view>{{ cur + 1 }} / {{ info.length }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'UiSwiper',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcur: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tcircular: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tautoplay: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tinterval: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 5000\r\n\t\t\t},\r\n\t\t\tduration: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 500\r\n\t\t\t},\r\n\t\t\tinfo: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tui: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmode: {\r\n\t\t\t\t//default\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\tdotStyle: {\r\n\t\t\t\t//default long tag\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\tdotCur: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'bg-black'\r\n\t\t\t},\r\n\t\t\tbg: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'bg-none'\r\n\t\t\t},\n\t\t\theight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '40vw'\r\n\t\t\t},\n\t\t\timgHeight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '40vw'\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\n\t\t\t  redictAppDetail: function (e) {\n\t\t\t            let {\n\t\t\t              type,\n\t\t\t              appid,\n\t\t\t              url,\n\t\t\t              path\n\t\t\t            } = e.currentTarget.dataset\n\t\t\t        \n\t\t\t            if (type === 'apppage') { // 小程序页面         \n\t\t\t              wx.navigateTo({\n\t\t\t                url: path\n\t\t\t              })\n\t\t\t            }\n\t\t\t            if (type === 'webpage') { // web-view页面\n\t\t\t              url = '../webpage/webpage?url=' + url\n\t\t\t              wx.navigateTo({\n\t\t\t                url: url\n\t\t\t              })\n\t\t\t            }\n\t\t\t            if (type === 'miniapp') { // 其他小程序\n\t\t\t              wx.navigateToMiniProgram({\n\t\t\t                appId: appid,\n\t\t\t                path: path\n\t\t\t              })\n\t\t\t            }\n\t\t\t          },\r\n\t\t\tswiperChange(e) {\r\n\t\t\t\tthis.cur = e.detail.current;\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/deep/ .ui-swiper {\r\n\t\tposition: relative;\r\n\r\n\t\t.ui-swiper-main {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t.ui-swiper-main image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t.ui-swiper-dot {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\tbottom: 20rpx;\r\n\t\t\theight: 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&.default view {\r\n\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\twidth: 6px;\r\n\t\t\t\theight: 6px;\r\n\t\t\t\tborder: 2px solid transparent;\r\n\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t&.cur {\r\n\t\t\t\t\twidth: 8px;\r\n\t\t\t\t\theight: 8px;\r\n\t\t\t\t\topacity: 1;\r\n\t\t\t\t\tborder: 0px solid transparent;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.cur::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\t\twidth: 4px;\r\n\t\t\t\t\theight: 4px;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.long view {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tborder-radius: 100rpx;\r\n\t\t\t\twidth: 6px;\r\n\t\t\t\theight: 6px;\r\n\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t&.cur {\r\n\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\topacity: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.cur::after {}\r\n\t\t\t}\r\n\r\n\t\t\t&.line {\r\n\t\t\t\tbottom: 20rpx;\r\n\r\n\t\t\t\tview {\r\n\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\twidth: 30px;\r\n\t\t\t\t\theight: 3px;\r\n\t\t\t\t\topacity: 0.3;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t&.cur {\r\n\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.tag {\r\n\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 20rpx;\r\n\t\t\t\tright: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.card {\r\n\t\t\tswiper-item {\r\n\t\t\t\twidth: 610rpx !important;\r\n\t\t\t\tleft: 70rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tpadding: 20rpx 0rpx 60rpx;\r\n\t\t\t\toverflow: initial;\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item .ui-swiper-main {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbackground-size: cover;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tborder-radius: inherit;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item .ui-swiper-main::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tbackground: inherit;\r\n\t\t\t\tfilter: blur(5px);\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t\tleft: 10rpx;\r\n\t\t\t\tz-index: -1;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\ttransform-origin: 0 0;\r\n\t\t\t\tborder-radius: inherit;\r\n\t\t\t\ttransform: scale(1, 1);\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item.cur .ui-swiper-main {\r\n\t\t\t\ttransform: scale(1);\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t}\r\n\r\n\t\t\t.ui-swiper-dot.tag {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 85rpx;\r\n\t\t\t\tright: 75rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.hotelCard {\r\n\t\t\tswiper-item {\r\n\t\t\t\twidth: 650rpx !important;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tpadding: 0rpx 0rpx 50rpx;\r\n\t\t\t\toverflow: initial;\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item .ui-swiper-main {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbackground-size: cover;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 400rpx;\r\n\t\t\t\t\tborder-radius: 10rpx 10rpx 0 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item .ui-swiper-main::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tbackground: inherit;\r\n\t\t\t\tfilter: blur(5px);\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t\tleft: 10rpx;\r\n\t\t\t\tz-index: -1;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\ttransform-origin: 0 0;\r\n\t\t\t\tborder-radius: inherit;\r\n\t\t\t\ttransform: scale(1, 1);\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item.cur .ui-swiper-main {\r\n\t\t\t\ttransform: scale(1);\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t\topacity: 1;\r\n\t\t\t}\r\n\r\n\t\t\t.ui-swiper-dot {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.hotelDetail {\r\n\t\t\tswiper-item {\r\n\t\t\t\twidth: 690rpx !important;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tpadding: 20rpx 0rpx;\r\n\t\t\t\toverflow: initial;\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item .ui-swiper-main {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\ttransform: scale(0.96);\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbackground-size: cover;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tswiper-item.cur .ui-swiper-main {\r\n\t\t\t\ttransform: scale(0.96);\r\n\t\t\t\ttransition: all 0.2s ease-in 0s;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-swiper.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-swiper.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191785\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}