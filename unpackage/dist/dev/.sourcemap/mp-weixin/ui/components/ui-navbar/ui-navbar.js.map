{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?14a8", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?ad48", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?895a", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?8f02", "uni-app:///ui/components/ui-navbar/ui-navbar.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?cf9f", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-navbar/ui-navbar.vue?2ac8"], "names": ["name", "data", "statusCur", "capsuleStyle", "capsuleBack", "opacityVal", "props", "back", "type", "default", "backtext", "bg", "status", "img", "opacity", "opacityChange", "opacityBg", "noFixed", "ui", "capsule", "stopBack", "placeholder", "created", "uni", "width", "height", "margin", "mounted", "destroyed", "watch", "handler", "immediate", "methods", "_onShow", "_navback", "changeStatus", "opacityChangeStatus"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAAo3B,CAAgB,q0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+Bx4B;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IAAA;IACA;IAIAC;MACA;MACA;IACA;IAEA;MACAC;MACAC;MACAC;IACA;IACA;MACA;QACAF;QACAC;QACAC;MACA;IACA;MACA;IACA;EAGA;EACAC;IACA;IACA;EACA;EACAC;IACAL;EACA;EACAM;IACAjB;MACAkB;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;UAEA;QAEA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxLA;AAAA;AAAA;AAAA;AAAunD,CAAgB,49CAAG,EAAC,C;;;;;;;;;;;ACA3oD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-navbar/ui-navbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-navbar.vue?vue&type=template&id=c8519ef2&\"\nvar renderjs\nimport script from \"./ui-navbar.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-navbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-navbar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-navbar/ui-navbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-navbar.vue?vue&type=template&id=c8519ef2&\"", "var components\ntry {\n  components = {\n    uiFixed: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-fixed/ui-fixed\" */ \"@/ui/components/ui-fixed/ui-fixed.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 =\n    !_vm.slots.default && _vm.capsule\n      ? _vm.__get_style([_vm.capsuleBack])\n      : null\n  var s1 =\n    !_vm.slots.default && _vm.slots.right\n      ? _vm.__get_style([_vm.capsuleStyle])\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-navbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-navbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ui-fixed :noFixed=\"noFixed\" :val=\"0\" :index=\"1000\" noNav :bg=\"bg\" :ui=\"ui\" :opacity=\"opacity\" :opacityVal=\"opacityVal\" :placeholder=\"placeholder\">\r\n\t\t<view class=\"ui-navbar-box\" :class=\"[{'border-bottom':!opacity&&bg!='bg-none'}]\" :style=\"{ paddingTop: sys_statusBar + 'px', height: sys_navBar + 'px' }\">\r\n\t\t\t<view class=\"ui-bar\" :class=\"status == '' ? `text-a` : status == 'light' ? 'text-white' : 'text-black'\" :style=\"[{ height: sys_navBar - sys_statusBar + 'px' }]\">\r\n\t\t\t\t<block v-if=\"slots.default\"><slot></slot></block>\r\n\t\t\t\t<block v-else>\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<view class=\"back\" @tap=\"_navback\" v-if=\"back && !capsule\">\r\n\t\t\t\t\t\t\t<view class=\"back-icon icon-fw text-xl\" :class=\"[sys_isFirstPage ? '_icon-home-o' : '_icon-back', statusCur, { opacityIcon: opacityBg }]\"></view>\r\n\t\t\t\t\t\t\t<view>{{ backtext }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"capsule\" :style=\"[capsuleBack]\" v-if=\"capsule\" :class=\"[{ isFristPage: sys_isFirstPage }, statusCur]\">\r\n\t\t\t\t\t\t\t<view class=\"capsule-back\" @tap=\"_navback\"><text class=\"icon-fw text-xl _icon-back\"></text></view>\r\n\t\t\t\t\t\t\t<view class=\"capsule-home\" @tap=\"_toHome\"><text class=\"icon-fw text-xl\" :class=\"sys_isFirstPage ? '_icon-home' : '_icon-home-o'\"></text></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<slot name=\"left\"></slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"center\" v-if=\"slots.center\"><slot name=\"center\"></slot></view>\r\n\t\t\t\t\t<view class=\"right\" v-if=\"slots.center && !slots.right\"></view>\r\n\t\t\t\t\t<view class=\"right\" v-if=\"slots.right\">\r\n\t\t\t\t\t\t<view class=\"right-content\"><slot name=\"right\"></slot></view>\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<view :style=\"[capsuleStyle]\"></view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</ui-fixed>\r\n</template>\r\n<script>\r\nexport default {\r\n\tname: 'UiNavbar',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusCur: '',\r\n\t\t\tcapsuleStyle: {},\r\n\t\t\tcapsuleBack: {}, \r\n\t\t\topacityVal: 0\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tback: {\r\n\t\t\t//是否返回上一页\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\tbacktext: {\r\n\t\t\t//返回文本\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tbg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'bg-blur'\r\n\t\t},\r\n\t\tstatus: {\r\n\t\t\t//状态栏颜色 可以选择light dark/其他字符串视为黑色\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\timg: {\r\n\t\t\t//如果bg == bg-img 则加载对应的图片\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\topacity: {\r\n\t\t\t//是否开启滑动渐变\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\topacityChange: {\r\n\t\t\t//开启滑动渐变后 文本样式是否翻转\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\topacityBg: {\r\n\t\t\t//开启滑动渐变后 返回按钮是否添加背景\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tnoFixed: {\r\n\t\t\t//是否浮动\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tui: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tcapsule: {\r\n\t\t\t//是否开启胶囊返回\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tstopBack: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tplaceholder: {\r\n\t\t\ttype: [Boolean],\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.sys_layer = 1000;\r\n\t\t// #ifdef MP-ALIPAY\r\n\t\tmy.hideAllFavoriteMenu();\r\n\t\t// #endif\r\n\t\tuni.$on('_scrollTop_' + this.$root._uid, e => {\r\n\t\t\tthis.sys_scrollTop = e;\r\n\t\t\tthis.opacityChangeStatus();\r\n\t\t});\r\n\t\t// #ifdef MP\r\n\t\tthis.capsuleStyle = {\r\n\t\t\twidth: this.sys_capsule.width + 'px',\r\n\t\t\theight: this.sys_capsule.height + 'px',\r\n\t\t\tmargin: '0 ' + (this.sys_info.windowWidth - this.sys_capsule.right) + 'px'\r\n\t\t};\r\n\t\tif (this.sys_isFirstPage) {\r\n\t\t\tthis.capsuleBack = {\r\n\t\t\t\twidth: this.sys_capsule.height + 'px',\r\n\t\t\t\theight: this.sys_capsule.height + 'px',\r\n\t\t\t\tmargin: '0 ' + (this.sys_info.windowWidth - this.sys_capsule.right) + 'px'\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\tthis.capsuleBack = this.capsuleStyle;\r\n\t\t}\r\n\t\t// #endif\r\n\r\n\t},\r\n\tmounted() { \r\n\t\t// //设置 statusBarStyle App端必须在渲染后\r\n\t\tthis.changeStatus();\r\n\t},\r\n\tdestroyed() {\r\n\t\tuni.$off('_scrollTop_' + this.$root._uid);\r\n\t},\r\n\twatch: {\r\n\t\tstatus: {\r\n\t\t\thandler(val) {\r\n\t\t\t\tif (val!='') {\r\n\t\t\t\t\tthis.$store.commit('setStatusStyle',val);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t_onShow(){\r\n\t\t\tthis.changeStatus();\t\r\n\t\t},\r\n\t\t_navback() {\r\n\t\t\tif (this.stopBack) {\r\n\t\t\t\tthis.$emit('navback');\r\n\t\t\t} else {\r\n\t\t\t\tthis._backPage();\r\n\t\t\t}\r\n\t\t},\r\n\t\tchangeStatus() {\r\n\t\t\tif (this.status == '') {\r\n\t\t\t\tif(this.sys_theme == 'auto'){\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tthis.$store.commit('setStatusStyle',uni.getSystemInfoSync().theme == 'light' ? 'dark' : 'light')\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$store.commit('setStatusStyle',this.sys_theme == 'light' ? 'dark' : 'light');\r\n\t\t\t\t}\t\t\t\t\r\n\t\t\t} else {\r\n\t\t\t\tthis.$store.commit('setStatusStyle',this.status);\r\n\t\t\t}\r\n\t\t},\r\n\t\topacityChangeStatus() {\r\n\t\t\tlet e = this.sys_scrollTop;\r\n\t\t\tthis.opacityVal = e > this.sys_navBar ? 1 : e * 0.01;\r\n\t\t\t// if (e > this.sys_navBar / 2) {\r\n\t\t\t// \tif ((this.bg == 'ui-BG' || this.bg == 'bg-blur') && this.status == '') {\r\n\t\t\t// \t\tthis.$store.commit('setStatusStyle',this.sys_theme == 'light' ? 'dark' : 'light');\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tthis.$store.commit('setStatusStyle',this.status == 'light' ? 'dark' : 'light');\r\n\t\t\t// \t}\r\n\t\t\t// } \r\n\t\t},\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ui-navbar-box {\r\n\tbackground-color: transparent;\r\n\twidth: 100%;\r\n\t.ui-bar {\r\n\t\tposition: relative;\r\n\t\tz-index: 2;\r\n\t\twhite-space: nowrap;\r\n\t\tdisplay: flex;\r\n\t\tposition: relative;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\t.left {\r\n\t\t\t@include flex-bar;\r\n\t\t\tflex: 1;\r\n\t\t\t.back {\r\n\t\t\t\t@include flex-bar;\r\n\t\t\t\t.back-icon {\r\n\t\t\t\t\t@include flex-center;\r\n\t\t\t\t\twidth: 56rpx;\r\n\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\t\t&.opacityIcon {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tbackground-color: rgba(127, 127, 127, 0.5);\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\theight: 200%;\r\n\t\t\t\t\t\t\twidth: 200%;\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\tborder-radius: inherit;\r\n\t\t\t\t\t\t\ttransform: scale(0.5);\r\n\t\t\t\t\t\t\ttransform-origin: 0 0;\r\n\t\t\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\t\t\tborder: 1px solid currentColor;\r\n\t\t\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::before {\r\n\t\t\t\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t/* #ifdef  MP-ALIPAY */\r\n\t\t\t\t._icon-back {\r\n\t\t\t\t\topacity: 0;\r\n\t\t\t\t}\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t\t.capsule {\r\n\t\t\t\t@include flex-bar;\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t&.dark {\r\n\t\t\t\t\tbackground-color: rgba(255, 255, 255, 0.5);\r\n\t\t\t\t}\r\n\t\t\t\t&.light {\r\n\t\t\t\t\tbackground-color: rgba(0, 0, 0, 0.15);\r\n\t\t\t\t}\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\theight: 60%;\r\n\t\t\t\t\twidth: 1px;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttop: 20%;\r\n\t\t\t\t\tbackground-color: currentColor;\r\n\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t}\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\theight: 200%;\r\n\t\t\t\t\twidth: 200%;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tborder-radius: inherit;\r\n\t\t\t\t\ttransform: scale(0.5);\r\n\t\t\t\t\ttransform-origin: 0 0;\r\n\t\t\t\t\topacity: 0.1;\r\n\t\t\t\t\tborder: 1px solid currentColor;\r\n\t\t\t\t\tpointer-events: none;\r\n\t\t\t\t}\r\n\t\t\t\t.capsule-back,\r\n\t\t\t\t.capsule-home {\r\n\t\t\t\t\t@include flex-center;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t}\r\n\t\t\t\t&.isFristPage {\r\n\t\t\t\t\t.capsule-back,\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.right {\r\n\t\t\t@include flex-bar;\r\n\t\t\tflex: 1;\r\n\t\t\t.right-content {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex-direction: row-reverse;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.center {\r\n\t\t\t@include flex-center;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\ttext-align: center;\r\n\t\t\tflex: 2;\r\n\t\t\t.image {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\theight: 36px;\r\n\t\t\t\tmax-width: calc(100vw - 200px);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.ui-bar-bg {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\ttop: 0;\r\n\t\tz-index: 1;\r\n\t\tpointer-events: none;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-navbar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-navbar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191770\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}