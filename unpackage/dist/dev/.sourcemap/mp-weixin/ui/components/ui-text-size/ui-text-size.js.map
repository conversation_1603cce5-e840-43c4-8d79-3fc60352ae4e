{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-text-size/ui-text-size.vue?3fad", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-text-size/ui-text-size.vue?fbb8", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-text-size/ui-text-size.vue?a954", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-text-size/ui-text-size.vue?78a4", "uni-app:///ui/components/ui-text-size/ui-text-size.vue"], "names": ["name", "data", "size", "props", "ui", "type", "default", "watch", "created", "_this", "methods", "slider<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;;;AAG3D;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2LAEN;AACP,KAAK;AACL;AACA,aAAa,yNAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAu3B,CAAgB,w0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiB34B;AAAA,eACA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "ui/components/ui-text-size/ui-text-size.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-text-size.vue?vue&type=template&id=d9c1381a&\"\nvar renderjs\nimport script from \"./ui-text-size.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-text-size.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-text-size/ui-text-size.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-text-size.vue?vue&type=template&id=d9c1381a&\"", "var components\ntry {\n  components = {\n    uiMenu: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-menu/ui-menu\" */ \"@/ui/components/ui-menu/ui-menu.vue\"\n      )\n    },\n    uiMenuItem: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-menu-item/ui-menu-item\" */ \"@/ui/components/ui-menu-item/ui-menu-item.vue\"\n      )\n    },\n    uiModal: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-modal/ui-modal\" */ \"@/ui/components/ui-modal/ui-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$Modal.show(\"textSizeSet\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-text-size.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-text-size.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"\">\n\t\t<ui-menu :ui=\"ui\"><ui-menu-item title=\"文字大小\" @click.native=\"$Modal.show('textSizeSet')\"></ui-menu-item></ui-menu>\n\t\t<ui-modal name=\"textSizeSet\" title=\"文字大小\">\t\t\t\n\t\t\t<view class=\"p-3 mx-5 text-center mt-4\">\n\t\t\t\t选择合适的文字大小来动态调整界面布局。\n\t\t\t</view>\n\t\t\t<view class=\"flex-bar p-3\">\n\t\t\t\t<view class=\"p-2\" style=\"font-size: 12px;\">小</view>\n\t\t\t\t<view class=\"p-2 flex-sub\"><slider @change=\"sliderChange\" :value=\"sys_text\" :min=\"0\" :max=\"4\" /></view>\n\t\t\t\t<view class=\"p-2 mr-2\" style=\"font-size: 20px;\">大</view>\n\t\t\t</view>\n\t\t</ui-modal>\n\t</view>\n</template>\n\n<script>\nlet _this = null;\nexport default {\n\tname: 'UiTextSize',\n\tdata() {\n\t\treturn {\n\t\t\tsize: ['sm', 'df', 'lg', 'xl', 'xxl']\n\t\t};\n\t},\n\tprops: {\n\t\tui: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t}\n\t},\n\twatch: {},\n\tcreated() {\n\t\t_this = this;\n\t},\n\tmethods: {\n\t\tsliderChange(e) {\n\t\t\tthis.$store.commit('setText',e.detail.value);\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\"></style>\n"], "sourceRoot": ""}