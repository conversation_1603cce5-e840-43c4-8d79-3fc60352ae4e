{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?1eb3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?26bb", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?2d5f", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?823c", "uni-app:///ui/components/ui-title/ui-title.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?f1f3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-title/ui-title.vue?4f2f"], "names": ["name", "data", "props", "ui", "type", "default", "bg", "title", "desc", "titleUi", "descUi", "depth", "dot", "line", "isIcon", "hasDot", "hasLine", "align", "tpl", "inLine", "watch", "created", "mounted", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,o0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+Bv4B;EACAA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;EACAC;EACAC;EACAC;AACA;AAAA,2B;;;;;;;;;;;;ACtGA;AAAA;AAAA;AAAA;AAAsnD,CAAgB,29CAAG,EAAC,C;;;;;;;;;;;ACA1oD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-title/ui-title.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-title.vue?vue&type=template&id=29fca953&\"\nvar renderjs\nimport script from \"./ui-title.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-title.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-title.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-title/ui-title.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-title.vue?vue&type=template&id=29fca953&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-title.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-title.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ui-title\" :class=\"[ui,align, tpl, bg, { line: inLine }, 'heading-' + depth]\">\r\n\t\t<view class=\"anchor\" :id=\"title\" :style=\"{top:`-${sys_navBar}px`}\"></view>\r\n\t\t<view class=\"action\" :class=\"[hasLine ? 'hasLine' : '', hasDot ? 'hasDot' : '']\">\r\n\t\t\t<div class=\"action-icon\">\r\n\t\t\t\t<block v-if=\"isIcon\">\r\n\t\t\t\t\t<view class=\"ui-TC-Main _icon-titles\" v-if=\"depth < 3\"></view>\r\n\t\t\t\t\t<view class=\"ui-TC-Main _icon-title\" v-if=\"depth == 3\"></view>\r\n\t\t\t\t\t<view class=\"ui-TC-Main _icon-title icon-sm\" v-if=\"depth > 3\"></view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<slot name=\"icon\"></slot>\r\n\t\t\t</div>\r\n\t\t\t<view class=\"action-title\" :class=\"[titleUi]\" v-if=\"title != ''\">\r\n\t\t\t\t{{ title }}\r\n\t\t\t\t<slot></slot>\r\n\t\t\t\t<view class=\"action-line\" :class=\"[line]\" v-if=\"hasLine\">\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"action-dot\" :class=\"[dot]\" v-if=\"hasDot\">\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-desc\" :class=\"[descUi]\" v-if=\"desc != ''\">{{ desc }}</view>\r\n\t\t</view>\r\n\t\t<view class=\"more\"><slot name=\"more\"></slot></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: 'UiTitle',\r\n\tdata() {\r\n\t\treturn {};\r\n\t},\r\n\tprops: {\r\n\t\tui: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tbg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'bg-none'\r\n\t\t},\r\n\t\ttitle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tdesc: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ttitleUi: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-TC'\r\n\t\t},\r\n\t\tdescUi: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-TC-3'\r\n\t\t},\r\n\t\tdepth: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tdot: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-BG-Main'\r\n\t\t},\r\n\t\tline: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-BG-Main'\r\n\t\t},\r\n\t\tisIcon: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\thasDot: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\thasLine: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\talign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\ttpl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'default'\r\n\t\t},\r\n\t\tinLine: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\twatch: {},\r\n\tcreated() {},\r\n\tmounted() {},\r\n\tmethods: {}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\nui-title {\r\n\tdisplay: block;\r\n}\r\n\r\n.ui-title {\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\t// padding: 30rpx;\r\n\t.anchor {\r\n\t\tpointer-events: none;\r\n\t\tposition: absolute;\r\n\t}\r\n\t&.line {\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t.action-icon {\r\n\t\tmargin-left: -10rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t.action {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t&.hasLine {\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 2;\r\n\t\t\t.action-line {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 68%;\r\n\t\t\t\theight: 0.5em;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tz-index: -1;\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.hasDot {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\tz-index: 2;\r\n\t\t\t.action-dot {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 18px;\r\n\t\t\t\theight: 18px;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\ttop: -0.3em;\r\n\t\t\t\tz-index: -1;\r\n\t\t\t\tmargin-left: -0.5em;\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.action-title {\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t.action-desc {\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\ttext-transform: Capitalize;\r\n\t\t}\r\n\t}\r\n\t&.center {\r\n\t\tjustify-content: center;\r\n\t\tletter-spacing: 5rpx;\r\n\t\t.hasLine {\r\n\t\t\t.action-line {\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\tmargin: auto;\r\n\t\t\t}\r\n\t\t}\r\n\t}\n\t&.right {\r\n\t\tjustify-content: flex-end;\r\n\t\tletter-spacing: 5rpx;\r\n\t\t.hasDot {\r\n\t\t\t.action-dot {\r\n\t\t\t\tleft: auto;\r\n\t\t\t\tright: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.hasLine {\r\n\t\t\t.action-line {\r\n\t\t\t\tleft: auto;\r\n\t\t\t\tright: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.stack {\r\n\t\t.action {\r\n\t\t\tposition: relative;\r\n\t\t\t&.hasLine {\r\n\t\t\t\t.action-line {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.action-title {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: 2;\r\n\t\t\t\ttop: 10rpx;\r\n\t\t\t}\r\n\t\t\t.action-desc {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\ttop: -10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t&.center,&.right {\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t\t.action {\n\t\t\t\tposition: initial;\n\t\t\t}\n\t\t\t.action-desc {\n\t\t\t\ttop: 10rpx;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\tmargin: auto;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\t\t&.right {\r\n\t\t\t.action-desc { \r\n\t\t\t\ttext-align: right;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t&.center-column {\r\n\t\tjustify-content: center;\r\n\t\tletter-spacing: 5rpx;\r\n\t\t.action {\r\n\t\t\tflex-direction: column;\r\n\t\t\t.action-desc {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t}\r\n\t\t\t&.hasDot {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t.action-dot {\r\n\t\t\t\t\theight: 0px;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\tview {\r\n\t\t\t\t\t\theight: 1px;\r\n\t\t\t\t\t\tbackground: inherit;\r\n\t\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\tleft: -500rpx;\r\n\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\tright: -500rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\twidth: 4px;\r\n\t\t\t\t\t\t\theight: 12px;\r\n\t\t\t\t\t\t\tborder-radius: 20px;\r\n\t\t\t\t\t\t\tbackground: inherit;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\ttop: -5px;\r\n\t\t\t\t\t\t\tleft: -10px;\r\n\t\t\t\t\t\t\ttransform: rotate(30deg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::before {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\twidth: 4px;\r\n\t\t\t\t\t\t\theight: 20px;\r\n\t\t\t\t\t\t\tborder-radius: 20px;\r\n\t\t\t\t\t\t\tbackground: inherit;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\ttop: -10px;\r\n\t\t\t\t\t\t\tleft: -20px;\r\n\t\t\t\t\t\t\ttransform: rotate(30deg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&.hasLine {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t.action-line {\r\n\t\t\t\t\theight: 0px;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\tview {\r\n\t\t\t\t\t\theight: 1px;\r\n\t\t\t\t\t\tbackground: inherit;\r\n\t\t\t\t\t\twidth: 160rpx;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tmargin: auto;\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\tleft: -480rpx;\r\n\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\tright: -480rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tbackground: linear-gradient(to left, var(--ui-BG), rgba(255, 255, 255, 0));\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.heading-0 {\n\tpadding-top: #{map-get($spacers, 4)};\n\tpadding-bottom: #{map-get($spacers, 4)};\n\tfont-size: calc(#{map-get($fontsize, xl)}rpx + var(--textSize)) !important;\n\t.action-desc{\n\t\tfont-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;\n\t}\r\n}\n\r\n.heading-1 {\n\tpadding-top: #{map-get($spacers, 5)};\n\tpadding-bottom: #{map-get($spacers, 4)};\n\tfont-size: calc(#{map-get($fontsize, xxl)}rpx + var(--textSize)) !important;\n\t.action-desc{\n\t\tfont-size: calc(#{map-get($fontsize, lg)}rpx + var(--textSize)) !important;\n\t}\r\n}\r\n\r\n.heading-2 {\n\tpadding-top: #{map-get($spacers, 4)};\n\tpadding-bottom: #{map-get($spacers, 3)};\n\tfont-size: calc(#{map-get($fontsize, xl)}rpx + var(--textSize)) !important;\t\n\t.action-desc{\n\t\tfont-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;\n\t}\r\n}\r\n\r\n.heading-3 {\n\tpadding-top: #{map-get($spacers, 3)};\n\tpadding-bottom: #{map-get($spacers, 2)};\n\tfont-size: calc(#{map-get($fontsize, lg)}rpx + var(--textSize)) !important;\t\n\t.action-desc{\n\t\tfont-size: calc(#{map-get($fontsize, sm)}rpx + var(--textSize)) !important;\n\t}\r\n}\r\n\r\n.heading-4 {\n\tpadding-top: #{map-get($spacers, 2)};\n\tpadding-bottom: #{map-get($spacers, 1)};\n\tfont-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;\t\n\t.action-desc{\n\t\tfont-size: calc(#{map-get($fontsize, xs)}rpx + var(--textSize)) !important;\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-title.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-title.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191833\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}