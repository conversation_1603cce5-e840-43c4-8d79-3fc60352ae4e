{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?9f31", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?8dbe", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?36ab", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?906f", "uni-app:///ui/components/ui-sys/ui-sys.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?cbd6", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-sys/ui-sys.vue?360d"], "names": ["name", "data", "isloading", "props", "styles", "type", "default", "bg", "ui", "img", "navBg", "loading", "title", "footer", "tabbar", "created", "_this", "uni", "watch", "handler", "immediate", "computed", "dialog", "toast", "mounted", "setTimeout", "destroyed", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyCr4B;AAAA;AAAA;AACA;AAAA,gBACA;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IAAA;IACAC;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;IACAP;MACAQ;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IAAA;IACAC;MACA;QACA;MACA;IACA;EACA;EACAC;IACAT;EACA;EACAU;AACA;AAAA,4B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAAonD,CAAgB,y9CAAG,EAAC,C;;;;;;;;;;;ACAxoD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-sys/ui-sys.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-sys.vue?vue&type=template&id=198f749a&\"\nvar renderjs\nimport script from \"./ui-sys.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-sys.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-sys.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-sys/ui-sys.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-sys.vue?vue&type=template&id=198f749a&\"", "var components\ntry {\n  components = {\n    appNavbar: function () {\n      return import(\n        /* webpackChunkName: \"app/components/app-navbar/app-navbar\" */ \"@/app/components/app-navbar/app-navbar.vue\"\n      )\n    },\n    appFooter: function () {\n      return import(\n        /* webpackChunkName: \"app/components/app-footer/app-footer\" */ \"@/app/components/app-footer/app-footer.vue\"\n      )\n    },\n    uiTabbar: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-tabbar/ui-tabbar\" */ \"@/ui/components/ui-tabbar/ui-tabbar.vue\"\n      )\n    },\n    uiModal: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-modal/ui-modal\" */ \"@/ui/components/ui-modal/ui-modal.vue\"\n      )\n    },\n    uiToast: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-toast/ui-toast\" */ \"@/ui/components/ui-toast/ui-toast.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.styles])\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.dialog.success($event)\n    }\n    _vm.e1 = function ($event) {\n      return _vm.toast.success($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-sys.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-sys.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ui-page\" :class=\"['theme-' + sys_theme, 'main-' + sys_main, 'font-' + sys_text]\">\r\n\t\t<view class=\"page-body\" :class=\"[bg,ui]\" :style=\"[styles]\">\r\n\t\t\t<view class=\"page-body-main\">\r\n\t\t\t\t<app-navbar :bg=\"navBg\" :title=\"title\" v-if=\"title\"></app-navbar>\r\n\t\t\t\t<slot></slot>\r\n\t\t\t\t<app-footer v-if=\"footer\"></app-footer>\r\n\t\t\t\t<ui-tabbar ui=\"shadow border-top\" v-if=\"tabbar\"></ui-tabbar>\r\n\r\n\t\t\t\t<view class=\"loading-body bg-blur\" :style=\"[{ zIndex: 1999 }]\" v-if=\"isloading\">\r\n\t\t\t\t\t<view class=\"_icon-loading icon-spin text-sl mb-3\"></view>\r\n\t\t\t\t\t<view>加载中</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<ui-modal\r\n\t\t\t\tname=\"sys_dialog\"\r\n\t\t\t\t:title=\"dialog.title\"\r\n\t\t\t\t:content=\"dialog.content\"\r\n\t\t\t\t:showCancel=\"dialog.showCancel\"\r\n\t\t\t\t:cancelText=\"dialog.cancelText\"\r\n\t\t\t\t:confirmText=\"dialog.confirmText\"\r\n\t\t\t\t@success=\"dialog.success($event)\"\r\n\t\t\t></ui-modal>\r\n\r\n\t\t\t<ui-toast\r\n\t\t\t\tname=\"sys_dialog\"\r\n\t\t\t\t:title=\"toast.title\"\r\n\t\t\t\t:duration=\"toast.duration\"\r\n\t\t\t\t:mask=\"toast.mask\"\r\n\t\t\t\t:isLoading=\"toast.isLoading\"\r\n\t\t\t\t:image=\"toast.image\"\r\n\t\t\t\t:icon=\"toast.icon\"\r\n\t\t\t\t@success=\"toast.success($event)\"\r\n\t\t\t></ui-toast>\r\n\t\t\t<image :src=\"img\" mode=\"aspectFill\" v-if=\"img\" class=\"page-img\"></image>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\nvar _this = {};\r\nexport default {\r\n\tname: 'UiSys',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisloading: true\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tstyles: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t},\r\n\t\tbg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-BG-2'\r\n\t\t},\r\n\t\tui: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\timg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tnavBg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'bg-blur'\r\n\t\t},\r\n\t\tloading: {\r\n\t\t\ttype: [String, Boolean],\r\n\t\t\tdefault: 'auto'\r\n\t\t},\r\n\t\ttitle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tfooter: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\ttabbar: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\t_this = this;\r\n\t\tuni.$on('_hideLoading_' + this.$root._uid, e => {\r\n\t\t\tif (this.loading == 'auto') {\r\n\t\t\t\tthis.isloading = e;\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\twatch:{\r\n\t\tloading: {\r\n\t\t\thandler(val) {\r\n\t\t\t\tif (val==false) {\r\n\t\t\t\t\tthis.isloading = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState({\r\n\t\t\tdialog: state => state.modal.dialog,\r\n\t\t\ttoast: state => state.modal.toast\r\n\t\t})\r\n\t},\r\n\tmounted() {\r\n\t\tsetTimeout(() => {\r\n\t\t\tif (this.loading == 'auto') {\r\n\t\t\t\tthis.isloading = false;\r\n\t\t\t}\r\n\t\t}, 800);\r\n\t},\r\n\tdestroyed() {\r\n\t\tuni.$off('_hideLoading_' + this.$root._uid);\r\n\t},\r\n\tmethods: {}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ui-page {\r\n\tposition: relative;\r\n\tcolor: var(--ui-TC);\r\n\tbackground-color: var(--ui-BG-1) !important;\r\n\tz-index: 2;\r\n\tdisplay: flex;\r\n\t.page-body {\r\n\t\tflex: 1;\r\n\t\tmin-height: 100vh;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t\toverflow: hidden;\r\n\t\t.page-body-main {\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\t\t.page-img {\r\n\t\t\theight: 177vw;\r\n\t\t\twidth: 100vw;\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tz-index: 0;\r\n\t\t}\r\n\t}\r\n}\r\n.loading-body {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 999;\r\n\t// opacity: .98;\r\n}\r\n\r\n// .ui-toast-box {\r\n// \tposition: fixed;\r\n// \twidth: 100vw;\r\n// \theight: 20vh;\r\n// \tbottom: 5vh;\r\n// \tleft: 0;\r\n// \tdisplay: flex;\r\n// \talign-items: center;\r\n// \tjustify-content: center;\r\n// \tpointer-events: none;\r\n// \tz-index: 999;\r\n// \t.ui-toast{\r\n// \t\tpadding: .5em 1em;\r\n// \t\tborder-radius: $radius;\r\n// \t\tmax-width: 90vw;\r\n// \t\tmax-height: 20vh;\r\n// \t\toverflow: hidden;\r\n// \t}\r\n// }\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-sys.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-sys.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191698\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}