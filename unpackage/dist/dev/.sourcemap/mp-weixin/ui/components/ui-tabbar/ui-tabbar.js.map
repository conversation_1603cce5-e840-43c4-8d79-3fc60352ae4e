{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?6b5e", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?3507", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?da37", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?146e", "uni-app:///ui/components/ui-tabbar/ui-tabbar.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?e0e0", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-tabbar/ui-tabbar.vue?f6f1"], "names": ["name", "data", "cur", "props", "tpl", "type", "default", "icon", "bg", "ui", "curText", "text", "noFixed", "watch", "computed", "tabbar", "created", "uni", "methods", "_onShow", "clickItem", "changeTab", "url", "fail", "console", "navTo", "tabbarheight"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAo3B,CAAgB,q0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoBx4B;EACAA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACAJ;QACAK;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACAR;QACAK;QACAC;UACAC;QACA;MACA;IACA;IACAE;MACAT;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAAunD,CAAgB,49CAAG,EAAC,C;;;;;;;;;;;ACA3oD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-tabbar/ui-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-tabbar.vue?vue&type=template&id=4a5db1a3&\"\nvar renderjs\nimport script from \"./ui-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-tabbar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-tabbar/ui-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-tabbar.vue?vue&type=template&id=4a5db1a3&\"", "var components\ntry {\n  components = {\n    uiFixed: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-fixed/ui-fixed\" */ \"@/ui/components/ui-fixed/ui-fixed.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-tabbar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ui-fixed bottom @getHeight=\"tabbarheight\" :bg=\"bg\" :placeholder=\"true\" :ui=\"ui\" :noFixed=\"noFixed\">\r\n\t\t<view class=\"ui-tabbar-box\">\r\n\t\t\t<view class=\"ui-tabbar\">\r\n\t\t\t\t<block class=\"\" v-for=\"(item, index) in tabbar\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"ui-tabbar-item\" :class=\"[index == cur ? curText : text, item.type, { cur: index == cur }]\" @tap=\"clickItem(item)\">\r\n\t\t\t\t\t\t<view class=\"ui-tabbar-icon\" :class=\"item.center ? `center border shadow-${sys_main}` : ''\">\r\n\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t<text :class=\"index == cur ? item.curIcon : item.icon\" ></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ui-tabbar-text\">{{ item.title }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</ui-fixed>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tname: 'UiTabbar',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcur: 0\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\ttpl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\ticon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tbg: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'bg-blur'\r\n\t\t},\r\n\t\tui: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tcurText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'ui-TC-Main'\r\n\t\t},\r\n\t\ttext: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'text-c'\r\n\t\t},\n\t\tnoFixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t},\r\n\twatch: {},\r\n\tcomputed: {\r\n\t\ttabbar() {\r\n\t\t\treturn this.$store.getters.getTabbar;\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tuni.hideTabBar();\r\n\t},\r\n\tmethods: {\r\n\t\t_onShow() {\n\t\t\tlet page = getCurrentPages();\n\t\t\tlet url = page[page.length - 1].route\r\n\t\t\tthis.tabbar.map((item,index)=>{\n\t\t\t\tif(item.url == '/'+url){\n\t\t\t\t\tthis.cur = index\n\t\t\t\t}\n\t\t\t})\r\n\t\t},\r\n\t\tclickItem(item) {\r\n\t\t\tif (item.type == 'tab') {\r\n\t\t\t\tthis.changeTab(item.url);\r\n\t\t\t}\r\n\t\t\tif (item.type == 'nav') {\r\n\t\t\t\tthis.navTo(item.url);\r\n\t\t\t}\r\n\t\t},\r\n\t\tchangeTab(url) {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: url,\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tnavTo(url) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: url,\r\n\t\t\t\tfail(res) {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\ttabbarheight(val) {\r\n\t\t\tuni.setStorageSync('tabbar', val);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ui-tabbar-box {\r\n\t.ui-tabbar {\r\n\t\tpadding: 10rpx;\r\n\t\tdisplay: flex;\r\n\r\n\t\t.ui-tabbar-item {\r\n\t\t\ttext-align: center;\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\t\t\tposition: relative;\r\n\t\t\tz-index: 10;\r\n\r\n\t\t\t.ui-tabbar-icon {\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-size: 45rpx;\r\n\r\n\t\t\t\t// background-color: #f1f1f1;\r\n\t\t\t\t.ui-tabbar-image {\r\n\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.center {\r\n\t\t\t\t\tbackground-color: var(--ui-BG-Main);\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\ttransform: scale(1.3) translateY(-6px);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.badge {\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.ui-btn {\r\n\t\t\t\tmargin-top: -10px;\r\n\t\t\t\tmargin-bottom: 5px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\r\n\t\t\t.ui-tabbar-text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: var(--ui-TC-2);\r\n\t\t\t}\r\n\r\n\t\t\t&.cur .ui-tabbar-text {\r\n\t\t\t\tcolor: var(--ui-TC-Main);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-tabbar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-tabbar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191815\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}