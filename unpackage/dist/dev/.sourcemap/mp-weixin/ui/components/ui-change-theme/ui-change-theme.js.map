{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?26d9", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?2454", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?1c68", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?32bc", "uni-app:///ui/components/ui-change-theme/ui-change-theme.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?8cd5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/ui/components/ui-change-theme/ui-change-theme.vue?2151"], "names": ["name", "data", "color", "props", "ui", "type", "default", "watch", "created", "_this", "methods", "setTheme", "chooseColor"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACc;;;AAG5E;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA03B,CAAgB,20BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0D94B;AAAA,eACA;EACAA;EACAC;IACA;MACAC,yGACA;IAEA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAA6nD,CAAgB,k+CAAG,EAAC,C;;;;;;;;;;;ACAjpD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "ui/components/ui-change-theme/ui-change-theme.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ui-change-theme.vue?vue&type=template&id=5dbb1c3f&\"\nvar renderjs\nimport script from \"./ui-change-theme.vue?vue&type=script&lang=js&\"\nexport * from \"./ui-change-theme.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ui-change-theme.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"ui/components/ui-change-theme/ui-change-theme.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-change-theme.vue?vue&type=template&id=5dbb1c3f&\"", "var components\ntry {\n  components = {\n    uiSwitch: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-switch/ui-switch\" */ \"@/ui/components/ui-switch/ui-switch.vue\"\n      )\n    },\n    uiRadio: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-radio/ui-radio\" */ \"@/ui/components/ui-radio/ui-radio.vue\"\n      )\n    },\n    uiModal: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-modal/ui-modal\" */ \"@/ui/components/ui-modal/ui-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$Modal.show(\"ColorPicker\")\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-change-theme.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-change-theme.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"ui-menu sm-border\" :class=\"[ui]\">\r\n\t\t\t<!-- #ifndef APP-PLUS -->\r\n\t\t\t<view class=\"ui-menu-item\" @tap=\"setTheme(sys_theme == 'auto' ? 'light' : 'auto')\">\r\n\t\t\t\t<view class=\"ui-menu-item-content\">跟随系统</view>\r\n\t\t\t\t<view class=\"action\">\r\n\t\t\t\t\t<ui-switch :checked=\"sys_theme == 'auto'\"></ui-switch>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\r\n\t\t\t<view class=\"ui-menu-item\" v-if=\"sys_theme != 'auto'\">\r\n\t\t\t\t<view class=\"flex flex-center py-5 text-center flex-sub\">\r\n\t\t\t\t\t<view class=\"pr-5\">\r\n\t\t\t\t\t\t<view class=\"mobile-phone border\" @tap=\"setTheme('light')\"\r\n\t\t\t\t\t\t\t:class=\"{ cur: sys_theme == 'light' }\">\r\n\t\t\t\t\t\t\t<view class=\"ui-BG-Main radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"bg-gray radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"bg-gray radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"borders border-top mt-5 mx-4 border-dark-6\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>浅色</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"pl-5\">\r\n\t\t\t\t\t\t<view class=\"mobile-phone border dark\" @tap=\"setTheme('dark')\"\r\n\t\t\t\t\t\t\t:class=\"{ cur: sys_theme == 'dark' }\">\r\n\t\t\t\t\t\t\t<view class=\"ui-BG-Main radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"bg-dark-4 radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"bg-dark-4 radius\"></view>\r\n\t\t\t\t\t\t\t<view class=\"borders border-top mt-5 mx-4 border-white\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>深色</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ui-menu-item\">\r\n\t\t\t\t<view class=\"ui-menu-item-content\">强调色</view>\r\n\t\t\t\t<view class=\"action\" @tap=\"$Modal.show('ColorPicker')\">\r\n\t\t\t\t\t<ui-radio :bg=\"`bg-${sys_main} shadow-${sys_main} shadow-sm`\"></ui-radio>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<ui-modal name=\"ColorPicker\">\r\n\t\t\t<view class=\"ui-grid ui-cols-6 p-3\">\r\n\t\t\t\t<view class=\"text-center ui-item py-2 my-2\" v-for=\"(item, index) in color\" :key=\"index\"\r\n\t\t\t\t\t@tap=\"chooseColor(item)\">\r\n\t\t\t\t\t<ui-radio :value=\"sys_main\" :label=\"item\" :bg=\"`bg-${item} shadow-${item} shadow-sm`\"\r\n\t\t\t\t\t\t:unbg=\"`bg-${item} shadow-${item} shadow-sm`\"></ui-radio>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</ui-modal>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet _this = null;\r\n\texport default {\r\n\t\tname: 'UiChangeTheme',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcolor: ['red', 'orange', 'yellow', 'olive', 'green', 'cyan', 'blue', 'purple', 'mauve', 'pink', 'brown',\r\n\t\t\t\t\t'grey'\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tui: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t},\r\n\t\twatch: {},\r\n\t\tcreated() {\r\n\t\t\t_this = this;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetTheme(val) {\r\n\t\t\t\tthis.$store.commit('setTheme', val);\r\n\t\t\t},\r\n\t\t\tchooseColor(val) {\r\n\t\t\t\tthis.$store.commit('setMain', val);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.mobile-phone {\r\n\t\twidth: 170rpx;\r\n\t\theight: 305rpx;\r\n\t\tborder-radius: 10px;\r\n\t\tpadding: 10rpx 15rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tmargin-bottom: 10px;\r\n\r\n\t\t&:after {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t&.cur:after {\r\n\t\t\tdisplay: block;\r\n\t\t\tborder: 10rpx solid var(--ui-BG-Main) !important;\r\n\t\t\tborder-radius: 20px;\r\n\t\t}\r\n\r\n\t\t.ui-BG-Main {\r\n\t\t\theight: 100rpx;\r\n\t\t\tmargin-top: 10px;\r\n\t\t\tbackground-color: rgba(122, 122, 122, 0.5) !important;\r\n\t\t}\r\n\r\n\t\t&.cur .ui-BG-Main {\r\n\t\t\tbackground-color: var(--ui-BG-Main) !important;\r\n\t\t}\r\n\r\n\t\t.bg-gray,\r\n\t\t.bg-dark-4 {\r\n\t\t\theight: 40rpx;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\r\n\t\t&.dark {\r\n\t\t\tbackground-color: #232323;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-change-theme.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ui-change-theme.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736191801\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}