{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?5f1b", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?f3cd", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?dca0", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?fa86", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?2ec4", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/index/index.vue?8562"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "postsList", "StickyList", "isLastPage", "page", "search", "categories", "showerror", "showCategoryName", "categoryName", "showallDisplay", "displayHeader", "displaySwiper", "floatDisplay", "displayfirstSwiper", "listAdsuccess", "webSiteName", "domain", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "swipe_nav", "selected_nav", "components", "props", "onShareAppMessage", "title", "path", "success", "fail", "onPullDownRefresh", "self", "postsShowSwiperList", "onReachBottom", "console", "onLoad", "uni", "setTimeout", "onShow", "nowDate", "methods", "formSubmit", "key", "url", "content", "showCancel", "onShareTimeline", "getHomeconfig", "fetchPostsData", "getCategoriesRequest", "getPostsRequest", "item", "post_medium_image", "strdate", "mask", "duration", "fetchstickyPostsData", "getStickyPostsRequest", "loadMore", "redictDetail", "onNavRedirect", "extraData", "redictAppDetail", "e", "type", "appid", "redictHome"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,iMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAi2B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC+Kr3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACAvB;MACAG;MACAE;MACAC;MACAV;MACAC;MACA2B;IACA;IACA;IACA;EACA;EACAC;IACA;IAEA;MACAF;QACA1B;MACA;MACA6B;MACA;IACA;MACAA;IACA;EACA;EACAC;IACA;;IAEAC;MACAV;IACA;IAEAK;IACAA;IACA;;IAEA;MACAA;QACAZ;MACA;MACAiB;;MAEAC;QACAN;UACAZ;QACA;MACA;IACA;IAEA;EACA;EACAmB;IACAF;IACA;IACAG;IACAA;EAGA;EACAC;IACAC;MACA;MACA;MAEA;QACAC;MACA;QACAA;MACA;MAEA;QACAC;QACAP;UACAO;QACA;MACA;QACAP;UACAV;UACAkB;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;QACApB;QACAC;MACA;IACA;IAEAoB;MACA;MACA;MACA;MACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAhB;UACAV;UACAC;QAAA,mBACAA,cACA;QACAc;QACAA;QACAA;QACAA;MACA;IACA;IAEA;IACAY;MACA;MACA;MACA;MACA;MACA;MAEA;QACAjB;UACA7B;QACA;MACA;MAEA;MACA6B;QACAX;MACA;MACA;MACA6B;QACA;UACAhD;UACA8B;YACAxB;UACA;QACA;QAEA;QACA2C;UACA;YACA;cACA;gBACAnB;kBACA3B;kBACAgB;gBACA;cACA;cAEAW;gBACAjB;gBACAZ,0CACAD;kBACA;kBAEA,0BACA;oBACAkD,qBACA;kBACA;oBACAA;kBACA;kBAEA,SACAC,qBACA,aACAA,yBACA;oBACAD,yBACA;kBACA;kBAEAA,wBACAE;kBACA;gBACA;cACA;YACA;cACA,0BACA;gBACAtB;kBACA3B;kBACAgB;gBACA;gBACAgB;kBACAV;kBACA4B;kBACAC;gBACA;cACA;gBACAnB;kBACAV;kBACA6B;gBACA;cACA;YACA;UACA;QACA;UACA;YACAxB;cACAvB;cACAM;YACA;UACA;YACAsB;cACAV;cACAkB;cACAC;YACA;YACAd;cACA1B;YACA;UACA;QACA;UACA+B;UACAL;YACAX;UACA;UACAgB;QACA;MACA;IACA;IACA;IACAoB;MACA;MACA;MACA;MACA;MACAzB;QACAX;MACA;MACA;MACA6B;QACA;UACAhD;UACA8B;YACAxB;UACA;QACA;QAEA;QACAkD;UACA;YACA;cACA;cACA1B;gBACAjB;gBACAX;cACA;YACA,QAEA;UACA;QACA,8BAEA;UACAiC;UACAL;YACAX;UACA;UACAgB;QACA;MACA;IACA;IACA;IACAsB;MACA;MAEA;QACA3B;UACA1B;QACA;;QAEA;MACA;QACA+B;UACAV;UACA4B;UACAC;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;QACAhB;MACAP;QACAO;MACA;IACA;IACA;IACAiB;MACA;MACA;MACA;MACA,yFACAC;MAEA;QACA;QACAzB;UACAO;QACA;MACA;QAAA;QACA;UACAA;UACAP;YACAO;UACA;QACA;QAAA;QACA;UACA;QASA;IACA;IACA;IACAmB;MACA,4BAKAC;QAJAC;QACAC;QACAtB;QACAhB;MAGA;QACA;QACAS;UACAO;QACA;MACA;MAEA;QACA;QACAA;QACAP;UACAO;QACA;MACA;MAEA;QACA;QACAA;QACAP;UACAO;QACA;MACA;IACA;IACA;IACAuB;MACA;MACA;QACAvB;MACAP;QACAO;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACplBA;AAAA;AAAA;AAAA;AAA0tC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACA9uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n    uiNavbar: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-navbar/ui-navbar\" */ \"@/ui/components/ui-navbar/ui-navbar.vue\"\n      )\n    },\n    uiSwiper: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-swiper/ui-swiper\" */ \"@/ui/components/ui-swiper/ui-swiper.vue\"\n      )\n    },\n    uiTitle: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-title/ui-title\" */ \"@/ui/components/ui-title/ui-title.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.swipe_nav.length\n  var g1 = _vm.selected_nav.length\n  var g2 = g1 > 0 ? _vm.selected_nav.length : null\n  var g3 = _vm.StickyList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ui-sys tabbar>\r\n\t\t<ui-navbar :back=\"false\" bg=\"ui-BG-Main\" op isSlot>\r\n\t\t\t<view class=\"flex-bar p-3\">\r\n\t\t\t\t<image src=\"/static/images/logo.png\" mode=\"aspectFill\" style=\"width: 200rpx;height: 56rpx;\" />\r\n\t\t\t</view>\r\n\t\t</ui-navbar>\r\n\t\t<!-- 首次进入引导添加到“我的小程序” -->\r\n\t\t<view class=\"addMyMiniapp\" v-if=\"isFirst\" @tap.stop.prevent=\"shutAddMyMiniapp\">\r\n\t\t\t<view>点击加入我的小程序 ↑</view>\r\n\t\t\t<text>了解\"{{webSiteName}}\"最新文章</text>\r\n\t\t</view>\r\n\r\n\r\n\t\t<view class=\"index-top  \">\r\n\r\n\t\t\t<view class=\"indexbg ui-BG-Main\" :style=\"'margin-top: -'+(sys_navBar+sys_statusBar+sys_capsule.top)+'rpx;'\">\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"ui-BG-card ui-BG \">\r\n\t\t\t\t<!-- 搜索 -->\r\n\t\t\t\t<form @submit.stop.prevent=\"formSubmit\" @reset.stop.prevent=\"formReset\" id=\"search-form\">\r\n\t\t\t\t\t<view class=\"ui-search-bar  \">\r\n\t\t\t\t\t\t<!-- <view class=\"ui-search-action\">\r\n            <ui-avatar slot=\"left\" ui=\"round\" src=\"/images/gravatar.png\" />\r\n          </view> -->\r\n\t\t\t\t\t\t<view class=\"ui-search-form   round\">\r\n\t\t\t\t\t\t\t<text class=\"ui-search-icon _icon-search\" />\r\n\t\t\t\t\t\t\t<input class=\"ui-search-input ui-search-text-left\" value=\"\" id=\"search-input\" name=\"input\"\r\n\t\t\t\t\t\t\t\tconfirm-type=\"search\" placeholder=\"搜索你感兴趣的内容...\" @confirm=\"formSubmit\" />\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"ui-search-action\">\r\n\t\t\t\t\t\t\t<button slot=\"right\" class=\"ui-btn ui-BG-Main round sm\" form-type=\"submit\">搜索</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</form>\r\n\r\n\r\n\t\t\t\t<!-- 轮播图 -->\r\n\t\t\t\t<view class=\" \">\r\n\t\t\t\t\t<ui-swiper :info=\"swipe_nav\" v-if=\"swipe_nav.length > 0\" dotStyle=\"long\" dotCur=\"ui-BG-Main\"\r\n\t\t\t\t\t\theight=\"260rpx\" imgHeight=\"260rpx\" imgName=\"image\" swiperCss=\"radius\"\r\n\t\t\t\t\t\t@uiTap=\"redictAppDetail\" />\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\r\n\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 图标导航 -->\r\n\t\t<view class=\"index-sticky ui-BG-card ui-BG\" v-if=\"selected_nav.length > 0\">\r\n\t\t\t<ui-title title=\"精选栏目\" desc=\"热点栏目推荐\" depth=\"3\">\r\n\t\t\t\t<text slot=\"icon\" class=\"ui-TC-Main cicon-slack-square\" />\r\n\t\t\t</ui-title>\r\n\t\t\t<view :class=\"'ui-grid '+(selected_nav.length<5?'ui-cols-4':'ui-cols-5')\">\r\n\r\n\t\t\t\t<block v-for=\"(item,index) in (selected_nav)\" :key=\"item.id\">\r\n\r\n\r\n\r\n\t\t\t\t\t<view class=\"ui-item  \" v-if=\"item.enable=='yes'\" @tap.stop.prevent=\"redictAppDetail\" :id=\"item.id\"\r\n\t\t\t\t\t\t:data-type=\"item.type\" :data-url=\"item.url\" :data-appid=\"item.appid\" :data-path=\"item.path\">\r\n\t\t\t\t\t\t<image class=\"round\" mode=\"aspectFill\" :src=\"item.image\"></image>\r\n\t\t\t\t\t\t<text class=\"ui-TC text-df\">{{item.title}}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<!-- 推荐文章 -->\r\n\t\t<view class=\" ui-BG-card ui-BG\" v-if=\"StickyList.length > 0\">\r\n\t\t\t<ui-title title=\"推荐文章\" desc=\"汇聚精彩文章\" depth=\"3\">\r\n\t\t\t\t<text slot=\"icon\" class=\"ui-TC-Main cicon-whatshot\" />\r\n\t\t\t</ui-title>\r\n\r\n\r\n\t\t\t<swiper class=\"sticky-list\" :autoplay=\"true\" interval=\"5000\" duration=\"400\" :circular=\"true\"\r\n\t\t\t\tdisplay-multiple-items=\"2\" :vertical=\"true\">\r\n\t\t\t\t<swiper-item class=\"sticky-list-item\" :id=\"item.id\" @tap=\"redictDetail\"\r\n\t\t\t\t\tv-for=\"(item,index) in StickyList\" :key=\"item.id\">\r\n\r\n\r\n\r\n\t\t\t\t\t<view class=\"sticky-list-item-title ui-TC text-df\">\r\n\t\t\t\t\t\t<text class=\"dot ui-BG-Main\"></text> {{item.title.rendered}}\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\r\n\t\t</view>\r\n\r\n\r\n\t\t<!-- 文章列表 -->\r\n\t\t<view class=\"container \">\r\n\t\t\t<!-- 列表template模板 -->\r\n\t\t\t<view class=\"post-list\">\r\n\t\t\t\t<block v-for=\"(item,index) in postsList\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"post-item ui-BG ui-BG-card\" :index=\"index\" :id=\"item.id\" @tap=\"redictDetail\">\r\n\t\t\t\t\t\t<view class=\"post-desc\">\r\n\t\t\t\t\t\t\t<view class=\"post-title text-lg ui-TC\">\r\n\t\t\t\t\t\t\t\t<text>{{item.title.rendered}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"post-data ui-TC-3 text-xs\">\r\n\t\t\t\t\t\t\t\t<text>{{item.date}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-popover-o  \"> {{item.total_comments}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-favorite-o \"> {{item.like_count}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-eye-o \"> {{item.pageviews}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image :src=\"item.post_medium_image\" mode=\"aspectFill\" class=\"post-img\"\r\n\t\t\t\t\t\t\tv-if=\"item.post_medium_image\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view :style=\"'display:'+(isLoading?'block':'none')+';margin-top: 48rpx;'\">\r\n\t\t\t\t<!-- parse <template is=\"tempLoading\"/> -->\r\n\t\t\t\t<block name=\"tempLoading\">\r\n\t\t\t\t\t<view class=\"sk-three-bounce\">\r\n\t\t\t\t\t\t<view class=\"sk-child sk-bounce1 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t\t<view class=\"sk-child sk-bounce2 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t\t<view class=\"sk-child sk-bounce3 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t\t<!-- 无更多文章提示 -->\r\n\t\t\t<view class=\"loadingmore\" :style=\"'display:'+(floatDisplay)\">\r\n\t\t\t\t<view class=\"no-more\" v-if=\"(isLastPage)\">- 无更多文章 -</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 版权信息template模板 -->\r\n\t\t\t<view class=\"copyright\">\r\n\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 无法访问网络 -->\r\n\t\t<view class=\"showerror\" :style=\"'display:'+(showerror)\">\r\n\t\t\t<text class=\"cicon-wifi-off text-xsl\"></text>\r\n\t\t\t<view class=\"errortext\">\r\n\t\t\t\t暂时无法访问网络，下拉重试...\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</ui-sys>\r\n</template>\r\n\r\n<script>\r\n\t/*\r\n\t * \r\n\t * 微慕小程序开源版\r\n\t * author: jianbo\r\n\t * organization: 微慕  www.minapper.com\r\n\t * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n\t * 技术支持微信号：iamxjb\r\n\t * 开源协议：MIT\r\n\t * \r\n\t *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\r\n\t */\r\n\tvar Api = require(\"../../utils/api.js\");\r\n\tvar util = require(\"../../utils/util.js\");\r\n\r\n\tvar wxApi = require(\"../../utils/wxApi.js\");\r\n\tvar wxRequest = require(\"../../utils/wxRequest.js\");\r\n\timport config from \"../../utils/config\";\r\n\tconst Adapter = require(\"../../utils/adapter.js\");\r\n\tvar pageCount = config.getPageCount;\r\n\tvar webSiteName = config.getWebsiteName;\r\n\tvar domain = config.getDomain;\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpostsList: [],\r\n\t\t\t\tStickyList: [],\r\n\t\t\t\tisLastPage: false,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tsearch: '',\r\n\t\t\t\tcategories: 0,\r\n\t\t\t\tshowerror: \"none\",\r\n\t\t\t\tshowCategoryName: \"\",\r\n\t\t\t\tcategoryName: \"\",\r\n\t\t\t\tshowallDisplay: \"block\",\r\n\t\t\t\tdisplayHeader: \"none\",\r\n\t\t\t\tdisplaySwiper: \"none\",\r\n\t\t\t\tfloatDisplay: \"none\",\r\n\t\t\t\tdisplayfirstSwiper: \"none\",\r\n\t\t\t\tlistAdsuccess: true,\r\n\t\t\t\twebSiteName: webSiteName,\r\n\t\t\t\tdomain: domain,\r\n\t\t\t\tisFirst: false,\r\n\t\t\t\t// 是否第一次打开,\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tswipe_nav: [],\r\n\t\t\t\tselected_nav: []\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tcomponents: {},\r\n\t\tprops: {},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',\r\n\t\t\t\tpath: 'pages/index/index',\r\n\t\t\t\tsuccess: function(res) { // 转发成功\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) { // 转发失败\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tvar self = this;\r\n\t\t\tself.setData({\r\n\t\t\t\tshowerror: \"none\",\r\n\t\t\t\tshowallDisplay: \"block\",\r\n\t\t\t\tdisplaySwiper: \"none\",\r\n\t\t\t\tfloatDisplay: \"none\",\r\n\t\t\t\tisLastPage: false,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tpostsShowSwiperList: []\r\n\t\t\t});\r\n\t\t\tthis.getHomeconfig();\r\n\t\t\tthis.fetchPostsData(self);\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tvar self = this;\r\n\r\n\t\t\tif (!self.isLastPage) {\r\n\t\t\t\tself.setData({\r\n\t\t\t\t\tpage: self.page + 1\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('当前页' + self.page);\r\n\t\t\t\tthis.fetchPostsData(self);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('最后一页');\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tvar self = this; // 设置页面标题：文章分类\r\n\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: webSiteName\r\n\t\t\t});\r\n\r\n\t\t\tself.fetchPostsData(self); // 判断用户是不是第一次打开，弹出添加到我的小程序提示\r\n\t\t\tself.fetchstickyPostsData(self);\r\n\t\t\tvar isFirstStorage = uni.getStorageSync('isFirst'); // console.log(isFirstStorage);\r\n\r\n\t\t\tif (!isFirstStorage) {\r\n\t\t\t\tself.setData({\r\n\t\t\t\t\tisFirst: true\r\n\t\t\t\t});\r\n\t\t\t\tuni.setStorageSync('isFirst', 'no'); // console.log(wx.getStorageSync('isFirst'));\r\n\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\tisFirst: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 5000);\r\n\t\t\t}\r\n\r\n\t\t\tthis.getHomeconfig();\r\n\t\t},\r\n\t\tonShow: function(options) {\r\n\t\t\tuni.setStorageSync('openLinkCount', 0);\r\n\t\t\tvar nowDate = new Date();\r\n\t\t\tnowDate = nowDate.getFullYear() + \"-\" + (nowDate.getMonth() + 1) + '-' + nowDate.getDate();\r\n\t\t\tnowDate = new Date(nowDate).getTime();\r\n\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tformSubmit: function(e) {\r\n\t\t\t\tvar url = '../list/list';\r\n\t\t\t\tvar key = '';\r\n\r\n\t\t\t\tif (e.currentTarget.id == \"search-input\") {\r\n\t\t\t\t\tkey = e.detail.value;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tkey = e.detail.value.input;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (key != '') {\r\n\t\t\t\t\turl = url + '?search=' + key;\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '请输入内容',\r\n\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 自定义分享朋友圈\r\n\t\t\tonShareTimeline: function() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\ttitle: '“' + webSiteName + '”小程序,基于微慕WordPress版小程序构建',\r\n\t\t\t\t\tpath: 'pages/index/index'\r\n\t\t\t\t};\r\n\t\t\t},\r\n\r\n\t\t\tgetHomeconfig() {\r\n\t\t\t\t//获取扩展设置\r\n\t\t\t\tvar self = this;\r\n\t\t\t\tvar getHomeconfig = wxRequest.getRequest(Api.get_homeconfig());\r\n\t\t\t\tgetHomeconfig.then(res => {\r\n\t\t\t\t\t// console.log(res.data);\r\n\t\t\t\t\tlet expand = res.data.expand;\r\n\t\t\t\t\tlet swipe_nav = expand.swipe_nav;\r\n\t\t\t\t\tlet selected_nav = expand.selected_nav;\r\n\t\t\t\t\tlet _d = res.data.downloadfileDomain;\r\n\t\t\t\t\tlet _b = res.data.businessDomain;\r\n\t\t\t\t\tlet zanImageurl = res.data.zanImageurl;\r\n\t\t\t\t\tlet logoImageurl = res.data.logoImageurl;\r\n\t\t\t\t\tlet downloadfileDomain = _d.length ? _d.split(',') : [];\r\n\t\t\t\t\tlet businessDomain = _b.length ? _b.split(',') : [];\r\n\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\tswipe_nav: swipe_nav,\r\n\t\t\t\t\t\tselected_nav,\r\n\t\t\t\t\t\tselected_nav\r\n\t\t\t\t\t});\r\n\t\t\t\t\tuni.setStorageSync('downloadfileDomain', downloadfileDomain);\r\n\t\t\t\t\tuni.setStorageSync('businessDomain', businessDomain);\r\n\t\t\t\t\tuni.setStorageSync('zanImageurl', zanImageurl);\r\n\t\t\t\t\tuni.setStorageSync('logoImageurl', logoImageurl);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t//获取文章列表数据\r\n\t\t\tfetchPostsData: function(data) {\r\n\t\t\t\tvar self = this;\r\n\t\t\t\tif (!data) data = {};\r\n\t\t\t\tif (!data.page) data.page = 1;\r\n\t\t\t\tif (!data.categories) data.categories = 0;\r\n\t\t\t\tif (!data.search) data.search = '';\r\n\r\n\t\t\t\tif (data.page === 1) {\r\n\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\tpostsList: []\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t;\r\n\t\t\t\tself.setData({\r\n\t\t\t\t\tisLoading: true\r\n\t\t\t\t});\r\n\t\t\t\tvar getCategoriesRequest = wxRequest.getRequest(Api.getCategoriesIds());\r\n\t\t\t\tgetCategoriesRequest.then(res => {\r\n\t\t\t\t\tif (!res.data.Ids == \"\") {\r\n\t\t\t\t\t\tdata.categories = res.data.Ids;\r\n\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\tcategories: res.data.Ids\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar getPostsRequest = wxRequest.getRequest(Api.getPosts(data));\r\n\t\t\t\t\tgetPostsRequest.then(response => {\r\n\t\t\t\t\t\tif (response.statusCode === 200) {\r\n\t\t\t\t\t\t\tif (response.data.length) {\r\n\t\t\t\t\t\t\t\tif (response.data.length < pageCount) {\r\n\t\t\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\t\t\tisLastPage: true,\r\n\t\t\t\t\t\t\t\t\t\tisLoading: false\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\t\tfloatDisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\tpostsList: self.postsList.concat(response\r\n\t\t\t\t\t\t\t\t\t\t.data.map(function(item) {\r\n\t\t\t\t\t\t\t\t\t\t\tvar strdate = item.date;\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tif (item.category_name !=\r\n\t\t\t\t\t\t\t\t\t\t\t\tnull) {\r\n\t\t\t\t\t\t\t\t\t\t\t\titem.categoryImage =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"/static/images/category.png\";\r\n\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\titem.categoryImage = \"\";\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\tif (item\r\n\t\t\t\t\t\t\t\t\t\t\t\t.post_medium_image ==\r\n\t\t\t\t\t\t\t\t\t\t\t\tnull || item\r\n\t\t\t\t\t\t\t\t\t\t\t\t.post_medium_image == ''\r\n\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\t\titem.post_medium_image =\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"/static/images/logo700.png\";\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\titem.date = util.cutstr(\r\n\t\t\t\t\t\t\t\t\t\t\t\tstrdate, 10, 1);\r\n\t\t\t\t\t\t\t\t\t\t\treturn item;\r\n\t\t\t\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tif (response.data.code ==\r\n\t\t\t\t\t\t\t\t\t\"rest_post_invalid_page_number\") {\r\n\t\t\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\t\t\tisLastPage: true,\r\n\t\t\t\t\t\t\t\t\t\tisLoading: false\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '没有更多内容',\r\n\t\t\t\t\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: response.data.message,\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).catch(function(response) {\r\n\t\t\t\t\t\tif (data.page == 1) {\r\n\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\tshowerror: \"block\",\r\n\t\t\t\t\t\t\t\tfloatDisplay: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '加载失败',\r\n\t\t\t\t\t\t\t\tcontent: '加载数据失败,请重试.',\r\n\t\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\tpage: data.page - 1\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).finally(function(response) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\tisLoading: false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//获取置顶文章列表数据\r\n\t\t\tfetchstickyPostsData: function(data) {\r\n\t\t\t\tvar self = this;\r\n\t\t\t\tif (!data) data = {};\r\n\t\t\t\tif (!data.categories) data.categories = 0;\r\n\t\t\t\tif (!data.search) data.search = '';\r\n\t\t\t\tself.setData({\r\n\t\t\t\t\tisLoading: true\r\n\t\t\t\t});\r\n\t\t\t\tvar getCategoriesRequest = wxRequest.getRequest(Api.getCategoriesIds());\r\n\t\t\t\tgetCategoriesRequest.then(res => {\r\n\t\t\t\t\tif (!res.data.Ids == \"\") {\r\n\t\t\t\t\t\tdata.categories = res.data.Ids;\r\n\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\tcategories: res.data.Ids\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar getStickyPostsRequest = wxRequest.getRequest(Api.getStickyPosts(data));\r\n\t\t\t\t\tgetStickyPostsRequest.then(response => {\r\n\t\t\t\t\t\tif (response.statusCode === 200) {\r\n\t\t\t\t\t\t\tif (response.data.length) {\r\n\t\t\t\t\t\t\t\t// console.log(response.data)\r\n\t\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\t\tfloatDisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\tStickyList: response.data\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).catch(function(response) {\r\n\r\n\t\t\t\t\t}).finally(function(response) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\tisLoading: false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//加载分页\r\n\t\t\tloadMore: function(e) {\r\n\t\t\t\tvar self = this;\r\n\r\n\t\t\t\tif (!self.isLastPage) {\r\n\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\tpage: self.page + 1\r\n\t\t\t\t\t}); //console.log('当前页' + self.data.page);\r\n\r\n\t\t\t\t\tthis.fetchPostsData(self);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '没有更多内容',\r\n\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转至查看文章详情\r\n\t\t\tredictDetail: function(e) {\r\n\t\t\t\t// console.log('查看文章');\r\n\t\t\t\tvar id = e.currentTarget.id,\r\n\t\t\t\t\turl = '../detail/detail?id=' + id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//首页图标跳转\r\n\t\t\tonNavRedirect: function(e) {\r\n\t\t\t\tvar redicttype = e.currentTarget.dataset.redicttype;\r\n\t\t\t\tvar url = e.currentTarget.dataset.url == null ? '' : e.currentTarget.dataset.url;\r\n\t\t\t\tvar appid = e.currentTarget.dataset.appid == null ? '' : e.currentTarget.dataset.appid;\r\n\t\t\t\tvar extraData = e.currentTarget.dataset.extraData == null ? '' : e.currentTarget.dataset\r\n\t\t\t\t\t.extraData;\r\n\r\n\t\t\t\tif (redicttype == 'apppage') {\r\n\t\t\t\t\t//跳转到小程序内部页面         \r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (redicttype == 'webpage') //跳转到web-view内嵌的页面\r\n\t\t\t\t{\r\n\t\t\t\t\turl = '../webpage/webpage?url=' + url;\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (redicttype == 'miniapp') //跳转到其他app\r\n\t\t\t\t{\r\n\t\t\t\t\tvar n = this;\r\n\t\t\t\t\t//#ifdef APP-PLUS  \r\n\t\t\t\t\tconsole.log(n.sweixin);\r\n\t\t\t\t\tn.sweixin ? n.sweixin.launchMiniProgram({\r\n\t\t\t\t\t\tid: 'gh_17dae6107b00'\r\n\t\t\t\t\t}) : plus.nativeUI.alert('当前环境不支持微信操作!');\r\n\r\n\t\t\t\t\t//#endif  \r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转至查看小程序列表页面或文章详情页\r\n\t\t\tredictAppDetail: function(e) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttype,\r\n\t\t\t\t\tappid,\r\n\t\t\t\t\turl,\r\n\t\t\t\t\tpath\r\n\t\t\t\t} = e.currentTarget.dataset;\r\n\r\n\t\t\t\tif (type === 'apppage') {\r\n\t\t\t\t\t// 小程序页面         \r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: path\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type === 'webpage') {\r\n\t\t\t\t\t// web-view页面\r\n\t\t\t\t\turl = '../webpage/webpage?url=' + url;\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type === 'miniapp') {\r\n\t\t\t\t\t// 其他小程序\r\n\t\t\t\t\turl = '../webpage/webpage?url=' + url;\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//返回首页\r\n\t\t\tredictHome: function(e) {\r\n\t\t\t\t//console.log('查看某类别下的文章');  \r\n\t\t\t\tvar id = e.currentTarget.dataset.id,\r\n\t\t\t\t\turl = '/pages/index/index';\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t/*\r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n */\r\n\t.index-top {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.indexbg {\r\n\t\tposition: absolute;\r\n\t\ttop: -30rpx;\r\n\t\twidth: 750rpx;\r\n\t\theight: 500rpx;\r\n\t\tz-index: -1;\r\n\r\n\t}\r\n\r\n\t/* 轮播图 */\r\n\r\n\r\n\t/* 置顶文章 */\r\n\r\n\r\n\t.sticky-list {\r\n\t\theight: 130rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t.sticky-list-item-title {\r\n\t\tdisplay: block;\r\n\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\r\n\t/* 图标导航 */\r\n\r\n\r\n\t.ui-item {\r\n\t\tdisplay: block;\r\n\t\tpadding: 24rpx 0 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.ui-item image {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tmargin: auto;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.ui-item text {\r\n\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 300;\r\n\t}\r\n\r\n\t/* 搜索 */\r\n\t.ui-search-bar {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 100rpx;\r\n\t\tmargin-bottom: 15rpx;\r\n\t}\r\n\r\n\t.ui-search-bar .ui-search-action+.ui-search-form {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.ui-search-bar .ui-search-form {\r\n\t\tposition: relative;\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #333;\r\n\t\theight: 68rpx;\r\n\t\tfont-size: 24rpx;\r\n\r\n\r\n\t}\r\n\r\n\t.ui-search-icon,\r\n\t.ui-search-close {\r\n\t\theight: 68rpx;\r\n\t\tline-height: 68rpx;\r\n\t}\r\n\r\n\t.ui-search-icon {\r\n\t\tmargin-left: 25rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.ui-search-input {\r\n\t\tposition: relative;\r\n\t\tflex: 1;\r\n\t\theight: 68rpx;\r\n\r\n\r\n\t}\r\n\r\n\t.ui-search-text-left {\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.ui-search-text-center {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.ui-search-text-right {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.input-placeholder {\r\n\t\tcolor: inherit;\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t.ui-search-close {\r\n\t\tmargin-left: 20rpx;\r\n\t\tmargin-right: 25rpx;\r\n\t}\r\n\r\n\t.ui-search-bar .ui-search-form+.ui-search-action {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\r\n\r\n\r\n\t/*模块标题样式 */\r\n\t.common-subtitle {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\theight: 130rpx;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.common-subtitle-left {\r\n\t\tfont-size: 34rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.common-subtitle-right {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #959595;\r\n\t\tmargin-right: 6rpx;\r\n\t}\r\n\r\n\t/* 精选栏目菜单导航 */\r\n\r\n\r\n\t.selected-nav-list {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 24rpx 40rpx;\r\n\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.selected-nav-item {\r\n\t\tmargin-right: 16rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.selected-nav-item image {\r\n\t\twidth: 156rpx;\r\n\t\theight: 156rpx;\r\n\t\tbackground: #f5f7f7;\r\n\t}\r\n\r\n\t.selected-nav-item text {\r\n\t\tline-height: 26rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.list-item-ad {\r\n\t\tmargin: 24rpx;\r\n\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: var(--radius);\r\n\t}\r\n\r\n\t/* 引导添加到我的小程序 */\r\n\t.addMyMiniapp {\r\n\t\tcolor: #333;\r\n\t\tline-height: 1.2;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 24rpx 24rpx;\r\n\t\tposition: fixed;\r\n\t\ttop: 20rpx;\r\n\t\tright: 24rpx;\r\n\t\tz-index: 999;\r\n\t\tbox-shadow: 0 16rpx 30rpx -12rpx rgba(88, 88, 88, 0.2);\r\n\t}\r\n\r\n\t.addMyMiniapp>view {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.addMyMiniapp>text {\r\n\t\tfont-size: 20rpx;\r\n\t\tfont-weight: 200;\r\n\t}\r\n\r\n\t.addMyMiniapp::before {\r\n\t\tcontent: '';\r\n\t\twidth: 20rpx;\r\n\t\theight: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\ttransform: rotate(45deg);\r\n\t\tposition: absolute;\r\n\t\tright: 118rpx;\r\n\t\ttop: -10rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190070\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}