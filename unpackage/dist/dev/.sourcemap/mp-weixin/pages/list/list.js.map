{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?d4b5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?ea27", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?4105", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?5609", "uni-app:///pages/list/list.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?d8f3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/list/list.vue?3d18"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "postsList", "pagesList", "categoriesList", "postsShowSwiperList", "isLastPage", "page", "search", "categories", "categoriesName", "categoriesImage", "showerror", "isCategoryPage", "isSearchPage", "showallDisplay", "displaySwiper", "floatDisplay", "search<PERSON>ey", "webSiteName", "domain", "listAdsuccess", "isLoading", "components", "props", "onShareAppMessage", "path", "success", "fail", "onReachBottom", "self", "console", "onLoad", "Adapter", "uni", "methods", "formSubmit", "url", "onShareTimeline", "query", "categoryID", "imageUrl", "reload", "loadMore", "mask", "duration", "fetchPostsData", "getPostsRequest", "item", "content", "showCancel", "redictDetail", "fetchCategoriesData", "getCategoryRequest", "catImage", "adbin<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAg2B,CAAgB,g0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoJp3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,mBAAO,CAAC,4BAAoB;AACtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAC1C;AACA,gBAAgB,mBAAO,CAAC,kCAA0B;AAElD;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EACAC;EACAC;IACA;IACA;IAEA;MACAxB;MACAyB;IACA;MACAzB;MACAyB;IACA;IAEA;MACAzB;MACAyB;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACAC;IACA;IAEA;MACAC;QACAvB;MACA;MACAwB;MACA;IACA;MACAA;IACA;EACA;EACAC;IACA;;IAEAC;IAEA;MACAH;QACArB;QACAI;MACA;MACAiB;IACA;IAEA;MACAI;QACAjC;MACA;MACA6B;QACAtB;QACAM;QACAI;MACA;MACA;IACA;EACA;EACAiB;IACAC;MACA;MAEA;QACAC;MACA;MAEAH;QACAG;MACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACArC;QACAsC;UACAC;QACA;MACA;QACAvC;QACAsC;UACA/B;QACA;MACA;MAEA;QACAP;QACAyB;QACAa;QACAE;MACA;IACA;IACAC;MACA;MAEA;QACAZ;UACAjB;UACAE;UACAH;QACA;QACAkB;MACA;MAEA;QACAA;UACAhB;UACAC;UACAH;UACAM;QACA;MACA;MAEAY;IACA;IACA;IACAa;MACA;MAEA;QACAb;UACAvB;QACA;QACAwB;QACA;MACA;QACAG;UACAjC;UACA2C;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MAEA;QACAhB;UACA5B;QACA;MACA;MAEA;MACA4B;QACAR;MACA;MACA;MACAyB;QACA;UACA;YACAjB;cACAxB;cACAgB;YACA;UACA;UAEA;UACAQ;YACAb;YACAF;YACAb;cACA;cAEA;gBACA8C;cACA;gBACAA;cACA;cAEA;gBACAA;cACA;cAEAA;cACA;YACA;UACA;UACA;UACA;QACA;UACA;YACAlB;cACAxB;cACAgB;YACA;UACA;YACAY;cACAjC;cACA4C;YACA;UACA;QACA;MACA;QACA;UACAf;YACAlB;YACAK;UACA;QACA;UACAiB;YACAjC;YACAgD;YACAC;UACA;UACApB;YACAvB;UACA;QACA;MACA;QACA2B;QACAJ;UACAR;QACA;MACA;IACA;IACA;IACA6B;MACA;MACA;QACAd;MACAH;QACAG;MACA;IACA;IACA;IACAe;MACA;MACAtB;QACA1B;MACA;MACA;MACAiD;QACA;QAEA;UACAC;QACA;UACAA;QACA;QAEAxB;UACA1B;UACAO;UACAD;QACA;QACAwB;UACAjC;UACA0B;UAAA;QAEA;QACAG;MACA;IACA;IACAyB;MACA;MACAxB;MACAA;MAEA;QACAD;UACAT;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxcA;AAAA;AAAA;AAAA;AAAytC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACA7uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/list/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/list/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=7d5e07c6&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/list/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=7d5e07c6&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<block>\r\n\t\t<!--\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n *\r\n */-->\r\n\r\n\r\n\r\n\t\t<ui-sys >\r\n\t\t\t<view class=\"topic-common-list\" :style=\"'display:'+(isCategoryPage)\">\r\n\t\t\t\t<view class=\"topic-list-item\">\r\n\t\t\t\t\t<image :src=\"categoriesImage\" class=\"cover\" mode=\"aspectFill\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view class=\"topic-content-brief\">\r\n\t\t\t\t\t\t<text>{{categoriesList.description}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<view class=\"topic-common-list\" :style=\"'display:'+(isSearchPage)\">\r\n\t\t\t\t<view class=\"topic-list-item\">\r\n\t\t\t\t\t<image src=\"/static/images/website-search.png\" class=\"cover\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"topic-content-brief\">\r\n\t\t\t\t\t\t<view class=\"topic-content-title\">\r\n\t\t\t\t\t\t\t<text>搜索关键字：</text>\r\n\t\t\t\t\t\t\t<text class=\"searchKey\">{{searchKey}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"search-tips\">* 本搜索是全文搜索</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"container\">\r\n\t\t\t\t<view class=\"showerror\" :style=\"'display:'+(showerror)\">\r\n\t\t\t\t\t<text class=\"cicon-wifi-off text-xsl\"></text>\r\n\t\t\t\t\t<view class=\"errortext\">\r\n\t\t\t\t\t\t暂时无法访问网络\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<button class=\"more-button\" @tap=\"reload\" size=\"mini\">重新加载</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view :style=\"'display:'+(showallDisplay)\">\r\n\t\t\t\t\t<!-- parse <template is=\"tempCommonList\" :data=\"postsList,listAdsuccess\"/> -->\r\n\t\t\t\t\r\n\r\n\t\t\t\t\t\t<view class=\"post-list\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in (postsList)\" :key=\"index\">\r\n\r\n\t<!-- #ifdef MP -->\r\n\r\n\t\t\t\t\t\t\t\t<view class=\"list-item-ad  \" :index=\"index\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"index%item.listAdEvery ==0  && index!=0 && item.listAd =='1' && item.listAdId !=''  && listAdsuccess\">\r\n\t\t\t\t\t\t\t\t\t<block v-if=\"(index/item.listAdEvery)%2==0\">\r\n\t\t\t\t\t\t\t\t\t\t<ad :unit-id=\"item.videoAdId\" ad-type=\"video\" ad-theme=\"white\"\r\n\t\t\t\t\t\t\t\t\t\t\t@error=\"adbinderror\"></ad>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t\t\t<ad :unit-id=\"item.listAdId\" ad-intervals=\"30\" @error=\"adbinderror\"></ad>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n<!-- #endif -->\r\n\r\n\r\n\r\n\r\n\t\t\t\t\t\t\t\t<view class=\"post-item ui-BG ui-BG-card\" :index=\"index\" :id=\"item.id\"\r\n\t\t\t\t\t\t\t\t\t@tap=\"redictDetail\">\r\n\r\n\t\t\t\t\t\t\t\t\t<view class=\"post-desc\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"post-title text-lg ui-TC\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{item.title.rendered}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"post-data ui-TC-3 text-xs\">\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t<text>{{item.date}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-popover-o  \"> {{item.total_comments}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-favorite-o \"> {{item.like_count}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-eye-o \"> {{item.pageviews}}</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.post_medium_image\" mode=\"aspectFill\" class=\"post-img\"\r\n\t\t\t\t\t\t\t\t\t\tv-if=\"item.post_medium_image\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t<view :style=\"'display:'+(isLoading?'block':'none')\">\r\n\t\t\t\t\t\t<!-- parse <template is=\"tempLoading\"/> -->\r\n\t\t\t\t\t\t<block name=\"tempLoading\">\r\n\t\t\t\t\t\t\t<view class=\"sk-three-bounce\">\r\n\t\t\t\t\t\t\t\t<view class=\"sk-child sk-bounce1 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"sk-child sk-bounce2 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t\t\t\t<view class=\"sk-child sk-bounce3 ui-BG-Main-2\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"loadingmore\" :style=\"'display:'+(floatDisplay)\">\r\n\t\t\t\t\t\t<view class=\"no-more\" v-if=\"(isLastPage)\">- 无更多文章 -</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"copyright\" :style=\"'display:'+(floatDisplay)\">\r\n\t\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</ui-sys>\r\n\t</block>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * \n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n */\nvar Api = require(\"../../utils/api.js\");\r\nvar util = require(\"../../utils/util.js\");\r\n\r\nvar wxApi = require(\"../../utils/wxApi.js\");\r\nconst Adapter = require(\"../../utils/adapter.js\");\r\nvar wxRequest = require(\"../../utils/wxRequest.js\");\r\nimport config from \"../../utils/config\";\r\nvar pageCount = config.getPageCount;\r\nvar webSiteName = config.getWebsiteName;\r\nvar domain = config.getDomain;\r\n\r\nexport default {\n  data() {\n    return {\n      title: '文章列表',\n      postsList: {},\n      pagesList: {},\n      categoriesList: {},\n      postsShowSwiperList: {},\n      isLastPage: false,\n      page: 1,\n      search: '',\n      categories: 0,\n      categoriesName: '',\n      categoriesImage: \"\",\n      showerror: \"none\",\n      isCategoryPage: \"none\",\n      isSearchPage: \"none\",\n      showallDisplay: \"block\",\n      displaySwiper: \"block\",\n      floatDisplay: \"none\",\n      searchKey: \"\",\n      webSiteName: webSiteName,\n      domain: domain,\n      listAdsuccess: true,\n      isLoading: false\n    };\n  },\n\n  components: {},\n  props: {},\n  onShareAppMessage: function () {\n    var title = \"分享“\" + webSiteName + \"”\";\n    var path = \"\";\n\n    if (this.categories && this.categories != 0) {\n      title += \"的专题：\" + this.categoriesList.name;\n      path = 'pages/list/list?categoryID=' + this.categoriesList.id;\n    } else {\n      title += \"的搜索内容：\" + this.searchKey;\n      path = 'pages/list/list?search=' + this.searchKey;\n    }\n\n    return {\n      title: title,\n      path: path,\n      success: function (res) {// 转发成功\n      },\n      fail: function (res) {// 转发失败\n      }\n    };\n  },\n  onReachBottom: function () {\n    var self = this;\n\n    if (!self.isLastPage) {\n      self.setData({\n        page: self.page + 1\n      });\n      console.log('当前页' + self.page);\n      this.fetchPostsData(self);\n    } else {\n      console.log('最后一页');\n    }\n  },\n  onLoad: function (options) {\n    var self = this; // 设置插屏广告\n\n    Adapter.setInterstitialAd(\"enable_list_interstitial_ad\");\n\n    if (options.categoryID && options.categoryID != 0) {\n      self.setData({\n        categories: options.categoryID,\n        isCategoryPage: \"block\"\n      });\n      self.fetchCategoriesData(options.categoryID);\n    }\n\n    if (options.search && options.search != '') {\n      uni.setNavigationBarTitle({\n        title: \"搜索\"\n      });\n      self.setData({\n        search: options.search,\n        isSearchPage: \"block\",\n        searchKey: options.search\n      });\n      this.fetchPostsData(self);\n    }\n  },\n  methods: {\n    formSubmit: function (e) {\n      var url = '../list/list';\n\n      if (e.detail.value.input != '') {\n        url = url + '?search=' + e.detail.value.input;\n      }\n\n      uni.navigateTo({\n        url: url\n      });\n    },\n    onShareTimeline: function () {\n      var path = \"\";\n      var query = {};\n      var title = \"\";\n\n      if (this.categories && this.categories != 0) {\n        title += this.categoriesList.name + \"-\" + this.categoriesList.description;\n        query = {\n          categoryID: this.categoriesList.id\n        };\n      } else {\n        title += webSiteName + \"的搜索内容：\" + this.searchKey;\n        query = {\n          search: this.searchKey\n        };\n      }\n\n      return {\n        title: title,\n        path: 'pages/list/list',\n        query: query,\n        imageUrl: this.categoriesImage\n      };\n    },\n    reload: function (e) {\n      var self = this;\n\n      if (self.categories && self.categories != 0) {\n        self.setData({\n          isCategoryPage: \"block\",\n          showallDisplay: \"none\",\n          showerror: \"none\"\n        });\n        self.fetchCategoriesData(self.categories);\n      }\n\n      if (self.search && self.search != '') {\n        self.setData({\n          isSearchPage: \"block\",\n          showallDisplay: \"none\",\n          showerror: \"none\",\n          searchKey: self.search\n        });\n      }\n\n      self.fetchPostsData(self);\n    },\n    //加载分页\n    loadMore: function (e) {\n      var self = this;\n\n      if (!self.isLastPage) {\n        self.setData({\n          page: self.page + 1\n        });\n        console.log('当前页' + self.page);\n        this.fetchPostsData(self);\n      } else {\n        uni.showToast({\n          title: '没有更多内容',\n          mask: false,\n          duration: 1000\n        });\n      }\n    },\n    //获取文章列表数据\n    fetchPostsData: function (data) {\n      var self = this;\n      if (!data) data = {};\n      if (!data.page) data.page = 1;\n      if (!data.categories) data.categories = 0;\n      if (!data.search) data.search = '';\n\n      if (data.page === 1) {\n        self.setData({\n          postsList: []\n        });\n      }\n\n      ;\n      self.setData({\n        isLoading: true\n      });\n      var getPostsRequest = wxRequest.getRequest(Api.getPosts(data));\n      getPostsRequest.then(response => {\n        if (response.statusCode === 200) {\n          if (response.data.length < pageCount) {\n            self.setData({\n              isLastPage: true,\n              isLoading: false\n            });\n          }\n\n          ;\n          self.setData({\n            floatDisplay: \"block\",\n            showallDisplay: \"block\",\n            postsList: self.postsList.concat(response.data.map(function (item) {\n              var strdate = item.date;\n\n              if (item.category_name != null) {\n                item.categoryImage = \"/static/images/topic.png\";\n              } else {\n                item.categoryImage = \"\";\n              }\n\n              if (item.post_medium_image == null || item.post_medium_image == '') {\n                item.post_medium_image = \"/static/images/logo700.png\";\n              }\n\n              item.date = util.cutstr(strdate, 10, 1);\n              return item;\n            }))\n          }); // setTimeout(function () {\n          //     wx.hideLoading();\n          // }, 1500);\n        } else {\n          if (response.data.code == \"rest_post_invalid_page_number\") {\n            self.setData({\n              isLastPage: true,\n              isLoading: false\n            });\n          } else {\n            uni.showToast({\n              title: response.data.message,\n              duration: 1500\n            });\n          }\n        }\n      }).catch(function () {\n        if (data.page == 1) {\n          self.setData({\n            showerror: \"block\",\n            floatDisplay: \"none\"\n          });\n        } else {\n          uni.showModal({\n            title: '加载失败',\n            content: '加载数据失败,请重试.',\n            showCancel: false\n          });\n          self.setData({\n            page: data.page - 1\n          });\n        }\n      }).finally(function () {\n        uni.hideLoading();\n        self.setData({\n          isLoading: false\n        });\n      });\n    },\n    // 跳转至查看文章详情\n    redictDetail: function (e) {\n      // console.log('查看文章');\n      var id = e.currentTarget.id,\n          url = '../detail/detail?id=' + id;\n      uni.navigateTo({\n        url: url\n      });\n    },\n    //获取分类列表\n    fetchCategoriesData: function (id) {\n      var self = this;\n      self.setData({\n        categoriesList: []\n      });\n      var getCategoryRequest = wxRequest.getRequest(Api.getCategoryByID(id));\n      getCategoryRequest.then(response => {\n        var catImage = \"\";\n\n        if (typeof response.data.category_thumbnail_image == \"undefined\" || response.data.category_thumbnail_image == \"\") {\n          catImage = \"/static/images/website.png\";\n        } else {\n          catImage = response.data.category_thumbnail_image;\n        }\n\n        self.setData({\n          categoriesList: response.data,\n          categoriesImage: catImage,\n          categoriesName: response.data.name\n        });\n        uni.setNavigationBarTitle({\n          title: response.data.name,\n          success: function (res) {// success\n          }\n        });\n        self.fetchPostsData(self);\n      });\n    },\n    adbinderror: function (e) {\n      var self = this;\n      console.log(e.detail.errCode);\n      console.log(e.detail.errMsg);\n\n      if (e.detail.errCode) {\n        self.setData({\n          listAdsuccess: false\n        });\n      }\n    }\n  }\n};\r\n</script>\r\n<style>\r\n\t/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n */\r\n\r\n\t.topic-list-item {\r\n\t\theight: 240rpx;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\r\n\t}\r\n\r\n\t.cover {\r\n\t\twidth: 100%;\r\n\t\theight: 240rpx;\r\n\t\tposition: absolute;\r\n\t\tfilter: saturate(180%) blur(35px);\r\n\t}\r\n\r\n\t.topic-content-brief {\r\n\t\twidth: 80%;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\tz-index: 2;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.topic-content-brief text {\r\n\t\tfont-size: 16px;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/* 搜索列表 */\r\n\r\n\t.topic-content-title text {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: 500;\r\n\t\tline-height: 1.6;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.searchKey {\r\n\t\tcolor: #121b23;\r\n\t}\r\n\r\n\t.search-tips {\r\n\t\tfont-size: 10px;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190093\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}