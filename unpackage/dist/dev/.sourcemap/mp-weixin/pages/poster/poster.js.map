{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?2493", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?40e5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?122c", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?d03c", "uni-app:///pages/poster/poster.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?8192", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/poster/poster.vue?541e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "posterImageUrl", "dialog", "title", "content", "hidden", "webSiteName", "domain", "onLoad", "uni", "success", "that", "methods", "savePosterImage", "url", "filePath", "console", "showCancel", "delta", "fail", "posterImageClick", "urls", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAk2B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0Dt3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,mBAAO,CAAC,4BAAoB;AAEtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAE1C,gBAAgB,mBAAO,CAAC,kCAA0B;AAElD,WAAW,mBAAO,CAAC,6BAAqB;AAGxC;AAAA,eACA;EAAAC;IACA;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;IACA;EACA;EAAAC;IACA;IACAC;MACAN;MACAO;MAAA;IAEA;IACAC;MACAV;IACA;EACA;EACAW;IACAC;MACA;MACAJ;QACAK;QACAJ;UACAD;YACAM;YAEAL;cACAM;cACAP;gBACAN;gBACAC;gBACAa;gBACAP;kBACA;oBACAD;sBACAS;oBACA;kBACA;gBACA;cACA;YACA;UAEA;QACA;QACAC;UACAH;QACA;MACA;IACA;IAEAI;MACA;MACAX;QACAY;MACA;IACA;IAEAC;MACAN;IACA;EACA;AAAA;AAAA,2B;;;;;;;;;;;;;AC/HA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/poster/poster.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/poster/poster.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./poster.vue?vue&type=template&id=132a9d26&\"\nvar renderjs\nimport script from \"./poster.vue?vue&type=script&lang=js&\"\nexport * from \"./poster.vue?vue&type=script&lang=js&\"\nimport style0 from \"./poster.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/poster/poster.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=template&id=132a9d26&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"", "<template>\r\n<!--\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\n *\n */-->\n\n\n\n\n<view class=\"container\">\n    <view class=\"wrapper\">\n        <image @tap=\"posterImageClick\" :data-src=\"posterImageUrl\" mode=\"widthFix\" class=\"posterimage\" :src=\"posterImageUrl\"></image>\n    </view>\n     <view style=\"text-align:center\">\n            <button class=\"gotowebpage-button\" formType=\"submit\" size=\"mini\" @tap=\"savePosterImage\">保存图片</button>\n            <modal :title=\"dialog.title\" v-if=\"dialog.hidden\" no-cancel @confirm=\"confirm\">{{dialog.content}}</modal>\n        </view>\n        <view style=\"color: #888;font-size: 9pt;text-align: center;margin-top:10rpx\">保存至相册后进行分享</view>\n\n<view class=\"copyright\">\n    <!-- parse <template is=\"tempCopyright\"/> --><block name=\"tempCopyright\">\r\n<view style=\"margin-top: 24rpx;\">  © {{webSiteName}} {{domain}} </view>\r\n</block>\n</view>\n\n</view>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\n * \n */\nvar Api = require('../../utils/api.js');\n\nvar util = require('../../utils/util.js');\n\nvar wxApi = require('../../utils/wxApi.js');\n\nvar wxRequest = require('../../utils/wxRequest.js');\n\nvar auth = require('../../utils/auth.js');\n\nimport config from '../../utils/config.js';\nvar app = getApp();\nexport default {data() {\n            return {\n              posterImageUrl: \"\",\n\n              dialog: {\n                title: '',\n                content: '',\n                hidden: true\n              },\n\n              webSiteName: \"\",\n              domain: \"\"\n            };\n        },onLoad: function (options) {\n  var that = this;\n  uni.setNavigationBarTitle({\n    title: '海报',\n    success: function (res) {// success\n    }\n  });\n  that.setData({\n    posterImageUrl: options.posterImageUrl\n  });\n}, \nmethods: {\n  savePosterImage: function () {\n    var that = this;\n    uni.downloadFile({\n      url: that.data.posterImageUrl,\n      success: function (res) {\n        uni.saveImageToPhotosAlbum({\n          filePath: res.tempFilePath,\n\n          success(result) {\n            console.log(result);\n            uni.showModal({\n              title: '提示',\n              content: '二维码海报已存入手机相册，赶快分享吧',\n              showCancel: false,\n              success: function (res) {\n                if (res.confirm) {\n                  uni.navigateBack({\n                    delta: 1\n                  });\n                }\n              }\n            });\n          }\n\n        });\n      },\n      fail: function (res) {\n        console.log(res);\n      }\n    });\n  },\n\n  posterImageClick: function (e) {\n    var src = e.currentTarget.dataset.src;\n    uni.previewImage({\n      urls: [src]\n    });\n  },\n\n  confirm() {\n    console.log(\"占位：函数 confirm 未声明\");\n  }\n}};\r\n</script>\r\n<style >\r\n\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n * \r\n */\r\n\r\n\r\n .wrapper{\r\n  padding: 5rpx;\r\n  border: 2rpx solid #eee;\r\n  margin-top: 10rpx; \r\n -webkit-box-shadow: 3px 3px 3px;  \r\n  -moz-box-shadow: 3px 3px 3px;  \r\n  box-shadow: 3px 3px 3px #eee;  \r\n}\r\n.posterimage{\r\n    width: 100%;\r\n    height:100%\r\n}\r\n\r\n.gotowebpage-button {\r\n  font-size: 0.785714286rem;\r\n  font-weight: normal;\r\n  color:  #fff;\r\n  background-color: #118fff;\r\n  background-repeat: repeat-x;\r\n  line-height: 80rpx;\r\n  margin-top: 40rpx;\r\n  width: 320rpx;\r\n  height: 60rpx;\r\n  border-radius: 60rpx;\r\n}\r\n\r\n.gotowebpage-button::after{\r\n   border: none; \r\n}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190089\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}