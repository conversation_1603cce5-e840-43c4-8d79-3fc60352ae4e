{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?2a43", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?bd85", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?6dfc", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?2f09", "uni-app:///pages/page/page.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?0102", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/page/page.vue?0efd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "pageData", "rendered", "content", "pagesList", "hidden", "webSiteName", "domain", "display", "onLoad", "uni", "withShareTicket", "menus", "success", "onShareTimeline", "imageUrl", "query", "id", "methods", "fetchData", "that", "url", "mdrender", "console", "setTimeout", "fetchPagesData", "wxParseTagATap"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAg2B,CAAgB,g0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4Cp3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,UAAU,mBAAO,CAAC,4BAAoB;AAEtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC;AACA;AAAA,eACA;EAAAC;IACA;MACAC;MAEAC;QACAD;UACAE;QACA;QAEAC;UACAD;QACA;MACA;MAEAE;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAAAC;IACA;IACAC;MACAC;MACAC;MACAC;MAAA;IAEA;EACA;EACAC;IACA;IAEA;MACAC;IACA;IAEA;MACAf;MACAgB;QACAC;MACA;MACAF;IACA;EACA;EACAG;IACAC;MACA;MACAC;QACAf;MACA;MACAK;QACAW;UACAC;QACA;QACAT;UACAU;UACAH;YACAnB;UACA;UACAuB;YACAJ;cACAf;YACA;UACA;QACA;MACA;IACA;IAEAoB;MACA;MACAf;QACAW;QACAR;UACAO;YACAhB;UACA;UACAoB;YACAJ;cACAf;YACA;UACA;QACA;MACA;IACA;IAEAqB;MACAH;IACA;EACA;AAAA;AAAA,2B;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAAytC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACA7uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/page/page.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/page/page.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./page.vue?vue&type=template&id=dc477334&\"\nvar renderjs\nimport script from \"./page.vue?vue&type=script&lang=js&\"\nexport * from \"./page.vue?vue&type=script&lang=js&\"\nimport style0 from \"./page.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/page/page.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./page.vue?vue&type=template&id=dc477334&\"", "var components\ntry {\n  components = {\n    mpHtml: function () {\n      return import(\n        /* webpackChunkName: \"components/mp-html/mp-html\" */ \"@/components/mp-html/mp-html\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./page.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./page.vue?vue&type=script&lang=js&\"", "<template>\r\n<!--\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n *\r\n */-->\r\n\r\n \r\n<view class=\"container\">\r\n    <view class=\"content-article \">\r\n                    <view class=\"entry-title\">\r\n                   {{pageData.title.rendered}}\r\n                    </view>\r\n                    <view class=\"entry-summary\">\r\n                   <mp-html :content=\"pageData.content.rendered\" @linktap=\"wxParseTagATap\" :copy-link=\"false\" :selectable=\"true\" :lazy-load=\"true\" :markdown=\"true\"/>\r\n                    </view>\r\n                </view>\r\n\r\n            <view class=\"ditail-copyright\" :style=\"'display:'+(display)\">\r\n \r\n  </view>\r\n</view>\r\n\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\n * \n */\nimport config from '../../utils/config.js';\n\nvar Api = require('../../utils/api.js');\n\nvar util = require('../../utils/util.js');\n\nvar webSiteName = config.getWebsiteName;\nvar domain = config.getDomain;\nexport default {data() {\n            return {\n              title: '页面内容',\n\n              pageData: {\n                title: {\n                  rendered: \"\"\n                },\n\n                content: {\n                  rendered: \"\"\n                }\n              },\n\n              pagesList: {},\n              hidden: false,\n              webSiteName: webSiteName,\n              domain: domain,\n              display: \"\"\n            };\n        },onLoad: function (options) {\n  this.fetchData(options.id);\n  uni.showShareMenu({\n    withShareTicket: true,\n    menus: ['shareAppMessage', 'shareTimeline'],\n    success: function (e) {//console.log(e);\n    }\n  });\n}, \nonShareTimeline: function () {\n  let imageUrl = this.data.pageData.post_full_image;\n\n  if (imageUrl == '') {\n    imageUrl = this.data.pageData.postImageUrl;\n  }\n\n  return {\n    title: this.data.pageData.title.rendered,\n    query: {\n      id: this.data.pageData.id\n    },\n    imageUrl\n  };\n}, \nmethods: {\n  fetchData: function (id) {\n    var that = this;\n    that.setData({\n      hidden: false\n    });\n    uni.request({\n      url: Api.getPageByID(id, {\n        mdrender: false\n      }),\n      success: function (response) {\n        console.log(response);\n        that.setData({\n          pageData: response.data\n        });\n        setTimeout(function () {\n          that.setData({\n            hidden: true\n          });\n        }, 300);\n      }\n    });\n  },\n\n  fetchPagesData: function () {\n    var that = this;\n    uni.request({\n      url: Api.getPages(),\n      success: function (response) {\n        that.setData({\n          pagesList: response.data\n        });\n        setTimeout(function () {\n          that.setData({\n            hidden: true\n          });\n        }, 300);\n      }\n    });\n  },\n\n  wxParseTagATap() {\n    console.log(\"占位：函数 wxParseTagATap 未声明\");\n  }\n}};\r\n</script>\r\n<style >\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n */\r\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./page.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./page.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190113\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}