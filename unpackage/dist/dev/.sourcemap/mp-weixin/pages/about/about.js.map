{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?1ec3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?87b3", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?984d", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?4d4f", "uni-app:///pages/about/about.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?d4a8", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/about/about.vue?c949"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "pageData", "post_content", "pagesList", "display", "praiseList", "dialog", "content", "hidden", "system", "webSiteName", "domain", "downloadFileDomain", "businessDomain", "onLoad", "uni", "withShareTicket", "menus", "onPullDownRefresh", "that", "onShareAppMessage", "path", "success", "fail", "onShareTimeline", "methods", "copyLink", "duration", "wxParseTagATap", "url", "href", "appId", "n", "getPostSlugRequest", "openLinkCount", "console", "openLinkDoc", "self", "fileType", "filePath", "fieldType", "getPageRequest"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,oOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAi2B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiEr3B;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AAGA;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;QACAN;QACAO;QACAC;MACA;MAEAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IAEAC;MACAC;MACAC;IACA;EAEA;EACAC;IACA;IACAC;MACAf;MACAH;IACA;IACA;;IAEAc;EACA;EACAK;IACA;MACApB;MACAqB;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACA;EACAC;IACA;MACAxB;IACA;EACA;EACAyB;IAGAC;MACA;MACAX;QACAhB;QACAuB;UACAP;YACAO;cACAP;gBACAf;gBACA2B;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;;MAEA;MAEA;QACA;QACA;MACA;MAEA;QACA;UACA;UACAb;YACAc;UACA;QACA;UACA;YAAA;YACA;cACAC;cACAf;gBACAc;cACA;YACA;YACA;cAAA;cACA;gBACAd;kBACAgB;kBACAV;gBACA;cACA;UACA;QACA;QAEA;MACA;MAEA;MACA;;MAEA;QACA;QAEA;UACA;YACAW;YACA;UACA;QACA;QAEA;UACA;UAEA;YACAH;YACAd;cACAc;YACA;UACA;YACAV;UACA;QACA;UACAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEA;QACA;QAEA;UACA;UAEA;YACAU;YACAd;cACAc;YACA;UACA;YACAV;UACA;UAEA;QACA;QAEA;UACAJ;YACAc;UACA;QACA;UACA;UACAI;YACA;cACA;gBACA;gBACA;gBAEA;kBACAlB;oBACAc;kBACA;gBACA;kBACAd;oBACAc;kBACA;kBACAK;kBACAnB;gBACA;cACA;gBACA;gBAEA;kBACAc;kBACAd;oBACAc;kBACA;gBACA;kBACAV;gBACA;cACA;YACA;UACA;YACAgB;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;;MAEA;MACA;MAEA;QACA;UACAJ;UACA;QACA;MACA;MAEA;QACAK;QACA;MACA;MAEA;MACA;MAEA;QACAR;QACAS;MACA;QACAT;QACAS;MACA;MAEAvB;QACAc;QACAP;UACA;UACAP;YACAwB;YACAC;UACA;QACA;QACAjB;UACAY;QACA;MACA;IACA;EAAA,uDAEA;IACApB;MACAhB;MACAuB;QACAP;UACAO;YACAP;cACAf;cACA2B;YACA;UACA;QACA;MACA;IACA;EACA,yDAIA;IACA;IACA;IACAc;MACAN;MACApB;QACAf;QACAsB;QAAA;MAEA;MACAH;QACAlB;MACA;MACAkB;QACAf;MACA;IACA;EACA,0EAEA;IACA+B;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5XA;AAAA;AAAA;AAAA;AAA0tC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACA9uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/about/about.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/about/about.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./about.vue?vue&type=template&id=92c79dbc&\"\nvar renderjs\nimport script from \"./about.vue?vue&type=script&lang=js&\"\nexport * from \"./about.vue?vue&type=script&lang=js&\"\nimport style0 from \"./about.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/about/about.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=template&id=92c79dbc&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n    mpHtml: function () {\n      return import(\n        /* webpackChunkName: \"components/mp-html/mp-html\" */ \"@/components/mp-html/mp-html\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=script&lang=js&\"", "<template>\r\n\r\n\t<!--\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n *\r\n */-->\r\n\r\n\r\n\r\n\t<ui-sys >\r\n\t\t<view class=\"ui-BG-card ui-BG\" :style=\"'display:'+(display)\">\r\n\r\n\r\n\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t<view class=\"excerpt\">\r\n\t\t\t\t\t<!-- <rich-text nodes=\"{{pageData.content.rendered}}\"></rich-text> -->\r\n\t\t\t\t\t<mp-html :content=\"pageData.post_content\" @linktap=\"wxParseTagATap\" :copy-link=\"false\"\r\n\t\t\t\t\t\t:selectable=\"true\" :lazy-load=\"true\" :markdown=\"true\" />\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"copyright\">\r\n\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</ui-sys>\r\n\r\n</template>\r\n\r\n<script>\r\n\t/*\r\n\t * \r\n\t * 微慕小程序开源版\r\n\t * author: jianbo\r\n\t * organization: 微慕  www.minapper.com\r\n\t * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n\t * 技术支持微信号：iamxjb\r\n\t * 开源协议：MIT\r\n\t * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n\t * \r\n\t */\r\n\tvar Api = require('../../utils/api.js');\r\n\r\n\tvar util = require('../../utils/util.js');\r\n\r\n\tvar wxApi = require('../../utils/wxApi.js');\r\n\r\n\tvar wxRequest = require('../../utils/wxRequest.js');\r\n\r\n\tvar Auth = require('../../utils/auth.js');\r\n\r\n\timport config from '../../utils/config.js';\r\n\tvar app = getApp();\r\n\tvar webSiteName = config.getWebsiteName;\r\n\tvar domain = config.getDomain;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '页面内容',\r\n\t\t\t\tpageData: {\r\n\t\t\t\t\tpost_content: \"\",\r\n\t\t\t\t},\r\n\t\t\t\tpagesList: {},\r\n\t\t\t\tdisplay: 'none',\r\n\t\t\t\tpraiseList: [],\r\n\t\t\t\tdialog: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tcontent: '',\r\n\t\t\t\t\thidden: true\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tsystem: \"\",\r\n\t\t\t\twebSiteName: webSiteName,\r\n\t\t\t\tdomain: domain,\r\n\t\t\t\tdownloadFileDomain: config.getDownloadFileDomain,\r\n\t\t\t\tbusinessDomain: uni.getStorageSync('businessDomain')\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tvar that = this;\r\n\t\t\tthis.fetchData();\n\t\t\t// #ifdef  MP-WEIXIN\r\n\t\t\tuni.showShareMenu({\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t});\n\t\t\t//#endif\r\n\t\t},\r\n\t\tonPullDownRefresh: function() {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.setData({\r\n\t\t\t\tdisplay: 'none',\r\n\t\t\t\tpageData: {}\r\n\t\t\t});\r\n\t\t\tthis.fetchData(); //消除下刷新出现空白矩形的问题。\r\n\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t},\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '关于“' + config.getWebsiteName + '”小程序',\r\n\t\t\t\tpath: 'pages/about/about',\r\n\t\t\t\tsuccess: function(res) { // 转发成功\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) { // 转发失败\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 自定义分享朋友圈\r\n\t\tonShareTimeline: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '关于“' + config.getWebsiteName + '”小程序'\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\r\n\t\r\n\t\t\tcopyLink: function(url) {\r\n\t\t\t\t//this.ShowHideMenu();\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.getClipboardData({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t//给a标签添加跳转和复制链接事件\r\n\t\t\twxParseTagATap: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar href = e.currentTarget.dataset.src;\r\n\t\t\t\tlet appid = e.currentTarget.dataset.appid;\r\n\t\t\t\tlet redirectype = e.currentTarget.dataset.redirectype;\r\n\t\t\t\tlet path = e.currentTarget.dataset.path; // 判断a标签src里是不是插入的文档链接\r\n\r\n\t\t\t\tlet isDoc = /\\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(href);\r\n\r\n\t\t\t\tif (isDoc) {\r\n\t\t\t\t\tthis.openLinkDoc(e);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (redirectype) {\r\n\t\t\t\t\tif (redirectype == 'apppage') {\r\n\t\t\t\t\t\t//跳转到小程序内部页面         \r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: path\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (redirectype == 'webpage') //跳转到web-view内嵌的页面\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\thref = '../webpage/webpage?url=' + href;\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tif (redirectype == 'miniapp') //跳转其他小程序\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\t\t\t\t\tappId: appid,\r\n\t\t\t\t\t\t\t\t\tpath: path\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar enterpriseMinapp = that.data.pageData.enterpriseMinapp;\r\n\t\t\t\tvar domain = config.getDomain; //可以在这里进行一些路由处理\r\n\r\n\t\t\t\tif (href.indexOf(domain) == -1) {\r\n\t\t\t\t\tvar n = 0;\r\n\r\n\t\t\t\t\tfor (var i = 0; i < that.data.businessDomain.length; i++) {\r\n\t\t\t\t\t\tif (href.indexOf(that.data.businessDomain[i].domain) != -1) {\r\n\t\t\t\t\t\t\tn++;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (n > 0) {\r\n\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t} // wx.setClipboardData({\r\n\t\t\t\t\t//   data: href,\r\n\t\t\t\t\t//   success: function (res) {\r\n\t\t\t\t\t//     wx.getClipboardData({\r\n\t\t\t\t\t//       success: function (res) {\r\n\t\t\t\t\t//         wx.showToast({\r\n\t\t\t\t\t//           title: '链接已复制',\r\n\t\t\t\t\t//           //icon: 'success',\r\n\t\t\t\t\t//           duration: 2000\r\n\t\t\t\t\t//         })\r\n\t\t\t\t\t//       }\r\n\t\t\t\t\t//     })\r\n\t\t\t\t\t//   }\r\n\t\t\t\t\t// })\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar slug = util.GetUrlFileName(href, domain);\r\n\r\n\t\t\t\t\tif (slug == \"\") {\r\n\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (slug == 'index') {\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar getPostSlugRequest = wxRequest.getRequest(Api.getPostBySlug(slug));\r\n\t\t\t\t\t\tgetPostSlugRequest.then(res => {\r\n\t\t\t\t\t\t\tif (res.statusCode == 200) {\r\n\t\t\t\t\t\t\t\tif (res.data.length != 0) {\r\n\t\t\t\t\t\t\t\t\tvar postID = res.data[0].id;\r\n\t\t\t\t\t\t\t\t\tvar openLinkCount = uni.getStorageSync('openLinkCount') || 0;\r\n\r\n\t\t\t\t\t\t\t\t\tif (openLinkCount > 4) {\r\n\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '../detail/detail?id=' + postID\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '../detail/detail?id=' + postID\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\topenLinkCount++;\r\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('openLinkCount', openLinkCount);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).catch(res => {\r\n\t\t\t\t\t\t\tconsole.log(response.data.message);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开文档\r\n\t\t\topenLinkDoc(e) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tlet url;\r\n\t\t\t\tlet fileType; // 如果是a标签href中插入的文档\r\n\r\n\t\t\t\tlet src = e.currentTarget.dataset.src;\r\n\t\t\t\tvar n = 0;\r\n\r\n\t\t\t\tfor (var i = 0; i < self.data.downloadFileDomain.length; i++) {\r\n\t\t\t\t\tif (src.indexOf(self.data.downloadFileDomain[i].domain) != -1) {\r\n\t\t\t\t\t\tn++;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (n == 0) {\r\n\t\t\t\t\tself.copyLink(src);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet docType;\r\n\t\t\t\tlet isDoc = /\\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(src);\r\n\r\n\t\t\t\tif (src && isDoc) {\r\n\t\t\t\t\turl = src;\r\n\t\t\t\t\tfileType = /doc|docx|xls|xlsx|ppt|pptx|pdf$/.exec(src)[0];\r\n\t\t\t\t} else {\r\n\t\t\t\t\turl = e.currentTarget.dataset.filelink;\r\n\t\t\t\t\tfileType = e.currentTarget.dataset.filetype;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconst filePath = res.tempFilePath;\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tfieldType: fileType\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\tconsole.log('下载文档失败:' + error);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tcopyLink: function(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.getClipboardData({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\r\n\r\n\t\t\tfetchData: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar getPageRequest = wxRequest.getRequest(Api.getAboutPage());\r\n\t\t\t\tgetPageRequest.then(response => {\r\n\t\t\t\t\tconsole.log(response);\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: response.data.post_title,\r\n\t\t\t\t\t\tsuccess: function(res) { // success\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tpageData: response.data\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tdisplay: 'block'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tconfirm() {\r\n\t\t\t\tconsole.log(\"占位：函数 confirm 未声明\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n * \r\n */\r\n\r\n\r\n\t.wrapper {\r\n\t\tpadding: 5rpx;\r\n\t}\r\n\r\n\t.excerpt {\r\n\r\n\t\tline-height: 64rpx;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.h2 {\r\n\t\tclear: both;\r\n\t\tborder-width: 1px 0;\r\n\t\tcolor: #666;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.clearfix {\r\n\t\tmargin: 5px 15px;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t.wxParse-a {\r\n\t\tcolor: #21759b\r\n\t}\r\n\r\n\t.wxParse-p {\r\n\t\tmargin-top: 0.8em;\r\n\t\tmargin-bottom: 0.8em\r\n\t}\r\n\r\n\t.wxParse-p image {\r\n\t\tmargin: 0 auto;\r\n\t\twidth: 100% !important;\r\n\t\tbox-shadow: 2rpx 4rpx 16rpx #666\r\n\t}\r\n\r\n\t.praisePost {\r\n\t\tposition: relative;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\r\n\r\n\t}\r\n\r\n\t.praiseTitle {\r\n\t\ttext-align: center;\r\n\t\tfont-weight: normal;\r\n\t\tline-height: 40rpx;\r\n\t\tmargin-top: 50rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #959595 !important;\r\n\t\tvertical-align: middle;\r\n\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 30rpx;\r\n\r\n\r\n\r\n\t}\r\n\r\n\t.praiseText {\r\n\t\ttext-align: center;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #21759b !important;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t}\r\n\r\n\t.img-like {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tdisplay: inline-block;\r\n\t\ttext-align: center;\r\n\t\tmargin-right: 20rpx;\r\n\t\tmargin-top: 8rpx;\r\n\t}\r\n\r\n\r\n\t.gravatarLikeImg {\r\n\t\tmargin-top: 4px;\r\n\r\n\t\theight: 48rpx;\r\n\t\twidth: 48rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.praise-button {\r\n\t\tfont-size: 0.785714286rem;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #fff;\r\n\r\n\t\tbackground-repeat: repeat-x;\r\n\t\tmargin-top: 24rpx;\r\n\t\twidth: 320rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t}\r\n\r\n\t.praise-button::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.gotowebpage-button {\r\n\t\tfont-size: 0.785714286rem;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #fff;\r\n\r\n\t\tbackground-repeat: repeat-x;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\twidth: 320rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t}\r\n\r\n\t.gotowebpage-button::after {\r\n\t\tborder: none;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./about.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190076\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}