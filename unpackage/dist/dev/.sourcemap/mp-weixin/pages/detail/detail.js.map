{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?95e6", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?0bc6", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?9673", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?505a", "uni-app:///pages/detail/detail.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?a1f4", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/detail/detail.vue?b396"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "target", "title", "webSiteName", "wechat", "detail", "category_name", "detailAdId", "detailAd", "rendered", "pageviews", "content", "previous_post_id", "next_post_id", "previous_post_title", "next_post_title", "videoAdId", "id", "commentsList", "ChildrenCommentsList", "commentCount", "detailDate", "commentValue", "display", "showerror", "page", "isLastPage", "parentID", "focus", "placeholder", "postID", "scrollHeight", "postList", "link", "dialog", "hidden", "isShow", "isLoad", "menuBackgroup", "likeImag", "likeList", "likeCount", "displayLike", "userid", "toFromId", "commentdate", "flag", "logo", "enableComment", "isLoading", "system", "downloadFileDomain", "businessDomain", "isPlayAudio", "audioSeek", "audioDuration", "showTime1", "showTime2", "audioTime", "displayAudio", "shareImagePath", "detailSummaryHeight", "detailAdsuccess", "detailTopAdsuccess", "fristOpen", "domain", "onLoad", "uni", "withShareTicket", "menus", "success", "that", "platform", "onUnload", "rewardedVideoAd", "innerAudioContext", "ctx", "onShareAppMessage", "console", "path", "imageUrl", "fail", "onShareTimeline", "query", "methods", "onLoadClone3389", "showLikeImg", "avatarurl", "likes", "fristOpenComment", "gotowebpage", "url", "copyLink", "duration", "goHome", "fetchDetailData", "getPostDetailRequest", "res", "self", "errMessage", "setTimeout", "tagsArr", "tags", "getPostTagsRequest", "slider<PERSON><PERSON><PERSON>", "value", "InitializationAudio", "min", "sec", "loadAudio", "playAudio", "wxParseTagATap", "href", "appId", "n", "getPostSlugRequest", "openLinkCount", "openLinkDoc", "fileType", "filePath", "fieldType", "showCustomizeModal", "loadMore", "mask", "confirm", "adbin<PERSON><PERSON><PERSON>", "adTopbinderror", "readMore", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,oOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAk2B,CAAgB,k0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACqMt3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA,eACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QAEAN;UACAO;QACA;QAEAC;QAEAC;UACAF;QACA;QAEAG;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAhC;QACAS;QACAwB;MACA;MAEAxB;MACAyB;MAEA;MACAC;MAEA;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,8DACA,qDACA,uDACA,yDACA,yDACA;EAIA;EAEAC;IACA;IAIAC;MACAC;MACAC;MACAC;IACA;IAGAC;IACAJ;MACAG;QACA;QACAC;UACArB;UACAsB;QACA;MACA;IACA;EAEA;EACAC;IACA;;IAGA;MACAC;IACA;IAEAC;IACAC;EACA;EACAC;IAEAC;IACA;MACA5E;MACA6E;MACAC;MACAV;QACA;QACAQ;MACA;MACAG;QACAH;MACA;IACA;EACA;;EACA;EACAI;IACA;IACA;MACAhF;MACAiF;QACAlE;MACA;MACA+D;IACA;EACA;EACAI;IACAC;IAEAC;MACA;MACA;MACA;MAEA;QACA;MACA;MAEA;MAEA;QACA;QAEA;UACAC;QACA;QAEAC;MACA;MAEA;MACAjB;QACA/B;MACA;IACA;IAEA;IACAiD;MACA;QACAhE;QACAP;QACAQ;MACA;MACA;MACA;QACAD;MACA;IACA;IAEAiE;MACA;MAGA;MAEA;QACA;QACAvB;UACAwB;QACA;MACA;QACApB;MACA;IACA;IAEAqB;MACAzB;QACAnE;QACAsE;UACAH;YACAG;cACAH;gBACAjE;gBACA2F;cACA;YACA;UACA;QACA;MACA;IACA;IAGAC;MACA3B;QACAwB;MACA;IACA;IAEA;IACAI;MACA;MACA;MACA;MACAC;QACAC;QAEA;UACAC;YACA1E;YACAD;YACA4E;UACA;UACA;QACA;;QAGAhC;UACAjE;QACA;QACA;QACAkG;UACAF;QACA;;QAGA;;QAKAA;UACA7F;UACAyB;UACAG;UACAZ;UACAE;QACA;QACA;MACA;QACA;QACA8E;QAEA;UACA;QACA;QAEA;QAEA;UACA;YACAC;UACA;YACAA;UACA;QACA;QAEA;UACA;UACAC;YACAL;cACAlE;YACA;UACA;QACA;MACA;QACA8C;MACA;IACA;IAEA;IACA0B;MACA;MACA7B;;MAEA;MACA;QACAjB;MACA;MACA;;MAEA+C;;MAEA;QACAnD;QACAD;MACA;;MAEAsB;;MAEAA;IACA;IAEA;IACA+B;MACA;;MAEA/B;MACA;;MAEAA;MACAA;MACAA;QACA;QACAA;QACAyB;UACA;UACA;UACA;UACA;UAEA;YACAO;UACA;UAEA;YACAC;UACA;UAEArC;YACAhB;YACAE;UACA;QACA;MACA;IACA;IAEAoD;MACA;IAEA;;IAEAC;MACA;MACA;MACA;MACA;MACAnC;;MAEAJ;QACAlB;MACA;MAEA;QACA;QACAkB;UACAjB;QACA;MACA;QACA;QACAqB;QAEA;UACAJ;YACAhB;UACA;QACA;;QAGAoB;QACAA;MACA;IACA;IAEA;IACAoC;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MAEA;QACA;QACA;MACA;MAEA;QACA;UACA;UACA5C;YACAwB;UACA;QACA;UACA;YAAA;YACA;cACAqB;cACA7C;gBACAwB;cACA;YACA;YACA;cAAA;cACA;gBACAxB;kBACA8C;kBACAlC;gBACA;cACA;UACA;QACA;QAEA;MACA;MAEA;;MAEA;QACA;QAEA;UACA;YACAmC;YACA;UACA;QACA;QAEA;UACA;UAEA;YACAvB;YACAxB;cACAwB;YACA;UACA;YACApB;UACA;QACA;UACAA;QACA;MACA;QACA;QAEA;UACA;UAEA;YACAoB;YACAxB;cACAwB;YACA;UACA;YACApB;UACA;UAEA;QACA;QAEA;UACAJ;YACAwB;UACA;QACA;UACA;UACAwB;YACA;cACA;gBACA;gBACA;gBAEA;kBACAhD;oBACAwB;kBACA;gBACA;kBACAxB;oBACAwB;kBACA;kBACAyB;kBACAjD;gBACA;cACA;gBACA;gBAEA;kBACAwB;kBACAxB;oBACAwB;kBACA;gBACA;kBACApB;gBACA;cACA;YACA;UACA;YACAO;UACA;QACA;MACA;IACA;IAEA;IACAuC;MACA;MACA;MACA;;MAEA;MACA;MAEA;QACA;UACAH;UACA;QACA;MACA;MAEA;QACAhB;QACA;MACA;MAEA;MACA;MAEA;QACAP;QACA2B;MACA;QACA3B;QACA2B;MACA;MAEAnD;QACAwB;QACArB;UACA;UACAH;YACAoD;YACAC;UACA;QACA;QACAvC;UACAH;QACA;MACA;IACA;IAEAR;MACA,IACAjE,SACA4F,IADA5F;MAEAyE;IACA;IAEA2C;MACA;QACAxH;MACA;IACA;IAIA;IACAyH;MACA;MAEA;QACAnD;UACA9C;QACA;QACAqD;QACA;MACA;QACAX;UACAjE;UACAyH;UACA9B;QACA;MACA;IACA;IAEA+B;MACA;QACA;QACA;QACA;MACA;IACA;IAOAC;MACA;MACA/C;MACAA;MAEA;QACAP;UACAT;QACA;MACA;IACA;IAEAgE;MACA;MACAhD;MACAA;MAEA;QACAP;UACAR;QACA;MACA;IACA;IAEA;IACAgE;MACA;MACA;MAEA;QACA5D;UACAjE;UACA8H;UACAnC;QACA;QACAtB;UACAV;QACA;MACA;QACAa;UACAA;YAAA;UAAA;YACAI;YACAP;cACAV;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACv4BA;AAAA;AAAA;AAAA;AAA2tC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACA/uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/detail/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/detail/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=3e159eb4&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/detail/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=3e159eb4&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n    mpHtml: function () {\n      return import(\n        /* webpackChunkName: \"components/mp-html/mp-html\" */ \"@/components/mp-html/mp-html\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.postList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\r\n\t<!--/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n *Copyright (c) 2017 https://www.minapper.com All rights reserved.\r\n * \r\n */-->\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t<ui-sys  >\r\n\t\t<view>\r\n\t\t\t<!-- 小程序广告 -->\r\n\t\t\t<!-- #ifdef MP -->\r\n\r\n\r\n\t\t\t<view class=\"ad-box\" v-if=\"detail.detailAdId !='' && detail.detailAd=='1' && detailTopAdsuccess\">\r\n\t\t\t\t<ad :unit-id=\"detail.detailAdId\" ad-intervals=\"30\" ad-type=\"video\" @error=\"adTopbinderror\"></ad>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 文章 -->\r\n\t\t\t<view class=\"detail-content ui-BG-card ui-BG\" :style=\"'display:'+(display)\">\r\n\t\t\t\t<!-- 标题 -->\r\n\t\t\t\t<view class=\"entry-title text-xxl ui-TC\">\r\n\t\t\t\t\t<text class=\"entry-title text-xxl ui-TC\">{{detail.title.rendered}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 文章互动数据 -->\r\n\t\t\t\t<view class=\"entry-data ui-TC-3 text-xs\">\r\n\t\t\t\t\t<text>{{detailDate}}</text>\r\n\t\t\t\t\t<!-- \t<text class=\"cicon-popover-o\"> {{total_comments}}</text> -->\r\n\t\t\t\t\t<text class=\"cicon-eye-o\"> {{detail.pageviews}}</text>\r\n\t\t\t\t\t<!-- \t<text class=\"cicon-favorite-o\"> {{likeCount}}</text> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 文章正文 -->\r\n\t\t\t\t<view class=\"entry-summary\" id=\"entry-summary\">\r\n\r\n\t\t\t\t\t<mp-html :content=\"detail.content.rendered\" @linktap=\"wxParseTagATap\" :copy-link=\"true\"\r\n\t\t\t\t\t\t:selectable=\"true\" :lazy-load=\"true\" :markdown=\"true\"  :img-cache=\"true\"/>\r\n\t\t\t\t\t<!-- 阅读更多 -->\r\n\t\t\t\t\t<!-- \t<view class=\"detail-read-more\" v-if=\"detailSummaryHeight != ''\">\r\n\t\t\t\t\t\t\t<view class=\"detail-readmore-mask\">\r\n\t\t\t\t\t\t\t\t<view class=\"detail-readmore-btn\" @tap=\"readMore\">点此观看视频，查看隐藏内容</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 版权声明 -->\r\n\r\n\r\n\t\t\t\t<!-- 点个赞 -->\r\n\r\n\t\t\t\t<view class=\"bottomlink text-sm   bg-grey-thin\" :style=\"'display:'+(display)\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text style=\"margin-top: 10rpx;\">文章ID：</text>{{postID}}\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<text style=\"\">声明：</text>\r\n\t\t\t\t\t本站所有内容来自网友投稿发布。如若本站内容侵犯了原著者的合法权益，可联系{{wechat}}进行处理。\r\n\r\n\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\r\n\t\t\t<!-- 上下文 -->\r\n\t\t\t<view v-if=\"detail.previous_post_id || detail.next_post_id\" class=\"up-down-post ui-BG-card ui-BG\">\r\n\t\t\t\t<!-- 上一篇 -->\r\n\t\t\t\t<view v-if=\"detail.previous_post_id\" class=\"up-post\">\r\n\t\t\t\t\t<navigator :url=\"'../detail/detail?id='+(detail.previous_post_id)\" open-type=\"redirect\"\r\n\t\t\t\t\t\thover-class=\"relatedNavigator\">上一篇：{{detail.previous_post_title}}</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 下一篇 -->\r\n\t\t\t\t<view v-if=\"detail.next_post_id\" class=\"down-post\">\r\n\t\t\t\t\t<navigator :url=\"'../detail/detail?id='+(detail.next_post_id)\" open-type=\"redirect\"\r\n\t\t\t\t\t\thover-class=\"relatedNavigator\">{{detail.next_post_title}}：下一篇</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 小程序广告 -->\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<view class=\"ad-box-video \" v-if=\"detail.videoAdId !='' && detail.detailAd=='1' && detailAdsuccess\">\r\n\t\t\t\t<ad :unit-id=\"detail.videoAdId\" ad-intervals=\"30\" ad-type=\"video\" @error=\"adbinderror\"></ad>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\r\n\t\t\t<!-- 猜你喜欢 -->\r\n\t\t\t<view v-if=\"postList.length != 0\" class=\"relatedPost ui-BG-card ui-BG\" :style=\"'display:'+(display)\">\r\n\t\t\t\t<view class=\"subTitle ui-TC\">猜你喜欢</view>\r\n\t\t\t\t<view class=\"subTitle_line\"></view>\r\n\t\t\t\t<block v-for=\"(item,index) in postList\" :key=\"item.id\">\r\n\r\n\r\n\r\n\t\t\t\t\t<navigator :url=\"'../detail/detail?id='+(item.id)\" open-type=\"redirect\"\r\n\t\t\t\t\t\thover-class=\"relatedNavigator\">\r\n\t\t\t\t\t\t<view class=\"relatedText ui-TC-1  \"> <text class=\"dot ui-BG-Main\"></text>\r\n\t\t\t\t\t\t\t{{item.title.rendered}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</navigator>\r\n\r\n\r\n\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"ditail-copyright\" :style=\"'display:'+(display)\">\r\n\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 评论区 -->\r\n\t\t\t<!-- <view :style=\"'display:'+(enableComment?'block':'none')\">\r\n\t\t\t\t\t<view class=\"foot ui-BG\" :style=\"'display:'+(display)\">\r\n\t\t\t\t\t\t<view class=\"foot_memu\" :style=\"'margin-bottom: '+(sys_info.safeArea.top)+'rpx;'\">\r\n\t\t\t\t\t\t\t<view class=\" comment ui-BG-2 ui-TC-2\" @tap=\"showCustomizeModal\" data-key=\"drawer\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-popover-o \"> 说点什么吧...</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"icon\" @tap.stop.prevent=\"praise\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-sponsor ui-TC-2\"> </text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"icon\" @tap.stop.prevent=\"onCreatePoster\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-camera-lens ui-TC-2\"> </text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-weixin ui-TC-2\" style=\" font-size: 32px;\"> </text>\r\n\t\t\t\t\t\t\t\t<button class=\"ui-btn\" open-type=\"share\"></button>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n -->\r\n\r\n\t\t\t<!-- 无法访问网络时 -->\r\n\t\t\t<view class=\"showerror\" :style=\"'display:'+(showerror)\">\r\n\t\t\t\t<text class=\"cicon-wifi-off text-xsl\"></text>\r\n\t\t\t\t<view class=\"errortext\">{{errMessage}}</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<!-- <form @submit.stop.prevent=\"formSubmit\" :report-submit=\"true\">\r\n\t\t\t\t<ui-modal :show=\"target === 'drawer'\" mask=\"40\" align=\"bottom\" :showCancel=\"false\">\r\n\r\n\t\t\t\t\t<view class=\"comment-respond radius\">\r\n\t\t\t\t\t\t<input class=\"comment-input radius\" maxlength=\"200\" name=\"inputPostID\" :value=\"detail.id\"\r\n\t\t\t\t\t\t\tstyle=\"display:none\" />\r\n\r\n\t\t\t\t\t\t<view class=\"comment-box\">\r\n\r\n\r\n\t\t\t\t\t\t \r\n\t\t\t<view class=\"comment_input_box\">\r\n\t\t\t\t<textarea class=\"comment-input radius ui-BG-2 ui-TC-2\" type=\"text\" confirm-type=\"send\"\r\n\t\t\t\t\tcursor-spacing=\"10\" maxlength=\"100\" name=\"inputComment\" :value=\"content\" :placeholder=\"placeholder\"\r\n\t\t\t\t\t@blur=\"onReplyBlur\" @focus=\"onRepleyFocus\" auto-height />\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"comment_btn ui-BG-Main text-sm\"> 发送 </view>\r\n\t\t\t<button class=\" comment_btn_button  ui-btn \" formType=\"submit\"> </button>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\r\n\t\t\t</ui-modal>\r\n\t\t\t</form>\r\n\t\t\t-->\r\n\t</ui-sys>\r\n\r\n</template>\r\n\r\n<script>\r\n\t/*\r\n\t * 微慕小程序开源版\r\n\t * author: jianbo\r\n\t * organization: 微慕  www.minapper.com\r\n\t * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n\t * 技术支持微信号：iamxjb\r\n\t * 开源协议：MIT\r\n\t *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\r\n\t */\r\n\timport config from '../../utils/config.js';\r\n\r\n\tvar Api = require('../../utils/api.js');\r\n\r\n\tvar util = require('../../utils/util.js');\r\n\r\n\tvar Auth = require('../../utils/auth.js');\r\n\r\n\tvar wxApi = require('../../utils/wxApi.js');\r\n\r\n\tvar wxRequest = require('../../utils/wxRequest.js');\r\n\r\n\tconst Adapter = require('../../utils/adapter.js');\r\n\r\n\tconst innerAudioContext = uni.createInnerAudioContext();\r\n\tlet ctx = uni.createCanvasContext('mycanvas');\r\n\tvar app = getApp();\r\n\tlet isFocusing = false;\r\n\tconst pageCount = config.getPageCount;\r\n\tvar webSiteName = config.getWebsiteName;\r\n\tvar domain = config.getDomain;\r\n\r\n\tvar wechat = config.getWecat;\r\n\tlet rewardedVideoAd = null;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttarget: '',\r\n\t\t\t\ttitle: '文章内容',\r\n\t\t\t\twebSiteName: webSiteName,\r\n\t\t\t\twechat: wechat,\r\n\t\t\t\tdetail: {\r\n\t\t\t\t\tcategory_name: \"\",\r\n\t\t\t\t\tdetailAdId: \"\",\r\n\t\t\t\t\tdetailAd: \"\",\r\n\r\n\t\t\t\t\ttitle: {\r\n\t\t\t\t\t\trendered: \"\"\r\n\t\t\t\t\t},\r\n\r\n\t\t\t\t\tpageviews: \"\",\r\n\r\n\t\t\t\t\tcontent: {\r\n\t\t\t\t\t\trendered: \"\"\r\n\t\t\t\t\t},\r\n\r\n\t\t\t\t\tprevious_post_id: \"\",\r\n\t\t\t\t\tnext_post_id: \"\",\r\n\t\t\t\t\tprevious_post_title: \"\",\r\n\t\t\t\t\tnext_post_title: \"\",\r\n\t\t\t\t\tvideoAdId: \"\",\r\n\t\t\t\t\tid: \"\"\r\n\t\t\t\t},\r\n\r\n\t\t\t\tcommentsList: [],\r\n\t\t\t\tChildrenCommentsList: [],\r\n\t\t\t\tcommentCount: '',\r\n\t\t\t\tdetailDate: '',\r\n\t\t\t\tcommentValue: '',\r\n\t\t\t\tdisplay: 'none',\r\n\t\t\t\tshowerror: 'none',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tisLastPage: false,\r\n\t\t\t\tparentID: \"0\",\r\n\t\t\t\tfocus: false,\r\n\t\t\t\tplaceholder: \"请写下您想说的话...\",\r\n\t\t\t\tpostID: null,\r\n\t\t\t\tscrollHeight: 0,\r\n\t\t\t\tpostList: [],\r\n\t\t\t\tlink: '',\r\n\r\n\t\t\t\tdialog: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tcontent: '',\r\n\t\t\t\t\thidden: true\r\n\t\t\t\t},\r\n\r\n\t\t\t\tcontent: '',\r\n\t\t\t\tisShow: true,\r\n\r\n\t\t\t\t//控制menubox是否显示\r\n\t\t\t\tisLoad: true,\r\n\r\n\t\t\t\t//解决menubox执行一次  \r\n\t\t\t\tmenuBackgroup: false,\r\n\r\n\t\t\t\tlikeImag: \"/static/pages/detail/like.png\",\r\n\t\t\t\tlikeList: [],\r\n\t\t\t\tlikeCount: 0,\r\n\t\t\t\tdisplayLike: 'none',\r\n\t\t\t\tuserid: \"\",\r\n\t\t\t\ttoFromId: \"\",\r\n\t\t\t\tcommentdate: \"\",\r\n\t\t\t\tflag: 1,\r\n\t\t\t\tlogo: uni.getStorageSync('logoImageurl'),\r\n\t\t\t\tenableComment: true,\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tsystem: '',\r\n\t\t\t\tdownloadFileDomain: uni.getStorageSync('downloadfileDomain'),\r\n\t\t\t\tbusinessDomain: uni.getStorageSync('businessDomain'),\r\n\t\t\t\tisPlayAudio: false,\r\n\t\t\t\taudioSeek: 0,\r\n\t\t\t\taudioDuration: 0,\r\n\t\t\t\tshowTime1: '00:00',\r\n\t\t\t\tshowTime2: '00:00',\r\n\t\t\t\taudioTime: 0,\r\n\t\t\t\tdisplayAudio: 'none',\r\n\t\t\t\tshareImagePath: '',\r\n\t\t\t\tdetailSummaryHeight: '',\r\n\t\t\t\tdetailAdsuccess: true,\r\n\t\t\t\tdetailTopAdsuccess: true,\r\n\t\t\t\tfristOpen: false,\r\n\t\t\t\tdomain: domain,\r\n\t\t\t\tdetailSummaryHeight: '',\r\n\t\t\t\tplatform: '',\r\n\t\t\t\terrMessage: \"\",\r\n\t\t\t\tpostImageUrl: \"\",\r\n\t\t\t\tposterConfig: \"\",\r\n\r\n\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad: function(options) {\r\n\t\t\tvar that = this;\n\t\t\t// #ifdef  MP-WEIXIN\n\t\t\t\n\t\t\t\r\n\t\t\tuni.showShareMenu({\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: [\"shareAppMessage\", \"shareTimeline\"],\r\n\t\t\t\tsuccess: function(e) {}\r\n\t\t\t});\n\n// #endif\r\n\t\t\tthat.fetchDetailData(options.id);\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(t) {\r\n\t\t\t\t\tvar system = t.system.indexOf(\"iOS\") != -1 ? \"iOS\" : \"Android\";\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tsystem: system,\r\n\t\t\t\t\t\tplatform: t.platform\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t},\r\n\t\tonUnload: function() {\r\n\t\t\t//卸载页面，清除计步器\r\n\r\n\r\n\t\t\tif (rewardedVideoAd && rewardedVideoAd.destroy) {\r\n\t\t\t\trewardedVideoAd.destroy();\r\n\t\t\t}\r\n\r\n\t\t\tinnerAudioContext.destroy();\r\n\t\t\tctx = null;\r\n\t\t},\r\n\t\tonShareAppMessage: function(res) {\r\n\r\n\t\t\tconsole.log(res);\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '分享\"' + webSiteName + '\"的文章：' + this.data.detail.title.rendered,\r\n\t\t\t\tpath: 'pages/detail/detail?id=' + this.data.detail.id,\r\n\t\t\t\timageUrl: this.data.detail.post_full_image,\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t// 转发成功\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t},\r\n\t\t\t\tfail: function(res) {\r\n\t\t\t\t\tconsole.log(res); // 转发失败\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 自定义分享朋友圈\r\n\t\tonShareTimeline: function() {\r\n\t\t\tlet imageUrl = this.data.detail.post_full_image;\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.data.detail.title.rendered,\r\n\t\t\t\tquery: {\r\n\t\t\t\t\tid: this.data.detail.id\r\n\t\t\t\t},\r\n\t\t\t\timageUrl\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadClone3389: function(options) {},\r\n\r\n\t\t\tshowLikeImg: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar flag = false;\r\n\t\t\t\tvar _likes = that.data.detail.avatarurls;\r\n\r\n\t\t\t\tif (!_likes) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar likes = [];\r\n\r\n\t\t\t\tfor (var i = 0; i < _likes.length; i++) {\r\n\t\t\t\t\tvar avatarurl = \"/static/images/gravatar.png\";\r\n\r\n\t\t\t\t\tif (_likes[i].avatarurl.indexOf('wx.qlogo.cn') != -1) {\r\n\t\t\t\t\t\tavatarurl = _likes[i].avatarurl;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlikes[i] = avatarurl;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar temp = likes;\r\n\t\t\t\tthat.setData({\r\n\t\t\t\t\tlikeList: likes\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 首次加载评论\r\n\t\t\tfristOpenComment() {\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tcommentsList: [],\r\n\t\t\t\t\tisLastPage: false\r\n\t\t\t\t});\r\n\t\t\t\tthis.fetchCommentData();\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\tpage: this.data.page + 1\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgotowebpage: function() {\r\n\t\t\t\tvar that = this;\r\n\r\n\r\n\t\t\t\tvar url = '';\r\n\r\n\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\tvar url = '../webpage/webpage';\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url + '?url=' + self.link\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.copyLink(self.link);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tcopyLink: function(url) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.getClipboardData({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tgoHome: function() {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t//获取文章内容\r\n\t\t\tfetchDetailData: function(id) {\r\n\t\t\t\tvar self = this;\r\n\t\t\t\tvar getPostDetailRequest = wxRequest.getRequest(Api.getPostByID(id));\r\n\t\t\t\tvar res;\r\n\t\t\t\tgetPostDetailRequest.then(response => {\r\n\t\t\t\t\tres = response;\r\n\r\n\t\t\t\t\tif (response.data.code && response.data.data.status == \"404\") {\r\n\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\tshowerror: 'block',\r\n\t\t\t\t\t\t\tdisplay: 'none',\r\n\t\t\t\t\t\t\terrMessage: response.data.message\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t} // 设置页面标题：文章分类\r\n\r\n\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.data.category_name\r\n\t\t\t\t\t});\r\n\t\t\t\t\t//WxParse.wxParse('article', 'html', response.data.content.rendered, self, 5)\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tself.article_article = response.data.content.rendered;\r\n\t\t\t\t\t}, 200);\r\n\r\n\r\n\t\t\t\t\t// 如果超过指定数量\r\n\r\n\r\n\r\n\r\n\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\tdetail: response.data,\r\n\t\t\t\t\t\tpostID: id,\r\n\t\t\t\t\t\tlink: response.data.link,\r\n\t\t\t\t\t\tdetailDate: util.cutstr(response.data.date, 10, 1),\r\n\t\t\t\t\t\tdisplay: 'block',\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn response.data;\r\n\t\t\t\t}).then(response => {\r\n\t\t\t\t\tvar tagsArr = [];\r\n\t\t\t\t\ttagsArr = res.data.tags;\r\n\r\n\t\t\t\t\tif (!tagsArr) {\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tvar tags = \"\";\r\n\r\n\t\t\t\t\tfor (var i = 0; i < tagsArr.length; i++) {\r\n\t\t\t\t\t\tif (i == 0) {\r\n\t\t\t\t\t\t\ttags += tagsArr[i];\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\ttags += \",\" + tagsArr[i];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (tags != \"\") {\r\n\t\t\t\t\t\tvar getPostTagsRequest = wxRequest.getRequest(Api.getPostsByTags(id, tags));\r\n\t\t\t\t\t\tgetPostTagsRequest.then(response => {\r\n\t\t\t\t\t\t\tself.setData({\r\n\t\t\t\t\t\t\t\tpostList: response.data\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(function(error) {\r\n\t\t\t\t\tconsole.log('error: ' + error);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t//拖动进度条事件\r\n\t\t\tsliderChange: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tinnerAudioContext.src = this.data.detail.audios[0].src; //获取进度条百分比\r\n\r\n\t\t\t\tvar value = e.detail.value;\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\taudioTime: value\r\n\t\t\t\t});\r\n\t\t\t\tvar duration = this.data.audioDuration; //根据进度条百分比及歌曲总时间，计算拖动位置的时间\r\n\r\n\t\t\t\tvalue = parseInt(value * duration / 100); //更改状态\r\n\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\taudioSeek: value,\r\n\t\t\t\t\tisPlayAudio: true\r\n\t\t\t\t}); //调用seek方法跳转歌曲时间\r\n\r\n\t\t\t\tinnerAudioContext.seek(value); //播放歌曲\r\n\r\n\t\t\t\tinnerAudioContext.play();\r\n\t\t\t},\r\n\r\n\t\t\t//初始化播放器，获取duration\r\n\t\t\tInitializationAudio: function(audiosrc) {\r\n\t\t\t\tvar that = this; //设置src\r\n\r\n\t\t\t\tinnerAudioContext.src = audiosrc; //运行一次\r\n\t\t\t\t//innerAudioContext.play();\r\n\r\n\t\t\t\tinnerAudioContext.autoplay = false;\r\n\t\t\t\tinnerAudioContext.pause();\r\n\t\t\t\tinnerAudioContext.onCanplay(() => {\r\n\t\t\t\t\t//初始化duration\r\n\t\t\t\t\tinnerAudioContext.duration;\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\t//延时获取音频真正的duration\r\n\t\t\t\t\t\tvar duration = innerAudioContext.duration;\r\n\t\t\t\t\t\tvar min = parseInt(duration / 60);\r\n\t\t\t\t\t\tvar sec = parseInt(duration % 60);\r\n\r\n\t\t\t\t\t\tif (min.toString().length == 1) {\r\n\t\t\t\t\t\t\tmin = `0${min}`;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tif (sec.toString().length == 1) {\r\n\t\t\t\t\t\t\tsec = `0${sec}`;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\t\taudioDuration: innerAudioContext.duration,\r\n\t\t\t\t\t\t\tshowTime2: `${min}:${sec}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tloadAudio: function() {\r\n\t\t\t\tvar that = this; //设置一个计步器\r\n\r\n\t\t\t},\r\n\r\n\t\t\tplayAudio: function() {\r\n\t\t\t\t//获取播放状态和当前播放时间  \r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar isPlayAudio = that.data.isPlayAudio;\r\n\t\t\t\tvar seek = that.data.audioSeek;\r\n\t\t\t\tinnerAudioContext.pause(); //更改播放状态\r\n\r\n\t\t\t\tthat.setData({\r\n\t\t\t\t\tisPlayAudio: !isPlayAudio\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (isPlayAudio) {\r\n\t\t\t\t\t//如果在播放则记录播放的时间seek，暂停\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\taudioSeek: innerAudioContext.currentTime\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t//如果在暂停，获取播放时间并继续播放\r\n\t\t\t\t\tinnerAudioContext.src = that.data.detail.audios[0].src;\r\n\r\n\t\t\t\t\tif (innerAudioContext.duration != 0) {\r\n\t\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\t\taudioDuration: innerAudioContext.duration\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} //跳转到指定时间播放\r\n\r\n\r\n\t\t\t\t\tinnerAudioContext.seek(seek);\r\n\t\t\t\t\tinnerAudioContext.play();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t//给a标签添加跳转和复制链接事件\r\n\t\t\twxParseTagATap: function(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet href = e.detail.src || e.detail.href;\r\n\t\t\t\tlet domain = config.getDomain;\r\n\t\t\t\tlet appid = e.detail.appid;\r\n\t\t\t\tlet redirectype = e.detail.redirectype;\r\n\t\t\t\tlet path = e.detail.path; // 判断a标签src里是不是插入的文档链接\r\n\r\n\t\t\t\tlet isDoc = /\\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(href);\r\n\r\n\t\t\t\tif (isDoc) {\r\n\t\t\t\t\tthis.openLinkDoc(e);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (redirectype) {\r\n\t\t\t\t\tif (redirectype == 'apppage') {\r\n\t\t\t\t\t\t//跳转到小程序内部页面         \r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: path\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (redirectype == 'webpage') //跳转到web-view内嵌的页面\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\thref = '../webpage/webpage?url=' + href;\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tif (redirectype == 'miniapp') //跳转其他小程序\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\t\t\t\t\tappId: appid,\r\n\t\t\t\t\t\t\t\t\tpath: path\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvar enterpriseMinapp = that.data.detail.enterpriseMinapp; //可以在这里进行一些路由处理\r\n\r\n\t\t\t\tif (href.indexOf(domain) == -1) {\r\n\t\t\t\t\tvar n = 0;\r\n\r\n\t\t\t\t\tfor (var i = 0; i < that.data.businessDomain.length; i++) {\r\n\t\t\t\t\t\tif (href.indexOf(that.data.businessDomain[i].domain) != -1) {\r\n\t\t\t\t\t\t\tn++;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (n > 0) {\r\n\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar slug = util.GetUrlFileName(href, domain);\r\n\r\n\t\t\t\t\tif (slug == \"\") {\r\n\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (slug == 'index') {\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar getPostSlugRequest = wxRequest.getRequest(Api.getPostBySlug(slug));\r\n\t\t\t\t\t\tgetPostSlugRequest.then(res => {\r\n\t\t\t\t\t\t\tif (res.statusCode == 200) {\r\n\t\t\t\t\t\t\t\tif (res.data.length != 0) {\r\n\t\t\t\t\t\t\t\t\tvar postID = res.data[0].id;\r\n\t\t\t\t\t\t\t\t\tvar openLinkCount = uni.getStorageSync('openLinkCount') || 0;\r\n\r\n\t\t\t\t\t\t\t\t\tif (openLinkCount > 4) {\r\n\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '../detail/detail?id=' + postID\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '../detail/detail?id=' + postID\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\topenLinkCount++;\r\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('openLinkCount', openLinkCount);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tvar url = '../webpage/webpage';\r\n\r\n\t\t\t\t\t\t\t\t\tif (enterpriseMinapp == \"1\") {\r\n\t\t\t\t\t\t\t\t\t\turl = '../webpage/webpage';\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: url + '?url=' + href\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tthat.copyLink(href);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).catch(res => {\r\n\t\t\t\t\t\t\tconsole.log(response.data.message);\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开文档\r\n\t\t\topenLinkDoc(e) {\r\n\t\t\t\tlet self = this;\r\n\t\t\t\tlet url;\r\n\t\t\t\tlet fileType; // 如果是a标签href中插入的文档\r\n\r\n\t\t\t\tlet src = e.currentTarget.dataset.src;\r\n\t\t\t\tvar n = 0;\r\n\r\n\t\t\t\tfor (var i = 0; i < self.data.downloadFileDomain.length; i++) {\r\n\t\t\t\t\tif (src.indexOf(self.data.downloadFileDomain[i]) != -1) {\r\n\t\t\t\t\t\tn++;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (n == 0) {\r\n\t\t\t\t\tself.copyLink(src);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet docType;\r\n\t\t\t\tlet isDoc = /\\.(doc|docx|xls|xlsx|ppt|pptx|pdf)$/.test(src);\r\n\r\n\t\t\t\tif (src && isDoc) {\r\n\t\t\t\t\turl = src;\r\n\t\t\t\t\tfileType = /doc|docx|xls|xlsx|ppt|pptx|pdf$/.exec(src)[0];\r\n\t\t\t\t} else {\r\n\t\t\t\t\turl = e.currentTarget.dataset.filelink;\r\n\t\t\t\t\tfileType = e.currentTarget.dataset.filetype;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: url,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconst filePath = res.tempFilePath;\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tfieldType: fileType\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(error) {\r\n\t\t\t\t\t\tconsole.log('下载文档失败:' + error);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tsuccess(res) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdetail\r\n\t\t\t\t} = res;\r\n\t\t\t\tconsole.log(detail);\r\n\t\t\t},\r\n\r\n\t\t\tshowCustomizeModal(e) {\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\ttarget: e.currentTarget.dataset.key\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t//底部刷新\r\n\t\t\tloadMore: function(e) {\r\n\t\t\t\tvar that = this;\r\n\r\n\t\t\t\tif (!that.data.isLastPage) {\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tpage: that.data.page + 1\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log('当前页' + that.data.page);\r\n\t\t\t\t\tthis.fetchCommentData();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '没有更多内容',\r\n\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tconfirm: function() {\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\t\"dialog.hidden\": true,\r\n\t\t\t\t\t\"dialog.title\": '',\r\n\t\t\t\t\t\"dialog.content\": ''\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\tadbinderror: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tconsole.log(e.detail.errCode);\r\n\t\t\t\tconsole.log(e.detail.errMsg);\r\n\r\n\t\t\t\tif (e.detail.errCode) {\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tdetailAdsuccess: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tadTopbinderror: function(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tconsole.log(e.detail.errCode);\r\n\t\t\t\tconsole.log(e.detail.errMsg);\r\n\r\n\t\t\t\tif (e.detail.errCode) {\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tdetailTopAdsuccess: false\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 阅读更多\r\n\t\t\treadMore: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar platform = that.data.platform;\r\n\r\n\t\t\t\tif (platform == 'devtools') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"开发工具无法显示激励视频\",\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\tdetailSummaryHeight: ''\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\trewardedVideoAd.show().catch(() => {\r\n\t\t\t\t\t\trewardedVideoAd.load().then(() => rewardedVideoAd.show()).catch(err => {\r\n\t\t\t\t\t\t\tconsole.log('激励视频 广告显示失败');\r\n\t\t\t\t\t\t\tthat.setData({\r\n\t\t\t\t\t\t\t\tdetailSummaryHeight: ''\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * \r\n */\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t/* 文章正文样式 */\r\n\r\n\t.entry-summary {\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 64rpx;\r\n\t\toverflow: hidden;\r\n\t\toverflow-x: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.entry-summary image {\r\n\t\twidth: 100%\r\n\t}\r\n\r\n\t/* 标题 */\r\n\r\n\t.entry-title {\r\n\r\n\r\n\t\tfont-weight: 600;\r\n\t\toutline: none;\r\n\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t/* 文章互动数据 */\r\n\r\n\t.entry-data {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.entry-data text {\r\n\t\tmargin-right: 32rpx;\r\n\t}\r\n\r\n\t.entry-data>image {\r\n\t\twidth: 24rpx;\r\n\t\theight: 24rpx;\r\n\t}\r\n\r\n\t.entry-data>image:not(:first-child) {\r\n\t\tmargin-left: 32rpx;\r\n\t}\r\n\r\n\t/* 上下文 */\r\n\r\n\r\n\t.up-post {\r\n\t\ttext-align: left;\r\n\t\twidth: 90%;\r\n\r\n\t}\r\n\r\n\t.down-post {\r\n\t\ttext-align: right;\r\n\t\tmargin-top: 16rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t/* 猜你喜欢、评论标题下的横线 */\r\n\tnavigator {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.subTitle {\r\n\t\tmargin: 0 0 20rpx;\r\n\r\n\t\tfont-weight: 500;\r\n\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.subTitle_line {\r\n\t\twidth: 100%;\r\n\t\theight: 1rpx;\r\n\t\tbackground-color: #eee;\r\n\t\tmargin-bottom: 32rpx;\r\n\t\tposition: relative;\r\n\t\toverflow: visible;\r\n\t}\r\n\r\n\t.subTitle_line::before {\r\n\t\tcontent: \"\";\r\n\t\tdisplay: block;\r\n\t\twidth: 100rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: #959595;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: -2rpx;\r\n\t}\r\n\r\n\t.subTitle_line_comment {\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t/* 猜你喜欢 */\r\n\r\n\r\n\t.relatedText {\r\n\r\n\t\toverflow: hidden;\r\n\t\tdisplay: block;\r\n\t\theight: 52rpx !important;\r\n\t\tline-height: 52rpx;\r\n\t\tpadding: 4rpx 0;\r\n\t}\r\n\r\n\t/* 点赞 */\r\n\r\n\t.likePost {\r\n\t\tposition: relative;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.likePost_title {\r\n\r\n\t\tpadding: 12px 0 6px;\r\n\t\tfont-size: 14px;\r\n\t\toverflow: hidden;\r\n\t\tjustify-content: center;\r\n\t\tfont-weight: 300;\r\n\t}\r\n\r\n\t.likePost_title .left {\r\n\t\tfloat: left;\r\n\t\theight: 30px;\r\n\t\tpadding: 0 8px;\r\n\r\n\t\tborder-radius: var(--radius);\r\n\t}\r\n\r\n\t.likePost_title .left text {\r\n\r\n\t\tline-height: 30px;\r\n\r\n\t}\r\n\r\n\t.likePost_title .right {\r\n\t\tfloat: right;\r\n\t\tline-height: 30px;\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #bbbbbb !important;\r\n\t}\r\n\r\n\t.likePost_img {\r\n\r\n\t\tmargin-top: 5px;\r\n\t\tmargin-bottom: 5px;\r\n\t\toverflow: hidden;\r\n\r\n\t}\r\n\r\n\t.likePost_img .right {\r\n\t\tfloat: right;\r\n\t\tmargin-right: 0;\r\n\r\n\t\toverflow: hidden;\r\n\t\tdisplay: block;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 12px;\r\n\t\tpadding: 0 10rpx;\r\n\t\tmin-width: 80rpx;\r\n\t\theight: 50rpx;\r\n\t\tline-height: 50rpx;\r\n\t\tmargin: 6rpx;\r\n\t\tborder-radius: var(--radius);\r\n\t\tcolor: #f7f7f7 !important;\r\n\t\tfont-weight: 300;\r\n\t}\r\n\r\n\r\n\r\n\t.like-gravatar {\r\n\t\tmargin-top: 4px;\r\n\t\theight: 50rpx;\r\n\t\twidth: 50rpx;\r\n\t\tmargin-right: -10rpx;\r\n\t\tborder-radius: 50px;\r\n\t}\r\n\r\n\t/* 评论 */\r\n\r\n\t.comment-container {\r\n\r\n\t\toverflow: visible;\r\n\t}\r\n\r\n\t.comment-num {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #959595;\r\n\t}\r\n\r\n\t.comment {\r\n\t\tmargin: 24rpx 0;\r\n\t\tpadding: 24rpx 0;\r\n\t}\r\n\r\n\t.comment-children {\r\n\t\tborder-top: 1px solid var(--ui-Border);\r\n\t\tmargin-left: 40rpx;\r\n\t}\r\n\r\n\t.comment-user {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\r\n\t\tfont-weight: 500;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.comment-username {\r\n\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.comment-gravatar {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tdisplay: block;\r\n\t\tborder-radius: 50px;\r\n\t\tbackground-color: #ccc;\r\n\t}\r\n\r\n\t.comment-user-right {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\r\n\t.replay-user {\r\n\r\n\t\tmargin: 10rpx 0;\r\n\t\tmargin-left: 86rpx;\r\n\t}\r\n\r\n\t.comment-user image {\r\n\t\tmargin-right: 16rpx;\r\n\t}\r\n\r\n\t.comment-summary {\r\n\r\n\t\tmargin-left: 86rpx;\r\n\t}\r\n\r\n\t.comment-repaly {\r\n\t\tcolor: #757575;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 1.6rem;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.comment-post {\r\n\t\tcolor: #757575;\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 1.6rem;\r\n\t\tmargin-bottom: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.comment-reply-title {\r\n\t\tfont-size: 40rpx;\r\n\t\tline-height: 1.5;\r\n\t\tcolor: #21759b;\r\n\t}\r\n\r\n\t.foot {\r\n\t\tposition: fixed;\r\n\t\tdisplay: block;\r\n\t\tz-index: 90;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tpadding: 0 24rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.foot_memu {\r\n\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\r\n\r\n\t.foot_memu .icon {\r\n\t\tdisplay: block;\r\n\r\n\t\theight: 64rpx;\r\n\t\tline-height: 64rpx;\r\n\r\n\t\tpadding: 0 0 0 24rpx;\r\n\t\tmargin: 18rpx 0 0 22rpx;\r\n\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.foot_memu .icon text {\r\n\r\n\t\tfont-size: 28px;\r\n\t}\r\n\r\n\t.foot_memu .comment {\r\n\t\tdisplay: block;\r\n\t\twidth: 340rpx;\r\n\t\theight: 64rpx;\r\n\t\tline-height: 64rpx;\r\n\t\tfont-size: 14px;\r\n\t\tpadding: 0 24rpx;\r\n\t\tmargin: 18rpx 33rpx 0 10rpx;\r\n\t\tborder-radius: 30px;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t.ui-modal-btn {\r\n\r\n\t\tpadding: 24rpx 48rpx !important;\r\n\t\ttext-align: right !important;\r\n\t}\r\n\r\n\t.comment-box {\r\n\t\twidth: 750rpx;\r\n\t\tmin-height: 100rpx;\r\n\t}\r\n\r\n\t.comment_input_box {\r\n\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.comment_btn {\r\n\r\n\t\tpadding: 12rpx 24rpx;\r\n\t\tborder-radius: 50px;\r\n\t\tfloat: right;\r\n\t\tmargin: 0 24rpx 24rpx 0;\r\n\t}\r\n\r\n\t.comment_btn_button {\r\n\t\tposition: absolute;\r\n\t\tbottom: 24rpx !important;\r\n\t\tright: 24rpx !important;\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.comment-input {\r\n\t\tmargin: 24rpx;\r\n\t\twidth: 702rpx;\r\n\t\tmin-height: 60rpx;\r\n\r\n\t\tpadding: 24rpx;\r\n\t}\r\n\r\n\r\n\t.ui-btn {\r\n\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.bottomlink {\r\n\t\tmargin-top: 48rpx;\r\n\t\twidth: 100%;\r\n\t\tpadding: 12px;\r\n\t\tborder-radius: var(--radius);\r\n\t\tbox-shadow: rgba(12, 12, 12, 0.03) 0px 2px 15px -5px;\r\n\r\n\r\n\t}\r\n\r\n\t.bottomlink .left {\r\n\r\n\t\tfloat: left;\r\n\r\n\t}\r\n\r\n\t.bottomlink .right {\r\n\r\n\t\tfloat: right;\r\n\r\n\t}\r\n\r\n\t/* tab栏更多图标 */\r\n\r\n\r\n\r\n\t.gravatarImg {\r\n\t\theight: 50rpx;\r\n\t\twidth: 50rpx;\r\n\t}\r\n\r\n\t.more-comment {\r\n\t\tfont-size: 30rpx;\r\n\t\tline-height: 1.8rem;\r\n\t\tmargin-bottom: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.img-plus {\r\n\t\twidth: 48rpx !important;\r\n\t\theight: 48rpx !important;\r\n\t\tmargin-right: 10px;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t/*  comment end  */\r\n\r\n\t.relatedNavigator {\r\n\t\tcolor: #21759b !important;\r\n\t}\r\n\r\n\t.sharedetail {\r\n\t\tposition: relative;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.share-button {\r\n\t\topacity: 0;\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tmargin: 0;\r\n\t\tpadding: 0;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.share-title {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 30rpx;\r\n\t\tpadding-top: 20rpx;\r\n\t}\r\n\r\n\t.copy-button {\r\n\t\tcolor: #296fd0 !important;\r\n\t}\r\n\r\n\t.showMessage {\r\n\t\ttext-align: center;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: sandybrown;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.ditail-copyright {\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 1.2;\r\n\t\tfont-weight: normal;\r\n\t\ttext-align: center;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 180rpx;\r\n\t}\r\n\r\n\t.wxParse-a {\r\n\t\tcolor: #21759b;\r\n\t}\r\n\r\n\t.wxParse-code {\r\n\t\tpadding: 2px 4px !important;\r\n\t\tfont-size: 90% !important;\r\n\t\tborder-radius: 3px !important;\r\n\t\tcolor: #eb2226 !important;\r\n\t\tbackground-color: #f9f2f4 !important;\r\n\t\tfont-family: Source Code Pro, Consolas, Menlo, Monaco, Courier New, monospace !important;\r\n\t}\r\n\r\n\t/*   menu-box      */\r\n\r\n\t.menu-box {\r\n\t\tposition: relative;\r\n\t\theight: 80px;\r\n\t\tpadding: 5px 0;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-bottom: -80px;\r\n\t}\r\n\r\n\t.menuBackground {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tbackground-color: transparent;\r\n\t\tz-index: 99;\r\n\t}\r\n\r\n\t.iconList {\r\n\t\ttext-align: center;\r\n\t\tborder-top: 1px solid #eee;\r\n\t}\r\n\r\n\t.iconLine {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.iconLine text {\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #959595;\r\n\t}\r\n\r\n\t.iconLine view {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.text-center {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.img-menu {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tdisplay: inline-block;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.img-menu2 {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: inline-block;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.emoji-move-in {\r\n\t\t-webkit-animation: emoji-move-in 0.3s forwards;\r\n\t\tanimation: emoji-move-in 0.3s forwards;\r\n\t}\r\n\r\n\t.emoji-move-out {\r\n\t\t-webkit-animation: emoji-move-out 0.3s forwards;\r\n\t\tanimation: emoji-move-out 0.3s forwards;\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.no-emoji-move {\r\n\t\t-webkit-animation: none;\r\n\t\tanimation: none;\r\n\t}\r\n\r\n\t@-webkit-keyframes emoji-move-in {\r\n\t\t0% {\r\n\t\t\tmargin-bottom: -80px;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes emoji-move-in {\r\n\t\t0% {\r\n\t\t\tmargin-bottom: -80px;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n\r\n\t@-webkit-keyframes emoji-move-out {\r\n\t\t0% {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tmargin-bottom: -80px;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes emoji-move-out {\r\n\t\t0% {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tmargin-bottom: -80px;\r\n\t\t}\r\n\t}\r\n\r\n\t/*   end menu-box  */\r\n\r\n\t.canvas-box {\r\n\t\tposition: fixed;\r\n\t\ttop: 999999rpx;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\r\n\t/* 音频 */\r\n\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.audioPlayer {\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 32rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.player {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.audioControls {\r\n\t\twidth: 100%;\r\n\t\theight: 184rpx;\r\n\t\tbackground: #fdfdfd;\r\n\t\tborder-radius: 4rpx;\r\n\t\tborder: 2rpx solid #eee;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.audio_left {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.audio_img {\r\n\t\twidth: 180rpx !important;\r\n\t\theight: 180rpx !important;\r\n\t\tborder-top-left-radius: 4rpx;\r\n\t\tborder-bottom-left-radius: 4rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.audio_btn {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\tz-index: 999;\r\n\t}\r\n\r\n\t.audio_btn_icon {\r\n\t\twidth: 80rpx !important;\r\n\t\theight: 80rpx !important;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.audio_right {\r\n\t\tpadding: 0 36rpx 0 16rpx;\r\n\t\twidth: 340rpx;\r\n\t\theight: 180rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.audio_title {\r\n\t\tfont-size: 15px;\r\n\t\tfont-weight: 600;\r\n\t\tline-height: 1;\r\n\t\twidth: 340rpx;\r\n\t\ttext-align: left;\r\n\t\tcolor: #333;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t}\r\n\r\n\t.audio_singer {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #959595;\r\n\t\tline-height: 1;\r\n\t\twidth: 340rpx;\r\n\t\ttext-align: left;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\tmargin: 12rpx 0 -16rpx;\r\n\t}\r\n\r\n\t.slider slider {\r\n\t\twidth: 336rpx;\r\n\t}\r\n\r\n\t.time_box {\r\n\t\twidth: 340rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tline-height: 1;\r\n\t\tcolor: #959595;\r\n\t\tfont-size: 12px;\r\n\t\tmargin-top: -20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.wxParse-audio {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t/* 音频 */\r\n\r\n\r\n\r\n\t/* 小程序广告 */\r\n\t.ad-box {\r\n\t\tmargin: 24rpx;\r\n\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\t.ad-box-video {\r\n\t\t/* padding: 40rpx 40rpx 0;*/\r\n\t\tmargin: 24rpx;\r\n\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t\tborder-radius: 12rpx;\r\n\t}\r\n\r\n\tad {\r\n\t\tz-index: 1 !important;\r\n\r\n\t}\r\n\r\n\tvideo {\r\n\t\tz-index: 1 !important;\r\n\r\n\t}\r\n\r\n\r\n\t/* “阅读更多”样式 */\r\n\r\n\t.detail-read-more {\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t}\r\n\r\n\t.detail-readmore-mask {\r\n\t\theight: 200rpx;\r\n\t\twidth: 100%;\r\n\t\tborder-radius: var(--radius);\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 80%);\r\n\t}\r\n\r\n\t.detail-readmore-btn {\r\n\t\twidth: 100%;\r\n\t\theight: 64rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tline-height: 1;\r\n\t\ttext-align: center;\r\n\t\tcolor: var(--ui-BG-Main) !important;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t}\r\n\r\n\t.detail-readmore-btn::after {\r\n\t\tcontent: \"\";\r\n\t\tdisplay: inline-block;\r\n\t\tborder: solid rgb(75, 75, 75);\r\n\t\tborder-width: 0 0 2px 2px;\r\n\t\tpadding: 3px;\r\n\t\tposition: absolute;\r\n\t\tright: 50%;\r\n\t\tbottom: 6rpx;\r\n\t\ttransform: translateX(4px) rotate(-45deg);\r\n\t\topacity: 0.6;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190060\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}