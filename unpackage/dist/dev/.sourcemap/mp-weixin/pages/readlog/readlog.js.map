{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?a309", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?1bfe", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?e488", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?f787", "uni-app:///pages/readlog/readlog.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?f81d", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/readlog/readlog.vue?4540"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pagetitle", "readLogs", "topBarItems", "id", "name", "selected", "tab", "showerror", "<PERSON><PERSON><PERSON>", "subscription", "userInfo", "is<PERSON>ogin", "avatarUrl", "nick<PERSON><PERSON>", "userLevel", "openid", "isLoginPopup", "webSiteName", "domain", "dialog", "hidden", "title", "content", "onLoad", "<PERSON><PERSON>", "onReady", "onShareAppMessage", "path", "success", "fail", "methods", "agreeGetUser", "refresh", "args", "postUpdateUserInfoRequest", "uni", "that", "icon", "duration", "onLoadTag", "refreshlive", "getliveinfo", "mask", "exit", "url", "clear", "redictDetail", "onTapTag", "fetchPostsData", "self", "count", "getMyCommentsPosts", "item", "console", "getMylikePosts", "getMyPraisePosts", "getMysubPost", "getNewComments", "closeLoginPopup", "openLoginPopup", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAm2B,CAAgB,m0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2Fv3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,UAAU,mBAAO,CAAC,4BAAoB;AAEtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAE1C,gBAAgB,mBAAO,CAAC,kCAA0B;AAElD;AACA;AACA;AAAA,eACA;EAAAC;IACA;MACAC;MACAC;MAEAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;MAAA,CACA;;MAEAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;MAEAC;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAAA;AACA;AACA;EACAC;IACA;;IAEAC;IACAA;IACA;IACA;EACA;EACAC;IACA;IACAD;EACA;EACAE;IACA;IACA;IACA;MACAL;MACAM;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACAC;IACAC;MACA;MACAP;IACA;IAEAQ;MACA;MAEA;QACA;QACA;QACAC;QACAA;QACAA;QACA;QACA;QACAC;UACA;YACA;YACAC;YACAA;YACAC;cACA1B;YACA;YACA0B;cACAtB;YACA;YACAqB;cACAd;cACAgB;cACAC;cACAV;YACA;UACA;YACAO;cACAd;cACAgB;cACAC;cACAV;YACA;UACA;QACA;MACA;QACAJ;MACA;IACA;IAEAe;MACA;MACA;;MAEA;QACA;UACArC;QACA;UACAA;QACA;MACA;MAEAkC;QACAlC;QACAI;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEAkC;MACA;MACA;MACAC;QACAN;UACAd;UACAqB;UACAL;UACAC;QACA;MACA;IACA;IAEAK;MACAnB;MACAW;QACAS;MACA;IACA;IAEAC;MACArB;IACA;IAEA;IACAsB;MACA;MACA;MACA;MACA;MAEA;QACAF;MACA;QACAA;MACA;MAEAT;QACAS;MACA;IACA;IAEAG;MACA;MACA;MACA;;MAEA;QACA;UACA7C;QACA;UACAA;QACA;MACA;MAEAkC;QACAlC;QACAI;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA0C;MAAA;MACAC;MACAA;QACA1C;QACAC;MACA;MACA;MACA;MAEA;QACA;UACA;QACA;UACAgB;UACA;QACA;MACA;MAEA;QACAyB;UACAjD;UACAC;YACAiD;YACA;UACA;QACA;QAEA;UACAD;YACAzC;UACA;QACA;MACA;QACA;UACAyC;YACAjD;YACAC;UACA;UACA;UACAkD;YACA;cACAF;gBACAhD;kBACAiD;kBACAE;kBACAA;kBACA;gBACA;cACA;cAEA;gBACAH;kBACAzC;gBACA;cACA;YACA;cACA6C;cACAJ;gBACA1C;cACA;YACA;UACA;QACA;UACA;YACA0C;cACAjD;cACAC;YACA;YACA;YACAqD;cACA;gBACAL;kBACAhD;oBACAiD;oBACAE;oBACAA;oBACAA;oBACA;kBACA;gBACA;gBAEA;kBACAH;oBACAzC;kBACA;gBACA;cACA;gBACA6C;gBACAJ;kBACA1C;gBACA;cACA;YACA;UACA;YACA;cACA0C;gBACAhD;cACA;cACA;cACAsD;gBACA;kBACAN;oBACAhD;sBACAiD;sBACAE;sBACAA;sBACAA;sBACA;oBACA;kBACA;kBAEA;oBACAH;sBACAzC;oBACA;kBACA;gBACA;kBACA6C;kBACA;oBACA9C;kBACA;gBACA;cACA;YACA;cACA;gBACA0C;kBACAjD;kBACAC;gBACA;gBACA;gBACA;gBACAuD;kBACA;oBACA;oBAEA;sBACAP;wBACAhD;0BACAiD;0BACAE;0BACAA;0BACAA;0BACA;wBACA;sBACA;oBACA;oBAEA;sBACAH;wBACAzC;sBACA;oBACA;kBACA;oBACA6C;oBACA;sBACA9C;oBACA;kBACA;gBACA;cACA;gBACA;kBACA0C;oBACAhD;kBACA;kBACA;kBACAwD;oBACA;sBACAR;wBACAhD;0BACAiD;0BACAE;0BACAA;0BACAA;0BACA;wBACA;sBACA;sBAEA;wBACAH;0BACAzC;wBACA;sBACA;oBACA;sBACA6C;sBACAJ;wBACA1C;sBACA;oBACA;kBACA;oBACA8C;oBACAJ;sBACA1C;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAmD;MACA;QACA1C;MACA;IACA;IAEA2C;MACA;QACA3C;MACA;IACA;IAEA4C;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AAAA;AAAA,2B;;;;;;;;;;;;;ACjiBA;AAAA;AAAA;AAAA;AAA4tC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACAhvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/readlog/readlog.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/readlog/readlog.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./readlog.vue?vue&type=template&id=4dc61ce4&\"\nvar renderjs\nimport script from \"./readlog.vue?vue&type=script&lang=js&\"\nexport * from \"./readlog.vue?vue&type=script&lang=js&\"\nimport style0 from \"./readlog.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/readlog/readlog.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./readlog.vue?vue&type=template&id=4dc61ce4&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.readLogs.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./readlog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./readlog.vue?vue&type=script&lang=js&\"", "<template>\r\n<block>\r\n\n \n\n\n<ui-sys :title=\"'我的'+(pagetitle)\">\n<view class=\" ui-BG-card ui-BG\">\n\n \n\t<view class=\" \">\n\t\t<view class=\"showerror\" :style=\"'display:'+(showerror)\">\n\t\t\t<text class=\" cuIcon-warn lg text-grey\" style=\"height:50px;width:50px;font-size:48px;\" @tap.stop.prevent=\"exit\"></text>\n\t\t\t<view class=\"errortext\">\n\t\t\t\t暂时无法访问网络\n\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- parse <template is=\"login-popup\" :data=\"show: isLoginPopup, userInfo: userInfo\"></template> --><block name=\"login-popup\">\r\n    <view :class=\"'zan-popup zan-popup--center login-popup '+(isLoginPopup ? 'zan-popup--show' : '')\">\r\n        <!-- 遮罩层 -->\r\n        <view class=\"zan-popup__mask\" @tap=\"closeLoginPopup\"></view>\r\n        <!-- 弹出层内容 -->\r\n        <view class=\"zan-popup__container\">\r\n            <view class=\"login-popup-wrapper ui-BG\">\r\n\t\t\t\t<!-- parse <template is=\"login\" :data=\"userInfo: userInfo\"></template> --><block name=\"login\">\r\n  <block v-if=\"(!userInfo.isLogin)\">\r\n\r\n    <view class=\"login-inner \">\r\n      <image class=\"avatar\" src=\"/static/images/gravatar.png\"></image>\r\n      <view class=\"username ui-TC-Main\">\r\n        <text>立即登录，体验更多功能</text>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"login-btn-box\">\r\n      <button class=\"login-btn ui-BG\" @tap=\"closeLoginPopup\">取消</button>\r\n      <!-- <button class=\"login-btn\" open-type=\"getUserInfo\" bindgetuserinfo=\"agreeGetUser\">确定</button> -->\r\n        <button class=\"login-btn ui-BG\" @tap=\"agreeGetUser\">确定</button>\r\n    </view>\r\n\r\n  </block>\r\n  <block v-else>\r\n    <view class=\"login-inner\">\r\n      <image class=\"avatar\" :src=\"userInfo.avatarUrl\"></image>\r\n      <view class=\"username\">\r\n        <text>{{userInfo.nickName}}</text>\r\n      </view>\r\n    </view>\r\n  </block>\r\n</block>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</block>\n\n\t\t<view class=\"log-list\">\n\t\t\t<view v-for=\"(item,index) in (readLogs)\" :key=\"item.id\">\n\t\t\t\t\n\n\n<view class=\"log-items\" :index=\"index\" :id=\"item[0]\" :data-itemtype=\"item[2]\" @tap.stop.prevent=\"redictDetail\">\n\t\t\t\t\t<text class=\"ren_mtie_xx text-lg ui-TC\"> <text :class=\"'ren_mtie'+(index + 1)\">{{index + 1}}</text> {{item[1]}}</text>\n\t\t\t\t</view>\n\n\n\n\t\t\t</view>\n\t\t\t<view class=\"log-items\" v-if=\"readLogs.length<1\">\n\t\t\t\t<text class=\"log_tips\">没有相关文章！</text>\n\t\t\t</view>\n\t\t</view>\n\n\t</view>\n</view>\n </ui-sys>\r\n</block>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * \n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n */\nimport config from '../../utils/config.js';\n\nvar Api = require('../../utils/api.js');\n\nvar util = require('../../utils/util.js');\n\nvar Auth = require('../../utils/auth.js');\n\nvar wxApi = require('../../utils/wxApi.js');\n\nvar wxRequest = require('../../utils/wxRequest.js');\n\nvar app = getApp();\nvar webSiteName = config.getWebsiteName;\nvar domain = config.getDomain;\nexport default {data() {\n            return {\n              pagetitle: '记录',\n              readLogs: [],\n\n              topBarItems: [// id name selected 选中状态\n              {\n                id: '1',\n                name: '浏览',\n                selected: true\n              }, {\n                id: '2',\n                name: '评论',\n                selected: false\n              }, {\n                id: '3',\n                name: '点赞',\n                selected: false\n              }, {\n                id: '4',\n                name: '鼓励',\n                selected: false\n              }, {\n                id: '5',\n                name: '订阅',\n                selected: false\n              } // { id: '6', name: '言论', selected: false }\n              ],\n\n              tab: '1',\n              showerror: \"none\",\n              shownodata: \"none\",\n              subscription: \"\",\n\n              userInfo: {\n                isLogin: \"\",\n                avatarUrl: \"\",\n                nickName: \"\"\n              },\n\n              userLevel: {},\n              openid: '',\n              isLoginPopup: false,\n              webSiteName: webSiteName,\n              domain: domain,\n\n              dialog: {\n                hidden: false,\n                title: \"\",\n                content: \"\"\n              }\n            };\n        },/**\n * 生命周期函数--监听页面加载\n */\nonLoad: function (options) {\n  var that = this; //self.fetchPostsData('1');\n\n  Auth.setUserInfoData(that);\n  Auth.checkLogin(that);\n  var id = options.key;\n  this.onLoadTag(id);\n}, \nonReady: function () {\n  var that = this;\n  Auth.checkSession(that, 'isLoginNow');\n}, \nonShareAppMessage: function () {\n  var title = \"分享我在“\" + config.getWebsiteName + \"浏览、评论、点赞、鼓励的文章\";\n  var path = \"pages/readlog/readlog\";\n  return {\n    title: title,\n    path: path,\n    success: function (res) {// 转发成功\n    },\n    fail: function (res) {// 转发失败\n    }\n  };\n}, \nmethods: {\n    agreeGetUser: function (e) {\n      let that = this;\n      Auth.checkAgreeGetUser(e, app, that, '0');\n    },\n\n    refresh: function (e) {\n      var that = this;\n\n      if (that.data.openid) {\n        var args = {};\n        var userInfo = e.detail.userInfo;\n        args.openid = that.data.openid;\n        args.avatarUrl = userInfo.avatarUrl;\n        args.nickname = userInfo.nickName;\n        var url = Api.getUpdateUserInfo();\n        var postUpdateUserInfoRequest = wxRequest.postRequest(url, args);\n        postUpdateUserInfoRequest.then(res => {\n          if (res.data.status == '200') {\n            var userLevel = res.data.userLevel;\n            uni.setStorageSync('userInfo', userInfo);\n            uni.setStorageSync('userLevel', userLevel);\n            that.setData({\n              userInfo: userInfo\n            });\n            that.setData({\n              userLevel: userLevel\n            });\n            uni.showToast({\n              title: res.data.message,\n              icon: 'success',\n              duration: 900,\n              success: function () {}\n            });\n          } else {\n            uni.showToast({\n              title: res.data.message,\n              icon: 'success',\n              duration: 900,\n              success: function () {}\n            });\n          }\n        });\n      } else {\n        Auth.checkSession(that, 'isLoginNow');\n      }\n    },\n\n    onLoadTag: function (tab) {\n      var that = this;\n      var topBarItems = that.data.topBarItems; // 切换topBarItem \n\n      for (var i = 0; i < topBarItems.length; i++) {\n        if (tab == topBarItems[i].id) {\n          topBarItems[i].selected = true;\n        } else {\n          topBarItems[i].selected = false;\n        }\n      }\n\n      that.setData({\n        topBarItems: topBarItems,\n        tab: tab\n      });\n\n      if (tab !== 0) {\n        this.fetchPostsData(tab);\n      } else {\n        this.fetchPostsData(\"1\");\n      }\n    },\n\n    refreshlive: function () {\n      let openid = this.data.openid;\n      var getliveinfo = wxRequest.getRequest(Api.refreshliveinfo(openid));\n      getliveinfo.then(res => {\n        uni.showToast({\n          title: res.data.message,\n          mask: false,\n          icon: \"none\",\n          duration: 3000\n        });\n      });\n    },\n\n    exit: function (e) {\n      Auth.logout(this);\n      uni.reLaunch({\n        url: '../index/index'\n      });\n    },\n\n    clear: function (e) {\n      Auth.logout(this);\n    },\n\n    // 跳转至查看文章详情\n    redictDetail: function (e) {\n      // console.log('查看文章');\n      var id = e.currentTarget.id;\n      var itemtype = e.currentTarget.dataset.itemtype;\n      var url = \"\";\n\n      if (itemtype == \"1\") {\n        url = '../list/list?categoryID=' + id;\n      } else {\n        url = '../detail/detail?id=' + id;\n      }\n\n      uni.navigateTo({\n        url: url\n      });\n    },\n\n    onTapTag: function (e) {\n      var that = this;\n      var tab = e.currentTarget.id;\n      var topBarItems = that.data.topBarItems; // 切换topBarItem \n\n      for (var i = 0; i < topBarItems.length; i++) {\n        if (tab == topBarItems[i].id) {\n          topBarItems[i].selected = true;\n        } else {\n          topBarItems[i].selected = false;\n        }\n      }\n\n      that.setData({\n        topBarItems: topBarItems,\n        tab: tab\n      });\n\n      if (tab !== 0) {\n        this.fetchPostsData(tab);\n      } else {\n        this.fetchPostsData(\"1\");\n      }\n    },\n\n    fetchPostsData: function (tab) {\n      self = this;\n      self.setData({\n        showerror: 'none',\n        shownodata: 'none'\n      });\n      var count = 0;\n      var openid = \"\";\n\n      if (tab != '1') {\n        if (self.data.openid) {\n          var openid = self.data.openid;\n        } else {\n          Auth.checkSession(self, 'isLoginNow');\n          return;\n        }\n      }\n\n      if (tab == '1') {\n        self.setData({\n          pagetitle: \"浏览\",\n          readLogs: (uni.getStorageSync('readLogs') || []).map(function (log) {\n            count++;\n            return log;\n          })\n        });\n\n        if (count == 0) {\n          self.setData({\n            shownodata: 'block'\n          });\n        }\n      } else {\n        if (tab == '2') {\n          self.setData({\n            pagetitle: \"评论\",\n            readLogs: []\n          });\n          var getMyCommentsPosts = wxRequest.getRequest(Api.getWeixinComment(openid));\n          getMyCommentsPosts.then(response => {\n            if (response.statusCode == 200) {\n              self.setData({\n                readLogs: self.data.readLogs.concat(response.data.data.map(function (item) {\n                  count++;\n                  item[0] = item.post_id;\n                  item[1] = item.post_title;\n                  return item;\n                }))\n              });\n\n              if (count == 0) {\n                self.setData({\n                  shownodata: 'block'\n                });\n              }\n            } else {\n              console.log(response);\n              self.setData({\n                showerror: 'block'\n              });\n            }\n          });\n        } else {\n          if (tab == '3') {\n            self.setData({\n              pagetitle: \"点赞\",\n              readLogs: []\n            });\n            var getMylikePosts = wxRequest.getRequest(Api.getMyLikeUrl(openid));\n            getMylikePosts.then(response => {\n              if (response.statusCode == 200) {\n                self.setData({\n                  readLogs: self.data.readLogs.concat(response.data.data.map(function (item) {\n                    count++;\n                    item[0] = item.post_id;\n                    item[1] = item.post_title;\n                    item[2] = \"0\";\n                    return item;\n                  }))\n                });\n\n                if (count == 0) {\n                  self.setData({\n                    shownodata: 'block'\n                  });\n                }\n              } else {\n                console.log(response);\n                self.setData({\n                  showerror: 'block'\n                });\n              }\n            });\n          } else {\n            if (tab == '4') {\n              self.setData({\n                readLogs: []\n              });\n              var getMyPraisePosts = wxRequest.getRequest(Api.getMyPraiseUrl(openid));\n              getMyPraisePosts.then(response => {\n                if (response.statusCode == 200) {\n                  self.setData({\n                    readLogs: self.data.readLogs.concat(response.data.data.map(function (item) {\n                      count++;\n                      item[0] = item.post_id;\n                      item[1] = item.post_title;\n                      item[2] = \"0\";\n                      return item;\n                    }))\n                  });\n\n                  if (count == 0) {\n                    self.setData({\n                      shownodata: 'block'\n                    });\n                  }\n                } else {\n                  console.log(response);\n                  this.setData({\n                    showerror: 'block'\n                  });\n                }\n              });\n            } else {\n              if (tab == '5') {\n                self.setData({\n                  pagetitle: \"订阅\",\n                  readLogs: []\n                });\n                var url = Api.getSubscription() + '?openid=' + openid;\n                var getMysubPost = wxRequest.getRequest(url);\n                getMysubPost.then(response => {\n                  if (response.statusCode == 200) {\n                    var usermetaList = response.data.usermetaList;\n\n                    if (usermetaList) {\n                      self.setData({\n                        readLogs: self.data.readLogs.concat(usermetaList.map(function (item) {\n                          count++;\n                          item[0] = item.ID;\n                          item[1] = item.post_title;\n                          item[2] = \"0\";\n                          return item;\n                        }))\n                      });\n                    }\n\n                    if (count == 0) {\n                      self.setData({\n                        shownodata: 'block'\n                      });\n                    }\n                  } else {\n                    console.log(response);\n                    this.setData({\n                      showerror: 'block'\n                    });\n                  }\n                });\n              } else {\n                if (tab == '6') {\n                  self.setData({\n                    readLogs: []\n                  });\n                  var getNewComments = wxRequest.getRequest(Api.getNewComments());\n                  getNewComments.then(response => {\n                    if (response.statusCode == 200) {\n                      self.setData({\n                        readLogs: self.data.readLogs.concat(response.data.map(function (item) {\n                          count++;\n                          item[0] = item.post;\n                          item[1] = util.removeHTML(item.content.rendered + '(' + item.author_name + ')');\n                          item[2] = \"0\";\n                          return item;\n                        }))\n                      });\n\n                      if (count == 0) {\n                        self.setData({\n                          shownodata: 'block'\n                        });\n                      }\n                    } else {\n                      console.log(response);\n                      self.setData({\n                        showerror: 'block'\n                      });\n                    }\n                  }).catch(function () {\n                    console.log(response);\n                    self.setData({\n                      showerror: 'block'\n                    });\n                  });\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n\n    closeLoginPopup() {\n      this.setData({\n        isLoginPopup: false\n      });\n    },\n\n    openLoginPopup() {\n      this.setData({\n        isLoginPopup: true\n      });\n    },\n\n    confirm: function () {\n      this.setData({\n        \"dialog.hidden\": true,\n        \"dialog.title\": '',\n        \"dialog.content\": ''\n      });\n    }\n}};\r\n</script>\r\n<style >\r\n/**app.wxss**/\n\n.readlog-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  box-sizing: border-box;\n}\n\n/*波浪*/\n \n.ren_mtie_xx {\n  width: 100%;\n  display: block;\n \n  padding: 10rpx 0;\n  /*border-bottom: 1px solid #f0f0f0;*/\n  overflow: hidden;\n}\n\n.ren_mtie_xx text {\n  float: left;\n  margin-right: 7px;\n width: 20px;\n  height: 20px;\n  line-height: 20px;\n  background: #ccc;\n  font-size: 12px;\n  color: #fff;\n  text-align: center;\n  border-radius: var(--radius);\n}\n\n.ren_mtie_xx .ren_mtie1 {\n  background: #fa6567;\n}\n\n.ren_mtie_xx .ren_mtie2 {\n  background: #ffa800;\n}\n\n.ren_mtie_xx .ren_mtie3 {\n  background: #7bbf1e;\n}\n\n.userinfo {\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  \n  height: 348rpx;\n  width: 100%;\n}\n\n.userinfo_text_box {\n  margin-left: 40rpx;\n  color: #fff;\n}\n\n.userinfo_nickname {\n  font-size: 48rpx;\n  font-weight: 800;\n  line-height: 1;\n  display: block;\n}\n\n.userinfo_des_box {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10rpx;\n}\n\n.userinfo_des {\n  display: block;\n  font-size: 28rpx;\n  font-weight: 300;\n  line-height: 1;\n}\n\n.update_btn_box {\n  margin-left: 20rpx;\n}\n\n.usertool {\n  background-color: #0081ff;\n  height: 60rpx;\n  text-align: right;\n  position: absolute;\n  top: 30rpx;\n  right: 0;\n}\n\n.usertool-img {\n  width: 40rpx;\n  height: 40rpx;\n  margin-right: 32rpx;\n  opacity: 0.6;\n}\n\n.userinfo-avatar {\n  width: 148rpx;\n  height: 148rpx;\n  margin-left: 48rpx;\n  /* border-radius: 50%; */\n}\n\n.userinfo-img {\n  width: 40rpx;\n  height: 40rpx;\n}\n\n.update_btn_box {\n  font-size: 20rpx;\n  color: #fff;\n}\n\n.update-button {\n  border-radius: 24rpx;\n  line-height: 42rpx;\n  background-color: rgba(0, 0, 0, 0.2);\n\n  padding: 0 16rpx;\n \n  font-size: 24rpx;\n  font-weight: 500;\n  color: #fff;\n}\n\n.update-button::after {\n  border: none;\n}\n\n.top-bar {\n  height: 100rpx;\n  background: #fff;\n  width: 100%;\n  z-index: 1;\n  box-shadow: 0px 4px 8px #f7f7f7;\n  display: flex;\n}\n\n.top-bar-item {\n  flex: 1;\n  display: inline-block;\n  background: #fff;\n  color: #777;\n  text-align: center;\n  line-height: 100rpx;\n  font-size: 30rpx;\n}\n\n.top-bar-item-Selected {\n  border-bottom: 1px solid #0081ff;\n  font-weight: bold;\n  color: #0081ff;\n}\n\n/* 文章列表样式 */\n\n.list_container {\n  margin: 10rpx;\n  padding: 20rpx;\n  \n}\n\n.dec {\n  margin-top: 50rpx;\n}\n\n.dec-text {\n  font-size: 25rpx;\n  color: #757575;\n}\n\n.log-list {\n  display: flex;\n  flex-direction: column;\n  /* margin-top: 5rpx; */\n}\n\n/*.log-items {\n  margin-top: 20rpx;\n}\n*/\n.log-text {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #777;\n  text-align: left;\n  word-wrap: break-word;\n  word-break: break-all;\n}\n\n.log_tips {\n  text-align: center;\n  color: #c4c4c4;\n  font-size: 30rpx;\n  display: block;\n  margin: 0 auto;\n}\n\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./readlog.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./readlog.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190103\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}