{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?8536", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?184f", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?3bb5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?604a", "uni-app:///pages/topic/topic.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?41f5", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/topic/topic.vue?dc3f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "text", "categoriesList", "floatDisplay", "webSiteName", "domain", "components", "props", "onLoad", "uni", "title", "Adapter", "onShow", "onShareAppMessage", "path", "success", "fail", "methods", "fetchCategoriesData", "self", "getCategoriesIdsRequest", "ids", "getCategoriesRequest", "item", "console", "catch", "onShareTimeline", "reloadData", "newCategoriesList", "redictIndex", "url", "confirm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAi2B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkEr3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU,mBAAO,CAAC,4BAAoB;AACtC,gBAAgB,mBAAO,CAAC,kCAA0B;AAClD;AACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EACAC;EACAC;IACAC;MACAC;IACA;IACAC;IACA;EACA;EACAC;EACAC;IACA;MACAH;MACAI;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACAC;QACAjB;MACA;;MAEA;MACAkB;QACA;QACA;QAEA;UACAC;QACA;QAEA;QACAC;UACA;YACAH;cACAhB;cACAD;gBACA;kBACAqB;gBACA;;gBAGA;cACA;YACA;UACA;YACAC;UACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA,CACAC;UACAD;QACA;MACA;IACA;IACA;IACAE;MACA;QACAhB;QACAI;MACA;IACA;IACAa;MACA;MACA;MACA;MAEA;QACA;UACAzB;UACAA;QACA;QAEA0B;MACA;MAEA;QACAT;UACAjB;QACA;MACA;IACA;IACA;IACA2B;MACA;MACA;MACA;MACA;MACApB;QACAqB;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnMA;AAAA;AAAA;AAAA;AAA0tC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACA9uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/topic/topic.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/topic/topic.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./topic.vue?vue&type=template&id=1fb07e34&\"\nvar renderjs\nimport script from \"./topic.vue?vue&type=script&lang=js&\"\nexport * from \"./topic.vue?vue&type=script&lang=js&\"\nimport style0 from \"./topic.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/topic/topic.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=template&id=1fb07e34&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n    uiNavbar: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-navbar/ui-navbar\" */ \"@/ui/components/ui-navbar/ui-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<block>\r\n\r\n\r\n\t\t<ui-sys tabbar>\r\n\t\t\t<ui-navbar :back=\"false\" isSlot>\r\n\t\t\t\t<view class=\"flex-center flex-sub\">分类</view>\r\n\t\t\t</ui-navbar>\r\n\t\t\t<view class=\"ui-grid ui-cols-2\">\r\n\t\t\t\t<view class=\"ui-item \" v-for=\"(item,index) in (categoriesList)\" :key=\"item.id\">\r\n\r\n\r\n\r\n\t\t\t\t\t<view class=\"ui-BG ui-BG-card\">\r\n\t\t\t\t\t<!-- \t<view class=\"ui-card-sub radius\" :data-subflag=\"item.subflag\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\******************=\"postsub\">\r\n\t\t\t\t\t\t\t<block v-if=\"item.subflag==0\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-toggle-o text-xs  \"> 订阅</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t<block v-if=\"item.subflag==1\">\r\n\t\t\t\t\t\t\t\t<text class=\"cicon-toggle text-xs \"> 已订阅</text>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"ui-card-bg \" :data-item=\"item.name\" :data-id=\"item.id\" @tap=\"redictIndex\"\r\n\t\t\t\t\t\t\t:style=\"'background-image:url('+(item.category_thumbnail_image)+') ;'\">\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image mode=\"aspectFill\" class=\"ui-card-image\" :src=\"item.category_thumbnail_image\"\r\n\t\t\t\t\t\t\t:data-item=\"item.name\" :data-id=\"item.id\" @tap=\"redictIndex\"></image>\r\n\t\t\t\t\t\t<view class=\"ui-card-content ui-BG radius\" :data-item=\"item.name\" :data-id=\"item.id\"\r\n\t\t\t\t\t\t\t@tap=\"redictIndex\">\r\n\t\t\t\t\t\t\t<view> <text class=\"text-lg ui-TC\">{{item.name}} </text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"ui-TC-3 text-xs \">{{item.count}}篇文章 </text>\r\n\t\t\t\t\t\t\t<text class=\"ui-TC-3 text-sm text-linecut-2 dec\">{{item.description}} </text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t \r\n\t\t\t<!-- 版权信息 -->\r\n\t\t\t<view class=\"copyright\" :style=\"'display:'+(floatDisplay)\">\r\n\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</ui-sys>\r\n\t</block>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * \n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n */\nimport config from \"../../utils/config\";\r\nvar Api = require(\"../../utils/api.js\");\r\nvar wxRequest = require(\"../../utils/wxRequest.js\");\r\nconst Adapter = require(\"../../utils/adapter.js\");\r\nvar webSiteName = config.getWebsiteName;\r\nvar domain = config.getDomain;\r\n\r\nexport default {\n  data() {\n    return {\n      text: \"Page topic\",\n      categoriesList: {},\n      floatDisplay: \"none\",\n      webSiteName: webSiteName,\n      domain: domain\n    };\n  },\n\n  components: {},\n  props: {},\n  onLoad: function (options) {\n    uni.setNavigationBarTitle({\n      title: '分类'\n    });\n    Adapter.setInterstitialAd(\"enable_topic_interstitial_ad\");\n    this.fetchCategoriesData();\n  },\n  onShow: function () {},\n  onShareAppMessage: function () {\n    return {\n      title: '分享“' + config.getWebsiteName + '”的专题栏目.',\n      path: 'pages/topic/topic',\n      success: function (res) {// 转发成功\n      },\n      fail: function (res) {// 转发失败\n      }\n    };\n  },\n  methods: {\n    //获取分类列表\n    fetchCategoriesData: function () {\n      var self = this;\n      self.setData({\n        categoriesList: []\n      }); //console.log(Api.getCategories());\n\n      var getCategoriesIdsRequest = wxRequest.getRequest(Api.getCategoriesIds());\n      getCategoriesIdsRequest.then(res => {\n        var ids = \"\";\n        var openid = self.openid;\n\n        if (!res.data.Ids == \"\") {\n          ids = res.data.Ids;\n        }\n\n        var getCategoriesRequest = wxRequest.getRequest(Api.getCategories(ids, openid));\n        getCategoriesRequest.then(response => {\n          if (response.statusCode === 200) {\n            self.setData({\n              floatDisplay: \"block\",\n              categoriesList: self.categoriesList.concat(response.data.map(function (item) {\n                if (typeof item.category_thumbnail_image == \"undefined\" || item.category_thumbnail_image == \"\") {\n                  item.category_thumbnail_image = \"/static/images/website.png\";\n                } // item.subimg = \"subscription.png\";\n\n\n                return item;\n              }))\n            });\n          } else {\n            console.log(response);\n          }\n        }) // .then(res=>{\n        //     if (self.data.openid) {                \n        //         setTimeout(function () {\n        //             self.getSubscription();\n        //         }, 500);  \n        //     }\n        // })\n        .catch(function (response) {\n          console.log(response);\n        }).finally(function () {});\n      });\n    },\n    // 自定义分享朋友圈\n    onShareTimeline: function () {\n      return {\n        title: '“' + config.getWebsiteName + '”的专题栏目',\n        path: 'pages/topic/topic'\n      };\n    },\n    reloadData: function (id, subflag, subimg) {\n      var self = this;\n      var newCategoriesList = [];\n      var categoriesList = self.categoriesList;\n\n      for (var i = 0; i < categoriesList.length; i++) {\n        if (categoriesList[i].id == id) {\n          categoriesList[i].subflag = subflag;\n          categoriesList[i].subimg = subimg;\n        }\n\n        newCategoriesList.push(categoriesList[i]);\n      }\n\n      if (newCategoriesList.length > 0) {\n        self.setData({\n          categoriesList: newCategoriesList\n        });\n      }\n    },\n    //跳转至某分类下的文章列表\n    redictIndex: function (e) {\n      //console.log('查看某类别下的文章');  \n      var id = e.currentTarget.dataset.id;\n      var name = e.currentTarget.dataset.item;\n      var url = '../list/list?categoryID=' + id;\n      uni.navigateTo({\n        url: url\n      });\n    },\n    confirm: function () {\n      this.setData({\n        'dialog.hidden': true,\n        'dialog.title': '',\n        'dialog.content': ''\n      });\n    }\n  }\n};\r\n</script>\r\n<style>\r\n\t.ui-grid {\r\n\t\tmargin: 12rpx;\r\n\t}\r\n\r\n\t.ui-BG-card {\r\n\t\tmargin: 12rpx;\r\n\t\tpadding: 0;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.ui-item {\r\n\r\n\t\ttext-align: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\r\n\t.ui-card {\r\n\t\tmargin: 12rpx !important;\r\n\t}\r\n\r\n\t.ui-card-bg {\r\n\t\theight: 160rpx;\r\n\t\tposition: relative;\r\n\t\tbackground-size: cover;\r\n\t\tbackground-position: center;\r\n\t\toverflow: hidden;\r\n\t\tfilter: saturate(160%) blur(22px);\r\n\r\n\t}\r\n\r\n\t.ui-card-image {\r\n\t\tposition: absolute;\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tdisplay: inline-block;\r\n\t\ttop: 100rpx;\r\n\t\tleft: 121.5rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 100%;\r\n\t\tborder: 1px solid #fff;\r\n\t}\r\n\r\n\t.ui-card-sub {\r\n\t\tposition: absolute;\r\n\t\tright: 10px;\r\n\t\ttop: 10px;\r\n\t\tborder: solid 1px rgba(255, 255, 255, .5);\r\n\t\tz-index: 1;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 2px 6px;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.1);\r\n\r\n\t}\r\n\r\n\t.ui-card-sub:hover {\r\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t}\r\n\r\n\t.ui-card-content {\r\n\t\tpadding: 60rpx 12rpx 24rpx;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 9;\r\n\t\theight: 236rpx;\r\n\t}\r\n\r\n\t.ui-card-content .dec {\r\n\t\tmargin-top: 10rpx;\r\n\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./topic.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190087\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}