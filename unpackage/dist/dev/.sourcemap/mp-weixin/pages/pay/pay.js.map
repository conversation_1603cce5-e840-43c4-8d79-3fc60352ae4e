{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?d5ab", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?8cfb", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?14ab", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?7bfd", "uni-app:///pages/pay/pay.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?4c5f", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/pay/pay.vue?fd4d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "prices", "openid", "postid", "total_fee", "flag", "webSiteName", "domain", "praise<PERSON>ord", "selected", "display", "onLoad", "that", "methods", "cancel", "uni", "delta", "selectItem", "totalfee", "postPraiseRequest", "orderid", "money", "form_id", "updatePraiseRequest", "console", "title", "duration", "success", "template_id", "fromUser", "icon", "complete"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+1B,CAAgB,+zBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8En3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AArBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,mBAAO,CAAC,4BAAoB;AAEtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAE1C,gBAAgB,mBAAO,CAAC,kCAA0B;AAGlD;AACA;AACA;AACA;AAAA,eACA;EAAAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAAA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACAH;IACAI;MACAV;MACAC;MACAE;MACAG;IACA;EACA;EACAK;IACAC;MACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACAC;MACA;MACA;MACA;QACAhB;QACAgB;MACA;MACA;MACAC;QACA;UACA;UACAJ;YACA;YACA;YACA;YACA;YACA;YACA;cACA;cACA;gBACAb;gBACAC;gBACAiB;gBACAC;cACA;cACA;cACAC;cACA;;cAEAC;gBACAC;cACA;gBACAT;kBACAU;kBACAC;kBACAC;oBACA3B;sBACAE;sBACAC;sBACAyB;sBACAN;sBACAlB;sBACAC;sBACAwB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;cACA;YACA;;YACA;cACAd;gBACAU;gBACAK;cACA;YACA;YACAC;cACA;gBACAhB;kBACAU;kBACAK;gBACA;cACA;YACA;UACA;QACA;UACAN;QACA;MACA;IACA;EACA;AAAA;AAAA,2B;;;;;;;;;;;;;AC9MA;AAAA;AAAA;AAAA;AAAwtC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACA5uC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pay/pay.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pay/pay.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pay.vue?vue&type=template&id=c10d0c50&\"\nvar renderjs\nimport script from \"./pay.vue?vue&type=script&lang=js&\"\nexport * from \"./pay.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pay.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pay/pay.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=template&id=c10d0c50&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=script&lang=js&\"", "<template>\r\n<!--\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\n *\n */-->\n\n\n\n\n<view class=\"container\">\n  <!-- parse <template is=\"tempHeader\"/> --><block name=\"tempHeader\">\r\n  <view class=\"header\">\r\n    <image src=\"/static/images/logo700.png\" style=\"height:200rpx;width:400rpx\"></image>\r\n    <view style=\"margin-top:40rpx\">\r\n      守望生活, 审视自我,宁静雅致的小轩馆.\r\n    </view>\r\n  </view>\r\n</block> \n\n  <view class=\"title\">请选{{praiseWord}}的金额</view>\n\n  <view class=\"price-list\">\n    <view class=\"item\" :data-item=\"item\" @tap=\"selectItem\" v-for=\"(item,index) in (prices)\" :key=\"index\">\n      \n\n\n<view :class=\"'price-box '+(selected==item?'active':'')\">\n        <em><text class=\"price\">{{item}}</text>  元</em> \n      </view>\n\n\n\n    </view>\n  </view>\n\n <view style=\"text-align: center\">\n  <button class=\"cancel-button;margin-bottom: 40rpx\" formType=\"submit\" size=\"mini\" @tap=\"cancel\">取消</button>\n </view>\n\n  <view class=\"ditail-copyright\" :style=\"'display:'+(display)\">\n     <!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> --><block name=\"tempCopyright\">\r\n<view style=\"margin-top: 24rpx;\">  © {{webSiteName}} {{domain}} </view>\r\n</block>\n  </view>\n\n</view>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n * \n */\nvar Api = require('../../utils/api.js');\n\nvar util = require('../../utils/util.js');\n\nvar auth = require('../../utils/auth.js');\n\nvar wxApi = require('../../utils/wxApi.js');\n\nvar wxRequest = require('../../utils/wxRequest.js');\n\nimport config from '../../utils/config.js';\nvar webSiteName = config.getWebsiteName;\nvar domain = config.getDomain;\nvar app = getApp();\nvar praiseWord = \"鼓励\";\nexport default {data() {\n            return {\n              prices: [6, 8, 18, 66, 88, 188],\n              openid: '',\n              postid: '',\n              total_fee: '',\n              flag: '1',\n              webSiteName: webSiteName,\n              domain: domain,\n              praiseWord: \"\",\n              selected: \"\",\n              display: \"\"\n            };\n        },/**\n * 进入页面\n */\nonLoad: function (options) {\n  var that = this;\n  var openid = options.openid;\n  var postid = options.postid;\n  var flag = options.flag;\n  praiseWord = options.praiseWord;\n  that.setData({\n    openid: openid,\n    postid: postid,\n    flag: flag,\n    praiseWord: praiseWord\n  });\n}, \nmethods: {\n    cancel: function () {\n      uni.navigateBack({\n        delta: 1\n      });\n    },\n\n    /**\n     * 选中鼓励金额\n     */\n    selectItem: function (event) {\n      var totalfee = event.currentTarget.dataset.item;\n      var money = totalfee;\n      totalfee = totalfee;\n      var that = this;\n      var url = Api.postPraiseUrl();\n      var data = {\n        openid: that.data.openid,\n        totalfee: totalfee\n      };\n      var postPraiseRequest = wxRequest.postRequest(url, data);\n      postPraiseRequest.then(response => {\n        if (response.data) {\n          var temp = response.data;\n          uni.requestPayment({\n            'timeStamp': response.data.timeStamp,\n            'nonceStr': response.data.nonceStr,\n            'package': response.data.package,\n            'signType': 'MD5',\n            'paySign': response.data.paySign,\n            'success': function (res) {\n              var url = Api.updatePraiseUrl();\n              var data = {\n                openid: that.data.openid,\n                postid: that.data.postid,\n                orderid: response.data.nonceStr,\n                money: totalfee\n              };\n              var form_id = response.data.package;\n              form_id = form_id.substring(10);\n              var updatePraiseRequest = wxRequest.postRequest(url, data); //更新鼓励数据\n\n              updatePraiseRequest.then(response => {\n                console.log(response.data.message);\n              }).then(res => {\n                uni.showToast({\n                  title: '谢谢' + praiseWord + '！',\n                  duration: 2000,\n                  success: function () {\n                    data = {\n                      openid: that.data.openid,\n                      postid: that.data.postid,\n                      template_id: that.data.template_id,\n                      form_id: form_id,\n                      total_fee: money,\n                      flag: that.data.flag,\n                      fromUser: \"None\"\n                    }; // url = Api.sendMessagesUrl();\n                    // var sendMessageRequest = wxRequest.postRequest(url, data);\n                    // sendMessageRequest.then(response => {\n                    //     if (response.data.status == '200') {\n                    //         console.log(response.data.message);\n                    //         wx.navigateBack({\n                    //             delta: 1\n                    //         })\n                    //     }\n                    //     else {\n                    //         console.log(response.data.message);\n                    //     }\n                    // });\n                  }\n                });\n              });\n            },\n            'fail': function (res) {\n              uni.showToast({\n                title: res.errMsg,\n                icon: 'success'\n              });\n            },\n            complete: function (res) {\n              if (res.errMsg == 'requestPayment:fail cancel') {\n                uni.showToast({\n                  title: '取消' + praiseWord,\n                  icon: 'success'\n                });\n              }\n            }\n          });\n        } else {\n          console.log(response.data.message);\n        }\n      });\n    }\n}};\r\n</script>\r\n<style >\r\n\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\n * \n */\n\n.container {\n  width: 100%;\n  margin-bottom: 10rpx;\n  padding:0 0rpx\n}\n\n\n.title\n{\n  text-align: center;\n  margin-bottom: 50rpx;\n}\n\n.price-list {\n  padding: 0 15rpx;\n  text-align: center;\n  flex-direction: row;\n}\n\n.price-list .item {\n  float: left;\n  margin-right: 10rpx;\n  margin-left: 10rpx;\n  margin-bottom: 20rpx;\n  width: 220rpx;\n}\n\n.price-list .price-box {\n  width: 100%;\n  height: 100rpx;\n  border-radius: 20rpx;\n  border: 1rpx solid #3366CC;\n  text-align: center;\n}\n\n.price-list .price-box em {\n  line-height: 100rpx;\n  color: #d55a4a;\n  font-size: 30rpx;\n}\n\n.price-list .price-box .price {\n  font-size: 50rpx;\n  display: inline-block;\n}\n\n.price-list .price-box.active {\n  background-color: #3366CC;\n}\n\n.price-list .price-box.active em {\n  color: #fff;\n  background-color: #3366CC;\n}\n\n\n.cancel-button {\n  font-size: 0.785714286rem;\n  font-weight: normal;\n  color:  #3366CC;\n  background-color: #fff;\n  background-repeat: repeat-x;\n  margin-top: 30rpx;\n  width: 300rpx;\n}\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pay.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190084\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}