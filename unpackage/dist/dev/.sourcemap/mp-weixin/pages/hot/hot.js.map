{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/hot/hot.vue?6b71", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/hot/hot.vue?23dc", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/hot/hot.vue?719c", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/hot/hot.vue?dd6f", "uni-app:///pages/hot/hot.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "title", "postsList", "pagesList", "categoriesList", "postsShowSwiperList", "isLastPage", "page", "search", "categories", "categoriesName", "categoriesImage", "showerror", "isCategoryPage", "isSearchPage", "showallDisplay", "displaySwiper", "floatDisplay", "search<PERSON>ey", "topBarItems", "id", "name", "selected", "tab", "webSiteName", "domain", "components", "props", "onShareAppMessage", "path", "success", "fail", "onLoad", "Adapter", "methods", "formSubmit", "url", "uni", "onShareTimeline", "reload", "self", "onTapTag", "fetchPostsData", "mask", "getTopHotPostsRequest", "item", "console", "setTimeout", "redictDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;;;AAGlD;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA+1B,CAAgB,+zBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwGn3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,mBAAO,CAAC,4BAAoB;AACtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAC1C,gBAAgB,mBAAO,CAAC,kCAA0B;AAClD;AACA;AACA;AAAA,eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EACAC;EACAC;IACA;IACA;IACA;MACA3B;MACA4B;MACAC;MAAA,CACA;MACAC;MAAA;IAEA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACAC;MACA;MAEA;QACAC;MACA;MAEAC;QACAD;MACA;IACA;IACA;IACAE;MACA;QACArC;QACA4B;MACA;IACA;IACAU;MACA;MACAC;IACA;IACAC;MACA;MACA;MACA;;MAEA;QACA;UACAtB;QACA;UACAA;QACA;MACA;MAEAqB;QACArB;QACAI;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IACA;IACAmB;MACA;MACAF;QACAtC;MACA;MACAmC;QACApC;QACA0C;MACA;MACA;MACAC;QACA;UACAJ;YACAzB;YACAE;YACAf;cACA;cAEA;gBACA2C;cACA;cAEAA;cACA;YACA;UACA;QACA;UACAC;QACA;MACA;QACAT;QACAG;UACA5B;UACAK;QACA;MACA;QACA8B;UACAV;QACA;MACA;IACA;IACA;IACAW;MACA;MACA;QACAZ;MACAC;QACAD;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/hot/hot.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/hot/hot.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./hot.vue?vue&type=template&id=41251cbc&\"\nvar renderjs\nimport script from \"./hot.vue?vue&type=script&lang=js&\"\nexport * from \"./hot.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/hot/hot.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hot.vue?vue&type=template&id=41251cbc&\"", "var components\ntry {\n  components = {\n    uiSys: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-sys/ui-sys\" */ \"@/ui/components/ui-sys/ui-sys.vue\"\n      )\n    },\n    uiNavbar: function () {\n      return import(\n        /* webpackChunkName: \"ui/components/ui-navbar/ui-navbar\" */ \"@/ui/components/ui-navbar/ui-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hot.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./hot.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<block>\r\n\t\t<!--\r\n/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n *\r\n */-->\r\n\r\n\r\n\r\n\t\t<ui-sys tabbar>\r\n\t\t\t<ui-navbar :back=\"false\" isSlot>\r\n\t\t\t\t<view class=\"flex-center flex-sub\">排行榜</view>\r\n\t\t\t</ui-navbar>\r\n\t\t\t<!-- <view class=\"tab-bar ui-BG\">\r\n\t\t\t\t<block v-for=\"(item,index) in topBarItems\" :key=\"item.id\">\r\n\t\t\t\t\t<view :class=\"'tab-item '+(item.selected ? 'tab-Selected': '')\" @tap.stop.prevent=\"onTapTag\"\r\n\t\t\t\t\t\t:id=\"item.id\">{{item.name}}</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view> -->\r\n\r\n\t\t\t<view class=\"container\">\r\n\t\t\t\t<view class=\"showerror\" :style=\"'display:'+(showerror)\">\r\n\t\t\t\t\t<text class=\"cicon-wifi-off text-xsl\"></text>\r\n\r\n\t\t\t\t\t<view class=\"errortext\">\r\n\t\t\t\t\t\t暂时无法访问网络\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t<button class=\"more-button\" @tap=\"reload\" size=\"mini\">重新加载</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view :style=\"'display:'+(showallDisplay)\">\r\n\r\n\t\t\t\t\t<view class=\"post-list\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in (postsList)\" :key=\"item.id\">\r\n\r\n\r\n\r\n\t\t\t\t\t\t\t<view class=\"post-item ui-BG ui-BG-card\" :index=\"index\" :id=\"item.post_id\"\r\n\t\t\t\t\t\t\t\t@tap=\"redictDetail\">\r\n\r\n\t\t\t\t\t\t\t\t<view class=\"post-desc\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"post-title text-lg ui-TC\">\r\n\t\t\t\t\t\t\t\t\t\t<text>{{item.post_title}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"post-data ui-TC-3 text-xs\">\r\n\r\n\t\t\t\t\t\t\t\t\t\t<text>{{item.post_date}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-popover-o  \"> {{item.comment_total}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-favorite-o \"> {{item.like_count}}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"cicon-eye-o \"> {{item.pageviews}}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t<image :src=\"item.post_thumbnail_image\" mode=\"aspectFill\" class=\"post-img\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"copyright\" :style=\"'display:'+(floatDisplay)\">\r\n\t\t\t\t\t<!-- parse <template is=\"tempCopyright\" :data=\"webSiteName:webSiteName,domain:domain\"/> -->\r\n\t\t\t\t\t<block name=\"tempCopyright\">\r\n\t\t\t\t\t\t<view style=\"margin-top: 24rpx;\"> © {{webSiteName}} {{domain}} </view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</ui-sys>\r\n\t</block>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n * \n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n */\nvar Api = require(\"../../utils/api.js\");\r\nvar util = require(\"../../utils/util.js\");\r\n\r\nvar wxApi = require(\"../../utils/wxApi.js\");\r\nvar wxRequest = require(\"../../utils/wxRequest.js\");\r\nconst Adapter = require(\"../../utils/adapter.js\");\r\nvar webSiteName = config.getWebsiteName;\r\nvar domain = config.getDomain;\r\nimport config from \"../../utils/config\";\r\n\r\nexport default {\n  data() {\n    return {\n      title: '文章列表',\n      postsList: {},\n      pagesList: {},\n      categoriesList: {},\n      postsShowSwiperList: {},\n      isLastPage: false,\n      page: 1,\n      search: '',\n      categories: 0,\n      categoriesName: '',\n      categoriesImage: \"\",\n      showerror: \"none\",\n      isCategoryPage: \"none\",\n      isSearchPage: \"none\",\n      showallDisplay: \"block\",\n      displaySwiper: \"block\",\n      floatDisplay: \"none\",\n      searchKey: \"\",\n      topBarItems: [// id name selected 选中状态\n      {\n        id: '2',\n        name: '浏览数',\n        selected: true\n      }],\n      tab: '2',\n      webSiteName: webSiteName,\n      domain: domain\n    };\n  },\n\n  components: {},\n  props: {},\n  onShareAppMessage: function () {\n    var title = \"分享“\" + webSiteName + \"”的文章排行。\";\n    var path = \"pages/hot/hot\";\n    return {\n      title: title,\n      path: path,\n      success: function (res) {// 转发成功\n      },\n      fail: function (res) {// 转发失败\n      }\n    };\n  },\n  onLoad: function (options) {\n    this.fetchPostsData(\"2\");\n    Adapter.setInterstitialAd(\"enable_hot_interstitial_ad\");\n  },\n  methods: {\n    formSubmit: function (e) {\n      var url = '../list/list';\n\n      if (e.detail.value.input != '') {\n        url = url + '?search=' + e.detail.value.input;\n      }\n\n      uni.navigateTo({\n        url: url\n      });\n    },\n    // 自定义分享朋友圈\n    onShareTimeline: function () {\n      return {\n        title: \"“\" + webSiteName + \"”的文章排行\",\n        path: 'pages/hot/hot'\n      };\n    },\n    reload: function (e) {\n      var self = this;\n      self.fetchPostsData(self);\n    },\n    onTapTag: function (e) {\n      var self = this;\n      var tab = e.currentTarget.id;\n      var topBarItems = self.topBarItems; // 切换topBarItem \n\n      for (var i = 0; i < topBarItems.length; i++) {\n        if (tab == topBarItems[i].id) {\n          topBarItems[i].selected = true;\n        } else {\n          topBarItems[i].selected = false;\n        }\n      }\n\n      self.setData({\n        topBarItems: topBarItems,\n        tab: tab\n      });\n\n      if (tab !== 0) {\n        this.fetchPostsData(tab);\n      } else {\n        this.fetchPostsData(\"2\");\n      }\n    },\n    //获取文章列表数据\n    fetchPostsData: function (tab) {\n      var self = this;\n      self.setData({\n        postsList: []\n      });\n      uni.showLoading({\n        title: '正在加载',\n        mask: true\n      });\n      var getTopHotPostsRequest = wxRequest.getRequest(Api.getTopHotPosts(tab));\n      getTopHotPostsRequest.then(response => {\n        if (response.statusCode === 200) {\n          self.setData({\n            showallDisplay: \"block\",\n            floatDisplay: \"block\",\n            postsList: self.postsList.concat(response.data.map(function (item) {\n              var strdate = item.post_date;\n\n              if (item.post_thumbnail_image == null || item.post_thumbnail_image == '') {\n                item.post_thumbnail_image = \"/static/images/logo700.png\";\n              }\n\n              item.post_date = util.cutstr(strdate, 10, 1);\n              return item;\n            }))\n          });\n        } else if (response.statusCode === 404) {\n          console.log('加载数据失败,可能缺少相应的数据');\n        }\n      }).catch(function () {\n        uni.hideLoading();\n        self.setData({\n          showerror: \"block\",\n          floatDisplay: \"block\"\n        });\n      }).finally(function () {\n        setTimeout(function () {\n          uni.hideLoading();\n        }, 1500);\n      });\n    },\n    // 跳转至查看文章详情\n    redictDetail: function (e) {\n      // console.log('查看文章');\n      var id = e.currentTarget.id,\n          url = '../detail/detail?id=' + id;\n      uni.navigateTo({\n        url: url\n      });\n    }\n  }\n};\r\n</script>\r\n\t/*\r\n * \r\n * 微慕小程序开源版\r\n * author: jianbo\r\n * organization: 微慕  www.minapper.com\r\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\r\n * 技术支持微信号：iamxjb\r\n * 开源协议：MIT\r\n * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.\r\n */\r\n\r\n\r\n\r\n\t.list-header {\r\n\t\tpadding: 50rpx 0;\r\n\t\ttext-align: left;\r\n\t\tfont-weight: normal;\r\n\t\tfont-size: 40rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tcolor: #21759b;\r\n\t\tborder-bottom: 2rpx solid #ededed;\r\n\t\tmargin-bottom: 48rpx;\r\n\t}\r\n\r\n\t/*topic list*/\r\n\r\n\t.topic-common-list {\r\n\t\tpadding: 30rpx 30rpx;\r\n\t}\r\n\r\n\t.topic-list-item {\r\n\t\tposition: relative;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\toverflow: hidden;\r\n\t\tclear: both;\r\n\t\tborder: 1px solid #ccc;\r\n\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\r\n\t.topic-list-item image.cover {\r\n\t\twidth: 100%;\r\n\t\theight: 300rpx;\r\n\t}\r\n\r\n\t.topic-content-title {\r\n\t\tpadding: 10rpx 30rpx;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.topic-content-title text {\r\n\t\tfont-size: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #121b23;\r\n\t}\r\n\r\n\t.topic-content-brief {\r\n\t\tpadding: 0rpx 30rpx 30rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 40rpx;\r\n\t\tfont-weight: 400;\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\tcolor: #989ebb;\r\n\t}\r\n\r\n\t.searchKey {\r\n\t\tcolor: #121b23;\r\n\t}\r\n\r\n\t/* tab选项卡 */\r\n\r\n\t.tab-bar {\r\n\t\theight: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.tab-item {\r\n\t\tflex: 1;\r\n\t\tdisplay: inline-block;\r\n\r\n\t\ttext-align: center;\r\n\t\tline-height: 100rpx;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t/* 选中样式 */\r\n\r\n\t.tab-Selected {\r\n\t\tposition: relative;\r\n\t\tcolor: var(--ui-BG-Main) !important;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.tab-Selected::after {\r\n\t\tcontent: '';\r\n\t\theight: 4rpx;\r\n\t\twidth: 40rpx;\r\n\t\tbackground-color: var(--ui-BG-Main) !important;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\tbottom: 0;\r\n\t\tmargin-left: -20rpx;\r\n\t}\r\n</style>\n"], "sourceRoot": ""}