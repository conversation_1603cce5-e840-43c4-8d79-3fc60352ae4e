{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?2d09", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?b39b", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?6af9", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?f59f", "uni-app:///pages/webpage/webpage.vue", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?3606", "webpack:////Users/<USER>/Documents/HBuilderProjects/WordPress/pages/webpage/webpage.vue?ac1f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "url", "title", "onLoad", "console", "that", "onShareAppMessage", "path", "success", "fail", "methods"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACsN;AACtN,gBAAgB,uNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAm2B,CAAgB,m0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8Bv3B;;;;;;;;;;;;;;;;;;;;AAXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,UAAU,mBAAO,CAAC,4BAAoB;AAEtC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,WAAW,mBAAO,CAAC,6BAAqB;AAExC,YAAY,mBAAO,CAAC,8BAAsB;AAE1C,gBAAgB,mBAAO,CAAC,kCAA0B;AAAlD,eAEA;EAAAC;IACA;MACAC;MACAC;IACA;EACA;EAAA;AACA;AACA;EACAC;IACA;;IAEAC;IAEA;MACA;MAEA;QACAH;MACA;MAEAI;QACAJ;MACA;IACA;MACAI;QACAJ;MACA;IACA;EACA;EACAK;IACA;IACA;IAEA;MACAL;IACA;IAEA;MACAA;IACA;IAEAA;IACAG;IACA;MACAF;MACAK;MACAC;QACA;QACAJ;MACA;MACAK;MAAA;IAEA;EACA;EACAC;AAAA;AAAA,2B;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAA4tC,CAAgB,4oCAAG,EAAC,C;;;;;;;;;;;ACAhvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/webpage/webpage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/webpage/webpage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./webpage.vue?vue&type=template&id=74dae6ce&\"\nvar renderjs\nimport script from \"./webpage.vue?vue&type=script&lang=js&\"\nexport * from \"./webpage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./webpage.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/webpage/webpage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webpage.vue?vue&type=template&id=74dae6ce&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webpage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webpage.vue?vue&type=script&lang=js&\"", "<template>\r\n<block>\r\n<!--/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n * \n */-->\n<web-view :src=\"url\"></web-view>\n\r\n</block>\r\n</template>\r\n\r\n<script>\r\n/*\n * \n * 微慕小程序开源版\n * author: jianbo\n * organization: 微慕  www.minapper.com\n * github:    https://github.com/iamxjb/winxin-app-watch-life.net\n * 技术支持微信号：iamxjb\n * 开源协议：MIT\n *  *Copyright (c) 2017 https://www.minapper.com All rights reserved.\n * \n */\nimport config from '../../utils/config.js';\n\nvar Api = require('../../utils/api.js');\n\nvar util = require('../../utils/util.js');\n\nvar auth = require('../../utils/auth.js');\n\nvar wxApi = require('../../utils/wxApi.js');\n\nvar wxRequest = require('../../utils/wxRequest.js');\n\nexport default {data() {\n            return {\n  url: null,\n  title: \"\"\n};\n        },/**\n * 生命周期函数--监听页面加载\n */\nonLoad: function (options) {\n  var that = this; //console.log(decodeURIComponent(options.url));\n\n  console.log(options);\n\n  if (options.url != null) {\n    var url = decodeURIComponent(options.url);\n\n    if (url.indexOf('*') != -1) {\n      url = url.replace(\"*\", \"?\");\n    }\n\n    that.setData({\n      url: url\n    });\n  } else {\n    that.setData({\n      url: 'https://' + config.getDomain\n    });\n  }\n}, \nonShareAppMessage: function (options) {\n  var that = this;\n  var url = options.webViewUrl;\n\n  if (url.indexOf(\"mp.weixin.qq.com\") != -1) {\n    url = that.data.url;\n  }\n\n  if (url.indexOf(\"?\") != -1) {\n    url = url.replace(\"?\", \"*\");\n  }\n\n  url = 'pages/webpage/webpage?url=' + url;\n  console.log(url);\n  return {\n    title: '分享\"' + config.getWebsiteName + '\"的文章' + that.data.title,\n    path: url,\n    success: function (res) {\n      // 转发成功\n      console.log(url);\n    },\n    fail: function (res) {// 转发失败\n    }\n  };\n}, \nmethods: {}};\r\n</script>\r\n<style >\r\n/* pages/webpage.wxss */\r\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webpage.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webpage.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1731736190098\n      var cssReload = require(\"/Applications/HBuilderX-Alpha.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}