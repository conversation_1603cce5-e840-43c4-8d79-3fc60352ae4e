@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*每个页面公共css */
.theme-darkblue {
  --ui-BG:                        #111111;
  --ui-BG-1:                      #181818;
  --ui-BG-2:                      #1b1b1b;
  --ui-BG-3:                      #222222;
  --ui-BG-4:                      #282828;
  --ui-TC:                       	#ffffff;
  --ui-TC-1:                      #d4d4d4;
  --ui-TC-2:                      #919191;
  --ui-TC-3:                      #6a6a6a;
  --ui-TC-4:                      #474747;
  --ui-Blur:                    	rgba(17, 17, 17, 0.85);
  --ui-Blur-1:                	rgba(24, 24, 24, 0.85);
  --ui-Blur-2:                	rgba(27, 27, 27, 0.85);
  --ui-Blur-3:                	rgba(34, 34, 34, 0.85);
  --ui-Border:           		   	#303030;
  --ui-Outline:                   rgba(255,255,255,0.1);
  --ui-Line:                      rgba(119,119,119,0.25);
}
.theme-lightblue {
  --ui-BG:                        #ffffff;
  --ui-BG-1:                      #f6f6f6;
  --ui-BG-2:                      #f1f1f1;
  --ui-BG-3:                      #e8e8e8;
  --ui-BG-4:                      #e0e0e0;
  --ui-TC:                    	#303030;
  --ui-TC-1:                    	#525252;
  --ui-TC-2:                    	#777777;
  --ui-TC-3:                    	#9e9e9e;
  --ui-TC-4:                    	#c6c6c6;
  --ui-Blur:                    	rgba(255, 255, 255, 0.85);
  --ui-Blur-1:                	rgba(246, 246, 246, 0.85);
  --ui-Blur-2:                	rgba(241,241,241,0.85);
  --ui-Blur-3:                	rgba(232,232,232,0.85);
  --ui-Border:           		   	#d4d4d4;
  --ui-Outline:                   rgba(0,0,0,0.1);
  --ui-Line:                      rgba(119,119,119,0.25);
}
.theme-light {
  --ui-BG:                        #ffffff;
  --ui-BG-1:                      #f6f6f6;
  --ui-BG-2:                      #f1f1f1;
  --ui-BG-3:                      #e8e8e8;
  --ui-BG-4:                      #e0e0e0;
  --ui-TC:                    	#303030;
  --ui-TC-1:                    	#525252;
  --ui-TC-2:                    	#777777;
  --ui-TC-3:                    	#9e9e9e;
  --ui-TC-4:                    	#c6c6c6;
  --ui-Blur:                    	rgba(255, 255, 255, 0.98);
  --ui-Blur-1:                	rgba(255, 255, 255, 0.75);
  --ui-Blur-2:                	rgba(255,255,255,0.25);
  --ui-Blur-3:                	rgba(255,255,255,0.05);
  --ui-Border:           		   	rgba(119,119,119,0.25);
  --ui-Outline:                   rgba(0,0,0,0.1);
  --ui-Line:                      rgba(119,119,119,0.25);
  --ui-Shadow:           		   	0 .5em 1em rgba(0, 0, 0, 0.15);
  --ui-Shadow-sm:                 0 .125em .25em rgba(0, 0, 0, 0.075);
  --ui-Shadow-lg:                 0 1em 3em rgba(0, 0, 0, 0.175);
  --ui-Shadow-inset:              inset 0 .1em .2em rgba(0, 0, 0, 0.075);
  --ui-Shadow-opacity:           		0.45;
  --ui-Shadow-opacity-sm:             0.075;
  --ui-Shadow-opacity-lg:          	0.65;
  --ui-BG-opacity:    			.1;
}
.theme-dark {
  --ui-BG:                        #393939;
  --ui-BG-1:                      #333333;
  --ui-BG-2:                      #2c2c2c;
  --ui-BG-3:                      #292929;
  --ui-BG-4:                      #222222;
  --ui-TC:                       	#ffffff;
  --ui-TC-1:                      #d4d4d4;
  --ui-TC-2:                      #919191;
  --ui-TC-3:                      #6a6a6a;
  --ui-TC-4:                      #474747;
  --ui-Blur:                    	rgba(38, 38, 38, 0.98);
  --ui-Blur-1:                	rgba(38, 38, 38, 0.75);
  --ui-Blur-2:                	rgba(38, 38, 38, 0.25);
  --ui-Blur-3:                	rgba(38, 38, 38, 0.05);
  --ui-Border:           		   	rgba(119, 119, 119, 0.25);
  --ui-Outline:                   rgba(255,255,255,0.1);
  --ui-Line:                      rgba(119,119,119,0.25);
  --ui-Shadow:           		   	0 .5em 1em rgba(0, 0, 0, 0.45);
  --ui-Shadow-sm:                 0 .125em .25em rgba(0, 0, 0, 0.475);
  --ui-Shadow-lg:                 0 1em 3em rgba(0, 0, 0, 0.475);
  --ui-Shadow-inset:              inset 0 1px 2px rgba(0, 0, 0, 0.475);
  --ui-Shadow-opacity:           		0.55;
  --ui-Shadow-opacity-sm:             0.175;
  --ui-Shadow-opacity-lg:          	0.75;
  --ui-BG-opacity:    			.1;
}
.theme-auto {
  --ui-BG:                        #ffffff;
  --ui-BG-1:                      #f6f6f6;
  --ui-BG-2:                      #f1f1f1;
  --ui-BG-3:                      #e8e8e8;
  --ui-BG-4:                      #e0e0e0;
  --ui-TC:                    	#303030;
  --ui-TC-1:                    	#525252;
  --ui-TC-2:                    	#777777;
  --ui-TC-3:                    	#9e9e9e;
  --ui-TC-4:                    	#c6c6c6;
  --ui-Blur:                    	rgba(255, 255, 255, 0.98);
  --ui-Blur-1:                	rgba(255, 255, 255, 0.75);
  --ui-Blur-2:                	rgba(255,255,255,0.25);
  --ui-Blur-3:                	rgba(255,255,255,0.05);
  --ui-Border:           		   	rgba(119,119,119,0.25);
  --ui-Outline:                   rgba(0,0,0,0.1);
  --ui-Line:                      rgba(119,119,119,0.25);
  --ui-Shadow:           		   	0 .5em 1em rgba(0, 0, 0, 0.15);
  --ui-Shadow-sm:                 0 .125em .25em rgba(0, 0, 0, 0.075);
  --ui-Shadow-lg:                 0 1em 3em rgba(0, 0, 0, 0.175);
  --ui-Shadow-inset:              inset 0 .1em .2em rgba(0, 0, 0, 0.075);
  --ui-Shadow-opacity:           		0.45;
  --ui-Shadow-opacity-sm:             0.075;
  --ui-Shadow-opacity-lg:          	0.65;
  --ui-BG-opacity:    			.1;
}
@media (prefers-color-scheme: dark) {
.theme-auto {
    --ui-BG:                        #393939;
    --ui-BG-1:                      #333333;
    --ui-BG-2:                      #2c2c2c;
    --ui-BG-3:                      #292929;
    --ui-BG-4:                      #222222;
    --ui-TC:                       	#ffffff;
    --ui-TC-1:                      #d4d4d4;
    --ui-TC-2:                      #919191;
    --ui-TC-3:                      #6a6a6a;
    --ui-TC-4:                      #474747;
    --ui-Blur:                    	rgba(38, 38, 38, 0.98);
    --ui-Blur-1:                	rgba(38, 38, 38, 0.75);
    --ui-Blur-2:                	rgba(38, 38, 38, 0.25);
    --ui-Blur-3:                	rgba(38, 38, 38, 0.05);
    --ui-Border:           		   	rgba(119, 119, 119, 0.25);
    --ui-Outline:                   rgba(255,255,255,0.1);
    --ui-Line:                      rgba(119,119,119,0.25);
    --ui-Shadow:           		   	0 .5em 1em rgba(0, 0, 0, 0.45);
    --ui-Shadow-sm:                 0 .125em .25em rgba(0, 0, 0, 0.475);
    --ui-Shadow-lg:                 0 1em 3em rgba(0, 0, 0, 0.475);
    --ui-Shadow-inset:              inset 0 1px 2px rgba(0, 0, 0, 0.475);
    --ui-Shadow-opacity:           		0.55;
    --ui-Shadow-opacity-sm:             0.175;
    --ui-Shadow-opacity-lg:          	0.75;
    --ui-BG-opacity:    			.1;
}
}
.ui-BG {
  background-color: var(--ui-BG) !important;
  color: var(--ui-TC);
}
.ui-TC {
  color: var(--ui-TC) !important;
}
.ui-BG-Main {
  background-color: var(--ui-BG-Main) !important;
  color: var(--ui-BG-Main-TC) !important;
}
.ui-TC-Main {
  color: var(--ui-BG-Main) !important;
}
.ui-BG-1 {
  background-color: var(--ui-BG-1) !important;
  color: var(--ui-TC);
}
.ui-TC-1 {
  color: var(--ui-TC-1) !important;
}
.ui-BG-Main-1 {
  background-color: var(--ui-BG-Main-1) !important;
  color: var(--ui-BG-Main-TC) !important;
}
.ui-TC-Main-1 {
  color: var(--ui-BG-Main-1) !important;
}
.ui-BG-2 {
  background-color: var(--ui-BG-2) !important;
  color: var(--ui-TC);
}
.ui-TC-2 {
  color: var(--ui-TC-2) !important;
}
.ui-BG-Main-2 {
  background-color: var(--ui-BG-Main-2) !important;
  color: var(--ui-BG-Main-TC) !important;
}
.ui-TC-Main-2 {
  color: var(--ui-BG-Main-2) !important;
}
.ui-BG-3 {
  background-color: var(--ui-BG-3) !important;
  color: var(--ui-TC);
}
.ui-TC-3 {
  color: var(--ui-TC-3) !important;
}
.ui-BG-Main-3 {
  background-color: var(--ui-BG-Main-3) !important;
  color: var(--ui-BG-Main-TC) !important;
}
.ui-TC-Main-3 {
  color: var(--ui-BG-Main-3) !important;
}
.ui-BG-4 {
  background-color: var(--ui-BG-4) !important;
  color: var(--ui-TC);
}
.ui-TC-4 {
  color: var(--ui-TC-4) !important;
}
.ui-BG-Main-4 {
  background-color: var(--ui-BG-Main-4) !important;
  color: var(--ui-BG-Main-TC) !important;
}
.ui-TC-Main-4 {
  color: var(--ui-BG-Main-4) !important;
}
.main-yellow {
  --ui-BG-Main: #fbbd08;
  --ui-BG-Main-1: rgba(228, 181, 46, 0.97);
  --ui-BG-Main-2: rgba(206, 173, 77, 0.92);
  --ui-BG-Main-3: rgba(195, 161, 62, 0.68);
  --ui-BG-Main-4: rgba(195, 161, 63, 0.46);
  --ui-BG-Main-TC: #333333 !important;
}
.main-orange {
  --ui-BG-Main: #f37b1d;
  --ui-BG-Main-1: rgba(221, 133, 64, 0.97);
  --ui-BG-Main-2: rgba(201, 141, 94, 0.92);
  --ui-BG-Main-3: rgba(190, 128, 80, 0.68);
  --ui-BG-Main-4: rgba(190, 128, 80, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-red {
  --ui-BG-Main: #e54d42;
  --ui-BG-Main-1: rgba(210, 105, 97, 0.97);
  --ui-BG-Main-2: rgba(193, 128, 123, 0.92);
  --ui-BG-Main-3: rgba(181, 115, 110, 0.68);
  --ui-BG-Main-4: rgba(181, 115, 110, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-pink {
  --ui-BG-Main: #e03997;
  --ui-BG-Main-1: rgba(204, 90, 154, 0.97);
  --ui-BG-Main-2: rgba(186, 118, 156, 0.92);
  --ui-BG-Main-3: rgba(174, 104, 143, 0.68);
  --ui-BG-Main-4: rgba(174, 104, 143, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-mauve {
  --ui-BG-Main: #b745cb;
  --ui-BG-Main-1: rgba(171, 102, 183, 0.97);
  --ui-BG-Main-2: rgba(160, 130, 165, 0.92);
  --ui-BG-Main-3: rgba(147, 117, 153, 0.68);
  --ui-BG-Main-4: rgba(147, 117, 153, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-purple {
  --ui-BG-Main: #8044de;
  --ui-BG-Main-1: rgba(140, 99, 203, 0.97);
  --ui-BG-Main-2: rgba(149, 126, 185, 0.92);
  --ui-BG-Main-3: rgba(136, 112, 174, 0.68);
  --ui-BG-Main-4: rgba(136, 113, 174, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-violet {
  --ui-BG-Main: #8044de;
  --ui-BG-Main-1: rgba(140, 99, 203, 0.97);
  --ui-BG-Main-2: rgba(149, 126, 185, 0.92);
  --ui-BG-Main-3: rgba(136, 112, 174, 0.68);
  --ui-BG-Main-4: rgba(136, 113, 174, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-blue {
  --ui-BG-Main: #0081ff;
  --ui-BG-Main-1: rgba(38, 136, 231, 0.97);
  --ui-BG-Main-2: rgba(71, 141, 209, 0.92);
  --ui-BG-Main-3: rgba(56, 128, 198, 0.68);
  --ui-BG-Main-4: rgba(56, 128, 198, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-cyan {
  --ui-BG-Main: #37c0fe;
  --ui-BG-Main-1: rgba(85, 188, 235, 0.97);
  --ui-BG-Main-2: rgba(111, 184, 218, 0.92);
  --ui-BG-Main-3: rgba(97, 173, 207, 0.68);
  --ui-BG-Main-4: rgba(97, 173, 207, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-green {
  --ui-BG-Main: #3eb93b;
  --ui-BG-Main-1: rgba(95, 166, 93, 0.97);
  --ui-BG-Main-2: rgba(123, 148, 122, 0.92);
  --ui-BG-Main-3: rgba(109, 135, 109, 0.68);
  --ui-BG-Main-4: rgba(109, 135, 109, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-olive {
  --ui-BG-Main: #8dc63f;
  --ui-BG-Main-1: rgba(144, 178, 97, 0.97);
  --ui-BG-Main-2: rgba(145, 159, 127, 0.92);
  --ui-BG-Main-3: rgba(132, 146, 113, 0.68);
  --ui-BG-Main-4: rgba(132, 146, 113, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-grey {
  --ui-BG-Main: #8799a3;
  --ui-BG-Main-1: rgba(155, 155, 155, 0.97);
  --ui-BG-Main-2: rgba(159, 159, 159, 0.92);
  --ui-BG-Main-3: rgba(147, 147, 147, 0.68);
  --ui-BG-Main-4: rgba(147, 147, 147, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
.main-brown {
  --ui-BG-Main: #a5673f;
  --ui-BG-Main-1: rgba(149, 116, 95, 0.97);
  --ui-BG-Main-2: rgba(133, 127, 123, 0.92);
  --ui-BG-Main-3: rgba(119, 113, 109, 0.68);
  --ui-BG-Main-4: rgba(119, 113, 109, 0.46);
  --ui-BG-Main-TC: #ffffff !important;
}
page {
  color: var(--text-a);
  background-color: var(--ui-BG-1) !important;
  font-family: 'ui-num',system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
}
/* ==================
         初始化
 ==================== */
view {
  box-sizing: border-box;
}
.ui-link {
  cursor: pointer;
}
navigator {
  display: inline-flex;
}
navigator.navigator-hover {
  background-color: inherit;
  -webkit-transform: translate(1rpx, 1rpx);
          transform: translate(1rpx, 1rpx);
}
/* ==================
         辅助类
 ==================== */
.none {
  display: none !important;
}
.inline {
  display: inline !important;
}
.inline-block {
  display: inline-block !important;
}
.block {
  display: block !important;
}
.touch-none {
  pointer-events: none;
}
.touch-all {
  pointer-events: all;
}
.flex {
  display: flex !important;
}
.inline-flex {
  display: inline-flex !important;
}
.w-100 {
  width: 100%;
}
/* -- 浮动 -- */
.cf::after,
.cf::before {
  content: '';
  display: table;
}
.cf::after {
  clear: both;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.position-center {
  position: absolute !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.position-relative {
  position: relative;
}
/* -- 工具类 -- */
.m-0 {
  margin: 0 !important;
}
.m-1 {
  margin: 5rpx !important;
}
.m-2 {
  margin: 10rpx !important;
}
.m-3 {
  margin: 20rpx !important;
}
.m-4 {
  margin: 30rpx !important;
}
.m-5 {
  margin: 60rpx !important;
}
.m-6 {
  margin: 100rpx !important;
}
.m-auto {
  margin: auto !important;
}
.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
.mx-1 {
  margin-right: 5rpx !important;
  margin-left: 5rpx !important;
}
.mx-2 {
  margin-right: 10rpx !important;
  margin-left: 10rpx !important;
}
.mx-3 {
  margin-right: 20rpx !important;
  margin-left: 20rpx !important;
}
.mx-4 {
  margin-right: 30rpx !important;
  margin-left: 30rpx !important;
}
.mx-5 {
  margin-right: 60rpx !important;
  margin-left: 60rpx !important;
}
.mx-6 {
  margin-right: 100rpx !important;
  margin-left: 100rpx !important;
}
.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}
.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.my-1 {
  margin-top: 5rpx !important;
  margin-bottom: 5rpx !important;
}
.my-2 {
  margin-top: 10rpx !important;
  margin-bottom: 10rpx !important;
}
.my-3 {
  margin-top: 20rpx !important;
  margin-bottom: 20rpx !important;
}
.my-4 {
  margin-top: 30rpx !important;
  margin-bottom: 30rpx !important;
}
.my-5 {
  margin-top: 60rpx !important;
  margin-bottom: 60rpx !important;
}
.my-6 {
  margin-top: 100rpx !important;
  margin-bottom: 100rpx !important;
}
.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mt-1 {
  margin-top: 5rpx !important;
}
.mt-2 {
  margin-top: 10rpx !important;
}
.mt-3 {
  margin-top: 20rpx !important;
}
.mt-4 {
  margin-top: 30rpx !important;
}
.mt-5 {
  margin-top: 60rpx !important;
}
.mt-6 {
  margin-top: 100rpx !important;
}
.mt-auto {
  margin-top: auto !important;
}
.mr-0 {
  margin-right: 0 !important;
}
.mr-1 {
  margin-right: 5rpx !important;
}
.mr-2 {
  margin-right: 10rpx !important;
}
.mr-3 {
  margin-right: 20rpx !important;
}
.mr-4 {
  margin-right: 30rpx !important;
}
.mr-5 {
  margin-right: 60rpx !important;
}
.mr-6 {
  margin-right: 100rpx !important;
}
.mr-auto {
  margin-right: auto !important;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mb-1 {
  margin-bottom: 5rpx !important;
}
.mb-2 {
  margin-bottom: 10rpx !important;
}
.mb-3 {
  margin-bottom: 20rpx !important;
}
.mb-4 {
  margin-bottom: 30rpx !important;
}
.mb-5 {
  margin-bottom: 60rpx !important;
}
.mb-6 {
  margin-bottom: 100rpx !important;
}
.mb-auto {
  margin-bottom: auto !important;
}
.ml-0 {
  margin-left: 0 !important;
}
.ml-1 {
  margin-left: 5rpx !important;
}
.ml-2 {
  margin-left: 10rpx !important;
}
.ml-3 {
  margin-left: 20rpx !important;
}
.ml-4 {
  margin-left: 30rpx !important;
}
.ml-5 {
  margin-left: 60rpx !important;
}
.ml-6 {
  margin-left: 100rpx !important;
}
.ml-auto {
  margin-left: auto !important;
}
.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: 5rpx !important;
}
.p-2 {
  padding: 10rpx !important;
}
.p-3 {
  padding: 20rpx !important;
}
.p-4 {
  padding: 30rpx !important;
}
.p-5 {
  padding: 60rpx !important;
}
.p-6 {
  padding: 100rpx !important;
}
.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.px-1 {
  padding-right: 5rpx !important;
  padding-left: 5rpx !important;
}
.px-2 {
  padding-right: 10rpx !important;
  padding-left: 10rpx !important;
}
.px-3 {
  padding-right: 20rpx !important;
  padding-left: 20rpx !important;
}
.px-4 {
  padding-right: 30rpx !important;
  padding-left: 30rpx !important;
}
.px-5 {
  padding-right: 60rpx !important;
  padding-left: 60rpx !important;
}
.px-6 {
  padding-right: 100rpx !important;
  padding-left: 100rpx !important;
}
.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.py-1 {
  padding-top: 5rpx !important;
  padding-bottom: 5rpx !important;
}
.py-2 {
  padding-top: 10rpx !important;
  padding-bottom: 10rpx !important;
}
.py-3 {
  padding-top: 20rpx !important;
  padding-bottom: 20rpx !important;
}
.py-4 {
  padding-top: 30rpx !important;
  padding-bottom: 30rpx !important;
}
.py-5 {
  padding-top: 60rpx !important;
  padding-bottom: 60rpx !important;
}
.py-6 {
  padding-top: 100rpx !important;
  padding-bottom: 100rpx !important;
}
.pt-0 {
  padding-top: 0 !important;
}
.pt-1 {
  padding-top: 5rpx !important;
}
.pt-2 {
  padding-top: 10rpx !important;
}
.pt-3 {
  padding-top: 20rpx !important;
}
.pt-4 {
  padding-top: 30rpx !important;
}
.pt-5 {
  padding-top: 60rpx !important;
}
.pt-6 {
  padding-top: 100rpx !important;
}
.pr-0 {
  padding-right: 0 !important;
}
.pr-1 {
  padding-right: 5rpx !important;
}
.pr-2 {
  padding-right: 10rpx !important;
}
.pr-3 {
  padding-right: 20rpx !important;
}
.pr-4 {
  padding-right: 30rpx !important;
}
.pr-5 {
  padding-right: 60rpx !important;
}
.pr-6 {
  padding-right: 100rpx !important;
}
.pb-0 {
  padding-bottom: 0 !important;
}
.pb-1 {
  padding-bottom: 5rpx !important;
}
.pb-2 {
  padding-bottom: 10rpx !important;
}
.pb-3 {
  padding-bottom: 20rpx !important;
}
.pb-4 {
  padding-bottom: 30rpx !important;
}
.pb-5 {
  padding-bottom: 60rpx !important;
}
.pb-6 {
  padding-bottom: 100rpx !important;
}
.pl-0 {
  padding-left: 0 !important;
}
.pl-1 {
  padding-left: 5rpx !important;
}
.pl-2 {
  padding-left: 10rpx !important;
}
.pl-3 {
  padding-left: 20rpx !important;
}
.pl-4 {
  padding-left: 30rpx !important;
}
.pl-5 {
  padding-left: 60rpx !important;
}
.pl-6 {
  padding-left: 100rpx !important;
}
.text-light {
  font-weight: 300 !important;
}
.text-lighter {
  font-weight: lighter !important;
}
.text-normal {
  font-weight: 400 !important;
}
.text-bold {
  font-weight: 700 !important;
}
.text-bolder {
  font-weight: 900 !important;
}
.text-left {
  text-align: left !important;
}
.text-right {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
.text-yellow {
  color: #fbbd08 !important;
}
.text-orange {
  color: #f37b1d !important;
}
.text-red {
  color: #e54d42 !important;
}
.text-pink {
  color: #e03997 !important;
}
.text-mauve {
  color: #b745cb !important;
}
.text-purple {
  color: #8044de !important;
}
.text-violet {
  color: #8044de !important;
}
.text-blue {
  color: #0081ff !important;
}
.text-cyan {
  color: #37c0fe !important;
}
.text-green {
  color: #3eb93b !important;
}
.text-olive {
  color: #8dc63f !important;
}
.text-grey {
  color: #8799a3 !important;
}
.text-brown {
  color: #a5673f !important;
}
.text-white {
  color: #ffffff !important;
}
.text-gray-f {
  color: #f8f9fa !important;
}
.text-gray-e {
  color: #eeeeee !important;
}
.text-gray-d {
  color: #dddddd !important;
}
.text-gray-c {
  color: #cccccc !important;
}
.text-gray-b {
  color: #bbbbbb !important;
}
.text-gray-a {
  color: #aaaaaa !important;
}
.text-gray {
  color: #aaaaaa !important;
}
.text-dark-9 {
  color: #999999 !important;
}
.text-dark-8 {
  color: #888888 !important;
}
.text-dark-7 {
  color: #777777 !important;
}
.text-dark-6 {
  color: #666666 !important;
}
.text-dark-5 {
  color: #555555 !important;
}
.text-dark-4 {
  color: #444444 !important;
}
.text-dark-3 {
  color: #333333 !important;
}
.text-dark-2 {
  color: #222222 !important;
}
.text-dark-1 {
  color: #111111 !important;
}
.text-black {
  color: #000000 !important;
}
.text-reset {
  color: inherit !important;
}
.lh-1 {
  line-height: 1 !important;
}
.lh-sm {
  line-height: 1.25 !important;
}
.lh-base {
  line-height: 1.5 !important;
}
.lh-lg {
  line-height: 2 !important;
}
.text-nowrap {
  white-space: nowrap !important;
}
.radius {
  border-radius: 10rpx !important;
}
.radius-sm {
  border-radius: 6rpx !important;
}
.radius-lg {
  border-radius: 40rpx !important;
}
.radius-0 {
  border-radius: 0 !important;
}
.round {
  border-radius: 1000rpx !important;
}
.round-circle {
  border-radius: 50% !important;
}
.radius-top {
  border-top-left-radius: 10rpx !important;
  border-top-right-radius: 10rpx !important;
}
.radius-right {
  border-top-right-radius: 10rpx !important;
  border-bottom-right-radius: 10rpx !important;
}
.radius-bottom {
  border-bottom-right-radius: 10rpx !important;
  border-bottom-left-radius: 10rpx !important;
}
.radius-left {
  border-bottom-left-radius: 10rpx !important;
  border-top-left-radius: 10rpx !important;
}
.radius-lr {
  border-top-left-radius: 10rpx !important;
  border-bottom-right-radius: 10rpx !important;
}
.radius-lr {
  border-top-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.radius-rl {
  border-top-right-radius: 10rpx !important;
  border-bottom-left-radius: 10rpx !important;
}
.radius-rl {
  border-top-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
/* ==================
          背景
 ==================== */
/* -- 基础色 -- */
.bg-yellow {
  background-color: #fbbd08 !important;
  color: #333333 !important;
}
.bg-orange {
  background-color: #f37b1d !important;
  color: #ffffff !important;
}
.bg-red {
  background-color: #e54d42 !important;
  color: #ffffff !important;
}
.bg-pink {
  background-color: #e03997 !important;
  color: #ffffff !important;
}
.bg-mauve {
  background-color: #b745cb !important;
  color: #ffffff !important;
}
.bg-purple {
  background-color: #8044de !important;
  color: #ffffff !important;
}
.bg-violet {
  background-color: #8044de !important;
  color: #ffffff !important;
}
.bg-blue {
  background-color: #0081ff !important;
  color: #ffffff !important;
}
.bg-cyan {
  background-color: #37c0fe !important;
  color: #ffffff !important;
}
.bg-green {
  background-color: #3eb93b !important;
  color: #ffffff !important;
}
.bg-olive {
  background-color: #8dc63f !important;
  color: #ffffff !important;
}
.bg-grey {
  background-color: #8799a3 !important;
  color: #ffffff !important;
}
.bg-brown {
  background-color: #a5673f !important;
  color: #ffffff !important;
}
.bg-dark-9 {
  background-color: #999999 !important;
  color: #ffffff !important;
}
.bg-dark-8 {
  background-color: #888888 !important;
  color: #ffffff !important;
}
.bg-dark-7 {
  background-color: #777777 !important;
  color: #ffffff !important;
}
.bg-dark-6 {
  background-color: #666666 !important;
  color: #ffffff !important;
}
.bg-dark-5 {
  background-color: #555555 !important;
  color: #ffffff !important;
}
.bg-dark-4 {
  background-color: #444444 !important;
  color: #ffffff !important;
}
.bg-dark-3 {
  background-color: #333333 !important;
  color: #ffffff !important;
}
.bg-dark-2 {
  background-color: #222222 !important;
  color: #ffffff !important;
}
.bg-dark-1 {
  background-color: #111111 !important;
  color: #ffffff !important;
}
.bg-black {
  background-color: #000000 !important;
  color: #ffffff !important;
}
/* -- 浅色 -- */
.bg-yellow-light {
  background-image: linear-gradient(45deg, white, #fef5da) !important;
  color: #fbbd08 !important;
}
.bg-yellow-thin {
  background-color: rgba(251, 189, 8, var(--ui-BG-opacity)) !important;
  color: #fbbd08 !important;
}
.bg-orange-light {
  background-image: linear-gradient(45deg, white, #fdebdd) !important;
  color: #f37b1d !important;
}
.bg-orange-thin {
  background-color: rgba(243, 123, 29, var(--ui-BG-opacity)) !important;
  color: #f37b1d !important;
}
.bg-red-light {
  background-image: linear-gradient(45deg, white, #fbe4e3) !important;
  color: #e54d42 !important;
}
.bg-red-thin {
  background-color: rgba(229, 77, 66, var(--ui-BG-opacity)) !important;
  color: #e54d42 !important;
}
.bg-pink-light {
  background-image: linear-gradient(45deg, white, #fae1ef) !important;
  color: #e03997 !important;
}
.bg-pink-thin {
  background-color: rgba(224, 57, 151, var(--ui-BG-opacity)) !important;
  color: #e03997 !important;
}
.bg-mauve-light {
  background-image: linear-gradient(45deg, white, #f4e3f7) !important;
  color: #b745cb !important;
}
.bg-mauve-thin {
  background-color: rgba(183, 69, 203, var(--ui-BG-opacity)) !important;
  color: #b745cb !important;
}
.bg-purple-light {
  background-image: linear-gradient(45deg, white, #ece3fa) !important;
  color: #8044de !important;
}
.bg-purple-thin {
  background-color: rgba(128, 68, 222, var(--ui-BG-opacity)) !important;
  color: #8044de !important;
}
.bg-violet-light {
  background-image: linear-gradient(45deg, white, #ece3fa) !important;
  color: #8044de !important;
}
.bg-violet-thin {
  background-color: rgba(128, 68, 222, var(--ui-BG-opacity)) !important;
  color: #8044de !important;
}
.bg-blue-light {
  background-image: linear-gradient(45deg, white, #d9ecff) !important;
  color: #0081ff !important;
}
.bg-blue-thin {
  background-color: rgba(0, 129, 255, var(--ui-BG-opacity)) !important;
  color: #0081ff !important;
}
.bg-cyan-light {
  background-image: linear-gradient(45deg, white, #e1f6ff) !important;
  color: #37c0fe !important;
}
.bg-cyan-thin {
  background-color: rgba(55, 192, 254, var(--ui-BG-opacity)) !important;
  color: #37c0fe !important;
}
.bg-green-light {
  background-image: linear-gradient(45deg, white, #e2f5e2) !important;
  color: #3eb93b !important;
}
.bg-green-thin {
  background-color: rgba(62, 185, 59, var(--ui-BG-opacity)) !important;
  color: #3eb93b !important;
}
.bg-olive-light {
  background-image: linear-gradient(45deg, white, #eef6e2) !important;
  color: #8dc63f !important;
}
.bg-olive-thin {
  background-color: rgba(141, 198, 63, var(--ui-BG-opacity)) !important;
  color: #8dc63f !important;
}
.bg-grey-light {
  background-image: linear-gradient(45deg, white, #edf0f1) !important;
  color: #8799a3 !important;
}
.bg-grey-thin {
  background-color: rgba(135, 153, 163, var(--ui-BG-opacity)) !important;
  color: #8799a3 !important;
}
.bg-brown-light {
  background-image: linear-gradient(45deg, white, #f2e8e2) !important;
  color: #a5673f !important;
}
.bg-brown-thin {
  background-color: rgba(165, 103, 63, var(--ui-BG-opacity)) !important;
  color: #a5673f !important;
}
/* -- 渐变色 -- */
.bg-yellow-orange {
  background-image: linear-gradient(130deg, #fbbd08, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-yellow-red {
  background-image: linear-gradient(130deg, #fbbd08, #e54d42) !important;
  color: #ffffff !important;
}
.bg-yellow-pink {
  background-image: linear-gradient(130deg, #fbbd08, #e03997) !important;
  color: #ffffff !important;
}
.bg-yellow-mauve {
  background-image: linear-gradient(130deg, #fbbd08, #b745cb) !important;
  color: #ffffff !important;
}
.bg-yellow-purple {
  background-image: linear-gradient(130deg, #fbbd08, #8044de) !important;
  color: #ffffff !important;
}
.bg-yellow-violet {
  background-image: linear-gradient(130deg, #fbbd08, #8044de) !important;
  color: #ffffff !important;
}
.bg-yellow-blue {
  background-image: linear-gradient(130deg, #fbbd08, #0081ff) !important;
  color: #ffffff !important;
}
.bg-yellow-cyan {
  background-image: linear-gradient(130deg, #fbbd08, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-yellow-green {
  background-image: linear-gradient(130deg, #fbbd08, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-yellow-olive {
  background-image: linear-gradient(130deg, #fbbd08, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-yellow-grey {
  background-image: linear-gradient(130deg, #fbbd08, #8799a3) !important;
  color: #ffffff !important;
}
.bg-yellow-brown {
  background-image: linear-gradient(130deg, #fbbd08, #a5673f) !important;
  color: #ffffff !important;
}
.bg-orange-yellow {
  background-image: linear-gradient(130deg, #f37b1d, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-orange-red {
  background-image: linear-gradient(130deg, #f37b1d, #e54d42) !important;
  color: #ffffff !important;
}
.bg-orange-pink {
  background-image: linear-gradient(130deg, #f37b1d, #e03997) !important;
  color: #ffffff !important;
}
.bg-orange-mauve {
  background-image: linear-gradient(130deg, #f37b1d, #b745cb) !important;
  color: #ffffff !important;
}
.bg-orange-purple {
  background-image: linear-gradient(130deg, #f37b1d, #8044de) !important;
  color: #ffffff !important;
}
.bg-orange-violet {
  background-image: linear-gradient(130deg, #f37b1d, #8044de) !important;
  color: #ffffff !important;
}
.bg-orange-blue {
  background-image: linear-gradient(130deg, #f37b1d, #0081ff) !important;
  color: #ffffff !important;
}
.bg-orange-cyan {
  background-image: linear-gradient(130deg, #f37b1d, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-orange-green {
  background-image: linear-gradient(130deg, #f37b1d, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-orange-olive {
  background-image: linear-gradient(130deg, #f37b1d, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-orange-grey {
  background-image: linear-gradient(130deg, #f37b1d, #8799a3) !important;
  color: #ffffff !important;
}
.bg-orange-brown {
  background-image: linear-gradient(130deg, #f37b1d, #a5673f) !important;
  color: #ffffff !important;
}
.bg-red-yellow {
  background-image: linear-gradient(130deg, #e54d42, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-red-orange {
  background-image: linear-gradient(130deg, #e54d42, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-red-pink {
  background-image: linear-gradient(130deg, #e54d42, #e03997) !important;
  color: #ffffff !important;
}
.bg-red-mauve {
  background-image: linear-gradient(130deg, #e54d42, #b745cb) !important;
  color: #ffffff !important;
}
.bg-red-purple {
  background-image: linear-gradient(130deg, #e54d42, #8044de) !important;
  color: #ffffff !important;
}
.bg-red-violet {
  background-image: linear-gradient(130deg, #e54d42, #8044de) !important;
  color: #ffffff !important;
}
.bg-red-blue {
  background-image: linear-gradient(130deg, #e54d42, #0081ff) !important;
  color: #ffffff !important;
}
.bg-red-cyan {
  background-image: linear-gradient(130deg, #e54d42, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-red-green {
  background-image: linear-gradient(130deg, #e54d42, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-red-olive {
  background-image: linear-gradient(130deg, #e54d42, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-red-grey {
  background-image: linear-gradient(130deg, #e54d42, #8799a3) !important;
  color: #ffffff !important;
}
.bg-red-brown {
  background-image: linear-gradient(130deg, #e54d42, #a5673f) !important;
  color: #ffffff !important;
}
.bg-pink-yellow {
  background-image: linear-gradient(130deg, #e03997, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-pink-orange {
  background-image: linear-gradient(130deg, #e03997, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-pink-red {
  background-image: linear-gradient(130deg, #e03997, #e54d42) !important;
  color: #ffffff !important;
}
.bg-pink-mauve {
  background-image: linear-gradient(130deg, #e03997, #b745cb) !important;
  color: #ffffff !important;
}
.bg-pink-purple {
  background-image: linear-gradient(130deg, #e03997, #8044de) !important;
  color: #ffffff !important;
}
.bg-pink-violet {
  background-image: linear-gradient(130deg, #e03997, #8044de) !important;
  color: #ffffff !important;
}
.bg-pink-blue {
  background-image: linear-gradient(130deg, #e03997, #0081ff) !important;
  color: #ffffff !important;
}
.bg-pink-cyan {
  background-image: linear-gradient(130deg, #e03997, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-pink-green {
  background-image: linear-gradient(130deg, #e03997, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-pink-olive {
  background-image: linear-gradient(130deg, #e03997, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-pink-grey {
  background-image: linear-gradient(130deg, #e03997, #8799a3) !important;
  color: #ffffff !important;
}
.bg-pink-brown {
  background-image: linear-gradient(130deg, #e03997, #a5673f) !important;
  color: #ffffff !important;
}
.bg-mauve-yellow {
  background-image: linear-gradient(130deg, #b745cb, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-mauve-orange {
  background-image: linear-gradient(130deg, #b745cb, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-mauve-red {
  background-image: linear-gradient(130deg, #b745cb, #e54d42) !important;
  color: #ffffff !important;
}
.bg-mauve-pink {
  background-image: linear-gradient(130deg, #b745cb, #e03997) !important;
  color: #ffffff !important;
}
.bg-mauve-purple {
  background-image: linear-gradient(130deg, #b745cb, #8044de) !important;
  color: #ffffff !important;
}
.bg-mauve-violet {
  background-image: linear-gradient(130deg, #b745cb, #8044de) !important;
  color: #ffffff !important;
}
.bg-mauve-blue {
  background-image: linear-gradient(130deg, #b745cb, #0081ff) !important;
  color: #ffffff !important;
}
.bg-mauve-cyan {
  background-image: linear-gradient(130deg, #b745cb, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-mauve-green {
  background-image: linear-gradient(130deg, #b745cb, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-mauve-olive {
  background-image: linear-gradient(130deg, #b745cb, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-mauve-grey {
  background-image: linear-gradient(130deg, #b745cb, #8799a3) !important;
  color: #ffffff !important;
}
.bg-mauve-brown {
  background-image: linear-gradient(130deg, #b745cb, #a5673f) !important;
  color: #ffffff !important;
}
.bg-purple-yellow {
  background-image: linear-gradient(130deg, #8044de, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-purple-orange {
  background-image: linear-gradient(130deg, #8044de, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-purple-red {
  background-image: linear-gradient(130deg, #8044de, #e54d42) !important;
  color: #ffffff !important;
}
.bg-purple-pink {
  background-image: linear-gradient(130deg, #8044de, #e03997) !important;
  color: #ffffff !important;
}
.bg-purple-mauve {
  background-image: linear-gradient(130deg, #8044de, #b745cb) !important;
  color: #ffffff !important;
}
.bg-purple-violet {
  background-image: linear-gradient(130deg, #8044de, #8044de) !important;
  color: #ffffff !important;
}
.bg-purple-blue {
  background-image: linear-gradient(130deg, #8044de, #0081ff) !important;
  color: #ffffff !important;
}
.bg-purple-cyan {
  background-image: linear-gradient(130deg, #8044de, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-purple-green {
  background-image: linear-gradient(130deg, #8044de, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-purple-olive {
  background-image: linear-gradient(130deg, #8044de, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-purple-grey {
  background-image: linear-gradient(130deg, #8044de, #8799a3) !important;
  color: #ffffff !important;
}
.bg-purple-brown {
  background-image: linear-gradient(130deg, #8044de, #a5673f) !important;
  color: #ffffff !important;
}
.bg-violet-yellow {
  background-image: linear-gradient(130deg, #8044de, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-violet-orange {
  background-image: linear-gradient(130deg, #8044de, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-violet-red {
  background-image: linear-gradient(130deg, #8044de, #e54d42) !important;
  color: #ffffff !important;
}
.bg-violet-pink {
  background-image: linear-gradient(130deg, #8044de, #e03997) !important;
  color: #ffffff !important;
}
.bg-violet-mauve {
  background-image: linear-gradient(130deg, #8044de, #b745cb) !important;
  color: #ffffff !important;
}
.bg-violet-purple {
  background-image: linear-gradient(130deg, #8044de, #8044de) !important;
  color: #ffffff !important;
}
.bg-violet-blue {
  background-image: linear-gradient(130deg, #8044de, #0081ff) !important;
  color: #ffffff !important;
}
.bg-violet-cyan {
  background-image: linear-gradient(130deg, #8044de, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-violet-green {
  background-image: linear-gradient(130deg, #8044de, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-violet-olive {
  background-image: linear-gradient(130deg, #8044de, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-violet-grey {
  background-image: linear-gradient(130deg, #8044de, #8799a3) !important;
  color: #ffffff !important;
}
.bg-violet-brown {
  background-image: linear-gradient(130deg, #8044de, #a5673f) !important;
  color: #ffffff !important;
}
.bg-blue-yellow {
  background-image: linear-gradient(130deg, #0081ff, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-blue-orange {
  background-image: linear-gradient(130deg, #0081ff, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-blue-red {
  background-image: linear-gradient(130deg, #0081ff, #e54d42) !important;
  color: #ffffff !important;
}
.bg-blue-pink {
  background-image: linear-gradient(130deg, #0081ff, #e03997) !important;
  color: #ffffff !important;
}
.bg-blue-mauve {
  background-image: linear-gradient(130deg, #0081ff, #b745cb) !important;
  color: #ffffff !important;
}
.bg-blue-purple {
  background-image: linear-gradient(130deg, #0081ff, #8044de) !important;
  color: #ffffff !important;
}
.bg-blue-violet {
  background-image: linear-gradient(130deg, #0081ff, #8044de) !important;
  color: #ffffff !important;
}
.bg-blue-cyan {
  background-image: linear-gradient(130deg, #0081ff, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-blue-green {
  background-image: linear-gradient(130deg, #0081ff, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-blue-olive {
  background-image: linear-gradient(130deg, #0081ff, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-blue-grey {
  background-image: linear-gradient(130deg, #0081ff, #8799a3) !important;
  color: #ffffff !important;
}
.bg-blue-brown {
  background-image: linear-gradient(130deg, #0081ff, #a5673f) !important;
  color: #ffffff !important;
}
.bg-cyan-yellow {
  background-image: linear-gradient(130deg, #37c0fe, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-cyan-orange {
  background-image: linear-gradient(130deg, #37c0fe, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-cyan-red {
  background-image: linear-gradient(130deg, #37c0fe, #e54d42) !important;
  color: #ffffff !important;
}
.bg-cyan-pink {
  background-image: linear-gradient(130deg, #37c0fe, #e03997) !important;
  color: #ffffff !important;
}
.bg-cyan-mauve {
  background-image: linear-gradient(130deg, #37c0fe, #b745cb) !important;
  color: #ffffff !important;
}
.bg-cyan-purple {
  background-image: linear-gradient(130deg, #37c0fe, #8044de) !important;
  color: #ffffff !important;
}
.bg-cyan-violet {
  background-image: linear-gradient(130deg, #37c0fe, #8044de) !important;
  color: #ffffff !important;
}
.bg-cyan-blue {
  background-image: linear-gradient(130deg, #37c0fe, #0081ff) !important;
  color: #ffffff !important;
}
.bg-cyan-green {
  background-image: linear-gradient(130deg, #37c0fe, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-cyan-olive {
  background-image: linear-gradient(130deg, #37c0fe, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-cyan-grey {
  background-image: linear-gradient(130deg, #37c0fe, #8799a3) !important;
  color: #ffffff !important;
}
.bg-cyan-brown {
  background-image: linear-gradient(130deg, #37c0fe, #a5673f) !important;
  color: #ffffff !important;
}
.bg-green-yellow {
  background-image: linear-gradient(130deg, #3eb93b, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-green-orange {
  background-image: linear-gradient(130deg, #3eb93b, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-green-red {
  background-image: linear-gradient(130deg, #3eb93b, #e54d42) !important;
  color: #ffffff !important;
}
.bg-green-pink {
  background-image: linear-gradient(130deg, #3eb93b, #e03997) !important;
  color: #ffffff !important;
}
.bg-green-mauve {
  background-image: linear-gradient(130deg, #3eb93b, #b745cb) !important;
  color: #ffffff !important;
}
.bg-green-purple {
  background-image: linear-gradient(130deg, #3eb93b, #8044de) !important;
  color: #ffffff !important;
}
.bg-green-violet {
  background-image: linear-gradient(130deg, #3eb93b, #8044de) !important;
  color: #ffffff !important;
}
.bg-green-blue {
  background-image: linear-gradient(130deg, #3eb93b, #0081ff) !important;
  color: #ffffff !important;
}
.bg-green-cyan {
  background-image: linear-gradient(130deg, #3eb93b, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-green-olive {
  background-image: linear-gradient(130deg, #3eb93b, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-green-grey {
  background-image: linear-gradient(130deg, #3eb93b, #8799a3) !important;
  color: #ffffff !important;
}
.bg-green-brown {
  background-image: linear-gradient(130deg, #3eb93b, #a5673f) !important;
  color: #ffffff !important;
}
.bg-olive-yellow {
  background-image: linear-gradient(130deg, #8dc63f, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-olive-orange {
  background-image: linear-gradient(130deg, #8dc63f, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-olive-red {
  background-image: linear-gradient(130deg, #8dc63f, #e54d42) !important;
  color: #ffffff !important;
}
.bg-olive-pink {
  background-image: linear-gradient(130deg, #8dc63f, #e03997) !important;
  color: #ffffff !important;
}
.bg-olive-mauve {
  background-image: linear-gradient(130deg, #8dc63f, #b745cb) !important;
  color: #ffffff !important;
}
.bg-olive-purple {
  background-image: linear-gradient(130deg, #8dc63f, #8044de) !important;
  color: #ffffff !important;
}
.bg-olive-violet {
  background-image: linear-gradient(130deg, #8dc63f, #8044de) !important;
  color: #ffffff !important;
}
.bg-olive-blue {
  background-image: linear-gradient(130deg, #8dc63f, #0081ff) !important;
  color: #ffffff !important;
}
.bg-olive-cyan {
  background-image: linear-gradient(130deg, #8dc63f, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-olive-green {
  background-image: linear-gradient(130deg, #8dc63f, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-olive-grey {
  background-image: linear-gradient(130deg, #8dc63f, #8799a3) !important;
  color: #ffffff !important;
}
.bg-olive-brown {
  background-image: linear-gradient(130deg, #8dc63f, #a5673f) !important;
  color: #ffffff !important;
}
.bg-grey-yellow {
  background-image: linear-gradient(130deg, #8799a3, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-grey-orange {
  background-image: linear-gradient(130deg, #8799a3, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-grey-red {
  background-image: linear-gradient(130deg, #8799a3, #e54d42) !important;
  color: #ffffff !important;
}
.bg-grey-pink {
  background-image: linear-gradient(130deg, #8799a3, #e03997) !important;
  color: #ffffff !important;
}
.bg-grey-mauve {
  background-image: linear-gradient(130deg, #8799a3, #b745cb) !important;
  color: #ffffff !important;
}
.bg-grey-purple {
  background-image: linear-gradient(130deg, #8799a3, #8044de) !important;
  color: #ffffff !important;
}
.bg-grey-violet {
  background-image: linear-gradient(130deg, #8799a3, #8044de) !important;
  color: #ffffff !important;
}
.bg-grey-blue {
  background-image: linear-gradient(130deg, #8799a3, #0081ff) !important;
  color: #ffffff !important;
}
.bg-grey-cyan {
  background-image: linear-gradient(130deg, #8799a3, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-grey-green {
  background-image: linear-gradient(130deg, #8799a3, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-grey-olive {
  background-image: linear-gradient(130deg, #8799a3, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-grey-brown {
  background-image: linear-gradient(130deg, #8799a3, #a5673f) !important;
  color: #ffffff !important;
}
.bg-brown-yellow {
  background-image: linear-gradient(130deg, #a5673f, #fbbd08) !important;
  color: #ffffff !important;
}
.bg-brown-orange {
  background-image: linear-gradient(130deg, #a5673f, #f37b1d) !important;
  color: #ffffff !important;
}
.bg-brown-red {
  background-image: linear-gradient(130deg, #a5673f, #e54d42) !important;
  color: #ffffff !important;
}
.bg-brown-pink {
  background-image: linear-gradient(130deg, #a5673f, #e03997) !important;
  color: #ffffff !important;
}
.bg-brown-mauve {
  background-image: linear-gradient(130deg, #a5673f, #b745cb) !important;
  color: #ffffff !important;
}
.bg-brown-purple {
  background-image: linear-gradient(130deg, #a5673f, #8044de) !important;
  color: #ffffff !important;
}
.bg-brown-violet {
  background-image: linear-gradient(130deg, #a5673f, #8044de) !important;
  color: #ffffff !important;
}
.bg-brown-blue {
  background-image: linear-gradient(130deg, #a5673f, #0081ff) !important;
  color: #ffffff !important;
}
.bg-brown-cyan {
  background-image: linear-gradient(130deg, #a5673f, #37c0fe) !important;
  color: #ffffff !important;
}
.bg-brown-green {
  background-image: linear-gradient(130deg, #a5673f, #3eb93b) !important;
  color: #ffffff !important;
}
.bg-brown-olive {
  background-image: linear-gradient(130deg, #a5673f, #8dc63f) !important;
  color: #ffffff !important;
}
.bg-brown-grey {
  background-image: linear-gradient(130deg, #a5673f, #8799a3) !important;
  color: #ffffff !important;
}
.bg-yellow-gradient {
  background-image: linear-gradient(45deg, #f5fe00, #ff6600) !important;
  color: #333333 !important;
}
.bg-orange-gradient {
  background-image: linear-gradient(45deg, #ff9700, #ed1c24) !important;
  color: #ffffff !important;
}
.bg-red-gradient {
  background-image: linear-gradient(45deg, #f33a41, #ed0586) !important;
  color: #ffffff !important;
}
.bg-pink-gradient {
  background-image: linear-gradient(45deg, #fea894, #ff1047) !important;
  color: #ffffff !important;
}
.bg-mauve-gradient {
  background-image: linear-gradient(45deg, #c01f95, #7115cc) !important;
  color: #ffffff !important;
}
.bg-purple-gradient {
  background-image: linear-gradient(45deg, #9829ea, #5908fb) !important;
  color: #ffffff !important;
}
.bg-blue-gradient {
  background-image: linear-gradient(45deg, #00b8f9, #0166eb) !important;
  color: #ffffff !important;
}
.bg-cyan-gradient {
  background-image: linear-gradient(45deg, #06edfe, #48b2fe) !important;
  color: #ffffff !important;
}
.bg-green-gradient {
  background-image: linear-gradient(45deg, #3ab54a, #8cc63f) !important;
  color: #ffffff !important;
}
.bg-olive-gradient {
  background-image: linear-gradient(45deg, #90e630, #39d266) !important;
  color: #ffffff !important;
}
.bg-grey-gradient {
  background-image: linear-gradient(45deg, #9aadb9, #354855) !important;
  color: #ffffff !important;
}
.bg-brown-gradient {
  background-image: linear-gradient(45deg, #ca6f2e, #cb1413) !important;
  color: #ffffff !important;
}
.bg-white {
  background-color: #ffffff !important;
  color: #333333 !important;
}
.bg-gray-f {
  background-color: #f8f9fa !important;
  color: #333333 !important;
}
.bg-gray-e {
  background-color: #eeeeee !important;
  color: #333333 !important;
}
.bg-gray-d {
  background-color: #dddddd !important;
  color: #333333 !important;
}
.bg-gray-c {
  background-color: #cccccc !important;
  color: #333333 !important;
}
.bg-gray-b {
  background-color: #bbbbbb !important;
  color: #333333 !important;
}
.bg-gray-a {
  background-color: #aaaaaa !important;
  color: #333333 !important;
}
.bg-gray {
  background-color: #aaaaaa !important;
  color: #333333 !important;
}
.bg-square {
  background-color: #fff;
  background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%), linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%);
  background-size: 40rpx 40rpx;
  background-position: 0 0, 20rpx 20rpx;
}
.bg-none {
  background: transparent !important;
  color: inherit !important;
}
[class*='bg-mask'] {
  position: relative;
  color: #FFFFFF !important;
}
[class*='bg-mask'] > view,
[class*='bg-mask'] > text {
  position: relative;
  z-index: 1;
  color: #ffffff;
}
[class*='bg-mask']::before {
  content: '';
  border-radius: inherit;
  width: 100%;
  height: 100%;
  position: absolute !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 0;
}
.bg-mask-80::before {
  background: rgba(0, 0, 0, 0.8) !important;
}
.bg-mask-50::before {
  background: rgba(0, 0, 0, 0.5) !important;
}
.bg-mask-20::before {
  background: rgba(0, 0, 0, 0.2) !important;
}
.bg-mask-top::before {
  background-color: rgba(0, 0, 0, 0);
  background-image: linear-gradient(black, rgba(0, 0, 0, 0.618), rgba(0, 0, 0, 0.01));
}
.bg-white-top {
  background-color: rgba(0, 0, 0, 0);
  background-image: linear-gradient(white, rgba(255, 255, 255, 0.3));
}
.bg-mask-bottom::before {
  background-color: rgba(0, 0, 0, 0);
  background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.618), black);
}
.bg-img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
[class*='bg-blur'] {
  position: relative;
}
[class*='bg-blur'] > view,
[class*='bg-blur'] > text {
  position: relative;
  z-index: 1;
}
[class*='bg-blur']::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  border-radius: inherit;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
@supports ((-webkit-backdrop-filter: blur(20px)) or (backdrop-filter: blur(20px))) {
.bg-blur::before {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    color: var(--ui-TC);
    background-color: var(--ui-Blur-1);
}
.bg-blur-1::before {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    color: var(--ui-TC);
    background-color: var(--ui-Blur-2);
}
.bg-blur-2::before {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    color: var(--ui-TC);
    background-color: var(--ui-Blur-3);
}
}
@supports not ((-webkit-backdrop-filter: blur(5px)) or (backdrop-filter: blur(5px))) {
.bg-blur {
    color: var(--ui-TC);
}
.bg-blur::before {
    background-color: var(--ui-BG);
}
.bg-blur-1 {
    color: var(--ui-TC);
}
.bg-blur-1::before {
    background-color: var(--ui-BG-1);
}
.bg-blur-2 {
    color: var(--ui-TC);
}
.bg-blur-2::before {
    background-color: var(--ui-BG-2);
}
}
/* ==================
          栅栏
 ==================== */
.ui-container {
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
  padding-left: 30rpx;
  padding-right: 30rpx;
  width: 100%;
  max-width: 1440px;
}
.ui-container-fluid {
  max-width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.ui-grid {
  display: flex;
  flex-wrap: wrap;
}
.ui-grid.multi-column {
  display: block;
  -webkit-column-count: 2;
          column-count: 2;
  -webkit-column-width: 0px;
          column-width: 0px;
  -webkit-column-gap: 0px;
          column-gap: 0px;
}
.ui-grid.multi-column > .ui-item {
  -webkit-column-break-inside: avoid;
          break-inside: avoid;
  padding: 0.001em;
}
.ui-grid.grid-square {
  overflow: hidden;
}
.ui-grid.grid-square > .ui-item {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}
.ui-grid.grid-square.ui-cols-1 > .ui-item {
  padding-bottom: calc((100% - 0rpx) / 1);
  height: 0;
  width: calc((100% - 0rpx) / 1);
}
.ui-grid.grid-square.ui-cols-2 > .ui-item {
  padding-bottom: calc((100% - 20rpx) / 2);
  height: 0;
  width: calc((100% - 20rpx) / 2);
}
.ui-grid.grid-square.ui-cols-3 > .ui-item {
  padding-bottom: calc((100% - 40rpx) / 3);
  height: 0;
  width: calc((100% - 40rpx) / 3);
}
.ui-grid.grid-square.ui-cols-4 > .ui-item {
  padding-bottom: calc((100% - 60rpx) / 4);
  height: 0;
  width: calc((100% - 60rpx) / 4);
}
.ui-grid.grid-square.ui-cols-5 > .ui-item {
  padding-bottom: calc((100% - 80rpx) / 5);
  height: 0;
  width: calc((100% - 80rpx) / 5);
}
.ui-grid.grid-square.ui-cols-6 > .ui-item {
  padding-bottom: calc((100% - 100rpx) / 6);
  height: 0;
  width: calc((100% - 100rpx) / 6);
}
.ui-grid.grid-square.ui-cols-7 > .ui-item {
  padding-bottom: calc((100% - 120rpx) / 7);
  height: 0;
  width: calc((100% - 120rpx) / 7);
}
.ui-grid.grid-square.ui-cols-8 > .ui-item {
  padding-bottom: calc((100% - 140rpx) / 8);
  height: 0;
  width: calc((100% - 140rpx) / 8);
}
.ui-grid.grid-square.ui-cols-9 > .ui-item {
  padding-bottom: calc((100% - 160rpx) / 9);
  height: 0;
  width: calc((100% - 160rpx) / 9);
}
.ui-grid.grid-square.ui-cols-10 > .ui-item {
  padding-bottom: calc((100% - 180rpx) / 10);
  height: 0;
  width: calc((100% - 180rpx) / 10);
}
.ui-grid.grid-square.ui-cols-11 > .ui-item {
  padding-bottom: calc((100% - 200rpx) / 11);
  height: 0;
  width: calc((100% - 200rpx) / 11);
}
.ui-grid.grid-square.ui-cols-12 > .ui-item {
  padding-bottom: calc((100% - 220rpx) / 12);
  height: 0;
  width: calc((100% - 220rpx) / 12);
}
.ui-grid.grid-square.ui-cols-1 > .ui-item:nth-child(1n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-2 > .ui-item:nth-child(2n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-3 > .ui-item:nth-child(3n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-4 > .ui-item:nth-child(4n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-5 > .ui-item:nth-child(5n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-6 > .ui-item:nth-child(6n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-7 > .ui-item:nth-child(7n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-8 > .ui-item:nth-child(8n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-9 > .ui-item:nth-child(9n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-10 > .ui-item:nth-child(10n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-11 > .ui-item:nth-child(11n) {
  margin-right: 0;
}
.ui-grid.grid-square.ui-cols-12 > .ui-item:nth-child(12n) {
  margin-right: 0;
}
.ui-cols-1 .ui-item {
  width: calc(100% / 1);
}
.ui-cols-2 .ui-item {
  width: calc(100% / 2);
}
.ui-cols-3 .ui-item {
  width: calc(100% / 3);
}
.ui-cols-4 .ui-item {
  width: calc(100% / 4);
}
.ui-cols-5 .ui-item {
  width: calc(100% / 5);
}
.ui-cols-6 .ui-item {
  width: calc(100% / 6);
}
.ui-cols-7 .ui-item {
  width: calc(100% / 7);
}
.ui-cols-8 .ui-item {
  width: calc(100% / 8);
}
.ui-cols-9 .ui-item {
  width: calc(100% / 9);
}
.ui-cols-10 .ui-item {
  width: calc(100% / 10);
}
.ui-cols-11 .ui-item {
  width: calc(100% / 11);
}
.ui-cols-12 .ui-item {
  width: calc(100% / 12);
}
.ui-col-1 {
  width: calc(100% / 12 * 1);
}
.ui-col-2 {
  width: calc(100% / 12 * 2);
}
.ui-col-3 {
  width: calc(100% / 12 * 3);
}
.ui-col-4 {
  width: calc(100% / 12 * 4);
}
.ui-col-5 {
  width: calc(100% / 12 * 5);
}
.ui-col-6 {
  width: calc(100% / 12 * 6);
}
.ui-col-7 {
  width: calc(100% / 12 * 7);
}
.ui-col-8 {
  width: calc(100% / 12 * 8);
}
.ui-col-9 {
  width: calc(100% / 12 * 9);
}
.ui-col-10 {
  width: calc(100% / 12 * 10);
}
.ui-col-11 {
  width: calc(100% / 12 * 11);
}
.ui-col-12 {
  width: calc(100% / 12 * 12);
}
@media screen and (min-width: 0px) {
.ui-col-xs-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-xs-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-xs-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-xs-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-xs-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-xs-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-xs-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-xs-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-xs-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-xs-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-xs-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-xs-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-xs-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-xs-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-xs-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-xs-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-xs-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-xs-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-xs-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-xs-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-xs-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-xs-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-xs-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-xs-12 .ui-item {
    width: calc(100% / 12);
}
}
@media screen and (min-width: 320px) {
.ui-col-sm-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-sm-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-sm-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-sm-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-sm-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-sm-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-sm-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-sm-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-sm-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-sm-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-sm-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-sm-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-sm-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-sm-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-sm-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-sm-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-sm-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-sm-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-sm-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-sm-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-sm-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-sm-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-sm-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-sm-12 .ui-item {
    width: calc(100% / 12);
}
}
@media screen and (min-width: 768px) {
.ui-col-md-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-md-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-md-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-md-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-md-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-md-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-md-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-md-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-md-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-md-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-md-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-md-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-md-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-md-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-md-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-md-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-md-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-md-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-md-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-md-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-md-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-md-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-md-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-md-12 .ui-item {
    width: calc(100% / 12);
}
}
@media screen and (min-width: 1025px) {
.ui-col-lg-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-lg-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-lg-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-lg-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-lg-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-lg-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-lg-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-lg-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-lg-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-lg-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-lg-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-lg-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-lg-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-lg-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-lg-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-lg-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-lg-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-lg-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-lg-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-lg-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-lg-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-lg-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-lg-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-lg-12 .ui-item {
    width: calc(100% / 12);
}
}
@media screen and (min-width: 1440px) {
.ui-col-xl-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-xl-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-xl-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-xl-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-xl-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-xl-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-xl-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-xl-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-xl-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-xl-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-xl-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-xl-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-xl-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-xl-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-xl-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-xl-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-xl-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-xl-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-xl-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-xl-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-xl-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-xl-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-xl-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-xl-12 .ui-item {
    width: calc(100% / 12);
}
}
@media screen and (min-width: 1920px) {
.ui-col-xxl-1 {
    width: calc(100% / 12 * 1);
}
.ui-cols-xxl-1 .ui-item {
    width: calc(100% / 1);
}
.ui-col-xxl-2 {
    width: calc(100% / 12 * 2);
}
.ui-cols-xxl-2 .ui-item {
    width: calc(100% / 2);
}
.ui-col-xxl-3 {
    width: calc(100% / 12 * 3);
}
.ui-cols-xxl-3 .ui-item {
    width: calc(100% / 3);
}
.ui-col-xxl-4 {
    width: calc(100% / 12 * 4);
}
.ui-cols-xxl-4 .ui-item {
    width: calc(100% / 4);
}
.ui-col-xxl-5 {
    width: calc(100% / 12 * 5);
}
.ui-cols-xxl-5 .ui-item {
    width: calc(100% / 5);
}
.ui-col-xxl-6 {
    width: calc(100% / 12 * 6);
}
.ui-cols-xxl-6 .ui-item {
    width: calc(100% / 6);
}
.ui-col-xxl-7 {
    width: calc(100% / 12 * 7);
}
.ui-cols-xxl-7 .ui-item {
    width: calc(100% / 7);
}
.ui-col-xxl-8 {
    width: calc(100% / 12 * 8);
}
.ui-cols-xxl-8 .ui-item {
    width: calc(100% / 8);
}
.ui-col-xxl-9 {
    width: calc(100% / 12 * 9);
}
.ui-cols-xxl-9 .ui-item {
    width: calc(100% / 9);
}
.ui-col-xxl-10 {
    width: calc(100% / 12 * 10);
}
.ui-cols-xxl-10 .ui-item {
    width: calc(100% / 10);
}
.ui-col-xxl-11 {
    width: calc(100% / 12 * 11);
}
.ui-cols-xxl-11 .ui-item {
    width: calc(100% / 11);
}
.ui-col-xxl-12 {
    width: calc(100% / 12 * 12);
}
.ui-cols-xxl-12 .ui-item {
    width: calc(100% / 12);
}
}
/* ==================
		弹性布局
 ==================== */
.flex {
  display: flex !important;
}
.flex-sub {
  flex: 1 !important;
}
.flex-twice {
  flex: 2 !important;
}
.flex-treble {
  flex: 3 !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-bar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.basis-xs {
  flex-basis: 20% !important;
}
.basis-sm {
  flex-basis: 40% !important;
}
.basis-df {
  flex-basis: 50% !important;
}
.basis-lg {
  flex-basis: 60% !important;
}
.basis-xl {
  flex-basis: 80% !important;
}
.align-start {
  align-items: flex-start !important;
}
.align-end {
  align-items: flex-end !important;
}
.align-center {
  align-items: center !important;
}
.align-stretch {
  align-items: stretch !important;
}
.align-baseline {
  align-items: baseline !important;
}
.self-start {
  align-self: flex-start !important;
}
.self-end {
  align-self: flex-end !important;
}
.self-center {
  align-self: center !important;
}
.self-stretch {
  align-self: stretch !important;
}
.self-baseline {
  align-self: baseline !important;
}
.justify-start {
  justify-content: flex-start !important;
}
.justify-end {
  justify-content: flex-end !important;
}
.justify-center {
  justify-content: center !important;
}
.justify-between {
  justify-content: space-between !important;
}
.justify-around {
  justify-content: space-around !important;
}
/* ==================
          边框
 ==================== */
/* -- 实线 -- */
.border {
  overflow: initial !important;
}
[class*='border'],
[class*='dashed'] {
  position: relative;
}
[class*='border'].dline,
[class*='dashed'].dline {
  --ui-Border:var(--ui-BG-3)
			;
}
[class*='border']::after,
[class*='dashed']::after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-radius: inherit;
}
[class*='border'].radius::after,
[class*='dashed'].radius::after {
  border-radius: calc(10rpx * 2);
}
[class*='border'].round::after,
[class*='dashed'].round::after {
  border-radius: 1000rpx;
}
.border::after {
  border: 1px solid var(--ui-Border);
}
.borders::after {
  border: 4rpx solid var(--ui-Border);
}
.borderss::after {
  border: 8rpx solid var(--ui-Border);
}
.border-top::after {
  border-top: 1px solid var(--ui-Border);
}
.borders-top::after {
  border-top: 4rpx solid var(--ui-Border);
}
.borderss-top::after {
  border-top: 8rpx solid var(--ui-Border);
}
.border-right::after {
  border-right: 1px solid var(--ui-Border);
}
.borders-right::after {
  border-right: 4rpx solid var(--ui-Border);
}
.borderss-right::after {
  border-right: 8rpx solid var(--ui-Border);
}
.border-bottom::after {
  border-bottom: 1px solid var(--ui-Border);
}
.borders-bottom::after {
  border-bottom: 4rpx solid var(--ui-Border);
}
.borderss-bottom::after {
  border-bottom: 8rpx solid var(--ui-Border);
}
.border-left::after {
  border-left: 1px solid var(--ui-Border);
}
.borders-left::after {
  border-left: 4rpx solid var(--ui-Border);
}
.borderss-left::after {
  border-left: 8rpx solid var(--ui-Border);
}
/* -- 虚线 -- */
.dashed::after {
  border: 4rpx dashed var(--ui-Border);
}
.dasheds::after {
  border: 6rpx dashed var(--ui-Border);
}
.dashed-top::after {
  border-top: 4rpx dashed var(--ui-Border);
}
.dasheds-top::after {
  border-top: 6rpx dashed var(--ui-Border);
}
.dashed-right::after {
  border-right: 4rpx dashed var(--ui-Border);
}
.dasheds-right::after {
  border-right: 6rpx dashed var(--ui-Border);
}
.dashed-bottom::after {
  border-bottom: 4rpx dashed var(--ui-Border);
}
.dasheds-bottom::after {
  border-bottom: 6rpx dashed var(--ui-Border);
}
.dashed-left::after {
  border-left: 4rpx dashed var(--ui-Border);
}
.dasheds-left::after {
  border-left: 6rpx dashed var(--ui-Border);
}
.border-yellow::after, .border-yellow[class*='-shine']::before {
  border-color: #fbbd08 !important;
}
.border-orange::after, .border-orange[class*='-shine']::before {
  border-color: #f37b1d !important;
}
.border-red::after, .border-red[class*='-shine']::before {
  border-color: #e54d42 !important;
}
.border-pink::after, .border-pink[class*='-shine']::before {
  border-color: #e03997 !important;
}
.border-mauve::after, .border-mauve[class*='-shine']::before {
  border-color: #b745cb !important;
}
.border-purple::after, .border-purple[class*='-shine']::before {
  border-color: #8044de !important;
}
.border-violet::after, .border-violet[class*='-shine']::before {
  border-color: #8044de !important;
}
.border-blue::after, .border-blue[class*='-shine']::before {
  border-color: #0081ff !important;
}
.border-cyan::after, .border-cyan[class*='-shine']::before {
  border-color: #37c0fe !important;
}
.border-green::after, .border-green[class*='-shine']::before {
  border-color: #3eb93b !important;
}
.border-olive::after, .border-olive[class*='-shine']::before {
  border-color: #8dc63f !important;
}
.border-grey::after, .border-grey[class*='-shine']::before {
  border-color: #8799a3 !important;
}
.border-brown::after, .border-brown[class*='-shine']::before {
  border-color: #a5673f !important;
}
.border-dark-9::after, .border-dark-9[class*='-shine']::before {
  border-color: #999999 !important;
}
.border-dark-8::after, .border-dark-8[class*='-shine']::before {
  border-color: #888888 !important;
}
.border-dark-7::after, .border-dark-7[class*='-shine']::before {
  border-color: #777777 !important;
}
.border-dark-6::after, .border-dark-6[class*='-shine']::before {
  border-color: #666666 !important;
}
.border-dark-5::after, .border-dark-5[class*='-shine']::before {
  border-color: #555555 !important;
}
.border-dark-4::after, .border-dark-4[class*='-shine']::before {
  border-color: #444444 !important;
}
.border-dark-3::after, .border-dark-3[class*='-shine']::before {
  border-color: #333333 !important;
}
.border-dark-2::after, .border-dark-2[class*='-shine']::before {
  border-color: #222222 !important;
}
.border-dark-1::after, .border-dark-1[class*='-shine']::before {
  border-color: #111111 !important;
}
.border-black::after, .border-black[class*='-shine']::before {
  border-color: #000000 !important;
}
.border-white::after, .border-white[class*='-shine']::before {
  border-color: #ffffff !important;
}
.border-gray-f::after, .border-gray-f[class*='-shine']::before {
  border-color: #f8f9fa !important;
}
.border-gray-e::after, .border-gray-e[class*='-shine']::before {
  border-color: #eeeeee !important;
}
.border-gray-d::after, .border-gray-d[class*='-shine']::before {
  border-color: #dddddd !important;
}
.border-gray-c::after, .border-gray-c[class*='-shine']::before {
  border-color: #cccccc !important;
}
.border-gray-b::after, .border-gray-b[class*='-shine']::before {
  border-color: #bbbbbb !important;
}
.border-gray-a::after, .border-gray-a[class*='-shine']::before {
  border-color: #aaaaaa !important;
}
.border-gray::after, .border-gray[class*='-shine']::before {
  border-color: #aaaaaa !important;
}
.main-a-border::after,
.main-a-border[class*='-shine']::before {
  border-color: var(--main-a) !important;
}
.main-b-border::after,
.main-b-border[class*='-shine']::before {
  border-color: var(--main-b) !important;
}
.main-c-border::after,
.main-c-border[class*='-shine']::before {
  border-color: var(--main-c) !important;
}
.main-d-border::after,
.main-d-border[class*='-shine']::before {
  border-color: var(--main-d) !important;
}
.main-e-border::after,
.main-e-border[class*='-shine']::before {
  border-color: var(--main-e) !important;
}
.dashed-shine,
.dasheds-shine {
  position: relative;
  overflow: hidden;
}
.dashed-shine::after, .dashed-shine::before, .dasheds-shine::after, .dasheds-shine::before {
  border-style: dashed;
  border-color: var(--ui-Border);
  -webkit-animation: shineafter 1s infinite linear;
          animation: shineafter 1s infinite linear;
  width: calc(200% + 40px);
  height: 200%;
  border-width: 2px 0;
}
.dashed-shine::before,
.dasheds-shine::before {
  content: ' ';
  position: absolute;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-animation: shinebefore 1s infinite linear;
          animation: shinebefore 1s infinite linear;
  width: 200%;
  height: calc(200% + 40px);
  border-width: 0 2px;
}
.dasheds-shine::after, .dasheds-shine::before {
  border-width: 4px 0;
}
.dasheds-shine::before {
  border-width: 0 4px;
}
@-webkit-keyframes shineafter {
0% {
    top: 0;
    left: -22px;
}
100% {
    top: 0px;
    left: 0px;
}
}
@keyframes shineafter {
0% {
    top: 0;
    left: -22px;
}
100% {
    top: 0px;
    left: 0px;
}
}
@-webkit-keyframes shinebefore {
0% {
    top: -22px;
    left: 0;
}
100% {
    top: 0px;
    left: 0px;
}
}
@keyframes shinebefore {
0% {
    top: -22px;
    left: 0;
}
100% {
    top: 0px;
    left: 0px;
}
}
/* ==================
          文本
 ==================== */
.font-0 {
  font-size: 24rpx;
  --textSize:-4rpx;
}
.font-1 {
  font-size: 28rpx;
  --textSize:0rpx;
}
.font-2 {
  font-size: 32rpx;
  --textSize:4rpx;
}
.font-3 {
  font-size: 36rpx;
  --textSize:8rpx;
}
.font-4 {
  font-size: 40rpx;
  --textSize:12rpx;
}
.text-xs, .text-10 {
  font-size: calc(20rpx + var(--textSize)) !important;
}
.text-sm, .text-12 {
  font-size: calc(24rpx + var(--textSize)) !important;
}
.text-df, .text-14 {
  font-size: calc(28rpx + var(--textSize)) !important;
}
.text-lg, .text-16 {
  font-size: calc(32rpx + var(--textSize)) !important;
}
.text-xl, .text-18 {
  font-size: calc(36rpx + var(--textSize)) !important;
}
.text-xxl, .text-22 {
  font-size: calc(44rpx + var(--textSize)) !important;
}
.text-sl, .text-40 {
  font-size: calc(80rpx + var(--textSize)) !important;
}
.text-xsl, .text-60 {
  font-size: calc(120rpx + var(--textSize)) !important;
}
.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
[class*='text-linecut'] {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}
.text-linecut-2 {
  -webkit-line-clamp: 2;
}
.text-linecut-3 {
  -webkit-line-clamp: 3;
}
.text-linecut-4 {
  -webkit-line-clamp: 4;
}
.text-linecut-5 {
  -webkit-line-clamp: 5;
}
.text-linecut-6 {
  -webkit-line-clamp: 6;
}
.text-linecut-7 {
  -webkit-line-clamp: 7;
}
.text-linecut-8 {
  -webkit-line-clamp: 8;
}
.text-linecut-9 {
  -webkit-line-clamp: 9;
}
.text-linecut-10 {
  -webkit-line-clamp: 10;
}
.text-justify {
  text-align: justify;
}
.text-justify-line {
  text-align: justify;
  line-height: 0.5em;
  margin-top: 0.5em;
}
.text-justify-line::after {
  content: '.';
  display: inline-block;
  width: 100%;
}
.text-Abc {
  text-transform: Capitalize !important;
}
.text-ABC {
  text-transform: Uppercase !important;
}
.text-abc {
  text-transform: Lowercase !important;
}
.text-del, .text-line {
  text-decoration: line-through !important;
}
.text-bottomline {
  text-decoration: underline !important;
}
.text-italic {
  font-style: italic !important;
}
.text-style-none {
  text-decoration: none !important;
}
.text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}
.text-reset {
  color: inherit !important;
}
.text-price::before {
  content: '¥';
  font-size: 80%;
  margin-right: 4rpx;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
/* ==================
          阴影
 ==================== */
.shadow {
  box-shadow: var(--ui-Shadow);
}
.shadow-sm {
  box-shadow: var(--ui-Shadow-sm);
}
.shadow-lg {
  box-shadow: var(--ui-Shadow-lg);
}
.shadow-inset {
  box-shadow: var(--ui-Shadow-inset);
}
.shadow-yellow {
  box-shadow: 0 0.5em 1em rgba(251, 189, 8, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-yellow {
  box-shadow: 0 0.125em 0.25em rgba(251, 189, 8, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-yellow {
  box-shadow: 0 1em 3em rgba(251, 189, 8, var(--ui-Shadow-opacity-lg));
}
.shadow-orange {
  box-shadow: 0 0.5em 1em rgba(243, 123, 29, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-orange {
  box-shadow: 0 0.125em 0.25em rgba(243, 123, 29, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-orange {
  box-shadow: 0 1em 3em rgba(243, 123, 29, var(--ui-Shadow-opacity-lg));
}
.shadow-red {
  box-shadow: 0 0.5em 1em rgba(229, 77, 66, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-red {
  box-shadow: 0 0.125em 0.25em rgba(229, 77, 66, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-red {
  box-shadow: 0 1em 3em rgba(229, 77, 66, var(--ui-Shadow-opacity-lg));
}
.shadow-pink {
  box-shadow: 0 0.5em 1em rgba(224, 57, 151, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-pink {
  box-shadow: 0 0.125em 0.25em rgba(224, 57, 151, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-pink {
  box-shadow: 0 1em 3em rgba(224, 57, 151, var(--ui-Shadow-opacity-lg));
}
.shadow-mauve {
  box-shadow: 0 0.5em 1em rgba(183, 69, 203, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-mauve {
  box-shadow: 0 0.125em 0.25em rgba(183, 69, 203, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-mauve {
  box-shadow: 0 1em 3em rgba(183, 69, 203, var(--ui-Shadow-opacity-lg));
}
.shadow-purple {
  box-shadow: 0 0.5em 1em rgba(128, 68, 222, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-purple {
  box-shadow: 0 0.125em 0.25em rgba(128, 68, 222, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-purple {
  box-shadow: 0 1em 3em rgba(128, 68, 222, var(--ui-Shadow-opacity-lg));
}
.shadow-violet {
  box-shadow: 0 0.5em 1em rgba(128, 68, 222, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-violet {
  box-shadow: 0 0.125em 0.25em rgba(128, 68, 222, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-violet {
  box-shadow: 0 1em 3em rgba(128, 68, 222, var(--ui-Shadow-opacity-lg));
}
.shadow-blue {
  box-shadow: 0 0.5em 1em rgba(0, 129, 255, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-blue {
  box-shadow: 0 0.125em 0.25em rgba(0, 129, 255, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-blue {
  box-shadow: 0 1em 3em rgba(0, 129, 255, var(--ui-Shadow-opacity-lg));
}
.shadow-cyan {
  box-shadow: 0 0.5em 1em rgba(55, 192, 254, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-cyan {
  box-shadow: 0 0.125em 0.25em rgba(55, 192, 254, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-cyan {
  box-shadow: 0 1em 3em rgba(55, 192, 254, var(--ui-Shadow-opacity-lg));
}
.shadow-green {
  box-shadow: 0 0.5em 1em rgba(62, 185, 59, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-green {
  box-shadow: 0 0.125em 0.25em rgba(62, 185, 59, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-green {
  box-shadow: 0 1em 3em rgba(62, 185, 59, var(--ui-Shadow-opacity-lg));
}
.shadow-olive {
  box-shadow: 0 0.5em 1em rgba(141, 198, 63, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-olive {
  box-shadow: 0 0.125em 0.25em rgba(141, 198, 63, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-olive {
  box-shadow: 0 1em 3em rgba(141, 198, 63, var(--ui-Shadow-opacity-lg));
}
.shadow-grey {
  box-shadow: 0 0.5em 1em rgba(135, 153, 163, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-grey {
  box-shadow: 0 0.125em 0.25em rgba(135, 153, 163, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-grey {
  box-shadow: 0 1em 3em rgba(135, 153, 163, var(--ui-Shadow-opacity-lg));
}
.shadow-brown {
  box-shadow: 0 0.5em 1em rgba(165, 103, 63, var(--ui-Shadow-opacity));
}
.shadow-sm.shadow-brown {
  box-shadow: 0 0.125em 0.25em rgba(165, 103, 63, var(--ui-Shadow-opacity));
}
.shadow-lg.shadow-brown {
  box-shadow: 0 1em 3em rgba(165, 103, 63, var(--ui-Shadow-opacity-lg));
}
.shadow-warp {
  position: relative;
}
.shadow-warp:before, .shadow-warp:after {
  position: absolute;
  content: '';
  bottom: -10rpx;
  left: 20rpx;
  width: calc(50% - 40rpx);
  height: 30rpx;
  -webkit-transform: skew(0deg, -6deg);
          transform: skew(0deg, -6deg);
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
  background-color: rgba(0, 0, 0, var(--ui-Shadow-opacity));
  -webkit-filter: blur(20rpx);
          filter: blur(20rpx);
  z-index: -1;
  opacity: 0.5;
}
.shadow-warp:after {
  right: 20rpx;
  left: auto;
  -webkit-transform: skew(0deg, 6deg);
          transform: skew(0deg, 6deg);
}
.shadow-blur {
  position: relative;
}
.shadow-blur::before {
  content: '';
  display: block;
  background: inherit;
  -webkit-filter: blur(20rpx);
          filter: blur(20rpx);
  position: absolute;
  width: 100%;
  height: 100%;
  top: .5em;
  left: .5em;
  z-index: -1;
  opacity: var(--ui-Shadow-opacity-lg);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  border-radius: inherit;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}
.drop-shadow {
  -webkit-filter: drop-shadow(0 0 30rpx rgba(0, 0, 0, 0.1));
          filter: drop-shadow(0 0 30rpx rgba(0, 0, 0, 0.1));
}
.drop-shadow-sm {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.06));
          filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.06));
}
.drop-shadow-lg {
  -webkit-filter: drop-shadow(0 30rpx 60rpx rgba(0, 0, 0, 0.2));
          filter: drop-shadow(0 30rpx 60rpx rgba(0, 0, 0, 0.2));
}
.drop-shadow-yellow {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(205, 153, 3, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(205, 153, 3, 0.3));
}
.drop-shadow-sm.drop-shadow-yellow {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(205, 153, 3, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(205, 153, 3, 0.3));
}
.drop-shadow-lg.drop-shadow-yellow {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(205, 153, 3, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(205, 153, 3, 0.2));
}
.drop-shadow-orange {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(210, 98, 11, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(210, 98, 11, 0.3));
}
.drop-shadow-sm.drop-shadow-orange {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(210, 98, 11, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(210, 98, 11, 0.3));
}
.drop-shadow-lg.drop-shadow-orange {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(210, 98, 11, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(210, 98, 11, 0.2));
}
.drop-shadow-red {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(214, 42, 30, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(214, 42, 30, 0.3));
}
.drop-shadow-sm.drop-shadow-red {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(214, 42, 30, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(214, 42, 30, 0.3));
}
.drop-shadow-lg.drop-shadow-red {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(214, 42, 30, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(214, 42, 30, 0.2));
}
.drop-shadow-pink {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(199, 31, 126, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(199, 31, 126, 0.3));
}
.drop-shadow-sm.drop-shadow-pink {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(199, 31, 126, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(199, 31, 126, 0.3));
}
.drop-shadow-lg.drop-shadow-pink {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(199, 31, 126, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(199, 31, 126, 0.2));
}
.drop-shadow-mauve {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(154, 48, 173, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(154, 48, 173, 0.3));
}
.drop-shadow-sm.drop-shadow-mauve {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(154, 48, 173, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(154, 48, 173, 0.3));
}
.drop-shadow-lg.drop-shadow-mauve {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(154, 48, 173, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(154, 48, 173, 0.2));
}
.drop-shadow-purple {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(101, 36, 203, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(101, 36, 203, 0.3));
}
.drop-shadow-sm.drop-shadow-purple {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(101, 36, 203, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(101, 36, 203, 0.3));
}
.drop-shadow-lg.drop-shadow-purple {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(101, 36, 203, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(101, 36, 203, 0.2));
}
.drop-shadow-violet {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(101, 36, 203, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(101, 36, 203, 0.3));
}
.drop-shadow-sm.drop-shadow-violet {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(101, 36, 203, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(101, 36, 203, 0.3));
}
.drop-shadow-lg.drop-shadow-violet {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(101, 36, 203, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(101, 36, 203, 0.2));
}
.drop-shadow-blue {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(0, 103, 204, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(0, 103, 204, 0.3));
}
.drop-shadow-sm.drop-shadow-blue {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(0, 103, 204, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(0, 103, 204, 0.3));
}
.drop-shadow-lg.drop-shadow-blue {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(0, 103, 204, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(0, 103, 204, 0.2));
}
.drop-shadow-cyan {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(4, 176, 254, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(4, 176, 254, 0.3));
}
.drop-shadow-sm.drop-shadow-cyan {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(4, 176, 254, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(4, 176, 254, 0.3));
}
.drop-shadow-lg.drop-shadow-cyan {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(4, 176, 254, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(4, 176, 254, 0.2));
}
.drop-shadow-green {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(49, 146, 47, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(49, 146, 47, 0.3));
}
.drop-shadow-sm.drop-shadow-green {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(49, 146, 47, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(49, 146, 47, 0.3));
}
.drop-shadow-lg.drop-shadow-green {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(49, 146, 47, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(49, 146, 47, 0.2));
}
.drop-shadow-olive {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(114, 162, 48, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(114, 162, 48, 0.3));
}
.drop-shadow-sm.drop-shadow-olive {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(114, 162, 48, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(114, 162, 48, 0.3));
}
.drop-shadow-lg.drop-shadow-olive {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(114, 162, 48, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(114, 162, 48, 0.2));
}
.drop-shadow-grey {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(107, 128, 140, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(107, 128, 140, 0.3));
}
.drop-shadow-sm.drop-shadow-grey {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(107, 128, 140, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(107, 128, 140, 0.3));
}
.drop-shadow-lg.drop-shadow-grey {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(107, 128, 140, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(107, 128, 140, 0.2));
}
.drop-shadow-brown {
  -webkit-filter: drop-shadow(0 15rpx 15rpx rgba(128, 80, 49, 0.3));
          filter: drop-shadow(0 15rpx 15rpx rgba(128, 80, 49, 0.3));
}
.drop-shadow-sm.drop-shadow-brown {
  -webkit-filter: drop-shadow(0 4rpx 4rpx rgba(128, 80, 49, 0.3));
          filter: drop-shadow(0 4rpx 4rpx rgba(128, 80, 49, 0.3));
}
.drop-shadow-lg.drop-shadow-brown {
  -webkit-filter: drop-shadow(0 50rpx 100rpx rgba(128, 80, 49, 0.2));
          filter: drop-shadow(0 50rpx 100rpx rgba(128, 80, 49, 0.2));
}
@font-face {
  font-family: 'colorui';
  /* Project id 2620914 */
  src: url("data:application/x-font-woff2;charset=utf-8;base64,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") format("woff2");
}
[class*='_icon-'] {
  font-family: 'colorui' !important;
  display: inline-block;
}
@font-face {
  font-family: 'ui-num';
  src: url("data:application/x-font-ttf;base64,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") format("woff2");
  font-weight: normal;
  font-style: normal;
}
._icon-checkbox:before {
  content: "\e713";
}
._icon-box:before {
  content: "\e714";
}
._icon-checkbox-o:before {
  content: "\e715";
}
._icon-round:before {
  content: "\e716";
}
._icon-home-o:before {
  content: "\e70a";
}
._icon-home:before {
  content: "\e70d";
}
._icon-edit:before {
  content: "\e649";
}
._icon-close:before {
  content: "\e6ed";
}
._icon-check-round:before {
  content: "\e6f1";
}
._icon-check-round-o:before {
  content: "\e6f2";
}
._icon-close-round:before {
  content: "\e6f3";
}
._icon-close-round-o:before {
  content: "\e6f4";
}
._icon-waiting:before {
  content: "\e6f8";
}
._icon-waiting-o:before {
  content: "\e6f9";
}
._icon-warn:before {
  content: "\e662";
}
._icon-warn-o:before {
  content: "\e675";
}
._icon-more:before {
  content: "\e688";
}
._icon-delete:before {
  content: "\e707";
}
._icon-delete-o:before {
  content: "\e709";
}
._icon-add-round:before {
  content: "\e717";
}
._icon-add-round-o:before {
  content: "\e718";
}
._icon-add:before {
  content: "\e6e4";
}
._icon-info:before {
  content: "\e6ef";
}
._icon-info-o:before {
  content: "\e705";
}
._icon-move:before {
  content: "\e768";
}
._icon-title:before {
  content: "\e82f";
}
._icon-titles:before {
  content: "\e745";
}
._icon-loading:before {
  content: "\e746";
}
._icon-copy-o:before {
  content: "\e7bc";
}
._icon-copy:before {
  content: "\e85c";
}
._icon-loader:before {
  content: "\e76d";
}
._icon-search:before {
  content: "\e782";
}
._icon-back:before {
  content: "\e600";
}
._icon-forward:before {
  content: "\e601";
}
._icon-arrow:before {
  content: "\e608";
}
._icon-drop-down:before {
  content: "\e61c";
}
._icon-drop-up:before {
  content: "\e61d";
}
._icon-check:before {
  content: "\e69f";
}
._icon-move-round:before {
  content: "\e602";
}
._icon-move-round-o:before {
  content: "\e603";
}
@font-face {
  font-family: 'coloricon';
  src: url("data:application/x-font-woff2;charset=utf-8;base64,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**************************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") format("woff2");
  font-weight: normal;
  font-style: normal;
}
[class*='cicon-'] {
  font-family: 'coloricon';
  display: inline-block;
}
.cicon-Aa:before {
  content: "\e7a1";
}
.cicon-accounts:before {
  content: "\e681";
}
.cicon-accounts-o:before {
  content: "\e686";
}
.cicon-add:before {
  content: "\e6e4";
}
.cicon-add-round:before {
  content: "\e717";
}
.cicon-add-round-o:before {
  content: "\e718";
}
.cicon-alarm:before {
  content: "\e61e";
}
.cicon-album:before {
  content: "\e759";
}
.cicon-alipay:before {
  content: "\e6e1";
}
.cicon-android:before {
  content: "\e6e2";
}
.cicon-angle:before {
  content: "\e605";
}
.cicon-apple:before {
  content: "\e8e7";
}
.cicon-apps:before {
  content: "\e737";
}
.cicon-archive:before {
  content: "\e7ae";
}
.cicon-archive-o:before {
  content: "\e7ad";
}
.cicon-arrow:before {
  content: "\e608";
}
.cicon-at-line:before {
  content: "\e75c";
}
.cicon-avatar:before {
  content: "\e663";
}
.cicon-avatar-o:before {
  content: "\e665";
}
.cicon-avatars:before {
  content: "\e67e";
}
.cicon-avatars-o:before {
  content: "\e680";
}
.cicon-back:before {
  content: "\e600";
}
.cicon-backspace:before {
  content: "\e6a9";
}
.cicon-backup:before {
  content: "\e61f";
}
.cicon-backup-restore:before {
  content: "\e62d";
}
.cicon-barcode:before {
  content: "\e71f";
}
.cicon-book:before {
  content: "\e6a2";
}
.cicon-bookmark:before {
  content: "\e6a3";
}
.cicon-bookmark-o:before {
  content: "\e697";
}
.cicon-bookmarks:before {
  content: "\e6a6";
}
.cicon-box:before {
  content: "\e714";
}
.cicon-box-block:before {
  content: "\e6ac";
}
.cicon-box-right:before {
  content: "\e6a0";
}
.cicon-brand:before {
  content: "\e726";
}
.cicon-brand-o:before {
  content: "\e727";
}
.cicon-building:before {
  content: "\e6c3";
}
.cicon-building-o:before {
  content: "\e6c7";
}
.cicon-camera:before {
  content: "\e6fa";
}
.cicon-camera-add:before {
  content: "\e736";
}
.cicon-camera-add-o:before {
  content: "\e735";
}
.cicon-camera-lens:before {
  content: "\e68f";
}
.cicon-camera-lens-o:before {
  content: "\e68e";
}
.cicon-camera-o:before {
  content: "\e6fb";
}
.cicon-camera-rotate:before {
  content: "\e71e";
}
.cicon-card:before {
  content: "\e744";
}
.cicon-cardboard:before {
  content: "\e7a9";
}
.cicon-cardboard-o:before {
  content: "\e7aa";
}
.cicon-cardboard-off-o:before {
  content: "\e7af";
}
.cicon-cart:before {
  content: "\e70b";
}
.cicon-cart-o:before {
  content: "\e708";
}
.cicon-chat:before {
  content: "\e739";
}
.cicon-chat-bubble:before {
  content: "\e69b";
}
.cicon-chat-bubble-o:before {
  content: "\e6a7";
}
.cicon-chat-list:before {
  content: "\e69d";
}
.cicon-chat-list-o:before {
  content: "\e6aa";
}
.cicon-chat-o:before {
  content: "\e73c";
}
.cicon-chat-smile:before {
  content: "\e779";
}
.cicon-chat-smile-o:before {
  content: "\e78e";
}
.cicon-chat-smiles:before {
  content: "\e76b";
}
.cicon-chat-smiles-o:before {
  content: "\e74a";
}
.cicon-check:before {
  content: "\e69f";
}
.cicon-checkbox:before {
  content: "\e713";
}
.cicon-checkbox-o:before {
  content: "\e715";
}
.cicon-check-round:before {
  content: "\e6f1";
}
.cicon-check-round-o:before {
  content: "\e6f2";
}
.cicon-choiceness:before {
  content: "\e728";
}
.cicon-choiceness-o:before {
  content: "\e729";
}
.cicon-chrome:before {
  content: "\e6e3";
}
.cicon-circle:before {
  content: "\e7b0";
}
.cicon-circle-o:before {
  content: "\e7b1";
}
.cicon-close:before {
  content: "\e6ed";
}
.cicon-close-round:before {
  content: "\e6f3";
}
.cicon-close-round-o:before {
  content: "\e6f4";
}
.cicon-clothes:before {
  content: "\e72a";
}
.cicon-clothes-o:before {
  content: "\e72b";
}
.cicon-cloud:before {
  content: "\e64e";
}
.cicon-cloud-done:before {
  content: "\e641";
}
.cicon-cloud-download:before {
  content: "\e647";
}
.cicon-cloud-o:before {
  content: "\e646";
}
.cicon-cloud-off:before {
  content: "\e64b";
}
.cicon-cloud-upload:before {
  content: "\e687";
}
.cicon-code-box:before {
  content: "\e7c3";
}
.cicon-coin:before {
  content: "\e78a";
}
.cicon-coin-o:before {
  content: "\e79d";
}
.cicon-comment:before {
  content: "\e738";
}
.cicon-comment-o:before {
  content: "\e70e";
}
.cicon-community:before {
  content: "\e742";
}
.cicon-community-o:before {
  content: "\e743";
}
.cicon-countdown:before {
  content: "\e722";
}
.cicon-countdown-o:before {
  content: "\e723";
}
.cicon-creative:before {
  content: "\e72c";
}
.cicon-creative-o:before {
  content: "\e72d";
}
.cicon-crop:before {
  content: "\e6d9";
}
.cicon-crown:before {
  content: "\e776";
}
.cicon-crown-o:before {
  content: "\e777";
}
.cicon-cut:before {
  content: "\e74b";
}
.cicon-DarkMode:before {
  content: "\e7c4";
}
.cicon-dashboard:before {
  content: "\e62e";
}
.cicon-delete:before {
  content: "\e6bd";
}
.cicon-delete-close:before {
  content: "\e6ae";
}
.cicon-delete-line:before {
  content: "\e707";
}
.cicon-delete-line-o:before {
  content: "\e709";
}
.cicon-delete-o:before {
  content: "\e69a";
}
.cicon-deliver:before {
  content: "\e7f7";
}
.cicon-deliver-o:before {
  content: "\e6ff";
}
.cicon-demo:before {
  content: "\e916";
}
.cicon-discover:before {
  content: "\e70c";
}
.cicon-discover-o:before {
  content: "\e702";
}
.cicon-discuss-fill:before {
  content: "\e790";
}
.cicon-discuss-line:before {
  content: "\e78f";
}
.cicon-dollar:before {
  content: "\e79f";
}
.cicon-dollar-o:before {
  content: "\e79e";
}
.cicon-done:before {
  content: "\e633";
}
.cicon-done-all:before {
  content: "\e62a";
}
.cicon-douyin:before {
  content: "\e6e7";
}
.cicon-drop-down:before {
  content: "\e61c";
}
.cicon-drop-up:before {
  content: "\e61d";
}
.cicon-eject:before {
  content: "\e63a";
}
.cicon-ellipse:before {
  content: "\e74c";
}
.cicon-emoji:before {
  content: "\e78d";
}
.cicon-emoji-o:before {
  content: "\e6ee";
}
.cicon-equalizer:before {
  content: "\e802";
}
.cicon-eraser:before {
  content: "\e770";
}
.cicon-eraser-o:before {
  content: "\e772";
}
.cicon-evaluate:before {
  content: "\e7f0";
}
.cicon-evaluate-o:before {
  content: "\e700";
}
.cicon-event-close:before {
  content: "\e6a5";
}
.cicon-event-done:before {
  content: "\e6b2";
}
.cicon-event-list:before {
  content: "\e6b8";
}
.cicon-explore:before {
  content: "\e628";
}
.cicon-explore-line:before {
  content: "\e719";
}
.cicon-explore-line-o:before {
  content: "\e710";
}
.cicon-explore-o:before {
  content: "\e626";
}
.cicon-extension:before {
  content: "\e620";
}
.cicon-extension-o:before {
  content: "\e63f";
}
.cicon-eye:before {
  content: "\e740";
}
.cicon-eye-favor:before {
  content: "\e7b4";
}
.cicon-eye-favor-o:before {
  content: "\e7b5";
}
.cicon-eye-o:before {
  content: "\e741";
}
.cicon-eye-off:before {
  content: "\e7b3";
}
.cicon-eye-off-o:before {
  content: "\e7b2";
}
.cicon-facebook:before {
  content: "\e6ea";
}
.cicon-favorite:before {
  content: "\e623";
}
.cicon-favorite-o:before {
  content: "\e621";
}
.cicon-female:before {
  content: "\e72f";
}
.cicon-file:before {
  content: "\e857";
}
.cicon-file-copy:before {
  content: "\e85c";
}
.cicon-file-copy-o:before {
  content: "\e7bc";
}
.cicon-file-o:before {
  content: "\e7bb";
}
.cicon-file-text:before {
  content: "\e858";
}
.cicon-file-text-o:before {
  content: "\e7b9";
}
.cicon-filter:before {
  content: "\e6ec";
}
.cicon-fingerprint:before {
  content: "\e63b";
}
.cicon-first-page:before {
  content: "\e60c";
}
.cicon-flag:before {
  content: "\e64d";
}
.cicon-flag-o:before {
  content: "\e64c";
}
.cicon-flash-close:before {
  content: "\e73b";
}
.cicon-flash-off:before {
  content: "\e6d5";
}
.cicon-flash-on:before {
  content: "\e6dc";
}
.cicon-flash-open:before {
  content: "\e74f";
}
.cicon-folder:before {
  content: "\e6a1";
}
.cicon-folder-add:before {
  content: "\e6b4";
}
.cicon-folder-o:before {
  content: "\e6b0";
}
.cicon-folder-special:before {
  content: "\e65c";
}
.cicon-forward:before {
  content: "\e601";
}
.cicon-fullscreen:before {
  content: "\e915";
}
.cicon-fullscreen-exit:before {
  content: "\e914";
}
.cicon-game:before {
  content: "\e6c0";
}
.cicon-game-o:before {
  content: "\e6d1";
}
.cicon-git-commit:before {
  content: "\e7be";
}
.cicon-git-commit-o:before {
  content: "\e7bd";
}
.cicon-github:before {
  content: "\e6e9";
}
.cicon-github-circle:before {
  content: "\ead8";
}
.cicon-goods:before {
  content: "\e778";
}
.cicon-goodsnew:before {
  content: "\e7bf";
}
.cicon-goodsnew-o:before {
  content: "\e7c0";
}
.cicon-goods-o:before {
  content: "\e70f";
}
.cicon-GooglePlaylogo:before {
  content: "\e6e5";
}
.cicon-grid:before {
  content: "\e6ce";
}
.cicon-grid-o:before {
  content: "\e6cc";
}
.cicon-group:before {
  content: "\e7f5";
}
.cicon-group-o:before {
  content: "\e753";
}
.cicon-guanli:before {
  content: "\e750";
}
.cicon-headset:before {
  content: "\e6a4";
}
.cicon-headset-mic:before {
  content: "\e6b1";
}
.cicon-help:before {
  content: "\e66b";
}
.cicon-help-o:before {
  content: "\e65e";
}
.cicon-home:before {
  content: "\e70d";
}
.cicon-home-2:before {
  content: "\e6fd";
}
.cicon-home-2-o:before {
  content: "\e6cf";
}
.cicon-home-3:before {
  content: "\e6fc";
}
.cicon-home-3-o:before {
  content: "\e6e0";
}
.cicon-home-4:before {
  content: "\e732";
}
.cicon-home-4-o:before {
  content: "\e6e6";
}
.cicon-home-community:before {
  content: "\e799";
}
.cicon-home-dot:before {
  content: "\e794";
}
.cicon-home-dot-o:before {
  content: "\e797";
}
.cicon-home-line:before {
  content: "\e793";
}
.cicon-home-line-o:before {
  content: "\e792";
}
.cicon-home-o:before {
  content: "\e70a";
}
.cicon-home-sm:before {
  content: "\e798";
}
.cicon-home-smile:before {
  content: "\e79c";
}
.cicon-home-smile-o:before {
  content: "\e7a0";
}
.cicon-home-smline:before {
  content: "\e791";
}
.cicon-home-smline-o:before {
  content: "\e731";
}
.cicon-home-sm-o:before {
  content: "\e79b";
}
.cicon-hotel:before {
  content: "\e7a8";
}
.cicon-hotel-o:before {
  content: "\e7a3";
}
.cicon-huohu:before {
  content: "\e72e";
}
.cicon-IE:before {
  content: "\e922";
}
.cicon-image-text:before {
  content: "\e781";
}
.cicon-image-text-o:before {
  content: "\e758";
}
.cicon-import-export:before {
  content: "\e615";
}
.cicon-info:before {
  content: "\e6ef";
}
.cicon-info-o:before {
  content: "\e705";
}
.cicon-input:before {
  content: "\e75f";
}
.cicon-input-o:before {
  content: "\e6c8";
}
.cicon-keyboard:before {
  content: "\e6b6";
}
.cicon-kinds:before {
  content: "\e748";
}
.cicon-last-page:before {
  content: "\e60d";
}
.cicon-layout:before {
  content: "\e7e8";
}
.cicon-layout-o:before {
  content: "\e7e7";
}
.cicon-LightMode:before {
  content: "\e7ba";
}
.cicon-link:before {
  content: "\e6ab";
}
.cicon-link-off:before {
  content: "\e6b9";
}
.cicon-loader-fill:before {
  content: "\e76d";
}
.cicon-loading:before {
  content: "\e746";
}
.cicon-loading1:before {
  content: "\e749";
}
.cicon-loading2:before {
  content: "\e7f1";
}
.cicon-location-off:before {
  content: "\e671";
}
.cicon-location-off-o:before {
  content: "\e66d";
}
.cicon-location-on:before {
  content: "\e65f";
}
.cicon-location-on-o:before {
  content: "\e661";
}
.cicon-lock:before {
  content: "\e6ad";
}
.cicon-lock-o:before {
  content: "\e6b3";
}
.cicon-lock-open:before {
  content: "\e6ba";
}
.cicon-logout:before {
  content: "\e76e";
}
.cicon-loop:before {
  content: "\e616";
}
.cicon-magic:before {
  content: "\e6b7";
}
.cicon-magic-o:before {
  content: "\e6c2";
}
.cicon-mail:before {
  content: "\e6be";
}
.cicon-mail-o:before {
  content: "\e6bc";
}
.cicon-male:before {
  content: "\e730";
}
.cicon-mic:before {
  content: "\e656";
}
.cicon-mic-none:before {
  content: "\e642";
}
.cicon-mic-off:before {
  content: "\e652";
}
.cicon-miniprogram:before {
  content: "\e7d6";
}
.cicon-mobile:before {
  content: "\e854";
}
.cicon-mobile-o:before {
  content: "\e7b6";
}
.cicon-moneybag:before {
  content: "\e7ce";
}
.cicon-moneybag-o:before {
  content: "\e7d1";
}
.cicon-more:before {
  content: "\e688";
}
.cicon-more-tag:before {
  content: "\e672";
}
.cicon-move:before {
  content: "\e768";
}
.cicon-move-round:before {
  content: "\e602";
}
.cicon-move-round-o:before {
  content: "\e603";
}
.cicon-music:before {
  content: "\e795";
}
.cicon-music-off:before {
  content: "\e796";
}
.cicon-my:before {
  content: "\e78c";
}
.cicon-my-o:before {
  content: "\e78b";
}
.cicon-near-me:before {
  content: "\e654";
}
.cicon-near-me-o:before {
  content: "\e649";
}
.cicon-not:before {
  content: "\e667";
}
.cicon-notice:before {
  content: "\e666";
}
.cicon-notice-active:before {
  content: "\e66f";
}
.cicon-notice-active-o:before {
  content: "\e65d";
}
.cicon-notice-o:before {
  content: "\e664";
}
.cicon-notice-off:before {
  content: "\e6b5";
}
.cicon-notice-off-o:before {
  content: "\e6bb";
}
.cicon-numcode:before {
  content: "\e755";
}
.cicon-order:before {
  content: "\e786";
}
.cicon-order-o:before {
  content: "\e7b8";
}
.cicon-paint:before {
  content: "\e75d";
}
.cicon-paint-o:before {
  content: "\e75a";
}
.cicon-palette:before {
  content: "\e696";
}
.cicon-palette-o:before {
  content: "\e691";
}
.cicon-pause:before {
  content: "\e669";
}
.cicon-pause-circle:before {
  content: "\e678";
}
.cicon-person:before {
  content: "\e679";
}
.cicon-person-add:before {
  content: "\e668";
}
.cicon-person-add-o:before {
  content: "\e66a";
}
.cicon-person-o:before {
  content: "\e67d";
}
.cicon-person-pin-circle:before {
  content: "\e66c";
}
.cicon-person-pin-circle-o:before {
  content: "\e670";
}
.cicon-phone:before {
  content: "\e6f0";
}
.cicon-phone-call:before {
  content: "\e6d7";
}
.cicon-pic:before {
  content: "\e756";
}
.cicon-pic-o:before {
  content: "\e69e";
}
.cicon-pin-drop:before {
  content: "\e648";
}
.cicon-pin-drop-o:before {
  content: "\e655";
}
.cicon-place:before {
  content: "\e651";
}
.cicon-place-o:before {
  content: "\e650";
}
.cicon-play-arrow:before {
  content: "\e66e";
}
.cicon-play-circle:before {
  content: "\e674";
}
.cicon-play-circle-o:before {
  content: "\e67f";
}
.cicon-popover:before {
  content: "\e74e";
}
.cicon-popover-o:before {
  content: "\e757";
}
.cicon-present:before {
  content: "\e73a";
}
.cicon-present-o:before {
  content: "\e711";
}
.cicon-progress:before {
  content: "\e784";
}
.cicon-qq:before {
  content: "\e7d9";
}
.cicon-qr-code-fill:before {
  content: "\e767";
}
.cicon-qr-code-line:before {
  content: "\e75e";
}
.cicon-quill:before {
  content: "\e760";
}
.cicon-quill-o:before {
  content: "\e761";
}
.cicon-radio:before {
  content: "\e6d4";
}
.cicon-radiobox:before {
  content: "\e763";
}
.cicon-radiobox-o:before {
  content: "\e75b";
}
.cicon-recharge:before {
  content: "\e71c";
}
.cicon-recharge-o:before {
  content: "\e71d";
}
.cicon-record:before {
  content: "\e7a4";
}
.cicon-record-o:before {
  content: "\e7a6";
}
.cicon-redo:before {
  content: "\e612";
}
.cicon-redpacket:before {
  content: "\e7d3";
}
.cicon-redpacket-o:before {
  content: "\e71a";
}
.cicon-refresh:before {
  content: "\e611";
}
.cicon-repair:before {
  content: "\e73f";
}
.cicon-repair-o:before {
  content: "\e73e";
}
.cicon-repeat:before {
  content: "\e617";
}
.cicon-replay:before {
  content: "\e619";
}
.cicon-reply:before {
  content: "\e618";
}
.cicon-reply-all:before {
  content: "\e614";
}
.cicon-road-map:before {
  content: "\e769";
}
.cicon-road-map-o:before {
  content: "\e76a";
}
.cicon-round:before {
  content: "\e716";
}
.cicon-round-angle:before {
  content: "\e6f5";
}
.cicon-round-angle-o:before {
  content: "\e6f6";
}
.cicon-round-arrow-line:before {
  content: "\e734";
}
.cicon-round-box:before {
  content: "\e604";
}
.cicon-safe:before {
  content: "\e77f";
}
.cicon-safe-check:before {
  content: "\e875";
}
.cicon-safe-check-o:before {
  content: "\e876";
}
.cicon-safe-flash:before {
  content: "\e783";
}
.cicon-safe-flash-o:before {
  content: "\e775";
}
.cicon-safe-key:before {
  content: "\e76c";
}
.cicon-safe-key-o:before {
  content: "\e766";
}
.cicon-safe-o:before {
  content: "\e77e";
}
.cicon-save:before {
  content: "\e677";
}
.cicon-save-o:before {
  content: "\e684";
}
.cicon-scan:before {
  content: "\e703";
}
.cicon-scissors:before {
  content: "\e762";
}
.cicon-search:before {
  content: "\e6f7";
}
.cicon-search-line:before {
  content: "\e771";
}
.cicon-searchlist:before {
  content: "\e720";
}
.cicon-search-o:before {
  content: "\e782";
}
.cicon-search-sm:before {
  content: "\e631";
}
.cicon-service:before {
  content: "\e73d";
}
.cicon-service-fill:before {
  content: "\e704";
}
.cicon-service-o:before {
  content: "\e721";
}
.cicon-set:before {
  content: "\e773";
}
.cicon-set-list:before {
  content: "\e76f";
}
.cicon-set-o:before {
  content: "\e774";
}
.cicon-settings:before {
  content: "\e77a";
}
.cicon-settings-o:before {
  content: "\e780";
}
.cicon-share:before {
  content: "\e6c5";
}
.cicon-share-line-o:before {
  content: "\e74d";
}
.cicon-shengji:before {
  content: "\e747";
}
.cicon-shopping-cart:before {
  content: "\e685";
}
.cicon-shopping-cart-o:before {
  content: "\e676";
}
.cicon-show:before {
  content: "\e785";
}
.cicon-show-o:before {
  content: "\e787";
}
.cicon-shuffle:before {
  content: "\e61a";
}
.cicon-sip:before {
  content: "\e764";
}
.cicon-sip-o:before {
  content: "\e765";
}
.cicon-skip-next:before {
  content: "\e6dd";
}
.cicon-skip-previous:before {
  content: "\e6d6";
}
.cicon-slack:before {
  content: "\e87b";
}
.cicon-slack-square:before {
  content: "\e891";
}
.cicon-sort:before {
  content: "\e6bf";
}
.cicon-sort-order:before {
  content: "\e6fe";
}
.cicon-sound:before {
  content: "\e77b";
}
.cicon-sponsor:before {
  content: "\e77c";
}
.cicon-sponsor-o:before {
  content: "\e77d";
}
.cicon-star:before {
  content: "\e683";
}
.cicon-star-half:before {
  content: "\e67c";
}
.cicon-star-o:before {
  content: "\e67b";
}
.cicon-stock:before {
  content: "\e789";
}
.cicon-stop:before {
  content: "\e6db";
}
.cicon-store:before {
  content: "\e7ac";
}
.cicon-store-0:before {
  content: "\e7ab";
}
.cicon-store-2:before {
  content: "\e7a7";
}
.cicon-store-2-o:before {
  content: "\e7a5";
}
.cicon-sub-left:before {
  content: "\e60b";
}
.cicon-sub-right:before {
  content: "\e60f";
}
.cicon-subtitles:before {
  content: "\e6da";
}
.cicon-subtitles-o:before {
  content: "\e6d8";
}
.cicon-sync-alt:before {
  content: "\e613";
}
.cicon-tag:before {
  content: "\e751";
}
.cicon-tag-o:before {
  content: "\e752";
}
.cicon-taobao:before {
  content: "\e712";
}
.cicon-terminal:before {
  content: "\e7c1";
}
.cicon-terminal-o:before {
  content: "\e7c2";
}
.cicon-thumb-down:before {
  content: "\e6c1";
}
.cicon-thumb-down-o:before {
  content: "\e6c9";
}
.cicon-thumb-up:before {
  content: "\e6c6";
}
.cicon-thumb-up-line:before {
  content: "\e71b";
}
.cicon-thumb-up-line-o:before {
  content: "\e6eb";
}
.cicon-thumb-up-o:before {
  content: "\e6cb";
}
.cicon-ticket:before {
  content: "\e800";
}
.cicon-ticket-o:before {
  content: "\e701";
}
.cicon-time:before {
  content: "\e6f8";
}
.cicon-time-o:before {
  content: "\e6f9";
}
.cicon-timer:before {
  content: "\e69c";
}
.cicon-title:before {
  content: "\e82f";
}
.cicon-titles:before {
  content: "\e745";
}
.cicon-toggle:before {
  content: "\e706";
}
.cicon-toggle-o:before {
  content: "\e733";
}
.cicon-topbar:before {
  content: "\e788";
}
.cicon-translate:before {
  content: "\e79a";
}
.cicon-tree:before {
  content: "\e659";
}
.cicon-Tt:before {
  content: "\e7a2";
}
.cicon-twiter:before {
  content: "\e6e8";
}
.cicon-cicon-community-o:before {
  content: "\e6df";
}
.cicon-undo:before {
  content: "\e61b";
}
.cicon-unfold-less:before {
  content: "\e60e";
}
.cicon-unfold-more:before {
  content: "\e609";
}
.cicon-upstage:before {
  content: "\e724";
}
.cicon-upstage-o:before {
  content: "\e725";
}
.cicon-view-agenda:before {
  content: "\e639";
}
.cicon-view-array:before {
  content: "\e636";
}
.cicon-view-carousel:before {
  content: "\e638";
}
.cicon-view-column:before {
  content: "\e632";
}
.cicon-view-day:before {
  content: "\e627";
}
.cicon-view-headline:before {
  content: "\e62b";
}
.cicon-view-list:before {
  content: "\e63c";
}
.cicon-view-module:before {
  content: "\e629";
}
.cicon-view-quilt:before {
  content: "\e630";
}
.cicon-volume:before {
  content: "\e6c4";
}
.cicon-volume-off:before {
  content: "\e6cd";
}
.cicon-warn:before {
  content: "\e662";
}
.cicon-warn-o:before {
  content: "\e675";
}
.cicon-wechat-pay:before {
  content: "\e7e6";
}
.cicon-weibo-fill:before {
  content: "\e7e4";
}
.cicon-weibo-o:before {
  content: "\e7e3";
}
.cicon-weixin:before {
  content: "\e6de";
}
.cicon-whatshot:before {
  content: "\e6ca";
}
.cicon-whatshot-o:before {
  content: "\e6d0";
}
.cicon-wifi:before {
  content: "\e6d2";
}
.cicon-wifi-off:before {
  content: "\e6d3";
}
.cicon-yamaxun:before {
  content: "\e7b7";
}
.cicon-zuoji:before {
  content: "\e754";
}
.icon-spin {
  -webkit-animation: icon-spin 2s infinite linear;
          animation: icon-spin 2s infinite linear;
}
.icon-pulse {
  -webkit-animation: icon-spin 1s infinite steps(8);
          animation: icon-spin 1s infinite steps(8);
}
@-webkit-keyframes icon-spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
}
}
@keyframes icon-spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
}
}
.icon-90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.icon-180 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.icon-270 {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}
.icon-x {
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1);
}
.icon-y {
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1);
}
.icon-fw {
  width: 1.28571em;
  text-align: center;
}
.icon-xs {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
}
.icon-sm {
  -webkit-transform: scale(0.75);
          transform: scale(0.75);
}
.icon-df {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.icon-lg {
  -webkit-transform: scale(1.25);
          transform: scale(1.25);
}
.icon-xl {
  -webkit-transform: scale(1.5);
          transform: scale(1.5);
}
.icon-xxl {
  -webkit-transform: scale(2);
          transform: scale(2);
}
.icon-sl {
  -webkit-transform: scale(6);
          transform: scale(6);
}
.icon-xsl {
  -webkit-transform: scale(10);
          transform: scale(10);
}
.ui-btn-box {
  display: inline-block;
}
.ui-btn {
  position: relative;
  border: 0rpx;
  display: inline-block;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: .7857em 1.5em .7857em;
  font-size: 28rpx;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin: 0 0.25em 0 0;
  -webkit-transform: translate(0rpx, 0rpx);
          transform: translate(0rpx, 0rpx);
  border-radius: 10rpx;
  white-space: nowrap;
  color: var(--text-a);
  background-color: var(--ui-BG);
  vertical-align: baseline;
}
.ui-btn:first-child:last-child {
  margin: 0;
}
.ui-btn:not([class*='round'])::after {
  border-radius: calc(10rpx * 2);
}
.ui-btn:not([class*='border'])::after {
  display: none;
}
.ui-btn.round::after {
  border-radius: 1000rpx;
}
.ui-btn.icon {
  padding: 0.8em 0.8em;
}
.ui-btn.sm {
  font-size: 24rpx;
}
.ui-btn.lg {
  font-size: 32rpx;
}
.ui-btn.xl {
  font-size: 36rpx;
}
.ui-btn.block {
  width: 100%;
  display: block;
  font-size: 32rpx;
}
.ui-btn[disabled] {
  opacity: 0.6;
}
.ui-btn.none-style {
  background-color: transparent !important;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
}
.ui-btn:not(.icon) [class*='icon-'] {
  margin: 0 .25em;
}
.ui-table {
  background-color: var(--ui-BG);
  max-width: 100%;
  display: table;
}
.ui-table.table-full {
  width: 100%;
}
.ui-table.table-radius {
  border-radius: 10rpx;
}
.ui-table.table-radius .ui-table-header .ui-table-tr {
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
}
.ui-table.table-radius .ui-table-header .ui-table-th:first-child {
  border-top-left-radius: 10rpx;
}
.ui-table.table-radius .ui-table-header .ui-table-th:last-child {
  border-top-right-radius: 10rpx;
}
.ui-table .ui-table-header {
  display: table-header-group;
}
.ui-table .ui-table-header .ui-table-th {
  font-weight: bold;
  border-bottom: 1px solid var(--ui-Border);
  white-space: nowrap;
  padding: 1em .8em;
}
.ui-table .ui-table-tr {
  display: table-row;
  z-index: 1;
}
.ui-table .ui-table-body {
  display: table-row-group;
  position: relative;
}
.ui-table .ui-table-body .ui-table-tr:hover {
  background-color: var(--ui-BG-1) !important;
}
.ui-table .ui-table-body .ui-table-loading {
  min-height: 300px;
  position: absolute !important;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--ui-Border);
}
.ui-table .ui-table-td,
.ui-table .ui-table-th {
  display: table-cell;
  text-align: unset;
  padding: 0.5em .8em;
  vertical-align: middle;
}
.ui-table.table-border,
.ui-table.table-border .ui-table-td,
.ui-table.table-border .ui-table-th {
  position: relative;
}
.ui-table.table-border::after,
.ui-table.table-border .ui-table-td::after,
.ui-table.table-border .ui-table-th::after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 1px solid var(--ui-Border);
  z-index: 1;
}
.ui-table.table-border .ui-table-td::after,
.ui-table.table-border .ui-table-th::after {
  border-width: 1px 1px 0 0;
}
.ui-table.table-border .ui-table-td:last-child::after,
.ui-table.table-border .ui-table-th:last-child::after {
  border-right: none;
}
.ui-table.table-radius::after {
  border-radius: calc(10rpx * 2);
}
.ui-table.table-radius .ui-table-tr .ui-table-th:first-child {
  border-top-left-radius: calc(10rpx * 2);
}
.ui-table.table-radius .ui-table-tr .ui-table-th:last-child {
  border-top-right-radius: calc(10rpx * 2);
}
.ui-table.table-radius .ui-table-tr:last-child .ui-table-td:first-child {
  border-bottom-left-radius: 10rpx;
}
.ui-table.table-radius .ui-table-tr:last-child .ui-table-td:last-child {
  border-bottom-right-radius: 10rpx;
}
.ui-table.table-striped > .ui-table-body > .ui-table-tr:nth-child(2n + 1),
.ui-table.table-striped > .ui-table-body > .ui-table-tr:nth-child(2n + 1) {
  background-color: var(--ui-BG-1);
}
.table-responsive {
  width: inherit;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}
.table-responsive .table-responsive-box {
  position: relative;
  overflow: hidden;
}
.ui-code {
  font-family: Monaco, Menlo, Consolas, 'Courier New';
  font-size: 90%;
  position: relative;
  z-index: 1;
  color: var(--ui-TC);
}
.ui-code .ui-rich-text {
  display: inline-block;
}
.ui-code.code {
  display: inline-block;
  padding: 0 10rpx;
  margin: 0 10rpx;
  border-radius: 6rpx;
  line-height: 1.6;
  vertical-align: baseline;
}
.ui-code.pre {
  display: block;
  margin: 1em 0;
  line-height: 1.6;
}
.ui-code.pre.hasTitle {
  margin: 3.2em 0 1em;
}
.ui-code.pre .ui-code-title {
  position: absolute;
  top: -2.2em;
  color: var(--ui-TC-2);
  left: 0;
}
.ui-code.pre .ui-rich-text {
  padding: 40rpx;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
}
.ui-code.pre .ui-scroll-view.ui-scroll {
  max-height: 500px;
  white-space: pre;
}
.ui-code.pre .ui-copy-btn {
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  padding: 0.8em;
  border-radius: 0 6rpx 0 6rpx;
}
/* PrismJS 1.19.0
https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript */
/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */
._code[class*="language-"],
._pre[class*="language-"] {
  color: black;
  background: none;
  text-shadow: 0 1px white;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  -webkit-hyphens: none;
  hyphens: none;
}
._pre[class*="language-"]::selection, ._pre[class*="language-"] ::selection, ._code[class*="language-"]::selection, ._code[class*="language-"] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}
@media print {
._code[class*="language-"],
  ._pre[class*="language-"] {
    text-shadow: none;
}
}
/* Code blocks */
._pre[class*="language-"] {
  padding: 1em;
  margin: .5em 0;
  overflow: auto;
}
:not(._pre) > ._code[class*="language-"],
._pre[class*="language-"] {
  background: #f5f2f0;
}
/* Inline code */
:not(._pre) > ._code[class*="language-"] {
  padding: .1em;
  border-radius: .3em;
  white-space: normal;
}
.comment,
.prolog,
.doctype,
.cdata {
  color: slategray;
}
.punctuation {
  color: #8799a3;
}
.namespace {
  opacity: .7;
}
.property,
.tag,
.boolean,
.number,
.constant,
.symbol,
.deleted {
  color: #3eb93b;
}
.selector,
.attr-name,
.string,
.char,
.builtin,
.inserted {
  color: #0081ff;
}
.operator,
.entity,
.url,
.language-css .string,
.style .string {
  color: #a5673f;
}
.atrule,
.attr-value,
.keyword {
  color: #37c0fe;
}
.function,
.class-name {
  color: #e54d42;
}
.regex,
.important,
.variable {
  color: #f37b1d;
}
.important,
.bold {
  font-weight: bold;
}
.italic {
  font-style: italic;
}
.entity {
  cursor: help;
}
/* ==================
         表单
 ==================== */
.ui-form-groups {
  padding: 1rpx 24rpx;
  display: flex;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}
.ui-form-groups .title {
  text-align: justify;
  padding-right: 30rpx;
  font-size: 30rpx;
  position: relative;
  height: 60rpx;
  line-height: 60rpx;
}
.ui-form-groups .content {
  flex: 1;
}
.ui-form-groups input {
  flex: 1;
  font-size: 30rpx;
  color: #555;
  padding-right: 20rpx;
}
.ui-form-groups text[class*='icon-'] {
  font-size: 36rpx;
  padding: 0;
  box-sizing: border-box;
}
.ui-form-groups textarea {
  margin: 32rpx 0 30rpx;
  height: 4.6em;
  width: 100%;
  line-height: 1.2em;
  flex: 1;
  font-size: 28rpx;
  padding: 0;
}
.ui-form-groups picker,
.ui-form-groups .arrow {
  flex: 1;
  padding-right: 40rpx;
  overflow: hidden;
  position: relative;
}
.ui-form-groups picker .picker,
.ui-form-groups .arrow > view {
  line-height: 100rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}
.ui-form-groups picker::after,
.ui-form-groups .arrow::after {
  font-family: 'ui';
  display: block;
  content: '\e605';
  position: absolute;
  font-size: 34rpx;
  color: #8799a3;
  line-height: 100rpx;
  width: 60rpx;
  text-align: center;
  top: 0;
  bottom: 0;
  right: -20rpx;
  margin: auto;
}
.ui-form-groups textarea[disabled],
.ui-form-groups textarea[disabled] .placeholder {
  color: transparent;
}
.ui-form-groups.align-start .title {
  height: 1em;
  margin-top: 32rpx;
  line-height: 1em;
}
.ui-form-groups .grid-square > view {
  background-color: #f8f8f8;
  border-radius: 12rpx;
}
.ui-form-groups .grid-square > view .mask {
  background-color: rgba(0, 0, 0, 0.6);
  position: absolute;
  font-size: 20rpx;
  color: #ffffff;
  width: 100%;
  bottom: 0;
  text-align: center;
  padding: 6rpx 0;
}
.ui-form-groups .grid-square > view .mask.red-mask {
  background-color: rgba(255, 80, 80, 0.6);
}
.ui-form-groups .grid-square > view [class*='icon'] {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  -webkit-transform: scale(1.5);
          transform: scale(1.5);
  justify-content: center;
}
.ui-form-groups .grid-square > view .text-gray {
  position: absolute;
  width: 100%;
  font-size: 24rpx;
  text-align: center;
  bottom: 20rpx;
}
.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}
.ui-menu {
  background-color: var(--ui-BG);
}
.ui-menu-item {
  position: relative;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 4em;
  padding: 0 30rpx;
}
.ui-menu-item .ui-menu-item-icon {
  width: 1.7em;
  margin-right: 0.3em;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transform: scale(1.3);
          transform: scale(1.3);
}
.ui-menu-item .ui-menu-item-icon .ui-menu-item-image {
  width: 1.2em;
  height: 1.2em;
  display: inline-block;
}
.ui-menu-item .ui-menu-item-content {
  flex: 1;
  position: relative;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ui-menu-item .ui-menu-item-arrow {
  width: 1.6em;
  text-align: center;
  color: var(--ui-TC-3);
}
.ui-menu-item.first-item::after {
  display: none;
}
.ui-menu-item:first-child::after {
  display: none;
}
.cu-markdown {
  position: relative;
  z-index: 1;
}
.cu-markdown.selectable {
  cursor: auto;
  -webkit-user-select: text;
          user-select: text;
}
.cu-markdown inline {
  display: inline-block;
}
.cu-markdown .list .list-item {
  line-height: 1.8;
}
.cu-markdown .list .list-item .list {
  margin-left: 1.28571em;
}
.cu-markdown .list .list-item .list .ui-title {
  -webkit-transform: scale(0.6);
          transform: scale(0.6);
}
.cu-markdown .list .list-item .list .ui-title:before {
  content: '\e716';
}
.cu-markdown .list .list-item-p {
  position: relative;
  padding-left: 1.5em;
}
.cu-markdown .list .list-item-p .list-item-t {
  display: block;
  width: 1.3em;
  text-align: center;
  position: absolute;
  left: 0;
}
.cu-markdown .md-table + .md-table {
  margin-top: 30rpx;
}
.paragraph, .blockquote {
  margin: 0 0 40rpx;
  line-height: 1.8;
}
.blockquote {
  padding: 20rpx 30rpx;
  border-left-style: solid;
  border-left-width: 10rpx;
  border-color: var(--ui-Border);
  background: none repeat scroll 0 0 rgba(102, 128, 153, 0.05);
}
.blockquote .paragraph, .blockquote .blockquote {
  margin-bottom: 30rpx;
}
.blockquote .paragraph:last-child, .blockquote .blockquote:last-child {
  margin-bottom: 0;
}
/* ==================
         卡片
 ==================== */
.ui-cards {
  display: block;
  overflow: hidden;
}
.ui-cards .ui-btn.badge {
  top: 0;
  right: 0;
  font-size: 24rpx;
  padding: 0rpx 15rpx;
  height: 40rpx;
}
.ui-cards.no-card > .ui-item {
  margin: 0rpx;
  border-radius: 0rpx;
}
.ui-cards > .ui-item {
  display: block;
  overflow: hidden;
  border-radius: 10rpx;
  margin: 30rpx;
}
.ui-cards > .ui-item.shadow-blur {
  overflow: initial;
}
.ui-cards .grid.grid-square {
  margin-bottom: -20rpx;
}
.ui-cards.article {
  display: block;
}
.ui-cards.article > .ui-item {
  padding: 30rpx;
  background-color: var(--box-bg);
  display: flex;
  align-items: flex-start;
}
.ui-cards.article > .time {
  padding: 30rpx 0 0 30rpx;
}
.ui-cards.article > .ui-item .title {
  font-size: 30rpx;
  font-weight: 900;
  color: #333333;
}
.ui-cards.article > .ui-item .content {
  flex: 1;
}
.ui-cards.article > .ui-item > image {
  width: 240rpx;
  height: 6.4em;
  margin-left: 20rpx;
  border-radius: 6rpx;
}
.ui-cards.article > .ui-item .content .desc {
  font-size: 12px;
  color: var(--text-c);
}
.ui-cards.article > .ui-item .content .text-content {
  font-size: 28rpx;
  color: #888;
}
.ui-cards.case .image {
  position: relative;
}
.ui-cards.case .image image {
  width: 100%;
  display: block;
}
.ui-cards.case .image .ui-tag {
  position: absolute;
  right: 0;
  top: 0;
}
.ui-cards.case .image .ui-bar {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: transparent;
  padding: 0rpx 30rpx;
}
.ui-cards.case .image .bg-black {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}
.ui-cards.case.no-card .image {
  margin: 30rpx 30rpx 0;
  overflow: hidden;
  border-radius: 10rpx;
}
.ui-cards.dynamic {
  display: block;
}
.ui-cards.dynamic > .ui-item {
  display: block;
  overflow: hidden;
}
.ui-cards.dynamic > .ui-item > .text-content {
  padding: 0 30rpx 0;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}
.ui-cards.dynamic > .ui-item .square-img {
  width: 100%;
  height: 200rpx;
  border-radius: 6rpx;
}
.ui-cards.dynamic > .ui-item .only-img {
  width: 100%;
  height: 320rpx;
  border-radius: 6rpx;
}
.ui-cards.goods {
  display: block;
}
.ui-cards.goods > .ui-item {
  padding: 30rpx;
  display: flex;
  position: relative;
  background-color: var(--ui-BG);
}
.ui-cards.goods > .ui-item + .ui-item {
  border-top: 1rpx solid #eeeeee;
}
.ui-cards.goods > .ui-item .content {
  width: 410rpx;
  padding: 0rpx;
}
.ui-cards.goods > .ui-item .title {
  font-size: 30rpx;
  font-weight: 900;
  color: #333333;
  line-height: 1.4;
  height: 1.4em;
  overflow: hidden;
}
.ui-cards.goods.col-goods.col-twice {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30rpx;
}
.ui-cards.goods.col-goods.col-twice > .ui-item {
  width: calc(50% - 30rpx);
  margin: 20rpx 20rpx 0rpx 20rpx;
}
.ui-cards.goods.col-goods.col-twice > .ui-item .content {
  padding: 20rpx;
}
.ui-cards.goods.col-goods.col-twice > .ui-item:nth-child(2n) {
  margin-left: 0rpx;
}
.ui-cards.goods.col-goods > .ui-item {
  padding: 0rpx;
  display: block;
  border: 0px;
}
.ui-cards.goods.col-goods > .ui-item .content {
  width: 100%;
  padding: 30rpx;
}
.ui-cards.goods.no-card > .ui-item .content {
  width: 470rpx;
  padding: 0rpx;
}
.ui-cards.goods.no-card > .ui-item .title,
.ui-cards.goods.col-goods > .ui-item .title {
  height: 3em;
  overflow: hidden;
}
.ui-cards.goods > .ui-item .text-linecut-2 {
  -webkit-line-clamp: 1;
}
.ui-cards.goods.no-card > .ui-item .text-linecut-2,
.ui-cards.goods.col-goods > .ui-item .text-linecut-2 {
  -webkit-line-clamp: 2;
  line-height: 1.6em;
  height: 3.2em;
}
.ui-cards.goods > .ui-item > image {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  border-radius: 6rpx;
}
.ui-cards.goods.no-card > .ui-item > image {
  width: 220rpx;
  height: 170rpx;
}
.ui-cards.goods.col-goods > .ui-item > image {
  width: 100%;
  height: 340rpx;
  border-bottom-left-radius: 0rpx;
  border-bottom-right-radius: 0rpx;
  display: block;
}
.ui-cards.goods.col-goods.col-twice > .ui-item > image {
  height: 236rpx;
}
.ui-cards.loan {
  display: block;
}
.ui-cards.loan > .ui-item {
  padding: 30rpx 0 30rpx 30rpx;
  display: flex;
  position: relative;
  background-color: var(--box-bg);
}
.ui-cards.loan > .ui-item .content {
  width: 450rpx;
  padding: 0rpx;
}
.ui-cards.loan > .ui-item .content .tag-list {
  width: 450rpx;
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  margin-top: 18rpx;
}
.ui-cards.loan > .ui-item .action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.ui-cards.houses {
  display: block;
}
.ui-cards.houses > .ui-item {
  padding: 20rpx;
  display: flex;
  position: relative;
  background-color: var(--box-bg);
}
.ui-cards.houses > .ui-item .image {
  width: 230rpx;
  height: 180rpx;
  margin-right: 20rpx;
  border-radius: 6rpx;
}
.ui-cards.houses > .ui-item .content {
  width: 400rpx;
  padding: 0rpx;
}
.ui-cards.houses > .ui-item .content .tag-list {
  width: 400rpx;
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  margin-top: 10rpx;
}
.ui-cards.houses > .ui-item .content .tag-list .ui-item {
  height: 20px;
  line-height: 20px;
}
.ui-cards.houses > .ui-item .action {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.ui-cards.product {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30rpx;
}
.ui-cards.product > .ui-item {
  width: calc(100% - 15rpx);
  margin: 20rpx 20rpx 0rpx 20rpx;
  background-color: var(--box-bg);
  position: relative;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.ui-cards.product > .ui-item .content {
  padding: 20rpx;
}
.ui-cards.product > .ui-item .content .text-cut {
  font-size: 16px;
}
.ui-cards.product > .ui-item .image {
  width: 100%;
  height: 240rpx;
  border-radius: 6rpx 0 0 6rpx;
  display: block;
}
.ui-cards.product > .ui-item .ui-progress-tag {
  width: 4em;
  text-align: right;
  font-size: 12px;
}
.ui-cards.product > .ui-item .border-top {
  width: 100%;
}
.ui-cards.product > .ui-item .ui-tag {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 6rpx 0 6rpx 0;
}
.ui-cards.shop {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30rpx;
}
.ui-cards.shop > .ui-item {
  width: calc(50% - 30rpx);
  margin: 20rpx 20rpx 0rpx 20rpx;
  background-color: var(--box-bg);
  padding: 20rpx;
}
.ui-cards.shop > .ui-item .content {
  margin-top: 15rpx;
}
.ui-cards.shop > .ui-item .image {
  width: 100%;
  height: 285rpx;
  border-radius: 6rpx;
  display: block;
}
.ui-cards.shop > .ui-item:nth-child(2n) {
  margin-left: 0rpx;
}
.ui-cards.orders .ui-item {
  margin-top: 30rpx;
}
.ui-cards.orders .ui-item .address-box {
  padding: 15rpx;
  margin: 0 30rpx 30rpx;
  border: 1px solid;
  border-color: var(--main-a);
  border-radius: 10px;
  position: relative;
}
.ui-cards.orders .ui-item .address-box .ui-form-group {
  min-height: 10px;
}
/*
 * 
 * 微慕小程序开源版
 * author: jianbo
 * organization: 微慕  www.minapper.com
 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
 * 技术支持微信号：iamxjb
 * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.
 * 
 */
page {
  --radius: 12rpx;
}
ad {
  border-radius: var(--radius);
}
image {
  border-radius: var(--radius);
}
.sticky-list-item {
  overflow: hidden;
  display: block;
  height: 60rpx !important;
  line-height: 60rpx;
  max-height: 60rpx;
  padding: 4rpx 0;
}
.dot {
  float: left;
  width: 8rpx;
  height: 8rpx;
  font-size: 0;
  margin: 22rpx 12rpx 10rpx 16rpx;
}
.ui-BG-card {
  margin: 24rpx;
  padding: 24rpx;
  border-radius: var(--radius);
  box-shadow: rgba(12, 12, 12, 0.03) 0px 2px 15px -5px;
}
.heading-3 {
  padding-top: 10rpx !important;
  margin: 0 0 10rpx !important;
}
.header {
  padding: 50rpx 0;
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  border-bottom: 2rpx solid #ededed;
  margin-bottom: 8rpx;
}
.wxParse-p {
  margin-top: 30rpx;
}
.loadingmore {
  margin-top: 24rpx;
  text-align: center;
  margin-bottom: 24rpx;
}
.more-button {
  font-size: 0.785714286rem;
  font-weight: normal;
  color: #959595;
  background-color: #eee;
  background-repeat: repeat-x;
  margin-top: 30rpx;
  width: 240rpx;
  border-radius: 300rpx;
}
.more-button::after {
  border: none;
}
.no-more {
  color: #757575;
  font-size: 30rpx;
  line-height: 1.8rem;
  margin-bottom: 15rpx;
  text-align: center;
  margin-top: 15rpx;
}
/* 文章列表样式 */
.post-item {
  display: flex;
  overflow: hidden;
}
.post-img {
  width: 202.5rpx;
  height: 135rpx;
  border-radius: 12rpx;
  background-color: #959595;
}
.post-desc {
  flex: 1;
  margin-right: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.post-title {
  height: 80rpx;
  margin-bottom: 20rpx;
}
.post-title > text {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.post-data {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.post-data > image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
}
.post-data > text {
  margin-right: 32rpx;
}
/*common list end */
.showerror {
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  color: #757575;
  margin-top: 100rpx;
}
.errortext {
  margin-top: 50rpx;
  table-layout: center;
}
.copyright {
  font-size: 26rpx;
  line-height: 1.6;
  font-weight: 400;
  text-align: center;
  color: #c4c4c4;
  margin-top: 48rpx;
  padding-bottom: 80rpx;
}
.common-gap {
  width: 100%;
  height: 24rpx;
  background-color: #f5f7f7;
}
/* 进度条 */
.sk-three-bounce {
  margin: 20rpx auto;
  width: 200rpx;
  text-align: center;
}
.sk-three-bounce .sk-child {
  width: 30rpx;
  height: 30rpx;
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
  animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
}
.sk-three-bounce .sk-bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.sk-three-bounce .sk-bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
@-webkit-keyframes sk-three-bounce {
0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
@keyframes sk-three-bounce {
0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
    transform: scale(1);
}
}
/* 登录框 */
.login-popup .zan-popup__container {
  background-color: transparent;
}
.login-popup-wrapper {
  margin: 0 60rpx;
  overflow: hidden;
  border-radius: var(--radius);
}
.logintips {
  color: #7c7c7c;
  font-size: 24rpx;
  padding: 0 48rpx;
}
.login-popup button {
  background-color: transparent;
  padding: 0;
}
.login-popup .login-inner {
  padding: 48rpx;
}
.login-inner {
  display: flex;
}
.login-inner .avatar {
  width: 50px;
  height: 50px;
  margin-right: 10px;
  flex-shrink: 0;
  border-radius: 500px;
}
.login-inner .close-btn {
  width: 20px;
  height: 20px;
}
.login-inner .username {
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.login-inner .username text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 32rpx;
}
.login-btn-box {
  display: flex;
  border-top: 1px solid var(--ui-TC-3);
}
.login-btn-box button {
  width: 50%;
  border-radius: 0;
  font-size: 15px;
  line-height: 3;
}
.login-btn-box button:first-child {
  border-right: 1px solid var(--ui-TC-3);
}
.login-btn-box button::after {
  border: none;
}
/* 弹窗按钮 */
.popup-btn {
  display: flex;
  width: 100%;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  text-align: left;
  border: 0;
  border-radius: 0;
  background-color: #fff;
}
.popup-btn .button-main {
  flex-grow: 1;
  overflow: hidden;
}
.popup-btn .go-popup {
  display: flex;
  align-items: center;
}
.popup-btn .go-popup image {
  width: 15px;
  height: 15px;
}
/* 弹窗 */
.login-popup .zan-popup__mask {
  z-index: 11;
}
.login-popup .zan-popup__container {
  z-index: 12;
}
/* 弹窗按钮 */
.popup-btn {
  display: flex;
  width: 100%;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  text-align: left;
  border: 0;
  border-radius: 0;
  background-color: #fff;
}
.popup-btn .button-main {
  flex-grow: 1;
  overflow: hidden;
}
.popup-btn .go-popup {
  display: flex;
  align-items: center;
}
.popup-btn .go-popup image {
  width: 15px;
  height: 15px;
}
/* 弹窗 */
.login-popup .zan-popup__mask {
  z-index: 998;
}
.login-popup .zan-popup__container {
  z-index: 999;
}
.zan-popup {
  visibility: hidden;
}
.zan-popup--show {
  visibility: visible;
}
.zan-popup__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  display: none;
}
.zan-popup__container {
  position: fixed;
  left: 50%;
  top: 50%;
  background: #fff;
  -webkit-transform: translate3d(-50%, -50%, 0);
          transform: translate3d(-50%, -50%, 0);
  -webkit-transform-origin: center;
          transform-origin: center;
  transition: all .4s ease;
  z-index: 11;
  opacity: 0;
}
.zan-popup--show .zan-popup__container {
  opacity: 1;
}
.zan-popup--show .zan-popup__mask {
  display: block;
}
/* 清除浮动通用代码 */
.clearfix::after {
  content: '.';
  clear: both;
  display: block;
  height: 0;
  visibility: hidden;
}
.clearfix {
  zoom: 1;
}
.ditail-copyright {
  font-size: 26rpx;
  line-height: 1.2;
  font-weight: normal;
  text-align: center;
  color: #999;
  margin-top: 20rpx;
  margin-bottom: 120rpx;
}

