<ui-fixed vue-id="41e13d42-1" noFixed="{{noFixed}}" val="{{0}}" index="{{1000}}" noNav="{{true}}" bg="{{bg}}" ui="{{ui}}" opacity="{{opacity}}" opacityVal="{{opacityVal}}" placeholder="{{placeholder}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['ui-navbar-box',[(!opacity&&bg!='bg-none')?'border-bottom':'']]}}" style="{{'padding-top:'+(sys_statusBar+'px')+';'+('height:'+(sys_navBar+'px')+';')}}"><view class="{{['ui-bar',status==''?'text-a':status=='light'?'text-white':'text-black']}}" style="{{'height:'+(sys_navBar-sys_statusBar+'px')+';'}}"><block wx:if="{{slots.default}}"><block><slot></slot></block></block><block wx:else><block><view class="left"><block wx:if="{{back&&!capsule}}"><view data-event-opts="{{[['tap',[['_navback',['$event']]]]]}}" class="back" bindtap="__e"><view class="{{['back-icon','icon-fw','text-xl',sys_isFirstPage?'_icon-home-o':'_icon-back',statusCur,[(opacityBg)?'opacityIcon':'']]}}"></view><view>{{backtext}}</view></view></block><block wx:if="{{capsule}}"><view class="{{['capsule',[(sys_isFirstPage)?'isFristPage':''],statusCur]}}" style="{{$root.s0}}"><view data-event-opts="{{[['tap',[['_navback',['$event']]]]]}}" class="capsule-back" bindtap="__e"><text class="icon-fw text-xl _icon-back"></text></view><view data-event-opts="{{[['tap',[['_toHome',['$event']]]]]}}" class="capsule-home" bindtap="__e"><text class="{{['icon-fw','text-xl',sys_isFirstPage?'_icon-home':'_icon-home-o']}}"></text></view></view></block><slot name="left"></slot></view><block wx:if="{{slots.center}}"><view class="center"><slot name="center"></slot></view></block><block wx:if="{{slots.center&&!slots.right}}"><view class="right"></view></block><block wx:if="{{slots.right}}"><view class="right"><view class="right-content"><slot name="right"></slot></view><view style="{{$root.s1}}"></view></view></block></block></block></view></view></ui-fixed>