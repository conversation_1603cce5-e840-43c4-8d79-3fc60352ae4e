@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ui-navbar-box {
  background-color: transparent;
  width: 100%;
}
.ui-navbar-box .ui-bar {
  position: relative;
  z-index: 2;
  white-space: nowrap;
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
}
.ui-navbar-box .ui-bar .left {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.ui-navbar-box .ui-bar .left .back {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ui-navbar-box .ui-bar .left .back .back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  margin: 0 10rpx;
}
.ui-navbar-box .ui-bar .left .back .back-icon.opacityIcon {
  position: relative;
  border-radius: 50%;
  background-color: rgba(127, 127, 127, 0.5);
}
.ui-navbar-box .ui-bar .left .back .back-icon.opacityIcon::after {
  content: '';
  display: block;
  position: absolute;
  height: 200%;
  width: 200%;
  left: 0;
  top: 0;
  border-radius: inherit;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  opacity: 0.1;
  border: 1px solid currentColor;
  pointer-events: none;
}
.ui-navbar-box .ui-bar .left .back .back-icon.opacityIcon::before {
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
}
.ui-navbar-box .ui-bar .left .capsule {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 100px;
  position: relative;
}
.ui-navbar-box .ui-bar .left .capsule.dark {
  background-color: rgba(255, 255, 255, 0.5);
}
.ui-navbar-box .ui-bar .left .capsule.light {
  background-color: rgba(0, 0, 0, 0.15);
}
.ui-navbar-box .ui-bar .left .capsule::after {
  content: '';
  display: block;
  position: absolute;
  height: 60%;
  width: 1px;
  left: 50%;
  top: 20%;
  background-color: currentColor;
  opacity: 0.1;
  pointer-events: none;
}
.ui-navbar-box .ui-bar .left .capsule::before {
  content: '';
  display: block;
  position: absolute;
  height: 200%;
  width: 200%;
  left: 0;
  top: 0;
  border-radius: inherit;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  opacity: 0.1;
  border: 1px solid currentColor;
  pointer-events: none;
}
.ui-navbar-box .ui-bar .left .capsule .capsule-back,
.ui-navbar-box .ui-bar .left .capsule .capsule-home {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.ui-navbar-box .ui-bar .left .capsule.isFristPage .capsule-back, .ui-navbar-box .ui-bar .left .capsule.isFristPage::after {
  display: none;
}
.ui-navbar-box .ui-bar .right {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.ui-navbar-box .ui-bar .right .right-content {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  flex: 1;
}
.ui-navbar-box .ui-bar .center {
  display: flex;
  align-items: center;
  justify-content: center;
  text-overflow: ellipsis;
  text-align: center;
  flex: 2;
}
.ui-navbar-box .ui-bar .center .image {
  display: block;
  height: 36px;
  max-width: calc(100vw - 200px);
}
.ui-navbar-box .ui-bar-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 1;
  pointer-events: none;
}

