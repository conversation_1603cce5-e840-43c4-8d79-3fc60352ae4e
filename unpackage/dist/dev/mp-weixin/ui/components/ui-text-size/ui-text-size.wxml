<view><ui-menu vue-id="5e64c91a-1" ui="{{ui}}" bind:__l="__l" vue-slots="{{['default']}}"><ui-menu-item vue-id="{{('5e64c91a-2')+','+('5e64c91a-1')}}" title="文字大小" data-event-opts="{{[['tap',[['e0']]]]}}" bindtap="__e" bind:__l="__l"></ui-menu-item></ui-menu><ui-modal vue-id="5e64c91a-3" name="textSizeSet" title="文字大小" bind:__l="__l" vue-slots="{{['default']}}"><view class="p-3 mx-5 text-center mt-4">选择合适的文字大小来动态调整界面布局。</view><view class="flex-bar p-3"><view class="p-2" style="font-size:12px;">小</view><view class="p-2 flex-sub"><slider value="{{sys_text}}" min="{{0}}" max="{{4}}" data-event-opts="{{[['change',[['sliderChange',['$event']]]]]}}" bindchange="__e"></slider></view><view class="p-2 mr-2" style="font-size:20px;">大</view></view></ui-modal></view>