@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ui-switch {
  display: inline-block;
  cursor: pointer;
}
.ui-switch .ui-switch-wrapper {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}
.ui-switch .ui-switch-input {
  -webkit-appearance: none;
          appearance: none;
  position: relative;
  width: 47px;
  height: 26px;
  outline: 0;
  border-radius: 16px;
  box-sizing: border-box;
  background-color: rgba(119, 119, 119, 0.3);
  transition: background-color 0.1s, border 0.1s;
}
.ui-switch .ui-switch-input:after {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 200px;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
  width: 20px;
  height: 20px;
  margin: 3px;
  background-color: #fff;
}
.ui-switch .ui-switch-input.ui-switch-input-checked:after {
  -webkit-transform: translateX(21px);
          transform: translateX(21px);
}
.ui-switch.disabled {
  cursor: not-allowed;
}
.ui-switch.disabled .ui-switch-input {
  opacity: 0.7;
}

