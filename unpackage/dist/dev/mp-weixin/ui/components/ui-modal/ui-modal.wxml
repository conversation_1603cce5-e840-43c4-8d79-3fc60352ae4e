<view><view data-event-opts="{{[['tap',[['_cancel',['$event']]]]]}}" class="{{['ui-modal',align,tpl,name==target?'show':'hide',[(mask||mask=='80')?'bg-mask-80':''],[(mask=='20')?'bg-mask-20':''],[(mask=='40')?'bg-mask-40':'']]}}" style="{{'top:'+((noNav?0:sys_navBar+top)+'px')+';'}}" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="{{['ui-dialog',transparent?'bg-none':'',ui]}}" style="{{(dialog)}}" catchtap="__e"><block wx:if="{{title!=''}}"><view class="ui-modal-title border-bottom"><ui-title vue-id="43e7931a-1" title="{{title}}" align="center" text="text-xl" bind:__l="__l"></ui-title></view></block><block wx:if="{{name!='sys_dialog'}}"><slot></slot></block><block wx:if="{{content!=''}}"><view class="ui-modal-content">{{content}}</view></block><block wx:if="{{option&&!iconCancel}}"><view class="{{['ui-modal-option','flex-bar',(!transparent)?'border-top':'']}}"><block wx:if="{{showCancel}}"><view data-event-opts="{{[['tap',[['_cancel',['$event']]]]]}}" class="ui-modal-btn border-right" bindtap="__e">{{cancelText}}</view></block><view data-event-opts="{{[['tap',[['_confirm',['$event']]]]]}}" class="ui-modal-btn ui-TC-Main" bindtap="__e">{{confirmText}}</view></view></block><block wx:if="{{iconCancel&&transparent}}"><view data-event-opts="{{[['tap',[['_cancel',['$event']]]]]}}" class="flex-center" bindtap="__e"><button class="ui-btn bg-none icon xxl borders round border-white"><text class="_icon-close text-white"></text></button></view></block></view></view></view>