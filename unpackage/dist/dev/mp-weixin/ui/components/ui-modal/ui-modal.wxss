@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ui-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 880;
  opacity: 0;
  outline: 0;
  text-align: center;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-perspective: 2000rpx;
          perspective: 2000rpx;
  transition: 0.3s;
  pointer-events: none;
}
.ui-modal .ui-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 680rpx;
  max-width: 100%;
  background-color: var(--ui-BG);
  border-radius: 10rpx;
  overflow: hidden;
  text-align: left;
  color: var(--ui-TC);
}
.ui-modal .ui-dialog .ui-modal-content {
  padding: 10rpx 50rpx 50rpx;
  font-size: 32rpx;
  color: var(--ui-TC-2);
}
.ui-modal .ui-dialog .ui-modal-content:first-child {
  padding: 50rpx;
  color: var(--ui-TC);
}
.ui-modal .ui-dialog .ui-modal-option {
  display: flex;
  padding: 15rpx 0;
  justify-content: center;
}
.ui-modal .ui-dialog .ui-modal-option .ui-modal-btn {
  flex: 1;
  padding: 15rpx 0;
  text-align: center;
}
.ui-modal .ui-dialog.bg-none .ui-modal-option {
  background: transparent;
  color: #FFFFFF;
}
.ui-modal .ui-dialog.bg-none .ui-modal-option .ui-modal-btn {
  border: 1px solid #FFFFFF;
  border-radius: 500px;
  margin: 0 40rpx;
  color: #FFFFFF !important;
}
.ui-modal.show {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}
.ui-modal.show.bg-none {
  pointer-events: none;
}
.ui-modal.show.bg-none .ui-dialog {
  pointer-events: auto;
}
.ui-modal.bottom {
  margin-bottom: -1000rpx;
}
.ui-modal.bottom::after {
  vertical-align: bottom;
}
.ui-modal.bottom .ui-dialog {
  align-self: flex-end;
  width: 100%;
  border-radius: 0;
  padding-bottom: calc(env(safe-area-inset-bottom) / 4 * 3);
}
.ui-modal.bottom.show {
  margin-bottom: 0;
}
.ui-modal.top {
  margin-top: -1000rpx;
}
.ui-modal.top::after {
  vertical-align: top;
}
.ui-modal.top .ui-dialog {
  align-self: flex-start;
  width: 100%;
  border-radius: 0;
}
.ui-modal.top.show {
  margin-top: 0;
}
.ui-modal.left, .ui-modal.right {
  -webkit-transform: scale(1);
          transform: scale(1);
  display: flex;
}
.ui-modal.right {
  justify-content: flex-end;
}
.ui-modal.left .ui-dialog,
.ui-modal.right .ui-dialog {
  height: 100%;
  min-width: 200rpx;
  border-radius: 0;
  margin: initial;
  transition: 0.3s;
  width: auto;
  padding-bottom: calc(env(safe-area-inset-bottom) / 4 * 3);
}
.ui-modal.left .ui-dialog {
  -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
}
.ui-modal.right .ui-dialog {
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}
.ui-modal.left.show .ui-dialog,
.ui-modal.right.show .ui-dialog {
  -webkit-transform: translateX(0%);
          transform: translateX(0%);
}

