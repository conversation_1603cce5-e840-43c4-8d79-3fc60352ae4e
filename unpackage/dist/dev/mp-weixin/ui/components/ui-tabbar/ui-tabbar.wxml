<ui-fixed vue-id="015903c3-1" bottom="{{true}}" bg="{{bg}}" placeholder="{{true}}" ui="{{ui}}" noFixed="{{noFixed}}" data-event-opts="{{[['^getHeight',[['tabbarheight']]]]}}" bind:getHeight="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="ui-tabbar-box"><view class="ui-tabbar"><block wx:for="{{tabbar}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['clickItem',['$0'],[[['tabbar','',index]]]]]]]}}" class="{{['ui-tabbar-item',index==cur?curText:text,item.type,[(index==cur)?'cur':'']]}}" bindtap="__e"><view class="{{['ui-tabbar-icon',item.center?'center border shadow-'+sys_main:'']}}"><text class="{{[index==cur?item.curIcon:item.icon]}}"></text></view><view class="ui-tabbar-text">{{item.title}}</view></view></block></block><slot></slot></view></view></ui-fixed>