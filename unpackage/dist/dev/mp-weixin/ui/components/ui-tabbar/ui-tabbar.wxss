@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ui-tabbar-box .ui-tabbar {
  padding: 10rpx;
  display: flex;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item {
  text-align: center;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  z-index: 10;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-tabbar-icon {
  height: 60rpx;
  width: 60rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 45rpx;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-tabbar-icon .ui-tabbar-image {
  width: 50rpx;
  height: 50rpx;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-tabbar-icon.center {
  background-color: var(--ui-BG-Main);
  border-radius: 50%;
  color: #ffffff;
  font-size: 32rpx;
  -webkit-transform: scale(1.3) translateY(-6px);
          transform: scale(1.3) translateY(-6px);
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-tabbar-icon .badge {
  top: 0;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-btn {
  margin-top: -10px;
  margin-bottom: 5px;
  position: relative;
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item .ui-tabbar-text {
  font-size: 24rpx;
  color: var(--ui-TC-2);
}
.ui-tabbar-box .ui-tabbar .ui-tabbar-item.cur .ui-tabbar-text {
  color: var(--ui-TC-Main);
}

