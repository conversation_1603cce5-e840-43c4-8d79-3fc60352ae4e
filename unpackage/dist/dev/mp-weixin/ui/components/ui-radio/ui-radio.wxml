<view data-event-opts="{{[['tap',[['_onRadioClick',['$event']]]]]}}" class="{{['ui-radio',[(isDisabled)?'disabled':''],[(src)?'img':''],ui]}}" bindtap="__e"><block wx:if="{{!none}}"><view class="{{['ui-radio-input',isChecked?'cur '+bg:unbg,src?'radius':'round']}}"></view></block><block wx:if="{{src}}"><image class="ui-radio-img radius" src="{{src}}" mode="aspectFill"></image></block><block wx:else><view class="ui-radio-content"><slot></slot></view></block><block wx:if="{{$root.m0}}"><view class="{{['ui-radio-bg','round',isChecked?'cur '+bg:'']}}"></view></block></view>