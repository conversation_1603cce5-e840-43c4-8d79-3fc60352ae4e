@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ui-toast-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 880;
  opacity: 0;
  outline: 0;
  text-align: center;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  -webkit-perspective: 2000rpx;
          perspective: 2000rpx;
  pointer-events: none;
}
.ui-toast-box .ui-toast {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  background-color: var(--ui-BG);
  border-radius: 10rpx;
  overflow: hidden;
  text-align: left;
  color: var(--ui-TC);
  padding: 0.5em 1em;
  border-radius: 10rpx;
  max-width: 90vw;
  max-height: 20vh;
  overflow: hidden;
}
.ui-toast-box .ui-toast.hasIcon {
  text-align: center;
  padding: 0.5em 1em 1em;
}
.ui-toast-box .ui-toast.hasIcon .ui-toast-icon {
  display: block;
  width: 170rpx;
  height: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
}
.ui-toast-box::before {
  content: '\200B';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.ui-toast-box.show {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: 0.3s;
  overflow-x: hidden;
  overflow-y: auto;
}

