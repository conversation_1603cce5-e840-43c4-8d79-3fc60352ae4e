<view class="ui-fixed"><view class="{{['ui-fixed-box',[(fixed)?'fixed':'']]}}" style="{{'left:'+(sticky?'auto':'0px')+';'+('top:'+(fixed&&!bottom?(noNav?val:val+sys_navBar)+'px':'auto')+';')+('bottom:'+(fixed&&bottom?val+'px':'auto')+';')+('z-index:'+(index+sys_layer)+';')}}" id="{{'fixed-'+_uid}}"><view data-event-opts="{{[['tap',[['_toTop',['$event']]]]]}}" class="ui-fixed-content" style="{{'z-index:'+(index+sys_layer)+';'}}" bindtap="__e"><slot></slot></view><block wx:if="{{bottom&&!noSafe}}"><view class="{{['ui-fixed-bottom',bg]}}"></view></block><view class="{{['ui-fixed-bg',ui,bg]}}" style="{{$root.s0}}"></view></view><block wx:if="{{sticky?fixed:placeholder&&fixed}}"><view class="skeleton" style="{{'height:'+(content.height+'px')+';'+('width:'+(width+'px')+';')}}" id="{{'skeleton-'+_uid}}"></view></block></view>