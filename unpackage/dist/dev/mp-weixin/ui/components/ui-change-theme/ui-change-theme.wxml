<view><view class="{{['ui-menu','sm-border',ui]}}"><view data-event-opts="{{[['tap',[['setTheme',[sys_theme=='auto'?'light':'auto']]]]]}}" class="ui-menu-item" bindtap="__e"><view class="ui-menu-item-content">跟随系统</view><view class="action"><ui-switch vue-id="59a8eca7-1" checked="{{sys_theme=='auto'}}" bind:__l="__l"></ui-switch></view></view><block wx:if="{{sys_theme!='auto'}}"><view class="ui-menu-item"><view class="flex flex-center py-5 text-center flex-sub"><view class="pr-5"><view data-event-opts="{{[['tap',[['setTheme',['light']]]]]}}" class="{{['mobile-phone','border',(sys_theme=='light')?'cur':'']}}" bindtap="__e"><view class="ui-BG-Main radius"></view><view class="bg-gray radius"></view><view class="bg-gray radius"></view><view class="borders border-top mt-5 mx-4 border-dark-6"></view></view><view>浅色</view></view><view class="pl-5"><view data-event-opts="{{[['tap',[['setTheme',['dark']]]]]}}" class="{{['mobile-phone','border','dark',(sys_theme=='dark')?'cur':'']}}" bindtap="__e"><view class="ui-BG-Main radius"></view><view class="bg-dark-4 radius"></view><view class="bg-dark-4 radius"></view><view class="borders border-top mt-5 mx-4 border-white"></view></view><view>深色</view></view></view></view></block><view class="ui-menu-item"><view class="ui-menu-item-content">强调色</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="action" bindtap="__e"><ui-radio vue-id="59a8eca7-2" bg="{{'bg-'+sys_main+' shadow-'+sys_main+' shadow-sm'}}" bind:__l="__l"></ui-radio></view></view></view><ui-modal vue-id="59a8eca7-3" name="ColorPicker" bind:__l="__l" vue-slots="{{['default']}}"><view class="ui-grid ui-cols-6 p-3"><block wx:for="{{color}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['chooseColor',['$0'],[[['color','',index]]]]]]]}}" class="text-center ui-item py-2 my-2" bindtap="__e"><ui-radio vue-id="{{('59a8eca7-4-'+index)+','+('59a8eca7-3')}}" value="{{sys_main}}" label="{{item}}" bg="{{'bg-'+item+' shadow-'+item+' shadow-sm'}}" unbg="{{'bg-'+item+' shadow-'+item+' shadow-sm'}}" bind:__l="__l"></ui-radio></view></block></view></ui-modal></view>