@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
ui-title {
  display: block;
}
.ui-title {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ui-title .anchor {
  pointer-events: none;
  position: absolute;
}
.ui-title.line {
  padding: 0 30rpx;
}
.ui-title .action-icon {
  margin-left: -10rpx;
  margin-right: 10rpx;
}
.ui-title .action {
  display: flex;
  align-items: center;
}
.ui-title .action.hasLine {
  position: relative;
  z-index: 2;
}
.ui-title .action.hasLine .action-line {
  position: absolute;
  width: 68%;
  height: 0.5em;
  bottom: 0;
  z-index: -1;
  opacity: 0.7;
}
.ui-title .action.hasDot {
  position: relative;
  margin-left: 10rpx;
  z-index: 2;
}
.ui-title .action.hasDot .action-dot {
  position: absolute;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  top: -0.3em;
  z-index: -1;
  margin-left: -0.5em;
  opacity: 0.7;
}
.ui-title .action .action-title {
  font-weight: bold;
}
.ui-title .action .action-desc {
  margin-left: 10rpx;
  text-transform: Capitalize;
}
.ui-title.center {
  justify-content: center;
  letter-spacing: 5rpx;
}
.ui-title.center .hasLine .action-line {
  left: 0;
  right: 0;
  margin: auto;
}
.ui-title.right {
  justify-content: flex-end;
  letter-spacing: 5rpx;
}
.ui-title.right .hasDot .action-dot {
  left: auto;
  right: 0;
}
.ui-title.right .hasLine .action-line {
  left: auto;
  right: 0;
}
.ui-title.stack .action {
  position: relative;
}
.ui-title.stack .action.hasLine .action-line {
  width: 100%;
}
.ui-title.stack .action .action-title {
  position: relative;
  z-index: 2;
  top: 10rpx;
}
.ui-title.stack .action .action-desc {
  position: absolute;
  z-index: 1;
  left: 30rpx;
  margin: 0;
  font-size: 60rpx;
  font-weight: bold;
  opacity: 0.3;
  top: -10rpx;
}
.ui-title.stack.center, .ui-title.stack.right {
  position: relative;
  z-index: 1;
}
.ui-title.stack.center .action, .ui-title.stack.right .action {
  position: initial;
}
.ui-title.stack.center .action-desc, .ui-title.stack.right .action-desc {
  top: 10rpx;
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
}
.ui-title.stack.right .action-desc {
  text-align: right;
}
.ui-title.center-column {
  justify-content: center;
  letter-spacing: 5rpx;
}
.ui-title.center-column .action {
  flex-direction: column;
}
.ui-title.center-column .action .action-desc {
  font-size: 24rpx;
  margin: 0;
}
.ui-title.center-column .action.hasDot {
  position: relative;
}
.ui-title.center-column .action.hasDot .action-dot {
  height: 0px;
  width: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.ui-title.center-column .action.hasDot .action-dot view {
  height: 1px;
  background: inherit;
  width: 100rpx;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
}
.ui-title.center-column .action.hasDot .action-dot view:first-child {
  left: -500rpx;
  right: 0;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.ui-title.center-column .action.hasDot .action-dot view:last-child {
  left: 0;
  right: -500rpx;
}
.ui-title.center-column .action.hasDot .action-dot view::after {
  content: '';
  width: 4px;
  height: 12px;
  border-radius: 20px;
  background: inherit;
  display: block;
  position: absolute;
  top: -5px;
  left: -10px;
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg);
}
.ui-title.center-column .action.hasDot .action-dot view::before {
  content: '';
  width: 4px;
  height: 20px;
  border-radius: 20px;
  background: inherit;
  display: block;
  position: absolute;
  top: -10px;
  left: -20px;
  -webkit-transform: rotate(30deg);
          transform: rotate(30deg);
}
.ui-title.center-column .action.hasLine {
  position: relative;
}
.ui-title.center-column .action.hasLine .action-line {
  height: 0px;
  width: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.ui-title.center-column .action.hasLine .action-line view {
  height: 1px;
  background: inherit;
  width: 160rpx;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
}
.ui-title.center-column .action.hasLine .action-line view:first-child {
  left: -480rpx;
  right: 0;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.ui-title.center-column .action.hasLine .action-line view:last-child {
  left: 0;
  right: -480rpx;
}
.ui-title.center-column .action.hasLine .action-line view::after {
  content: '';
  width: 100%;
  height: 100%;
  background: linear-gradient(to left, var(--ui-BG), rgba(255, 255, 255, 0));
  display: block;
  position: absolute;
}
.heading-0 {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  font-size: calc(36rpx + var(--textSize)) !important;
}
.heading-0 .action-desc {
  font-size: calc(28rpx + var(--textSize)) !important;
}
.heading-1 {
  padding-top: 60rpx;
  padding-bottom: 30rpx;
  font-size: calc(44rpx + var(--textSize)) !important;
}
.heading-1 .action-desc {
  font-size: calc(32rpx + var(--textSize)) !important;
}
.heading-2 {
  padding-top: 30rpx;
  padding-bottom: 20rpx;
  font-size: calc(36rpx + var(--textSize)) !important;
}
.heading-2 .action-desc {
  font-size: calc(28rpx + var(--textSize)) !important;
}
.heading-3 {
  padding-top: 20rpx;
  padding-bottom: 10rpx;
  font-size: calc(32rpx + var(--textSize)) !important;
}
.heading-3 .action-desc {
  font-size: calc(24rpx + var(--textSize)) !important;
}
.heading-4 {
  padding-top: 10rpx;
  padding-bottom: 5rpx;
  font-size: calc(28rpx + var(--textSize)) !important;
}
.heading-4 .action-desc {
  font-size: calc(20rpx + var(--textSize)) !important;
}

