<view><view class="{{['ui-swiper',mode,bg,ui]}}"><swiper style="{{'height:'+(height)+';'}}" circular="{{circular}}" autoplay="{{true}}" interval="{{interval}}" duration="500" data-event-opts="{{[['change',[['swiperChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.enable=='yes'}}"><swiper-item class="{{[(cur==index)?'cur':'']}}" data-item="{{item}}" id="{{item.id}}" index="{{index}}" data-type="{{item.type}}" data-appid="{{item.appid}}" data-path="{{item.path}}" data-url="{{item.url}}" data-event-opts="{{[['tap',[['redictAppDetail',['$event']]]]]}}" bindtap="__e"><view class="ui-swiper-main radius"><image style="{{'height:'+(imgHeight)+';'}}" src="{{item.image}}" mode="aspectFill"></image></view></swiper-item></block></block></swiper><block wx:if="{{dotStyle!='tag'}}"><view class="{{['ui-swiper-dot',dotStyle]}}"><block wx:for="{{info}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{[cur==index?'cur':'',dotCur]}}"></view></block></view></block><block wx:if="{{dotStyle=='tag'}}"><view class="{{['ui-swiper-dot',dotStyle]}}"><view class="{{['ui-tag','radius',dotCur]}}"><view>{{cur+1+" / "+$root.g0}}</view></view></view></block></view></view>