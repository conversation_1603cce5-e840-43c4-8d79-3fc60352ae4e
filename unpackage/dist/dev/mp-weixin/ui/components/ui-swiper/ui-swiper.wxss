@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
 .ui-swiper {
  position: relative;
}
 .ui-swiper .ui-swiper-main {
  width: 100%;
  height: 100%;
}
 .ui-swiper .ui-swiper-main image {
  width: 100%;
  height: 100%;
}
 .ui-swiper .ui-swiper-dot {
  position: absolute;
  width: 100%;
  bottom: 20rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
 .ui-swiper .ui-swiper-dot.default view {
  display: inline-flex;
  border-radius: 50rpx;
  width: 6px;
  height: 6px;
  border: 2px solid transparent;
  margin: 0 10rpx;
  opacity: 0.3;
  position: relative;
  justify-content: center;
  align-items: center;
}
 .ui-swiper .ui-swiper-dot.default view.cur {
  width: 8px;
  height: 8px;
  opacity: 1;
  border: 0px solid transparent;
}
 .ui-swiper .ui-swiper-dot.default view.cur::after {
  content: '';
  border-radius: 50rpx;
  width: 4px;
  height: 4px;
  background-color: #fff;
}
 .ui-swiper .ui-swiper-dot.long view {
  display: inline-block;
  border-radius: 100rpx;
  width: 6px;
  height: 6px;
  margin: 0 10rpx;
  opacity: 0.3;
  position: relative;
}
 .ui-swiper .ui-swiper-dot.long view.cur {
  width: 24rpx;
  opacity: 1;
}
 .ui-swiper .ui-swiper-dot.line {
  bottom: 20rpx;
}
 .ui-swiper .ui-swiper-dot.line view {
  display: inline-block;
  width: 30px;
  height: 3px;
  opacity: 0.3;
  position: relative;
}
 .ui-swiper .ui-swiper-dot.line view.cur {
  opacity: 1;
}
 .ui-swiper .ui-swiper-dot.tag {
  justify-content: flex-end;
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
}
 .ui-swiper.card swiper-item {
  width: 610rpx !important;
  left: 70rpx;
  box-sizing: border-box;
  padding: 20rpx 0rpx 60rpx;
  overflow: initial;
}
 .ui-swiper.card swiper-item .ui-swiper-main {
  width: 100%;
  display: block;
  height: 100%;
  border-radius: 10rpx;
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  transition: all 0.2s ease-in 0s;
  position: relative;
  background-size: cover;
}
 .ui-swiper.card swiper-item .ui-swiper-main image {
  height: 100%;
  border-radius: inherit;
}
 .ui-swiper.card swiper-item .ui-swiper-main::before {
  content: '';
  display: block;
  background: inherit;
  -webkit-filter: blur(5px);
          filter: blur(5px);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10rpx;
  left: 10rpx;
  z-index: -1;
  opacity: 0.3;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  border-radius: inherit;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}
 .ui-swiper.card swiper-item.cur .ui-swiper-main {
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: all 0.2s ease-in 0s;
}
 .ui-swiper.card .ui-swiper-dot.tag {
  position: absolute;
  bottom: 85rpx;
  right: 75rpx;
}
 .ui-swiper.hotelCard swiper-item {
  width: 650rpx !important;
  left: 30rpx;
  box-sizing: border-box;
  padding: 0rpx 0rpx 50rpx;
  overflow: initial;
}
 .ui-swiper.hotelCard swiper-item .ui-swiper-main {
  width: 100%;
  display: block;
  height: 100%;
  border-radius: 10rpx;
  -webkit-transform: scale(0.9);
          transform: scale(0.9);
  opacity: 0.8;
  transition: all 0.2s ease-in 0s;
  position: relative;
  background-size: cover;
}
 .ui-swiper.hotelCard swiper-item .ui-swiper-main image {
  width: 100%;
  height: 400rpx;
  border-radius: 10rpx 10rpx 0 0;
}
 .ui-swiper.hotelCard swiper-item .ui-swiper-main::before {
  content: '';
  display: block;
  background: inherit;
  -webkit-filter: blur(5px);
          filter: blur(5px);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10rpx;
  left: 10rpx;
  z-index: -1;
  opacity: 0.3;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  border-radius: inherit;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}
 .ui-swiper.hotelCard swiper-item.cur .ui-swiper-main {
  -webkit-transform: scale(1);
          transform: scale(1);
  transition: all 0.2s ease-in 0s;
  opacity: 1;
}
 .ui-swiper.hotelCard .ui-swiper-dot {
  display: none;
}
 .ui-swiper.hotelDetail swiper-item {
  width: 690rpx !important;
  left: 30rpx;
  box-sizing: border-box;
  padding: 20rpx 0rpx;
  overflow: initial;
}
 .ui-swiper.hotelDetail swiper-item .ui-swiper-main {
  width: 100%;
  display: block;
  height: 100%;
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
  transition: all 0.2s ease-in 0s;
  position: relative;
  background-size: cover;
}
 .ui-swiper.hotelDetail swiper-item .ui-swiper-main image {
  height: 100%;
}
 .ui-swiper.hotelDetail swiper-item.cur .ui-swiper-main {
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
  transition: all 0.2s ease-in 0s;
}

