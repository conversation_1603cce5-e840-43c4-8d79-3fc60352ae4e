<view class="{{['ui-page','theme-'+sys_theme,'main-'+sys_main,'font-'+sys_text]}}"><view class="{{['page-body',bg,ui]}}" style="{{$root.s0}}"><view class="page-body-main"><block wx:if="{{title}}"><app-navbar vue-id="73d0f1b3-1" bg="{{navBg}}" title="{{title}}" bind:__l="__l"></app-navbar></block><slot></slot><block wx:if="{{footer}}"><app-footer vue-id="73d0f1b3-2" bind:__l="__l"></app-footer></block><block wx:if="{{tabbar}}"><ui-tabbar vue-id="73d0f1b3-3" ui="shadow border-top" bind:__l="__l"></ui-tabbar></block><block wx:if="{{isloading}}"><view class="loading-body bg-blur" style="{{'z-index:'+(1999)+';'}}"><view class="_icon-loading icon-spin text-sl mb-3"></view><view>加载中</view></view></block></view><ui-modal vue-id="73d0f1b3-4" name="sys_dialog" title="{{dialog.title}}" content="{{dialog.content}}" showCancel="{{dialog.showCancel}}" cancelText="{{dialog.cancelText}}" confirmText="{{dialog.confirmText}}" data-event-opts="{{[['^success',[['e0']]]]}}" bind:success="__e" bind:__l="__l"></ui-modal><ui-toast vue-id="73d0f1b3-5" name="sys_dialog" title="{{toast.title}}" duration="{{toast.duration}}" mask="{{toast.mask}}" isLoading="{{toast.isLoading}}" image="{{toast.image}}" icon="{{toast.icon}}" data-event-opts="{{[['^success',[['e1']]]]}}" bind:success="__e" bind:__l="__l"></ui-toast><block wx:if="{{img}}"><image class="page-img" src="{{img}}" mode="aspectFill"></image></block></view></view>