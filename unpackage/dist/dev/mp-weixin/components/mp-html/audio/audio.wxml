<block wx:if="{{controls}}"><view class="_contain"><view class="_poster" style="{{('background-image:url('+poster+')')}}"><view data-event-opts="{{[['tap',[['_buttonTap',['$event']]]]]}}" class="_button" bindtap="__e"><view class="{{[playing?'_pause':'_play']}}"></view></view></view><view class="_title"><view class="_name">{{name||'未知音频'}}</view><view class="_author">{{author||'未知作者'}}</view></view><slider class="_slider" activeColor="#585959" block-size="12" handle-size="12" disabled="{{error}}" value="{{value}}" data-event-opts="{{[['changing',[['_seeking',['$event']]]],['change',[['_seeked',['$event']]]]]}}" bindchanging="__e" bindchange="__e"></slider><view class="_time">{{time||'00:00'}}</view></view></block>