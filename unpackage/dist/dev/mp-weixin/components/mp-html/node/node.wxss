
 .hl-code, .hl-pre{color:#f8f8f2;background:0 0;text-shadow:0 1px rgba(0,0,0,.3);font-family:Consolas,Monaco,'Andale Mono','Ubuntu Mono',monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;tab-size:4;-webkit-hyphens:none;hyphens:none} .hl-pre{padding:1em;margin:.5em 0;overflow:auto;border-radius:.3em} .hl-pre{background:#272822} .hl-cdata, .hl-comment, .hl-doctype, .hl-prolog{color:#8292a2} .hl-punctuation{color:#f8f8f2} .hl-namespace{opacity:.7} .hl-constant, .hl-deleted, .hl-property, .hl-symbol, .hl-tag{color:#f92672} .hl-boolean, .hl-number{color:#ae81ff} .hl-attr-name, .hl-builtin, .hl-char, .hl-inserted, .hl-selector, .hl-string{color:#a6e22e} .hl-entity, .hl-operator, .hl-url, .hl-variable, .language-css .hl-string, .style .hl-string{color:#f8f8f2} .hl-atrule, .hl-attr-value, .hl-class-name, .hl-function{color:#e6db74} .hl-keyword{color:#66d9ef} .hl-important, .hl-regex{color:#fd971f} .hl-bold, .hl-important{font-weight:700} .hl-italic{font-style:italic} .hl-entity{cursor:help} .md-p {
  -webkit-margin-before: 1em;
          margin-block-start: 1em;
  -webkit-margin-after: 1em;
          margin-block-end: 1em;
} .md-table, .md-blockquote {
  margin-bottom: 16px;
} .md-table {
  box-sizing: border-box;
  width: 100%;
  overflow: auto;
  border-spacing: 0;
  border-collapse: collapse;
} .md-tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
} .md-table .md-tr:nth-child(2n) {
  background-color: #f6f8fa;
} .md-th, .md-td {
  padding: 6px 13px !important;
  border: 1px solid #dfe2e5;
} .md-th {
  font-weight: 600;
} .md-blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
} .md-code {
  padding: 0.2em 0.4em;
  font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
} .md-pre .md-code {
  padding: 0;
  font-size: 100%;
  background: transparent;
  border: 0;
} .hl-pre {
  position: relative;
} .hl-code {
  overflow: auto;
  display: block;
} .hl-language {
  font-size: 12px;
  font-weight: 600;
  position: absolute;
  right: 8px;
  text-align: right;
  top: 3px;
} .hl-pre {
  padding-top: 1.5em;
}/* a 标签默认效果 */._a {
  padding: 1.5px 0 1.5px 0;
  color: #366092;
  word-break: break-all;
}/* a 标签点击态效果 */._hover {
  text-decoration: underline;
  opacity: 0.7;
}/* 图片默认效果 */._img {
  max-width: 100%;
  -webkit-touch-callout: none;
}/* 内部样式 */._block {
  display: block;
}._b,
._strong {
  font-weight: bold;
}._code {
  font-family: monospace;
}._del {
  text-decoration: line-through;
}._em,
._i {
  font-style: italic;
}._h1 {
  font-size: 2em;
}._h2 {
  font-size: 1.5em;
}._h3 {
  font-size: 1.17em;
}._h5 {
  font-size: 0.83em;
}._h6 {
  font-size: 0.67em;
}._h1,
._h2,
._h3,
._h4,
._h5,
._h6 {
  display: block;
  font-weight: bold;
}._image {
  height: 1px;
}._ins {
  text-decoration: underline;
}._li {
  display: list-item;
}._ol {
  list-style-type: decimal;
}._ol,
._ul {
  display: block;
  padding-left: 40px;
  margin: 1em 0;
}._q::before {
  content: '"';
}._q::after {
  content: '"';
}._sub {
  font-size: smaller;
  vertical-align: sub;
}._sup {
  font-size: smaller;
  vertical-align: super;
}._thead,
._tbody,
._tfoot {
  display: table-row-group;
}._tr {
  display: table-row;
}._td,
._th {
  display: table-cell;
  vertical-align: middle;
}._th {
  font-weight: bold;
  text-align: center;
}._ul {
  list-style-type: disc;
}._ul ._ul {
  margin: 0;
  list-style-type: circle;
}._ul ._ul ._ul {
  list-style-type: square;
}._abbr,
._b,
._code,
._del,
._em,
._i,
._ins,
._label,
._q,
._span,
._strong,
._sub,
._sup {
  display: inline;
}




