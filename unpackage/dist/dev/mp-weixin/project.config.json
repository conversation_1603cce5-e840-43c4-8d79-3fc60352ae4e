{"description": "项目配置文件。", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": false, "postcss": false, "minified": false, "newFeature": true, "bigPackageSizeSupport": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "compileType": "miniprogram", "libVersion": "3.6.2", "appid": "touristappid", "projectname": "WordPress", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}