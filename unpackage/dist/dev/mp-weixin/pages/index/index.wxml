<ui-sys vue-id="8dd740cc-1" tabbar="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><ui-navbar vue-id="{{('8dd740cc-2')+','+('8dd740cc-1')}}" back="{{false}}" bg="ui-BG-Main" op="{{true}}" isSlot="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex-bar p-3"><image style="width:200rpx;height:56rpx;" src="/static/images/logo.png" mode="aspectFill"></image></view></ui-navbar><block wx:if="{{isFirst}}"><view data-event-opts="{{[['tap',[['shutAddMyMiniapp',['$event']]]]]}}" class="addMyMiniapp" catchtap="__e"><view>点击加入我的小程序 ↑</view><text>{{"了解\""+webSiteName+"\"最新文章"}}</text></view></block><view class="index-top"><view class="indexbg ui-BG-Main" style="{{('margin-top: -'+(sys_navBar+sys_statusBar+sys_capsule.top)+'rpx;')}}"></view><view class="ui-BG-card ui-BG"><form id="search-form" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" catchsubmit="__e" catchreset="__e"><view class="ui-search-bar"><view class="ui-search-form round"><text class="ui-search-icon _icon-search"></text><input class="ui-search-input ui-search-text-left" value="" id="search-input" name="input" confirm-type="search" placeholder="搜索你感兴趣的内容..." data-event-opts="{{[['confirm',[['formSubmit',['$event']]]]]}}" bindconfirm="__e"/></view><view class="ui-search-action"><button class="ui-btn ui-BG-Main round sm" slot="right" form-type="submit">搜索</button></view></view></form><view class><block wx:if="{{$root.g0>0}}"><ui-swiper vue-id="{{('8dd740cc-3')+','+('8dd740cc-1')}}" info="{{swipe_nav}}" dotStyle="long" dotCur="ui-BG-Main" height="260rpx" imgHeight="260rpx" imgName="image" swiperCss="radius" data-event-opts="{{[['^uiTap',[['redictAppDetail']]]]}}" bind:uiTap="__e" bind:__l="__l"></ui-swiper></block></view></view></view><block wx:if="{{$root.g1>0}}"><view class="index-sticky ui-BG-card ui-BG"><ui-title vue-id="{{('8dd740cc-4')+','+('8dd740cc-1')}}" title="精选栏目" desc="热点栏目推荐" depth="3" bind:__l="__l" vue-slots="{{['icon']}}"><text class="ui-TC-Main cicon-slack-square" slot="icon"></text></ui-title><view class="{{['ui-grid '+($root.g2<5?'ui-cols-4':'ui-cols-5')]}}"><block wx:for="{{selected_nav}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><block wx:if="{{item.enable=='yes'}}"><view class="ui-item" id="{{item.id}}" data-type="{{item.type}}" data-url="{{item.url}}" data-appid="{{item.appid}}" data-path="{{item.path}}" data-event-opts="{{[['tap',[['redictAppDetail',['$event']]]]]}}" catchtap="__e"><image class="round" mode="aspectFill" src="{{item.image}}"></image><text class="ui-TC text-df">{{item.title}}</text></view></block></block></block></view></view></block><block wx:if="{{$root.g3>0}}"><view class="ui-BG-card ui-BG"><ui-title vue-id="{{('8dd740cc-5')+','+('8dd740cc-1')}}" title="推荐文章" desc="汇聚精彩文章" depth="3" bind:__l="__l" vue-slots="{{['icon']}}"><text class="ui-TC-Main cicon-whatshot" slot="icon"></text></ui-title><swiper class="sticky-list" autoplay="{{true}}" interval="5000" duration="400" circular="{{true}}" display-multiple-items="2" vertical="{{true}}"><block wx:for="{{StickyList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><swiper-item class="sticky-list-item" id="{{item.id}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" bindtap="__e"><view class="sticky-list-item-title ui-TC text-df"><text class="dot ui-BG-Main"></text>{{''+item.title.rendered+''}}</view></swiper-item></block></swiper></view></block><view class="container"><view class="post-list"><block wx:for="{{postsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="post-item ui-BG ui-BG-card" index="{{index}}" id="{{item.id}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" bindtap="__e"><view class="post-desc"><view class="post-title text-lg ui-TC"><text>{{item.title.rendered}}</text></view><view class="post-data ui-TC-3 text-xs"><text>{{item.date}}</text><text class="cicon-popover-o">{{''+item.total_comments}}</text><text class="cicon-favorite-o">{{''+item.like_count}}</text><text class="cicon-eye-o">{{''+item.pageviews}}</text></view></view><block wx:if="{{item.post_medium_image}}"><image class="post-img" src="{{item.post_medium_image}}" mode="aspectFill"></image></block></view></block></block></view><view style="{{('display:'+(isLoading?'block':'none')+';margin-top: 48rpx;')}}"><block name="tempLoading"><view class="sk-three-bounce"><view class="sk-child sk-bounce1 ui-BG-Main-2"></view><view class="sk-child sk-bounce2 ui-BG-Main-2"></view><view class="sk-child sk-bounce3 ui-BG-Main-2"></view></view></block></view><view class="loadingmore" style="{{('display:'+floatDisplay)}}"><block wx:if="{{isLastPage}}"><view class="no-more">- 无更多文章 -</view></block></view><view class="copyright"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view><view class="showerror" style="{{('display:'+showerror)}}"><text class="cicon-wifi-off text-xsl"></text><view class="errortext">暂时无法访问网络，下拉重试...</view></view></ui-sys>