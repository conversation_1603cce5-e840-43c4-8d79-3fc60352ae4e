<block><ui-sys vue-id="0e4143a4-1" tabbar="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><ui-navbar vue-id="{{('0e4143a4-2')+','+('0e4143a4-1')}}" back="{{false}}" isSlot="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex-center flex-sub">排行榜</view></ui-navbar><view class="container"><view class="showerror" style="{{('display:'+showerror)}}"><text class="cicon-wifi-off text-xsl"></text><view class="errortext">暂时无法访问网络<view><button class="more-button" size="mini" data-event-opts="{{[['tap',[['reload',['$event']]]]]}}" bindtap="__e">重新加载</button></view></view></view><view style="{{('display:'+showallDisplay)}}"><view class="post-list"><block wx:for="{{postsList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="post-item ui-BG ui-BG-card" index="{{index}}" id="{{item.post_id}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" bindtap="__e"><view class="post-desc"><view class="post-title text-lg ui-TC"><text>{{item.post_title}}</text></view><view class="post-data ui-TC-3 text-xs"><text>{{item.post_date}}</text><text class="cicon-popover-o">{{''+item.comment_total}}</text><text class="cicon-favorite-o">{{''+item.like_count}}</text><text class="cicon-eye-o">{{''+item.pageviews}}</text></view></view><image class="post-img" src="{{item.post_thumbnail_image}}" mode="aspectFill"></image></view></block></block></view></view><view class="copyright" style="{{('display:'+floatDisplay)}}"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view></ui-sys></block>