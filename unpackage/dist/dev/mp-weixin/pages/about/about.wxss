






























































































































































































































































































































































































	/*
 * 
 * 微慕小程序开源版
 * author: jianbo
 * organization: 微慕  www.minapper.com
 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
 * 技术支持微信号：iamxjb
 * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.
 * 
 */
.wrapper {
		padding: 5rpx;
}
.excerpt {

		line-height: 64rpx;
		text-align: left;
}
.h2 {
		clear: both;
		border-width: 1px 0;
		color: #666;
		text-align: center;
		font-size: 28rpx;
		line-height: 64rpx;
		font-weight: bold;
}
.clearfix {
		margin: 5px 15px;
		display: inline-block;
}
.wxParse-a {
		color: #21759b
}
.wxParse-p {
		margin-top: 0.8em;
		margin-bottom: 0.8em
}
.wxParse-p image {
		margin: 0 auto;
		width: 100% !important;
		box-shadow: 2rpx 4rpx 16rpx #666
}
.praisePost {
		position: relative;
		text-align: left;
		margin-top: 30rpx;
		margin-bottom: 30rpx;
}
.praiseTitle {
		text-align: center;
		font-weight: normal;
		line-height: 40rpx;
		margin-top: 50rpx;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #959595 !important;
		vertical-align: middle;

		height: 70rpx;
		border-radius: 30rpx;
}
.praiseText {
		text-align: center;
		font-weight: normal;
		font-size: 26rpx;
		line-height: 50rpx;
		color: #21759b !important;
		margin-bottom: 20rpx;
}
.img-like {
		width: 50rpx;
		height: 50rpx;
		display: inline-block;
		text-align: center;
		margin-right: 20rpx;
		margin-top: 8rpx;
}
.gravatarLikeImg {
		margin-top: 4px;

		height: 48rpx;
		width: 48rpx;
		margin-right: 20rpx;
}
.praise-button {
		font-size: 0.785714286rem;
		font-weight: normal;
		color: #fff;

		background-repeat: repeat-x;
		margin-top: 24rpx;
		width: 320rpx;
		height: 60rpx;
		border-radius: 60rpx;
}
.praise-button::after {
		border: none;
}
.gotowebpage-button {
		font-size: 0.785714286rem;
		font-weight: normal;
		color: #fff;

		background-repeat: repeat-x;
		line-height: 80rpx;
		margin-top: 40rpx;
		width: 320rpx;
		height: 60rpx;
		border-radius: 60rpx;
}
.gotowebpage-button::after {
		border: none;
}

