<ui-sys vue-id="1cea7424-1" bind:__l="__l" vue-slots="{{['default']}}"><view class="ui-BG-card ui-BG" style="{{('display:'+display)}}"><view class="wrapper"><view class="excerpt"><mp-html vue-id="{{('1cea7424-2')+','+('1cea7424-1')}}" content="{{pageData.post_content}}" copy-link="{{false}}" selectable="{{true}}" lazy-load="{{true}}" markdown="{{true}}" data-event-opts="{{[['^linktap',[['wxParseTagATap']]]]}}" bind:linktap="__e" bind:__l="__l"></mp-html></view></view><view class="copyright"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view></ui-sys>