


























































































































































































































	/*
 * 
 * 微慕小程序开源版
 * author: jianbo
 * organization: 微慕  www.minapper.com
 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
 * 技术支持微信号：iamxjb
 * 开源协议：MIT
 * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.
 */
.list-header {
		padding: 50rpx 0;
		text-align: left;
		font-weight: normal;
		font-size: 40rpx;
		line-height: 40rpx;
		color: #21759b;
		border-bottom: 2rpx solid #ededed;
		margin-bottom: 48rpx;
}

	/*topic list*/
.topic-list-item {
		position: relative;
		overflow: hidden;
		clear: both;

		background: #fff;
}
.topic-list-item image.cover {
		width: 100%;
		height: 200rpx;
		/*filter: blur(4px);*/
}
.log-list {
		display: flex;
		flex-direction: column;
}
.log-items {
		padding: 24rpx 24rpx 24rpx 100rpx;
		position: relative;
}
.log-items_tx {
		position: absolute;
		top: 24rpx;
		left: 24rpx;
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
		background: #f8f8f8 !important;
}
.log-items_tx image {
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
}
.log-items_top {
		height: 50rpx;
		line-height: 50rpx;
		overflow: hidden;
}
.flr {
		float: right;
}
.log-items_nr {
		margin: 8rpx 0;
}

