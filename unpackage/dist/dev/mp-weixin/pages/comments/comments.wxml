<block><ui-sys vue-id="12c898c0-1" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="showerror" style="{{('display:'+showerror)}}"><text class="cicon-wifi-off text-xsl"></text><view class="errortext">暂时无法访问网络<view><button class="more-button" size="mini" data-event-opts="{{[['tap',[['reload',['$event']]]]]}}" bindtap="__e">重新加载</button></view></view></view><view style="{{('display:'+showallDisplay)}}"><view class="log-list"><block wx:for="{{readLogs}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><view class="log-items ui-BG-card ui-BG" index="{{index}}" id="{{item[0]}}" data-itemtype="{{item[2]}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" catchtap="__e"><view class="log-items_tx"><image src="{{item.author_url}}"></image></view><view class="log-items_top"><text class="flr ui-TC-3 text-sm">{{item[2]}}</text><text class="name ui-TC text-df">{{item.author_name}}</text></view><view class="log-items_nr ui-TC text-df">{{item[1]+''}}<text class="ui-TC-Main-2 text-sm flr">查看原文</text></view></view></block></block></view></view><view class="copyright"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view></ui-sys></block>