<view class="container"><view class="wrapper"><image class="posterimage" data-src="{{posterImageUrl}}" mode="widthFix" src="{{posterImageUrl}}" data-event-opts="{{[['tap',[['posterImageClick',['$event']]]]]}}" bindtap="__e"></image></view><view style="text-align:center;"><button class="gotowebpage-button" formType="submit" size="mini" data-event-opts="{{[['tap',[['savePosterImage',['$event']]]]]}}" bindtap="__e">保存图片</button><block wx:if="{{dialog.hidden}}"><modal vue-id="522988a0-1" title="{{dialog.title}}" no-cancel="{{true}}" data-event-opts="{{[['^confirm',[['confirm']]]]}}" bind:confirm="__e" bind:__l="__l" vue-slots="{{['default']}}">{{dialog.content}}</modal></block></view><view style="color:#888;font-size:9pt;text-align:center;margin-top:10rpx;">保存至相册后进行分享</view><view class="copyright"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view>