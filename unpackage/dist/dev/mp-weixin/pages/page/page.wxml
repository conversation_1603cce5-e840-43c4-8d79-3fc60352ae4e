<view class="container"><view class="content-article"><view class="entry-title">{{''+pageData.title.rendered+''}}</view><view class="entry-summary"><mp-html vue-id="aca5f140-1" content="{{pageData.content.rendered}}" copy-link="{{false}}" selectable="{{true}}" lazy-load="{{true}}" markdown="{{true}}" data-event-opts="{{[['^linktap',[['wxParseTagATap']]]]}}" bind:linktap="__e" bind:__l="__l"></mp-html></view></view><view class="ditail-copyright" style="{{('display:'+display)}}"></view></view>