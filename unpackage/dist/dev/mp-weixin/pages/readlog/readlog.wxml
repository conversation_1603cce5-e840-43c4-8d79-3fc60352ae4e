<block><ui-sys vue-id="6afe00a2-1" title="{{'我的'+pagetitle}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="ui-BG-card ui-BG"><view class><view class="showerror" style="{{('display:'+showerror)}}"><text data-event-opts="{{[['tap',[['exit',['$event']]]]]}}" class="cuIcon-warn lg text-grey" style="height:50px;width:50px;font-size:48px;" catchtap="__e"></text><view class="errortext">暂时无法访问网络</view></view><block name="login-popup"><view class="{{['zan-popup zan-popup--center login-popup '+(isLoginPopup?'zan-popup--show':'')]}}"><view data-event-opts="{{[['tap',[['closeLoginPopup',['$event']]]]]}}" class="zan-popup__mask" bindtap="__e"></view><view class="zan-popup__container"><view class="login-popup-wrapper ui-BG"><block name="login"><block wx:if="{{!userInfo.isLogin}}"><block><view class="login-inner"><image class="avatar" src="/static/images/gravatar.png"></image><view class="username ui-TC-Main"><text>立即登录，体验更多功能</text></view></view><view class="login-btn-box"><button data-event-opts="{{[['tap',[['closeLoginPopup',['$event']]]]]}}" class="login-btn ui-BG" bindtap="__e">取消</button><button data-event-opts="{{[['tap',[['agreeGetUser',['$event']]]]]}}" class="login-btn ui-BG" bindtap="__e">确定</button></view></block></block><block wx:else><block><view class="login-inner"><image class="avatar" src="{{userInfo.avatarUrl}}"></image><view class="username"><text>{{userInfo.nickName}}</text></view></view></block></block></block></view></view></view></block><view class="log-list"><block wx:for="{{readLogs}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view><view class="log-items" index="{{index}}" id="{{item[0]}}" data-itemtype="{{item[2]}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" catchtap="__e"><text class="ren_mtie_xx text-lg ui-TC"><text class="{{['ren_mtie'+(index+1)]}}">{{index+1}}</text>{{''+item[1]}}</text></view></view></block><block wx:if="{{$root.g0<1}}"><view class="log-items"><text class="log_tips">没有相关文章！</text></view></block></view></view></view></ui-sys></block>