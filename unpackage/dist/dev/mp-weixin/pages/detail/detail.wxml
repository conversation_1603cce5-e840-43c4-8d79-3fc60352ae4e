<ui-sys vue-id="3db16520-1" bind:__l="__l" vue-slots="{{['default']}}"><view><block wx:if="{{detail.detailAdId!=''&&detail.detailAd=='1'&&detailTopAdsuccess}}"><view class="ad-box"><ad unit-id="{{detail.detailAdId}}" ad-intervals="30" ad-type="video" data-event-opts="{{[['error',[['adTopbinderror',['$event']]]]]}}" binderror="__e"></ad></view></block><view class="detail-content ui-BG-card ui-BG" style="{{('display:'+display)}}"><view class="entry-title text-xxl ui-TC"><text class="entry-title text-xxl ui-TC">{{detail.title.rendered}}</text></view><view class="entry-data ui-TC-3 text-xs"><text>{{detailDate}}</text><text class="cicon-eye-o">{{''+detail.pageviews}}</text></view><view class="entry-summary" id="entry-summary"><mp-html vue-id="{{('3db16520-2')+','+('3db16520-1')}}" content="{{detail.content.rendered}}" copy-link="{{true}}" selectable="{{true}}" lazy-load="{{true}}" markdown="{{true}}" img-cache="{{true}}" data-event-opts="{{[['^linktap',[['wxParseTagATap']]]]}}" bind:linktap="__e" bind:__l="__l"></mp-html></view><view class="bottomlink text-sm bg-grey-thin" style="{{('display:'+display)}}"><view><text style="margin-top:10rpx;">文章ID：</text>{{postID+''}}</view><text>声明：</text>{{'本站所有内容来自网友投稿发布。如若本站内容侵犯了原著者的合法权益，可联系'+wechat+'进行处理。'}}</view></view><block wx:if="{{detail.previous_post_id||detail.next_post_id}}"><view class="up-down-post ui-BG-card ui-BG"><block wx:if="{{detail.previous_post_id}}"><view class="up-post"><navigator url="{{'../detail/detail?id='+detail.previous_post_id}}" open-type="redirect" hover-class="relatedNavigator">{{"上一篇："+detail.previous_post_title}}</navigator></view></block><block wx:if="{{detail.next_post_id}}"><view class="down-post"><navigator url="{{'../detail/detail?id='+detail.next_post_id}}" open-type="redirect" hover-class="relatedNavigator">{{detail.next_post_title+"：下一篇"}}</navigator></view></block></view></block><block wx:if="{{detail.videoAdId!=''&&detail.detailAd=='1'&&detailAdsuccess}}"><view class="ad-box-video"><ad unit-id="{{detail.videoAdId}}" ad-intervals="30" ad-type="video" data-event-opts="{{[['error',[['adbinderror',['$event']]]]]}}" binderror="__e"></ad></view></block><block wx:if="{{$root.g0!=0}}"><view class="relatedPost ui-BG-card ui-BG" style="{{('display:'+display)}}"><view class="subTitle ui-TC">猜你喜欢</view><view class="subTitle_line"></view><block wx:for="{{postList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block><navigator url="{{'../detail/detail?id='+item.id}}" open-type="redirect" hover-class="relatedNavigator"><view class="relatedText ui-TC-1"><text class="dot ui-BG-Main"></text>{{''+item.title.rendered+''}}</view></navigator></block></block></view></block><view class="ditail-copyright" style="{{('display:'+display)}}"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view><view class="showerror" style="{{('display:'+showerror)}}"><text class="cicon-wifi-off text-xsl"></text><view class="errortext">{{errMessage}}</view></view></view></ui-sys>