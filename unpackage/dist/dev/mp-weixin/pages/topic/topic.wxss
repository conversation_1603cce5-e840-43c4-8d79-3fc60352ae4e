
.ui-grid {
	margin: 12rpx;
}
.ui-BG-card {
	margin: 12rpx;
	padding: 0;
	overflow: hidden;
}
.ui-item {

	text-align: center;
	position: relative;
}
.ui-card {
	margin: 12rpx !important;
}
.ui-card-bg {
	height: 160rpx;
	position: relative;
	background-size: cover;
	background-position: center;
	overflow: hidden;
	-webkit-filter: saturate(160%) blur(22px);
	        filter: saturate(160%) blur(22px);
}
.ui-card-image {
	position: absolute;
	width: 120rpx;
	height: 120rpx;
	display: inline-block;
	top: 100rpx;
	left: 121.5rpx;
	background-color: #fff;
	border-radius: 100%;
	border: 1px solid #fff;
}
.ui-card-sub {
	position: absolute;
	right: 10px;
	top: 10px;
	border: solid 1px rgba(255, 255, 255, .5);
	z-index: 1;
	color: #fff;
	padding: 2px 6px;
	background-color: rgba(0, 0, 0, 0.1);
}
.ui-card-sub:hover {
	background-color: rgba(0, 0, 0, 0.4);
}
.ui-card-content {
	padding: 60rpx 12rpx 24rpx;
	overflow: hidden;
	background-color: #fff;
	z-index: 9;
	height: 236rpx;
}
.ui-card-content .dec {
	margin-top: 10rpx;
}

