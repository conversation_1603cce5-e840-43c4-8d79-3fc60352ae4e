<block><ui-sys vue-id="14d0dce0-1" tabbar="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><ui-navbar vue-id="{{('14d0dce0-2')+','+('14d0dce0-1')}}" back="{{false}}" isSlot="{{true}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="flex-center flex-sub">分类</view></ui-navbar><view class="ui-grid ui-cols-2"><block wx:for="{{categoriesList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="ui-item"><view class="ui-BG ui-BG-card"><view class="ui-card-bg" style="{{('background-image:url('+item.category_thumbnail_image+') ;')}}" data-item="{{item.name}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['redictIndex',['$event']]]]]}}" bindtap="__e"></view><image class="ui-card-image" mode="aspectFill" src="{{item.category_thumbnail_image}}" data-item="{{item.name}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['redictIndex',['$event']]]]]}}" bindtap="__e"></image><view class="ui-card-content ui-BG radius" data-item="{{item.name}}" data-id="{{item.id}}" data-event-opts="{{[['tap',[['redictIndex',['$event']]]]]}}" bindtap="__e"><view><text class="text-lg ui-TC">{{item.name+''}}</text></view><text class="ui-TC-3 text-xs">{{item.count+'篇文章'}}</text><text class="ui-TC-3 text-sm text-linecut-2 dec">{{item.description+''}}</text></view></view></view></block></view><view class="copyright" style="{{('display:'+floatDisplay)}}"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></ui-sys></block>