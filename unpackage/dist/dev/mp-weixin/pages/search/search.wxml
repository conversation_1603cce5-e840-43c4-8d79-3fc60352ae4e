<block><ui-sys vue-id="50cad900-1" bind:__l="__l" vue-slots="{{['default']}}"><view style="{{('display:'+(isArticlesList?'block':'none'))}}"><form id="search-form" data-event-opts="{{[['submit',[['formSubmit',['$event']]]],['reset',[['formReset',['$event']]]]]}}" catchsubmit="__e" catchreset="__e"><view class="search-box"><input class="search-input" value="" id="search-input" name="input" confirm-type="search" placeholder="守望轩" data-event-opts="{{[['confirm',[['formSubmit',['$event']]]]]}}" bindconfirm="__e"/><button class="search-button" form-type="submit" size="mini" plain="{{true}}"><icon type="search" color="#959595" size="16"></icon></button></view></form><view class="list-container"><view class="copyright"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view></view></ui-sys></block>