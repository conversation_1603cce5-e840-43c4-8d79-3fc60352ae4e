










































































































































































































































































































































































































































































	/*
 * 
 * 微慕小程序开源版
 * author: jianbo
 * organization: 微慕  www.minapper.com
 * github:    https://github.com/iamxjb/winxin-app-watch-life.net
 * 技术支持微信号：iamxjb
 * 开源协议：MIT
 * Copyright (c) 2017  微慕 https://www.minapper.com All rights reserved.
 */
.topic-list-item {
		height: 240rpx;
		overflow: hidden;
		position: relative;
		background-color: rgba(0, 0, 0, 0.3);
}
.cover {
		width: 100%;
		height: 240rpx;
		position: absolute;
		-webkit-filter: saturate(180%) blur(35px);
		        filter: saturate(180%) blur(35px);
}
.topic-content-brief {
		width: 80%;
		position: absolute;
		left: 50%;
		top: 50%;
		-webkit-transform: translate(-50%, -50%);
		        transform: translate(-50%, -50%);
		z-index: 2;
		text-align: center;
}
.topic-content-brief text {
		font-size: 16px;
		line-height: 1.6;
		color: #fff;
}

	/* 搜索列表 */
.topic-content-title text {
		font-size: 18px;
		font-weight: 500;
		line-height: 1.6;
		color: #fff;
}
.searchKey {
		color: #121b23;
}
.search-tips {
		font-size: 10px;
}

