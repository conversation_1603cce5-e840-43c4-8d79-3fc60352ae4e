<block><ui-sys vue-id="2e637200-1" bind:__l="__l" vue-slots="{{['default']}}"><view class="topic-common-list" style="{{('display:'+isCategoryPage)}}"><view class="topic-list-item"><image class="cover" src="{{categoriesImage}}" mode="aspectFill"></image><view class="topic-content-brief"><text>{{categoriesList.description}}</text></view></view></view><view class="topic-common-list" style="{{('display:'+isSearchPage)}}"><view class="topic-list-item"><image class="cover" src="/static/images/website-search.png" mode="aspectFill"></image><view class="topic-content-brief"><view class="topic-content-title"><text>搜索关键字：</text><text class="searchKey">{{searchKey}}</text></view><text class="search-tips">* 本搜索是全文搜索</text></view></view></view><view class="container"><view class="showerror" style="{{('display:'+showerror)}}"><text class="cicon-wifi-off text-xsl"></text><view class="errortext">暂时无法访问网络<view><button class="more-button" size="mini" data-event-opts="{{[['tap',[['reload',['$event']]]]]}}" bindtap="__e">重新加载</button></view></view></view><view style="{{('display:'+showallDisplay)}}"><view class="post-list"><block wx:for="{{postsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{index%item.listAdEvery==0&&index!=0&&item.listAd=='1'&&item.listAdId!=''&&listAdsuccess}}"><view class="list-item-ad" index="{{index}}"><block wx:if="{{index/item.listAdEvery%2==0}}"><block><ad unit-id="{{item.videoAdId}}" ad-type="video" ad-theme="white" data-event-opts="{{[['error',[['adbinderror',['$event']]]]]}}" binderror="__e"></ad></block></block><block wx:else><block><ad unit-id="{{item.listAdId}}" ad-intervals="30" data-event-opts="{{[['error',[['adbinderror',['$event']]]]]}}" binderror="__e"></ad></block></block></view></block><view class="post-item ui-BG ui-BG-card" index="{{index}}" id="{{item.id}}" data-event-opts="{{[['tap',[['redictDetail',['$event']]]]]}}" bindtap="__e"><view class="post-desc"><view class="post-title text-lg ui-TC"><text>{{item.title.rendered}}</text></view><view class="post-data ui-TC-3 text-xs"><text>{{item.date}}</text><text class="cicon-popover-o">{{''+item.total_comments}}</text><text class="cicon-favorite-o">{{''+item.like_count}}</text><text class="cicon-eye-o">{{''+item.pageviews}}</text></view></view><block wx:if="{{item.post_medium_image}}"><image class="post-img" src="{{item.post_medium_image}}" mode="aspectFill"></image></block></view></block></block></view><view style="{{('display:'+(isLoading?'block':'none'))}}"><block name="tempLoading"><view class="sk-three-bounce"><view class="sk-child sk-bounce1 ui-BG-Main-2"></view><view class="sk-child sk-bounce2 ui-BG-Main-2"></view><view class="sk-child sk-bounce3 ui-BG-Main-2"></view></view></block></view><view class="loadingmore" style="{{('display:'+floatDisplay)}}"><block wx:if="{{isLastPage}}"><view class="no-more">- 无更多文章 -</view></block></view></view><view class="copyright" style="{{('display:'+floatDisplay)}}"><block name="tempCopyright"><view style="margin-top:24rpx;">{{'© '+webSiteName+" "+domain+''}}</view></block></view></view></ui-sys></block>