<template>
	<view class="ui-title" :class="[ui,align, tpl, bg, { line: inLine }, 'heading-' + depth]">
		<view class="anchor" :id="title" :style="{top:`-${sys_navBar}px`}"></view>
		<view class="action" :class="[hasLine ? 'hasLine' : '', hasDot ? 'hasDot' : '']">
			<div class="action-icon">
				<block v-if="isIcon">
					<view class="ui-TC-Main _icon-titles" v-if="depth < 3"></view>
					<view class="ui-TC-Main _icon-title" v-if="depth == 3"></view>
					<view class="ui-TC-Main _icon-title icon-sm" v-if="depth > 3"></view>
				</block>
				<slot name="icon">
					
				</slot>
				<!-- <img style="height: 18px;margin-right: -5px;text-transform: Capitalize;" src="@/static/home-icon/shuxian.png" alt="" /> -->
			</div>
			<!-- text-df -->
			<view class="action-title" :class="[titleUi]" v-if="title != ''">
				{{ title }}
				<slot></slot>
				<view class="action-line" :class="[line]" v-if="hasLine">
					<view></view>
					<view></view>
				</view>
				<view class="action-dot" :class="[dot]" v-if="hasDot">
					<view></view>
					<view></view>
				</view>
			</view>
			<view class="action-desc" :class="[descUi]" v-if="desc != ''">{{ desc }}</view>
		</view>
		<view class="more"><slot name="more"></slot>
		<icon-right v-if="moreIcon" :size="16" style="top: 3px;left: 1px;" :fill="['#6B7280']" /></view>
	</view>
</template>

<script>
export default {
	name: 'UiTitle',
	data() {
		return {};
	},
	props: {
		ui: {
			type: String,
			default: ''
		},
		bg: {
			type: String,
			default: 'bg-none'
		},
		title: {
			type: String,
			default: ''
		},
		desc: {
			type: String,
			default: ''
		},
		titleUi: {
			type: String,
			default: 'ui-TC'
		},
		descUi: {
			type: String,
			default: 'ui-TC-3'
		},
		depth: {
			type: [Number, String],
			default: 0
		},
		dot: {
			type: String,
			default: 'ui-BG-Main'
		},
		line: {
			type: String,
			default: 'ui-BG-Main'
		},
		isIcon: {
			type: Boolean,
			default: false
		},
		hasDot: {
			type: Boolean,
			default: false
		},
		hasLine: {
			type: Boolean,
			default: false
		},
		align: {
			type: String,
			default: 'left'
		},
		tpl: {
			type: String,
			default: 'default'
		},
		inLine: {
			type: Boolean,
			default: false
		},
		moreIcon: {
			type: Boolean,
			default: false
		}
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {}
};
</script>

<style lang="scss">
ui-title {
	display: block;
}

.ui-title {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0 !important;
	// padding: 30rpx;
	.anchor {
		pointer-events: none;
		position: absolute;
	}
	&.line {
		padding: 0 30rpx;
	}
	.action-icon {
		margin-left: -10rpx;
		margin-right: 10rpx;
	}
	.more {
		font-size: 14px;
		color: var(--ui-TC-6);
		font-family: "Inter";
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
	.action {
		display: flex;
		align-items: center;
		&.hasLine {
			position: relative;
			z-index: 2;
			.action-line {
				position: absolute;
				width: 68%;
				height: 0.5em;
				bottom: 0;
				z-index: -1;
				opacity: 0.7;
			}
		}
		&.hasDot {
			position: relative;
			margin-left: 10rpx;
			z-index: 2;
			.action-dot {
				position: absolute;
				width: 18px;
				height: 18px;
				border-radius: 50%;
				top: -0.3em;
				z-index: -1;
				margin-left: -0.5em;
				opacity: 0.7;
			}
		}
		.action-title {
			font-weight: bold;
		}
		.action-desc {
			margin-left: 10rpx;
			text-transform: Capitalize;
		}
	}
	&.center {
		justify-content: center;
		letter-spacing: 5rpx;
		.hasLine {
			.action-line {
				left: 0;
				right: 0;
				margin: auto;
			}
		}
	}
	&.right {
		justify-content: flex-end;
		letter-spacing: 5rpx;
		.hasDot {
			.action-dot {
				left: auto;
				right: 0;
			}
		}
		.hasLine {
			.action-line {
				left: auto;
				right: 0;
			}
		}
	}
	&.stack {
		.action {
			position: relative;
			&.hasLine {
				.action-line {
					width: 100%;
				}
			}
			.action-title {
				position: relative;
				z-index: 2;
				top: 10rpx;
			}
			.action-desc {
				position: absolute;
				z-index: 1;
				left: 30rpx;
				margin: 0;
				font-size: 60rpx;
				font-weight: bold;
				opacity: 0.3;
				top: -10rpx;
			}
		}
		&.center,&.right {
			position: relative;
			z-index: 1;
			.action {
				position: initial;
			}
			.action-desc {
				top: 10rpx;
				left: 0;
				right: 0;
				margin: auto;
				text-align: center;
			}
		}
		&.right {
			.action-desc { 
				text-align: right;
			}
		}
	}
	&.center-column {
		justify-content: center;
		letter-spacing: 5rpx;
		.action {
			flex-direction: column;
			.action-desc {
				font-size: 24rpx;
				margin: 0;
			}
			&.hasDot {
				position: relative;
				.action-dot {
					height: 0px;
					width: 100%;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					margin: auto;
					view {
						height: 1px;
						background: inherit;
						width: 100rpx;
						position: absolute;
						top: 0;
						bottom: 0;
						margin: auto;
						&:first-child {
							left: -500rpx;
							right: 0;
							transform: rotate(180deg);
						}
						&:last-child {
							left: 0;
							right: -500rpx;
						}
						&::after {
							content: '';
							width: 4px;
							height: 12px;
							border-radius: 20px;
							background: inherit;
							display: block;
							position: absolute;
							top: -5px;
							left: -10px;
							transform: rotate(30deg);
						}
						&::before {
							content: '';
							width: 4px;
							height: 20px;
							border-radius: 20px;
							background: inherit;
							display: block;
							position: absolute;
							top: -10px;
							left: -20px;
							transform: rotate(30deg);
						}
					}
				}
			}
			&.hasLine {
				position: relative;
				.action-line {
					height: 0px;
					width: 100%;
					top: 0;
					bottom: 0;
					left: 0;
					right: 0;
					margin: auto;
					view {
						height: 1px;
						background: inherit;
						width: 160rpx;
						position: absolute;
						top: 0;
						bottom: 0;
						margin: auto;
						&:first-child {
							left: -480rpx;
							right: 0;
							transform: rotate(180deg);
						}
						&:last-child {
							left: 0;
							right: -480rpx;
						}
						&::after {
							content: '';
							width: 100%;
							height: 100%;
							background: linear-gradient(to left, var(--ui-BG), rgba(255, 255, 255, 0));
							display: block;
							position: absolute;
						}
					}
				}
			}
		}
	}
}

.heading-0 {
	padding-top: #{map-get($spacers, 4)};
	padding-bottom: #{map-get($spacers, 4)};
	font-size: calc(#{map-get($fontsize, xl)}rpx + var(--textSize)) !important;
	.action-desc{
		font-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;
	}
}

.heading-1 {
	padding-top: #{map-get($spacers, 5)};
	padding-bottom: #{map-get($spacers, 4)};
	font-size: calc(#{map-get($fontsize, xxl)}rpx + var(--textSize)) !important;
	.action-desc{
		font-size: calc(#{map-get($fontsize, lg)}rpx + var(--textSize)) !important;
	}
}

.heading-2 {
	padding-top: #{map-get($spacers, 4)};
	padding-bottom: #{map-get($spacers, 3)};
	font-size: calc(#{map-get($fontsize, xl)}rpx + var(--textSize)) !important;	
	.action-desc{
		font-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;
	}
}

.heading-3 {
	padding-top: #{map-get($spacers, 3)};
	padding-bottom: #{map-get($spacers, 2)};
	font-size: calc(#{map-get($fontsize, lg)}rpx + var(--textSize)) !important;	
	.action-desc{
		font-size: calc(#{map-get($fontsize, sm)}rpx + var(--textSize)) !important;
	}
}

.heading-4 {
	padding-top: #{map-get($spacers, 2)};
	padding-bottom: #{map-get($spacers, 1)};
	font-size: calc(#{map-get($fontsize, df)}rpx + var(--textSize)) !important;	
	.action-desc{
		font-size: calc(#{map-get($fontsize, xs)}rpx + var(--textSize)) !important;
	}
}
</style>
