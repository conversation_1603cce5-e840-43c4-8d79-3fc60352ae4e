<template>
	<view class="ui-menu-item-new" :class="[{ 'arrow': arrow },{'first-item':isFirstChild},ui]">
		<!-- <view class="ui-menu-item-new-left" v-if="customScopedSlots.icon||icon!=''"> -->
		<template v-if="customScopedSlots.leftIcon">
			<view class="ui-menu-item-new-left">
				<slot name="leftIcon"></slot>
			</view>
		</template>
			<!-- <template v-if="customScopedSlots.icon">
				<slot name="icon"></slot>
			</template>
			<template v-else>
				<view :class="icon"></view>
			</template> -->
		<!-- </view> -->
		<view class="ui-menu-item-new-content">
			<view class="ui-menu-item-new-title">
				{{ title }}
			</view>
			<view class="ui-menu-item-new-action" v-if="customScopedSlots.action">
				<slot name="action"></slot>
			</view>
		</view>
		<!-- <view class="ui-menu-item-new-right" v-if="arrow"> -->
			<!-- <view class="_icon-forward"></view> -->
			<!-- <icon-right class="ui-menu-item-new-right" :size="16" /> -->
		<!-- </view> -->
		<template v-if="customScopedSlots.rightIcon">
			<view class="ui-menu-item-new-right" v-if="customScopedSlots.rightIcon">
				<slot name="rightIcon"></slot>
			</view>
		</template>
	</view>
</template>

<script>

export default {
	name: 'UiMenuItemNew',
	data() {
		return { 
		};
	},
	props: {
		ui: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: ''
		},
		icon: {
			type: String,
			default: ''
		},
		arrow: {
			type: [Boolean, String],
			default: false
		}
	},
	created() {},
	computed: {
		isFirstChild(){
			let parent = this._getParent('UiMenu')
			// 判断是否存在 ui-Menu 组件
			if(parent){ 
				if (!parent.firstChildAppend) {
					parent.firstChildAppend = true;
					return true;
				}
			}
			return false
		}
	}, 
	watch: {},
	methods: { 
	}
};
</script>

<style lang="scss">
.ui-menu-item-new {
	padding: 16px !important;
	display: flex;
	align-items: center;
	border-bottom: 1px solid var(--ui-TC-8);
}
.ui-menu-item-new-left {
	width: 28px;
	padding-right: 8px;
	display: flex;
	align-items: center;
}
.ui-menu-item-new-title {
	color: var(--ui-TC-7);
	font-family: "Inter";
	font-size: 16px;
	font-style: normal;
	font-weight: 400;
	// line-height: 24px; /* 1.5 */
}
.ui-menu-item-new-content {
	flex: 1;
}
.ui-menu-item-new-right {
	width: 16px;
	display: flex;
	align-items: center;
}
</style>
