<template>
	<view style="margin-bottom: 0px;">
		<view class="ui-swiper" :class="[mode,bg,ui]">
			<swiper :circular="circular" :autoplay="autoplay" :interval="interval" duration="500" :style="{height: height}"
				@change="swiperChange">
				<swiper-item v-for="(item, index) in info" :key="index" :class="{ cur: cur == index }" :data-item="item" v-if="item.enable=='yes'" :id="item.id" :index="index" :data-type="item.type" :data-appid="item.appid" :data-path="item.path" :data-url="item.url" @tap="redictAppDetail">
					<view v-if="isImage" class="ui-swiper-main radius">
						<image class="swiper-image" :src="item.image" :style="{height: imgHeight}" mode="aspectFill"></image>
					</view>
					<view v-else class="ui-swiper-main radius">
						<view :style="{height: imgHeight}" style="background: #F5F9FF;">
							<view class="content">
								<view class="left">
									<view class="title">{{item.title}}</view>
									<view class="describe">{{item.description}}</view>
								</view>
								<view class="right">
									<image class="swiper-image" :src="item.image" mode="aspectFill"></image>
								</view>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="ui-swiper-dot" :class="dotStyle" v-if="dotStyle != 'tag'">
				<!-- class="ui-BG-yellow" 黄色有点不和谐-->
				<view v-for="(item, index) in info" :key="index" :class="[cur == index ? 'cur' : '', dotCur]"></view>
			</view>
			<view class="ui-swiper-dot" :class="dotStyle" v-if="dotStyle == 'tag'">
				<view class="ui-tag radius" :class="[dotCur]">
					<view>{{ cur + 1 }} / {{ info.length }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'UiSwiper',
		data() {
			return {
				cur: 0,
			}
		},
		props: {
			isImage: {
				type: Boolean,
				default: true
			},
			circular: {
				type: Boolean,
				default: true
			},
			autoplay: {
				type: Boolean,
				default: true
			},
			interval: {
				type: Number,
				default: 5000
			},
			duration: {
				type: Number,
				default: 500
			},
			info: {
				type: Array,
				default () {
					return [];
				}
			},
			ui: {
				type: String,
				default: ''
			},
			mode: {
				//default
				type: String,
				default: 'default'
			},
			dotStyle: {
				//default long tag
				type: String,
				default: 'default'
			},
			dotCur: {
				type: String,
				default: 'bg-black'
			},
			bg: {
				type: String,
				default: 'bg-none'
			},
			height: {
				type: String,
				default: '40vw'
			},
			imgHeight: {
				type: String,
				default: '40vw'
			},
		},
		methods: {
			  redictAppDetail: function (e) {
			            let {
			              type,
			              appid,
			              url,
			              path
			            } = e.currentTarget.dataset
			        
			            if (type === 'apppage') { // 小程序页面         
			              wx.navigateTo({
			                url: path
			              })
			            }
			            if (type === 'webpage') { // web-view页面
			              url = '../webpage/webpage?url=' + url
			              wx.navigateTo({
			                url: url
			              })
			            }
			            if (type === 'miniapp') { // 其他小程序
			              wx.navigateToMiniProgram({
			                appId: appid,
			                path: path
			              })
			            }
			          },
			swiperChange(e) {
				this.cur = e.detail.current;
			},
		}
	};
</script>

<style lang="scss">
	::v-deep .ui-swiper {
		position: relative;
		.ui-swiper-main {
			width: 100%;
			height: 100%;
		}
		.ui-swiper-main .content {
			display: flex;
			width: 100%;
			padding: 64rpx 40rpx;
		}
		.ui-swiper-main .content .left{
			flex: 1;
			// padding: 20rpx;
		}
		.ui-swiper-main .content .right{
			width: 160rpx;
			height: 160rpx;
		}
		.title {
			color: var(--ui-TC-7) !important;
			font-family: "Inter";
			font-size: 48rpx;
			font-style: normal;
			font-weight: 700;
			line-height: 84rpx;
		}
		.describe {
			color: #4B5563;
			font-family: "Inter";
			font-size: 16px;
			font-style: normal;
			font-weight: 400;
			line-height: 64rpx; /* 1.5 */
		}
		.ui-swiper-main .content .right image {
			width: 100%;
			height: 100%;
			border-radius: 32rpx;
		}
		.ui-swiper-main .swiper-image {
			width: 100%;
			height: 100%;
			// border-radius: var(--radius);
		}

		.ui-swiper-dot {
			position: absolute;
			width: 100%;
			bottom: 32px;
			height: 8px;
			display: flex;
			align-items: center;
			justify-content: center;

			&.default view {
				display: inline-flex;
				border-radius: 50rpx;
				width: 8px;
				height: 8px;
				border: 2px solid transparent;
				margin: 0 10rpx;
				background-color: red;
				// opacity: 0.3;
				position: relative;
				justify-content: center;
				align-items: center;
				
				&.cur {
					// width: 8px;
					height: 8px;
					opacity: 1;
					border: 0px solid transparent;
				}

				&.cur::after {
					content: '';
					border-radius: 50rpx;
					// width: 4px;
					// height: 4px;
					background-color: #fff;
				}
			}

			&.long view {
				display: inline-block;
				border-radius: 100rpx;
				width: 8px;
				height: 8px;
				margin: 0 10rpx;
				opacity: 0.3;
				position: relative;
				
				&.cur {
					width: 16rpx;
					opacity: 1;
				}

				&.cur::after {}
			}

			&.line {
				bottom: 20rpx;

				view {
					display: inline-block;
					width: 30px;
					height: 3px;
					opacity: 0.3;
					position: relative;
					&.cur {
						opacity: 1;
					}
				}
			}

			&.tag {
				justify-content: flex-end;
				position: absolute;
				bottom: 20rpx;
				right: 20rpx;
			}
		}
		// .ui-swiper-dot .ui-BG-Main  {
			
		// }
		.ui-swiper-dot.long .ui-BG-Main {
			background-color: #D1D5DB !important;
			margin: 0 4px;
			opacity: 1;
		}
		.ui-swiper-dot.long .ui-BG-Main.cur {
			background-color: var(--ui-BG-Main) !important;
			width: 8px;
		}
		&.card {
			swiper-item {
				width: 610rpx !important;
				left: 70rpx;
				box-sizing: border-box;
				padding: 20rpx 0rpx 60rpx;
				overflow: initial;
			}

			swiper-item .ui-swiper-main {
				width: 100%;
				display: block;
				height: 100%;
				border-radius: 10rpx;
				transform: scale(0.9);
				transition: all 0.2s ease-in 0s;
				position: relative;
				background-size: cover;

				image {
					height: 100%;
					border-radius: inherit;
				}
			}

			swiper-item .ui-swiper-main::before {
				content: '';
				display: block;
				background: inherit;
				filter: blur(5px);
				position: absolute;
				width: 100%;
				height: 100%;
				top: 10rpx;
				left: 10rpx;
				z-index: -1;
				opacity: 0.3;
				transform-origin: 0 0;
				border-radius: inherit;
				transform: scale(1, 1);
			}

			swiper-item.cur .ui-swiper-main {
				transform: scale(1);
				transition: all 0.2s ease-in 0s;
			}

			.ui-swiper-dot.tag {
				position: absolute;
				bottom: 85rpx;
				right: 75rpx;
			}
		}

		&.hotelCard {
			swiper-item {
				width: 650rpx !important;
				left: 30rpx;
				box-sizing: border-box;
				padding: 0rpx 0rpx 50rpx;
				overflow: initial;
			}

			swiper-item .ui-swiper-main {
				width: 100%;
				display: block;
				height: 100%;
				border-radius: 10rpx;
				transform: scale(0.9);
				opacity: 0.8;
				transition: all 0.2s ease-in 0s;
				position: relative;
				background-size: cover;

				image {
					width: 100%;
					height: 400rpx;
					border-radius: 10rpx 10rpx 0 0;
				}
			}

			swiper-item .ui-swiper-main::before {
				content: '';
				display: block;
				background: inherit;
				filter: blur(5px);
				position: absolute;
				width: 100%;
				height: 100%;
				top: 10rpx;
				left: 10rpx;
				z-index: -1;
				opacity: 0.3;
				transform-origin: 0 0;
				border-radius: inherit;
				transform: scale(1, 1);
			}

			swiper-item.cur .ui-swiper-main {
				transform: scale(1);
				transition: all 0.2s ease-in 0s;
				opacity: 1;
			}

			.ui-swiper-dot {
				display: none;
			}
		}

		&.hotelDetail {
			swiper-item {
				width: 690rpx !important;
				left: 30rpx;
				box-sizing: border-box;
				padding: 20rpx 0rpx;
				overflow: initial;
			}

			swiper-item .ui-swiper-main {
				width: 100%;
				display: block;
				height: 100%;
				transform: scale(0.96);
				transition: all 0.2s ease-in 0s;
				position: relative;
				background-size: cover;

				image {
					height: 100%;
				}
			}

			swiper-item.cur .ui-swiper-main {
				transform: scale(0.96);
				transition: all 0.2s ease-in 0s;
			}
		}
	}
</style>
