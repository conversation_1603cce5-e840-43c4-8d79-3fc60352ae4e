@import './light'; //浅蓝主题
@import './dark'; //深蓝主题
// 多主题
.theme-light {
	@include theme-light;
}
.theme-dark {
	@include theme-dark;
}
.theme-auto {
	@include theme-light;
}
@media (prefers-color-scheme: dark) {
	.theme-auto {
		@include theme-dark;
	}
}

@each $value in ('', '-1', '-2', '-3', '-4') {
	.ui-BG#{$value} {
		background-color: var(--ui-BG#{$value}) !important;
		color: var(--ui-TC);
	}
	.ui-TC#{$value} {
		color: var(--ui-TC#{$value}) !important;
	}
	.ui-BG-Main#{$value} {
		background-color: var(--ui-BG-Main#{$value}) !important;
		color: var(--ui-BG-Main-TC) !important;
	}
	.ui-BG-Main#{$value}.ui-BG-transparent {
		background-color: transparent !important;
		// background: linear-gradient(to bottom, var(--ui-BG-Main), var(--ui-BG-Main),var(--ui-BG-1));
	}
	.ui-BG-Main#{$value}.ui-BG-yellow {
		background-color: #FFE945 !important;
	}
	.ui-TC-Main#{$value} {
		color: var(--ui-BG-Main#{$value}) !important;
	}
}

@each $color, $value in $colors {
	.main-#{$color} {
		--ui-BG-Main: #{$value};
		--ui-BG-Main-1: #{mix(rgba(255, 255, 255, 0.7), desaturate($value, 10%), 10%)};
		--ui-BG-Main-2: #{mix(rgba(255, 255, 255, 0.6), desaturate($value, 40%), 20%)};
		--ui-BG-Main-6: #{mix(rgba(255, 255, 255, 0.1), desaturate($value, 10%), 92%)};
		--ui-BG-Main-3: #{mix(rgba(119, 119, 119, 0.2), desaturate($value, 40%), 40%)};
		--ui-BG-Main-4: #{mix(rgba(119, 119, 119, 0.1), desaturate($value, 40%), 60%)};
		--ui-BG-Main-5: #{mix(rgba(233, 244, 255, 1), desaturate($value, 40%), 100%)};

		@if $color == 'yellow' {
			--ui-BG-Main-TC: #333333 !important;
		} @else {
			--ui-BG-Main-TC: #ffffff !important;
		}
	}
}
