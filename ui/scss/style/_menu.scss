.ui-menu{
	background-color: var(--ui-BG);
}

.ui-menu-item {
	position: relative;
	@include flex-bar;
	min-height: 4em;
	padding: 0 30rpx;
	.ui-menu-item-icon {
		width: 1.7em;
		margin-right: 0.3em;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		transform: scale(1.3);
	}
	.ui-menu-item-icon .ui-menu-item-image {
		width: 1.2em;
		height: 1.2em;
		display: inline-block;
	}
	.ui-menu-item-content {
		flex: 1;
		position: relative;
		@include flex-bar;
	}
	.ui-menu-item-arrow {
		width: 1.6em;
		text-align: center;
		color: var(--ui-TC-3);
	}
	 
	&.first-item::after {
		display: none;
	}
	&:first-child::after{
		display: none;
	}
}
