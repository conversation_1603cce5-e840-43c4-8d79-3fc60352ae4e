{"description": "项目配置文件。", "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": ["sass"]}, "compileType": "miniprogram", "libVersion": "2.21.3", "appid": "wx39075d0bde24f9f7", "projectname": "www.watch-life.net", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"list": [{"name": "test", "pathName": "", "title": "", "cacheKey": ""}]}, "conversation": {"list": [{"name": "test", "query": "", "boxQI": "", "service": {}}]}, "plugin": {"list": []}, "game": {"list": []}, "miniprogram": {"list": [{"id": -1, "name": "评论", "pathName": "pages/comments/comments", "query": "", "scene": null}, {"id": -1, "name": "专题页", "pathName": "pages/topic/topic", "query": "", "scene": null}, {"id": -1, "name": "我的", "pathName": "pages/readlog/readlog", "query": "", "scene": null}, {"id": -1, "name": "文章详情", "pathName": "pages/detail/detail", "query": "id=1553", "scene": null}]}}}