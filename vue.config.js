module.exports = {
	devServer: {
	    proxy: {
	      '/api': {
	        // target: 'http://************:8081', // 洪川后端服务地址
			target: 'http://*************:7081', // 公司演示环境地址
			// target: 'http://*************:8082', // 蒋平服务地址
			// target: 'http://************:8082', // 文博服务地址
			// target: 'http://localhost:8888', // 公司演示环境外网映射本地
	        changeOrigin: true, // 允许跨域
	        // pathRewrite: {
	        //   // '^/api': '' // 重写路径，去掉/api前缀
	        // }
	      }
	    }
	},
	chainWebpack: (config) => {
	    config.module
	      .rule('mjs$')
	      .test(/\.mjs$/)
	      .include.add(/node_modules/)
	      .end()
	      .type('javascript/auto');
	},
}